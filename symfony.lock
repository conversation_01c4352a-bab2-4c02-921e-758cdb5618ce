{"api-platform/core": {"version": "2.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.5", "ref": "b86557ce5677fa855b1b2608f4a4bc4a8fed8be7"}, "files": ["config/packages/api_platform.yaml", "config/routes/api_platform.yaml", "src/Entity/.gitignore"]}, "dama/doctrine-test-bundle": {"version": "7.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.0", "ref": "2c920f73a217f30bd4a37833c91071f4d3dc1ecd"}, "files": ["config/packages/test/dama_doctrine_test_bundle.yaml"]}, "doctrine/annotations": {"version": "1.14", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "a2759dd6123694c8d901d0ec80006e044c2e6457"}, "files": ["config/routes/annotations.yaml"]}, "doctrine/deprecations": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "87424683adc81d7dc305eefec1fced883084aab9"}}, "doctrine/doctrine-bundle": {"version": "2.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.3", "ref": "117acb189cca1d761fd50fa47dceecff1ad9d266"}, "files": ["config/packages/doctrine.yaml", "config/packages/prod/doctrine.yaml", "config/packages/test/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-fixtures-bundle": {"version": "3.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "1f5514cfa15b947298df4d771e694e578d4c204d"}, "files": ["src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "google/apiclient": {"version": "2.14", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.10", "ref": "07a97ec434d43b7903c78069a04c92adb6442e52"}, "files": ["config/packages/google_apiclient.yaml"]}, "knplabs/knp-menu-bundle": {"version": "v3.2.0"}, "knplabs/knp-snappy-bundle": {"version": "1.9", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.5", "ref": "c81bdcf4a9d4e7b1959071457f9608631865d381"}, "files": ["config/packages/knp_snappy.yaml"]}, "lexik/jwt-authentication-bundle": {"version": "2.21", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.5", "ref": "e9481b233a11ef7e15fe055a2b21fd3ac1aa2bb7"}, "files": ["config/packages/lexik_jwt_authentication.yaml"]}, "nelmio/api-doc-bundle": {"version": "4.11", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.0", "ref": "c8e0c38e1a280ab9e37587a8fa32b251d5bc1c94"}, "files": ["config/packages/nelmio_api_doc.yaml", "config/routes/nelmio_api_doc.yaml"]}, "nelmio/cors-bundle": {"version": "2.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.5", "ref": "6bea22e6c564fba3a1391615cada1437d0bde39c"}, "files": ["config/packages/nelmio_cors.yaml"]}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "7364a21d87e658eb363c5020c072ecfdc12e2326"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "scheb/2fa-bundle": {"version": "5.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.0", "ref": "0a83961ef50ff91812b229a6f0caf28431d94aec"}, "files": ["config/packages/scheb_2fa.yaml", "config/routes/scheb_2fa.yaml"]}, "sensio/framework-extra-bundle": {"version": "6.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.2", "ref": "fb7e19da7f013d0d422fa9bce16f5c510e27609b"}, "files": ["config/packages/sensio_framework_extra.yaml"]}, "sonata-project/admin-bundle": {"version": "4.22", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.0", "ref": "0e5931df1732e3dccfba42a20853049e5e9db6ae"}, "files": ["config/packages/sonata_admin.yaml", "config/routes/sonata_admin.yaml", "src/Admin/.gitignore"]}, "sonata-project/block-bundle": {"version": "4.19", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.11", "ref": "b4edd2a1e6ac1827202f336cac2771cb529de542"}, "files": ["config/packages/sonata_block.yaml"]}, "sonata-project/core-bundle": {"version": "3.12", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.9", "ref": "2f69bd1995730b73a5211a9707622fb25a925df7"}, "files": ["config/packages/sonata_core.yaml"]}, "sonata-project/datagrid-bundle": {"version": "2.5.0"}, "sonata-project/doctrine-extensions": {"version": "1.18", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.8", "ref": "4ea4a4b6730f83239608d7d4c849533645c70169"}}, "sonata-project/doctrine-orm-admin-bundle": {"version": "4.9.1"}, "sonata-project/exporter": {"version": "2.14", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.4", "ref": "93d6df022ef1dc24bdfa8667ddd560bbde89a7cc"}}, "sonata-project/form-extensions": {"version": "1.18", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.4", "ref": "9c8a1e8ce2b1f215015ed16652c4ed18eb5867fd"}, "files": ["config/packages/sonata_form.yaml"]}, "sonata-project/twig-extensions": {"version": "1.12", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.2", "ref": "30dba2f9b719f21b497a6302f41aac07f9079e13"}}, "symfony/console": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.4", "ref": "fd5340d07d4c90504843b53da41525cf42e31f5c"}, "files": ["bin/console", "config/bootstrap.php"]}, "symfony/debug-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.1", "ref": "0ce7a032d344fb7b661cd25d31914cd703ad445b"}, "files": ["config/packages/dev/debug.yaml"]}, "symfony/flex": {"version": "1.21", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": [".env"]}, "symfony/framework-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.4", "ref": "24eb45d1355810154890460e6a05c0ca27318fe7"}, "files": ["config/bootstrap.php", "config/packages/cache.yaml", "config/packages/framework.yaml", "config/packages/test/framework.yaml", "config/preload.php", "config/routes/dev/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/google-mailer": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.4", "ref": "f8fd4ddb9b477510f8f4bce2b9c054ab428c0120"}}, "symfony/mailer": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "bedeb3d54aa5404e29811c44346a1151f846f741"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.39", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/monolog-bundle": {"version": "3.8", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.3", "ref": "2120e71a370db3a494b8afcc42d7cfb2cff6f910"}, "files": ["config/packages/dev/monolog.yaml", "config/packages/prod/deprecations.yaml", "config/packages/prod/monolog.yaml", "config/packages/test/monolog.yaml"]}, "symfony/phpunit-bridge": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "221b9cc0a623f567e83ccd61a8ac82a9a91e2b5b"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/routing": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.2", "ref": "683dcb08707ba8d41b7e34adb0344bfd68d248a7"}, "files": ["config/packages/prod/routing.yaml", "config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.4", "ref": "7b4408dc203049666fe23fabed23cbadc6d8440f"}, "files": ["config/packages/security.yaml"]}, "symfony/swiftmailer-bundle": {"version": "3.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.5", "ref": "f0b2fccdca2dfd97dc2fd5ad216d5e27c4f895ac"}, "files": ["config/packages/dev/swiftmailer.yaml", "config/packages/swiftmailer.yaml", "config/packages/test/swiftmailer.yaml"]}, "symfony/translation": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.3", "ref": "2ad9d2545bce8ca1a863e50e92141f0b9d87ffcd"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/twig-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.4", "ref": "73baff3f7b3cea12a73812a7cfd2c0924a9e250f"}, "files": ["config/packages/test/twig.yaml", "config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "3eb8df139ec05414489d55b97603c5f6ca0c44cb"}, "files": ["config/packages/test/validator.yaml", "config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.3", "ref": "6bdfa1a95f6b2e677ab985cd1af2eae35d62e0f6"}, "files": ["config/packages/dev/web_profiler.yaml", "config/packages/test/web_profiler.yaml", "config/routes/dev/web_profiler.yaml"]}, "symfonycasts/reset-password-bundle": {"version": "1.14", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "97c1627c0384534997ae1047b93be517ca16de43"}, "files": ["config/packages/reset_password.yaml"]}, "twig/extensions": {"version": "1.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "a86723ee8d8b2f9437c8ce60a5546a1c267da5ed"}, "files": ["config/packages/twig_extensions.yaml"]}, "twig/extra-bundle": {"version": "v3.7.0"}, "vich/uploader-bundle": {"version": "1.23", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.13", "ref": "1b3064c2f6b255c2bc2f56461aaeb76b11e07e36"}, "files": ["config/packages/vich_uploader.yaml"]}}