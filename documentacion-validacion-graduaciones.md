# Documentación: Validación de Graduaciones en Backend

## Problema Identificado

Se detectó que el sistema estaba generando órdenes de laboratorio (graduaciones) para todos los productos en una venta, cuando solo debería generarlas para productos específicos:

1. Productos de tipo armazón (tipoproducto = '1') con categoría que contenga 'armazón' o 'frame'
2. Productos con código de barras específico ('111389607921')

Aunque existía validación en el frontend, faltaba implementar esta misma validación en el backend, lo que permitía la creación incorrecta de órdenes de laboratorio para productos que no deberían tenerlas, como servicios (tipoproducto = '2').

## Solución Implementada

Se ha añadido validación en el backend para asegurar que las órdenes de laboratorio solo se creen para productos que cumplan con los criterios especificados. Esta validación se ha implementado en tres lugares clave:

1. En el procesamiento de graduaciones con el nuevo enfoque (idRow)
2. En el procesamiento de graduaciones con el enfoque anterior (para compatibilidad)
3. En el método `agregarGraduacion` que se llama cuando un usuario añade una graduación a través de la interfaz

## Cambios Específicos

### 1. Validación en el Procesamiento de Graduaciones (Nuevo Enfoque)

```php
// Verificar si el producto cumple con los criterios para tener graduación
$isValidForGraduation = false;

// Obtener información del producto
$producto = $Stock->getProductoIdproducto();
$tipoproducto = $producto->getTipoproducto();
$categoria = $producto->getCategoriaIdcategoria();
$barcode = $Stock->getCodigobarras();

// Validar según los criterios
if ($tipoproducto === '1' && $categoria) {
    $categoriaNombre = $categoria->getNombre();
    if (preg_match('/armaz[oó]n|frame/i', $categoriaNombre)) {
        $isValidForGraduation = true;
    }
}

if ($barcode === '111389607921') {
    $isValidForGraduation = true;
}

// Si no cumple con los criterios, no crear orden de laboratorio
if (!$isValidForGraduation) {
    continue;
}
```

### 2. Validación en el Procesamiento de Graduaciones (Enfoque Anterior)

Se implementó una validación similar en el código que maneja el enfoque anterior, asegurando que los productos de servicio o que no sean armazones no generen órdenes de laboratorio.

### 3. Validación en el Método agregarGraduacion

```php
// Verificar si el producto cumple con los criterios para tener graduación
$isValidForGraduation = false;

// Obtener información del producto
$tipoproducto = $producto->getTipoproducto();
$barcode = $stock->getCodigobarras();

// Validar según los criterios
if ($tipoproducto === '1') {
    $categoriaNombre = $categoria->getNombre();
    if (preg_match('/armaz[oó]n|frame/i', $categoriaNombre)) {
        $isValidForGraduation = true;
    }
}

if ($barcode === '111389607921') {
    $isValidForGraduation = true;
}

// Si no cumple con los criterios, no permitir la creación de orden de laboratorio
if (!$isValidForGraduation) {
    throw new \Exception('La graduación solo puede aplicarse a productos tipo armazón o con código de barras específico');
}
```

## Impacto Esperado

Con estos cambios, el sistema ahora:

1. **Solo genera órdenes de laboratorio para productos adecuados**: Armazones (tipoproducto = '1') con categoría que contenga 'armazón' o 'frame', o productos con código de barras específico.

2. **Evita la creación de órdenes innecesarias**: Los productos de servicio (tipoproducto = '2') y otros productos que no cumplan los criterios no generarán órdenes de laboratorio.

3. **Mantiene consistencia entre frontend y backend**: La validación en el backend ahora coincide con la que ya existía en el frontend.

4. **Mejora la integridad de los datos**: Evita la creación de registros innecesarios en la base de datos.

## Notas Adicionales

Esta implementación mantiene la compatibilidad con el código existente y no afecta a otras funcionalidades del sistema. La validación se ha implementado de manera que sea consistente con la lógica ya existente en el frontend.