# config/validator/validation.yaml
App\Entity\Usuario:
    constraints:
        - Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity:
            fields: email
            message: 'Ya hay una cuenta registrada con este correo electrónico'

App\Entity\Graduacion:
    properties:
        cb:
            - Expression:
                expression: "not (this.getTipolente() == '2' and this.getCb() == null )"
                message: "Ingresa una curva base válida"

        diam:
            - Expression:
                expression: "not (this.getTipolente() == '2' and this.getDiam() == null )"
                message: "Ingresa un diametro válido"

        tipolentecontacto:
            - Expression:
                expression: "not (this.getTipolente() == '2' and this.getTipolentecontacto() == null )"
                message: "Ingresa un tipo de lente de contacto válido"
        
        materialIdmaterial:
            - Expression:
                expression: "not (this.getTipolente() == '1' and this.getMaterialIdmaterial() == null )"
                message: "Ingresa un material válido"

        disenolenteIddisenolente:
            - Expression:
                expression: "not (this.getTipolente() == '1' and this.getDisenolenteIddisenolente() == null )"
                message: "Ingresa un diseño válido"

        tratamientoIdtratamiento:
            - Expression:
                expression: "not (this.getTipolente() == '1' and this.getTratamientoIdtratamiento() == null )"
                message: "Ingresa un tratamiento válido"