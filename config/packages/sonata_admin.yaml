sonata_admin:
  title: 'Optimo'
  title_logo: 'img/Mesa de trabajo 21.png'
  options:
    html5_validate: true
    sort_admins: false

  dashboard:
    groups:
      Products: [ ]
      Orders: [ ]
      References: [ ]

      Almacen:
        label: Almacen
        label_catalogue: Inventario
        icon: '<i class="fa-solid fa-shop-lock"></i>'
        items:
          - admin.ventaalmacen
          - admin.stockalmacen
          - admin.producto
          - admin.reglaabastecimiento
          - admin.traspasoalmacen
          - admin.productotraspasoalmacen
          - admin.cargainventariolog
          - admin.ordensalida
          - route: seleccionar-productos
            label: Traspaso de productos
          - route: app_merma
            label: Merma
          - route: app_reporte_flujo_expediente
            label: Reporte Graduaciones
        roles: [ 'ROLE_SUPER_ADMIN' ]

      Punto de venta Vendedor:
        label: Punto de venta
        label_catalogue: Punto de venta
        icon: '<i class="fa-solid fa-hand-holding-dollar"></i>'
        items:
          - admin.venta
          - admin.stock
          - route: nueva-venta
            label: Nueva venta
        roles: [ 'ROLE_VENDEDOR' ]

      Punto de venta:
        label: Punto de venta
        label_catalogue: Punto de venta
        icon: '<i class="fa-solid fa-hand-holding-dollar"></i>'
        items:
          - admin.venta
          - admin.stockventa
          - admin.pago
          - admin.cupon
          - admin.cuponmarca
          - admin.ventacupon
          - admin.tipoventa
          - admin.sellreference
        roles: [ 'ROLE_SUPER_ADMIN' ]

      Administración:
        label: Administración
        label_catalogue: Administración
        icon: '<i class="fa-solid fa-user-tie"></i>'
        items:
          - admin.venta
          - admin.stockventa
          - admin.pago
          - admin.sellreference
          - route: app_comisiones
            label: Reporte de Comisiones
          - route: app_reporte_flujo_expediente
            label: Reporte Graduaciones
        roles: [ 'ROLE_SUPER_ADMIN' ]

      FlujosExpedientes:
        label: Flujos de Expedientes
        label_catalogue: Flujos de Expedientes
        icon: '<i class="fa fa-paperclip" aria-hidden="true"></i>'
        items:
          - admin.ordenlab
          - admin.flujoexpediente
          - route: app_laboratory_order
            label: Configuración ordenes de laboratorio
        roles: [ 'ROLE_VENDEDOR' ]

      LabDashboard:
        label: Laboratorio
        label_catalogue: Facturación
        icon: '<i class="fa fa-flask" aria-hidden="true"></i>'
        items:
          - route: app_lab_dashboarda
            label: Dashboard
        roles: [ 'ROLE_LAB', 'ROLE_CALIDAD' ]

      Catalogos:
        label: Catálogos
        label_catalogue: Catálogos
        icon: '<i class="fa fa-book" aria-hidden="true"></i>'
        items:
          - admin.producto
          - admin.sucursal
          - admin.proveedor
          - admin.proveedorcontacto
          - admin.proveedoremail
          - admin.proveedortelefono
          - admin.medida
          - admin.unidad_medida
          - admin.clase
          - admin.categoria
          - admin.marca
          - admin.empresacliente
          - admin.tratamiento
          - admin.tipobisel
          - admin.disenolentes
          - admin.material
          - admin.categoriadocumentos
          - admin.categoriaanuncio
        roles: [ 'ROLE_SUPER_ADMIN','ROLE_ADMIN' ]

      clientes:
        label: Clientes
        label_catalogue: Clientes
        icon: '<i class="fa-solid fa-users" aria-hidden="true"></i>'
        items:
          - admin.cliente
          - admin.unidad
          - route: subir-clientes-excel
            label: Subir Excel Clientes
        roles: [ 'ROLE_SUPER_ADMIN','ROLE_ADMIN','ROLE_VENDEDOR' ]

      Configuracion:
        label: Configuración
        label_catalogue: Configuración
        icon: '<i class="fa fa-cog" aria-hidden="true"></i>'
        items:
          - admin.usuario
          - admin.empresa
          - admin.usuarioempresapermiso
          - route: config_entry_points
            label: Accessos
        roles: [ 'ROLE_SUPER_ADMIN' ]

      Reportes:
        label: Reportes
        label_catalogue: Reportes
        icon: '<i class="fa fa-line-chart" aria-hidden="true"></i>'
        items:
        
          - route: app_oficial
            label: Dashboard 

          - route: almacen-stock
            label: Reporte de inventario

          - route: app_comisiones
            label: Reporte de Comisiones

          - route: reporte-producto
            label: Reporte de productos vendidos

          - route: app_reporte_ventas
            label: Reportes de ventas

          - route: app_referencias
            label: Reportes de Referencias

          - route: armazones-vendidos-con-stock
            label: Errores de sistema 1

          - route: app_reporte_rezagados
            label: Reporte de Rezagados
        roles: [ 'ROLE_SUPER_ADMIN', 'ROLE_SUPERVISOR' ]

      Informes:
        label: Informes
        label_catalogue: Informes
        icon: '<i class="fa fa-line-chart" aria-hidden="true"></i>'
        items:
          - route: corte_caja
            label: Corte de caja
          - route: informe-ventas
            label: Informe de ventas
        roles: [ 'ROLE_VENDEDOR' ]

      Calendarios:
        label: Calendarios
        label_catalogue: Facturación
        icon: '<i class="fa fa-calendar" aria-hidden="true"></i>'
        items:
          - route: app_dates_calendar
            label: Citas
        roles: [ 'ROLE_VENDEDOR' ]

      Envios:
        label: Envíos
        label_catalogue: Envios
        icon: '<i class="fa-solid fa-box"></i>'
        items:
          - route: app_shipment
            label: Configuración de envíos
            roles: [ 'ROLE_VENDEDOR' ]
          - route: shipment-get-delivery-dashboard
            label: Dashboard de envíos
            roles: [ 'ROLE_VENDEDOR' ]
        roles: [ 'ROLE_VENDEDOR' ]

      autorizaciones:
        label: 'Autorizaciones'
        label_catalogue: 'Autorizaciones'
        icon: '<i class="fa-solid fa-check-circle"></i>'
        items:
          - route: app_dashboard_autorizaciones_dash
            label: Dashboard de Autorizaciones
          - route: 'app_factura_autorizacion'
            route_params:
              stage: 1
            label: 'Mandar autorizaciones'
            roles: [ 'ROLE_VENDEDOR' ]
          - route: 'app_factura_autorizacion'
            route_params:
              stage: 2
            label: 'Recibir autorizaciones'
            roles: [ 'ROLE_ADMIN' ]
          - route: 'app_factura_autorizacion'
            route_params:
              stage: 3
            label: 'Mandar a facturar'
            roles: [ 'ROLE_ADMIN' ]
          - route: 'app_factura_autorizacion'
            route_params:
              stage: 4
            label: 'Facturar autorizaciones'
            roles: [ 'ROLE_SUPER_ADMIN' ]
          - admin.ventagroup
        roles: [ 'ROLE_VENDEDOR', 'ROLE_MENSAJERO' ]

      Facturacion:
        label: Facturación
        label_catalogue: Facturación
        icon: '<i class="fa fa-file-text" aria-hidden="true"></i>'
        items:
          - admin.ventafactura
          - admin.clientefacturadatos
          - route: app_reporte_facturacion
            label: Reporte de Ventas Facturadas
        roles: [ 'ROLE_ADMIN' ]

      Carga masiva:
        label: Carga masiva
        label_catalogue: Carga masiva
        icon: '<i class="fa fa-refresh" aria-hidden="true"></i>'
        items:
          - route: r_Inventario_cargaMasivaIndex
            label: Cargar Inventario
          - route: cargar_inventario
            label: Cargar los productos (LEGACY)
          - route: cargar_costos
            label: Cargar los costos (LEGACY)
          - route: r_Inventario_carga_inventario_bodega
            label: Cargar el inventario (LEGACY)
          - route: subir-excel
            label: Cargar  los datos de UAM
        roles: [ 'ROLE_SUPER_ADMIN' ]

      Utilidades:
        label: Utilidades
        label_catalogue: Utilidades
        icon: '<i class="fa fa-wrench" aria-hidden="true"></i>'
        items:
          - admin.documentos
          - admin.anuncios
        roles: [ 'ROLE_ADMIN' ]

      Cobranza:
        label: Cobranza
        label_catalogue: Cobranza
        icon: '<i class="fa fa-credit-card" aria-hidden="true"></i>'
        items:
          - route: app_cobranza
            label: Ventas a Crédito
        roles: [ 'ROLE_SUPER_ADMIN','ROLE_EJECUTIVO_COBRANZA' ]

      Compras:
        label: Compras
        label_catalogue: Cobranza
        icon: '<i class="fa-solid fa-cart-shopping" aria-hidden="true"></i>'
        items:
          - route: app_proveedor
            label: Registro de Proveedores
          - route: app_orden_compra
            label: Creador de Orden de compra
          - route: app_administrador_orden_compra
            label: Administrador de Ordenes de Compra
        roles: [ 'ROLE_SUPER_ADMIN','ROLE_EJECUTIVO_COBRANZA' ]

      Marketing:
        label: Marketing
        label_catalogue: Marketing
        icon: '<i class="fa-solid fa-bullhorn" aria-hidden="true"></i>'
        items:
          - route: app_marketing_campaign
            label: Campaña
          - route: app_messages
            label: Mensajes
        roles: [ 'ROLE_SUPER_ADMIN' ]


  templates:
    dashboard: 'admin/dashboard.html.twig'
    layout: 'admin/layout.html.twig'
    tab_menu_template: 'admin/menu.html.twig'
    base_list_field: 'admin/base_list_field.html.twig'
    #list: 'admin/base_list.html.twig'
    user_block: 'admin/user_block.html.twig'

sonata_block:
  blocks:
    sonata.admin.block.admin_list:
      contexts: [ admin ]