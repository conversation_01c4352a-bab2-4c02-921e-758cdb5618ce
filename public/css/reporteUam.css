/* Estilos para el contenedor */
.container {
    margin-top: 20px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

/* Estilos básicos para la tabla */
table#reporteUAM {
    width: 100%;
    border-collapse: collapse;
    text-align: left;
    color: #333;
}

/* Estilos para las cabeceras de la tabla */
table#reporteUAM thead th {
    background-color: #4CAF50;
    color: white;
    padding: 10px;
    border-bottom: 2px solid #ddd;
}

/* Estilos para las celdas de la tabla */
table#reporteUAM tbody td {
    padding: 8px;
    border-bottom: 1px solid #ddd;
}

/* Estilos para las filas al pasar el mouse */
table#reporteUAM tbody tr:hover {
    background-color: #f5f5f5;
}

/* Estilos para el texto de las celdas para que no se rompa en varias líneas */
table#reporteUAM td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

/* Ajustes para dispositivos móviles */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 5px;
    }

    table#reporteUAM thead {
        display: none;
    }

    table#reporteUAM, table#reporteUAM tbody, table#reporteUAM tr, table#reporteUAM td {
        display: block;
        width: 100%;
    }

    table#reporteUAM tr {
        margin-bottom: 15px;
    }

    table#reporteUAM td {
        text-align: right;
        padding-left: 50%;
        text-align: right;
        position: relative;
    }

    table#reporteUAM td:before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        width: 50%;
        padding-left: 15px;
        font-weight: bold;
        text-align: left;
    }
}
