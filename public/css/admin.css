.input-group {
  width: 100%;
}

.input-group.input-group-addon {
  width: 10%;
}

td.sonata-ba-list-field.sonata-ba-list-field-currency, td.sonata-ba-list-field.sonata-ba-list-field-percent, td.sonata-ba-list-field.sonata-ba-list-field-integer {
  text-align: center;
}

.sonata-ba-field input {
  text-align: center;
}

.select2-container .select2-choice > .select2-chosen {
  text-align: center;
}

.select2-results .select2-result-label {
  text-align: center;
}

.field-actions {
  text-align: center;
}

.sonata-ba-form-actions {
  text-align: right;
}

.btn-group-vertical>.btn-group:after, .btn-group-vertical>.btn-group:before, .btn-toolbar:after, .btn-toolbar:before, .clearfix:after, .clearfix:before, .container-fluid:after, .container-fluid:before, .container:after, .container:before, .dl-horizontal dd:after, .dl-horizontal dd:before, .form-horizontal .form-group:after, .form-horizontal .form-group:before, .modal-footer:after, .modal-footer:before, .modal-header:after, .modal-header:before, .nav:after, .nav:before, .navbar-collapse:after, .navbar-collapse:before, .navbar-header:after, .navbar-header:before, .navbar:after, .navbar:before, .pager:after, .pager:before, .panel-body:after, .panel-body:before, .row:after, .row:before {
  content: none;
}

.nav.navbar-nav {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
}

.fa-caret-down:before {
  content: none !important;
}

.navbar-top-links li {
  display: flex;
}

.breadcrumb>li+li:before {
  content: "/\00a0";
  padding: 0 5px;
  display: flex;
  padding: 14px;
}

.logo img {
  display: inline;
  max-height: 100%;
  max-width: 190px;
  padding-bottom: 4px;
}

.skin-black.main-header.navbar>.sidebar-toggle {
  border-right: 1px solid #FFFFFF;
  color: #777777;
}

.skin-black.main-header.navbar.sidebar-toggle:hover {
  background: #FFFFFF;
  color: #115FD1;
}

.skin-black .main-header .navbar .nav>li>a {
  color: #777777;
}

.skin-black .main-header .navbar .nav>li>a:hover {
  color: #115FD1;
}

.skin-black .main-header .navbar .navbar-custom-menu .navbar-nav>li>a, .skin-black .main-header .navbar .navbar-right>li>a {
  border-left: 1px solid #eee;
  border-right-width: 0;
  color: #777777;
}

.skin-black .main-header .navbar .nav .open>a, .skin-black .main-header .navbar .nav .open>a:focus, .skin-black .main-header .navbar .nav .open>a:hover, .skin-black .main-header .navbar .nav>.active>a, .skin-black .main-header .navbar .nav>li>a:active, .skin-black .main-header .navbar .nav>li>a:focus, .skin-black .main-header .navbar .nav>li>a:hover {
  background: #FFFFFF;
  color: #115FD1;
}

.skin-black .left-side, .skin-black .main-sidebar, .skin-black .wrapper {
  background-color: #FFFFFF;
}

.skin-black .sidebar-menu>li.active>a {
  border-left-color: #115FD1;
}

.skin-black .sidebar-menu>li.active>a, .skin-black .sidebar-menu>li.menu-open>a, .skin-black .sidebar-menu>li:hover>a {
  background: #115FD1;
  color: #FFFFFF;
}

.skin-black .sidebar-menu .treeview-menu>li>a {
  color: #777777;
  white-space: initial;
}

.skin-black .sidebar-menu .treeview-menu>li>a:hover {
  color: #115FD1;
}

.skin-black .sidebar-menu .treeview-menu>li.active>a, .skin-black .sidebar-menu .treeview-menu>li>a:hover {
  color: #115FD1;
}

.skin-black .sidebar-menu>li>.treeview-menu {
  background: #FFFFFF;
  margin: 0 1px;
}

.skin-black .sidebar a {
  color: #777777;
}

.main-header .logo {
  display: block;
  float: left;
  font-family: Helvetica Neue,Helvetica,Arial,sans-serif;
  font-size: 20px;
  font-weight: 300;
  height: 60px;
  line-height: 50px;
  overflow: hidden;
  padding: 0 15px;
  text-align: center;
  transition: width .3s ease-in-out;
  width: 230px;
}

.sidebar {
  margin-top: 15px;
}

.input-group-addon {
  width: 30px !important;
  height: 30px !important;
  border: none;
}

.input-daterange.input-group.rango-tiempo {
  display: flex !important;
  flex-direction: row;
  align-items: center;
  flex-wrap: nowrap;
  align-content: center;
}

.input-sm.form-control {
  margin: 25px 0px 25px 0px;
}

.caret {
  display: none;
}

.navbar-nav .dropdown-menu {
  position: absolute;
}

.col-sm-9 {
  display: flex;
  flex-direction: column;
}

.btn-primary {
  background-color: #115FD1;
  border-color: #115FD1;
}

a.sonata-toggle-filter.sonata-ba-action {
  color: #115FD1;
}

.pagination>.active>a, .pagination>.active>a:focus, .pagination>.active>a:hover, .pagination>.active>span, .pagination>.active>span:focus, .pagination>.active>span:hover {
  background-color: #115FD1;
  border-color: #115FD1;
}

th {
  text-align: center;
}

.alert-info, .bg-aqua, .callout.callout-info, .label-info, .modal-info .modal-body {
  background-color: #777777!important;
}

.input-sm {
  height: 34px;
  border-radius: 10px;
}

.input-group-addon {
  width: 40px !important;
  min-width: 40px !important;
}

.text-center.negritas {
  font-weight: bold;
}

h4.mx-4 {
  text-align: center;
}

.titlefile {
  font-family:'JetBrains Mono' !important;
  font-size: 24px;
}

.row.file {
  margin-left: 0px;
  margin-right: 0px;
}

.ajax-file-upload {
  font-family:'Open Sans' !important;
  font-size: 13px !important;
  font-weight: 100 !important;
  vertical-align: middle !important;
  box-shadow: none !important;
  padding: 6px 10px 6px 10px !important;
  margin: 10px 10px 10px 10px !important;
  height: 32px !important;
}

.ajax-upload-dragdrop {
  width: 420px  !important;
  text-align: center  !important;
  padding: 10px 10px 10px 10px  !important;
}

.ajax-file-upload-red {
  border-radius: 4px;
  display: inline-block;
  font-family: 'Open Sans' !important;
  font-size: 13px;
  font-weight: normal;
  padding: 4px 15px;
  text-decoration: none;
  text-shadow: 0 1px 0 #777777  !important;
  cursor: pointer;
  vertical-align: top;
  margin: 5px 10px 5px 0px;
}

.ajax-file-upload-statusbar {
  border-radius: 0px !important;
  padding: 5px 5px 5px 15px;
}


.form-group.has-success label {
  color: #115FD1 !important;
}

.form-group.has-success .form-control, .form-group.has-success .input-group-addon {
  box-shadow: none;
}

div.card-header.ticket-encabezado {
  font-family: 'Courier Prime', monospace;
  font-size: 20px;
}

.col-md-6.text-center.ticket-subencabezado h3{
  font-family: 'Courier Prime', monospace;
  font-size: 18px;
}

.col-md-6.col-sm-6.col-xs-12.ticket-nombre h3{
  font-family: 'Courier Prime', monospace;
  font-size: 15px;
}

p.lead.ticket-parrafo {
  font-family: 'Courier Prime', monospace;
  font-size: 15px;
}

.col-md-12.ticket-titulo h2 {
  font-family: 'Courier Prime', monospace;
  font-size: 18px;
}

table.table.table-striped.ticket-table {
  font-family: 'Courier Prime', monospace;
  font-size: 12px;
}

.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - var(--bs-modal-margin) * 2);
  justify-content: center !important;
}

.modal-content {
  width: 100% !important;
}

.nav-item.active {
  display: flex;
  border-left: 3px solid transparent;
  padding: 12px 5px 12px 15px;
}
.card-header.stock-encabezado {
  font-family: 'JetBrains Mono', monospace !important;
  text-align: center !important;
  font-weight: 600;
  font-size: 14px !important;
}

.col-md-6.stock-subencabezado h3{
  font-family: 'Open Sans', sans-serif !important;
  font-size: 17px !important;
}

.col-md-4.stock-subencabezado h3{
  font-family: 'Open Sans', sans-serif !important;
  font-size: 17px !important;
}

.row {
  margin-left: 0px !important;
  margin-right: 0px !important;
}

table.table.table-striped.stock-table {
  font-family: 'Open Sans', sans-serif !important;
  font-size: 12px;
}

h2.col-md-12 .text-center .stock-subencabezado  {
  font-family: 'JetBrains Mono', monospace !important;
  font-size: 17px !important;
}

.card-header {
  font-family: 'JetBrains Mono', monospace !important;
  text-align: center !important;
}

label.form-label {
  font-family: 'Open Sans', sans-serif !important;
}

h3.card-title {
  font-family: 'Open Sans', sans-serif !important;
  font-size: 17px;
  color: #1A2649;
}

button.btn.btn-warning {
  font-family: 'Open Sans', sans-serif !important;
}

.col-md-12.boton {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  align-items: center;
  justify-content: center;
}

.col-md-4.tipoc {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  align-items: center;
  justify-content: flex-end;
}

.col-md-3.tipoc {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  align-items: center;
  justify-content: flex-end;
}

.col-md-2.tipoc {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  align-items: center;
  justify-content: flex-end;
}

.col-md-2.tipoca {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  align-items: center;
  justify-content: center;
}

.col-md-1.tipoc {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  align-items: center;
  justify-content: flex-end;
}

th {
  font-size: 14px;
}

.subtable {
  font-size: 18px;
  text-align: center;
}

.subtable2 {
  font-size: 18px;
  text-align:start;
}

.col-md-3.boton2 {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  align-items: center;
  justify-content: flex-end;
}
.col-md-2 {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  align-items: center;
  justify-content: center;
}

.card-title {
  font-family: 'JetBrains Mono', monospace !important;
  font-weight: 600;
}

strong {
  font-weight: 400 !important;
}

#msj-tipo-venta {
  font-size: 13px;
  font-family: 'JetBrains Mono', monospace !important;
  text-align: justify;
  font-weight: 500 !important;
}

.div-title {
  font-size: 15px;
  font-family: 'JetBrains Mono', monospace !important;
  font-weight: bold;
}

.titulotext {
  font-family: 'Open Sans', sans-serif !important;
}

.subtext {
  font-family: 'Open Sans', sans-serif !important;
  font-weight: none;
  font-size: 12px;
  margin-top: 9px;
}



.modal-title.fs-5 {
  font-size: 15px !important;
  font-family: 'JetBrains Mono', monospace !important;
  font-weight: bold;
}

.form-label text {
  font-family: 'Open Sans', sans-serif !important;
  font-weight: none;
  font-size: 12px;
}

.col-md-2.buscar {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  align-items: center;
  justify-content: flex-end;
}

.text-start {
  font-family: 'Open Sans', sans-serif !important;
  font-weight: none;
  font-size: 13px;
}

h5.card-title.reporte {
  font-size: 16px !important;
  font-family: 'JetBrains Mono', monospace !important;
  font-weight: bold;
  margin: 8px 0px 8px 0px;
}

button.btn.btn-primary.reporte {
  margin: 8px 0px 8px 0px;
}

input.form-control.reset.reporte {
  margin: 0px 8px 0px 8px;
}

.select2.select2-container.select2-container--bootstrap {
  margin: 0 !important;
}

.lab.reporte {
  font-family: 'Open Sans', sans-serif !important;
  font-size: 12px;
  margin: 8px 0px 8px 0px;
}

.check {
  font-family: 'Open Sans', sans-serif !important;
  font-size: 11px;
  margin: 5px 10px 5px 0px !important;
  text-transform: uppercase;
}

.filter-title {
  width: 100%;
  font-size: 14px;
  text-align: justify;
  margin-top: 10px;
}

.col-md-12.opciones {
  display: flex;
  align-content: center;
  align-items: center;
  justify-content: center;
}

.col-md-9.usuario {
  display: flex;
  align-content: center;
  align-items: center;
}

.col-md-6.opciones{
  display: flex;
  align-content: center;
  align-items: center;
  justify-content: center;
}

.input-sm.form-control {
  margin: 0px 0px 0px 0px;
}

.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  margin-left: 10px !important;
}

.col-md-12.filter {
  margin-left: 16px !important;
}

.miLista {
  width: 172px; 
  height: 200px;
  overflow: auto;
  border: 1px solid #bcbcbc; 
  background-color: white;
}

.cont {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  align-content: center;
  
}

.conte {
  display: flex;
  justify-content: space-around;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
}

.contenedor {
  display: flex;
  justify-content: space-around; 
}

fieldset, legend {
  border: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.titulos-reportes {
  font-family: 'JetBrains Mono', monospace !important;
  font-size: 25px;
  text-transform: uppercase;
}

@media (max-width: 767px) {
  a.logo { 
    background-color: #FFFFFF !important;
  }

  .main-header .logo {
    height: 45px;
    width: 290px;
  }

  strong {
    text-align: center;
  }

  .col-md-12.opciones {
    display: flex;
    flex-direction: column;
  }

  input.form-control.reset.reporte {
    margin: 20px 8px 20px 8px;
  }

  .check {
    font-size: 10px;
  }

  .filter-title {
    font-size: 12px;
    
  }

  .btn.btn-success.mt-5.btn-nuevo-cliente {
    font-size: 8px;
  }

  .btn.btn-info.mt-5.btn-nuevo-cliente {
    font-size: 8px;
  }

  .sorting_1 {
    font-size: 12px;
  }

  h2 {
    font-size: 20px;
  }

  h5.card-title.reporte {
    font-size: 16px !important;
    text-align: center;
  }

  h3.card-title {
    font-size: 16px !important;
    text-align: center;
  }

  .col-md-3.button-buscar {
    display: flex;
    justify-content: center;
  }

  .col-md-6.opciones.cent {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }
  .col-md-12.filtro {
    padding-top: 13px;
  }

  table.dataTable tbody td {
    padding: 8px 2px;
  }
}

.bg-gradient-primary.shadow-primary.border-radius-lg.py-3.pe-1 {
  background-image: linear-gradient(186deg, #ffffff 0%, #ffffff 100%) !important;
}

.text-white.font-weight-bolder.text-center.mt-2.mb-0 {
  color: #777777 !important;
  font-family: 'Open Sans', sans-serif !important;
  font-weight: bold !important;
  margin-top: 0px !important;
}

.text-white.font-weight-bolder.text-center.mt-2.mb-0.title {
  margin-top: 35px !important;
}

.row {
  --bs-gutter-x: 1.5rem !important;
  --bs-gutter-y: 0 !important;
  display: flex !important;
  flex-wrap: wrap !important;
  margin-top: calc(-1 * var(--bs-gutter-y)) !important;
  margin-right: calc(-.5 * var(--bs-gutter-x)) !important;
  margin-left: calc(-.5 * var(--bs-gutter-x)) !important;
}

#formulario {
  display: flex !important;
  justify-content: center !important;
  width: 100% !important;
  flex-direction: column !important;
}

.form-control {
  padding: 0.5rem  0.5rem !important;
}

.form-label.required {
  padding-left: 0px;
  font-family: 'Open Sans', sans-serif !important;
  font-weight: none;
  font-size: 12px;
  margin-top: 10px;
}

.col-md-12.form {
  margin-top: 15px !important;
}

.col-md-6.form {
  margin-top: 15px !important;
}

#clientefacturadatos_Enviar {
  margin-top: 35px !important;
  background-color: #616160 !important;
  color: #FFFFFF;
  font-family: 'Open Sans', sans-serif !important;
}

.form-label.form {
  margin-top: 20px !important;
}

.d-none {
  display: none !important;
}

#formulario {
  display: flex;
  justify-content: center;
  width: 75%;
  flex-direction: column;
}

#formularioClienteFactura {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.required {
  padding: 10px;
}

#my-3 {
  width: 85%;
}

#formularioClienteFactura {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

#clientefacturadatos_brochure {
  padding:7px; 
}

.logo {
  display: flex;
  padding: 15px;
  justify-content: space-around;
}

.clientefacturadatos_brochure {
  display: flex;
}

.selecArchivo {
  display: flex;
  flex-direction: column-reverse;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
}

#clientefacturadatos_brochure {
  display: flex;
  justify-content: center;
  align-items: center;
}

.clientefacturadatos_brochure {
  display: list-item;
}

#msj-tipo-venta {
  white-space: pre-line;
  height: 150px;
  overflow-y: scroll;
}

.logo-empresa {
  margin-top: 40px;
  margin-bottom: 11px;
}

.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
  margin-top: 60px;
}