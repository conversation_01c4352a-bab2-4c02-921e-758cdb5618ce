.card {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
    transition: 0.3s;
    border-radius: 5px; /* Bordes redondeados para la tarjeta */
}

.card:hover {
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2);
}

.card .text-center {
    padding: 1rem;
}

.card .btn-info {
    border: 0;
    background-color: #17a2b8;
    color: white;
}

.card .btn-info:hover {
    background-color: #138496;
    color: white;
}

.form-control.border-warning {
    border-width: 2px; /* Hace que el borde sea un poco más grueso para destacar */
}

/* Mejora el espaciado y la visualización del textarea */
textarea.form-control {
    min-height: 150px; /* Ajusta esto según tus necesidades */
}

/* Ajustes específicos para móviles y tablets */
@media (max-width: 768px) {
    .card .text-center {
        padding: 9px;
    }
}
