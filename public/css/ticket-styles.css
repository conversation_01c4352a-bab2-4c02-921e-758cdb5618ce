/* Ticket Styles - Based on design specifications */

/* Color Palette */
:root {
  --primary: #124DDE;
  --primary-light: rgba(18, 77, 222, 0.05);
  --secondary: #28C76F;
  --warning: #FFA726;
  --warning-light: #FFE0B2;
  --gray-900: #1E1E1E;
  --gray-200: #F5F6FA;
  --white: #FFFFFF;
  --border-color: #E0E0E0;
}

/* Typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&family=Roboto+Mono&display=swap');

body {
  background-color: var(--gray-200);
  color: var(--gray-900);
  font-family: 'Inter', sans-serif;
  font-size: 14px;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
}

h1 {
  font-size: 22px;
}

h2 {
  font-size: 20px;
}

h3 {
  font-size: 18px;
}

.monospace {
  font-family: 'Roboto Mono', monospace;
  font-size: 14px;
}

/* Layout */
.ticket-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  background-color: var(--white);
}

/* Header */
.ticket-header {
  display: grid;
  grid-template-columns: 1fr 1fr;
  background-color: var(--white);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 16px;
  margin-bottom: 24px;
}

/* Payment Status Card */
.payment-status-card {
  background-color: var(--primary-light);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
}

.payment-status-header {
  background-color: var(--primary);
  color: var(--white);
  padding: 12px 16px;
  border-radius: 12px 12px 0 0;
}

/* Badges */
.badge-pending {
  background-color: rgba(255, 167, 38, 0.1);
  color: var(--warning);
  padding: 4px 8px;
  border-radius: 4px;
}

.badge-paid {
  background-color: rgba(40, 199, 111, 0.1);
  color: var(--secondary);
  padding: 4px 8px;
  border-radius: 4px;
}

/* Metadata */
.metadata-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 24px;
}

.metadata-label {
  color: #666;
  text-transform: uppercase;
  font-size: 12px;
  margin-bottom: 4px;
}

.metadata-value {
  color: var(--gray-900);
  font-size: 14px;
}

/* Products Table */
.products-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 24px;
}

.products-table thead {
  position: sticky;
  top: 0;
  background-color: var(--gray-200);
}

.products-table th {
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
}

.products-table td {
  padding: 12px 16px;
  border-top: 1px solid var(--border-color);
}

.products-table tbody tr:nth-child(even) {
  background-color: #FAFAFA;
}

.products-table tbody tr:nth-child(odd) {
  background-color: var(--white);
}

.products-table .price {
  text-align: right;
  font-family: 'Roboto Mono', monospace;
}

.subtotals-card {
  background-color: var(--gray-900);
  color: var(--white);
  padding: 16px;
  border-radius: 0 0 12px 12px;
}

/* No Payments Alert */
.no-payments-alert {
  background-color: var(--warning-light);
  color: var(--warning);
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
}

.no-payments-alert i {
  font-size: 20px;
  margin-right: 12px;
}

.alert-close {
  margin-left: auto;
  cursor: pointer;
  font-size: 16px;
}

/* Actions Bar */
.actions-bar {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 0;
  position: sticky;
  bottom: 0;
  background-color: var(--white);
  border-top: 1px solid var(--border-color);
}

.btn {
  padding: 0 24px;
  height: 40px;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn:active {
  transform: scale(0.97);
}

.btn-primary {
  background-color: var(--primary);
  color: var(--white);
  border: none;
}

.btn-outline {
  background-color: transparent;
  color: var(--gray-900);
  border: 1px solid var(--border-color);
}

.btn-cyan {
  background-color: #00CFE8;
  color: var(--white);
  border: none;
}

/* Loader */
.progress-bar {
  width: 100%;
  height: 4px;
  background-color: var(--gray-200);
  overflow: hidden;
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 30%;
  height: 100%;
  background-color: var(--primary);
  animation: progress 1.5s infinite ease-in-out;
}

@keyframes progress {
  0% {
    left: -30%;
  }
  100% {
    left: 100%;
  }
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .ticket-header,
  .metadata-grid {
    grid-template-columns: 1fr;
  }
  
  .actions-bar {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
}

/* Animations */
.fade-in {
  animation: fadeIn 0.15s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.98);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Table interactions */
.products-table th {
  cursor: pointer;
}

.products-table th:hover {
  text-decoration: underline;
}

/* Card hover effects */
.card {
  transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}