.content-wrapper {
    background-color: #0D143D;
}
.content-header {
    display: none;
}
.content {
    padding: 0;
}
.card.dashboard {
    border: 0px solid transparent;
    background-color: transparent;
}
.dashboard-title {
    color: #FFFFFF;
    font-family: "Lato", sans-serif !important;
    font-weight: 700;
    font-size: 25px;
    text-align: center;
    margin: 35px;
}
.dashboard-subtitle {
    color: #116AEC;
    font-family: "Lato", sans-serif !important;
    font-weight: 700;
    font-size: 18px;
    text-align: center;
    margin: 0;
}
.laboratorio-colum {
    background-color: white;
    border-radius: 20px;
    padding: 20px 0px;
    margin: 10px;
    width: 15%;
}
.laboratorio-columna {
    background-color: #116AEC;
    border-radius: 20px;
    padding: 20px 0px;
    margin: 10px;
    width: 15%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.dashboard-text {
    color: #FFFFFF;
    font-family: "<PERSON>to", sans-serif !important;
    font-weight: 400;
    font-size: 15px;
    text-align: center;
}
.btn-detail {
    background-color: #FFE600;
    border-radius: 20px;
    font-family: "Lato", sans-serif !important;
    font-weight: 400;
    padding: 5px 10px;
    width: 60%;
    font-size: 14px;
}
hr {
    border: 0 !important;
    height: 3px !important;
    background-color: #FFFFFF !important;
    margin: 0 !important;
    width: 100%;
}
.div-con-borde-derecho {
    width: 200px;
    height: 100%;
    border-right: 3px solid #FFFFFF;
    padding: 10px;
    margin: 0 20px;
}
.modal-title {
    color: #116AEC;
    font-family: "Lato", sans-serif !important;
    font-weight: 700;
    font-size: 25px;
}
.icon-orden {
    width: 45px;
    margin: 15px;
}
.scrollable-table {
    max-height: 600px; /* Ajusta esta altura según tus necesidades */
    overflow-y: auto;
}
