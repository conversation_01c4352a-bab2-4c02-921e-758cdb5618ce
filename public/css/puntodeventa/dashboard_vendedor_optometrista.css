.content-wrapper {
    background-color: #0D143D;
}
.content-header {
    display: none;
}
.content {
    padding: 0;
}
.card.dashboard {
    border: 0px solid transparent;
    background-color: transparent;
}
.dashboard-card {
    margin: 10px 0px;
}
.card-header.dashboard {
    color: #116AEC;
    background-color: #FFFFFF;
    font-family: "Lato", sans-serif !important;
    font-weight: 700;
    font-size: 25px;
    border-radius: 15px 15px 0 0;
    padding: 15px 0px;
}
.card-body.dashboard  {
    background-color: #116AEC;
    color: #FFFFFF;
    font-family: "Lato", sans-serif !important;
    font-weight: 700;
    border-radius: 0 0 15px 15px;
    text-align: center;
    padding: 15px;
}
.card-body.dashboard-body  {
    background-color: #116AEC;
    color: #FFFFFF;
    font-family: "Lato", sans-serif !important;
    font-weight: 700;
    text-align: center;
    padding: 30px;
}
.card-text {
    font-size: 20px;
}
.btn-detail {
    background-color: #FFE600;
    border-radius: 20px;
    font-family: "Lato", sans-serif !important;
    font-weight: 700;
    padding: 5px 20px;
}

.btn-detail-success {
    background-color: #30c850;
    border-radius: 20px;
    font-family: "Lato", sans-serif !important;
    font-weight: 700;
    padding: 5px 20px;
}

.card-footer.dashboard {
    border: 0px solid transparent;
    background-color: #116AEC;
    border-radius: 0 0 15px 15px;
    text-align: center;
}

.dashboard-text {
    color: #116AEC;
    font-family: "Lato", sans-serif !important;
    font-weight: 700;
    font-size: 25px;
}
#orders {
    background-color: #FFFFFF;
    color: #116AEC;
    font-family: "Lato", sans-serif !important;
    font-weight: 700;
    font-size: 28px;
    padding: 20px 5px !important;
}
.table-title {
    color: #FFFFFF;
    font-family: "Lato", sans-serif !important;
    font-weight: 700;
    padding: 15px 5px !important;
}
#ventas {
    font-family: "Lato", sans-serif !important;
    font-weight: 700;
    font-size: 25px;
}
.card-header.dashboard-two {
    color: #FFFFFF;
    background-color: #116AEC;
    font-family: "Lato", sans-serif !important;
    font-weight: 700;
    font-size: 20px;
    border-radius: 15px 15px 0 0;
    padding: 15px 0px;
}
.card-body.dashboard-body-two  {
    background-color: #FFFFFF;
    color: #116AEC;
    font-family: "Lato", sans-serif !important;
    font-weight: 700;
    text-align: center;
    padding: 20px;
    border-radius:  0 0 15px 15px;
}
#ticket {
    font-size: 28px;
}
.card-body.dashboard-body-trasp  {
    background-color: #FFFFFF;
    color: #116AEC;
    font-family: "Lato", sans-serif !important;
    font-weight: 700;
    text-align: center;
}
.table-traspasos {
    color: #116AEC;
    font-family: "Lato", sans-serif !important;
    font-weight: 700;
    padding: 15px 5px !important;
}
.table-bordered, .table-bordered>tbody>tr>td, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
    border: 1px solid #116AEC;
}
#stock {
    background-color: #FFFFFF;
    color: #116AEC;
    font-family: "Lato", sans-serif !important;
    font-weight: 700;
    font-size: 20px;
    padding: 10px 5px !important;
}
.white-border th, .white-border td {
    border: 2px solid white !important;
    padding: 20px !important;
    text-align: center;
    color: #FFFFFF;
    font-family: "Lato", sans-serif !important;
    vertical-align: middle !important;
}
.white-border td {
    font-weight: 400;
    font-size: 15px;
}
button.btn.flujo-op {
    border-radius: 20px !important;
    width: 90%;
    align-items: center;
}
hr {
    border: 0 !important; 
    height: 3px !important; 
    background-color: #FFFFFF !important; 
    margin: 0 !important;
    width: 100%;
}
.div-con-borde-derecho {
    width: 200px; 
    height: 100%; 
    border-right: 3px solid #FFFFFF; 
    padding: 10px; 
    margin: 0 20px; 
}

.select2-results {
    background-color: white;
    color: black;
    font-size: 13px; /* Cambia el tamaño de la fuente */
    border: 1px solid #ccc; /* Cambia el borde */
    padding: 5px; /* Cambia el relleno */
}
