.miLista::-webkit-scrollbar {
    -webkit-appearance: none;
}

.miLista::-webkit-scrollbar:vertical {
    width:10px;
}

.miLista::-webkit-scrollbar-button:increment,.miLista::-webkit-scrollbar-button {
    display: none;
} 

.miLista::-webkit-scrollbar:horizontal {
    height: 40px;
}

.miLista::-webkit-scrollbar-thumb {
    background-color: #797979;
    border-radius: 10px;
    border: 2px solid #f1f2f3;
}

.miLista::-webkit-scrollbar-track {
    border-radius: 10px;  
}
.filter-dashboard {
    margin-top: 20px !important;
}
.table-dashboard {
    margin-top: 20px !important;
}
.miLista{
    width: 100%!important;
}