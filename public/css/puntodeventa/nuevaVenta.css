.stash {
    display: none;
}
label.form-label {
    width: 100%;
}
/*xs*/
@media (max-width: 576px) {
    .button-search-modal-new-sale {
        width: 50%;
        margin-right: 50px;
        margin-left: 80px;
        margin-top: 50px;
    }
    .separation-top-movile-new-sale {
        margin-top: 24px !important;
    }
    .buscar-cupon {
        margin-top: 24px !important;
    }
    .card.signature {
        width: 100% !important;
    }
    .separation-top-movil-new-sale {
        margin-top: 20px;
    }
    .style-button-new-sale {
        font-size: 15px !important;
        margin-bottom: 15px;
    }
    .style-button-new-sale.cancel {
        font-size: 15px;
        padding-left: 27px;
        padding-right: 27px;
    }
    .button-search-new-sale {
        text-align: end;
    }
    .button-search-new-sale button {
        font-size: 15px
    }
    .card-title {
        font-size: 20px;
    }
    .container-option-new-sale {
        display: flex;
    }
    .text-option-new-sale {
        font-size: 18px;
        color: #000;
        font-family: JetBrains Mono;
        font-style: normal;
    }
    .text-option-new-sale a {
        font-size: 18px;
        color: #adadad;
    }
    .separation-new-sale {
        margin-right: 5px;
    }
    .text-end {
        padding-top: 20px;
    }
    .option-1-new-sale {
        text-align: justify;
        cursor: pointer;
        border-bottom: 1px solid #adadad;
        padding-bottom: 15px;
    }
    .option-2-new-sale {
        text-align: justify;
        cursor: pointer;
        border-bottom: 1px solid #adadad;
        padding-top: 15px;
        padding-bottom: 15px;
    }
    .option-3-new-sale {
        text-align: justify;
        cursor: pointer;
        padding-top: 15px;
    }
    .option-1-new-sale:hover {
        font-weight: 600;
    }
    .option-2-new-sale:hover {
        font-weight: 600;
    }
    .option-3-new-sale:hover {
        font-weight: 600;
    }
    .container-information-new-sale {
        background-color: white;
        display: none;
    }
    .txt-information-new-sale {
        font-size: 13px;
        color: #000;
        font-family: JetBrains Mono;
        font-style: normal;
        text-align: center;
        margin-bottom: 5px;
        margin-top: 5px;
    }
    .container-form-new-sale {
        margin-left: 5%;
        margin-right: 5%;
        margin-top: 3%;
    }
    .div-title {
        font-size: 18px;
        font-family: 'JetBrains Mono', monospace !important;
        font-weight: bold;
        margin-bottom: 15px;
    }
    .border-title-new-sale {
        border-bottom: 1px solid #3A5FAC;
        margin-bottom: 25px;
    }
    .box-information-new-sale {
        border: 1px solid;
        padding: 15px;
        height: 145px;
        margin-left: 0px;
    }
    .titulotext {
        font-family: 'Open Sans', sans-serif !important;
        font-size: 14px;
    }
    .bottom-align-new-sale {
        text-align: justify;
    }
    .margin-buttom-new-sale {
        margin-bottom: 0px;
    }
    .btn-nuevo-cliente,.btn-reset-cliente {
        margin-top: 0px !important;
    }
    .separation-top-new-sale {
        margin-top: 20px;
    }
    .select2.select2-container.select2-container--bootstrap {
        margin: 0px !important;
    }
    .form-group {
        margin-top: 0px;
    }
    .container-code-bar-new-sale {
        display: flex;
    }
    .add-payment-new-sale {
        margin-top: 20px !important;
    }
    .signature {
        padding: 0px !important;
    }
    .guardar-venta {
        width: 80% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-bottom: 20px;
        margin-top: 20px;
    }
    .guardar-cotizacion {
        width: 80% !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }
    .btn-pdf {
        width: 80% !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }
    .btn-nueva-venta {
        width: 80% !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }
    .table {
        margin-bottom: 20px;
        height: 150px;
        max-width: 100%;
    }
    .table-total-new-sale {
        margin-bottom: 20px;
        width: 1070px;
        height: 200px;
        max-width: none;
    }
    .scroll-table-modal-new-sale {
        overflow-x: scroll;
    }
    .container-button {
        text-align: center;
    }
    .button-arrow-new-sale {
        background: none;
        border: 0px;
    }
    #button-right-new-sale:hover {
        scale: 0.9;
    }
    #button-left-new-sale:hover {
        scale: 0.9;
    }
}
/*sm*/
@media (min-width: 576px) and (max-width: 768px) {
    .button-search-modal-new-sale {
        width: 60%;
        margin-right: 120px;
        margin-left: 120px;
        margin-top: 50px;
    }
    .separation-top-movile-new-sale {
        margin-top: 24px !important;
    }
    .buscar-cupon {
        margin-top: 24px !important;
    }
    .card.signature {
        width: 100% !important;
    }
    .separation-top-movil-new-sale {
        margin-top: 20px;
    }
    .style-button-new-sale {
        font-size: 15px !important;
        margin-bottom: 15px;
    }
    .style-button-new-sale.cancel {
        font-size: 15px;
        padding-left: 27px;
        padding-right: 27px;
    }
    .button-search-new-sale {
        text-align: end;
    }
    .button-search-new-sale button {
        font-size: 15px
    }
    .card-title {
        font-size: 20px;
    }
    .container-option-new-sale {
        display: flex;
    }
    .text-option-new-sale {
        font-size: 18px;
        color: #000;
        font-family: JetBrains Mono;
        font-style: normal;
    }
    .text-option-new-sale a {
        font-size: 18px;
        color: #adadad;
    }
    .separation-new-sale {
        margin-right: 5px;
    }
    .text-end {
        padding-top: 20px;
    }
    .option-1-new-sale {
        text-align: justify;
        cursor: pointer;
        border-bottom: 1px solid #adadad;
        padding-bottom: 15px;
    }
    .option-2-new-sale {
        text-align: justify;
        cursor: pointer;
        border-bottom: 1px solid #adadad;
        padding-top: 15px;
        padding-bottom: 15px;
    }
    .option-3-new-sale {
        text-align: justify;
        cursor: pointer;
        padding-top: 15px;
    }
    .option-1-new-sale:hover {
        font-weight: 600;
    }
    .option-2-new-sale:hover {
        font-weight: 600;
    }
    .option-3-new-sale:hover {
        font-weight: 600;
    }
    .container-information-new-sale {
        background-color: white;
        display: none;
    }
    .txt-information-new-sale {
        font-size: 13px;
        color: #000;
        font-family: JetBrains Mono;
        font-style: normal;
        text-align: center;
        margin-bottom: 5px;
        margin-top: 5px;
    }
    .container-form-new-sale {
        margin-left: 5%;
        margin-right: 5%;
        margin-top: 3%;
    }
    .div-title {
        font-size: 18px;
        font-family: 'JetBrains Mono', monospace !important;
        font-weight: bold;
        margin-bottom: 15px;
    }
    .border-title-new-sale {
        border-bottom: 1px solid #3A5FAC;
        margin-bottom: 25px;
    }
    .box-information-new-sale {
        border: 1px solid;
        padding: 15px;
        height: 145px;
        margin-left: 0px;
    }
    .titulotext {
        font-family: 'Open Sans', sans-serif !important;
        font-size: 14px;
    }
    .bottom-align-new-sale {
        text-align: center;
    }
    .margin-buttom-new-sale {
       /* margin-bottom: 25px;*/
    }
    .btn-nuevo-cliente,.btn-reset-cliente {
        margin-top: 0px !important;
    }
    .separation-top-new-sale {
        margin-top: 20px;
    }
    .select2.select2-container.select2-container--bootstrap {
        margin: 0px !important;
    }
    .form-group {
        margin-top: 0px;
    }
    .container-code-bar-new-sale {
        display: flex;
    }
    .add-payment-new-sale {
        margin-top: 20px !important;
    }
    .signature {
        padding: 0px !important;
    }
    .guardar-venta {
        width: 80% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-bottom: 20px;
        margin-top: 20px;
    }
    .guardar-cotizacion {
        width: 80% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 20px;
    }
    .btn-pdf {
        width: 80% !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }
    .btn-nueva-venta {
        width: 80% !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }
    .table {
        margin-bottom: 20px;
        max-width: 100%;
        height: 150px;
        
    }
    .table-total-new-sale {
        margin-bottom: 20px;
        width: 1500px;
        height: 200px;
        max-width: none;
    }
    .scroll-table-modal-new-sale {
        overflow-x: scroll;
    }
    .container-button {
        text-align: center;
    }
    .button-arrow-new-sale {
        background: none;
        border: 0px;
    }
    #button-right-new-sale:hover {
        scale: 0.9;
    }
    #button-left-new-sale:hover {
        scale: 0.9;
    }
}
/*md*/
@media (min-width: 768px) and (max-width: 992px) {

    .button-search-modal-new-sale {
        width: 60%;
        margin-right: 120px;
        margin-left: 160px;
        margin-top: 50px;
    }
    .card.signature {
        width: 100% !important;
    }
    .separation-top-movile-new-sale {
        margin-top: 40px !important;
    }
    .separation-top-movil-new-sale {
        margin-top: 20px;
    }
    .bottom-align-new-sale button {
        width: 75%;
        margin-top: 15px !important;
        margin-bottom: 15px !important;
    }
    .container-option-new-sale {
        display: flex;
    }
    .text-option-new-sale {
        font-size: 18px;
        color: #000;
        font-family: JetBrains Mono;
        font-style: normal;
    }
    .text-option-new-sale a {
        font-size: 18px;
        color: #adadad;
    }
    .separation-new-sale {
        margin-right: 5px;
    }
    .text-end {
        padding-top: 20px;
    }
    .option-1-new-sale {
        text-align: center;
        border-right: 1px solid;
        cursor: pointer;
    }
    .option-2-new-sale {
        text-align: center;
        border-right: 1px solid;
        cursor: pointer;
    }
    .option-3-new-sale {
        text-align: center;
        cursor: pointer;
    }
    .option-1-new-sale:hover {
        font-weight: 600;
    }
    .option-2-new-sale:hover {
        font-weight: 600;
    }
    .option-3-new-sale:hover {
        font-weight: 600;
    }
    .container-information-new-sale {
        background-color: white;
        display: none;
    }
    .txt-information-new-sale {
        font-size: 13px;
        color: #000;
        font-family: JetBrains Mono;
        font-style: normal;
        text-align: center;
        margin-bottom: 5px;
        margin-top: 5px;
    }
    .container-form-new-sale {
        margin-left: 5%;
        margin-right: 5%;
        margin-top: 3%;
    }
    .div-title {
        font-size: 20px;
        font-family: 'JetBrains Mono', monospace !important;
        font-weight: bold;
    }
    .border-title-new-sale {
        border-bottom: 1px solid #3A5FAC;
        margin-bottom: 25px;
    }
    .box-information-new-sale {
        border: 1px solid;
        padding: 15px;
        margin-left: 0px;
        height: 120px;
    }
    .titulotext {
        font-family: 'Open Sans', sans-serif !important;
        font-size: 14px;
    }
    .bottom-align-new-sale {
        text-align: center;
    }
    .margin-buttom-new-sale {
        /*margin-bottom: 70px;*/
    }
    .btn-nuevo-cliente,.btn-reset-cliente {
        margin-top: 0px !important;
    }
    .separation-top-new-sale {
        margin-top: 20px;
    }
    .select2.select2-container.select2-container--bootstrap {
        margin: 0px !important;
    }
    .form-group {
        margin-top: 0px;
    }
    .container-code-bar-new-sale {
        display: flex;
    }
    .add-payment-new-sale {
        margin-top: 40px !important;
    }
    .signature {
        padding: 0px !important;
    }
    .guardar-venta {
        width: 85% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 20px;
    }
    .guardar-cotizacion {
        width: 85% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 20px;
    }
    .btn-pdf {
        width: 85% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 20px;
    }
    .btn-nueva-venta {
        width: 85% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 20px;
    }
    .table {
        margin-bottom: 20px;
        height: 150px;
        max-width: 100%;
    }
    .table-total-new-sale {
        margin-bottom: 20px;
        width: 1500px;
        height: 200px;
        max-width: none;
    }
    .scroll-table-modal-new-sale {
        overflow-x: scroll;
    }
    .container-button {
        text-align: center;
    }
    .button-arrow-new-sale {
        background: none;
        border: 0px;
    }
    #button-right-new-sale:hover {
        scale: 0.9;
    }
    #button-left-new-sale:hover {
        scale: 0.9;
    }
    
}
/*lg*/
@media (min-width: 992px) and (max-width: 1200px) {
    .button-search-modal-new-sale {
        width: 30%;
        margin-right: 120px;
        margin-left: 340px;
        margin-top: 50px;
    }
    
    .separation-top-movile-new-sale {
        margin-top: 40px !important;
    }
    .card.signature.large-card {
        width: 100% !important;
    }
    .bottom-align-new-sale button {
        width: 75%;
        margin-top: 15px !important;
        margin-bottom: 15px !important;
    }
    .container-option-new-sale {
        display: flex;
    }
    .text-option-new-sale {
        font-size: 18px;
        color: #000;
        font-family: JetBrains Mono;
        font-style: normal;
    }
    .text-option-new-sale a {
        font-size: 18px;
        color: #adadad;
    }
    .separation-new-sale {
        margin-right: 5px;
    }
    .text-end {
        padding-top: 20px;
    }
    .option-1-new-sale {
        text-align: center;
        border-right: 1px solid;
        cursor: pointer;
    }
    .option-2-new-sale {
        text-align: center;
        border-right: 1px solid;
        cursor: pointer;
    }
    .option-3-new-sale {
        text-align: center;
        cursor: pointer;
    }
    .option-1-new-sale:hover {
        font-weight: 600;
    }
    .option-2-new-sale:hover {
        font-weight: 600;
    }
    .option-3-new-sale:hover {
        font-weight: 600;
    }
    .container-information-new-sale {
        background-color: white;
        display: none;
    }
    .txt-information-new-sale {
        font-size: 13px;
        color: #000;
        font-family: JetBrains Mono;
        font-style: normal;
        text-align: center;
        margin-bottom: 5px;
        margin-top: 5px;
    }
    .container-form-new-sale {
        margin-left: 15%;
        margin-right: 15%;
        margin-top: 3%;
    }
    .div-title {
        font-size: 23px;
        font-family: 'JetBrains Mono', monospace !important;
        font-weight: bold;
    }
    .border-title-new-sale {
        border-bottom: 1px solid #3A5FAC;
        margin-bottom: 25px;
    }
    .box-information-new-sale {
        border: 1px solid;
        padding: 15px;
        margin-left: 35px;
        height: 80px;
    }
    .titulotext {
        font-family: 'Open Sans', sans-serif !important;
        font-size: 14px;
    }
    .bottom-align-new-sale {
        text-align: center;
    }
    .margin-buttom-new-sale {
       /* margin-bottom: 70px;*/
    }
    .btn-nuevo-cliente,.btn-reset-cliente {
        margin-top: 0px !important;
    }
    .separation-top-new-sale {
        margin-top: 20px;
    }
    .select2.select2-container.select2-container--bootstrap {
        margin: 0px !important;
    }
    .form-group {
        margin-top: 0px;
    }
    .container-code-bar-new-sale {
        display: flex;
    }
    .add-payment-new-sale {
        margin-top: 40px !important;
    }
    .signature {
        padding: 0px !important;
    }
    .guardar-venta {
        width: 85% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 20px;
    }
    .guardar-cotizacion {
        width: 85% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 20px;
    }
    .btn-pdf {
        width: 85% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 20px;
    }
    .btn-nueva-venta {
        width: 85% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 20px;
    }
    .table {
        margin-bottom: 20px;
        max-width: 100%;
        height: 150px;
        
    }
    .table-total-new-sale {
        margin-bottom: 20px;
        width: 1130px;
        height: 200px;
        max-width: none;
    }
    .scroll-table-modal-new-sale {
        overflow-x: scroll;
    }
    .container-button {
        text-align: center;
    }
    .button-arrow-new-sale {
        background: none;
        border: 0px;
    }
    #button-right-new-sale:hover {
        scale: 0.9;
    }
    #button-left-new-sale:hover {
        scale: 0.9;
    }
}
/*xl*/
@media (min-width: 1200px) and (max-width: 1400px) {
    .separation-top-movile-new-sale {
        margin-top: 24px !important;
    }
    .button-search-modal-new-sale {
        width: 30%;
        margin-right: 120px;
        margin-left: 420px;
        margin-top: 50px;
    }
    .col-xl-5 {
        width: 41.66666667% !important;
    }
    .col-xl-3 {
        width: 25% !important;
    }
    .col-xl-2 {
        width: 16.66666667% !important;
    }
    .col-xl-4 {
        width: 33.33333333% !important;
    }
    .col-xl-8 {
        width: 66.66666667% !important;
    }
    .container-option-new-sale {
        display: flex;
    }
    .text-option-new-sale {
        font-size: 18px;
        color: #000;
        font-family: JetBrains Mono;
        font-style: normal;
    }
    .text-option-new-sale a {
        font-size: 18px;
        color: #adadad;
    }
    .separation-new-sale {
        margin-right: 5px;
    }
    .text-end {
        padding-top: 20px;
    }
    .option-1-new-sale {
        text-align: center;
        border-right: 1px solid;
        cursor: pointer;
    }
    .option-2-new-sale {
        text-align: center;
        border-right: 1px solid;
        cursor: pointer;
    }
    .option-3-new-sale {
        text-align: center;
        cursor: pointer;
    }
    .option-1-new-sale:hover {
        font-weight: 600;
    }
    .option-2-new-sale:hover {
        font-weight: 600;
    }
    .option-3-new-sale:hover {
        font-weight: 600;
    }
    .container-information-new-sale {
        background-color: white;
        display: none;
    }
    .txt-information-new-sale {
        font-size: 13px;
        color: #000;
        font-family: JetBrains Mono;
        font-style: normal;
        text-align: center;
        margin-bottom: 5px;
        margin-top: 5px;
    }
    .container-form-new-sale {
        margin-left: 15%;
        margin-right: 15%;
        margin-top: 3%;
    }
    .div-title {
        font-size: 23px;
        font-family: 'JetBrains Mono', monospace !important;
        font-weight: bold;
    }
    .border-title-new-sale {
        border-bottom: 1px solid #3A5FAC;
        margin-bottom: 25px;
    }
    .box-information-new-sale {
        border: 1px solid;
        padding: 15px;
        margin-left: 35px;
        height: 80px;
    }
    .titulotext {
        font-family: 'Open Sans', sans-serif !important;
        font-size: 14px;
    }
    .bottom-align-new-sale {
        text-align: end;
    }
    .margin-buttom-new-sale {
      /*  margin-bottom: 70px;*/
    }
    .btn-nuevo-cliente,.btn-reset-cliente {
        margin-top: 0px !important;
    }
    .separation-top-new-sale {
        margin-top: 20px;
    }
    .select2.select2-container.select2-container--bootstrap {
        margin: 0px !important;
    }
    .form-group {
        margin-top: 0px;
    }
    .container-code-bar-new-sale {
        display: flex;
    }
    .add-payment-new-sale {
        margin-top: 40px !important;
    }
    .signature {
        padding: 0px !important;
    }
    .guardar-venta {
        width: 50% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 20px;
    }
    .guardar-cotizacion {
        width: 50% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 20px;
    }
    .btn-pdf {
        width: 50% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 20px;
    }
    .btn-nueva-venta {
        width: 50% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 20px;
    }
    .table {
        margin-bottom: 20px;
        max-width: 100%;
        height: 150px;
        
    }
    .table-total-new-sale {
        margin-bottom: 20px;
        width: 1130px;
        height: 200px;
        max-width: none;
    }
    .scroll-table-modal-new-sale {
        overflow-x: scroll;
    }
    .container-button {
        text-align: center;
    }
    .button-arrow-new-sale {
        background: none;
        border: 0px;
    }
    #button-right-new-sale:hover {
        scale: 0.9;
    }
    #button-left-new-sale:hover {
        scale: 0.9;
    }
}
/*xxl*/
@media (min-width: 1400px) {
    .separation-top-movile-new-sale {
        margin-top: 24px !important;
    }
    .button-search-modal-new-sale {
        width: 30%;
        margin-right: 120px;
        margin-left: 490px;
        margin-top: 50px;
    }
    .col-xl-5 {
        width: 41.66666667% !important;
    }
    .col-xl-3 {
        width: 25% !important;
    }
    .col-xl-2 {
        width: 16.66666667% !important;
    }
    .col-xl-4 {
        width: 33.33333333% !important;
    }
    .col-xl-8 {
        width: 66.66666667% !important;
    }
    .container-option-new-sale {
        display: flex;
    }
    .text-option-new-sale {
        font-size: 18px;
        color: #000;
        font-family: JetBrains Mono;
        font-style: normal;
    }
    .text-option-new-sale a {
        font-size: 18px;
        color: #adadad;
    }
    .separation-new-sale {
        margin-right: 5px;
    }
    .text-end {
        padding-top: 20px;
    }
    .option-1-new-sale {
        text-align: center;
        border-right: 1px solid;
        cursor: pointer;
    }
    .option-2-new-sale {
        text-align: center;
        border-right: 1px solid;
        cursor: pointer;
    }
    .option-3-new-sale {
        text-align: center;
        cursor: pointer;
    }
    .option-1-new-sale:hover {
        font-weight: 600;
    }
    .option-2-new-sale:hover {
        font-weight: 600;
    }
    .option-3-new-sale:hover {
        font-weight: 600;
    }
    .container-information-new-sale {
        background-color: white;
        display: none;
    }
    .txt-information-new-sale {
        font-size: 13px;
        color: #000;
        font-family: JetBrains Mono;
        font-style: normal;
        text-align: center;
        margin-bottom: 5px;
        margin-top: 5px;
    }
    .container-form-new-sale {
        margin-left: 15%;
        margin-right: 15%;
        margin-top: 3%;
    }
    .div-title {
        font-size: 23px;
        font-family: 'JetBrains Mono', monospace !important;
        font-weight: bold;
    }
    .border-title-new-sale {
        border-bottom: 1px solid #3A5FAC;
        margin-bottom: 25px;
    }
    .box-information-new-sale {
        border: 1px solid;
        padding: 15px;
        margin-left: 35px;
        height: 80px;
    }
    .titulotext {
        font-family: 'Open Sans', sans-serif !important;
        font-size: 14px;
    }
    .bottom-align-new-sale {
        text-align: end;
    }
    .margin-buttom-new-sale {
        /*margin-bottom: 70px;*/
    }
    .btn-nuevo-cliente,.btn-reset-cliente {
        margin-top: 0px !important;
    }
    .separation-top-new-sale {
        margin-top: 20px;
    }
    .select2.select2-container.select2-container--bootstrap {
        margin: 0px !important;
    }
    .form-group {
        margin-top: 0px;
    }
    .container-code-bar-new-sale {
        display: flex;
    }
    .add-payment-new-sale {
        margin-top: 40px !important;
    }
    .signature {
        padding: 0px !important;
    }
    .guardar-venta {
        width: 50% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 20px;
    }
    .guardar-cotizacion {
        width: 50% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 20px;
    }
    .btn-pdf {
        width: 50% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 20px;
    }
    .btn-nueva-venta {
        width: 50% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        margin-top: 20px;
    }
    .table {
        margin-bottom: 20px;
        max-width: 100%;
        height: 150px;
        
    }
    .table-total-new-sale {
        margin-bottom: 20px;
        width: 1130px;
        height: 200px;
        max-width: none;
    }
    .scroll-table-modal-new-sale {
        overflow-x: scroll;
    }
    .container-button {
        text-align: center;
    }
    .button-arrow-new-sale {
        background: none;
        border: 0px;
    }
    #button-right-new-sale:hover {
        scale: 0.9;
    }
    #button-left-new-sale:hover {
        scale: 0.9;
    }
}
