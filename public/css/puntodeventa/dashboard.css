.dashboard-bussines {
    border: 1px solid #000;
    background: #FFF;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
    border-style: none;
    margin-top: 20px;
    margin-bottom: 20px;
}

.select2.select2-container.select2-container--bootstrap {
    margin-top: 0px !important;
}
.label_bussines {
    margin-top: 10px;
}
.btn {
    height: 28px !important;
    padding-top: 2px !important;
    padding-left: 6px;
    padding-right: 6px;
    margin-top: 0px !important;
} 

.padre-buttom-1 {
    border-right: 1px solid grey;
    padding-right: 5px;
}
.padre-buttom-1 button {
    background: none;
    border: none;
}
.padre-buttom-1 button:hover {
    scale: 0.9;
}
.padre-buttom-2 button {
    background: none;
    border: none;
}
.padre-buttom-2 button:hover {
    scale: 0.9;
}
#idordensalida{
    max-width: none;
    width: 100%;
    height: 123px;
}
#idoptometrista {
    max-width: none;
    width: 100%;
    height: 123px;
    margin-bottom: 0vw;
}
.miLista {
    width: 215px; /* Configura el ancho al tamaño que prefieras */
    height: 100px; /* Configura la altura al tamaño que prefieras */
    overflow: auto; /* Esto agrega el scroll cuando el contenido supera la altura definida */
    border: 1px solid #bcbcbc; /* Esto agrega un borde alrededor de tu lista, puedes quitarlo si no lo necesitas */
    background-color: white;
}
.card-flow {
    border-bottom: 2px solid #3A5FAC;
    padding-left: 0px;
    padding-right: 0px;
    margin-left: 5px !important;
    margin-right: 5px !important;
}
.card-flow-left {
    padding-left: 1px;
}
.card-flow-rigth {
    padding-right: 0;
}
.buttom-flow {
    background: #115FD1;
    color: #fff;
    border-style: none;
    padding-top: 3px;
    padding-bottom: 3px;
    padding-left: 13px;
    padding-right: 13px;
    border-radius: 5px;
    margin-top: 11px;
}
.buttom-flow:hover {
    /*background-color: #ADB8DC;*/
    scale: 0.9;
}
.card-flow-back {
    height: 100px !important;
    background-color: #fff;
}
.content-buttom-detail {
    text-align: center;
}
.content-buttom-detail__a{
    background: none;
    border: none;
}
.content-buttom-detail__a:hover {
    scale: 0.9;
}
.title-dashboard {
    font-size: 26px;
}
.content-dashboard_father {
    padding-left: 6px;
    padding-right: 30px;
}
.table-responsive {
    height: 160px;
    overflow-y: scroll;
}
.mod-dialog {
    margin-right: 110px;
    margin-left: 110px;
    margin-top: 0;
}
.dashboard-user-title-left {
    text-align: left;  
    margin-top: 15px;
    margin-bottom: 10px;  
    padding-left: 0;
}
.dashboard-user-title-rigth {
    text-align: right; 
    margin-top: 25px;
    padding-right: 0px;
}
.dashboard-subline-title {
    border-bottom: 2px solid #3A5FAC;
}
.dashboard-modal__flow {
    width: 15%;
    margin-right: 5px;
    margin-top: 20px !important;
    margin-bottom: 20px !important;
}
.form-group {
    margin-top: 35px;
    text-align: justify;
}
.dashboard__btn-modal__new {
    background: #3498db;
    border: none;
    color: #fff;
    padding-top: 3px;
    padding-bottom: 3px;
    border-radius: 3px;
    width: 100px;
}
.dashboard__btn-modal__new:hover {
   scale: 0.9;
}
.dashboard__btn-modal__search {
    background: #27ae60;
    color: #fff;
    border: none;
    padding-top: 3px;
    padding-bottom: 3px;
    border-radius: 3px;
    width: 100px;
}
.dashboard__btn-modal__search:hover {
    scale: 0.9;
}
.dashboard__btn-modal__next {
    background: #1AB7EA;
    color: #fff;
    border: none;
    padding-top: 3px;
    padding-bottom: 3px;
    border-radius: 3px;
    width: 84px;
}
.dashboard__btn-modal__next:hover {
    scale: 0.9;
}
.dashboard-user-center {
    margin: auto;
}
.dashboard__flow-new_user {
    text-align: justify;
}
.dashboard-cta__flow-user {
    background: #27ae60;
    color: #fff;
    border: none;
    padding-top: 3px;
    padding-bottom: 3px;
    border-radius: 3px;
    width: 140px;
}
.dashboard-cta__flow-user:hover {
    scale: 0.9;
}
/*Responsiveness in 8 devices*/
/*xs*/
@media (max-width: 576px) {
    .title-dashboard {
        font-size: 20px;
        margin-bottom: 15px;
    }
    
    .dashboard_starting-order {
        margin-top: 30px;
    }
    .dashboard_bussines_search {
        padding-right: 0px;
    }
    h3.card-title {
        font-size: 12px !important;
        text-align: left;
    }
    .dashboard-buttom-flow {
        font-size: 12px;
        padding-left: 5px;
        padding-right: 5px;
    }
    .card-flow-rigth {
        padding-right: 0px;
        padding-left: 0px;
    }
    .miLista {
        width: 100%; /* Configura el ancho al tamaño que prefieras */
        height: 100px; /* Configura la altura al tamaño que prefieras */
        overflow: auto; /* Esto agrega el scroll cuando el contenido supera la altura definida */
        border: 1px solid #bcbcbc; /* Esto agrega un borde alrededor de tu lista, puedes quitarlo si no lo necesitas */
        background-color: white;
    }
    /*Style about the modal*/
    .mod-dialog {
        margin-right: 0px;
        margin-left: 0px;
    }
    .dashboard-user-title-rigth {
        text-align: justify;
        margin-top: 0px;
        padding-right: 0px;
        padding-left: 0px;
    }
    #opciones {
        width:100%!important;
    }
    
    .form-group {
        text-align: justify;
        width: 100%;
    }
    .container {
        width: 100% !important;
    }
    .dashboard-user-center {
        margin: auto;
        width: 100%;
    }
    #agregar_cliente_Agregar {
        background-color: #27ae60;
        margin-top: 25px;
        color: #fff;
        width: 50%;
        height: 30px;
        border-radius: 3px;
        margin-left: auto;
        margin-right: auto;
    }
    #agregar_cliente_Agregar:hover {
        scale: 0.9;
    }
    .dashboard-cta__flow-user {
        background: #27ae60;
        color: #fff;
        border: none;
        padding-top: 3px;
        padding-bottom: 3px;
        border-radius: 3px;
        width: 113px;
        font-size: 12px;
        margin-top: 25px;
        margin-left: 24px;
    }
    .dashboard-cta__flow-user:hover {
        scale: 0.9;
    }
}
/*sm*/
@media (min-width: 576px) and (max-width: 768px) {
    .mod-dialog {
        margin-right: 0px;
        margin-left: 0px;
    }
    .dashboard_starting-order {
        margin-top: 30px;
    }
    .miLista {
        width: 100%; /* Configura el ancho al tamaño que prefieras */
        height: 100px; /* Configura la altura al tamaño que prefieras */
        overflow: auto; /* Esto agrega el scroll cuando el contenido supera la altura definida */
        border: 1px solid #bcbcbc; /* Esto agrega un borde alrededor de tu lista, puedes quitarlo si no lo necesitas */
        background-color: white;
    }
    
    .dashboard-user-center {
        margin: auto;
        width: 100%;
    }
    .form-group {
        text-align: justify;
        width: 100%;
    }
    #agregar_cliente_Agregar {
        background-color: #27ae60;
        margin-top: 25px;
        color: #fff;
        width: 50%;
        height: 30px;
        border-radius: 3px;
        margin-left: auto;
        margin-right: auto;
    }
    #agregar_cliente_Agregar:hover {
        scale: 0.9;
    }
}
/*md*/
@media (min-width: 768px) and (max-width: 992px) {
    .dashboard_starting-order {
        margin-top: 30px;
    }
    .dashboard-user-center {
        margin: auto;
        width: 100%;
    }
    
    .container {
        width: 100% !important;
    }
    .form-group {
        text-align: justify;
        width: 100%;
    }
    #agregar_cliente_Agregar {
        background-color: #27ae60;
        margin-top: 25px;
        color: #fff;
        width: 50%;
        height: 30px;
        border-radius: 3px;
        margin-left: auto;
        margin-right: auto;
    }
    #agregar_cliente_Agregar:hover {
        scale: 0.9;
    }
    
}
/*lg*/
@media (min-width: 992px) and (max-width: 1200px) {
    h3.card-title {
        font-family: 'Open Sans', sans-serif !important;
        font-size: 15px;
        color: #1A2649;
    }
    .card-flow-left {
        padding-left: 1px;
        padding-right: 0px;
    }
    .container {
        width: 100% !important;
    }
    .form-group {
        text-align: justify;
        width: 100%;
    }
    #agregar_cliente_Agregar {
        background-color: #27ae60;
        margin-top: 35px;
        color: #fff;
        width: 25%;
        height: 30px;
        border-radius: 3px;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 35px;
    }
    #agregar_cliente_Agregar:hover {
        scale: 0.9;
    }
    .modal-body {
        margin-top: 20px
    }
}
/*xl*/
@media (min-width: 1200px) and (max-width: 1400px) {
    #agregar_cliente_Agregar {
        background-color: #27ae60;
        margin-top: 35px;
        color: #fff;
        width: 20%;
        height: 30px;
        border-radius: 3px;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 35px;
    }
    #agregar_cliente_Agregar:hover {
        scale: 0.9;
    }
    .modal-body {
        margin-top: 20px
    }
    .container {
        width: 100% !important;
    }
}
/*xxl*/
@media (min-width: 1400px) {
    #agregar_cliente_Agregar {
        background-color: #27ae60;
        margin-top: 35px;
        color: #fff;
        width: 20%;
        height: 30px;
        border-radius: 3px;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 35px;
    }
    #agregar_cliente_Agregar:hover {
        scale: 0.9;
    }
    .modal-body {
        margin-top: 20px
    }
    .container {
        width: 100% !important;
    }
}

