.nav>li>a:active, .nav>li>a:focus, .nav>li>a:hover {
    background: #FFFFFF;
    color: #3A5FAC;
    border-bottom: 2px solid #3A5FAC;
}
/*Personal information */
.subtitles-text.proceedings-text {
    padding-bottom: 10px;
    border-bottom: 2px solid #3A5FAC;
    margin-top: 35px;
}
.proceedings-text {
    padding-bottom: 10px;
    border-bottom: 2px solid #3A5FAC;
    margin-top: 35px;
}
.proceedings-subtext {
    padding-bottom: 10px;
    border-bottom: 2px solid #7198F2;
    margin-top: 35px;
}
.col-3.employee-number label {
    margin-top: 24px;
    margin-bottom: 11px;
}
.col-3.employee-enterprise label {
    margin-top: 24px;
    margin-bottom: 11px;
}
.col-3.input-form select {
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}
.col-3.input-form input {
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}
.col-4.input-form select {
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}
.col-4.input-form input{
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}
.col-6.input-form select {
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}

.col-6.input-form input {
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}
.row.datos-direccion-sub {
    margin-top: 10px !important;
}
.row.personal-data-form label {
    font-family: 'Open Sans', sans-serif;
    font-size: 15px;
    font-weight: 500;
}
.row.send-button.personal-data-form {
    margin-top: 25px !important;
    margin-bottom: 25px !important;
    align-items: center;
    justify-content: center;
}
.send-button.personal-data-form:hover button {
    background: #115FD1;
    color: #FFFFFF;
    border-radius: 5px;
    border: #115FD1;
}
.no-required  label{
    padding: 10px 10px 10px 0px !important;
    margin: 10px 0px 5px 0px !important;
}
/* Eye exam */
.row.graduation-form {
    justify-content: center;
}
.row.graduation-form label {
    font-family: 'Open Sans', sans-serif;
    font-size: 15px;
    font-weight: 500;
}
.col-9.graduation textarea {
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}
.col-2.graduation input {
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}
.col-4.graduation select {
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}
.col-4.graduation input {
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}
.row.graduation-formdos {
    margin-top: 15px !important;
}
.row.graduation-form {
    margin-top: 15px !important;
}
.col-6.graduation select {
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}
.graduation input {
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}

/* Order laboratory */
.row.order-laboratory label {
    font-family: 'Open Sans', sans-serif;
    font-size: 13px;
    font-weight: 500;
}
.col-md-10.order th {
    font-family: 'Open Sans', sans-serif;
    font-size: 15px;
    font-weight: 600;
}
.col-md-10.order td {
    font-family: 'Open Sans', sans-serif;
    font-size: 14px;
    font-weight: 500;
}
.col-3.order select {
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}
.col-4.order select {
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}
.col-5.order.not-required  select {
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}
.row.order-laboratory.form-space.dtd-a {
    justify-content: center !important;
}
.col-3.order.not-required label{
    margin-top: 20px ;
}
.col-4.order input {
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}
.col-5.graduation textarea{
    border-radius: 6px;
    border: 1px solid #BCBCBC;
    margin-bottom: 40px;
}
.col-3.order input {
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}
.col-4.order.not-required label{
    margin-top: 12px;
    margin-bottom: 12px;
}
.new-button {
    border: none;
    background: none; 
    padding: 0 15px;
}
.new-button:hover {
    scale: 0.9;
}
.col-md-2.order button {
    margin-right: 30px;
}
.subtable {
    font-size: 17px !important;
    text-align: center;
}

/* Purchase order */
.row.purchase-order {
    align-items: center;
    align-content: center;
    flex-direction: row;
    justify-content: center;
}
.row.purchase-order label {
    font-family: 'Open Sans', sans-serif;
    font-size: 15px;
    font-weight: 500;
}
.row.purchase-order p {
    font-family: 'Open Sans', sans-serif;
    font-size: 15px;
    font-weight: 500;
}
.col-5.purchase input {
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}
.col-2.purchase.icon-margin {
    margin-top: 13px;
}

/* signature */
.row.signature-end {
    flex-direction: row;
    align-content: center;
    justify-content: space-evenly;
    align-items: center;
    margin-bottom: 20px;
}
.card.signature {
    height: 15rem;
}
.needs-validation.signature {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-content: center;
    align-items: center;
    justify-content: space-evenly;
}
.new-button.signature {
    margin: 10px 0 0 24px;
}
.row.signature-end p {
    font-family: 'Open Sans', sans-serif;
    font-size: 15px;
    font-weight: 500;
}
.card-body.signature {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-top: 0px;
    margin-bottom: 20px;
    align-items: center;
}
p.signature-number {
    margin-left: 8px;
    margin-top: 11px;
    margin-bottom: 0;
    padding-bottom: 0;
    color:#BCBCBC;
}
.needs-validation.signaturer input {
    border-radius: 6px;
    border: 1px solid #BCBCBC;
}

/* Responsiveness in 8 devices */
/* xs */
@media (max-width: 576px) {
    /* Purchase order */
    

    /* signature */
    .card-title {
        font-size: 15px;
    }
    a.btn-collapse {
        font-size: 13px;
    }
    .subtitles-text.proceedings-text {
        margin-top: 7px;
    }
    .card.signature {
        width: 20rem !important;
        height: 19rem;
        margin-bottom: 15px;
    }
    .needs-validation.signature {
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        align-content: center;
        align-items: center;
        justify-content: center;
    }
    .new-button.signature {
        margin: 0;
    }
}
/* sm */
@media (min-width: 576px) and (max-width: 768px) {
    /* signature */
    .card-title {
        font-size: 17px;
    }
    a.btn-collapse {
        font-size: 13px;
    }
    .subtitles-text.proceedings-text {
        margin-top: 7px;
    }
    .card.signature {
        width: 20rem !important;
        height: 19rem;
        margin-bottom: 15px;
    }
    .needs-validation.signature {
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        align-content: center;
        align-items: center;
        justify-content: center;
    }
    .new-button.signature {
        margin: 0;
    }
}
/* md */
@media (min-width: 768px) and (max-width: 992px) {
    /* signature */
    .card-title {
        font-size: 17px;
    }
    a.btn-collapse {
        font-size: 13px;
    }
    .subtitles-text.proceedings-text {
        margin-top: 7px;
    }
    .card.signature {
        width: 34rem !important;
        height: 19rem;
        margin-bottom: 15px;
    }
    .needs-validation.signature {
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        align-content: center;
        align-items: center;
        justify-content: center;
    }
    .new-button.signature {
        margin: 0;
    }
    .tab-content {
        padding: 20px;
    }
    .row.personal-data-form label {
        font-size: 11px;
        font-weight: 500;
    }
    .row.graduation-form label {
        font-size: 12px;
        font-weight: 500;
    }
    .row.graduation-formdos {
        margin-top: 15px !important;
        display: flex;
        flex-direction: column;
        align-items: center;
        align-content: center;
    }
}
/* lg */
@media (min-width: 992px) and (max-width: 1200px) {
    /* signature */
    .card-title {
        font-size: 17px;
    }
    a.btn-collapse {
        font-size: 13px;
    }
    .subtitles-text.proceedings-text {
        margin-top: 7px;
    }
    .card.signature {
        width: 20rem !important;
        height: 19rem;
        margin-bottom: 15px;
    }

    .card.signature.large-card {
        width: 40rem !important;
    }
    .needs-validation.signature {
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        align-content: center;
        align-items: center;
        justify-content: center;
    }
    .new-button.signature {
        margin-top: 5px;
    }
    .tab-content {
        padding: 20px;
    }
}
/* xl */
@media (min-width: 1200px) and (max-width: 1400px) {
    
}
/* xxl */
@media (min-width: 1400px) {
    
}