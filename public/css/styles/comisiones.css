.body {
    min-height: 100vh;
}
.page-title {
    font-family: "League Spartan", system-ui;
    font-weight: 700;
    font-style: normal;
    color: #3A5FAC;
}
.page-subtitle {
    font-family: "League Spartan", system-ui;
    font-weight: 600;
    font-style: normal;
    color: #144A9E;
    font-size: 24px;
}
.text-input {
    font-family: "League Spartan", system-ui;
    font-weight: 700;
    font-style: normal;
    font-size: 14px;
    padding: 0 15px;
    margin: 0;
    text-align: end;
}
.date-input {
    border-radius: 5px;
    border: 1px solid #BCBCBC;
}
.title-table {
    background-color: #144A9E;
    color: #FFFFFF;
}
div.dt-buttons {
    position: relative;
    float: right !important;
}
.form-control.form-control-sm {
    width: 30%;
    border-radius: 5px;
    border: 1px solid #BCBCBC;
    margin-left: 15px;
    text-align: start;
}
.pagination {
    margin: 10px 0 !important;
}
.dataTables_filter label {
    font-family: "League Spartan", system-ui;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: start;
}