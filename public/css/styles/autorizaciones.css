.body {
    min-height: 100vh;
}
.page-title {
    font-family: "League Spartan", system-ui;
    font-weight: 700;
    font-style: normal;
    color: #3A5FAC;
}
.page-subtitle {
    font-family: "League Spartan", system-ui;
    font-weight: 600;
    font-style: normal;
    color: #144A9E;
    font-size: 20px;
}
.input-aut {
    border-radius: 4px;
    border: 1px solid #BCBCBC;
}
.text-input {
    font-family: "League Spartan", system-ui;
    font-weight: 700;
    font-style: normal;
    font-size: 14px;
    padding: 0 15px;
    text-align: end;
}
#autorizacionesTable_wrapper {
    margin-top: 50px !important;
}
.title-table {
    background-color: #144A9E;
    color: #FFFFFF;
}
div.col-sm-12.col-md-7 {
    justify-content: end !important;
    display: flex !important;
}
.dataTables_filter label {
    font-family: "League Spartan", system-ui;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: end;
}
.form-control.form-control-sm {
    width: 40%;
    border-radius: 5px;
    border: 1px solid #BCBCBC;
    margin-left: 15px;
    text-align: start;
}