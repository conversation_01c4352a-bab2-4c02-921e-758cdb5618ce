body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    margin: 0;
    padding: 20px;
}

.container.boxShadow {
    max-width: 100%;
    padding: 1rem;
    margin: 0 auto;
    margin: auto;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table-provider {
    width: 100%;
    /* Para ocupar el ancho disponible */
    padding: 0.5rem;
    /* Espaciado interno */
    border: 1px solid #ccc;
    /* Bordes sutiles */
    border-radius: 0.25rem;
    /* Bordes redondeados */
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    font-size: 16px;
    -webkit-appearance: none;
    /* Desactiva el estilo predeterminado en navegadores WebKit/Blink */
    -moz-appearance: none;
    /* Desactiva el estilo predeterminado en Firefox */
    appearance: none;
    /* Desactiva el estilo predeterminado del navegador */
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><polyline points="6,7 10,11 14,7" stroke="#333" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/></svg>') no-repeat right 0.75rem center/16px 16px;
    /* Icono personalizado para el desplegable */
    background-color: #fff;
    /* Fondo blanco */
    margin-bottom: 1rem;
    /* Espacio debajo del select */
}

.table-provider:focus {
    outline: none;
    border-color: #0056b3;
    box-shadow: 0 0 0 0.2rem rgba(0, 86, 179, .25);
}

.table-provider option {
    padding: 5px 10px;
}

.table-responsive {
    padding: 1rem;
}

.selectFilters{
  
}

/* Estilos para los checkboxes */
.custom-checkbox {
    padding-right: 1rem;
  }
  
  .custom-checkbox input[type="checkbox"] {
    margin-right: 0.5rem;
  }
  
  /* Estilo para el botón */
  .btn-enviar {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 1rem;
  }
  
  .btn-enviar:hover {
    background-color: #0056b3;
  }
  
  /* Media query para ajustar los elementos en pantallas medianas */
  @media (min-width: 768px) {
    .container > .row {
      align-items: center; 
    }
  }
  
  /* Ajuste para pantallas más pequeñas */
  @media (max-width: 767px) {
    .custom-checkbox {
      margin-bottom: 1rem; /* Espaciado entre elementos en pantallas pequeñas */
    }
  
    .btn-enviar {
      width: 100%;
    }
  }


h3.text-center {
  color: #fff; 
  background: linear-gradient(45deg, #0056b3, #0056b3);
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); 
}


.fas.fa-user {
  margin-right: 10px;
}

@media (max-width: 768px) {
  h3.text-center {
      font-size: 18px;
      padding: 8px;
  }
}

/* Estilo para el título */
h3 {
  
  color: #333; 
  font-size: 20px; 
  margin-bottom: 10px;
}


#totalSeleccionados {
  font-weight: 600;
  color: #4A90E2;
  padding: 5px 10px; 
  background-color: #E8F4FD; 
  border-radius: 5px; 
  transition: all 0.3s ease-in-out;
}

@keyframes pulse {
  0% {
      transform: scale(1);
      
  }
  50% {
      transform: scale(1.1);
      box-shadow: 0 0 10px 10px rgba(74, 144, 226, 0);
  }
  100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(74, 144, 226, 0);
  }
}
.pulse-animation {
  animation: pulse 1.5s infinite;
}

@media (max-width: 768px) {
  .totalSeleccionadosTittle {
      font-size: 18px;
  }
  #totalSeleccionados {
      font-size: 16px;
  }
}


.table-responsive {
  overflow-x: auto;
}

.boxShadow {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); 
  margin: 20px 0;
}
