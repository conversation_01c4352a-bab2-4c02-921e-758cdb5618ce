:root {
    --primary-color: #115FD1;
    --primary-color-light: rgba(17, 95, 209, 0.1);
    --primary-color-hover: rgba(17, 95, 209, 0.2);
    --border-radius: 6px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --border-color: #dee2e6;
    --text-color: #495057;
    --text-muted: #6c757d;
    --background-stripe: #f8f9fa;
    --shadow-sm: 0 1px 2px rgba(0,0,0,0.05);
    --shadow-md: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-lg: 0 4px 8px rgba(0,0,0,0.15);
    --font-size-base: 14px;
    --font-size-lg: 16px;
    --header-bg: #f0f5ff;
    --row-hover: #f5f9ff;
    --table-border: #c8d6e5;
}

/* Estilos base de la tabla */
.dataTables_wrapper {
    width: 100%;
    margin: 20px 0;
    font-size: var(--font-size-base);
    color: var(--text-color);
    box-shadow: var(--shadow-lg);
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* Estilos del encabezado de la tabla */
.table {
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.page-title{
    color: #115FD1;
}

.table thead th {
    background-color: var(--header-bg);
    font-size: var(--font-size-lg);
    font-weight: 600;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 2px solid var(--primary-color);
    white-space: nowrap;
    position: relative;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
    vertical-align: middle;
    color: var(--primary-color);
    letter-spacing: 0.5px;
    box-shadow: 0 2px 3px rgba(0,0,0,0.05);
}

.table thead th:hover {
    background-color: var(--primary-color-hover);
}

/* Estilos de las celdas */
.table tbody td {
    padding: var(--spacing-md) var(--spacing-lg);
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s;
    text-align: center;
    border-right: 1px solid var(--border-color);
}

.table tbody tr:hover td {
    background-color: var(--row-hover);
}

.table-bordered {
    border: 1px solid var(--table-border);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Alineación según tipo de contenido */
.table td.text-right,
.table th.text-right {
    text-align: right;
}

.select2-container {
    width: 100% !important;
    max-width: 300px;
}

.select2-container--open .select2-dropdown {
    width: auto !important;
    min-width: 100%;
    max-width: 300px;
    white-space: normal;
}

.filtros-container {
    position: relative;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.filtros-section {
    background: white;
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
}

.filtros-section-title {
    color: #115FD1;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
    transition: border-color 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #115FD1;
    box-shadow: 0 0 0 0.2rem rgba(17, 95, 209, 0.25);
}

.btn-aplicar-filtros {
    background-color: #115FD1;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    border: none;
    font-weight: 600;
    width: 100%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-aplicar-filtros:hover {
    background-color: #0d4aa3;
    transform: translateY(-1px);
}

.btn-limpiar-filtros {
    background-color: #6c757d;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    border: none;
    font-weight: 600;
    width: 100%;
    transition: all 0.3s ease;
    margin-top: 0.5rem;
}

.btn-limpiar-filtros:hover {
    background-color: #5a6268;
}

@media (max-width: 768px) {
    .filtros-container {
        padding: 1rem;
    }

    .filtros-section {
        padding: 0.75rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }
}

/* Estilos mejorados para Select2 */
.select2-container {
    width: 100% !important;
    max-width: 300px !important;
}

.select2-container .select2-selection--single {
    height: 38px;
    padding: 5px;
    border: 1px solid #ced4da;
    border-radius: 6px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 28px;
    color: #495057;
    padding-left: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    padding-right: 20px !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 36px;
}

.select2-dropdown {
    border: 1px solid #ced4da;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    width: auto !important;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 6px;
    width: 100% !important;
    max-width: calc(300px - 24px) !important;
}

.select2-results__option {
    padding: 6px 12px;
    white-space: normal;
    word-wrap: break-word;
    font-size: 14px;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #115FD1;
}

/* Ajuste para el dropdown de Select2 */
.select2-container--open .select2-dropdown {
    min-width: 100%;
    max-width: 300px !important;
    width: auto !important;
}

/* Asegurar que el texto largo se ajuste correctamente */
.select2-results__option {
    white-space: normal;
    word-wrap: break-word;
}

/* Columna de venta ID solo se oculta cuando se agrupa */
.venta-id-column {
    display: table-cell;
}

/* Cuando se agrupa por venta ID, ocultar la columna */
.grouped-by-venta .venta-id-column {
    display: none;
}

/* Estilos para los controles de DataTables */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
    padding: 15px;
    color: var(--text-color);
}

.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 6px 10px;
    background-color: white;
    box-shadow: var(--shadow-sm);
    transition: all 0.2s;
}

.dataTables_wrapper .dataTables_length select:focus,
.dataTables_wrapper .dataTables_filter input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(17, 95, 209, 0.25);
    outline: none;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border-radius: var(--border-radius);
    padding: 6px 12px;
    margin: 0 2px;
    border: 1px solid var(--border-color);
    background: white;
    color: var(--text-color) !important;
    transition: all 0.2s;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: var(--primary-color-light) !important;
    border-color: var(--primary-color);
    color: var(--primary-color) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: var(--primary-color) !important;
    border-color: var(--primary-color);
    color: white !important;
    font-weight: bold;
}

/* Estilos para el grupo de filas */
.dtrg-group td,
tr.group-header td {
    background-color: var(--header-bg) !important;
    font-weight: bold;
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    padding: 10px 15px !important;
    text-align: left !important;
    font-size: 1.1em;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.2s;
}

tr.group-header:hover td {
    background-color: var(--primary-color-hover) !important;
    cursor: pointer;
}

tr.group-header td i {
    margin-right: 8px;
    color: var(--primary-color);
}

/* Estilos para los botones de acción */
.btn-action {
    padding: 6px 12px;
    border-radius: var(--border-radius);
    border: none;
    background-color: var(--primary-color);
    color: white;
    font-weight: 500;
    transition: all 0.2s;
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}

.btn-action:hover {
    background-color: #0d4aa3;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Estilos para la tabla con FixedHeader */
.fixedHeader-floating {
    box-shadow: var(--shadow-lg);
}

/* Mejoras para dispositivos móviles */
@media (max-width: 768px) {
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        text-align: center;
        float: none;
        display: block;
        margin: 10px 0;
    }
}

.dt-buttons {
    margin-top: 12px;
    margin-left: 16px;
}

.dt-buttons .excel-btn {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: white !important;
    padding: 8px 16px !important;
    border-radius: 4px !important;
    font-size: 14px !important;
    position: relative;
    top: 2px;
    left: 4px;
} 