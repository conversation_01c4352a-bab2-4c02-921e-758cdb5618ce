.custom-checkbox input[type="checkbox"] {
    width: 35px;   /* ajusta según tus necesidades */
    height: 35px;  /* ajusta según tus necesidades */
}


.table-orden {
    display: block;
    background-color: #007bff; /* Color de fondo azul */
    color: #fff; /* Color de texto blanco */
    padding: 10px 15px;
    margin-top: 25px;
    border-radius: 5px;
    margin-bottom: 20px; /* Espacio entre el botón y la tabla */
    text-decoration: none; /* Quitar subrayado del enlace */
    transition: background-color 0.8s; /* Transición suave al cambiar de color */
}

/* Reseteo para el navegador y fuentes básicas */
body {
    font-family: 'Arial', sans-serif;
    font-size: 16px;
    line-height: 1.5;
    color: #333;
}

/* Estilo para el campo de entrada */
.input-sm.form-control {
    padding: 10px 12px;
    border: 1px solid #dcdcdc;
    border-radius: 4px;
    font-size: 14px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.input-sm.form-control:focus {
    border-color: #5b9bd5;
    box-shadow: 0 2px 8px rgba(91,155,213,0.25);
    outline: none;
}

/* Estilo para el botón */
button {
    background-color: #007bff;
    color: #fff;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: #0056b3;
}

