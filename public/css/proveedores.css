/* Estilo general para la tabla */
#tablaProveedor {
    width: 100%;
    border-collapse: collapse;
}

/* Estilo para el encabezado de la tabla */
#tablaProveedor thead th {
    background-color: #f5f5f5;
    border-bottom: 2px solid #e0e0e0;
    padding: 10px 8px;
}

/* Estilo para las filas de la tabla */
#tablaProveedor tbody tr {
    border-bottom: 1px solid #e0e0e0;
}

/* Estilo para las celdas de la tabla */
#tablaProveedor td {
    padding: 8px 10px;
}

/* Estilo para los botones en la columna de opciones */
.text-center.align-middle a {
    margin: 0 5px; /* Espacio entre los botones */
    display: inline-block;
    vertical-align: middle;
}

/*'Agregar Proveedor'*/
.table-provider {
    display: block;
    background-color: #007bff; /* Color de fondo azul */
    color: #fff; /* Color de texto blanco */
    padding: 10px 15px;
    margin-top: 25px;
    border-radius: 5px;
    margin-bottom: 20px; /* Espacio entre el botón y la tabla */
    text-decoration: none; /* Quitar subrayado del enlace */
    transition: background-color 0.8s; /* Transición suave al cambiar de color */
}

.table-provider:hover {
    background-color: #0056b3; /* Color de fondo un poco más oscuro al pasar el ratón */
    color: #fff; /* Mantener el color de texto blanco al pasar el ratón */
}

.table-provider i {
    margin-right: 5px; /* Espacio entre el icono y el texto */
}


.small-input {
    width: 100%; 
    padding: 10px;
}
.form-group {
    margin-bottom: 1.5rem;
}
.form-card {
    background-color: #f7f7f7;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 20px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

.card {
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.card-title {
    font-weight: 600;
}

.form-control {
    border-radius: 5px;
}

.label, .check {
    display: block;
    margin-bottom: 10px;
}

.check input[type="checkbox"] {
    margin-right: 5px;
}
