body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
}
.bodyb {
    background-color: #0D143D;
    min-height: 100vh;
    /* Asegura que cubra al menos el 100% de la altura de la ventana */
    display: flex;
    flex-direction: column;
    /* Alinea los elementos verticalmente */
    align-items: center;
    /* Centra los elementos horizontalmente */
    padding-top: 20px;
    /* Espacio superior para el título */
}

.container {
    background-color: #116AEC;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    width: 80%;
    /* Ajusta el ancho según sea necesario */
}

h1 {
    font-size: 4rem;
    color: #ffffff;
    /* Color del texto blanco */
    margin-bottom: 20px;
    text-align: center;
}

h2 {
    font-size: 3rem;
    color: #ffffff;
    /* Color del texto azul oscuro */
    margin-bottom: 20px;
    text-align: center;
}


.custom-select option {
    color: #000000;
}

.custom-input::-webkit-calendar-picker-indicator {
    filter: invert(0);
    /* Asegura que el icono del calendario no se invierte */
}

.custom-select:focus,
.custom-input:focus {
    outline: none;
    box-shadow: 0 0 10px rgba(30, 144, 255, 0.5);
}

.row.mb-3.justify-content-center {
    text-align: center;
}

.dataTables_wrapper .dataTables_filter {
    float: left;
}

.dataTables_wrapper .dataTables_paginate {
    float: right;
}

.form-control {
    margin-bottom: 10px;
    border-radius: 0%;
}

.rangoFecha {
    color: #ffffff;
}

.btn-primary {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: #ffffff;
    padding: 10px 20px;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.btn-primary:hover {
    background-color: #138f9e;
    border-color: #138f9e;
}

.table {
    width: 100%;
    margin-top: 20px;
    color: #343a40;
    background-color: #ffffff;
    border-collapse: collapse;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #dee2e6;
}

.table thead th {
    background-color: #17a2b8;
    color: white;
    border: 1px solid #dee2e6;
    padding: 12px;
}

.table tbody td {
    border: 1px solid #dee2e6;
    padding: 12px;
    vertical-align: middle;
}

.table tbody tr:nth-child(odd) {
    background-color: #f2f2f2;
}

.table tbody tr:hover {
    background-color: #e0e0e0;
}