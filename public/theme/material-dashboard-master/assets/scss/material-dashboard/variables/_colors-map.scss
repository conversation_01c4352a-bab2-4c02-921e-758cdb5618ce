// these might be useful in a switch to sass...at some point.

//$bmd-colors: (
//        "red": $red,
//        "pink": $pink,
//        "purple": $purple,
//        "deep-purple": $deep-purple,
//        "indigo": $indigo,
//        "blue": $blue,
//        "light-blue": $light-blue,
//        "cyan": $cyan,
//        "teal": $teal,
//        "green": $green,
//        "light-green": $light-green,
//        "lime": $lime,
//        "yellow": $yellow,
//        "amber": $amber,
//        "orange": $orange,
//        "deep-orange": $deep-orange,
//        "brown": $brown,
//        "grey": $grey,
//        "blue-grey": $blue-grey
//);
//
//$bmd-colors-map: (
//        "red-50": (name: "red", color: $red-50, number: "-50"),
//        "red-100": (name: "red", color: $red-100, number: "-100"),
//        "red-200": (name: "red", color: $red-200, number: "-200"),
//        "red-300": (name: "red", color: $red-300, number: "-300"),
//        "red-400": (name: "red", color: $red-400, number: "-400"),
//        "red-500": (name: "red", color: $red-500, number: "-500"),
//        "red-600": (name: "red", color: $red-600, number: "-600"),
//        "red-700": (name: "red", color: $red-700, number: "-700"),
//        "red-800": (name: "red", color: $red-800, number: "-800"),
//        "red-900": (name: "red", color: $red-900, number: "-900"),
//        "red-a100": (name: "red", color: $red-a100, number: "-a100"),
//        "red-a200": (name: "red", color: $red-a200, number: "-a200"),
//        "red-a400": (name: "red", color: $red-a400, number: "-a400"),
//        "red-a700": (name: "red", color: $red-a700, number: "-a700"),
//        "red": (name: "red", color: $red, number: ""),
//        "pink-50": (name: "pink", color: $pink-50, number: "-50"),
//        "pink-100": (name: "pink", color: $pink-100, number: "-100"),
//        "pink-200": (name: "pink", color: $pink-200, number: "-200"),
//        "pink-300": (name: "pink", color: $pink-300, number: "-300"),
//        "pink-400": (name: "pink", color: $pink-400, number: "-400"),
//        "pink-500": (name: "pink", color: $pink-500, number: "-500"),
//        "pink-600": (name: "pink", color: $pink-600, number: "-600"),
//        "pink-700": (name: "pink", color: $pink-700, number: "-700"),
//        "pink-800": (name: "pink", color: $pink-800, number: "-800"),
//        "pink-900": (name: "pink", color: $pink-900, number: "-900"),
//        "pink-a100": (name: "pink", color: $pink-a100, number: "-a100"),
//        "pink-a200": (name: "pink", color: $pink-a200, number: "-a200"),
//        "pink-a400": (name: "pink", color: $pink-a400, number: "-a400"),
//        "pink-a700": (name: "pink", color: $pink-a700, number: "-a700"),
//        "pink": (name: "pink", color: $pink, number: ""),
//        "purple-50": (name: "purple", color: $purple-50, number: "-50"),
//        "purple-100": (name: "purple", color: $purple-100, number: "-100"),
//        "purple-200": (name: "purple", color: $purple-200, number: "-200"),
//        "purple-300": (name: "purple", color: $purple-300, number: "-300"),
//        "purple-400": (name: "purple", color: $purple-400, number: "-400"),
//        "purple-500": (name: "purple", color: $purple-500, number: "-500"),
//        "purple-600": (name: "purple", color: $purple-600, number: "-600"),
//        "purple-700": (name: "purple", color: $purple-700, number: "-700"),
//        "purple-800": (name: "purple", color: $purple-800, number: "-800"),
//        "purple-900": (name: "purple", color: $purple-900, number: "-900"),
//        "purple-a100": (name: "purple", color: $purple-a100, number: "-a100"),
//        "purple-a200": (name: "purple", color: $purple-a200, number: "-a200"),
//        "purple-a400": (name: "purple", color: $purple-a400, number: "-a400"),
//        "purple-a700": (name: "purple", color: $purple-a700, number: "-a700"),
//        "purple": (name: "purple", color: $purple, number: ""),
//        "deep-purple-50": (name: "deep-purple", color: $deep-purple-50, number: "-50"),
//        "deep-purple-100": (name: "deep-purple", color: $deep-purple-100, number: "-100"),
//        "deep-purple-200": (name: "deep-purple", color: $deep-purple-200, number: "-200"),
//        "deep-purple-300": (name: "deep-purple", color: $deep-purple-300, number: "-300"),
//        "deep-purple-400": (name: "deep-purple", color: $deep-purple-400, number: "-400"),
//        "deep-purple-500": (name: "deep-purple", color: $deep-purple-500, number: "-500"),
//        "deep-purple-600": (name: "deep-purple", color: $deep-purple-600, number: "-600"),
//        "deep-purple-700": (name: "deep-purple", color: $deep-purple-700, number: "-700"),
//        "deep-purple-800": (name: "deep-purple", color: $deep-purple-800, number: "-800"),
//        "deep-purple-900": (name: "deep-purple", color: $deep-purple-900, number: "-900"),
//        "deep-purple-a100": (name: "deep-purple", color: $deep-purple-a100, number: "-a100"),
//        "deep-purple-a200": (name: "deep-purple", color: $deep-purple-a200, number: "-a200"),
//        "deep-purple-a400": (name: "deep-purple", color: $deep-purple-a400, number: "-a400"),
//        "deep-purple-a700": (name: "deep-purple", color: $deep-purple-a700, number: "-a700"),
//        "deep-purple": (name: "deep-purple", color: $deep-purple, number: ""),
//        "indigo-50": (name: "indigo", color: $indigo-50, number: "-50"),
//        "indigo-100": (name: "indigo", color: $indigo-100, number: "-100"),
//        "indigo-200": (name: "indigo", color: $indigo-200, number: "-200"),
//        "indigo-300": (name: "indigo", color: $indigo-300, number: "-300"),
//        "indigo-400": (name: "indigo", color: $indigo-400, number: "-400"),
//        "indigo-500": (name: "indigo", color: $indigo-500, number: "-500"),
//        "indigo-600": (name: "indigo", color: $indigo-600, number: "-600"),
//        "indigo-700": (name: "indigo", color: $indigo-700, number: "-700"),
//        "indigo-800": (name: "indigo", color: $indigo-800, number: "-800"),
//        "indigo-900": (name: "indigo", color: $indigo-900, number: "-900"),
//        "indigo-a100": (name: "indigo", color: $indigo-a100, number: "-a100"),
//        "indigo-a200": (name: "indigo", color: $indigo-a200, number: "-a200"),
//        "indigo-a400": (name: "indigo", color: $indigo-a400, number: "-a400"),
//        "indigo-a700": (name: "indigo", color: $indigo-a700, number: "-a700"),
//        "indigo": (name: "indigo", color: $indigo, number: ""),
//        "blue-50": (name: "blue", color: $blue-50, number: "-50"),
//        "blue-100": (name: "blue", color: $blue-100, number: "-100"),
//        "blue-200": (name: "blue", color: $blue-200, number: "-200"),
//        "blue-300": (name: "blue", color: $blue-300, number: "-300"),
//        "blue-400": (name: "blue", color: $blue-400, number: "-400"),
//        "blue-500": (name: "blue", color: $blue-500, number: "-500"),
//        "blue-600": (name: "blue", color: $blue-600, number: "-600"),
//        "blue-700": (name: "blue", color: $blue-700, number: "-700"),
//        "blue-800": (name: "blue", color: $blue-800, number: "-800"),
//        "blue-900": (name: "blue", color: $blue-900, number: "-900"),
//        "blue-a100": (name: "blue", color: $blue-a100, number: "-a100"),
//        "blue-a200": (name: "blue", color: $blue-a200, number: "-a200"),
//        "blue-a400": (name: "blue", color: $blue-a400, number: "-a400"),
//        "blue-a700": (name: "blue", color: $blue-a700, number: "-a700"),
//        "blue": (name: "blue", color: $blue, number: ""),
//        "light-blue-50": (name: "light-blue", color: $light-blue-50, number: "-50"),
//        "light-blue-100": (name: "light-blue", color: $light-blue-100, number: "-100"),
//        "light-blue-200": (name: "light-blue", color: $light-blue-200, number: "-200"),
//        "light-blue-300": (name: "light-blue", color: $light-blue-300, number: "-300"),
//        "light-blue-400": (name: "light-blue", color: $light-blue-400, number: "-400"),
//        "light-blue-500": (name: "light-blue", color: $light-blue-500, number: "-500"),
//        "light-blue-600": (name: "light-blue", color: $light-blue-600, number: "-600"),
//        "light-blue-700": (name: "light-blue", color: $light-blue-700, number: "-700"),
//        "light-blue-800": (name: "light-blue", color: $light-blue-800, number: "-800"),
//        "light-blue-900": (name: "light-blue", color: $light-blue-900, number: "-900"),
//        "light-blue-a100": (name: "light-blue", color: $light-blue-a100, number: "-a100"),
//        "light-blue-a200": (name: "light-blue", color: $light-blue-a200, number: "-a200"),
//        "light-blue-a400": (name: "light-blue", color: $light-blue-a400, number: "-a400"),
//        "light-blue-a700": (name: "light-blue", color: $light-blue-a700, number: "-a700"),
//        "light-blue": (name: "light-blue", color: $light-blue, number: ""),
//        "cyan-50": (name: "cyan", color: $cyan-50, number: "-50"),
//        "cyan-100": (name: "cyan", color: $cyan-100, number: "-100"),
//        "cyan-200": (name: "cyan", color: $cyan-200, number: "-200"),
//        "cyan-300": (name: "cyan", color: $cyan-300, number: "-300"),
//        "cyan-400": (name: "cyan", color: $cyan-400, number: "-400"),
//        "cyan-500": (name: "cyan", color: $cyan-500, number: "-500"),
//        "cyan-600": (name: "cyan", color: $cyan-600, number: "-600"),
//        "cyan-700": (name: "cyan", color: $cyan-700, number: "-700"),
//        "cyan-800": (name: "cyan", color: $cyan-800, number: "-800"),
//        "cyan-900": (name: "cyan", color: $cyan-900, number: "-900"),
//        "cyan-a100": (name: "cyan", color: $cyan-a100, number: "-a100"),
//        "cyan-a200": (name: "cyan", color: $cyan-a200, number: "-a200"),
//        "cyan-a400": (name: "cyan", color: $cyan-a400, number: "-a400"),
//        "cyan-a700": (name: "cyan", color: $cyan-a700, number: "-a700"),
//        "cyan": (name: "cyan", color: $cyan, number: ""),
//        "teal-50": (name: "teal", color: $teal-50, number: "-50"),
//        "teal-100": (name: "teal", color: $teal-100, number: "-100"),
//        "teal-200": (name: "teal", color: $teal-200, number: "-200"),
//        "teal-300": (name: "teal", color: $teal-300, number: "-300"),
//        "teal-400": (name: "teal", color: $teal-400, number: "-400"),
//        "teal-500": (name: "teal", color: $teal-500, number: "-500"),
//        "teal-600": (name: "teal", color: $teal-600, number: "-600"),
//        "teal-700": (name: "teal", color: $teal-700, number: "-700"),
//        "teal-800": (name: "teal", color: $teal-800, number: "-800"),
//        "teal-900": (name: "teal", color: $teal-900, number: "-900"),
//        "teal-a100": (name: "teal", color: $teal-a100, number: "-a100"),
//        "teal-a200": (name: "teal", color: $teal-a200, number: "-a200"),
//        "teal-a400": (name: "teal", color: $teal-a400, number: "-a400"),
//        "teal-a700": (name: "teal", color: $teal-a700, number: "-a700"),
//        "teal": (name: "teal", color: $teal, number: ""),
//        "green-50": (name: "green", color: $green-50, number: "-50"),
//        "green-100": (name: "green", color: $green-100, number: "-100"),
//        "green-200": (name: "green", color: $green-200, number: "-200"),
//        "green-300": (name: "green", color: $green-300, number: "-300"),
//        "green-400": (name: "green", color: $green-400, number: "-400"),
//        "green-500": (name: "green", color: $green-500, number: "-500"),
//        "green-600": (name: "green", color: $green-600, number: "-600"),
//        "green-700": (name: "green", color: $green-700, number: "-700"),
//        "green-800": (name: "green", color: $green-800, number: "-800"),
//        "green-900": (name: "green", color: $green-900, number: "-900"),
//        "green-a100": (name: "green", color: $green-a100, number: "-a100"),
//        "green-a200": (name: "green", color: $green-a200, number: "-a200"),
//        "green-a400": (name: "green", color: $green-a400, number: "-a400"),
//        "green-a700": (name: "green", color: $green-a700, number: "-a700"),
//        "green": (name: "green", color: $green, number: ""),
//        "light-green-50": (name: "light-green", color: $light-green-50, number: "-50"),
//        "light-green-100": (name: "light-green", color: $light-green-100, number: "-100"),
//        "light-green-200": (name: "light-green", color: $light-green-200, number: "-200"),
//        "light-green-300": (name: "light-green", color: $light-green-300, number: "-300"),
//        "light-green-400": (name: "light-green", color: $light-green-400, number: "-400"),
//        "light-green-500": (name: "light-green", color: $light-green-500, number: "-500"),
//        "light-green-600": (name: "light-green", color: $light-green-600, number: "-600"),
//        "light-green-700": (name: "light-green", color: $light-green-700, number: "-700"),
//        "light-green-800": (name: "light-green", color: $light-green-800, number: "-800"),
//        "light-green-900": (name: "light-green", color: $light-green-900, number: "-900"),
//        "light-green-a100": (name: "light-green", color: $light-green-a100, number: "-a100"),
//        "light-green-a200": (name: "light-green", color: $light-green-a200, number: "-a200"),
//        "light-green-a400": (name: "light-green", color: $light-green-a400, number: "-a400"),
//        "light-green-a700": (name: "light-green", color: $light-green-a700, number: "-a700"),
//        "light-green": (name: "light-green", color: $light-green, number: ""),
//        "lime-50": (name: "lime", color: $lime-50, number: "-50"),
//        "lime-100": (name: "lime", color: $lime-100, number: "-100"),
//        "lime-200": (name: "lime", color: $lime-200, number: "-200"),
//        "lime-300": (name: "lime", color: $lime-300, number: "-300"),
//        "lime-400": (name: "lime", color: $lime-400, number: "-400"),
//        "lime-500": (name: "lime", color: $lime-500, number: "-500"),
//        "lime-600": (name: "lime", color: $lime-600, number: "-600"),
//        "lime-700": (name: "lime", color: $lime-700, number: "-700"),
//        "lime-800": (name: "lime", color: $lime-800, number: "-800"),
//        "lime-900": (name: "lime", color: $lime-900, number: "-900"),
//        "lime-a100": (name: "lime", color: $lime-a100, number: "-a100"),
//        "lime-a200": (name: "lime", color: $lime-a200, number: "-a200"),
//        "lime-a400": (name: "lime", color: $lime-a400, number: "-a400"),
//        "lime-a700": (name: "lime", color: $lime-a700, number: "-a700"),
//        "lime": (name: "lime", color: $lime, number: ""),
//        "yellow-50": (name: "yellow", color: $yellow-50, number: "-50"),
//        "yellow-100": (name: "yellow", color: $yellow-100, number: "-100"),
//        "yellow-200": (name: "yellow", color: $yellow-200, number: "-200"),
//        "yellow-300": (name: "yellow", color: $yellow-300, number: "-300"),
//        "yellow-400": (name: "yellow", color: $yellow-400, number: "-400"),
//        "yellow-500": (name: "yellow", color: $yellow-500, number: "-500"),
//        "yellow-600": (name: "yellow", color: $yellow-600, number: "-600"),
//        "yellow-700": (name: "yellow", color: $yellow-700, number: "-700"),
//        "yellow-800": (name: "yellow", color: $yellow-800, number: "-800"),
//        "yellow-900": (name: "yellow", color: $yellow-900, number: "-900"),
//        "yellow-a100": (name: "yellow", color: $yellow-a100, number: "-a100"),
//        "yellow-a200": (name: "yellow", color: $yellow-a200, number: "-a200"),
//        "yellow-a400": (name: "yellow", color: $yellow-a400, number: "-a400"),
//        "yellow-a700": (name: "yellow", color: $yellow-a700, number: "-a700"),
//        "yellow": (name: "yellow", color: $yellow, number: ""),
//        "amber-50": (name: "amber", color: $amber-50, number: "-50"),
//        "amber-100": (name: "amber", color: $amber-100, number: "-100"),
//        "amber-200": (name: "amber", color: $amber-200, number: "-200"),
//        "amber-300": (name: "amber", color: $amber-300, number: "-300"),
//        "amber-400": (name: "amber", color: $amber-400, number: "-400"),
//        "amber-500": (name: "amber", color: $amber-500, number: "-500"),
//        "amber-600": (name: "amber", color: $amber-600, number: "-600"),
//        "amber-700": (name: "amber", color: $amber-700, number: "-700"),
//        "amber-800": (name: "amber", color: $amber-800, number: "-800"),
//        "amber-900": (name: "amber", color: $amber-900, number: "-900"),
//        "amber-a100": (name: "amber", color: $amber-a100, number: "-a100"),
//        "amber-a200": (name: "amber", color: $amber-a200, number: "-a200"),
//        "amber-a400": (name: "amber", color: $amber-a400, number: "-a400"),
//        "amber-a700": (name: "amber", color: $amber-a700, number: "-a700"),
//        "amber": (name: "amber", color: $amber, number: ""),
//        "orange-50": (name: "orange", color: $orange-50, number: "-50"),
//        "orange-100": (name: "orange", color: $orange-100, number: "-100"),
//        "orange-200": (name: "orange", color: $orange-200, number: "-200"),
//        "orange-300": (name: "orange", color: $orange-300, number: "-300"),
//        "orange-400": (name: "orange", color: $orange-400, number: "-400"),
//        "orange-500": (name: "orange", color: $orange-500, number: "-500"),
//        "orange-600": (name: "orange", color: $orange-600, number: "-600"),
//        "orange-700": (name: "orange", color: $orange-700, number: "-700"),
//        "orange-800": (name: "orange", color: $orange-800, number: "-800"),
//        "orange-900": (name: "orange", color: $orange-900, number: "-900"),
//        "orange-a100": (name: "orange", color: $orange-a100, number: "-a100"),
//        "orange-a200": (name: "orange", color: $orange-a200, number: "-a200"),
//        "orange-a400": (name: "orange", color: $orange-a400, number: "-a400"),
//        "orange-a700": (name: "orange", color: $orange-a700, number: "-a700"),
//        "orange": (name: "orange", color: $orange, number: ""),
//        "deep-orange-50": (name: "deep-orange", color: $deep-orange-50, number: "-50"),
//        "deep-orange-100": (name: "deep-orange", color: $deep-orange-100, number: "-100"),
//        "deep-orange-200": (name: "deep-orange", color: $deep-orange-200, number: "-200"),
//        "deep-orange-300": (name: "deep-orange", color: $deep-orange-300, number: "-300"),
//        "deep-orange-400": (name: "deep-orange", color: $deep-orange-400, number: "-400"),
//        "deep-orange-500": (name: "deep-orange", color: $deep-orange-500, number: "-500"),
//        "deep-orange-600": (name: "deep-orange", color: $deep-orange-600, number: "-600"),
//        "deep-orange-700": (name: "deep-orange", color: $deep-orange-700, number: "-700"),
//        "deep-orange-800": (name: "deep-orange", color: $deep-orange-800, number: "-800"),
//        "deep-orange-900": (name: "deep-orange", color: $deep-orange-900, number: "-900"),
//        "deep-orange-a100": (name: "deep-orange", color: $deep-orange-a100, number: "-a100"),
//        "deep-orange-a200": (name: "deep-orange", color: $deep-orange-a200, number: "-a200"),
//        "deep-orange-a400": (name: "deep-orange", color: $deep-orange-a400, number: "-a400"),
//        "deep-orange-a700": (name: "deep-orange", color: $deep-orange-a700, number: "-a700"),
//        "deep-orange": (name: "deep-orange", color: $deep-orange, number: ""),
//        "brown-50": (name: "brown", color: $brown-50, number: "-50"),
//        "brown-100": (name: "brown", color: $brown-100, number: "-100"),
//        "brown-200": (name: "brown", color: $brown-200, number: "-200"),
//        "brown-300": (name: "brown", color: $brown-300, number: "-300"),
//        "brown-400": (name: "brown", color: $brown-400, number: "-400"),
//        "brown-500": (name: "brown", color: $brown-500, number: "-500"),
//        "brown-600": (name: "brown", color: $brown-600, number: "-600"),
//        "brown-700": (name: "brown", color: $brown-700, number: "-700"),
//        "brown-800": (name: "brown", color: $brown-800, number: "-800"),
//        "brown-900": (name: "brown", color: $brown-900, number: "-900"),
//        "brown-a100": (name: "brown", color: $brown-a100, number: "-a100"),
//        "brown-a200": (name: "brown", color: $brown-a200, number: "-a200"),
//        "brown-a400": (name: "brown", color: $brown-a400, number: "-a400"),
//        "brown-a700": (name: "brown", color: $brown-a700, number: "-a700"),
//        "brown": (name: "brown", color: $brown, number: ""),
//        "grey-50": (name: "grey", color: $grey-50, number: "-50"),
//        "grey-100": (name: "grey", color: $grey-100, number: "-100"),
//        "grey-200": (name: "grey", color: $grey-200, number: "-200"),
//        "grey-300": (name: "grey", color: $grey-300, number: "-300"),
//        "grey-400": (name: "grey", color: $grey-400, number: "-400"),
//        "grey-500": (name: "grey", color: $grey-500, number: "-500"),
//        "grey-600": (name: "grey", color: $grey-600, number: "-600"),
//        "grey-700": (name: "grey", color: $grey-700, number: "-700"),
//        "grey-800": (name: "grey", color: $grey-800, number: "-800"),
//        "grey-900": (name: "grey", color: $grey-900, number: "-900"),
//        "grey-a100": (name: "grey", color: $grey-a100, number: "-a100"),
//        "grey-a200": (name: "grey", color: $grey-a200, number: "-a200"),
//        "grey-a400": (name: "grey", color: $grey-a400, number: "-a400"),
//        "grey-a700": (name: "grey", color: $grey-a700, number: "-a700"),
//        "grey": (name: "grey", color: $grey, number: ""),
//        "blue-grey-50": (name: "blue-grey", color: $blue-grey-50, number: "-50"),
//        "blue-grey-100": (name: "blue-grey", color: $blue-grey-100, number: "-100"),
//        "blue-grey-200": (name: "blue-grey", color: $blue-grey-200, number: "-200"),
//        "blue-grey-300": (name: "blue-grey", color: $blue-grey-300, number: "-300"),
//        "blue-grey-400": (name: "blue-grey", color: $blue-grey-400, number: "-400"),
//        "blue-grey-500": (name: "blue-grey", color: $blue-grey-500, number: "-500"),
//        "blue-grey-600": (name: "blue-grey", color: $blue-grey-600, number: "-600"),
//        "blue-grey-700": (name: "blue-grey", color: $blue-grey-700, number: "-700"),
//        "blue-grey-800": (name: "blue-grey", color: $blue-grey-800, number: "-800"),
//        "blue-grey-900": (name: "blue-grey", color: $blue-grey-900, number: "-900"),
//        "blue-grey-a100": (name: "blue-grey", color: $blue-grey-a100, number: "-a100"),
//        "blue-grey-a200": (name: "blue-grey", color: $blue-grey-a200, number: "-a200"),
//        "blue-grey-a400": (name: "blue-grey", color: $blue-grey-a400, number: "-a400"),
//        "blue-grey-a700": (name: "blue-grey", color: $blue-grey-a700, number: "-a700"),
//        "blue-grey": (name: "blue-grey", color: $blue-grey, number: "")
//);
