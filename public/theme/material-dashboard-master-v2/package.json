{"name": "material-dashboard", "version": "3.0.1", "description": "<PERSON><PERSON> Dashboard for Bootstrap 5", "main": "pages/dashboard.html", "directories": {"example": "root"}, "scripts": {"open-app": "gulp open-app", "start": "npm run open-app", "test": "echo \"Error: no test specified\" && exit 1", "watch": "gulp-watch"}, "repository": {"type": "git", "url": "git+https://github.com/creativetimofficial/material-dashboard.git"}, "keywords": ["material dashboard", "bootstrap 5", "dashboard", "creative tim", "html dashboard", "html css dashboard", "web dashboard", "freebie", "free bootstrap dashboard", "css3 dashboard", "bootstrap dashboard", "frontend", "responsive bootstrap dashboard"], "author": "Creative Tim <<EMAIL>> (https://www.creative-tim.com/)", "license": "MIT", "bugs": {"url": "https://github.com/creativetimofficial/material-dashboard/issues"}, "devDependencies": {"gulp": "^4.0.2", "gulp-autoprefixer": "^7.0.1", "gulp-clean": "^0.4.0", "gulp-install": "^1.1.0", "gulp-sass": "^5.1.0", "gulp-sourcemaps": "^2.6.5", "gulp-open": "^3.0.1", "sass": "^1.49.9"}, "homepage": "http://creative-tim.com/product/material-dashboard"}