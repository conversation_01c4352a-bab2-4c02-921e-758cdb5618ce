<IfModule mod_rewrite.c>
    RewriteEngine on
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
    RewriteCond %{REQUEST_FILENAME} -s [OR]
    RewriteCond %{REQUEST_FILENAME} -l [OR]
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^.*$ - [NC,L]
    RewriteRule ^(.*) index.php [NC,L]
</IfModule>


	php_value max_execution_time 99999999999
	php_value session.gc_maxlifetime 9999999999

    php_value memory_limit 700000M
    php_value upload_max_filesize 4000M
    php_value post_max_size 4000000M
    php_value upload_max_filesize 4000M
    php_value max_file_uploads 500M
    php_value max_input_vars 100000M

