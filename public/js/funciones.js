function regresarInventario(idstockventa) {
    var url = $("#url-regresar-apartado").val();

    Swal.fire({
        title: 'Por qué se va a regresar al inventario?',
        input: 'text',
        inputAttributes: {
            autocapitalize: 'off'
        },
        showCancelButton: true,
        confirmButtonText: 'Aceptar',
        showLoaderOnConfirm: true,
        preConfirm: (login) => {
            return fetch(url + `/` + idstockventa)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(response.statusText)
                    }
                    return response.json()
                })
                .catch(error => {
                    Swal.showValidationMessage(
                        `Request failed: ${error}`
                    )
                })
        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then((result) => {
        console.log(result.value);
        if (result.value.exito) {
            Swal.fire({
                icon: 'success',
                title: 'Listo',
                showConfirmButton: false

            })

            setTimeout(() => {
                document.location.reload();
            }, 2000);
        } else {
            Swal.fire({
                title: result.value.msj,
                icon: 'error',
            })
        }
    })
}


function visorAnuncio(idanuncios) {

    console.log("abrir visor anuncio ");

    $("#contenedor-modal-visor-anuncio").html("");

    let url = $("#url_app_abrir_visor_anuncio").val();
    console.log(url);

    $.ajax({
        method: "POST",
        url: url,
        data: {idanuncios: idanuncios}
    })
        .done(function (html) {
            $("#contenedor-modal-visor-anuncio").html(html);
            $("#modal-visor-anuncio").modal("show");

        });
}


function Mostrar(elemento) {
    var tipoPersona = elemento.value;
    var grupoMorales = document.getElementById('grupoMorales');
    var grupoFisicas = document.getElementById('grupoFisicas');

    if (tipoPersona === '1') {
        grupoMorales.style.display = 'block';
        grupoFisicas.style.display = 'none';
    } else {
        grupoMorales.style.display = 'none';
        grupoFisicas.style.display = 'block';
    }
}

function visualizarDocumento(idventa) {

    console.log("abrir visor ");

    $("#contenedor-modal-visor").html("");

    let url = $("#url-app_abrir_visor_ventas").val();
    console.log(url);

    $.ajax({
        method: "POST",
        url: url,
        data: {idventa: idventa}
    })
        .done(function (html) {
            $("#contenedor-modal-visor-venta").html(html);
            console.log(html);
            $("#modal-visor-ventas").modal("show");
        });
}
        
        
        
        
        

        
        