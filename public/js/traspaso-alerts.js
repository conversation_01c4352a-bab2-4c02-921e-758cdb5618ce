/**
 * Helper JavaScript para manejar alertas de SweetAlert2 en el módulo de Traspaso
 */
class TraspasoAlerts {
    
    /**
     * Configuraciones base para diferentes tipos de alerta
     */
    static configs = {
        success: {
            icon: 'success',
            confirmButtonColor: '#28a745',
            timer: 3000,
            showConfirmButton: false
        },
        error: {
            icon: 'error',
            confirmButtonColor: '#dc3545',
            showConfirmButton: true
        },
        warning: {
            icon: 'warning',
            confirmButtonColor: '#ffc107',
            showConfirmButton: true
        },
        info: {
            icon: 'info',
            confirmButtonColor: '#17a2b8',
            showConfirmButton: true
        },
        question: {
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#007bff',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Sí, continuar',
            cancelButtonText: 'Cancelar'
        }
    };

    /**
     * Muestra alerta de éxito
     */
    static success(title, text = null) {
        return Swal.fire({
            ...this.configs.success,
            title: title,
            text: text
        });
    }

    /**
     * Muestra alerta de error
     */
    static error(title, text = null, errorCode = null) {
        const config = {
            ...this.configs.error,
            title: title,
            text: text
        };
        
        if (errorCode) {
            config.footer = `Código de error: ${errorCode}`;
        }
        
        return Swal.fire(config);
    }

    /**
     * Muestra alerta de producto agregado
     */
    static productAdded(codigo, modelo = null) {
        const text = modelo ? `Producto: ${modelo} (Código: ${codigo})` : `Código: ${codigo}`;
        return this.success('Producto Agregado', text);
    }

    /**
     * Muestra alerta para múltiples productos procesados
     */
    static multipleProducts(successful, errors) {
        const totalSuccessful = successful.length;
        const totalErrors = errors.length;
        
        if (totalErrors === 0) {
            return this.success(
                'Productos Procesados',
                `Se agregaron ${totalSuccessful} productos exitosamente`
            );
        }
        
        if (totalSuccessful === 0) {
            return Swal.fire({
                ...this.configs.error,
                title: 'Error al Procesar Productos',
                text: `No se pudo agregar ningún producto. ${totalErrors} errores encontrados.`,
                footer: 'Revise los códigos e intente nuevamente'
            });
        }
        
        return Swal.fire({
            ...this.configs.warning,
            title: 'Procesamiento Parcial',
            text: `Se agregaron ${totalSuccessful} productos. ${totalErrors} productos presentaron errores.`,
            footer: 'Revise los errores en la tabla inferior'
        });
    }

    /**
     * Muestra confirmación para realizar traspaso
     */
    static confirmTransfer(sucursalOrigen, sucursalDestino, totalProductos) {
        return Swal.fire({
            ...this.configs.question,
            title: '¿Confirmar Traspaso?',
            html: `
                <div class='text-left'>
                    <p><strong>Origen:</strong> ${sucursalOrigen}</p>
                    <p><strong>Destino:</strong> ${sucursalDestino}</p>
                    <p><strong>Productos:</strong> ${totalProductos}</p>
                </div>
            `,
            confirmButtonText: 'Sí, realizar traspaso',
            cancelButtonText: 'Cancelar'
        });
    }

    /**
     * Muestra alerta de traspaso completado
     */
    static transferCompleted(idTraspaso) {
        return Swal.fire({
            ...this.configs.success,
            title: 'Traspaso Completado',
            text: `El traspaso #${idTraspaso} se ha creado exitosamente`,
            timer: 5000,
            showConfirmButton: true,
            confirmButtonText: 'Ver Detalles'
        });
    }

    /**
     * Muestra alerta de orden de salida aceptada
     */
    static exitOrderAccepted() {
        return this.success(
            'Orden Aceptada',
            'La orden de salida ha sido aceptada y los productos han sido transferidos'
        );
    }

    /**
     * Muestra alerta de orden de salida rechazada
     */
    static exitOrderRejected() {
        return Swal.fire({
            ...this.configs.info,
            title: 'Orden Rechazada',
            text: 'La orden de salida ha sido rechazada y los productos han sido liberados'
        });
    }

    /**
     * Muestra alerta de error de Google Sheets
     */
    static googleSheetsError(errorCode, details = null) {
        const errorMessages = {
            'TRASPASO_501': 'Rango de Google Sheets inválido',
            'TRASPASO_502': 'Error en la API de Google Sheets',
            'TRASPASO_503': 'Error de autenticación con Google Sheets'
        };
        
        const baseMessage = errorMessages[errorCode] || 'Error de Google Sheets';
        const text = details ? `${baseMessage}\n\nDetalles: ${details}` : baseMessage;
        
        return Swal.fire({
            ...this.configs.error,
            title: 'Error de Google Sheets',
            text: text,
            footer: `Código: ${errorCode}`
        });
    }

    /**
     * Muestra alerta de stock insuficiente
     */
    static insufficientStock(codigo, disponible, solicitado) {
        return Swal.fire({
            ...this.configs.warning,
            title: 'Stock Insuficiente',
            html: `
                <div class='text-left'>
                    <p><strong>Código:</strong> ${codigo}</p>
                    <p><strong>Disponible:</strong> ${disponible}</p>
                    <p><strong>Solicitado:</strong> ${solicitado}</p>
                </div>
            `
        });
    }

    /**
     * Muestra confirmación para eliminar producto
     */
    static confirmRemoveProduct(codigo) {
        return Swal.fire({
            ...this.configs.question,
            title: '¿Eliminar Producto?',
            text: `¿Está seguro de eliminar el producto ${codigo} del traspaso?`,
            confirmButtonText: 'Sí, eliminar',
            confirmButtonColor: '#dc3545'
        });
    }

    /**
     * Maneja respuestas AJAX y muestra alertas apropiadas
     */
    static handleAjaxResponse(response) {
        if (response.success) {
            if (response.data && response.data.errors && response.data.errors.length > 0) {
                // Respuesta parcialmente exitosa con errores
                this.multipleProducts(response.data.successful_codes || [], response.data.errors);
            } else {
                // Respuesta completamente exitosa
                this.success('Operación Exitosa', response.message);
            }
        } else {
            // Respuesta de error
            const errorCode = response.error ? response.error.code : null;
            const errorMessage = response.error ? response.error.message : response.message;
            
            if (errorCode && errorCode.startsWith('TRASPASO_5')) {
                // Error de Google Sheets
                this.googleSheetsError(errorCode, response.error.details);
            } else {
                // Error general
                this.error('Error', errorMessage, errorCode);
            }
        }
    }

    /**
     * Muestra alerta desde configuración JSON
     */
    static fromJson(configJson) {
        const config = typeof configJson === 'string' ? JSON.parse(configJson) : configJson;
        return Swal.fire(config);
    }

    /**
     * Muestra toast (notificación pequeña)
     */
    static toast(type, message) {
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.addEventListener('mouseenter', Swal.stopTimer);
                toast.addEventListener('mouseleave', Swal.resumeTimer);
            }
        });

        return Toast.fire({
            icon: type,
            title: message
        });
    }

    /**
     * Muestra loading mientras se procesa una operación
     */
    static showLoading(title = 'Procesando...', text = 'Por favor espere') {
        return Swal.fire({
            title: title,
            text: text,
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    }

    /**
     * Cierra cualquier alerta activa
     */
    static close() {
        Swal.close();
    }
}
