// tinymce-config.js
/*
tinymce.init({
  selector: '.tinymce',  
  plugins: 'a_tinymce_plugin',
  a_plugin_option: true,
  a_configuration_option: 400
}); 
*/


tinymce.init({
  selector: '.tiny',
  skin: 'jam',
  icons: 'jam',
  plugins: 'code image link lists',
  toolbar: 'undo redo | styles | bold italic underline forecolor backcolor | link image code | align | bullist numlist',
  menubar: false
  
});