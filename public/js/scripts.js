
function validarNumero(e) {
    e.value = (e.value + '').replace(/[^0-9,.]/g, '');
    var valor = $(e).val().split(',').join('');
    $(e).val(formatNumber(valor));

}
function formatNumber(num) {
    return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,");
}
function NaN2Zero(n){
    return isNaN( n ) ? 0 : n;
}

function applyFormatCurrency(sender) {
    $(sender).blur(function () {
        $(sender).html(null);
        $(this).formatCurrency({colorize: true, negativeFormat: '-%s%n', roundToDecimalPlace: 2});

    })
            .keyup(function (e) {
                var e = window.event || e;
                var keyUnicode = e.charCode || e.keyCode;
                if (e !== undefined) {
                    switch (keyUnicode) {
                        case 16:
                            break; // Shift
                        case 17:
                            break; // Ctrl
                        case 18:
                            break; // Alt
                        case 27:
                            this.value = '';
                            break; // Esc: clear entry
                        case 35:
                            break; // End
                        case 36:
                            break; // Home
                        case 37:
                            break; // cursor left
                        case 38:
                            break; // cursor up
                        case 39:
                            break; // cursor right
                        case 40:
                            break; // cursor down
                        case 78:
                            break; // N (Opera 9.63+ maps the "." from the number key section to the "N" key too!) (See: http://unixpapa.com/js/key.html search for ". Del")
                        case 110:
                            break; // . number block (Opera 9.63+ maps the "." from the number block to the "N" key (78) !!!)
                        case 190:
                            break; // .
                        default:
                            $(this).formatCurrency({colorize: true, negativeFormat: '-%s%n', roundToDecimalPlace: -1, eventOnDecimalsEntered: true});
                    }
                }
            })
            .bind('decimalsEntered', function (e, cents) {
                if (String(cents).length > 2) {
                    /* var errorMsg = 'Please do not enter any cents (0.' + cents + ')';
                     $('#formatWhileTypingAndWarnOnDecimalsEnteredNotification2').html(errorMsg);
                     log('Event on decimals entered: ' + errorMsg);*/
                }
            });
}
function formatoPesos(sender,decimales) {

    $(sender).blur(function () {
        $(sender).html(null);
        $(this).formatCurrency({colorize: true, negativeFormat: '-%s%n', roundToDecimalPlace: decimales});

    })
            .keyup(function (e) {
                var e = window.event || e;
                var keyUnicode = e.charCode || e.keyCode;
                if (e !== undefined) {
                    switch (keyUnicode) {
                        case 16:
                            break; // Shift
                        case 17:
                            break; // Ctrl
                        case 18:
                            break; // Alt
                        case 27:
                            this.value = '';
                            break; // Esc: clear entry
                        case 35:
                            break; // End
                        case 36:
                            break; // Home
                        case 37:
                            break; // cursor left
                        case 38:
                            break; // cursor up
                        case 39:
                            break; // cursor right
                        case 40:
                            break; // cursor down
                        case 78:
                            break; // N (Opera 9.63+ maps the "." from the number key section to the "N" key too!) (See: http://unixpapa.com/js/key.html search for ". Del")
                        case 110:
                            break; // . number block (Opera 9.63+ maps the "." from the number block to the "N" key (78) !!!)
                        case 190:
                            break; // .
                        default:
                            $(this).formatCurrency({colorize: true, negativeFormat: '-%s%n', roundToDecimalPlace: -1, eventOnDecimalsEntered: true});
                    }
                }
            })
            .bind('decimalsEntered', function (e, cents) {
                if (String(cents).length > 2) {
                    /* var errorMsg = 'Please do not enter any cents (0.' + cents + ')';
                     $('#formatWhileTypingAndWarnOnDecimalsEnteredNotification2').html(errorMsg);
                     log('Event on decimals entered: ' + errorMsg);*/
                }
            });
}
function quitarFormato(valor) {
    var v=valor;
    if(typeof valor === 'string' ){
      valor = valor.split(',').join('');
      valor = parseFloat(valor.replace('$', ""));
      valor=NaN2Zero(valor);
      //console.log("valor final "+valor);
    }

    return valor;
}
function ponerFormatoPesos(input,decimales=2){
    $(input).formatCurrency({colorize: true, negativeFormat: '-%s%n', roundToDecimalPlace: decimales});
}

function loadingGif(idcontenedor){

    $("#" + idcontenedor).html('<div align="center"><img style="max-width: 100%;" src="/img/log.gif"></div>');

}

function number_format(amount, decimals) {

    amount += ''; // por si pasan un numero en vez de un string
    amount = parseFloat(amount.replace(/[^0-9\.]/g, '')); // elimino cualquier cosa que no sea numero o punto

    decimals = decimals || 0; // por si la variable no fue fue pasada

    // si no es un numero o es igual a cero retorno el mismo cero
    if (isNaN(amount) || amount === 0)
        return parseFloat(0).toFixed(decimals);

    // si es mayor o menor que cero retorno el valor formateado como numero
    amount = '' + amount.toFixed(decimals);

    var amount_parts = amount.split('.'),
        regexp = /(\d+)(\d{3})/;

    while (regexp.test(amount_parts[0]))
        amount_parts[0] = amount_parts[0].replace(regexp, '$1' + ',' + '$2');

    return amount_parts.join('.');
}

function changeButton(id, enable = 0, icon = 0){
    if (enable == 1){
        $("#"+id).removeClass("disabled");
        if (icon == 0) $("#"+id).text('Enviar');


    } else{
        $("#"+id).addClass("disabled");
        if (icon == 0) $("#"+id).text('Enviando');

    }
    
}

$(document).ready(function(){

    url = window.location.href;

    urlMatch = "app/venta/list?";

    var parts = url.split('admin/');

    if (parts.length > 1) {
        
        var result = parts[1];

        if ( urlMatch.includes(result) ){
            $(".navbar-collapse").append("<h4 style='color:red'>Las ventas con folio rojo significan que un producto ha sido vendido debajo de su costo</h4>")
        }
    }
    
    

    
});

