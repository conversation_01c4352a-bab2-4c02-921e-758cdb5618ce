

@import "bootstrap/functions";
@import "bootstrap/variables";
@import "bootstrap/mixins";

$font-family-sans-serif-2: 'Roboto', sans-serif;
$font-family-serif: 'Source Serif Pro', serif;
$font-family-sans-serif: 'Roboto', sans-serif;



body {
	font-family: $font-family-sans-serif;
	background-color: #f8fafb;
}
p {
	color: darken(#ccc, 10%);
	font-weight: 300;
}
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
	font-family: $font-family-sans-serif;
}
a {
	transition: .3s all ease;
	&:hover {
		text-decoration: none!important;
	}
}
.content {
	padding: 7rem 0;
}
h2 {
	font-size: 20px;
}

.content {
	.bg {
		@include media-breakpoint-down(md) {
			height: 500px;
		}
	}
	.contents, .bg {
		width: 50%;
		@include media-breakpoint-down(lg) {
			width: 100%;
		}
		.form-group	{
			overflow: hidden;
			margin-bottom: 0;
			// border: 1px solid #efefef;
			padding: 15px 15px;
			border-bottom: none;
			position: relative;
			background: #edf2f5;
			border-bottom: 1px solid darken(#edf2f5, 2%);
			label {
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				transition: .3s all ease;
			}
			
			input {
				background: transparent;
			}

			&.first {
				border-top-left-radius: 7px;
				border-top-right-radius: 7px;
			}
			&.last {
				// border-bottom: 1px solid #efefef;
				border-bottom-left-radius: 7px;
				border-bottom-right-radius: 7px;
			}
			label {
				font-size: 12px;
				display: block;
				margin-bottom: 0;
				color: darken(#ccc, 10%);
			}
			&.focus {
				background: $white;
			}
			&.field--not-empty {
				label {
					margin-top: -20px;
				}
			}
		}
		.form-control {
			border: none;
			padding: 0;
			font-size: 20px;
			border-radius: 0;
			&:active, &:focus {
				outline: none;
				box-shadow: none;
			}
		}
	}
	.bg {
		background-size: cover;
		background-position: center;
	}
	a {
		color: #888;
		text-decoration: underline;
	}
	.btn {
		height: 54px;
		padding-left: 30px;
		padding-right: 30px;
	}
	.forgot-pass {
		position: relative;
		top: 2px;
		font-size: 14px;
	}
}

.social-login {
	a {
		
		text-decoration: none;
		
		position: relative;
		text-align: center;
		color: $white;
		margin-bottom: 10px;
		width: 50px;
		height: 50px;
		border-radius: 50%;
		display: inline-block;
		span {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
		}
		&:hover {
			color: $white;
		}
		&.facebook {
			background: #3b5998;
			&:hover {
				background: darken(#3b5998, 5%);
			}
		}
		&.twitter {
			background: #1da1f2;
			&:hover {
				background: darken(#1da1f2, 5%);
			}
		}
		&.google {
			background: #ea4335;
			&:hover {
				background: darken(#ea4335, 5%);
			}
		}
	}
}


.control {
  display: block;
  position: relative;
  padding-left: 30px;
  margin-bottom: 15px;
  cursor: pointer;
  font-size: 14px;
  .caption {
  	position: relative;
  	top: .2rem;
  	color: #888;
  }
}
.control input {
  position: absolute;
  z-index: -1;
  opacity: 0;
}
.control__indicator {
  position: absolute;
  top: 2px;
  left: 0;
  height: 20px;
  width: 20px;
  background: #e6e6e6;
  border-radius: 4px;
}
.control--radio .control__indicator {
  border-radius: 50%;
}
.control:hover input ~ .control__indicator,
.control input:focus ~ .control__indicator {
  background: #ccc;
}
.control input:checked ~ .control__indicator {
  background: $primary;
}
.control:hover input:not([disabled]):checked ~ .control__indicator,
.control input:checked:focus ~ .control__indicator {
  background: lighten($primary, 5%);
}
.control input:disabled ~ .control__indicator {
  background: #e6e6e6;
  opacity: 0.9;
  pointer-events: none;
}
.control__indicator:after {
	font-family: 'icomoon';
  content: '\e5ca';
  position: absolute;
  display: none;
  font-size: 16px;
  transition: .3s all ease;
}
.control input:checked ~ .control__indicator:after {
  display: block;
  color: #fff;
}
.control--checkbox .control__indicator:after {
	top: 50%;
	left: 50%;
	margin-top: -1px;
	transform: translate(-50%, -50%);
}
.control--checkbox input:disabled ~ .control__indicator:after {
  border-color: #7b7b7b;
}
.control--checkbox input:disabled:checked ~ .control__indicator {
  background-color: #7e0cf5;
  opacity: .2;
}