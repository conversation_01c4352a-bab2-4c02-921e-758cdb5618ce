body {
  background-image: linear-gradient(to top, #1C2743, #4B619A) !important;
  max-width: 100%;
  height: 100vh;
}
.row.login {
  height: 100vh;
  max-width: 100%;
}
.img-fluid.login {
  height: 100vh;
}
.row.logos {
  right: 75px;
  bottom: 45px;
  max-width: 100%;
}
.input-group-password {
  position: relative;
}
.btn-toggle-password {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  background: transparent;
  border: none;
}
.login-box-msg {
  display: none;
}
.form-label {
  font-family: "Lato", sans-serif;
  color: #FFFFFF;
}
.btn-primary {
  color: #FFFFFF;
  background-color: #061B3A;
  border-color: #061B3A;
  border-radius: 25px;
  width: 50%;
  font-family: "Lato", sans-serif;
  font-weight: 500;
}
.btn-primary:hover {
  color: #FFFFFF;
  background-color: #061B3A;
  border-color: #061B3A;
}
.btn-primary.disabled, .btn-primary:disabled {
  color: #FFFFFF;
  background-color: #061B3A;
  border-color: #061B3A;
}
/* xs */
@media (min-width: 320px) and (max-width: 576px) {
  .row.login {
    max-width: 100% !important;
    height: 100vh;
    display: flex;
    flex-direction: column-reverse;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    margin-right: auto;
  }
  .img-fluid.login {
    display: none;
  }
  .movil {
    max-width: 100%;
    padding: 0;
    align-content: center !important;
    align-items: center !important;
  }
  .row.logos {
    right: 15px;
    bottom: 36px;
    max-width: 100%;
  }
  .image-log {
    display: none !important;
  }
  .img-fluid.login {
    display: none !important;
  }
  .grupo {
    margin-bottom: 40px !important;
  }
  .form-label {
    font-size: 17px;
  }
  .btn-primary {
    font-size: 17px;
  }
  .form-control {
    height: calc(1.5em + 0.75rem + 7px);
    padding: 0.375rem 1.75rem;
    font-size: 17px;
  }
  .acceso {
    display: flex;
    flex-direction: column;
    justify-content: center;
    max-width: 84.66667%;
    padding: 0;
    align-content: center !important;
  }
}
/* sm */
@media (min-width: 576px) and (max-width: 768px) {
  .row.login {
    max-width: 100% !important;
    height: 100vh;
    display: flex;
    flex-direction: column-reverse;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: center;
  }
  .img-fluid.login {
    display: none;
  }
  .movil {
    max-width: 100%;
    padding: 0;
  }
  .row.logos {
    right: 15px;
    bottom: 36px;
    max-width: 100%;
  }
  .image-log {
    display: none;
  }
  .grupo {
    margin-bottom: 40px !important;
  }
  .form-label {
    font-size: 17px;
  }
  .btn-primary {
    font-size: 17px;
  }
  .form-control {
    height: calc(1.5em + 0.75rem + 7px);
    padding: 0.375rem 1.75rem;
    font-size: 17px;
  }
  .acceso {
    display: flex;
    flex-direction: column;
    justify-content: center;
    max-width: 84.66667%;
    padding: 0;
  }
}
/* md */
@media (min-width: 768px) and (max-width: 992px) {
  .row.login {
    max-width: 100% !important;
    height: 100vh;
    display: flex;
    flex-direction: column-reverse;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: center;
  }
  .img-fluid.login {
    display: none;
  }
  .movil {
    max-width: 100%;
    padding: 0;
  }
  .row.logos {
    right: 40px;
    bottom: 68px;
    max-width: 100%;
  }
  .image-log {
    display: none;
  }
  .grupo {
    margin-bottom: 52px !important;
  }
  .form-label {
    font-size: 22px;
  }
  .btn-primary {
    font-size: 22px;
  }
  .form-control {
    height: calc(1.5em + 0.75rem + 12px);
    padding: 0.375rem 1.75rem;
    font-size: 21px;
  }
}
/* lg */
@media (min-width: 992px) and (max-width: 1200px) {
  .row.logos {
    right: 25px;
    bottom: 45px;
    max-width: 100%;
  }
  .img-fluid.logo-empresa {
    width: 150px;
  }
  .acceso {
    display: flex;
    flex-direction: column;
    justify-content: center;
    max-width: 84.66667%;
  }
}
/* xl */
@media (min-width: 1200px) and (max-width: 1400px) {
  .row.logos {
    right: 25px;
    bottom: 45px;
    max-width: 100%;
  }
  .img-fluid.logo-empresa {
    width: 188px;
  }
  .acceso {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}
/* xxl */
@media (min-width: 1400px) {
  
}