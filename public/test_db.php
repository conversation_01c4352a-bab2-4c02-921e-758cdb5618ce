<?php
try {
    $pdo = new PDO("mysql:host=mysql;port=3306;dbname=grupooptimo_pv360_v13", "root", "root");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Conexión exitosa a la base de datos\n";
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM information_schema.tables WHERE table_schema = \"grupooptimo_pv360_v13\"");
    $result = $stmt->fetch();
    echo "Número de tablas en la base de datos: " . $result["total"] . "\n";
} catch(PDOException $e) {
    echo "Error de conexión: " . $e->getMessage() . "\n";
}
?>
