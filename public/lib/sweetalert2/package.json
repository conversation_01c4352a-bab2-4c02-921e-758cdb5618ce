{"_args": [["sweetalert2", "/var/www/html/pvoptica"]], "_from": "sweetalert2@latest", "_hasShrinkwrap": false, "_id": "sweetalert2@8.13.2", "_inCache": true, "_installable": true, "_location": "/sweetalert2", "_nodeVersion": "10.16.0", "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sweetalert2_8.13.2_1562572729274_0.7571074059406575"}, "_npmUser": {"email": "<EMAIL>", "name": "limonte"}, "_npmVersion": "6.9.0", "_phantomChildren": {}, "_requested": {"name": "sweetalert2", "raw": "sweetalert2", "rawSpec": "", "scope": null, "spec": "latest", "type": "tag"}, "_requiredBy": ["#USER"], "_resolved": "https://registry.npmjs.org/sweetalert2/-/sweetalert2-8.13.2.tgz", "_shasum": "fc147435ca5a44dd1c011bff242da04ae58ce5cc", "_shrinkwrap": null, "_spec": "sweetalert2", "_where": "/var/www/html/pvoptica", "author": {"email": "<EMAIL>", "name": "Limon Monte", "url": "https://limonte.github.io"}, "browser": "dist/sweetalert2.all.js", "bugs": {"url": "https://github.com/sweetalert2/sweetalert2/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/zenflow"}, {"name": "<PERSON>-<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/toverux"}, {"name": "<PERSON>", "url": "https://github.com/acupajoe"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/samturrell"}, {"name": "<PERSON>", "url": "https://github.com/birjolaxew"}, {"name": "<PERSON>", "url": "https://github.com/gverni"}], "dependencies": {}, "description": "A beautiful, responsive, customizable and accessible (WAI-ARIA) replacement for JavaScript's popup boxes, supported fork of sweetalert", "devDependencies": {"@babel/core": "^7.2.2", "@babel/plugin-transform-object-assign": "^7.2.0", "@babel/preset-env": "^7.3.1", "@sweetalert2/execute": "^1.0.0", "@sweetalert2/stylelint-config": "^1.1.2", "babel-loader": "^8.0.4", "babel-plugin-array-includes": "^2.0.3", "browser-sync": "^2.26.3", "ci-info": "^2.0.0", "custom-event-polyfill": "^1.0.6", "eslint": "^6.0.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.17.2", "eslint-plugin-node": "^9.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "gulp": "^4.0.0", "gulp-autoprefixer": "^6.0.0", "gulp-clean-css": "^4.0.0", "gulp-concat": "^2.6.1", "gulp-css2js": "^1.1.2", "gulp-eslint": "^6.0.0", "gulp-if": "^2.0.2", "gulp-noop": "^1.0.0", "gulp-rename": "^1.2.2", "gulp-rollup": "^2.16.2", "gulp-stylelint": "^9.0.0", "gulp-tslint": "^8.1.2", "gulp-typescript": "^5.0.0", "gulp-uglify": "^3.0.0", "jquery": "^3.3.1", "karma": "^4.0.0", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.2", "karma-firefox-launcher": "^1.1.0", "karma-ie-launcher": "^1.0.0", "karma-qunit": "^3.0.0", "karma-sauce-launcher": "^2.0.2", "karma-sourcemap-loader": "^0.3.7", "karma-spec-reporter": "^0.0.32", "karma-webpack": "^4.0.0", "merge2": "^1.2.3", "promise-polyfill": "^8.1.0", "qunit": "^2.8.0", "replace-in-file": "^4.0.0", "rollup": "^1.1.2", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-json": "^4.0.0", "sass": "^1.22.1", "sinon": "^7.2.3", "stylelint": "^10.0.0", "tslint": "^5.12.1", "typescript": "^3.2.4", "webpack": "^4.29.0"}, "directories": {}, "dist": {"fileCount": 75, "integrity": "sha512-si1mJhHXzA4R3LryswBJSYkn2PAgU5u+7iA0XbIn4q0vcdyrRul0UT5Z9s9n9z65Nv233FOZnbX6HsvApbcszg==", "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIve5CRA9TVsSAnZWagAALDAP/AqqDXbTUnkUZJBMl7j2\ngKp5w4TPYo0BjQPkt/ft3a7tRm/7FTyRbR31g/pEPtSuoR660rdItlv6mFFa\nNIRZ2fhVs0WUmjHqSUGbt3DF70L03cODlgmAlJKsxYRdxCb8lhkcfe+tpg6k\neGgm7OjV3+Soin4G/AQ8adpOzalCD3CVr4brcwQKpWDdc5nkGEPCjO9G2beb\n7qgggEVk1ecRort6cjQBWU9UQEu3ylw7r5iKBtJaTUsFLA9YukZUy5rncYxj\n5AhVqD3otiSkl3oaa+jZ/8NAr+ojRhgCvE/BmMwFxiolDC0ATHgAwmku6B+L\neJ5kKw0rvSQNu9ZUStX/pbjiNVZaD1FmQZ74OpwCsO30IsyshVCeqz8O4PfT\n3vfhceZjG86Dz2z0Lep1sz7RQo0GKxM5OAmGOQdUBf70yy1/I7yNAs3oX10O\nWvvIsdVNvjSPvAaRYIxSdAsUzlA0Lv+HeHu71jjNwKRIlFQ+B6Q93TD6bcms\npneOqLiP9XxXA4gk74ogtsaKSKlpM7eLCGTJ8mgrCIKGA1otem3mY3OgTwnb\nsrI/nec3st23rCOhrPM2Cx1BoRM7k5ckBIFSBVjDunKCBFvtylkSw4Yegx4T\ndDXu1yiwcTjJLEvsz1/Qx7kfyXBWKY+bslvw/v/H7py7+1jiCe3XjsrhV7ww\nFYiz\r\n=a4F3\r\n-----END PGP SIGNATURE-----\r\n", "shasum": "fc147435ca5a44dd1c011bff242da04ae58ce5cc", "tarball": "https://registry.npmjs.org/sweetalert2/-/sweetalert2-8.13.2.tgz", "unpackedSize": 565196}, "engines": {"node": ">=0.10.0"}, "gitHead": "f2dec5f8f053792788481aed9299cf6c5187d31e", "homepage": "https://sweetalert2.github.io/", "keywords": ["accessible", "alert", "confirm", "modal", "popup", "prompt", "sweetalert", "sweetalert2", "toast"], "license": "MIT", "main": "dist/sweetalert2.all.js", "maintainers": [{"name": "acupajoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "limonte", "email": "<EMAIL>"}, {"name": "sam<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zen_flow", "email": "<EMAIL>"}], "module": "src/sweetalert2.js", "name": "sweetalert2", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/sweetalert2/sweetalert2.git"}, "scripts": {"build": "gulp build", "check": "npm run check:lint && npm run check:require-in-node && npm run check:qunit && npm run check:qunit:minified && npm run check:ts", "check:jest": "jest --ci", "check:jsdelivr": "curl --location 'https://cdn.jsdelivr.net/npm/sweetalert2' 2>&1 | grep --quiet 'this.Swal'", "check:lint": "gulp lint", "check:qunit": "karma start karma.conf.js --single-run", "check:qunit:minified": "karma start karma.conf.js --single-run --minified", "check:qunit:minified:sauce": "karma start karma.conf.js --single-run --minified --sauce", "check:qunit:sauce": "karma start karma.conf.js --single-run --sauce", "check:require-in-node": "node test/require-in-node", "check:sauce": "npm run check:qunit:sauce && npm run check:qunit:minified:sauce", "check:third-party": "npm run check:unpkg && npm run check:jsdelivr && npm run check:wappa<PERSON><PERSON>", "check:ts": "tsc --lib dom,es6 sweetalert2.d.ts", "check:unpkg": "curl --location 'https://unpkg.com/sweetalert2' 2>&1 | grep --quiet 'this.Swal'", "check:wappalyzer": "curl 'https://api.wappalyzer.com/lookup-basic/v1/?url=https%3A%2F%2Fsweetalert2.github.io' 2>&1 | grep --quiet 'SweetAlert2'", "fix:lint": "eslint --fix .", "postinstall": "node bin/postinstall.js", "start": "gulp develop --continue-on-error --skip-minification --skip-standalone", "test": "npm run build && npm run check"}, "types": "sweetalert2.d.ts", "version": "8.13.2"}