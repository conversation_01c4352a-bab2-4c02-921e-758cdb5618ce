import { swalClasses } from '../../classes.js'
import * as dom from '../../dom/index.js'

function handleButtonsStyling (confirmButton, cancelButton, params) {
  dom.addClass([confirmButton, cancelButton], swalClasses.styled)

  // Buttons background colors
  if (params.confirmButtonColor) {
    confirmButton.style.backgroundColor = params.confirmButtonColor
  }
  if (params.cancelButtonColor) {
    cancelButton.style.backgroundColor = params.cancelButtonColor
  }

  // Loading state
  const confirmButtonBackgroundColor = window.getComputedStyle(confirmButton).getPropertyValue('background-color')
  confirmButton.style.borderLeftColor = confirmButtonBackgroundColor
  confirmButton.style.borderRightColor = confirmButtonBackgroundColor
}

function renderButton (button, buttonType, params) {
  dom.toggle(button, params['showC' + buttonType.substring(1) + 'Button'], 'inline-block')
  button.innerHTML = params[buttonType + 'ButtonText'] // Set caption text
  button.setAttribute('aria-label', params[buttonType + 'ButtonAriaLabel']) // ARIA label

  // Add buttons custom classes
  button.className = swalClasses[buttonType]
  dom.applyCustomClass(button, params.customClass, buttonType + 'Button')
  dom.addClass(button, params[buttonType + 'ButtonClass'])
}

export const renderActions = (instance, params) => {
  const actions = dom.getActions()
  const confirmButton = dom.getConfirmButton()
  const cancelButton = dom.getCancelButton()

  // Actions (buttons) wrapper
  if (!params.showConfirmButton && !params.showCancelButton) {
    dom.hide(actions)
  } else {
    dom.show(actions)
  }

  // Custom class
  dom.applyCustomClass(actions, params.customClass, 'actions')

  // Render confirm button
  renderButton(confirmButton, 'confirm', params)
  // render Cancel Button
  renderButton(cancelButton, 'cancel', params)

  if (params.buttonsStyling) {
    handleButtonsStyling(confirmButton, cancelButton, params)
  } else {
    dom.removeClass([confirmButton, cancelButton], swalClasses.styled)
    confirmButton.style.backgroundColor = confirmButton.style.borderLeftColor = confirmButton.style.borderRightColor = ''
    cancelButton.style.backgroundColor = cancelButton.style.borderLeftColor = cancelButton.style.borderRightColor = ''
  }
}
