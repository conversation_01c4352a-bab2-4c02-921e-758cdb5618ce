<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Line &amp; scatter</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        #chart {
      max-width: 650px;
      margin: 35px auto;
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/react@16.12/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@16.12/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prop-types@15.7.2/prop-types.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/babel-core/5.8.34/browser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-apexcharts@1.3.6/dist/react-apexcharts.iife.min.js"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    
  </head>

  <body>
    
    <div id="app"></div>

    <div id="html">
      &lt;div id=&quot;chart&quot;&gt;
  &lt;ReactApexChart options={this.state.options} series={this.state.series} type=&quot;line&quot; height={350} /&gt;
&lt;/div&gt;
    </div>

    <script type="text/babel">
      class ApexChart extends React.Component {
        constructor(props) {
          super(props);

          this.state = {
          
            series: [{
              name: 'Points',
              type: 'scatter',
            
              //2.14, 2.15, 3.61, 4.93, 2.4, 2.7, 4.2, 5.4, 6.1, 8.3
              data: [{
                x: 1,
                y: 2.14
              }, {
                x: 1.2,
                y: 2.19
              }, {
                x: 1.8,
                y: 2.43
              }, {
                x: 2.3,
                y: 3.8
              }, {
                x: 2.6,
                y: 4.14
              }, {
                x: 2.9,
                y: 5.4
              }, {
                x: 3.2,
                y: 5.8
              }, {
                x: 3.8,
                y: 6.04
              }, {
                x: 4.55,
                y: 6.77
              }, {
                x: 4.9,
                y: 8.1
              }, {
                x: 5.1,
                y: 9.4
              }, {
                x: 7.1,
                y: 7.14
              },{
                x: 9.18,
                y: 8.4
              }]
            }, {
              name: 'Line',
              type: 'line',
              data: [{
                x: 1,
                y: 2
              }, {
                x: 2,
                y: 3
              }, {
                x: 3,
                y: 4
              }, {
                x: 4,
                y: 5
              }, {
                x: 5,
                y: 6
              }, {
                x: 6,
                y: 7
              }, {
                x: 7,
                y: 8
              }, {
                x: 8,
                y: 9
              }, {
                x: 9,
                y: 10
              }, {
                x: 10,
                y: 11
              }]
            }],
            options: {
              chart: {
                height: 350,
                type: 'line',
              },
              fill: {
                type:'solid',
              },
              markers: {
                size: [6, 0]
              },
              tooltip: {
                shared: false,
                intersect: true,
              },
              legend: {
                show: false
              },
              xaxis: {
                type: 'numeric',
                min: 0,
                max: 12,
                tickAmount: 12
              }
            },
          
          
          };
        }

      

        render() {
          return (
            <div>
              <div id="chart">
                <ReactApexChart options={this.state.options} series={this.state.series} type="line" height={350} />
              </div>
              <div id="html-dist"></div>
            </div>
          );
        }
      }

      const domContainer = document.querySelector('#app');
      ReactDOM.render(React.createElement(ApexChart), domContainer);
    </script>

    
  </body>
</html>
