<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Syncing charts</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        #wrapper {
      padding-top: 20px;
      padding-left: 10px;
      background: #fff;
      border: 1px solid #ddd;
      box-shadow: 0 22px 35px -16px rgba(0, 0, 0, 0.1);
      max-width: 650px;
      margin: 35px auto;
    }
    
    .columns {
      columns: 2;
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/react@16.12/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@16.12/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prop-types@15.7.2/prop-types.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/babel-core/5.8.34/browser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-apexcharts@1.3.6/dist/react-apexcharts.iife.min.js"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    <script>
  // The global window.Apex variable below can be used to set common options for all charts on the page
  Apex = {
    chart: {
      height: 160,
    },
    dataLabels: {
      enabled: false
    },
    stroke: {
      curve: 'straight'
    },
    toolbar: {
      tools: {
        selection: false
      }
    },
    markers: {
      size: 6,
      hover: {
        size: 10
      }
    },
    tooltip: {
      followCursor: false,
      theme: 'dark',
      x: {
        show: false
      },
      marker: {
        show: false
      },
      y: {
        title: {
          formatter: function() {
            return ''
          }
        }
      }
    },
    grid: {
      clipMarkers: false
    },
    yaxis: {
      tickAmount: 2
    },
    xaxis: {
      type: 'datetime'
    },
  }
  
  /*
    // this function will generate output in this format
    // data = [
        [timestamp, 23],
        [timestamp, 33],
        [timestamp, 12]
        ...
    ]
  */
  function generateDayWiseTimeSeries(baseval, count, yrange) {
    var i = 0;
    var series = [];
    while (i < count) {
      var x = baseval;
      var y = Math.floor(Math.random() * (yrange.max - yrange.min + 1)) + yrange.min;
  
      series.push([x, y]);
      baseval += 86400000;
      i++;
    }
    return series;
  }
  </script>
  </head>

  <body>
    
    <div id="app"></div>

    <div id="html">
      &lt;div id=&quot;wrapper&quot;&gt;
  &lt;div id=&quot;chart-line&quot;&gt;
  &lt;ReactApexChart options={this.state.options} series={this.state.series} type=&quot;line&quot; height={160} /&gt;
&lt;/div&gt;
  &lt;div id=&quot;chart-line2&quot;&gt;
  &lt;ReactApexChart options={this.state.optionsLine2} series={this.state.seriesLine2} type=&quot;line&quot; height={160} /&gt;
&lt;/div&gt;
  &lt;div id=&quot;chart-area&quot;&gt;
  &lt;ReactApexChart options={this.state.optionsArea} series={this.state.seriesArea} type=&quot;area&quot; height={160} /&gt;
&lt;/div&gt;
  &lt;div class=&quot;columns&quot;&gt;
    &lt;div id=&quot;chart-small&quot;&gt;
  &lt;ReactApexChart options={this.state.optionsSmall} series={this.state.seriesSmall} type=&quot;area&quot; height={160} width={300} /&gt;
&lt;/div&gt;
    &lt;div id=&quot;chart-small2&quot;&gt;
  &lt;ReactApexChart options={this.state.optionsSmall2} series={this.state.seriesSmall2} type=&quot;area&quot; height={160} width={300} /&gt;
&lt;/div&gt;
  &lt;/div&gt;
  
&lt;/div&gt;
    </div>

    <script type="text/babel">
      class ApexChart extends React.Component {
        constructor(props) {
          super(props);

          this.state = {
          
            series: [{
              data: generateDayWiseTimeSeries(new Date('11 Feb 2017').getTime(), 20, {
                min: 10,
                max: 60
              })
            }],
            options: {
              chart: {
                id: 'fb',
                group: 'social',
                type: 'line',
                height: 160
              },
              colors: ['#008FFB']
            },
          
            seriesLine2: [{
              data: generateDayWiseTimeSeries(new Date('11 Feb 2017').getTime(), 20, {
                min: 10,
                max: 30
              })
            }],
            optionsLine2: {
              chart: {
                id: 'tw',
                group: 'social',
                type: 'line',
                height: 160
              },
              colors: ['#546E7A']
            },
          
            seriesArea: [{
              data: generateDayWiseTimeSeries(new Date('11 Feb 2017').getTime(), 20, {
                min: 10,
                max: 60
              })
            }],
            optionsArea: {
              chart: {
                id: 'yt',
                group: 'social',
                type: 'area',
                height: 160
              },
              colors: ['#00E396']
            },
          
            seriesSmall: [{
              data: generateDayWiseTimeSeries(new Date('11 Feb 2017').getTime(), 20, {
                min: 10,
                max: 60
              })
            }],
            optionsSmall: {
              chart: {
                id: 'ig',
                group: 'social',
                type: 'area',
                height: 160,
                width: 300
              },
              colors: ['#008FFB']
            },
          
            seriesSmall2: [{
              data: generateDayWiseTimeSeries(new Date('11 Feb 2017').getTime(), 20, {
                min: 10,
                max: 60
              })
            }],
            optionsSmall2: {
              chart: {
                id: 'li',
                group: 'social',
                type: 'area',
                height: 160,
                width: 300
              },
              colors: ['#546E7A']
            },
          
          
          };
        }

      

        render() {
          return (
            <div>
              <div id="wrapper">
                <div id="chart-line">
                <ReactApexChart options={this.state.options} series={this.state.series} type="line" height={160} />
              </div>
                <div id="chart-line2">
                <ReactApexChart options={this.state.optionsLine2} series={this.state.seriesLine2} type="line" height={160} />
              </div>
                <div id="chart-area">
                <ReactApexChart options={this.state.optionsArea} series={this.state.seriesArea} type="area" height={160} />
              </div>
                <div class="columns">
                  <div id="chart-small">
                <ReactApexChart options={this.state.optionsSmall} series={this.state.seriesSmall} type="area" height={160} width={300} />
              </div>
                  <div id="chart-small2">
                <ReactApexChart options={this.state.optionsSmall2} series={this.state.seriesSmall2} type="area" height={160} width={300} />
              </div>
                </div>
                
              </div>
              <div id="html-dist"></div>
            </div>
          );
        }
      }

      const domContainer = document.querySelector('#app');
      ReactDOM.render(React.createElement(ApexChart), domContainer);
    </script>

    
  </body>
</html>
