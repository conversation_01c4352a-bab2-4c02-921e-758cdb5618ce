<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Distributed Treemap</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        #chart {
      padding-right: 10px;
      max-width: 650px;
      margin: 35px auto;
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/react@16.12/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@16.12/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prop-types@15.7.2/prop-types.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/babel-core/5.8.34/browser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-apexcharts@1.3.6/dist/react-apexcharts.iife.min.js"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    
  </head>

  <body>
    
    <div id="app"></div>

    <div id="html">
      &lt;div id=&quot;chart&quot;&gt;
  &lt;ReactApexChart options={this.state.options} series={this.state.series} type=&quot;treemap&quot; height={350} /&gt;
&lt;/div&gt;
    </div>

    <script type="text/babel">
      class ApexChart extends React.Component {
        constructor(props) {
          super(props);

          this.state = {
          
            series: [
              {
                data: [
                  {
                    x: 'New Delhi',
                    y: 218
                  },
                  {
                    x: 'Kolkata',
                    y: 149
                  },
                  {
                    x: 'Mumbai',
                    y: 184
                  },
                  {
                    x: 'Ahmedabad',
                    y: 55
                  },
                  {
                    x: 'Bangaluru',
                    y: 84
                  },
                  {
                    x: 'Pune',
                    y: 31
                  },
                  {
                    x: 'Chennai',
                    y: 70
                  },
                  {
                    x: 'Jaipur',
                    y: 30
                  },
                  {
                    x: 'Surat',
                    y: 44
                  },
                  {
                    x: 'Hyderabad',
                    y: 68
                  },
                  {
                    x: 'Lucknow',
                    y: 28
                  },
                  {
                    x: 'Indore',
                    y: 19
                  },
                  {
                    x: 'Kanpur',
                    y: 29
                  }
                ]
              }
            ],
            options: {
              legend: {
                show: false
              },
              chart: {
                height: 350,
                type: 'treemap'
              },
              title: {
                text: 'Distibuted Treemap (different color for each cell)',
                align: 'center'
              },
              colors: [
                '#3B93A5',
                '#F7B844',
                '#ADD8C7',
                '#EC3C65',
                '#CDD7B6',
                '#C1F666',
                '#D43F97',
                '#1E5D8C',
                '#421243',
                '#7F94B0',
                '#EF6537',
                '#C0ADDB'
              ],
              plotOptions: {
                treemap: {
                  distributed: true,
                  enableShades: false
                }
              }
            },
          
          
          };
        }

      

        render() {
          return (
            <div>
              <div id="chart">
                <ReactApexChart options={this.state.options} series={this.state.series} type="treemap" height={350} />
              </div>
              <div id="html-dist"></div>
            </div>
          );
        }
      }

      const domContainer = document.querySelector('#app');
      ReactDOM.render(React.createElement(ApexChart), domContainer);
    </script>

    
  </body>
</html>
