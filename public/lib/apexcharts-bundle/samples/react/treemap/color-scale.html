<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Color-scale Treemap</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        #chart {
      padding-right: 10px;
      max-width: 650px;
      margin: 35px auto;
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/react@16.12/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@16.12/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prop-types@15.7.2/prop-types.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/babel-core/5.8.34/browser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-apexcharts@1.3.6/dist/react-apexcharts.iife.min.js"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    
  </head>

  <body>
    
    <div id="app"></div>

    <div id="html">
      &lt;div id=&quot;chart&quot;&gt;
  &lt;ReactApexChart options={this.state.options} series={this.state.series} type=&quot;treemap&quot; height={350} /&gt;
&lt;/div&gt;
    </div>

    <script type="text/babel">
      class ApexChart extends React.Component {
        constructor(props) {
          super(props);

          this.state = {
          
            series: [
              {
                data: [
                  {
                    x: 'INTC',
                    y: 1.2
                  },
                  {
                    x: 'GS',
                    y: 0.4
                  },
                  {
                    x: 'CVX',
                    y: -1.4
                  },
                  {
                    x: 'GE',
                    y: 2.7
                  },
                  {
                    x: 'CAT',
                    y: -0.3
                  },
                  {
                    x: 'RTX',
                    y: 5.1
                  },
                  {
                    x: 'CSCO',
                    y: -2.3
                  },
                  {
                    x: 'JNJ',
                    y: 2.1
                  },
                  {
                    x: 'PG',
                    y: 0.3
                  },
                  {
                    x: 'TRV',
                    y: 0.12
                  },
                  {
                    x: 'MMM',
                    y: -2.31
                  },
                  {
                    x: 'NKE',
                    y: 3.98
                  },
                  {
                    x: 'IYT',
                    y: 1.67
                  }
                ]
              }
            ],
            options: {
              legend: {
                show: false
              },
              chart: {
                height: 350,
                type: 'treemap'
              },
              title: {
                text: 'Treemap with Color scale'
              },
              dataLabels: {
                enabled: true,
                style: {
                  fontSize: '12px',
                },
                formatter: function(text, op) {
                  return [text, op.value]
                },
                offsetY: -4
              },
              plotOptions: {
                treemap: {
                  enableShades: true,
                  shadeIntensity: 0.5,
                  reverseNegativeShade: true,
                  colorScale: {
                    ranges: [
                      {
                        from: -6,
                        to: 0,
                        color: '#CD363A'
                      },
                      {
                        from: 0.001,
                        to: 6,
                        color: '#52B12C'
                      }
                    ]
                  }
                }
              }
            },
          
          
          };
        }

      

        render() {
          return (
            <div>
              <div id="chart">
                <ReactApexChart options={this.state.options} series={this.state.series} type="treemap" height={350} />
              </div>
              <div id="html-dist"></div>
            </div>
          );
        }
      }

      const domContainer = document.querySelector('#app');
      ReactDOM.render(React.createElement(ApexChart), domContainer);
    </script>

    
  </body>
</html>
