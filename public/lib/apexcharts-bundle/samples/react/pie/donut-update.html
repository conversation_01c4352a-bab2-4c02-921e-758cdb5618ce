<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Simple Pie</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        #chart {
      max-width: 480px;
      margin: 35px auto;
      padding: 0;
    }
    .actions {
      top: -10px;
      position: relative;
      z-index: 10;
      max-width: 400px;
      margin: 0 auto;
    }
    button {
      color: #fff;
      background: #20b2aa;
      padding: 5px 10px;
      margin: 2px;
      font-weight: bold;
      font-size: 13px;
      border-radius: 5px;
    }
    p {
      margin: 10px 0;
    }
    @media only screen and (max-width: 480px) {
      .actions {
        margin-top: 0;
        left: 0
      }
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/react@16.12/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@16.12/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prop-types@15.7.2/prop-types.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/babel-core/5.8.34/browser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-apexcharts@1.3.6/dist/react-apexcharts.iife.min.js"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    
  </head>

  <body>
    
    <div id="app"></div>

    <div id="html">
      &lt;div&gt;
  &lt;div class=&quot;chart-wrap&quot;&gt;
    &lt;div id=&quot;chart&quot;&gt;
  &lt;ReactApexChart options={this.state.options} series={this.state.series} type=&quot;donut&quot; width={380} /&gt;
&lt;/div&gt;
  &lt;/div&gt;

  &lt;div class=&quot;actions&quot;&gt;
    &lt;button
        
        onClick={() =&gt; this.appendData()}
        &gt;
      + ADD
    &lt;/button&gt;
    &amp;nbsp;
    &lt;button
        
        onClick={() =&gt; this.removeData()}
        &gt;
      - REMOVE
    &lt;/button&gt;
    &amp;nbsp;
    &lt;button
        
        onClick={() =&gt; this.randomize()}
        &gt;
      RANDOMIZE
    &lt;/button&gt;
    &amp;nbsp;
    &lt;button
        
        onClick={() =&gt; this.reset()}
        &gt;
      RESET
    &lt;/button&gt;
  &lt;/div&gt;
&lt;/div&gt;
    </div>

    <script type="text/babel">
      class ApexChart extends React.Component {
        constructor(props) {
          super(props);

          this.state = {
          
            series: [44, 55, 13, 33],
            options: {
              chart: {
                width: 380,
                type: 'donut',
              },
              dataLabels: {
                enabled: false
              },
              responsive: [{
                breakpoint: 480,
                options: {
                  chart: {
                    width: 200
                  },
                  legend: {
                    show: false
                  }
                }
              }],
              legend: {
                position: 'right',
                offsetY: 0,
                height: 230,
              }
            },
          
          
          };
        }

      
        appendData() {
          var arr = this.state.series.slice()
          arr.push(Math.floor(Math.random() * (100 - 1 + 1)) + 1)
        
          this.setState({
            series: arr
          })
        }
        
        removeData() {
          if(this.state.series.length === 1) return
          
          var arr = this.state.series.slice()
          arr.pop()
        
          this.setState({
            series: arr
          })
        }
        
        randomize() {
          this.setState({
            series: this.state.series.map(function() {
              return Math.floor(Math.random() * (100 - 1 + 1)) + 1
            })
          })
        }
        
        reset() {
          this.setState({
            series: [44, 55, 13, 33]
          })
        }
      

        render() {
          return (
            <div>
              <div>
                <div class="chart-wrap">
                  <div id="chart">
                <ReactApexChart options={this.state.options} series={this.state.series} type="donut" width={380} />
              </div>
                </div>
              
                <div class="actions">
                  <button
                      
                      onClick={() => this.appendData()}
                      >
                    + ADD
                  </button>
                  &nbsp;
                  <button
                      
                      onClick={() => this.removeData()}
                      >
                    - REMOVE
                  </button>
                  &nbsp;
                  <button
                      
                      onClick={() => this.randomize()}
                      >
                    RANDOMIZE
                  </button>
                  &nbsp;
                  <button
                      
                      onClick={() => this.reset()}
                      >
                    RESET
                  </button>
                </div>
              </div>
              <div id="html-dist"></div>
            </div>
          );
        }
      }

      const domContainer = document.querySelector('#app');
      ReactDOM.render(React.createElement(ApexChart), domContainer);
    </script>

    
  </body>
</html>
