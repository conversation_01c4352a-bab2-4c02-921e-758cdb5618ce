<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Line Chart with Annotations</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        #chart {
      max-width: 650px;
      margin: 35px auto;
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/react@16.12/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@16.12/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prop-types@15.7.2/prop-types.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/babel-core/5.8.34/browser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-apexcharts@1.3.6/dist/react-apexcharts.iife.min.js"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    <script src="../../assets/stock-prices.js"></script>
  </head>

  <body>
    
    <div id="app"></div>

    <div id="html">
      &lt;div id=&quot;chart&quot;&gt;
  &lt;ReactApexChart options={this.state.options} series={this.state.series} type=&quot;line&quot; /&gt;
&lt;/div&gt;
    </div>

    <script type="text/babel">
      class ApexChart extends React.Component {
        constructor(props) {
          super(props);

          this.state = {
          
            series: [{
                type: 'bar',
                data: series.monthDataSeries2.prices
            }, {
                type: 'line',
                data: series.monthDataSeries1.prices
            }],
            options: {
              chart: {
                type: 'line',
                id: 'chart',
                sparkline: {
                  //  enabled: true
                }
              },
              annotations: {
                yaxis: [{
                  y: 8200,
                  borderColor: '#FEB019',
                  label: {
                    borderColor: '#333',
                    style: {
                      fontSize: '15px',
                      color: '#333',
                      background: '#FEB019',
                    },
                    text: 'Y-axis annotation',
                  }
                }],
                xaxis: [{
                  x: new Date('23 Nov 2017').getTime(),
                  borderColor: '#00E396',
                  label: {
                    borderColor: '#00E396',
                    style: {
                      fontSize: '15px',
                      color: '#fff',
                      background: '#00E396',
                    },
                    offsetY: -10,
                    text: 'Vertical',
                  }
                }],
                points: [{
                  x: new Date('01 Dec 2017').getTime(),
                  y: 9025,
                  label: {
                    borderColor: '#FF4560',
                    offsetY: 0,
                    style: {
                      fontSize: '15px',
                      color: '#fff',
                      background: '#FF4560',
                    },
                    text: 'All time high',
                  }
                }]
              },
              plotOptions: {
                bar: {
                  columnWidth: '50%'
                }
              },
              markers: {
                size: 0
              },
              dataLabels: {
                enabled: false
              },
              stroke: {
                curve: 'straight'
              },
              legend: {
                show: false,
              },
              labels: series.monthDataSeries1.dates,
              xaxis: {
                type: 'datetime',
              }
            },
          
          
          };
        }

      
        componentDidMount() {
          ApexCharts.exec('chart', 'addYaxisAnnotation', {
            id: 'yaxis-anno',
            y: 9000,
            borderColor: '#FEB019',
            label: {
              borderColor: '#333',
              style: {
                fontSize: '15px',
                color: '#333',
                background: '#FEB019',
              },
              text: 'Y-axis - runtime',
            }
          });
        
          ApexCharts.exec('chart', 'addXaxisAnnotation', {
            id: 'xaxis-anno',
            x: new Date('25 Nov 2017').getTime(),
            borderColor: '#00E396',
            label: {
              orientation: 'vertical',
              borderColor: '#00E396',
              style: {
                fontSize: '15px',
                color: '#fff',
                background: '#00E396',
              },
              offsetY: -10,
              text: 'xaxis - runtime',
            }
          });
        
          ApexCharts.exec('chart', 'addPointAnnotation', {
            id: 'point-anno',
            x: new Date('17 Nov 2017').getTime(),
            y: 9425,
            label: {
              borderColor: '#FF4560',
              offsetY: 0,
              style: {
                fontSize: '15px',
                color: '#fff',
                background: '#FF4560',
              },
              text: 'Point - runtime',
            }
          });
        
          ApexCharts.exec('chart', 'removeAnnotation', 'point-anno');
        }
      

        render() {
          return (
            <div>
              <div id="chart">
                <ReactApexChart options={this.state.options} series={this.state.series} type="line" />
              </div>
              <div id="html-dist"></div>
            </div>
          );
        }
      }

      const domContainer = document.querySelector('#app');
      ReactDOM.render(React.createElement(ApexChart), domContainer);
    </script>

    
  </body>
</html>
