<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Dynamic Loaded Column Chart</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        body {
      background: #fff;
    }
    
    #wrap {
      margin: 45px auto;
      max-width: 800px;
      position: relative;
    }
    
    .chart-box {
      padding-left: 0;
    }
    
    #chart-year,
    #chart-quarter {
      width: 96%;
      max-width: 48%;
      box-shadow: none;
      padding-left: 0;
      padding-top: 20px;
      background: #fff;
      border: 1px solid #ddd;
    }
    
    #chart-year {
      float: left;
      position: relative;
      transition: 1s ease transform;
      z-index: 3;
    }
    
    #chart-year.chart-quarter-activated {
      transform: translateX(0);
      transition: 1s ease transform;
    }
    
    #chart-quarter {
      float: left;
      position: relative;
      z-index: -2;
      transition: 1s ease transform;
    }
    
    #chart-quarter.active {
      transition: 1.1s ease-in-out transform;
      transform: translateX(0);
      z-index: 1;
    }
    
    @media screen and (min-width: 480px) {
      #chart-year {
        transform: translateX(50%);
      }
      #chart-quarter {
        transform: translateX(-50%);
      }
    }
    
    select#model {
      display: none;
      position: absolute;
      top: -40px;
      left: 0;
      z-index: 2;
      cursor: pointer;
      transform: scale(0.8);
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/react@16.12/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@16.12/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prop-types@15.7.2/prop-types.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/babel-core/5.8.34/browser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-apexcharts@1.3.6/dist/react-apexcharts.iife.min.js"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    <script>
  Apex = {
    chart: {
      toolbar: {
        show: false
      }
    },
    tooltip: {
      shared: false
    },
    legend: {
      show: false
    }
  }
  
  var colors = ['#008FFB', '#00E396', '#FEB019', '#FF4560', '#775DD0', '#00D9E9', '#FF66C3'];
  
  /**
   * Randomize array element order in-place.
   * Using Durstenfeld shuffle algorithm.
   */
  function shuffleArray(array) {
    for (var i = array.length - 1; i > 0; i--) {
      var j = Math.floor(Math.random() * (i + 1));
      var temp = array[i];
      array[i] = array[j];
      array[j] = temp;
    }
    return array;
  }
  
  var arrayData = [{
    y: 400,
    quarters: [{
      x: 'Q1',
      y: 120
    }, {
      x: 'Q2',
      y: 90
    }, {
      x: 'Q3',
      y: 100
    }, {
      x: 'Q4',
      y: 90
    }]
  }, {
    y: 430,
    quarters: [{
      x: 'Q1',
      y: 120
    }, {
      x: 'Q2',
      y: 110
    }, {
      x: 'Q3',
      y: 90
    }, {
      x: 'Q4',
      y: 110
    }]
  }, {
    y: 448,
    quarters: [{
      x: 'Q1',
      y: 70
    }, {
      x: 'Q2',
      y: 100
    }, {
      x: 'Q3',
      y: 140
    }, {
      x: 'Q4',
      y: 138
    }]
  }, {
    y: 470,
    quarters: [{
      x: 'Q1',
      y: 150
    }, {
      x: 'Q2',
      y: 60
    }, {
      x: 'Q3',
      y: 190
    }, {
      x: 'Q4',
      y: 70
    }]
  }, {
    y: 540,
    quarters: [{
      x: 'Q1',
      y: 120
    }, {
      x: 'Q2',
      y: 120
    }, {
      x: 'Q3',
      y: 130
    }, {
      x: 'Q4',
      y: 170
    }]
  }, {
    y: 580,
    quarters: [{
      x: 'Q1',
      y: 170
    }, {
      x: 'Q2',
      y: 130
    }, {
      x: 'Q3',
      y: 120
    }, {
      x: 'Q4',
      y: 160
    }]
  }];
  
  function makeData() {
    var dataSet = shuffleArray(arrayData)
  
    var dataYearSeries = [{
      x: "2011",
      y: dataSet[0].y,
      color: colors[0],
      quarters: dataSet[0].quarters
    }, {
      x: "2012",
      y: dataSet[1].y,
      color: colors[1],
      quarters: dataSet[1].quarters
    }, {
      x: "2013",
      y: dataSet[2].y,
      color: colors[2],
      quarters: dataSet[2].quarters
    }, {
      x: "2014",
      y: dataSet[3].y,
      color: colors[3],
      quarters: dataSet[3].quarters
    }, {
      x: "2015",
      y: dataSet[4].y,
      color: colors[4],
      quarters: dataSet[4].quarters
    }, {
      x: "2016",
      y: dataSet[5].y,
      color: colors[5],
      quarters: dataSet[5].quarters
    }];
  
    return dataYearSeries
  }
  
  function updateQuarterChart(sourceChart, destChartIDToUpdate) {
    var series = [];
    var seriesIndex = 0;
    var colors = []
  
    if (sourceChart.w.globals.selectedDataPoints[0]) {
      var selectedPoints = sourceChart.w.globals.selectedDataPoints;
      for (var i = 0; i < selectedPoints[seriesIndex].length; i++) {
        var selectedIndex = selectedPoints[seriesIndex][i];
        var yearSeries = sourceChart.w.config.series[seriesIndex];
        series.push({
          name: yearSeries.data[selectedIndex].x,
          data: yearSeries.data[selectedIndex].quarters
        })
        colors.push(yearSeries.data[selectedIndex].color)
      }
  
      if (series.length === 0) series = [{
        data: []
      }]
  
      return ApexCharts.exec(destChartIDToUpdate, 'updateOptions', {
        series: series,
        colors: colors,
        fill: {
          colors: colors
        }
      })
    }
  }
  </script>
  </head>

  <body>
    
    <div id="app"></div>

    <div id="html">
      &lt;div id=&quot;wrap&quot;&gt;
  &lt;select id=&quot;model&quot; class=&quot;flat-select&quot;
      onChange={() =&gt; this.changeData()}
      &gt;
    &lt;option value=&quot;iphone5&quot;&gt;iPhone 5&lt;/option&gt;
    &lt;option value=&quot;iphone6&quot;&gt;iPhone 6&lt;/option&gt;
    &lt;option value=&quot;iphone7&quot;&gt;iPhone 7&lt;/option&gt;
  &lt;/select&gt;
  &lt;div id=&quot;chart-year&quot;&gt;
  &lt;ReactApexChart options={this.state.options} series={this.state.series} type=&quot;bar&quot; height={400} /&gt;
&lt;/div&gt;
  &lt;div id=&quot;chart-quarter&quot;&gt;
  &lt;ReactApexChart options={this.state.optionsQuarter} series={this.state.seriesQuarter} type=&quot;bar&quot; height={400} /&gt;
&lt;/div&gt;
&lt;/div&gt;
    </div>

    <script type="text/babel">
      class ApexChart extends React.Component {
        constructor(props) {
          super(props);

          this.state = {
          
            series: [{
              data: makeData()
            }],
            options: {
              chart: {
                id: 'barYear',
                height: 400,
                width: '100%',
                type: 'bar',
                events: {
                  dataPointSelection: function (e, chart, opts) {
                    var quarterChartEl = document.querySelector("#chart-quarter");
                    var yearChartEl = document.querySelector("#chart-year");
              
                    if (opts.selectedDataPoints[0].length === 1) {
                      if (quarterChartEl.classList.contains("active")) {
                        updateQuarterChart(chart, 'barQuarter')
                      } else {
                        yearChartEl.classList.add("chart-quarter-activated")
                        quarterChartEl.classList.add("active");
                        updateQuarterChart(chart, 'barQuarter')
                      }
                    } else {
                      updateQuarterChart(chart, 'barQuarter')
                    }
              
                    if (opts.selectedDataPoints[0].length === 0) {
                      yearChartEl.classList.remove("chart-quarter-activated")
                      quarterChartEl.classList.remove("active");
                    }
              
                  },
                  updated:  function (chart) {
                    updateQuarterChart(chart, 'barQuarter')
                  }
                }
              },
              plotOptions: {
                bar: {
                  distributed: true,
                  horizontal: true,
                  barHeight: '75%',
                  dataLabels: {
                    position: 'bottom'
                  }
                }
              },
              dataLabels: {
                enabled: true,
                textAnchor: 'start',
                style: {
                  colors: ['#fff']
                },
                formatter: function (val, opt) {
                  return opt.w.globals.labels[opt.dataPointIndex]
                },
                offsetX: 0,
                dropShadow: {
                  enabled: true
                }
              },
              
              colors: colors,
              
              states: {
                normal: {
                  filter: {
                    type: 'desaturate'
                  }
                },
                active: {
                  allowMultipleDataPointsSelection: true,
                  filter: {
                    type: 'darken',
                    value: 1
                  }
                }
              },
              tooltip: {
                x: {
                  show: false
                },
                y: {
                  title: {
                    formatter: function (val, opts) {
                      return opts.w.globals.labels[opts.dataPointIndex]
                    }
                  }
                }
              },
              title: {
                text: 'Yearly Results',
                offsetX: 15
              },
              subtitle: {
                text: '(Click on bar to see details)',
                offsetX: 15
              },
              yaxis: {
                labels: {
                  show: false
                }
              }
            },
          
            seriesQuarter: [{
              data: []
            }],
            optionsQuarter: {
              chart: {
                id: 'barQuarter',
                height: 400,
                width: '100%',
                type: 'bar',
                stacked: true
              },
              plotOptions: {
                bar: {
                  columnWidth: '50%',
                  horizontal: false
                }
              },
              legend: {
                show: false
              },
              grid: {
                yaxis: {
                  lines: {
                    show: false,
                  }
                },
                xaxis: {
                  lines: {
                    show: true,
                  }
                }
              },
              yaxis: {
                labels: {
                  show: false
                }
              },
              title: {
                text: 'Quarterly Results',
                offsetX: 10
              },
              tooltip: {
                x: {
                  formatter: function (val, opts) {
                    return opts.w.globals.seriesNames[opts.seriesIndex]
                  }
                },
                y: {
                  title: {
                    formatter: function (val, opts) {
                      return opts.w.globals.labels[opts.dataPointIndex]
                    }
                  }
                }
              }
            },
          
          
          };
        }

      
        changeData() {
          Apex.exec('barYear', 'updateSeries', [{
            data: makeData()
          }])
        }
      

        render() {
          return (
            <div>
              <div id="wrap">
                <select id="model" class="flat-select"
                    onChange={() => this.changeData()}
                    >
                  <option value="iphone5">iPhone 5</option>
                  <option value="iphone6">iPhone 6</option>
                  <option value="iphone7">iPhone 7</option>
                </select>
                <div id="chart-year">
                <ReactApexChart options={this.state.options} series={this.state.series} type="bar" height={400} />
              </div>
                <div id="chart-quarter">
                <ReactApexChart options={this.state.optionsQuarter} series={this.state.seriesQuarter} type="bar" height={400} />
              </div>
              </div>
              <div id="html-dist"></div>
            </div>
          );
        }
      }

      const domContainer = document.querySelector('#app');
      ReactDOM.render(React.createElement(ApexChart), domContainer);
    </script>

    
  </body>
</html>
