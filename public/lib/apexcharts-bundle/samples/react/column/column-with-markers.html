<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Bar with Markers</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        #chart {
      max-width: 650px;
      margin: 35px auto;
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/react@16.12/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@16.12/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prop-types@15.7.2/prop-types.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/babel-core/5.8.34/browser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-apexcharts@1.3.6/dist/react-apexcharts.iife.min.js"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    
  </head>

  <body>
    
    <div id="app"></div>

    <div id="html">
      &lt;div id=&quot;chart&quot;&gt;
  &lt;ReactApexChart options={this.state.options} series={this.state.series} type=&quot;bar&quot; height={350} /&gt;
&lt;/div&gt;
    </div>

    <script type="text/babel">
      class ApexChart extends React.Component {
        constructor(props) {
          super(props);

          this.state = {
          
            series: [
              {
                name: 'Actual',
                data: [
                  {
                    x: '2011',
                    y: 1292,
                    goals: [
                      {
                        name: 'Expected',
                        value: 1400,
                        strokeHeight: 5,
                        strokeColor: '#775DD0'
                      }
                    ]
                  },
                  {
                    x: '2012',
                    y: 4432,
                    goals: [
                      {
                        name: 'Expected',
                        value: 5400,
                        strokeHeight: 5,
                        strokeColor: '#775DD0'
                      }
                    ]
                  },
                  {
                    x: '2013',
                    y: 5423,
                    goals: [
                      {
                        name: 'Expected',
                        value: 5200,
                        strokeHeight: 5,
                        strokeColor: '#775DD0'
                      }
                    ]
                  },
                  {
                    x: '2014',
                    y: 6653,
                    goals: [
                      {
                        name: 'Expected',
                        value: 6500,
                        strokeHeight: 5,
                        strokeColor: '#775DD0'
                      }
                    ]
                  },
                  {
                    x: '2015',
                    y: 8133,
                    goals: [
                      {
                        name: 'Expected',
                        value: 6600,
                        strokeHeight: 13,
                        strokeWidth: 0,
                        strokeLineCap: 'round',
                        strokeColor: '#775DD0'
                      }
                    ]
                  },
                  {
                    x: '2016',
                    y: 7132,
                    goals: [
                      {
                        name: 'Expected',
                        value: 7500,
                        strokeHeight: 5,
                        strokeColor: '#775DD0'
                      }
                    ]
                  },
                  {
                    x: '2017',
                    y: 7332,
                    goals: [
                      {
                        name: 'Expected',
                        value: 8700,
                        strokeHeight: 5,
                        strokeColor: '#775DD0'
                      }
                    ]
                  },
                  {
                    x: '2018',
                    y: 6553,
                    goals: [
                      {
                        name: 'Expected',
                        value: 7300,
                        strokeHeight: 2,
                        strokeDashArray: 2,
                        strokeColor: '#775DD0'
                      }
                    ]
                  }
                ]
              }
            ],
            options: {
              chart: {
                height: 350,
                type: 'bar'
              },
              plotOptions: {
                bar: {
                  columnWidth: '60%'
                }
              },
              colors: ['#00E396'],
              dataLabels: {
                enabled: false
              },
              legend: {
                show: true,
                showForSingleSeries: true,
                customLegendItems: ['Actual', 'Expected'],
                markers: {
                  fillColors: ['#00E396', '#775DD0']
                }
              }
            },
          
          
          };
        }

      

        render() {
          return (
            <div>
              <div id="chart">
                <ReactApexChart options={this.state.options} series={this.state.series} type="bar" height={350} />
              </div>
              <div id="html-dist"></div>
            </div>
          );
        }
      }

      const domContainer = document.querySelector('#app');
      ReactDOM.render(React.createElement(ApexChart), domContainer);
    </script>

    
  </body>
</html>
