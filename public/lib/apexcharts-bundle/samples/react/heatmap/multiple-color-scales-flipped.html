<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Simple HeatMap</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        #chart {
      max-width: 650px;
      margin: 35px auto;
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/react@16.12/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@16.12/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prop-types@15.7.2/prop-types.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/babel-core/5.8.34/browser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-apexcharts@1.3.6/dist/react-apexcharts.iife.min.js"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    <script>
  function generateData(count, yrange) {
    var i = 0;
    var series = [];
    while (i < count) {
      var y = Math.floor(Math.random() * (yrange.max - yrange.min + 1)) + yrange.min;
  
      series.push(y);
      i++;
    }
    return series;
  }
  
  var data = [{
      name: '10:00',
      data: generateData(15, {
        min: 0,
        max: 90
      })
    },
    {
      name: '10:30',
      data: generateData(15, {
        min: 0,
        max: 90
      })
    },
    {
      name: '11:00',
      data: generateData(15, {
        min: 0,
        max: 90
      })
    },
    {
      name: '11:30',
      data: generateData(15, {
        min: 0,
        max: 90
      })
    },
    {
      name: '12:00',
      data: generateData(15, {
        min: 0,
        max: 90
      })
    },
    {
      name: '12:30',
      data: generateData(15, {
        min: 0,
        max: 90
      })
    },
    {
      name: '13:00',
      data: generateData(15, {
        min: 0,
        max: 90
      })
    },
    {
      name: '13:30',
      data: generateData(15, {
        min: 0,
        max: 90
      })
    }
  ]
  
  data.reverse()
  </script>
  </head>

  <body>
    
    <div id="app"></div>

    <div id="html">
      &lt;div id=&quot;chart&quot;&gt;
  &lt;ReactApexChart options={this.state.options} series={this.state.series} type=&quot;heatmap&quot; height={350} /&gt;
&lt;/div&gt;
    </div>

    <script type="text/babel">
      class ApexChart extends React.Component {
        constructor(props) {
          super(props);

          this.state = {
          
            series: data,
            options: {
              chart: {
                height: 350,
                type: 'heatmap'
              },
              dataLabels: {
                enabled: false
              },
              plotOptions: {
                heatmap: {
                  colorScale: {
                    inverse: true
                  }
                }
              },
              colors: ["#F3B415", "#F27036", "#663F59", "#6A6E94", "#4E88B4", "#00A7C6", "#18D8D8", '#A9D794',
                '#46AF78', '#A93F55', '#8C5E58', '#2176FF', '#33A1FD', '#7A918D', '#BAFF29'
              ],
              xaxis: {
                type: 'category',
                categories: ['W1', 'W2', 'W3', 'W4', 'W5', 'W6', 'W7', 'W8', 'W9', 'W10', 'W11', 'W12', 'W13', 'W14', 'W15']
              },
              title: {
                text: 'Color Scales flipped Vertically'
              },
            },
          
          
          };
        }

      

        render() {
          return (
            <div>
              <div id="chart">
                <ReactApexChart options={this.state.options} series={this.state.series} type="heatmap" height={350} />
              </div>
              <div id="html-dist"></div>
            </div>
          );
        }
      }

      const domContainer = document.querySelector('#app');
      ReactDOM.render(React.createElement(ApexChart), domContainer);
    </script>

    
  </body>
</html>
