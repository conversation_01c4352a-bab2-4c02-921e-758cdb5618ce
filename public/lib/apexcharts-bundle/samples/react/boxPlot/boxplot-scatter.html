<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>BoxPlot And Scatter Chart</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        #chart {
      max-width: 650px;
      margin: 35px auto;
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/react@16.12/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@16.12/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prop-types@15.7.2/prop-types.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/babel-core/5.8.34/browser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-apexcharts@1.3.6/dist/react-apexcharts.iife.min.js"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    
  </head>

  <body>
    
    <div id="app"></div>

    <div id="html">
      &lt;div id=&quot;chart&quot;&gt;
  &lt;ReactApexChart options={this.state.options} series={this.state.series} type=&quot;boxPlot&quot; height={350} /&gt;
&lt;/div&gt;
    </div>

    <script type="text/babel">
      class ApexChart extends React.Component {
        constructor(props) {
          super(props);

          this.state = {
          
            series: [
              {
                name: 'box',
                type: 'boxPlot',
                data: [
                  {
                    x: new Date('2017-01-01').getTime(),
                    y: [54, 66, 69, 75, 88]
                  },
                  {
                    x: new Date('2018-01-01').getTime(),
                    y: [43, 65, 69, 76, 81]
                  },
                  {
                    x: new Date('2019-01-01').getTime(),
                    y: [31, 39, 45, 51, 59]
                  },
                  {
                    x: new Date('2020-01-01').getTime(),
                    y: [39, 46, 55, 65, 71]
                  },
                  {
                    x: new Date('2021-01-01').getTime(),
                    y: [29, 31, 35, 39, 44]
                  }
                ]
              },
              {
                name: 'outliers',
                type: 'scatter',
                data: [
                  {
                    x: new Date('2017-01-01').getTime(),
                    y: 32
                  },
                  {
                    x: new Date('2018-01-01').getTime(),
                    y: 25
                  },
                  {
                    x: new Date('2019-01-01').getTime(),
                    y: 64
                  },
                  {
                    x: new Date('2020-01-01').getTime(),
                    y: 27
                  },
                  {
                    x: new Date('2020-01-01').getTime(),
                    y: 78
                  },
                  {
                    x: new Date('2021-01-01').getTime(),
                    y: 15
                  }
                ]
              }
            ],
            options: {
              chart: {
                type: 'boxPlot',
                height: 350
              },
              colors: ['#008FFB', '#FEB019'],
              title: {
                text: 'BoxPlot - Scatter Chart',
                align: 'left'
              },
              xaxis: {
                type: 'datetime',
                tooltip: {
                  formatter: function(val) {
                    return new Date(val).getFullYear()
                  }
                }
              },
              tooltip: {
                shared: false,
                intersect: true
              }
            },
          
          
          };
        }

      

        render() {
          return (
            <div>
              <div id="chart">
                <ReactApexChart options={this.state.options} series={this.state.series} type="boxPlot" height={350} />
              </div>
              <div id="html-dist"></div>
            </div>
          );
        }
      }

      const domContainer = document.querySelector('#app');
      ReactDOM.render(React.createElement(ApexChart), domContainer);
    </script>

    
  </body>
</html>
