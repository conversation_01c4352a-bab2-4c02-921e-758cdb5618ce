<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Multi-series Timeline - Group rows &amp; custom tooltip</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        #chart {
      max-width: 650px;
      margin: 35px auto;
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/react@16.12/umd/react.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-dom@16.12/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prop-types@15.7.2/prop-types.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/babel-core/5.8.34/browser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/react-apexcharts@1.3.6/dist/react-apexcharts.iife.min.js"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.22.2/moment.min.js"></script>
  </head>

  <body>
    
    <div id="app"></div>

    <div id="html">
      &lt;div id=&quot;chart&quot;&gt;
  &lt;ReactApexChart options={this.state.options} series={this.state.series} type=&quot;rangeBar&quot; height={350} /&gt;
&lt;/div&gt;
    </div>

    <script type="text/babel">
      class ApexChart extends React.Component {
        constructor(props) {
          super(props);

          this.state = {
          
            series: [
              // George Washington
              {
                name: 'George Washington',
                data: [
                  {
                    x: 'President',
                    y: [
                      new Date(1789, 3, 30).getTime(),
                      new Date(1797, 2, 4).getTime()
                    ]
                  },
                ]
              },
              // John Adams
              {
                name: 'John Adams',
                data: [
                  {
                    x: 'President',
                    y: [
                      new Date(1797, 2, 4).getTime(),
                      new Date(1801, 2, 4).getTime()
                    ]
                  },
                  {
                    x: 'Vice President',
                    y: [
                      new Date(1789, 3, 21).getTime(),
                      new Date(1797, 2, 4).getTime()
                    ]
                  }
                ]
              },
              // Thomas Jefferson
              {
                name: 'Thomas Jefferson',
                data: [
                  {
                    x: 'President',
                    y: [
                      new Date(1801, 2, 4).getTime(),
                      new Date(1809, 2, 4).getTime()
                    ]
                  },
                  {
                    x: 'Vice President',
                    y: [
                      new Date(1797, 2, 4).getTime(),
                      new Date(1801, 2, 4).getTime()
                    ]
                  },
                  {
                    x: 'Secretary of State',
                    y: [
                      new Date(1790, 2, 22).getTime(),
                      new Date(1793, 11, 31).getTime()
                    ]
                  }
                ]
              },
              // Aaron Burr
              {
                name: 'Aaron Burr',
                data: [
                  {
                    x: 'Vice President',
                    y: [
                      new Date(1801, 2, 4).getTime(),
                      new Date(1805, 2, 4).getTime()
                    ]
                  }
                ]
              },
              // George Clinton
              {
                name: 'George Clinton',
                data: [
                  {
                    x: 'Vice President',
                    y: [
                      new Date(1805, 2, 4).getTime(),
                      new Date(1812, 3, 20).getTime()
                    ]
                  }
                ]
              },
              // John Jay
              {
                name: 'John Jay',
                data: [
                  {
                    x: 'Secretary of State',
                    y: [
                      new Date(1789, 8, 25).getTime(),
                      new Date(1790, 2, 22).getTime()
                    ]
                  }
                ]
              },
              // Edmund Randolph
              {
                name: 'Edmund Randolph',
                data: [
                  {
                    x: 'Secretary of State',
                    y: [
                      new Date(1794, 0, 2).getTime(),
                      new Date(1795, 7, 20).getTime()
                    ]
                  }
                ]
              },
              // Timothy Pickering
              {
                name: 'Timothy Pickering',
                data: [
                  {
                    x: 'Secretary of State',
                    y: [
                      new Date(1795, 7, 20).getTime(),
                      new Date(1800, 4, 12).getTime()
                    ]
                  }
                ]
              },
              // Charles Lee
              {
                name: 'Charles Lee',
                data: [
                  {
                    x: 'Secretary of State',
                    y: [
                      new Date(1800, 4, 13).getTime(),
                      new Date(1800, 5, 5).getTime()
                    ]
                  }
                ]
              },
              // John Marshall
              {
                name: 'John Marshall',
                data: [
                  {
                    x: 'Secretary of State',
                    y: [
                      new Date(1800, 5, 13).getTime(),
                      new Date(1801, 2, 4).getTime()
                    ]
                  }
                ]
              },
              // Levi Lincoln
              {
                name: 'Levi Lincoln',
                data: [
                  {
                    x: 'Secretary of State',
                    y: [
                      new Date(1801, 2, 5).getTime(),
                      new Date(1801, 4, 1).getTime()
                    ]
                  }
                ]
              },
              // James Madison
              {
                name: 'James Madison',
                data: [
                  {
                    x: 'Secretary of State',
                    y: [
                      new Date(1801, 4, 2).getTime(),
                      new Date(1809, 2, 3).getTime()
                    ]
                  }
                ]
              },
            ],
            options: {
              chart: {
                height: 350,
                type: 'rangeBar'
              },
              plotOptions: {
                bar: {
                  horizontal: true,
                  barHeight: '50%',
                  rangeBarGroupRows: true
                }
              },
              colors: [
                "#008FFB", "#00E396", "#FEB019", "#FF4560", "#775DD0",
                "#3F51B5", "#546E7A", "#D4526E", "#8D5B4C", "#F86624",
                "#D7263D", "#1B998B", "#2E294E", "#F46036", "#E2C044"
              ],
              fill: {
                type: 'solid'
              },
              xaxis: {
                type: 'datetime'
              },
              legend: {
                position: 'right'
              },
              tooltip: {
                custom: function(opts) {
                  const fromYear = new Date(opts.y1).getFullYear()
                  const toYear = new Date(opts.y2).getFullYear()
                  const values = opts.ctx.rangeBar.getTooltipValues(opts)
              
                  return (
                    '<div class="apexcharts-tooltip-rangebar">' +
                    '<div> <span class="series-name" style="color: ' +
                    values.color +
                    '">' +
                    (values.seriesName ? values.seriesName : '') +
                    '</span></div>' +
                    '<div> <span class="category">' +
                    values.ylabel +
                    ' </span> <span class="value start-value">' +
                    fromYear +
                    '</span> <span class="separator">-</span> <span class="value end-value">' +
                    toYear +
                    '</span></div>' +
                    '</div>'
                  )
                }
              }
            },
          
          
          };
        }

      

        render() {
          return (
            <div>
              <div id="chart">
                <ReactApexChart options={this.state.options} series={this.state.series} type="rangeBar" height={350} />
              </div>
              <div id="html-dist"></div>
            </div>
          );
        }
      }

      const domContainer = document.querySelector('#app');
      ReactDOM.render(React.createElement(ApexChart), domContainer);
    </script>

    
  </body>
</html>
