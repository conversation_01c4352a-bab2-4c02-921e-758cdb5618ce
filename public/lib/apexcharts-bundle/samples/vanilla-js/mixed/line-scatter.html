<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Line &amp; scatter</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        #chart {
      max-width: 650px;
      margin: 35px auto;
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    
  </head>

  <body>
     <div id="chart"></div>

    <script>
      
        var options = {
          series: [{
          name: 'Points',
          type: 'scatter',
        
          //2.14, 2.15, 3.61, 4.93, 2.4, 2.7, 4.2, 5.4, 6.1, 8.3
          data: [{
            x: 1,
            y: 2.14
          }, {
            x: 1.2,
            y: 2.19
          }, {
            x: 1.8,
            y: 2.43
          }, {
            x: 2.3,
            y: 3.8
          }, {
            x: 2.6,
            y: 4.14
          }, {
            x: 2.9,
            y: 5.4
          }, {
            x: 3.2,
            y: 5.8
          }, {
            x: 3.8,
            y: 6.04
          }, {
            x: 4.55,
            y: 6.77
          }, {
            x: 4.9,
            y: 8.1
          }, {
            x: 5.1,
            y: 9.4
          }, {
            x: 7.1,
            y: 7.14
          },{
            x: 9.18,
            y: 8.4
          }]
        }, {
          name: 'Line',
          type: 'line',
          data: [{
            x: 1,
            y: 2
          }, {
            x: 2,
            y: 3
          }, {
            x: 3,
            y: 4
          }, {
            x: 4,
            y: 5
          }, {
            x: 5,
            y: 6
          }, {
            x: 6,
            y: 7
          }, {
            x: 7,
            y: 8
          }, {
            x: 8,
            y: 9
          }, {
            x: 9,
            y: 10
          }, {
            x: 10,
            y: 11
          }]
        }],
          chart: {
          height: 350,
          type: 'line',
        },
        fill: {
          type:'solid',
        },
        markers: {
          size: [6, 0]
        },
        tooltip: {
          shared: false,
          intersect: true,
        },
        legend: {
          show: false
        },
        xaxis: {
          type: 'numeric',
          min: 0,
          max: 12,
          tickAmount: 12
        }
        };

        var chart = new ApexCharts(document.querySelector("#chart"), options);
        chart.render();
      
      
    </script>

    
  </body>
</html>
