<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Init Chart from data attrs</title>


    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
        #chart {
            max-width: 650px;
            margin: 35px auto;
        }
    </style>
</head>

<body>
    <div id="chart" data-apexcharts data-options='{
        "chart": {
            "type": "line"
        },
        "series": [{
            "name": "Desktops",
            "data": [30, 41, 35, 51]
        }],
        "xaxis": {
            "categories": ["Jan", "Feb", "Mar", "Apr"]
        }
    }'>

    </div>


    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

    <script>
        window.Apex = {
            chart: {
                height: 350
            },
            stroke: {
                curve: "smooth"
            },
            dataLabels: {
                enabled: false
            }
        }
        ApexCharts.initOnLoad()
    </script>
</body>

</html>