<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Syncing charts</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        #wrapper {
      padding-top: 20px;
      padding-left: 10px;
      background: #fff;
      border: 1px solid #ddd;
      box-shadow: 0 22px 35px -16px rgba(0, 0, 0, 0.1);
      max-width: 650px;
      margin: 35px auto;
    }
    
    .columns {
      columns: 2;
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    <script>
  // The global window.Apex variable below can be used to set common options for all charts on the page
  Apex = {
    chart: {
      height: 160,
    },
    dataLabels: {
      enabled: false
    },
    stroke: {
      curve: 'straight'
    },
    toolbar: {
      tools: {
        selection: false
      }
    },
    markers: {
      size: 6,
      hover: {
        size: 10
      }
    },
    tooltip: {
      followCursor: false,
      theme: 'dark',
      x: {
        show: false
      },
      marker: {
        show: false
      },
      y: {
        title: {
          formatter: function() {
            return ''
          }
        }
      }
    },
    grid: {
      clipMarkers: false
    },
    yaxis: {
      tickAmount: 2
    },
    xaxis: {
      type: 'datetime'
    },
  }
  
  /*
    // this function will generate output in this format
    // data = [
        [timestamp, 23],
        [timestamp, 33],
        [timestamp, 12]
        ...
    ]
  */
  function generateDayWiseTimeSeries(baseval, count, yrange) {
    var i = 0;
    var series = [];
    while (i < count) {
      var x = baseval;
      var y = Math.floor(Math.random() * (yrange.max - yrange.min + 1)) + yrange.min;
  
      series.push([x, y]);
      baseval += 86400000;
      i++;
    }
    return series;
  }
  </script>
  </head>

  <body>
     <div id="wrapper">
        <div id="chart-line"></div>
        <div id="chart-line2"></div>
        <div id="chart-area"></div>
        <div class="columns">
          <div id="chart-small"></div>
          <div id="chart-small2"></div>
        </div>
        
      </div>

    <script>
      
        var options = {
          series: [{
          data: generateDayWiseTimeSeries(new Date('11 Feb 2017').getTime(), 20, {
            min: 10,
            max: 60
          })
        }],
          chart: {
          id: 'fb',
          group: 'social',
          type: 'line',
          height: 160
        },
        colors: ['#008FFB']
        };

        var chart = new ApexCharts(document.querySelector("#chart-line"), options);
        chart.render();
      
        var optionsLine2 = {
          series: [{
          data: generateDayWiseTimeSeries(new Date('11 Feb 2017').getTime(), 20, {
            min: 10,
            max: 30
          })
        }],
          chart: {
          id: 'tw',
          group: 'social',
          type: 'line',
          height: 160
        },
        colors: ['#546E7A']
        };

        var chartLine2 = new ApexCharts(document.querySelector("#chart-line2"), optionsLine2);
        chartLine2.render();
      
        var optionsArea = {
          series: [{
          data: generateDayWiseTimeSeries(new Date('11 Feb 2017').getTime(), 20, {
            min: 10,
            max: 60
          })
        }],
          chart: {
          id: 'yt',
          group: 'social',
          type: 'area',
          height: 160
        },
        colors: ['#00E396']
        };

        var chartArea = new ApexCharts(document.querySelector("#chart-area"), optionsArea);
        chartArea.render();
      
        var optionsSmall = {
          series: [{
          data: generateDayWiseTimeSeries(new Date('11 Feb 2017').getTime(), 20, {
            min: 10,
            max: 60
          })
        }],
          chart: {
          id: 'ig',
          group: 'social',
          type: 'area',
          height: 160,
          width: 300
        },
        colors: ['#008FFB']
        };

        var chartSmall = new ApexCharts(document.querySelector("#chart-small"), optionsSmall);
        chartSmall.render();
      
        var optionsSmall2 = {
          series: [{
          data: generateDayWiseTimeSeries(new Date('11 Feb 2017').getTime(), 20, {
            min: 10,
            max: 60
          })
        }],
          chart: {
          id: 'li',
          group: 'social',
          type: 'area',
          height: 160,
          width: 300
        },
        colors: ['#546E7A']
        };

        var chartSmall2 = new ApexCharts(document.querySelector("#chart-small2"), optionsSmall2);
        chartSmall2.render();
      
      
    </script>

    
  </body>
</html>
