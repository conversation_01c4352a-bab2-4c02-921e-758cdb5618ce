<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Sparklines</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        .row {
      overflow: hidden;
      max-width: 890px;
      margin: 30px auto;
      display: flex;
    }
    
    .col-md-4 {
      width: 33.33%;
      padding: 0 25px;
    }
    
    table {
      width: 100%;
    }
    
    tbody tr {
      border-top: 1px solid #e7e7e7;
    }
    
    th {
      font-weight: bold;
      font-family: Helvetica;
      padding-bottom: 20px;
    }
    td, th {
      width: 25%;
      text-align: center;
      height: 65px;
    }
    
    td div {
      margin: 0 auto;
    }
    
    .left {
      float: left;
    }
    
    .right {
      float: right;
    }
    
    @media only screen and (max-width: 480px) {
      th:first-child, td:first-child {
        display: none;
      }
      .row {
        display: block;
      }
      .col-md-4 {
        padding: 0;
        width: 100%;
      }
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    <script>
  window.Apex = {
    stroke: {
      width: 3
    },
    markers: {
      size: 0
    },
    tooltip: {
      fixed: {
        enabled: true,
      }
    }
  };
  
  var randomizeArray = function (arg) {
    var array = arg.slice();
    var currentIndex = array.length,
      temporaryValue, randomIndex;
  
    while (0 !== currentIndex) {
  
      randomIndex = Math.floor(Math.random() * currentIndex);
      currentIndex -= 1;
  
      temporaryValue = array[currentIndex];
      array[currentIndex] = array[randomIndex];
      array[randomIndex] = temporaryValue;
    }
  
    return array;
  }
  
  // data for the sparklines that appear below header area
  var sparklineData = [47, 45, 54, 38, 56, 24, 65, 31, 37, 39, 62, 51, 35, 41, 35, 27, 93, 53, 61, 27, 54, 43, 19, 46];
  </script>
  </head>

  <body>
     <div>
        <div class="row">
          <div class="col-md-4">
            <div id="chart-spark1"></div>
          </div>
          <div class="col-md-4">
            <div id="chart-spark2"></div>
          </div>
          <div class="col-md-4">
            <div id="chart-spark3"></div>
          </div>
        </div>
      
        <div class="row">
          <table>
            <thead>
              <th>Total Value</th>
              <th>Percentage of Portfolio</th>
              <th>Last 10 days</th>
              <th>Volume</th>
            </thead>
            <tbody>
              <tr>
                <td>$32,554</td>
                <td>15%</td>
                <td>
                  <div id="chart-1"></div>
                </td>
                <td>
                  <div id="chart-5"></div>
                </td>
              </tr>
              <tr>
                <td>$23,533</td>
                <td>7%</td>
                <td>
                  <div id="chart-2"></div>
                </td>
                <td>
                  <div id="chart-6"></div>
                </td>
              </tr>
              <tr>
                <td>$54,276</td>
                <td>9%</td>
                <td>
                  <div id="chart-3"></div>
                </td>
                <td>
                  <div id="chart-7"></div>
                </td>
              </tr>
              <tr>
                <td>$11,533</td>
                <td>2%</td>
                <td>
                  <div id="chart-4"></div>
                </td>
                <td>
                  <div id="chart-8"></div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

    <script>
      
        var options = {
          series: [{
          data: randomizeArray(sparklineData)
        }],
          chart: {
          type: 'area',
          height: 160,
          sparkline: {
            enabled: true
          },
        },
        stroke: {
          curve: 'straight'
        },
        fill: {
          opacity: 0.3,
        },
        yaxis: {
          min: 0
        },
        colors: ['#DCE6EC'],
        title: {
          text: '$424,652',
          offsetX: 0,
          style: {
            fontSize: '24px',
          }
        },
        subtitle: {
          text: 'Sales',
          offsetX: 0,
          style: {
            fontSize: '14px',
          }
        }
        };

        var chart = new ApexCharts(document.querySelector("#chart-spark1"), options);
        chart.render();
      
        var optionsSpark2 = {
          series: [{
          data: randomizeArray(sparklineData)
        }],
          chart: {
          type: 'area',
          height: 160,
          sparkline: {
            enabled: true
          },
        },
        stroke: {
          curve: 'straight'
        },
        fill: {
          opacity: 0.3,
        },
        yaxis: {
          min: 0
        },
        colors: ['#DCE6EC'],
        title: {
          text: '$235,312',
          offsetX: 0,
          style: {
            fontSize: '24px',
          }
        },
        subtitle: {
          text: 'Expenses',
          offsetX: 0,
          style: {
            fontSize: '14px',
          }
        }
        };

        var chartSpark2 = new ApexCharts(document.querySelector("#chart-spark2"), optionsSpark2);
        chartSpark2.render();
      
        var optionsSpark3 = {
          series: [{
          data: randomizeArray(sparklineData)
        }],
          chart: {
          type: 'area',
          height: 160,
          sparkline: {
            enabled: true
          },
        },
        stroke: {
          curve: 'straight'
        },
        fill: {
          opacity: 0.3
        },
        xaxis: {
          crosshairs: {
            width: 1
          },
        },
        yaxis: {
          min: 0
        },
        title: {
          text: '$135,965',
          offsetX: 0,
          style: {
            fontSize: '24px',
          }
        },
        subtitle: {
          text: 'Profits',
          offsetX: 0,
          style: {
            fontSize: '14px',
          }
        }
        };

        var chartSpark3 = new ApexCharts(document.querySelector("#chart-spark3"), optionsSpark3);
        chartSpark3.render();
      
        var options1 = {
          series: [{
          data: [25, 66, 41, 89, 63, 25, 44, 12, 36, 9, 54]
        }],
          chart: {
          type: 'line',
          width: 100,
          height: 35,
          sparkline: {
            enabled: true
          }
        },
        tooltip: {
          fixed: {
            enabled: false
          },
          x: {
            show: false
          },
          y: {
            title: {
              formatter: function (seriesName) {
                return ''
              }
            }
          },
          marker: {
            show: false
          }
        }
        };

        var chart1 = new ApexCharts(document.querySelector("#chart-1"), options1);
        chart1.render();
      
        var options2 = {
          series: [{
          data: [12, 14, 2, 47, 42, 15, 47, 75, 65, 19, 14]
        }],
          chart: {
          type: 'line',
          width: 100,
          height: 35,
          sparkline: {
            enabled: true
          }
        },
        tooltip: {
          fixed: {
            enabled: false
          },
          x: {
            show: false
          },
          y: {
            title: {
              formatter: function (seriesName) {
                return ''
              }
            }
          },
          marker: {
            show: false
          }
        }
        };

        var chart2 = new ApexCharts(document.querySelector("#chart-2"), options2);
        chart2.render();
      
        var options3 = {
          series: [43, 32, 12, 9],
          chart: {
          type: 'pie',
          width: 40,
          height: 40,
          sparkline: {
            enabled: true
          }
        },
        stroke: {
          width: 1
        },
        tooltip: {
          fixed: {
            enabled: false
          },
        }
        };

        var chart3 = new ApexCharts(document.querySelector("#chart-3"), options3);
        chart3.render();
      
        var options4 = {
          series: [43, 32, 12, 9],
          chart: {
          type: 'donut',
          width: 40,
          height: 40,
          sparkline: {
            enabled: true
          }
        },
        stroke: {
          width: 1
        },
        tooltip: {
          fixed: {
            enabled: false
          },
        }
        };

        var chart4 = new ApexCharts(document.querySelector("#chart-4"), options4);
        chart4.render();
      
        var options5 = {
          series: [{
          data: [25, 66, 41, 89, 63, 25, 44, 12, 36, 9, 54]
        }],
          chart: {
          type: 'bar',
          width: 100,
          height: 35,
          sparkline: {
            enabled: true
          }
        },
        plotOptions: {
          bar: {
            columnWidth: '80%'
          }
        },
        labels: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
        xaxis: {
          crosshairs: {
            width: 1
          },
        },
        tooltip: {
          fixed: {
            enabled: false
          },
          x: {
            show: false
          },
          y: {
            title: {
              formatter: function (seriesName) {
                return ''
              }
            }
          },
          marker: {
            show: false
          }
        }
        };

        var chart5 = new ApexCharts(document.querySelector("#chart-5"), options5);
        chart5.render();
      
        var options6 = {
          series: [{
          data: [12, 14, 2, 47, 42, 15, 47, 75, 65, 19, 14]
        }],
          chart: {
          type: 'bar',
          width: 100,
          height: 35,
          sparkline: {
            enabled: true
          }
        },
        plotOptions: {
          bar: {
            columnWidth: '80%'
          }
        },
        labels: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
        xaxis: {
          crosshairs: {
            width: 1
          },
        },
        tooltip: {
          fixed: {
            enabled: false
          },
          x: {
            show: false
          },
          y: {
            title: {
              formatter: function (seriesName) {
                return ''
              }
            }
          },
          marker: {
            show: false
          }
        }
        };

        var chart6 = new ApexCharts(document.querySelector("#chart-6"), options6);
        chart6.render();
      
        var options7 = {
          series: [45],
          chart: {
          type: 'radialBar',
          width: 50,
          height: 50,
          sparkline: {
            enabled: true
          }
        },
        dataLabels: {
          enabled: false
        },
        plotOptions: {
          radialBar: {
            hollow: {
              margin: 0,
              size: '50%'
            },
            track: {
              margin: 0
            },
            dataLabels: {
              show: false
            }
          }
        }
        };

        var chart7 = new ApexCharts(document.querySelector("#chart-7"), options7);
        chart7.render();
      
        var options8 = {
          series: [53, 67],
          chart: {
          type: 'radialBar',
          width: 40,
          height: 40,
          sparkline: {
            enabled: true
          }
        },
        dataLabels: {
          enabled: false
        },
        plotOptions: {
          radialBar: {
            hollow: {
              margin: 0,
              size: '50%'
            },
            track: {
              margin: 1
            },
            dataLabels: {
              show: false
            }
          }
        }
        };

        var chart8 = new ApexCharts(document.querySelector("#chart-8"), options8);
        chart8.render();
      
      
    </script>

    
  </body>
</html>
