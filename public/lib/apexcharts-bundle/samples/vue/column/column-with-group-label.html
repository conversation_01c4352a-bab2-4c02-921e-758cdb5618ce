<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Column with Group Labels</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        #chart {
      max-width: 650px;
      margin: 35px auto;
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue-apexcharts"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/dayjs/1.11.0/dayjs.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/dayjs/1.11.0/plugin/quarterOfYear.min.js"></script>
  <script>
    dayjs.extend(window.dayjs_plugin_quarterOfYear)
  </script>
  </head>

  <body>
    
    <div id="app">
      <div id="chart">
      <apexchart type="bar" height="380" :options="chartOptions" :series="series"></apexchart>
    </div>
    </div>

    <!-- Below element is just for displaying source code. it is not required. DO NOT USE -->
    <div id="html">
      &lt;div id=&quot;chart&quot;&gt;
        &lt;apexchart type=&quot;bar&quot; height=&quot;380&quot; :options=&quot;chartOptions&quot; :series=&quot;series&quot;&gt;&lt;/apexchart&gt;
      &lt;/div&gt;
    </div>

    <script>
      new Vue({
        el: '#app',
        components: {
          apexchart: VueApexCharts,
        },
        data: {
          
          series: [{
            name: "sales",
            data: [{
              x: '2019/01/01',
              y: 400
            }, {
              x: '2019/04/01',
              y: 430
            }, {
              x: '2019/07/01',
              y: 448
            }, {
              x: '2019/10/01',
              y: 470
            }, {
              x: '2020/01/01',
              y: 540
            }, {
              x: '2020/04/01',
              y: 580
            }, {
              x: '2020/07/01',
              y: 690
            }, {
              x: '2020/10/01',
              y: 690
            }]
          }],
          chartOptions: {
            chart: {
              type: 'bar',
              height: 380
            },
            xaxis: {
              type: 'category',
              labels: {
                formatter: function(val) {
                  return "Q" + dayjs(val).quarter()
                }
              },
              group: {
                style: {
                  fontSize: '10px',
                  fontWeight: 700
                },
                groups: [
                  { title: '2019', cols: 4 },
                  { title: '2020', cols: 4 }
                ]
              }
            },
            title: {
                text: 'Grouped Labels on the X-axis',
            },
            tooltip: {
              x: {
                formatter: function(val) {
                  return "Q" + dayjs(val).quarter() + " " + dayjs(val).format("YYYY")
                }  
              }
            },
          },
          
          
        },
        
      })
    </script>
    
  </body>
</html>
