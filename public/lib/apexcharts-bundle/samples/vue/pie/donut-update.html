<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Simple Pie</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        #chart {
      max-width: 480px;
      margin: 35px auto;
      padding: 0;
    }
    .actions {
      top: -10px;
      position: relative;
      z-index: 10;
      max-width: 400px;
      margin: 0 auto;
    }
    button {
      color: #fff;
      background: #20b2aa;
      padding: 5px 10px;
      margin: 2px;
      font-weight: bold;
      font-size: 13px;
      border-radius: 5px;
    }
    p {
      margin: 10px 0;
    }
    @media only screen and (max-width: 480px) {
      .actions {
        margin-top: 0;
        left: 0
      }
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue-apexcharts"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    
  </head>

  <body>
    
    <div id="app">
      <div>
      <div class="chart-wrap">
        <div id="chart">
      <apexchart type="donut" width="380" :options="chartOptions" :series="series"></apexchart>
    </div>
      </div>
    
      <div class="actions">
        <button
            
            
            @click="appendData">
          + ADD
        </button>
        
        <button
            
            
            @click="removeData">
          - REMOVE
        </button>
        
        <button
            
            
            @click="randomize">
          RANDOMIZE
        </button>
        
        <button
            
            
            @click="reset">
          RESET
        </button>
      </div>
    </div>
    </div>

    <!-- Below element is just for displaying source code. it is not required. DO NOT USE -->
    <div id="html">
      &lt;div&gt;
        &lt;div class=&quot;chart-wrap&quot;&gt;
          &lt;div id=&quot;chart&quot;&gt;
        &lt;apexchart type=&quot;donut&quot; width=&quot;380&quot; :options=&quot;chartOptions&quot; :series=&quot;series&quot;&gt;&lt;/apexchart&gt;
      &lt;/div&gt;
        &lt;/div&gt;
      
        &lt;div class=&quot;actions&quot;&gt;
          &lt;button
              
              
              @click=&quot;appendData&quot;&gt;
            + ADD
          &lt;/button&gt;
          
          &lt;button
              
              
              @click=&quot;removeData&quot;&gt;
            - REMOVE
          &lt;/button&gt;
          
          &lt;button
              
              
              @click=&quot;randomize&quot;&gt;
            RANDOMIZE
          &lt;/button&gt;
          
          &lt;button
              
              
              @click=&quot;reset&quot;&gt;
            RESET
          &lt;/button&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    </div>

    <script>
      new Vue({
        el: '#app',
        components: {
          apexchart: VueApexCharts,
        },
        data: {
          
          series: [44, 55, 13, 33],
          chartOptions: {
            chart: {
              width: 380,
              type: 'donut',
            },
            dataLabels: {
              enabled: false
            },
            responsive: [{
              breakpoint: 480,
              options: {
                chart: {
                  width: 200
                },
                legend: {
                  show: false
                }
              }
            }],
            legend: {
              position: 'right',
              offsetY: 0,
              height: 230,
            }
          },
          
          
        },
        methods: {
          appendData: function () {
            var arr = this.series.slice()
            arr.push(Math.floor(Math.random() * (100 - 1 + 1)) + 1)
            this.series = arr
          },
        
          removeData: function () {
            if(this.series.length === 1) return
            var arr = this.series.slice()
            arr.pop()
            this.series = arr
          },
        
          randomize: function () {
            this.series = this.series.map(function() {
              return Math.floor(Math.random() * (100 - 1 + 1)) + 1
            })
          },
        
          reset: function () {
            this.series = [44, 55, 13, 33]
          }
        },
      })
    </script>
    
  </body>
</html>
