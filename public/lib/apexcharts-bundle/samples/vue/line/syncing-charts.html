<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Syncing charts</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        #wrapper {
      padding-top: 20px;
      padding-left: 10px;
      background: #fff;
      border: 1px solid #ddd;
      box-shadow: 0 22px 35px -16px rgba(0, 0, 0, 0.1);
      max-width: 650px;
      margin: 35px auto;
    }
    
    .columns {
      columns: 2;
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue-apexcharts"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    <script>
  // The global window.Apex variable below can be used to set common options for all charts on the page
  Apex = {
    chart: {
      height: 160,
    },
    dataLabels: {
      enabled: false
    },
    stroke: {
      curve: 'straight'
    },
    toolbar: {
      tools: {
        selection: false
      }
    },
    markers: {
      size: 6,
      hover: {
        size: 10
      }
    },
    tooltip: {
      followCursor: false,
      theme: 'dark',
      x: {
        show: false
      },
      marker: {
        show: false
      },
      y: {
        title: {
          formatter: function() {
            return ''
          }
        }
      }
    },
    grid: {
      clipMarkers: false
    },
    yaxis: {
      tickAmount: 2
    },
    xaxis: {
      type: 'datetime'
    },
  }
  
  /*
    // this function will generate output in this format
    // data = [
        [timestamp, 23],
        [timestamp, 33],
        [timestamp, 12]
        ...
    ]
  */
  function generateDayWiseTimeSeries(baseval, count, yrange) {
    var i = 0;
    var series = [];
    while (i < count) {
      var x = baseval;
      var y = Math.floor(Math.random() * (yrange.max - yrange.min + 1)) + yrange.min;
  
      series.push([x, y]);
      baseval += 86400000;
      i++;
    }
    return series;
  }
  </script>
  </head>

  <body>
    
    <div id="app">
      <div id="wrapper">
      <div id="chart-line">
      <apexchart type="line" height="160" :options="chartOptions" :series="series"></apexchart>
    </div>
      <div id="chart-line2">
      <apexchart type="line" height="160" :options="chartOptionsLine2" :series="seriesLine2"></apexchart>
    </div>
      <div id="chart-area">
      <apexchart type="area" height="160" :options="chartOptionsArea" :series="seriesArea"></apexchart>
    </div>
      <div class="columns">
        <div id="chart-small">
      <apexchart type="area" height="160" width="300" :options="chartOptionsSmall" :series="seriesSmall"></apexchart>
    </div>
        <div id="chart-small2">
      <apexchart type="area" height="160" width="300" :options="chartOptionsSmall2" :series="seriesSmall2"></apexchart>
    </div>
      </div>
      
    </div>
    </div>

    <!-- Below element is just for displaying source code. it is not required. DO NOT USE -->
    <div id="html">
      &lt;div id=&quot;wrapper&quot;&gt;
        &lt;div id=&quot;chart-line&quot;&gt;
        &lt;apexchart type=&quot;line&quot; height=&quot;160&quot; :options=&quot;chartOptions&quot; :series=&quot;series&quot;&gt;&lt;/apexchart&gt;
      &lt;/div&gt;
        &lt;div id=&quot;chart-line2&quot;&gt;
        &lt;apexchart type=&quot;line&quot; height=&quot;160&quot; :options=&quot;chartOptionsLine2&quot; :series=&quot;seriesLine2&quot;&gt;&lt;/apexchart&gt;
      &lt;/div&gt;
        &lt;div id=&quot;chart-area&quot;&gt;
        &lt;apexchart type=&quot;area&quot; height=&quot;160&quot; :options=&quot;chartOptionsArea&quot; :series=&quot;seriesArea&quot;&gt;&lt;/apexchart&gt;
      &lt;/div&gt;
        &lt;div class=&quot;columns&quot;&gt;
          &lt;div id=&quot;chart-small&quot;&gt;
        &lt;apexchart type=&quot;area&quot; height=&quot;160&quot; width=&quot;300&quot; :options=&quot;chartOptionsSmall&quot; :series=&quot;seriesSmall&quot;&gt;&lt;/apexchart&gt;
      &lt;/div&gt;
          &lt;div id=&quot;chart-small2&quot;&gt;
        &lt;apexchart type=&quot;area&quot; height=&quot;160&quot; width=&quot;300&quot; :options=&quot;chartOptionsSmall2&quot; :series=&quot;seriesSmall2&quot;&gt;&lt;/apexchart&gt;
      &lt;/div&gt;
        &lt;/div&gt;
        
      &lt;/div&gt;
    </div>

    <script>
      new Vue({
        el: '#app',
        components: {
          apexchart: VueApexCharts,
        },
        data: {
          
          series: [{
            data: generateDayWiseTimeSeries(new Date('11 Feb 2017').getTime(), 20, {
              min: 10,
              max: 60
            })
          }],
          chartOptions: {
            chart: {
              id: 'fb',
              group: 'social',
              type: 'line',
              height: 160
            },
            colors: ['#008FFB']
          },
          
          seriesLine2: [{
            data: generateDayWiseTimeSeries(new Date('11 Feb 2017').getTime(), 20, {
              min: 10,
              max: 30
            })
          }],
          chartOptionsLine2: {
            chart: {
              id: 'tw',
              group: 'social',
              type: 'line',
              height: 160
            },
            colors: ['#546E7A']
          },
          
          seriesArea: [{
            data: generateDayWiseTimeSeries(new Date('11 Feb 2017').getTime(), 20, {
              min: 10,
              max: 60
            })
          }],
          chartOptionsArea: {
            chart: {
              id: 'yt',
              group: 'social',
              type: 'area',
              height: 160
            },
            colors: ['#00E396']
          },
          
          seriesSmall: [{
            data: generateDayWiseTimeSeries(new Date('11 Feb 2017').getTime(), 20, {
              min: 10,
              max: 60
            })
          }],
          chartOptionsSmall: {
            chart: {
              id: 'ig',
              group: 'social',
              type: 'area',
              height: 160,
              width: 300
            },
            colors: ['#008FFB']
          },
          
          seriesSmall2: [{
            data: generateDayWiseTimeSeries(new Date('11 Feb 2017').getTime(), 20, {
              min: 10,
              max: 60
            })
          }],
          chartOptionsSmall2: {
            chart: {
              id: 'li',
              group: 'social',
              type: 'area',
              height: 160,
              width: 300
            },
            colors: ['#546E7A']
          },
          
          
        },
        
      })
    </script>
    
  </body>
</html>
