<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Area with missing data</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        #chart {
      max-width: 650px;
      margin: 35px auto;
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue-apexcharts"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    
  </head>

  <body>
    
    <div id="app">
      <div id="chart">
      <apexchart type="area" height="350" :options="chartOptions" :series="series"></apexchart>
    </div>
    </div>

    <!-- Below element is just for displaying source code. it is not required. DO NOT USE -->
    <div id="html">
      &lt;div id=&quot;chart&quot;&gt;
        &lt;apexchart type=&quot;area&quot; height=&quot;350&quot; :options=&quot;chartOptions&quot; :series=&quot;series&quot;&gt;&lt;/apexchart&gt;
      &lt;/div&gt;
    </div>

    <script>
      new Vue({
        el: '#app',
        components: {
          apexchart: VueApexCharts,
        },
        data: {
          
          series: [{
            name: 'Network',
            data: [{
                x: 'Dec 23 2017',
                y: null
              },
              {
                x: 'Dec 24 2017',
                y: 44
              },
              {
                x: 'Dec 25 2017',
                y: 31
              },
              {
                x: 'Dec 26 2017',
                y: 38
              },
              {
                x: 'Dec 27 2017',
                y: null
              },
              {
                x: 'Dec 28 2017',
                y: 32
              },
              {
                x: 'Dec 29 2017',
                y: 55
              },
              {
                x: 'Dec 30 2017',
                y: 51
              },
              {
                x: 'Dec 31 2017',
                y: 67
              },
              {
                x: 'Jan 01 2018',
                y: 22
              },
              {
                x: 'Jan 02 2018',
                y: 34
              },
              {
                x: 'Jan 03 2018',
                y: null
              },
              {
                x: 'Jan 04 2018',
                y: null
              },
              {
                x: 'Jan 05 2018',
                y: 11
              },
              {
                x: 'Jan 06 2018',
                y: 4
              },
              {
                x: 'Jan 07 2018',
                y: 15,
              },
              {
                x: 'Jan 08 2018',
                y: null
              },
              {
                x: 'Jan 09 2018',
                y: 9
              },
              {
                x: 'Jan 10 2018',
                y: 34
              },
              {
                x: 'Jan 11 2018',
                y: null
              },
              {
                x: 'Jan 12 2018',
                y: null
              },
              {
                x: 'Jan 13 2018',
                y: 13
              },
              {
                x: 'Jan 14 2018',
                y: null
              }
            ],
          }],
          chartOptions: {
            chart: {
              type: 'area',
              height: 350,
              animations: {
                enabled: false
              },
              zoom: {
                enabled: false
              },
            },
            dataLabels: {
              enabled: false
            },
            stroke: {
              curve: 'straight'
            },
            fill: {
              opacity: 0.8,
              type: 'pattern',
              pattern: {
                style: ['verticalLines', 'horizontalLines'],
                width: 5,
                height: 6
              },
            },
            markers: {
              size: 5,
              hover: {
                size: 9
              }
            },
            title: {
              text: 'Network Monitoring',
            },
            tooltip: {
              intersect: true,
              shared: false
            },
            theme: {
              palette: 'palette1'
            },
            xaxis: {
              type: 'datetime',
            },
            yaxis: {
              title: {
                text: 'Bytes Received'
              }
            }
          },
          
          
        },
        
      })
    </script>
    
  </body>
</html>
