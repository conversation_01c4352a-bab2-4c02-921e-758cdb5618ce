<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Advanced Overlapping Timeline</title>

    <link href="../../assets/styles.css" rel="stylesheet" />

    <style>
      
        #chart {
      max-width: 650px;
      margin: 35px auto;
    }
    
    .apexcharts-yaxis text {
      font-weight: bold;
    }
      
    </style>

    <script>
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/eligrey-classlist-js-polyfill@1.2.20171210/classList.min.js"><\/script>'
        )
      window.Promise ||
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/findindex_polyfill_mdn"><\/script>'
        )
    </script>

    
    <script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue-apexcharts"></script>
    

    <script>
      // Replace Math.random() with a pseudo-random number generator to get reproducible results in e2e tests
      // Based on https://gist.github.com/blixt/f17b47c62508be59987b
      var _seed = 42;
      Math.random = function() {
        _seed = _seed * 16807 % 2147483647;
        return (_seed - 1) / 2147483646;
      };
    </script>

    
  </head>

  <body>
    
    <div id="app">
      <div id="chart">
      <apexchart type="rangeBar" height="450" :options="chartOptions" :series="series"></apexchart>
    </div>
    </div>

    <!-- Below element is just for displaying source code. it is not required. DO NOT USE -->
    <div id="html">
      &lt;div id=&quot;chart&quot;&gt;
        &lt;apexchart type=&quot;rangeBar&quot; height=&quot;450&quot; :options=&quot;chartOptions&quot; :series=&quot;series&quot;&gt;&lt;/apexchart&gt;
      &lt;/div&gt;
    </div>

    <script>
      new Vue({
        el: '#app',
        components: {
          apexchart: VueApexCharts,
        },
        data: {
          
          series: [
            {
              name: 'Bob',
              data: [
                {
                  x: 'Design',
                  y: [
                    new Date('2019-03-05').getTime(),
                    new Date('2019-03-08').getTime()
                  ]
                },
                {
                  x: 'Code',
                  y: [
                    new Date('2019-03-02').getTime(),
                    new Date('2019-03-05').getTime()
                  ]
                },
                {
                  x: 'Code',
                  y: [
                    new Date('2019-03-05').getTime(),
                    new Date('2019-03-07').getTime()
                  ]
                },
                {
                  x: 'Test',
                  y: [
                    new Date('2019-03-03').getTime(),
                    new Date('2019-03-09').getTime()
                  ]
                },
                {
                  x: 'Test',
                  y: [
                    new Date('2019-03-08').getTime(),
                    new Date('2019-03-11').getTime()
                  ]
                },
                {
                  x: 'Validation',
                  y: [
                    new Date('2019-03-11').getTime(),
                    new Date('2019-03-16').getTime()
                  ]
                },
                {
                  x: 'Design',
                  y: [
                    new Date('2019-03-01').getTime(),
                    new Date('2019-03-03').getTime()
                  ],
                }
              ]
            },
            {
              name: 'Joe',
              data: [
                {
                  x: 'Design',
                  y: [
                    new Date('2019-03-02').getTime(),
                    new Date('2019-03-05').getTime()
                  ]
                },
                {
                  x: 'Test',
                  y: [
                    new Date('2019-03-06').getTime(),
                    new Date('2019-03-16').getTime()
                  ],
                  goals: [
                    {
                      name: 'Break',
                      value: new Date('2019-03-10').getTime(),
                      strokeColor: '#CD2F2A'
                    }
                  ]
                },
                {
                  x: 'Code',
                  y: [
                    new Date('2019-03-03').getTime(),
                    new Date('2019-03-07').getTime()
                  ]
                },
                {
                  x: 'Deployment',
                  y: [
                    new Date('2019-03-20').getTime(),
                    new Date('2019-03-22').getTime()
                  ]
                },
                {
                  x: 'Design',
                  y: [
                    new Date('2019-03-10').getTime(),
                    new Date('2019-03-16').getTime()
                  ]
                }
              ]
            },
            {
              name: 'Dan',
              data: [
                {
                  x: 'Code',
                  y: [
                    new Date('2019-03-10').getTime(),
                    new Date('2019-03-17').getTime()
                  ]
                },
                {
                  x: 'Validation',
                  y: [
                    new Date('2019-03-05').getTime(),
                    new Date('2019-03-09').getTime()
                  ],
                  goals: [
                    {
                      name: 'Break',
                      value: new Date('2019-03-07').getTime(),
                      strokeColor: '#CD2F2A'
                    }
                  ]
                },
              ]
            }
          ],
          chartOptions: {
            chart: {
              height: 450,
              type: 'rangeBar'
            },
            plotOptions: {
              bar: {
                horizontal: true,
                barHeight: '80%'
              }
            },
            xaxis: {
              type: 'datetime'
            },
            stroke: {
              width: 1
            },
            fill: {
              type: 'solid',
              opacity: 0.6
            },
            legend: {
              position: 'top',
              horizontalAlign: 'left'
            }
          },
          
          
        },
        
      })
    </script>
    
  </body>
</html>
