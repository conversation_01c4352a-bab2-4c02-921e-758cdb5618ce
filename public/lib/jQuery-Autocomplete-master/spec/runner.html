<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <title>Autocomplete Spec</title>
        <!-- jasmine -->
        <link rel="stylesheet" type="text/css" href="lib/jasmine-2.0.1/jasmine.css">
        <script type="text/javascript" src="lib/jasmine-2.0.1/jasmine.js"></script>
        <script type="text/javascript" src="lib/jasmine-2.0.1/jasmine-html.js"></script>
        <script type="text/javascript" src="lib/jasmine-2.0.1/boot.js"></script>
        
        <!-- jQuery -->
        <script src="../scripts/jquery-2.1.1.js"></script>
        <script src="../scripts/jquery.mockjax.js"></script>
        <script type="text/javascript">
            window.JSON || document.write('<scr' + 'ipt src="//cdnjs.cloudflare.com/ajax/libs/json2/20110223/json2.min.js"><\/scr' + 'ipt>');
        </script>

        <!-- Autocomplete -->
        <script src="../src/jquery.autocomplete.js"></script>
        <!-- specs -->
        <script type="text/javascript" src="autocompleteBehavior.js"></script>
    </head>
<body>

</body>
</html>
