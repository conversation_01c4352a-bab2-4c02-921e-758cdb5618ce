<div class="modal fade" id="indexDialogModal" tabindex="-1" aria-labelledby="indexDialogModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="indexDialogModalLabel">{% trans 'Loading' %}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{% trans 'Close' %}"></button>
      </div>
      <div class="modal-body"></div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" id="indexDialogModalGoButton">{% trans 'Go' %}</button>
        <button type="button" class="btn btn-secondary" data-bs-target="#indexDialogPreviewModal" data-bs-toggle="modal">{% trans 'Preview SQL' %}</button>
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans 'Close' %}</button>
      </div>
    </div>
  </div>
</div>
<div class="modal fade" id="indexDialogPreviewModal" aria-hidden="true" aria-labelledby="indexDialogPreviewModalLabel" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="indexDialogPreviewModalLabel">{% trans 'Preview SQL' %}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{% trans 'Close' %}"></button>
      </div>
      <div class="modal-body">
        <div class="spinner-border" role="status">
          <span class="visually-hidden">{% trans 'Loading…' %}</span>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" data-bs-target="#indexDialogModal" data-bs-toggle="modal">{% trans 'Go back' %}</button>
      </div>
    </div>
  </div>
</div>
