{% if not is_ajax %}
  </div>
{% endif %}
{% if not is_ajax and not is_minimal %}
  {% if self_url is not empty %}
    <div id="selflink" class="d-print-none">
      <a href="{{ self_url }}" title="{% trans 'Open new phpMyAdmin window' %}" target="_blank" rel="noopener noreferrer">
        {% if show_icons('TabsMode') %}
          {{ get_image('window-new', 'Open new phpMyAdmin window'|trans) }}
        {% else %}
          {% trans 'Open new phpMyAdmin window' %}
        {% endif %}
      </a>
    </div>
  {% endif %}

  <div class="clearfloat d-print-none" id="pma_errors">
    {{ error_messages|raw }}
  </div>

  {{ scripts|raw }}

  {% if is_demo %}
    <div id="pma_demo" class="d-print-none">
      {% apply notice %}
        <a href="{{ url('/') }}">{% trans 'phpMyAdmin Demo Server' %}:</a>
        {% if git_revision_info is not empty %}
          {% set revision_info -%}
            <a target="_blank" rel="noopener noreferrer" href="{{ git_revision_info.revisionUrl|link }}">{{ git_revision_info.revision }}</a>
          {%- endset %}
          {% set branch_info -%}
            <a target="_blank" rel="noopener noreferrer" href="{{ git_revision_info.branchUrl|link }}">{{ git_revision_info.branch }}</a>
          {%- endset %}
          {{ 'Currently running Git revision %1$s from the %2$s branch.'|trans|format(revision_info, branch_info)|raw }}
        {% else %}
          {% trans 'Git information missing!' %}
        {% endif %}
      {% endapply %}
    </div>
  {% endif %}

  {{ footer|raw }}
{% endif %}
{% if not is_ajax %}
  </body>
</html>
{% endif %}
