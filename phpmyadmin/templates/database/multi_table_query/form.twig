<ul class="nav nav-pills m-2">
  <li class="nav-item">
    <a class="nav-link active" href="{{ url('/database/multi-table-query', {'db': db}) }}">
      {% trans 'Multi-table query' %}
    </a>
  </li>

  <li class="nav-item">
    <a class="nav-link" href="{{ url('/database/qbe', {'db': db}) }}">
      {% trans 'Query by example' %}
    </a>
  </li>
</ul>

<div class="mb-3">
  <button class="btn btn-sm btn-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#queryWindow" aria-expanded="true" aria-controls="queryWindow">
    {% trans 'Query window' %}
  </button>
</div>
<div class="collapse show mb-3" id="queryWindow">

<form action="" id="multi_table_query_form" class="multi_table_query_form query_form">
    <input type="hidden" id="db_name" value="{{ db }}">
    <fieldset class="pma-fieldset">
        {% for table in tables %}
            <div class="d-none" id="{{ table.hash }}">
                <option value="*">*</option>
                {% for column in table.columns %}
                    <option value="{{ column }}">{{ column }}</option>
                {% endfor %}
            </div>
        {% endfor %}

        {% for id in 0..default_no_of_columns %}
            {% if id == 0 %}<div class="d-none" id="new_column_layout">{% endif %}
            <fieldset class="pma-fieldset column_details query-form__fieldset--inline position-relative">
                <select class="tableNameSelect query-form__select--inline">
                    <option value="">{% trans 'select table' %}</option>
                    {% for keyTableName, table in tables %}
                      <option data-hash="{{ table.hash }}" value="{{ keyTableName }}">{{ keyTableName }}</option>
                    {% endfor %}
                </select>
                <span>.</span>
                <select class="columnNameSelect query-form__select--inline">
                    <option value="">{% trans 'select column' %}</option>
                </select>
                <br>
                <input type="checkbox" checked="checked" class="show_col">
                <span>{% trans 'Show' %}</span>
                <br>
                <input type="text" placeholder="{% trans 'Table alias' %}" class="table_alias">
                <input type="text" placeholder="{% trans 'Column alias' %}" class="col_alias">
                <br>
                <input type="checkbox"
                    title="{% trans 'Use this column in criteria' %}"
                    class="criteria_col">

                <button class="btn btn-link p-0 jsCriteriaButton" type="button" data-bs-toggle="collapse" data-bs-target="#criteriaOptions{{ id }}" aria-expanded="false" aria-controls="criteriaOptions{{ id }}">
                  {% trans 'criteria' %}
                </button>
                <div class="collapse jsCriteriaOptions" id="criteriaOptions{{ id }}">

                <div>
                    <table class="table table-sm table-borderless align-middle w-auto">

                        <tr class="sort_order query-form__tr--bg-none">
                            <td>{% trans 'Sort' %}</td>
                            <td><input type="radio" name="sort[{{ id }}]">{% trans 'Ascending' %}</td>
                            <td><input type="radio" name="sort[{{ id }}]">{% trans 'Descending' %}</td>
                        </tr>

                        <tr class="logical_operator query-form__tr--bg-none query-form__tr--hide">
                            <td>{% trans 'Add as' %}</td>
                            <td>
                                <input type="radio"
                                    name="logical_op[{{ id }}]"
                                    value="AND"
                                    class="logical_op"
                                    checked="checked">
                                AND
                            </td>
                            <td>
                                <input type="radio"
                                    name="logical_op[{{ id }}]"
                                    value="OR"
                                    class="logical_op">
                                OR
                            </td>
                        </tr>

                        <tr class="query-form__tr--bg-none">
                            <td>Op </td>
                            <td>
                                <select class="criteria_op">
                                    <option value="=">=</option>
                                    <option value=">">&gt;</option>
                                    <option value=">=">&gt;=</option>
                                    <option value="<">&lt;</option>
                                    <option value="<=">&lt;=</option>
                                    <option value="!=">!=</option>
                                    <option value="LIKE">LIKE</option>
                                    <option value="LIKE %...%">LIKE %...%</option>
                                    <option value="NOT LIKE">NOT LIKE</option>
                                    <option value="NOT LIKE %...%">NOT LIKE %...%</option>
                                    <option value="IN (...)">IN (...)</option>
                                    <option value="NOT IN (...)">NOT IN (...)</option>
                                    <option value="BETWEEN">BETWEEN</option>
                                    <option value="NOT BETWEEN">NOT BETWEEN</option>
                                    <option value="IS NULL">IS NULL</option>
                                    <option value="IS NOT NULL">IS NOT NULL</option>
                                    <option value="REGEXP">REGEXP</option>
                                    <option value="REGEXP ^...$">REGEXP ^...$</option>
                                    <option value="NOT REGEXP">NOT REGEXP</option>
                                </select>
                            </td>
                            <td>
                                <select class="criteria_rhs">
                                    <option value="text">{% trans 'Text' %}</option>
                                    <option value="anotherColumn">{% trans 'Another column' %}</option>
                                </select>
                            </td>
                        </tr>

                        <tr class="rhs_table query-form__tr--hide query-form__tr--bg-none">
                            <td></td>
                            <td>
                                <select  class="tableNameSelect">
                                    <option value="">{% trans 'select table' %}</option>
                                    {% for keyTableName, table in tables %}
                                        <option data-hash="{{ table.hash }}" value="{{ keyTableName }}">{{ keyTableName }}</option>
                                    {% endfor %}
                                </select><span>.</span>
                            </td>
                            <td>
                                <select class="columnNameSelect query-form__select--inline">
                                    <option value="">{% trans 'select column' %}</option>
                                </select>
                            </td>
                        </tr>

                        <tr class="rhs_text query-form__tr--bg-none">
                            <td></td>
                            <td colspan="2">
                                <input type="text"
                                    class="rhs_text_val query-form__input--wide"
                                    placeholder="{% trans 'Enter criteria as free text' %}">
                            </td>
                        </tr>

                        </table>
                    </div>
                </div>
                <button type="button" class="btn-close position-absolute top-0 end-0 jsRemoveColumn" aria-label="{% trans 'Remove this column' %}"></button>
            </fieldset>
            {% if id == 0 %}</div>{% endif %}
        {% endfor %}

        <fieldset class="pma-fieldset query-form__fieldset--inline">
            <input class="btn btn-secondary" type="button" value="{% trans '+ Add column' %}" id="add_column_button">
        </fieldset>

        <fieldset class="pma-fieldset">
              {# Keep the block without a space between the open and close tag #}
                <textarea id="MultiSqlquery"
                    class="query-form__multi-sql-query"
                    cols="80"
                    rows="4"
                    name="sql_query"
                    dir="ltr"></textarea>
        </fieldset>
    </fieldset>

    <fieldset class="pma-fieldset tblFooters">
        <input class="btn btn-secondary" type="button" id="update_query_button" value="{% trans 'Update query' %}">
        <input class="btn btn-primary" type="button" id="submit_query" value="{% trans 'Submit query' %}">
    </fieldset>
</form>
</div>
<div id="sql_results"></div>
