<tr{% if row_class is not empty %} class="{{ row_class }}"{% endif %}>
  <td>
    <input type="checkbox" class="checkall" name="item_name[]" value="{{ trigger.name }}">
  </td>
  <td>
    <span class='drop_sql hide'>{{ trigger.drop }}</span>
    <strong>{{ trigger.name }}</strong>
  </td>
  {% if table is empty %}
    <td>
      <a href="{{ url('/table/triggers', {'db': db, 'table': trigger.table}) }}">{{ trigger.table }}</a>
    </td>
  {% endif %}
  <td>
    {{ trigger.action_timing }}
  </td>
  <td>
    {{ trigger.event_manipulation }}
  </td>
  <td>
    {% if has_edit_privilege %}
      <a class="ajax edit_anchor" href="{{ url('/database/triggers', {
        'db': db,
        'table': table,
        'edit_item': true,
        'item_name': trigger.name
      }) }}">
        {{ get_icon('b_edit', 'Edit'|trans) }}
      </a>
    {% else %}
      {{ get_icon('bd_edit', 'Edit'|trans) }}
    {% endif %}
  </td>
  <td>
    <a class="ajax export_anchor" href="{{ url('/database/triggers', {
      'db': db,
      'table': table,
      'export_item': true,
      'item_name': trigger.name
    }) }}">
      {{ get_icon('b_export', 'Export'|trans) }}
    </a>
  </td>
  <td>
    {% if has_drop_privilege %}
      {{ link_or_button(
        url('/sql'),
        {
          'db': db,
          'table': table,
          'sql_query': trigger.drop,
          'goto': url('/database/triggers', {'db': db})
        },
        get_icon('b_drop', 'Drop'|trans),
        {'class': 'ajax drop_anchor'}
      ) }}
    {% else %}
      {{ get_icon('bd_drop', 'Drop'|trans) }}
    {% endif %}
  </td>
</tr>
