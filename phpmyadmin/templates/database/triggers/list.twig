<div class="container-fluid my-3">
  <h2>
    {{ get_icon('b_triggers', 'Triggers'|trans) }}
    {{ show_mysql_docu('TRIGGERS') }}
  </h2>

  <div class="d-flex flex-wrap my-3">
    <div>
      <div class="input-group">
        <div class="input-group-text">
          <div class="form-check mb-0">
            <input class="form-check-input checkall_box" type="checkbox" value="" id="checkAllCheckbox" form="rteListForm">
            <label class="form-check-label" for="checkAllCheckbox">{% trans 'Check all' %}</label>
          </div>
        </div>
        <button class="btn btn-outline-secondary" id="bulkActionExportButton" type="submit" name="submit_mult" value="export" form="rteListForm" title="{% trans 'Export' %}">
          {{ get_icon('b_export', 'Export'|trans) }}
        </button>
        <button class="btn btn-outline-secondary" id="bulkActionDropButton" type="submit" name="submit_mult" value="drop" form="rteListForm" title="{% trans 'Drop' %}">
          {{ get_icon('b_drop', 'Drop'|trans) }}
        </button>
      </div>
    </div>

    <div class="ms-auto">
      <a class="ajax add_anchor btn btn-primary{{ not has_privilege ? ' disabled' }}" href="{{ url('/database/triggers', {'db': db, 'table': table, 'add_item': true}) }}" role="button"{{ not has_privilege ? ' tabindex="-1" aria-disabled="true"' }}>
        {{ get_icon('b_trigger_add', 'Create new trigger'|trans) }}
      </a>
    </div>
  </div>

  <form id="rteListForm" class="ajax" action="{{ url(table is not empty ? '/table/triggers' : '/database/triggers') }}">
    {{ get_hidden_inputs(db, table) }}

    <div id="nothing2display"{{ items is not empty ? ' class="hide"' }}>
      {% trans 'There are no triggers to display.' %}
    </div>

    <table id="triggersTable" class="table table-striped table-hover{{ items is empty ? ' hide' }} w-auto data">
      <thead>
        <tr>
          <th></th>
          <th>{% trans 'Name' %}</th>
          {% if table is empty %}
            <th>{% trans 'Table' %}</th>
          {% endif %}
          <th>{% trans 'Time' %}</th>
          <th>{% trans 'Event' %}</th>
          <th colspan="3"></th>
        </tr>
      </thead>
      <tbody>
        <tr class="hide">{% for i in 0..(table is empty ? 7 : 6) %}<td></td>{% endfor %}</tr>

        {{ rows|raw }}
      </tbody>
    </table>
  </form>
</div>
