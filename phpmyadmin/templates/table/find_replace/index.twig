<ul class="nav nav-pills m-2">
  <li class="nav-item">
    <a class="nav-link" href="{{ url('/table/search', {'db': db, 'table': table, 'pos': 0}) }}">
      {{ get_icon('b_search', 'Table search'|trans, false, false, 'TabsMode') }}
    </a>
  </li>

  <li class="nav-item">
    <a class="nav-link" href="{{ url('/table/zoom-search', {'db': db, 'table': table}) }}">
      {{ get_icon('b_select', 'Zoom search'|trans, false, false, 'TabsMode') }}
    </a>
  </li>

  <li class="nav-item">
    <a class="nav-link active" href="{{ url('/table/find-replace', {'db': db, 'table': table}) }}">
      {{ get_icon('b_find_replace', 'Find and replace'|trans, false, false, 'TabsMode') }}
    </a>
  </li>
</ul>

<form method="post" action="{{ url('/table/find-replace') }}" name="insertForm" id="find_replace_form" class="ajax lock-page">
  {{ get_hidden_inputs(db, table) }}
  <input type="hidden" name="goto" value="{{ goto }}">
  <input type="hidden" name="back" value="{{ url('/table/find-replace') }}">

  <div class="card">
    <div class="card-header">{% trans 'Find and replace' %}</div>

    <div class="card-body">
      <div class="mb-3">
        <label class="form-label" for="findInput">{% trans 'Find:' %}</label>
        <input class="form-control" type="text" value="" name="find" id="findInput" required>
      </div>

      <div class="mb-3">
        <label class="form-label" for="replaceWithInput">{% trans 'Replace with:' %}</label>
        <input class="form-control" type="text" value="" name="replaceWith" id="replaceWithInput">
      </div>

      <div class="mb-3">
        <label class="form-label" for="columnIndexSelect">{% trans 'Column:' %}</label>
        <select class="form-select" name="columnIndex" id="columnIndexSelect">
          {% for i in 0..column_names|length - 1 %}
            {% set type = types[column_names[i]] %}

            {% if sql_types.getTypeClass(type) == 'CHAR' %}
              <option value="{{ i }}">
                {{- column_names[i] -}}
              </option>
            {% endif %}
          {% endfor %}
        </select>
      </div>

      <div class="form-check">
        <input class="form-check-input" type="checkbox" name="useRegex" id="useRegex">
        <label class="form-check-label" for="useRegex">{% trans 'Use regular expression' %}</label>
      </div>
    </div>

    <div class="card-footer">
      <input class="btn btn-primary" type="submit" name="submit" value="{% trans 'Go' %}">
    </div>
  </div>
</form>
<div id="sqlqueryresultsouter"></div>
