<div class="container-fluid">
  <h2>{% trans 'Repair partition' %}</h2>

  {{ message|raw }}

  {% for name, table in rows %}
    <div class="card mb-3">
      <div class="card-header">{{ name }} ({{ partition_name }})</div>

      <ul class="list-group list-group-flush">
        {% for row in table %}
          <li class="list-group-item">
            {% if row.Op|lower != 'repair' %}
              <span class="badge bg-secondary text-dark">{{ row.Op|title }}</span>
            {% endif %}

            {% set badge_variation %}
              {%- if row.Msg_type|lower == 'error' -%}
                bg-danger
              {%- elseif row.Msg_type|lower == 'warning' -%}
                bg-warning
              {%- elseif row.Msg_type|lower == 'info' or row.Msg_type|lower == 'note' -%}
                bg-info
              {%- else -%}
                bg-secondary
              {%- endif -%}
            {% endset %}
            <span class="badge {{ badge_variation }} text-dark">{{ row.Msg_type|title }}</span>

            {{ row.Msg_text }}
          </li>
        {% endfor %}
      </ul>
    </div>
  {% endfor %}
</div>
