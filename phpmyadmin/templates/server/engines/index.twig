<div class="container-fluid">
  <div class="row">
  <h2>
    {{ get_image('b_engine') }}
    {% trans 'Storage engines' %}
  </h2>
  </div>

  <div class="table-responsive">
    <table class="table table-striped table-hover w-auto">
      <thead>
        <tr>
          <th scope="col">{% trans 'Storage Engine' %}</th>
          <th scope="col">{% trans 'Description' %}</th>
        </tr>
      </thead>
      <tbody>
        {% for engine, details in engines %}
          <tr class="
            {{- details['Support'] == 'NO' or details['Support'] == 'DISABLED' ? ' disabled' }}
            {{ details['Support'] == 'DEFAULT' ? ' marked' }}">
            <td>
              <a rel="newpage" href="{{ url('/server/engines/' ~ engine) }}">
                {{ details['Engine'] }}
              </a>
            </td>
            <td>{{ details['Comment'] }}</td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>
