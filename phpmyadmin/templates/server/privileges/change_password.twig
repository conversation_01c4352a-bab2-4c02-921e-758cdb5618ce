<form method="post" id="change_password_form" action="
  {{- is_privileges ? url('/server/privileges') : url('/user-password') }}" name="chgPassword" class="{{ is_privileges ? 'submenu-item' }}" autocomplete="off">
  {{ get_hidden_inputs() }}
  {% if is_privileges %}
    <input type="hidden" name="username" value="{{ username }}">
    <input type="hidden" name="hostname" value="{{ hostname }}">
  {% endif %}

  <fieldset class="pma-fieldset" id="fieldset_change_password">
    <legend{{ is_privileges ? ' data-submenu-label="Change password"' }}>{% trans 'Change password' %}</legend>
    <table class="table table-borderless w-auto">
      <tr>
        <td colspan="2">
          <input type="radio" name="nopass" value="1" id="nopass_1">
          <label for="nopass_1">{% trans 'No Password' %}</label>
        </td>
      </tr>
      <tr class="align-middle">
        <td>
          <input type="radio" name="nopass" value="0" id="nopass_0" checked="checked">
          <label for="nopass_0">{% trans 'Password:' %}&nbsp;</label>
        </td>
        <td>
          {% trans 'Enter:' %}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <input type="password" spellcheck="false" name="pma_pw" id="text_pma_change_pw" size="10" class="textfield"
                 onkeyup="Functions.checkPasswordStrength($(this).val(), $('#change_password_strength_meter'), meter_obj_label = $('#change_password_strength'), CommonParams.get('user'));"
                 onchange="nopass[1].checked = true">
          <span>{% trans %}Strength:{% context %}Password strength{% endtrans %}</span>
          <meter max="4" id="change_password_strength_meter" name="pw_meter"></meter>
          <span id="change_password_strength" name="pw_strength"></span>
          <br>
          {% trans 'Re-type:' %}&nbsp;
          <input type="password" spellcheck="false" name="pma_pw2" id="text_pma_change_pw2" size="10" class="textfield" onchange="nopass[1].checked = true">
        </td>
      </tr>

      {% if not is_new or (is_new and has_more_auth_plugins) %}
        <tr class="align-middle">
          <td>
            <label for="select_authentication_plugin_cp">{% trans 'Password Hashing:' %}</label>
          </td>
          <td>
            <select name="authentication_plugin" id="select_authentication_plugin_cp">
              {% for plugin_name, plugin_description in active_auth_plugins %}
                <option value="{{ plugin_name }}"{{ plugin_name == orig_auth_plugin ? ' selected' }}>{{ plugin_description }}</option>
              {% endfor %}
            </select>
          </td>
        </tr>
      {% endif %}

      <tr id="tr_element_before_generate_password"></tr>
    </table>

    {% if is_new and has_more_auth_plugins %}
      <div{{ orig_auth_plugin != 'sha256_password' ? ' class="hide"' }} id="ssl_reqd_warning_cp">
        {% apply notice %}
          {% trans %}
            This method requires using an '<i>SSL connection</i>' or an '<i>unencrypted connection that encrypts the password using RSA</i>'; while connecting to the server.
          {% endtrans %}
          {{ show_mysql_docu('sha256-authentication-plugin') }}
        {% endapply %}
      </div>
    {% endif %}

  </fieldset>

  <fieldset id="fieldset_change_password_footer" class="pma-fieldset tblFooters">
    <input type="hidden" name="change_pw" value="1">
    <input class="btn btn-primary" type="submit" value="{% trans 'Go' %}">
  </fieldset>
</form>
