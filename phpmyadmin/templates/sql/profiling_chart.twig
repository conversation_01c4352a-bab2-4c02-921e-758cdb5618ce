<fieldset class="pma-fieldset">
  <legend>{% trans 'Profiling' %}</legend>
  <div class="float-start mx-2">
    <h3>{% trans 'Detailed profile' %}</h3>
    <table class="table table-sm table-striped" id="profiletable">
      <thead>
      <tr>
        <th>
          {% trans 'Order' %}
          <div class="sorticon"></div>
        </th>
        <th>
          {% trans 'State' %}
          <div class="sorticon"></div>
        </th>
        <th>
          {% trans 'Time' %}
          <div class="sorticon"></div>
        </th>
      </tr>
      </thead>
      <tbody>
        {% for state in profiling.profile %}
          <tr>
            <td>{{ loop.index }}</td>
            <td>{{ state.status }}</td>
            <td class="text-end">
              {{ state.duration }}s
              <span class="rawvalue hide">{{ state.duration_raw }}</span>
            </td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>

  <div class="float-start mx-2">
    <h3>{% trans 'Summary by state' %}{{ show_mysql_docu('general-thread-states') }}</h3>
    <table class="table table-sm table-striped" id="profilesummarytable">
      <thead>
      <tr>
        <th>
          {% trans 'State' %}
          <div class="sorticon"></div>
        </th>
        <th>
          {% trans 'Total Time' %}
          <div class="sorticon"></div>
        </th>
        <th>
          {% trans '% Time' %}
          <div class="sorticon"></div>
        </th>
        <th>
          {% trans 'Calls' %}
          <div class="sorticon"></div>
        </th>
        <th>
          {% trans 'ø Time' %}
          <div class="sorticon"></div>
        </th>
      </tr>
      </thead>
      <tbody>
        {% for name, stats in profiling.states %}
          <tr>
            <td>{{ name }}</td>
            <td class="text-end">
              {{ format_number(stats.total_time, 3, 1) }}s
              <span class="rawvalue hide">{{ stats.total_time }}</span>
            </td>
            <td class="text-end">
              {{ format_number(100 * (stats.total_time / profiling.total_time), 0, 2) }}%
            </td>
            <td class="text-end">{{ stats.calls }}</td>
            <td class="text-end">
              {{ format_number(stats.total_time / stats.calls, 3, 1) }}s
              <span class="rawvalue hide">
                {{ (stats.total_time / stats.calls)|number_format(8, '.', '') }}
              </span>
            </td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  <div class='clearfloat'></div>

  <div id="profilingChartData" class="hide">
    {{- profiling.chart|json_encode() -}}
  </div>
  <div id="profilingchart" class="hide"></div>

  <script type="text/javascript">
    AJAX.registerOnload('sql.js', function () {
      Sql.makeProfilingChart();
      Sql.initProfilingTables();
    });
  </script>
</fieldset>
