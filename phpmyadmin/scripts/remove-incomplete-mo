#!/bin/sh
#
# Removes mo files for incomplete translations
#

# Do not run as CGI
if [ -n "$GATEWAY_INTERFACE" ] ; then
    echo 'Can not invoke as CGI!'
    exit 1
fi

set -e

#
# How many percent needs to be translated
#
THRESHOLD=40

#
# Generated output file
#
TMPOUTPUTFILE=libraries/language_stats.inc.php.tmp
OUTPUTFILE=libraries/language_stats.inc.php

if [ ! -z "$1" ] ; then
    THRESHOLD=$1
fi

echo '<?php' > $TMPOUTPUTFILE
echo '/**' >> $TMPOUTPUTFILE
echo ' * Automatically generated file, do not edit!' >> $TMPOUTPUTFILE
echo ' * Generated by scripts/remove-incomplete-mo' >> $TMPOUTPUTFILE
echo ' */' >> $TMPOUTPUTFILE
echo '' >> $TMPOUTPUTFILE
echo "\$GLOBALS['language_stats'] = [" >> $TMPOUTPUTFILE

check() {
    TMPOUTPUTFILE=$2
    lang=`echo $1 | sed 's@po/\(.*\)\.po@\1@'`
    STATS=`LC_ALL=C  msgfmt --statistics -o /dev/null $1 2>&1`
    if echo $STATS | grep -q ' translated ' ; then
        TRANSLATED=`echo $STATS | sed 's/\(^\|.* \)\([0-9]*\) translated.*/\2/'`
    else
        TRANSLATED=0
    fi
    if echo $STATS | grep -q ' fuzzy ' ; then
        FUZZY=`echo $STATS | sed 's/\(^\|.* \)\([0-9]*\) fuzzy.*/\2/'`
    else
        FUZZY=0
    fi
    if echo $STATS | grep -q ' untranslated ' ; then
        UNTRANSLATED=`echo $STATS | sed 's/\(^\|.* \)\([0-9]*\) untranslated.*/\2/'`
    else
        UNTRANSLATED=0
    fi
    PERCENT=`expr 100 \* $TRANSLATED / \( $TRANSLATED + $FUZZY + $UNTRANSLATED \) || true`
    echo "    '$lang' => $PERCENT," >> $TMPOUTPUTFILE

    if [ $PERCENT -lt $THRESHOLD ] ; then
        echo "Removing $lang, only $PERCENT%"
        rm -f locale/$lang/LC_MESSAGES/phpmyadmin.mo
        rmdir locale/$lang/LC_MESSAGES
        rmdir locale/$lang
    fi
}

for x in po/*.po  ; do
    check $x $TMPOUTPUTFILE
done

echo '];' >> $TMPOUTPUTFILE

mv $TMPOUTPUTFILE $OUTPUTFILE
