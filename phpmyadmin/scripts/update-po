#!/bin/sh
# vim: expandtab sw=4 ts=4 sts=4:
export LC_ALL=C

# Do not run as CGI
if [ -n "$GATEWAY_INTERFACE" ] ; then
    echo 'Can not invoke as CGI!'
    exit 1
fi

# Exit on failure
set -e

# Generate Twig template cache in clean dir
rm -rf twig-templates/
php scripts/console cache:warmup --twig-po --env development

# Update pot (template), ensure that advisor is at the end
LOCS=`ls po/*.po | sed 's@.*/\(.*\)\.po@\1@'`

find . -name '*.php' -not -path './test/*' -not -path './po/*' -not -path './tmp/*' -not -path './release/*' -not -path './node_modules/*' -not -path './vendor/*' > twig-templates/filesList

php ./scripts/match-twig-cache
cat ./twig-templates/filesList | sort --dictionary-order --ignore-case --output ./twig-templates/filesList
php ./scripts/match-twig-cache --reverse

xgettext \
    -d phpmyadmin \
    --msgid-bugs-address=<EMAIL> \
    -o po/phpmyadmin.pot \
    --language=PHP \
    --add-comments=l10n \
    --add-location \
    --debug \
    --from-code=utf-8 \
    --keyword=__ --keyword=_gettext --keyword=_pgettext:1c,2 --keyword=_ngettext:1,2 \
    --copyright-holder="phpMyAdmin devel team" \
    `cat ./twig-templates/filesList`

# Fixup filenames for Twig templates
php scripts/console fix-po-twig

# Remote twig templates
rm -rf twig-templates/

# Keep in sync with create-release.sh
fetchReleaseFromFile() {
    php -r "define('VERSION_SUFFIX', ''); require_once('libraries/classes/Version.php'); echo \PhpMyAdmin\Version::VERSION;"
}

ver="$(fetchReleaseFromFile)"

sed -i.~ '
    s/SOME DESCRIPTIVE TITLE/phpMyAdmin translation/;
    s/PACKAGE/phpMyAdmin/;
    s/(C) YEAR/(C) 2003 - '`date +%Y`'/;
    s/VERSION/'$ver'/;
    ' po/phpmyadmin.pot

# Update po files (translations)
for loc in $LOCS ; do
    sed -i.~ '
        s/SOME DESCRIPTIVE TITLE/phpMyAdmin translation/;
        s/PACKAGE/phpMyAdmin/;
        s/(C) YEAR/(C) 2003 - '`date +%Y`'/;
        s/VERSION/'$ver'/;
        s/Project-Id-Version: phpMyAdmin .*/Project-Id-Version: phpMyAdmin '$ver'\\n"/;
        ' po/$loc.po
    msgmerge --previous -U po/$loc.po po/phpmyadmin.pot
done

# Commit changes
git add po/*.po po/phpmyadmin.pot
git commit -s -m 'Update po files

[ci skip]'
