#!/usr/bin/env php
<?php

use Php<PERSON>yAdmin\Command\CacheWarmupCommand;
use Php<PERSON>yAdmin\Command\FixPoTwigCommand;
use PhpMyAdmin\Command\SetVersionCommand;
use PhpMyAdmin\Command\WriteGitRevisionCommand;
use Php<PERSON>yAdmin\Command\TwigLintCommand;
use Php<PERSON>yAdmin\Config;
use PhpMyAdmin\Core;
use PhpMyAdmin\DatabaseInterface;
use PhpMyAdmin\Tests\Stubs\DbiDummy;
use Symfony\Component\Console\Application;

if (! defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(__DIR__) . DIRECTORY_SEPARATOR);
}

define('PHPMYADMIN', true);
require_once ROOT_PATH . 'libraries/constants.php';
require_once AUTOLOAD_FILE;

if (! class_exists(Application::class)) {
    echo 'Be sure to have dev-dependencies installed.' . PHP_EOL;
    echo 'Command aborted.' . PHP_EOL;
    exit(1);
}

$containerBuilder = Core::getContainerBuilder();
$cfg['environment'] = 'production';
$config = new Config(CONFIG_FILE);
$config->set('environment', $cfg['environment']);
$dbi = new DatabaseInterface(new DbiDummy());

$application = new Application('phpMyAdmin Console Tool');

$application->add(new CacheWarmupCommand());
$application->add(new TwigLintCommand());
$application->add(new SetVersionCommand());
$application->add(new WriteGitRevisionCommand());
$application->add(new FixPoTwigCommand());

$application->run();
