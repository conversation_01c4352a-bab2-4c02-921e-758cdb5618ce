<?xml version="1.0" encoding="UTF-8"?>
<phpunit
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/8.5/phpunit.xsd"
    bootstrap="test/bootstrap-dist.php"
    colors="true"
    forceCoversAnnotation="true"
    verbose="true"
    executionOrder="random"
    defaultTestSuite="unit"
>
    <php>
        <const name="PHPMYADMIN" value="1"/>
        <const name="TESTSUITE" value="1"/>
        <env name="LC_ALL" value="C.UTF8" force="true"/>
    </php>

    <testsuites>
        <testsuite name="unit">
            <directory suffix="Test.php">test/classes</directory>
        </testsuite>
        <testsuite name="selenium">
            <directory suffix="Test.php">test/selenium</directory>
        </testsuite>
    </testsuites>

    <logging>
        <log type="coverage-clover" target="build/logs/clover.xml" />
        <log type="coverage-xml" target="build/logs/coverage-xml"/>
        <log type="junit" target="build/logs/junit.xml" />
    </logging>

    <filter>
        <whitelist>
            <directory suffix=".php">.</directory>
            <exclude>
                <directory>examples</directory>
                <directory>libraries/cache</directory>
                <directory>node_modules</directory>
                <directory>test</directory>
                <directory>tmp</directory>
                <directory>vendor</directory>
            </exclude>
        </whitelist>
    </filter>
</phpunit>
