<?xml version="1.0" encoding="UTF-8"?>
<office:document-content xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0"
    xmlns:style="urn:oasis:names:tc:opendocument:xmlns:style:1.0"
    xmlns:text="urn:oasis:names:tc:opendocument:xmlns:text:1.0"
    xmlns:table="urn:oasis:names:tc:opendocument:xmlns:table:1.0"
    xmlns:draw="urn:oasis:names:tc:opendocument:xmlns:drawing:1.0"
    xmlns:fo="urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
    xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0"
    xmlns:number="urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0"
    xmlns:presentation="urn:oasis:names:tc:opendocument:xmlns:presentation:1.0"
    xmlns:svg="urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0"
    xmlns:chart="urn:oasis:names:tc:opendocument:xmlns:chart:1.0"
    xmlns:dr3d="urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0"
    xmlns:math="http://www.w3.org/1998/Math/MathML"
    xmlns:form="urn:oasis:names:tc:opendocument:xmlns:form:1.0"
    xmlns:script="urn:oasis:names:tc:opendocument:xmlns:script:1.0"
    xmlns:ooo="http://openoffice.org/2004/office"
    xmlns:ooow="http://openoffice.org/2004/writer"
    xmlns:oooc="http://openoffice.org/2004/calc"
    xmlns:dom="http://www.w3.org/2001/xml-events"
    xmlns:xforms="http://www.w3.org/2002/xforms"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:rpt="http://openoffice.org/2005/report"
    xmlns:of="urn:oasis:names:tc:opendocument:xmlns:of:1.2"
    xmlns:xhtml="http://www.w3.org/1999/xhtml"
    xmlns:grddl="http://www.w3.org/2003/g/data-view#"
    xmlns:tableooo="http://openoffice.org/2009/table"
    xmlns:field="urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0"
    xmlns:gnm="http://www.gnumeric.org/odf-extension/1.0" office:version="1.2">
    <office:scripts/>
    <office:font-face-decls>
        <style:font-face style:name="Arial" svg:font-family="Arial"/>
        <style:font-face style:name="Sans" svg:font-family="Sans"/>
        <style:font-face style:name="Arimo" svg:font-family="Arimo" style:font-family-generic="swiss" style:font-pitch="variable"/>
        <style:font-face style:name="DejaVu Sans Condensed" svg:font-family="&apos;DejaVu Sans Condensed&apos;" style:font-family-generic="system" style:font-pitch="variable"/>
        <style:font-face style:name="Noto Sans CJK SC Thin" svg:font-family="&apos;Noto Sans CJK SC Thin&apos;" style:font-family-generic="system" style:font-pitch="variable"/>
    </office:font-face-decls>
    <office:automatic-styles>
        <style:style style:name="co1" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="2.593cm"/>
        </style:style>
        <style:style style:name="co2" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="6.262cm"/>
        </style:style>
        <style:style style:name="co3" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="7.781cm"/>
        </style:style>
        <style:style style:name="co4" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="3.625cm"/>
        </style:style>
        <style:style style:name="co5" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="3.969cm"/>
        </style:style>
        <style:style style:name="co6" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="1.402cm"/>
        </style:style>
        <style:style style:name="co7" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="3.836cm"/>
        </style:style>
        <style:style style:name="co8" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="1.693cm"/>
        </style:style>
        <style:style style:name="co9" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="3.572cm"/>
        </style:style>
        <style:style style:name="co10" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="1.931cm"/>
        </style:style>
        <style:style style:name="co11" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="14.175cm"/>
        </style:style>
        <style:style style:name="co12" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="3.545cm"/>
        </style:style>
        <style:style style:name="co13" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="3.889cm"/>
        </style:style>
        <style:style style:name="co14" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="2.699cm"/>
        </style:style>
        <style:style style:name="co15" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="3.413cm"/>
        </style:style>
        <style:style style:name="co16" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="1.879cm"/>
        </style:style>
        <style:style style:name="co17" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="3.254cm"/>
        </style:style>
        <style:style style:name="co18" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="2.328cm"/>
        </style:style>
        <style:style style:name="co19" style:family="table-column">
            <style:table-column-properties fo:break-before="auto" style:column-width="3.307cm"/>
        </style:style>
        <style:style style:name="ro1" style:family="table-row">
            <style:table-row-properties style:row-height="0.446cm" fo:break-before="auto" style:use-optimal-row-height="true"/>
        </style:style>
        <style:style style:name="ro2" style:family="table-row">
            <style:table-row-properties style:row-height="0.427cm" fo:break-before="auto" style:use-optimal-row-height="true"/>
        </style:style>
        <style:style style:name="ta1" style:family="table" style:master-page-name="Shop">
            <style:table-properties table:display="true" style:writing-mode="lr-tb"/>
        </style:style>
        <style:style style:name="ta2" style:family="table" style:master-page-name="Feuille_20_1">
            <style:table-properties table:display="true" style:writing-mode="lr-tb"/>
        </style:style>
        <number:number-style style:name="N2">
            <number:number number:decimal-places="2" number:min-integer-digits="1"/>
        </number:number-style>
        <number:boolean-style style:name="N99">
            <number:boolean/>
        </number:boolean-style>
        <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default">
            <style:table-cell-properties style:glyph-orientation-vertical="auto" style:cell-protect="protected" style:print-content="true" style:diagonal-bl-tr="none" style:diagonal-tl-br="none" fo:background-color="transparent" fo:wrap-option="no-wrap" fo:border="0.035cm solid #000000" style:rotation-angle="0" style:rotation-align="none" style:shrink-to-fit="false" gnm:background-colour="#ffffff" gnm:pattern-colour="#000000" gnm:pattern="0" style:vertical-align="bottom"/>
            <style:paragraph-properties fo:margin-left="0cm"/>
            <style:text-properties fo:color="#000000" style:text-line-through-style="none" style:font-name="Arial" fo:font-size="10pt" fo:font-style="normal" style:text-underline-style="none" fo:font-weight="normal" style:text-underline-mode="continuous" style:text-overline-mode="continuous" style:text-line-through-mode="continuous"/>
        </style:style>
        <style:style style:name="ce2" style:family="table-cell" style:parent-style-name="Default">
            <style:table-cell-properties style:glyph-orientation-vertical="auto" style:cell-protect="protected" style:print-content="true" style:diagonal-bl-tr="none" style:diagonal-tl-br="none" fo:background-color="transparent" fo:wrap-option="wrap" fo:border="0.035cm solid #000000" style:rotation-angle="0" style:rotation-align="none" style:shrink-to-fit="false" gnm:background-colour="#ffffff" gnm:pattern-colour="#000000" gnm:pattern="0" style:vertical-align="middle"/>
            <style:paragraph-properties fo:margin-left="0cm"/>
            <style:text-properties fo:color="#000000" style:text-line-through-style="none" style:font-name="Arial" fo:font-size="10pt" fo:font-style="normal" style:text-underline-style="none" fo:font-weight="normal" style:text-underline-mode="continuous" style:text-overline-mode="continuous" style:text-line-through-mode="continuous"/>
        </style:style>
        <style:style style:name="ce3" style:family="table-cell" style:parent-style-name="Gnumeric-default" style:data-style-name="N108"/>
        <style:style style:name="ce4" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N108">
            <style:table-cell-properties style:glyph-orientation-vertical="auto" style:cell-protect="protected" style:print-content="true" style:diagonal-bl-tr="none" style:diagonal-tl-br="none" fo:background-color="transparent" fo:wrap-option="no-wrap" fo:border="0.035cm solid #000000" style:rotation-angle="0" style:rotation-align="none" style:shrink-to-fit="false" gnm:background-colour="#ffffff" gnm:pattern-colour="#000000" gnm:pattern="0" style:vertical-align="bottom"/>
            <style:paragraph-properties fo:margin-left="0cm"/>
            <style:text-properties fo:color="#000000" style:text-line-through-style="none" style:font-name="Sans" fo:font-size="10pt" fo:font-style="normal" style:text-underline-style="none" fo:font-weight="normal" style:text-underline-mode="continuous" style:text-overline-mode="continuous" style:text-line-through-mode="continuous"/>
        </style:style>
        <style:style style:name="ce5" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N108"/>
        <style:style style:name="ce6" style:family="table-cell" style:parent-style-name="Default">
            <style:table-cell-properties style:glyph-orientation-vertical="auto" style:cell-protect="protected" style:print-content="true" style:diagonal-bl-tr="none" style:diagonal-tl-br="none" fo:background-color="transparent" fo:wrap-option="no-wrap" fo:border="0.035cm solid #000000" style:rotation-angle="0" style:rotation-align="none" style:shrink-to-fit="false" gnm:background-colour="#ffffff" gnm:pattern-colour="#000000" gnm:pattern="0" style:vertical-align="bottom"/>
            <style:paragraph-properties fo:margin-left="0cm"/>
            <style:text-properties fo:color="#000000" style:text-line-through-style="none" style:font-name="Sans" fo:font-size="10pt" fo:font-style="normal" style:text-underline-style="none" fo:font-weight="normal" style:text-underline-mode="continuous" style:text-overline-mode="continuous" style:text-line-through-mode="continuous"/>
        </style:style>
        <style:style style:name="ce7" style:family="table-cell" style:parent-style-name="Default">
            <style:table-cell-properties style:glyph-orientation-vertical="auto" style:cell-protect="protected" style:print-content="true" style:diagonal-bl-tr="none" style:diagonal-tl-br="none" style:text-align-source="value-type" style:repeat-content="false" fo:background-color="transparent" fo:wrap-option="no-wrap" fo:border="none" style:rotation-angle="0" style:rotation-align="none" style:shrink-to-fit="false" gnm:background-colour="#ffffff" gnm:pattern-colour="#000000" gnm:pattern="0" style:vertical-align="bottom"/>
            <style:paragraph-properties fo:margin-left="0cm"/>
            <style:text-properties fo:color="#000000" style:text-line-through-style="none" style:font-name="Sans" fo:font-size="10pt" fo:font-style="normal" style:text-underline-style="none" fo:font-weight="normal" style:text-underline-mode="continuous" style:text-overline-mode="continuous" style:text-line-through-mode="continuous"/>
        </style:style>
        <style:style style:name="ce8" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N107">
            <style:table-cell-properties style:glyph-orientation-vertical="auto" style:cell-protect="protected" style:print-content="true" style:diagonal-bl-tr="none" style:diagonal-tl-br="none" style:text-align-source="value-type" style:repeat-content="false" fo:background-color="transparent" fo:wrap-option="no-wrap" fo:border="none" style:rotation-angle="0" style:rotation-align="none" style:shrink-to-fit="false" gnm:background-colour="#ffffff" gnm:pattern-colour="#000000" gnm:pattern="0" style:vertical-align="bottom"/>
            <style:paragraph-properties fo:margin-left="0cm"/>
            <style:text-properties fo:color="#000000" style:text-line-through-style="none" style:font-name="Sans" fo:font-size="10pt" fo:font-style="normal" style:text-underline-style="none" fo:font-weight="normal" style:text-underline-mode="continuous" style:text-overline-mode="continuous" style:text-line-through-mode="continuous"/>
        </style:style>
        <style:style style:name="ce9" style:family="table-cell" style:parent-style-name="Default">
            <style:table-cell-properties style:glyph-orientation-vertical="auto" style:cell-protect="protected" style:print-content="true" style:diagonal-bl-tr="none" style:diagonal-tl-br="none" style:text-align-source="value-type" style:repeat-content="false" fo:background-color="transparent" fo:wrap-option="no-wrap" fo:border="none" style:rotation-angle="0" style:rotation-align="none" style:shrink-to-fit="false" gnm:background-colour="#ffffff" gnm:pattern-colour="#000000" gnm:pattern="0" style:vertical-align="bottom"/>
            <style:paragraph-properties fo:margin-left="0cm"/>
            <style:text-properties fo:color="#0000ff" style:text-line-through-style="none" style:font-name="Sans" fo:font-size="10pt" fo:font-style="normal" style:text-underline-style="solid" style:text-underline-width="auto" style:text-underline-color="font-color" fo:font-weight="normal" style:text-underline-mode="continuous" style:text-overline-mode="continuous" style:text-line-through-mode="continuous"/>
        </style:style>
        <style:style style:name="ce10" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N2">
            <style:table-cell-properties style:glyph-orientation-vertical="auto" style:cell-protect="protected" style:print-content="true" style:diagonal-bl-tr="none" style:diagonal-tl-br="none" style:text-align-source="value-type" style:repeat-content="false" fo:background-color="transparent" fo:wrap-option="no-wrap" fo:border="none" style:rotation-angle="0" style:rotation-align="none" style:shrink-to-fit="false" gnm:background-colour="#ffffff" gnm:pattern-colour="#000000" gnm:pattern="0" style:vertical-align="bottom"/>
            <style:paragraph-properties fo:margin-left="0cm"/>
            <style:text-properties fo:color="#000000" style:text-line-through-style="none" style:font-name="Sans" fo:font-size="10pt" fo:font-style="normal" style:text-underline-style="none" fo:font-weight="normal" style:text-underline-mode="continuous" style:text-overline-mode="continuous" style:text-line-through-mode="continuous"/>
        </style:style>
        <style:style style:name="ce11" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N112">
            <style:table-cell-properties style:glyph-orientation-vertical="auto" style:cell-protect="protected" style:print-content="true" style:diagonal-bl-tr="none" style:diagonal-tl-br="none" style:text-align-source="value-type" style:repeat-content="false" fo:background-color="transparent" fo:wrap-option="no-wrap" fo:border="none" style:rotation-angle="0" style:rotation-align="none" style:shrink-to-fit="false" gnm:background-colour="#ffffff" gnm:pattern-colour="#000000" gnm:pattern="0" style:vertical-align="bottom"/>
            <style:paragraph-properties fo:margin-left="0cm"/>
            <style:text-properties fo:color="#000000" style:text-line-through-style="none" style:font-name="Sans" fo:font-size="10pt" fo:font-style="normal" style:text-underline-style="none" fo:font-weight="normal" style:text-underline-mode="continuous" style:text-overline-mode="continuous" style:text-line-through-mode="continuous"/>
        </style:style>
        <style:style style:name="ce12" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N99">
            <style:table-cell-properties style:glyph-orientation-vertical="auto" style:cell-protect="protected" style:print-content="true" style:diagonal-bl-tr="none" style:diagonal-tl-br="none" style:text-align-source="value-type" style:repeat-content="false" fo:background-color="transparent" fo:wrap-option="no-wrap" fo:border="none" style:rotation-angle="0" style:rotation-align="none" style:shrink-to-fit="false" gnm:background-colour="#ffffff" gnm:pattern-colour="#000000" gnm:pattern="0" style:vertical-align="bottom"/>
            <style:paragraph-properties fo:margin-left="0cm"/>
            <style:text-properties fo:color="#000000" style:text-line-through-style="none" style:font-name="Sans" fo:font-size="10pt" fo:font-style="normal" style:text-underline-style="none" fo:font-weight="normal" style:text-underline-mode="continuous" style:text-overline-mode="continuous" style:text-line-through-mode="continuous"/>
        </style:style>
        <style:style style:name="ce13" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N115">
            <style:table-cell-properties style:glyph-orientation-vertical="auto" style:cell-protect="protected" style:print-content="true" style:diagonal-bl-tr="none" style:diagonal-tl-br="none" style:text-align-source="value-type" style:repeat-content="false" fo:background-color="transparent" fo:wrap-option="no-wrap" fo:border="none" style:rotation-angle="0" style:rotation-align="none" style:shrink-to-fit="false" gnm:background-colour="#ffffff" gnm:pattern-colour="#000000" gnm:pattern="0" style:vertical-align="bottom"/>
            <style:paragraph-properties fo:margin-left="0cm"/>
            <style:text-properties fo:color="#000000" style:text-line-through-style="none" style:font-name="Sans" fo:font-size="10pt" fo:font-style="normal" style:text-underline-style="none" fo:font-weight="normal" style:text-underline-mode="continuous" style:text-overline-mode="continuous" style:text-line-through-mode="continuous"/>
        </style:style>
    </office:automatic-styles>
    <office:body>
        <office:spreadsheet>
            <table:calculation-settings table:case-sensitive="false" table:automatic-find-labels="false" table:use-regular-expressions="false">
                <table:iteration table:status="enable"/>
            </table:calculation-settings>
            <table:table table:name="Shop" table:style-name="ta1">
                <office:forms form:automatic-focus="false" form:apply-design-mode="false"/>
                <table:table-column table:style-name="co1" table:default-cell-style-name="Default"/>
                <table:table-column table:style-name="co2" table:default-cell-style-name="Default"/>
                <table:table-column table:style-name="co3" table:default-cell-style-name="Default"/>
                <table:table-column table:style-name="co4" table:default-cell-style-name="ce5"/>
                <table:table-column table:style-name="co5" table:default-cell-style-name="Default"/>
                <table:table-column table:style-name="co6" table:default-cell-style-name="Default"/>
                <table:table-column table:style-name="co7" table:default-cell-style-name="Default"/>
                <table:table-column table:style-name="co6" table:default-cell-style-name="Default"/>
                <table:table-column table:style-name="co8" table:default-cell-style-name="Default"/>
                <table:table-column table:style-name="co9" table:default-cell-style-name="Default"/>
                <table:table-column table:style-name="co10" table:default-cell-style-name="Default"/>
                <table:table-column table:style-name="co11" table:default-cell-style-name="Default"/>
                <table:table-column table:style-name="co12" table:default-cell-style-name="Default"/>
                <table:table-column table:style-name="co13" table:default-cell-style-name="Default"/>
                <table:table-column table:style-name="co14" table:default-cell-style-name="Default"/>
                <table:table-column table:style-name="co4" table:default-cell-style-name="Default"/>
                <table:table-column table:style-name="co15" table:default-cell-style-name="Default"/>
                <table:table-column table:style-name="co16" table:default-cell-style-name="Default"/>
                <table:table-column table:style-name="co17" table:default-cell-style-name="Default"/>
                <table:table-column table:style-name="co18" table:number-columns-repeated="1005" table:default-cell-style-name="Default"/>
                <table:table-row table:style-name="ro1">
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>Artikelnummer</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>Name</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>keywords</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="ce3" office:value-type="string">
                        <text:p>EK_Preis</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>Preis</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>Details</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>addInfo</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>Einheit</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>Wirkstoff</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>verkuerztHaltbar</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>kuehlkette</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>Gebinde</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>Verbrauchsnachweis</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>Genehmigungspflichtig</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>Gefahrstoff</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>GefahrArbeitsbereich</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>Verwendungszweck</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>Verbrauch</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>showLagerbestand</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="1005"/>
                </table:table-row>
                <table:table-row table:style-name="ro1">
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="2"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>Schlüsselwörter</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="ce3" office:value-type="string">
                        <text:p>Einkaufspreis (Netto)</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>VK-Preis (Orientierung)</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>Hintergrundinformation</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>VPE</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>verkürzte Haltbarkeit</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>ja/nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>Stück,Rolle,Pack,Flasche,Sack,Eimer,Karton,Palette,Beutel,Kanister,Paar</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>ja/nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>ja/nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>GHS01-GHS09</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>Arbeitsbereich</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="2"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>ja/nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="1005"/>
                </table:table-row>
                <table:table-row table:style-name="ro1">
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>1005</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>Beatmungsfilter</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default"/>
                    <table:table-cell table:style-name="ce3" office:value-type="currency" office:currency="EUR" office:value="0.85">
                        <text:p>0,85 €</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="float" office:value="1.2">
                        <text:p>1,2</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="2"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="float" office:value="5">
                        <text:p>5</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="2"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>Stück</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="4"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>ja</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="1005"/>
                </table:table-row>
                <table:table-row table:style-name="ro1">
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>04-3-06</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>Absaugkatheter, CH06 grün</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default"/>
                    <table:table-cell table:style-name="ce4" office:value-type="currency" office:currency="EUR" office:value="0.13">
                        <text:p>0,13 €</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="ce6" office:value-type="float" office:value="0.13">
                        <text:p>0,13</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="2"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="float" office:value="1">
                        <text:p>1</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="3"/>
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>Stück</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="4"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>ja</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="1005"/>
                </table:table-row>
                <table:table-row table:style-name="ro1">
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>04-3-10</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>Absaugkatheter, CH10 schwarz</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default"/>
                    <table:table-cell table:style-name="ce4" office:value-type="currency" office:currency="EUR" office:value="0.13">
                        <text:p>0,13 €</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="ce6" office:value-type="float" office:value="0.13">
                        <text:p>0,13</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="2"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="float" office:value="1">
                        <text:p>1</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="3"/>
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>Stück</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="4"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>ja</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="1005"/>
                </table:table-row>
                <table:table-row table:style-name="ro1">
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>04-3-18</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>Absaugkatheter, CH18 rot</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default"/>
                    <table:table-cell table:style-name="ce4" office:value-type="currency" office:currency="EUR" office:value="0.13">
                        <text:p>0,13 €</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="ce6" office:value-type="float" office:value="0.13">
                        <text:p>0,13</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="2"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="float" office:value="1">
                        <text:p>1</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="3"/>
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>Stück</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="4"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>ja</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="1005"/>
                </table:table-row>
                <table:table-row table:style-name="ro1">
                    <table:table-cell table:style-name="ce2" office:value-type="string">
                        <text:p>06-38</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>Bakterienfilter</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default"/>
                    <table:table-cell table:style-name="ce4" office:value-type="currency" office:currency="EUR" office:value="1.25">
                        <text:p>1,25 €</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="ce6" office:value-type="float" office:value="1.25">
                        <text:p>1,25</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="2"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="float" office:value="1">
                        <text:p>1</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="3"/>
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>Stück</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="4"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>ja</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="1005"/>
                </table:table-row>
                <table:table-row table:style-name="ro1">
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>05-453</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>Blockerspritze für Larynxtubus, Erwachsen</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default"/>
                    <table:table-cell table:style-name="ce4" office:value-type="currency" office:currency="EUR" office:value="2.6">
                        <text:p>2,60 €</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="ce6" office:value-type="float" office:value="2.6">
                        <text:p>2,6</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="2"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="float" office:value="1">
                        <text:p>1</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="3"/>
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>Stück</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="4"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>ja</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="1005"/>
                </table:table-row>
                <table:table-row table:style-name="ro1">
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>04-402</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>Absaugschlauch mit Fingertip für Accuvac</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default"/>
                    <table:table-cell table:style-name="ce4" office:value-type="currency" office:currency="EUR" office:value="1.7">
                        <text:p>1,70 €</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="ce6" office:value-type="float" office:value="1.7">
                        <text:p>1,7</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="2"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="float" office:value="1">
                        <text:p>1</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="3"/>
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>Stück</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="4"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>ja</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="1005"/>
                </table:table-row>
                <table:table-row table:style-name="ro1">
                    <table:table-cell table:style-name="ce2" office:value-type="string">
                        <text:p>02-580</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>Einmalbeatmungsbeutel, Erwachsen</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default"/>
                    <table:table-cell table:style-name="ce4" office:value-type="currency" office:currency="EUR" office:value="8.9">
                        <text:p>8,90 €</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="ce6" office:value-type="float" office:value="8.9">
                        <text:p>8,9</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="2"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="float" office:value="1">
                        <text:p>1</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="3"/>
                    <table:table-cell table:style-name="ce1" office:value-type="string">
                        <text:p>Stück</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>nein</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="4"/>
                    <table:table-cell table:style-name="Gnumeric-default" office:value-type="string">
                        <text:p>ja</text:p>
                    </table:table-cell>
                    <table:table-cell table:style-name="Gnumeric-default" table:number-columns-repeated="1005"/>
                </table:table-row>
                <table:table-row table:style-name="ro1" table:number-rows-repeated="1048565">
                    <table:table-cell table:number-columns-repeated="1024"/>
                </table:table-row>
                <table:table-row table:style-name="ro1">
                    <table:table-cell table:number-columns-repeated="1024"/>
                </table:table-row>
                <table:named-expressions>
                    <table:named-expression table:name="Sheet_Title" table:base-cell-address="$Shop.$A$1" table:expression="&quot;Shop&quot;"/>
                    <table:named-expression table:name="Print_Area" table:base-cell-address="$Shop.$A$1" table:expression="#ref!"/>
                </table:named-expressions>
            </table:table>
            <table:table table:name="Feuille 1" table:style-name="ta2">
                <office:forms form:automatic-focus="false" form:apply-design-mode="false"/>
                <table:table-column table:style-name="co19" table:default-cell-style-name="ce7"/>
                <table:table-column table:style-name="co8" table:number-columns-repeated="1023" table:default-cell-style-name="ce7"/>
                <table:table-row table:style-name="ro2">
                    <table:table-cell office:value-type="string"><text:p>value</text:p></table:table-cell>
                    <table:table-cell table:number-columns-repeated="1023"/>
                </table:table-row>
                <table:table-row table:style-name="ro2">
                    <table:table-cell office:value-type="string"><text:p><EMAIL></text:p></table:table-cell>
                    <table:table-cell table:number-columns-repeated="1023"/>
                </table:table-row>
                <table:table-row table:style-name="ro2">
                    <table:table-cell office:value-type="string"><text:p>123 45</text:p></table:table-cell>
                    <table:table-cell table:number-columns-repeated="1023"/>
                </table:table-row>
                <table:table-row table:style-name="ro2">
                    <table:table-cell table:style-name="ce8" office:value-type="float" office:value="123.45"><text:p>123 </text:p></table:table-cell>
                    <table:table-cell table:number-columns-repeated="1023"/>
                </table:table-row>
                <table:table-row table:style-name="ro1">
                    <table:table-cell table:style-name="ce9" office:value-type="string"><text:p><text:a xlink:href="mailto:<EMAIL>"><EMAIL></text:a></text:p></table:table-cell>
                    <table:table-cell table:number-columns-repeated="1023"/>
                </table:table-row>
                <table:table-row table:style-name="ro1">
                    <table:table-cell table:style-name="ce9" office:value-type="string"><text:p><text:a xlink:href="https://example.org/">https://example.org</text:a></text:p></table:table-cell>
                    <table:table-cell table:number-columns-repeated="1023"/>
                </table:table-row>
                <table:table-row table:style-name="ro1">
                    <table:table-cell table:style-name="ce9" office:value-type="string"><text:p><text:a xlink:href="#">example.txt</text:a></text:p></table:table-cell>
                    <table:table-cell table:number-columns-repeated="1023"/>
                </table:table-row>
                <table:table-row table:style-name="ro1">
                    <table:table-cell table:style-name="ce9" office:value-type="string"><text:p><text:a xlink:href="#Feuille 1.A1:A4">&apos;Feuille 1&apos;!A1:A4</text:a></text:p></table:table-cell>
                    <table:table-cell table:number-columns-repeated="1023"/>
                </table:table-row>
                <table:table-row table:style-name="ro2">
                    <table:table-cell table:style-name="ce10" office:value-type="float" office:value="1.50008"><text:p>1,50</text:p></table:table-cell>
                    <table:table-cell table:number-columns-repeated="1023"/>
                </table:table-row>
                <table:table-row table:style-name="ro2">
                    <table:table-cell table:style-name="ce11" office:value-type="percentage" office:value="0.05"><text:p>05,0000%</text:p></table:table-cell>
                    <table:table-cell table:number-columns-repeated="1023"/>
                </table:table-row>
                <table:table-row table:style-name="ro1">
                    <table:table-cell table:style-name="ce12" office:value-type="boolean"><text:p>true</text:p></table:table-cell>
                    <table:table-cell table:number-columns-repeated="1023"/>
                </table:table-row>
                <table:table-row table:style-name="ro2">
                    <table:table-cell table:style-name="ce13" office:value-type="currency" office:currency="EUR" office:value="12"><text:p>012,000 €</text:p>
                    </table:table-cell>
                    <table:table-cell table:number-columns-repeated="1023"/>
                </table:table-row>
                <table:table-row table:style-name="ro2" table:number-rows-repeated="1048563">
                    <table:table-cell table:number-columns-repeated="1024"/>
                </table:table-row>
                <table:table-row table:style-name="ro2">
                    <table:table-cell table:number-columns-repeated="1024"/>
                </table:table-row>
                <table:named-expressions>
                    <table:named-expression table:name="Sheet_Title" table:base-cell-address="$&apos;Feuille 1&apos;.$A$1" table:expression="&quot;Feuille 1&quot;"/>
                    <table:named-expression table:name="Print_Area" table:base-cell-address="$&apos;Feuille 1&apos;.$A$1" table:expression="#ref!"/>
                </table:named-expressions>
            </table:table>
            <table:named-expressions/>
        </office:spreadsheet>
    </office:body>
</office:document-content>