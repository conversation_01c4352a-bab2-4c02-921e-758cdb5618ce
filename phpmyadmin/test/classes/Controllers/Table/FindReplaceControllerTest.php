<?php

declare(strict_types=1);

namespace PhpMyAdmin\Tests\Controllers\Table;

use PhpMyAdmin\Controllers\Table\FindReplaceController;
use PhpMyAdmin\DatabaseInterface;
use Php<PERSON>yAdmin\ResponseRenderer;
use PhpMyAdmin\Template;
use PhpMyAdmin\Tests\AbstractTestCase;
use PhpMyAdmin\Types;

/**
 * @covers \PhpMyAdmin\Controllers\Table\FindReplaceController
 */
class FindReplaceControllerTest extends AbstractTestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        parent::setLanguage();
        parent::setGlobalConfig();
        parent::setTheme();

        $GLOBALS['server'] = 1;
        $GLOBALS['db'] = 'db';
        $GLOBALS['table'] = 'table';
        $GLOBALS['PMA_PHP_SELF'] = 'index.php';
        $GLOBALS['cfg']['Server']['DisableIS'] = false;

        $dbi = $this->getMockBuilder(DatabaseInterface::class)
            ->disableOriginalConstructor()
            ->getMock();
        $dbi->types = new Types($dbi);

        $columns = [
            [
                'Field' => 'Field1',
                'Type' => 'Type1',
                'Null' => 'Null1',
                'Collation' => 'Collation1',
            ],
            [
                'Field' => 'Field2',
                'Type' => 'Type2',
                'Null' => 'Null2',
                'Collation' => 'Collation2',
            ],
        ];
        $dbi->expects($this->any())->method('getColumns')
            ->will($this->returnValue($columns));

        $show_create_table = "CREATE TABLE `table` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `dbase` varchar(255) COLLATE utf8_bin NOT NULL DEFAULT '',
        `user` varchar(255) COLLATE utf8_bin NOT NULL DEFAULT '',
        `label` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '',
        `query` text COLLATE utf8_bin NOT NULL,
        PRIMARY KEY (`id`),
        KEY `foreign_field` (`foreign_db`,`foreign_table`)
        ) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_bin "
            . "COMMENT='table'";

        $dbi->expects($this->any())->method('fetchValue')
            ->will($this->returnValue($show_create_table));
        $dbi->expects($this->any())->method('escapeString')
            ->will($this->returnArgument(0));

        $GLOBALS['dbi'] = $dbi;
    }

    public function testReplace(): void
    {
        $tableSearch = new FindReplaceController(
            ResponseRenderer::getInstance(),
            new Template(),
            $GLOBALS['db'],
            $GLOBALS['table'],
            $GLOBALS['dbi']
        );
        $columnIndex = 0;
        $find = 'Field';
        $replaceWith = 'Column';
        $useRegex = false;
        $charSet = 'UTF-8';
        $tableSearch->replace($columnIndex, $find, $replaceWith, $useRegex, $charSet);

        $sql_query = $GLOBALS['sql_query'];
        $result = 'UPDATE `table` SET `Field1` = '
            . "REPLACE(`Field1`, 'Field', 'Column') "
            . "WHERE `Field1` LIKE '%Field%' COLLATE UTF-8_bin";
        $this->assertEquals($result, $sql_query);
    }

    public function testReplaceWithRegex(): void
    {
        $tableSearch = new FindReplaceController(
            ResponseRenderer::getInstance(),
            new Template(),
            $GLOBALS['db'],
            $GLOBALS['table'],
            $GLOBALS['dbi']
        );

        $columnIndex = 0;
        $find = 'Field';
        $replaceWith = 'Column';
        $useRegex = true;
        $charSet = 'UTF-8';

        $tableSearch->replace($columnIndex, $find, $replaceWith, $useRegex, $charSet);

        $sql_query = $GLOBALS['sql_query'];

        $result = 'UPDATE `table` SET `Field1` = `Field1`'
            . " WHERE `Field1` RLIKE 'Field' COLLATE UTF-8_bin";

        $this->assertEquals($result, $sql_query);
    }
}
