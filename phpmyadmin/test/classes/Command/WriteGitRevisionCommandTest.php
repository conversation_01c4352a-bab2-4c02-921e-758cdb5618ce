<?php

declare(strict_types=1);

namespace PhpMyAdmin\Tests\Command;

use Php<PERSON>yAdmin\Command\WriteGitRevisionCommand;
use PhpMyAdmin\Tests\AbstractTestCase;
use Symfony\Component\Console\Command\Command;

use function class_exists;
use function sprintf;

/**
 * @covers \PhpMyAdmin\Command\WriteGitRevisionCommand
 */
class WriteGitRevisionCommandTest extends AbstractTestCase
{
    /** @var WriteGitRevisionCommand */
    private $command;

    public function testGetGeneratedClassValidVersion(): void
    {
        if (! class_exists(Command::class)) {
            $this->markTestSkipped('The Symfony Console is missing');
        }

        $this->command = $this->getMockBuilder(WriteGitRevisionCommand::class)
            ->onlyMethods(['gitCli'])
            ->getMock();

        $this->command->expects($this->exactly(3))
            ->method('gitCli')
            ->withConsecutive(
                ['describe --always'],
                ['log -1 --format="%H"'],
                ['symbolic-ref -q HEAD']
            )
            ->willReturnOnConsecutiveCalls(
                'RELEASE_5_1_0-638-g1c018e2a6c',
                '1c018e2a6c6d518c4a2dde059e49f33af67c4636',
                'refs/heads/cli-rev-info'
            );

        $output = $this->callFunction(
            $this->command,
            WriteGitRevisionCommand::class,
            'getRevisionInfo',
            [
                'https://github.com/phpmyadmin/phpmyadmin/commit/%s',
                'https://github.com/phpmyadmin/phpmyadmin/tree/%s',
            ]
        );
        $template = <<<'PHP'
<?php

declare(strict_types=1);

/**
 * This file is generated by scripts/console.
 *
 * @see \PhpMyAdmin\Command\WriteGitRevisionCommand
 */
return [
    'revision' => '%s',
    'revisionUrl' => '%s',
    'branch' => '%s',
    'branchUrl' => '%s',
];

PHP;
        $this->assertSame(
            sprintf(
                $template,
                'RELEASE_5_1_0-638-g1c018e2a6c',
                'https://github.com/phpmyadmin/phpmyadmin/commit/1c018e2a6c6d518c4a2dde059e49f33af67c4636',
                'cli-rev-info',
                'https://github.com/phpmyadmin/phpmyadmin/tree/cli-rev-info'
            ),
            $output
        );
    }
}
