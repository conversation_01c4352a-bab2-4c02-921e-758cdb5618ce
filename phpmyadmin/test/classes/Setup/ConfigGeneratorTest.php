<?php

declare(strict_types=1);

namespace Php<PERSON>yAdmin\Tests\Setup;

use PhpMyAdmin\Config\ConfigFile;
use PhpMyAdmin\Setup\ConfigGenerator;
use PhpMyAdmin\Tests\AbstractTestCase;
use PhpMyAdmin\Version;
use ReflectionClass;

use function explode;
use function hex2bin;
use function mb_strlen;
use function str_repeat;

use const SODIUM_CRYPTO_SECRETBOX_KEYBYTES;

/**
 * @covers \PhpMyAdmin\Setup\ConfigGenerator
 */
class ConfigGeneratorTest extends AbstractTestCase
{
    /**
     * Test for ConfigGenerator::getConfigFile
     *
     * @group medium
     */
    public function testGetConfigFile(): void
    {
        unset($_SESSION['eol']);

        parent::setGlobalConfig();

        $GLOBALS['server'] = 0;
        $cf = new ConfigFile();
        $_SESSION['ConfigFile0'] = [
            'a',
            'b',
            'c',
        ];
        $_SESSION['ConfigFile0']['Servers'] = [
            [
                1,
                2,
                3,
            ],
        ];

        $cf->setPersistKeys(['1/', 2]);

        $result = ConfigGenerator::getConfigFile($cf);

        $this->assertStringContainsString(
            "<?php\n" .
            "/**\n" .
            " * Generated configuration file\n" .
            ' * Generated by: phpMyAdmin ' . Version::VERSION . " setup script\n",
            $result
        );

        $this->assertStringContainsString(
            "/* Servers configuration */\n" .
            '$i = 0;' . "\n\n" .
            "/* Server: localhost [0] */\n" .
            '$i++;' . "\n" .
            '$cfg[\'Servers\'][$i][\'0\'] = 1;' . "\n" .
            '$cfg[\'Servers\'][$i][\'1\'] = 2;' . "\n" .
            '$cfg[\'Servers\'][$i][\'2\'] = 3;' . "\n\n" .
            "/* End of servers configuration */\n\n",
            $result
        );
    }

    /**
     * Test for ConfigGenerator::getVarExport
     */
    public function testGetVarExport(): void
    {
        $reflection = new ReflectionClass(ConfigGenerator::class);
        $method = $reflection->getMethod('getVarExport');
        $method->setAccessible(true);

        $this->assertEquals(
            '$cfg[\'var_name\'] = 1;' . "\n",
            $method->invoke(null, 'var_name', 1, "\n")
        );

        $this->assertEquals(
            '$cfg[\'var_name\'] = array (' .
            "\n);\n",
            $method->invoke(null, 'var_name', [], "\n")
        );

        $this->assertEquals(
            '$cfg[\'var_name\'] = [1, 2, 3];' . "\n",
            $method->invoke(
                null,
                'var_name',
                [
                    1,
                    2,
                    3,
                ],
                "\n"
            )
        );

        $this->assertEquals(
            '$cfg[\'var_name\'][\'1a\'] = \'foo\';' . "\n" .
            '$cfg[\'var_name\'][\'b\'] = \'bar\';' . "\n",
            $method->invoke(
                null,
                'var_name',
                [
                    '1a' => 'foo',
                    'b' => 'bar',
                ],
                "\n"
            )
        );
    }

    public function testGetVarExportForBlowfishSecret(): void
    {
        $reflection = new ReflectionClass(ConfigGenerator::class);
        $method = $reflection->getMethod('getVarExport');
        $method->setAccessible(true);

        $this->assertEquals(
            '$cfg[\'blowfish_secret\'] = \sodium_hex2bin(\''
            . '6161616161616161616161616161616161616161616161616161616161616161\');' . "\n",
            $method->invoke(null, 'blowfish_secret', str_repeat('a', SODIUM_CRYPTO_SECRETBOX_KEYBYTES), "\n")
        );

        /** @var string $actual */
        $actual = $method->invoke(null, 'blowfish_secret', 'invalid secret', "\n");
        $this->assertStringStartsWith('$cfg[\'blowfish_secret\'] = \sodium_hex2bin(\'', $actual);
        $this->assertStringEndsWith('\');' . "\n", $actual);
        $pieces = explode('\'', $actual);
        $this->assertCount(5, $pieces);
        $binaryString = hex2bin($pieces[3]);
        $this->assertIsString($binaryString);
        $this->assertSame(SODIUM_CRYPTO_SECRETBOX_KEYBYTES, mb_strlen($binaryString, '8bit'));
    }

    /**
     * Test for ConfigGenerator::isZeroBasedArray
     */
    public function testIsZeroBasedArray(): void
    {
        $reflection = new ReflectionClass(ConfigGenerator::class);
        $method = $reflection->getMethod('isZeroBasedArray');
        $method->setAccessible(true);

        $this->assertFalse(
            $method->invoke(
                null,
                [
                    'a' => 1,
                    'b' => 2,
                ]
            )
        );

        $this->assertFalse(
            $method->invoke(
                null,
                [
                    0 => 1,
                    1 => 2,
                    3 => 3,
                ]
            )
        );

        $this->assertTrue(
            $method->invoke(
                null,
                []
            )
        );

        $this->assertTrue(
            $method->invoke(
                null,
                [
                    1,
                    2,
                    3,
                ]
            )
        );
    }

    /**
     * Test for ConfigGenerator::exportZeroBasedArray
     */
    public function testExportZeroBasedArray(): void
    {
        $reflection = new ReflectionClass(ConfigGenerator::class);
        $method = $reflection->getMethod('exportZeroBasedArray');
        $method->setAccessible(true);

        $arr = [
            1,
            2,
            3,
            4,
        ];

        $result = $method->invoke(null, $arr, "\n");

        $this->assertEquals('[1, 2, 3, 4]', $result);

        $arr = [
            1,
            2,
            3,
            4,
            7,
            'foo',
        ];

        $result = $method->invoke(null, $arr, "\n");

        $this->assertEquals(
            '[' . "\n" .
            '    1,' . "\n" .
            '    2,' . "\n" .
            '    3,' . "\n" .
            '    4,' . "\n" .
            '    7,' . "\n" .
            '    \'foo\']',
            $result
        );
    }
}
