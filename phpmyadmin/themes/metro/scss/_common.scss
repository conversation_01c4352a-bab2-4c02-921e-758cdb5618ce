#li_select_fontsize {
  display: none;
}

/* Override style formats by html tags */

font[color="red"],
span[style="color: #FF0000"] {
  color: $browse-warning-color !important;
}

strong {
  font-weight: normal;
}

/* login */

body#loginform {
  margin: 0;
  background-color: #666;

  .card-header,
  form#login_form label {
    display: none !important;
  }

  #page_content {
    background-color: $navi-background;
    margin: 0 !important;
    padding: 20px;
    margin-top: 10% !important;
    min-height: 240px;
  }

  div.container {
    color: $body-bg;
    text-align: left;
    width: 48em;
    margin-left: auto;
    margin-right: auto;

    &::before {
      color: $body-bg;
      background: url('../img/user.svg');/* The user avatar icon */
      content: "";
      float: left;
      margin-right: 20px;
      margin-bottom: 10px;
      background-color: $th-color;
      background-repeat: no-repeat;
      background-size: cover;
      overflow: hidden;
      height: 240px;
      width: 240px;
      line-height: 1;
      text-align: center;
    }
  }

  h1 {
    display: inline-block;
    text-align: left;
    color: $body-bg;
    font-size: 2.5em;
    padding-top: 0;
    margin-right: -50%;
    line-height: 2;
  }

  a.logo,
  .pma-fieldset legend {
    display: none;
  }

  .item {
    margin-bottom: 10px;
  }

  input {
    &.textfield {
      width: 100%;
      border: 1px solid #fff;
      background: $navi-color;
      color: $th-color;
      box-sizing: border-box;
      margin: 0;

      &:hover,
      &:focus {
        background-color: #fff;
        color: #333;
        box-sizing: border-box;
        margin: 0;
      }
    }

    &[type="submit"] {
      background-color: $th-color;
      border: none;
      padding: 7px;
      margin: 0;
    }
  }

  select {
    margin: 0 !important;
    border: 1px solid #ccc;
    background: white;
    color: #333;
    padding-left: 5px;
    padding-right: 5px;
    font-family: $font-family-base;
    min-width: 100%;

    &:hover {
      border: 1px solid $navi-color;
    }
  }

  br {
    display: none;
  }

  .card-body {
    padding-top: 0;
    padding-left: 0;
    padding-right: 0;

    .col-sm-8 {
      // Force 100% because the form label is hidden
      width: 100%;
    }

    a {
      color: white;
    }
  }

  .card,
  .card-footer,
  .pma-fieldset {
    border: none;
    color: $navi-color;
    padding: 0;
    margin-top: 0;
    margin-bottom: 10px;
    background: none;

    &:first-child {
      border-bottom: none;
      margin: 0;
    }

    &.tblFooters {
      border: none;
      margin: 0;
      clear: none;
    }
  }
}

.turnOffSelect {
  user-select: none;
}

#page_content {
  margin: 20px !important;
}

h2 {
  // Hiding icons in the page titles
  img {
    display: none;
  }

  a img {
    display: inline;
  }
}

.data {
  margin: 10px 0;
}

button.mult_submit,
.checkall_box + label {
  text-decoration: none;
  color: #235a81;
  cursor: pointer;
  outline: none;
}

button.mult_submit {
  &:hover,
  &:focus {
    text-decoration: underline;
    color: #235a81;
  }
}

.checkall_box + label:hover {
  text-decoration: underline;
  color: #235a81;
}

dfn:hover {
  cursor: help;
}

.data th {
  border-bottom: 1px solid $border-color;
}

.data th a:hover {
  color: #999 !important;
}

.column_heading,
.column_action {
  border: 1px solid $border-color;
  background-color: #f6f6f6;
}

a img {
  border: 0;
}

hr {
  color: $border-color;
  background-color: $border-color;
  border: 0;
  height: 1px;
}

form {
  padding: 0;
  margin: 0;
  display: inline;
}

input {
  &[type="text"],
  &[type="password"],
  &[type="number"] {
    border: 1px solid $browse-gray-color;
    color: $th-color;
    padding: 5px;
    font-family: $font-family-base;
    background-color: $body-bg;

    &:focus {
      border: 1px solid $navi-hover-background;
      color: $main-color;
    }
  }

  &:not(.form-control)[type="text"],
  &:not(.form-control)[type="password"],
  &:not(.form-control)[type="number"] {
    margin: 6px;
  }
}

.sqlbutton {
  margin-top: 1em;
  margin-left: 0 !important;
  margin-right: 14px !important;
}

button:not(.accordion-button) {
  margin-left: 14px;
  padding: 4px;
  color: $button-color;
  text-decoration: none;
  background-color: $button-background;

  &:hover {
    background-color: $button-hover;
    color: white;
    text-decoration: none;
  }
}

button.jsAccountLocking {
  background: none;
  color: var(--bs-link-color);
  margin-left: 0;

  &:hover {
    background: none;
    color: var(--bs-link-color);
    text-decoration: underline;
  }
}

button.jsPrintButton {
  background: none;
  color: #444;
  margin-left: 0;
  padding: var(--bs-btn-padding-y) var(--bs-btn-padding-x);

  &:hover {
    background: none;
    color: #444;
    text-decoration: none;
    border: 1px solid #444;
  }
}

textarea {
  overflow: visible;
  border: 1px solid $browse-gray-color;
  color: $th-color;
  background-color: $body-bg;
}

.pma-fieldset {
  margin-top: 20px;
  border: 1px solid $border-color;
  padding: 20px;
  background-color: $th-background;

  .pma-fieldset {
    margin: 20px;
    margin-bottom: 0;
    background-color: $body-bg;
    border: none;
  }
}

legend {
  float: none;
  padding: 0 5px;
  width: initial;
  font-size: 1em;
}

/* buttons in some browsers (eg. Konqueror) are block elements,
   this breaks design */
button {
  display: inline;
}

img,
button {
  vertical-align: middle;
}

select {
  border: 1px solid $browse-gray-color;
  color: $th-color;
  padding: 4px;
  font-family: $font-family-base;
  background-color: $body-bg;
  max-width: 17em;

  &:not(.form-select) {
    margin: 5px;
  }

  &:focus {
    border: 1px solid $navi-hover-background;
    color: $main-color;
  }
}

/* classes */
.clearfloat {
  clear: both;
}

.paddingtop {
  padding-top: 1em;
}

.separator {
  color: #fff;
}

.result_query {
  background: $th-background;
  margin-bottom: 20px;
}

div.tools {
  padding: 10px;
  text-align: right;

  span {
    float: right;
    margin: 6px 2px;
  }

  a {
    color: $blue-header !important;
  }
}

.pma-fieldset.tblFooters {
  margin-top: -1px;
  border-top: 0;
  text-align: right;
  float: none;
  clear: both;
}

div.null_div {
  height: 20px;
  text-align: center;
  font-style: normal;
  min-width: 50px;
}

.pma-fieldset {
  .formelement {
    float: left;
    margin-right: 0.5em;
  }

  /* revert for Gecko */
  div[class="formelement"] {
    white-space: normal;
  }
}

button.mult_submit {
  border: none;
  background-color: transparent;
  color: $browse-pointer-color;
  margin: 0;
}

.structure_actions_dropdown {
  background-color: $browse-marker-background;
  color: $main-color;

  .icon {
    vertical-align: middle !important;
  }
}

/* marks table rows/cells if the db field is in a where condition */

.condition {
  border-color: $browse-warning-color !important;
}

th.condition {
  border: 1px solid $browse-warning-color;
  background: $browse-warning-color;
  color: $body-bg !important;

  a {
    border: 1px solid $browse-warning-color;
    background: $browse-warning-color;
    color: $body-bg !important;
  }
}

td {
  &.condition {
    border: 1px solid;
  }

  // cells with the value NULL
  &.null {
    font-style: italic;
    color: #7d7d7d;
  }
}

table {
  .valueHeader,
  .value {
    text-align: right;
    white-space: normal;
  }
}

.value {
  font-family: $font-family-monospace;
}

img.lightbulb {
  cursor: pointer;
}

.pdflayout {
  overflow: hidden;
  clip: inherit;
  background-color: #fff;
  display: none;
  border: 1px solid #000;
  position: relative;
}

.pdflayout_table {
  background: #d3dce3;
  color: #000;
  overflow: hidden;
  clip: inherit;
  z-index: 2;
  display: inline;
  visibility: inherit;
  cursor: move;
  position: absolute;
  font-size: 80%;
  border: 1px dashed #000;
}

/* Doc links in SQL */
.cm-sql-doc {
  text-decoration: none;
  border-bottom: 1px dotted #999;
  color: inherit !important;
}

.selectallarrow {
  margin-right: 0.3em;
  margin-left: 0.6em;
}

.with-selected {
  margin-left: 2em;
}

/* message boxes: error, confirmation */
#pma_errors,
#pma_demo,
#pma_footer {
  position: relative;
  padding: 20px;
}

#pma_errors #pma_errors {
  padding: 0;
}

.confirmation {
  color: $button-color;
  background-color: $browse-warning-color;
}

.pma-fieldset.confirmation legend {
  background-color: $browse-warning-color;
}

.error {
  border: 1px solid $browse-warning-color !important;
  padding: 5px;
  color: $button-color;
  background-color: $browse-warning-color;
}

/* end messageboxes */

.column_name {
  font-size: 80%;
  margin: 5px 2px;
}

.new_central_col {
  width: 100%;
}

.tblcomment {
  font-size: 70%;
  font-weight: normal;
  color: #009;
}

.tblHeaders {
  font-family: $font-family-bold;
  color: $th-color;
  background: $th-background;
  font-weight: normal;
}

div.tools,
.tblFooters {
  font-weight: normal;
  color: $th-color;
  background: $th-background;
}

.tblHeaders,
div.tools,
.tblFooters {
  a {
    &:link,
    &:active,
    &:visited {
      color: #00f;
    }

    &:hover {
      color: #f00;
    }
  }
}

/* disabled text */
.disabled {
  color: #666;

  a {
    &:link,
    &:active,
    &:visited {
      color: #666;
    }

    &:hover {
      color: #666;
      text-decoration: none;
    }
  }
}

tr.disabled td,
td.disabled {
  background-color: #f3f3f3;
  color: #aaa;
}

.pre_wrap {
  white-space: pre-wrap;
}

.pre_wrap br {
  display: none;
}

li.last.database {
  // Avoid having the last item of the tree hidden behind the scroll bar
  margin-bottom: 15px !important;
}

/* zoom search */
div#dataDisplay {
  input,
  select {
    margin: 0;
    margin-right: 0.5em;
  }

  th {
    line-height: 2em;
  }
}

img.calendar {
  border: none;
}

form.clock {
  text-align: center;
}

/* end Calendar */

/* table stats */
div#tablestatistics table {
  float: left;
  margin-bottom: 0.5em;
  margin-right: 1.5em;
  margin-top: 0.5em;
  min-width: 16em;
}

/* END table stats */

/* Heading */
#topmenucontainer {
  width: 100%;
}

#page_nav_icons {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 99;
  padding: $breadcrumb-navbar-padding-y $breadcrumb-padding-x;
}

#page_settings_icon {
  cursor: pointer;
  display: none;
}

#page_settings_modal,
#pma_navigation_settings {
  display: none;
}

#span_table_comment {
  font-weight: normal;
  font-style: italic;
  white-space: nowrap;
  margin-left: 10px;
}

#textSQLDUMP {
  width: 95%;
  height: 95%;
  font-family: $font-family-monospace;
  font-size: 110%;
}

#TooltipContainer {
  position: absolute;
  z-index: 99;
  width: 20em;
  height: auto;
  overflow: visible;
  visibility: hidden;
  background-color: #ffc;
  color: #060;
  border: 0.1em solid #000;
  padding: 0.5em;
}

/* user privileges */

#fieldset_add_user_login {
  div.item {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ddd;
    padding-bottom: 0.3em;
    margin-bottom: 0.3em;
  }

  label {
    float: left;
    display: block;
    width: 10em;
    max-width: 100%;
    text-align: right;
    padding-right: 0.5em;
    margin-bottom: 0;
  }

  span.options {
    float: left;
    display: block;
    width: 12em;
    max-width: 100%;
    padding-right: 0.5em;

    #select_pred_username,
    #select_pred_hostname,
    #select_pred_password {
      width: 100%;
      max-width: 100%;
    }
  }

  input {
    width: 12em;
    clear: right;
    max-width: 100%;
  }

  span.options input {
    width: auto;

    &[type="button"] {
      margin: 4px;
    }
  }
}

#fieldset_user_priv div.item {
  float: left;
  width: 9em;
  max-width: 100%;

  div.item {
    float: none;
  }

  label {
    white-space: nowrap;
  }

  select {
    width: 100%;
  }
}

#fieldset_user_global_rights .pma-fieldset,
#fieldset_user_group_rights .pma-fieldset {
  float: left;
}

/* END user privileges */

/* serverstatus */

.linkElem:hover {
  text-decoration: underline;
  color: #235a81;
  cursor: pointer;
}

h3#serverstatusqueries span {
  font-size: 60%;
  display: inline;
}

div#serverStatusTabs {
  margin-top: 1em;
}

caption a.top {
  float: right;
}

div#serverstatusquerieschart {
  float: left;
  width: 500px;
  height: 350px;
  padding-left: 30px;
}

div {
  &#serverstatus table {
    tbody td.descr a,
    .tblFooters a {
      white-space: nowrap;
    }
  }

  &.liveChart {
    clear: both;
    min-width: 500px;
    height: 400px;
    padding-bottom: 80px;
  }
}

div#chartVariableSettings {
  margin-left: 10px;
}

table#chartGrid {
  td {
    padding: 3px;
    margin: 0;
  }

  div.monitorChart {
    background: $th-background;
    min-width: 1px;
  }
}

div.tabLinks {
  margin-left: 0.3em;
  float: left;
  padding: 5px 0;

  a,
  label {
    margin-right: 7px;
  }

  .icon {
    margin: -0.2em 0.3em 0 0;
  }
}

.popupContent {
  display: none;
  position: absolute;
  border: 1px solid #ccc;
  margin: 0;
  padding: 3px;
  background-color: #fff;
  z-index: 2;
}

div {
  &#logTable {
    padding-top: 10px;
    clear: both;

    table {
      width: 100%;
    }
  }

  &#queryAnalyzerDialog {
    min-width: 700px;

    div {
      &.CodeMirror-scroll {
        height: auto;
      }

      &#queryProfiling {
        height: 300px;
      }
    }

    td.explain {
      width: 250px;
    }

    table.queryNums {
      display: none;
      border: 0;
      text-align: left;
    }
  }
}

.smallIndent {
  padding-left: 7px;
}

/* end serverstatus */

/* profiling */

div#profilingchart {
  width: 850px;
  height: 370px;
  float: left;
}

/* END profiling */

/* table charting */

#resizer {
  border: 1px solid silver;
}

/* make room for the resize handle */
#inner-resizer {
  padding: 10px;
}

/* querybox */

#togglequerybox {
  margin: 0 10px;
}

#serverstatus h3 {
  margin: 15px 0;
  font-weight: normal;
  color: #999;
  font-size: 1.7em;
}

textarea {
  &#sqlquery {
    width: 100%;
    border: 1px solid #aaa;
    padding: 5px;
    font-family: inherit;
  }

  &#sql_query_edit {
    height: 7em;
    width: 95%;
    display: block;
  }
}

/* end querybox */

/* main page */

#mysqlmaininformation,
#pmamaininformation {
  float: left;
  width: 49%;
}

#maincontainer ul {
  list-style-type: square;
  vertical-align: middle;
  color: $th-color;
  margin-left: 20px;

  li {
    line-height: 1.5;
  }
}

#full_name_layer {
  position: absolute;
  padding: 2px;
  margin-top: -3px;
  z-index: 801;
  border: solid 1px #888;
  background: #fff;
}

/* END main page */

/* iconic view for ul items */

li {
  br {
    display: none;
  }

  &#li_mysql_client_version {
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/* END iconic view for ul items */

#body_browse_foreigners {
  background: $navi-background;
  margin: 0.5em 0.5em 0 0.5em;
}

#bodyquerywindow {
  background: $navi-background;
}

#bodythemes {
  width: 500px;
  margin: auto;
  text-align: center;

  img {
    border: 0.1em solid #000;
  }

  a:hover img {
    border: 0.1em solid red;
  }
}

#selflink {
  clear: both;
  display: block;
  margin-top: 20px;
  margin-bottom: 20px;
  margin-left: 20px;
  margin-right: 20px;
  border-top: 1px solid silver;
  text-align: right;
}

.operations_half_width {
  width: 100%;
  float: left;
  margin-bottom: 10px;
}

.operations_full_width {
  width: 100%;
  clear: both;
}

#qbe_div_table_list,
#qbe_div_sql_query {
  float: left;
}

kbd {
  color: $main-color;
  background-color: transparent;
  box-shadow: none;
}

code {
  font-size: 1em;
  color: $main-color;

  &.php {
    display: block;
    padding-left: 0.3em;
    margin-top: 0;
    margin-bottom: 0;
    max-height: 10em;
    overflow: auto;
    direction: ltr;
  }
}

.sqlOuter code.sql,
div.sqlvalidate,
#inline_editor_outer {
  display: block;
  padding: 1em;
  margin: 1em;
  overflow: auto;
  background-color: $body-bg;
  border: 1px solid $border-color;
  direction: ltr;
}

textarea#partitiondefinition {
  height: 3em;
}

/* for elements that should be revealed only via js */
.hide {
  display: none;
}

#list_server {
  list-style-image: none;
  padding: 0;
}

/**
  *  Progress bar styles
  */

div {
  &.upload_progress {
    width: 400px;
    margin: 3em auto;
    text-align: center;
  }

  &.upload_progress_bar_outer {
    border: 1px solid #000;
    width: 202px;
    position: relative;
    margin: 0 auto 1em;
    color: $main-color;
  }

  &.upload_progress_bar_inner {
    background-color: $navi-background;
    width: 0;
    height: 12px;
    margin: 1px;
    overflow: hidden;
    color: $browse-marker-color;
    position: relative;
  }

  &.upload_progress_bar_outer div.percentage {
    position: absolute;
    top: 0;
    left: 0;
    width: 202px;
  }

  &.upload_progress_bar_inner div.percentage {
    top: -1px;
    left: -1px;
  }

  &#statustext {
    margin-top: 0.5em;
  }
}

table {
  &#serverconnection_src_remote,
  &#serverconnection_trg_remote,
  &#serverconnection_src_local,
  &#serverconnection_trg_local {
    float: left;
  }
}

/**
  *  Validation error message styles
  */

input {
  &[type="text"].invalid_value,
  &[type="password"].invalid_value,
  &[type="number"].invalid_value,
  &[type="date"].invalid_value .invalid_value {
    background: #fcc;
  }
}

/**
  *  Ajax notification styling
  */

/* additional styles */
.ajax_notification {
  top: 0;
  position: fixed;
  z-index: 1100;
  text-align: center;
  display: inline;
  left: 0;
  right: 0;
  background-image: url("../img/ajax_clock_small.gif");
  background-repeat: no-repeat;
  background-position: 46%;
  margin: 0;
  background-color: $navi-color;
  color: $main-color;
  padding: 10px !important;
  border: none;
}

.dismissable {
  margin-left: -10px;
  margin-top: -10px;
}

#loading_parent {
  /** Need this parent to properly center the notification division */
  position: relative;
  width: 100%;
}

#popup_background {
  display: none;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: #000;
  z-index: 1000;
  overflow: hidden;
}

#structure-action-links a {
  margin-right: 1em;
}

/**
 * Indexes
 */

#index_frm {
  .index_info {
    input,
    select {
      width: 14em;
      box-sizing: border-box;
    }

    div {
      padding: 0.2em 0;
    }

    .label {
      float: left;
      min-width: 12em;
    }
  }

  .slider {
    width: 10em;
    margin: 0.6em;
    float: left;
  }

  .add_fields {
    float: left;

    input {
      margin-left: 1em;
    }
  }

  input {
    margin: 0;
  }

  td {
    vertical-align: middle;
  }
}

table#index_columns {
  width: 100%;

  select {
    width: 85%;
    float: right;
  }
}

#move_columns_dialog {
  div {
    padding: 1em;
  }

  ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  li {
    background: $th-background;
    border: 1px solid #aaa;
    color: $th-color;
    font-weight: bold;
    margin: 0.4em;
    padding: 0.2em;
  }
}

/* config forms */

.config-form {
  .pma-fieldset {
    margin-top: 0;
    padding: 0;
    clear: both;
    background: none;
  }

  legend {
    display: none;
  }

  .pma-fieldset {
    p {
      margin: 0;
      padding: 10px;
      background: #fff;
      font-family: $font-family-light;
      font-size: 16px;
    }

    /* form error list */
    .errors {
      margin: 0 -2px 1em;
      padding: 0.5em 1.5em;
      background: #fbead9;
      border-color: #c83838;
      border-style: solid;
      border-width: 1px 0;
      list-style: none;
      font-family: sans-serif;
      font-size: small;
    }

    /* field error list */
    .inline_errors {
      margin: 0.3em 0.3em 0.3em;
      margin-left: 0;
      padding: 0;
      list-style: none;
      color: #9a0000;
      font-size: small;
    }

    table {
      background-color: #fff;
    }

    label {
      font-weight: normal;
    }

    textarea {
      margin: 5px;
      padding: 5px;
    }
  }
}

.insertRowTable textarea {
  margin: 5px;
  padding: 5px;
}

.config-form .card {
  margin-top: 0;
}

.config-form fieldset {
  th {
    padding: 10px;
    padding-left: 0.5em;
    text-align: left;
    vertical-align: top;
  }

  .doc {
    margin-left: 1em;
  }

  .disabled-notice {
    margin-left: 1em;
    font-size: 80%;
    text-transform: uppercase;
    color: #e00;
    cursor: help;
  }

  td {
    padding-top: 0.3em;
    padding-bottom: 0.3em;
    vertical-align: top;
    border-bottom: 1px $navi-color solid;
    border-right: none;
  }

  th {
    border-bottom: 1px $navi-color solid;
    border-right: none;

    small {
      display: block;
      font-weight: normal;
      font-family: sans-serif;
      font-size: x-small;
      color: #444;
    }
  }
}

fieldset {
  .group-header {
    th {
      background: $bg-two;
    }

    + tr th {
      padding-top: 0.6em;
    }
  }

  .group-field-1 th,
  .group-header-2 th {
    padding-left: 1.5em;
  }

  .group-field-2 th,
  .group-header-3 th {
    padding-left: 3em;
  }

  .group-field-3 th {
    padding-left: 4.5em;
  }

  .disabled-field {
    th {
      color: #666;
      background-color: #ddd;

      small {
        color: #666;
        background-color: #ddd;
      }
    }

    td {
      color: #666;
      background-color: #ddd;
    }
  }
}

form {
  &.create_table_form .pma-fieldset.tblFooters,
  &#multi_edit_central_columns .pma-fieldset.tblFooters {
    background: none;
    border: none;
  }
}

#db_or_table_specific_priv .tblFooters {
  margin-top: -68px;
}

#edit_user_dialog,
#add_user_dialog {
  margin: 20px !important;
}

.config-form {
  span.checkbox {
    padding: 2px;
    display: inline-block;
  }

  /* customized field */
  .custom {
    background: #ffc;
  }

  span.checkbox.custom {
    padding: 1px;
    border: 1px #edec90 solid;
    background: #ffc;
  }

  img.ic_s_reload {
    filter: invert(70%);
  }

  .field-error {
    border-color: #a11 !important;
  }

  input {
    &[type="text"],
    &[type="password"],
    &[type="number"] {
      border: 1px #a7a6aa solid;
      height: auto;

      &:focus {
        border: 1px #6676ff solid;
        background: #f7fbff;
      }
    }
  }

  select,
  textarea {
    border: 1px #a7a6aa solid;
    height: auto;

    &:focus {
      border: 1px #6676ff solid;
      background: #f7fbff;
    }
  }

  .field-comment-mark {
    font-family: serif;
    color: #007;
    cursor: help;
    padding: 0 0.2em;
    font-weight: bold;
    font-style: italic;
  }

  .field-comment-warning {
    color: #a00;
  }

  dd {
    margin-left: 0.5em;

    &::before {
      content: "\25B8  ";
    }
  }
}

.click-hide-message {
  cursor: pointer;
}

.prefsmanage_opts {
  margin-left: 2em;
}

#prefs_autoload {
  margin-bottom: 0.5em;
  margin-left: 0.5em;
}

input#auto_increment_opt {
  width: min-content;
}

#placeholder {
  .button {
    position: absolute;
    cursor: pointer;
  }

  div.button {
    font-size: smaller;
    color: #999;
    background-color: #eee;
    padding: 2px;
  }
}

.wrapper {
  float: left;
  margin-bottom: 1.5em;
}

.toggleButton {
  position: relative;
  cursor: pointer;
  font-size: 0.8em;
  text-align: center;
  line-height: 1.4em;
  height: 1.55em;
  overflow: hidden;
  border-right: 0.1em solid #888;
  border-left: 0.1em solid #888;

  table,
  td,
  img {
    padding: 0;
    position: relative;
  }

  .toggle-container {
    position: absolute;

    td {
      background: none;
    }
  }

  .toggleOn {
    color: #fff;
    padding: 0 1em;
  }

  .toggleOff {
    padding: 0 1em;
  }
}

.doubleFieldset {
  .pma-fieldset {
    width: 48%;
    float: left;
    padding: 0;
  }

  legend {
    margin-left: 1.5em;
  }

  div.wrap {
    padding: 1.5em;
  }
}

form.append_fields_form .tblFooters {
  background: none;
  border: none;
}

#table_columns {
  display: block;
  overflow: auto;

  input {
    &[type="text"],
    &[type="password"],
    &[type="number"],
    &[type="date"] {
      width: 10em;
      box-sizing: border-box;
    }
  }

  select {
    width: 10em;
    box-sizing: border-box;
    margin: 6px;
  }
}

#placeholder {
  position: relative;
  border: 1px solid #aaa;
  float: right;
  overflow: hidden;

  .button {
    position: absolute;
  }
}

.placeholderDrag {
  cursor: move;
}

#left_arrow {
  left: 8px;
  top: 26px;
}

#right_arrow {
  left: 26px;
  top: 26px;
}

#up_arrow {
  left: 17px;
  top: 8px;
}

#down_arrow {
  left: 17px;
  top: 44px;
}

#zoom_in {
  left: 17px;
  top: 67px;
}

#zoom_world {
  left: 17px;
  top: 85px;
}

#zoom_out {
  left: 17px;
  top: 103px;
}

.colborder {
  cursor: col-resize;
  height: 100%;
  margin-left: -6px;
  position: absolute;
  width: 5px;
}

.colborder_active {
  border-right: 2px solid #a44;
}

.pma_table {
  th.draggable span {
    display: block;
    overflow: hidden;
  }
}

.pma_table {
  td {
    position: static;
  }

  tbody td span {
    display: block;
    overflow: hidden;

    code span {
      display: inline;
    }
  }
}

.modal-copy input {
  display: block;
  width: 100%;
  margin-top: 1.5em;
  padding: 0.3em 0;
}

.cRsz {
  position: absolute;
}

.cCpy {
  background: #333;
  color: #fff;
  font-weight: bold;
  margin: 0.1em;
  padding: 0.3em;
  position: absolute;
}

.cPointer {
  $height: 20px;
  $width: 10px;

  height: $height;
  width: $width;
  margin-left: $height * -0.5;
  margin-top: $width * -0.5;
  position: absolute;
  background: url("../img/col_pointer.png");
}

.tooltip {
  background: #333 !important;
  opacity: 0.8 !important;
  z-index: 9999;
  border: 1px solid #000 !important;
  font-size: 10px !important;
  font-weight: normal !important;
  padding: 5px !important;
  width: 260px;
  line-height: 1.5;

  * {
    background: none !important;
    color: #fff !important;
  }
}

.cDrop {
  right: 0;
  position: absolute;
  top: 0;
}

.coldrop {
  background: url("../img/col_drop.png");
  cursor: pointer;
  height: 16px;
  margin-top: 0.3em;
  width: 16px;

  &:hover {
    background-color: #999;
  }
}

.coldrop-hover {
  background-color: #999;
}

.cList {
  background: #fff;
  border: solid 1px #ccc;
  position: absolute;
  margin-left: 75%;
  right: 0;
  max-width: 100%;
  overflow-wrap: break-word;

  .lDiv div {
    padding: 0.2em 0.5em 0.2em;
    padding-left: 0.2em;
    white-space: nowrap;

    &:hover {
      background: $navi-background;
      cursor: pointer;
      color: #fff;
    }

    input {
      cursor: pointer;
    }
  }
}

.showAllColBtn {
  border-bottom: solid 1px #ccc;
  border-top: solid 1px #ccc;
  cursor: pointer;
  font-size: 0.9em;
  font-family: $font-family-bold;
  padding: 0.35em 1em;
  text-align: center;
  font-weight: normal;

  &:hover {
    background: $navi-background;
    cursor: pointer;
    color: #fff;
  }
}

#page_content {
  line-height: 1.5;
}

.navigation_separator {
  color: #eee;
  display: inline-block;
  font-size: 1.5em;
  text-align: center;
  height: 1.4em;
}

.navigation {
  width: 100%;
  background-color: $navi-background;
  color: $navi-color;

  td {
    margin: 0;
    padding: 0;
    vertical-align: middle;
    white-space: nowrap;
  }

  input {
    &[type="submit"] {
      background: $navi-background;
      color: $navi-color;
      border: none;
      filter: none;
      margin: 5px;
      padding: 0.4em;

      &:hover {
        color: $button-color;
        cursor: pointer;
        background-color: $th-color;
      }
    }

    &.edit_mode_active {
      color: $button-color;
      cursor: pointer;
      background-color: $th-color;
    }
  }

  select {
    margin: 0 0.8em;
    border: none;
  }

  input[type="text"] {
    border: none;
  }
}

.navigation_goto {
  width: 100%;
}

.insertRowTable {
  td,
  th {
    vertical-align: middle;
  }
}

.cEdit {
  margin: 0;
  padding: 0;
  position: absolute;

  input {
    &[type="text"],
    &[type="password"],
    &[type="number"] {
      background: #fff;
      height: 100%;
      margin: 0;
      padding: 0;
    }
  }

  .edit_area {
    background: #fff;
    border: 1px solid #999;
    min-width: 10em;
    padding: 0.3em 0.5em;

    select,
    textarea {
      width: 97%;
    }
  }

  .cell_edit_hint {
    color: #555;
    font-size: 0.8em;
    margin: 0.3em 0.2em;
  }

  .edit_box {
    overflow-x: hidden;
    overflow-y: scroll;
    padding: 0;
    margin-top: 10px;
  }

  .edit_box_posting {
    background: #fff url("../img/ajax_clock_small.gif") no-repeat right center;
    padding-right: 1.5em;
  }

  .edit_area_loading {
    background: #fff url("../img/ajax_clock_small.gif") no-repeat center;
    height: 10em;
  }

  .goto_link {
    background: #eee;
    color: #555;
    padding: 0.2em 0.3em;
  }
}

.saving_edited_data {
  background: url("../img/ajax_clock_small.gif") no-repeat left;
  padding-left: 20px;
}

.relationalTable select {
  width: 125px;
  margin-right: 5px;
}

/* css for timepicker */

.ui-timepicker-div {
  .ui-widget-header {
    margin-bottom: 8px;
  }

  dl {
    text-align: left;

    dt {
      height: 25px;
      margin-bottom: -25px;
    }

    dd {
      margin: 0 10px 10px 85px;
    }
  }

  td {
    font-size: 90%;
  }
}

.ui-tpicker-grid-label {
  background: none;
  border: none;
  margin: 0;
  padding: 0;
}

.ui-timepicker-rtl {
  direction: rtl;

  dl {
    text-align: right;

    dd {
      margin: 0 65px 10px 10px;
    }
  }
}

.ui_tpicker_time_input {
  width: 100%;
}

body .ui-widget {
  font-size: 1em;
}

body #ui-datepicker-div {
  z-index: 9999 !important;
}

.ui-dialog .pma-fieldset legend a {
  color: #235a81;
}

div#page_content div {
  &#tableslistcontainer {
    margin-top: 1em;

    table.data {
      border-top: 0.1px solid #eee;
    }
  }

  &.result_query {
    margin-top: 1em;
  }
}

table.show_create {
  margin-top: 1em;

  td {
    border-right: 1px solid #bbb;
  }
}

#alias_modal {
  table th {
    vertical-align: middle;
    padding-left: 1em;
  }

  label.col-2 {
    min-width: 20%;
    display: inline-block;
  }

  select {
    width: 25%;
    margin-right: 2em;
  }

  label {
    font-weight: bold;
  }
}

.small_font {
  font-size: smaller;
}

/* Console styles */
#pma_console_container {
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 100;
}

textarea {
  resize: both;
}

#pma_console {
  position: relative;
  margin-left: 250px;

  .templates {
    display: none;
  }

  .mid_text {
    vertical-align: middle;
  }

  .toolbar {
    position: relative;
    background: $th-background;
    border-top: solid 1px #ccc;
    cursor: n-resize;

    span {
      vertical-align: middle;
    }

    &.collapsed {
      cursor: default;

      &:not(:hover) {
        display: inline-block;
        border-right: solid 1px #ccc;
      }

      > .button {
        display: none;
      }
    }
  }

  .message span {
    &.text,
    &.action {
      padding: 0 3px;
      display: inline-block;
    }
  }

  .toolbar {
    .button,
    .text {
      padding: 0 3px;
      display: inline-block;
    }
  }

  .switch_button {
    padding: 0 3px;
    display: inline-block;
  }

  .message span.action,
  .toolbar .button,
  .switch_button {
    cursor: pointer;
  }

  .toolbar {
    .text {
      font-weight: bold;
    }

    .button,
    .text {
      margin-right: 0.4em;
      float: right;
    }
  }

  .content {
    overflow-x: hidden;
    overflow-y: auto;
    margin-bottom: -65px;
    border-top: solid 1px #ccc;
    background: #fff;
    padding-top: 0.4em;

    &.console_dark_theme {
      background: #000;
      color: #fff;

      .CodeMirror-wrap {
        background: #000;
        color: #fff;
      }

      .action_content {
        color: #000;
      }

      .message {
        border-color: #373b41;
      }

      .CodeMirror-cursor {
        border-color: #fff;
      }

      .cm-keyword {
        color: #de935f;
      }
    }
  }

  .message,
  .query_input {
    position: relative;
    font-family: Monaco, Consolas, monospace;
    cursor: text;
    margin: 0 10px 0.2em 1.4em;
  }

  .message {
    border-bottom: solid 1px #ccc;
    padding-bottom: 0.2em;

    &.expanded > .action_content {
      position: relative;
    }

    &::before {
      left: -0.7em;
      position: absolute;
      content: ">";
    }
  }

  .query_input {
    &::before {
      left: -0.7em;
      position: absolute;
      content: ">";
      top: -2px;
    }

    textarea {
      width: 100%;
      height: 4em;
      resize: vertical;
    }
  }

  .message {
    &:hover::before {
      color: #7cf;
      font-weight: bold;
    }

    &.expanded::before {
      content: "]";
    }

    &.welcome::before {
      display: none;
    }

    &.failed {
      &::before,
      &.expanded::before,
      &:hover::before {
        content: "=";
        color: #944;
      }
    }

    &.pending::before {
      opacity: 0.3;
    }

    &.collapsed > .query {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    &.expanded > .query {
      display: block;
      white-space: pre;
      word-wrap: break-word;
    }

    .text.targetdb,
    &.collapsed .action.collapse,
    &.expanded .action.expand {
      display: none;
    }

    .action {
      &.requery,
      &.profiling,
      &.explain,
      &.bookmark {
        display: none;
      }
    }

    &.select .action {
      &.profiling,
      &.explain {
        display: inline-block;
      }
    }

    &.history .text.targetdb,
    &.successed .text.targetdb {
      display: inline-block;
    }

    &.history .action {
      &.requery,
      &.bookmark {
        display: inline-block;
      }
    }

    &.bookmark .action {
      &.requery,
      &.bookmark {
        display: inline-block;
      }
    }

    &.successed .action {
      &.requery,
      &.bookmark {
        display: inline-block;
      }
    }

    .action_content {
      position: absolute;
      bottom: 100%;
      background: #ccc;
      border: solid 1px #aaa;
    }
  }

  .message {
    &.bookmark .text.targetdb,
    .text.query_time {
      margin: 0;
      display: inline-block;
    }

    &.failed .text.query_time,
    .text.failed {
      display: none;
    }

    &.failed .text.failed {
      display: inline-block;
    }

    .text {
      background: #fff;
    }

    &.collapsed {
      > .action_content {
        display: none;
      }

      &:hover > .action_content {
        display: block;
      }
    }

    .bookmark_label {
      padding: 0 4px;
      top: 0;
      background: #369;
      color: #fff;

      &.shared {
        background: #396;
      }
    }
  }

  .query_input {
    position: relative;
  }

  .mid_layer {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    background: #ccc;
    display: none;
    cursor: pointer;
    z-index: 200;
  }

  .card {
    position: absolute;
    width: 94%;
    height: 100%;
    min-height: 48px;
    left: 100%;
    top: 0;
    border-left: solid 1px #999;
    z-index: 300;
    transition: left 0.2s;

    &.show {
      left: 6%;
    }
  }
}

#pma_bookmarks .content.add_bookmark,
#pma_console_options .content {
  padding: 4px 6px;
}

#pma_bookmarks .content.add_bookmark {
  .options {
    margin-left: 1.4em;
    padding-bottom: 0.4em;
    margin-bottom: 0.4em;
    border-bottom: solid 1px #ccc;

    button {
      margin: 0 7px;
      vertical-align: bottom;
    }
  }

  input[type="text"] {
    margin: 0;
    padding: 2px 4px;
  }
}

#pma_console {
  .button.hide,
  .message span.text.hide {
    display: none;
  }
}

#debug_console {
  &.grouped .ungroup_queries {
    display: inline-block;
  }

  &.ungrouped {
    .group_queries {
      display: inline-block;
    }

    .ungroup_queries,
    .sort_count {
      display: none;
    }
  }

  &.grouped .group_queries {
    display: none;
  }

  .count {
    margin-right: 8px;
  }

  .show_trace .trace,
  .show_args .args {
    display: block;
  }

  .hide_trace .trace,
  .hide_args .args,
  .show_trace .action.dbg_show_trace,
  .hide_trace .action.dbg_hide_trace {
    display: none;
  }

  .traceStep {
    &.hide_args .action.dbg_hide_args,
    &.show_args .action.dbg_show_args {
      display: none;
    }

    &::after {
      content: "";
      display: table;
      clear: both;
    }
  }

  .trace.welcome::after,
  .debug > .welcome::after {
    content: "";
    display: table;
    clear: both;
  }

  .debug_summary {
    float: left;
  }

  .trace.welcome .time,
  .traceStep .file,
  .script_name {
    float: right;
  }

  .traceStep .args pre {
    margin: 0;
  }
}

/* Code mirror console style */

.cm-s-pma {
  .CodeMirror-code {
    font-family: Monaco, Consolas, monospace;

    pre {
      font-family: Monaco, Consolas, monospace;
    }
  }

  .CodeMirror-measure > pre,
  .CodeMirror-code > pre,
  .CodeMirror-lines {
    padding: 0;
  }

  &.CodeMirror {
    resize: none;
    height: auto;
    width: 100%;
    min-height: initial;
    max-height: initial;
  }

  .CodeMirror-scroll {
    cursor: text;
  }
}

/* PMA drop-improt style */

.pma_drop_handler {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(0, 0, 0, 0.6);
  height: 100%;
  z-index: 999;
  color: white;
  font-size: 30pt;
  text-align: center;
  padding-top: 20%;
}

.pma_sql_import_status {
  display: none;
  position: fixed;
  bottom: 0;
  right: 25px;
  width: 400px;
  border: 1px solid #999;
  background: #f3f3f3;

  h2 {
    background-color: #bbb;
    padding: 0.1em 0.3em;
    margin-top: 0;
    margin-bottom: 0;
    color: #fff;
    font-size: 1.6em;
    font-weight: normal;
  }
}

.pma_drop_result h2 {
  background-color: #bbb;
  padding: 0.1em 0.3em;
  margin-top: 0;
  margin-bottom: 0;
  color: #fff;
  font-size: 1.6em;
  font-weight: normal;
}

.pma_sql_import_status {
  div {
    height: 270px;
    overflow-y: auto;
    overflow-x: hidden;
    list-style-type: none;

    li {
      padding: 8px 10px;
      border-bottom: 1px solid #bbb;
      color: rgb(148, 14, 14);
      background: white;

      .filesize {
        float: right;
      }
    }
  }

  h2 {
    .minimize {
      float: right;
      margin-right: 5px;
      padding: 0 10px;
    }

    .close {
      float: right;
      margin-right: 5px;
      padding: 0 10px;
      display: none;
    }

    .minimize:hover,
    .close:hover {
      background: rgba(155, 149, 149, 0.78);
      cursor: pointer;
    }
  }
}

.pma_drop_result h2 .close:hover {
  background: rgba(155, 149, 149, 0.78);
  cursor: pointer;
}

.pma_drop_file_status {
  color: #235a81;

  span.underline:hover {
    cursor: pointer;
    text-decoration: underline;
  }
}

.pma_drop_result {
  position: fixed;
  top: 10%;
  left: 20%;
  width: 60%;
  background: white;
  min-height: 300px;
  z-index: 800;
  cursor: move;

  h2 .close {
    float: right;
    margin-right: 5px;
    padding: 0 10px;
  }
}

.dependencies_box {
  background-color: white;
  border: 3px ridge black;
}

#composite_index_list {
  list-style-type: none;
  list-style-position: inside;
}

span.drag_icon {
  display: inline-block;
  background-image: url("../img/s_sortable.png");
  background-position: center center;
  background-repeat: no-repeat;
  width: 1em;
  height: 3em;
  cursor: move;
}

.topmargin {
  margin-top: 1em;
}

meter {
  &[value="1"]::-webkit-meter-optimum-value {
    background: linear-gradient(white 3%, #e32929 5%, transparent 10%, #e32929);
  }

  &[value="2"]::-webkit-meter-optimum-value {
    background: linear-gradient(white 3%, #f60 5%, transparent 10%, #f60);
  }

  &[value="3"]::-webkit-meter-optimum-value {
    background: linear-gradient(white 3%, #ffd700 5%, transparent 10%, #ffd700);
  }
}

/* styles for jQuery-ui to support rtl languages */

body {
  .ui-dialog {
    .ui-dialog-titlebar-close {
      right: 0.3em;
      left: initial;
    }

    .ui-dialog-title {
      float: left;
    }

    .ui-dialog-buttonpane .ui-dialog-buttonset {
      float: right;
    }
  }

  .ui-corner-all,
  .ui-corner-top,
  .ui-corner-left,
  .ui-corner-tl {
    border-top-left-radius: 0;
  }

  .ui-corner-all,
  .ui-corner-top,
  .ui-corner-right,
  .ui-corner-tr {
    border-top-right-radius: 0;
  }

  .ui-corner-all,
  .ui-corner-bottom,
  .ui-corner-left,
  .ui-corner-bl {
    border-bottom-left-radius: 0;
  }

  .ui-corner-all,
  .ui-corner-bottom,
  .ui-corner-right,
  .ui-corner-br {
    border-bottom-right-radius: 0;
  }

  .ui-dialog {
    padding: 0;

    .ui-widget-header {
      color: $button-color;
      border: none;
      background-color: $navi-background;
      background-image: none;
    }

    .ui-dialog-title {
      padding: 5px;
      font-weight: normal;
    }

    .ui-dialog-buttonpane button {
      font-family: $font-family-base;
      color: $button-color;
      background-color: $navi-background;
      background-image: none;
      border: 1px solid $button-background;

      &.ui-state-hover {
        background-color: $button-hover;
        border: 1px solid $button-hover;
      }

      &.ui-state-active {
        background-color: #333;
        border: 1px solid #333;
      }
    }
  }
}

#tooltip_editor {
  font-size: 12px;
  background-color: #fff;
  opacity: 0.95;
  filter: alpha(opacity=95);
  padding: 5px;
}

#tooltip_font {
  font-weight: bold;
}

#selection_box {
  z-index: 1000;
  height: 205px;
  position: absolute;
  background-color: #87ceeb;
  opacity: 0.4;
  filter: alpha(opacity=40);
  pointer-events: none;
}

#filterQueryText {
  vertical-align: baseline;
}

/* templates/database/multi_table_query */

.multi_table_query_form {
  .query-form__tr--hide {
    display: none;
  }

  .query-form__fieldset--inline,
  .query-form__select--inline {
    display: inline;
  }

  .query-form__tr--bg-none {
    background: none;
  }

  .query-form__input--wide {
    width: 91%;
  }

  .query-form__multi-sql-query {
    float: left;
  }
}

// Enable scrollable blocks of code
.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}

// Extra large devices (large desktops, 1200px and up)
@include media-breakpoint-up(xl) {
  div.tools {
    text-align: left;
  }

  .pma-fieldset.tblFooters,
  .tblFooters {
    text-align: left;
  }
}

.resize-vertical {
  resize: vertical;
}

.table-responsive-md .data {
  z-index: 9;
}
