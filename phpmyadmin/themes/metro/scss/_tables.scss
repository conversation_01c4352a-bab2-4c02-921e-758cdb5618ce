.table {
  caption-side: top;

  td {
    touch-action: manipulation;
    vertical-align: middle;
  }

  th {
    background-color: $th-background;
    font-family: $font-family-bold;
    font-weight: normal;
    border-bottom: 1px solid $border-color;

    a {
      color: $th-color;
    }
  }

  caption {
    font-family: $font-family-bold;
    background-color: $th-background;
    font-weight: normal;
    text-align: center;
  }
}

@media only screen and (min-width: 768px) {
  .table th.position-sticky {
    top: 87px;
  }
}
