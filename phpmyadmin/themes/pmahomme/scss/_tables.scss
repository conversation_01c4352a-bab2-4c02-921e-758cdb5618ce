.table {
  caption-side: top;

  th,
  td {
    text-shadow: 0 1px 0 #fff;
    vertical-align: middle;
  }

  th {
    color: $th-color;
    text-align: left;
  }

  td {
    touch-action: manipulation;
  }

  thead th {
    border-right: 1px solid #fff;
    background-image: linear-gradient(#fff, #ccc);
  }

  caption {
    background-color: $th-background;
    font-weight: bold;
    text-align: center;
  }
}

.table-hover {
  tbody tr {
    &:hover {
      background: linear-gradient(#ced6df, #b6c6d7);
    }
  }
}

.table-striped {
  > tbody > tr:nth-of-type(#{$table-striped-order}) > th {
    color: $th-color;
  }
}

@media only screen and (min-width: 768px) {
  .table th.position-sticky {
    top: 60px;
  }
}
