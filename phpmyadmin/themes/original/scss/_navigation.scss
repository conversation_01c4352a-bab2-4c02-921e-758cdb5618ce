// Navigation styles for the original theme

#pma_navigation_content {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  padding-bottom: 1em;
}

#pma_navigation {
  background: $navi-background;
  color: $navi-color;
  width: $navi-width;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  border-right: 1px solid gray;
  z-index: 800;

  ul {
    margin: 0;
  }

  form {
    margin: 0;
    padding: 0;
    display: inline;
  }

  select {
    &#select_server,
    &#lightm_db {
      width: 100%;
    }
  }

  div {
    &.pageselector {
      text-align: center;
      margin: 0 0 0;
      margin-left: 0.75em;
      border-left: 1px solid #666;
    }

    &#pmalogo {
      background-color: $navi-background;
      padding: 0.3em;
    }

    &#recentTableList,
    &#FavoriteTableList {
      text-align: center;
      margin-bottom: 0.5em;
    }
  }

  #recentTable,
  #FavoriteTable {
    width: 200px;
  }

  #pmalogo,
  #serverChoice,
  #navipanellinks,
  #recentTableList,
  #FavoriteTableList,
  #databaseList,
  div.pageselector.dbselector {
    text-align: center;
    margin-bottom: 0.3em;
    padding-bottom: 0.3em;
    border: 0;
  }

  #navipanellinks .icon {
    margin: 0;
  }

  #recentTableList select,
  #FavoriteTableList select,
  #serverChoice select {
    width: 80%;
  }

  #recentTableList,
  #FavoriteTableList {
    margin-bottom: 0;
    padding-bottom: 0;
  }
}

#pma_navigation_content > img.throbber {
  display: block;
  margin: 0 auto;
}

#pma_navigation_tree {
  margin: 0;
  margin-left: 1em;
  color: #444;
  height: 74%;
  position: relative;
}

#pma_navigation_select_database {
  text-align: left;
  padding: 0 0 0;
  border: 0;
  margin: 0;
}

#pma_navigation_db_select {
  margin-top: 0.5em;
  margin-left: 0.75em;

  select {
    background: linear-gradient(#fff, #f1f1f1, #fff);
    -webkit-border-radius: 2px;
    border-radius: 2px;
    border: 1px solid #bbb;
    border-top: 1px solid #bbb;
    color: #333;
    padding: 4px 6px;
    margin: 0 0 0;
    width: 92%;
    font-size: 1.11em;
  }
}

#pma_navigation_tree_content {
  width: 100%;
  overflow: hidden;
  overflow-y: auto;
  position: absolute;
  height: 100%;

  a.hover_show_full {
    position: relative;
    z-index: 100;
    vertical-align: sub;
  }

  li.nav_node_table {
    background-color: #0000;
  }
}

#pma_navigation_tree {
  a {
    color: $navi-color;
    padding-left: 0;

    &:hover {
      text-decoration: underline;
    }
  }

  li {
    margin-bottom: 0;

    &.activePointer,
    &.selected {
      color: $navi-pointer-color;
      background-color: $navi-pointer-background;
    }

    .dbItemControls {
      padding-right: 4px;
      float: right;
    }

    .navItemControls {
      display: none;
      padding-right: 4px;
      float: right;
    }

    &.activePointer .navItemControls {
      display: block;
      opacity: 0.5;

      &:hover {
        opacity: 1;
      }
    }
  }

  ul {
    clear: both;
    padding: 0;
    list-style-type: none;
    margin: 0;

    ul {
      position: relative;
    }
  }

  li {
    white-space: nowrap;
    clear: both;
    min-height: 16px;
  }

  img {
    margin: 0;
  }

  i {
    display: block;
  }

  div.block {
    position: relative;
    width: 1.5em;
    height: 1.5em;
    min-width: 16px;
    min-height: 16px;
    float: left;

    &.double {
      width: 3em;
    }

    i,
    b {
      width: 1.5em;
      height: 1.7em;
      min-width: 16px;
      min-height: 8px;
      position: absolute;
      bottom: 0.7em;
      left: 0.75em;
      z-index: 0;
    }

    i {
      border-left: 1px solid #666;
      border-bottom: 1px solid #666;
      position: relative;
      z-index: 0;

      &.first {
        border-left: 0;
      }
    }

    // Bottom segment for the tree element connections
    b {
      display: block;
      height: 0.75em;
      bottom: -0.5rem;// Compensate for vertical-align: sub; of hover_show_full
      left: 0.75em;
      border-left: 1px solid #666;
    }

    a,
    u {
      position: absolute;
      left: 50%;
      top: 50%;
      z-index: 10;
    }

    a + a {
      left: 100%;
    }

    &.double {
      a,
      u {
        left: 25%;
      }

      a + a {
        left: 70%;
      }
    }

    img {
      position: relative;
      top: -0.6em;
      left: 0;
      margin-left: -5px;
    }
  }

  li {
    &.last > ul {
      background: none;
    }

    > {
      a,
      i {
        line-height: 1.5em;
        height: 1.5em;
        padding-left: 0.3em;
      }
    }
  }

  .list_container {
    border-left: 1px solid #666;
    margin-left: 0.75em;
    padding-left: 0.75em;

    li.last.database {
      // Revert the effect of the rule that is applied on all the tree
      margin-bottom: 0 !important;
    }
  }

  .last > .list_container {
    border-left: 0 solid #666;
  }
}

li.fast_filter {
  padding-left: 0.75em;
  margin-left: 0.75em;
  padding-right: 35px;
  border-left: 1px solid #666;

  input {
    font-size: 0.7em;
  }

  .searchClauseClear {
    border: 0;
    font-weight: bold;
    color: #800;
    font-size: 0.7em;
  }

  &.db_fast_filter {
    border: 0;
    margin-left: 0;
  }
}

/* Resize handler */
#pma_navigation_resizer {
  width: 3px;
  height: 100%;
  background-color: #aaa;
  cursor: col-resize;
  position: fixed;
  top: 0;
  left: $navi-width;
  z-index: 801;
}

#pma_navigation_collapser {
  width: 20px;
  height: 22px;
  line-height: 22px;
  background: #eee;
  color: #555;
  font-weight: bold;
  position: fixed;
  top: 0;
  left: $navi-width;
  text-align: center;
  cursor: pointer;
  z-index: 800;
  text-shadow: 0 1px 0 #fff;
  filter: dropshadow(color = #fff, offx = 0, offy = 1);
  border: 1px solid #888;
}

#navigation_controls_outer {
  min-height: 21px !important;

  &.activePointer {
    background-color: transparent !important;
  }
}

#navigation_controls {
  float: right;
  padding-right: 23px;
}

.pma_quick_warp {
  margin-top: 5px;
  margin-left: 2px;
  position: relative;

  .drop_list {
    float: left;
    margin-left: 3px;
    padding: 2px 0;
  }

  .drop_button {
    padding: 0 0.3em;
    border: 1px solid #ddd;
    background: #f2f2f2;
    cursor: pointer;
  }

  .drop_list {
    &:hover .drop_button {
      background: #fff;
    }

    ul {
      position: absolute;
      margin: 0;
      padding: 0;
      overflow: hidden;
      overflow-y: auto;
      list-style: none;
      background: #fff;
      border: 1px solid #ddd;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      top: 100%;
      left: 3px;
      right: 0;
      display: none;
      z-index: 802;
    }

    &:hover ul {
      display: block;
    }

    li {
      white-space: nowrap;

      img {
        vertical-align: sub;
      }

      &:hover {
        background: #f2f2f2;
      }
    }

    a {
      display: block;
      padding: 0.1em 0.3em;

      &.favorite_table_anchor {
        clear: left;
        float: left;
        padding: 0.1em 0.3em 0;
      }
    }
  }
}
