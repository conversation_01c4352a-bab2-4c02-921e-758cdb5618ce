<?php

namespace Psalm\Internal\PhpVisitor;

use PhpParser;

use function is_string;

/**
 * @internal
 */
class ShortClosureVisitor extends Php<PERSON>ars<PERSON>\NodeVisitorAbstract
{
    /**
     * @var array<string, bool>
     */
    protected $used_variables = [];

    public function enterNode(PhpParser\Node $node): ?int
    {
        if ($node instanceof PhpParser\Node\Expr\Variable && is_string($node->name)) {
            $this->used_variables['$' . $node->name] = true;
        }

        return null;
    }

    /**
     * @return array<string, bool>
     */
    public function getUsedVariables(): array
    {
        return $this->used_variables;
    }
}
