<?php

namespace Psalm\Plugin\Hook;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node;
use Psalm\Codebase;
use Psalm\FileManipulation;
use Psalm\StatementsSource;
use Psalm\Storage\FunctionLikeStorage;

/** @deprecated going to be removed in Psalm 5 */
interface AfterFunctionLikeAnalysisInterface
{
    /**
     * Called after a statement has been checked
     *
     * @param  FileManipulation[]   $file_replacements
     *
     * @return null|false
     */
    public static function afterStatementAnalysis(
        Node\FunctionLike $stmt,
        FunctionLikeStorage $classlike_storage,
        StatementsSource $statements_source,
        Codebase $codebase,
        array &$file_replacements = []
    ): ?bool;
}
