<?php

/**
 * The SoapClient class provides a client for SOAP 1.1, SOAP 1.2 servers. It can be used in WSDL
 * or non-WSDL mode.
 * @link https://php.net/manual/en/class.soapclient.php
 */
class SoapClient  {

    /**
     * SoapClient constructor
     * @link https://php.net/manual/en/soapclient.soapclient.php
     * @param mixed $wsdl <p>
     * URI of the WSDL file or <b>NULL</b> if working in
     * non-WSDL mode.
     * </p>
     * <p>
     * During development, WSDL caching may be disabled by the
     * use of the soap.wsdl_cache_ttl <i>php.ini</i> setting
     * otherwise changes made to the WSDL file will have no effect until
     * soap.wsdl_cache_ttl is expired.
     * </p>
     * @param array $options [optional] <p>
     * An array of options. If working in WSDL mode, this parameter is optional.
     * If working in non-WSDL mode, the location and
     * uri options must be set, where location
     * is the URL of the SOAP server to send the request to, and uri
     * is the target namespace of the SOAP service.
     * </p>
     * <p>
     * The style and use options only work in
     * non-WSDL mode. In WSDL mode, they come from the WSDL file.
     * </p>
     * <p>
     * The soap_version option should be one of either
     * <b>SOAP_1_1</b> or <b>SOAP_1_2</b> to
     * select SOAP 1.1 or 1.2, respectively. If omitted, 1.1 is used.
     * </p>
     * <p>
     * For HTTP authentication, the login and
     * password options can be used to supply credentials.
     * For making an HTTP connection through
     * a proxy server, the options proxy_host,
     * proxy_port, proxy_login
     * and proxy_password are also available.
     * For HTTPS client certificate authentication use
     * local_cert and passphrase options. An
     * authentication may be supplied in the authentication
     * option. The authentication method may be either
     * <b>SOAP_AUTHENTICATION_BASIC</b> (default) or
     * <b>SOAP_AUTHENTICATION_DIGEST</b>.
     * </p>
     * <p>
     * The compression option allows to use compression
     * of HTTP SOAP requests and responses.
     * </p>
     * <p>
     * The encoding option defines internal character
     * encoding. This option does not change the encoding of SOAP requests (it is
     * always utf-8), but converts strings into it.
     * </p>
     * <p>
     * The trace option enables tracing of request so faults
     * can be backtraced. This defaults to <b>FALSE</b>
     * </p>
     * <p>
     * The classmap option can be used to map some WSDL
     * types to PHP classes. This option must be an array with WSDL types
     * as keys and names of PHP classes as values.
     * </p>
     * <p>
     * Setting the boolean trace option enables use of the
     * methods
     * SoapClient->__getLastRequest,
     * SoapClient->__getLastRequestHeaders,
     * SoapClient->__getLastResponse and
     * SoapClient->__getLastResponseHeaders.
     * </p>
     * <p>
     * The exceptions option is a boolean value defining whether
     * soap errors throw exceptions of type
     * SoapFault.
     * </p>
     * <p>
     * The connection_timeout option defines a timeout in seconds
     * for the connection to the SOAP service. This option does not define a timeout
     * for services with slow responses. To limit the time to wait for calls to finish the
     * default_socket_timeout setting
     * is available.
     * </p>
     * <p>
     * The typemap option is an array of type mappings.
     * Type mapping is an array with keys type_name,
     * type_ns (namespace URI), from_xml
     * (callback accepting one string parameter) and to_xml
     * (callback accepting one object parameter).
     * </p>
     * <p>
     * The cache_wsdl option is one of
     * <b>WSDL_CACHE_NONE</b>,
     * <b>WSDL_CACHE_DISK</b>,
     * <b>WSDL_CACHE_MEMORY</b> or
     * <b>WSDL_CACHE_BOTH</b>.
     * </p>
     * <p>
     * The user_agent option specifies string to use in
     * User-Agent header.
     * </p>
     * <p>
     * The stream_context option is a resource
     * for context.
     * </p>
     * <p>
     * The features option is a bitmask of
     * <b>SOAP_SINGLE_ELEMENT_ARRAYS</b>,
     * <b>SOAP_USE_XSI_ARRAY_TYPE</b>,
     * <b>SOAP_WAIT_ONE_WAY_CALLS</b>.
     * </p>
     * <p>
     * The keep_alive option is a boolean value defining whether
     * to send the Connection: Keep-Alive header or
     * Connection: close.
     * </p>
     * <p>
     * The ssl_method option is one of
     * <b>SOAP_SSL_METHOD_TLS</b>,
     * <b>SOAP_SSL_METHOD_SSLv2</b>,
     * <b>SOAP_SSL_METHOD_SSLv3</b> or
     * <b>SOAP_SSL_METHOD_SSLv23</b>.
     * </p>
     * @throws SoapFault A SoapFault exception will be thrown if the wsdl URI cannot be loaded.
     * @since 5.0.1
     */
    public function __construct ($wsdl, array $options = null) {}

     /**
      * Calls a SOAP function (deprecated)
      * @link https://php.net/manual/en/soapclient.call.php
      * @param string $function_name
      * @param array $arguments
      * @return mixed
      * @since 5.0.1
      */
     public function __call ($function_name, $arguments) {}

     /**
      * Calls a SOAP function
      * @link https://php.net/manual/en/soapclient.soapcall.php
      * @param string $function_name <p>
      * The name of the SOAP function to call.
      * </p>
      * @param array $arguments <p>
      * An array of the arguments to pass to the function. This can be either
      * an ordered or an associative array. Note that most SOAP servers require
      * parameter names to be provided, in which case this must be an
      * associative array.
      * </p>
      * @param array $options [optional] <p>
      * An associative array of options to pass to the client.
      * </p>
      * <p>
      * The location option is the URL of the remote Web service.
      * </p>
      * <p>
      * The uri option is the target namespace of the SOAP service.
      * </p>
      * <p>
      * The soapaction option is the action to call.
      * </p>
      * @param mixed $input_headers [optional] <p>
      * An array of headers to be sent along with the SOAP request.
      * </p>
      * @param array $output_headers [optional] <p>
      * If supplied, this array will be filled with the headers from the SOAP response.
      * </p>
      * @return mixed SOAP functions may return one, or multiple values. If only one value is returned
      * by the SOAP function, the return value of __soapCall will be
      * a simple value (e.g. an integer, a string, etc). If multiple values are
      * returned, __soapCall will return
      * an associative array of named output parameters.
      * </p>
      * <p>
      * On error, if the SoapClient object was constructed with the exceptions
      * option set to <b>FALSE</b>, a SoapFault object will be returned.
      * @since 5.0.1
      */
     public function __soapCall (string $function_name, array $arguments, array $options = null, $input_headers = null, &$output_headers = null) {}

     /**
      * Returns last SOAP request
      * @link https://php.net/manual/en/soapclient.getlastrequest.php
      * @return string|null The last SOAP request, as an XML string.
      * @since 5.0.1
      */
     public function __getLastRequest () {}

     /**
      * Returns last SOAP response
      * @link https://php.net/manual/en/soapclient.getlastresponse.php
      * @return string|null The last SOAP response, as an XML string.
      * @since 5.0.1
      */
     public function __getLastResponse () {}

     /**
      * Returns the SOAP headers from the last request
      * @link https://php.net/manual/en/soapclient.getlastrequestheaders.php
      * @return string|null The last SOAP request headers.
      * @since 5.0.1
      */
     public function __getLastRequestHeaders () {}

     /**
      * Returns the SOAP headers from the last response
      * @link https://php.net/manual/en/soapclient.getlastresponseheaders.php
      * @return string|null The last SOAP response headers.
      * @since 5.0.1
      */
     public function __getLastResponseHeaders () {}

     /**
      * Returns list of available SOAP functions
      * @link https://php.net/manual/en/soapclient.getfunctions.php
      * @return array|null The array of SOAP function prototypes, detailing the return type,
      * the function name and type-hinted parameters.
      * @since 5.0.1
      */
     public function __getFunctions () {}

     /**
      * Returns a list of SOAP types
      * @link https://php.net/manual/en/soapclient.gettypes.php
      * @return array|null The array of SOAP types, detailing all structures and types.
      * @since 5.0.1
      */
     public function __getTypes () {}

     /**
      * Returns a list of all cookies
      * @link https://php.net/manual/en/soapclient.getcookies.php
      * @return array The array of all cookies
      * @since 5.4.3
      */
     public function __getCookies () {}

     /**
      * The __setCookie purpose
      * @link https://php.net/manual/en/soapclient.setcookie.php
      * @param string $name <p>
      * The name of the cookie.
      * </p>
      * @param string $value [optional] <p>
      * The value of the cookie. If not specified, the cookie will be deleted.
      * </p>
      * @return void No value is returned.
      * @since 5.0.4
      */
     public function __setCookie ($name, $value = null) {}

     /**
      * Sets the location of the Web service to use
      * @link https://php.net/manual/en/soapclient.setlocation.php
      * @param string $new_location [optional] <p>
      * The new endpoint URL.
      * </p>
      * @return string The old endpoint URL.
      * @since 5.0.1
      */
     public function __setLocation ($new_location = null) {}

     /**
      * Sets SOAP headers for subsequent calls
      * @link https://php.net/manual/en/soapclient.setsoapheaders.php
      * @param mixed $soapheaders [optional] <p>
      * The headers to be set. It could be <b>SoapHeader</b>
      * object or array of <b>SoapHeader</b> objects.
      * If not specified or set to <b>NULL</b>, the headers will be deleted.
      * </p>
      * @return bool <b>TRUE</b> on success or <b>FALSE</b> on failure.
      * @since 5.0.5
      */
     public function __setSoapHeaders ($soapheaders = null) {}

}
