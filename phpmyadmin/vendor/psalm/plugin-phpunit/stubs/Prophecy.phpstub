<?php
namespace Prophecy\Prophecy {
    /** @template T */
    class ObjectProphecy implements ProphecyInterface
    {
        /** @return T */
        public function reveal() {}
    }

    class MethodProphecy
    {
        public function willReturn(...$values): self {}
    }
}

namespace Prophecy {
    use Prophecy\Argument\Token;
    class Argument
    {
        /** @param callable(mixed...):bool $callback */
        public static function that(callable $callback): Token\CallbackToken {}

        /** @param mixed ...$tokens */
        public static function allOf(...$tokens): Token\LogicalAndToken {}
    }
}

namespace Prophecy\PhpUnit {
    use Prophecy\Prophecy\ObjectProphecy;
    trait ProphecyTrait
    {
        /**
         * @template T
         * @param class-string<T> $classOrInterface
         * @return ObjectProphecy<T>
         */
        protected function prophesize($classOrInterface): ObjectProphecy {}
    }
}
