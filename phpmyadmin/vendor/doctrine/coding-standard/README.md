# Doctrine Coding Standard

[![Build Status](https://github.com/doctrine/coding-standard/workflows/Continuous%20Integration/badge.svg)](https://github.com/doctrine/coding-standard/actions?query=workflow%3A%22Continuous+Integration%22+)
[![Continuous Integration](https://github.com/doctrine/coding-standard/workflows/Continuous%20Integration/badge.svg?branch=8.2.x)](https://github.com/doctrine/coding-standard/actions)
[![Total Downloads](https://img.shields.io/packagist/dt/doctrine/coding-standard.svg?style=flat-square)](https://packagist.org/packages/doctrine/coding-standard)
[![Latest Stable Version](https://img.shields.io/packagist/v/doctrine/coding-standard.svg?style=flat-square)](https://packagist.org/packages/doctrine/coding-standard)

The Doctrine Coding Standard is a set of [PHP_CodeSniffer](https://github.com/squizlabs/PHP_CodeSniffer) rules applied to all Doctrine projects. Doctrine Coding Standard is heavily based on [Slevomat Coding Standard](https://github.com/slevomat/coding-standard).

## More resources:

* [Website](https://www.doctrine-project.org/)
* [Documentation](https://www.doctrine-project.org/projects/doctrine-coding-standard/en/latest/)
* [Downloads](https://github.com/doctrine/coding-standard/coding-standard)
