<?xml version="1.0"?>
<ruleset
    name="Doctrine"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="../../vendor/squizlabs/php_codesniffer/phpcs.xsd"
>
    <description>The Doctrine coding standard.</description>

    <!-- Import PSR-12 coding standard (base) -->
    <rule ref="PSR12">
        <!-- checked by SlevomatCodingStandard.Namespaces.UseSpacing -->
        <exclude name="PSR2.Namespaces.UseDeclaration.SpaceAfterLastUse"/>
        <exclude name="PSR12.Files.FileHeader"/>
        <!-- checked by SlevomatCodingStandard.Namespaces.NamespaceSpacing -->
        <exclude name="PSR2.Namespaces.NamespaceDeclaration.BlankLineAfter"/>
        <!-- checked by SlevomatCodingStandard.Operators.SpreadOperatorSpacing -->
        <exclude name="Squiz.Functions.FunctionDeclarationArgumentSpacing.SpacingAfterVariadic"/>
        <!-- checked by SlevomatCodingStandard.TypeHints.ReturnTypeHintSpacing -->
        <exclude name="PSR12.Functions.ReturnTypeDeclaration"/>
        <!-- checked by SlevomatCodingStandard.Classes.TraitUseSpacing -->
        <exclude name="PSR12.Traits.UseDeclaration"/>
        <!-- checked by SlevomatCodingStandard.TypeHints.ReturnTypeHintSpacing.WhitespaceAfterNullabilitySymbol -->
        <exclude name="PSR12.Functions.NullableTypeDeclaration.WhitespaceFound"/>
        <!-- checked by PSR12.ControlStructures.ControlStructureSpacing -->
        <exclude name="PSR2.ControlStructures.ControlStructureSpacing.SpacingAfterOpenBrace"/>
    </rule>

    <!-- Force array element indentation with 4 spaces -->
    <rule ref="Generic.Arrays.ArrayIndent"/>
    <!-- Forbid `array(...)` -->
    <rule ref="Generic.Arrays.DisallowLongArraySyntax"/>
    <!-- Forbid duplicate classes -->
    <rule ref="Generic.Classes.DuplicateClassName"/>
    <!-- Forbid empty statements -->
    <rule ref="Generic.CodeAnalysis.EmptyStatement">
        <!-- But allow empty catch -->
        <exclude name="Generic.CodeAnalysis.EmptyStatement.DetectedCatch"/>
    </rule>
    <!-- Forbid final methods in final classes -->
    <rule ref="Generic.CodeAnalysis.UnnecessaryFinalModifier"/>
    <!-- Forbid useless empty method overrides -->
    <rule ref="Generic.CodeAnalysis.UselessOverridingMethod"/>
    <!-- Forbid inline HTML in PHP code -->
    <rule ref="Generic.Files.InlineHTML"/>
    <!-- Align corresponding assignment statement tokens -->
    <rule ref="Generic.Formatting.MultipleStatementAlignment">
        <properties>
            <property name="error" value="true"/>
        </properties>
    </rule>
    <!-- Force whitespace after a type cast -->
    <rule ref="Generic.Formatting.SpaceAfterCast">
        <properties>
            <property name="spacing" value="1"/>
        </properties>
    </rule>
    <!-- Force whitespace after `!` -->
    <rule ref="Generic.Formatting.SpaceAfterNot"/>
    <!-- Forbid PHP 4 constructors -->
    <rule ref="Generic.NamingConventions.ConstructorName"/>
    <!-- Forbid any content before opening tag -->
    <rule ref="Generic.PHP.CharacterBeforePHPOpeningTag"/>
    <!-- Forbid deprecated functions -->
    <rule ref="Generic.PHP.DeprecatedFunctions"/>
    <!-- Forbid alias functions, i.e. `sizeof()`, `delete()` -->
    <rule ref="Generic.PHP.ForbiddenFunctions">
        <properties>
            <property name="forbiddenFunctions" type="array">
                <element key="chop" value="rtrim"/>
                <element key="close" value="closedir"/>
                <element key="compact" value="null"/>
                <element key="delete" value="unset"/>
                <element key="doubleval" value="floatval"/>
                <element key="extract" value="null"/>
                <element key="fputs" value="fwrite"/>
                <element key="ini_alter" value="ini_set"/>
                <element key="is_double" value="is_float"/>
                <element key="is_integer" value="is_int"/>
                <element key="is_long" value="is_int"/>
                <element key="is_null" value="null"/>
                <element key="is_real" value="is_float"/>
                <element key="is_writeable" value="is_writable"/>
                <element key="join" value="implode"/>
                <element key="key_exists" value="array_key_exists"/>
                <element key="pos" value="current"/>
                <element key="settype" value="null"/>
                <element key="show_source" value="highlight_file"/>
                <element key="sizeof" value="count"/>
                <element key="strchr" value="strstr"/>
                <element key="user_error" value="trigger_error"/>
            </property>
        </properties>
    </rule>
    <!-- Forbid useless inline string concatenation -->
    <rule ref="Generic.Strings.UnnecessaryStringConcat">
        <!-- But multiline is useful for readability -->
        <properties>
            <property name="allowMultiline" value="true"/>
        </properties>
    </rule>
    <!-- Forbid backtick operator -->
    <rule ref="Generic.PHP.BacktickOperator"/>
    <!-- Force PHP 7 param and return types to be lowercased -->
    <rule ref="Generic.PHP.LowerCaseType"/>
    <!-- Forbid `php_sapi_name()` function -->
    <rule ref="Generic.PHP.SAPIUsage"/>
    <!-- Forbid comments starting with # -->
    <rule ref="PEAR.Commenting.InlineComment"/>
    <!-- Disallow else if in favor of elseif -->
    <rule ref="PSR2.ControlStructures.ElseIfDeclaration.NotAllowed">
        <type>error</type>
    </rule>
    <!-- Require that single line arrays have the correct spacing: no space around brackets and one space after comma -->
    <rule ref="SlevomatCodingStandard.Arrays.SingleLineArrayWhitespace"/>
    <!-- Require comma after last element in multi-line array -->
    <rule ref="SlevomatCodingStandard.Arrays.TrailingArrayComma"/>
    <!-- Require presence of constant visibility -->
    <rule ref="SlevomatCodingStandard.Classes.ClassConstantVisibility">
        <properties>
            <property name="fixable" value="true"/>
        </properties>
    </rule>
    <!-- Forbid LSB for constants (static::FOO) -->
    <rule ref="SlevomatCodingStandard.Classes.DisallowLateStaticBindingForConstants"/>
    <!-- Forbid more than one constant declared per statement -->
    <rule ref="SlevomatCodingStandard.Classes.DisallowMultiConstantDefinition"/>
    <!-- Forbid empty lines around type declarations -->
    <rule ref="SlevomatCodingStandard.Classes.EmptyLinesAroundClassBraces">
        <properties>
            <property name="linesCountAfterOpeningBrace" value="0"/>
            <property name="linesCountBeforeClosingBrace" value="0"/>
        </properties>
    </rule>
    <!-- Require usage of ::class instead of __CLASS__, get_class(), get_class($this), get_called_class() and get_parent_class() -->
    <rule ref="SlevomatCodingStandard.Classes.ModernClassNameReference"/>
    <!-- Forbid uses of multiple traits separated by comma -->
    <rule ref="SlevomatCodingStandard.Classes.TraitUseDeclaration"/>
    <!-- Require no spaces before trait use, between trait uses and one space after trait uses -->
    <rule ref="SlevomatCodingStandard.Classes.TraitUseSpacing">
        <properties>
            <property name="linesCountAfterLastUse" value="1"/>
            <property name="linesCountAfterLastUseWhenLastInClass" value="0"/>
            <property name="linesCountBeforeFirstUse" value="0"/>
            <property name="linesCountBetweenUses" value="0"/>
        </properties>
    </rule>
    <!-- Forbid prefix and suffix "Abstract" for abstract classes -->
    <rule ref="SlevomatCodingStandard.Classes.SuperfluousAbstractClassNaming"/>
    <!-- Forbid prefix and suffix "Exception" for exception classes -->
    <rule ref="SlevomatCodingStandard.Classes.SuperfluousExceptionNaming"/>
    <!-- Forbid prefix and suffix "Interface" for interfaces -->
    <rule ref="SlevomatCodingStandard.Classes.SuperfluousInterfaceNaming"/>
    <!-- Forbid suffix "Trait" for traits -->
    <rule ref="SlevomatCodingStandard.Classes.SuperfluousTraitNaming"/>
    <!-- Require specific order of phpDoc annotations with empty newline between specific groups -->
    <rule ref="SlevomatCodingStandard.Commenting.DocCommentSpacing">
        <properties>
            <property name="linesCountBeforeFirstContent" value="0"/>
            <property name="linesCountAfterLastContent" value="0"/>
            <property name="linesCountBetweenDescriptionAndAnnotations" value="1"/>
            <property name="linesCountBetweenAnnotationsGroups" value="1"/>
            <property name="annotationsGroups" type="array">
                <element value="
                    @internal,
                    @deprecated,
                "/>
                <element value="
                    @link,
                    @see,
                    @uses,
                "/>
                <element value="
                    @ORM\,
                    @ODM\,
                    @PHPCR\,
                "/>
                <element value="
                    @param,
                    @psalm-param,
                    @phpstan-param,
                "/>
                <element value="
                    @return,
                    @psalm-return,
                    @phpstan-return,
                "/>
                <element value="@throws"/>
            </property>
        </properties>
    </rule>
    <!-- Forbid useless annotations - Git and LICENCE file provide more accurate information -->
    <rule ref="SlevomatCodingStandard.Commenting.ForbiddenAnnotations">
        <properties>
            <property name="forbiddenAnnotations" type="array">
                <element value="@api"/>
                <element value="@author"/>
                <element value="@category"/>
                <element value="@copyright"/>
                <element value="@created"/>
                <element value="@license"/>
                <element value="@package"/>
                <element value="@since"/>
                <element value="@subpackage"/>
                <element value="@version"/>
            </property>
        </properties>
    </rule>
    <!-- Forbid empty comments -->
    <rule ref="SlevomatCodingStandard.Commenting.EmptyComment"/>
    <!-- Forbid useless comments -->
    <rule ref="SlevomatCodingStandard.Commenting.ForbiddenComments">
        <properties>
            <property name="forbiddenCommentPatterns" type="array">
                <element value="~^(?:(?!private|protected|static)\S+ )?(?:con|de)structor\.\z~i"/>
                <element value="~^Created by .+\.\z~i"/>
                <element value="~^(User|Date|Time): \S+\z~i"/>
                <element value="~^\S+ [gs]etter\.\z~i"/>
                <element value="~^(Class|Interface|Trait) \S+\z~i"/>
            </property>
        </properties>
    </rule>
    <!-- Report invalid format of inline phpDocs with @var -->
    <rule ref="SlevomatCodingStandard.Commenting.InlineDocCommentDeclaration"/>
    <!-- Require comments with single line written as one-liners -->
    <rule ref="SlevomatCodingStandard.Commenting.RequireOneLinePropertyDocComment"/>
    <!-- Forbid assignments in conditions -->
    <rule ref="SlevomatCodingStandard.ControlStructures.AssignmentInCondition"/>
    <!-- Require consistent spacing for block structures -->
    <rule ref="SlevomatCodingStandard.ControlStructures.BlockControlStructureSpacing">
        <exclude name="SlevomatCodingStandard.ControlStructures.BlockControlStructureSpacing.IncorrectLinesCountBeforeControlStructure" />
        <exclude name="SlevomatCodingStandard.ControlStructures.BlockControlStructureSpacing.IncorrectLinesCountBeforeFirstControlStructure" />
        <properties>
            <property name="controlStructures" type="array">
                <element value="if" />
                <element value="do" />
                <element value="while" />
                <element value="for" />
                <element value="foreach" />
                <element value="switch" />
                <element value="try" />
                <element value="default" />
            </property>
        </properties>
    </rule>
    <!-- Forbid fancy yoda conditions -->
    <rule ref="SlevomatCodingStandard.ControlStructures.DisallowYodaComparison"/>
    <!-- Require usage of early exit -->
    <rule ref="SlevomatCodingStandard.ControlStructures.EarlyExit"/>
    <!-- Require consistent spacing for jump statements -->
    <rule ref="SlevomatCodingStandard.ControlStructures.JumpStatementsSpacing">
        <properties>
            <property name="linesCountBeforeWhenFirstInCaseOrDefault" value="0"/>
            <property name="linesCountAfterWhenLastInCaseOrDefault" value="1"/>
            <property name="linesCountAfterWhenLastInLastCaseOrDefault" value="0"/>
            <property name="jumpStatements" type="array">
                <element value="return" />
                <element value="throw" />
                <element value="yield" />
                <element value="yield_from" />
            </property>
        </properties>
    </rule>
    <!-- Require language constructs without parentheses -->
    <rule ref="SlevomatCodingStandard.ControlStructures.LanguageConstructWithParentheses"/>
    <!-- Require usage of null coalesce operator equal operator when possible -->
    <rule ref="SlevomatCodingStandard.ControlStructures.RequireNullCoalesceEqualOperator"/>
    <!-- Require usage of null coalesce operator when possible -->
    <rule ref="SlevomatCodingStandard.ControlStructures.RequireNullCoalesceOperator"/>
    <!-- Forbid usage of conditions when a simple return can be used -->
    <rule ref="SlevomatCodingStandard.ControlStructures.UselessIfConditionWithReturn"/>
    <!-- Forbid usage of boolean-only ternary operator usage (e.g. $foo ? true : false) -->
    <rule ref="SlevomatCodingStandard.ControlStructures.UselessTernaryOperator"/>
    <!-- Forbid useless unreachable catch blocks -->
    <rule ref="SlevomatCodingStandard.Exceptions.DeadCatch"/>
    <!-- Require using Throwable instead of Exception -->
    <rule ref="SlevomatCodingStandard.Exceptions.ReferenceThrowableOnly"/>
    <!-- Ensure Arrow Functions declaration format -->
    <rule ref="SlevomatCodingStandard.Functions.ArrowFunctionDeclaration">
        <properties>
            <property name="spacesCountAfterKeyword" value="1"/>
            <property name="spacesCountBeforeArrow" value="1"/>
            <property name="spacesCountAfterArrow" value="1"/>
        </properties>
    </rule>
    <!-- Require closures not referencing $this be static -->
    <rule ref="SlevomatCodingStandard.Functions.StaticClosure"/>
    <!-- Forbid unused variables passed to closures via `use` -->
    <rule ref="SlevomatCodingStandard.Functions.UnusedInheritedVariablePassedToClosure"/>
    <!-- Require use statements to be alphabetically sorted -->
    <rule ref="SlevomatCodingStandard.Namespaces.AlphabeticallySortedUses"/>
    <!-- Forbid fancy group uses -->
    <rule ref="SlevomatCodingStandard.Namespaces.DisallowGroupUse"/>
    <!-- Forbid multiple use statements on same line -->
    <rule ref="SlevomatCodingStandard.Namespaces.MultipleUsesPerLine"/>
    <!-- Require newlines around namespace declaration -->
    <rule ref="SlevomatCodingStandard.Namespaces.NamespaceSpacing"/>
    <!-- Forbid using absolute class name references (except global ones) -->
    <rule ref="SlevomatCodingStandard.Namespaces.ReferenceUsedNamesOnly">
        <properties>
            <property name="allowFallbackGlobalConstants" value="false"/>
            <property name="allowFallbackGlobalFunctions" value="false"/>
            <property name="allowFullyQualifiedGlobalClasses" value="false"/>
            <property name="allowFullyQualifiedGlobalConstants" value="false"/>
            <property name="allowFullyQualifiedGlobalFunctions" value="false"/>
            <property name="allowFullyQualifiedNameForCollidingClasses" value="true"/>
            <property name="allowFullyQualifiedNameForCollidingConstants" value="true"/>
            <property name="allowFullyQualifiedNameForCollidingFunctions" value="true"/>
            <property name="searchAnnotations" value="true"/>
        </properties>
    </rule>
    <!-- Forbid unused use statements -->
    <rule ref="SlevomatCodingStandard.Namespaces.UnusedUses">
        <properties>
            <property name="searchAnnotations" value="true"/>
        </properties>
    </rule>
    <!-- Forbid superfluous leading backslash in use statements -->
    <rule ref="SlevomatCodingStandard.Namespaces.UseDoesNotStartWithBackslash"/>
    <!-- Forbid useless uses of the same namespace -->
    <rule ref="SlevomatCodingStandard.Namespaces.UseFromSameNamespace"/>
    <!-- Require empty newlines before and after uses -->
    <rule ref="SlevomatCodingStandard.Namespaces.UseSpacing">
        <properties>
            <property name="linesCountAfterLastUse" value="1"/>
            <property name="linesCountBeforeFirstUse" value="1"/>
            <property name="linesCountBetweenUseTypes" value="1"/>
        </properties>
    </rule>
    <!-- Forbid useless alias for classes, constants and functions -->
    <rule ref="SlevomatCodingStandard.Namespaces.UselessAlias"/>
    <!-- Forbid weak comparisons -->
    <rule ref="SlevomatCodingStandard.Operators.DisallowEqualOperators"/>
    <!-- Forbid spacing before the negative operator `-` -->
    <rule ref="SlevomatCodingStandard.Operators.NegationOperatorSpacing"/>
    <!-- Require the usage of assignment operators, eg `+=`, `.=` when possible -->
    <rule ref="SlevomatCodingStandard.Operators.RequireCombinedAssignmentOperator"/>
    <!-- Require no spacing after spread operator -->
    <rule ref="SlevomatCodingStandard.Operators.SpreadOperatorSpacing"/>
    <!-- Forbid argument unpacking for functions specialized by PHP VM -->
    <rule ref="SlevomatCodingStandard.PHP.OptimizedFunctionsWithoutUnpacking"/>
    <!-- Forbid `list(...)` syntax -->
    <rule ref="SlevomatCodingStandard.PHP.ShortList"/>
    <!-- Forbid use of longhand cast operators -->
    <rule ref="SlevomatCodingStandard.PHP.TypeCast"/>
    <!-- Require presence of declare(strict_types=1) -->
    <rule ref="SlevomatCodingStandard.TypeHints.DeclareStrictTypes">
        <properties>
            <property
                name="declareOnFirstLine"
                value="false"
            />
            <property
                name="linesCountBeforeDeclare"
                value="1"
            />
            <property
                name="spacesCountAroundEqualsSign"
                value="0"
            />
            <property
                name="linesCountAfterDeclare"
                value="1"
            />
        </properties>
    </rule>
    <!-- Forbid useless parentheses -->
    <rule ref="SlevomatCodingStandard.PHP.UselessParentheses"/>
    <!-- Forbid useless semicolon `;` -->
    <rule ref="SlevomatCodingStandard.PHP.UselessSemicolon"/>
    <!-- Require /* @var type $foo */ and similar simple inline annotations to be replaced by assert() -->
    <rule ref="SlevomatCodingStandard.PHP.RequireExplicitAssertion"/>
    <!-- Require use of short versions of scalar types (i.e. int instead of integer) -->
    <rule ref="SlevomatCodingStandard.TypeHints.LongTypeHints"/>
    <!-- Require the `null` type hint to be in the last position of annotations -->
    <rule ref="SlevomatCodingStandard.TypeHints.NullTypeHintOnLastPosition"/>
    <!-- Require ? when default value is null -->
    <rule ref="SlevomatCodingStandard.TypeHints.NullableTypeForNullDefaultValue"/>
    <!-- Require one space between typehint and variable, require no space between nullability sign and typehint -->
    <rule ref="SlevomatCodingStandard.TypeHints.ParameterTypeHintSpacing"/>
    <!-- Require space around colon in return types -->
    <rule ref="SlevomatCodingStandard.TypeHints.ReturnTypeHintSpacing">
        <properties>
            <property name="spacesCountBeforeColon" value="0"/>
        </properties>
    </rule>
    <!-- Require types to be written as natively if possible;
         require iterable types to specify phpDoc with their content;
         forbid useless/duplicated information in phpDoc -->
    <rule ref="SlevomatCodingStandard.TypeHints.ParameterTypeHint">
        <properties>
            <property name="traversableTypeHints" type="array">
                <element value="Traversable"/>
                <element value="Iterator"/>
                <element value="IteratorAggregate"/>
                <element value="Doctrine\Common\Collections\Collection"/>
            </property>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.TypeHints.PropertyTypeHint">
        <properties>
            <property name="traversableTypeHints" type="array">
                <element value="Traversable"/>
                <element value="Iterator"/>
                <element value="IteratorAggregate"/>
                <element value="Doctrine\Common\Collections\Collection"/>
            </property>
        </properties>
    </rule>
    <rule ref="SlevomatCodingStandard.TypeHints.PropertyTypeHintSpacing"/>
    <rule ref="SlevomatCodingStandard.TypeHints.ReturnTypeHint">
        <properties>
            <property name="traversableTypeHints" type="array">
                <element value="Traversable"/>
                <element value="Iterator"/>
                <element value="IteratorAggregate"/>
                <element value="Doctrine\Common\Collections\Collection"/>
            </property>
        </properties>
    </rule>
    <!-- Forbid useless @var for constants -->
    <rule ref="SlevomatCodingStandard.TypeHints.UselessConstantTypeHint"/>
    <!-- Forbid useless phpDocs for functions -->
    <rule ref="SlevomatCodingStandard.Commenting.UselessFunctionDocComment">
        <properties>
            <property name="traversableTypeHints" type="array">
                <element value="Traversable"/>
                <element value="Iterator"/>
                <element value="IteratorAggregate"/>
                <element value="Doctrine\Common\Collections\Collection"/>
            </property>
        </properties>
    </rule>
    <!-- Forbid useless inherit doc comment -->
    <rule ref="SlevomatCodingStandard.Commenting.UselessInheritDocComment"/>
    <!-- Forbid duplicated variables assignments -->
    <rule ref="SlevomatCodingStandard.Variables.DuplicateAssignmentToVariable"/>
    <!-- Forbid useless variables -->
    <rule ref="SlevomatCodingStandard.Variables.UselessVariable"/>
    <!-- Forbid spaces around square brackets -->
    <rule ref="Squiz.Arrays.ArrayBracketSpacing"/>
    <!-- Force array declaration structure -->
    <rule ref="Squiz.Arrays.ArrayDeclaration">
        <!-- Disable arrow alignment -->
        <exclude name="Squiz.Arrays.ArrayDeclaration.DoubleArrowNotAligned"/>
        <!-- Uses indentation of only single space -->
        <exclude name="Squiz.Arrays.ArrayDeclaration.KeyNotAligned"/>
        <!-- Allow multiple values on a single line -->
        <exclude name="Squiz.Arrays.ArrayDeclaration.SingleLineNotAllowed"/>
        <!-- Disable alignment of braces -->
        <exclude name="Squiz.Arrays.ArrayDeclaration.CloseBraceNotAligned"/>
        <!-- Disable alignment of values with opening brace -->
        <exclude name="Squiz.Arrays.ArrayDeclaration.ValueNotAligned"/>
        <!-- Checked by SlevomatCodingStandard.Arrays.TrailingArrayComma.MissingTrailingComma -->
        <exclude name="Squiz.Arrays.ArrayDeclaration.NoCommaAfterLast"/>
        <exclude name="Squiz.Arrays.ArrayDeclaration.NoComma"/>
    </rule>
    <!-- Forbid class being in a file with different name -->
    <rule ref="Squiz.Classes.ClassFileName"/>
    <!-- Force `self::` for self-reference, force lower-case self, forbid spaces around `::` -->
    <rule ref="Squiz.Classes.SelfMemberReference"/>
    <!-- Force phpDoc alignment -->
    <rule ref="Squiz.Commenting.DocCommentAlignment">
        <!-- Allow extra spaces after star, i.e. for indented annotations -->
        <exclude name="Squiz.Commenting.DocCommentAlignment.SpaceAfterStar"/>
    </rule>
    <!-- Force rules for function phpDoc -->
    <rule ref="Squiz.Commenting.FunctionComment">
        <!-- Allow `@throws` without description -->
        <exclude name="Squiz.Commenting.FunctionComment.EmptyThrows"/>
        <!-- Does not work properly with PHP 7 / short-named types -->
        <exclude name="Squiz.Commenting.FunctionComment.IncorrectParamVarName"/>
        <!-- Does not support collections, i.e. `string[]` -->
        <exclude name="Squiz.Commenting.FunctionComment.IncorrectTypeHint"/>
        <!-- Forces incorrect types -->
        <exclude name="Squiz.Commenting.FunctionComment.InvalidReturn"/>
        <!-- Breaks with compound return types, i.e. `string|null` -->
        <exclude name="Squiz.Commenting.FunctionComment.InvalidReturnNotVoid"/>
        <!-- Breaks when all params are not documented -->
        <exclude name="Squiz.Commenting.FunctionComment.InvalidTypeHint"/>
        <!-- Doc comment is not required for every method -->
        <exclude name="Squiz.Commenting.FunctionComment.Missing"/>
        <!-- Do not require comments for `@param` -->
        <exclude name="Squiz.Commenting.FunctionComment.MissingParamComment"/>
        <!-- Do not require `@param` for all parameters -->
        <exclude name="Squiz.Commenting.FunctionComment.MissingParamTag"/>
        <!-- Do not require `@return` for void methods -->
        <exclude name="Squiz.Commenting.FunctionComment.MissingReturn"/>
        <!-- Comments don't have to be sentences -->
        <exclude name="Squiz.Commenting.FunctionComment.ParamCommentFullStop"/>
        <!-- Comments don't have to be sentences -->
        <exclude name="Squiz.Commenting.FunctionComment.ParamCommentNotCapital"/>
        <!-- Breaks when all params are not documented -->
        <exclude name="Squiz.Commenting.FunctionComment.ParamNameNoMatch"/>
        <!-- Doesn't respect inheritance -->
        <exclude name="Squiz.Commenting.FunctionComment.ScalarTypeHintMissing"/>
        <!-- `@throws` lines can often be read as a sentence,
             i.e. `@throws RuntimeException if the file could not be written.` -->
        <exclude name="Squiz.Commenting.FunctionComment.ThrowsNotCapital"/>
        <!-- Doesn't work with self as typehint -->
        <exclude name="Squiz.Commenting.FunctionComment.TypeHintMissing"/>
    </rule>
    <!-- Forbid global functions -->
    <rule ref="Squiz.Functions.GlobalFunction"/>
    <!-- Force camelCase variables -->
    <rule ref="Squiz.NamingConventions.ValidVariableName">
        <exclude name="Squiz.NamingConventions.ValidVariableName.PrivateNoUnderscore" />
    </rule>
    <!-- Forbid `AND` and `OR`, require `&&` and `||` -->
    <rule ref="Squiz.Operators.ValidLogicalOperators"/>
    <!-- Forbid `global` -->
    <rule ref="Squiz.PHP.GlobalKeyword"/>
    <!-- Forbid functions inside functions -->
    <rule ref="Squiz.PHP.InnerFunctions"/>
    <!-- Require PHP function calls in lowercase -->
    <rule ref="Squiz.PHP.LowercasePHPFunctions"/>
    <!-- Forbid dead code -->
    <rule ref="Squiz.PHP.NonExecutableCode"/>
    <!-- Forbid `$this` inside static function -->
    <rule ref="Squiz.Scope.StaticThisUsage"/>
    <!-- Force whitespace before and after concatenation -->
    <rule ref="Squiz.Strings.ConcatenationSpacing">
        <properties>
            <property name="spacing" value="1"/>
            <property name="ignoreNewlines" value="true"/>
        </properties>
    </rule>
    <!-- Forbid strings in `"` unless necessary -->
    <rule ref="Squiz.Strings.DoubleQuoteUsage"/>
    <rule ref="Squiz.Strings.DoubleQuoteUsage.ContainsVar">
        <message>Variable "%s" not allowed in double quoted string; use sprintf() or concatenation instead</message>
    </rule>
    <!-- Forbid braces around string in `echo` -->
    <rule ref="Squiz.Strings.EchoedStrings"/>
    <!-- Forbid spaces in type casts -->
    <rule ref="Squiz.WhiteSpace.CastSpacing"/>
    <!-- Forbid blank line after function opening brace -->
    <rule ref="Squiz.WhiteSpace.FunctionOpeningBraceSpace"/>
    <!-- Require 1 line before and after function, except at the top and bottom -->
    <rule ref="Squiz.WhiteSpace.FunctionSpacing">
        <properties>
            <property name="spacing" value="1" />
            <property name="spacingBeforeFirst" value="0"/>
            <property name="spacingAfterLast" value="0"/>
        </properties>
    </rule>
    <!-- Require there be no space between increment/decrement operator and its operand -->
    <rule ref="Generic.WhiteSpace.IncrementDecrementSpacing"/>
    <!-- Require space after language constructs -->
    <rule ref="Squiz.WhiteSpace.LanguageConstructSpacing"/>
    <!-- Require space around logical operators -->
    <rule ref="Squiz.WhiteSpace.LogicalOperatorSpacing"/>
    <!-- Forbid spaces around `->` operator -->
    <rule ref="Squiz.WhiteSpace.ObjectOperatorSpacing">
        <properties>
            <property name="ignoreNewlines" value="true"/>
        </properties>
    </rule>
    <!-- Forbid spaces before semicolon `;` -->
    <rule ref="Squiz.WhiteSpace.SemicolonSpacing"/>
    <!-- Forbid superfluous whitespaces -->
    <rule ref="Squiz.WhiteSpace.SuperfluousWhitespace">
        <properties>
            <!-- turned on by PSR-12 -> turning back off -->
            <property name="ignoreBlankLines" value="false"/>
        </properties>
    </rule>
    <rule ref="Squiz.WhiteSpace.SuperfluousWhitespace.EmptyLines">
        <!-- turned off by PSR-12 -> turning back on -->
        <severity>5</severity>
    </rule>
</ruleset>
