<?php

return [
    'apache_getenv',
    'apache_get_version',
    'apache_request_headers',
    'apache_reset_timeout',
    'apache_response_headers',
    'apache_setenv',
    'apcu_cache_info',
    'apcu_cas',
    'apcu_dec',
    'apcu_fetch',
    'apcu_inc',
    'apcu_sma_info',
    'apc_fetch',
    'array_combine',
    'array_flip',
    'array_replace',
    'array_replace_recursive',
    'array_walk_recursive',
    'arsort',
    'asort',
    'base64_decode',
    'bzclose',
    'bzflush',
    'bzread',
    'bzwrite',
    'chdir',
    'chgrp',
    'chmod',
    'chown',
    'chroot',
    'class_alias',
    'class_implements',
    'class_parents',
    'class_uses',
    'cli_set_process_title',
    'closelog',
    'com_event_sink',
    'com_load_typelib',
    'com_print_typeinfo',
    'convert_uudecode',
    'convert_uuencode',
    'copy',
    'create_function',
    'cubrid_free_result',
    'cubrid_get_charset',
    'cubrid_get_client_info',
    'cubrid_get_db_parameter',
    'cubrid_get_server_info',
    'cubrid_insert_id',
    'cubrid_lob2_new',
    'cubrid_lob2_size',
    'cubrid_lob2_size64',
    'cubrid_lob2_tell',
    'cubrid_lob2_tell64',
    'cubrid_set_db_parameter',
    'curl_escape',
    'curl_exec',
    'curl_getinfo',
    'curl_init',
    'curl_multi_errno',
    'curl_multi_info_read',
    'curl_multi_init',
    'curl_setopt',
    'curl_share_errno',
    'curl_share_setopt',
    'curl_unescape',
    'date',
    'date_parse',
    'date_parse_from_format',
    'date_sunrise',
    'date_sunset',
    'date_sun_info',
    'db2_autocommit',
    'db2_bind_param',
    'db2_client_info',
    'db2_close',
    'db2_commit',
    'db2_execute',
    'db2_free_result',
    'db2_free_stmt',
    'db2_get_option',
    'db2_pclose',
    'db2_rollback',
    'db2_server_info',
    'db2_set_option',
    'define',
    'deflate_add',
    'deflate_init',
    'disk_free_space',
    'disk_total_space',
    'dl',
    'dns_get_record',
    'eio_busy',
    'eio_chmod',
    'eio_chown',
    'eio_close',
    'eio_custom',
    'eio_dup2',
    'eio_event_loop',
    'eio_fallocate',
    'eio_fchmod',
    'eio_fdatasync',
    'eio_fstat',
    'eio_fstatvfs',
    'eio_fsync',
    'eio_ftruncate',
    'eio_futime',
    'eio_grp',
    'eio_lstat',
    'eio_mkdir',
    'eio_mknod',
    'eio_nop',
    'eio_readahead',
    'eio_readdir',
    'eio_readlink',
    'eio_rename',
    'eio_rmdir',
    'eio_seek',
    'eio_sendfile',
    'eio_stat',
    'eio_statvfs',
    'eio_symlink',
    'eio_sync',
    'eio_syncfs',
    'eio_sync_file_range',
    'eio_truncate',
    'eio_unlink',
    'eio_utime',
    'eio_write',
    'error_log',
    'fastcgi_finish_request',
    'fbird_blob_cancel',
    'fclose',
    'fflush',
    'file',
    'fileatime',
    'filectime',
    'fileinode',
    'filemtime',
    'fileowner',
    'filesize',
    'file_get_contents',
    'file_put_contents',
    'filter_input_array',
    'filter_var_array',
    'finfo_close',
    'finfo_open',
    'flock',
    'fopen',
    'fputcsv',
    'fread',
    'fsockopen',
    'ftp_alloc',
    'ftp_append',
    'ftp_cdup',
    'ftp_chdir',
    'ftp_chmod',
    'ftp_close',
    'ftp_connect',
    'ftp_delete',
    'ftp_fget',
    'ftp_fput',
    'ftp_get',
    'ftp_login',
    'ftp_mkdir',
    'ftp_mlsd',
    'ftp_nlist',
    'ftp_pasv',
    'ftp_put',
    'ftp_pwd',
    'ftp_rename',
    'ftp_rmdir',
    'ftp_site',
    'ftp_ssl_connect',
    'ftp_systype',
    'ftruncate',
    'fwrite',
    'getallheaders',
    'getcwd',
    'gethostname',
    'getimagesize',
    'getlastmod',
    'getmygid',
    'getmyinode',
    'getmypid',
    'getmyuid',
    'getopt',
    'getprotobyname',
    'getprotobynumber',
    'get_headers',
    'glob',
    'gmdate',
    'gmp_binomial',
    'gmp_export',
    'gmp_import',
    'gmp_random_seed',
    'gnupg_adddecryptkey',
    'gnupg_addencryptkey',
    'gnupg_addsignkey',
    'gnupg_cleardecryptkeys',
    'gnupg_clearencryptkeys',
    'gnupg_clearsignkeys',
    'gnupg_setarmor',
    'gnupg_setsignmode',
    'gzclose',
    'gzcompress',
    'gzdecode',
    'gzdeflate',
    'gzencode',
    'gzgets',
    'gzgetss',
    'gzinflate',
    'gzpassthru',
    'gzrewind',
    'gzuncompress',
    'hash_hkdf',
    'hash_update_file',
    'header_register_callback',
    'hex2bin',
    'highlight_file',
    'highlight_string',
    'ibase_add_user',
    'ibase_backup',
    'ibase_blob_cancel',
    'ibase_blob_create',
    'ibase_blob_get',
    'ibase_close',
    'ibase_commit',
    'ibase_commit_ret',
    'ibase_connect',
    'ibase_delete_user',
    'ibase_drop_db',
    'ibase_free_event_handler',
    'ibase_free_query',
    'ibase_free_result',
    'ibase_maintain_db',
    'ibase_modify_user',
    'ibase_name_result',
    'ibase_pconnect',
    'ibase_restore',
    'ibase_rollback',
    'ibase_rollback_ret',
    'ibase_service_attach',
    'ibase_service_detach',
    'iconv',
    'iconv_get_encoding',
    'iconv_set_encoding',
    'image2wbmp',
    'imageaffine',
    'imageaffinematrixconcat',
    'imageaffinematrixget',
    'imagealphablending',
    'imageantialias',
    'imagearc',
    'imagebmp',
    'imagechar',
    'imagecharup',
    'imagecolorat',
    'imagecolordeallocate',
    'imagecolormatch',
    'imageconvolution',
    'imagecopy',
    'imagecopymerge',
    'imagecopymergegray',
    'imagecopyresampled',
    'imagecopyresized',
    'imagecreate',
    'imagecreatefrombmp',
    'imagecreatefromgd',
    'imagecreatefromgd2',
    'imagecreatefromgd2part',
    'imagecreatefromgif',
    'imagecreatefromjpeg',
    'imagecreatefrompng',
    'imagecreatefromwbmp',
    'imagecreatefromwebp',
    'imagecreatefromxbm',
    'imagecreatefromxpm',
    'imagecreatetruecolor',
    'imagecrop',
    'imagecropauto',
    'imagedashedline',
    'imagedestroy',
    'imageellipse',
    'imagefill',
    'imagefilledarc',
    'imagefilledellipse',
    'imagefilledpolygon',
    'imagefilledrectangle',
    'imagefilltoborder',
    'imagefilter',
    'imageflip',
    'imagegammacorrect',
    'imagegd',
    'imagegd2',
    'imagegif',
    'imagegrabscreen',
    'imagegrabwindow',
    'imagejpeg',
    'imagelayereffect',
    'imageline',
    'imageloadfont',
    'imageopenpolygon',
    'imagepng',
    'imagepolygon',
    'imagerectangle',
    'imagerotate',
    'imagesavealpha',
    'imagescale',
    'imagesetbrush',
    'imagesetclip',
    'imagesetinterpolation',
    'imagesetpixel',
    'imagesetstyle',
    'imagesetthickness',
    'imagesettile',
    'imagestring',
    'imagestringup',
    'imagesx',
    'imagesy',
    'imagetruecolortopalette',
    'imagettfbbox',
    'imagettftext',
    'imagewbmp',
    'imagewebp',
    'imagexbm',
    'imap_append',
    'imap_check',
    'imap_clearflag_full',
    'imap_close',
    'imap_createmailbox',
    'imap_deletemailbox',
    'imap_fetchstructure',
    'imap_gc',
    'imap_headerinfo',
    'imap_mail',
    'imap_mailboxmsginfo',
    'imap_mail_compose',
    'imap_mail_copy',
    'imap_mail_move',
    'imap_mutf7_to_utf8',
    'imap_num_msg',
    'imap_open',
    'imap_renamemailbox',
    'imap_savebody',
    'imap_setacl',
    'imap_setflag_full',
    'imap_set_quota',
    'imap_sort',
    'imap_subscribe',
    'imap_thread',
    'imap_timeout',
    'imap_undelete',
    'imap_unsubscribe',
    'imap_utf8_to_mutf7',
    'inet_ntop',
    'inflate_add',
    'inflate_get_read_len',
    'inflate_get_status',
    'inflate_init',
    'ingres_autocommit',
    'ingres_close',
    'ingres_commit',
    'ingres_connect',
    'ingres_execute',
    'ingres_field_name',
    'ingres_field_type',
    'ingres_free_result',
    'ingres_pconnect',
    'ingres_result_seek',
    'ingres_rollback',
    'ingres_set_environment',
    'ini_get',
    'ini_set',
    'inotify_init',
    'inotify_rm_watch',
    'iptcembed',
    'iptcparse',
    'jdtounix',
    'jpeg2wbmp',
    'json_decode',
    'json_encode',
    'json_last_error_msg',
    'krsort',
    'ksort',
    'lchgrp',
    'lchown',
    'ldap_add',
    'ldap_add_ext',
    'ldap_bind',
    'ldap_bind_ext',
    'ldap_control_paged_result',
    'ldap_control_paged_result_response',
    'ldap_count_entries',
    'ldap_delete',
    'ldap_delete_ext',
    'ldap_exop',
    'ldap_exop_passwd',
    'ldap_exop_whoami',
    'ldap_explode_dn',
    'ldap_first_attribute',
    'ldap_first_entry',
    'ldap_free_result',
    'ldap_get_attributes',
    'ldap_get_dn',
    'ldap_get_entries',
    'ldap_get_option',
    'ldap_get_values',
    'ldap_get_values_len',
    'ldap_list',
    'ldap_modify_batch',
    'ldap_mod_add',
    'ldap_mod_add_ext',
    'ldap_mod_del',
    'ldap_mod_del_ext',
    'ldap_mod_replace',
    'ldap_mod_replace_ext',
    'ldap_next_attribute',
    'ldap_parse_exop',
    'ldap_parse_result',
    'ldap_read',
    'ldap_rename',
    'ldap_rename_ext',
    'ldap_sasl_bind',
    'ldap_search',
    'ldap_set_option',
    'ldap_unbind',
    'libxml_get_last_error',
    'libxml_set_external_entity_loader',
    'link',
    'lzf_compress',
    'lzf_decompress',
    'mailparse_msg_extract_part_file',
    'mailparse_msg_free',
    'mailparse_msg_parse',
    'mailparse_msg_parse_file',
    'mailparse_stream_encode',
    'mb_chr',
    'mb_detect_order',
    'mb_encoding_aliases',
    'mb_eregi_replace',
    'mb_ereg_replace',
    'mb_ereg_replace_callback',
    'mb_ereg_search_getregs',
    'mb_ereg_search_init',
    'mb_ereg_search_regs',
    'mb_ereg_search_setpos',
    'mb_http_output',
    'mb_internal_encoding',
    'mb_ord',
    'mb_parse_str',
    'mb_regex_encoding',
    'mb_send_mail',
    'mb_split',
    'mb_str_split',
    'md5_file',
    'metaphone',
    'mime_content_type',
    'mkdir',
    'mktime',
    'msg_queue_exists',
    'msg_receive',
    'msg_remove_queue',
    'msg_send',
    'msg_set_queue',
    'msql_affected_rows',
    'msql_close',
    'msql_connect',
    'msql_create_db',
    'msql_data_seek',
    'msql_db_query',
    'msql_drop_db',
    'msql_field_len',
    'msql_field_name',
    'msql_field_seek',
    'msql_field_table',
    'msql_field_type',
    'msql_free_result',
    'msql_pconnect',
    'msql_query',
    'msql_select_db',
    'mysqli_get_cache_stats',
    'mysqli_get_client_stats',
    'mysqlnd_ms_dump_servers',
    'mysqlnd_ms_fabric_select_global',
    'mysqlnd_ms_fabric_select_shard',
    'mysqlnd_ms_get_last_used_connection',
    'mysqlnd_qc_clear_cache',
    'mysqlnd_qc_set_is_select',
    'mysqlnd_qc_set_storage_handler',
    'mysql_close',
    'mysql_connect',
    'mysql_create_db',
    'mysql_data_seek',
    'mysql_db_name',
    'mysql_db_query',
    'mysql_drop_db',
    'mysql_fetch_lengths',
    'mysql_field_flags',
    'mysql_field_len',
    'mysql_field_name',
    'mysql_field_seek',
    'mysql_free_result',
    'mysql_get_host_info',
    'mysql_get_proto_info',
    'mysql_get_server_info',
    'mysql_info',
    'mysql_list_dbs',
    'mysql_list_fields',
    'mysql_list_processes',
    'mysql_list_tables',
    'mysql_num_fields',
    'mysql_num_rows',
    'mysql_query',
    'mysql_real_escape_string',
    'mysql_result',
    'mysql_select_db',
    'mysql_set_charset',
    'mysql_tablename',
    'mysql_thread_id',
    'mysql_unbuffered_query',
    'natcasesort',
    'natsort',
    'ob_end_clean',
    'ob_end_flush',
    'oci_bind_array_by_name',
    'oci_bind_by_name',
    'oci_cancel',
    'oci_close',
    'oci_commit',
    'oci_connect',
    'oci_define_by_name',
    'oci_execute',
    'oci_fetch_all',
    'oci_field_name',
    'oci_field_precision',
    'oci_field_scale',
    'oci_field_size',
    'oci_field_type',
    'oci_field_type_raw',
    'oci_free_descriptor',
    'oci_free_statement',
    'oci_new_collection',
    'oci_new_connect',
    'oci_new_cursor',
    'oci_new_descriptor',
    'oci_num_fields',
    'oci_num_rows',
    'oci_parse',
    'oci_pconnect',
    'oci_result',
    'oci_rollback',
    'oci_server_version',
    'oci_set_action',
    'oci_set_call_timeout',
    'oci_set_client_identifier',
    'oci_set_client_info',
    'oci_set_db_operation',
    'oci_set_edition',
    'oci_set_module_name',
    'oci_set_prefetch',
    'oci_statement_type',
    'oci_unregister_taf_callback',
    'odbc_autocommit',
    'odbc_binmode',
    'odbc_columnprivileges',
    'odbc_columns',
    'odbc_commit',
    'odbc_data_source',
    'odbc_exec',
    'odbc_execute',
    'odbc_fetch_into',
    'odbc_field_len',
    'odbc_field_name',
    'odbc_field_num',
    'odbc_field_scale',
    'odbc_field_type',
    'odbc_foreignkeys',
    'odbc_gettypeinfo',
    'odbc_longreadlen',
    'odbc_prepare',
    'odbc_primarykeys',
    'odbc_result',
    'odbc_result_all',
    'odbc_rollback',
    'odbc_setoption',
    'odbc_specialcolumns',
    'odbc_statistics',
    'odbc_tableprivileges',
    'odbc_tables',
    'opcache_compile_file',
    'opcache_get_status',
    'opendir',
    'openlog',
    'openssl_cipher_iv_length',
    'openssl_csr_export',
    'openssl_csr_export_to_file',
    'openssl_csr_get_subject',
    'openssl_csr_new',
    'openssl_csr_sign',
    'openssl_decrypt',
    'openssl_dh_compute_key',
    'openssl_digest',
    'openssl_encrypt',
    'openssl_open',
    'openssl_pbkdf2',
    'openssl_pkcs7_decrypt',
    'openssl_pkcs7_encrypt',
    'openssl_pkcs7_read',
    'openssl_pkcs7_sign',
    'openssl_pkcs12_export',
    'openssl_pkcs12_export_to_file',
    'openssl_pkcs12_read',
    'openssl_pkey_export',
    'openssl_pkey_export_to_file',
    'openssl_pkey_get_private',
    'openssl_pkey_get_public',
    'openssl_pkey_new',
    'openssl_private_decrypt',
    'openssl_private_encrypt',
    'openssl_public_decrypt',
    'openssl_public_encrypt',
    'openssl_random_pseudo_bytes',
    'openssl_seal',
    'openssl_sign',
    'openssl_x509_export',
    'openssl_x509_export_to_file',
    'openssl_x509_fingerprint',
    'openssl_x509_read',
    'output_add_rewrite_var',
    'output_reset_rewrite_vars',
    'pack',
    'parse_ini_file',
    'parse_ini_string',
    'parse_url',
    'password_hash',
    'pcntl_exec',
    'pcntl_getpriority',
    'pcntl_setpriority',
    'pcntl_signal_dispatch',
    'pcntl_sigprocmask',
    'pcntl_strerror',
    'PDF_activate_item',
    'PDF_add_locallink',
    'PDF_add_nameddest',
    'PDF_add_note',
    'PDF_add_pdflink',
    'PDF_add_thumbnail',
    'PDF_add_weblink',
    'PDF_attach_file',
    'PDF_begin_layer',
    'PDF_begin_page',
    'PDF_begin_page_ext',
    'PDF_circle',
    'PDF_clip',
    'PDF_close',
    'PDF_closepath',
    'PDF_closepath_fill_stroke',
    'PDF_closepath_stroke',
    'PDF_close_pdi',
    'PDF_close_pdi_page',
    'PDF_concat',
    'PDF_continue_text',
    'PDF_curveto',
    'PDF_delete',
    'PDF_end_layer',
    'PDF_end_page',
    'PDF_end_page_ext',
    'PDF_end_pattern',
    'PDF_end_template',
    'PDF_fill',
    'PDF_fill_stroke',
    'PDF_fit_image',
    'PDF_fit_pdi_page',
    'PDF_fit_textline',
    'PDF_initgraphics',
    'PDF_lineto',
    'PDF_makespotcolor',
    'PDF_moveto',
    'PDF_open_file',
    'PDF_place_image',
    'PDF_place_pdi_page',
    'PDF_rect',
    'PDF_restore',
    'PDF_rotate',
    'PDF_save',
    'PDF_scale',
    'PDF_setcolor',
    'PDF_setdash',
    'PDF_setdashpattern',
    'PDF_setflat',
    'PDF_setfont',
    'PDF_setgray',
    'PDF_setgray_fill',
    'PDF_setgray_stroke',
    'PDF_setlinejoin',
    'PDF_setlinewidth',
    'PDF_setmatrix',
    'PDF_setmiterlimit',
    'PDF_setrgbcolor',
    'PDF_setrgbcolor_fill',
    'PDF_setrgbcolor_stroke',
    'PDF_set_border_color',
    'PDF_set_border_dash',
    'PDF_set_border_style',
    'PDF_set_info',
    'PDF_set_layer_dependency',
    'PDF_set_parameter',
    'PDF_set_text_pos',
    'PDF_set_value',
    'PDF_show',
    'PDF_show_xy',
    'PDF_skew',
    'PDF_stroke',
    'pg_cancel_query',
    'pg_client_encoding',
    'pg_close',
    'pg_connect',
    'pg_connection_reset',
    'pg_convert',
    'pg_copy_from',
    'pg_copy_to',
    'pg_dbname',
    'pg_delete',
    'pg_end_copy',
    'pg_execute',
    'pg_field_name',
    'pg_field_table',
    'pg_field_type',
    'pg_flush',
    'pg_free_result',
    'pg_host',
    'pg_insert',
    'pg_last_error',
    'pg_last_notice',
    'pg_last_oid',
    'pg_lo_close',
    'pg_lo_export',
    'pg_lo_import',
    'pg_lo_open',
    'pg_lo_read',
    'pg_lo_read_all',
    'pg_lo_seek',
    'pg_lo_truncate',
    'pg_lo_unlink',
    'pg_lo_write',
    'pg_meta_data',
    'pg_options',
    'pg_parameter_status',
    'pg_pconnect',
    'pg_ping',
    'pg_port',
    'pg_prepare',
    'pg_put_line',
    'pg_query',
    'pg_query_params',
    'pg_result_error_field',
    'pg_result_seek',
    'pg_select',
    'pg_send_execute',
    'pg_send_prepare',
    'pg_send_query',
    'pg_send_query_params',
    'pg_socket',
    'pg_trace',
    'pg_tty',
    'pg_update',
    'pg_version',
    'phpcredits',
    'phpinfo',
    'png2wbmp',
    'posix_access',
    'posix_getgrnam',
    'posix_getpgid',
    'posix_initgroups',
    'posix_kill',
    'posix_mkfifo',
    'posix_mknod',
    'posix_setegid',
    'posix_seteuid',
    'posix_setgid',
    'posix_setpgid',
    'posix_setrlimit',
    'posix_setuid',
    'preg_match',
    'preg_match_all',
    'preg_replace',
    'preg_split',
    'proc_get_status',
    'proc_nice',
    'pspell_add_to_personal',
    'pspell_add_to_session',
    'pspell_clear_session',
    'pspell_config_create',
    'pspell_config_data_dir',
    'pspell_config_dict_dir',
    'pspell_config_ignore',
    'pspell_config_mode',
    'pspell_config_personal',
    'pspell_config_repl',
    'pspell_config_runtogether',
    'pspell_config_save_repl',
    'pspell_new',
    'pspell_new_config',
    'pspell_save_wordlist',
    'pspell_store_replacement',
    'ps_add_launchlink',
    'ps_add_locallink',
    'ps_add_note',
    'ps_add_pdflink',
    'ps_add_weblink',
    'ps_arc',
    'ps_arcn',
    'ps_begin_page',
    'ps_begin_pattern',
    'ps_begin_template',
    'ps_circle',
    'ps_clip',
    'ps_close',
    'ps_closepath',
    'ps_closepath_stroke',
    'ps_close_image',
    'ps_continue_text',
    'ps_curveto',
    'ps_delete',
    'ps_end_page',
    'ps_end_pattern',
    'ps_end_template',
    'ps_fill',
    'ps_fill_stroke',
    'ps_get_parameter',
    'ps_hyphenate',
    'ps_include_file',
    'ps_lineto',
    'ps_moveto',
    'ps_new',
    'ps_open_file',
    'ps_place_image',
    'ps_rect',
    'ps_restore',
    'ps_rotate',
    'ps_save',
    'ps_scale',
    'ps_setcolor',
    'ps_setdash',
    'ps_setflat',
    'ps_setfont',
    'ps_setgray',
    'ps_setlinecap',
    'ps_setlinejoin',
    'ps_setlinewidth',
    'ps_setmiterlimit',
    'ps_setoverprintmode',
    'ps_setpolydash',
    'ps_set_border_color',
    'ps_set_border_dash',
    'ps_set_border_style',
    'ps_set_info',
    'ps_set_parameter',
    'ps_set_text_pos',
    'ps_set_value',
    'ps_shading',
    'ps_shading_pattern',
    'ps_shfill',
    'ps_show',
    'ps_show2',
    'ps_show_xy',
    'ps_show_xy2',
    'ps_stroke',
    'ps_symbol',
    'ps_translate',
    'putenv',
    'readfile',
    'readgzfile',
    'readline_add_history',
    'readline_callback_handler_install',
    'readline_clear_history',
    'readline_completion_function',
    'readline_read_history',
    'readline_write_history',
    'readlink',
    'realpath',
    'register_tick_function',
    'rename',
    'rewind',
    'rewinddir',
    'rmdir',
    'rpmaddtag',
    'rrd_create',
    'rsort',
    'sapi_windows_cp_conv',
    'sapi_windows_cp_set',
    'sapi_windows_generate_ctrl_event',
    'sapi_windows_vt100_support',
    'scandir',
    'sem_acquire',
    'sem_get',
    'sem_release',
    'sem_remove',
    'session_abort',
    'session_decode',
    'session_destroy',
    'session_regenerate_id',
    'session_reset',
    'session_unset',
    'session_write_close',
    'settype',
    'set_include_path',
    'set_time_limit',
    'sha1_file',
    'shmop_delete',
    'shmop_read',
    'shmop_write',
    'shm_put_var',
    'shm_remove',
    'shm_remove_var',
    'shuffle',
    'simplexml_import_dom',
    'simplexml_load_file',
    'simplexml_load_string',
    'sleep',
    'socket_accept',
    'socket_addrinfo_bind',
    'socket_addrinfo_connect',
    'socket_bind',
    'socket_connect',
    'socket_create',
    'socket_create_listen',
    'socket_create_pair',
    'socket_export_stream',
    'socket_getpeername',
    'socket_getsockname',
    'socket_get_option',
    'socket_import_stream',
    'socket_listen',
    'socket_read',
    'socket_send',
    'socket_sendmsg',
    'socket_sendto',
    'socket_set_block',
    'socket_set_nonblock',
    'socket_set_option',
    'socket_shutdown',
    'socket_write',
    'socket_wsaprotocol_info_export',
    'socket_wsaprotocol_info_import',
    'socket_wsaprotocol_info_release',
    'sodium_crypto_pwhash',
    'sodium_crypto_pwhash_str',
    'solr_get_version',
    'sort',
    'soundex',
    'spl_autoload_register',
    'spl_autoload_unregister',
    'sprintf',
    'sqlsrv_begin_transaction',
    'sqlsrv_cancel',
    'sqlsrv_client_info',
    'sqlsrv_close',
    'sqlsrv_commit',
    'sqlsrv_configure',
    'sqlsrv_execute',
    'sqlsrv_free_stmt',
    'sqlsrv_get_field',
    'sqlsrv_next_result',
    'sqlsrv_num_fields',
    'sqlsrv_num_rows',
    'sqlsrv_prepare',
    'sqlsrv_query',
    'sqlsrv_rollback',
    'ssdeep_fuzzy_compare',
    'ssdeep_fuzzy_hash',
    'ssdeep_fuzzy_hash_filename',
    'ssh2_auth_agent',
    'ssh2_auth_hostbased_file',
    'ssh2_auth_password',
    'ssh2_auth_pubkey_file',
    'ssh2_connect',
    'ssh2_disconnect',
    'ssh2_exec',
    'ssh2_publickey_add',
    'ssh2_publickey_init',
    'ssh2_publickey_remove',
    'ssh2_scp_recv',
    'ssh2_scp_send',
    'ssh2_sftp',
    'ssh2_sftp_chmod',
    'ssh2_sftp_mkdir',
    'ssh2_sftp_rename',
    'ssh2_sftp_rmdir',
    'ssh2_sftp_symlink',
    'ssh2_sftp_unlink',
    'stream_context_set_params',
    'stream_copy_to_stream',
    'stream_filter_append',
    'stream_filter_prepend',
    'stream_filter_register',
    'stream_filter_remove',
    'stream_get_contents',
    'stream_isatty',
    'stream_resolve_include_path',
    'stream_set_blocking',
    'stream_set_timeout',
    'stream_socket_accept',
    'stream_socket_client',
    'stream_socket_pair',
    'stream_socket_server',
    'stream_socket_shutdown',
    'stream_supports_lock',
    'stream_wrapper_register',
    'stream_wrapper_restore',
    'stream_wrapper_unregister',
    'strptime',
    'strtotime',
    'substr',
    'swoole_async_write',
    'swoole_async_writefile',
    'swoole_event_defer',
    'swoole_event_del',
    'swoole_event_write',
    'symlink',
    'syslog',
    'system',
    'tempnam',
    'timezone_name_from_abbr',
    'time_nanosleep',
    'time_sleep_until',
    'tmpfile',
    'touch',
    'uasort',
    'uksort',
    'unlink',
    'unpack',
    'uopz_extend',
    'uopz_implement',
    'usort',
    'virtual',
    'vsprintf',
    'xdiff_file_bdiff',
    'xdiff_file_bpatch',
    'xdiff_file_diff',
    'xdiff_file_diff_binary',
    'xdiff_file_patch_binary',
    'xdiff_file_rabdiff',
    'xdiff_string_bpatch',
    'xdiff_string_patch',
    'xdiff_string_patch_binary',
    'xmlrpc_set_type',
    'xml_parser_create',
    'xml_parser_create_ns',
    'xml_set_object',
    'yaml_parse',
    'yaml_parse_file',
    'yaml_parse_url',
    'yaz_ccl_parse',
    'yaz_close',
    'yaz_connect',
    'yaz_database',
    'yaz_element',
    'yaz_present',
    'yaz_search',
    'yaz_wait',
    'zip_entry_close',
    'zip_entry_open',
    'zip_entry_read',
    'zlib_decode',
];
