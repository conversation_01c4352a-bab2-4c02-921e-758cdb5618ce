<?php

declare(strict_types=1);

use <PERSON>\Renaming\Rector\FuncCall\RenameFunctionRector;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;

# This file configures rector/rector:~0.7.0 to replace all PHP functions with their equivalent "safe" functions
return static function (ContainerConfigurator $containerConfigurator): void {
    $services = $containerConfigurator->services();

    $services->set(RenameFunctionRector::class)
        ->call('configure', [[ RenameFunctionRector::OLD_FUNCTION_TO_NEW_FUNCTION => [
            'apache_getenv' => 'Safe\apache_getenv',
            'apache_get_version' => 'Safe\apache_get_version',
            'apache_request_headers' => 'Safe\apache_request_headers',
            'apache_reset_timeout' => 'Safe\apache_reset_timeout',
            'apache_response_headers' => 'Safe\apache_response_headers',
            'apache_setenv' => 'Safe\apache_setenv',
            'apcu_cache_info' => 'Safe\apcu_cache_info',
            'apcu_cas' => 'Safe\apcu_cas',
            'apcu_dec' => 'Safe\apcu_dec',
            'apcu_fetch' => 'Safe\apcu_fetch',
            'apcu_inc' => 'Safe\apcu_inc',
            'apcu_sma_info' => 'Safe\apcu_sma_info',
            'apc_fetch' => 'Safe\apc_fetch',
            'array_combine' => 'Safe\array_combine',
            'array_flip' => 'Safe\array_flip',
            'array_replace' => 'Safe\array_replace',
            'array_replace_recursive' => 'Safe\array_replace_recursive',
            'array_walk_recursive' => 'Safe\array_walk_recursive',
            'arsort' => 'Safe\arsort',
            'asort' => 'Safe\asort',
            'base64_decode' => 'Safe\base64_decode',
            'bzclose' => 'Safe\bzclose',
            'bzflush' => 'Safe\bzflush',
            'bzread' => 'Safe\bzread',
            'bzwrite' => 'Safe\bzwrite',
            'chdir' => 'Safe\chdir',
            'chgrp' => 'Safe\chgrp',
            'chmod' => 'Safe\chmod',
            'chown' => 'Safe\chown',
            'chroot' => 'Safe\chroot',
            'class_alias' => 'Safe\class_alias',
            'class_implements' => 'Safe\class_implements',
            'class_parents' => 'Safe\class_parents',
            'class_uses' => 'Safe\class_uses',
            'cli_set_process_title' => 'Safe\cli_set_process_title',
            'closelog' => 'Safe\closelog',
            'com_event_sink' => 'Safe\com_event_sink',
            'com_load_typelib' => 'Safe\com_load_typelib',
            'com_print_typeinfo' => 'Safe\com_print_typeinfo',
            'convert_uudecode' => 'Safe\convert_uudecode',
            'convert_uuencode' => 'Safe\convert_uuencode',
            'copy' => 'Safe\copy',
            'create_function' => 'Safe\create_function',
            'cubrid_free_result' => 'Safe\cubrid_free_result',
            'cubrid_get_charset' => 'Safe\cubrid_get_charset',
            'cubrid_get_client_info' => 'Safe\cubrid_get_client_info',
            'cubrid_get_db_parameter' => 'Safe\cubrid_get_db_parameter',
            'cubrid_get_server_info' => 'Safe\cubrid_get_server_info',
            'cubrid_insert_id' => 'Safe\cubrid_insert_id',
            'cubrid_lob2_new' => 'Safe\cubrid_lob2_new',
            'cubrid_lob2_size' => 'Safe\cubrid_lob2_size',
            'cubrid_lob2_size64' => 'Safe\cubrid_lob2_size64',
            'cubrid_lob2_tell' => 'Safe\cubrid_lob2_tell',
            'cubrid_lob2_tell64' => 'Safe\cubrid_lob2_tell64',
            'cubrid_set_db_parameter' => 'Safe\cubrid_set_db_parameter',
            'curl_escape' => 'Safe\curl_escape',
            'curl_exec' => 'Safe\curl_exec',
            'curl_getinfo' => 'Safe\curl_getinfo',
            'curl_init' => 'Safe\curl_init',
            'curl_multi_errno' => 'Safe\curl_multi_errno',
            'curl_multi_info_read' => 'Safe\curl_multi_info_read',
            'curl_multi_init' => 'Safe\curl_multi_init',
            'curl_setopt' => 'Safe\curl_setopt',
            'curl_share_errno' => 'Safe\curl_share_errno',
            'curl_share_setopt' => 'Safe\curl_share_setopt',
            'curl_unescape' => 'Safe\curl_unescape',
            'date' => 'Safe\date',
            'date_parse' => 'Safe\date_parse',
            'date_parse_from_format' => 'Safe\date_parse_from_format',
            'date_sunrise' => 'Safe\date_sunrise',
            'date_sunset' => 'Safe\date_sunset',
            'date_sun_info' => 'Safe\date_sun_info',
            'db2_autocommit' => 'Safe\db2_autocommit',
            'db2_bind_param' => 'Safe\db2_bind_param',
            'db2_client_info' => 'Safe\db2_client_info',
            'db2_close' => 'Safe\db2_close',
            'db2_commit' => 'Safe\db2_commit',
            'db2_execute' => 'Safe\db2_execute',
            'db2_free_result' => 'Safe\db2_free_result',
            'db2_free_stmt' => 'Safe\db2_free_stmt',
            'db2_get_option' => 'Safe\db2_get_option',
            'db2_pclose' => 'Safe\db2_pclose',
            'db2_rollback' => 'Safe\db2_rollback',
            'db2_server_info' => 'Safe\db2_server_info',
            'db2_set_option' => 'Safe\db2_set_option',
            'define' => 'Safe\define',
            'deflate_add' => 'Safe\deflate_add',
            'deflate_init' => 'Safe\deflate_init',
            'disk_free_space' => 'Safe\disk_free_space',
            'disk_total_space' => 'Safe\disk_total_space',
            'dl' => 'Safe\dl',
            'dns_get_record' => 'Safe\dns_get_record',
            'eio_busy' => 'Safe\eio_busy',
            'eio_chmod' => 'Safe\eio_chmod',
            'eio_chown' => 'Safe\eio_chown',
            'eio_close' => 'Safe\eio_close',
            'eio_custom' => 'Safe\eio_custom',
            'eio_dup2' => 'Safe\eio_dup2',
            'eio_event_loop' => 'Safe\eio_event_loop',
            'eio_fallocate' => 'Safe\eio_fallocate',
            'eio_fchmod' => 'Safe\eio_fchmod',
            'eio_fdatasync' => 'Safe\eio_fdatasync',
            'eio_fstat' => 'Safe\eio_fstat',
            'eio_fstatvfs' => 'Safe\eio_fstatvfs',
            'eio_fsync' => 'Safe\eio_fsync',
            'eio_ftruncate' => 'Safe\eio_ftruncate',
            'eio_futime' => 'Safe\eio_futime',
            'eio_grp' => 'Safe\eio_grp',
            'eio_lstat' => 'Safe\eio_lstat',
            'eio_mkdir' => 'Safe\eio_mkdir',
            'eio_mknod' => 'Safe\eio_mknod',
            'eio_nop' => 'Safe\eio_nop',
            'eio_readahead' => 'Safe\eio_readahead',
            'eio_readdir' => 'Safe\eio_readdir',
            'eio_readlink' => 'Safe\eio_readlink',
            'eio_rename' => 'Safe\eio_rename',
            'eio_rmdir' => 'Safe\eio_rmdir',
            'eio_seek' => 'Safe\eio_seek',
            'eio_sendfile' => 'Safe\eio_sendfile',
            'eio_stat' => 'Safe\eio_stat',
            'eio_statvfs' => 'Safe\eio_statvfs',
            'eio_symlink' => 'Safe\eio_symlink',
            'eio_sync' => 'Safe\eio_sync',
            'eio_syncfs' => 'Safe\eio_syncfs',
            'eio_sync_file_range' => 'Safe\eio_sync_file_range',
            'eio_truncate' => 'Safe\eio_truncate',
            'eio_unlink' => 'Safe\eio_unlink',
            'eio_utime' => 'Safe\eio_utime',
            'eio_write' => 'Safe\eio_write',
            'error_log' => 'Safe\error_log',
            'fastcgi_finish_request' => 'Safe\fastcgi_finish_request',
            'fbird_blob_cancel' => 'Safe\fbird_blob_cancel',
            'fclose' => 'Safe\fclose',
            'fflush' => 'Safe\fflush',
            'file' => 'Safe\file',
            'fileatime' => 'Safe\fileatime',
            'filectime' => 'Safe\filectime',
            'fileinode' => 'Safe\fileinode',
            'filemtime' => 'Safe\filemtime',
            'fileowner' => 'Safe\fileowner',
            'filesize' => 'Safe\filesize',
            'file_get_contents' => 'Safe\file_get_contents',
            'file_put_contents' => 'Safe\file_put_contents',
            'filter_input_array' => 'Safe\filter_input_array',
            'filter_var_array' => 'Safe\filter_var_array',
            'finfo_close' => 'Safe\finfo_close',
            'finfo_open' => 'Safe\finfo_open',
            'flock' => 'Safe\flock',
            'fopen' => 'Safe\fopen',
            'fputcsv' => 'Safe\fputcsv',
            'fread' => 'Safe\fread',
            'fsockopen' => 'Safe\fsockopen',
            'ftp_alloc' => 'Safe\ftp_alloc',
            'ftp_append' => 'Safe\ftp_append',
            'ftp_cdup' => 'Safe\ftp_cdup',
            'ftp_chdir' => 'Safe\ftp_chdir',
            'ftp_chmod' => 'Safe\ftp_chmod',
            'ftp_close' => 'Safe\ftp_close',
            'ftp_connect' => 'Safe\ftp_connect',
            'ftp_delete' => 'Safe\ftp_delete',
            'ftp_fget' => 'Safe\ftp_fget',
            'ftp_fput' => 'Safe\ftp_fput',
            'ftp_get' => 'Safe\ftp_get',
            'ftp_login' => 'Safe\ftp_login',
            'ftp_mkdir' => 'Safe\ftp_mkdir',
            'ftp_mlsd' => 'Safe\ftp_mlsd',
            'ftp_nlist' => 'Safe\ftp_nlist',
            'ftp_pasv' => 'Safe\ftp_pasv',
            'ftp_put' => 'Safe\ftp_put',
            'ftp_pwd' => 'Safe\ftp_pwd',
            'ftp_rename' => 'Safe\ftp_rename',
            'ftp_rmdir' => 'Safe\ftp_rmdir',
            'ftp_site' => 'Safe\ftp_site',
            'ftp_ssl_connect' => 'Safe\ftp_ssl_connect',
            'ftp_systype' => 'Safe\ftp_systype',
            'ftruncate' => 'Safe\ftruncate',
            'fwrite' => 'Safe\fwrite',
            'getallheaders' => 'Safe\getallheaders',
            'getcwd' => 'Safe\getcwd',
            'gethostname' => 'Safe\gethostname',
            'getimagesize' => 'Safe\getimagesize',
            'getlastmod' => 'Safe\getlastmod',
            'getmygid' => 'Safe\getmygid',
            'getmyinode' => 'Safe\getmyinode',
            'getmypid' => 'Safe\getmypid',
            'getmyuid' => 'Safe\getmyuid',
            'getopt' => 'Safe\getopt',
            'getprotobyname' => 'Safe\getprotobyname',
            'getprotobynumber' => 'Safe\getprotobynumber',
            'get_headers' => 'Safe\get_headers',
            'glob' => 'Safe\glob',
            'gmdate' => 'Safe\gmdate',
            'gmp_binomial' => 'Safe\gmp_binomial',
            'gmp_export' => 'Safe\gmp_export',
            'gmp_import' => 'Safe\gmp_import',
            'gmp_random_seed' => 'Safe\gmp_random_seed',
            'gnupg_adddecryptkey' => 'Safe\gnupg_adddecryptkey',
            'gnupg_addencryptkey' => 'Safe\gnupg_addencryptkey',
            'gnupg_addsignkey' => 'Safe\gnupg_addsignkey',
            'gnupg_cleardecryptkeys' => 'Safe\gnupg_cleardecryptkeys',
            'gnupg_clearencryptkeys' => 'Safe\gnupg_clearencryptkeys',
            'gnupg_clearsignkeys' => 'Safe\gnupg_clearsignkeys',
            'gnupg_setarmor' => 'Safe\gnupg_setarmor',
            'gnupg_setsignmode' => 'Safe\gnupg_setsignmode',
            'gzclose' => 'Safe\gzclose',
            'gzcompress' => 'Safe\gzcompress',
            'gzdecode' => 'Safe\gzdecode',
            'gzdeflate' => 'Safe\gzdeflate',
            'gzencode' => 'Safe\gzencode',
            'gzgets' => 'Safe\gzgets',
            'gzgetss' => 'Safe\gzgetss',
            'gzinflate' => 'Safe\gzinflate',
            'gzpassthru' => 'Safe\gzpassthru',
            'gzrewind' => 'Safe\gzrewind',
            'gzuncompress' => 'Safe\gzuncompress',
            'hash_hkdf' => 'Safe\hash_hkdf',
            'hash_update_file' => 'Safe\hash_update_file',
            'header_register_callback' => 'Safe\header_register_callback',
            'hex2bin' => 'Safe\hex2bin',
            'highlight_file' => 'Safe\highlight_file',
            'highlight_string' => 'Safe\highlight_string',
            'ibase_add_user' => 'Safe\ibase_add_user',
            'ibase_backup' => 'Safe\ibase_backup',
            'ibase_blob_cancel' => 'Safe\ibase_blob_cancel',
            'ibase_blob_create' => 'Safe\ibase_blob_create',
            'ibase_blob_get' => 'Safe\ibase_blob_get',
            'ibase_close' => 'Safe\ibase_close',
            'ibase_commit' => 'Safe\ibase_commit',
            'ibase_commit_ret' => 'Safe\ibase_commit_ret',
            'ibase_connect' => 'Safe\ibase_connect',
            'ibase_delete_user' => 'Safe\ibase_delete_user',
            'ibase_drop_db' => 'Safe\ibase_drop_db',
            'ibase_free_event_handler' => 'Safe\ibase_free_event_handler',
            'ibase_free_query' => 'Safe\ibase_free_query',
            'ibase_free_result' => 'Safe\ibase_free_result',
            'ibase_maintain_db' => 'Safe\ibase_maintain_db',
            'ibase_modify_user' => 'Safe\ibase_modify_user',
            'ibase_name_result' => 'Safe\ibase_name_result',
            'ibase_pconnect' => 'Safe\ibase_pconnect',
            'ibase_restore' => 'Safe\ibase_restore',
            'ibase_rollback' => 'Safe\ibase_rollback',
            'ibase_rollback_ret' => 'Safe\ibase_rollback_ret',
            'ibase_service_attach' => 'Safe\ibase_service_attach',
            'ibase_service_detach' => 'Safe\ibase_service_detach',
            'iconv' => 'Safe\iconv',
            'iconv_get_encoding' => 'Safe\iconv_get_encoding',
            'iconv_set_encoding' => 'Safe\iconv_set_encoding',
            'image2wbmp' => 'Safe\image2wbmp',
            'imageaffine' => 'Safe\imageaffine',
            'imageaffinematrixconcat' => 'Safe\imageaffinematrixconcat',
            'imageaffinematrixget' => 'Safe\imageaffinematrixget',
            'imagealphablending' => 'Safe\imagealphablending',
            'imageantialias' => 'Safe\imageantialias',
            'imagearc' => 'Safe\imagearc',
            'imagebmp' => 'Safe\imagebmp',
            'imagechar' => 'Safe\imagechar',
            'imagecharup' => 'Safe\imagecharup',
            'imagecolorat' => 'Safe\imagecolorat',
            'imagecolordeallocate' => 'Safe\imagecolordeallocate',
            'imagecolormatch' => 'Safe\imagecolormatch',
            'imageconvolution' => 'Safe\imageconvolution',
            'imagecopy' => 'Safe\imagecopy',
            'imagecopymerge' => 'Safe\imagecopymerge',
            'imagecopymergegray' => 'Safe\imagecopymergegray',
            'imagecopyresampled' => 'Safe\imagecopyresampled',
            'imagecopyresized' => 'Safe\imagecopyresized',
            'imagecreate' => 'Safe\imagecreate',
            'imagecreatefrombmp' => 'Safe\imagecreatefrombmp',
            'imagecreatefromgd' => 'Safe\imagecreatefromgd',
            'imagecreatefromgd2' => 'Safe\imagecreatefromgd2',
            'imagecreatefromgd2part' => 'Safe\imagecreatefromgd2part',
            'imagecreatefromgif' => 'Safe\imagecreatefromgif',
            'imagecreatefromjpeg' => 'Safe\imagecreatefromjpeg',
            'imagecreatefrompng' => 'Safe\imagecreatefrompng',
            'imagecreatefromwbmp' => 'Safe\imagecreatefromwbmp',
            'imagecreatefromwebp' => 'Safe\imagecreatefromwebp',
            'imagecreatefromxbm' => 'Safe\imagecreatefromxbm',
            'imagecreatefromxpm' => 'Safe\imagecreatefromxpm',
            'imagecreatetruecolor' => 'Safe\imagecreatetruecolor',
            'imagecrop' => 'Safe\imagecrop',
            'imagecropauto' => 'Safe\imagecropauto',
            'imagedashedline' => 'Safe\imagedashedline',
            'imagedestroy' => 'Safe\imagedestroy',
            'imageellipse' => 'Safe\imageellipse',
            'imagefill' => 'Safe\imagefill',
            'imagefilledarc' => 'Safe\imagefilledarc',
            'imagefilledellipse' => 'Safe\imagefilledellipse',
            'imagefilledpolygon' => 'Safe\imagefilledpolygon',
            'imagefilledrectangle' => 'Safe\imagefilledrectangle',
            'imagefilltoborder' => 'Safe\imagefilltoborder',
            'imagefilter' => 'Safe\imagefilter',
            'imageflip' => 'Safe\imageflip',
            'imagegammacorrect' => 'Safe\imagegammacorrect',
            'imagegd' => 'Safe\imagegd',
            'imagegd2' => 'Safe\imagegd2',
            'imagegif' => 'Safe\imagegif',
            'imagegrabscreen' => 'Safe\imagegrabscreen',
            'imagegrabwindow' => 'Safe\imagegrabwindow',
            'imagejpeg' => 'Safe\imagejpeg',
            'imagelayereffect' => 'Safe\imagelayereffect',
            'imageline' => 'Safe\imageline',
            'imageloadfont' => 'Safe\imageloadfont',
            'imageopenpolygon' => 'Safe\imageopenpolygon',
            'imagepng' => 'Safe\imagepng',
            'imagepolygon' => 'Safe\imagepolygon',
            'imagerectangle' => 'Safe\imagerectangle',
            'imagerotate' => 'Safe\imagerotate',
            'imagesavealpha' => 'Safe\imagesavealpha',
            'imagescale' => 'Safe\imagescale',
            'imagesetbrush' => 'Safe\imagesetbrush',
            'imagesetclip' => 'Safe\imagesetclip',
            'imagesetinterpolation' => 'Safe\imagesetinterpolation',
            'imagesetpixel' => 'Safe\imagesetpixel',
            'imagesetstyle' => 'Safe\imagesetstyle',
            'imagesetthickness' => 'Safe\imagesetthickness',
            'imagesettile' => 'Safe\imagesettile',
            'imagestring' => 'Safe\imagestring',
            'imagestringup' => 'Safe\imagestringup',
            'imagesx' => 'Safe\imagesx',
            'imagesy' => 'Safe\imagesy',
            'imagetruecolortopalette' => 'Safe\imagetruecolortopalette',
            'imagettfbbox' => 'Safe\imagettfbbox',
            'imagettftext' => 'Safe\imagettftext',
            'imagewbmp' => 'Safe\imagewbmp',
            'imagewebp' => 'Safe\imagewebp',
            'imagexbm' => 'Safe\imagexbm',
            'imap_append' => 'Safe\imap_append',
            'imap_check' => 'Safe\imap_check',
            'imap_clearflag_full' => 'Safe\imap_clearflag_full',
            'imap_close' => 'Safe\imap_close',
            'imap_createmailbox' => 'Safe\imap_createmailbox',
            'imap_deletemailbox' => 'Safe\imap_deletemailbox',
            'imap_fetchstructure' => 'Safe\imap_fetchstructure',
            'imap_gc' => 'Safe\imap_gc',
            'imap_headerinfo' => 'Safe\imap_headerinfo',
            'imap_mail' => 'Safe\imap_mail',
            'imap_mailboxmsginfo' => 'Safe\imap_mailboxmsginfo',
            'imap_mail_compose' => 'Safe\imap_mail_compose',
            'imap_mail_copy' => 'Safe\imap_mail_copy',
            'imap_mail_move' => 'Safe\imap_mail_move',
            'imap_mutf7_to_utf8' => 'Safe\imap_mutf7_to_utf8',
            'imap_num_msg' => 'Safe\imap_num_msg',
            'imap_open' => 'Safe\imap_open',
            'imap_renamemailbox' => 'Safe\imap_renamemailbox',
            'imap_savebody' => 'Safe\imap_savebody',
            'imap_setacl' => 'Safe\imap_setacl',
            'imap_setflag_full' => 'Safe\imap_setflag_full',
            'imap_set_quota' => 'Safe\imap_set_quota',
            'imap_sort' => 'Safe\imap_sort',
            'imap_subscribe' => 'Safe\imap_subscribe',
            'imap_thread' => 'Safe\imap_thread',
            'imap_timeout' => 'Safe\imap_timeout',
            'imap_undelete' => 'Safe\imap_undelete',
            'imap_unsubscribe' => 'Safe\imap_unsubscribe',
            'imap_utf8_to_mutf7' => 'Safe\imap_utf8_to_mutf7',
            'inet_ntop' => 'Safe\inet_ntop',
            'inflate_add' => 'Safe\inflate_add',
            'inflate_get_read_len' => 'Safe\inflate_get_read_len',
            'inflate_get_status' => 'Safe\inflate_get_status',
            'inflate_init' => 'Safe\inflate_init',
            'ingres_autocommit' => 'Safe\ingres_autocommit',
            'ingres_close' => 'Safe\ingres_close',
            'ingres_commit' => 'Safe\ingres_commit',
            'ingres_connect' => 'Safe\ingres_connect',
            'ingres_execute' => 'Safe\ingres_execute',
            'ingres_field_name' => 'Safe\ingres_field_name',
            'ingres_field_type' => 'Safe\ingres_field_type',
            'ingres_free_result' => 'Safe\ingres_free_result',
            'ingres_pconnect' => 'Safe\ingres_pconnect',
            'ingres_result_seek' => 'Safe\ingres_result_seek',
            'ingres_rollback' => 'Safe\ingres_rollback',
            'ingres_set_environment' => 'Safe\ingres_set_environment',
            'ini_get' => 'Safe\ini_get',
            'ini_set' => 'Safe\ini_set',
            'inotify_init' => 'Safe\inotify_init',
            'inotify_rm_watch' => 'Safe\inotify_rm_watch',
            'iptcembed' => 'Safe\iptcembed',
            'iptcparse' => 'Safe\iptcparse',
            'jdtounix' => 'Safe\jdtounix',
            'jpeg2wbmp' => 'Safe\jpeg2wbmp',
            'json_decode' => 'Safe\json_decode',
            'json_encode' => 'Safe\json_encode',
            'json_last_error_msg' => 'Safe\json_last_error_msg',
            'krsort' => 'Safe\krsort',
            'ksort' => 'Safe\ksort',
            'lchgrp' => 'Safe\lchgrp',
            'lchown' => 'Safe\lchown',
            'ldap_add' => 'Safe\ldap_add',
            'ldap_add_ext' => 'Safe\ldap_add_ext',
            'ldap_bind' => 'Safe\ldap_bind',
            'ldap_bind_ext' => 'Safe\ldap_bind_ext',
            'ldap_control_paged_result' => 'Safe\ldap_control_paged_result',
            'ldap_control_paged_result_response' => 'Safe\ldap_control_paged_result_response',
            'ldap_count_entries' => 'Safe\ldap_count_entries',
            'ldap_delete' => 'Safe\ldap_delete',
            'ldap_delete_ext' => 'Safe\ldap_delete_ext',
            'ldap_exop' => 'Safe\ldap_exop',
            'ldap_exop_passwd' => 'Safe\ldap_exop_passwd',
            'ldap_exop_whoami' => 'Safe\ldap_exop_whoami',
            'ldap_explode_dn' => 'Safe\ldap_explode_dn',
            'ldap_first_attribute' => 'Safe\ldap_first_attribute',
            'ldap_first_entry' => 'Safe\ldap_first_entry',
            'ldap_free_result' => 'Safe\ldap_free_result',
            'ldap_get_attributes' => 'Safe\ldap_get_attributes',
            'ldap_get_dn' => 'Safe\ldap_get_dn',
            'ldap_get_entries' => 'Safe\ldap_get_entries',
            'ldap_get_option' => 'Safe\ldap_get_option',
            'ldap_get_values' => 'Safe\ldap_get_values',
            'ldap_get_values_len' => 'Safe\ldap_get_values_len',
            'ldap_list' => 'Safe\ldap_list',
            'ldap_modify_batch' => 'Safe\ldap_modify_batch',
            'ldap_mod_add' => 'Safe\ldap_mod_add',
            'ldap_mod_add_ext' => 'Safe\ldap_mod_add_ext',
            'ldap_mod_del' => 'Safe\ldap_mod_del',
            'ldap_mod_del_ext' => 'Safe\ldap_mod_del_ext',
            'ldap_mod_replace' => 'Safe\ldap_mod_replace',
            'ldap_mod_replace_ext' => 'Safe\ldap_mod_replace_ext',
            'ldap_next_attribute' => 'Safe\ldap_next_attribute',
            'ldap_parse_exop' => 'Safe\ldap_parse_exop',
            'ldap_parse_result' => 'Safe\ldap_parse_result',
            'ldap_read' => 'Safe\ldap_read',
            'ldap_rename' => 'Safe\ldap_rename',
            'ldap_rename_ext' => 'Safe\ldap_rename_ext',
            'ldap_sasl_bind' => 'Safe\ldap_sasl_bind',
            'ldap_search' => 'Safe\ldap_search',
            'ldap_set_option' => 'Safe\ldap_set_option',
            'ldap_unbind' => 'Safe\ldap_unbind',
            'libxml_get_last_error' => 'Safe\libxml_get_last_error',
            'libxml_set_external_entity_loader' => 'Safe\libxml_set_external_entity_loader',
            'link' => 'Safe\link',
            'lzf_compress' => 'Safe\lzf_compress',
            'lzf_decompress' => 'Safe\lzf_decompress',
            'mailparse_msg_extract_part_file' => 'Safe\mailparse_msg_extract_part_file',
            'mailparse_msg_free' => 'Safe\mailparse_msg_free',
            'mailparse_msg_parse' => 'Safe\mailparse_msg_parse',
            'mailparse_msg_parse_file' => 'Safe\mailparse_msg_parse_file',
            'mailparse_stream_encode' => 'Safe\mailparse_stream_encode',
            'mb_chr' => 'Safe\mb_chr',
            'mb_detect_order' => 'Safe\mb_detect_order',
            'mb_encoding_aliases' => 'Safe\mb_encoding_aliases',
            'mb_eregi_replace' => 'Safe\mb_eregi_replace',
            'mb_ereg_replace' => 'Safe\mb_ereg_replace',
            'mb_ereg_replace_callback' => 'Safe\mb_ereg_replace_callback',
            'mb_ereg_search_getregs' => 'Safe\mb_ereg_search_getregs',
            'mb_ereg_search_init' => 'Safe\mb_ereg_search_init',
            'mb_ereg_search_regs' => 'Safe\mb_ereg_search_regs',
            'mb_ereg_search_setpos' => 'Safe\mb_ereg_search_setpos',
            'mb_http_output' => 'Safe\mb_http_output',
            'mb_internal_encoding' => 'Safe\mb_internal_encoding',
            'mb_ord' => 'Safe\mb_ord',
            'mb_parse_str' => 'Safe\mb_parse_str',
            'mb_regex_encoding' => 'Safe\mb_regex_encoding',
            'mb_send_mail' => 'Safe\mb_send_mail',
            'mb_split' => 'Safe\mb_split',
            'mb_str_split' => 'Safe\mb_str_split',
            'md5_file' => 'Safe\md5_file',
            'metaphone' => 'Safe\metaphone',
            'mime_content_type' => 'Safe\mime_content_type',
            'mkdir' => 'Safe\mkdir',
            'mktime' => 'Safe\mktime',
            'msg_queue_exists' => 'Safe\msg_queue_exists',
            'msg_receive' => 'Safe\msg_receive',
            'msg_remove_queue' => 'Safe\msg_remove_queue',
            'msg_send' => 'Safe\msg_send',
            'msg_set_queue' => 'Safe\msg_set_queue',
            'msql_affected_rows' => 'Safe\msql_affected_rows',
            'msql_close' => 'Safe\msql_close',
            'msql_connect' => 'Safe\msql_connect',
            'msql_create_db' => 'Safe\msql_create_db',
            'msql_data_seek' => 'Safe\msql_data_seek',
            'msql_db_query' => 'Safe\msql_db_query',
            'msql_drop_db' => 'Safe\msql_drop_db',
            'msql_field_len' => 'Safe\msql_field_len',
            'msql_field_name' => 'Safe\msql_field_name',
            'msql_field_seek' => 'Safe\msql_field_seek',
            'msql_field_table' => 'Safe\msql_field_table',
            'msql_field_type' => 'Safe\msql_field_type',
            'msql_free_result' => 'Safe\msql_free_result',
            'msql_pconnect' => 'Safe\msql_pconnect',
            'msql_query' => 'Safe\msql_query',
            'msql_select_db' => 'Safe\msql_select_db',
            'mysqli_get_cache_stats' => 'Safe\mysqli_get_cache_stats',
            'mysqli_get_client_stats' => 'Safe\mysqli_get_client_stats',
            'mysqlnd_ms_dump_servers' => 'Safe\mysqlnd_ms_dump_servers',
            'mysqlnd_ms_fabric_select_global' => 'Safe\mysqlnd_ms_fabric_select_global',
            'mysqlnd_ms_fabric_select_shard' => 'Safe\mysqlnd_ms_fabric_select_shard',
            'mysqlnd_ms_get_last_used_connection' => 'Safe\mysqlnd_ms_get_last_used_connection',
            'mysqlnd_qc_clear_cache' => 'Safe\mysqlnd_qc_clear_cache',
            'mysqlnd_qc_set_is_select' => 'Safe\mysqlnd_qc_set_is_select',
            'mysqlnd_qc_set_storage_handler' => 'Safe\mysqlnd_qc_set_storage_handler',
            'mysql_close' => 'Safe\mysql_close',
            'mysql_connect' => 'Safe\mysql_connect',
            'mysql_create_db' => 'Safe\mysql_create_db',
            'mysql_data_seek' => 'Safe\mysql_data_seek',
            'mysql_db_name' => 'Safe\mysql_db_name',
            'mysql_db_query' => 'Safe\mysql_db_query',
            'mysql_drop_db' => 'Safe\mysql_drop_db',
            'mysql_fetch_lengths' => 'Safe\mysql_fetch_lengths',
            'mysql_field_flags' => 'Safe\mysql_field_flags',
            'mysql_field_len' => 'Safe\mysql_field_len',
            'mysql_field_name' => 'Safe\mysql_field_name',
            'mysql_field_seek' => 'Safe\mysql_field_seek',
            'mysql_free_result' => 'Safe\mysql_free_result',
            'mysql_get_host_info' => 'Safe\mysql_get_host_info',
            'mysql_get_proto_info' => 'Safe\mysql_get_proto_info',
            'mysql_get_server_info' => 'Safe\mysql_get_server_info',
            'mysql_info' => 'Safe\mysql_info',
            'mysql_list_dbs' => 'Safe\mysql_list_dbs',
            'mysql_list_fields' => 'Safe\mysql_list_fields',
            'mysql_list_processes' => 'Safe\mysql_list_processes',
            'mysql_list_tables' => 'Safe\mysql_list_tables',
            'mysql_num_fields' => 'Safe\mysql_num_fields',
            'mysql_num_rows' => 'Safe\mysql_num_rows',
            'mysql_query' => 'Safe\mysql_query',
            'mysql_real_escape_string' => 'Safe\mysql_real_escape_string',
            'mysql_result' => 'Safe\mysql_result',
            'mysql_select_db' => 'Safe\mysql_select_db',
            'mysql_set_charset' => 'Safe\mysql_set_charset',
            'mysql_tablename' => 'Safe\mysql_tablename',
            'mysql_thread_id' => 'Safe\mysql_thread_id',
            'mysql_unbuffered_query' => 'Safe\mysql_unbuffered_query',
            'natcasesort' => 'Safe\natcasesort',
            'natsort' => 'Safe\natsort',
            'ob_end_clean' => 'Safe\ob_end_clean',
            'ob_end_flush' => 'Safe\ob_end_flush',
            'oci_bind_array_by_name' => 'Safe\oci_bind_array_by_name',
            'oci_bind_by_name' => 'Safe\oci_bind_by_name',
            'oci_cancel' => 'Safe\oci_cancel',
            'oci_close' => 'Safe\oci_close',
            'oci_commit' => 'Safe\oci_commit',
            'oci_connect' => 'Safe\oci_connect',
            'oci_define_by_name' => 'Safe\oci_define_by_name',
            'oci_execute' => 'Safe\oci_execute',
            'oci_fetch_all' => 'Safe\oci_fetch_all',
            'oci_field_name' => 'Safe\oci_field_name',
            'oci_field_precision' => 'Safe\oci_field_precision',
            'oci_field_scale' => 'Safe\oci_field_scale',
            'oci_field_size' => 'Safe\oci_field_size',
            'oci_field_type' => 'Safe\oci_field_type',
            'oci_field_type_raw' => 'Safe\oci_field_type_raw',
            'oci_free_descriptor' => 'Safe\oci_free_descriptor',
            'oci_free_statement' => 'Safe\oci_free_statement',
            'oci_new_collection' => 'Safe\oci_new_collection',
            'oci_new_connect' => 'Safe\oci_new_connect',
            'oci_new_cursor' => 'Safe\oci_new_cursor',
            'oci_new_descriptor' => 'Safe\oci_new_descriptor',
            'oci_num_fields' => 'Safe\oci_num_fields',
            'oci_num_rows' => 'Safe\oci_num_rows',
            'oci_parse' => 'Safe\oci_parse',
            'oci_pconnect' => 'Safe\oci_pconnect',
            'oci_result' => 'Safe\oci_result',
            'oci_rollback' => 'Safe\oci_rollback',
            'oci_server_version' => 'Safe\oci_server_version',
            'oci_set_action' => 'Safe\oci_set_action',
            'oci_set_call_timeout' => 'Safe\oci_set_call_timeout',
            'oci_set_client_identifier' => 'Safe\oci_set_client_identifier',
            'oci_set_client_info' => 'Safe\oci_set_client_info',
            'oci_set_db_operation' => 'Safe\oci_set_db_operation',
            'oci_set_edition' => 'Safe\oci_set_edition',
            'oci_set_module_name' => 'Safe\oci_set_module_name',
            'oci_set_prefetch' => 'Safe\oci_set_prefetch',
            'oci_statement_type' => 'Safe\oci_statement_type',
            'oci_unregister_taf_callback' => 'Safe\oci_unregister_taf_callback',
            'odbc_autocommit' => 'Safe\odbc_autocommit',
            'odbc_binmode' => 'Safe\odbc_binmode',
            'odbc_columnprivileges' => 'Safe\odbc_columnprivileges',
            'odbc_columns' => 'Safe\odbc_columns',
            'odbc_commit' => 'Safe\odbc_commit',
            'odbc_data_source' => 'Safe\odbc_data_source',
            'odbc_exec' => 'Safe\odbc_exec',
            'odbc_execute' => 'Safe\odbc_execute',
            'odbc_fetch_into' => 'Safe\odbc_fetch_into',
            'odbc_field_len' => 'Safe\odbc_field_len',
            'odbc_field_name' => 'Safe\odbc_field_name',
            'odbc_field_num' => 'Safe\odbc_field_num',
            'odbc_field_scale' => 'Safe\odbc_field_scale',
            'odbc_field_type' => 'Safe\odbc_field_type',
            'odbc_foreignkeys' => 'Safe\odbc_foreignkeys',
            'odbc_gettypeinfo' => 'Safe\odbc_gettypeinfo',
            'odbc_longreadlen' => 'Safe\odbc_longreadlen',
            'odbc_prepare' => 'Safe\odbc_prepare',
            'odbc_primarykeys' => 'Safe\odbc_primarykeys',
            'odbc_result' => 'Safe\odbc_result',
            'odbc_result_all' => 'Safe\odbc_result_all',
            'odbc_rollback' => 'Safe\odbc_rollback',
            'odbc_setoption' => 'Safe\odbc_setoption',
            'odbc_specialcolumns' => 'Safe\odbc_specialcolumns',
            'odbc_statistics' => 'Safe\odbc_statistics',
            'odbc_tableprivileges' => 'Safe\odbc_tableprivileges',
            'odbc_tables' => 'Safe\odbc_tables',
            'opcache_compile_file' => 'Safe\opcache_compile_file',
            'opcache_get_status' => 'Safe\opcache_get_status',
            'opendir' => 'Safe\opendir',
            'openlog' => 'Safe\openlog',
            'openssl_cipher_iv_length' => 'Safe\openssl_cipher_iv_length',
            'openssl_csr_export' => 'Safe\openssl_csr_export',
            'openssl_csr_export_to_file' => 'Safe\openssl_csr_export_to_file',
            'openssl_csr_get_subject' => 'Safe\openssl_csr_get_subject',
            'openssl_csr_new' => 'Safe\openssl_csr_new',
            'openssl_csr_sign' => 'Safe\openssl_csr_sign',
            'openssl_decrypt' => 'Safe\openssl_decrypt',
            'openssl_dh_compute_key' => 'Safe\openssl_dh_compute_key',
            'openssl_digest' => 'Safe\openssl_digest',
            'openssl_encrypt' => 'Safe\openssl_encrypt',
            'openssl_open' => 'Safe\openssl_open',
            'openssl_pbkdf2' => 'Safe\openssl_pbkdf2',
            'openssl_pkcs7_decrypt' => 'Safe\openssl_pkcs7_decrypt',
            'openssl_pkcs7_encrypt' => 'Safe\openssl_pkcs7_encrypt',
            'openssl_pkcs7_read' => 'Safe\openssl_pkcs7_read',
            'openssl_pkcs7_sign' => 'Safe\openssl_pkcs7_sign',
            'openssl_pkcs12_export' => 'Safe\openssl_pkcs12_export',
            'openssl_pkcs12_export_to_file' => 'Safe\openssl_pkcs12_export_to_file',
            'openssl_pkcs12_read' => 'Safe\openssl_pkcs12_read',
            'openssl_pkey_export' => 'Safe\openssl_pkey_export',
            'openssl_pkey_export_to_file' => 'Safe\openssl_pkey_export_to_file',
            'openssl_pkey_get_private' => 'Safe\openssl_pkey_get_private',
            'openssl_pkey_get_public' => 'Safe\openssl_pkey_get_public',
            'openssl_pkey_new' => 'Safe\openssl_pkey_new',
            'openssl_private_decrypt' => 'Safe\openssl_private_decrypt',
            'openssl_private_encrypt' => 'Safe\openssl_private_encrypt',
            'openssl_public_decrypt' => 'Safe\openssl_public_decrypt',
            'openssl_public_encrypt' => 'Safe\openssl_public_encrypt',
            'openssl_random_pseudo_bytes' => 'Safe\openssl_random_pseudo_bytes',
            'openssl_seal' => 'Safe\openssl_seal',
            'openssl_sign' => 'Safe\openssl_sign',
            'openssl_x509_export' => 'Safe\openssl_x509_export',
            'openssl_x509_export_to_file' => 'Safe\openssl_x509_export_to_file',
            'openssl_x509_fingerprint' => 'Safe\openssl_x509_fingerprint',
            'openssl_x509_read' => 'Safe\openssl_x509_read',
            'output_add_rewrite_var' => 'Safe\output_add_rewrite_var',
            'output_reset_rewrite_vars' => 'Safe\output_reset_rewrite_vars',
            'pack' => 'Safe\pack',
            'parse_ini_file' => 'Safe\parse_ini_file',
            'parse_ini_string' => 'Safe\parse_ini_string',
            'parse_url' => 'Safe\parse_url',
            'password_hash' => 'Safe\password_hash',
            'pcntl_exec' => 'Safe\pcntl_exec',
            'pcntl_getpriority' => 'Safe\pcntl_getpriority',
            'pcntl_setpriority' => 'Safe\pcntl_setpriority',
            'pcntl_signal_dispatch' => 'Safe\pcntl_signal_dispatch',
            'pcntl_sigprocmask' => 'Safe\pcntl_sigprocmask',
            'pcntl_strerror' => 'Safe\pcntl_strerror',
            'PDF_activate_item' => 'Safe\PDF_activate_item',
            'PDF_add_locallink' => 'Safe\PDF_add_locallink',
            'PDF_add_nameddest' => 'Safe\PDF_add_nameddest',
            'PDF_add_note' => 'Safe\PDF_add_note',
            'PDF_add_pdflink' => 'Safe\PDF_add_pdflink',
            'PDF_add_thumbnail' => 'Safe\PDF_add_thumbnail',
            'PDF_add_weblink' => 'Safe\PDF_add_weblink',
            'PDF_attach_file' => 'Safe\PDF_attach_file',
            'PDF_begin_layer' => 'Safe\PDF_begin_layer',
            'PDF_begin_page' => 'Safe\PDF_begin_page',
            'PDF_begin_page_ext' => 'Safe\PDF_begin_page_ext',
            'PDF_circle' => 'Safe\PDF_circle',
            'PDF_clip' => 'Safe\PDF_clip',
            'PDF_close' => 'Safe\PDF_close',
            'PDF_closepath' => 'Safe\PDF_closepath',
            'PDF_closepath_fill_stroke' => 'Safe\PDF_closepath_fill_stroke',
            'PDF_closepath_stroke' => 'Safe\PDF_closepath_stroke',
            'PDF_close_pdi' => 'Safe\PDF_close_pdi',
            'PDF_close_pdi_page' => 'Safe\PDF_close_pdi_page',
            'PDF_concat' => 'Safe\PDF_concat',
            'PDF_continue_text' => 'Safe\PDF_continue_text',
            'PDF_curveto' => 'Safe\PDF_curveto',
            'PDF_delete' => 'Safe\PDF_delete',
            'PDF_end_layer' => 'Safe\PDF_end_layer',
            'PDF_end_page' => 'Safe\PDF_end_page',
            'PDF_end_page_ext' => 'Safe\PDF_end_page_ext',
            'PDF_end_pattern' => 'Safe\PDF_end_pattern',
            'PDF_end_template' => 'Safe\PDF_end_template',
            'PDF_fill' => 'Safe\PDF_fill',
            'PDF_fill_stroke' => 'Safe\PDF_fill_stroke',
            'PDF_fit_image' => 'Safe\PDF_fit_image',
            'PDF_fit_pdi_page' => 'Safe\PDF_fit_pdi_page',
            'PDF_fit_textline' => 'Safe\PDF_fit_textline',
            'PDF_initgraphics' => 'Safe\PDF_initgraphics',
            'PDF_lineto' => 'Safe\PDF_lineto',
            'PDF_makespotcolor' => 'Safe\PDF_makespotcolor',
            'PDF_moveto' => 'Safe\PDF_moveto',
            'PDF_open_file' => 'Safe\PDF_open_file',
            'PDF_place_image' => 'Safe\PDF_place_image',
            'PDF_place_pdi_page' => 'Safe\PDF_place_pdi_page',
            'PDF_rect' => 'Safe\PDF_rect',
            'PDF_restore' => 'Safe\PDF_restore',
            'PDF_rotate' => 'Safe\PDF_rotate',
            'PDF_save' => 'Safe\PDF_save',
            'PDF_scale' => 'Safe\PDF_scale',
            'PDF_setcolor' => 'Safe\PDF_setcolor',
            'PDF_setdash' => 'Safe\PDF_setdash',
            'PDF_setdashpattern' => 'Safe\PDF_setdashpattern',
            'PDF_setflat' => 'Safe\PDF_setflat',
            'PDF_setfont' => 'Safe\PDF_setfont',
            'PDF_setgray' => 'Safe\PDF_setgray',
            'PDF_setgray_fill' => 'Safe\PDF_setgray_fill',
            'PDF_setgray_stroke' => 'Safe\PDF_setgray_stroke',
            'PDF_setlinejoin' => 'Safe\PDF_setlinejoin',
            'PDF_setlinewidth' => 'Safe\PDF_setlinewidth',
            'PDF_setmatrix' => 'Safe\PDF_setmatrix',
            'PDF_setmiterlimit' => 'Safe\PDF_setmiterlimit',
            'PDF_setrgbcolor' => 'Safe\PDF_setrgbcolor',
            'PDF_setrgbcolor_fill' => 'Safe\PDF_setrgbcolor_fill',
            'PDF_setrgbcolor_stroke' => 'Safe\PDF_setrgbcolor_stroke',
            'PDF_set_border_color' => 'Safe\PDF_set_border_color',
            'PDF_set_border_dash' => 'Safe\PDF_set_border_dash',
            'PDF_set_border_style' => 'Safe\PDF_set_border_style',
            'PDF_set_info' => 'Safe\PDF_set_info',
            'PDF_set_layer_dependency' => 'Safe\PDF_set_layer_dependency',
            'PDF_set_parameter' => 'Safe\PDF_set_parameter',
            'PDF_set_text_pos' => 'Safe\PDF_set_text_pos',
            'PDF_set_value' => 'Safe\PDF_set_value',
            'PDF_show' => 'Safe\PDF_show',
            'PDF_show_xy' => 'Safe\PDF_show_xy',
            'PDF_skew' => 'Safe\PDF_skew',
            'PDF_stroke' => 'Safe\PDF_stroke',
            'pg_cancel_query' => 'Safe\pg_cancel_query',
            'pg_client_encoding' => 'Safe\pg_client_encoding',
            'pg_close' => 'Safe\pg_close',
            'pg_connect' => 'Safe\pg_connect',
            'pg_connection_reset' => 'Safe\pg_connection_reset',
            'pg_convert' => 'Safe\pg_convert',
            'pg_copy_from' => 'Safe\pg_copy_from',
            'pg_copy_to' => 'Safe\pg_copy_to',
            'pg_dbname' => 'Safe\pg_dbname',
            'pg_delete' => 'Safe\pg_delete',
            'pg_end_copy' => 'Safe\pg_end_copy',
            'pg_execute' => 'Safe\pg_execute',
            'pg_field_name' => 'Safe\pg_field_name',
            'pg_field_table' => 'Safe\pg_field_table',
            'pg_field_type' => 'Safe\pg_field_type',
            'pg_flush' => 'Safe\pg_flush',
            'pg_free_result' => 'Safe\pg_free_result',
            'pg_host' => 'Safe\pg_host',
            'pg_insert' => 'Safe\pg_insert',
            'pg_last_error' => 'Safe\pg_last_error',
            'pg_last_notice' => 'Safe\pg_last_notice',
            'pg_last_oid' => 'Safe\pg_last_oid',
            'pg_lo_close' => 'Safe\pg_lo_close',
            'pg_lo_export' => 'Safe\pg_lo_export',
            'pg_lo_import' => 'Safe\pg_lo_import',
            'pg_lo_open' => 'Safe\pg_lo_open',
            'pg_lo_read' => 'Safe\pg_lo_read',
            'pg_lo_read_all' => 'Safe\pg_lo_read_all',
            'pg_lo_seek' => 'Safe\pg_lo_seek',
            'pg_lo_truncate' => 'Safe\pg_lo_truncate',
            'pg_lo_unlink' => 'Safe\pg_lo_unlink',
            'pg_lo_write' => 'Safe\pg_lo_write',
            'pg_meta_data' => 'Safe\pg_meta_data',
            'pg_options' => 'Safe\pg_options',
            'pg_parameter_status' => 'Safe\pg_parameter_status',
            'pg_pconnect' => 'Safe\pg_pconnect',
            'pg_ping' => 'Safe\pg_ping',
            'pg_port' => 'Safe\pg_port',
            'pg_prepare' => 'Safe\pg_prepare',
            'pg_put_line' => 'Safe\pg_put_line',
            'pg_query' => 'Safe\pg_query',
            'pg_query_params' => 'Safe\pg_query_params',
            'pg_result_error_field' => 'Safe\pg_result_error_field',
            'pg_result_seek' => 'Safe\pg_result_seek',
            'pg_select' => 'Safe\pg_select',
            'pg_send_execute' => 'Safe\pg_send_execute',
            'pg_send_prepare' => 'Safe\pg_send_prepare',
            'pg_send_query' => 'Safe\pg_send_query',
            'pg_send_query_params' => 'Safe\pg_send_query_params',
            'pg_socket' => 'Safe\pg_socket',
            'pg_trace' => 'Safe\pg_trace',
            'pg_tty' => 'Safe\pg_tty',
            'pg_update' => 'Safe\pg_update',
            'pg_version' => 'Safe\pg_version',
            'phpcredits' => 'Safe\phpcredits',
            'phpinfo' => 'Safe\phpinfo',
            'png2wbmp' => 'Safe\png2wbmp',
            'posix_access' => 'Safe\posix_access',
            'posix_getgrnam' => 'Safe\posix_getgrnam',
            'posix_getpgid' => 'Safe\posix_getpgid',
            'posix_initgroups' => 'Safe\posix_initgroups',
            'posix_kill' => 'Safe\posix_kill',
            'posix_mkfifo' => 'Safe\posix_mkfifo',
            'posix_mknod' => 'Safe\posix_mknod',
            'posix_setegid' => 'Safe\posix_setegid',
            'posix_seteuid' => 'Safe\posix_seteuid',
            'posix_setgid' => 'Safe\posix_setgid',
            'posix_setpgid' => 'Safe\posix_setpgid',
            'posix_setrlimit' => 'Safe\posix_setrlimit',
            'posix_setuid' => 'Safe\posix_setuid',
            'preg_match' => 'Safe\preg_match',
            'preg_match_all' => 'Safe\preg_match_all',
            'preg_replace' => 'Safe\preg_replace',
            'preg_split' => 'Safe\preg_split',
            'proc_get_status' => 'Safe\proc_get_status',
            'proc_nice' => 'Safe\proc_nice',
            'pspell_add_to_personal' => 'Safe\pspell_add_to_personal',
            'pspell_add_to_session' => 'Safe\pspell_add_to_session',
            'pspell_clear_session' => 'Safe\pspell_clear_session',
            'pspell_config_create' => 'Safe\pspell_config_create',
            'pspell_config_data_dir' => 'Safe\pspell_config_data_dir',
            'pspell_config_dict_dir' => 'Safe\pspell_config_dict_dir',
            'pspell_config_ignore' => 'Safe\pspell_config_ignore',
            'pspell_config_mode' => 'Safe\pspell_config_mode',
            'pspell_config_personal' => 'Safe\pspell_config_personal',
            'pspell_config_repl' => 'Safe\pspell_config_repl',
            'pspell_config_runtogether' => 'Safe\pspell_config_runtogether',
            'pspell_config_save_repl' => 'Safe\pspell_config_save_repl',
            'pspell_new' => 'Safe\pspell_new',
            'pspell_new_config' => 'Safe\pspell_new_config',
            'pspell_save_wordlist' => 'Safe\pspell_save_wordlist',
            'pspell_store_replacement' => 'Safe\pspell_store_replacement',
            'ps_add_launchlink' => 'Safe\ps_add_launchlink',
            'ps_add_locallink' => 'Safe\ps_add_locallink',
            'ps_add_note' => 'Safe\ps_add_note',
            'ps_add_pdflink' => 'Safe\ps_add_pdflink',
            'ps_add_weblink' => 'Safe\ps_add_weblink',
            'ps_arc' => 'Safe\ps_arc',
            'ps_arcn' => 'Safe\ps_arcn',
            'ps_begin_page' => 'Safe\ps_begin_page',
            'ps_begin_pattern' => 'Safe\ps_begin_pattern',
            'ps_begin_template' => 'Safe\ps_begin_template',
            'ps_circle' => 'Safe\ps_circle',
            'ps_clip' => 'Safe\ps_clip',
            'ps_close' => 'Safe\ps_close',
            'ps_closepath' => 'Safe\ps_closepath',
            'ps_closepath_stroke' => 'Safe\ps_closepath_stroke',
            'ps_close_image' => 'Safe\ps_close_image',
            'ps_continue_text' => 'Safe\ps_continue_text',
            'ps_curveto' => 'Safe\ps_curveto',
            'ps_delete' => 'Safe\ps_delete',
            'ps_end_page' => 'Safe\ps_end_page',
            'ps_end_pattern' => 'Safe\ps_end_pattern',
            'ps_end_template' => 'Safe\ps_end_template',
            'ps_fill' => 'Safe\ps_fill',
            'ps_fill_stroke' => 'Safe\ps_fill_stroke',
            'ps_get_parameter' => 'Safe\ps_get_parameter',
            'ps_hyphenate' => 'Safe\ps_hyphenate',
            'ps_include_file' => 'Safe\ps_include_file',
            'ps_lineto' => 'Safe\ps_lineto',
            'ps_moveto' => 'Safe\ps_moveto',
            'ps_new' => 'Safe\ps_new',
            'ps_open_file' => 'Safe\ps_open_file',
            'ps_place_image' => 'Safe\ps_place_image',
            'ps_rect' => 'Safe\ps_rect',
            'ps_restore' => 'Safe\ps_restore',
            'ps_rotate' => 'Safe\ps_rotate',
            'ps_save' => 'Safe\ps_save',
            'ps_scale' => 'Safe\ps_scale',
            'ps_setcolor' => 'Safe\ps_setcolor',
            'ps_setdash' => 'Safe\ps_setdash',
            'ps_setflat' => 'Safe\ps_setflat',
            'ps_setfont' => 'Safe\ps_setfont',
            'ps_setgray' => 'Safe\ps_setgray',
            'ps_setlinecap' => 'Safe\ps_setlinecap',
            'ps_setlinejoin' => 'Safe\ps_setlinejoin',
            'ps_setlinewidth' => 'Safe\ps_setlinewidth',
            'ps_setmiterlimit' => 'Safe\ps_setmiterlimit',
            'ps_setoverprintmode' => 'Safe\ps_setoverprintmode',
            'ps_setpolydash' => 'Safe\ps_setpolydash',
            'ps_set_border_color' => 'Safe\ps_set_border_color',
            'ps_set_border_dash' => 'Safe\ps_set_border_dash',
            'ps_set_border_style' => 'Safe\ps_set_border_style',
            'ps_set_info' => 'Safe\ps_set_info',
            'ps_set_parameter' => 'Safe\ps_set_parameter',
            'ps_set_text_pos' => 'Safe\ps_set_text_pos',
            'ps_set_value' => 'Safe\ps_set_value',
            'ps_shading' => 'Safe\ps_shading',
            'ps_shading_pattern' => 'Safe\ps_shading_pattern',
            'ps_shfill' => 'Safe\ps_shfill',
            'ps_show' => 'Safe\ps_show',
            'ps_show2' => 'Safe\ps_show2',
            'ps_show_xy' => 'Safe\ps_show_xy',
            'ps_show_xy2' => 'Safe\ps_show_xy2',
            'ps_stroke' => 'Safe\ps_stroke',
            'ps_symbol' => 'Safe\ps_symbol',
            'ps_translate' => 'Safe\ps_translate',
            'putenv' => 'Safe\putenv',
            'readfile' => 'Safe\readfile',
            'readgzfile' => 'Safe\readgzfile',
            'readline_add_history' => 'Safe\readline_add_history',
            'readline_callback_handler_install' => 'Safe\readline_callback_handler_install',
            'readline_clear_history' => 'Safe\readline_clear_history',
            'readline_completion_function' => 'Safe\readline_completion_function',
            'readline_read_history' => 'Safe\readline_read_history',
            'readline_write_history' => 'Safe\readline_write_history',
            'readlink' => 'Safe\readlink',
            'realpath' => 'Safe\realpath',
            'register_tick_function' => 'Safe\register_tick_function',
            'rename' => 'Safe\rename',
            'rewind' => 'Safe\rewind',
            'rewinddir' => 'Safe\rewinddir',
            'rmdir' => 'Safe\rmdir',
            'rpmaddtag' => 'Safe\rpmaddtag',
            'rrd_create' => 'Safe\rrd_create',
            'rsort' => 'Safe\rsort',
            'sapi_windows_cp_conv' => 'Safe\sapi_windows_cp_conv',
            'sapi_windows_cp_set' => 'Safe\sapi_windows_cp_set',
            'sapi_windows_generate_ctrl_event' => 'Safe\sapi_windows_generate_ctrl_event',
            'sapi_windows_vt100_support' => 'Safe\sapi_windows_vt100_support',
            'scandir' => 'Safe\scandir',
            'sem_acquire' => 'Safe\sem_acquire',
            'sem_get' => 'Safe\sem_get',
            'sem_release' => 'Safe\sem_release',
            'sem_remove' => 'Safe\sem_remove',
            'session_abort' => 'Safe\session_abort',
            'session_decode' => 'Safe\session_decode',
            'session_destroy' => 'Safe\session_destroy',
            'session_regenerate_id' => 'Safe\session_regenerate_id',
            'session_reset' => 'Safe\session_reset',
            'session_unset' => 'Safe\session_unset',
            'session_write_close' => 'Safe\session_write_close',
            'settype' => 'Safe\settype',
            'set_include_path' => 'Safe\set_include_path',
            'set_time_limit' => 'Safe\set_time_limit',
            'sha1_file' => 'Safe\sha1_file',
            'shmop_delete' => 'Safe\shmop_delete',
            'shmop_read' => 'Safe\shmop_read',
            'shmop_write' => 'Safe\shmop_write',
            'shm_put_var' => 'Safe\shm_put_var',
            'shm_remove' => 'Safe\shm_remove',
            'shm_remove_var' => 'Safe\shm_remove_var',
            'shuffle' => 'Safe\shuffle',
            'simplexml_import_dom' => 'Safe\simplexml_import_dom',
            'simplexml_load_file' => 'Safe\simplexml_load_file',
            'simplexml_load_string' => 'Safe\simplexml_load_string',
            'sleep' => 'Safe\sleep',
            'socket_accept' => 'Safe\socket_accept',
            'socket_addrinfo_bind' => 'Safe\socket_addrinfo_bind',
            'socket_addrinfo_connect' => 'Safe\socket_addrinfo_connect',
            'socket_bind' => 'Safe\socket_bind',
            'socket_connect' => 'Safe\socket_connect',
            'socket_create' => 'Safe\socket_create',
            'socket_create_listen' => 'Safe\socket_create_listen',
            'socket_create_pair' => 'Safe\socket_create_pair',
            'socket_export_stream' => 'Safe\socket_export_stream',
            'socket_getpeername' => 'Safe\socket_getpeername',
            'socket_getsockname' => 'Safe\socket_getsockname',
            'socket_get_option' => 'Safe\socket_get_option',
            'socket_import_stream' => 'Safe\socket_import_stream',
            'socket_listen' => 'Safe\socket_listen',
            'socket_read' => 'Safe\socket_read',
            'socket_send' => 'Safe\socket_send',
            'socket_sendmsg' => 'Safe\socket_sendmsg',
            'socket_sendto' => 'Safe\socket_sendto',
            'socket_set_block' => 'Safe\socket_set_block',
            'socket_set_nonblock' => 'Safe\socket_set_nonblock',
            'socket_set_option' => 'Safe\socket_set_option',
            'socket_shutdown' => 'Safe\socket_shutdown',
            'socket_write' => 'Safe\socket_write',
            'socket_wsaprotocol_info_export' => 'Safe\socket_wsaprotocol_info_export',
            'socket_wsaprotocol_info_import' => 'Safe\socket_wsaprotocol_info_import',
            'socket_wsaprotocol_info_release' => 'Safe\socket_wsaprotocol_info_release',
            'sodium_crypto_pwhash' => 'Safe\sodium_crypto_pwhash',
            'sodium_crypto_pwhash_str' => 'Safe\sodium_crypto_pwhash_str',
            'solr_get_version' => 'Safe\solr_get_version',
            'sort' => 'Safe\sort',
            'soundex' => 'Safe\soundex',
            'spl_autoload_register' => 'Safe\spl_autoload_register',
            'spl_autoload_unregister' => 'Safe\spl_autoload_unregister',
            'sprintf' => 'Safe\sprintf',
            'sqlsrv_begin_transaction' => 'Safe\sqlsrv_begin_transaction',
            'sqlsrv_cancel' => 'Safe\sqlsrv_cancel',
            'sqlsrv_client_info' => 'Safe\sqlsrv_client_info',
            'sqlsrv_close' => 'Safe\sqlsrv_close',
            'sqlsrv_commit' => 'Safe\sqlsrv_commit',
            'sqlsrv_configure' => 'Safe\sqlsrv_configure',
            'sqlsrv_execute' => 'Safe\sqlsrv_execute',
            'sqlsrv_free_stmt' => 'Safe\sqlsrv_free_stmt',
            'sqlsrv_get_field' => 'Safe\sqlsrv_get_field',
            'sqlsrv_next_result' => 'Safe\sqlsrv_next_result',
            'sqlsrv_num_fields' => 'Safe\sqlsrv_num_fields',
            'sqlsrv_num_rows' => 'Safe\sqlsrv_num_rows',
            'sqlsrv_prepare' => 'Safe\sqlsrv_prepare',
            'sqlsrv_query' => 'Safe\sqlsrv_query',
            'sqlsrv_rollback' => 'Safe\sqlsrv_rollback',
            'ssdeep_fuzzy_compare' => 'Safe\ssdeep_fuzzy_compare',
            'ssdeep_fuzzy_hash' => 'Safe\ssdeep_fuzzy_hash',
            'ssdeep_fuzzy_hash_filename' => 'Safe\ssdeep_fuzzy_hash_filename',
            'ssh2_auth_agent' => 'Safe\ssh2_auth_agent',
            'ssh2_auth_hostbased_file' => 'Safe\ssh2_auth_hostbased_file',
            'ssh2_auth_password' => 'Safe\ssh2_auth_password',
            'ssh2_auth_pubkey_file' => 'Safe\ssh2_auth_pubkey_file',
            'ssh2_connect' => 'Safe\ssh2_connect',
            'ssh2_disconnect' => 'Safe\ssh2_disconnect',
            'ssh2_exec' => 'Safe\ssh2_exec',
            'ssh2_publickey_add' => 'Safe\ssh2_publickey_add',
            'ssh2_publickey_init' => 'Safe\ssh2_publickey_init',
            'ssh2_publickey_remove' => 'Safe\ssh2_publickey_remove',
            'ssh2_scp_recv' => 'Safe\ssh2_scp_recv',
            'ssh2_scp_send' => 'Safe\ssh2_scp_send',
            'ssh2_sftp' => 'Safe\ssh2_sftp',
            'ssh2_sftp_chmod' => 'Safe\ssh2_sftp_chmod',
            'ssh2_sftp_mkdir' => 'Safe\ssh2_sftp_mkdir',
            'ssh2_sftp_rename' => 'Safe\ssh2_sftp_rename',
            'ssh2_sftp_rmdir' => 'Safe\ssh2_sftp_rmdir',
            'ssh2_sftp_symlink' => 'Safe\ssh2_sftp_symlink',
            'ssh2_sftp_unlink' => 'Safe\ssh2_sftp_unlink',
            'stream_context_set_params' => 'Safe\stream_context_set_params',
            'stream_copy_to_stream' => 'Safe\stream_copy_to_stream',
            'stream_filter_append' => 'Safe\stream_filter_append',
            'stream_filter_prepend' => 'Safe\stream_filter_prepend',
            'stream_filter_register' => 'Safe\stream_filter_register',
            'stream_filter_remove' => 'Safe\stream_filter_remove',
            'stream_get_contents' => 'Safe\stream_get_contents',
            'stream_isatty' => 'Safe\stream_isatty',
            'stream_resolve_include_path' => 'Safe\stream_resolve_include_path',
            'stream_set_blocking' => 'Safe\stream_set_blocking',
            'stream_set_timeout' => 'Safe\stream_set_timeout',
            'stream_socket_accept' => 'Safe\stream_socket_accept',
            'stream_socket_client' => 'Safe\stream_socket_client',
            'stream_socket_pair' => 'Safe\stream_socket_pair',
            'stream_socket_server' => 'Safe\stream_socket_server',
            'stream_socket_shutdown' => 'Safe\stream_socket_shutdown',
            'stream_supports_lock' => 'Safe\stream_supports_lock',
            'stream_wrapper_register' => 'Safe\stream_wrapper_register',
            'stream_wrapper_restore' => 'Safe\stream_wrapper_restore',
            'stream_wrapper_unregister' => 'Safe\stream_wrapper_unregister',
            'strptime' => 'Safe\strptime',
            'strtotime' => 'Safe\strtotime',
            'substr' => 'Safe\substr',
            'swoole_async_write' => 'Safe\swoole_async_write',
            'swoole_async_writefile' => 'Safe\swoole_async_writefile',
            'swoole_event_defer' => 'Safe\swoole_event_defer',
            'swoole_event_del' => 'Safe\swoole_event_del',
            'swoole_event_write' => 'Safe\swoole_event_write',
            'symlink' => 'Safe\symlink',
            'syslog' => 'Safe\syslog',
            'system' => 'Safe\system',
            'tempnam' => 'Safe\tempnam',
            'timezone_name_from_abbr' => 'Safe\timezone_name_from_abbr',
            'time_nanosleep' => 'Safe\time_nanosleep',
            'time_sleep_until' => 'Safe\time_sleep_until',
            'tmpfile' => 'Safe\tmpfile',
            'touch' => 'Safe\touch',
            'uasort' => 'Safe\uasort',
            'uksort' => 'Safe\uksort',
            'unlink' => 'Safe\unlink',
            'unpack' => 'Safe\unpack',
            'uopz_extend' => 'Safe\uopz_extend',
            'uopz_implement' => 'Safe\uopz_implement',
            'usort' => 'Safe\usort',
            'virtual' => 'Safe\virtual',
            'vsprintf' => 'Safe\vsprintf',
            'xdiff_file_bdiff' => 'Safe\xdiff_file_bdiff',
            'xdiff_file_bpatch' => 'Safe\xdiff_file_bpatch',
            'xdiff_file_diff' => 'Safe\xdiff_file_diff',
            'xdiff_file_diff_binary' => 'Safe\xdiff_file_diff_binary',
            'xdiff_file_patch_binary' => 'Safe\xdiff_file_patch_binary',
            'xdiff_file_rabdiff' => 'Safe\xdiff_file_rabdiff',
            'xdiff_string_bpatch' => 'Safe\xdiff_string_bpatch',
            'xdiff_string_patch' => 'Safe\xdiff_string_patch',
            'xdiff_string_patch_binary' => 'Safe\xdiff_string_patch_binary',
            'xmlrpc_set_type' => 'Safe\xmlrpc_set_type',
            'xml_parser_create' => 'Safe\xml_parser_create',
            'xml_parser_create_ns' => 'Safe\xml_parser_create_ns',
            'xml_set_object' => 'Safe\xml_set_object',
            'yaml_parse' => 'Safe\yaml_parse',
            'yaml_parse_file' => 'Safe\yaml_parse_file',
            'yaml_parse_url' => 'Safe\yaml_parse_url',
            'yaz_ccl_parse' => 'Safe\yaz_ccl_parse',
            'yaz_close' => 'Safe\yaz_close',
            'yaz_connect' => 'Safe\yaz_connect',
            'yaz_database' => 'Safe\yaz_database',
            'yaz_element' => 'Safe\yaz_element',
            'yaz_present' => 'Safe\yaz_present',
            'yaz_search' => 'Safe\yaz_search',
            'yaz_wait' => 'Safe\yaz_wait',
            'zip_entry_close' => 'Safe\zip_entry_close',
            'zip_entry_open' => 'Safe\zip_entry_open',
            'zip_entry_read' => 'Safe\zip_entry_read',
            'zlib_decode' => 'Safe\zlib_decode',
]]]);
};
