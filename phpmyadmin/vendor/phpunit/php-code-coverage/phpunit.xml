<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/6.0/phpunit.xsd"
         bootstrap="tests/bootstrap.php"
         backupGlobals="false"
         verbose="true">
    <testsuite name="default">
        <directory suffix="Test.php">tests/tests</directory>
    </testsuite>

    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">src</directory>
        </whitelist>
    </filter>

    <php>
        <ini name="serialize_precision" value="14"/>
    </php>
</phpunit>

