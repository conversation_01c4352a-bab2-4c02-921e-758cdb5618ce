<?xml version="1.0"?>
<phpunit xmlns="https://schema.phpunit.de/coverage/1.0">
  <build time="%s" phpunit="%s" coverage="%s">
    <runtime name="%s" version="%s" url="%s"/>
    <driver%S/>
  </build>
  <project source="%s">
    <tests>
      <test name="FileWithIgnoredLines" size="unknown" result="-1" status="UNKNOWN"/>
    </tests>
    <directory name="%s">
      <totals>
        <lines total="37" comments="12" code="25" executable="2" executed="1" percent="50.00"/>
        <methods count="0" tested="0" percent="0"/>
        <functions count="1" tested="1" percent="100.00"/>
        <classes count="0" tested="0" percent="0"/>
        <traits count="0" tested="0" percent="0"/>
      </totals>
      <file name="source_with_ignore.php" href="source_with_ignore.php.xml">
        <totals>
          <lines total="37" comments="12" code="25" executable="2" executed="1" percent="50.00"/>
          <methods count="0" tested="0" percent="0"/>
          <functions count="1" tested="1" percent="100.00"/>
          <classes count="0" tested="0" percent="0"/>
          <traits count="0" tested="0" percent="0"/>
        </totals>
      </file>
    </directory>
  </project>
</phpunit>
