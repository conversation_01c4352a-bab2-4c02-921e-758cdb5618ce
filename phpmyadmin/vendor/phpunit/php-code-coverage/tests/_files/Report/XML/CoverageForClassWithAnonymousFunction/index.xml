<?xml version="1.0"?>
<phpunit xmlns="https://schema.phpunit.de/coverage/1.0">
  <build time="%s" phpunit="%s" coverage="%s">
    <runtime name="%s" version="%s" url="%s"/>
    <driver%S/>
  </build>
  <project source="%s">
    <tests>
      <test name="ClassWithAnonymousFunction" size="unknown" result="-1" status="UNKNOWN"/>
    </tests>
    <directory name="%s">
      <totals>
        <lines total="19" comments="2" code="17" executable="8" executed="7" percent="87.50"/>
        <methods count="1" tested="0" percent="0.00"/>
        <functions count="0" tested="0" percent="0"/>
        <classes count="1" tested="0" percent="0.00"/>
        <traits count="0" tested="0" percent="0"/>
      </totals>
      <file name="source_with_class_and_anonymous_function.php" href="source_with_class_and_anonymous_function.php.xml">
        <totals>
          <lines total="19" comments="2" code="17" executable="8" executed="7" percent="87.50"/>
          <methods count="1" tested="0" percent="0.00"/>
          <functions count="0" tested="0" percent="0"/>
          <classes count="1" tested="0" percent="0.00"/>
          <traits count="0" tested="0" percent="0"/>
        </totals>
      </file>
    </directory>
  </project>
</phpunit>
