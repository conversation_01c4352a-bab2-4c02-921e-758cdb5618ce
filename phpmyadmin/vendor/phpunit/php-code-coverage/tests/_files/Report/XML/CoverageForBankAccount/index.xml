<?xml version="1.0"?>
<phpunit xmlns="https://schema.phpunit.de/coverage/1.0">
  <build time="%s" phpunit="%s" coverage="%s">
    <runtime name="%s" version="%s" url="%s"/>
    <driver%S/>
  </build>
  <project source="%s">
    <tests>
      <test name="BankAccountTest::testBalanceIsInitiallyZero" size="unknown" result="-1" status="UNKNOWN"/>
      <test name="BankAccountTest::testBalanceCannotBecomeNegative" size="unknown" result="-1" status="UNKNOWN"/>
      <test name="BankAccountTest::testBalanceCannotBecomeNegative2" size="unknown" result="-1" status="UNKNOWN"/>
      <test name="BankAccountTest::testDepositWithdrawMoney" size="unknown" result="-1" status="UNKNOWN"/>
    </tests>
    <directory name="%s">
      <totals>
        <lines total="33" comments="0" code="33" executable="10" executed="5" percent="50.00"/>
        <methods count="4" tested="3" percent="75.00"/>
        <functions count="0" tested="0" percent="0"/>
        <classes count="1" tested="0" percent="0.00"/>
        <traits count="0" tested="0" percent="0"/>
      </totals>
      <file name="BankAccount.php" href="BankAccount.php.xml">
        <totals>
          <lines total="33" comments="0" code="33" executable="10" executed="5" percent="50.00"/>
          <methods count="4" tested="3" percent="75.00"/>
          <functions count="0" tested="0" percent="0"/>
          <classes count="1" tested="0" percent="0.00"/>
          <traits count="0" tested="0" percent="0"/>
        </totals>
      </file>
    </directory>
  </project>
</phpunit>
