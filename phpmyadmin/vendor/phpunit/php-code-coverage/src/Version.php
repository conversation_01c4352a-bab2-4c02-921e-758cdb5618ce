<?php declare(strict_types=1);
/*
 * This file is part of the php-code-coverage package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace <PERSON><PERSON><PERSON>\CodeCoverage;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Version as VersionId;

final class Version
{
    /**
     * @var string
     */
    private static $version;

    public static function id(): string
    {
        if (self::$version === null) {
            $version       = new VersionId('7.0.15', \dirname(__DIR__));
            self::$version = $version->getVersion();
        }

        return self::$version;
    }
}
