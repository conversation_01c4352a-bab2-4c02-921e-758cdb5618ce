<?php
namespace PHPSTORM_META {

    override(
        \PHPUnit\Framework\TestCase::createMock(0),
        map([""=>"$0"])
    );

    override(
        \PHPUnit\Framework\TestCase::createStub(0),
        map([""=>"$0"])
    );

    override(
        \PHPUnit\Framework\TestCase::createConfiguredMock(0),
        map([""=>"$0"])
    );

    override(
        \PHPUnit\Framework\TestCase::createPartialMock(0),
        map([""=>"$0"])
    );

    override(
        \PHPUnit\Framework\TestCase::createTestProxy(0),
        map([""=>"$0"])
    );

    override(
        \PHPUnit\Framework\TestCase::getMockForAbstractClass(0),
        map([""=>"$0"])
    );
}
