{"name": "phpunit/phpunit", "description": "The PHP Unit Testing framework.", "type": "library", "keywords": ["phpunit", "xunit", "testing"], "homepage": "https://phpunit.de/", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues"}, "prefer-stable": true, "require": {"php": ">=7.2", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "doctrine/instantiator": "^1.3.1", "myclabs/deep-copy": "^1.10.0", "phar-io/manifest": "^2.0.3", "phar-io/version": "^3.0.2", "phpunit/php-code-coverage": "^7.0.12", "phpunit/php-file-iterator": "^2.0.4", "phpunit/php-text-template": "^1.2.1", "phpunit/php-timer": "^2.1.2", "sebastian/comparator": "^3.0.5", "sebastian/diff": "^3.0.2", "sebastian/environment": "^4.2.3", "sebastian/exporter": "^3.1.5", "sebastian/global-state": "^3.0.0", "sebastian/object-enumerator": "^3.0.3", "sebastian/resource-operations": "^2.0.1", "sebastian/type": "^1.1.3", "sebastian/version": "^2.0.1"}, "config": {"platform": {"php": "7.2.0"}, "optimize-autoloader": true, "sort-packages": true}, "suggest": {"phpunit/php-invoker": "^2.0.0", "ext-soap": "*", "ext-xdebug": "*"}, "bin": ["phpunit"], "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/"], "files": ["src/Framework/Assert/Functions.php", "tests/_files/CoverageNamespacedFunctionTest.php", "tests/_files/CoveredFunction.php", "tests/_files/NamespaceCoveredFunction.php"]}, "extra": {"branch-alias": {"dev-master": "8.5-dev"}}}