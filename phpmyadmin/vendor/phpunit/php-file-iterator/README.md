[![Build Status](https://travis-ci.org/sebastian<PERSON><PERSON>/php-file-iterator.svg?branch=master)](https://travis-ci.org/sebastian<PERSON>mann/php-file-iterator)

# php-file-iterator

## Installation

You can add this library as a local, per-project dependency to your project using [Composer](https://getcomposer.org/):

    composer require phpunit/php-file-iterator

If you only need this library during development, for instance to run your project's test suite, then you should add it as a development-time dependency:

    composer require --dev phpunit/php-file-iterator

