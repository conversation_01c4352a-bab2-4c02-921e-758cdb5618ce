{"name": "phpunit/php-file-iterator", "description": "FilterIterator implementation that filters files based on a list of suffixes.", "type": "library", "keywords": ["iterator", "filesystem"], "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues"}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}}