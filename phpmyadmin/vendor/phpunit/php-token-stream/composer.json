{"name": "phpunit/php-token-stream", "description": "Wrapper around PHP's tokenizer extension.", "type": "library", "keywords": ["tokenizer"], "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-token-stream/issues"}, "prefer-stable": true, "require": {"php": ">=7.1", "ext-tokenizer": "*"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "config": {"optimize-autoloader": true, "sort-packages": true}, "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-master": "3.1-dev"}}}