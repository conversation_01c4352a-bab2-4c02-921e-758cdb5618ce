{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "f2ac4614ce4f7273fd54a64b65fd047a", "packages": [{"name": "phar-io/version", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "d06a5000ac1a258a7d035295f0bd4ae7c859bc4f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/d06a5000ac1a258a7d035295f0bd4ae7c859bc4f", "reference": "d06a5000ac1a258a7d035295f0bd4ae7c859bc4f", "shasum": ""}, "require": {"php": "^7.2"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "time": "2020-05-09T21:27:55+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "^7.2", "ext-dom": "*", "ext-phar": "*", "ext-xmlwriter": "*"}, "platform-dev": []}