name: "Continuous Integration"

on:
  - pull_request
  - push

env:
  COMPOSER_ROOT_VERSION: 1.99

jobs:
  composer-json-lint:
    name: "Lint composer.json"

    runs-on: "ubuntu-latest"

    strategy:
      matrix:
        php-version:
          - "8.1"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v2"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          coverage: "none"
          php-version: "${{ matrix.php-version }}"
          tools: composer-normalize

      - name: "Get composer cache directory"
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"

      - name: "Cache dependencies"
        uses: actions/cache@v2
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-php-${{ matrix.php-version }}-${{ matrix.dependencies }}-composer-${{ hashFiles('**/composer.json') }}
          restore-keys: ${{ runner.os }}-php-${{ matrix.php-version }}-${{ matrix.dependencies }}-composer-

      - name: "Install dependencies"
        run: "composer update --no-interaction --no-progress"

      - name: "Validate composer.json"
        run: "composer validate --strict"

      - name: "Normalize composer.json"
        run: "composer-normalize --dry-run"

  tests:
    name: "Tests"

    runs-on: "ubuntu-latest"

    strategy:
      matrix:
        php-version:
          - "7.1"
          - "7.2"
          - "7.3"
          - "7.4"
          - "8.0"
          - "8.1"
        dependencies:
          - "lowest"
          - "highest"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v2"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          php-version: "${{ matrix.php-version }}"
          ini-values: zend.assertions=1

      - name: "Get composer cache directory"
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"

      - name: "Cache dependencies"
        uses: actions/cache@v2
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-php-${{ matrix.php-version }}-${{ matrix.dependencies }}-composer-${{ hashFiles('**/composer.json') }}
          restore-keys: ${{ runner.os }}-php-${{ matrix.php-version }}-${{ matrix.dependencies }}-composer-

      - name: "Install lowest dependencies"
        if: ${{ matrix.dependencies == 'lowest' }}
        run: "composer update --no-interaction --no-progress --prefer-lowest"

      - name: "Install highest dependencies"
        if: ${{ matrix.dependencies == 'highest' }}
        run: "composer update --no-interaction --no-progress"

      - name: "Run tests"
        timeout-minutes: 3
        run: "vendor/bin/phpunit"
