{"name": "squizlabs/php_codesniffer", "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "type": "library", "keywords": ["phpcs", "standards"], "homepage": "https://github.com/squizlabs/PHP_CodeSniffer", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "role": "lead"}], "support": {"issues": "https://github.com/squizlabs/PHP_CodeSniffer/issues", "wiki": "https://github.com/squizlabs/PHP_CodeSniffer/wiki", "source": "https://github.com/squizlabs/PHP_CodeSniffer"}, "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "require": {"php": ">=5.4.0", "ext-tokenizer": "*", "ext-xmlwriter": "*", "ext-simplexml": "*"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "bin": ["bin/phpcs", "bin/phpcbf"]}