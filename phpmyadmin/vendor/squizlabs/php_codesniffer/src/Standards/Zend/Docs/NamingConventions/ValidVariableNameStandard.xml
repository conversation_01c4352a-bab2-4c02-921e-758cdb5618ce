<documentation title="Variable Names">
    <standard>
    <![CDATA[
    Variable names should be camelCased with the first letter lowercase.  Private and protected member variables should begin with an underscore
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: A multi-word variable uses camel casing.">
        <![CDATA[
<em>$testNumber</em> = 1;
        ]]>
        </code>
        <code title="Invalid: A multi-word variable uses underscores and initial capitalization.">
        <![CDATA[
<em>$Test_Number</em> = 1;
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: A private member variable begins with an underscore.">
        <![CDATA[
class Foo
{
    private $<em>_</em>bar;
}
        ]]>
        </code>
        <code title="Invalid: A private member variable does not begin with an underscore.">
        <![CDATA[
class Foo
{
    private $bar;
}
        ]]>
        </code>
    </code_comparison>
</documentation>
