<?php


/**
 * This file contains an example of coding styles.
 */
declare(strict_types=1);


namespace Vendor\Package;

use Vendor\Package\{ClassA as A, ClassB, ClassC as C};
// Comments are probably ok; PSR-12 doesn't say.
use Vendor\Package\SomeNamespace\ClassD as D;
use Vendor\Package\AnotherNamespace\ClassE as E;

// Comments are probably ok; PSR-12 doesn't say.
use function Vendor\Package\{functionA, functionB, functionC};

use function Another\Vendor\functionD;


use const Vendor\Package\{CONSTANT_A, CONSTANT_B, CONSTANT_C};
use const Another\Vendor\CONSTANT_D;


/**
 * FooBar is an example class.
 */
class FooBar
{
    // ... additional PHP code ...
}
