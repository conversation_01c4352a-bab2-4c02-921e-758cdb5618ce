<?php
/**
 * Unit test class for the NullableWhitespace sniff.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2006-2018 Squiz Pty Ltd (ABN **************)
 * @license   https://github.com/squizlabs/PHP_CodeSniffer/blob/master/licence.txt BSD Licence
 */

namespace PHP_CodeSniffer\Standards\PSR12\Tests\Functions;

use PHP_CodeSniffer\Tests\Standards\AbstractSniffUnitTest;

class NullableTypeDeclarationUnitTest extends AbstractSniffUnitTest
{


    /**
     * Returns the lines where errors should occur.
     *
     * The key of the array should represent the line number and the value
     * should represent the number of errors that should occur on that line.
     *
     * @return array<int, int>
     */
    protected function getErrorList()
    {
        return [
            23 => 1,
            24 => 1,
            25 => 1,
            30 => 1,
            31 => 1,
            32 => 1,
            43 => 2,
            48 => 1,
            50 => 1,
            51 => 1,
            53 => 1,
            57 => 2,
            58 => 2,
            59 => 2,
            87 => 1,
        ];

    }//end getErrorList()


    /**
     * Returns the lines where warnings should occur.
     *
     * The key of the array should represent the line number and the value
     * should represent the number of warnings that should occur on that line.
     *
     * @return array<int, int>
     */
    protected function getWarningList()
    {
        return [];

    }//end getWarningList()


}//end class
