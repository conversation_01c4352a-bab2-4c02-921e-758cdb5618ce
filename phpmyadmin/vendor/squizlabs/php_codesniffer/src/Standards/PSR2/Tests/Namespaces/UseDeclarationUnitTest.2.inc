<?php
namespace MyProject;

use  Bar<PERSON><PERSON> as Bar;
use My\Full\Classname as Another, My\Full\NSname;
use function My\Full\functionname as somefunction, My\Full\otherfunction;
use const My\Full\constantname as someconstant, My\Full\otherconstant;

use BarClass as Bar,FooClass,BazClass as Baz;
use function My\Full\functionname as somefunction,My\Full\otherfunction;
use const My\Full\constantname as someconstant,My\Full\otherconstant;


namespace AnotherProject;

use ArrayObject;


$foo = 'bar';

?>
