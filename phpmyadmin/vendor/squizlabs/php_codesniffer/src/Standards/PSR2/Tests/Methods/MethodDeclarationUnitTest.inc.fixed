<?php

class MyClass
{
    function _myFunction() {}
    private function myFunction() {}
    function __myFunction() {}
    public static function myFunction() {}
    public static /*comment*/ function myFunction() {}
    final public function myFunction() {}
    final public /*comment*/ function myFunction() {}
    abstract private function myFunction();
    abstract private /*comment*/ function myFunction();
    final public /*comment*/ static function myFunction() {}
    abstract final protected static function myFunction();
    public function _() {}
}

interface MyInterface
{
    function _myFunction();
    function __myFunction();
    public static function myFunction();
    public static function myFunction();
    public function _();
}

trait MyTrait
{
    function _myFunction() {}
    private function myFunction() {}
    function __myFunction() {}
    public static function myFunction() {}
    public static function myFunction() {}
    final /*comment*/ public function myFunction() {}
    final public function myFunction() {}
    abstract private function myFunction();
    abstract private function myFunction();
    final public static function myFunction() {}
    /*comment*/ abstract final protected static function myFunction();
    public function _() {}
}

$a = new class()
{
    function _myFunction() {}
    private function myFunction() {}
    function __myFunction() {}
    public static function myFunction() {}
    public static function myFunction() {}
    final public function myFunction() {}
    final public function myFunction() {}
    abstract private function myFunction();
    abstract private function myFunction();
    final public static function myFunction() {}
    abstract final protected static function myFunction();
    public function _() {}
}

class Nested_Function {
    public function getAnonymousClass() {
        return new class() {
			final private static function _nested_function() {}
        };
    }
}
