<documentation title="Property Declarations">
    <standard>
    <![CDATA[
    Property names should not be prefixed with an underscore to indicate visibility.  Visibility should be used to declare properties rather than the var keyword.  Only one property should be declared within a statement.  The static declaration must come after the visibility declaration.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Correct property naming.">
        <![CDATA[
class Foo
{
    private $<em>bar</em>;
}
        ]]>
        </code>
        <code title="Invalid: An underscore prefix used to indicate visibility.">
        <![CDATA[
class Foo
{
    private $<em>_bar</em>;
}
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Visibility of property declared.">
        <![CDATA[
class Foo
{
    <em>private</em> $bar;
}
        ]]>
        </code>
        <code title="Invalid: Var keyword used to declare property.">
        <![CDATA[
class Foo
{
    <em>var</em> $bar;
}
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: One property declared per statement.">
        <![CDATA[
class Foo
{
    private $bar;
    private $baz;
}
        ]]>
        </code>
        <code title="Invalid: Multiple properties declared in one statement.">
        <![CDATA[
class Foo
{
    private <em>$bar, $baz</em>;
}
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: If declared as static, the static declaration must come after the visibility declaration.">
        <![CDATA[
class Foo
{
    public <em>static</em> $bar;
    private $baz;
}
        ]]>
        </code>
        <code title="Invalid: Static declaration before the visibility declaration.">
        <![CDATA[
class Foo
{
    <em>static<em> protected $bar;
}
        ]]>
        </code>
    </code_comparison>
</documentation>
