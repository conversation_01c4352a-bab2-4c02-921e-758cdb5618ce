<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="PSR2" xsi:noNamespaceSchemaLocation="../../../phpcs.xsd">
    <description>The PSR-2 coding standard.</description>
    <arg name="tab-width" value="4"/>

    <!-- 2. General -->

    <!-- 2.1 Basic Coding Standard -->

    <!-- Include the whole PSR-1 standard -->
    <rule ref="PSR1"/>

    <!-- 2.2 Files -->

    <!-- All PHP files MUST use the Unix LF (linefeed) line ending. -->
    <rule ref="Generic.Files.LineEndings">
        <properties>
            <property name="eolChar" value="\n"/>
        </properties>
    </rule>

    <!-- All PHP files MUST end with a single blank line. -->
    <!-- checked by PSR2.Files.EndFileNewline -->

    <!-- The closing ?> tag MUST be omitted from files containing only PHP. -->
    <!-- checked by PSR2.Files.ClosingTag -->

    <!-- 2.3 Lines -->

    <!-- The soft limit on line length MUST be 120 characters; automated style checkers MUST warn but MUST NOT error at the soft limit. -->
    <rule ref="Generic.Files.LineLength">
        <properties>
            <property name="lineLimit" value="120"/>
            <property name="absoluteLineLimit" value="0"/>
        </properties>
    </rule>

    <!-- There MUST NOT be trailing whitespace at the end of non-blank lines. -->
    <rule ref="Squiz.WhiteSpace.SuperfluousWhitespace">
        <properties>
            <property name="ignoreBlankLines" value="true"/>
        </properties>
    </rule>
    <rule ref="Squiz.WhiteSpace.SuperfluousWhitespace.StartFile">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.WhiteSpace.SuperfluousWhitespace.EndFile">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.WhiteSpace.SuperfluousWhitespace.EmptyLines">
        <severity>0</severity>
    </rule>

    <!-- There MUST NOT be more than one statement per line. -->
    <rule ref="Generic.Formatting.DisallowMultipleStatements"/>

    <!-- 2.4 Indenting -->

    <!-- Code MUST use an indent of 4 spaces, and MUST NOT use tabs for indenting. -->
    <rule ref="Generic.WhiteSpace.ScopeIndent">
        <properties>
            <property name="ignoreIndentationTokens" type="array">
                <element value="T_COMMENT"/>
                <element value="T_DOC_COMMENT_OPEN_TAG"/>
            </property>
        </properties>
    </rule>
    <rule ref="Generic.WhiteSpace.DisallowTabIndent"/>

    <!-- 2.5 Keywords and True/False/Null -->

    <!-- PHP keywords MUST be in lower case. -->
    <rule ref="Generic.PHP.LowerCaseKeyword"/>

    <!-- The PHP constants true, false, and null MUST be in lower case. -->
    <rule ref="Generic.PHP.LowerCaseConstant"/>

    <!-- 3. Namespace and Use Declarations -->

    <!-- When present, there MUST be one blank line after the namespace declaration. -->
    <!-- checked by PSR2.Namespaces.NamespaceDeclaration -->

    <!-- When present, all use declarations MUST go after the namespace declaration.
         There MUST be one use keyword per declaration.
         There MUST be one blank line after the use block. -->
    <!-- checked by PSR2.Namespaces.UseDeclaration -->

    <!-- 4. Classes, Properties, and Methods -->

    <!-- 4.1. Extends and Implements -->

    <!-- The extends and implements keywords MUST be declared on the same line as the class name.
         The opening brace for the class go MUST go on its own line; the closing brace for the class MUST go on the next line after the body.
         Lists of implements MAY be split across multiple lines, where each subsequent line is indented once. When doing so, the first item in the list MUST be on the next line, and there MUST be only one interface per line. -->
    <!-- checked by PSR2.Classes.ClassDeclaration -->

    <!-- 4.2. Properties -->

    <!-- Visibility MUST be declared on all properties.
         The var keyword MUST NOT be used to declare a property.
         There MUST NOT be more than one property declared per statement.
         Property names SHOULD NOT be prefixed with a single underscore to indicate protected or private visibility. -->
    <!-- checked by PSR2.Classes.PropertyDeclaration -->
    <rule ref="PSR2.Classes.PropertyDeclaration.SpacingAfterType">
        <severity>0</severity>
    </rule>

    <!-- 4.3 Methods -->

    <!-- Visibility MUST be declared on all methods. -->
    <rule ref="Squiz.Scope.MethodScope"/>
    <rule ref="Squiz.WhiteSpace.ScopeKeywordSpacing"/>

    <!-- Method names SHOULD NOT be prefixed with a single underscore to indicate protected or private visibility. -->
    <!-- checked by PSR2.Methods.MethodDeclaration -->

    <!-- Method names MUST NOT be declared with a space after the method name. The opening brace MUST go on its own line, and the closing brace MUST go on the next line following the body. There MUST NOT be a space after the opening parenthesis, and there MUST NOT be a space before the closing parenthesis. -->
    <!-- checked by PSR2.Methods.FunctionClosingBrace -->
    <rule ref="Squiz.Functions.FunctionDeclaration"/>
    <rule ref="Squiz.Functions.LowercaseFunctionKeywords"/>

    <!-- 4.4 Method Arguments -->

    <!-- In the argument list, there MUST NOT be a space before each comma, and there MUST be one space after each comma. -->
    <rule ref="Squiz.Functions.FunctionDeclarationArgumentSpacing">
        <properties>
            <property name="equalsSpacing" value="1"/>
        </properties>
    </rule>
    <rule ref="Squiz.Functions.FunctionDeclarationArgumentSpacing.SpacingAfterHint">
        <severity>0</severity>
    </rule>

    <!-- Method arguments with default values MUST go at the end of the argument list. -->
    <rule ref="PEAR.Functions.ValidDefaultValue"/>

    <!-- Argument lists MAY be split across multiple lines, where each subsequent line is indented once. When doing so, the first item in the list MUST be on the next line, and there MUST be only one argument per line. When the argument list is split across multiple lines, the closing parenthesis and opening brace MUST be placed together on their own line with one space between them. -->
    <rule ref="Squiz.Functions.MultiLineFunctionDeclaration"/>

    <!-- 4.5 abstract, final, and static -->

    <!-- When present, the abstract and final declarations MUST precede the visibility declaration.
         When present, the static declaration MUST come after the visibility declaration. -->
    <!-- checked by PSR2.Methods.MethodDeclaration -->

    <!-- 4.6 Method and Function Calls -->

    <!-- When making a method or function call, there MUST NOT be a space between the method or function name and the opening parenthesis, there MUST NOT be a space after the opening parenthesis, and there MUST NOT be a space before the closing parenthesis. In the argument list, there MUST NOT be a space before each comma, and there MUST be one space after each comma.
    Argument lists MAY be split across multiple lines, where each subsequent line is indented once. When doing so, the first item in the list MUST be on the next line, and there MUST be only one argument per line. -->
    <rule ref="Generic.Functions.FunctionCallArgumentSpacing"/>
    <rule ref="PSR2.Methods.FunctionCallSignature.SpaceAfterCloseBracket">
        <severity>0</severity>
    </rule>
    <rule ref="PSR2.Methods.FunctionCallSignature.OpeningIndent">
        <severity>0</severity>
    </rule>

    <!-- 5. Control Structures -->

    <!-- The general style rules for control structures are as follows:
         There MUST be one space after the control structure keyword
         There MUST NOT be a space after the opening parenthesis
         There MUST NOT be a space before the closing parenthesis
         There MUST be one space between the closing parenthesis and the opening brace
         The structure body MUST be indented once
         The closing brace MUST be on the next line after the body -->
    <rule ref="Squiz.ControlStructures.ControlSignature"/>
    <rule ref="Squiz.WhiteSpace.ControlStructureSpacing.SpacingAfterOpen"/>
    <rule ref="Squiz.WhiteSpace.ControlStructureSpacing.SpacingBeforeClose"/>
    <rule ref="Squiz.WhiteSpace.ScopeClosingBrace"/>
    <rule ref="Squiz.ControlStructures.ForEachLoopDeclaration"/>
    <rule ref="Squiz.ControlStructures.ForLoopDeclaration"/>
    <rule ref="Squiz.ControlStructures.ForLoopDeclaration.SpacingAfterOpen">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.ControlStructures.ForLoopDeclaration.SpacingBeforeClose">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.ControlStructures.LowercaseDeclaration"/>
    <!-- checked by PSR2.ControlStructures.ControlStructureSpacing -->

    <!-- exclude this message as it is already checked by Generic.PHP.LowerCaseKeyword -->
    <rule ref="Squiz.ControlStructures.ForEachLoopDeclaration.AsNotLower">
        <severity>0</severity>
    </rule>

    <!-- exclude these messages as they are already checked by PSR2.ControlStructures.ControlStructureSpacing -->
    <rule ref="Squiz.ControlStructures.ForEachLoopDeclaration.SpaceAfterOpen">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.ControlStructures.ForEachLoopDeclaration.SpaceBeforeClose">
        <severity>0</severity>
    </rule>

    <!-- The body of each structure MUST be enclosed by braces. This standardizes how the structures look, and reduces the likelihood of introducing errors as new lines get added to the body. -->
    <rule ref="Generic.ControlStructures.InlineControlStructure"/>

    <!-- 5.1. if, elseif, else -->

    <!-- The keyword elseif SHOULD be used instead of else if so that all control keywords look like single words. -->
    <!-- checked by PSR2.ControlStructures.ElseIfDeclaration -->

    <!-- 5.2. switch, case -->

    <!-- The case statement MUST be indented once from switch, and the break keyword (or other terminating keyword) MUST be indented at the same level as the case body. There MUST be a comment such as // no break when fall-through is intentional in a non-empty case body. -->
    <!-- checked by PSR2.ControlStructures.SwitchDeclaration -->

    <!-- 6. Closures -->

    <!-- Closures MUST be declared with a space after the function keyword, and a space before and after the use keyword.
         The opening brace MUST go on the same line, and the closing brace MUST go on the next line following the body.
         There MUST NOT be a space after the opening parenthesis of the argument list or variable list, and there MUST NOT be a space before the closing parenthesis of the argument list or variable list.
         In the argument list and variable list, there MUST NOT be a space before each comma, and there MUST be one space after each comma.
         Closure arguments with default values MUST go at the end of the argument list.
         Argument lists and variable lists MAY be split across multiple lines, where each subsequent line is indented once. When doing so, the first item in the list MUST be on the next line, and there MUST be only one argument or variable per line.
         When the ending list (whether or arguments or variables) is split across multiple lines, the closing parenthesis and opening brace MUST be placed together on their own line with one space between them. -->
    <!-- checked in Squiz.Functions.MultiLineFunctionDeclaration -->
</ruleset>
