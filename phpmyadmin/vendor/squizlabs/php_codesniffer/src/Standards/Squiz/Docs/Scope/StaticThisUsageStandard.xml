<documentation title="Static This Usage">
    <standard>
    <![CDATA[
    Static methods should not use $this.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Using self:: to access static variables.">
        <![CDATA[
class Foo
{
    <em>static</em> function bar()
    {
        return <em>self</em>::$staticMember;
    }
}
]]>
        </code>
        <code title="Invalid: Using $this-> to access static variables.">
        <![CDATA[
class Foo
{
    <em>static</em> function bar()
    {
    return <em>$this</em>->$staticMember;
    }
}
]]>
        </code>
    </code_comparison>
</documentation>
