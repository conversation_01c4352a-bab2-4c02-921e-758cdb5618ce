<documentation title="Lowercase Function Keywords">
    <standard>
    <![CDATA[
    The php keywords function, public, private, protected, and static should be lowercase.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Lowercase function keyword.">
        <![CDATA[
<em>function</em> foo()
{
    return true;
}
]]>
        </code>
        <code title="Invalid: Uppercase function keyword.">
        <![CDATA[
<em>FUNCTION</em> foo()
{
    return true;
}
]]>
        </code>
    </code_comparison>
</documentation>
