<documentation title="Scope Keyword Spacing">
    <standard>
    <![CDATA[
    The php keywords static, public, private, and protected should have one space after them.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: A single space following the keywords.">
        <![CDATA[
public<em> </em>static<em> </em>function foo()
{
}
]]>
        </code>
        <code title="Invalid: Multiple spaces following the keywords.">
        <![CDATA[
public<em>  </em>static<em>  </em>function foo()
{
}
]]>
        </code>
    </code_comparison>
</documentation>
