<documentation title="Function Opening Brace Whitespace">
    <standard>
    <![CDATA[
    The opening brace for functions should be on a new line with no blank lines surrounding it.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Opening brace is on a new line.">
        <![CDATA[
function foo()
<em>{</em>
}
]]>
        </code>
        <code title="Invalid: Opening brace is on the same line as the function declaration.">
        <![CDATA[
function foo() <em>{</em>
}
]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: No blank lines after the opening brace.">
        <![CDATA[
function foo()
{
<em>    return 42;</em>
}
]]>
        </code>
        <code title="Invalid: A blank line after the opening brace.">
        <![CDATA[
function foo()
{
<em></em>
    return 42;
}
]]>
        </code>
    </code_comparison>
</documentation>
