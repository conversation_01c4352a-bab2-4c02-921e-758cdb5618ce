<documentation title="Object Operator Spacing">
    <standard>
    <![CDATA[
    The object operator (->) should not have any space around it.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: No spaces around the object operator.">
        <![CDATA[
$foo<em></em>-><em></em>bar();
]]>
        </code>
        <code title="Invalid: Whitespace surrounding the object operator.">
        <![CDATA[
$foo<em> </em>-><em> </em>bar();
]]>
        </code>
    </code_comparison>
</documentation>
