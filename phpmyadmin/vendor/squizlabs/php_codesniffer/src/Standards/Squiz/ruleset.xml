<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="Squiz" xsi:noNamespaceSchemaLocation="../../../phpcs.xsd">
    <description>The Squiz coding standard.</description>

    <!-- Include some specific sniffs -->
    <rule ref="Generic.Arrays.DisallowLongArraySyntax"/>
    <rule ref="Generic.CodeAnalysis.EmptyStatement"/>
    <rule ref="Generic.Commenting.Todo"/>
    <rule ref="Generic.Commenting.DocComment"/>
    <rule ref="Generic.ControlStructures.InlineControlStructure"/>
    <rule ref="Generic.Formatting.DisallowMultipleStatements"/>
    <rule ref="Generic.Formatting.SpaceAfterCast"/>
    <rule ref="Generic.Functions.FunctionCallArgumentSpacing"/>
    <rule ref="Generic.NamingConventions.ConstructorName"/>
    <rule ref="Generic.NamingConventions.UpperCaseConstantName"/>
    <rule ref="Generic.PHP.DeprecatedFunctions"/>
    <rule ref="Generic.PHP.DisallowShortOpenTag"/>
    <rule ref="Generic.PHP.LowerCaseKeyword"/>
    <rule ref="Generic.PHP.LowerCaseConstant"/>
    <rule ref="Generic.Strings.UnnecessaryStringConcat"/>
    <rule ref="Generic.WhiteSpace.DisallowTabIndent"/>
    <rule ref="Generic.WhiteSpace.LanguageConstructSpacing"/>
    <rule ref="Generic.WhiteSpace.IncrementDecrementSpacing"/>
    <rule ref="Generic.WhiteSpace.ScopeIndent"/>
    <rule ref="PEAR.ControlStructures.MultiLineCondition"/>
    <rule ref="PEAR.Files.IncludingFile"/>
    <rule ref="PEAR.Formatting.MultiLineAssignment"/>
    <rule ref="PEAR.Functions.ValidDefaultValue"/>
    <rule ref="PSR2.Files.EndFileNewline"/>
    <rule ref="Zend.Files.ClosingTag"/>
    <rule ref="Zend.Debug.CodeAnalyzer"/>

    <!-- Lines can be 120 chars long, but never show errors -->
    <rule ref="Generic.Files.LineLength">
        <properties>
            <property name="lineLimit" value="120"/>
            <property name="absoluteLineLimit" value="0"/>
        </properties>
    </rule>

    <!-- Use Unix newlines -->
    <rule ref="Generic.Files.LineEndings">
        <properties>
            <property name="eolChar" value="\n"/>
        </properties>
    </rule>

    <!-- Have 20 chars padding maximum and always show as errors -->
    <rule ref="Generic.Formatting.MultipleStatementAlignment">
        <properties>
            <property name="maxPadding" value="20"/>
            <property name="error" value="true"/>
        </properties>
    </rule>

    <!-- Ban some functions -->
    <rule ref="Generic.PHP.ForbiddenFunctions">
        <properties>
            <property name="forbiddenFunctions" type="array">
                <element key="sizeof" value="count"/>
                <element key="delete" value="unset"/>
                <element key="print" value="echo"/>
                <element key="is_null" value="null"/>
                <element key="create_function" value="null"/>
            </property>
        </properties>
    </rule>

    <!-- We allow empty catch statements -->
    <rule ref="Generic.CodeAnalysis.EmptyStatement.DetectedCATCH">
        <severity>0</severity>
    </rule>

    <!-- We don't want gsjlint throwing errors for things we already check -->
    <rule ref="Generic.Debug.ClosureLinter">
        <properties>
            <property name="errorCodes" type="array" value="0210"/>
            <property name="ignoreCodes" type="array" value="0001,0110,0240"/>
        </properties>
    </rule>
    <rule ref="Generic.Debug.ClosureLinter.ExternalToolError">
        <message>%2$s</message>
    </rule>

    <!-- Only one argument per line in multi-line function calls -->
    <rule ref="PEAR.Functions.FunctionCallSignature">
        <properties>
            <property name="allowMultipleArguments" value="false"/>
        </properties>
    </rule>

    <!-- We use custom indent rules for arrays -->
    <rule ref="Generic.Arrays.ArrayIndent"/>
    <rule ref="Squiz.Arrays.ArrayDeclaration.KeyNotAligned">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.Arrays.ArrayDeclaration.ValueNotAligned">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.Arrays.ArrayDeclaration.CloseBraceNotAligned">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.Arrays.ArrayDeclaration.CloseBraceNewLine">
        <severity>0</severity>
    </rule>

    <!-- Squiz.WhiteSpace.LanguageConstructSpacing is deprecated -->
    <rule ref="Squiz.WhiteSpace.LanguageConstructSpacing">
        <severity>0</severity>
    </rule>

    <!-- Prevent fixer conflict for conflicting rules. -->
    <rule ref="Squiz.Commenting.InlineComment">
        <exclude name="Squiz.Commenting.InlineComment.SpacingAfterAtFunctionEnd"/>
    </rule>

    <!-- Private methods MUST not be prefixed with an underscore -->
    <rule ref="Squiz.NamingConventions.ValidFunctionName.PrivateNoUnderscore">
        <severity>0</severity>
    </rule>
    <rule ref="PSR2.Methods.MethodDeclaration.Underscore">
        <type>error</type>
    </rule>

    <!-- Private properties MUST not be prefixed with an underscore -->
    <rule ref="Squiz.NamingConventions.ValidVariableName.PrivateNoUnderscore">
        <severity>0</severity>
    </rule>
    <rule ref="PSR2.Classes.PropertyDeclaration.Underscore">
        <type>error</type>
    </rule>
</ruleset>
