function FuncOne()
{
    // Code here.

}//end AdjustModalDialogWidgetType


Testing.prototype = {

    doSomething: function()
    {
        // Code here.
    },

    doSomethingElse: function()
    {
        // Code here.

    },
};

function FuncFour()
{
    // Code here.
}

function FuncFive()
{
    // Code here.


}

function valid()
{
   if (true) {
       test = {
           namespaces: {}
       };
   }

}

dfx.addEvent(this.rightScroll, 'mousedown', function() {
    t = setInterval(function() {
        pos -= 10;
    }, 30);
});

// Valid because function is empty.
if (dfx.isFn(callback) === false) {
    callback = function() {};
    callback = function() { };
}

AbstractAttributeEditorWidgetType.prototype = {
    isActive: function() {
        return this.active;
    },

    activate: function(data)
    {
        var x = {
            test: function () {
                alert('This is ok');

            }
        };

        this.active = true;

    }

};

var myFunc = function()
{
    var x = 1;

    blah(function() {
        alert(2);
    });

    blah(function() { alert(2); });

    return x;

}

a.prototype = {

    a: function()
    {
        var settings = {
            default: ''
        };

    },

    b: function()
    {
        var self = this;

    }

};

var a = new function()
{
    this.initScreen = function(usersFolderid)
    {
        for (var i = 0; i < paramSelectors.length; i++) {
        }//end for
    }

};

a.prototype = {

    this.addItem(
            id,
            {
                b: function()
                {
                    for (var i = 0; i < paramSelectors.length; i++) {
                    }//end for

                }
            }
    )

};
