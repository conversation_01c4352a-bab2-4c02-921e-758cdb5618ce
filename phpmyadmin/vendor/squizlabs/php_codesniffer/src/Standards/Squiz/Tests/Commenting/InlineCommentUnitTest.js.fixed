// Some content here.
var code = 'hello';

// This comment contains # multiple
// hash signs (#).
code = 'hello';

/**
 * This is the first line of a function comment.
 * This is the second line.
 */
function testFunction()
{
    // Callback methods which are added by external objects.
    this.callbacks = {};

}//end testFunction()

/**
 * This is the first line of a class comment.
 * This is the second line.
 */
myClass.prototype = {

    /**
     * This is the first line of a method comment.
     * This is the second line.
     */
    load: function(url, callback)
    {
        // Some code here.
    }
};

// some code goes here!
/*
    A longer comment goes here.
    It spans multiple lines!!
    Or does it?
*/

// 0This is a simple multi-line
// comment!
code = 'hello';

// This is not valid.
code = 'hello';

// Neither is this!
code = 'hello';

code = 'hello';

/** Neither is this! **/
code = 'hello';

/**
 * This is the first line of a function comment.
 * This is the second line.
 */
var myFunction = function() {
}

/**
 * This is the first line of a function comment.
 * This is the second line.
 */
myFunction = function() {
}

/**
 * This is the first line of a function comment.
 * This is the second line.
 */
myClass.myFunction = function() {
}

dfx.getIframeDocument = function(iframe)
{
    return doc;

};//end dfx.getIframeDocument()

mig.Gallery.prototype = {

    init: function(cb)
    {

    },//end init()

    imageClicked: function(id)
    {

    }//end imageClicked()

};

// Here is some inline example code:
// -> One
// -> One.One
// -> Two
/*
    Here is some inline example code:
        -> One
            -> One.One
        -> Two
*/


var foo = 'foo'; // Var set to foo.

console.info(foo);

// Comment here.
console.info(foo);

// **
* invalid comment
*/

// some comment without capital or full stop
console.log(foo); // An unrelated comment.

// An unrelated comment.
console.log(foo); // some comment without capital or full stop
