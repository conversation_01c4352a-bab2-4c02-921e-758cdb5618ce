<?php
/**
 * Some info about the class here
 *
 */
class MyClass
{
    /**
     * Some info about the function here.
     *
     * @return void
     */
    function myFunction() {}
}

/**
 *  Some info about the class here
 *
 */
class MyClass
{
    /**
     * Some info about the function here.
     *
     * @return void
     */
    function myFunction() {}
}

/**
 * Some info about the class here
 *
 */
class MyClass
{
    /**
     * Some info about the function here.
     *
     * @return void
     */
    function myFunction() {}
}

/** @var Database $mockedDatabase */
/** @var Container $mockedContainer */

function myFunction()
{
    echo 'hi';
    /**
        Comment here.
        */
}

/**
 * Creates a map of tokens => line numbers for each token.
 *
 * Long description with some points:
 *   - one
 *   - two
 *   - three
 *
 * @param array  &$tokens   The array of tokens to process.
 * @param object $tokenizer The tokenizer being used to
 *                          process this file.
 * @param string $eolChar   The EOL character to use for splitting strings.
 *
 * @return void
 */
function myFunction() {}

class MyClass2
{
    /**
     * Some info about the variable here.
     */
    var $x;
}

/** ************************************************************************
 * Example with no errors.
 **************************************************************************/
function example() {}
