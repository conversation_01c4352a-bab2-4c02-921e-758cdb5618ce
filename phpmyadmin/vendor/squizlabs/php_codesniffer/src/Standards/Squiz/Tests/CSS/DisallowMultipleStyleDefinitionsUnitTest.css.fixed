.SettingsTabPaneWidgetType-tab-mid {
   background: transparent url(tab_inact_mid.png) repeat-x;
   height: 100%;
float: left;
   line-height: -25px;
   cursor: pointer; 
margin: 10px; 
float: right;
}

/* testing embedded PHP */
li {
    background:url(<?php print $staticserver; ?>/images/<?php print $staticdir; ?>/bullet.gif) left <?php print $left; ?>px no-repeat; 
margin:0px; 
padding-left:10px; 
margin-bottom:<?php echo $marginBottom; ?>px; 
line-height:13px;
    filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#2e62a8, endColorstr=#123363);
}

/* Document handling of comments and annotations. */
div#annotations {-webkit-tap-highlight-color:transparent;/* phpcs:disable Standard.Cat.SniffName */
-webkit-touch-callout:none;/*phpcs:enable*/
-webkit-user-select:none;}

div#comments {height:100%;/*comment*/
width:100%;}
