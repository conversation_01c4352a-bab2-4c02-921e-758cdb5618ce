.my-style {
    opacity: 0;
    opacity: 0;
    opacity: 1;
    opacity: 1;
    opacity: 1.5;
    opacity: 0.5;
    opacity: 0.5;
    opacity: 2;
    opacity: -1;
    opacity: 0.55;
}

div {
  font-size: 1.2em;
  background: linear-gradient(to bottom, #00F, #0F0) repeat scroll 50% 50% #EEE;
  min-width: 250px;
  max-width: 100%;
  padding-bottom: 50px;
  box-shadow: 2px -3px 3px rgba(100, 100, 100, 0.33);
  border-left: 1px solid #000;
  border-right: 1px solid #000;
  border-top: 1px solid #000;
  border-bottom: 1px dotted #000;
  background: url(../1/2/3/4-5-6_7_100x100.png) repeat scroll 50% 50% #EEE;
  opacity: -1;
}

.my-commented-style {
    opacity: /*comment*/ 0;
    opacity: /* phpcs:ignore Standard.Cat.Sniff -- for reasons */
		0;
    opacity: /*comment*/ 1;
    opacity: /*comment*/ 0.5;
}
