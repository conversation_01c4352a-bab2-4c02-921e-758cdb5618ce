body {
    font-family: Arial, Helvetica, sans-serif;
    margin: 40px 0 0 0;
    padding: 0;
    background: #8FB7DB url(diag_lines_bg.gif) top left;
}

td {
    margin: 40px;
    padding: 20px;
}

/*
#AdminScreenModeWidgetType-tab_pane-containers .TabPaneWidgetType-tab-selected-left {
    background: transparent url(images/ScreenImages/tab_on_left.png) no-repeat;
}
#AdminScreenModeWidgetType-tab_pane-containers .TabPaneWidgetType-tab-selected-right {
    background: transparent url(images/ScreenImages/tab_on_right.png) no-repeat;
}
*/

.GUITextBox.container:after {}

@media screen and (max-device-width: 769px) {

    header #logo img {
        max-width: 100%;
        padding: 20px;
        margin: 40px;
    }
}

@media screen and (max-device-width: 769px) {

    header #logo img {
        max-width: 100%;
    }

    header #logo img {
        min-width: 100%;
    }

}

td {
    margin: 40px;
    padding: 20px;
}

.GUIFileUpload {
/*     opacity: 0.25; */
}

.foo
{
    border: none;
}

.mortgage-calculator h2 {
    background: #072237;
    color: #fff;
    font-weight: normal;
    height: 50px;
    line-height: 50px;
    padding: 0 0 0 30px;
}

.WhitelistCommentIndentationShouldBeIgnored {
/* phpcs:disable Standard.Category.Sniff -- for reasons. */
}

/* syntax error */
--------------------------------------------- */
