.AssetLineageWidgetType-item {
    color: #FFF;
}

.AssetLineageWidgetType-title {
    color: #CCC;
}

.AssetLineageWidgetType-item {
    color: #CCC;
}

.AssetLineageWidgetType-item .selected {
}

.AssetLineageWidgetType-item.selected {
}

#Blah .AssetLineageWidgetType-item {
}

#X.selected,
.AssetLineageWidgetType-item {
}

.MyClass, .YourClass {
}

.YourClass, .MyClass {
}

.YourClass, .MyClass, .OurClass {
}


.ClassAtTopOfMediaBlock {
}

@media print {
    .ClassAtTopOfMediaBlock {
    }

    .ClassInMultipleMediaBlocks {
    }
}

.ClassNotAtTopOfMediaBlock {
}

@media handheld {
    .SameClassInMediaBlock {
    }

    .ClassNotAtTopOfMediaBlock {
    }

    .SameClassInMediaBlock {
    }
}

@media braille {
    .PlaceholderClass {
    }

    .ClassNotAtTopOfMediaBlock {
    }

    .ClassInMultipleMediaBlocks {
    }
}

.foo /* any comment */
{ color: red; }

/* print comment */
@media print {
    /* comment1 */
    td {
    }

    /* comment2 */
    img {
    }

    /* comment3 */
    td {
    }
}

@media handheld /* handheld comment */
{
    td /* comment1 */
    {
    }

    img /* comment2 */
    {
    }

    td /* comment3 */
    {
    }
}
