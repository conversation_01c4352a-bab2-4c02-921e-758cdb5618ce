function long_function()
{
    if (longFunction) {
        // This is a long
        // IF statement
        // that does
        // not have
        // an ELSE
        // block on it
        variable = 'hello';

        if (variable === 'hello') {
            // This is a short
            // IF statement
        }

        if (variable === 'hello') {
            // This is a short
            // IF statement
        } else {
            // This is a short ELSE
            // statement
        }
    }//end if

    if (longFunction) {
        // This is a long
        // IF statement
        // that does
        // not have
        // an ELSE
        // block on it
        variable = 'hello';

        if (variable === 'hello') {
            // This is a short
            // IF statement
        }

        if (variable === 'hello') {
            // This is a short
            // IF statement
        } else {
            // This is a short ELSE
            // statement
        }
    }

    if (longFunction) {
        // This is a long
        // IF statement
        // that does
        // not have
        // an ELSE
        // block on it
        variable = 'hello';

        if (variable === 'hello') {
            // This is a short
            // IF statement
        }

        if (variable === 'hello') {
            // This is a short
            // IF statement
        } else {
            // This is a short ELSE
            // statement
        }
    } else {
        // Short ELSE
    }//end if

    if (longFunction) {
        // This is a long
        // IF statement
        // that does
        // not have
        // an ELSE
        // block on it
        variable = 'hello';

        if (variable === 'hello') {
            // This is a short
            // IF statement
        }

        if (variable === 'hello') {
            // This is a short
            // IF statement
        } else {
            // This is a short ELSE
            // statement
        }
    } else {
        // Short ELSE
    }
}

for (variable=1; variable < 20; variable++) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
}//end for

for (variable=1; variable < 20; variable++) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    for (val =1; val < 20; val++) {
        // Short for.
    }
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
}

while (variable < 20) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
}//end while

while (variable < 20) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    while (something) {
        // Short while.
    }
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
}

if (longFunction) {
    // This is a long
    // IF statement
    // that does
    // not have
    // an ELSE
    // block on it
    variable = 'hello';

    if (variable === 'hello') {
        // This is a short
        // IF statement
    }

    if (variable === 'hello') {
        // This is a short
        // IF statement
    } else {
        // This is a short ELSE
        // statement
    }
} //end if

if (longFunction) {
    // This is a long
    // IF statement
    // that does
    // not have
    // an ELSE
    // block on it
    variable = 'hello';

    if (variable === 'hello') {
        // This is a short
        // IF statement
    }

    if (variable === 'hello') {
        // This is a short
        // IF statement
    } else {
        // This is a short ELSE
        // statement
    }
} else {
    // Short ELSE
} //end if

for (variable=1; variable < 20; variable++) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
} //end for

while (variable < 20) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
} //end while

while (variable < 20) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
}//end for

if (true) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
} else if (condition) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
} else {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
}//end if

if (something) {
    // Line 1
    // Line 2
} else if (somethingElse) {
    // Line 1
    // Line 2
} else {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
}

switch (something) {
    case '1':
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    break;
    case '2':
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    break;
    case '3':
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    break;
    case '4':
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    break;
    case '5':
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    break;
}

// Wrong comment
if (condition) {
    condition = true;
}//end foreach
