<?php

function long_function()
{
    if ($longFunction) {
        // This is a long
        // IF statement
        // that does
        // not have
        // an ELSE
        // block on it
        $variable = 'hello';

        if ($variable === 'hello') {
            // This is a short
            // IF statement
        }

        if ($variable === 'hello') {
            // This is a short
            // IF statement
        } else {
            // This is a short ELSE
            // statement
        }
    }//end if

    if ($longFunction) {
        // This is a long
        // IF statement
        // that does
        // not have
        // an ELSE
        // block on it
        $variable = 'hello';

        if ($variable === 'hello') {
            // This is a short
            // IF statement
        }

        if ($variable === 'hello') {
            // This is a short
            // IF statement
        } else {
            // This is a short ELSE
            // statement
        }
    }//end if

    if ($longFunction) {
        // This is a long
        // IF statement
        // that does
        // not have
        // an ELSE
        // block on it
        $variable = 'hello';

        if ($variable === 'hello') {
            // This is a short
            // IF statement
        }

        if ($variable === 'hello') {
            // This is a short
            // IF statement
        } else {
            // This is a short ELSE
            // statement
        }
    } else {
        // Short ELSE
    }//end if

    if ($longFunction) {
        // This is a long
        // IF statement
        // that does
        // not have
        // an ELSE
        // block on it
        $variable = 'hello';

        if ($variable === 'hello') {
            // This is a short
            // IF statement
        }

        if ($variable === 'hello') {
            // This is a short
            // IF statement
        } else {
            // This is a short ELSE
            // statement
        }
    } else {
        // Short ELSE
    }//end if
}

foreach ($var as $val) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
}//end foreach

foreach ($var as $val) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    foreach ($blah as $val) {
        // Short foreach.
    }
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
}//end foreach

for ($var =1; $var < 20; $var++) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
}//end for

for ($var =1; $var < 20; $var++) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    for ($val =1; $val < 20; $val++) {
        // Short for.
    }
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
}//end for

while ($var < 20) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
}//end while

while ($var < 20) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    while ($something) {
        // Short while.
    }
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
}//end while

if ($longFunction) {
    // This is a long
    // IF statement
    // that does
    // not have
    // an ELSE
    // block on it
    $variable = 'hello';

    if ($variable === 'hello') {
        // This is a short
        // IF statement
    }

    if ($variable === 'hello') {
        // This is a short
        // IF statement
    } else {
        // This is a short ELSE
        // statement
    }
} //end if

if ($longFunction) {
    // This is a long
    // IF statement
    // that does
    // not have
    // an ELSE
    // block on it
    $variable = 'hello';

    if ($variable === 'hello') {
        // This is a short
        // IF statement
    }

    if ($variable === 'hello') {
        // This is a short
        // IF statement
    } else {
        // This is a short ELSE
        // statement
    }
} else {
    // Short ELSE
} //end if

foreach ($var as $val) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
} //end foreach

for ($var =1; $var < 20; $var++) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
} //end for

while ($var < 20) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
} //end while

while ($var < 20) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
}//end while

if (true) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
} else if ($condition) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
} elseif ($cond) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
} else {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
}//end if

if ($something) {
    // Line 1
    // Line 2
} else if ($somethingElse) {
    // Line 1
    // Line 2
} else {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
}//end if

switch ($something) {
    case '1':
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    break;
    case '2':
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    break;
    case '3':
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    break;
    case '4':
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    break;
    case '5':
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    break;
}//end switch

// Wrong comment
if ($condition) {
    echo "true";
}//end if

if ($condition) {
    echo "true";
} //end if

try {
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
} catch (Exception $e) {
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
}//end try

try {
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
} catch (Exception $e) {
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
}

try {
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
} catch (Exception $e) {
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
}//end try

try {
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
} catch (DALException $e) {
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
} catch (ChannelException $e) {
    // some code here.
} catch (Exception $e) {
    // some code here.
}//end try

switch ($foo) {
    case 'one' : {
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        break;
    }//end case
    case 'one' :
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
        // some code here.
    break;
}//end switch

// Yes, code like this does exist.
if ($foo) {
    return $foo;
} elseif ($bar)
    return $bar;

switch ($foo) {
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
    case 15:
    case 16:
    case 17:
    case 18:
    case 19:
    case 20:
    case 21:
    case 22:
    case 23:
    case 24:
    case 25:
    case 26:
    case 27:
    case 28:
    case 29:
    case 30:
    case 31:
    case 32:
    case 33:
    case 34:
    case 35:
    case 36:
    case 37:
    case 38:
    case 39:
    case 40:
    case 41:
    case 42:
    case 43:
    case 44:
    case 45:
    case 46:
    case 47:
    case 48:
    case 49:
    case 50:
    case 51:
    case 52:
    case 53:
    case 54:
    case 55:
    case 56:
    case 57:
    case 58:
    case 59:
        echo $foo;
        break;
}//end switch

// phpcs:set Squiz.Commenting.LongConditionClosingComment lineLimit 5
// phpcs:set Squiz.Commenting.LongConditionClosingComment commentFormat //Dragonbait-%s

function quite_long_function()
{
	// Ok - below limit
    if ($longFunction) {
        $variable = 'hello';
	}

	// Ok - correct comment
    if ($longFunction) {
        // This is a long
        // IF statement
        // that does
        // not have
        // an ELSE
        // block on it
    }//Dragonbait-if

	// This should be caught - wrong comment
    if ($longFunction) {
        // This is a long
        // IF statement
        // that does
        // not have
        // an ELSE
        // block on it
        $variable = 'hello';
    } else {
        // Short ELSE
    }//Dragonbait-if

	// This should be caught - no comment
    if ($longFunction) {
        // This is a long
        // IF statement
        // that does
        // not have
        // an ELSE
        // block on it
        $variable = 'hello';
    } else {
        // Short ELSE
    }//Dragonbait-if
}

// phpcs:set Squiz.Commenting.LongConditionClosingComment lineLimit 30
// phpcs:set Squiz.Commenting.LongConditionClosingComment commentFormat // Bye-Bye %s()

// Ok - below limit
foreach ($var as $val) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
}

// Ok - has correct comment
foreach ($var as $val) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
    // Line 21
    // Line 22
    // Line 23
    // Line 24
    // Line 25
    // Line 26
    // Line 27
    // Line 28
    // Line 29
    // Line 30
}// Bye-Bye foreach()

// This should be caught - wrong comment
foreach ($var as $val) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
    // Line 21
    // Line 22
    // Line 23
    // Line 24
    // Line 25
    // Line 26
    // Line 27
    // Line 28
    // Line 29
    // Line 30
}// Bye-Bye foreach()

// This should be caught - no comment
foreach ($var as $val) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
    // Line 21
    // Line 22
    // Line 23
    // Line 24
    // Line 25
    // Line 26
    // Line 27
    // Line 28
    // Line 29
    // Line 30
}// Bye-Bye foreach()

// phpcs:set Squiz.Commenting.LongConditionClosingComment lineLimit 20
// phpcs:set Squiz.Commenting.LongConditionClosingComment commentFormat //end %s

try {
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
} catch (DALException $e) {
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
    // some code here.
} catch (ChannelException $e) {
    // some code here.
} finally {
    // some code here.
}//end try

$expr = match ($foo) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
}; //end match

$expr = match ($foo) {
    // Line 1
    // Line 2
    // Line 3
    // Line 4
    // Line 5
    // Line 6
    // Line 7
    // Line 8
    // Line 9
    // Line 10
    // Line 11
    // Line 12
    // Line 13
    // Line 14
    // Line 15
    // Line 16
    // Line 17
    // Line 18
    // Line 19
    // Line 20
};//end match

$array = [
    'match' => match ($foo) {
        // Line 1
        // Line 2
        // Line 3
        // Line 4
        // Line 5
        // Line 6
        // Line 7
        // Line 8
        // Line 9
        // Line 10
        // Line 11
        // Line 12
        // Line 13
        // Line 14
        // Line 15
        // Line 16
        // Line 17
        // Line 18
        // Line 19
        // Line 20
    },//end match
];
