<documentation title="Object Operator Indentation">
    <standard>
    <![CDATA[
    Chained object operators when spread out over multiple lines should be the first thing on the line and be indented by 1 level.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Object operator at the start of a new line.">
        <![CDATA[
$foo
    <em>-></em>bar()
    <em>-></em>baz();
        ]]>
        </code>
        <code title="Invalid: Object operator at the end of the line.">
        <![CDATA[
$foo<em>-></em>
    bar()<em>-></em>
    baz();
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Object operator indented correctly.">
        <![CDATA[
$foo
<em>    </em>->bar()
<em>    </em>->baz();
        ]]>
        </code>
        <code title="Invalid: Object operator not indented correctly.">
        <![CDATA[
$foo
<em></em>->bar()
<em></em>->baz();
        ]]>
        </code>
    </code_comparison>
</documentation>
