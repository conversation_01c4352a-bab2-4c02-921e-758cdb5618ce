<documentation title="Function Calls">
    <standard>
    <![CDATA[
      Functions should be called with no spaces between the function name, the opening parenthesis, and the first parameter; and no space between the last parameter, the closing parenthesis, and the semicolon.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: spaces between parameters">
        <![CDATA[
$var = foo($bar, $baz, $quux);
        ]]>
        </code>
        <code title="Invalid: additional spaces used">
        <![CDATA[
$var = foo<em> </em>(<em> </em>$bar, $baz, $quux<em> </em>)<em> </em>;
        ]]>
        </code>
    </code_comparison>
</documentation>
