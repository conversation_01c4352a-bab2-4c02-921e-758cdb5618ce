<documentation title="Class Names">
    <standard>
    <![CDATA[
    Classes should be given descriptive names. Avoid using abbreviations where possible. Class names should always begin with an uppercase letter. The PEAR class hierarchy is also reflected in the class name, each level of the hierarchy separated with a single underscore.
    ]]>
    </standard>
    <code_comparison>
        <code title="Examples of valid class names">
        <![CDATA[
Log
Net_Finger
HTML_Upload_Error
        ]]>
        </code>
        <code title="Examples of invalid class names">
        <![CDATA[
log
NetFinger
HTML-Upload-Error
        ]]>
        </code>
    </code_comparison>
</documentation>
