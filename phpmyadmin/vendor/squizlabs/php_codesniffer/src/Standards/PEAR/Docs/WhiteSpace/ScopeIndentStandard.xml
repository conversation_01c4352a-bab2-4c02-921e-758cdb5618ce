<documentation title="Scope Indentation">
    <standard>
    <![CDATA[
    Any scope openers except for switch statements should be indented 1 level.  This includes classes, functions, and control structures.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Consistent indentation level for scope.">
        <![CDATA[
function foo()
{
<em>    </em>if ($test) {
<em>        </em>$var = 1;
<em>    </em>}
}
        ]]>
        </code>
        <code title="Invalid: Indentation is not used for scope.">
        <![CDATA[
function foo()
{
<em></em>if ($test) {
<em></em>$var = 1;
<em></em>}
}
        ]]>
        </code>
    </code_comparison>
</documentation>
