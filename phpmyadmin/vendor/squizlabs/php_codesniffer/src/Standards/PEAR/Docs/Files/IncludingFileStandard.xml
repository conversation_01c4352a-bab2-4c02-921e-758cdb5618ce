<documentation title="Including Code">
    <standard>
    <![CDATA[
    Anywhere you are unconditionally including a class file, use <em>require_once</em>. Anywhere you are conditionally including a class file (for example, factory methods), use <em>include_once</em>. Either of these will ensure that class files are included only once. They share the same file list, so you don't need to worry about mixing them - a file included with <em>require_once</em> will not be included again by <em>include_once</em>.
    ]]>
    </standard>
    <standard>
    <![CDATA[
    Note that <em>include_once</em> and <em>require_once</em> are statements, not functions. Parentheses should not surround the subject filename.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: used as statement">
        <![CDATA[
require_once 'PHP/CodeSniffer.php';
        ]]>
        </code>
        <code title="Invalid: used as function">
        <![CDATA[
require_once<em>(</em>'PHP/CodeSniffer.php'<em>)</em>;
        ]]>
        </code>
    </code_comparison>
</documentation>
