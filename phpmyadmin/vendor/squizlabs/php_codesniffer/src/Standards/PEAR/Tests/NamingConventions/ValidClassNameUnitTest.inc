<?php

class Valid_Name {}

class invalid_Name {}

class invalid_name {}

class Invalid_name {}

class VALID_Name {}

class VALID_NAME {}

class VALID_Name {}

class ValidName {}

class _Invalid_Name {}


interface Valid_Name {}

interface invalid_Name {}

interface invalid_name {}

interface Invalid_name {}

interface VALID_Name {}

interface VALID_NAME {}

interface VALID_Name {}

interface ValidName {}

interface _Invalid_Name {}

class ___ {}

interface ___ {}

class Invalid__Name {}

interface Invalid__Name {}

trait Valid_Name {}

trait invalid_Name {}

trait invalid_name {}

trait Invalid_name {}

trait VALID_Name {}

trait VALID_NAME {}

trait VALID_Name {}

trait ValidName {}

trait _Invalid_Name {}

trait ___ {}

trait Invalid__Name {}
