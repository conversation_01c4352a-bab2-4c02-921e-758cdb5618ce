<?php
declare(encoding='utf-8');
/* vim: set expandtab tabstop=4 shiftwidth=4 softtabstop=4: */

/**
*
* Short description for fileasdasd.
*
*
* asdasd
* long description for file (if any)
* asdasdadada
*
* PHP versio
*
* LICENSE: This source file is subject to version 3.0 of the PHP license
* that is available through the world-wide-web at the following URI:
* http://www.php.net/license/3_0.txt.  If you did not receive a copy of
* the PHP License and are unable to obtain it through the web, please
* send a <NAME_EMAIL> so we can mail you a copy immediately.
* @category   _wrong_category
* @package    PHP_CodeSniffer
* @package    ADDITIONAL PACKAGE TAG
* @subpackage SUBPACKAGE TAG
* <AUTHOR> <<EMAIL>>
* <AUTHOR> g<PERSON>@squiz.net
* <AUTHOR> T <<EMAIL>>
* <AUTHOR> @copyright  1997~1994 The PHP Group
* @copyright  1997~1994 The PHP Group
* @license    http://www.php.net/license/3_0.txt
* @see
* @see
* @version    INVALID VERSION CONTENT
* @see        Net_Sample::Net_Sample()
* @see        Net_Other
* @deprecated asd
* @since      Class available since Release 1.2.0
* @summary    An unknown summary tag
* @package    ''
* @subpackage !!
* <AUTHOR> AUthor <<EMAIL>>
*/
require_once '/some/path.php';
?>
<?php
/**
* This bit here is not qualified as file comment
*
* as it is not after the first open tag
*
*/
?>
