<documentation title="Opening Brace in Function Declarations">
    <standard>
    <![CDATA[
    Function declarations follow the "Kernighan/<PERSON> style". The function brace is on the same line as the function declaration. One space is required between the closing parenthesis and the brace.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: brace on same line">
        <![CDATA[
function fooFunction($arg1, $arg2 = '')<em> {</em>
    ...
}
        ]]>
        </code>
        <code title="Invalid: brace on next line">
        <![CDATA[
function fooFunction($arg1, $arg2 = '')
<em>{</em>
    ...
}
        ]]>
        </code>
    </code_comparison>
</documentation>
