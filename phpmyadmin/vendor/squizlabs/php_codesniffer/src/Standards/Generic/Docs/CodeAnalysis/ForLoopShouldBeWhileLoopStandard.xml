<documentation title="Condition-Only For Loops">
    <standard>
    <![CDATA[
    For loops that have only a second expression (the condition) should be converted to while loops.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: A for loop is used with all three expressions.">
        <![CDATA[
for (<em>$i = 0</em>; $i < 10; <em>$i++</em>) {
    echo "{$i}\n";
}
        ]]>
        </code>
        <code title="Invalid: A for loop is used without a first or third expression.">
        <![CDATA[
for (<em></em>;$test;<em></em>) {
    $test = doSomething();
}
        ]]>
        </code>
    </code_comparison>
</documentation>
