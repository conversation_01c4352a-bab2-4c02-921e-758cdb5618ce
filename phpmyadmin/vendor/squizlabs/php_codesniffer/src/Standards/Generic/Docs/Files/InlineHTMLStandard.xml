<documentation title="Inline HTML">
    <standard>
    <![CDATA[
    Files that contain php code should only have php code and should not have any "inline html".
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: A php file with only php code in it.">
        <![CDATA[
<?php
$foo = 'bar';
echo $foo . 'baz';
        ]]>
        </code>
        <code title="Invalid: A php file with html in it outside of the php tags.">
        <![CDATA[
<em>some string here</em>
<?php
$foo = 'bar';
echo $foo . 'baz';
        ]]>
        </code>
    </code_comparison>
</documentation>
