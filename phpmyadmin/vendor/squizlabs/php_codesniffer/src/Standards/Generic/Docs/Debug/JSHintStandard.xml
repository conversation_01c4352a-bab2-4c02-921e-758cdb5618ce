<documentation title="JSHint">
    <standard>
    <![CDATA[
    All javascript files should pass basic JSHint tests.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Valid JS Syntax is used.">
        <![CDATA[
<em>var</em> foo = 5;
        ]]>
        </code>
        <code title="Invalid: The Javascript is using an undefined variable.">
        <![CDATA[
<em></em>foo = 5;
        ]]>
        </code>
    </code_comparison>
</documentation>
