<documentation title="Fixme Comments">
    <standard>
    <![CDATA[
    FIXME Statements should be taken care of.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: A comment without a fixme.">
        <![CDATA[
// <em>Handle strange case</em>
if ($test) {
    $var = 1;
}
        ]]>
        </code>
        <code title="Invalid: A fixme comment.">
        <![CDATA[
// <em>FIXME</em>: This needs to be fixed!
if ($test) {
    $var = 1;
}
        ]]>
        </code>
    </code_comparison>
</documentation>
