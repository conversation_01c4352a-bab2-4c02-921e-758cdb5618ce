<documentation title="Opening Brace in Function Declarations">
    <standard>
    <![CDATA[
    Function declarations follow the "BSD/Allman style". The function brace is on the line following the function declaration and is indented to the same column as the start of the function declaration.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: brace on next line">
        <![CDATA[
function fooFunction($arg1, $arg2 = '')
<em>{</em>
    ...
}
        ]]>
        </code>
        <code title="Invalid: brace on same line">
        <![CDATA[
function fooFunction($arg1, $arg2 = '') <em>{</em>
    ...
}
        ]]>
        </code>
    </code_comparison>
</documentation>
