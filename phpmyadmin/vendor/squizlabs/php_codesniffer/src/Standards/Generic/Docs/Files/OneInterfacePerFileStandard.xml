<documentation title="One Interface Per File">
    <standard>
    <![CDATA[
    There should only be one interface defined in a file.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Only one interface in the file.">
        <![CDATA[
<?php
<em>interface Foo</em>
{
}
        ]]>
        </code>
        <code title="Invalid: Multiple interfaces defined in one file.">
        <![CDATA[
<?php
<em>interface Foo</em>
{
}

<em>interface Bar</em>
{
}
        ]]>
        </code>
    </code_comparison>
</documentation>
