<documentation title="For Loops With Function Calls in the Test">
    <standard>
    <![CDATA[
    For loops should not call functions inside the test for the loop when they can be computed beforehand.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: A for loop that determines its end condition before the loop starts.">
        <![CDATA[
<em>$end = count($foo);</em>
for ($i = 0; $i < $end; $i++) {
    echo $foo[$i]."\n";
}
        ]]>
        </code>
        <code title="Invalid: A for loop that unnecessarily computes the same value on every iteration.">
        <![CDATA[
for ($i = 0; $i < <em>count($foo)</em>; $i++) {
    echo $foo[$i]."\n";
}
        ]]>
        </code>
    </code_comparison>
</documentation>
