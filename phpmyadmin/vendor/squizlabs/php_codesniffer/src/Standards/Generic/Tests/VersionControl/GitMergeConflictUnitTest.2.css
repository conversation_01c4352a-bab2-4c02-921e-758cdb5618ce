/*
 * This is a CSS comment.
<<<<<<< HEAD
 * This is a merge conflict started in a comment, ending in a CSS block.
 */
.SettingsTabPaneWidgetType-tab-mid {
   background: transparent url(tab_inact_mid.png) repeat-x;
=======
 * which should be detected.
 **/
.SettingsTabPaneWidgetType-tab-start {
   line-height: -25px;
>>>>>>> ref/heads/feature-branch
   cursor: pointer;
   -moz-user-select: none;
}

/*
 * The above tests are based on "normal" tokens.
 * The below test checks that once the tokenizer breaks down because of
 * unexpected merge conflict boundaries, subsequent boundaries will still
 * be detected correctly.
 */

/*
 * This is a CSS comment.
<<<<<<< HEAD
 * This is a merge conflict...
=======
 * which should be detected.
>>>>>>> ref/heads/feature-branch
 */
