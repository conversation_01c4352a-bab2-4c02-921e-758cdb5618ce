<?php

// True
function myFunction($arg1, $arg2=TRUE)
{
}
function myFunction($arg1, $arg2=TRUE)
{
}
function myFunction($arg1, $arg2=TRUE)
{
}

if ($variable === TRUE) { }
if ($variable === TRUE) { }
if ($variable === TRUE) { }


// False
function myFunction($arg1, $arg2=FALSE)
{
}
function myFunction($arg1, $arg2=FALSE)
{
}
function myFunction($arg1, $arg2=FALSE)
{
}

if ($variable === FALSE) { }
if ($variable === FALSE) { }
if ($variable === FALSE) { }


// Null
function myFunction($arg1, $arg2=NULL)
{
}
function myFunction($arg1, $arg2=NULL)
{
}
function myFunction($arg1, $arg2=NULL)
{
}

if ($variable === NULL) { }
if ($variable === NULL) { }
if ($variable === NULL) { }

$x = new stdClass();
$x->null = 7;

use Zend\Log\Writer\Null as NullWriter;
new \Zend\Log\Writer\Null();

namespace False;

class True extends Null implements False {}

use True\Something;
use Something\True;
class MyClass
{
    public function myFunction()
    {
        $var = array('foo' => new True());
    }
}

$x = $f?FALSE:TRUE;
$x = $f? FALSE:TRUE;

class MyClass
{
    // Spice things up a little.
    const true = FALSE;
}

var_dump(MyClass::true);

function true() {}