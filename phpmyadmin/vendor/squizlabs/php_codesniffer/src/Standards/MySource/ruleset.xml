<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="MySource" xsi:noNamespaceSchemaLocation="../../../phpcs.xsd">
    <description>The MySource coding standard builds on the Squiz coding standard. Currently used for MySource Mini development.</description>

    <exclude-pattern>*/Tests/*</exclude-pattern>
    <exclude-pattern>*/Oven/*</exclude-pattern>
    <exclude-pattern>*/data/*</exclude-pattern>
    <exclude-pattern>*/jquery.js</exclude-pattern>
    <exclude-pattern>*/jquery.*.js</exclude-pattern>
    <exclude-pattern>*/viper/*</exclude-pattern>
    <exclude-pattern>DALConf.inc</exclude-pattern>

    <!-- Include the whole Squiz standard except FunctionComment, which we override -->
    <rule ref="Squiz">
        <exclude name="Squiz.Commenting.FunctionComment"/>
    </rule>

</ruleset>
