<?xml version="1.0" encoding="UTF-8"?>

<phpunit backupGlobals="false"
         backupStaticAttributes="false"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnFailure="false"
         bootstrap="vendor/autoload.php"
>
    <testsuites>
        <testsuite name="Twig Extensions Test Suite">
            <directory>./test/</directory>
        </testsuite>
    </testsuites>

    <logging>
        <log type="coverage-clover" target="build/logs/clover.xml"/>
    </logging>

    <filter>
        <whitelist>
            <directory suffix=".php">./src/</directory>
        </whitelist>
    </filter>
</phpunit>
