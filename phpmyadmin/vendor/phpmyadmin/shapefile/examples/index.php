<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>ShapeFiles demo</title>
</head>

<body>
This demo illustrates some of the possibile uses of ShapeFiles. There are several more, just check the source code to see the methods implemented in the classes ShapeFile and ShapeRecord.
<p><a href="create_shapefile.php">Create new ShapeFile</a></p>
<p><a href="read_point.php">Read Point ShapeFile</a></p>
<p><a href="read_polygon.php">Read Polygon ShapeFile</a>   </p>
</body>
</html>
