{"query": "SELECT wins FROM players WHERE auth = '[U1:123456789]' LIMIT 1\n", "lexer": {"@type": "PhpMyAdmin\\SqlParser\\Lexer", "PARSER_METHODS": ["parseDelimiter", "parseWhitespace", "parseNumber", "parseComment", "parseOperator", "parseBool", "parseString", "parseSymbol", "parseKeyword", "parseLabel", "parseUnknown"], "KEYWORD_NAME_INDICATORS": ["FROM", "SET", "WHERE"], "OPERATOR_NAME_INDICATORS": [",", "."], "str": "SELECT wins FROM players WHERE auth = '[U1:123456789]' LIMIT 1\n", "len": 63, "last": 63, "list": {"@type": "PhpMyAdmin\\SqlParser\\TokensList", "tokens": [{"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 0}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 6}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "wins", "value": "wins", "keyword": null, "type": 0, "flags": 0, "position": 7}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 11}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 12}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 16}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "players", "value": "players", "keyword": null, "type": 0, "flags": 0, "position": 17}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 24}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 25}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 30}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "auth", "value": "auth", "keyword": null, "type": 0, "flags": 0, "position": 31}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 35}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 36}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 37}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "'[U1:123456789]'", "value": "[U1:123456789]", "keyword": null, "type": 7, "flags": 1, "position": 38}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 54}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LIMIT", "value": "LIMIT", "keyword": "LIMIT", "type": 1, "flags": 3, "position": 55}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 60}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 61}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 62}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": null, "value": null, "keyword": null, "type": 9, "flags": 0, "position": null}], "count": 21, "idx": 0}, "DEFAULT_DELIMITER": ";", "delimiter": ";", "delimiterLen": 1, "strict": false, "errors": []}, "parser": null, "errors": {"lexer": [], "parser": []}}