{"query": "\n\n\nSELECT      \n\t\t'w h i t e\t\ts p a c e'\n\n\t\t\n\t\t\t\n                \n             ", "lexer": {"@type": "PhpMyAdmin\\SqlParser\\Lexer", "PARSER_METHODS": ["parseDelimiter", "parseWhitespace", "parseNumber", "parseComment", "parseOperator", "parseBool", "parseString", "parseSymbol", "parseKeyword", "parseLabel", "parseUnknown"], "KEYWORD_NAME_INDICATORS": ["FROM", "SET", "WHERE"], "OPERATOR_NAME_INDICATORS": [",", "."], "str": "\n\n\nSELECT      \n\t\t'w h i t e\t\ts p a c e'\n\n\t\t\n\t\t\t\n                \n             ", "len": 79, "last": 79, "list": {"@type": "PhpMyAdmin\\SqlParser\\TokensList", "tokens": [{"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n\n\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 0}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 3}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "      \n\t\t", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 9}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "'w h i t e\t\ts p a c e'", "value": "w h i t e\t\ts p a c e", "keyword": null, "type": 7, "flags": 1, "position": 18}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n\n\t\t\n\t\t\t\n                \n             ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 40}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": null, "value": null, "keyword": null, "type": 9, "flags": 0, "position": null}], "count": 6, "idx": 0}, "DEFAULT_DELIMITER": ";", "delimiter": ";", "delimiterLen": 1, "strict": false, "errors": []}, "parser": null, "errors": {"lexer": [], "parser": []}}