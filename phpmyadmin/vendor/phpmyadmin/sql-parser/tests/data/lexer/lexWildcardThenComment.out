{"query": "SELECT */* comment */\n\nSELECT /* comment */*\n\nSELECT 2*/* comment */3\n\nSELECT 2/* comment */*3\n\nSELECT */*\ncomment\non\nmultiple\nlines\n*/FROM\n\nDELETE foo.*/* foo */ USING\n\nDELETE foo.*/* foo */,bar.*/*bar*/ USING\n\nSELECT `*`/*with comment*/ AS star_field\n\nSELECT `*`,*/*with comment*/\n\nDELETE a.*/*multi\nline /* with C open tag\ncomment inside */ USING\n\nSELECT 2*/* operator */3 + 3/* operator */*2,/* start wildcard */*/* end wildcard */\n\nSELECT `*`/*a*/*/*b*/`*`\n\n-- invalid queries\n/* SELECT */*", "lexer": {"@type": "PhpMyAdmin\\SqlParser\\Lexer", "PARSER_METHODS": ["parseDelimiter", "parseWhitespace", "parseNumber", "parseComment", "parseOperator", "parseBool", "parseString", "parseSymbol", "parseKeyword", "parseLabel", "parseUnknown"], "KEYWORD_NAME_INDICATORS": ["FROM", "SET", "WHERE"], "OPERATOR_NAME_INDICATORS": [",", "."], "str": "SELECT */* comment */\n\nSELECT /* comment */*\n\nSELECT 2*/* comment */3\n\nSELECT 2/* comment */*3\n\nSELECT */*\ncomment\non\nmultiple\nlines\n*/FROM\n\nDELETE foo.*/* foo */ USING\n\nDELETE foo.*/* foo */,bar.*/*bar*/ USING\n\nSELECT `*`/*with comment*/ AS star_field\n\nSELECT `*`,*/*with comment*/\n\nDELETE a.*/*multi\nline /* with C open tag\ncomment inside */ USING\n\nSELECT 2*/* operator */3 + 3/* operator */*2,/* start wildcard */*/* end wildcard */\n\nSELECT `*`/*a*/*/*b*/`*`\n\n-- invalid queries\n/* SELECT */*", "len": 495, "last": 495, "list": {"@type": "PhpMyAdmin\\SqlParser\\TokensList", "tokens": [{"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 0}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 6}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 7}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 8}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 21}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 23}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 29}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 30}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 43}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 44}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 46}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 52}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2", "value": 2, "keyword": null, "type": 6, "flags": 0, "position": 53}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 54}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 55}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3", "value": 3, "keyword": null, "type": 6, "flags": 0, "position": 68}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 69}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 71}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 77}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2", "value": 2, "keyword": null, "type": 6, "flags": 0, "position": 78}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 79}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 92}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3", "value": 3, "keyword": null, "type": 6, "flags": 0, "position": 93}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 94}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 96}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 102}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 103}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/*\ncomment\non\nmultiple\nlines\n*/", "value": "/*\ncomment\non\nmultiple\nlines\n*/", "keyword": null, "type": 4, "flags": 2, "position": 104}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 135}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 139}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DELETE", "value": "DELETE", "keyword": "DELETE", "type": 1, "flags": 3, "position": 141}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 147}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "foo", "value": "foo", "keyword": null, "type": 0, "flags": 0, "position": 148}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 151}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 152}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* foo */", "value": "/* foo */", "keyword": null, "type": 4, "flags": 2, "position": 153}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 162}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "USING", "value": "USING", "keyword": "USING", "type": 1, "flags": 3, "position": 163}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 168}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DELETE", "value": "DELETE", "keyword": "DELETE", "type": 1, "flags": 3, "position": 170}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 176}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "foo", "value": "foo", "keyword": null, "type": 0, "flags": 0, "position": 177}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 180}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 181}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* foo */", "value": "/* foo */", "keyword": null, "type": 4, "flags": 2, "position": 182}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 191}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "bar", "value": "bar", "keyword": null, "type": 0, "flags": 0, "position": 192}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 195}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 196}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/*bar*/", "value": "/*bar*/", "keyword": null, "type": 4, "flags": 2, "position": 197}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 204}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "USING", "value": "USING", "keyword": "USING", "type": 1, "flags": 3, "position": 205}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 210}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 212}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 218}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`*`", "value": "*", "keyword": null, "type": 8, "flags": 2, "position": 219}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/*with comment*/", "value": "/*with comment*/", "keyword": null, "type": 4, "flags": 2, "position": 222}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 238}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 239}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 241}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "star_field", "value": "star_field", "keyword": null, "type": 0, "flags": 0, "position": 242}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 252}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 254}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 260}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`*`", "value": "*", "keyword": null, "type": 8, "flags": 2, "position": 261}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 264}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 265}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/*with comment*/", "value": "/*with comment*/", "keyword": null, "type": 4, "flags": 2, "position": 266}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 282}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DELETE", "value": "DELETE", "keyword": "DELETE", "type": 1, "flags": 3, "position": 284}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 290}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 291}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 292}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 293}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/*multi\nline /* with C open tag\ncomment inside */", "value": "/*multi\nline /* with C open tag\ncomment inside */", "keyword": null, "type": 4, "flags": 2, "position": 294}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 343}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "USING", "value": "USING", "keyword": "USING", "type": 1, "flags": 3, "position": 344}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 349}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 351}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 357}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2", "value": 2, "keyword": null, "type": 6, "flags": 0, "position": 358}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 359}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* operator */", "value": "/* operator */", "keyword": null, "type": 4, "flags": 2, "position": 360}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3", "value": 3, "keyword": null, "type": 6, "flags": 0, "position": 374}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 375}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "+", "value": "+", "keyword": null, "type": 2, "flags": 1, "position": 376}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 377}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3", "value": 3, "keyword": null, "type": 6, "flags": 0, "position": 378}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* operator */", "value": "/* operator */", "keyword": null, "type": 4, "flags": 2, "position": 379}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 393}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2", "value": 2, "keyword": null, "type": 6, "flags": 0, "position": 394}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 395}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* start wildcard */", "value": "/* start wildcard */", "keyword": null, "type": 4, "flags": 2, "position": 396}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 416}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* end wildcard */", "value": "/* end wildcard */", "keyword": null, "type": 4, "flags": 2, "position": 417}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 435}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 437}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 443}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`*`", "value": "*", "keyword": null, "type": 8, "flags": 2, "position": 444}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/*a*/", "value": "/*a*/", "keyword": null, "type": 4, "flags": 2, "position": 447}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 452}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/*b*/", "value": "/*b*/", "keyword": null, "type": 4, "flags": 2, "position": 453}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`*`", "value": "*", "keyword": null, "type": 8, "flags": 2, "position": 458}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 461}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-- invalid queries", "value": "-- invalid queries", "keyword": null, "type": 4, "flags": 4, "position": 463}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 481}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* SELECT */", "value": "/* SELECT */", "keyword": null, "type": 4, "flags": 2, "position": 482}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 494}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": null, "value": null, "keyword": null, "type": 9, "flags": 0, "position": null}], "count": 109, "idx": 0}, "DEFAULT_DELIMITER": ";", "delimiter": ";", "delimiterLen": 1, "strict": false, "errors": []}, "parser": null, "errors": {"lexer": [], "parser": []}}