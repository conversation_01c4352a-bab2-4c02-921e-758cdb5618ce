{"query": "SELECT 2*3\nSELECT 2 * 3\nSELECT field * 8\nSELECT 8 * field\nSELECT foo * bar\nSELECT `escaped_field` * 16\nSELECT 16 * `escaped_field`\nSELECT `foo` * `bar`\nSELECT `foo`*`bar`\nSELECT 1 * (SELECT COUNT(1) FROM nb_rows)\nSELECT (SELECT COUNT(1) FROM nb_rows) * 1\nSELECT (SELECT COUNT(1) FROM nb_rows) * (SELECT COUNT(1) FROM nb_rows)\nSELECT (1 * 1) * (2 * 2)\nSELECT 1 * (2 * (3 * (4 * 5)))\nSELECT 2.71 * 3.14\nSELECT 2.71 * -3.14\nSELECT -2.71 * 3.14\nSELECT -2.71 * -3.14\nSELECT 0xABC * 0xCBA\nSELECT 0xABC * -0xCBA\nSELECT -0xABC * 0xCBA\nSELECT -0xABC * -0xCBA\n\n-- Now same but with comments inside (C style comments could conflicts with operator if lexer is failing)\nSELECT 2 * /* comment */3\nSELECT 2/* comment */ * 3\nSELECT 2/* comment with * inside */ * 3\nSELECT /* comment */ field /* comment */ * /* comment */ 8 /* comment */\nSELECT /* comment */ 8 /* comment */ * /* comment */ field /* comment */\nSELECT /* comment */ foo /* comment */ * /* comment */ bar /* comment */\nSELECT /* comment */ `escaped_field` /* comment */ * /* comment */ 16 /* comment */\nSELECT /* comment */ 16 /* comment */ * /* comment */ `escaped_field` /* comment */\nSELECT /* comment */ `foo` /* comment */ * /* comment */ `bar` /* comment */\nSELECT /* `comment` */ `foo` /* `comment` */ * /* `comment` */ `bar` /* `comment` */\nSELECT /* comment */ 1 /* comment */ * /* comment */ (SELECT COUNT(/* comment */1/* comment */) /* comment */ FROM nb_rows)\nSELECT /* comment */ (SELECT COUNT(1) /* comment */FROM/* comment */ nb_rows) /* comment */ * /* comment */ 1\nSELECT (SELECT /* comment */ COUNT(1) /* comment */ FROM /* comment */ nb_rows) /* comment */ * /* comment */ (SELECT COUNT(1) FROM nb_rows)\nSELECT (1 * 1) /* comment */ * /* comment */ (2 * 2)\nSELECT /* comment */ 1 /* comment */ * /* comment */ (2 /* comment */ * /* comment */ (3 /* comment */ * /* comment */ (4 /* comment */ * /* comment */ 5))) /* comment */\nSELECT 2.71 /* comment */ * /* comment */ 3.14\nSELECT 2.71 /* comment */ * /* comment */ -3.14\nSELECT -2.71 /* comment */ * /* comment */ 3.14\nSELECT -2.71 /* comment */ * /* comment */ -3.14\nSELECT 0xABC /* comment */ * /* comment */ 0xCBA\nSELECT 0xABC /* comment */ * /* comment */ -0xCBA\nSELECT -0xABC /* comment */ * /* comment */ 0xCBA\nSELECT -0xABC /* comment */ * /* comment */ -0xCBA\nSELECT 1 /* comment with FROM keyword */ * /* comment with USING keyword */1\nSELECT 1 /* comment with ) */ * /* comment with , keyword */1\n\n", "lexer": {"@type": "PhpMyAdmin\\SqlParser\\Lexer", "PARSER_METHODS": ["parseDelimiter", "parseWhitespace", "parseNumber", "parseComment", "parseOperator", "parseBool", "parseString", "parseSymbol", "parseKeyword", "parseLabel", "parseUnknown"], "KEYWORD_NAME_INDICATORS": ["FROM", "SET", "WHERE"], "OPERATOR_NAME_INDICATORS": [",", "."], "str": "SELECT 2*3\nSELECT 2 * 3\nSELECT field * 8\nSELECT 8 * field\nSELECT foo * bar\nSELECT `escaped_field` * 16\nSELECT 16 * `escaped_field`\nSELECT `foo` * `bar`\nSELECT `foo`*`bar`\nSELECT 1 * (SELECT COUNT(1) FROM nb_rows)\nSELECT (SELECT COUNT(1) FROM nb_rows) * 1\nSELECT (SELECT COUNT(1) FROM nb_rows) * (SELECT COUNT(1) FROM nb_rows)\nSELECT (1 * 1) * (2 * 2)\nSELECT 1 * (2 * (3 * (4 * 5)))\nSELECT 2.71 * 3.14\nSELECT 2.71 * -3.14\nSELECT -2.71 * 3.14\nSELECT -2.71 * -3.14\nSELECT 0xABC * 0xCBA\nSELECT 0xABC * -0xCBA\nSELECT -0xABC * 0xCBA\nSELECT -0xABC * -0xCBA\n\n-- Now same but with comments inside (C style comments could conflicts with operator if lexer is failing)\nSELECT 2 * /* comment */3\nSELECT 2/* comment */ * 3\nSELECT 2/* comment with * inside */ * 3\nSELECT /* comment */ field /* comment */ * /* comment */ 8 /* comment */\nSELECT /* comment */ 8 /* comment */ * /* comment */ field /* comment */\nSELECT /* comment */ foo /* comment */ * /* comment */ bar /* comment */\nSELECT /* comment */ `escaped_field` /* comment */ * /* comment */ 16 /* comment */\nSELECT /* comment */ 16 /* comment */ * /* comment */ `escaped_field` /* comment */\nSELECT /* comment */ `foo` /* comment */ * /* comment */ `bar` /* comment */\nSELECT /* `comment` */ `foo` /* `comment` */ * /* `comment` */ `bar` /* `comment` */\nSELECT /* comment */ 1 /* comment */ * /* comment */ (SELECT COUNT(/* comment */1/* comment */) /* comment */ FROM nb_rows)\nSELECT /* comment */ (SELECT COUNT(1) /* comment */FROM/* comment */ nb_rows) /* comment */ * /* comment */ 1\nSELECT (SELECT /* comment */ COUNT(1) /* comment */ FROM /* comment */ nb_rows) /* comment */ * /* comment */ (SELECT COUNT(1) FROM nb_rows)\nSELECT (1 * 1) /* comment */ * /* comment */ (2 * 2)\nSELECT /* comment */ 1 /* comment */ * /* comment */ (2 /* comment */ * /* comment */ (3 /* comment */ * /* comment */ (4 /* comment */ * /* comment */ 5))) /* comment */\nSELECT 2.71 /* comment */ * /* comment */ 3.14\nSELECT 2.71 /* comment */ * /* comment */ -3.14\nSELECT -2.71 /* comment */ * /* comment */ 3.14\nSELECT -2.71 /* comment */ * /* comment */ -3.14\nSELECT 0xABC /* comment */ * /* comment */ 0xCBA\nSELECT 0xABC /* comment */ * /* comment */ -0xCBA\nSELECT -0xABC /* comment */ * /* comment */ 0xCBA\nSELECT -0xABC /* comment */ * /* comment */ -0xCBA\nSELECT 1 /* comment with FROM keyword */ * /* comment with USING keyword */1\nSELECT 1 /* comment with ) */ * /* comment with , keyword */1\n\n", "len": 2429, "last": 2429, "list": {"@type": "PhpMyAdmin\\SqlParser\\TokensList", "tokens": [{"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 0}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 6}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2", "value": 2, "keyword": null, "type": 6, "flags": 0, "position": 7}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 8}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3", "value": 3, "keyword": null, "type": 6, "flags": 0, "position": 9}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 10}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 11}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 17}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2", "value": 2, "keyword": null, "type": 6, "flags": 0, "position": 18}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 19}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 20}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 21}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3", "value": 3, "keyword": null, "type": 6, "flags": 0, "position": 22}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 23}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 24}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 30}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "field", "value": "field", "keyword": "FIELD", "type": 1, "flags": 33, "position": 31}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 36}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 37}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 38}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "8", "value": 8, "keyword": null, "type": 6, "flags": 0, "position": 39}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 40}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 41}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 47}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "8", "value": 8, "keyword": null, "type": 6, "flags": 0, "position": 48}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 49}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 50}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 51}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "field", "value": "field", "keyword": "FIELD", "type": 1, "flags": 33, "position": 52}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 57}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 58}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 64}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "foo", "value": "foo", "keyword": null, "type": 0, "flags": 0, "position": 65}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 68}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 69}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 70}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "bar", "value": "bar", "keyword": null, "type": 0, "flags": 0, "position": 71}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 74}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 75}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 81}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`escaped_field`", "value": "escaped_field", "keyword": null, "type": 8, "flags": 2, "position": 82}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 97}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 98}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 99}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "16", "value": 16, "keyword": null, "type": 6, "flags": 0, "position": 100}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 102}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 103}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 109}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "16", "value": 16, "keyword": null, "type": 6, "flags": 0, "position": 110}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 112}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 113}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 114}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`escaped_field`", "value": "escaped_field", "keyword": null, "type": 8, "flags": 2, "position": 115}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 130}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 131}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 137}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`foo`", "value": "foo", "keyword": null, "type": 8, "flags": 2, "position": 138}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 143}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 144}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 145}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`bar`", "value": "bar", "keyword": null, "type": 8, "flags": 2, "position": 146}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 151}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 152}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 158}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`foo`", "value": "foo", "keyword": null, "type": 8, "flags": 2, "position": 159}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 164}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`bar`", "value": "bar", "keyword": null, "type": 8, "flags": 2, "position": 165}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 170}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 171}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 177}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 178}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 179}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 180}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 181}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 182}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 183}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 189}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "COUNT", "value": "COUNT", "keyword": "COUNT", "type": 1, "flags": 33, "position": 190}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 195}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 196}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 197}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 198}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 199}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 203}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "nb_rows", "value": "nb_rows", "keyword": null, "type": 0, "flags": 0, "position": 204}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 211}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 212}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 213}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 219}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 220}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 221}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 227}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "COUNT", "value": "COUNT", "keyword": "COUNT", "type": 1, "flags": 33, "position": 228}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 233}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 234}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 235}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 236}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 237}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 241}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "nb_rows", "value": "nb_rows", "keyword": null, "type": 0, "flags": 0, "position": 242}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 249}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 250}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 251}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 252}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 253}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 254}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 255}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 261}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 262}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 263}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 269}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "COUNT", "value": "COUNT", "keyword": "COUNT", "type": 1, "flags": 33, "position": 270}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 275}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 276}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 277}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 278}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 279}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 283}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "nb_rows", "value": "nb_rows", "keyword": null, "type": 0, "flags": 0, "position": 284}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 291}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 292}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 293}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 294}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 295}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 296}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 302}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "COUNT", "value": "COUNT", "keyword": "COUNT", "type": 1, "flags": 33, "position": 303}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 308}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 309}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 310}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 311}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 312}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 316}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "nb_rows", "value": "nb_rows", "keyword": null, "type": 0, "flags": 0, "position": 317}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 324}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 325}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 326}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 332}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 333}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 334}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 335}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 336}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 337}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 338}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 339}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 340}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 341}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 342}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 343}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2", "value": 2, "keyword": null, "type": 6, "flags": 0, "position": 344}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 345}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 346}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 347}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2", "value": 2, "keyword": null, "type": 6, "flags": 0, "position": 348}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 349}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 350}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 351}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 357}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 358}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 359}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 360}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 361}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 362}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2", "value": 2, "keyword": null, "type": 6, "flags": 0, "position": 363}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 364}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 365}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 366}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 367}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3", "value": 3, "keyword": null, "type": 6, "flags": 0, "position": 368}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 369}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 370}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 371}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 372}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "4", "value": 4, "keyword": null, "type": 6, "flags": 0, "position": 373}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 374}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 375}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 376}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "5", "value": 5, "keyword": null, "type": 6, "flags": 0, "position": 377}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 378}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 379}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 380}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 381}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 382}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 388}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2.71", "value": 2.71, "keyword": null, "type": 6, "flags": 2, "position": 389}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 393}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 394}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 395}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3.14", "value": 3.14, "keyword": null, "type": 6, "flags": 2, "position": 396}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 400}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 401}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 407}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2.71", "value": 2.71, "keyword": null, "type": 6, "flags": 2, "position": 408}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 412}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 413}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 414}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-3.14", "value": -3.14, "keyword": null, "type": 6, "flags": 10, "position": 415}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 420}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 421}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 427}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-2.71", "value": -2.71, "keyword": null, "type": 6, "flags": 10, "position": 428}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 433}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 434}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 435}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3.14", "value": 3.14, "keyword": null, "type": 6, "flags": 2, "position": 436}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 440}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 441}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 447}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-2.71", "value": -2.71, "keyword": null, "type": 6, "flags": 10, "position": 448}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 453}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 454}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 455}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-3.14", "value": -3.14, "keyword": null, "type": 6, "flags": 10, "position": 456}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 461}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 462}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 468}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "0xABC", "value": 2748, "keyword": null, "type": 6, "flags": 1, "position": 469}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 474}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 475}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 476}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "0xCBA", "value": 3258, "keyword": null, "type": 6, "flags": 1, "position": 477}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 482}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 483}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 489}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "0xABC", "value": 2748, "keyword": null, "type": 6, "flags": 1, "position": 490}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 495}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 496}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 497}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-0xCBA", "value": -3258, "keyword": null, "type": 6, "flags": 9, "position": 498}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 504}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 505}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 511}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-0xABC", "value": -2748, "keyword": null, "type": 6, "flags": 9, "position": 512}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 518}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 519}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 520}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "0xCBA", "value": 3258, "keyword": null, "type": 6, "flags": 1, "position": 521}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 526}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 527}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 533}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-0xABC", "value": -2748, "keyword": null, "type": 6, "flags": 9, "position": 534}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 540}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 541}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 542}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-0xCBA", "value": -3258, "keyword": null, "type": 6, "flags": 9, "position": 543}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 549}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-- Now same but with comments inside (C style comments could conflicts with operator if lexer is failing)", "value": "-- Now same but with comments inside (C style comments could conflicts with operator if lexer is failing)", "keyword": null, "type": 4, "flags": 4, "position": 551}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 656}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 657}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 663}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2", "value": 2, "keyword": null, "type": 6, "flags": 0, "position": 664}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 665}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 666}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 667}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 668}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3", "value": 3, "keyword": null, "type": 6, "flags": 0, "position": 681}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 682}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 683}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 689}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2", "value": 2, "keyword": null, "type": 6, "flags": 0, "position": 690}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 691}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 704}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 705}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 706}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3", "value": 3, "keyword": null, "type": 6, "flags": 0, "position": 707}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 708}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 709}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 715}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2", "value": 2, "keyword": null, "type": 6, "flags": 0, "position": 716}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment with * inside */", "value": "/* comment with * inside */", "keyword": null, "type": 4, "flags": 2, "position": 717}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 744}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 745}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 746}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3", "value": 3, "keyword": null, "type": 6, "flags": 0, "position": 747}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 748}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 749}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 755}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 756}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 769}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "field", "value": "field", "keyword": "FIELD", "type": 1, "flags": 33, "position": 770}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 775}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 776}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 789}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 790}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 791}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 792}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 805}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "8", "value": 8, "keyword": null, "type": 6, "flags": 0, "position": 806}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 807}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 808}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 821}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 822}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 828}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 829}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 842}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "8", "value": 8, "keyword": null, "type": 6, "flags": 0, "position": 843}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 844}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 845}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 858}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 859}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 860}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 861}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 874}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "field", "value": "field", "keyword": "FIELD", "type": 1, "flags": 33, "position": 875}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 880}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 881}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 894}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 895}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 901}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 902}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 915}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "foo", "value": "foo", "keyword": null, "type": 0, "flags": 0, "position": 916}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 919}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 920}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 933}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 934}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 935}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 936}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 949}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "bar", "value": "bar", "keyword": null, "type": 0, "flags": 0, "position": 950}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 953}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 954}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 967}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 968}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 974}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 975}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 988}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`escaped_field`", "value": "escaped_field", "keyword": null, "type": 8, "flags": 2, "position": 989}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1004}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1005}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1018}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 1019}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1020}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1021}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1034}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "16", "value": 16, "keyword": null, "type": 6, "flags": 0, "position": 1035}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1037}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1038}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1051}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1052}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1058}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1059}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1072}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "16", "value": 16, "keyword": null, "type": 6, "flags": 0, "position": 1073}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1075}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1076}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1089}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 1090}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1091}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1092}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1105}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`escaped_field`", "value": "escaped_field", "keyword": null, "type": 8, "flags": 2, "position": 1106}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1121}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1122}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1135}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1136}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1142}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1143}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1156}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`foo`", "value": "foo", "keyword": null, "type": 8, "flags": 2, "position": 1157}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1162}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1163}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1176}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 1177}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1178}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1179}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1192}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`bar`", "value": "bar", "keyword": null, "type": 8, "flags": 2, "position": 1193}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1198}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1199}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1212}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1213}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1219}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* `comment` */", "value": "/* `comment` */", "keyword": null, "type": 4, "flags": 2, "position": 1220}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1235}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`foo`", "value": "foo", "keyword": null, "type": 8, "flags": 2, "position": 1236}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1241}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* `comment` */", "value": "/* `comment` */", "keyword": null, "type": 4, "flags": 2, "position": 1242}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1257}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 1258}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1259}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* `comment` */", "value": "/* `comment` */", "keyword": null, "type": 4, "flags": 2, "position": 1260}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1275}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`bar`", "value": "bar", "keyword": null, "type": 8, "flags": 2, "position": 1276}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1281}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* `comment` */", "value": "/* `comment` */", "keyword": null, "type": 4, "flags": 2, "position": 1282}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1297}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1298}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1304}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1305}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1318}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 1319}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1320}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1321}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1334}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 1335}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1336}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1337}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1350}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1351}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1352}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1358}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "COUNT", "value": "COUNT", "keyword": "COUNT", "type": 1, "flags": 33, "position": 1359}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1364}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1365}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 1378}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1379}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 1392}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1393}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1394}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1407}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 1408}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1412}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "nb_rows", "value": "nb_rows", "keyword": null, "type": 0, "flags": 0, "position": 1413}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 1420}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1421}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1422}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1428}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1429}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1442}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1443}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1444}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1450}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "COUNT", "value": "COUNT", "keyword": "COUNT", "type": 1, "flags": 33, "position": 1451}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1456}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 1457}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 1458}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1459}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1460}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 1473}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1477}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1490}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "nb_rows", "value": "nb_rows", "keyword": null, "type": 0, "flags": 0, "position": 1491}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 1498}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1499}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1500}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1513}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 1514}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1515}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1516}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1529}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 1530}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1531}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1532}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1538}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1539}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1540}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1546}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1547}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1560}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "COUNT", "value": "COUNT", "keyword": "COUNT", "type": 1, "flags": 33, "position": 1561}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1566}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 1567}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 1568}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1569}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1570}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1583}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 1584}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1588}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1589}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1602}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "nb_rows", "value": "nb_rows", "keyword": null, "type": 0, "flags": 0, "position": 1603}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 1610}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1611}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1612}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1625}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 1626}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1627}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1628}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1641}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1642}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1643}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1649}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "COUNT", "value": "COUNT", "keyword": "COUNT", "type": 1, "flags": 33, "position": 1650}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1655}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 1656}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 1657}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1658}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 1659}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1663}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "nb_rows", "value": "nb_rows", "keyword": null, "type": 0, "flags": 0, "position": 1664}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 1671}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1672}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1673}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1679}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1680}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 1681}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1682}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 1683}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1684}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 1685}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 1686}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1687}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1688}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1701}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 1702}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1703}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1704}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1717}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1718}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2", "value": 2, "keyword": null, "type": 6, "flags": 0, "position": 1719}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1720}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 1721}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1722}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2", "value": 2, "keyword": null, "type": 6, "flags": 0, "position": 1723}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 1724}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1725}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1726}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1732}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1733}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1746}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 1747}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1748}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1749}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1762}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 1763}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1764}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1765}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1778}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1779}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2", "value": 2, "keyword": null, "type": 6, "flags": 0, "position": 1780}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1781}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1782}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1795}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 1796}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1797}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1798}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1811}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1812}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3", "value": 3, "keyword": null, "type": 6, "flags": 0, "position": 1813}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1814}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1815}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1828}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 1829}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1830}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1831}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1844}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1845}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "4", "value": 4, "keyword": null, "type": 6, "flags": 0, "position": 1846}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1847}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1848}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1861}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 1862}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1863}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1864}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1877}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "5", "value": 5, "keyword": null, "type": 6, "flags": 0, "position": 1878}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 1879}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 1880}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 1881}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1882}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1883}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1896}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1897}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1903}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2.71", "value": 2.71, "keyword": null, "type": 6, "flags": 2, "position": 1904}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1908}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1909}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1922}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 1923}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1924}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1925}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1938}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3.14", "value": 3.14, "keyword": null, "type": 6, "flags": 2, "position": 1939}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1943}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1944}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1950}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2.71", "value": 2.71, "keyword": null, "type": 6, "flags": 2, "position": 1951}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1955}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1956}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1969}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 1970}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1971}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1972}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1985}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-3.14", "value": -3.14, "keyword": null, "type": 6, "flags": 10, "position": 1986}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1991}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1992}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1998}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-2.71", "value": -2.71, "keyword": null, "type": 6, "flags": 10, "position": 1999}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2004}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 2005}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2018}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 2019}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2020}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 2021}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2034}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3.14", "value": 3.14, "keyword": null, "type": 6, "flags": 2, "position": 2035}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2039}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 2040}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2046}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-2.71", "value": -2.71, "keyword": null, "type": 6, "flags": 10, "position": 2047}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2052}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 2053}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2066}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 2067}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2068}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 2069}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2082}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-3.14", "value": -3.14, "keyword": null, "type": 6, "flags": 10, "position": 2083}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2088}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 2089}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2095}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "0xABC", "value": 2748, "keyword": null, "type": 6, "flags": 1, "position": 2096}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2101}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 2102}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2115}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 2116}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2117}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 2118}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2131}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "0xCBA", "value": 3258, "keyword": null, "type": 6, "flags": 1, "position": 2132}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2137}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 2138}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2144}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "0xABC", "value": 2748, "keyword": null, "type": 6, "flags": 1, "position": 2145}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2150}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 2151}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2164}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 2165}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2166}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 2167}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2180}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-0xCBA", "value": -3258, "keyword": null, "type": 6, "flags": 9, "position": 2181}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2187}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 2188}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2194}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-0xABC", "value": -2748, "keyword": null, "type": 6, "flags": 9, "position": 2195}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2201}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 2202}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2215}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 2216}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2217}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 2218}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2231}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "0xCBA", "value": 3258, "keyword": null, "type": 6, "flags": 1, "position": 2232}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2237}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 2238}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2244}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-0xABC", "value": -2748, "keyword": null, "type": 6, "flags": 9, "position": 2245}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2251}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 2252}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2265}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 2266}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2267}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 2268}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2281}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-0xCBA", "value": -3258, "keyword": null, "type": 6, "flags": 9, "position": 2282}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2288}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 2289}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2295}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 2296}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2297}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment with FROM keyword */", "value": "/* comment with FROM keyword */", "keyword": null, "type": 4, "flags": 2, "position": 2298}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2329}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 2330}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2331}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment with USING keyword */", "value": "/* comment with USING keyword */", "keyword": null, "type": 4, "flags": 2, "position": 2332}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 2364}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2365}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 2366}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2372}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 2373}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2374}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment with ) */", "value": "/* comment with ) */", "keyword": null, "type": 4, "flags": 2, "position": 2375}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2395}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 1, "position": 2396}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2397}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment with , keyword */", "value": "/* comment with , keyword */", "keyword": null, "type": 4, "flags": 2, "position": 2398}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 2426}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2427}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": null, "value": null, "keyword": null, "type": 9, "flags": 0, "position": null}], "count": 672, "idx": 0}, "DEFAULT_DELIMITER": ";", "delimiter": ";", "delimiterLen": 1, "strict": false, "errors": []}, "parser": null, "errors": {"lexer": [], "parser": []}}