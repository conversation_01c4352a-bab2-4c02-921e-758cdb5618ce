{"query": "CREATE PROCEDURE doiterate(p1 INT)\nBEGIN\n  label1 : LOOP\n    SET p1 = p1 + 1;\n    IF p1 < 10 THEN\n      ITERATE label1;\n    END IF;\n    LEAVE label1;\n  END LOOP label1;\n  SET @x = p1;\nEND", "lexer": {"@type": "PhpMyAdmin\\SqlParser\\Lexer", "PARSER_METHODS": ["parseDelimiter", "parseWhitespace", "parseNumber", "parseComment", "parseOperator", "parseBool", "parseString", "parseSymbol", "parseKeyword", "parseLabel", "parseUnknown"], "KEYWORD_NAME_INDICATORS": ["FROM", "SET", "WHERE"], "OPERATOR_NAME_INDICATORS": [",", "."], "str": "CREATE PROCEDURE doiterate(p1 INT)\nBEGIN\n  label1 : LOOP\n    SET p1 = p1 + 1;\n    IF p1 < 10 THEN\n      ITERATE label1;\n    END IF;\n    LEAVE label1;\n  END LOOP label1;\n  SET @x = p1;\nEND", "len": 187, "last": 187, "list": {"@type": "PhpMyAdmin\\SqlParser\\TokensList", "tokens": [{"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CREATE", "value": "CREATE", "keyword": "CREATE", "type": 1, "flags": 3, "position": 0}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 6}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "PROCEDURE", "value": "PROCEDURE", "keyword": "PROCEDURE", "type": 1, "flags": 3, "position": 7}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 16}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "doiterate", "value": "doiterate", "keyword": null, "type": 0, "flags": 0, "position": 17}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 26}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "p1", "value": "p1", "keyword": null, "type": 0, "flags": 0, "position": 27}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 29}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "INT", "value": "INT", "keyword": "INT", "type": 1, "flags": 11, "position": 30}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 33}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 34}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "BEGIN", "value": "BEGIN", "keyword": "BEGIN", "type": 1, "flags": 1, "position": 35}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 40}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "label1 :", "value": "label1 :", "keyword": null, "type": 10, "flags": 0, "position": 43}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 51}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LOOP", "value": "LOOP", "keyword": "LOOP", "type": 1, "flags": 3, "position": 52}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n    ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 56}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SET", "value": "SET", "keyword": "SET", "type": 1, "flags": 11, "position": 61}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 64}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "p1", "value": "p1", "keyword": null, "type": 0, "flags": 0, "position": 65}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 67}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 68}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 69}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "p1", "value": "p1", "keyword": null, "type": 0, "flags": 0, "position": 70}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 72}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "+", "value": "+", "keyword": null, "type": 2, "flags": 1, "position": 73}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 74}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 75}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 76}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n    ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 77}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "IF", "value": "IF", "keyword": "IF", "type": 1, "flags": 35, "position": 82}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 84}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "p1", "value": "p1", "keyword": null, "type": 0, "flags": 0, "position": 85}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 87}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "<", "value": "<", "keyword": null, "type": 2, "flags": 2, "position": 88}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 89}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "10", "value": 10, "keyword": null, "type": 6, "flags": 0, "position": 90}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 92}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "THEN", "value": "THEN", "keyword": "THEN", "type": 1, "flags": 3, "position": 93}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n      ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 97}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ITERATE", "value": "ITERATE", "keyword": "ITERATE", "type": 1, "flags": 3, "position": 104}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 111}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "label1", "value": "label1", "keyword": null, "type": 0, "flags": 0, "position": 112}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 118}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n    ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 119}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "END", "value": "END", "keyword": "END", "type": 1, "flags": 1, "position": 124}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 127}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "IF", "value": "IF", "keyword": "IF", "type": 1, "flags": 35, "position": 128}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 130}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n    ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 131}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEAVE", "value": "LEAVE", "keyword": "LEAVE", "type": 1, "flags": 3, "position": 136}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 141}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "label1", "value": "label1", "keyword": null, "type": 0, "flags": 0, "position": 142}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 148}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 149}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "END", "value": "END", "keyword": "END", "type": 1, "flags": 1, "position": 152}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 155}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LOOP", "value": "LOOP", "keyword": "LOOP", "type": 1, "flags": 3, "position": 156}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 160}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "label1", "value": "label1", "keyword": null, "type": 0, "flags": 0, "position": 161}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 167}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 168}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SET", "value": "SET", "keyword": "SET", "type": 1, "flags": 11, "position": 171}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 174}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "@x", "value": "x", "keyword": null, "type": 8, "flags": 1, "position": 175}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 177}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 178}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 179}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "p1", "value": "p1", "keyword": null, "type": 0, "flags": 0, "position": 180}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 182}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 183}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "END", "value": "END", "keyword": "END", "type": 1, "flags": 1, "position": 184}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": null, "value": null, "keyword": null, "type": 9, "flags": 0, "position": null}], "count": 73, "idx": 0}, "DEFAULT_DELIMITER": ";", "delimiter": ";", "delimiterLen": 1, "strict": false, "errors": []}, "parser": null, "errors": {"lexer": [], "parser": []}}