{"query": "SET @idx := 1;\nSELECT @idx, @`idx`, @'idx', @@hostname", "lexer": {"@type": "PhpMyAdmin\\SqlParser\\Lexer", "PARSER_METHODS": ["parseDelimiter", "parseWhitespace", "parseNumber", "parseComment", "parseOperator", "parseBool", "parseString", "parseSymbol", "parseKeyword", "parseLabel", "parseUnknown"], "KEYWORD_NAME_INDICATORS": ["FROM", "SET", "WHERE"], "OPERATOR_NAME_INDICATORS": [",", "."], "str": "SET @idx := 1;\nSELECT @idx, @`idx`, @'idx', @@hostname", "len": 54, "last": 54, "list": {"@type": "PhpMyAdmin\\SqlParser\\TokensList", "tokens": [{"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SET", "value": "SET", "keyword": "SET", "type": 1, "flags": 11, "position": 0}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "@idx", "value": "idx", "keyword": null, "type": 8, "flags": 1, "position": 4}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 8}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ":=", "value": ":=", "keyword": null, "type": 2, "flags": 8, "position": 9}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 11}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 12}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 13}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 14}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 15}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 21}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "@idx", "value": "idx", "keyword": null, "type": 8, "flags": 1, "position": 22}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 26}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 27}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "@`idx`", "value": "idx", "keyword": null, "type": 8, "flags": 1, "position": 28}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 34}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 35}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "@'idx'", "value": "idx", "keyword": null, "type": 8, "flags": 1, "position": 36}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 42}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 43}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "@@hostname", "value": "hostname", "keyword": null, "type": 8, "flags": 9, "position": 44}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": null, "value": null, "keyword": null, "type": 9, "flags": 0, "position": null}], "count": 22, "idx": 0}, "DEFAULT_DELIMITER": ";", "delimiter": ";", "delimiterLen": 1, "strict": false, "errors": []}, "parser": null, "errors": {"lexer": [], "parser": []}}