{"query": "DELIMTER //\nSELECT a,b FROM test //\nSELECT * FROM bar //", "lexer": {"@type": "PhpMyAdmin\\SqlParser\\Lexer", "PARSER_METHODS": ["parseDelimiter", "parseWhitespace", "parseNumber", "parseComment", "parseOperator", "parseBool", "parseString", "parseSymbol", "parseKeyword", "parseLabel", "parseUnknown"], "KEYWORD_NAME_INDICATORS": ["FROM", "SET", "WHERE"], "OPERATOR_NAME_INDICATORS": [",", "."], "str": "DELIMTER //\nSELECT a,b FROM test //\nSELECT * FROM bar //", "len": 56, "last": 56, "list": {"@type": "PhpMyAdmin\\SqlParser\\TokensList", "tokens": [{"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DELIMTER", "value": "DELIMTER", "keyword": null, "type": 0, "flags": 0, "position": 0}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 8}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/", "value": "/", "keyword": null, "type": 2, "flags": 1, "position": 9}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/", "value": "/", "keyword": null, "type": 2, "flags": 1, "position": 10}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 11}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 12}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 18}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 19}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 20}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "b", "value": "b", "keyword": null, "type": 0, "flags": 0, "position": 21}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 22}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 23}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 27}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "test", "value": "test", "keyword": null, "type": 0, "flags": 0, "position": 28}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 32}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/", "value": "/", "keyword": null, "type": 2, "flags": 1, "position": 33}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/", "value": "/", "keyword": null, "type": 2, "flags": 1, "position": 34}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 35}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 36}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 42}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 43}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 44}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 45}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 49}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "bar", "value": "bar", "keyword": null, "type": 0, "flags": 0, "position": 50}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 53}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/", "value": "/", "keyword": null, "type": 2, "flags": 1, "position": 54}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/", "value": "/", "keyword": null, "type": 2, "flags": 1, "position": 55}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": null, "value": null, "keyword": null, "type": 9, "flags": 0, "position": null}], "count": 29, "idx": 0}, "DEFAULT_DELIMITER": ";", "delimiter": ";", "delimiterLen": 1, "strict": false, "errors": []}, "parser": null, "errors": {"lexer": [], "parser": []}}