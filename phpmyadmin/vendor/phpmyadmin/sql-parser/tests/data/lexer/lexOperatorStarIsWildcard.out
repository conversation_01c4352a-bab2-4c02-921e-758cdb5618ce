{"query": "SELECT * FROM\nSELECT *FROM\nSELECT a.* FROM\nSELECT a.*,b.* FROM\nSELECT a.*, b.* FROM\nSELECT a.*, /* with a comment */ b.* FROM\nSELECT a.*,/* with a comment */b.* FROM\nSELECT a.* /* comment */ FROM\n-- SELECT a.*/* comment */ FROM (This one is not working yet because of https://github.com/phpmyadmin/sql-parser/issues/285. Please uncomment when this issue is fixed.)\nSELECT DISTINCT * FROM\nSELECT DISTINCT *FROM\nSELECT DISTINCT a.* FROM\nSELECT DISTINCT a.*,b.* FROM\nSELECT DISTINCT a.*, b.* FROM\nSELECT DISTINCT a.*, /* with a comment */ b.* FROM\nSELECT DISTINCT a.*,/* with a comment */b.* FROM\nSELECT DISTINCT a.* /* comment */ FROM\n-- SELECT DISTINCT a.*/* comment */ FROM (This one is not working yet because of https://github.com/phpmyadmin/sql-parser/issues/285. Please uncomment when this issue is fixed.)\nSELECT `*` FROM table_name\nSELECT `*`.* FROM table_name AS `*`\nSELECT COUNT(*) FROM table_name\nSELECT COUNT( * ) FROM table_name\nSELECT COUNT( * /* comment with *,USING,FROM */) FROM table_name\nSELECT COUNT(`*`) FROM table_name\nSELECT 1 FROM table_name WHERE LABEL LIKE '%*%'\nDELETE a.* USING\nDELETE a.*, b.* USING\nDELETE a.* ,b.* USING\nDELETE a.* , b.* USING\nDELETE a.* /* comment */ USING\nDELETE a.* /* comment */, b.* /*comment*/ USING\nDELETE a.* /* comment */ ,b.* /*comment*/ USING\nDELETE a.* /* comment */ , b.* /*comment*/ USING\n\n", "lexer": {"@type": "PhpMyAdmin\\SqlParser\\Lexer", "PARSER_METHODS": ["parseDelimiter", "parseWhitespace", "parseNumber", "parseComment", "parseOperator", "parseBool", "parseString", "parseSymbol", "parseKeyword", "parseLabel", "parseUnknown"], "KEYWORD_NAME_INDICATORS": ["FROM", "SET", "WHERE"], "OPERATOR_NAME_INDICATORS": [",", "."], "str": "SELECT * FROM\nSELECT *FROM\nSELECT a.* FROM\nSELECT a.*,b.* FROM\nSELECT a.*, b.* FROM\nSELECT a.*, /* with a comment */ b.* FROM\nSELECT a.*,/* with a comment */b.* FROM\nSELECT a.* /* comment */ FROM\n-- SELECT a.*/* comment */ FROM (This one is not working yet because of https://github.com/phpmyadmin/sql-parser/issues/285. Please uncomment when this issue is fixed.)\nSELECT DISTINCT * FROM\nSELECT DISTINCT *FROM\nSELECT DISTINCT a.* FROM\nSELECT DISTINCT a.*,b.* FROM\nSELECT DISTINCT a.*, b.* FROM\nSELECT DISTINCT a.*, /* with a comment */ b.* FROM\nSELECT DISTINCT a.*,/* with a comment */b.* FROM\nSELECT DISTINCT a.* /* comment */ FROM\n-- SELECT DISTINCT a.*/* comment */ FROM (This one is not working yet because of https://github.com/phpmyadmin/sql-parser/issues/285. Please uncomment when this issue is fixed.)\nSELECT `*` FROM table_name\nSELECT `*`.* FROM table_name AS `*`\nSELECT COUNT(*) FROM table_name\nSELECT COUNT( * ) FROM table_name\nSELECT COUNT( * /* comment with *,USING,FROM */) FROM table_name\nSELECT COUNT(`*`) FROM table_name\nSELECT 1 FROM table_name WHERE LABEL LIKE '%*%'\nDELETE a.* USING\nDELETE a.*, b.* USING\nDELETE a.* ,b.* USING\nDELETE a.* , b.* USING\nDELETE a.* /* comment */ USING\nDELETE a.* /* comment */, b.* /*comment*/ USING\nDELETE a.* /* comment */ ,b.* /*comment*/ USING\nDELETE a.* /* comment */ , b.* /*comment*/ USING\n\n", "len": 1348, "last": 1348, "list": {"@type": "PhpMyAdmin\\SqlParser\\TokensList", "tokens": [{"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 0}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 6}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 7}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 8}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 9}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 13}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 14}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 20}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 21}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 22}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 26}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 27}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 33}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 34}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 35}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 36}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 37}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 38}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 42}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 43}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 49}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 50}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 51}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 52}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 53}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "b", "value": "b", "keyword": null, "type": 0, "flags": 0, "position": 54}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 55}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 56}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 57}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 58}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 62}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 63}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 69}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 70}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 71}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 72}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 73}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 74}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "b", "value": "b", "keyword": null, "type": 0, "flags": 0, "position": 75}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 76}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 77}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 78}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 79}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 83}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 84}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 90}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 91}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 92}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 93}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 94}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 95}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* with a comment */", "value": "/* with a comment */", "keyword": null, "type": 4, "flags": 2, "position": 96}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 116}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "b", "value": "b", "keyword": null, "type": 0, "flags": 0, "position": 117}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 118}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 119}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 120}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 121}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 125}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 126}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 132}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 133}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 134}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 135}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 136}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* with a comment */", "value": "/* with a comment */", "keyword": null, "type": 4, "flags": 2, "position": 137}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "b", "value": "b", "keyword": null, "type": 0, "flags": 0, "position": 157}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 158}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 159}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 160}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 161}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 165}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 166}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 172}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 173}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 174}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 175}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 176}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 177}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 190}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 191}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 195}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-- SELECT a.*/* comment */ FROM (This one is not working yet because of https://github.com/phpmyadmin/sql-parser/issues/285. Please uncomment when this issue is fixed.)", "value": "-- SELECT a.*/* comment */ FROM (This one is not working yet because of https://github.com/phpmyadmin/sql-parser/issues/285. Please uncomment when this issue is fixed.)", "keyword": null, "type": 4, "flags": 4, "position": 196}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 364}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 365}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 371}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DISTINCT", "value": "DISTINCT", "keyword": "DISTINCT", "type": 1, "flags": 3, "position": 372}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 380}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 381}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 382}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 383}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 387}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 388}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 394}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DISTINCT", "value": "DISTINCT", "keyword": "DISTINCT", "type": 1, "flags": 3, "position": 395}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 403}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 404}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 405}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 409}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 410}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 416}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DISTINCT", "value": "DISTINCT", "keyword": "DISTINCT", "type": 1, "flags": 3, "position": 417}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 425}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 426}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 427}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 428}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 429}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 430}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 434}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 435}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 441}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DISTINCT", "value": "DISTINCT", "keyword": "DISTINCT", "type": 1, "flags": 3, "position": 442}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 450}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 451}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 452}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 453}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 454}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "b", "value": "b", "keyword": null, "type": 0, "flags": 0, "position": 455}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 456}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 457}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 458}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 459}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 463}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 464}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 470}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DISTINCT", "value": "DISTINCT", "keyword": "DISTINCT", "type": 1, "flags": 3, "position": 471}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 479}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 480}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 481}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 482}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 483}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 484}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "b", "value": "b", "keyword": null, "type": 0, "flags": 0, "position": 485}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 486}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 487}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 488}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 489}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 493}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 494}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 500}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DISTINCT", "value": "DISTINCT", "keyword": "DISTINCT", "type": 1, "flags": 3, "position": 501}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 509}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 510}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 511}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 512}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 513}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 514}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* with a comment */", "value": "/* with a comment */", "keyword": null, "type": 4, "flags": 2, "position": 515}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 535}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "b", "value": "b", "keyword": null, "type": 0, "flags": 0, "position": 536}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 537}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 538}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 539}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 540}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 544}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 545}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 551}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DISTINCT", "value": "DISTINCT", "keyword": "DISTINCT", "type": 1, "flags": 3, "position": 552}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 560}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 561}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 562}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 563}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 564}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* with a comment */", "value": "/* with a comment */", "keyword": null, "type": 4, "flags": 2, "position": 565}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "b", "value": "b", "keyword": null, "type": 0, "flags": 0, "position": 585}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 586}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 587}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 588}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 589}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 593}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 594}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 600}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DISTINCT", "value": "DISTINCT", "keyword": "DISTINCT", "type": 1, "flags": 3, "position": 601}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 609}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 610}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 611}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 612}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 613}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 614}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 627}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 628}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 632}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "-- SELECT DISTINCT a.*/* comment */ FROM (This one is not working yet because of https://github.com/phpmyadmin/sql-parser/issues/285. Please uncomment when this issue is fixed.)", "value": "-- SELECT DISTINCT a.*/* comment */ FROM (This one is not working yet because of https://github.com/phpmyadmin/sql-parser/issues/285. Please uncomment when this issue is fixed.)", "keyword": null, "type": 4, "flags": 4, "position": 633}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 810}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 811}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 817}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`*`", "value": "*", "keyword": null, "type": 8, "flags": 2, "position": 818}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 821}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 822}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 826}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "table_name", "value": "table_name", "keyword": "TABLE_NAME", "type": 1, "flags": 1, "position": 827}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 837}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 838}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 844}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`*`", "value": "*", "keyword": null, "type": 8, "flags": 2, "position": 845}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 848}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 849}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 850}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 851}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 855}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "table_name", "value": "table_name", "keyword": "TABLE_NAME", "type": 1, "flags": 1, "position": 856}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 866}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 867}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 869}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`*`", "value": "*", "keyword": null, "type": 8, "flags": 2, "position": 870}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 873}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 874}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 880}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "COUNT", "value": "COUNT", "keyword": "COUNT", "type": 1, "flags": 33, "position": 881}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 886}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 887}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 888}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 889}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 890}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 894}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "table_name", "value": "table_name", "keyword": "TABLE_NAME", "type": 1, "flags": 1, "position": 895}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 905}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 906}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 912}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "COUNT", "value": "COUNT", "keyword": "COUNT", "type": 1, "flags": 33, "position": 913}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 918}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 919}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 920}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 921}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 922}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 923}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 924}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 928}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "table_name", "value": "table_name", "keyword": "TABLE_NAME", "type": 1, "flags": 1, "position": 929}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 939}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 940}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 946}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "COUNT", "value": "COUNT", "keyword": "COUNT", "type": 1, "flags": 33, "position": 947}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 952}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 953}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 954}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 955}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment with *,USING,FROM */", "value": "/* comment with *,USING,FROM */", "keyword": null, "type": 4, "flags": 2, "position": 956}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 987}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 988}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 989}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 993}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "table_name", "value": "table_name", "keyword": "TABLE_NAME", "type": 1, "flags": 1, "position": 994}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1004}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1005}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1011}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "COUNT", "value": "COUNT", "keyword": "COUNT", "type": 1, "flags": 33, "position": 1012}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1017}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`*`", "value": "*", "keyword": null, "type": 8, "flags": 2, "position": 1018}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 1021}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1022}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 1023}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1027}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "table_name", "value": "table_name", "keyword": "TABLE_NAME", "type": 1, "flags": 1, "position": 1028}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1038}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1039}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1045}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 1046}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1047}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 1048}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1052}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "table_name", "value": "table_name", "keyword": "TABLE_NAME", "type": 1, "flags": 1, "position": 1053}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1063}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 1064}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1069}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LABEL", "value": "LABEL", "keyword": null, "type": 0, "flags": 0, "position": 1070}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1075}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LIKE", "value": "LIKE", "keyword": "LIKE", "type": 1, "flags": 3, "position": 1076}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1080}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "'%*%'", "value": "%*%", "keyword": null, "type": 7, "flags": 1, "position": 1081}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1086}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DELETE", "value": "DELETE", "keyword": "DELETE", "type": 1, "flags": 3, "position": 1087}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1093}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 1094}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1095}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 1096}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1097}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "USING", "value": "USING", "keyword": "USING", "type": 1, "flags": 3, "position": 1098}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1103}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DELETE", "value": "DELETE", "keyword": "DELETE", "type": 1, "flags": 3, "position": 1104}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1110}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 1111}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1112}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 1113}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1114}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1115}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "b", "value": "b", "keyword": null, "type": 0, "flags": 0, "position": 1116}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1117}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 1118}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1119}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "USING", "value": "USING", "keyword": "USING", "type": 1, "flags": 3, "position": 1120}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1125}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DELETE", "value": "DELETE", "keyword": "DELETE", "type": 1, "flags": 3, "position": 1126}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1132}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 1133}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1134}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 1135}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1136}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1137}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "b", "value": "b", "keyword": null, "type": 0, "flags": 0, "position": 1138}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1139}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 1140}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1141}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "USING", "value": "USING", "keyword": "USING", "type": 1, "flags": 3, "position": 1142}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1147}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DELETE", "value": "DELETE", "keyword": "DELETE", "type": 1, "flags": 3, "position": 1148}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1154}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 1155}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1156}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 1157}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1158}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1159}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1160}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "b", "value": "b", "keyword": null, "type": 0, "flags": 0, "position": 1161}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1162}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 1163}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1164}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "USING", "value": "USING", "keyword": "USING", "type": 1, "flags": 3, "position": 1165}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1170}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DELETE", "value": "DELETE", "keyword": "DELETE", "type": 1, "flags": 3, "position": 1171}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1177}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 1178}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1179}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 1180}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1181}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1182}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1195}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "USING", "value": "USING", "keyword": "USING", "type": 1, "flags": 3, "position": 1196}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1201}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DELETE", "value": "DELETE", "keyword": "DELETE", "type": 1, "flags": 3, "position": 1202}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1208}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 1209}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1210}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 1211}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1212}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1213}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1226}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1227}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "b", "value": "b", "keyword": null, "type": 0, "flags": 0, "position": 1228}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1229}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 1230}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1231}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/*comment*/", "value": "/*comment*/", "keyword": null, "type": 4, "flags": 2, "position": 1232}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1243}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "USING", "value": "USING", "keyword": "USING", "type": 1, "flags": 3, "position": 1244}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1249}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DELETE", "value": "DELETE", "keyword": "DELETE", "type": 1, "flags": 3, "position": 1250}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1256}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 1257}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1258}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 1259}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1260}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1261}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1274}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1275}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "b", "value": "b", "keyword": null, "type": 0, "flags": 0, "position": 1276}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1277}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 1278}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1279}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/*comment*/", "value": "/*comment*/", "keyword": null, "type": 4, "flags": 2, "position": 1280}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1291}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "USING", "value": "USING", "keyword": "USING", "type": 1, "flags": 3, "position": 1292}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1297}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DELETE", "value": "DELETE", "keyword": "DELETE", "type": 1, "flags": 3, "position": 1298}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1304}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 1305}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1306}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 1307}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1308}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 1309}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1322}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1323}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1324}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "b", "value": "b", "keyword": null, "type": 0, "flags": 0, "position": 1325}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1326}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 1327}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1328}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/*comment*/", "value": "/*comment*/", "keyword": null, "type": 4, "flags": 2, "position": 1329}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1340}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "USING", "value": "USING", "keyword": "USING", "type": 1, "flags": 3, "position": 1341}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1346}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": null, "value": null, "keyword": null, "type": 9, "flags": 0, "position": null}], "count": 382, "idx": 0}, "DEFAULT_DELIMITER": ";", "delimiter": ";", "delimiterLen": 1, "strict": false, "errors": []}, "parser": null, "errors": {"lexer": [], "parser": []}}