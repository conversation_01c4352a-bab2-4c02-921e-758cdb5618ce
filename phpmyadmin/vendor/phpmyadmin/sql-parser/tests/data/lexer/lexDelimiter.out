{"query": "DELIMITER GO\nSELECT a,b FROM foo GO\nSELECT * FROM bar", "lexer": {"@type": "PhpMyAdmin\\SqlParser\\Lexer", "PARSER_METHODS": ["parseDelimiter", "parseWhitespace", "parseNumber", "parseComment", "parseOperator", "parseBool", "parseString", "parseSymbol", "parseKeyword", "parseLabel", "parseUnknown"], "KEYWORD_NAME_INDICATORS": ["FROM", "SET", "WHERE"], "OPERATOR_NAME_INDICATORS": [",", "."], "str": "DELIMITER GO\nSELECT a,b FROM foo GO\nSELECT * FROM bar", "len": 53, "last": 53, "list": {"@type": "PhpMyAdmin\\SqlParser\\TokensList", "tokens": [{"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DELIMITER", "value": "DELIMITER", "keyword": null, "type": 0, "flags": 0, "position": 0}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 9}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "GO", "value": "GO", "keyword": null, "type": 9, "flags": 0, "position": 10}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 12}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 13}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 19}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "a", "value": "a", "keyword": null, "type": 0, "flags": 0, "position": 20}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 21}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "b", "value": "b", "keyword": null, "type": 0, "flags": 0, "position": 22}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 23}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 24}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 28}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "foo", "value": "foo", "keyword": null, "type": 0, "flags": 0, "position": 29}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 32}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "GO", "value": "GO", "keyword": null, "type": 9, "flags": 0, "position": 33}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 35}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 36}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 42}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 43}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 44}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 45}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 49}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "bar", "value": "bar", "keyword": null, "type": 0, "flags": 0, "position": 50}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": null, "value": null, "keyword": null, "type": 9, "flags": 0, "position": null}], "count": 24, "idx": 0}, "DEFAULT_DELIMITER": ";", "delimiter": "GO", "delimiterLen": 2, "strict": false, "errors": []}, "parser": null, "errors": {"lexer": [], "parser": []}}