{"query": "CREATE VIEW  `employees_view` AS\nSELECT *\nFROM\n    `employees`\nWHERE\n    `employees`.`gender` = 'M'\nUNION\nSELECT *\nFROM\n    `employees`\nWHERE\n    `employees`.`gender` = 'F';", "lexer": {"@type": "PhpMyAdmin\\SqlParser\\Lexer", "PARSER_METHODS": ["parseDelimiter", "parseWhitespace", "parseNumber", "parseComment", "parseOperator", "parseBool", "parseString", "parseSymbol", "parseKeyword", "parseLabel", "parseUnknown"], "KEYWORD_NAME_INDICATORS": ["FROM", "SET", "WHERE"], "OPERATOR_NAME_INDICATORS": [",", "."], "str": "CREATE VIEW  `employees_view` AS\nSELECT *\nFROM\n    `employees`\nWHERE\n    `employees`.`gender` = 'M'\nUNION\nSELECT *\nFROM\n    `employees`\nWHERE\n    `employees`.`gender` = 'F';", "len": 173, "last": 173, "list": {"@type": "PhpMyAdmin\\SqlParser\\TokensList", "tokens": [{"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CREATE", "value": "CREATE", "keyword": "CREATE", "type": 1, "flags": 3, "position": 0}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 6}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "VIEW", "value": "VIEW", "keyword": "VIEW", "type": 1, "flags": 1, "position": 7}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 11}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`employees_view`", "value": "employees_view", "keyword": null, "type": 8, "flags": 2, "position": 13}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 29}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 30}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 32}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 33}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 39}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 40}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 41}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 42}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n    ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 46}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`employees`", "value": "employees", "keyword": null, "type": 8, "flags": 2, "position": 51}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 62}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 63}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n    ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 68}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`employees`", "value": "employees", "keyword": null, "type": 8, "flags": 2, "position": 73}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 84}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`gender`", "value": "gender", "keyword": null, "type": 8, "flags": 2, "position": 85}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 93}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 94}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 95}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "'M'", "value": "M", "keyword": null, "type": 7, "flags": 1, "position": 96}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 99}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "UNION", "value": "UNION", "keyword": "UNION", "type": 1, "flags": 3, "position": 100}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 105}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 106}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 112}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 113}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 114}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 115}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n    ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 119}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`employees`", "value": "employees", "keyword": null, "type": 8, "flags": 2, "position": 124}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 135}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 136}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n    ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 141}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`employees`", "value": "employees", "keyword": null, "type": 8, "flags": 2, "position": 146}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 157}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`gender`", "value": "gender", "keyword": null, "type": 8, "flags": 2, "position": 158}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 166}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 167}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 168}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "'F'", "value": "F", "keyword": null, "type": 7, "flags": 1, "position": 169}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 172}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": null, "value": null, "keyword": null, "type": 9, "flags": 0, "position": null}], "count": 47, "idx": 47}, "DEFAULT_DELIMITER": ";", "delimiter": ";", "delimiterLen": 1, "strict": false, "errors": []}, "parser": {"@type": "PhpMyAdmin\\SqlParser\\Parser", "STATEMENT_PARSERS": {"DESCRIBE": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "DESC": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "EXPLAIN": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "FLUSH": "", "GRANT": "", "HELP": "", "SET PASSWORD": "", "STATUS": "", "USE": "", "ANALYZE": "PhpMyAdmin\\SqlParser\\Statements\\AnalyzeStatement", "BACKUP": "PhpMyAdmin\\SqlParser\\Statements\\BackupStatement", "CHECK": "PhpMyAdmin\\SqlParser\\Statements\\CheckStatement", "CHECKSUM": "PhpMyAdmin\\SqlParser\\Statements\\ChecksumStatement", "OPTIMIZE": "PhpMyAdmin\\SqlParser\\Statements\\OptimizeStatement", "REPAIR": "PhpMyAdmin\\SqlParser\\Statements\\RepairStatement", "RESTORE": "PhpMyAdmin\\SqlParser\\Statements\\RestoreStatement", "SET": "PhpMyAdmin\\SqlParser\\Statements\\SetStatement", "SHOW": "PhpMyAdmin\\SqlParser\\Statements\\ShowStatement", "ALTER": "PhpMyAdmin\\SqlParser\\Statements\\AlterStatement", "CREATE": "PhpMyAdmin\\SqlParser\\Statements\\CreateStatement", "DROP": "PhpMyAdmin\\SqlParser\\Statements\\DropStatement", "RENAME": "PhpMyAdmin\\SqlParser\\Statements\\RenameStatement", "TRUNCATE": "PhpMyAdmin\\SqlParser\\Statements\\TruncateStatement", "CALL": "PhpMyAdmin\\SqlParser\\Statements\\CallStatement", "DELETE": "PhpMyAdmin\\SqlParser\\Statements\\DeleteStatement", "DO": "", "HANDLER": "", "INSERT": "PhpMyAdmin\\SqlParser\\Statements\\InsertStatement", "LOAD DATA": "PhpMyAdmin\\SqlParser\\Statements\\LoadStatement", "REPLACE": "PhpMyAdmin\\SqlParser\\Statements\\ReplaceStatement", "SELECT": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "UPDATE": "PhpMyAdmin\\SqlParser\\Statements\\UpdateStatement", "WITH": "PhpMyAdmin\\SqlParser\\Statements\\WithStatement", "DEALLOCATE": "", "EXECUTE": "", "PREPARE": "", "BEGIN": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "COMMIT": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "ROLLBACK": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "START TRANSACTION": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "PURGE": "PhpMyAdmin\\SqlParser\\Statements\\PurgeStatement", "LOCK": "PhpMyAdmin\\SqlParser\\Statements\\LockStatement", "UNLOCK": "PhpMyAdmin\\SqlParser\\Statements\\LockStatement"}, "KEYWORD_PARSERS": {"PARTITION BY": [], "SUBPARTITION BY": [], "_OPTIONS": {"class": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "field": "options"}, "_END_OPTIONS": {"class": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "field": "end_options"}, "INTERSECT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "EXCEPT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION ALL": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION DISTINCT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "ALTER": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "ANALYZE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "BACKUP": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CALL": {"class": "PhpMyAdmin\\SqlParser\\Components\\FunctionCall", "field": "call"}, "CHECK": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CHECKSUM": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CROSS JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "DROP": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "fields", "options": {"parseField": "table"}}, "FORCE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "FROM": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "from", "options": {"field": "table"}}, "GROUP BY": {"class": "PhpMyAdmin\\SqlParser\\Components\\GroupKeyword", "field": "group"}, "HAVING": {"class": "PhpMyAdmin\\SqlParser\\Components\\Condition", "field": "having"}, "IGNORE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "INTO": {"class": "PhpMyAdmin\\SqlParser\\Components\\IntoKeyword", "field": "into"}, "JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LEFT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LEFT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "ON": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "RIGHT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "RIGHT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "INNER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "FULL JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "FULL OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL LEFT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL RIGHT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL LEFT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL RIGHT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "STRAIGHT_JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LIMIT": {"class": "PhpMyAdmin\\SqlParser\\Components\\Limit", "field": "limit"}, "OPTIMIZE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "ORDER BY": {"class": "PhpMyAdmin\\SqlParser\\Components\\OrderKeyword", "field": "order"}, "PARTITION": {"class": "PhpMyAdmin\\SqlParser\\Components\\ArrayObj", "field": "partition"}, "PROCEDURE": {"class": "PhpMyAdmin\\SqlParser\\Components\\FunctionCall", "field": "procedure"}, "RENAME": {"class": "PhpMyAdmin\\SqlParser\\Components\\RenameOperation", "field": "renames"}, "REPAIR": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "RESTORE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "SET": {"class": "PhpMyAdmin\\SqlParser\\Components\\SetOperation", "field": "set"}, "SELECT": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "expr"}, "TRUNCATE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "UPDATE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "USE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "VALUE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Array2d", "field": "values"}, "VALUES": {"class": "PhpMyAdmin\\SqlParser\\Components\\Array2d", "field": "values"}, "WHERE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Condition", "field": "where"}}, "list": {"@type": "@1"}, "statements": [{"@type": "PhpMyAdmin\\SqlParser\\Statements\\CreateStatement", "OPTIONS": {"TEMPORARY": 1, "OR REPLACE": 2, "ALGORITHM": [3, "var="], "DEFINER": [4, "expr="], "SQL SECURITY": [5, "var"], "DATABASE": 6, "EVENT": 6, "FUNCTION": 6, "INDEX": 6, "UNIQUE INDEX": 6, "FULLTEXT INDEX": 6, "SPATIAL INDEX": 6, "PROCEDURE": 6, "SERVER": 6, "TABLE": 6, "TABLESPACE": 6, "TRIGGER": 6, "USER": 6, "VIEW": 6, "SCHEMA": 6, "IF NOT EXISTS": 7}, "DB_OPTIONS": {"CHARACTER SET": [1, "var="], "CHARSET": [1, "var="], "DEFAULT CHARACTER SET": [1, "var="], "DEFAULT CHARSET": [1, "var="], "DEFAULT COLLATE": [2, "var="], "COLLATE": [2, "var="]}, "TABLE_OPTIONS": {"ENGINE": [1, "var="], "AUTO_INCREMENT": [2, "var="], "AVG_ROW_LENGTH": [3, "var"], "CHARACTER SET": [4, "var="], "CHARSET": [4, "var="], "DEFAULT CHARACTER SET": [4, "var="], "DEFAULT CHARSET": [4, "var="], "CHECKSUM": [5, "var"], "DEFAULT COLLATE": [6, "var="], "COLLATE": [6, "var="], "COMMENT": [7, "var="], "CONNECTION": [8, "var"], "DATA DIRECTORY": [9, "var"], "DELAY_KEY_WRITE": [10, "var"], "INDEX DIRECTORY": [11, "var"], "INSERT_METHOD": [12, "var"], "KEY_BLOCK_SIZE": [13, "var"], "MAX_ROWS": [14, "var"], "MIN_ROWS": [15, "var"], "PACK_KEYS": [16, "var"], "PASSWORD": [17, "var"], "ROW_FORMAT": [18, "var"], "TABLESPACE": [19, "var"], "STORAGE": [20, "var"], "UNION": [21, "var"], "PAGE_COMPRESSED": [22, "var"], "PAGE_COMPRESSION_LEVEL": [23, "var"]}, "FUNC_OPTIONS": {"NOT": [2, "var"], "FUNCTION": [3, "var="], "PROCEDURE": [3, "var="], "CONTAINS SQL": 4, "NO SQL": 4, "READS SQL DATA": 4, "MODIFIES SQL DATA": 4, "SQL SECURITY": [6, "var"], "LANGUAGE": [7, "var"], "COMMENT": [8, "var"], "CREATE": 1, "DETERMINISTIC": 2}, "TRIGGER_OPTIONS": {"BEFORE": 1, "AFTER": 1, "INSERT": 2, "UPDATE": 2, "DELETE": 2}, "name": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "employees_view", "column": null, "expr": "`employees_view`", "alias": null, "function": null, "subquery": null}, "entityOptions": null, "fields": null, "with": null, "select": {"@type": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "OPTIONS": {"ALL": 1, "DISTINCT": 1, "DISTINCTROW": 1, "HIGH_PRIORITY": 2, "MAX_STATEMENT_TIME": [3, "var="], "STRAIGHT_JOIN": 4, "SQL_SMALL_RESULT": 5, "SQL_BIG_RESULT": 6, "SQL_BUFFER_RESULT": 7, "SQL_CACHE": 8, "SQL_NO_CACHE": 8, "SQL_CALC_FOUND_ROWS": 9}, "END_OPTIONS": {"FOR UPDATE": 1, "LOCK IN SHARE MODE": 1}, "CLAUSES": {"SELECT": ["SELECT", 2], "_OPTIONS": ["_OPTIONS", 1], "_SELECT": ["SELECT", 1], "INTO": ["INTO", 3], "FROM": ["FROM", 3], "FORCE": ["FORCE", 1], "USE": ["USE", 1], "IGNORE": ["IGNORE", 3], "PARTITION": ["PARTITION", 3], "JOIN": ["JOIN", 1], "FULL JOIN": ["FULL JOIN", 1], "INNER JOIN": ["INNER JOIN", 1], "LEFT JOIN": ["LEFT JOIN", 1], "LEFT OUTER JOIN": ["LEFT OUTER JOIN", 1], "RIGHT JOIN": ["RIGHT JOIN", 1], "RIGHT OUTER JOIN": ["RIGHT OUTER JOIN", 1], "NATURAL JOIN": ["NATURAL JOIN", 1], "NATURAL LEFT JOIN": ["NATURAL LEFT JOIN", 1], "NATURAL RIGHT JOIN": ["NATURAL RIGHT JOIN", 1], "NATURAL LEFT OUTER JOIN": ["NATURAL LEFT OUTER JOIN", 1], "NATURAL RIGHT OUTER JOIN": ["NATURAL RIGHT JOIN", 1], "WHERE": ["WHERE", 3], "GROUP BY": ["GROUP BY", 3], "HAVING": ["HAVING", 3], "ORDER BY": ["ORDER BY", 3], "LIMIT": ["LIMIT", 3], "PROCEDURE": ["PROCEDURE", 3], "UNION": ["UNION", 1], "EXCEPT": ["EXCEPT", 1], "INTERSECT": ["INTERSECT", 1], "_END_OPTIONS": ["_END_OPTIONS", 1]}, "expr": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "*", "alias": null, "function": null, "subquery": null}], "from": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "employees", "column": null, "expr": "`employees`", "alias": null, "function": null, "subquery": null}], "index_hints": null, "partition": null, "where": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["employees", "gender", "M"], "isOperator": false, "expr": "`employees`.`gender` = 'M'"}], "group": null, "having": null, "order": null, "limit": null, "procedure": null, "into": null, "join": null, "union": [], "end_options": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "first": 8, "last": 25}, "like": null, "partitionBy": null, "partitionsNum": null, "subpartitionBy": null, "subpartitionsNum": null, "partitions": null, "table": null, "return": null, "parameters": null, "body": [{"@type": "@28"}, {"@type": "@29"}, {"@type": "@30"}, {"@type": "@31"}, {"@type": "@32"}, {"@type": "@33"}, {"@type": "@34"}, {"@type": "@35"}, {"@type": "@36"}, {"@type": "@37"}, {"@type": "@38"}, {"@type": "@39"}, {"@type": "@40"}, {"@type": "@41"}, {"@type": "@42"}, {"@type": "@43"}, {"@type": "@44"}, {"@type": "@45"}, {"@type": "@46"}], "CLAUSES": [], "END_OPTIONS": [], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"6": "VIEW"}}, "first": 0, "last": 45}], "brackets": 0, "strict": false, "errors": []}, "errors": {"lexer": [], "parser": []}}