{"query": "SELECT * FROM film WHERE film_id = 10 OR film_id >= 20;\nSELECT * FROM film WHERE (film_id < 10) || (film_id > 20);\nSELECT * FROM film WHERE `film_id` != 10 AND `film_id` <= 20;\nSELECT * FROM film WHERE `film`.`film_id` <> 10 && `film`.`film_id` <= 20;\nSELECT * FROM film WHERE film.film_id < 20 XOR film.rating = 'PG-13';\nSELECT * FROM film WHERE /* film_id = */ film_id = 10;\nSELECT * FROM film WHERE NOT film_id > 10;\nSELECT * FROM film WHERE ! (film_id > 10);\nSELECT * FROM film WHERE description IS NULL;\nSELECT * FROM film WHERE description IS NOT NULL;\nSELECT * FROM film WHERE film_id BETWEEN 10 AND 20;\nSELECT * FROM film WHERE film_id NOT BETWEEN 10 AND 20;\nSELECT * FROM film WHERE film_id IN (3,5,7);\nSELECT * FROM film WHERE rating = UPPER('pg');\nSELECT * FROM film WHERE rating SOUNDS LIKE 'PG';", "lexer": {"@type": "PhpMyAdmin\\SqlParser\\Lexer", "PARSER_METHODS": ["parseDelimiter", "parseWhitespace", "parseNumber", "parseComment", "parseOperator", "parseBool", "parseString", "parseSymbol", "parseKeyword", "parseLabel", "parseUnknown"], "KEYWORD_NAME_INDICATORS": ["FROM", "SET", "WHERE"], "OPERATOR_NAME_INDICATORS": [",", "."], "str": "SELECT * FROM film WHERE film_id = 10 OR film_id >= 20;\nSELECT * FROM film WHERE (film_id < 10) || (film_id > 20);\nSELECT * FROM film WHERE `film_id` != 10 AND `film_id` <= 20;\nSELECT * FROM film WHERE `film`.`film_id` <> 10 && `film`.`film_id` <= 20;\nSELECT * FROM film WHERE film.film_id < 20 XOR film.rating = 'PG-13';\nSELECT * FROM film WHERE /* film_id = */ film_id = 10;\nSELECT * FROM film WHERE NOT film_id > 10;\nSELECT * FROM film WHERE ! (film_id > 10);\nSELECT * FROM film WHERE description IS NULL;\nSELECT * FROM film WHERE description IS NOT NULL;\nSELECT * FROM film WHERE film_id BETWEEN 10 AND 20;\nSELECT * FROM film WHERE film_id NOT BETWEEN 10 AND 20;\nSELECT * FROM film WHERE film_id IN (3,5,7);\nSELECT * FROM film WHERE rating = UPPER('pg');\nSELECT * FROM film WHERE rating SOUNDS LIKE 'PG';", "len": 808, "last": 808, "list": {"@type": "PhpMyAdmin\\SqlParser\\TokensList", "tokens": [{"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 0}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 6}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 7}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 8}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 9}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 13}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film", "value": "film", "keyword": null, "type": 0, "flags": 0, "position": 14}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 18}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 19}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 24}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film_id", "value": "film_id", "keyword": null, "type": 0, "flags": 0, "position": 25}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 32}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 33}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 34}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "10", "value": 10, "keyword": null, "type": 6, "flags": 0, "position": 35}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 37}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "OR", "value": "OR", "keyword": "OR", "type": 1, "flags": 3, "position": 38}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 40}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film_id", "value": "film_id", "keyword": null, "type": 0, "flags": 0, "position": 41}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 48}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ">=", "value": ">=", "keyword": null, "type": 2, "flags": 2, "position": 49}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 51}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "20", "value": 20, "keyword": null, "type": 6, "flags": 0, "position": 52}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 54}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 55}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 56}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 62}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 63}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 64}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 65}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 69}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film", "value": "film", "keyword": null, "type": 0, "flags": 0, "position": 70}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 74}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 75}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 80}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 81}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film_id", "value": "film_id", "keyword": null, "type": 0, "flags": 0, "position": 82}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 89}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "<", "value": "<", "keyword": null, "type": 2, "flags": 2, "position": 90}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 91}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "10", "value": 10, "keyword": null, "type": 6, "flags": 0, "position": 92}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 94}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 95}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "||", "value": "||", "keyword": null, "type": 2, "flags": 2, "position": 96}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 98}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 99}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film_id", "value": "film_id", "keyword": null, "type": 0, "flags": 0, "position": 100}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 107}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ">", "value": ">", "keyword": null, "type": 2, "flags": 2, "position": 108}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 109}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "20", "value": 20, "keyword": null, "type": 6, "flags": 0, "position": 110}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 112}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 113}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 114}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 115}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 121}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 122}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 123}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 124}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 128}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film", "value": "film", "keyword": null, "type": 0, "flags": 0, "position": 129}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 133}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 134}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 139}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`film_id`", "value": "film_id", "keyword": null, "type": 8, "flags": 2, "position": 140}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 149}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "!=", "value": "!=", "keyword": null, "type": 2, "flags": 2, "position": 150}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 152}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "10", "value": 10, "keyword": null, "type": 6, "flags": 0, "position": 153}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 155}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AND", "value": "AND", "keyword": "AND", "type": 1, "flags": 3, "position": 156}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 159}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`film_id`", "value": "film_id", "keyword": null, "type": 8, "flags": 2, "position": 160}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 169}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "<=", "value": "<=", "keyword": null, "type": 2, "flags": 2, "position": 170}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 172}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "20", "value": 20, "keyword": null, "type": 6, "flags": 0, "position": 173}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 175}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 176}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 177}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 183}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 184}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 185}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 186}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 190}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film", "value": "film", "keyword": null, "type": 0, "flags": 0, "position": 191}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 195}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 196}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 201}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`film`", "value": "film", "keyword": null, "type": 8, "flags": 2, "position": 202}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 208}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`film_id`", "value": "film_id", "keyword": null, "type": 8, "flags": 2, "position": 209}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 218}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "<>", "value": "<>", "keyword": null, "type": 2, "flags": 2, "position": 219}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 221}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "10", "value": 10, "keyword": null, "type": 6, "flags": 0, "position": 222}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 224}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "&&", "value": "&&", "keyword": null, "type": 2, "flags": 2, "position": 225}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 227}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`film`", "value": "film", "keyword": null, "type": 8, "flags": 2, "position": 228}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 234}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`film_id`", "value": "film_id", "keyword": null, "type": 8, "flags": 2, "position": 235}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 244}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "<=", "value": "<=", "keyword": null, "type": 2, "flags": 2, "position": 245}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 247}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "20", "value": 20, "keyword": null, "type": 6, "flags": 0, "position": 248}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 250}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 251}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 252}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 258}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 259}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 260}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 261}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 265}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film", "value": "film", "keyword": null, "type": 0, "flags": 0, "position": 266}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 270}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 271}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 276}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film", "value": "film", "keyword": null, "type": 0, "flags": 0, "position": 277}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 281}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film_id", "value": "film_id", "keyword": null, "type": 0, "flags": 0, "position": 282}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 289}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "<", "value": "<", "keyword": null, "type": 2, "flags": 2, "position": 290}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 291}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "20", "value": 20, "keyword": null, "type": 6, "flags": 0, "position": 292}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 294}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "XOR", "value": "XOR", "keyword": "XOR", "type": 1, "flags": 3, "position": 295}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 298}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film", "value": "film", "keyword": null, "type": 0, "flags": 0, "position": 299}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 303}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "rating", "value": "rating", "keyword": null, "type": 0, "flags": 0, "position": 304}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 310}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 311}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 312}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "'PG-13'", "value": "PG-13", "keyword": null, "type": 7, "flags": 1, "position": 313}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 320}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 321}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 322}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 328}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 329}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 330}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 331}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 335}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film", "value": "film", "keyword": null, "type": 0, "flags": 0, "position": 336}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 340}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 341}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 346}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* film_id = */", "value": "/* film_id = */", "keyword": null, "type": 4, "flags": 2, "position": 347}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 362}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film_id", "value": "film_id", "keyword": null, "type": 0, "flags": 0, "position": 363}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 370}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 371}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 372}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "10", "value": 10, "keyword": null, "type": 6, "flags": 0, "position": 373}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 375}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 376}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 377}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 383}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 384}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 385}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 386}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 390}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film", "value": "film", "keyword": null, "type": 0, "flags": 0, "position": 391}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 395}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 396}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 401}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NOT", "value": "NOT", "keyword": "NOT", "type": 1, "flags": 3, "position": 402}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 405}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film_id", "value": "film_id", "keyword": null, "type": 0, "flags": 0, "position": 406}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 413}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ">", "value": ">", "keyword": null, "type": 2, "flags": 2, "position": 414}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 415}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "10", "value": 10, "keyword": null, "type": 6, "flags": 0, "position": 416}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 418}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 419}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 420}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 426}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 427}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 428}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 429}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 433}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film", "value": "film", "keyword": null, "type": 0, "flags": 0, "position": 434}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 438}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 439}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 444}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "!", "value": "!", "keyword": null, "type": 2, "flags": 2, "position": 445}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 446}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 447}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film_id", "value": "film_id", "keyword": null, "type": 0, "flags": 0, "position": 448}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 455}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ">", "value": ">", "keyword": null, "type": 2, "flags": 2, "position": 456}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 457}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "10", "value": 10, "keyword": null, "type": 6, "flags": 0, "position": 458}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 460}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 461}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 462}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 463}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 469}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 470}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 471}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 472}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 476}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film", "value": "film", "keyword": null, "type": 0, "flags": 0, "position": 477}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 481}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 482}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 487}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "description", "value": "description", "keyword": null, "type": 0, "flags": 0, "position": 488}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 499}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "IS", "value": "IS", "keyword": "IS", "type": 1, "flags": 3, "position": 500}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 502}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NULL", "value": "NULL", "keyword": "NULL", "type": 1, "flags": 3, "position": 503}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 507}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 508}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 509}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 515}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 516}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 517}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 518}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 522}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film", "value": "film", "keyword": null, "type": 0, "flags": 0, "position": 523}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 527}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 528}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 533}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "description", "value": "description", "keyword": null, "type": 0, "flags": 0, "position": 534}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 545}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "IS", "value": "IS", "keyword": "IS", "type": 1, "flags": 3, "position": 546}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 548}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NOT NULL", "value": "NOT NULL", "keyword": "NOT NULL", "type": 1, "flags": 7, "position": 549}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 557}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 558}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 559}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 565}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 566}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 567}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 568}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 572}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film", "value": "film", "keyword": null, "type": 0, "flags": 0, "position": 573}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 577}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 578}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 583}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film_id", "value": "film_id", "keyword": null, "type": 0, "flags": 0, "position": 584}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 591}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "BETWEEN", "value": "BETWEEN", "keyword": "BETWEEN", "type": 1, "flags": 3, "position": 592}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 599}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "10", "value": 10, "keyword": null, "type": 6, "flags": 0, "position": 600}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 602}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AND", "value": "AND", "keyword": "AND", "type": 1, "flags": 3, "position": 603}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 606}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "20", "value": 20, "keyword": null, "type": 6, "flags": 0, "position": 607}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 609}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 610}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 611}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 617}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 618}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 619}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 620}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 624}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film", "value": "film", "keyword": null, "type": 0, "flags": 0, "position": 625}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 629}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 630}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 635}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film_id", "value": "film_id", "keyword": null, "type": 0, "flags": 0, "position": 636}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 643}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NOT", "value": "NOT", "keyword": "NOT", "type": 1, "flags": 3, "position": 644}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 647}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "BETWEEN", "value": "BETWEEN", "keyword": "BETWEEN", "type": 1, "flags": 3, "position": 648}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 655}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "10", "value": 10, "keyword": null, "type": 6, "flags": 0, "position": 656}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 658}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AND", "value": "AND", "keyword": "AND", "type": 1, "flags": 3, "position": 659}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 662}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "20", "value": 20, "keyword": null, "type": 6, "flags": 0, "position": 663}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 665}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 666}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 667}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 673}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 674}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 675}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 676}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 680}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film", "value": "film", "keyword": null, "type": 0, "flags": 0, "position": 681}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 685}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 686}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 691}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film_id", "value": "film_id", "keyword": null, "type": 0, "flags": 0, "position": 692}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 699}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "IN", "value": "IN", "keyword": "IN", "type": 1, "flags": 35, "position": 700}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 702}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 703}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3", "value": 3, "keyword": null, "type": 6, "flags": 0, "position": 704}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 705}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "5", "value": 5, "keyword": null, "type": 6, "flags": 0, "position": 706}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 707}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "7", "value": 7, "keyword": null, "type": 6, "flags": 0, "position": 708}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 709}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 710}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 711}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 712}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 718}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 719}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 720}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 721}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 725}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film", "value": "film", "keyword": null, "type": 0, "flags": 0, "position": 726}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 730}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 731}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 736}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "rating", "value": "rating", "keyword": null, "type": 0, "flags": 0, "position": 737}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 743}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 744}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 745}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "UPPER", "value": "UPPER", "keyword": "UPPER", "type": 1, "flags": 33, "position": 746}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 751}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "'pg'", "value": "pg", "keyword": null, "type": 7, "flags": 1, "position": 752}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 756}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 757}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 758}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 759}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 765}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 766}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 767}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 768}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 772}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "film", "value": "film", "keyword": null, "type": 0, "flags": 0, "position": 773}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 777}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 778}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 783}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "rating", "value": "rating", "keyword": null, "type": 0, "flags": 0, "position": 784}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 790}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SOUNDS", "value": "SOUNDS", "keyword": "SOUNDS", "type": 1, "flags": 1, "position": 791}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 797}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LIKE", "value": "LIKE", "keyword": "LIKE", "type": 1, "flags": 3, "position": 798}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 802}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "'PG'", "value": "PG", "keyword": null, "type": 7, "flags": 1, "position": 803}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 807}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": null, "value": null, "keyword": null, "type": 9, "flags": 0, "position": null}], "count": 336, "idx": 336}, "DEFAULT_DELIMITER": ";", "delimiter": ";", "delimiterLen": 1, "strict": false, "errors": []}, "parser": {"@type": "PhpMyAdmin\\SqlParser\\Parser", "STATEMENT_PARSERS": {"DESCRIBE": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "DESC": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "EXPLAIN": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "FLUSH": "", "GRANT": "", "HELP": "", "SET PASSWORD": "", "STATUS": "", "USE": "", "ANALYZE": "PhpMyAdmin\\SqlParser\\Statements\\AnalyzeStatement", "BACKUP": "PhpMyAdmin\\SqlParser\\Statements\\BackupStatement", "CHECK": "PhpMyAdmin\\SqlParser\\Statements\\CheckStatement", "CHECKSUM": "PhpMyAdmin\\SqlParser\\Statements\\ChecksumStatement", "OPTIMIZE": "PhpMyAdmin\\SqlParser\\Statements\\OptimizeStatement", "REPAIR": "PhpMyAdmin\\SqlParser\\Statements\\RepairStatement", "RESTORE": "PhpMyAdmin\\SqlParser\\Statements\\RestoreStatement", "SET": "PhpMyAdmin\\SqlParser\\Statements\\SetStatement", "SHOW": "PhpMyAdmin\\SqlParser\\Statements\\ShowStatement", "ALTER": "PhpMyAdmin\\SqlParser\\Statements\\AlterStatement", "CREATE": "PhpMyAdmin\\SqlParser\\Statements\\CreateStatement", "DROP": "PhpMyAdmin\\SqlParser\\Statements\\DropStatement", "RENAME": "PhpMyAdmin\\SqlParser\\Statements\\RenameStatement", "TRUNCATE": "PhpMyAdmin\\SqlParser\\Statements\\TruncateStatement", "CALL": "PhpMyAdmin\\SqlParser\\Statements\\CallStatement", "DELETE": "PhpMyAdmin\\SqlParser\\Statements\\DeleteStatement", "DO": "", "HANDLER": "", "INSERT": "PhpMyAdmin\\SqlParser\\Statements\\InsertStatement", "LOAD DATA": "PhpMyAdmin\\SqlParser\\Statements\\LoadStatement", "REPLACE": "PhpMyAdmin\\SqlParser\\Statements\\ReplaceStatement", "SELECT": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "UPDATE": "PhpMyAdmin\\SqlParser\\Statements\\UpdateStatement", "WITH": "PhpMyAdmin\\SqlParser\\Statements\\WithStatement", "DEALLOCATE": "", "EXECUTE": "", "PREPARE": "", "BEGIN": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "COMMIT": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "ROLLBACK": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "START TRANSACTION": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "PURGE": "PhpMyAdmin\\SqlParser\\Statements\\PurgeStatement", "LOCK": "PhpMyAdmin\\SqlParser\\Statements\\LockStatement", "UNLOCK": "PhpMyAdmin\\SqlParser\\Statements\\LockStatement"}, "KEYWORD_PARSERS": {"PARTITION BY": [], "SUBPARTITION BY": [], "_OPTIONS": {"class": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "field": "options"}, "_END_OPTIONS": {"class": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "field": "end_options"}, "INTERSECT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "EXCEPT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION ALL": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION DISTINCT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "ALTER": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "ANALYZE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "BACKUP": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CALL": {"class": "PhpMyAdmin\\SqlParser\\Components\\FunctionCall", "field": "call"}, "CHECK": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CHECKSUM": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CROSS JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "DROP": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "fields", "options": {"parseField": "table"}}, "FORCE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "FROM": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "from", "options": {"field": "table"}}, "GROUP BY": {"class": "PhpMyAdmin\\SqlParser\\Components\\GroupKeyword", "field": "group"}, "HAVING": {"class": "PhpMyAdmin\\SqlParser\\Components\\Condition", "field": "having"}, "IGNORE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "INTO": {"class": "PhpMyAdmin\\SqlParser\\Components\\IntoKeyword", "field": "into"}, "JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LEFT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LEFT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "ON": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "RIGHT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "RIGHT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "INNER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "FULL JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "FULL OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL LEFT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL RIGHT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL LEFT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL RIGHT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "STRAIGHT_JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LIMIT": {"class": "PhpMyAdmin\\SqlParser\\Components\\Limit", "field": "limit"}, "OPTIMIZE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "ORDER BY": {"class": "PhpMyAdmin\\SqlParser\\Components\\OrderKeyword", "field": "order"}, "PARTITION": {"class": "PhpMyAdmin\\SqlParser\\Components\\ArrayObj", "field": "partition"}, "PROCEDURE": {"class": "PhpMyAdmin\\SqlParser\\Components\\FunctionCall", "field": "procedure"}, "RENAME": {"class": "PhpMyAdmin\\SqlParser\\Components\\RenameOperation", "field": "renames"}, "REPAIR": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "RESTORE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "SET": {"class": "PhpMyAdmin\\SqlParser\\Components\\SetOperation", "field": "set"}, "SELECT": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "expr"}, "TRUNCATE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "UPDATE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "USE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "VALUE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Array2d", "field": "values"}, "VALUES": {"class": "PhpMyAdmin\\SqlParser\\Components\\Array2d", "field": "values"}, "WHERE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Condition", "field": "where"}}, "list": {"@type": "@1"}, "statements": [{"@type": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "OPTIONS": {"ALL": 1, "DISTINCT": 1, "DISTINCTROW": 1, "HIGH_PRIORITY": 2, "MAX_STATEMENT_TIME": [3, "var="], "STRAIGHT_JOIN": 4, "SQL_SMALL_RESULT": 5, "SQL_BIG_RESULT": 6, "SQL_BUFFER_RESULT": 7, "SQL_CACHE": 8, "SQL_NO_CACHE": 8, "SQL_CALC_FOUND_ROWS": 9}, "END_OPTIONS": {"FOR UPDATE": 1, "LOCK IN SHARE MODE": 1}, "CLAUSES": {"SELECT": ["SELECT", 2], "_OPTIONS": ["_OPTIONS", 1], "_SELECT": ["SELECT", 1], "INTO": ["INTO", 3], "FROM": ["FROM", 3], "FORCE": ["FORCE", 1], "USE": ["USE", 1], "IGNORE": ["IGNORE", 3], "PARTITION": ["PARTITION", 3], "JOIN": ["JOIN", 1], "FULL JOIN": ["FULL JOIN", 1], "INNER JOIN": ["INNER JOIN", 1], "LEFT JOIN": ["LEFT JOIN", 1], "LEFT OUTER JOIN": ["LEFT OUTER JOIN", 1], "RIGHT JOIN": ["RIGHT JOIN", 1], "RIGHT OUTER JOIN": ["RIGHT OUTER JOIN", 1], "NATURAL JOIN": ["NATURAL JOIN", 1], "NATURAL LEFT JOIN": ["NATURAL LEFT JOIN", 1], "NATURAL RIGHT JOIN": ["NATURAL RIGHT JOIN", 1], "NATURAL LEFT OUTER JOIN": ["NATURAL LEFT OUTER JOIN", 1], "NATURAL RIGHT OUTER JOIN": ["NATURAL RIGHT JOIN", 1], "WHERE": ["WHERE", 3], "GROUP BY": ["GROUP BY", 3], "HAVING": ["HAVING", 3], "ORDER BY": ["ORDER BY", 3], "LIMIT": ["LIMIT", 3], "PROCEDURE": ["PROCEDURE", 3], "UNION": ["UNION", 1], "EXCEPT": ["EXCEPT", 1], "INTERSECT": ["INTERSECT", 1], "_END_OPTIONS": ["_END_OPTIONS", 1]}, "expr": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "*", "alias": null, "function": null, "subquery": null}], "from": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "film", "column": null, "expr": "film", "alias": null, "function": null, "subquery": null}], "index_hints": null, "partition": null, "where": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["film_id"], "isOperator": false, "expr": "film_id = 10"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": [], "isOperator": true, "expr": "OR"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["film_id"], "isOperator": false, "expr": "film_id >= 20"}], "group": null, "having": null, "order": null, "limit": null, "procedure": null, "into": null, "join": null, "union": [], "end_options": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "first": 0, "last": 22}, {"@type": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "OPTIONS": {"ALL": 1, "DISTINCT": 1, "DISTINCTROW": 1, "HIGH_PRIORITY": 2, "MAX_STATEMENT_TIME": [3, "var="], "STRAIGHT_JOIN": 4, "SQL_SMALL_RESULT": 5, "SQL_BIG_RESULT": 6, "SQL_BUFFER_RESULT": 7, "SQL_CACHE": 8, "SQL_NO_CACHE": 8, "SQL_CALC_FOUND_ROWS": 9}, "END_OPTIONS": {"FOR UPDATE": 1, "LOCK IN SHARE MODE": 1}, "CLAUSES": {"SELECT": ["SELECT", 2], "_OPTIONS": ["_OPTIONS", 1], "_SELECT": ["SELECT", 1], "INTO": ["INTO", 3], "FROM": ["FROM", 3], "FORCE": ["FORCE", 1], "USE": ["USE", 1], "IGNORE": ["IGNORE", 3], "PARTITION": ["PARTITION", 3], "JOIN": ["JOIN", 1], "FULL JOIN": ["FULL JOIN", 1], "INNER JOIN": ["INNER JOIN", 1], "LEFT JOIN": ["LEFT JOIN", 1], "LEFT OUTER JOIN": ["LEFT OUTER JOIN", 1], "RIGHT JOIN": ["RIGHT JOIN", 1], "RIGHT OUTER JOIN": ["RIGHT OUTER JOIN", 1], "NATURAL JOIN": ["NATURAL JOIN", 1], "NATURAL LEFT JOIN": ["NATURAL LEFT JOIN", 1], "NATURAL RIGHT JOIN": ["NATURAL RIGHT JOIN", 1], "NATURAL LEFT OUTER JOIN": ["NATURAL LEFT OUTER JOIN", 1], "NATURAL RIGHT OUTER JOIN": ["NATURAL RIGHT JOIN", 1], "WHERE": ["WHERE", 3], "GROUP BY": ["GROUP BY", 3], "HAVING": ["HAVING", 3], "ORDER BY": ["ORDER BY", 3], "LIMIT": ["LIMIT", 3], "PROCEDURE": ["PROCEDURE", 3], "UNION": ["UNION", 1], "EXCEPT": ["EXCEPT", 1], "INTERSECT": ["INTERSECT", 1], "_END_OPTIONS": ["_END_OPTIONS", 1]}, "expr": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "*", "alias": null, "function": null, "subquery": null}], "from": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "film", "column": null, "expr": "film", "alias": null, "function": null, "subquery": null}], "index_hints": null, "partition": null, "where": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["film_id"], "isOperator": false, "expr": "(film_id < 10)"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": [], "isOperator": true, "expr": "||"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["film_id"], "isOperator": false, "expr": "(film_id > 20)"}], "group": null, "having": null, "order": null, "limit": null, "procedure": null, "into": null, "join": null, "union": [], "end_options": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "first": 23, "last": 51}, {"@type": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "OPTIONS": {"ALL": 1, "DISTINCT": 1, "DISTINCTROW": 1, "HIGH_PRIORITY": 2, "MAX_STATEMENT_TIME": [3, "var="], "STRAIGHT_JOIN": 4, "SQL_SMALL_RESULT": 5, "SQL_BIG_RESULT": 6, "SQL_BUFFER_RESULT": 7, "SQL_CACHE": 8, "SQL_NO_CACHE": 8, "SQL_CALC_FOUND_ROWS": 9}, "END_OPTIONS": {"FOR UPDATE": 1, "LOCK IN SHARE MODE": 1}, "CLAUSES": {"SELECT": ["SELECT", 2], "_OPTIONS": ["_OPTIONS", 1], "_SELECT": ["SELECT", 1], "INTO": ["INTO", 3], "FROM": ["FROM", 3], "FORCE": ["FORCE", 1], "USE": ["USE", 1], "IGNORE": ["IGNORE", 3], "PARTITION": ["PARTITION", 3], "JOIN": ["JOIN", 1], "FULL JOIN": ["FULL JOIN", 1], "INNER JOIN": ["INNER JOIN", 1], "LEFT JOIN": ["LEFT JOIN", 1], "LEFT OUTER JOIN": ["LEFT OUTER JOIN", 1], "RIGHT JOIN": ["RIGHT JOIN", 1], "RIGHT OUTER JOIN": ["RIGHT OUTER JOIN", 1], "NATURAL JOIN": ["NATURAL JOIN", 1], "NATURAL LEFT JOIN": ["NATURAL LEFT JOIN", 1], "NATURAL RIGHT JOIN": ["NATURAL RIGHT JOIN", 1], "NATURAL LEFT OUTER JOIN": ["NATURAL LEFT OUTER JOIN", 1], "NATURAL RIGHT OUTER JOIN": ["NATURAL RIGHT JOIN", 1], "WHERE": ["WHERE", 3], "GROUP BY": ["GROUP BY", 3], "HAVING": ["HAVING", 3], "ORDER BY": ["ORDER BY", 3], "LIMIT": ["LIMIT", 3], "PROCEDURE": ["PROCEDURE", 3], "UNION": ["UNION", 1], "EXCEPT": ["EXCEPT", 1], "INTERSECT": ["INTERSECT", 1], "_END_OPTIONS": ["_END_OPTIONS", 1]}, "expr": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "*", "alias": null, "function": null, "subquery": null}], "from": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "film", "column": null, "expr": "film", "alias": null, "function": null, "subquery": null}], "index_hints": null, "partition": null, "where": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["film_id"], "isOperator": false, "expr": "`film_id` != 10"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": [], "isOperator": true, "expr": "AND"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["film_id"], "isOperator": false, "expr": "`film_id` <= 20"}], "group": null, "having": null, "order": null, "limit": null, "procedure": null, "into": null, "join": null, "union": [], "end_options": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "first": 52, "last": 76}, {"@type": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "OPTIONS": {"ALL": 1, "DISTINCT": 1, "DISTINCTROW": 1, "HIGH_PRIORITY": 2, "MAX_STATEMENT_TIME": [3, "var="], "STRAIGHT_JOIN": 4, "SQL_SMALL_RESULT": 5, "SQL_BIG_RESULT": 6, "SQL_BUFFER_RESULT": 7, "SQL_CACHE": 8, "SQL_NO_CACHE": 8, "SQL_CALC_FOUND_ROWS": 9}, "END_OPTIONS": {"FOR UPDATE": 1, "LOCK IN SHARE MODE": 1}, "CLAUSES": {"SELECT": ["SELECT", 2], "_OPTIONS": ["_OPTIONS", 1], "_SELECT": ["SELECT", 1], "INTO": ["INTO", 3], "FROM": ["FROM", 3], "FORCE": ["FORCE", 1], "USE": ["USE", 1], "IGNORE": ["IGNORE", 3], "PARTITION": ["PARTITION", 3], "JOIN": ["JOIN", 1], "FULL JOIN": ["FULL JOIN", 1], "INNER JOIN": ["INNER JOIN", 1], "LEFT JOIN": ["LEFT JOIN", 1], "LEFT OUTER JOIN": ["LEFT OUTER JOIN", 1], "RIGHT JOIN": ["RIGHT JOIN", 1], "RIGHT OUTER JOIN": ["RIGHT OUTER JOIN", 1], "NATURAL JOIN": ["NATURAL JOIN", 1], "NATURAL LEFT JOIN": ["NATURAL LEFT JOIN", 1], "NATURAL RIGHT JOIN": ["NATURAL RIGHT JOIN", 1], "NATURAL LEFT OUTER JOIN": ["NATURAL LEFT OUTER JOIN", 1], "NATURAL RIGHT OUTER JOIN": ["NATURAL RIGHT JOIN", 1], "WHERE": ["WHERE", 3], "GROUP BY": ["GROUP BY", 3], "HAVING": ["HAVING", 3], "ORDER BY": ["ORDER BY", 3], "LIMIT": ["LIMIT", 3], "PROCEDURE": ["PROCEDURE", 3], "UNION": ["UNION", 1], "EXCEPT": ["EXCEPT", 1], "INTERSECT": ["INTERSECT", 1], "_END_OPTIONS": ["_END_OPTIONS", 1]}, "expr": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "*", "alias": null, "function": null, "subquery": null}], "from": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "film", "column": null, "expr": "film", "alias": null, "function": null, "subquery": null}], "index_hints": null, "partition": null, "where": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["film", "film_id"], "isOperator": false, "expr": "`film`.`film_id` <> 10"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": [], "isOperator": true, "expr": "&&"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["film", "film_id"], "isOperator": false, "expr": "`film`.`film_id` <= 20"}], "group": null, "having": null, "order": null, "limit": null, "procedure": null, "into": null, "join": null, "union": [], "end_options": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "first": 77, "last": 105}, {"@type": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "OPTIONS": {"ALL": 1, "DISTINCT": 1, "DISTINCTROW": 1, "HIGH_PRIORITY": 2, "MAX_STATEMENT_TIME": [3, "var="], "STRAIGHT_JOIN": 4, "SQL_SMALL_RESULT": 5, "SQL_BIG_RESULT": 6, "SQL_BUFFER_RESULT": 7, "SQL_CACHE": 8, "SQL_NO_CACHE": 8, "SQL_CALC_FOUND_ROWS": 9}, "END_OPTIONS": {"FOR UPDATE": 1, "LOCK IN SHARE MODE": 1}, "CLAUSES": {"SELECT": ["SELECT", 2], "_OPTIONS": ["_OPTIONS", 1], "_SELECT": ["SELECT", 1], "INTO": ["INTO", 3], "FROM": ["FROM", 3], "FORCE": ["FORCE", 1], "USE": ["USE", 1], "IGNORE": ["IGNORE", 3], "PARTITION": ["PARTITION", 3], "JOIN": ["JOIN", 1], "FULL JOIN": ["FULL JOIN", 1], "INNER JOIN": ["INNER JOIN", 1], "LEFT JOIN": ["LEFT JOIN", 1], "LEFT OUTER JOIN": ["LEFT OUTER JOIN", 1], "RIGHT JOIN": ["RIGHT JOIN", 1], "RIGHT OUTER JOIN": ["RIGHT OUTER JOIN", 1], "NATURAL JOIN": ["NATURAL JOIN", 1], "NATURAL LEFT JOIN": ["NATURAL LEFT JOIN", 1], "NATURAL RIGHT JOIN": ["NATURAL RIGHT JOIN", 1], "NATURAL LEFT OUTER JOIN": ["NATURAL LEFT OUTER JOIN", 1], "NATURAL RIGHT OUTER JOIN": ["NATURAL RIGHT JOIN", 1], "WHERE": ["WHERE", 3], "GROUP BY": ["GROUP BY", 3], "HAVING": ["HAVING", 3], "ORDER BY": ["ORDER BY", 3], "LIMIT": ["LIMIT", 3], "PROCEDURE": ["PROCEDURE", 3], "UNION": ["UNION", 1], "EXCEPT": ["EXCEPT", 1], "INTERSECT": ["INTERSECT", 1], "_END_OPTIONS": ["_END_OPTIONS", 1]}, "expr": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "*", "alias": null, "function": null, "subquery": null}], "from": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "film", "column": null, "expr": "film", "alias": null, "function": null, "subquery": null}], "index_hints": null, "partition": null, "where": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["film", "film_id"], "isOperator": false, "expr": "film.film_id < 20"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": [], "isOperator": true, "expr": "XOR"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["film", "rating", "PG-13"], "isOperator": false, "expr": "film.rating = 'PG-13'"}], "group": null, "having": null, "order": null, "limit": null, "procedure": null, "into": null, "join": null, "union": [], "end_options": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "first": 106, "last": 134}, {"@type": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "OPTIONS": {"ALL": 1, "DISTINCT": 1, "DISTINCTROW": 1, "HIGH_PRIORITY": 2, "MAX_STATEMENT_TIME": [3, "var="], "STRAIGHT_JOIN": 4, "SQL_SMALL_RESULT": 5, "SQL_BIG_RESULT": 6, "SQL_BUFFER_RESULT": 7, "SQL_CACHE": 8, "SQL_NO_CACHE": 8, "SQL_CALC_FOUND_ROWS": 9}, "END_OPTIONS": {"FOR UPDATE": 1, "LOCK IN SHARE MODE": 1}, "CLAUSES": {"SELECT": ["SELECT", 2], "_OPTIONS": ["_OPTIONS", 1], "_SELECT": ["SELECT", 1], "INTO": ["INTO", 3], "FROM": ["FROM", 3], "FORCE": ["FORCE", 1], "USE": ["USE", 1], "IGNORE": ["IGNORE", 3], "PARTITION": ["PARTITION", 3], "JOIN": ["JOIN", 1], "FULL JOIN": ["FULL JOIN", 1], "INNER JOIN": ["INNER JOIN", 1], "LEFT JOIN": ["LEFT JOIN", 1], "LEFT OUTER JOIN": ["LEFT OUTER JOIN", 1], "RIGHT JOIN": ["RIGHT JOIN", 1], "RIGHT OUTER JOIN": ["RIGHT OUTER JOIN", 1], "NATURAL JOIN": ["NATURAL JOIN", 1], "NATURAL LEFT JOIN": ["NATURAL LEFT JOIN", 1], "NATURAL RIGHT JOIN": ["NATURAL RIGHT JOIN", 1], "NATURAL LEFT OUTER JOIN": ["NATURAL LEFT OUTER JOIN", 1], "NATURAL RIGHT OUTER JOIN": ["NATURAL RIGHT JOIN", 1], "WHERE": ["WHERE", 3], "GROUP BY": ["GROUP BY", 3], "HAVING": ["HAVING", 3], "ORDER BY": ["ORDER BY", 3], "LIMIT": ["LIMIT", 3], "PROCEDURE": ["PROCEDURE", 3], "UNION": ["UNION", 1], "EXCEPT": ["EXCEPT", 1], "INTERSECT": ["INTERSECT", 1], "_END_OPTIONS": ["_END_OPTIONS", 1]}, "expr": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "*", "alias": null, "function": null, "subquery": null}], "from": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "film", "column": null, "expr": "film", "alias": null, "function": null, "subquery": null}], "index_hints": null, "partition": null, "where": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["film_id"], "isOperator": false, "expr": "film_id = 10"}], "group": null, "having": null, "order": null, "limit": null, "procedure": null, "into": null, "join": null, "union": [], "end_options": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "first": 135, "last": 153}, {"@type": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "OPTIONS": {"ALL": 1, "DISTINCT": 1, "DISTINCTROW": 1, "HIGH_PRIORITY": 2, "MAX_STATEMENT_TIME": [3, "var="], "STRAIGHT_JOIN": 4, "SQL_SMALL_RESULT": 5, "SQL_BIG_RESULT": 6, "SQL_BUFFER_RESULT": 7, "SQL_CACHE": 8, "SQL_NO_CACHE": 8, "SQL_CALC_FOUND_ROWS": 9}, "END_OPTIONS": {"FOR UPDATE": 1, "LOCK IN SHARE MODE": 1}, "CLAUSES": {"SELECT": ["SELECT", 2], "_OPTIONS": ["_OPTIONS", 1], "_SELECT": ["SELECT", 1], "INTO": ["INTO", 3], "FROM": ["FROM", 3], "FORCE": ["FORCE", 1], "USE": ["USE", 1], "IGNORE": ["IGNORE", 3], "PARTITION": ["PARTITION", 3], "JOIN": ["JOIN", 1], "FULL JOIN": ["FULL JOIN", 1], "INNER JOIN": ["INNER JOIN", 1], "LEFT JOIN": ["LEFT JOIN", 1], "LEFT OUTER JOIN": ["LEFT OUTER JOIN", 1], "RIGHT JOIN": ["RIGHT JOIN", 1], "RIGHT OUTER JOIN": ["RIGHT OUTER JOIN", 1], "NATURAL JOIN": ["NATURAL JOIN", 1], "NATURAL LEFT JOIN": ["NATURAL LEFT JOIN", 1], "NATURAL RIGHT JOIN": ["NATURAL RIGHT JOIN", 1], "NATURAL LEFT OUTER JOIN": ["NATURAL LEFT OUTER JOIN", 1], "NATURAL RIGHT OUTER JOIN": ["NATURAL RIGHT JOIN", 1], "WHERE": ["WHERE", 3], "GROUP BY": ["GROUP BY", 3], "HAVING": ["HAVING", 3], "ORDER BY": ["ORDER BY", 3], "LIMIT": ["LIMIT", 3], "PROCEDURE": ["PROCEDURE", 3], "UNION": ["UNION", 1], "EXCEPT": ["EXCEPT", 1], "INTERSECT": ["INTERSECT", 1], "_END_OPTIONS": ["_END_OPTIONS", 1]}, "expr": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "*", "alias": null, "function": null, "subquery": null}], "from": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "film", "column": null, "expr": "film", "alias": null, "function": null, "subquery": null}], "index_hints": null, "partition": null, "where": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["film_id"], "isOperator": false, "expr": "NOT film_id > 10"}], "group": null, "having": null, "order": null, "limit": null, "procedure": null, "into": null, "join": null, "union": [], "end_options": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "first": 154, "last": 172}, {"@type": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "OPTIONS": {"ALL": 1, "DISTINCT": 1, "DISTINCTROW": 1, "HIGH_PRIORITY": 2, "MAX_STATEMENT_TIME": [3, "var="], "STRAIGHT_JOIN": 4, "SQL_SMALL_RESULT": 5, "SQL_BIG_RESULT": 6, "SQL_BUFFER_RESULT": 7, "SQL_CACHE": 8, "SQL_NO_CACHE": 8, "SQL_CALC_FOUND_ROWS": 9}, "END_OPTIONS": {"FOR UPDATE": 1, "LOCK IN SHARE MODE": 1}, "CLAUSES": {"SELECT": ["SELECT", 2], "_OPTIONS": ["_OPTIONS", 1], "_SELECT": ["SELECT", 1], "INTO": ["INTO", 3], "FROM": ["FROM", 3], "FORCE": ["FORCE", 1], "USE": ["USE", 1], "IGNORE": ["IGNORE", 3], "PARTITION": ["PARTITION", 3], "JOIN": ["JOIN", 1], "FULL JOIN": ["FULL JOIN", 1], "INNER JOIN": ["INNER JOIN", 1], "LEFT JOIN": ["LEFT JOIN", 1], "LEFT OUTER JOIN": ["LEFT OUTER JOIN", 1], "RIGHT JOIN": ["RIGHT JOIN", 1], "RIGHT OUTER JOIN": ["RIGHT OUTER JOIN", 1], "NATURAL JOIN": ["NATURAL JOIN", 1], "NATURAL LEFT JOIN": ["NATURAL LEFT JOIN", 1], "NATURAL RIGHT JOIN": ["NATURAL RIGHT JOIN", 1], "NATURAL LEFT OUTER JOIN": ["NATURAL LEFT OUTER JOIN", 1], "NATURAL RIGHT OUTER JOIN": ["NATURAL RIGHT JOIN", 1], "WHERE": ["WHERE", 3], "GROUP BY": ["GROUP BY", 3], "HAVING": ["HAVING", 3], "ORDER BY": ["ORDER BY", 3], "LIMIT": ["LIMIT", 3], "PROCEDURE": ["PROCEDURE", 3], "UNION": ["UNION", 1], "EXCEPT": ["EXCEPT", 1], "INTERSECT": ["INTERSECT", 1], "_END_OPTIONS": ["_END_OPTIONS", 1]}, "expr": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "*", "alias": null, "function": null, "subquery": null}], "from": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "film", "column": null, "expr": "film", "alias": null, "function": null, "subquery": null}], "index_hints": null, "partition": null, "where": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["film_id"], "isOperator": false, "expr": "! (film_id > 10)"}], "group": null, "having": null, "order": null, "limit": null, "procedure": null, "into": null, "join": null, "union": [], "end_options": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "first": 173, "last": 193}, {"@type": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "OPTIONS": {"ALL": 1, "DISTINCT": 1, "DISTINCTROW": 1, "HIGH_PRIORITY": 2, "MAX_STATEMENT_TIME": [3, "var="], "STRAIGHT_JOIN": 4, "SQL_SMALL_RESULT": 5, "SQL_BIG_RESULT": 6, "SQL_BUFFER_RESULT": 7, "SQL_CACHE": 8, "SQL_NO_CACHE": 8, "SQL_CALC_FOUND_ROWS": 9}, "END_OPTIONS": {"FOR UPDATE": 1, "LOCK IN SHARE MODE": 1}, "CLAUSES": {"SELECT": ["SELECT", 2], "_OPTIONS": ["_OPTIONS", 1], "_SELECT": ["SELECT", 1], "INTO": ["INTO", 3], "FROM": ["FROM", 3], "FORCE": ["FORCE", 1], "USE": ["USE", 1], "IGNORE": ["IGNORE", 3], "PARTITION": ["PARTITION", 3], "JOIN": ["JOIN", 1], "FULL JOIN": ["FULL JOIN", 1], "INNER JOIN": ["INNER JOIN", 1], "LEFT JOIN": ["LEFT JOIN", 1], "LEFT OUTER JOIN": ["LEFT OUTER JOIN", 1], "RIGHT JOIN": ["RIGHT JOIN", 1], "RIGHT OUTER JOIN": ["RIGHT OUTER JOIN", 1], "NATURAL JOIN": ["NATURAL JOIN", 1], "NATURAL LEFT JOIN": ["NATURAL LEFT JOIN", 1], "NATURAL RIGHT JOIN": ["NATURAL RIGHT JOIN", 1], "NATURAL LEFT OUTER JOIN": ["NATURAL LEFT OUTER JOIN", 1], "NATURAL RIGHT OUTER JOIN": ["NATURAL RIGHT JOIN", 1], "WHERE": ["WHERE", 3], "GROUP BY": ["GROUP BY", 3], "HAVING": ["HAVING", 3], "ORDER BY": ["ORDER BY", 3], "LIMIT": ["LIMIT", 3], "PROCEDURE": ["PROCEDURE", 3], "UNION": ["UNION", 1], "EXCEPT": ["EXCEPT", 1], "INTERSECT": ["INTERSECT", 1], "_END_OPTIONS": ["_END_OPTIONS", 1]}, "expr": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "*", "alias": null, "function": null, "subquery": null}], "from": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "film", "column": null, "expr": "film", "alias": null, "function": null, "subquery": null}], "index_hints": null, "partition": null, "where": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["description"], "isOperator": false, "expr": "description IS NULL"}], "group": null, "having": null, "order": null, "limit": null, "procedure": null, "into": null, "join": null, "union": [], "end_options": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "first": 194, "last": 210}, {"@type": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "OPTIONS": {"ALL": 1, "DISTINCT": 1, "DISTINCTROW": 1, "HIGH_PRIORITY": 2, "MAX_STATEMENT_TIME": [3, "var="], "STRAIGHT_JOIN": 4, "SQL_SMALL_RESULT": 5, "SQL_BIG_RESULT": 6, "SQL_BUFFER_RESULT": 7, "SQL_CACHE": 8, "SQL_NO_CACHE": 8, "SQL_CALC_FOUND_ROWS": 9}, "END_OPTIONS": {"FOR UPDATE": 1, "LOCK IN SHARE MODE": 1}, "CLAUSES": {"SELECT": ["SELECT", 2], "_OPTIONS": ["_OPTIONS", 1], "_SELECT": ["SELECT", 1], "INTO": ["INTO", 3], "FROM": ["FROM", 3], "FORCE": ["FORCE", 1], "USE": ["USE", 1], "IGNORE": ["IGNORE", 3], "PARTITION": ["PARTITION", 3], "JOIN": ["JOIN", 1], "FULL JOIN": ["FULL JOIN", 1], "INNER JOIN": ["INNER JOIN", 1], "LEFT JOIN": ["LEFT JOIN", 1], "LEFT OUTER JOIN": ["LEFT OUTER JOIN", 1], "RIGHT JOIN": ["RIGHT JOIN", 1], "RIGHT OUTER JOIN": ["RIGHT OUTER JOIN", 1], "NATURAL JOIN": ["NATURAL JOIN", 1], "NATURAL LEFT JOIN": ["NATURAL LEFT JOIN", 1], "NATURAL RIGHT JOIN": ["NATURAL RIGHT JOIN", 1], "NATURAL LEFT OUTER JOIN": ["NATURAL LEFT OUTER JOIN", 1], "NATURAL RIGHT OUTER JOIN": ["NATURAL RIGHT JOIN", 1], "WHERE": ["WHERE", 3], "GROUP BY": ["GROUP BY", 3], "HAVING": ["HAVING", 3], "ORDER BY": ["ORDER BY", 3], "LIMIT": ["LIMIT", 3], "PROCEDURE": ["PROCEDURE", 3], "UNION": ["UNION", 1], "EXCEPT": ["EXCEPT", 1], "INTERSECT": ["INTERSECT", 1], "_END_OPTIONS": ["_END_OPTIONS", 1]}, "expr": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "*", "alias": null, "function": null, "subquery": null}], "from": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "film", "column": null, "expr": "film", "alias": null, "function": null, "subquery": null}], "index_hints": null, "partition": null, "where": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["description"], "isOperator": false, "expr": "description IS NOT NULL"}], "group": null, "having": null, "order": null, "limit": null, "procedure": null, "into": null, "join": null, "union": [], "end_options": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "first": 211, "last": 227}, {"@type": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "OPTIONS": {"ALL": 1, "DISTINCT": 1, "DISTINCTROW": 1, "HIGH_PRIORITY": 2, "MAX_STATEMENT_TIME": [3, "var="], "STRAIGHT_JOIN": 4, "SQL_SMALL_RESULT": 5, "SQL_BIG_RESULT": 6, "SQL_BUFFER_RESULT": 7, "SQL_CACHE": 8, "SQL_NO_CACHE": 8, "SQL_CALC_FOUND_ROWS": 9}, "END_OPTIONS": {"FOR UPDATE": 1, "LOCK IN SHARE MODE": 1}, "CLAUSES": {"SELECT": ["SELECT", 2], "_OPTIONS": ["_OPTIONS", 1], "_SELECT": ["SELECT", 1], "INTO": ["INTO", 3], "FROM": ["FROM", 3], "FORCE": ["FORCE", 1], "USE": ["USE", 1], "IGNORE": ["IGNORE", 3], "PARTITION": ["PARTITION", 3], "JOIN": ["JOIN", 1], "FULL JOIN": ["FULL JOIN", 1], "INNER JOIN": ["INNER JOIN", 1], "LEFT JOIN": ["LEFT JOIN", 1], "LEFT OUTER JOIN": ["LEFT OUTER JOIN", 1], "RIGHT JOIN": ["RIGHT JOIN", 1], "RIGHT OUTER JOIN": ["RIGHT OUTER JOIN", 1], "NATURAL JOIN": ["NATURAL JOIN", 1], "NATURAL LEFT JOIN": ["NATURAL LEFT JOIN", 1], "NATURAL RIGHT JOIN": ["NATURAL RIGHT JOIN", 1], "NATURAL LEFT OUTER JOIN": ["NATURAL LEFT OUTER JOIN", 1], "NATURAL RIGHT OUTER JOIN": ["NATURAL RIGHT JOIN", 1], "WHERE": ["WHERE", 3], "GROUP BY": ["GROUP BY", 3], "HAVING": ["HAVING", 3], "ORDER BY": ["ORDER BY", 3], "LIMIT": ["LIMIT", 3], "PROCEDURE": ["PROCEDURE", 3], "UNION": ["UNION", 1], "EXCEPT": ["EXCEPT", 1], "INTERSECT": ["INTERSECT", 1], "_END_OPTIONS": ["_END_OPTIONS", 1]}, "expr": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "*", "alias": null, "function": null, "subquery": null}], "from": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "film", "column": null, "expr": "film", "alias": null, "function": null, "subquery": null}], "index_hints": null, "partition": null, "where": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["film_id"], "isOperator": false, "expr": "film_id BETWEEN 10 AND 20"}], "group": null, "having": null, "order": null, "limit": null, "procedure": null, "into": null, "join": null, "union": [], "end_options": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "first": 228, "last": 248}, {"@type": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "OPTIONS": {"ALL": 1, "DISTINCT": 1, "DISTINCTROW": 1, "HIGH_PRIORITY": 2, "MAX_STATEMENT_TIME": [3, "var="], "STRAIGHT_JOIN": 4, "SQL_SMALL_RESULT": 5, "SQL_BIG_RESULT": 6, "SQL_BUFFER_RESULT": 7, "SQL_CACHE": 8, "SQL_NO_CACHE": 8, "SQL_CALC_FOUND_ROWS": 9}, "END_OPTIONS": {"FOR UPDATE": 1, "LOCK IN SHARE MODE": 1}, "CLAUSES": {"SELECT": ["SELECT", 2], "_OPTIONS": ["_OPTIONS", 1], "_SELECT": ["SELECT", 1], "INTO": ["INTO", 3], "FROM": ["FROM", 3], "FORCE": ["FORCE", 1], "USE": ["USE", 1], "IGNORE": ["IGNORE", 3], "PARTITION": ["PARTITION", 3], "JOIN": ["JOIN", 1], "FULL JOIN": ["FULL JOIN", 1], "INNER JOIN": ["INNER JOIN", 1], "LEFT JOIN": ["LEFT JOIN", 1], "LEFT OUTER JOIN": ["LEFT OUTER JOIN", 1], "RIGHT JOIN": ["RIGHT JOIN", 1], "RIGHT OUTER JOIN": ["RIGHT OUTER JOIN", 1], "NATURAL JOIN": ["NATURAL JOIN", 1], "NATURAL LEFT JOIN": ["NATURAL LEFT JOIN", 1], "NATURAL RIGHT JOIN": ["NATURAL RIGHT JOIN", 1], "NATURAL LEFT OUTER JOIN": ["NATURAL LEFT OUTER JOIN", 1], "NATURAL RIGHT OUTER JOIN": ["NATURAL RIGHT JOIN", 1], "WHERE": ["WHERE", 3], "GROUP BY": ["GROUP BY", 3], "HAVING": ["HAVING", 3], "ORDER BY": ["ORDER BY", 3], "LIMIT": ["LIMIT", 3], "PROCEDURE": ["PROCEDURE", 3], "UNION": ["UNION", 1], "EXCEPT": ["EXCEPT", 1], "INTERSECT": ["INTERSECT", 1], "_END_OPTIONS": ["_END_OPTIONS", 1]}, "expr": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "*", "alias": null, "function": null, "subquery": null}], "from": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "film", "column": null, "expr": "film", "alias": null, "function": null, "subquery": null}], "index_hints": null, "partition": null, "where": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["film_id"], "isOperator": false, "expr": "film_id NOT BETWEEN 10 AND 20"}], "group": null, "having": null, "order": null, "limit": null, "procedure": null, "into": null, "join": null, "union": [], "end_options": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "first": 249, "last": 271}, {"@type": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "OPTIONS": {"ALL": 1, "DISTINCT": 1, "DISTINCTROW": 1, "HIGH_PRIORITY": 2, "MAX_STATEMENT_TIME": [3, "var="], "STRAIGHT_JOIN": 4, "SQL_SMALL_RESULT": 5, "SQL_BIG_RESULT": 6, "SQL_BUFFER_RESULT": 7, "SQL_CACHE": 8, "SQL_NO_CACHE": 8, "SQL_CALC_FOUND_ROWS": 9}, "END_OPTIONS": {"FOR UPDATE": 1, "LOCK IN SHARE MODE": 1}, "CLAUSES": {"SELECT": ["SELECT", 2], "_OPTIONS": ["_OPTIONS", 1], "_SELECT": ["SELECT", 1], "INTO": ["INTO", 3], "FROM": ["FROM", 3], "FORCE": ["FORCE", 1], "USE": ["USE", 1], "IGNORE": ["IGNORE", 3], "PARTITION": ["PARTITION", 3], "JOIN": ["JOIN", 1], "FULL JOIN": ["FULL JOIN", 1], "INNER JOIN": ["INNER JOIN", 1], "LEFT JOIN": ["LEFT JOIN", 1], "LEFT OUTER JOIN": ["LEFT OUTER JOIN", 1], "RIGHT JOIN": ["RIGHT JOIN", 1], "RIGHT OUTER JOIN": ["RIGHT OUTER JOIN", 1], "NATURAL JOIN": ["NATURAL JOIN", 1], "NATURAL LEFT JOIN": ["NATURAL LEFT JOIN", 1], "NATURAL RIGHT JOIN": ["NATURAL RIGHT JOIN", 1], "NATURAL LEFT OUTER JOIN": ["NATURAL LEFT OUTER JOIN", 1], "NATURAL RIGHT OUTER JOIN": ["NATURAL RIGHT JOIN", 1], "WHERE": ["WHERE", 3], "GROUP BY": ["GROUP BY", 3], "HAVING": ["HAVING", 3], "ORDER BY": ["ORDER BY", 3], "LIMIT": ["LIMIT", 3], "PROCEDURE": ["PROCEDURE", 3], "UNION": ["UNION", 1], "EXCEPT": ["EXCEPT", 1], "INTERSECT": ["INTERSECT", 1], "_END_OPTIONS": ["_END_OPTIONS", 1]}, "expr": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "*", "alias": null, "function": null, "subquery": null}], "from": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "film", "column": null, "expr": "film", "alias": null, "function": null, "subquery": null}], "index_hints": null, "partition": null, "where": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["film_id"], "isOperator": false, "expr": "film_id IN (3,5,7)"}], "group": null, "having": null, "order": null, "limit": null, "procedure": null, "into": null, "join": null, "union": [], "end_options": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "first": 272, "last": 294}, {"@type": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "OPTIONS": {"ALL": 1, "DISTINCT": 1, "DISTINCTROW": 1, "HIGH_PRIORITY": 2, "MAX_STATEMENT_TIME": [3, "var="], "STRAIGHT_JOIN": 4, "SQL_SMALL_RESULT": 5, "SQL_BIG_RESULT": 6, "SQL_BUFFER_RESULT": 7, "SQL_CACHE": 8, "SQL_NO_CACHE": 8, "SQL_CALC_FOUND_ROWS": 9}, "END_OPTIONS": {"FOR UPDATE": 1, "LOCK IN SHARE MODE": 1}, "CLAUSES": {"SELECT": ["SELECT", 2], "_OPTIONS": ["_OPTIONS", 1], "_SELECT": ["SELECT", 1], "INTO": ["INTO", 3], "FROM": ["FROM", 3], "FORCE": ["FORCE", 1], "USE": ["USE", 1], "IGNORE": ["IGNORE", 3], "PARTITION": ["PARTITION", 3], "JOIN": ["JOIN", 1], "FULL JOIN": ["FULL JOIN", 1], "INNER JOIN": ["INNER JOIN", 1], "LEFT JOIN": ["LEFT JOIN", 1], "LEFT OUTER JOIN": ["LEFT OUTER JOIN", 1], "RIGHT JOIN": ["RIGHT JOIN", 1], "RIGHT OUTER JOIN": ["RIGHT OUTER JOIN", 1], "NATURAL JOIN": ["NATURAL JOIN", 1], "NATURAL LEFT JOIN": ["NATURAL LEFT JOIN", 1], "NATURAL RIGHT JOIN": ["NATURAL RIGHT JOIN", 1], "NATURAL LEFT OUTER JOIN": ["NATURAL LEFT OUTER JOIN", 1], "NATURAL RIGHT OUTER JOIN": ["NATURAL RIGHT JOIN", 1], "WHERE": ["WHERE", 3], "GROUP BY": ["GROUP BY", 3], "HAVING": ["HAVING", 3], "ORDER BY": ["ORDER BY", 3], "LIMIT": ["LIMIT", 3], "PROCEDURE": ["PROCEDURE", 3], "UNION": ["UNION", 1], "EXCEPT": ["EXCEPT", 1], "INTERSECT": ["INTERSECT", 1], "_END_OPTIONS": ["_END_OPTIONS", 1]}, "expr": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "*", "alias": null, "function": null, "subquery": null}], "from": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "film", "column": null, "expr": "film", "alias": null, "function": null, "subquery": null}], "index_hints": null, "partition": null, "where": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["rating", "UPPER", "pg"], "isOperator": false, "expr": "rating = UPPER('pg')"}], "group": null, "having": null, "order": null, "limit": null, "procedure": null, "into": null, "join": null, "union": [], "end_options": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "first": 295, "last": 314}, {"@type": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "OPTIONS": {"ALL": 1, "DISTINCT": 1, "DISTINCTROW": 1, "HIGH_PRIORITY": 2, "MAX_STATEMENT_TIME": [3, "var="], "STRAIGHT_JOIN": 4, "SQL_SMALL_RESULT": 5, "SQL_BIG_RESULT": 6, "SQL_BUFFER_RESULT": 7, "SQL_CACHE": 8, "SQL_NO_CACHE": 8, "SQL_CALC_FOUND_ROWS": 9}, "END_OPTIONS": {"FOR UPDATE": 1, "LOCK IN SHARE MODE": 1}, "CLAUSES": {"SELECT": ["SELECT", 2], "_OPTIONS": ["_OPTIONS", 1], "_SELECT": ["SELECT", 1], "INTO": ["INTO", 3], "FROM": ["FROM", 3], "FORCE": ["FORCE", 1], "USE": ["USE", 1], "IGNORE": ["IGNORE", 3], "PARTITION": ["PARTITION", 3], "JOIN": ["JOIN", 1], "FULL JOIN": ["FULL JOIN", 1], "INNER JOIN": ["INNER JOIN", 1], "LEFT JOIN": ["LEFT JOIN", 1], "LEFT OUTER JOIN": ["LEFT OUTER JOIN", 1], "RIGHT JOIN": ["RIGHT JOIN", 1], "RIGHT OUTER JOIN": ["RIGHT OUTER JOIN", 1], "NATURAL JOIN": ["NATURAL JOIN", 1], "NATURAL LEFT JOIN": ["NATURAL LEFT JOIN", 1], "NATURAL RIGHT JOIN": ["NATURAL RIGHT JOIN", 1], "NATURAL LEFT OUTER JOIN": ["NATURAL LEFT OUTER JOIN", 1], "NATURAL RIGHT OUTER JOIN": ["NATURAL RIGHT JOIN", 1], "WHERE": ["WHERE", 3], "GROUP BY": ["GROUP BY", 3], "HAVING": ["HAVING", 3], "ORDER BY": ["ORDER BY", 3], "LIMIT": ["LIMIT", 3], "PROCEDURE": ["PROCEDURE", 3], "UNION": ["UNION", 1], "EXCEPT": ["EXCEPT", 1], "INTERSECT": ["INTERSECT", 1], "_END_OPTIONS": ["_END_OPTIONS", 1]}, "expr": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "*", "alias": null, "function": null, "subquery": null}], "from": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "film", "column": null, "expr": "film", "alias": null, "function": null, "subquery": null}], "index_hints": null, "partition": null, "where": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["rating", "SOUNDS", "PG"], "isOperator": false, "expr": "rating SOUNDS LIKE 'PG'"}], "group": null, "having": null, "order": null, "limit": null, "procedure": null, "into": null, "join": null, "union": [], "end_options": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "first": 315, "last": 333}], "brackets": 0, "strict": false, "errors": []}, "errors": {"lexer": [], "parser": []}}