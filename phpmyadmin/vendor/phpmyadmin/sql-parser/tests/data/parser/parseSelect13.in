SELECT post.*, post.username AS postusername, post.ipaddress AS ip, IF(post.visible = 2, 1, 0) AS isdeleted, user.*, userfield.*, usertextfield.*, icon.title as icontitle, icon.iconpath, avatar.avatarpath, NOT ISNULL(customavatar.userid) AS hascustomavatar, customavatar.dateline AS avatardateline,customavatar.width AS avwidth,customavatar.height AS avheight, spamlog.postid AS spamlog_postid, deletionlog.userid AS del_userid, deletionlog.username AS del_username, deletionlog.reason AS del_reason, editlog.userid AS edit_userid, editlog.username AS edit_username, editlog.dateline AS edit_dateline, editlog.reason AS edit_reason, editlog.hashistory, postparsed.pagetext_html, postparsed.hasimages, sigparsed.signatureparsed, sigparsed.hasimages AS sighasimages, sigpic.userid AS sigpic, sigpic.dateline AS sigpicdateline, sigpic.width AS sigpicwidth, sigpic.height AS sigpicheight, IF(displaygroupid=0, user.usergroupid, displaygroupid) AS displaygroupid, infractiongroupid , post_icon_list.icon_id_list, post_icon_list.is_auto AS icon_is_auto, approvedlog.modid AS approvedmodid, approvedlog.dateline AS approveddateline, approvedlog.status AS approvedstatus, approvedlog.info AS approvedinfo, movedlog.modid AS movedmodid, movedlog.dateline AS moveddateline, movedlog.status AS movedstatus, movedlog.info AS movedinfo, ( SELECT useragent FROM session WHERE userid=post.userid AND lastactivity > 1644859580 ORDER BY lastactivity DESC LIMIT 1 ) AS useragent, IF ( user.userid IS NOT NULL, (SELECT COUNT(usernoteid) FROM usernote AS usernote WHERE usernote.userid=user.userid AND usernote.priority>=0), 0 ) AS usernotecount , deletionlog.dateline AS del_dateline, scheduled_approval.defer_time AS vbpmal_approval_defer_time, additional_user_data.last_year_message_count, additional_user_data.last_year_reputation, additional_user_data.last_year_groan_count, paid_post_activation.activation_id AS paid_post_activation_id, alm_Model_UserData.credits FROM post AS post LEFT JOIN user AS user ON(user.userid = post.userid) LEFT JOIN userfield AS userfield ON(userfield.userid = user.userid) LEFT JOIN usertextfield AS usertextfield ON(usertextfield.userid = user.userid) LEFT JOIN icon AS icon ON(icon.iconid = post.iconid) LEFT JOIN avatar AS avatar  ON(avatar.avatarid = user.avatarid) LEFT JOIN customavatar AS customavatar ON(customavatar.userid = user.userid) LEFT JOIN spamlog AS spamlog ON(spamlog.postid = post.postid) LEFT JOIN deletionlog AS deletionlog ON(post.postid = deletionlog.primaryid AND deletionlog.type = 'post') LEFT JOIN editlog AS editlog ON(editlog.postid = post.postid) LEFT JOIN postparsed AS postparsed ON(postparsed.postid = post.postid AND postparsed.styleid = 23 AND postparsed.languageid = 5) LEFT JOIN sigparsed AS sigparsed ON(sigparsed.userid = user.userid AND sigparsed.styleid = 23 AND sigparsed.languageid = 5) LEFT JOIN sigpic AS sigpic ON(sigpic.userid = post.userid) LEFT JOIN vbppim_post_icon_list AS post_icon_list ON post_icon_list.post_id=post.postid LEFT JOIN vbpmal_log AS approvedlog ON (approvedlog.itemid=post.postid AND approvedlog.action='postapprove') LEFT JOIN vbpmal_log AS movedlog ON (movedlog.itemid=post.postid AND movedlog.action='postmove') LEFT JOIN vbpmal_scheduled_post_approval AS scheduled_approval ON scheduled_approval.post_id = post.postid LEFT JOIN vbpsmt_additional_user_data AS additional_user_data ON additional_user_data.userid=post.userid LEFT JOIN market_pp_post_activation_mapping AS paid_post_activation ON paid_post_activation.post_id = post.postid LEFT JOIN alm_Model_UserData AS alm_Model_UserData ON alm_Model_UserData.user_id=user.userid WHERE post.postid IN (0,3254399,3254508,3254743,3254817,3254969,3255328,3255582,3257603,3257873,3258126,3258150,3258254,3258272,3258311,3260767,3260770,3260776,3261180,3261263,3261317,3261318) ORDER BY post.dateline