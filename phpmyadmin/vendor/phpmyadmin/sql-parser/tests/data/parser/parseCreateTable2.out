{"query": "CREATE TABLE `payment` (\n  `payment_id` smallint(5) unsigned NOT NULL AUTO_INCREMENT,\n  `customer_id` smallint(5) unsigned NOT NULL,\n  `staff_id` tinyint(3) unsigned NOT NULL,\n  `rental_id` int(11) DEFAULT NULL,\n  `amount` decimal(5,2) NOT NULL,\n  `payment_date` datetime NOT NULL,\n  `last_update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n  PRIMARY KEY (`payment_id`),\n  KEY `idx_fk_staff_id` (`staff_id`),\n  KEY `idx_fk_customer_id` (`customer_id`),\n  KEY `fk_payment_rental` (`rental_id`),\n  CONSTRAINT `fk_payment_customer` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`customer_id`) ON UPDATE CASCADE,\n  CONSTRAINT `fk_payment_rental` FOREIGN KEY (`rental_id`) REFERENCES `rental` (`rental_id`) ON DELETE SET NULL ON UPDATE CASCADE,\n  CONSTRAINT `fk_payment_staff` FOREIGN KEY (`staff_id`) REFERENCES `staff` (`staff_id`) ON UPDATE CASCADE\n) ENGINE=InnoDB AUTO_INCREMENT=16050 DEFAULT CHARSET=utf8", "lexer": {"@type": "PhpMyAdmin\\SqlParser\\Lexer", "PARSER_METHODS": ["parseDelimiter", "parseWhitespace", "parseNumber", "parseComment", "parseOperator", "parseBool", "parseString", "parseSymbol", "parseKeyword", "parseLabel", "parseUnknown"], "KEYWORD_NAME_INDICATORS": ["FROM", "SET", "WHERE"], "OPERATOR_NAME_INDICATORS": [",", "."], "str": "CREATE TABLE `payment` (\n  `payment_id` smallint(5) unsigned NOT NULL AUTO_INCREMENT,\n  `customer_id` smallint(5) unsigned NOT NULL,\n  `staff_id` tinyint(3) unsigned NOT NULL,\n  `rental_id` int(11) DEFAULT NULL,\n  `amount` decimal(5,2) NOT NULL,\n  `payment_date` datetime NOT NULL,\n  `last_update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n  PRIMARY KEY (`payment_id`),\n  KEY `idx_fk_staff_id` (`staff_id`),\n  KEY `idx_fk_customer_id` (`customer_id`),\n  KEY `fk_payment_rental` (`rental_id`),\n  CONSTRAINT `fk_payment_customer` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`customer_id`) ON UPDATE CASCADE,\n  CONSTRAINT `fk_payment_rental` FOREIGN KEY (`rental_id`) REFERENCES `rental` (`rental_id`) ON DELETE SET NULL ON UPDATE CASCADE,\n  CONSTRAINT `fk_payment_staff` FOREIGN KEY (`staff_id`) REFERENCES `staff` (`staff_id`) ON UPDATE CASCADE\n) ENGINE=InnoDB AUTO_INCREMENT=16050 DEFAULT CHARSET=utf8", "len": 940, "last": 940, "list": {"@type": "PhpMyAdmin\\SqlParser\\TokensList", "tokens": [{"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CREATE", "value": "CREATE", "keyword": "CREATE", "type": 1, "flags": 3, "position": 0}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 6}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "TABLE", "value": "TABLE", "keyword": "TABLE", "type": 1, "flags": 3, "position": 7}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 12}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`payment`", "value": "payment", "keyword": null, "type": 8, "flags": 2, "position": 13}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 22}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 23}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 24}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`payment_id`", "value": "payment_id", "keyword": null, "type": 8, "flags": 2, "position": 27}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 39}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "smallint", "value": "SMALLINT", "keyword": "SMALLINT", "type": 1, "flags": 11, "position": 40}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 48}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "5", "value": 5, "keyword": null, "type": 6, "flags": 0, "position": 49}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 50}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 51}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "unsigned", "value": "UNSIGNED", "keyword": "UNSIGNED", "type": 1, "flags": 3, "position": 52}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 60}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NOT NULL", "value": "NOT NULL", "keyword": "NOT NULL", "type": 1, "flags": 7, "position": 61}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 69}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AUTO_INCREMENT", "value": "AUTO_INCREMENT", "keyword": "AUTO_INCREMENT", "type": 1, "flags": 1, "position": 70}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 84}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 85}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`customer_id`", "value": "customer_id", "keyword": null, "type": 8, "flags": 2, "position": 88}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 101}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "smallint", "value": "SMALLINT", "keyword": "SMALLINT", "type": 1, "flags": 11, "position": 102}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 110}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "5", "value": 5, "keyword": null, "type": 6, "flags": 0, "position": 111}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 112}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 113}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "unsigned", "value": "UNSIGNED", "keyword": "UNSIGNED", "type": 1, "flags": 3, "position": 114}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 122}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NOT NULL", "value": "NOT NULL", "keyword": "NOT NULL", "type": 1, "flags": 7, "position": 123}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 131}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 132}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`staff_id`", "value": "staff_id", "keyword": null, "type": 8, "flags": 2, "position": 135}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 145}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "tinyint", "value": "TINYINT", "keyword": "TINYINT", "type": 1, "flags": 11, "position": 146}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 153}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3", "value": 3, "keyword": null, "type": 6, "flags": 0, "position": 154}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 155}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 156}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "unsigned", "value": "UNSIGNED", "keyword": "UNSIGNED", "type": 1, "flags": 3, "position": 157}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 165}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NOT NULL", "value": "NOT NULL", "keyword": "NOT NULL", "type": 1, "flags": 7, "position": 166}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 174}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 175}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`rental_id`", "value": "rental_id", "keyword": null, "type": 8, "flags": 2, "position": 178}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 189}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "int", "value": "INT", "keyword": "INT", "type": 1, "flags": 11, "position": 190}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 193}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "11", "value": 11, "keyword": null, "type": 6, "flags": 0, "position": 194}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 196}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 197}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DEFAULT", "value": "DEFAULT", "keyword": "DEFAULT", "type": 1, "flags": 35, "position": 198}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 205}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NULL", "value": "NULL", "keyword": "NULL", "type": 1, "flags": 3, "position": 206}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 210}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 211}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`amount`", "value": "amount", "keyword": null, "type": 8, "flags": 2, "position": 214}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 222}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "decimal", "value": "DECIMAL", "keyword": "DECIMAL", "type": 1, "flags": 11, "position": 223}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 230}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "5", "value": 5, "keyword": null, "type": 6, "flags": 0, "position": 231}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 232}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2", "value": 2, "keyword": null, "type": 6, "flags": 0, "position": 233}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 234}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 235}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NOT NULL", "value": "NOT NULL", "keyword": "NOT NULL", "type": 1, "flags": 7, "position": 236}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 244}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 245}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`payment_date`", "value": "payment_date", "keyword": null, "type": 8, "flags": 2, "position": 248}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 262}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "datetime", "value": "datetime", "keyword": "DATETIME", "type": 1, "flags": 9, "position": 263}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 271}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NOT NULL", "value": "NOT NULL", "keyword": "NOT NULL", "type": 1, "flags": 7, "position": 272}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 280}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 281}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`last_update`", "value": "last_update", "keyword": null, "type": 8, "flags": 2, "position": 284}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 297}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "timestamp", "value": "timestamp", "keyword": "TIMESTAMP", "type": 1, "flags": 41, "position": 298}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 307}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NOT NULL", "value": "NOT NULL", "keyword": "NOT NULL", "type": 1, "flags": 7, "position": 308}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 316}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DEFAULT", "value": "DEFAULT", "keyword": "DEFAULT", "type": 1, "flags": 35, "position": 317}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 324}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CURRENT_TIMESTAMP", "value": "CURRENT_TIMESTAMP", "keyword": "CURRENT_TIMESTAMP", "type": 1, "flags": 35, "position": 325}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 342}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON UPDATE", "value": "ON UPDATE", "keyword": "ON UPDATE", "type": 1, "flags": 7, "position": 343}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 352}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CURRENT_TIMESTAMP", "value": "CURRENT_TIMESTAMP", "keyword": "CURRENT_TIMESTAMP", "type": 1, "flags": 35, "position": 353}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 370}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 371}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "PRIMARY KEY", "value": "PRIMARY KEY", "keyword": "PRIMARY KEY", "type": 1, "flags": 23, "position": 374}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 385}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 386}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`payment_id`", "value": "payment_id", "keyword": null, "type": 8, "flags": 2, "position": 387}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 399}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 400}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 401}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "KEY", "value": "KEY", "keyword": "KEY", "type": 1, "flags": 19, "position": 404}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 407}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`idx_fk_staff_id`", "value": "idx_fk_staff_id", "keyword": null, "type": 8, "flags": 2, "position": 408}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 425}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 426}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`staff_id`", "value": "staff_id", "keyword": null, "type": 8, "flags": 2, "position": 427}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 437}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 438}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 439}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "KEY", "value": "KEY", "keyword": "KEY", "type": 1, "flags": 19, "position": 442}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 445}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`idx_fk_customer_id`", "value": "idx_fk_customer_id", "keyword": null, "type": 8, "flags": 2, "position": 446}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 466}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 467}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`customer_id`", "value": "customer_id", "keyword": null, "type": 8, "flags": 2, "position": 468}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 481}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 482}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 483}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "KEY", "value": "KEY", "keyword": "KEY", "type": 1, "flags": 19, "position": 486}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 489}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`fk_payment_rental`", "value": "fk_payment_rental", "keyword": null, "type": 8, "flags": 2, "position": 490}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 509}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 510}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`rental_id`", "value": "rental_id", "keyword": null, "type": 8, "flags": 2, "position": 511}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 522}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 523}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 524}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CONSTRAINT", "value": "CONSTRAINT", "keyword": "CONSTRAINT", "type": 1, "flags": 3, "position": 527}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 537}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`fk_payment_customer`", "value": "fk_payment_customer", "keyword": null, "type": 8, "flags": 2, "position": 538}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 559}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FOREIGN KEY", "value": "FOREIGN KEY", "keyword": "FOREIGN KEY", "type": 1, "flags": 23, "position": 560}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 571}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 572}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`customer_id`", "value": "customer_id", "keyword": null, "type": 8, "flags": 2, "position": 573}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 586}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 587}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "REFERENCES", "value": "REFERENCES", "keyword": "REFERENCES", "type": 1, "flags": 3, "position": 588}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 598}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`customer`", "value": "customer", "keyword": null, "type": 8, "flags": 2, "position": 599}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 609}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 610}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`customer_id`", "value": "customer_id", "keyword": null, "type": 8, "flags": 2, "position": 611}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 624}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 625}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON UPDATE", "value": "ON UPDATE", "keyword": "ON UPDATE", "type": 1, "flags": 7, "position": 626}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 635}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CASCADE", "value": "CASCADE", "keyword": "CASCADE", "type": 1, "flags": 3, "position": 636}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 643}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 644}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CONSTRAINT", "value": "CONSTRAINT", "keyword": "CONSTRAINT", "type": 1, "flags": 3, "position": 647}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 657}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`fk_payment_rental`", "value": "fk_payment_rental", "keyword": null, "type": 8, "flags": 2, "position": 658}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 677}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FOREIGN KEY", "value": "FOREIGN KEY", "keyword": "FOREIGN KEY", "type": 1, "flags": 23, "position": 678}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 689}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 690}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`rental_id`", "value": "rental_id", "keyword": null, "type": 8, "flags": 2, "position": 691}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 702}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 703}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "REFERENCES", "value": "REFERENCES", "keyword": "REFERENCES", "type": 1, "flags": 3, "position": 704}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 714}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`rental`", "value": "rental", "keyword": null, "type": 8, "flags": 2, "position": 715}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 723}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 724}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`rental_id`", "value": "rental_id", "keyword": null, "type": 8, "flags": 2, "position": 725}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 736}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 737}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON DELETE", "value": "ON DELETE", "keyword": "ON DELETE", "type": 1, "flags": 7, "position": 738}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 747}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SET NULL", "value": "SET NULL", "keyword": "SET NULL", "type": 1, "flags": 7, "position": 748}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 756}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON UPDATE", "value": "ON UPDATE", "keyword": "ON UPDATE", "type": 1, "flags": 7, "position": 757}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 766}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CASCADE", "value": "CASCADE", "keyword": "CASCADE", "type": 1, "flags": 3, "position": 767}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 774}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 775}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CONSTRAINT", "value": "CONSTRAINT", "keyword": "CONSTRAINT", "type": 1, "flags": 3, "position": 778}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 788}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`fk_payment_staff`", "value": "fk_payment_staff", "keyword": null, "type": 8, "flags": 2, "position": 789}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 807}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FOREIGN KEY", "value": "FOREIGN KEY", "keyword": "FOREIGN KEY", "type": 1, "flags": 23, "position": 808}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 819}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 820}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`staff_id`", "value": "staff_id", "keyword": null, "type": 8, "flags": 2, "position": 821}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 831}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 832}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "REFERENCES", "value": "REFERENCES", "keyword": "REFERENCES", "type": 1, "flags": 3, "position": 833}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 843}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`staff`", "value": "staff", "keyword": null, "type": 8, "flags": 2, "position": 844}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 851}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 852}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`staff_id`", "value": "staff_id", "keyword": null, "type": 8, "flags": 2, "position": 853}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 863}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 864}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON UPDATE", "value": "ON UPDATE", "keyword": "ON UPDATE", "type": 1, "flags": 7, "position": 865}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 874}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CASCADE", "value": "CASCADE", "keyword": "CASCADE", "type": 1, "flags": 3, "position": 875}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 882}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 883}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 884}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ENGINE", "value": "ENGINE", "keyword": "ENGINE", "type": 1, "flags": 1, "position": 885}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 891}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "InnoDB", "value": "InnoDB", "keyword": null, "type": 0, "flags": 0, "position": 892}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 898}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AUTO_INCREMENT", "value": "AUTO_INCREMENT", "keyword": "AUTO_INCREMENT", "type": 1, "flags": 1, "position": 899}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 913}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "16050", "value": 16050, "keyword": null, "type": 6, "flags": 0, "position": 914}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 919}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DEFAULT CHARSET", "value": "DEFAULT CHARSET", "keyword": "DEFAULT CHARSET", "type": 1, "flags": 7, "position": 920}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 935}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "utf8", "value": "utf8", "keyword": null, "type": 0, "flags": 0, "position": 936}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": null, "value": null, "keyword": null, "type": 9, "flags": 0, "position": null}], "count": 212, "idx": 212}, "DEFAULT_DELIMITER": ";", "delimiter": ";", "delimiterLen": 1, "strict": false, "errors": []}, "parser": {"@type": "PhpMyAdmin\\SqlParser\\Parser", "STATEMENT_PARSERS": {"DESCRIBE": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "DESC": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "EXPLAIN": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "FLUSH": "", "GRANT": "", "HELP": "", "SET PASSWORD": "", "STATUS": "", "USE": "", "ANALYZE": "PhpMyAdmin\\SqlParser\\Statements\\AnalyzeStatement", "BACKUP": "PhpMyAdmin\\SqlParser\\Statements\\BackupStatement", "CHECK": "PhpMyAdmin\\SqlParser\\Statements\\CheckStatement", "CHECKSUM": "PhpMyAdmin\\SqlParser\\Statements\\ChecksumStatement", "OPTIMIZE": "PhpMyAdmin\\SqlParser\\Statements\\OptimizeStatement", "REPAIR": "PhpMyAdmin\\SqlParser\\Statements\\RepairStatement", "RESTORE": "PhpMyAdmin\\SqlParser\\Statements\\RestoreStatement", "SET": "PhpMyAdmin\\SqlParser\\Statements\\SetStatement", "SHOW": "PhpMyAdmin\\SqlParser\\Statements\\ShowStatement", "ALTER": "PhpMyAdmin\\SqlParser\\Statements\\AlterStatement", "CREATE": "PhpMyAdmin\\SqlParser\\Statements\\CreateStatement", "DROP": "PhpMyAdmin\\SqlParser\\Statements\\DropStatement", "RENAME": "PhpMyAdmin\\SqlParser\\Statements\\RenameStatement", "TRUNCATE": "PhpMyAdmin\\SqlParser\\Statements\\TruncateStatement", "CALL": "PhpMyAdmin\\SqlParser\\Statements\\CallStatement", "DELETE": "PhpMyAdmin\\SqlParser\\Statements\\DeleteStatement", "DO": "", "HANDLER": "", "INSERT": "PhpMyAdmin\\SqlParser\\Statements\\InsertStatement", "LOAD DATA": "PhpMyAdmin\\SqlParser\\Statements\\LoadStatement", "REPLACE": "PhpMyAdmin\\SqlParser\\Statements\\ReplaceStatement", "SELECT": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "UPDATE": "PhpMyAdmin\\SqlParser\\Statements\\UpdateStatement", "WITH": "PhpMyAdmin\\SqlParser\\Statements\\WithStatement", "DEALLOCATE": "", "EXECUTE": "", "PREPARE": "", "BEGIN": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "COMMIT": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "ROLLBACK": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "START TRANSACTION": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "PURGE": "PhpMyAdmin\\SqlParser\\Statements\\PurgeStatement", "LOCK": "PhpMyAdmin\\SqlParser\\Statements\\LockStatement", "UNLOCK": "PhpMyAdmin\\SqlParser\\Statements\\LockStatement"}, "KEYWORD_PARSERS": {"PARTITION BY": [], "SUBPARTITION BY": [], "_OPTIONS": {"class": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "field": "options"}, "_END_OPTIONS": {"class": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "field": "end_options"}, "INTERSECT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "EXCEPT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION ALL": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION DISTINCT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "ALTER": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "ANALYZE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "BACKUP": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CALL": {"class": "PhpMyAdmin\\SqlParser\\Components\\FunctionCall", "field": "call"}, "CHECK": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CHECKSUM": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CROSS JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "DROP": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "fields", "options": {"parseField": "table"}}, "FORCE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "FROM": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "from", "options": {"field": "table"}}, "GROUP BY": {"class": "PhpMyAdmin\\SqlParser\\Components\\GroupKeyword", "field": "group"}, "HAVING": {"class": "PhpMyAdmin\\SqlParser\\Components\\Condition", "field": "having"}, "IGNORE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "INTO": {"class": "PhpMyAdmin\\SqlParser\\Components\\IntoKeyword", "field": "into"}, "JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LEFT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LEFT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "ON": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "RIGHT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "RIGHT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "INNER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "FULL JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "FULL OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL LEFT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL RIGHT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL LEFT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL RIGHT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "STRAIGHT_JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LIMIT": {"class": "PhpMyAdmin\\SqlParser\\Components\\Limit", "field": "limit"}, "OPTIMIZE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "ORDER BY": {"class": "PhpMyAdmin\\SqlParser\\Components\\OrderKeyword", "field": "order"}, "PARTITION": {"class": "PhpMyAdmin\\SqlParser\\Components\\ArrayObj", "field": "partition"}, "PROCEDURE": {"class": "PhpMyAdmin\\SqlParser\\Components\\FunctionCall", "field": "procedure"}, "RENAME": {"class": "PhpMyAdmin\\SqlParser\\Components\\RenameOperation", "field": "renames"}, "REPAIR": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "RESTORE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "SET": {"class": "PhpMyAdmin\\SqlParser\\Components\\SetOperation", "field": "set"}, "SELECT": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "expr"}, "TRUNCATE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "UPDATE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "USE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "VALUE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Array2d", "field": "values"}, "VALUES": {"class": "PhpMyAdmin\\SqlParser\\Components\\Array2d", "field": "values"}, "WHERE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Condition", "field": "where"}}, "list": {"@type": "@1"}, "statements": [{"@type": "PhpMyAdmin\\SqlParser\\Statements\\CreateStatement", "OPTIONS": {"TEMPORARY": 1, "OR REPLACE": 2, "ALGORITHM": [3, "var="], "DEFINER": [4, "expr="], "SQL SECURITY": [5, "var"], "DATABASE": 6, "EVENT": 6, "FUNCTION": 6, "INDEX": 6, "UNIQUE INDEX": 6, "FULLTEXT INDEX": 6, "SPATIAL INDEX": 6, "PROCEDURE": 6, "SERVER": 6, "TABLE": 6, "TABLESPACE": 6, "TRIGGER": 6, "USER": 6, "VIEW": 6, "SCHEMA": 6, "IF NOT EXISTS": 7}, "DB_OPTIONS": {"CHARACTER SET": [1, "var="], "CHARSET": [1, "var="], "DEFAULT CHARACTER SET": [1, "var="], "DEFAULT CHARSET": [1, "var="], "DEFAULT COLLATE": [2, "var="], "COLLATE": [2, "var="]}, "TABLE_OPTIONS": {"ENGINE": [1, "var="], "AUTO_INCREMENT": [2, "var="], "AVG_ROW_LENGTH": [3, "var"], "CHARACTER SET": [4, "var="], "CHARSET": [4, "var="], "DEFAULT CHARACTER SET": [4, "var="], "DEFAULT CHARSET": [4, "var="], "CHECKSUM": [5, "var"], "DEFAULT COLLATE": [6, "var="], "COLLATE": [6, "var="], "COMMENT": [7, "var="], "CONNECTION": [8, "var"], "DATA DIRECTORY": [9, "var"], "DELAY_KEY_WRITE": [10, "var"], "INDEX DIRECTORY": [11, "var"], "INSERT_METHOD": [12, "var"], "KEY_BLOCK_SIZE": [13, "var"], "MAX_ROWS": [14, "var"], "MIN_ROWS": [15, "var"], "PACK_KEYS": [16, "var"], "PASSWORD": [17, "var"], "ROW_FORMAT": [18, "var"], "TABLESPACE": [19, "var"], "STORAGE": [20, "var"], "UNION": [21, "var"], "PAGE_COMPRESSED": [22, "var"], "PAGE_COMPRESSION_LEVEL": [23, "var"]}, "FUNC_OPTIONS": {"NOT": [2, "var"], "FUNCTION": [3, "var="], "PROCEDURE": [3, "var="], "CONTAINS SQL": 4, "NO SQL": 4, "READS SQL DATA": 4, "MODIFIES SQL DATA": 4, "SQL SECURITY": [6, "var"], "LANGUAGE": [7, "var"], "COMMENT": [8, "var"], "CREATE": 1, "DETERMINISTIC": 2}, "TRIGGER_OPTIONS": {"BEFORE": 1, "AFTER": 1, "INSERT": 2, "UPDATE": 2, "DELETE": 2}, "name": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "payment", "column": null, "expr": "`payment`", "alias": null, "function": null, "subquery": null}, "entityOptions": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"1": {"name": "ENGINE", "equals": true, "expr": "InnoDB", "value": "InnoDB"}, "2": {"name": "AUTO_INCREMENT", "equals": true, "expr": "16050", "value": "16050"}, "4": {"name": "DEFAULT CHARSET", "equals": true, "expr": "utf8", "value": "utf8"}}}, "fields": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "payment_id", "isConstraint": null, "type": {"@type": "PhpMyAdmin\\SqlParser\\Components\\DataType", "DATA_TYPE_OPTIONS": {"BINARY": 1, "CHARACTER SET": [2, "var"], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "UNSIGNED": 4, "ZEROFILL": 5}, "name": "SMALLINT", "parameters": ["5"], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"4": "UNSIGNED"}}}, "key": null, "references": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"1": "NOT NULL", "3": "AUTO_INCREMENT"}}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "customer_id", "isConstraint": null, "type": {"@type": "PhpMyAdmin\\SqlParser\\Components\\DataType", "DATA_TYPE_OPTIONS": {"BINARY": 1, "CHARACTER SET": [2, "var"], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "UNSIGNED": 4, "ZEROFILL": 5}, "name": "SMALLINT", "parameters": ["5"], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"4": "UNSIGNED"}}}, "key": null, "references": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"1": "NOT NULL"}}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "staff_id", "isConstraint": null, "type": {"@type": "PhpMyAdmin\\SqlParser\\Components\\DataType", "DATA_TYPE_OPTIONS": {"BINARY": 1, "CHARACTER SET": [2, "var"], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "UNSIGNED": 4, "ZEROFILL": 5}, "name": "TINYINT", "parameters": ["3"], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"4": "UNSIGNED"}}}, "key": null, "references": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"1": "NOT NULL"}}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "rental_id", "isConstraint": null, "type": {"@type": "PhpMyAdmin\\SqlParser\\Components\\DataType", "DATA_TYPE_OPTIONS": {"BINARY": 1, "CHARACTER SET": [2, "var"], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "UNSIGNED": 4, "ZEROFILL": 5}, "name": "INT", "parameters": ["11"], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "key": null, "references": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"2": {"name": "DEFAULT", "equals": false, "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "NULL", "alias": null, "function": null, "subquery": null}, "value": "NULL"}}}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "amount", "isConstraint": null, "type": {"@type": "PhpMyAdmin\\SqlParser\\Components\\DataType", "DATA_TYPE_OPTIONS": {"BINARY": 1, "CHARACTER SET": [2, "var"], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "UNSIGNED": 4, "ZEROFILL": 5}, "name": "DECIMAL", "parameters": ["5", "2"], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "key": null, "references": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"1": "NOT NULL"}}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "payment_date", "isConstraint": null, "type": {"@type": "PhpMyAdmin\\SqlParser\\Components\\DataType", "DATA_TYPE_OPTIONS": {"BINARY": 1, "CHARACTER SET": [2, "var"], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "UNSIGNED": 4, "ZEROFILL": 5}, "name": "DATETIME", "parameters": [], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "key": null, "references": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"1": "NOT NULL"}}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "last_update", "isConstraint": null, "type": {"@type": "PhpMyAdmin\\SqlParser\\Components\\DataType", "DATA_TYPE_OPTIONS": {"BINARY": 1, "CHARACTER SET": [2, "var"], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "UNSIGNED": 4, "ZEROFILL": 5}, "name": "TIMESTAMP", "parameters": [], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "key": null, "references": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"1": "NOT NULL", "2": {"name": "DEFAULT", "equals": false, "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "CURRENT_TIMESTAMP", "alias": null, "function": null, "subquery": null}, "value": "CURRENT_TIMESTAMP"}, "7": {"name": "ON UPDATE", "equals": false, "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "CURRENT_TIMESTAMP", "alias": null, "function": null, "subquery": null}, "value": "CURRENT_TIMESTAMP"}}}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": null, "isConstraint": null, "type": null, "key": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Key", "KEY_OPTIONS": {"KEY_BLOCK_SIZE": [1, "var="], "USING": [2, "var"], "WITH PARSER": [3, "var"], "COMMENT": [4, "var"], "CLUSTERING": [4, "var="], "ENGINE_ATTRIBUTE": [5, "var="], "SECONDARY_ENGINE_ATTRIBUTE": [5, "var="], "VISIBLE": 6, "INVISIBLE": 6, "IGNORED": 10, "NOT IGNORED": 10}, "name": null, "columns": [{"name": "payment_id"}], "type": "PRIMARY KEY", "expr": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "references": null, "options": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": null, "isConstraint": null, "type": null, "key": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Key", "KEY_OPTIONS": {"KEY_BLOCK_SIZE": [1, "var="], "USING": [2, "var"], "WITH PARSER": [3, "var"], "COMMENT": [4, "var"], "CLUSTERING": [4, "var="], "ENGINE_ATTRIBUTE": [5, "var="], "SECONDARY_ENGINE_ATTRIBUTE": [5, "var="], "VISIBLE": 6, "INVISIBLE": 6, "IGNORED": 10, "NOT IGNORED": 10}, "name": "idx_fk_staff_id", "columns": [{"name": "staff_id"}], "type": "KEY", "expr": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "references": null, "options": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": null, "isConstraint": null, "type": null, "key": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Key", "KEY_OPTIONS": {"KEY_BLOCK_SIZE": [1, "var="], "USING": [2, "var"], "WITH PARSER": [3, "var"], "COMMENT": [4, "var"], "CLUSTERING": [4, "var="], "ENGINE_ATTRIBUTE": [5, "var="], "SECONDARY_ENGINE_ATTRIBUTE": [5, "var="], "VISIBLE": 6, "INVISIBLE": 6, "IGNORED": 10, "NOT IGNORED": 10}, "name": "idx_fk_customer_id", "columns": [{"name": "customer_id"}], "type": "KEY", "expr": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "references": null, "options": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": null, "isConstraint": null, "type": null, "key": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Key", "KEY_OPTIONS": {"KEY_BLOCK_SIZE": [1, "var="], "USING": [2, "var"], "WITH PARSER": [3, "var"], "COMMENT": [4, "var"], "CLUSTERING": [4, "var="], "ENGINE_ATTRIBUTE": [5, "var="], "SECONDARY_ENGINE_ATTRIBUTE": [5, "var="], "VISIBLE": 6, "INVISIBLE": 6, "IGNORED": 10, "NOT IGNORED": 10}, "name": "fk_payment_rental", "columns": [{"name": "rental_id"}], "type": "KEY", "expr": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "references": null, "options": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "fk_payment_customer", "isConstraint": true, "type": null, "key": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Key", "KEY_OPTIONS": {"KEY_BLOCK_SIZE": [1, "var="], "USING": [2, "var"], "WITH PARSER": [3, "var"], "COMMENT": [4, "var"], "CLUSTERING": [4, "var="], "ENGINE_ATTRIBUTE": [5, "var="], "SECONDARY_ENGINE_ATTRIBUTE": [5, "var="], "VISIBLE": 6, "INVISIBLE": 6, "IGNORED": 10, "NOT IGNORED": 10}, "name": null, "columns": [{"name": "customer_id"}], "type": "FOREIGN KEY", "expr": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "references": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Reference", "REFERENCES_OPTIONS": {"MATCH": [1, "var"], "ON DELETE": [2, "var"], "ON UPDATE": [3, "var"]}, "table": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "customer", "column": null, "expr": "`customer`", "alias": null, "function": null, "subquery": null}, "columns": ["customer_id"], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"3": {"name": "ON UPDATE", "equals": false, "expr": "CASCADE", "value": "CASCADE"}}}}, "options": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "fk_payment_rental", "isConstraint": true, "type": null, "key": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Key", "KEY_OPTIONS": {"KEY_BLOCK_SIZE": [1, "var="], "USING": [2, "var"], "WITH PARSER": [3, "var"], "COMMENT": [4, "var"], "CLUSTERING": [4, "var="], "ENGINE_ATTRIBUTE": [5, "var="], "SECONDARY_ENGINE_ATTRIBUTE": [5, "var="], "VISIBLE": 6, "INVISIBLE": 6, "IGNORED": 10, "NOT IGNORED": 10}, "name": null, "columns": [{"name": "rental_id"}], "type": "FOREIGN KEY", "expr": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "references": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Reference", "REFERENCES_OPTIONS": {"MATCH": [1, "var"], "ON DELETE": [2, "var"], "ON UPDATE": [3, "var"]}, "table": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "rental", "column": null, "expr": "`rental`", "alias": null, "function": null, "subquery": null}, "columns": ["rental_id"], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"2": {"name": "ON DELETE", "equals": false, "expr": "SET NULL", "value": "SET NULL"}, "3": {"name": "ON UPDATE", "equals": false, "expr": "CASCADE", "value": "CASCADE"}}}}, "options": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "fk_payment_staff", "isConstraint": true, "type": null, "key": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Key", "KEY_OPTIONS": {"KEY_BLOCK_SIZE": [1, "var="], "USING": [2, "var"], "WITH PARSER": [3, "var"], "COMMENT": [4, "var"], "CLUSTERING": [4, "var="], "ENGINE_ATTRIBUTE": [5, "var="], "SECONDARY_ENGINE_ATTRIBUTE": [5, "var="], "VISIBLE": 6, "INVISIBLE": 6, "IGNORED": 10, "NOT IGNORED": 10}, "name": null, "columns": [{"name": "staff_id"}], "type": "FOREIGN KEY", "expr": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "references": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Reference", "REFERENCES_OPTIONS": {"MATCH": [1, "var"], "ON DELETE": [2, "var"], "ON UPDATE": [3, "var"]}, "table": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "staff", "column": null, "expr": "`staff`", "alias": null, "function": null, "subquery": null}, "columns": ["staff_id"], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"3": {"name": "ON UPDATE", "equals": false, "expr": "CASCADE", "value": "CASCADE"}}}}, "options": null}], "with": null, "select": null, "like": null, "partitionBy": null, "partitionsNum": null, "subpartitionBy": null, "subpartitionsNum": null, "partitions": null, "table": null, "return": null, "parameters": null, "body": [], "CLAUSES": [], "END_OPTIONS": [], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"6": "TABLE"}}, "first": 0, "last": 211}], "brackets": 0, "strict": false, "errors": []}, "errors": {"lexer": [], "parser": []}}