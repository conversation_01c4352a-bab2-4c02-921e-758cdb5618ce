CREATE TABLE ts(id INT, purchased DATE)
PARTITION BY /* comment */ RANGE(YEAR(purchased))
PARTITIONS 3
SUBPARTITION BY HASH(TO_DAYS(purchased))
SUBPARTITIONS 2(
PARTITION p0
VALUES LESS THAN(1990)(
    SUBPARTITION s0,
    SUBPARTITION s1
),
PARTITION p1
VALUES LESS THAN(2000)(
    SUBPARTITION s2,
    SUBPARTITION s3
),
PARTITION p2
VALUES LESS THAN MAXVALUE(
    SUBPARTITION s4,
    SUBPARTITION s5
)
);