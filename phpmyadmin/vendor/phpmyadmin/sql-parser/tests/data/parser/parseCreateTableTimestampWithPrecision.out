{"query": "CREATE TABLE `aa` (\n  `id` int(11) NOT NULL,\n  `rTime` timestamp(3) NOT NULL DEFAULT '0000-00-00 00:00:00.000' ON UPDATE CURRENT_TIMESTAMP(3),\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB DEFAULT CHARSET=latin1;", "lexer": {"@type": "PhpMyAdmin\\SqlParser\\Lexer", "PARSER_METHODS": ["parseDelimiter", "parseWhitespace", "parseNumber", "parseComment", "parseOperator", "parseBool", "parseString", "parseSymbol", "parseKeyword", "parseLabel", "parseUnknown"], "KEYWORD_NAME_INDICATORS": ["FROM", "SET", "WHERE"], "OPERATOR_NAME_INDICATORS": [",", "."], "str": "CREATE TABLE `aa` (\n  `id` int(11) NOT NULL,\n  `rTime` timestamp(3) NOT NULL DEFAULT '0000-00-00 00:00:00.000' ON UPDATE CURRENT_TIMESTAMP(3),\n  PRIMARY KEY (`id`)\n) ENGINE=InnoDB DEFAULT CHARSET=latin1;", "len": 203, "last": 203, "list": {"@type": "PhpMyAdmin\\SqlParser\\TokensList", "tokens": [{"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CREATE", "value": "CREATE", "keyword": "CREATE", "type": 1, "flags": 3, "position": 0}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 6}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "TABLE", "value": "TABLE", "keyword": "TABLE", "type": 1, "flags": 3, "position": 7}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 12}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`aa`", "value": "aa", "keyword": null, "type": 8, "flags": 2, "position": 13}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 17}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 18}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 19}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`id`", "value": "id", "keyword": null, "type": 8, "flags": 2, "position": 22}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 26}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "int", "value": "INT", "keyword": "INT", "type": 1, "flags": 11, "position": 27}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 30}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "11", "value": 11, "keyword": null, "type": 6, "flags": 0, "position": 31}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 33}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 34}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NOT NULL", "value": "NOT NULL", "keyword": "NOT NULL", "type": 1, "flags": 7, "position": 35}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 43}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 44}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`rTime`", "value": "rTime", "keyword": null, "type": 8, "flags": 2, "position": 47}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 54}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "timestamp", "value": "timestamp", "keyword": "TIMESTAMP", "type": 1, "flags": 41, "position": 55}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 64}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3", "value": 3, "keyword": null, "type": 6, "flags": 0, "position": 65}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 66}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 67}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NOT NULL", "value": "NOT NULL", "keyword": "NOT NULL", "type": 1, "flags": 7, "position": 68}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 76}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DEFAULT", "value": "DEFAULT", "keyword": "DEFAULT", "type": 1, "flags": 35, "position": 77}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 84}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "'0000-00-00 00:00:00.000'", "value": "0000-00-00 00:00:00.000", "keyword": null, "type": 7, "flags": 1, "position": 85}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 110}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON UPDATE", "value": "ON UPDATE", "keyword": "ON UPDATE", "type": 1, "flags": 7, "position": 111}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 120}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CURRENT_TIMESTAMP", "value": "CURRENT_TIMESTAMP", "keyword": "CURRENT_TIMESTAMP", "type": 1, "flags": 35, "position": 121}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 138}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3", "value": 3, "keyword": null, "type": 6, "flags": 0, "position": 139}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 140}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 141}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 142}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "PRIMARY KEY", "value": "PRIMARY KEY", "keyword": "PRIMARY KEY", "type": 1, "flags": 23, "position": 145}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 156}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 157}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`id`", "value": "id", "keyword": null, "type": 8, "flags": 2, "position": 158}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 162}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 163}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 164}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 165}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ENGINE", "value": "ENGINE", "keyword": "ENGINE", "type": 1, "flags": 1, "position": 166}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 172}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "InnoDB", "value": "InnoDB", "keyword": null, "type": 0, "flags": 0, "position": 173}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 179}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DEFAULT CHARSET", "value": "DEFAULT CHARSET", "keyword": "DEFAULT CHARSET", "type": 1, "flags": 7, "position": 180}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 195}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "latin1", "value": "latin1", "keyword": null, "type": 0, "flags": 0, "position": 196}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 202}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": null, "value": null, "keyword": null, "type": 9, "flags": 0, "position": null}], "count": 56, "idx": 56}, "DEFAULT_DELIMITER": ";", "delimiter": ";", "delimiterLen": 1, "strict": false, "errors": []}, "parser": {"@type": "PhpMyAdmin\\SqlParser\\Parser", "STATEMENT_PARSERS": {"DESCRIBE": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "DESC": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "EXPLAIN": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "FLUSH": "", "GRANT": "", "HELP": "", "SET PASSWORD": "", "STATUS": "", "USE": "", "ANALYZE": "PhpMyAdmin\\SqlParser\\Statements\\AnalyzeStatement", "BACKUP": "PhpMyAdmin\\SqlParser\\Statements\\BackupStatement", "CHECK": "PhpMyAdmin\\SqlParser\\Statements\\CheckStatement", "CHECKSUM": "PhpMyAdmin\\SqlParser\\Statements\\ChecksumStatement", "OPTIMIZE": "PhpMyAdmin\\SqlParser\\Statements\\OptimizeStatement", "REPAIR": "PhpMyAdmin\\SqlParser\\Statements\\RepairStatement", "RESTORE": "PhpMyAdmin\\SqlParser\\Statements\\RestoreStatement", "SET": "PhpMyAdmin\\SqlParser\\Statements\\SetStatement", "SHOW": "PhpMyAdmin\\SqlParser\\Statements\\ShowStatement", "ALTER": "PhpMyAdmin\\SqlParser\\Statements\\AlterStatement", "CREATE": "PhpMyAdmin\\SqlParser\\Statements\\CreateStatement", "DROP": "PhpMyAdmin\\SqlParser\\Statements\\DropStatement", "RENAME": "PhpMyAdmin\\SqlParser\\Statements\\RenameStatement", "TRUNCATE": "PhpMyAdmin\\SqlParser\\Statements\\TruncateStatement", "CALL": "PhpMyAdmin\\SqlParser\\Statements\\CallStatement", "DELETE": "PhpMyAdmin\\SqlParser\\Statements\\DeleteStatement", "DO": "", "HANDLER": "", "INSERT": "PhpMyAdmin\\SqlParser\\Statements\\InsertStatement", "LOAD DATA": "PhpMyAdmin\\SqlParser\\Statements\\LoadStatement", "REPLACE": "PhpMyAdmin\\SqlParser\\Statements\\ReplaceStatement", "SELECT": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "UPDATE": "PhpMyAdmin\\SqlParser\\Statements\\UpdateStatement", "WITH": "PhpMyAdmin\\SqlParser\\Statements\\WithStatement", "DEALLOCATE": "", "EXECUTE": "", "PREPARE": "", "BEGIN": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "COMMIT": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "ROLLBACK": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "START TRANSACTION": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "PURGE": "PhpMyAdmin\\SqlParser\\Statements\\PurgeStatement", "LOCK": "PhpMyAdmin\\SqlParser\\Statements\\LockStatement", "UNLOCK": "PhpMyAdmin\\SqlParser\\Statements\\LockStatement"}, "KEYWORD_PARSERS": {"PARTITION BY": [], "SUBPARTITION BY": [], "_OPTIONS": {"class": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "field": "options"}, "_END_OPTIONS": {"class": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "field": "end_options"}, "INTERSECT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "EXCEPT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION ALL": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION DISTINCT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "ALTER": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "ANALYZE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "BACKUP": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CALL": {"class": "PhpMyAdmin\\SqlParser\\Components\\FunctionCall", "field": "call"}, "CHECK": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CHECKSUM": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CROSS JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "DROP": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "fields", "options": {"parseField": "table"}}, "FORCE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "FROM": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "from", "options": {"field": "table"}}, "GROUP BY": {"class": "PhpMyAdmin\\SqlParser\\Components\\GroupKeyword", "field": "group"}, "HAVING": {"class": "PhpMyAdmin\\SqlParser\\Components\\Condition", "field": "having"}, "IGNORE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "INTO": {"class": "PhpMyAdmin\\SqlParser\\Components\\IntoKeyword", "field": "into"}, "JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LEFT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LEFT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "ON": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "RIGHT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "RIGHT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "INNER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "FULL JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "FULL OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL LEFT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL RIGHT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL LEFT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL RIGHT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "STRAIGHT_JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LIMIT": {"class": "PhpMyAdmin\\SqlParser\\Components\\Limit", "field": "limit"}, "OPTIMIZE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "ORDER BY": {"class": "PhpMyAdmin\\SqlParser\\Components\\OrderKeyword", "field": "order"}, "PARTITION": {"class": "PhpMyAdmin\\SqlParser\\Components\\ArrayObj", "field": "partition"}, "PROCEDURE": {"class": "PhpMyAdmin\\SqlParser\\Components\\FunctionCall", "field": "procedure"}, "RENAME": {"class": "PhpMyAdmin\\SqlParser\\Components\\RenameOperation", "field": "renames"}, "REPAIR": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "RESTORE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "SET": {"class": "PhpMyAdmin\\SqlParser\\Components\\SetOperation", "field": "set"}, "SELECT": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "expr"}, "TRUNCATE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "UPDATE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "USE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "VALUE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Array2d", "field": "values"}, "VALUES": {"class": "PhpMyAdmin\\SqlParser\\Components\\Array2d", "field": "values"}, "WHERE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Condition", "field": "where"}}, "list": {"@type": "@1"}, "statements": [{"@type": "PhpMyAdmin\\SqlParser\\Statements\\CreateStatement", "OPTIONS": {"TEMPORARY": 1, "OR REPLACE": 2, "ALGORITHM": [3, "var="], "DEFINER": [4, "expr="], "SQL SECURITY": [5, "var"], "DATABASE": 6, "EVENT": 6, "FUNCTION": 6, "INDEX": 6, "UNIQUE INDEX": 6, "FULLTEXT INDEX": 6, "SPATIAL INDEX": 6, "PROCEDURE": 6, "SERVER": 6, "TABLE": 6, "TABLESPACE": 6, "TRIGGER": 6, "USER": 6, "VIEW": 6, "SCHEMA": 6, "IF NOT EXISTS": 7}, "DB_OPTIONS": {"CHARACTER SET": [1, "var="], "CHARSET": [1, "var="], "DEFAULT CHARACTER SET": [1, "var="], "DEFAULT CHARSET": [1, "var="], "DEFAULT COLLATE": [2, "var="], "COLLATE": [2, "var="]}, "TABLE_OPTIONS": {"ENGINE": [1, "var="], "AUTO_INCREMENT": [2, "var="], "AVG_ROW_LENGTH": [3, "var"], "CHARACTER SET": [4, "var="], "CHARSET": [4, "var="], "DEFAULT CHARACTER SET": [4, "var="], "DEFAULT CHARSET": [4, "var="], "CHECKSUM": [5, "var"], "DEFAULT COLLATE": [6, "var="], "COLLATE": [6, "var="], "COMMENT": [7, "var="], "CONNECTION": [8, "var"], "DATA DIRECTORY": [9, "var"], "DELAY_KEY_WRITE": [10, "var"], "INDEX DIRECTORY": [11, "var"], "INSERT_METHOD": [12, "var"], "KEY_BLOCK_SIZE": [13, "var"], "MAX_ROWS": [14, "var"], "MIN_ROWS": [15, "var"], "PACK_KEYS": [16, "var"], "PASSWORD": [17, "var"], "ROW_FORMAT": [18, "var"], "TABLESPACE": [19, "var"], "STORAGE": [20, "var"], "UNION": [21, "var"], "PAGE_COMPRESSED": [22, "var"], "PAGE_COMPRESSION_LEVEL": [23, "var"]}, "FUNC_OPTIONS": {"NOT": [2, "var"], "FUNCTION": [3, "var="], "PROCEDURE": [3, "var="], "CONTAINS SQL": 4, "NO SQL": 4, "READS SQL DATA": 4, "MODIFIES SQL DATA": 4, "SQL SECURITY": [6, "var"], "LANGUAGE": [7, "var"], "COMMENT": [8, "var"], "CREATE": 1, "DETERMINISTIC": 2}, "TRIGGER_OPTIONS": {"BEFORE": 1, "AFTER": 1, "INSERT": 2, "UPDATE": 2, "DELETE": 2}, "name": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "aa", "column": null, "expr": "`aa`", "alias": null, "function": null, "subquery": null}, "entityOptions": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"1": {"name": "ENGINE", "equals": true, "expr": "InnoDB", "value": "InnoDB"}, "4": {"name": "DEFAULT CHARSET", "equals": true, "expr": "latin1", "value": "latin1"}}}, "fields": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "id", "isConstraint": null, "type": {"@type": "PhpMyAdmin\\SqlParser\\Components\\DataType", "DATA_TYPE_OPTIONS": {"BINARY": 1, "CHARACTER SET": [2, "var"], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "UNSIGNED": 4, "ZEROFILL": 5}, "name": "INT", "parameters": ["11"], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "key": null, "references": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"1": "NOT NULL"}}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "rTime", "isConstraint": null, "type": {"@type": "PhpMyAdmin\\SqlParser\\Components\\DataType", "DATA_TYPE_OPTIONS": {"BINARY": 1, "CHARACTER SET": [2, "var"], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "UNSIGNED": 4, "ZEROFILL": 5}, "name": "TIMESTAMP", "parameters": ["3"], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "key": null, "references": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"1": "NOT NULL", "2": {"name": "DEFAULT", "equals": false, "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": "0000-00-00 00:00:00.000", "expr": "'0000-00-00 00:00:00.000'", "alias": null, "function": null, "subquery": null}, "value": "'0000-00-00 00:00:00.000'"}, "7": {"name": "ON UPDATE", "equals": false, "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "CURRENT_TIMESTAMP(3)", "alias": null, "function": "CURRENT_TIMESTAMP", "subquery": null}, "value": "CURRENT_TIMESTAMP(3)"}}}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": null, "isConstraint": null, "type": null, "key": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Key", "KEY_OPTIONS": {"KEY_BLOCK_SIZE": [1, "var="], "USING": [2, "var"], "WITH PARSER": [3, "var"], "COMMENT": [4, "var"], "CLUSTERING": [4, "var="], "ENGINE_ATTRIBUTE": [5, "var="], "SECONDARY_ENGINE_ATTRIBUTE": [5, "var="], "VISIBLE": 6, "INVISIBLE": 6, "IGNORED": 10, "NOT IGNORED": 10}, "name": null, "columns": [{"name": "id"}], "type": "PRIMARY KEY", "expr": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "references": null, "options": null}], "with": null, "select": null, "like": null, "partitionBy": null, "partitionsNum": null, "subpartitionBy": null, "subpartitionsNum": null, "partitions": null, "table": null, "return": null, "parameters": null, "body": [], "CLAUSES": [], "END_OPTIONS": [], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"6": "TABLE"}}, "first": 0, "last": 54}], "brackets": 0, "strict": false, "errors": []}, "errors": {"lexer": [], "parser": []}}