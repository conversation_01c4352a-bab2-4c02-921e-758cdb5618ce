{"query": "CREATE TABLE ts(id INT, purchased DATE)\nPARTITION BY /* comment */ RANGE(YEAR(purchased))\nPARTITIONS 3\nSUBPARTITION BY HASH(TO_DAYS(purchased))\nSUBPARTITIONS 2(\nPARTITION p0\nVALUES LESS THAN(1990)(\n    SUBPARTITION s0,\n    SUBPARTITION s1\n),\nPARTITION p1\nVALUES LESS THAN(2000)(\n    SUBPARTITION s2,\n    SUBPARTITION s3\n),\nPARTITION p2\nVALUES LESS THAN MAXVALUE(\n    SUBPARTITION s4,\n    SUBPARTITION s5\n)\n);", "lexer": {"@type": "PhpMyAdmin\\SqlParser\\Lexer", "PARSER_METHODS": ["parseDelimiter", "parseWhitespace", "parseNumber", "parseComment", "parseOperator", "parseBool", "parseString", "parseSymbol", "parseKeyword", "parseLabel", "parseUnknown"], "KEYWORD_NAME_INDICATORS": ["FROM", "SET", "WHERE"], "OPERATOR_NAME_INDICATORS": [",", "."], "str": "CREATE TABLE ts(id INT, purchased DATE)\nPARTITION BY /* comment */ RANGE(YEAR(purchased))\nPARTITIONS 3\nSUBPARTITION BY HASH(TO_DAYS(purchased))\nSUBPARTITIONS 2(\nPARTITION p0\nVALUES LESS THAN(1990)(\n    SUBPARTITION s0,\n    SUBPARTITION s1\n),\nPARTITION p1\nVALUES LESS THAN(2000)(\n    SUBPARTITION s2,\n    SUBPARTITION s3\n),\nPARTITION p2\nVALUES LESS THAN MAXVALUE(\n    SUBPARTITION s4,\n    SUBPARTITION s5\n)\n);", "len": 408, "last": 408, "list": {"@type": "PhpMyAdmin\\SqlParser\\TokensList", "tokens": [{"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CREATE", "value": "CREATE", "keyword": "CREATE", "type": 1, "flags": 3, "position": 0}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 6}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "TABLE", "value": "TABLE", "keyword": "TABLE", "type": 1, "flags": 3, "position": 7}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 12}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ts", "value": "ts", "keyword": null, "type": 0, "flags": 0, "position": 13}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 15}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "id", "value": "id", "keyword": null, "type": 0, "flags": 0, "position": 16}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 18}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "INT", "value": "INT", "keyword": "INT", "type": 1, "flags": 11, "position": 19}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 22}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 23}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "purchased", "value": "purchased", "keyword": null, "type": 0, "flags": 0, "position": 24}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 33}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DATE", "value": "DATE", "keyword": "DATE", "type": 1, "flags": 41, "position": 34}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 38}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 39}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "PARTITION BY", "value": "PARTITION BY", "keyword": "PARTITION BY", "type": 1, "flags": 7, "position": 40}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 52}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "/* comment */", "value": "/* comment */", "keyword": null, "type": 4, "flags": 2, "position": 53}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 66}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "RANGE", "value": "RANGE", "keyword": "RANGE", "type": 1, "flags": 3, "position": 67}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 72}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "YEAR", "value": "YEAR", "keyword": "YEAR", "type": 1, "flags": 41, "position": 73}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 77}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "purchased", "value": "purchased", "keyword": null, "type": 0, "flags": 0, "position": 78}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 87}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 88}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 89}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "PARTITIONS", "value": "PARTITIONS", "keyword": "PARTITIONS", "type": 1, "flags": 1, "position": 90}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 100}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3", "value": 3, "keyword": null, "type": 6, "flags": 0, "position": 101}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 102}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SUBPARTITION BY", "value": "SUBPARTITION BY", "keyword": "SUBPARTITION BY", "type": 1, "flags": 7, "position": 103}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 118}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "HASH", "value": "HASH", "keyword": "HASH", "type": 1, "flags": 1, "position": 119}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 123}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "TO_DAYS", "value": "TO_DAYS", "keyword": "TO_DAYS", "type": 1, "flags": 33, "position": 124}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 131}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "purchased", "value": "purchased", "keyword": null, "type": 0, "flags": 0, "position": 132}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 141}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 142}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 143}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SUBPARTITIONS", "value": "SUBPARTITIONS", "keyword": "SUBPARTITIONS", "type": 1, "flags": 1, "position": 144}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 157}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2", "value": 2, "keyword": null, "type": 6, "flags": 0, "position": 158}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 159}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 160}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "PARTITION", "value": "PARTITION", "keyword": "PARTITION", "type": 1, "flags": 3, "position": 161}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 170}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "p0", "value": "p0", "keyword": null, "type": 0, "flags": 0, "position": 171}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 173}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "VALUES", "value": "VALUES", "keyword": "VALUES", "type": 1, "flags": 35, "position": 174}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 180}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LESS THAN", "value": "LESS THAN", "keyword": "LESS THAN", "type": 1, "flags": 7, "position": 181}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 190}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1990", "value": 1990, "keyword": null, "type": 6, "flags": 0, "position": 191}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 195}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 196}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n    ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 197}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SUBPARTITION", "value": "SUBPARTITION", "keyword": "SUBPARTITION", "type": 1, "flags": 1, "position": 202}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 214}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "s0", "value": "s0", "keyword": null, "type": 0, "flags": 0, "position": 215}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 217}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n    ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 218}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SUBPARTITION", "value": "SUBPARTITION", "keyword": "SUBPARTITION", "type": 1, "flags": 1, "position": 223}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 235}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "s1", "value": "s1", "keyword": null, "type": 0, "flags": 0, "position": 236}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 238}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 239}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 240}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 241}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "PARTITION", "value": "PARTITION", "keyword": "PARTITION", "type": 1, "flags": 3, "position": 242}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 251}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "p1", "value": "p1", "keyword": null, "type": 0, "flags": 0, "position": 252}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 254}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "VALUES", "value": "VALUES", "keyword": "VALUES", "type": 1, "flags": 35, "position": 255}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 261}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LESS THAN", "value": "LESS THAN", "keyword": "LESS THAN", "type": 1, "flags": 7, "position": 262}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 271}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2000", "value": 2000, "keyword": null, "type": 6, "flags": 0, "position": 272}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 276}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 277}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n    ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 278}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SUBPARTITION", "value": "SUBPARTITION", "keyword": "SUBPARTITION", "type": 1, "flags": 1, "position": 283}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 295}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "s2", "value": "s2", "keyword": null, "type": 0, "flags": 0, "position": 296}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 298}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n    ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 299}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SUBPARTITION", "value": "SUBPARTITION", "keyword": "SUBPARTITION", "type": 1, "flags": 1, "position": 304}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 316}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "s3", "value": "s3", "keyword": null, "type": 0, "flags": 0, "position": 317}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 319}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 320}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 321}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 322}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "PARTITION", "value": "PARTITION", "keyword": "PARTITION", "type": 1, "flags": 3, "position": 323}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 332}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "p2", "value": "p2", "keyword": null, "type": 0, "flags": 0, "position": 333}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 335}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "VALUES", "value": "VALUES", "keyword": "VALUES", "type": 1, "flags": 35, "position": 336}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 342}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LESS THAN", "value": "LESS THAN", "keyword": "LESS THAN", "type": 1, "flags": 7, "position": 343}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 352}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "MAXVALUE", "value": "MAXVALUE", "keyword": "MAXVALUE", "type": 1, "flags": 3, "position": 353}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 361}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n    ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 362}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SUBPARTITION", "value": "SUBPARTITION", "keyword": "SUBPARTITION", "type": 1, "flags": 1, "position": 367}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 379}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "s4", "value": "s4", "keyword": null, "type": 0, "flags": 0, "position": 380}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 382}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n    ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 383}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SUBPARTITION", "value": "SUBPARTITION", "keyword": "SUBPARTITION", "type": 1, "flags": 1, "position": 388}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 400}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "s5", "value": "s5", "keyword": null, "type": 0, "flags": 0, "position": 401}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 403}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 404}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 405}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 406}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ";", "value": ";", "keyword": null, "type": 9, "flags": 0, "position": 407}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": null, "value": null, "keyword": null, "type": 9, "flags": 0, "position": null}], "count": 120, "idx": 120}, "DEFAULT_DELIMITER": ";", "delimiter": ";", "delimiterLen": 1, "strict": false, "errors": []}, "parser": {"@type": "PhpMyAdmin\\SqlParser\\Parser", "STATEMENT_PARSERS": {"DESCRIBE": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "DESC": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "EXPLAIN": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "FLUSH": "", "GRANT": "", "HELP": "", "SET PASSWORD": "", "STATUS": "", "USE": "", "ANALYZE": "PhpMyAdmin\\SqlParser\\Statements\\AnalyzeStatement", "BACKUP": "PhpMyAdmin\\SqlParser\\Statements\\BackupStatement", "CHECK": "PhpMyAdmin\\SqlParser\\Statements\\CheckStatement", "CHECKSUM": "PhpMyAdmin\\SqlParser\\Statements\\ChecksumStatement", "OPTIMIZE": "PhpMyAdmin\\SqlParser\\Statements\\OptimizeStatement", "REPAIR": "PhpMyAdmin\\SqlParser\\Statements\\RepairStatement", "RESTORE": "PhpMyAdmin\\SqlParser\\Statements\\RestoreStatement", "SET": "PhpMyAdmin\\SqlParser\\Statements\\SetStatement", "SHOW": "PhpMyAdmin\\SqlParser\\Statements\\ShowStatement", "ALTER": "PhpMyAdmin\\SqlParser\\Statements\\AlterStatement", "CREATE": "PhpMyAdmin\\SqlParser\\Statements\\CreateStatement", "DROP": "PhpMyAdmin\\SqlParser\\Statements\\DropStatement", "RENAME": "PhpMyAdmin\\SqlParser\\Statements\\RenameStatement", "TRUNCATE": "PhpMyAdmin\\SqlParser\\Statements\\TruncateStatement", "CALL": "PhpMyAdmin\\SqlParser\\Statements\\CallStatement", "DELETE": "PhpMyAdmin\\SqlParser\\Statements\\DeleteStatement", "DO": "", "HANDLER": "", "INSERT": "PhpMyAdmin\\SqlParser\\Statements\\InsertStatement", "LOAD DATA": "PhpMyAdmin\\SqlParser\\Statements\\LoadStatement", "REPLACE": "PhpMyAdmin\\SqlParser\\Statements\\ReplaceStatement", "SELECT": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "UPDATE": "PhpMyAdmin\\SqlParser\\Statements\\UpdateStatement", "WITH": "PhpMyAdmin\\SqlParser\\Statements\\WithStatement", "DEALLOCATE": "", "EXECUTE": "", "PREPARE": "", "BEGIN": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "COMMIT": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "ROLLBACK": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "START TRANSACTION": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "PURGE": "PhpMyAdmin\\SqlParser\\Statements\\PurgeStatement", "LOCK": "PhpMyAdmin\\SqlParser\\Statements\\LockStatement", "UNLOCK": "PhpMyAdmin\\SqlParser\\Statements\\LockStatement"}, "KEYWORD_PARSERS": {"PARTITION BY": [], "SUBPARTITION BY": [], "_OPTIONS": {"class": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "field": "options"}, "_END_OPTIONS": {"class": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "field": "end_options"}, "INTERSECT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "EXCEPT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION ALL": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION DISTINCT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "ALTER": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "ANALYZE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "BACKUP": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CALL": {"class": "PhpMyAdmin\\SqlParser\\Components\\FunctionCall", "field": "call"}, "CHECK": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CHECKSUM": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CROSS JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "DROP": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "fields", "options": {"parseField": "table"}}, "FORCE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "FROM": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "from", "options": {"field": "table"}}, "GROUP BY": {"class": "PhpMyAdmin\\SqlParser\\Components\\GroupKeyword", "field": "group"}, "HAVING": {"class": "PhpMyAdmin\\SqlParser\\Components\\Condition", "field": "having"}, "IGNORE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "INTO": {"class": "PhpMyAdmin\\SqlParser\\Components\\IntoKeyword", "field": "into"}, "JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LEFT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LEFT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "ON": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "RIGHT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "RIGHT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "INNER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "FULL JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "FULL OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL LEFT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL RIGHT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL LEFT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL RIGHT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "STRAIGHT_JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LIMIT": {"class": "PhpMyAdmin\\SqlParser\\Components\\Limit", "field": "limit"}, "OPTIMIZE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "ORDER BY": {"class": "PhpMyAdmin\\SqlParser\\Components\\OrderKeyword", "field": "order"}, "PARTITION": {"class": "PhpMyAdmin\\SqlParser\\Components\\ArrayObj", "field": "partition"}, "PROCEDURE": {"class": "PhpMyAdmin\\SqlParser\\Components\\FunctionCall", "field": "procedure"}, "RENAME": {"class": "PhpMyAdmin\\SqlParser\\Components\\RenameOperation", "field": "renames"}, "REPAIR": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "RESTORE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "SET": {"class": "PhpMyAdmin\\SqlParser\\Components\\SetOperation", "field": "set"}, "SELECT": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "expr"}, "TRUNCATE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "UPDATE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "USE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "VALUE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Array2d", "field": "values"}, "VALUES": {"class": "PhpMyAdmin\\SqlParser\\Components\\Array2d", "field": "values"}, "WHERE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Condition", "field": "where"}}, "list": {"@type": "@1"}, "statements": [{"@type": "PhpMyAdmin\\SqlParser\\Statements\\CreateStatement", "OPTIONS": {"TEMPORARY": 1, "OR REPLACE": 2, "ALGORITHM": [3, "var="], "DEFINER": [4, "expr="], "SQL SECURITY": [5, "var"], "DATABASE": 6, "EVENT": 6, "FUNCTION": 6, "INDEX": 6, "UNIQUE INDEX": 6, "FULLTEXT INDEX": 6, "SPATIAL INDEX": 6, "PROCEDURE": 6, "SERVER": 6, "TABLE": 6, "TABLESPACE": 6, "TRIGGER": 6, "USER": 6, "VIEW": 6, "SCHEMA": 6, "IF NOT EXISTS": 7}, "DB_OPTIONS": {"CHARACTER SET": [1, "var="], "CHARSET": [1, "var="], "DEFAULT CHARACTER SET": [1, "var="], "DEFAULT CHARSET": [1, "var="], "DEFAULT COLLATE": [2, "var="], "COLLATE": [2, "var="]}, "TABLE_OPTIONS": {"ENGINE": [1, "var="], "AUTO_INCREMENT": [2, "var="], "AVG_ROW_LENGTH": [3, "var"], "CHARACTER SET": [4, "var="], "CHARSET": [4, "var="], "DEFAULT CHARACTER SET": [4, "var="], "DEFAULT CHARSET": [4, "var="], "CHECKSUM": [5, "var"], "DEFAULT COLLATE": [6, "var="], "COLLATE": [6, "var="], "COMMENT": [7, "var="], "CONNECTION": [8, "var"], "DATA DIRECTORY": [9, "var"], "DELAY_KEY_WRITE": [10, "var"], "INDEX DIRECTORY": [11, "var"], "INSERT_METHOD": [12, "var"], "KEY_BLOCK_SIZE": [13, "var"], "MAX_ROWS": [14, "var"], "MIN_ROWS": [15, "var"], "PACK_KEYS": [16, "var"], "PASSWORD": [17, "var"], "ROW_FORMAT": [18, "var"], "TABLESPACE": [19, "var"], "STORAGE": [20, "var"], "UNION": [21, "var"], "PAGE_COMPRESSED": [22, "var"], "PAGE_COMPRESSION_LEVEL": [23, "var"]}, "FUNC_OPTIONS": {"NOT": [2, "var"], "FUNCTION": [3, "var="], "PROCEDURE": [3, "var="], "CONTAINS SQL": 4, "NO SQL": 4, "READS SQL DATA": 4, "MODIFIES SQL DATA": 4, "SQL SECURITY": [6, "var"], "LANGUAGE": [7, "var"], "COMMENT": [8, "var"], "CREATE": 1, "DETERMINISTIC": 2}, "TRIGGER_OPTIONS": {"BEFORE": 1, "AFTER": 1, "INSERT": 2, "UPDATE": 2, "DELETE": 2}, "name": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "ts", "column": null, "expr": "ts", "alias": null, "function": null, "subquery": null}, "entityOptions": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "fields": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "id", "isConstraint": null, "type": {"@type": "PhpMyAdmin\\SqlParser\\Components\\DataType", "DATA_TYPE_OPTIONS": {"BINARY": 1, "CHARACTER SET": [2, "var"], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "UNSIGNED": 4, "ZEROFILL": 5}, "name": "INT", "parameters": [], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "key": null, "references": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "purchased", "isConstraint": null, "type": {"@type": "PhpMyAdmin\\SqlParser\\Components\\DataType", "DATA_TYPE_OPTIONS": {"BINARY": 1, "CHARACTER SET": [2, "var"], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "UNSIGNED": 4, "ZEROFILL": 5}, "name": "DATE", "parameters": [], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "key": null, "references": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}], "with": null, "select": null, "like": null, "partitionBy": "RANGE(YEAR(purchased))", "partitionsNum": 3, "subpartitionBy": "HASH(TO_DAYS(purchased))", "subpartitionsNum": 2, "partitions": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\PartitionDefinition", "OPTIONS": {"STORAGE ENGINE": [1, "var"], "ENGINE": [1, "var"], "COMMENT": [2, "var"], "DATA DIRECTORY": [3, "var"], "INDEX DIRECTORY": [4, "var"], "MAX_ROWS": [5, "var"], "MIN_ROWS": [6, "var"], "TABLESPACE": [7, "var"], "NODEGROUP": [8, "var"]}, "isSubpartition": false, "name": "p0", "type": "LESS THAN", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "(1990)", "alias": null, "function": null, "subquery": null}, "subpartitions": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\PartitionDefinition", "OPTIONS": {"STORAGE ENGINE": [1, "var"], "ENGINE": [1, "var"], "COMMENT": [2, "var"], "DATA DIRECTORY": [3, "var"], "INDEX DIRECTORY": [4, "var"], "MAX_ROWS": [5, "var"], "MIN_ROWS": [6, "var"], "TABLESPACE": [7, "var"], "NODEGROUP": [8, "var"]}, "isSubpartition": true, "name": "s0", "type": null, "expr": null, "subpartitions": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\PartitionDefinition", "OPTIONS": {"STORAGE ENGINE": [1, "var"], "ENGINE": [1, "var"], "COMMENT": [2, "var"], "DATA DIRECTORY": [3, "var"], "INDEX DIRECTORY": [4, "var"], "MAX_ROWS": [5, "var"], "MIN_ROWS": [6, "var"], "TABLESPACE": [7, "var"], "NODEGROUP": [8, "var"]}, "isSubpartition": true, "name": "s1", "type": null, "expr": null, "subpartitions": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\PartitionDefinition", "OPTIONS": {"STORAGE ENGINE": [1, "var"], "ENGINE": [1, "var"], "COMMENT": [2, "var"], "DATA DIRECTORY": [3, "var"], "INDEX DIRECTORY": [4, "var"], "MAX_ROWS": [5, "var"], "MIN_ROWS": [6, "var"], "TABLESPACE": [7, "var"], "NODEGROUP": [8, "var"]}, "isSubpartition": false, "name": "p1", "type": "LESS THAN", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "(2000)", "alias": null, "function": null, "subquery": null}, "subpartitions": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\PartitionDefinition", "OPTIONS": {"STORAGE ENGINE": [1, "var"], "ENGINE": [1, "var"], "COMMENT": [2, "var"], "DATA DIRECTORY": [3, "var"], "INDEX DIRECTORY": [4, "var"], "MAX_ROWS": [5, "var"], "MIN_ROWS": [6, "var"], "TABLESPACE": [7, "var"], "NODEGROUP": [8, "var"]}, "isSubpartition": true, "name": "s2", "type": null, "expr": null, "subpartitions": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\PartitionDefinition", "OPTIONS": {"STORAGE ENGINE": [1, "var"], "ENGINE": [1, "var"], "COMMENT": [2, "var"], "DATA DIRECTORY": [3, "var"], "INDEX DIRECTORY": [4, "var"], "MAX_ROWS": [5, "var"], "MIN_ROWS": [6, "var"], "TABLESPACE": [7, "var"], "NODEGROUP": [8, "var"]}, "isSubpartition": true, "name": "s3", "type": null, "expr": null, "subpartitions": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\PartitionDefinition", "OPTIONS": {"STORAGE ENGINE": [1, "var"], "ENGINE": [1, "var"], "COMMENT": [2, "var"], "DATA DIRECTORY": [3, "var"], "INDEX DIRECTORY": [4, "var"], "MAX_ROWS": [5, "var"], "MIN_ROWS": [6, "var"], "TABLESPACE": [7, "var"], "NODEGROUP": [8, "var"]}, "isSubpartition": false, "name": "p2", "type": "LESS THAN", "expr": "MAXVALUE", "subpartitions": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\PartitionDefinition", "OPTIONS": {"STORAGE ENGINE": [1, "var"], "ENGINE": [1, "var"], "COMMENT": [2, "var"], "DATA DIRECTORY": [3, "var"], "INDEX DIRECTORY": [4, "var"], "MAX_ROWS": [5, "var"], "MIN_ROWS": [6, "var"], "TABLESPACE": [7, "var"], "NODEGROUP": [8, "var"]}, "isSubpartition": true, "name": "s4", "type": null, "expr": null, "subpartitions": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\PartitionDefinition", "OPTIONS": {"STORAGE ENGINE": [1, "var"], "ENGINE": [1, "var"], "COMMENT": [2, "var"], "DATA DIRECTORY": [3, "var"], "INDEX DIRECTORY": [4, "var"], "MAX_ROWS": [5, "var"], "MIN_ROWS": [6, "var"], "TABLESPACE": [7, "var"], "NODEGROUP": [8, "var"]}, "isSubpartition": true, "name": "s5", "type": null, "expr": null, "subpartitions": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}], "table": null, "return": null, "parameters": null, "body": [], "CLAUSES": [], "END_OPTIONS": [], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"6": "TABLE"}}, "first": 0, "last": 117}], "brackets": 0, "strict": false, "errors": []}, "errors": {"lexer": [], "parser": []}}