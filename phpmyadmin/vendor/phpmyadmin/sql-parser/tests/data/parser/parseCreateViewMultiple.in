CREATE TABLE T1 (COL1 CHAR(10))

CREATE VIEW  V1 AS SELECT COL1
    FROM T1 WHERE COL1 LIKE 'A%'

CREATE VIEW V2 AS SELECT COL1
    FROM V1 WHERE COL1 LIKE '%Z'
                            WITH LOCAL CHECK OPTION

CREATE VIEW V3 AS SELECT COL1
    FROM V2 WHERE COL1 LIKE 'AB%'

CREATE VIEW V4 AS SELECT COL1
    FROM V3 WHERE COL1 LIKE '%YZ'
                            WITH CASCADED CHECK OPTION

CREATE VIEW V5 AS SELECT COL1
    FROM V4 WHERE COL1 LIKE 'ABC%'
