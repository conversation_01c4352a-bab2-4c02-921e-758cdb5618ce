{"query": "SET NAMES 'utf8' COLLATE 'utf8_general_ci'", "lexer": {"@type": "PhpMyAdmin\\SqlParser\\Lexer", "PARSER_METHODS": ["parseDelimiter", "parseWhitespace", "parseNumber", "parseComment", "parseOperator", "parseBool", "parseString", "parseSymbol", "parseKeyword", "parseLabel", "parseUnknown"], "KEYWORD_NAME_INDICATORS": ["FROM", "SET", "WHERE"], "OPERATOR_NAME_INDICATORS": [",", "."], "str": "SET NAMES 'utf8' COLLATE 'utf8_general_ci'", "len": 42, "last": 42, "list": {"@type": "PhpMyAdmin\\SqlParser\\TokensList", "tokens": [{"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SET", "value": "SET", "keyword": "SET", "type": 1, "flags": 11, "position": 0}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NAMES", "value": "NAMES", "keyword": "NAMES", "type": 1, "flags": 1, "position": 4}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 9}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "'utf8'", "value": "utf8", "keyword": null, "type": 7, "flags": 1, "position": 10}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 16}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "COLLATE", "value": "COLLATE", "keyword": "COLLATE", "type": 1, "flags": 3, "position": 17}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 24}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "'utf8_general_ci'", "value": "utf8_general_ci", "keyword": null, "type": 7, "flags": 1, "position": 25}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": null, "value": null, "keyword": null, "type": 9, "flags": 0, "position": null}], "count": 10, "idx": 10}, "DEFAULT_DELIMITER": ";", "delimiter": ";", "delimiterLen": 1, "strict": false, "errors": []}, "parser": {"@type": "PhpMyAdmin\\SqlParser\\Parser", "STATEMENT_PARSERS": {"DESCRIBE": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "DESC": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "EXPLAIN": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "FLUSH": "", "GRANT": "", "HELP": "", "SET PASSWORD": "", "STATUS": "", "USE": "", "ANALYZE": "PhpMyAdmin\\SqlParser\\Statements\\AnalyzeStatement", "BACKUP": "PhpMyAdmin\\SqlParser\\Statements\\BackupStatement", "CHECK": "PhpMyAdmin\\SqlParser\\Statements\\CheckStatement", "CHECKSUM": "PhpMyAdmin\\SqlParser\\Statements\\ChecksumStatement", "OPTIMIZE": "PhpMyAdmin\\SqlParser\\Statements\\OptimizeStatement", "REPAIR": "PhpMyAdmin\\SqlParser\\Statements\\RepairStatement", "RESTORE": "PhpMyAdmin\\SqlParser\\Statements\\RestoreStatement", "SET": "PhpMyAdmin\\SqlParser\\Statements\\SetStatement", "SHOW": "PhpMyAdmin\\SqlParser\\Statements\\ShowStatement", "ALTER": "PhpMyAdmin\\SqlParser\\Statements\\AlterStatement", "CREATE": "PhpMyAdmin\\SqlParser\\Statements\\CreateStatement", "DROP": "PhpMyAdmin\\SqlParser\\Statements\\DropStatement", "RENAME": "PhpMyAdmin\\SqlParser\\Statements\\RenameStatement", "TRUNCATE": "PhpMyAdmin\\SqlParser\\Statements\\TruncateStatement", "CALL": "PhpMyAdmin\\SqlParser\\Statements\\CallStatement", "DELETE": "PhpMyAdmin\\SqlParser\\Statements\\DeleteStatement", "DO": "", "HANDLER": "", "INSERT": "PhpMyAdmin\\SqlParser\\Statements\\InsertStatement", "LOAD DATA": "PhpMyAdmin\\SqlParser\\Statements\\LoadStatement", "REPLACE": "PhpMyAdmin\\SqlParser\\Statements\\ReplaceStatement", "SELECT": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "UPDATE": "PhpMyAdmin\\SqlParser\\Statements\\UpdateStatement", "WITH": "PhpMyAdmin\\SqlParser\\Statements\\WithStatement", "DEALLOCATE": "", "EXECUTE": "", "PREPARE": "", "BEGIN": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "COMMIT": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "ROLLBACK": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "START TRANSACTION": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "PURGE": "PhpMyAdmin\\SqlParser\\Statements\\PurgeStatement", "LOCK": "PhpMyAdmin\\SqlParser\\Statements\\LockStatement", "UNLOCK": "PhpMyAdmin\\SqlParser\\Statements\\LockStatement"}, "KEYWORD_PARSERS": {"PARTITION BY": [], "SUBPARTITION BY": [], "_OPTIONS": {"class": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "field": "options"}, "_END_OPTIONS": {"class": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "field": "end_options"}, "INTERSECT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "EXCEPT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION ALL": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION DISTINCT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "ALTER": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "ANALYZE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "BACKUP": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CALL": {"class": "PhpMyAdmin\\SqlParser\\Components\\FunctionCall", "field": "call"}, "CHECK": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CHECKSUM": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CROSS JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "DROP": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "fields", "options": {"parseField": "table"}}, "FORCE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "FROM": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "from", "options": {"field": "table"}}, "GROUP BY": {"class": "PhpMyAdmin\\SqlParser\\Components\\GroupKeyword", "field": "group"}, "HAVING": {"class": "PhpMyAdmin\\SqlParser\\Components\\Condition", "field": "having"}, "IGNORE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "INTO": {"class": "PhpMyAdmin\\SqlParser\\Components\\IntoKeyword", "field": "into"}, "JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LEFT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LEFT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "ON": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "RIGHT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "RIGHT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "INNER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "FULL JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "FULL OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL LEFT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL RIGHT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL LEFT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL RIGHT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "STRAIGHT_JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LIMIT": {"class": "PhpMyAdmin\\SqlParser\\Components\\Limit", "field": "limit"}, "OPTIMIZE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "ORDER BY": {"class": "PhpMyAdmin\\SqlParser\\Components\\OrderKeyword", "field": "order"}, "PARTITION": {"class": "PhpMyAdmin\\SqlParser\\Components\\ArrayObj", "field": "partition"}, "PROCEDURE": {"class": "PhpMyAdmin\\SqlParser\\Components\\FunctionCall", "field": "procedure"}, "RENAME": {"class": "PhpMyAdmin\\SqlParser\\Components\\RenameOperation", "field": "renames"}, "REPAIR": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "RESTORE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "SET": {"class": "PhpMyAdmin\\SqlParser\\Components\\SetOperation", "field": "set"}, "SELECT": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "expr"}, "TRUNCATE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "UPDATE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "USE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "VALUE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Array2d", "field": "values"}, "VALUES": {"class": "PhpMyAdmin\\SqlParser\\Components\\Array2d", "field": "values"}, "WHERE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Condition", "field": "where"}}, "list": {"@type": "@1"}, "statements": [{"@type": "PhpMyAdmin\\SqlParser\\Statements\\SetStatement", "CLAUSES": {"SET": ["SET", 3], "_END_OPTIONS": ["_END_OPTIONS", 1]}, "OPTIONS": {"CHARSET": [3, "var"], "CHARACTER SET": [3, "var"], "NAMES": [3, "var"], "PASSWORD": [3, "expr"], "SESSION": 3, "GLOBAL": 3, "PERSIST": 3, "PERSIST_ONLY": 3, "@@SESSION": 3, "@@GLOBAL": 3, "@@PERSIST": 3, "@@PERSIST_ONLY": 3}, "END_OPTIONS": {"COLLATE": [1, "var"], "DEFAULT": 1}, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"3": {"name": "NAMES", "equals": false, "expr": "'utf8'", "value": "utf8"}}}, "end_options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"1": {"name": "COLLATE", "equals": false, "expr": "'utf8_general_ci'", "value": "utf8_general_ci"}}}, "set": [], "first": 0, "last": 8}], "brackets": 0, "strict": false, "errors": []}, "errors": {"lexer": [], "parser": []}}