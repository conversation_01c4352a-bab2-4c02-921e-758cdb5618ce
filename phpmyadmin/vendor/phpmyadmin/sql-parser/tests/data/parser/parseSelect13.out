{"query": "SELECT post.*, post.username AS postusername, post.ipaddress AS ip, IF(post.visible = 2, 1, 0) AS isdeleted, user.*, userfield.*, usertextfield.*, icon.title as icontitle, icon.iconpath, avatar.avatarpath, NOT ISNULL(customavatar.userid) AS hascustomavatar, customavatar.dateline AS avatardateline,customavatar.width AS avwidth,customavatar.height AS avheight, spamlog.postid AS spamlog_postid, deletionlog.userid AS del_userid, deletionlog.username AS del_username, deletionlog.reason AS del_reason, editlog.userid AS edit_userid, editlog.username AS edit_username, editlog.dateline AS edit_dateline, editlog.reason AS edit_reason, editlog.hashistory, postparsed.pagetext_html, postparsed.hasimages, sigparsed.signatureparsed, sigparsed.hasimages AS sighasimages, sigpic.userid AS sigpic, sigpic.dateline AS sigpicdateline, sigpic.width AS sigpicwidth, sigpic.height AS sigpicheight, IF(displaygroupid=0, user.usergroupid, displaygroupid) AS displaygroupid, infractiongroupid , post_icon_list.icon_id_list, post_icon_list.is_auto AS icon_is_auto, approvedlog.modid AS approvedmodid, approvedlog.dateline AS approveddateline, approvedlog.status AS approvedstatus, approvedlog.info AS approvedinfo, movedlog.modid AS movedmodid, movedlog.dateline AS moveddateline, movedlog.status AS movedstatus, movedlog.info AS movedinfo, ( SELECT useragent FROM session WHERE userid=post.userid AND lastactivity > 1644859580 ORDER BY lastactivity DESC LIMIT 1 ) AS useragent, IF ( user.userid IS NOT NULL, (SELECT COUNT(usernoteid) FROM usernote AS usernote WHERE usernote.userid=user.userid AND usernote.priority>=0), 0 ) AS usernotecount , deletionlog.dateline AS del_dateline, scheduled_approval.defer_time AS vbpmal_approval_defer_time, additional_user_data.last_year_message_count, additional_user_data.last_year_reputation, additional_user_data.last_year_groan_count, paid_post_activation.activation_id AS paid_post_activation_id, alm_Model_UserData.credits FROM post AS post LEFT JOIN user AS user ON(user.userid = post.userid) LEFT JOIN userfield AS userfield ON(userfield.userid = user.userid) LEFT JOIN usertextfield AS usertextfield ON(usertextfield.userid = user.userid) LEFT JOIN icon AS icon ON(icon.iconid = post.iconid) LEFT JOIN avatar AS avatar  ON(avatar.avatarid = user.avatarid) LEFT JOIN customavatar AS customavatar ON(customavatar.userid = user.userid) LEFT JOIN spamlog AS spamlog ON(spamlog.postid = post.postid) LEFT JOIN deletionlog AS deletionlog ON(post.postid = deletionlog.primaryid AND deletionlog.type = 'post') LEFT JOIN editlog AS editlog ON(editlog.postid = post.postid) LEFT JOIN postparsed AS postparsed ON(postparsed.postid = post.postid AND postparsed.styleid = 23 AND postparsed.languageid = 5) LEFT JOIN sigparsed AS sigparsed ON(sigparsed.userid = user.userid AND sigparsed.styleid = 23 AND sigparsed.languageid = 5) LEFT JOIN sigpic AS sigpic ON(sigpic.userid = post.userid) LEFT JOIN vbppim_post_icon_list AS post_icon_list ON post_icon_list.post_id=post.postid LEFT JOIN vbpmal_log AS approvedlog ON (approvedlog.itemid=post.postid AND approvedlog.action='postapprove') LEFT JOIN vbpmal_log AS movedlog ON (movedlog.itemid=post.postid AND movedlog.action='postmove') LEFT JOIN vbpmal_scheduled_post_approval AS scheduled_approval ON scheduled_approval.post_id = post.postid LEFT JOIN vbpsmt_additional_user_data AS additional_user_data ON additional_user_data.userid=post.userid LEFT JOIN market_pp_post_activation_mapping AS paid_post_activation ON paid_post_activation.post_id = post.postid LEFT JOIN alm_Model_UserData AS alm_Model_UserData ON alm_Model_UserData.user_id=user.userid WHERE post.postid IN (0,3254399,3254508,3254743,3254817,3254969,3255328,3255582,3257603,3257873,3258126,3258150,3258254,3258272,3258311,3260767,3260770,3260776,3261180,3261263,3261317,3261318) ORDER BY post.dateline", "lexer": {"@type": "PhpMyAdmin\\SqlParser\\Lexer", "PARSER_METHODS": ["parseDelimiter", "parseWhitespace", "parseNumber", "parseComment", "parseOperator", "parseBool", "parseString", "parseSymbol", "parseKeyword", "parseLabel", "parseUnknown"], "KEYWORD_NAME_INDICATORS": ["FROM", "SET", "WHERE"], "OPERATOR_NAME_INDICATORS": [",", "."], "str": "SELECT post.*, post.username AS postusername, post.ipaddress AS ip, IF(post.visible = 2, 1, 0) AS isdeleted, user.*, userfield.*, usertextfield.*, icon.title as icontitle, icon.iconpath, avatar.avatarpath, NOT ISNULL(customavatar.userid) AS hascustomavatar, customavatar.dateline AS avatardateline,customavatar.width AS avwidth,customavatar.height AS avheight, spamlog.postid AS spamlog_postid, deletionlog.userid AS del_userid, deletionlog.username AS del_username, deletionlog.reason AS del_reason, editlog.userid AS edit_userid, editlog.username AS edit_username, editlog.dateline AS edit_dateline, editlog.reason AS edit_reason, editlog.hashistory, postparsed.pagetext_html, postparsed.hasimages, sigparsed.signatureparsed, sigparsed.hasimages AS sighasimages, sigpic.userid AS sigpic, sigpic.dateline AS sigpicdateline, sigpic.width AS sigpicwidth, sigpic.height AS sigpicheight, IF(displaygroupid=0, user.usergroupid, displaygroupid) AS displaygroupid, infractiongroupid , post_icon_list.icon_id_list, post_icon_list.is_auto AS icon_is_auto, approvedlog.modid AS approvedmodid, approvedlog.dateline AS approveddateline, approvedlog.status AS approvedstatus, approvedlog.info AS approvedinfo, movedlog.modid AS movedmodid, movedlog.dateline AS moveddateline, movedlog.status AS movedstatus, movedlog.info AS movedinfo, ( SELECT useragent FROM session WHERE userid=post.userid AND lastactivity > 1644859580 ORDER BY lastactivity DESC LIMIT 1 ) AS useragent, IF ( user.userid IS NOT NULL, (SELECT COUNT(usernoteid) FROM usernote AS usernote WHERE usernote.userid=user.userid AND usernote.priority>=0), 0 ) AS usernotecount , deletionlog.dateline AS del_dateline, scheduled_approval.defer_time AS vbpmal_approval_defer_time, additional_user_data.last_year_message_count, additional_user_data.last_year_reputation, additional_user_data.last_year_groan_count, paid_post_activation.activation_id AS paid_post_activation_id, alm_Model_UserData.credits FROM post AS post LEFT JOIN user AS user ON(user.userid = post.userid) LEFT JOIN userfield AS userfield ON(userfield.userid = user.userid) LEFT JOIN usertextfield AS usertextfield ON(usertextfield.userid = user.userid) LEFT JOIN icon AS icon ON(icon.iconid = post.iconid) LEFT JOIN avatar AS avatar  ON(avatar.avatarid = user.avatarid) LEFT JOIN customavatar AS customavatar ON(customavatar.userid = user.userid) LEFT JOIN spamlog AS spamlog ON(spamlog.postid = post.postid) LEFT JOIN deletionlog AS deletionlog ON(post.postid = deletionlog.primaryid AND deletionlog.type = 'post') LEFT JOIN editlog AS editlog ON(editlog.postid = post.postid) LEFT JOIN postparsed AS postparsed ON(postparsed.postid = post.postid AND postparsed.styleid = 23 AND postparsed.languageid = 5) LEFT JOIN sigparsed AS sigparsed ON(sigparsed.userid = user.userid AND sigparsed.styleid = 23 AND sigparsed.languageid = 5) LEFT JOIN sigpic AS sigpic ON(sigpic.userid = post.userid) LEFT JOIN vbppim_post_icon_list AS post_icon_list ON post_icon_list.post_id=post.postid LEFT JOIN vbpmal_log AS approvedlog ON (approvedlog.itemid=post.postid AND approvedlog.action='postapprove') LEFT JOIN vbpmal_log AS movedlog ON (movedlog.itemid=post.postid AND movedlog.action='postmove') LEFT JOIN vbpmal_scheduled_post_approval AS scheduled_approval ON scheduled_approval.post_id = post.postid LEFT JOIN vbpsmt_additional_user_data AS additional_user_data ON additional_user_data.userid=post.userid LEFT JOIN market_pp_post_activation_mapping AS paid_post_activation ON paid_post_activation.post_id = post.postid LEFT JOIN alm_Model_UserData AS alm_Model_UserData ON alm_Model_UserData.user_id=user.userid WHERE post.postid IN (0,3254399,3254508,3254743,3254817,3254969,3255328,3255582,3257603,3257873,3258126,3258150,3258254,3258272,3258311,3260767,3260770,3260776,3261180,3261263,3261317,3261318) ORDER BY post.dateline", "len": 3834, "last": 3834, "list": {"@type": "PhpMyAdmin\\SqlParser\\TokensList", "tokens": [{"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 0}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 6}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 7}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 11}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 12}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 13}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 14}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 15}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 19}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "username", "value": "username", "keyword": null, "type": 0, "flags": 0, "position": 20}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 28}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 29}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 31}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postusername", "value": "postusername", "keyword": null, "type": 0, "flags": 0, "position": 32}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 44}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 45}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 46}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 50}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ipaddress", "value": "ipaddress", "keyword": null, "type": 0, "flags": 0, "position": 51}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 60}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 61}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 63}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ip", "value": "ip", "keyword": null, "type": 0, "flags": 0, "position": 64}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 66}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 67}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "IF", "value": "IF", "keyword": "IF", "type": 1, "flags": 35, "position": 68}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 70}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 71}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 75}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "visible", "value": "visible", "keyword": null, "type": 0, "flags": 0, "position": 76}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 83}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 84}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 85}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "2", "value": 2, "keyword": null, "type": 6, "flags": 0, "position": 86}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 87}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 88}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 89}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 90}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 91}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "0", "value": 0, "keyword": null, "type": 6, "flags": 0, "position": 92}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 93}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 94}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 95}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 97}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "isdeleted", "value": "isdeleted", "keyword": null, "type": 0, "flags": 0, "position": 98}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 107}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 108}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "user", "value": "user", "keyword": "user", "type": 0, "flags": 0, "position": 109}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 113}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 114}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 115}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 116}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userfield", "value": "userfield", "keyword": null, "type": 0, "flags": 0, "position": 117}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 126}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 127}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 128}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 129}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "usertextfield", "value": "usertextfield", "keyword": null, "type": 0, "flags": 0, "position": 130}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 143}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "*", "value": "*", "keyword": null, "type": 2, "flags": 16, "position": 144}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 145}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 146}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "icon", "value": "icon", "keyword": null, "type": 0, "flags": 0, "position": 147}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 151}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "title", "value": "title", "keyword": null, "type": 0, "flags": 0, "position": 152}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 157}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "as", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 158}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 160}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "icontitle", "value": "icontitle", "keyword": null, "type": 0, "flags": 0, "position": 161}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 170}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 171}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "icon", "value": "icon", "keyword": null, "type": 0, "flags": 0, "position": 172}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 176}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "iconpath", "value": "iconpath", "keyword": null, "type": 0, "flags": 0, "position": 177}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 185}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 186}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "avatar", "value": "avatar", "keyword": null, "type": 0, "flags": 0, "position": 187}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 193}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "avatarpath", "value": "avatarpath", "keyword": null, "type": 0, "flags": 0, "position": 194}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 204}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 205}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NOT", "value": "NOT", "keyword": "NOT", "type": 1, "flags": 3, "position": 206}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 209}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ISNULL", "value": "ISNULL", "keyword": "ISNULL", "type": 1, "flags": 33, "position": 210}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 216}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "customavatar", "value": "customavatar", "keyword": null, "type": 0, "flags": 0, "position": 217}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 229}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 230}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 236}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 237}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 238}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 240}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyword": null, "type": 0, "flags": 0, "position": 241}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 256}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 257}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "customavatar", "value": "customavatar", "keyword": null, "type": 0, "flags": 0, "position": 258}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 270}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "dateline", "value": "dateline", "keyword": null, "type": 0, "flags": 0, "position": 271}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 279}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 280}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 282}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "avatardateline", "value": "avatardateline", "keyword": null, "type": 0, "flags": 0, "position": 283}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 297}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "customavatar", "value": "customavatar", "keyword": null, "type": 0, "flags": 0, "position": 298}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 310}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "width", "value": "width", "keyword": null, "type": 0, "flags": 0, "position": 311}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 316}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 317}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 319}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "avwidth", "value": "avwidth", "keyword": null, "type": 0, "flags": 0, "position": 320}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 327}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "customavatar", "value": "customavatar", "keyword": null, "type": 0, "flags": 0, "position": 328}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 340}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "height", "value": "height", "keyword": null, "type": 0, "flags": 0, "position": 341}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 347}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 348}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 350}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "avheight", "value": "avheight", "keyword": null, "type": 0, "flags": 0, "position": 351}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 359}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 360}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "spamlog", "value": "spamlog", "keyword": null, "type": 0, "flags": 0, "position": 361}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 368}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postid", "value": "postid", "keyword": null, "type": 0, "flags": 0, "position": 369}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 375}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 376}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 378}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "spamlog_postid", "value": "spamlog_postid", "keyword": null, "type": 0, "flags": 0, "position": 379}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 393}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 394}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "deletionlog", "value": "deletionlog", "keyword": null, "type": 0, "flags": 0, "position": 395}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 406}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 407}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 413}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 414}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 416}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "del_userid", "value": "del_userid", "keyword": null, "type": 0, "flags": 0, "position": 417}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 427}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 428}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "deletionlog", "value": "deletionlog", "keyword": null, "type": 0, "flags": 0, "position": 429}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 440}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "username", "value": "username", "keyword": null, "type": 0, "flags": 0, "position": 441}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 449}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 450}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 452}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "del_username", "value": "del_username", "keyword": null, "type": 0, "flags": 0, "position": 453}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 465}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 466}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "deletionlog", "value": "deletionlog", "keyword": null, "type": 0, "flags": 0, "position": 467}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 478}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "reason", "value": "reason", "keyword": null, "type": 0, "flags": 0, "position": 479}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 485}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 486}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 488}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "del_reason", "value": "del_reason", "keyword": null, "type": 0, "flags": 0, "position": 489}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 499}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 500}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "editlog", "value": "editlog", "keyword": null, "type": 0, "flags": 0, "position": 501}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 508}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 509}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 515}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 516}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 518}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "edit_userid", "value": "edit_userid", "keyword": null, "type": 0, "flags": 0, "position": 519}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 530}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 531}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "editlog", "value": "editlog", "keyword": null, "type": 0, "flags": 0, "position": 532}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 539}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "username", "value": "username", "keyword": null, "type": 0, "flags": 0, "position": 540}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 548}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 549}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 551}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "edit_username", "value": "edit_username", "keyword": null, "type": 0, "flags": 0, "position": 552}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 565}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 566}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "editlog", "value": "editlog", "keyword": null, "type": 0, "flags": 0, "position": 567}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 574}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "dateline", "value": "dateline", "keyword": null, "type": 0, "flags": 0, "position": 575}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 583}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 584}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 586}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "edit_dateline", "value": "edit_dateline", "keyword": null, "type": 0, "flags": 0, "position": 587}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 600}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 601}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "editlog", "value": "editlog", "keyword": null, "type": 0, "flags": 0, "position": 602}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 609}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "reason", "value": "reason", "keyword": null, "type": 0, "flags": 0, "position": 610}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 616}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 617}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 619}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "edit_reason", "value": "edit_reason", "keyword": null, "type": 0, "flags": 0, "position": 620}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 631}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 632}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "editlog", "value": "editlog", "keyword": null, "type": 0, "flags": 0, "position": 633}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 640}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "hashistory", "value": "hashistory", "keyword": null, "type": 0, "flags": 0, "position": 641}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 651}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 652}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postparsed", "value": "postparsed", "keyword": null, "type": 0, "flags": 0, "position": 653}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 663}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "pagetext_html", "value": "pagetext_html", "keyword": null, "type": 0, "flags": 0, "position": 664}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 677}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 678}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postparsed", "value": "postparsed", "keyword": null, "type": 0, "flags": 0, "position": 679}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 689}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "hasimages", "value": "hasimages", "keyword": null, "type": 0, "flags": 0, "position": 690}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 699}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 700}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sigparsed", "value": "sigparsed", "keyword": null, "type": 0, "flags": 0, "position": 701}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 710}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "signatureparsed", "value": "signatureparsed", "keyword": null, "type": 0, "flags": 0, "position": 711}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 726}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 727}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sigparsed", "value": "sigparsed", "keyword": null, "type": 0, "flags": 0, "position": 728}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 737}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "hasimages", "value": "hasimages", "keyword": null, "type": 0, "flags": 0, "position": 738}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 747}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 748}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 750}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sighasimages", "value": "sighasimages", "keyword": null, "type": 0, "flags": 0, "position": 751}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 763}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 764}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sigpic", "value": "sigpic", "keyword": null, "type": 0, "flags": 0, "position": 765}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 771}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 772}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 778}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 779}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 781}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sigpic", "value": "sigpic", "keyword": null, "type": 0, "flags": 0, "position": 782}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 788}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 789}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sigpic", "value": "sigpic", "keyword": null, "type": 0, "flags": 0, "position": 790}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 796}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "dateline", "value": "dateline", "keyword": null, "type": 0, "flags": 0, "position": 797}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 805}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 806}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 808}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sigpicdateline", "value": "sigpicdateline", "keyword": null, "type": 0, "flags": 0, "position": 809}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 823}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 824}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sigpic", "value": "sigpic", "keyword": null, "type": 0, "flags": 0, "position": 825}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 831}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "width", "value": "width", "keyword": null, "type": 0, "flags": 0, "position": 832}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 837}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 838}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 840}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sigpicwidth", "value": "sigpicwidth", "keyword": null, "type": 0, "flags": 0, "position": 841}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 852}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 853}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sigpic", "value": "sigpic", "keyword": null, "type": 0, "flags": 0, "position": 854}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 860}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "height", "value": "height", "keyword": null, "type": 0, "flags": 0, "position": 861}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 867}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 868}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 870}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sigpicheight", "value": "sigpicheight", "keyword": null, "type": 0, "flags": 0, "position": 871}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 883}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 884}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "IF", "value": "IF", "keyword": "IF", "type": 1, "flags": 35, "position": 885}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 887}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "displaygroupid", "value": "displaygroupid", "keyword": null, "type": 0, "flags": 0, "position": 888}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 902}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "0", "value": 0, "keyword": null, "type": 6, "flags": 0, "position": 903}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 904}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 905}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "user", "value": "user", "keyword": "user", "type": 0, "flags": 0, "position": 906}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 910}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "usergroupid", "value": "usergroupid", "keyword": null, "type": 0, "flags": 0, "position": 911}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 922}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 923}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "displaygroupid", "value": "displaygroupid", "keyword": null, "type": 0, "flags": 0, "position": 924}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 938}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 939}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 940}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 942}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "displaygroupid", "value": "displaygroupid", "keyword": null, "type": 0, "flags": 0, "position": 943}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 957}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 958}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "infractiongroupid", "value": "infractiongroupid", "keyword": null, "type": 0, "flags": 0, "position": 959}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 976}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 977}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 978}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post_icon_list", "value": "post_icon_list", "keyword": null, "type": 0, "flags": 0, "position": 979}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 993}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "icon_id_list", "value": "icon_id_list", "keyword": null, "type": 0, "flags": 0, "position": 994}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1006}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1007}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post_icon_list", "value": "post_icon_list", "keyword": null, "type": 0, "flags": 0, "position": 1008}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1022}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "is_auto", "value": "is_auto", "keyword": null, "type": 0, "flags": 0, "position": 1023}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1030}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 1031}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1033}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "icon_is_auto", "value": "icon_is_auto", "keyword": null, "type": 0, "flags": 0, "position": 1034}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1046}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1047}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "approvedlog", "value": "approvedlog", "keyword": null, "type": 0, "flags": 0, "position": 1048}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1059}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "modid", "value": "modid", "keyword": null, "type": 0, "flags": 0, "position": 1060}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1065}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 1066}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1068}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "approvedmodid", "value": "approvedmodid", "keyword": null, "type": 0, "flags": 0, "position": 1069}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1082}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1083}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "approvedlog", "value": "approvedlog", "keyword": null, "type": 0, "flags": 0, "position": 1084}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1095}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "dateline", "value": "dateline", "keyword": null, "type": 0, "flags": 0, "position": 1096}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1104}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 1105}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1107}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "approveddateline", "value": "approveddateline", "keyword": null, "type": 0, "flags": 0, "position": 1108}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1124}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1125}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "approvedlog", "value": "approvedlog", "keyword": null, "type": 0, "flags": 0, "position": 1126}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1137}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "status", "value": "status", "keyword": "STATUS", "type": 0, "flags": 0, "position": 1138}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1144}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 1145}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1147}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "approved<PERSON><PERSON>", "value": "approved<PERSON><PERSON>", "keyword": null, "type": 0, "flags": 0, "position": 1148}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1162}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1163}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "approvedlog", "value": "approvedlog", "keyword": null, "type": 0, "flags": 0, "position": 1164}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1175}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "info", "value": "info", "keyword": null, "type": 0, "flags": 0, "position": 1176}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1180}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 1181}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1183}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "approvedinfo", "value": "approvedinfo", "keyword": null, "type": 0, "flags": 0, "position": 1184}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1196}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1197}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "movedlog", "value": "movedlog", "keyword": null, "type": 0, "flags": 0, "position": 1198}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1206}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "modid", "value": "modid", "keyword": null, "type": 0, "flags": 0, "position": 1207}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1212}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 1213}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1215}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "movedmodid", "value": "movedmodid", "keyword": null, "type": 0, "flags": 0, "position": 1216}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1226}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1227}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "movedlog", "value": "movedlog", "keyword": null, "type": 0, "flags": 0, "position": 1228}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1236}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "dateline", "value": "dateline", "keyword": null, "type": 0, "flags": 0, "position": 1237}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1245}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 1246}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1248}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "moveddateline", "value": "moveddateline", "keyword": null, "type": 0, "flags": 0, "position": 1249}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1262}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1263}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "movedlog", "value": "movedlog", "keyword": null, "type": 0, "flags": 0, "position": 1264}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1272}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "status", "value": "status", "keyword": "STATUS", "type": 0, "flags": 0, "position": 1273}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1279}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 1280}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1282}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>", "keyword": null, "type": 0, "flags": 0, "position": 1283}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1294}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1295}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "movedlog", "value": "movedlog", "keyword": null, "type": 0, "flags": 0, "position": 1296}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1304}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "info", "value": "info", "keyword": null, "type": 0, "flags": 0, "position": 1305}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1309}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 1310}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1312}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "movedinfo", "value": "movedinfo", "keyword": null, "type": 0, "flags": 0, "position": 1313}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1322}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1323}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1324}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1325}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1326}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1332}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "useragent", "value": "useragent", "keyword": null, "type": 0, "flags": 0, "position": 1333}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1342}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 1343}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1347}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "session", "value": "session", "keyword": "SESSION", "type": 1, "flags": 1, "position": 1348}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1355}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 1356}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1361}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 1362}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 1368}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 1369}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1373}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 1374}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1380}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AND", "value": "AND", "keyword": "AND", "type": 1, "flags": 3, "position": 1381}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1384}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "lastactivity", "value": "lastactivity", "keyword": null, "type": 0, "flags": 0, "position": 1385}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1397}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ">", "value": ">", "keyword": null, "type": 2, "flags": 2, "position": 1398}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1399}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1644859580", "value": 1644859580, "keyword": null, "type": 6, "flags": 0, "position": 1400}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1410}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ORDER BY", "value": "ORDER BY", "keyword": "ORDER BY", "type": 1, "flags": 7, "position": 1411}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1419}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "lastactivity", "value": "lastactivity", "keyword": null, "type": 0, "flags": 0, "position": 1420}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1432}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DESC", "value": "DESC", "keyword": "DESC", "type": 1, "flags": 3, "position": 1433}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1437}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LIMIT", "value": "LIMIT", "keyword": "LIMIT", "type": 1, "flags": 3, "position": 1438}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1443}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "1", "value": 1, "keyword": null, "type": 6, "flags": 0, "position": 1444}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1445}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 1446}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1447}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 1448}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1450}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "useragent", "value": "useragent", "keyword": null, "type": 0, "flags": 0, "position": 1451}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1460}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1461}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "IF", "value": "IF", "keyword": "IF", "type": 1, "flags": 35, "position": 1462}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1464}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1465}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1466}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "user", "value": "user", "keyword": "user", "type": 0, "flags": 0, "position": 1467}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1471}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 1472}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1478}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "IS", "value": "IS", "keyword": "IS", "type": 1, "flags": 3, "position": 1479}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1481}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NOT NULL", "value": "NOT NULL", "keyword": "NOT NULL", "type": 1, "flags": 7, "position": 1482}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1490}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1491}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1492}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "SELECT", "value": "SELECT", "keyword": "SELECT", "type": 1, "flags": 3, "position": 1493}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1499}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "COUNT", "value": "COUNT", "keyword": "COUNT", "type": 1, "flags": 33, "position": 1500}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1505}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "usernoteid", "value": "usernoteid", "keyword": null, "type": 0, "flags": 0, "position": 1506}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 1516}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1517}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 1518}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1522}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "usernote", "value": "usernote", "keyword": null, "type": 0, "flags": 0, "position": 1523}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1531}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 1532}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1534}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "usernote", "value": "usernote", "keyword": null, "type": 0, "flags": 0, "position": 1535}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1543}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 1544}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1549}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "usernote", "value": "usernote", "keyword": null, "type": 0, "flags": 0, "position": 1550}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1558}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 1559}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 1565}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "user", "value": "user", "keyword": "user", "type": 0, "flags": 0, "position": 1566}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1570}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 1571}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1577}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AND", "value": "AND", "keyword": "AND", "type": 1, "flags": 3, "position": 1578}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1581}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "usernote", "value": "usernote", "keyword": null, "type": 0, "flags": 0, "position": 1582}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1590}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "priority", "value": "priority", "keyword": null, "type": 0, "flags": 0, "position": 1591}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ">=", "value": ">=", "keyword": null, "type": 2, "flags": 2, "position": 1599}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "0", "value": 0, "keyword": null, "type": 6, "flags": 0, "position": 1601}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 1602}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1603}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1604}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "0", "value": 0, "keyword": null, "type": 6, "flags": 0, "position": 1605}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1606}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 1607}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1608}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 1609}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1611}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "usernotecount", "value": "usernotecount", "keyword": null, "type": 0, "flags": 0, "position": 1612}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1625}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1626}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1627}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "deletionlog", "value": "deletionlog", "keyword": null, "type": 0, "flags": 0, "position": 1628}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1639}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "dateline", "value": "dateline", "keyword": null, "type": 0, "flags": 0, "position": 1640}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1648}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 1649}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1651}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "del_dateline", "value": "del_dateline", "keyword": null, "type": 0, "flags": 0, "position": 1652}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1664}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1665}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "scheduled_approval", "value": "scheduled_approval", "keyword": null, "type": 0, "flags": 0, "position": 1666}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1684}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "defer_time", "value": "defer_time", "keyword": null, "type": 0, "flags": 0, "position": 1685}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1695}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 1696}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1698}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "vbpmal_approval_defer_time", "value": "vbpmal_approval_defer_time", "keyword": null, "type": 0, "flags": 0, "position": 1699}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1725}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1726}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "additional_user_data", "value": "additional_user_data", "keyword": null, "type": 0, "flags": 0, "position": 1727}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1747}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "last_year_message_count", "value": "last_year_message_count", "keyword": null, "type": 0, "flags": 0, "position": 1748}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1771}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1772}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "additional_user_data", "value": "additional_user_data", "keyword": null, "type": 0, "flags": 0, "position": 1773}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1793}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "last_year_reputation", "value": "last_year_reputation", "keyword": null, "type": 0, "flags": 0, "position": 1794}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1814}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1815}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "additional_user_data", "value": "additional_user_data", "keyword": null, "type": 0, "flags": 0, "position": 1816}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1836}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "last_year_groan_count", "value": "last_year_groan_count", "keyword": null, "type": 0, "flags": 0, "position": 1837}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1858}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1859}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "paid_post_activation", "value": "paid_post_activation", "keyword": null, "type": 0, "flags": 0, "position": 1860}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1880}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "activation_id", "value": "activation_id", "keyword": null, "type": 0, "flags": 0, "position": 1881}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1894}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 1895}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1897}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "paid_post_activation_id", "value": "paid_post_activation_id", "keyword": null, "type": 0, "flags": 0, "position": 1898}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 1921}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1922}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "alm_Model_UserData", "value": "alm_Model_UserData", "keyword": null, "type": 0, "flags": 0, "position": 1923}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1941}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "credits", "value": "credits", "keyword": null, "type": 0, "flags": 0, "position": 1942}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1949}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FROM", "value": "FROM", "keyword": "FROM", "type": 1, "flags": 3, "position": 1950}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1954}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 1955}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1959}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 1960}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1962}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 1963}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1967}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 1968}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1977}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "user", "value": "user", "keyword": "USER", "type": 1, "flags": 33, "position": 1978}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1982}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 1983}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1985}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "user", "value": "user", "keyword": "USER", "type": 1, "flags": 33, "position": 1986}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 1990}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 1991}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 1993}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "user", "value": "user", "keyword": "user", "type": 0, "flags": 0, "position": 1994}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 1998}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 1999}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2005}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 2006}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2007}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 2008}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2012}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 2013}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 2019}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2020}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 2021}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2030}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userfield", "value": "userfield", "keyword": null, "type": 0, "flags": 0, "position": 2031}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2040}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 2041}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2043}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userfield", "value": "userfield", "keyword": null, "type": 0, "flags": 0, "position": 2044}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2053}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 2054}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 2056}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userfield", "value": "userfield", "keyword": null, "type": 0, "flags": 0, "position": 2057}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2066}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 2067}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2073}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 2074}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2075}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "user", "value": "user", "keyword": "user", "type": 0, "flags": 0, "position": 2076}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2080}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 2081}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 2087}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2088}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 2089}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2098}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "usertextfield", "value": "usertextfield", "keyword": null, "type": 0, "flags": 0, "position": 2099}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2112}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 2113}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2115}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "usertextfield", "value": "usertextfield", "keyword": null, "type": 0, "flags": 0, "position": 2116}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2129}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 2130}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 2132}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "usertextfield", "value": "usertextfield", "keyword": null, "type": 0, "flags": 0, "position": 2133}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2146}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 2147}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2153}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 2154}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2155}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "user", "value": "user", "keyword": "user", "type": 0, "flags": 0, "position": 2156}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2160}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 2161}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 2167}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2168}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 2169}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2178}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "icon", "value": "icon", "keyword": null, "type": 0, "flags": 0, "position": 2179}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2183}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 2184}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2186}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "icon", "value": "icon", "keyword": null, "type": 0, "flags": 0, "position": 2187}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2191}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 2192}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 2194}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "icon", "value": "icon", "keyword": null, "type": 0, "flags": 0, "position": 2195}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2199}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "iconid", "value": "iconid", "keyword": null, "type": 0, "flags": 0, "position": 2200}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2206}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 2207}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2208}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 2209}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2213}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "iconid", "value": "iconid", "keyword": null, "type": 0, "flags": 0, "position": 2214}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 2220}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2221}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 2222}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2231}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "avatar", "value": "avatar", "keyword": null, "type": 0, "flags": 0, "position": 2232}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2238}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 2239}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2241}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "avatar", "value": "avatar", "keyword": null, "type": 0, "flags": 0, "position": 2242}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2248}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 2250}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 2252}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "avatar", "value": "avatar", "keyword": null, "type": 0, "flags": 0, "position": 2253}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2259}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "avatarid", "value": "avatarid", "keyword": null, "type": 0, "flags": 0, "position": 2260}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2268}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 2269}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2270}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "user", "value": "user", "keyword": "user", "type": 0, "flags": 0, "position": 2271}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2275}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "avatarid", "value": "avatarid", "keyword": null, "type": 0, "flags": 0, "position": 2276}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 2284}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2285}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 2286}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2295}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "customavatar", "value": "customavatar", "keyword": null, "type": 0, "flags": 0, "position": 2296}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2308}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 2309}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2311}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "customavatar", "value": "customavatar", "keyword": null, "type": 0, "flags": 0, "position": 2312}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2324}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 2325}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 2327}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "customavatar", "value": "customavatar", "keyword": null, "type": 0, "flags": 0, "position": 2328}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2340}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 2341}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2347}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 2348}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2349}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "user", "value": "user", "keyword": "user", "type": 0, "flags": 0, "position": 2350}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2354}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 2355}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 2361}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2362}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 2363}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2372}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "spamlog", "value": "spamlog", "keyword": null, "type": 0, "flags": 0, "position": 2373}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2380}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 2381}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2383}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "spamlog", "value": "spamlog", "keyword": null, "type": 0, "flags": 0, "position": 2384}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2391}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 2392}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 2394}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "spamlog", "value": "spamlog", "keyword": null, "type": 0, "flags": 0, "position": 2395}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2402}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postid", "value": "postid", "keyword": null, "type": 0, "flags": 0, "position": 2403}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2409}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 2410}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2411}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 2412}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2416}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postid", "value": "postid", "keyword": null, "type": 0, "flags": 0, "position": 2417}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 2423}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2424}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 2425}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2434}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "deletionlog", "value": "deletionlog", "keyword": null, "type": 0, "flags": 0, "position": 2435}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2446}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 2447}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2449}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "deletionlog", "value": "deletionlog", "keyword": null, "type": 0, "flags": 0, "position": 2450}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2461}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 2462}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 2464}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 2465}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2469}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postid", "value": "postid", "keyword": null, "type": 0, "flags": 0, "position": 2470}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2476}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 2477}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2478}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "deletionlog", "value": "deletionlog", "keyword": null, "type": 0, "flags": 0, "position": 2479}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2490}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "primaryid", "value": "primaryid", "keyword": null, "type": 0, "flags": 0, "position": 2491}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2500}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AND", "value": "AND", "keyword": "AND", "type": 1, "flags": 3, "position": 2501}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2504}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "deletionlog", "value": "deletionlog", "keyword": null, "type": 0, "flags": 0, "position": 2505}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2516}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "type", "value": "type", "keyword": "TYPE", "type": 0, "flags": 0, "position": 2517}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2521}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 2522}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2523}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "'post'", "value": "post", "keyword": null, "type": 7, "flags": 1, "position": 2524}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 2530}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2531}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 2532}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2541}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "editlog", "value": "editlog", "keyword": null, "type": 0, "flags": 0, "position": 2542}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2549}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 2550}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2552}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "editlog", "value": "editlog", "keyword": null, "type": 0, "flags": 0, "position": 2553}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2560}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 2561}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 2563}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "editlog", "value": "editlog", "keyword": null, "type": 0, "flags": 0, "position": 2564}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2571}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postid", "value": "postid", "keyword": null, "type": 0, "flags": 0, "position": 2572}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2578}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 2579}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2580}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 2581}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2585}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postid", "value": "postid", "keyword": null, "type": 0, "flags": 0, "position": 2586}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 2592}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2593}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 2594}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2603}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postparsed", "value": "postparsed", "keyword": null, "type": 0, "flags": 0, "position": 2604}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2614}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 2615}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2617}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postparsed", "value": "postparsed", "keyword": null, "type": 0, "flags": 0, "position": 2618}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2628}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 2629}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 2631}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postparsed", "value": "postparsed", "keyword": null, "type": 0, "flags": 0, "position": 2632}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2642}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postid", "value": "postid", "keyword": null, "type": 0, "flags": 0, "position": 2643}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2649}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 2650}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2651}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 2652}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2656}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postid", "value": "postid", "keyword": null, "type": 0, "flags": 0, "position": 2657}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2663}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AND", "value": "AND", "keyword": "AND", "type": 1, "flags": 3, "position": 2664}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2667}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postparsed", "value": "postparsed", "keyword": null, "type": 0, "flags": 0, "position": 2668}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2678}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "styleid", "value": "styleid", "keyword": null, "type": 0, "flags": 0, "position": 2679}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2686}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 2687}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2688}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "23", "value": 23, "keyword": null, "type": 6, "flags": 0, "position": 2689}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2691}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AND", "value": "AND", "keyword": "AND", "type": 1, "flags": 3, "position": 2692}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2695}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postparsed", "value": "postparsed", "keyword": null, "type": 0, "flags": 0, "position": 2696}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2706}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "languageid", "value": "languageid", "keyword": null, "type": 0, "flags": 0, "position": 2707}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2717}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 2718}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2719}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "5", "value": 5, "keyword": null, "type": 6, "flags": 0, "position": 2720}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 2721}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2722}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 2723}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2732}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sigparsed", "value": "sigparsed", "keyword": null, "type": 0, "flags": 0, "position": 2733}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2742}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 2743}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2745}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sigparsed", "value": "sigparsed", "keyword": null, "type": 0, "flags": 0, "position": 2746}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2755}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 2756}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 2758}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sigparsed", "value": "sigparsed", "keyword": null, "type": 0, "flags": 0, "position": 2759}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2768}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 2769}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2775}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 2776}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2777}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "user", "value": "user", "keyword": "user", "type": 0, "flags": 0, "position": 2778}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2782}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 2783}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2789}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AND", "value": "AND", "keyword": "AND", "type": 1, "flags": 3, "position": 2790}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2793}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sigparsed", "value": "sigparsed", "keyword": null, "type": 0, "flags": 0, "position": 2794}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2803}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "styleid", "value": "styleid", "keyword": null, "type": 0, "flags": 0, "position": 2804}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2811}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 2812}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2813}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "23", "value": 23, "keyword": null, "type": 6, "flags": 0, "position": 2814}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2816}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AND", "value": "AND", "keyword": "AND", "type": 1, "flags": 3, "position": 2817}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2820}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sigparsed", "value": "sigparsed", "keyword": null, "type": 0, "flags": 0, "position": 2821}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2830}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "languageid", "value": "languageid", "keyword": null, "type": 0, "flags": 0, "position": 2831}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2841}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 2842}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2843}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "5", "value": 5, "keyword": null, "type": 6, "flags": 0, "position": 2844}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 2845}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2846}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 2847}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2856}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sigpic", "value": "sigpic", "keyword": null, "type": 0, "flags": 0, "position": 2857}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2863}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 2864}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2866}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sigpic", "value": "sigpic", "keyword": null, "type": 0, "flags": 0, "position": 2867}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2873}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 2874}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 2876}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "sigpic", "value": "sigpic", "keyword": null, "type": 0, "flags": 0, "position": 2877}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2883}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 2884}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2890}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 2891}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2892}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 2893}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2897}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 2898}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 2904}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2905}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 2906}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2915}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "vbppim_post_icon_list", "value": "vbppim_post_icon_list", "keyword": null, "type": 0, "flags": 0, "position": 2916}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2937}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 2938}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2940}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post_icon_list", "value": "post_icon_list", "keyword": null, "type": 0, "flags": 0, "position": 2941}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2955}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 2956}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2958}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post_icon_list", "value": "post_icon_list", "keyword": null, "type": 0, "flags": 0, "position": 2959}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2973}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post_id", "value": "post_id", "keyword": null, "type": 0, "flags": 0, "position": 2974}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 2981}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 2982}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 2986}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postid", "value": "postid", "keyword": null, "type": 0, "flags": 0, "position": 2987}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 2993}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 2994}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3003}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "vbpmal_log", "value": "vbpmal_log", "keyword": null, "type": 0, "flags": 0, "position": 3004}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3014}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 3015}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3017}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "approvedlog", "value": "approvedlog", "keyword": null, "type": 0, "flags": 0, "position": 3018}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3029}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 3030}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3032}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 3033}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "approvedlog", "value": "approvedlog", "keyword": null, "type": 0, "flags": 0, "position": 3034}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 3045}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "itemid", "value": "itemid", "keyword": null, "type": 0, "flags": 0, "position": 3046}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 3052}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 3053}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 3057}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postid", "value": "postid", "keyword": null, "type": 0, "flags": 0, "position": 3058}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3064}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AND", "value": "AND", "keyword": "AND", "type": 1, "flags": 3, "position": 3065}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3068}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "approvedlog", "value": "approvedlog", "keyword": null, "type": 0, "flags": 0, "position": 3069}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 3080}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "action", "value": "action", "keyword": "ACTION", "type": 0, "flags": 0, "position": 3081}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 3087}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "'postapprove'", "value": "postapprove", "keyword": null, "type": 7, "flags": 1, "position": 3088}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 3101}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3102}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 3103}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3112}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "vbpmal_log", "value": "vbpmal_log", "keyword": null, "type": 0, "flags": 0, "position": 3113}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3123}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 3124}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3126}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "movedlog", "value": "movedlog", "keyword": null, "type": 0, "flags": 0, "position": 3127}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3135}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 3136}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3138}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 3139}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "movedlog", "value": "movedlog", "keyword": null, "type": 0, "flags": 0, "position": 3140}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 3148}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "itemid", "value": "itemid", "keyword": null, "type": 0, "flags": 0, "position": 3149}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 3155}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 3156}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 3160}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postid", "value": "postid", "keyword": null, "type": 0, "flags": 0, "position": 3161}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3167}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AND", "value": "AND", "keyword": "AND", "type": 1, "flags": 3, "position": 3168}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3171}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "movedlog", "value": "movedlog", "keyword": null, "type": 0, "flags": 0, "position": 3172}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 3180}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "action", "value": "action", "keyword": "ACTION", "type": 0, "flags": 0, "position": 3181}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 3187}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "'postmove'", "value": "postmove", "keyword": null, "type": 7, "flags": 1, "position": 3188}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 3198}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3199}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 3200}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3209}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "vbpmal_scheduled_post_approval", "value": "vbpmal_scheduled_post_approval", "keyword": null, "type": 0, "flags": 0, "position": 3210}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3240}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 3241}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3243}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "scheduled_approval", "value": "scheduled_approval", "keyword": null, "type": 0, "flags": 0, "position": 3244}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3262}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 3263}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3265}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "scheduled_approval", "value": "scheduled_approval", "keyword": null, "type": 0, "flags": 0, "position": 3266}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 3284}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post_id", "value": "post_id", "keyword": null, "type": 0, "flags": 0, "position": 3285}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3292}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 3293}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3294}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 3295}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 3299}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postid", "value": "postid", "keyword": null, "type": 0, "flags": 0, "position": 3300}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3306}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 3307}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3316}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "vbpsmt_additional_user_data", "value": "vbpsmt_additional_user_data", "keyword": null, "type": 0, "flags": 0, "position": 3317}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3344}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 3345}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3347}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "additional_user_data", "value": "additional_user_data", "keyword": null, "type": 0, "flags": 0, "position": 3348}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3368}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 3369}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3371}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "additional_user_data", "value": "additional_user_data", "keyword": null, "type": 0, "flags": 0, "position": 3372}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 3392}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 3393}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 3399}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 3400}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 3404}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 3405}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3411}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 3412}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3421}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "market_pp_post_activation_mapping", "value": "market_pp_post_activation_mapping", "keyword": null, "type": 0, "flags": 0, "position": 3422}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3455}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 3456}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3458}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "paid_post_activation", "value": "paid_post_activation", "keyword": null, "type": 0, "flags": 0, "position": 3459}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3479}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 3480}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3482}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "paid_post_activation", "value": "paid_post_activation", "keyword": null, "type": 0, "flags": 0, "position": 3483}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 3503}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post_id", "value": "post_id", "keyword": null, "type": 0, "flags": 0, "position": 3504}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3511}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 3512}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3513}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 3514}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 3518}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postid", "value": "postid", "keyword": null, "type": 0, "flags": 0, "position": 3519}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3525}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "LEFT JOIN", "value": "LEFT JOIN", "keyword": "LEFT JOIN", "type": 1, "flags": 7, "position": 3526}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3535}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "alm_Model_UserData", "value": "alm_Model_UserData", "keyword": null, "type": 0, "flags": 0, "position": 3536}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3554}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AS", "value": "AS", "keyword": "AS", "type": 1, "flags": 3, "position": 3555}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3557}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "alm_Model_UserData", "value": "alm_Model_UserData", "keyword": null, "type": 0, "flags": 0, "position": 3558}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3576}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON", "value": "ON", "keyword": "ON", "type": 1, "flags": 3, "position": 3577}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3579}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "alm_Model_UserData", "value": "alm_Model_UserData", "keyword": null, "type": 0, "flags": 0, "position": 3580}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 3598}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "user_id", "value": "user_id", "keyword": null, "type": 0, "flags": 0, "position": 3599}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "=", "value": "=", "keyword": null, "type": 2, "flags": 2, "position": 3606}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "user", "value": "user", "keyword": "user", "type": 0, "flags": 0, "position": 3607}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 3611}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "userid", "value": "userid", "keyword": null, "type": 0, "flags": 0, "position": 3612}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3618}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "WHERE", "value": "WHERE", "keyword": "WHERE", "type": 1, "flags": 3, "position": 3619}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3624}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 3625}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 3629}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "postid", "value": "postid", "keyword": null, "type": 0, "flags": 0, "position": 3630}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3636}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "IN", "value": "IN", "keyword": "IN", "type": 1, "flags": 35, "position": 3637}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3639}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 3640}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "0", "value": 0, "keyword": null, "type": 6, "flags": 0, "position": 3641}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3642}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3254399", "value": 3254399, "keyword": null, "type": 6, "flags": 0, "position": 3643}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3650}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3254508", "value": 3254508, "keyword": null, "type": 6, "flags": 0, "position": 3651}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3658}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3254743", "value": 3254743, "keyword": null, "type": 6, "flags": 0, "position": 3659}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3666}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3254817", "value": 3254817, "keyword": null, "type": 6, "flags": 0, "position": 3667}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3674}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3254969", "value": 3254969, "keyword": null, "type": 6, "flags": 0, "position": 3675}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3682}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3255328", "value": 3255328, "keyword": null, "type": 6, "flags": 0, "position": 3683}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3690}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3255582", "value": 3255582, "keyword": null, "type": 6, "flags": 0, "position": 3691}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3698}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3257603", "value": 3257603, "keyword": null, "type": 6, "flags": 0, "position": 3699}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3706}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3257873", "value": 3257873, "keyword": null, "type": 6, "flags": 0, "position": 3707}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3714}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3258126", "value": 3258126, "keyword": null, "type": 6, "flags": 0, "position": 3715}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3722}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3258150", "value": 3258150, "keyword": null, "type": 6, "flags": 0, "position": 3723}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3730}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3258254", "value": 3258254, "keyword": null, "type": 6, "flags": 0, "position": 3731}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3738}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3258272", "value": 3258272, "keyword": null, "type": 6, "flags": 0, "position": 3739}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3746}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3258311", "value": 3258311, "keyword": null, "type": 6, "flags": 0, "position": 3747}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3754}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3260767", "value": 3260767, "keyword": null, "type": 6, "flags": 0, "position": 3755}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3762}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3260770", "value": 3260770, "keyword": null, "type": 6, "flags": 0, "position": 3763}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3770}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3260776", "value": 3260776, "keyword": null, "type": 6, "flags": 0, "position": 3771}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3778}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3261180", "value": 3261180, "keyword": null, "type": 6, "flags": 0, "position": 3779}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3786}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3261263", "value": 3261263, "keyword": null, "type": 6, "flags": 0, "position": 3787}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3794}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3261317", "value": 3261317, "keyword": null, "type": 6, "flags": 0, "position": 3795}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 3802}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "3261318", "value": 3261318, "keyword": null, "type": 6, "flags": 0, "position": 3803}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 3810}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3811}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ORDER BY", "value": "ORDER BY", "keyword": "ORDER BY", "type": 1, "flags": 7, "position": 3812}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 3820}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "post", "value": "post", "keyword": null, "type": 0, "flags": 0, "position": 3821}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ".", "value": ".", "keyword": null, "type": 2, "flags": 16, "position": 3825}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "dateline", "value": "dateline", "keyword": null, "type": 0, "flags": 0, "position": 3826}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": null, "value": null, "keyword": null, "type": 9, "flags": 0, "position": null}], "count": 1035, "idx": 1035}, "DEFAULT_DELIMITER": ";", "delimiter": ";", "delimiterLen": 1, "strict": false, "errors": []}, "parser": {"@type": "PhpMyAdmin\\SqlParser\\Parser", "STATEMENT_PARSERS": {"DESCRIBE": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "DESC": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "EXPLAIN": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "FLUSH": "", "GRANT": "", "HELP": "", "SET PASSWORD": "", "STATUS": "", "USE": "", "ANALYZE": "PhpMyAdmin\\SqlParser\\Statements\\AnalyzeStatement", "BACKUP": "PhpMyAdmin\\SqlParser\\Statements\\BackupStatement", "CHECK": "PhpMyAdmin\\SqlParser\\Statements\\CheckStatement", "CHECKSUM": "PhpMyAdmin\\SqlParser\\Statements\\ChecksumStatement", "OPTIMIZE": "PhpMyAdmin\\SqlParser\\Statements\\OptimizeStatement", "REPAIR": "PhpMyAdmin\\SqlParser\\Statements\\RepairStatement", "RESTORE": "PhpMyAdmin\\SqlParser\\Statements\\RestoreStatement", "SET": "PhpMyAdmin\\SqlParser\\Statements\\SetStatement", "SHOW": "PhpMyAdmin\\SqlParser\\Statements\\ShowStatement", "ALTER": "PhpMyAdmin\\SqlParser\\Statements\\AlterStatement", "CREATE": "PhpMyAdmin\\SqlParser\\Statements\\CreateStatement", "DROP": "PhpMyAdmin\\SqlParser\\Statements\\DropStatement", "RENAME": "PhpMyAdmin\\SqlParser\\Statements\\RenameStatement", "TRUNCATE": "PhpMyAdmin\\SqlParser\\Statements\\TruncateStatement", "CALL": "PhpMyAdmin\\SqlParser\\Statements\\CallStatement", "DELETE": "PhpMyAdmin\\SqlParser\\Statements\\DeleteStatement", "DO": "", "HANDLER": "", "INSERT": "PhpMyAdmin\\SqlParser\\Statements\\InsertStatement", "LOAD DATA": "PhpMyAdmin\\SqlParser\\Statements\\LoadStatement", "REPLACE": "PhpMyAdmin\\SqlParser\\Statements\\ReplaceStatement", "SELECT": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "UPDATE": "PhpMyAdmin\\SqlParser\\Statements\\UpdateStatement", "WITH": "PhpMyAdmin\\SqlParser\\Statements\\WithStatement", "DEALLOCATE": "", "EXECUTE": "", "PREPARE": "", "BEGIN": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "COMMIT": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "ROLLBACK": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "START TRANSACTION": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "PURGE": "PhpMyAdmin\\SqlParser\\Statements\\PurgeStatement", "LOCK": "PhpMyAdmin\\SqlParser\\Statements\\LockStatement", "UNLOCK": "PhpMyAdmin\\SqlParser\\Statements\\LockStatement"}, "KEYWORD_PARSERS": {"PARTITION BY": [], "SUBPARTITION BY": [], "_OPTIONS": {"class": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "field": "options"}, "_END_OPTIONS": {"class": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "field": "end_options"}, "INTERSECT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "EXCEPT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION ALL": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION DISTINCT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "ALTER": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "ANALYZE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "BACKUP": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CALL": {"class": "PhpMyAdmin\\SqlParser\\Components\\FunctionCall", "field": "call"}, "CHECK": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CHECKSUM": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CROSS JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "DROP": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "fields", "options": {"parseField": "table"}}, "FORCE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "FROM": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "from", "options": {"field": "table"}}, "GROUP BY": {"class": "PhpMyAdmin\\SqlParser\\Components\\GroupKeyword", "field": "group"}, "HAVING": {"class": "PhpMyAdmin\\SqlParser\\Components\\Condition", "field": "having"}, "IGNORE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "INTO": {"class": "PhpMyAdmin\\SqlParser\\Components\\IntoKeyword", "field": "into"}, "JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LEFT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LEFT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "ON": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "RIGHT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "RIGHT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "INNER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "FULL JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "FULL OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL LEFT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL RIGHT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL LEFT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL RIGHT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "STRAIGHT_JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LIMIT": {"class": "PhpMyAdmin\\SqlParser\\Components\\Limit", "field": "limit"}, "OPTIMIZE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "ORDER BY": {"class": "PhpMyAdmin\\SqlParser\\Components\\OrderKeyword", "field": "order"}, "PARTITION": {"class": "PhpMyAdmin\\SqlParser\\Components\\ArrayObj", "field": "partition"}, "PROCEDURE": {"class": "PhpMyAdmin\\SqlParser\\Components\\FunctionCall", "field": "procedure"}, "RENAME": {"class": "PhpMyAdmin\\SqlParser\\Components\\RenameOperation", "field": "renames"}, "REPAIR": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "RESTORE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "SET": {"class": "PhpMyAdmin\\SqlParser\\Components\\SetOperation", "field": "set"}, "SELECT": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "expr"}, "TRUNCATE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "UPDATE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "USE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "VALUE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Array2d", "field": "values"}, "VALUES": {"class": "PhpMyAdmin\\SqlParser\\Components\\Array2d", "field": "values"}, "WHERE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Condition", "field": "where"}}, "list": {"@type": "@1"}, "statements": [{"@type": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "OPTIONS": {"ALL": 1, "DISTINCT": 1, "DISTINCTROW": 1, "HIGH_PRIORITY": 2, "MAX_STATEMENT_TIME": [3, "var="], "STRAIGHT_JOIN": 4, "SQL_SMALL_RESULT": 5, "SQL_BIG_RESULT": 6, "SQL_BUFFER_RESULT": 7, "SQL_CACHE": 8, "SQL_NO_CACHE": 8, "SQL_CALC_FOUND_ROWS": 9}, "END_OPTIONS": {"FOR UPDATE": 1, "LOCK IN SHARE MODE": 1}, "CLAUSES": {"SELECT": ["SELECT", 2], "_OPTIONS": ["_OPTIONS", 1], "_SELECT": ["SELECT", 1], "INTO": ["INTO", 3], "FROM": ["FROM", 3], "FORCE": ["FORCE", 1], "USE": ["USE", 1], "IGNORE": ["IGNORE", 3], "PARTITION": ["PARTITION", 3], "JOIN": ["JOIN", 1], "FULL JOIN": ["FULL JOIN", 1], "INNER JOIN": ["INNER JOIN", 1], "LEFT JOIN": ["LEFT JOIN", 1], "LEFT OUTER JOIN": ["LEFT OUTER JOIN", 1], "RIGHT JOIN": ["RIGHT JOIN", 1], "RIGHT OUTER JOIN": ["RIGHT OUTER JOIN", 1], "NATURAL JOIN": ["NATURAL JOIN", 1], "NATURAL LEFT JOIN": ["NATURAL LEFT JOIN", 1], "NATURAL RIGHT JOIN": ["NATURAL RIGHT JOIN", 1], "NATURAL LEFT OUTER JOIN": ["NATURAL LEFT OUTER JOIN", 1], "NATURAL RIGHT OUTER JOIN": ["NATURAL RIGHT JOIN", 1], "WHERE": ["WHERE", 3], "GROUP BY": ["GROUP BY", 3], "HAVING": ["HAVING", 3], "ORDER BY": ["ORDER BY", 3], "LIMIT": ["LIMIT", 3], "PROCEDURE": ["PROCEDURE", 3], "UNION": ["UNION", 1], "EXCEPT": ["EXCEPT", 1], "INTERSECT": ["INTERSECT", 1], "_END_OPTIONS": ["_END_OPTIONS", 1]}, "expr": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "post", "column": null, "expr": "post.*", "alias": null, "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "post", "column": "username", "expr": "post.username", "alias": "postusername", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "post", "column": "ipaddress", "expr": "post.ipaddress", "alias": "ip", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "IF(post.visible = 2, 1, 0)", "alias": "isdeleted", "function": "IF", "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "user", "column": null, "expr": "user.*", "alias": null, "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "userfield", "column": null, "expr": "userfield.*", "alias": null, "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "usertextfield", "column": null, "expr": "usertextfield.*", "alias": null, "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "icon", "column": "title", "expr": "icon.title", "alias": "icontitle", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "icon", "column": "iconpath", "expr": "icon.iconpath", "alias": null, "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "avatar", "column": "avatarpath", "expr": "avatar.avatarpath", "alias": null, "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "NOT ISNULL(customavatar.userid)", "alias": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "function": "ISNULL", "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "customavatar", "column": "dateline", "expr": "customavatar.dateline", "alias": "avatardateline", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "customavatar", "column": "width", "expr": "customavatar.width", "alias": "avwidth", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "customavatar", "column": "height", "expr": "customavatar.height", "alias": "avheight", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "spamlog", "column": "postid", "expr": "spamlog.postid", "alias": "spamlog_postid", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "deletionlog", "column": "userid", "expr": "deletionlog.userid", "alias": "del_userid", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "deletionlog", "column": "username", "expr": "deletionlog.username", "alias": "del_username", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "deletionlog", "column": "reason", "expr": "deletionlog.reason", "alias": "del_reason", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "editlog", "column": "userid", "expr": "editlog.userid", "alias": "edit_userid", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "editlog", "column": "username", "expr": "editlog.username", "alias": "edit_username", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "editlog", "column": "dateline", "expr": "editlog.dateline", "alias": "edit_dateline", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "editlog", "column": "reason", "expr": "editlog.reason", "alias": "edit_reason", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "editlog", "column": "hashistory", "expr": "editlog.hashistory", "alias": null, "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "postparsed", "column": "pagetext_html", "expr": "postparsed.pagetext_html", "alias": null, "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "postparsed", "column": "hasimages", "expr": "postparsed.hasimages", "alias": null, "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "sigparsed", "column": "signatureparsed", "expr": "sigparsed.signatureparsed", "alias": null, "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "sigparsed", "column": "hasimages", "expr": "sigparsed.hasimages", "alias": "sighasimages", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "sigpic", "column": "userid", "expr": "sigpic.userid", "alias": "sigpic", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "sigpic", "column": "dateline", "expr": "sigpic.dateline", "alias": "sigpicdateline", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "sigpic", "column": "width", "expr": "sigpic.width", "alias": "sigpicwidth", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "sigpic", "column": "height", "expr": "sigpic.height", "alias": "sigpicheight", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "IF(displaygroupid=0, user.usergroupid, displaygroupid)", "alias": "displaygroupid", "function": "IF", "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": "infractiongroupid", "expr": "infractiongroupid", "alias": null, "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "post_icon_list", "column": "icon_id_list", "expr": "post_icon_list.icon_id_list", "alias": null, "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "post_icon_list", "column": "is_auto", "expr": "post_icon_list.is_auto", "alias": "icon_is_auto", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "approvedlog", "column": "modid", "expr": "approvedlog.modid", "alias": "approvedmodid", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "approvedlog", "column": "dateline", "expr": "approvedlog.dateline", "alias": "approveddateline", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "approvedlog", "column": "status", "expr": "approvedlog.status", "alias": "approved<PERSON><PERSON>", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "approvedlog", "column": "info", "expr": "approvedlog.info", "alias": "approvedinfo", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "movedlog", "column": "modid", "expr": "movedlog.modid", "alias": "movedmodid", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "movedlog", "column": "dateline", "expr": "movedlog.dateline", "alias": "moveddateline", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "movedlog", "column": "status", "expr": "movedlog.status", "alias": "<PERSON><PERSON><PERSON>", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "movedlog", "column": "info", "expr": "movedlog.info", "alias": "movedinfo", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "( SELECT useragent FROM session WHERE userid=post.userid AND lastactivity > 1644859580 ORDER BY lastactivity DESC LIMIT 1 )", "alias": "useragent", "function": null, "subquery": "SELECT"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "IF ( user.userid IS NOT NULL, (SELECT COUNT(usernoteid) FROM usernote AS usernote WHERE usernote.userid=user.userid AND usernote.priority>=0), 0 )", "alias": "usernotecount", "function": "IF", "subquery": "SELECT"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "deletionlog", "column": "dateline", "expr": "deletionlog.dateline", "alias": "del_dateline", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "scheduled_approval", "column": "defer_time", "expr": "scheduled_approval.defer_time", "alias": "vbpmal_approval_defer_time", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "additional_user_data", "column": "last_year_message_count", "expr": "additional_user_data.last_year_message_count", "alias": null, "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "additional_user_data", "column": "last_year_reputation", "expr": "additional_user_data.last_year_reputation", "alias": null, "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "additional_user_data", "column": "last_year_groan_count", "expr": "additional_user_data.last_year_groan_count", "alias": null, "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "paid_post_activation", "column": "activation_id", "expr": "paid_post_activation.activation_id", "alias": "paid_post_activation_id", "function": null, "subquery": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "alm_Model_UserData", "column": "credits", "expr": "alm_Model_UserData.credits", "alias": null, "function": null, "subquery": null}], "from": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "post", "column": null, "expr": "post", "alias": "post", "function": null, "subquery": null}], "index_hints": null, "partition": null, "where": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["post", "postid"], "isOperator": false, "expr": "post.postid IN (0,3254399,3254508,3254743,3254817,3254969,3255328,3255582,3257603,3257873,3258126,3258150,3258254,3258272,3258311,3260767,3260770,3260776,3261180,3261263,3261317,3261318)"}], "group": null, "having": null, "order": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\OrderKeyword", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "post", "column": "dateline", "expr": "post.dateline", "alias": null, "function": null, "subquery": null}, "type": "ASC"}], "limit": null, "procedure": null, "into": null, "join": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "user", "alias": "user", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["user", "userid", "post"], "isOperator": false, "expr": "(user.userid = post.userid)"}], "using": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "userfield", "column": null, "expr": "userfield", "alias": "userfield", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["userfield", "userid", "user"], "isOperator": false, "expr": "(userfield.userid = user.userid)"}], "using": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "usertextfield", "column": null, "expr": "usertextfield", "alias": "usertextfield", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["usertextfield", "userid", "user"], "isOperator": false, "expr": "(usertextfield.userid = user.userid)"}], "using": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "icon", "column": null, "expr": "icon", "alias": "icon", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["icon", "iconid", "post"], "isOperator": false, "expr": "(icon.iconid = post.iconid)"}], "using": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "avatar", "column": null, "expr": "avatar", "alias": "avatar", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["avatar", "avatarid", "user"], "isOperator": false, "expr": "(avatar.avatarid = user.avatarid)"}], "using": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "customavatar", "column": null, "expr": "customavatar", "alias": "customavatar", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["customavatar", "userid", "user"], "isOperator": false, "expr": "(customavatar.userid = user.userid)"}], "using": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "spamlog", "column": null, "expr": "spamlog", "alias": "spamlog", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["spamlog", "postid", "post"], "isOperator": false, "expr": "(spamlog.postid = post.postid)"}], "using": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "deletionlog", "column": null, "expr": "deletionlog", "alias": "deletionlog", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["post", "postid", "deletionlog", "primaryid"], "isOperator": false, "expr": "(post.postid = deletionlog.primaryid"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": [], "isOperator": true, "expr": "AND"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["deletionlog", "type", "post"], "isOperator": false, "expr": "deletionlog.type = 'post')"}], "using": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "editlog", "column": null, "expr": "editlog", "alias": "editlog", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["editlog", "postid", "post"], "isOperator": false, "expr": "(editlog.postid = post.postid)"}], "using": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "postparsed", "column": null, "expr": "postparsed", "alias": "postparsed", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["postparsed", "postid", "post"], "isOperator": false, "expr": "(postparsed.postid = post.postid"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": [], "isOperator": true, "expr": "AND"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["postparsed", "styleid"], "isOperator": false, "expr": "postparsed.styleid = 23"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": [], "isOperator": true, "expr": "AND"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["postparsed", "languageid"], "isOperator": false, "expr": "postparsed.languageid = 5)"}], "using": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "sigparsed", "column": null, "expr": "sigparsed", "alias": "sigparsed", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["sigparsed", "userid", "user"], "isOperator": false, "expr": "(sigparsed.userid = user.userid"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": [], "isOperator": true, "expr": "AND"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["sigparsed", "styleid"], "isOperator": false, "expr": "sigparsed.styleid = 23"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": [], "isOperator": true, "expr": "AND"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["sigparsed", "languageid"], "isOperator": false, "expr": "sigparsed.languageid = 5)"}], "using": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "sigpic", "column": null, "expr": "sigpic", "alias": "sigpic", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["sigpic", "userid", "post"], "isOperator": false, "expr": "(sigpic.userid = post.userid)"}], "using": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "vbppim_post_icon_list", "column": null, "expr": "vbppim_post_icon_list", "alias": "post_icon_list", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["post_icon_list", "post_id", "post", "postid"], "isOperator": false, "expr": "post_icon_list.post_id=post.postid"}], "using": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "vbpmal_log", "column": null, "expr": "vbpmal_log", "alias": "approvedlog", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["approvedlog", "itemid", "post", "postid"], "isOperator": false, "expr": "(approvedlog.itemid=post.postid"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": [], "isOperator": true, "expr": "AND"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["approvedlog", "action", "postapprove"], "isOperator": false, "expr": "approvedlog.action='postapprove')"}], "using": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "vbpmal_log", "column": null, "expr": "vbpmal_log", "alias": "movedlog", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["movedlog", "itemid", "post", "postid"], "isOperator": false, "expr": "(movedlog.itemid=post.postid"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": [], "isOperator": true, "expr": "AND"}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["movedlog", "action", "postmove"], "isOperator": false, "expr": "movedlog.action='postmove')"}], "using": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "vbpmal_scheduled_post_approval", "column": null, "expr": "vbpmal_scheduled_post_approval", "alias": "scheduled_approval", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["scheduled_approval", "post_id", "post", "postid"], "isOperator": false, "expr": "scheduled_approval.post_id = post.postid"}], "using": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "vbpsmt_additional_user_data", "column": null, "expr": "vbpsmt_additional_user_data", "alias": "additional_user_data", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["additional_user_data", "userid", "post"], "isOperator": false, "expr": "additional_user_data.userid=post.userid"}], "using": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "market_pp_post_activation_mapping", "column": null, "expr": "market_pp_post_activation_mapping", "alias": "paid_post_activation", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["paid_post_activation", "post_id", "post", "postid"], "isOperator": false, "expr": "paid_post_activation.post_id = post.postid"}], "using": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "JOINS": {"CROSS JOIN": "CROSS", "FULL JOIN": "FULL", "FULL OUTER JOIN": "FULL", "INNER JOIN": "INNER", "JOIN": "JOIN", "LEFT JOIN": "LEFT", "LEFT OUTER JOIN": "LEFT", "RIGHT JOIN": "RIGHT", "RIGHT OUTER JOIN": "RIGHT", "NATURAL JOIN": "NATURAL", "NATURAL LEFT JOIN": "NATURAL LEFT", "NATURAL RIGHT JOIN": "NATURAL RIGHT", "NATURAL LEFT OUTER JOIN": "NATURAL LEFT OUTER", "NATURAL RIGHT OUTER JOIN": "NATURAL RIGHT OUTER", "STRAIGHT_JOIN": "STRAIGHT"}, "type": "LEFT", "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "alm_Model_UserData", "column": null, "expr": "alm_Model_UserData", "alias": "alm_Model_UserData", "function": null, "subquery": null}, "on": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\Condition", "DELIMITERS": ["&&", "||", "AND", "OR", "XOR"], "ALLOWED_KEYWORDS": {"ALL": 1, "AND": 1, "BETWEEN": 1, "EXISTS": 1, "IF": 1, "IN": 1, "INTERVAL": 1, "IS": 1, "LIKE": 1, "MATCH": 1, "NOT IN": 1, "NOT NULL": 1, "NOT": 1, "NULL": 1, "OR": 1, "REGEXP": 1, "RLIKE": 1, "SOUNDS": 1, "XOR": 1}, "identifiers": ["alm_Model_UserData", "user_id", "user", "userid"], "isOperator": false, "expr": "alm_Model_UserData.user_id=user.userid"}], "using": null}], "union": [], "end_options": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "first": 0, "last": 1033}], "brackets": 0, "strict": false, "errors": []}, "errors": {"lexer": [], "parser": []}}