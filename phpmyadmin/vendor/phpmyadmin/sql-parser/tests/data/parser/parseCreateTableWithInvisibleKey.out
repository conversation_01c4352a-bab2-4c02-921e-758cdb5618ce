{"query": "CREATE TABLE `animes_comments` (\n  `anime_comment_id` bigint unsigned NOT NULL AUTO_INCREMENT,\n  `anime_id` bigint unsigned NOT NULL,\n  `user_id` bigint unsigned NOT NULL,\n  `comment_text` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL,\n  `comment_at` datetime DEFAULT NULL,\n  PRIMARY KEY (`anime_comment_id`),\n  KEY `animes_comments_animes_fk` (`anime_id`) invisible,\n  KEY `animes_comments_users_fk` (`user_id`),\n  <PERSON><PERSON>Y `comment_at_idx` (`comment_at`) ,\n  CONSTRAINT `animes_comments_animes_fk` FOREIGN KEY (`anime_id`) REFERENCES `animes` (`anime_id`) ON DELETE CASCADE ON UPDATE RESTRICT,\n  CONSTRAINT `animes_comments_users_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT)", "lexer": {"@type": "PhpMyAdmin\\SqlParser\\Lexer", "PARSER_METHODS": ["parseDelimiter", "parseWhitespace", "parseNumber", "parseComment", "parseOperator", "parseBool", "parseString", "parseSymbol", "parseKeyword", "parseLabel", "parseUnknown"], "KEYWORD_NAME_INDICATORS": ["FROM", "SET", "WHERE"], "OPERATOR_NAME_INDICATORS": [",", "."], "str": "CREATE TABLE `animes_comments` (\n  `anime_comment_id` bigint unsigned NOT NULL AUTO_INCREMENT,\n  `anime_id` bigint unsigned NOT NULL,\n  `user_id` bigint unsigned NOT NULL,\n  `comment_text` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL,\n  `comment_at` datetime DEFAULT NULL,\n  PRIMARY KEY (`anime_comment_id`),\n  KEY `animes_comments_animes_fk` (`anime_id`) invisible,\n  KEY `animes_comments_users_fk` (`user_id`),\n  <PERSON><PERSON>Y `comment_at_idx` (`comment_at`) ,\n  CONSTRAINT `animes_comments_animes_fk` FOREIGN KEY (`anime_id`) REFERENCES `animes` (`anime_id`) ON DELETE CASCADE ON UPDATE RESTRICT,\n  CONSTRAINT `animes_comments_users_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT)", "len": 730, "last": 730, "list": {"@type": "PhpMyAdmin\\SqlParser\\TokensList", "tokens": [{"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CREATE", "value": "CREATE", "keyword": "CREATE", "type": 1, "flags": 3, "position": 0}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 6}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "TABLE", "value": "TABLE", "keyword": "TABLE", "type": 1, "flags": 3, "position": 7}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 12}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`animes_comments`", "value": "animes_comments", "keyword": null, "type": 8, "flags": 2, "position": 13}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 30}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 31}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 32}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`anime_comment_id`", "value": "anime_comment_id", "keyword": null, "type": 8, "flags": 2, "position": 35}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 53}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "bigint", "value": "BIGINT", "keyword": "BIGINT", "type": 1, "flags": 11, "position": 54}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 60}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "unsigned", "value": "UNSIGNED", "keyword": "UNSIGNED", "type": 1, "flags": 3, "position": 61}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 69}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NOT NULL", "value": "NOT NULL", "keyword": "NOT NULL", "type": 1, "flags": 7, "position": 70}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 78}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "AUTO_INCREMENT", "value": "AUTO_INCREMENT", "keyword": "AUTO_INCREMENT", "type": 1, "flags": 1, "position": 79}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 93}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 94}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`anime_id`", "value": "anime_id", "keyword": null, "type": 8, "flags": 2, "position": 97}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 107}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "bigint", "value": "BIGINT", "keyword": "BIGINT", "type": 1, "flags": 11, "position": 108}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 114}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "unsigned", "value": "UNSIGNED", "keyword": "UNSIGNED", "type": 1, "flags": 3, "position": 115}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 123}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NOT NULL", "value": "NOT NULL", "keyword": "NOT NULL", "type": 1, "flags": 7, "position": 124}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 132}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 133}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`user_id`", "value": "user_id", "keyword": null, "type": 8, "flags": 2, "position": 136}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 145}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "bigint", "value": "BIGINT", "keyword": "BIGINT", "type": 1, "flags": 11, "position": 146}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 152}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "unsigned", "value": "UNSIGNED", "keyword": "UNSIGNED", "type": 1, "flags": 3, "position": 153}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 161}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NOT NULL", "value": "NOT NULL", "keyword": "NOT NULL", "type": 1, "flags": 7, "position": 162}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 170}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 171}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`comment_text`", "value": "comment_text", "keyword": null, "type": 8, "flags": 2, "position": 174}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 188}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "<PERSON><PERSON><PERSON>", "value": "VARCHAR", "keyword": "VARCHAR", "type": 1, "flags": 11, "position": 189}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 196}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "500", "value": 500, "keyword": null, "type": 6, "flags": 0, "position": 197}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 200}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 201}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "COLLATE", "value": "COLLATE", "keyword": "COLLATE", "type": 1, "flags": 3, "position": 202}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 209}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "utf8mb4_general_ci", "value": "utf8mb4_general_ci", "keyword": null, "type": 0, "flags": 0, "position": 210}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 228}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DEFAULT", "value": "DEFAULT", "keyword": "DEFAULT", "type": 1, "flags": 35, "position": 229}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 236}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NULL", "value": "NULL", "keyword": "NULL", "type": 1, "flags": 3, "position": 237}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 241}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 242}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`comment_at`", "value": "comment_at", "keyword": null, "type": 8, "flags": 2, "position": 245}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 257}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "datetime", "value": "datetime", "keyword": "DATETIME", "type": 1, "flags": 9, "position": 258}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 266}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "DEFAULT", "value": "DEFAULT", "keyword": "DEFAULT", "type": 1, "flags": 35, "position": 267}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 274}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "NULL", "value": "NULL", "keyword": "NULL", "type": 1, "flags": 3, "position": 275}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 279}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 280}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "PRIMARY KEY", "value": "PRIMARY KEY", "keyword": "PRIMARY KEY", "type": 1, "flags": 23, "position": 283}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 294}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 295}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`anime_comment_id`", "value": "anime_comment_id", "keyword": null, "type": 8, "flags": 2, "position": 296}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 314}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 315}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 316}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "KEY", "value": "KEY", "keyword": "KEY", "type": 1, "flags": 19, "position": 319}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 322}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`animes_comments_animes_fk`", "value": "animes_comments_animes_fk", "keyword": null, "type": 8, "flags": 2, "position": 323}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 350}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 351}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`anime_id`", "value": "anime_id", "keyword": null, "type": 8, "flags": 2, "position": 352}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 362}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 363}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "invisible", "value": "invisible", "keyword": null, "type": 0, "flags": 0, "position": 364}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 373}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 374}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "KEY", "value": "KEY", "keyword": "KEY", "type": 1, "flags": 19, "position": 377}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 380}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`animes_comments_users_fk`", "value": "animes_comments_users_fk", "keyword": null, "type": 8, "flags": 2, "position": 381}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 407}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 408}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`user_id`", "value": "user_id", "keyword": null, "type": 8, "flags": 2, "position": 409}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 418}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 419}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 420}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "KEY", "value": "KEY", "keyword": "KEY", "type": 1, "flags": 19, "position": 423}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 426}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`comment_at_idx`", "value": "comment_at_idx", "keyword": null, "type": 8, "flags": 2, "position": 427}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 443}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 444}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`comment_at`", "value": "comment_at", "keyword": null, "type": 8, "flags": 2, "position": 445}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 457}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 458}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 459}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 460}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CONSTRAINT", "value": "CONSTRAINT", "keyword": "CONSTRAINT", "type": 1, "flags": 3, "position": 463}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 473}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`animes_comments_animes_fk`", "value": "animes_comments_animes_fk", "keyword": null, "type": 8, "flags": 2, "position": 474}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 501}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FOREIGN KEY", "value": "FOREIGN KEY", "keyword": "FOREIGN KEY", "type": 1, "flags": 23, "position": 502}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 513}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 514}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`anime_id`", "value": "anime_id", "keyword": null, "type": 8, "flags": 2, "position": 515}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 525}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 526}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "REFERENCES", "value": "REFERENCES", "keyword": "REFERENCES", "type": 1, "flags": 3, "position": 527}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 537}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`animes`", "value": "animes", "keyword": null, "type": 8, "flags": 2, "position": 538}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 546}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 547}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`anime_id`", "value": "anime_id", "keyword": null, "type": 8, "flags": 2, "position": 548}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 558}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 559}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON DELETE", "value": "ON DELETE", "keyword": "ON DELETE", "type": 1, "flags": 7, "position": 560}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 569}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CASCADE", "value": "CASCADE", "keyword": "CASCADE", "type": 1, "flags": 3, "position": 570}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 577}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON UPDATE", "value": "ON UPDATE", "keyword": "ON UPDATE", "type": 1, "flags": 7, "position": 578}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 587}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "RESTRICT", "value": "RESTRICT", "keyword": "RESTRICT", "type": 1, "flags": 3, "position": 588}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ",", "value": ",", "keyword": null, "type": 2, "flags": 16, "position": 596}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "\n  ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 597}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CONSTRAINT", "value": "CONSTRAINT", "keyword": "CONSTRAINT", "type": 1, "flags": 3, "position": 600}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 610}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`animes_comments_users_fk`", "value": "animes_comments_users_fk", "keyword": null, "type": 8, "flags": 2, "position": 611}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 637}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "FOREIGN KEY", "value": "FOREIGN KEY", "keyword": "FOREIGN KEY", "type": 1, "flags": 23, "position": 638}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 649}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 650}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`user_id`", "value": "user_id", "keyword": null, "type": 8, "flags": 2, "position": 651}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 660}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 661}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "REFERENCES", "value": "REFERENCES", "keyword": "REFERENCES", "type": 1, "flags": 3, "position": 662}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 672}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`users`", "value": "users", "keyword": null, "type": 8, "flags": 2, "position": 673}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 680}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "(", "value": "(", "keyword": null, "type": 2, "flags": 16, "position": 681}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "`user_id`", "value": "user_id", "keyword": null, "type": 8, "flags": 2, "position": 682}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 691}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 692}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON DELETE", "value": "ON DELETE", "keyword": "ON DELETE", "type": 1, "flags": 7, "position": 693}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 702}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "CASCADE", "value": "CASCADE", "keyword": "CASCADE", "type": 1, "flags": 3, "position": 703}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 710}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "ON UPDATE", "value": "ON UPDATE", "keyword": "ON UPDATE", "type": 1, "flags": 7, "position": 711}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": " ", "value": " ", "keyword": null, "type": 3, "flags": 0, "position": 720}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": "RESTRICT", "value": "RESTRICT", "keyword": "RESTRICT", "type": 1, "flags": 3, "position": 721}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": ")", "value": ")", "keyword": null, "type": 2, "flags": 16, "position": 729}, {"@type": "PhpMyAdmin\\SqlParser\\Token", "token": null, "value": null, "keyword": null, "type": 9, "flags": 0, "position": null}], "count": 153, "idx": 153}, "DEFAULT_DELIMITER": ";", "delimiter": ";", "delimiterLen": 1, "strict": false, "errors": []}, "parser": {"@type": "PhpMyAdmin\\SqlParser\\Parser", "STATEMENT_PARSERS": {"DESCRIBE": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "DESC": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "EXPLAIN": "PhpMyAdmin\\SqlParser\\Statements\\ExplainStatement", "FLUSH": "", "GRANT": "", "HELP": "", "SET PASSWORD": "", "STATUS": "", "USE": "", "ANALYZE": "PhpMyAdmin\\SqlParser\\Statements\\AnalyzeStatement", "BACKUP": "PhpMyAdmin\\SqlParser\\Statements\\BackupStatement", "CHECK": "PhpMyAdmin\\SqlParser\\Statements\\CheckStatement", "CHECKSUM": "PhpMyAdmin\\SqlParser\\Statements\\ChecksumStatement", "OPTIMIZE": "PhpMyAdmin\\SqlParser\\Statements\\OptimizeStatement", "REPAIR": "PhpMyAdmin\\SqlParser\\Statements\\RepairStatement", "RESTORE": "PhpMyAdmin\\SqlParser\\Statements\\RestoreStatement", "SET": "PhpMyAdmin\\SqlParser\\Statements\\SetStatement", "SHOW": "PhpMyAdmin\\SqlParser\\Statements\\ShowStatement", "ALTER": "PhpMyAdmin\\SqlParser\\Statements\\AlterStatement", "CREATE": "PhpMyAdmin\\SqlParser\\Statements\\CreateStatement", "DROP": "PhpMyAdmin\\SqlParser\\Statements\\DropStatement", "RENAME": "PhpMyAdmin\\SqlParser\\Statements\\RenameStatement", "TRUNCATE": "PhpMyAdmin\\SqlParser\\Statements\\TruncateStatement", "CALL": "PhpMyAdmin\\SqlParser\\Statements\\CallStatement", "DELETE": "PhpMyAdmin\\SqlParser\\Statements\\DeleteStatement", "DO": "", "HANDLER": "", "INSERT": "PhpMyAdmin\\SqlParser\\Statements\\InsertStatement", "LOAD DATA": "PhpMyAdmin\\SqlParser\\Statements\\LoadStatement", "REPLACE": "PhpMyAdmin\\SqlParser\\Statements\\ReplaceStatement", "SELECT": "PhpMyAdmin\\SqlParser\\Statements\\SelectStatement", "UPDATE": "PhpMyAdmin\\SqlParser\\Statements\\UpdateStatement", "WITH": "PhpMyAdmin\\SqlParser\\Statements\\WithStatement", "DEALLOCATE": "", "EXECUTE": "", "PREPARE": "", "BEGIN": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "COMMIT": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "ROLLBACK": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "START TRANSACTION": "PhpMyAdmin\\SqlParser\\Statements\\TransactionStatement", "PURGE": "PhpMyAdmin\\SqlParser\\Statements\\PurgeStatement", "LOCK": "PhpMyAdmin\\SqlParser\\Statements\\LockStatement", "UNLOCK": "PhpMyAdmin\\SqlParser\\Statements\\LockStatement"}, "KEYWORD_PARSERS": {"PARTITION BY": [], "SUBPARTITION BY": [], "_OPTIONS": {"class": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "field": "options"}, "_END_OPTIONS": {"class": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "field": "end_options"}, "INTERSECT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "EXCEPT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION ALL": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "UNION DISTINCT": {"class": "PhpMyAdmin\\SqlParser\\Components\\UnionKeyword", "field": "union"}, "ALTER": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "ANALYZE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "BACKUP": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CALL": {"class": "PhpMyAdmin\\SqlParser\\Components\\FunctionCall", "field": "call"}, "CHECK": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CHECKSUM": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "CROSS JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "DROP": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "fields", "options": {"parseField": "table"}}, "FORCE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "FROM": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "from", "options": {"field": "table"}}, "GROUP BY": {"class": "PhpMyAdmin\\SqlParser\\Components\\GroupKeyword", "field": "group"}, "HAVING": {"class": "PhpMyAdmin\\SqlParser\\Components\\Condition", "field": "having"}, "IGNORE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "INTO": {"class": "PhpMyAdmin\\SqlParser\\Components\\IntoKeyword", "field": "into"}, "JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LEFT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LEFT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "ON": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "RIGHT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "RIGHT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "INNER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "FULL JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "FULL OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL LEFT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL RIGHT JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL LEFT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "NATURAL RIGHT OUTER JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "STRAIGHT_JOIN": {"class": "PhpMyAdmin\\SqlParser\\Components\\JoinKeyword", "field": "join"}, "LIMIT": {"class": "PhpMyAdmin\\SqlParser\\Components\\Limit", "field": "limit"}, "OPTIMIZE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "ORDER BY": {"class": "PhpMyAdmin\\SqlParser\\Components\\OrderKeyword", "field": "order"}, "PARTITION": {"class": "PhpMyAdmin\\SqlParser\\Components\\ArrayObj", "field": "partition"}, "PROCEDURE": {"class": "PhpMyAdmin\\SqlParser\\Components\\FunctionCall", "field": "procedure"}, "RENAME": {"class": "PhpMyAdmin\\SqlParser\\Components\\RenameOperation", "field": "renames"}, "REPAIR": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "RESTORE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "SET": {"class": "PhpMyAdmin\\SqlParser\\Components\\SetOperation", "field": "set"}, "SELECT": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "expr"}, "TRUNCATE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Expression", "field": "table", "options": {"parseField": "table"}}, "UPDATE": {"class": "PhpMyAdmin\\SqlParser\\Components\\ExpressionArray", "field": "tables", "options": {"parseField": "table"}}, "USE": {"class": "PhpMyAdmin\\SqlParser\\Components\\IndexHint", "field": "index_hints"}, "VALUE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Array2d", "field": "values"}, "VALUES": {"class": "PhpMyAdmin\\SqlParser\\Components\\Array2d", "field": "values"}, "WHERE": {"class": "PhpMyAdmin\\SqlParser\\Components\\Condition", "field": "where"}}, "list": {"@type": "@1"}, "statements": [{"@type": "PhpMyAdmin\\SqlParser\\Statements\\CreateStatement", "OPTIONS": {"TEMPORARY": 1, "OR REPLACE": 2, "ALGORITHM": [3, "var="], "DEFINER": [4, "expr="], "SQL SECURITY": [5, "var"], "DATABASE": 6, "EVENT": 6, "FUNCTION": 6, "INDEX": 6, "UNIQUE INDEX": 6, "FULLTEXT INDEX": 6, "SPATIAL INDEX": 6, "PROCEDURE": 6, "SERVER": 6, "TABLE": 6, "TABLESPACE": 6, "TRIGGER": 6, "USER": 6, "VIEW": 6, "SCHEMA": 6, "IF NOT EXISTS": 7}, "DB_OPTIONS": {"CHARACTER SET": [1, "var="], "CHARSET": [1, "var="], "DEFAULT CHARACTER SET": [1, "var="], "DEFAULT CHARSET": [1, "var="], "DEFAULT COLLATE": [2, "var="], "COLLATE": [2, "var="]}, "TABLE_OPTIONS": {"ENGINE": [1, "var="], "AUTO_INCREMENT": [2, "var="], "AVG_ROW_LENGTH": [3, "var"], "CHARACTER SET": [4, "var="], "CHARSET": [4, "var="], "DEFAULT CHARACTER SET": [4, "var="], "DEFAULT CHARSET": [4, "var="], "CHECKSUM": [5, "var"], "DEFAULT COLLATE": [6, "var="], "COLLATE": [6, "var="], "COMMENT": [7, "var="], "CONNECTION": [8, "var"], "DATA DIRECTORY": [9, "var"], "DELAY_KEY_WRITE": [10, "var"], "INDEX DIRECTORY": [11, "var"], "INSERT_METHOD": [12, "var"], "KEY_BLOCK_SIZE": [13, "var"], "MAX_ROWS": [14, "var"], "MIN_ROWS": [15, "var"], "PACK_KEYS": [16, "var"], "PASSWORD": [17, "var"], "ROW_FORMAT": [18, "var"], "TABLESPACE": [19, "var"], "STORAGE": [20, "var"], "UNION": [21, "var"], "PAGE_COMPRESSED": [22, "var"], "PAGE_COMPRESSION_LEVEL": [23, "var"]}, "FUNC_OPTIONS": {"NOT": [2, "var"], "FUNCTION": [3, "var="], "PROCEDURE": [3, "var="], "CONTAINS SQL": 4, "NO SQL": 4, "READS SQL DATA": 4, "MODIFIES SQL DATA": 4, "SQL SECURITY": [6, "var"], "LANGUAGE": [7, "var"], "COMMENT": [8, "var"], "CREATE": 1, "DETERMINISTIC": 2}, "TRIGGER_OPTIONS": {"BEFORE": 1, "AFTER": 1, "INSERT": 2, "UPDATE": 2, "DELETE": 2}, "name": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "animes_comments", "column": null, "expr": "`animes_comments`", "alias": null, "function": null, "subquery": null}, "entityOptions": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}, "fields": [{"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "anime_comment_id", "isConstraint": null, "type": {"@type": "PhpMyAdmin\\SqlParser\\Components\\DataType", "DATA_TYPE_OPTIONS": {"BINARY": 1, "CHARACTER SET": [2, "var"], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "UNSIGNED": 4, "ZEROFILL": 5}, "name": "BIGINT", "parameters": [], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"4": "UNSIGNED"}}}, "key": null, "references": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"1": "NOT NULL", "3": "AUTO_INCREMENT"}}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "anime_id", "isConstraint": null, "type": {"@type": "PhpMyAdmin\\SqlParser\\Components\\DataType", "DATA_TYPE_OPTIONS": {"BINARY": 1, "CHARACTER SET": [2, "var"], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "UNSIGNED": 4, "ZEROFILL": 5}, "name": "BIGINT", "parameters": [], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"4": "UNSIGNED"}}}, "key": null, "references": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"1": "NOT NULL"}}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "user_id", "isConstraint": null, "type": {"@type": "PhpMyAdmin\\SqlParser\\Components\\DataType", "DATA_TYPE_OPTIONS": {"BINARY": 1, "CHARACTER SET": [2, "var"], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "UNSIGNED": 4, "ZEROFILL": 5}, "name": "BIGINT", "parameters": [], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"4": "UNSIGNED"}}}, "key": null, "references": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"1": "NOT NULL"}}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "comment_text", "isConstraint": null, "type": {"@type": "PhpMyAdmin\\SqlParser\\Components\\DataType", "DATA_TYPE_OPTIONS": {"BINARY": 1, "CHARACTER SET": [2, "var"], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "UNSIGNED": 4, "ZEROFILL": 5}, "name": "VARCHAR", "parameters": ["500"], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"3": {"name": "COLLATE", "equals": false, "expr": "utf8mb4_general_ci", "value": "utf8mb4_general_ci"}}}}, "key": null, "references": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"2": {"name": "DEFAULT", "equals": false, "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "NULL", "alias": null, "function": null, "subquery": null}, "value": "NULL"}}}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "comment_at", "isConstraint": null, "type": {"@type": "PhpMyAdmin\\SqlParser\\Components\\DataType", "DATA_TYPE_OPTIONS": {"BINARY": 1, "CHARACTER SET": [2, "var"], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "UNSIGNED": 4, "ZEROFILL": 5}, "name": "DATETIME", "parameters": [], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "key": null, "references": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"2": {"name": "DEFAULT", "equals": false, "expr": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": null, "column": null, "expr": "NULL", "alias": null, "function": null, "subquery": null}, "value": "NULL"}}}}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": null, "isConstraint": null, "type": null, "key": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Key", "KEY_OPTIONS": {"KEY_BLOCK_SIZE": [1, "var="], "USING": [2, "var"], "WITH PARSER": [3, "var"], "COMMENT": [4, "var"], "CLUSTERING": [4, "var="], "ENGINE_ATTRIBUTE": [5, "var="], "SECONDARY_ENGINE_ATTRIBUTE": [5, "var="], "VISIBLE": 6, "INVISIBLE": 6, "IGNORED": 10, "NOT IGNORED": 10}, "name": null, "columns": [{"name": "anime_comment_id"}], "type": "PRIMARY KEY", "expr": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "references": null, "options": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": null, "isConstraint": null, "type": null, "key": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Key", "KEY_OPTIONS": {"KEY_BLOCK_SIZE": [1, "var="], "USING": [2, "var"], "WITH PARSER": [3, "var"], "COMMENT": [4, "var"], "CLUSTERING": [4, "var="], "ENGINE_ATTRIBUTE": [5, "var="], "SECONDARY_ENGINE_ATTRIBUTE": [5, "var="], "VISIBLE": 6, "INVISIBLE": 6, "IGNORED": 10, "NOT IGNORED": 10}, "name": "animes_comments_animes_fk", "columns": [{"name": "anime_id"}], "type": "KEY", "expr": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"6": "invisible"}}}, "references": null, "options": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": null, "isConstraint": null, "type": null, "key": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Key", "KEY_OPTIONS": {"KEY_BLOCK_SIZE": [1, "var="], "USING": [2, "var"], "WITH PARSER": [3, "var"], "COMMENT": [4, "var"], "CLUSTERING": [4, "var="], "ENGINE_ATTRIBUTE": [5, "var="], "SECONDARY_ENGINE_ATTRIBUTE": [5, "var="], "VISIBLE": 6, "INVISIBLE": 6, "IGNORED": 10, "NOT IGNORED": 10}, "name": "animes_comments_users_fk", "columns": [{"name": "user_id"}], "type": "KEY", "expr": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "references": null, "options": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": null, "isConstraint": null, "type": null, "key": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Key", "KEY_OPTIONS": {"KEY_BLOCK_SIZE": [1, "var="], "USING": [2, "var"], "WITH PARSER": [3, "var"], "COMMENT": [4, "var"], "CLUSTERING": [4, "var="], "ENGINE_ATTRIBUTE": [5, "var="], "SECONDARY_ENGINE_ATTRIBUTE": [5, "var="], "VISIBLE": 6, "INVISIBLE": 6, "IGNORED": 10, "NOT IGNORED": 10}, "name": "comment_at_idx", "columns": [{"name": "comment_at"}], "type": "KEY", "expr": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "references": null, "options": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "animes_comments_animes_fk", "isConstraint": true, "type": null, "key": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Key", "KEY_OPTIONS": {"KEY_BLOCK_SIZE": [1, "var="], "USING": [2, "var"], "WITH PARSER": [3, "var"], "COMMENT": [4, "var"], "CLUSTERING": [4, "var="], "ENGINE_ATTRIBUTE": [5, "var="], "SECONDARY_ENGINE_ATTRIBUTE": [5, "var="], "VISIBLE": 6, "INVISIBLE": 6, "IGNORED": 10, "NOT IGNORED": 10}, "name": null, "columns": [{"name": "anime_id"}], "type": "FOREIGN KEY", "expr": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "references": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Reference", "REFERENCES_OPTIONS": {"MATCH": [1, "var"], "ON DELETE": [2, "var"], "ON UPDATE": [3, "var"]}, "table": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "animes", "column": null, "expr": "`animes`", "alias": null, "function": null, "subquery": null}, "columns": ["anime_id"], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"2": {"name": "ON DELETE", "equals": false, "expr": "CASCADE", "value": "CASCADE"}, "3": {"name": "ON UPDATE", "equals": false, "expr": "RESTRICT", "value": "RESTRICT"}}}}, "options": null}, {"@type": "PhpMyAdmin\\SqlParser\\Components\\CreateDefinition", "FIELD_OPTIONS": {"_UNSORTED": true, "NOT NULL": 1, "NULL": 1, "DEFAULT": [2, "expr", {"breakOnAlias": true}], "CHARSET": [2, "var"], "COLLATE": [3, "var"], "AUTO_INCREMENT": 3, "PRIMARY": 4, "PRIMARY KEY": 4, "UNIQUE": 4, "UNIQUE KEY": 4, "COMMENT": [5, "var"], "COLUMN_FORMAT": [6, "var"], "ON UPDATE": [7, "expr"], "GENERATED ALWAYS": 8, "AS": [9, "expr", {"parenthesesDelimited": true}], "VIRTUAL": 10, "PERSISTENT": 11, "STORED": 11, "CHECK": [12, "expr", {"parenthesesDelimited": true}], "INVISIBLE": 13, "ENFORCED": 14, "NOT": 15, "COMPRESSED": 16}, "name": "animes_comments_users_fk", "isConstraint": true, "type": null, "key": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Key", "KEY_OPTIONS": {"KEY_BLOCK_SIZE": [1, "var="], "USING": [2, "var"], "WITH PARSER": [3, "var"], "COMMENT": [4, "var"], "CLUSTERING": [4, "var="], "ENGINE_ATTRIBUTE": [5, "var="], "SECONDARY_ENGINE_ATTRIBUTE": [5, "var="], "VISIBLE": 6, "INVISIBLE": 6, "IGNORED": 10, "NOT IGNORED": 10}, "name": null, "columns": [{"name": "user_id"}], "type": "FOREIGN KEY", "expr": null, "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": []}}, "references": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Reference", "REFERENCES_OPTIONS": {"MATCH": [1, "var"], "ON DELETE": [2, "var"], "ON UPDATE": [3, "var"]}, "table": {"@type": "PhpMyAdmin\\SqlParser\\Components\\Expression", "ALLOWED_KEYWORDS": {"AND": 1, "AS": 1, "BETWEEN": 1, "CASE": 1, "DUAL": 1, "DIV": 1, "IS": 1, "MOD": 1, "NOT": 1, "NOT NULL": 1, "NULL": 1, "OR": 1, "OVER": 1, "REGEXP": 1, "RLIKE": 1, "XOR": 1}, "database": null, "table": "users", "column": null, "expr": "`users`", "alias": null, "function": null, "subquery": null}, "columns": ["user_id"], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"2": {"name": "ON DELETE", "equals": false, "expr": "CASCADE", "value": "CASCADE"}, "3": {"name": "ON UPDATE", "equals": false, "expr": "RESTRICT", "value": "RESTRICT"}}}}, "options": null}], "with": null, "select": null, "like": null, "partitionBy": null, "partitionsNum": null, "subpartitionBy": null, "subpartitionsNum": null, "partitions": null, "table": null, "return": null, "parameters": null, "body": [], "CLAUSES": [], "END_OPTIONS": [], "options": {"@type": "PhpMyAdmin\\SqlParser\\Components\\OptionsArray", "options": {"6": "TABLE"}}, "first": 0, "last": 152}], "brackets": 0, "strict": false, "errors": []}, "errors": {"lexer": [], "parser": []}}