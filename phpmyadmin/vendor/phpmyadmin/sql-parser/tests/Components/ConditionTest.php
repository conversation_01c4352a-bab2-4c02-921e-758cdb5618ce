<?php

declare(strict_types=1);

namespace PhpMyAdmin\SqlParser\Tests\Components;

use Php<PERSON>yAdmin\SqlParser\Components\Condition;
use PhpMyAdmin\SqlParser\Parser;
use PhpMyAdmin\SqlParser\Tests\TestCase;

class ConditionTest extends TestCase
{
    public function testParse(): void
    {
        $component = Condition::parse(new Parser(), $this->getTokensList('/* id = */ id = 10'));
        $this->assertEquals($component[0]->expr, 'id = 10');
    }

    public function testParseBetween(): void
    {
        $component = Condition::parse(
            new Parser(),
            $this->getTokensList('(id BETWEEN 10 AND 20) OR (id BETWEEN 30 AND 40)')
        );
        $this->assertEquals($component[0]->expr, '(id BETWEEN 10 AND 20)');
        $this->assertEquals($component[1]->expr, 'OR');
        $this->assertEquals($component[2]->expr, '(id BETWEEN 30 AND 40)');
    }
}
