#!/usr/bin/env php
<?php
declare(strict_types=1);

$files = [
    __DIR__ . "/../vendor/autoload.php",
    __DIR__ . "/../../vendor/autoload.php",
    __DIR__ . "/../../../autoload.php",
    "vendor/autoload.php"
];

$found = false;
foreach ($files as $file) {
    if (file_exists($file)) {
        require_once $file;
        $found = true;
        break;
    }
}

if (!$found) {
    die(
        "You need to set up the project dependencies using the following commands:" . PHP_EOL .
        "curl -sS https://getcomposer.org/installer | php" . PHP_EOL .
        "php composer.phar install" . PHP_EOL
    );
}

$cli = new PhpMyAdmin\SqlParser\Utils\CLI();
exit($cli->runHighlight());
