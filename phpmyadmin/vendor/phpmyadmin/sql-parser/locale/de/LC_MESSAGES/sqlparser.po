# Automatically generated <>, 2010.
msgid ""
msgstr ""
"Project-Id-Version: phpMyAdmin-docs 4.0.0-dev\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2022-03-19 21:46+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: German <https://hosted.weblate.org/projects/phpmyadmin/"
"sql-parser/de/>\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.12-dev\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "Noch nicht implementiert."

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr ""
"Eine neue Anweisung wurde gefunden, aber kein Trennzeichen zwischen ihm und "
"dem vorhergehenden."

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr "Fehlendes Komma vor einer neuen ALTER-Operation."

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "Unerkannte ALTER-Operation."

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "%1$d Werte erwartet, aber %2$d gefunden."

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr ""
"Eine öffnende Klammer, gefolgt von einer Liste von Werten, wurde erwartet."

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr "Eine öffnende Klammer wurde erwartet."

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "Unerwartetes Schlüsselwort."

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "Unerwartetes Ende eines CASE Ausdrucks"

#: src/Components/CaseExpression.php:215
msgid "Potential duplicate alias of CASE expression."
msgstr "Möglicher doppelter Alias eines CASE Ausdrucks."

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr "Ein Alias wurde nach AS erwartet, aber stattdessen "

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "Ein Alias wurde zuvor gefunden."

#: src/Components/CaseExpression.php:255
msgid "An alias was expected after AS."
msgstr "Ein Alias wurde nach AS erwartet."

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""
"Ein Symbolname wurde erwartet! Ein reserviertes Schlüsselwort kann nicht "
"ohne Backquotes als Tabellenname verwendet werden."

#: src/Components/CreateDefinition.php:267
msgid "A symbol name was expected!"
msgstr "Ein Symbolname wurde erwartet!"

#: src/Components/CreateDefinition.php:299
msgid "A comma or a closing bracket was expected."
msgstr "Ein Komma oder eine schließende Klammer wurde erwartet."

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr "Eine schließende Klammer wurde erwartet."

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "Unerkannter Datentyp."

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
msgid "An expression was expected."
msgstr "Ein Ausdruck wurde erwartet."

#: src/Components/Expression.php:258 src/Components/Expression.php:419
msgid "An alias was expected."
msgstr "Ein Alias wurde erwartet."

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "Unerwarteter Punkt."

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "Unerwartetes Zeichen."

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr "Ein Offset wurde erwartet."

#: src/Components/LockExpression.php:94
msgid "Unexpected end of LOCK expression."
msgstr "Unerwartetes Ende eines LOCK-Ausdrucks."

#: src/Components/LockExpression.php:204
msgid "Unexpected end of Lock expression."
msgstr "Unerwartetes Ende eines LOCK-Ausdrucks."

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "Diese Option widerspricht „%1$s“."

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "Der ursprüngliche Tabellenname wurde erwartet."

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "Schlüsselwort „TO“ wurde erwartet."

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "Der neue Tabellenname wurde erwartet."

#: src/Components/RenameOperation.php:143
msgid "A rename operation was expected."
msgstr "Eine Umbenennungsoperation wurde erwartet."

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "Fehlender Ausdruck."

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "Unerwartetes Zeichen."

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr "Erwartete Whitespace vor dem Trennzeichen."

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr "Erwartete Trennzeichen."

#: src/Lexer.php:937
#, php-format
msgid "Ending quote %1$s was expected."
msgstr "Schließendes Anführungszeichen %1$s wurde erzeugt."

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "Variablenname wurde erwartet."

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "Unerwarteter Statement-Anfang."

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "Unerkannte Statement-Typ."

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "Bisher wurde keine Transaktion gestartet."

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr "Diese Art von Klausel wurde zuvor analysiert."

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "Unerkanntes Schlüsselwort."

#: src/Statement.php:398
msgid "Keyword at end of statement."
msgstr "Schlüsselwort am Ende der Anweisung."

#: src/Statement.php:543
msgid "Unexpected ordering of clauses."
msgstr "Unerwartete Datensatzanforderung."

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr "Der Name der Entität wurde erwartet."

#: src/Statements/CreateStatement.php:601
msgid "A table name was expected."
msgstr "Tabellenname wurde erwartet."

#: src/Statements/CreateStatement.php:606
msgid "At least one column definition was expected."
msgstr "Mindestens eine Felddefinition wurde erwartet."

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "Ein „RETURNS“-Schlüsselwort wurde erwartet."

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr "Diese Art von Klausel ist in Mehr-Tabellen-Anfragen nicht zulässig."

#: src/Statements/LockStatement.php:123
msgid "Unexpected end of LOCK statement."
msgstr "Unerwartetes Ende einer LOCK-Anweisung."

#: src/Statements/PurgeStatement.php:141
msgid "Unexpected keyword"
msgstr "Unerwartetes Schlüsselwort"

#: src/Statements/WithStatement.php:119
msgid "The name of the CTE was expected."
msgstr "Der Name des CTE wurde erwartet."

#: src/Statements/WithStatement.php:138
msgid "AS keyword was expected."
msgstr "Das Schlüsselwort AS wurde erwartet."

#: src/Statements/WithStatement.php:154
msgid "Subquery of the CTE was expected."
msgstr "Es wurde eine Unterabfrage des CTE erwartet."

#: src/Statements/WithStatement.php:271
msgid "Unexpected end of the WITH CTE."
msgstr "Unerwartetes Ende des WITH CTE."

#~ msgid "error #1"
#~ msgstr "Fehler #1"

#~ msgid "strict error"
#~ msgstr "strikter Fehler"
