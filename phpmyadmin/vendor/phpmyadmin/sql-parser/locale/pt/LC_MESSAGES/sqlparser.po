#
msgid ""
msgstr ""
"Project-Id-Version: SQL parser 5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2020-04-19 10:11+0000\n"
"Last-Translator: ssantos <<EMAIL>>\n"
"Language-Team: Portuguese <https://hosted.weblate.org/projects/phpmyadmin/"
"sql-parser/pt/>\n"
"Language: pt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.0.2-dev\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "Ainda não foi implementado."

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr ""
"Uma nova instrução foi encontrada, mas nenhum delimitador entre ela e a "
"anterior."

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr "Falta uma vírgula antes do início de uma nova operação de alteração."

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "Operação de alteração não reconhecida."

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "Eram esperados %1$d valores, mas foram encontrados %2$d."

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr ""
"Uma abertura de chaveta seguida de um conjunto de valores era esperada."

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr "Uma abertura de chaveta era esperada."

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "Palavra-chave inesperada."

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "Fim da expressão CASE inesperada"

#: src/Components/CaseExpression.php:215
msgid "Potential duplicate alias of CASE expression."
msgstr "Pseudónimo potencialmente duplicado da expressão CASE."

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr "Um pseudónimo esperado após AS, mas tem "

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "Um alias foi previamente encontrado."

#: src/Components/CaseExpression.php:255
msgid "An alias was expected after AS."
msgstr "Um pseudónimo foi esperado depois do AS."

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""
"Um nome de símbolo era esperado! Uma chave reservada não pode ser usada como "
"um nome de coluna sem as chaves."

#: src/Components/CreateDefinition.php:267
msgid "A symbol name was expected!"
msgstr "Um nome do símbolo era esperado!"

#: src/Components/CreateDefinition.php:299
msgid "A comma or a closing bracket was expected."
msgstr "Uma vírgula ou um colchete de fechamento era esperado."

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr "Um colchete ou parenteses era esperado."

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "Tipo de dado desconhecido."

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
msgid "An expression was expected."
msgstr "Uma expressão era esperada."

#: src/Components/Expression.php:258 src/Components/Expression.php:419
msgid "An alias was expected."
msgstr "Um alias era esperado."

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "Ponto inesperado."

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "Token inesperado."

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr "Um offset era esperado."

#: src/Components/LockExpression.php:94
msgid "Unexpected end of LOCK expression."
msgstr "Fim inesperado da expressão LOCK."

#: src/Components/LockExpression.php:204
msgid "Unexpected end of Lock expression."
msgstr "Fim inesperado da expressão CASE."

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "Essa opção conflita com \"%1$s1\"."

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "O nome antigo da tabela era esperado."

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "Palavra-chave \"TO\" era esperada."

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "O novo nome da tabela era esperado."

#: src/Components/RenameOperation.php:143
msgid "A rename operation was expected."
msgstr "Uma operação rename era esperada."

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "Faltando expressão."

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "Caractere inesperado."

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr "Espaço(s) em Branco(s) esperado antes do delimitador."

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr "Delimitador esperado."

#: src/Lexer.php:937
#, php-format
msgid "Ending quote %1$s was expected."
msgstr "Citação final %1$s1 era esperada."

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "Nome da variável era esperado."

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "Inesperado começo da declaração."

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "Tipo de declaração desconhecida."

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "Nenhuma transação foi iniciada anteriormente."

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr "Este tipo de cláusula foi previamente analisado."

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "Palavra-chave desconhecida."

#: src/Statement.php:398
msgid "Keyword at end of statement."
msgstr "Palavra-chave no final da declaração."

#: src/Statement.php:543
msgid "Unexpected ordering of clauses."
msgstr "Ordenação inesperada de cláusulas."

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr "O nome da entidade era esperado."

#: src/Statements/CreateStatement.php:601
msgid "A table name was expected."
msgstr "Um nome para a tabela era esperado."

#: src/Statements/CreateStatement.php:606
msgid "At least one column definition was expected."
msgstr "Pelo menos uma definição de coluna era esperada."

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "Uma palavra-chave \"RETURNS\" era esperada."

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr "Esse tipo de clausula não é válida em queries Multi-tabela."

#: src/Statements/LockStatement.php:123
msgid "Unexpected end of LOCK statement."
msgstr "Fim inesperado da declaração LOCK."

#: src/Statements/PurgeStatement.php:141
msgid "Unexpected keyword"
msgstr "Palavra-chave inesperada"

#: src/Statements/WithStatement.php:119
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "The name of the CTE was expected."
msgstr "O nome da entidade era esperado."

#: src/Statements/WithStatement.php:138
#, fuzzy
#| msgid "A \"RETURNS\" keyword was expected."
msgid "AS keyword was expected."
msgstr "Uma palavra-chave \"RETURNS\" era esperada."

#: src/Statements/WithStatement.php:154
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "Subquery of the CTE was expected."
msgstr "O nome da entidade era esperado."

#: src/Statements/WithStatement.php:271
#, fuzzy
#| msgid "Unexpected end of LOCK statement."
msgid "Unexpected end of the WITH CTE."
msgstr "Fim inesperado da declaração LOCK."

#~ msgid "error #1"
#~ msgstr "Erro #1"

#~ msgid "strict error"
#~ msgstr "Erro severo"
