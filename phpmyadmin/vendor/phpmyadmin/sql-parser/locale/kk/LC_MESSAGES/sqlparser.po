# phpMyAdmin translation.
# Copyright (C) 2003 - 2012 phpMyAdmin devel team
# This file is distributed under the same license as the phpMyAdmin package.
# Automatically generated, 2012.
#
msgid ""
msgstr ""
"Project-Id-Version: SQL parser 5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2019-04-01 19:04+0000\n"
"Last-Translator: WWWesten <<EMAIL>>\n"
"Language-Team: Kazakh <https://hosted.weblate.org/projects/phpmyadmin/sql-"
"parser/kk/>\n"
"Language: kk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 3.6-dev\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "Áli iske asyrylmady."

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr ""
"Jańa nusqaý tabyldy, biraq onyń men aldyńǵy bireý arasynda bólgish joq."

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr ""

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "Tanylmaǵan ózgertý áreketi."

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "%1$d mánderi kútilgen, biraq %2$d tabyldy."

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr "Ashýshy jaqsha, sodan keıin mánder jıyntyǵy kútilgen."

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr "Ashýshy jaqsha kútilgen."

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "Kútpegen kilt sóz."

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "CASE órneginiń kútpegen sońy"

#: src/Components/CaseExpression.php:215
#, fuzzy
#| msgid "Unexpected end of CASE expression"
msgid "Potential duplicate alias of CASE expression."
msgstr "CASE órneginiń kútpegen sońy"

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr ""

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "Aldynda búrkenshik aty tabylǵan."

#: src/Components/CaseExpression.php:255
#, fuzzy
#| msgid "An alias was expected."
msgid "An alias was expected after AS."
msgstr "Búrkenshik aty kútilgen."

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""
"Tańba aty kútilgen! Saqtalǵan kilt sózdi doǵal ekpindersiz baǵan aty retinde "
"paıdalanýǵa bolmaıdy."

#: src/Components/CreateDefinition.php:267
msgid "A symbol name was expected!"
msgstr "Tańba aty kútilgen!"

#: src/Components/CreateDefinition.php:299
msgid "A comma or a closing bracket was expected."
msgstr "Útir nemese jabushy jaqsha kútilgen."

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr "Jabushy jaqsha kútilgen."

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "Tanylmaǵan derekter túri."

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
msgid "An expression was expected."
msgstr "Órnek kútilgen."

#: src/Components/Expression.php:258 src/Components/Expression.php:419
msgid "An alias was expected."
msgstr "Búrkenshik aty kútilgen."

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "Kútpegen núkte."

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "Kútpegen tańbalaýysh."

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr "Yǵysý kútilgen."

#: src/Components/LockExpression.php:94
#, fuzzy
#| msgid "Unexpected end of CASE expression"
msgid "Unexpected end of LOCK expression."
msgstr "CASE órneginiń kútpegen sońy"

#: src/Components/LockExpression.php:204
#, fuzzy
#| msgid "Unexpected end of CASE expression"
msgid "Unexpected end of Lock expression."
msgstr "CASE órneginiń kútpegen sońy"

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "Bul opsıa «%1$s» degenmen qaıshy keledi."

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "Kesteniń eski ataýy kútilgen."

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "«TO» kilt sózi kútilgen."

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "Kesteniń jańa ataýy kútilgen."

#: src/Components/RenameOperation.php:143
msgid "A rename operation was expected."
msgstr "Qaıta ataý áreketi kútilgen."

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "Órnek joq."

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "Kútpegen tańba."

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr "Bólgishke deıin bosoryn(dar) kútilgen."

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr "Bólgish kútilgen."

#: src/Lexer.php:937
#, php-format
msgid "Ending quote %1$s was expected."
msgstr "«%1$s» jabýshy tyrnaqshasy kútilgen."

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "Aınymaly aty kútilgen."

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "Nusqaýdyń kútpegen bastalýy."

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "Tanylmaǵan nusqaý túri."

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "Eshbir tranzaksıa aldynda bastalǵan joq."

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr "Bul sóılemdiń túri aldynda taldandy."

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "Tanylmaǵan kilt sóz."

#: src/Statement.php:398
msgid "Keyword at end of statement."
msgstr "Nusqaý aıaǵynda kilt sóz."

#: src/Statement.php:543
msgid "Unexpected ordering of clauses."
msgstr "Kútpegen sóılemder reti."

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr "Nysan aty kútilgen."

#: src/Statements/CreateStatement.php:601
msgid "A table name was expected."
msgstr "Keste aty kútilgen."

#: src/Statements/CreateStatement.php:606
msgid "At least one column definition was expected."
msgstr "Eń keminde bir baǵannyń anyqtamsy kútilgen."

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "«RETURNS» kilt sózi kútilgen."

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr "Birneshe keste saýaldarynda bul sóılem tyri jaramsyz."

#: src/Statements/LockStatement.php:123
#, fuzzy
#| msgid "Unexpected beginning of statement."
msgid "Unexpected end of LOCK statement."
msgstr "Nusqaýdyń kútpegen bastalýy."

#: src/Statements/PurgeStatement.php:141
#, fuzzy
#| msgid "Unexpected keyword."
msgid "Unexpected keyword"
msgstr "Kútpegen kilt sóz."

#: src/Statements/WithStatement.php:119
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "The name of the CTE was expected."
msgstr "Nysan aty kútilgen."

#: src/Statements/WithStatement.php:138
#, fuzzy
#| msgid "A \"RETURNS\" keyword was expected."
msgid "AS keyword was expected."
msgstr "«RETURNS» kilt sózi kútilgen."

#: src/Statements/WithStatement.php:154
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "Subquery of the CTE was expected."
msgstr "Nysan aty kútilgen."

#: src/Statements/WithStatement.php:271
#, fuzzy
#| msgid "Unexpected beginning of statement."
msgid "Unexpected end of the WITH CTE."
msgstr "Nusqaýdyń kútpegen bastalýy."

#~ msgid "error #1"
#~ msgstr "qate № 1"

#~ msgid "strict error"
#~ msgstr "qatań qate"
