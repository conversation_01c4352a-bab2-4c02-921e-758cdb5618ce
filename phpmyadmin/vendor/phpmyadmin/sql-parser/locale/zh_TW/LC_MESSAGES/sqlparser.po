#
msgid ""
msgstr ""
"Project-Id-Version: SQL parser 5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2018-11-29 06:08+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Chinese (Traditional) <https://hosted.weblate.org/projects/"
"phpmyadmin/sql-parser/zh_Hant/>\n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 3.3-dev\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "尚未實作。"

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr "找到新的陳述句，但與前一陳述句之間沒有分隔符。"

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr ""

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "無法識別的修改操作。"

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "預期 %1$d 個數值，但找到 %2$d 個。"

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr "預期開括號後有一組數值。"

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr "預期要有開始括號。"

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "預期之外的關鍵字。"

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "預期之外的CASE陳述式"

#: src/Components/CaseExpression.php:215
#, fuzzy
#| msgid "Unexpected end of CASE expression"
msgid "Potential duplicate alias of CASE expression."
msgstr "預期之外的CASE陳述式"

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr ""

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "已於前面找到別名。"

#: src/Components/CaseExpression.php:255
#, fuzzy
#| msgid "An alias was expected."
msgid "An alias was expected after AS."
msgstr "預期要有別名。"

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr "預期有一個符號！保留字不可以在沒有反引號時用作欄位名稱。"

#: src/Components/CreateDefinition.php:267
msgid "A symbol name was expected!"
msgstr "預期要有符號名稱！"

#: src/Components/CreateDefinition.php:299
msgid "A comma or a closing bracket was expected."
msgstr "預期要有逗號或者結束括號。"

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr "預期要有結束括號。"

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "無法識別的資料類型。"

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
msgid "An expression was expected."
msgstr "預期要有表示式。"

#: src/Components/Expression.php:258 src/Components/Expression.php:419
msgid "An alias was expected."
msgstr "預期要有別名。"

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "預期之外的句點。"

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "預期之外的符號。"

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr "預期要有偏移量(Offset)。"

#: src/Components/LockExpression.php:94
#, fuzzy
#| msgid "Unexpected end of CASE expression"
msgid "Unexpected end of LOCK expression."
msgstr "預期之外的CASE陳述式"

#: src/Components/LockExpression.php:204
#, fuzzy
#| msgid "Unexpected end of CASE expression"
msgid "Unexpected end of Lock expression."
msgstr "預期之外的CASE陳述式"

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "此選項與 \"%1$s\" 發生衝突。"

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "預期要有舊的資料表名稱。"

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "預期要有 \"TO\" 關鍵字。"

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "預期要有新的資料表名稱。"

#: src/Components/RenameOperation.php:143
msgid "A rename operation was expected."
msgstr "預期要有重新命名的操作。"

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "缺少表達式。"

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "預期之外的字元。"

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr "預期要有空白於分隔符號之前。"

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr "預期要有分隔符號。"

#: src/Lexer.php:937
#, php-format
msgid "Ending quote %1$s was expected."
msgstr "預期要於結束使用括號 %1$s。"

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "預期要有變數名稱。"

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "預期之外的陳述句開頭。"

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "無法辨識的陳述句類型。"

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "前面未開啟交易(Transaction)模式。"

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr "此類型的子句已於前面解析過。"

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "無法辨識的關鍵字。"

#: src/Statement.php:398
msgid "Keyword at end of statement."
msgstr "陳述句結尾的關鍵字。"

#: src/Statement.php:543
msgid "Unexpected ordering of clauses."
msgstr "預期之外的排序語句。"

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr "預期要有實體名稱。"

#: src/Statements/CreateStatement.php:601
msgid "A table name was expected."
msgstr "預期有表格名稱。"

#: src/Statements/CreateStatement.php:606
msgid "At least one column definition was expected."
msgstr "預期要至少一個欄位定義。"

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "預期要使用 \"RETURNS\" 關鍵字。"

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr "此類型的子句不可用於多表查詢。"

#: src/Statements/LockStatement.php:123
#, fuzzy
#| msgid "Unexpected beginning of statement."
msgid "Unexpected end of LOCK statement."
msgstr "預期之外的陳述句開頭。"

#: src/Statements/PurgeStatement.php:141
#, fuzzy
#| msgid "Unexpected keyword."
msgid "Unexpected keyword"
msgstr "預期之外的關鍵字。"

#: src/Statements/WithStatement.php:119
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "The name of the CTE was expected."
msgstr "預期要有實體名稱。"

#: src/Statements/WithStatement.php:138
#, fuzzy
#| msgid "A \"RETURNS\" keyword was expected."
msgid "AS keyword was expected."
msgstr "預期要使用 \"RETURNS\" 關鍵字。"

#: src/Statements/WithStatement.php:154
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "Subquery of the CTE was expected."
msgstr "預期要有實體名稱。"

#: src/Statements/WithStatement.php:271
#, fuzzy
#| msgid "Unexpected beginning of statement."
msgid "Unexpected end of the WITH CTE."
msgstr "預期之外的陳述句開頭。"

#~ msgid "error #1"
#~ msgstr "錯誤 #1"

#~ msgid "strict error"
#~ msgstr "嚴謹錯誤"
