# Automatically generated <>, 2010.
msgid ""
msgstr ""
"Project-Id-Version: SQL parser 5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2022-03-21 18:54+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Italian <https://hosted.weblate.org/projects/phpmyadmin/"
"sql-parser/it/>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.12-dev\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "Non ancora implementato."

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr ""
"E' stato trovato un nuovo statement, ma non c'è alcun delimitatore tra il "
"nuovo statement e il precedente."

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr "Virgola assente prima dell'inizio di una nuova operazione alter."

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "Operazione di modifica non riconosciuta."

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "Erano attesi %1$d valori, ma ne sono stati trovati %2$d."

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr "Era attesa una parentesi aperta seguita da un insieme di valori."

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr "Era attesa una parentesi aperta."

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "Keyword inaspettata."

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "Inattesa fine dell'espressione CASE"

#: src/Components/CaseExpression.php:215
msgid "Potential duplicate alias of CASE expression."
msgstr "Potenziale alias duplicato dell'espressione CASE."

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr "Atteso un alias dopo AS ma trovato "

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "Un alias è stato trovato precedentemente."

#: src/Components/CaseExpression.php:255
msgid "An alias was expected after AS."
msgstr "Era atteso un alias dopo AS."

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""
"Era atteso un nome di simbolo! Un termine riservato non può essere usato "
"come nome di un campo senza inserirlo tra backquotes ( ` )."

#: src/Components/CreateDefinition.php:267
msgid "A symbol name was expected!"
msgstr "Era atteso un nome di simbolo!"

#: src/Components/CreateDefinition.php:299
msgid "A comma or a closing bracket was expected."
msgstr "Era attesa una virgola o una parentesi chiusa."

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr "Era attesa una parentesi chiusa."

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "Tipo dati non riconosciuto."

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
msgid "An expression was expected."
msgstr "Era attesa un'espressione."

#: src/Components/Expression.php:258 src/Components/Expression.php:419
msgid "An alias was expected."
msgstr "Era atteso un alias."

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "Segno di punteggiatura \"punto\" inatteso."

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "Token inatteso."

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr "Era atteso un offset."

#: src/Components/LockExpression.php:94
msgid "Unexpected end of LOCK expression."
msgstr "Inattesa fine dell'espressione LOCK."

#: src/Components/LockExpression.php:204
msgid "Unexpected end of Lock expression."
msgstr "Inattesa fine dell'espressione Lock."

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "Questa opzione è in conflitto con \"%1$s\"."

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "Era atteso il vecchio nome della tabella."

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "Era attesa la parola chiave \"TO\"."

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "Era atteso il nuovo nome della tabella."

#: src/Components/RenameOperation.php:143
msgid "A rename operation was expected."
msgstr "Era attesa una operazione di rinomina."

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "Espressione mancante."

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "Carattere inatteso."

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr "Erano attesi uno o più spazi bianchi prima del delimitatore."

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr "Era atteso un delimitatore."

#: src/Lexer.php:937
#, php-format
msgid "Ending quote %1$s was expected."
msgstr "Era atteso il fine quote %1$s."

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "Era atteso un nome di variabile."

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "Inizio di statement inatteso."

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "Tipo statement non riconosciuto."

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "Non è stata iniziata alcuna transazione in precedenza."

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr "Questo tipo di clausola è stata esaminata in precedenza."

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "Parola chiave non riconosciuta."

#: src/Statement.php:398
msgid "Keyword at end of statement."
msgstr "Parola chiave alla fine dello statement."

#: src/Statement.php:543
msgid "Unexpected ordering of clauses."
msgstr "Ordinamento di clausole inatteso."

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr "Era atteso il nome dell'entity."

#: src/Statements/CreateStatement.php:601
msgid "A table name was expected."
msgstr "Era atteso un nome di tabella."

#: src/Statements/CreateStatement.php:606
msgid "At least one column definition was expected."
msgstr "Era attesa almeno la definizione di un campo."

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "Era attesa una parola chiave \"RETURNS\"."

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr "Questo tipo di clausola non è valida nelle query Multi-tabella."

#: src/Statements/LockStatement.php:123
msgid "Unexpected end of LOCK statement."
msgstr "Inattesa fine dello statement LOCK."

#: src/Statements/PurgeStatement.php:141
msgid "Unexpected keyword"
msgstr "Keyword inaspettata"

#: src/Statements/WithStatement.php:119
msgid "The name of the CTE was expected."
msgstr "Era atteso il nome della CTE."

#: src/Statements/WithStatement.php:138
msgid "AS keyword was expected."
msgstr "Era attesa la parola chiave AS."

#: src/Statements/WithStatement.php:154
msgid "Subquery of the CTE was expected."
msgstr "Era attesa una subquery della CTE."

#: src/Statements/WithStatement.php:271
msgid "Unexpected end of the WITH CTE."
msgstr "Inattesa fine dell'espressione WITH CTE."

#~ msgid "error #1"
#~ msgstr "errore #1"

#~ msgid "strict error"
#~ msgstr "errore di tipo strict"
