# phpMyAdmin translation.
# Copyright (C) 2003 - 2013 phpMyAdmin devel team
# This file is distributed under the same license as the phpMyAdmin package.
# Automatically generated, 2013.
#
msgid ""
msgstr ""
"Project-Id-Version: SQL parser 5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2021-04-14 21:26+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Interlingua <https://hosted.weblate.org/projects/phpmyadmin/"
"sql-parser/ia/>\n"
"Language: ia\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.6-dev\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "Ancora non actuate."

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr ""
"On trovava un nove declaration, ma il non ha alcun demarcator inter isto e "
"le previe."

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr "mancante comma ante initiar un nove operation de alterar."

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "Operation d alteration non recognoscite."

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "%1$d valores esseva expectate, ma il trovava %2$d."

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr "On expectava un parenthesis aperite sequite per un insimul de valores."

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr "On expectava un parenthesis aperite."

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "Un parola clave non expectate."

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "Un fin inexpectate del expression CASE"

#: src/Components/CaseExpression.php:215
msgid "Potential duplicate alias of CASE expression."
msgstr "Un potential alias duplicate del expression CASE."

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr "Un alias attendite post AS ma obteneva "

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "Previemente on trovava un pseudonymo."

#: src/Components/CaseExpression.php:255
msgid "An alias was expected after AS."
msgstr "On expectava un pseudonymo AS."

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""
"On expectava un nomine de symbolo! Un parola clave reservate non pte esser "
"usate como nomine de columna sin retro citationes (backquotes)."

#: src/Components/CreateDefinition.php:267
msgid "A symbol name was expected!"
msgstr "On expectava un nomine de symbolo!"

#: src/Components/CreateDefinition.php:299
msgid "A comma or a closing bracket was expected."
msgstr "On expectava un comma o un parentheses claudite."

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr "On expectava un parenthesis claudite."

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "Typo de datos non cognoscite."

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
msgid "An expression was expected."
msgstr "On expectava un expression."

#: src/Components/Expression.php:258 src/Components/Expression.php:419
msgid "An alias was expected."
msgstr "On expectava un alias."

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "Puncto inexpectate."

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "Un indicio non expectate."

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr "On expectava un displaciamento."

#: src/Components/LockExpression.php:94
msgid "Unexpected end of LOCK expression."
msgstr "Un fin inexpectate del expression LOCK."

#: src/Components/LockExpression.php:204
msgid "Unexpected end of Lock expression."
msgstr "Un fin inexpectate del expression Lock."

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "Iste option conflige con \"%1$s\"."

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "On expectava le vetule nomine de le tabella."

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "On expectava le parola clave \"TO\"."

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "On expectava le nove nomine del tabella."

#: src/Components/RenameOperation.php:143
msgid "A rename operation was expected."
msgstr "On expectava un operation de renominar.."

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "Expression mancante."

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "Un character non expectate."

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr "On expectava spatio(s) blanc ante le demarcator."

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr "On expectava demarcator."

#: src/Lexer.php:937
#, php-format
msgid "Ending quote %1$s was expected."
msgstr "On expectava le fin de citation %1$s ."

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "On expectava un nomine de variabile."

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "Un initio de instruction non expectate."

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "Typo de declaration non recognoscite."

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "Necun transaction esseva initiate previemente."

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr "Iste typo de proposition esseva analysate previemente."

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "Parola clave non recognoscite."

#: src/Statement.php:398
msgid "Keyword at end of statement."
msgstr "Parola clave al fin del instruction."

#: src/Statement.php:543
msgid "Unexpected ordering of clauses."
msgstr "Un ordine de clausas non expectate."

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr "On expectava le nomine del entitate."

#: src/Statements/CreateStatement.php:601
msgid "A table name was expected."
msgstr "On expectava un nomine de tabella."

#: src/Statements/CreateStatement.php:606
msgid "At least one column definition was expected."
msgstr "On expectava al minus un definition de columna."

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "On expectava le parola clave \"RETURN\"."

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr "Iste typo de proposition non es valide in query de multi-tabella."

#: src/Statements/LockStatement.php:123
msgid "Unexpected end of LOCK statement."
msgstr "Un initio de instruction LOCK non expectate."

#: src/Statements/PurgeStatement.php:141
msgid "Unexpected keyword"
msgstr "Un parola clave non expectate"

#: src/Statements/WithStatement.php:119
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "The name of the CTE was expected."
msgstr "On expectava le nomine del entitate."

#: src/Statements/WithStatement.php:138
#, fuzzy
#| msgid "A \"RETURNS\" keyword was expected."
msgid "AS keyword was expected."
msgstr "On expectava le parola clave \"RETURN\"."

#: src/Statements/WithStatement.php:154
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "Subquery of the CTE was expected."
msgstr "On expectava le nomine del entitate."

#: src/Statements/WithStatement.php:271
#, fuzzy
#| msgid "Unexpected end of LOCK statement."
msgid "Unexpected end of the WITH CTE."
msgstr "Un initio de instruction LOCK non expectate."

#~ msgid "error #1"
#~ msgstr "error #1"

#~ msgid "strict error"
#~ msgstr "stricte error"
