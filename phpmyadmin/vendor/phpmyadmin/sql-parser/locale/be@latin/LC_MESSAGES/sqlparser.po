#
msgid ""
msgstr ""
"Project-Id-Version: SQL parser 5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2017-08-22 09:47+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Belarusian (latin) <https://hosted.weblate.org/projects/"
"phpmyadmin/sql-parser/be_Latn/>\n"
"Language: be@latin\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"
"X-Generator: Weblate 2.17-dev\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "Jašče nie realizavana."

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr ""
"Znojdziena novaje scviardžennie, alie miž im i papiarednim niama "
"razdzialiaĺnika."

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr ""

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "Nieraspaznanaja apieracyja zmieny."

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "Čakalasia %1$d značenniaŭ, a znojdziena %2$d."

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr "Čakalisia adkryvaĺnaja dužka i spis značenniaŭ."

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr "Čakalasia adkryvaĺnaja dužka."

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "Niečakanaje kliučavoje slova."

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "Niečakany kaniec CASE-vyrazu"

#: src/Components/CaseExpression.php:215
#, fuzzy
#| msgid "Unexpected end of CASE expression"
msgid "Potential duplicate alias of CASE expression."
msgstr "Niečakany kaniec CASE-vyrazu"

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr ""

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "Psieŭdanim byŭ znajdzieny raniej."

#: src/Components/CaseExpression.php:255
#, fuzzy
#| msgid "An alias was expected."
msgid "An alias was expected after AS."
msgstr "Čakaŭsia psieŭdanim."

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""
"Čakalasia nazva simvala! Zareziervavanyja kliučavyja slovy nieĺha "
"vykarystoŭvać jak nazvu slupka biez dvukossiaŭ."

#: src/Components/CreateDefinition.php:267
msgid "A symbol name was expected!"
msgstr "Čakalasia nazva simvala!"

#: src/Components/CreateDefinition.php:299
msgid "A comma or a closing bracket was expected."
msgstr "Čakalasia koska abo zakryvaĺnaja dužka."

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr "Čakalasia zakryvaĺnaja dužka."

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "Nieraspaznany typ danych."

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
msgid "An expression was expected."
msgstr "Čakaŭsia vyraz."

#: src/Components/Expression.php:258 src/Components/Expression.php:419
msgid "An alias was expected."
msgstr "Čakaŭsia psieŭdanim."

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "Niečakanaja kropka."

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "Niečakany znak."

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr "Čakaŭsia sostup."

#: src/Components/LockExpression.php:94
#, fuzzy
#| msgid "Unexpected end of CASE expression"
msgid "Unexpected end of LOCK expression."
msgstr "Niečakany kaniec CASE-vyrazu"

#: src/Components/LockExpression.php:204
#, fuzzy
#| msgid "Unexpected end of CASE expression"
msgid "Unexpected end of Lock expression."
msgstr "Niečakany kaniec CASE-vyrazu"

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "Hety paramietr kanfliktuje z «%1$s»."

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "Čakalasia staraja nazva tablicy."

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "Čakalacja kliučavoje slova «TO»."

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "Čakalasia novaja nazva tablicy."

#: src/Components/RenameOperation.php:143
msgid "A rename operation was expected."
msgstr "Čakalasia apieracyja pierajmienavannia."

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "Adsutničaje vyraz."

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "Niečakany simval."

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr "Čakaŭsia prahal(y) pierad razdzialiaĺnikam."

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr "Čakajecca razdzialiaĺnik."

#: src/Lexer.php:937
#, php-format
msgid "Ending quote %1$s was expected."
msgstr "Čakalasia kancavoje dvukossie %1$s."

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "Čakalasia nazva zmiennaj."

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "Niečakany pačatak scviardžennia."

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "Nieraspaznany typ scviardžennia."

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "Niama papiarednie zapuščanaj tranzakcyi."

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr "Hety typ vyrazu byŭ papiarednie razabrany."

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "Nieraspaznanaje kliučavoje slova."

#: src/Statement.php:398
msgid "Keyword at end of statement."
msgstr "Kliučavoje slova ŭ kancy scviardžennia."

#: src/Statement.php:543
msgid "Unexpected ordering of clauses."
msgstr "Niečakany paradak vyrazaŭ."

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr "Čakalasia nazva sutnasci."

#: src/Statements/CreateStatement.php:601
msgid "A table name was expected."
msgstr "Čakalasia nazva tablicy."

#: src/Statements/CreateStatement.php:606
msgid "At least one column definition was expected."
msgstr "Čakalasia aznačennie prynamsi adnaho slupka."

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "Čakalasia kliučavoje slova «RETURNS»."

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr "Hety typ vyrazu nie praviĺny dlia muĺtytabličnych zapytaŭ."

#: src/Statements/LockStatement.php:123
#, fuzzy
#| msgid "Unexpected beginning of statement."
msgid "Unexpected end of LOCK statement."
msgstr "Niečakany pačatak scviardžennia."

#: src/Statements/PurgeStatement.php:141
#, fuzzy
#| msgid "Unexpected keyword."
msgid "Unexpected keyword"
msgstr "Niečakanaje kliučavoje slova."

#: src/Statements/WithStatement.php:119
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "The name of the CTE was expected."
msgstr "Čakalasia nazva sutnasci."

#: src/Statements/WithStatement.php:138
#, fuzzy
#| msgid "A \"RETURNS\" keyword was expected."
msgid "AS keyword was expected."
msgstr "Čakalasia kliučavoje slova «RETURNS»."

#: src/Statements/WithStatement.php:154
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "Subquery of the CTE was expected."
msgstr "Čakalasia nazva sutnasci."

#: src/Statements/WithStatement.php:271
#, fuzzy
#| msgid "Unexpected beginning of statement."
msgid "Unexpected end of the WITH CTE."
msgstr "Niečakany pačatak scviardžennia."

#~ msgid "error #1"
#~ msgstr "pamylka #1"

#~ msgid "strict error"
#~ msgstr "niedapuščaĺnaja pamylka"
