# phpMyAdmin translation.
# Copyright (C) 2003 - 2011 phpMyAdmin devel team
# This file is distributed under the same license as the phpMyAdmin package.
msgid ""
msgstr ""
"Project-Id-Version: SQL parser 5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2020-10-10 15:54+0000\n"
"Last-Translator: <PERSON>.C <<EMAIL>>\n"
"Language-Team: Malayalam <https://hosted.weblate.org/projects/phpmyadmin/sql-"
"parser/ml/>\n"
"Language: ml\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.3-dev\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "ഇതുവരെ നടപ്പാക്കിയിട്ടില്ല."

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr ""
"ഒരു പുതിയ പ്രസ്താവന കണ്ടെത്തി, പക്ഷേ അതിനും മുമ്പത്തെ പ്രസ്താവനയ്ക്കും ഇടയിൽ അതിര്‍ത്തി ഇല്ല."

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr "ഒരു പുതിയ പരിവര്‍ത്തരം ആരംഭിക്കുന്നതിന് മുമ്പ് ഉദ്ധരണി സൂചകമായ ചിഹ്നം കാണുന്നില്ല."

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "തിരിച്ചറിയാത്ത വ്യത്യാസപ്പെടുത്തല്‍."

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "%1$d മൂല്യങ്ങൾ പ്രതീക്ഷിച്ചെങ്കിലും കണ്ടെത്തി %2$d."

#: src/Components/Array2d.php:111
#, fuzzy
msgid "An opening bracket followed by a set of values was expected."
msgstr "ഒരു ഓപ്പണിംഗ് ബ്രാക്കറ്റിന് ശേഷം ഒരു കൂട്ടം മൂല്യങ്ങൾ പ്രതീക്ഷിച്ചു."

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr "ഒരു ഓപ്പണിംഗ് ബ്രായ്ക്കറ്റ് പ്രതീക്ഷിച്ചു."

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "അപ്രതീക്ഷിത സൂചകപദം."

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "CASE പ്രയോഗശൈലിയുടെ അപ്രതീക്ഷിത അവസാനം"

#: src/Components/CaseExpression.php:215
msgid "Potential duplicate alias of CASE expression."
msgstr "CASE പ്രയോഗശൈലിയുടെ തനിപ്പകർപ്പ് അപരനാമം."

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr "ASന് ശേഷം പ്രതീക്ഷിച്ച ഒരു അപരനാമം ലഭിച്ചു "

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "ഒരു അപരനാമം മുമ്പ് കണ്ടെത്തി."

#: src/Components/CaseExpression.php:255
msgid "An alias was expected after AS."
msgstr "ASന് ശേഷം ഒരു അപരനാമം പ്രതീക്ഷിച്ചിരുന്നു."

#: src/Components/CreateDefinition.php:254
#, fuzzy
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""
"ഒരു ചിഹ്ന നാമം പ്രതീക്ഷിച്ചു! ബാക്ക് ഉദ്ധരണികൾ ഇല്ലാതെ ഒരു റിസർവ്ഡ് കീവേഡ്, നിരയുടെ പേരായി "
"ഉപയോഗിക്കാൻ കഴിയില്ല."

#: src/Components/CreateDefinition.php:267
msgid "A symbol name was expected!"
msgstr "ഒരു ചിഹ്ന നാമം പ്രതീക്ഷിച്ചു!"

#: src/Components/CreateDefinition.php:299
#, fuzzy
#| msgid "Remove database"
msgid "A comma or a closing bracket was expected."
msgstr "വിവരശേഖരം നീക്കുക"

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
#, fuzzy
msgid "A closing bracket was expected."
msgstr "ഒരു അടക്കല്‍ ബ്രാക്കറ്റ് പ്രതീക്ഷിച്ചു."

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "തിരിച്ചറിയാത്ത ഡാറ്റ തരം."

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
#, fuzzy
#| msgid "Remove database"
msgid "An expression was expected."
msgstr "വിവരശേഖരം നീക്കുക"

#: src/Components/Expression.php:258 src/Components/Expression.php:419
#, fuzzy
#| msgid "Remove database"
msgid "An alias was expected."
msgstr "വിവരശേഖരം നീക്കുക"

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "അപ്രതീക്ഷിത കുത്ത്‌."

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "അപ്രതീക്ഷിത അടയാളം."

#: src/Components/Limit.php:81 src/Components/Limit.php:108
#, fuzzy
msgid "An offset was expected."
msgstr "ഒരു ഓഫ്‌സെറ്റ് പ്രതീക്ഷിച്ചു."

#: src/Components/LockExpression.php:94
msgid "Unexpected end of LOCK expression."
msgstr "LOCK പ്രയോഗശൈലിയുടെ അപ്രതീക്ഷിത അവസാനം."

#: src/Components/LockExpression.php:204
#, fuzzy
msgid "Unexpected end of Lock expression."
msgstr "LOCK പ്രയോഗശൈലിയുടെ അപ്രതീക്ഷിത അവസാനം."

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "ഈ \"%1$s\"തിരഞ്ഞെടുക്കല്‍ പൊരുത്തപ്പെടുന്നില്ല."

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "പട്ടികയുടെ പഴയ പേര് പ്രതീക്ഷിച്ചു."

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "\"TO\" സൂചകപദം പ്രതീക്ഷിച്ചു."

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "പട്ടികയുടെ പുതിയ പേര് പ്രതീക്ഷിച്ചു."

#: src/Components/RenameOperation.php:143
#, fuzzy
#| msgid "Database %1$s has been created."
msgid "A rename operation was expected."
msgstr "വിവരശേഖരം(ങ്ങൾ) %1$s സൃഷ്ടിച്ചിരിക്കുന്നു."

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "പദപ്രയോഗം നഷ്‌ടമായി."

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "അപ്രതീക്ഷിത പ്രതീകം."

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr "അതിരുകൾക് മുൻപ് പ്രതീക്ഷിച്ച വിടവുകൾ."

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr "പ്രതീക്ഷിച്ച അതിര്‍ത്തി."

#: src/Lexer.php:937
#, php-format
msgid "Ending quote %1$s was expected."
msgstr "%1$s ഉദ്ധരണി അവസാനിക്കുന്നു."

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "പരിവര്‍ത്തിതവസ്‌തുവിന്റെ പേര് പ്രതീക്ഷിച്ചു."

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "പ്രസ്താവനയുടെ അപ്രതീക്ഷിത തുടക്കം."

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "തിരിച്ചറിയാത്ത പ്രസ്താവന തരം."

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "ഒരു ഇടപാടും മുമ്പ് ആരംഭിച്ചിട്ടില്ല."

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr "ഇത്തരത്തിലുള്ള ഉടമ്പടി മുമ്പ് വ്യാകരിച്ചു."

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "തിരിച്ചറിയാത്ത സൂചകപദം."

#: src/Statement.php:398
msgid "Keyword at end of statement."
msgstr "പ്രസ്താവനയുടെ അവസാനം സൂചകപദം."

#: src/Statement.php:543
msgid "Unexpected ordering of clauses."
msgstr "ഉപവാക്യങ്ങളുടെ അപ്രതീക്ഷിത ക്രമം."

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr "വസ്‌തുവിന്റെ പേര് പ്രതീക്ഷിച്ചു."

#: src/Statements/CreateStatement.php:601
#, fuzzy
#| msgid "Remove database"
msgid "A table name was expected."
msgstr "വിവരശേഖരം നീക്കുക"

#: src/Statements/CreateStatement.php:606
#, fuzzy
#| msgid "Database %1$s has been created."
msgid "At least one column definition was expected."
msgstr "വിവരശേഖരം(ങ്ങൾ) %1$s സൃഷ്ടിച്ചിരിക്കുന്നു."

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "ഒരു \"റിട്ടേൺസ്\" സൂചകപദം പ്രതീക്ഷിച്ചു."

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr "മൾട്ടി-ടേബിൾ അന്വേഷണങ്ങളിൽ ഇത്തരത്തിലുള്ള ഉപവാക്യം സാധുവല്ല."

#: src/Statements/LockStatement.php:123
msgid "Unexpected end of LOCK statement."
msgstr "LOCK പ്രസ്‌താവനയുടെ അപ്രതീക്ഷിത അവസാനം."

#: src/Statements/PurgeStatement.php:141
msgid "Unexpected keyword"
msgstr "അപ്രതീക്ഷിത സൂചകപദം."

#: src/Statements/WithStatement.php:119
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "The name of the CTE was expected."
msgstr "വസ്‌തുവിന്റെ പേര് പ്രതീക്ഷിച്ചു."

#: src/Statements/WithStatement.php:138
#, fuzzy
#| msgid "A \"RETURNS\" keyword was expected."
msgid "AS keyword was expected."
msgstr "ഒരു \"റിട്ടേൺസ്\" സൂചകപദം പ്രതീക്ഷിച്ചു."

#: src/Statements/WithStatement.php:154
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "Subquery of the CTE was expected."
msgstr "വസ്‌തുവിന്റെ പേര് പ്രതീക്ഷിച്ചു."

#: src/Statements/WithStatement.php:271
#, fuzzy
#| msgid "Unexpected end of LOCK statement."
msgid "Unexpected end of the WITH CTE."
msgstr "LOCK പ്രസ്‌താവനയുടെ അപ്രതീക്ഷിത അവസാനം."
