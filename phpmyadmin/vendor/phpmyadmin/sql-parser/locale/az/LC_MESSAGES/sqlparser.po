#
msgid ""
msgstr ""
"Project-Id-Version: SQL parser 5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2017-11-25 14:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Azerbaijani <https://hosted.weblate.org/projects/phpmyadmin/"
"sql-parser/az/>\n"
"Language: az\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 2.18-dev\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "Hələki tətbiq edilməyib."

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr ""

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr ""

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "Kateqoriya olunmamış alter əməliyyatı."

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "%1$d gözlənilirdi, amma %2$d tapıldı."

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr "Mötərizə ardından bir qrup dəyər gözlənilməkdə."

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr ""

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "Gözlənilməyən açar söz."

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "Gözlənməz Böyük/Kiçik hərf ifadəsi sonu"

#: src/Components/CaseExpression.php:215
#, fuzzy
#| msgid "Unexpected end of CASE expression"
msgid "Potential duplicate alias of CASE expression."
msgstr "Gözlənməz Böyük/Kiçik hərf ifadəsi sonu"

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr ""

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "Daha öncə alias tapıldı."

#: src/Components/CaseExpression.php:255
#, fuzzy
#| msgid "An alias was expected."
msgid "An alias was expected after AS."
msgstr "Alias gözlənilirdi."

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""
"Bir simvol adı gözlənilirdi! Dırnaqsız reserv sözlər sütün adı olaraq "
"istifadə edilə bilməz."

#: src/Components/CreateDefinition.php:267
msgid "A symbol name was expected!"
msgstr "Simvol adı gözlənilirdi!"

#: src/Components/CreateDefinition.php:299
msgid "A comma or a closing bracket was expected."
msgstr "Vergül və ya mötərizə gözlənilirdi."

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr "Mötərizə gözlənilirdi."

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "Tanınmayan verilən tipi."

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
msgid "An expression was expected."
msgstr "İfadə gözlənilməkdə."

#: src/Components/Expression.php:258 src/Components/Expression.php:419
msgid "An alias was expected."
msgstr "Alias gözlənilirdi."

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "Gözlənilməz nöqtə."

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "Gözlənilməyən token."

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr ""

#: src/Components/LockExpression.php:94
#, fuzzy
#| msgid "Unexpected end of CASE expression"
msgid "Unexpected end of LOCK expression."
msgstr "Gözlənməz Böyük/Kiçik hərf ifadəsi sonu"

#: src/Components/LockExpression.php:204
#, fuzzy
#| msgid "Unexpected end of CASE expression"
msgid "Unexpected end of Lock expression."
msgstr "Gözlənməz Böyük/Kiçik hərf ifadəsi sonu"

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "Bu seçim %1$s bununla zidiyyət təşkil edir."

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "Cədvəlin köhnə adı gözlənilirdi."

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "\"TO\" açar sözü gözlənilirdi."

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "Cədvəlin yeni adı gözlənilirdi."

#: src/Components/RenameOperation.php:143
msgid "A rename operation was expected."
msgstr "Yenidən adlandırma əməliyyatı gözlənilirdi."

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "Əksik ifadə."

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "Gözlənilməyən simvol."

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr ""

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr ""

#: src/Lexer.php:937
#, fuzzy, php-format
#| msgid "Event %1$s has been created."
msgid "Ending quote %1$s was expected."
msgstr "%1$s hadisəsi yaradıldı."

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "Dəyişən adı gözlənilirdi."

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "İfadənin gözlənilməz başlanğıcı."

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "Tanınmayan ifadə tipi."

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "Daha öncə tranzaksiya başladılmadı."

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr ""

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "Tanınmayan açarsöz."

#: src/Statement.php:398
msgid "Keyword at end of statement."
msgstr "İfadənin sonunda açar söz."

#: src/Statement.php:543
#, fuzzy
#| msgid "At Beginning of Table"
msgid "Unexpected ordering of clauses."
msgstr "Cədvəlin başına"

#: src/Statements/CreateStatement.php:552
#, fuzzy
#| msgid "The number of tables that are open."
msgid "The name of the entity was expected."
msgstr "Açıq olan cədvəl sayı."

#: src/Statements/CreateStatement.php:601
msgid "A table name was expected."
msgstr "Cədvəl adı gözlənilirdi."

#: src/Statements/CreateStatement.php:606
msgid "At least one column definition was expected."
msgstr "Ən az bir sütün açıqlaması gözlənilirdi."

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "\"RETURNS\" açarsözü gözlənilirdi."

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr ""

#: src/Statements/LockStatement.php:123
#, fuzzy
#| msgid "Unexpected beginning of statement."
msgid "Unexpected end of LOCK statement."
msgstr "İfadənin gözlənilməz başlanğıcı."

#: src/Statements/PurgeStatement.php:141
#, fuzzy
#| msgid "Unexpected keyword."
msgid "Unexpected keyword"
msgstr "Gözlənilməyən açar söz."

#: src/Statements/WithStatement.php:119
#, fuzzy
#| msgid "The number of tables that are open."
msgid "The name of the CTE was expected."
msgstr "Açıq olan cədvəl sayı."

#: src/Statements/WithStatement.php:138
#, fuzzy
#| msgid "A \"RETURNS\" keyword was expected."
msgid "AS keyword was expected."
msgstr "\"RETURNS\" açarsözü gözlənilirdi."

#: src/Statements/WithStatement.php:154
#, fuzzy
#| msgid "The number of tables that are open."
msgid "Subquery of the CTE was expected."
msgstr "Açıq olan cədvəl sayı."

#: src/Statements/WithStatement.php:271
#, fuzzy
#| msgid "Unexpected beginning of statement."
msgid "Unexpected end of the WITH CTE."
msgstr "İfadənin gözlənilməz başlanğıcı."

#~ msgid "error #1"
#~ msgstr "xəta #1"

#, fuzzy
#~| msgid "Query error"
#~ msgid "strict error"
#~ msgstr "Sorğu xətası"
