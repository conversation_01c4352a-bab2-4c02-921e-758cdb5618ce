# phpMyAdmin translation.
# Copyright (C) 2003 - 2010 phpMyAdmin devel team
# This file is distributed under the same license as the phpMyAdmin package.
# <AUTHOR> <EMAIL>, YEAR.
msgid ""
msgstr ""
"Project-Id-Version: SQL parser 5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2015-10-15 11:04+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Welsh <https://hosted.weblate.org/projects/phpmyadmin/master/"
"cy/>\n"
"Language: cy\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=(n==0) ? 0 : (n==1) ? 1 : (n==2) ? 2 : "
"(n==3) ? 3 :(n==6) ? 4 : 5;\n"
"X-Generator: Weblate 2.5-dev\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr ""

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr ""

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr ""

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr ""

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr ""

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr ""

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr ""

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr ""

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr ""

#: src/Components/CaseExpression.php:215
msgid "Potential duplicate alias of CASE expression."
msgstr ""

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr ""

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr ""

#: src/Components/CaseExpression.php:255
#, fuzzy
#| msgid "No databases selected."
msgid "An alias was expected after AS."
msgstr "Dim cronfeydd data wedi'u dewis."

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""

#: src/Components/CreateDefinition.php:267
#, fuzzy
#| msgid "Table name template"
msgid "A symbol name was expected!"
msgstr "Templed enw tabl"

#: src/Components/CreateDefinition.php:299
#, fuzzy
#| msgid "No databases selected."
msgid "A comma or a closing bracket was expected."
msgstr "Dim cronfeydd data wedi'u dewis."

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr ""

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr ""

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
#, fuzzy
#| msgid "No databases selected."
msgid "An expression was expected."
msgstr "Dim cronfeydd data wedi'u dewis."

#: src/Components/Expression.php:258 src/Components/Expression.php:419
#, fuzzy
#| msgid "No databases selected."
msgid "An alias was expected."
msgstr "Dim cronfeydd data wedi'u dewis."

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr ""

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr ""

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr ""

#: src/Components/LockExpression.php:94
#, fuzzy
#| msgid "Show dimension of tables"
msgid "Unexpected end of LOCK expression."
msgstr "Dangos dimensiynau'r tabl"

#: src/Components/LockExpression.php:204
#, fuzzy
#| msgid "Show dimension of tables"
msgid "Unexpected end of Lock expression."
msgstr "Dangos dimensiynau'r tabl"

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr ""

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr ""

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr ""

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr ""

#: src/Components/RenameOperation.php:143
#, fuzzy
#| msgid "The row has been deleted."
msgid "A rename operation was expected."
msgstr "Cafodd y rhes ei dileu"

#: src/Components/SetOperation.php:124
#, fuzzy
#| msgid "as regular expression"
msgid "Missing expression."
msgstr "fel mynegiad arferol (regular expression)"

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr ""

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr ""

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr ""

#: src/Lexer.php:937
#, fuzzy, php-format
#| msgid "Database %1$s has been created."
msgid "Ending quote %1$s was expected."
msgstr "Cafodd y gronfa ddata %1$s ei chreu."

#: src/Lexer.php:989
#, fuzzy
#| msgid "Table name template"
msgid "Variable name was expected."
msgstr "Templed enw tabl"

#: src/Parser.php:453
#, fuzzy
#| msgid "Show dimension of tables"
msgid "Unexpected beginning of statement."
msgstr "Dangos dimensiynau'r tabl"

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr ""

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr ""

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr ""

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr ""

#: src/Statement.php:398
#, fuzzy
#| msgid "Show dimension of tables"
msgid "Keyword at end of statement."
msgstr "Dangos dimensiynau'r tabl"

#: src/Statement.php:543
#, fuzzy
#| msgid "Show dimension of tables"
msgid "Unexpected ordering of clauses."
msgstr "Dangos dimensiynau'r tabl"

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr ""

#: src/Statements/CreateStatement.php:601
#, fuzzy
#| msgid "Table name template"
msgid "A table name was expected."
msgstr "Templed enw tabl"

#: src/Statements/CreateStatement.php:606
#, fuzzy
#| msgid "The row has been deleted."
msgid "At least one column definition was expected."
msgstr "Cafodd y rhes ei dileu"

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr ""

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr ""

#: src/Statements/LockStatement.php:123
#, fuzzy
#| msgid "Show dimension of tables"
msgid "Unexpected end of LOCK statement."
msgstr "Dangos dimensiynau'r tabl"

#: src/Statements/PurgeStatement.php:141
msgid "Unexpected keyword"
msgstr ""

#: src/Statements/WithStatement.php:119
#, fuzzy
#| msgid "Table name template"
msgid "The name of the CTE was expected."
msgstr "Templed enw tabl"

#: src/Statements/WithStatement.php:138
#, fuzzy
#| msgid "No databases selected."
msgid "AS keyword was expected."
msgstr "Dim cronfeydd data wedi'u dewis."

#: src/Statements/WithStatement.php:154
#, fuzzy
#| msgid "Database %1$s has been created."
msgid "Subquery of the CTE was expected."
msgstr "Cafodd y gronfa ddata %1$s ei chreu."

#: src/Statements/WithStatement.php:271
#, fuzzy
#| msgid "Show dimension of tables"
msgid "Unexpected end of the WITH CTE."
msgstr "Dangos dimensiynau'r tabl"

#, fuzzy
#~| msgid "Error"
#~ msgid "error #1"
#~ msgstr "Gwall"

#, fuzzy
#~| msgid "Query"
#~ msgid "strict error"
#~ msgstr "Ymholiad"
