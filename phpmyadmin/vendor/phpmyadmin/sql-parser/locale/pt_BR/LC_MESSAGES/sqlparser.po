#
msgid ""
msgstr ""
"Project-Id-Version: SQL parser 5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2020-08-14 00:37+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Portuguese (Brazil) <https://hosted.weblate.org/projects/"
"phpmyadmin/sql-parser/pt_BR/>\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.2-dev\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "Ainda não implementado."

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr ""
"Uma nova declaração foi encontrada, porém não há delimitador entre esta e a "
"anterior."

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr "Falta uma vírgula antes do início de uma nova operação de alteração."

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "Operação de alteração desconhecida."

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "Eram esperados %1$d valores, mas foram encontrados %2$d."

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr ""
"Era esperado um colchete de abertura seguido por um conjunto de valores."

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr "Era esperado um colchete de abertura."

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "Caracter inesperado."

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "Final inesperado da expressão CASE"

#: src/Components/CaseExpression.php:215
msgid "Potential duplicate alias of CASE expression."
msgstr "Pseudônimo potencialmente duplicado da expressão CASE."

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr "Um pseudônimo esperado após AS, mas tem "

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "Um nome correlação foi encontrado anteriormente."

#: src/Components/CaseExpression.php:255
msgid "An alias was expected after AS."
msgstr "Um pseudônimo foi esperado depois do AS."

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""
"Era esperado um nome de símbolo! Um caracter reservado não pode ser usado "
"como um nome de coluna sem apóstrofo."

#: src/Components/CreateDefinition.php:267
msgid "A symbol name was expected!"
msgstr "Era esperado um nome de símbolo!"

#: src/Components/CreateDefinition.php:299
msgid "A comma or a closing bracket was expected."
msgstr "Era esperado uma vírgula ou um colchete de fechamento."

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr "Era esperado um colchete de fechamento."

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "Tipo de dado desconhecido."

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
msgid "An expression was expected."
msgstr "Era esperado uma expressão."

#: src/Components/Expression.php:258 src/Components/Expression.php:419
msgid "An alias was expected."
msgstr "Era esperado um nome de correlação."

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "Ponto inesperado."

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "Símbolo (token) inesperado."

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr "Era esperado um espaçamento."

#: src/Components/LockExpression.php:94
msgid "Unexpected end of LOCK expression."
msgstr "Final inesperado da expressão LOCK."

#: src/Components/LockExpression.php:204
msgid "Unexpected end of Lock expression."
msgstr "Final inesperado da expressão Lock."

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "Esta opção está em conflito com \"%1$s\"."

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "Era esperado o nome anterior da tabela."

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "Era esperado o caracter \"TO\"."

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "Era esperado o novo nome da tabela."

#: src/Components/RenameOperation.php:143
msgid "A rename operation was expected."
msgstr "Era esperado uma operação renomear."

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "Faltando expressão."

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "Caracter inesperado."

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr "Era(m) esperado(s) espaço(s) em branco antes do delimitador."

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr "Esperado delimitador."

#: src/Lexer.php:937
#, php-format
msgid "Ending quote %1$s was expected."
msgstr "Era esperado fechar aspas %1$s."

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "Era perado nome de variável."

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "Início de declaração inesperado."

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "Tipo de declaração desconhecido."

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "Nenhuma operação foi iniciada anteriormente."

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr "Este tipo de sentença foi previamente analisado."

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "Comando desconhecido."

#: src/Statement.php:398
msgid "Keyword at end of statement."
msgstr "Palavra-chave no final da declaração."

#: src/Statement.php:543
msgid "Unexpected ordering of clauses."
msgstr "Ordenamento de sentenças inesperado."

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr "Era esperado o nome da entidade."

#: src/Statements/CreateStatement.php:601
msgid "A table name was expected."
msgstr "Era esperado um nome de tabela."

#: src/Statements/CreateStatement.php:606
msgid "At least one column definition was expected."
msgstr "Era esperado a definição de pelo menos uma coluna."

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "Era esperado um comando \"RETURNS\"."

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr "Este tipo de sentença não é válida em consultas à várias tabelas."

#: src/Statements/LockStatement.php:123
msgid "Unexpected end of LOCK statement."
msgstr "Fim inesperado da declaração LOCK."

#: src/Statements/PurgeStatement.php:141
msgid "Unexpected keyword"
msgstr "Palavra-chave inesperada"

#: src/Statements/WithStatement.php:119
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "The name of the CTE was expected."
msgstr "Era esperado o nome da entidade."

#: src/Statements/WithStatement.php:138
#, fuzzy
#| msgid "A \"RETURNS\" keyword was expected."
msgid "AS keyword was expected."
msgstr "Era esperado um comando \"RETURNS\"."

#: src/Statements/WithStatement.php:154
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "Subquery of the CTE was expected."
msgstr "Era esperado o nome da entidade."

#: src/Statements/WithStatement.php:271
#, fuzzy
#| msgid "Unexpected end of LOCK statement."
msgid "Unexpected end of the WITH CTE."
msgstr "Fim inesperado da declaração LOCK."

#~ msgid "error #1"
#~ msgstr "erro #1"

#~ msgid "strict error"
#~ msgstr "Erro estrito"
