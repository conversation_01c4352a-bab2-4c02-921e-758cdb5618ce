# phpMyAdmin translation.
# Bản dịch tiếng Việt dành cho phpMyAdmin.
# Copyright (C) 2003 - 2015 phpMyAdmin devel team
# This file is distributed under the same license as the phpMyAdmin package.
# Tr<PERSON><PERSON> <<EMAIL>>, 2015.
#
msgid ""
msgstr ""
"Project-Id-Version: SQL parser 5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2018-06-30 00:46+0000\n"
"Last-Translator: Name <<EMAIL>>\n"
"Language-Team: Vietnamese <https://hosted.weblate.org/projects/phpmyadmin/"
"sql-parser/vi/>\n"
"Language: vi\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 3.1-dev\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "Chưa được viết mã thực thi."

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr ""
"Tìm thấy câu lệnh mới, nhưng không có dấu ngăn cách giữa nó và lệnh trước."

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr ""

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "Không nhận ra thao tác thay đổi."

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "Cần giá trị %1$d, nhưng lại nhận được %2$d."

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr "Cần một dấu ngoặc ôm mở theo sau bởi một tập hợp các giá trị."

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr "Cần dấu ngoặc ôm mở."

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "Từ khóa không mong muốn."

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "Kết thúc bất ngờ của biểu thức CASE"

#: src/Components/CaseExpression.php:215
#, fuzzy
#| msgid "Unexpected end of CASE expression"
msgid "Potential duplicate alias of CASE expression."
msgstr "Kết thúc bất ngờ của biểu thức CASE"

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr ""

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "Một bí danh đã tìm thấy từ trước đây."

#: src/Components/CaseExpression.php:255
#, fuzzy
#| msgid "An alias was expected."
msgid "An alias was expected after AS."
msgstr "Cần một bí danh."

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""
"Cần tên biểu tượng. Một từ khoá truy ngược không thể được sử dụng như là một "
"tên cột mà không có các ngoặc sau."

#: src/Components/CreateDefinition.php:267
#, fuzzy
#| msgid "Variable name was expected."
msgid "A symbol name was expected!"
msgstr "Cần tên biến."

#: src/Components/CreateDefinition.php:299
msgid "A comma or a closing bracket was expected."
msgstr "Cần dấu phẩy hoặc dấu ngoặc ôm đóng."

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr "Cần dấu ngoặc ôm đóng."

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "Không nhận ra kiểu dữ liệu."

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
msgid "An expression was expected."
msgstr "Cần một biểu thức."

#: src/Components/Expression.php:258 src/Components/Expression.php:419
msgid "An alias was expected."
msgstr "Cần một bí danh."

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "Gặp dấu chấm không cần."

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "Gặp thẻ bài không cần."

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr "Cần vị trí tương đối."

#: src/Components/LockExpression.php:94
#, fuzzy
#| msgid "Unexpected end of CASE expression"
msgid "Unexpected end of LOCK expression."
msgstr "Kết thúc bất ngờ của biểu thức CASE"

#: src/Components/LockExpression.php:204
#, fuzzy
#| msgid "Unexpected end of CASE expression"
msgid "Unexpected end of Lock expression."
msgstr "Kết thúc bất ngờ của biểu thức CASE"

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "Tùy chọn này xung đột với \"%1$s\"."

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "Cần tên cũ của bảng."

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "Cần từ khóa \"TO\"."

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "Cần tên mới của bảng."

#: src/Components/RenameOperation.php:143
msgid "A rename operation was expected."
msgstr "Cần thao tác đổi tên."

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "Thiếu biểu thức chính quy."

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "Gặp ký tự không cần."

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr "Cần các khoảng trắng trước dấu phân tách."

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr "Cần dấu phân tách."

#: src/Lexer.php:937
#, php-format
msgid "Ending quote %1$s was expected."
msgstr "Cần trích dẫn đóng %1$s."

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "Cần tên biến."

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "Gặp phần đầu mệnh đề không cần."

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "Không thừa nhận kiểu mệnh đề."

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "Chưa có giao địch nào đã bắt đầu trước đây."

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr "Kiểu của mệnh đề này đã được phân tích trước đây."

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "Không nhận ra từ khóa."

#: src/Statement.php:398
#, fuzzy
#| msgid "Unexpected beginning of statement."
msgid "Keyword at end of statement."
msgstr "Gặp phần đầu mệnh đề không cần."

#: src/Statement.php:543
msgid "Unexpected ordering of clauses."
msgstr "Đặt mệnh đề không mong muốn."

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr "Cần tên của thực thể."

#: src/Statements/CreateStatement.php:601
#, fuzzy
#| msgid "Variable name was expected."
msgid "A table name was expected."
msgstr "Cần tên biến."

#: src/Statements/CreateStatement.php:606
msgid "At least one column definition was expected."
msgstr "Chưa có bảng nào được chọn."

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "Cần từ khóa \"RETURNS\"."

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr "Mệnh đề loại này không hợp lệ trong các truy vấn Đa bảng."

#: src/Statements/LockStatement.php:123
#, fuzzy
#| msgid "Unexpected beginning of statement."
msgid "Unexpected end of LOCK statement."
msgstr "Gặp phần đầu mệnh đề không cần."

#: src/Statements/PurgeStatement.php:141
#, fuzzy
#| msgid "Unexpected keyword."
msgid "Unexpected keyword"
msgstr "Từ khóa không mong muốn."

#: src/Statements/WithStatement.php:119
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "The name of the CTE was expected."
msgstr "Cần tên của thực thể."

#: src/Statements/WithStatement.php:138
#, fuzzy
#| msgid "A \"RETURNS\" keyword was expected."
msgid "AS keyword was expected."
msgstr "Cần từ khóa \"RETURNS\"."

#: src/Statements/WithStatement.php:154
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "Subquery of the CTE was expected."
msgstr "Cần tên của thực thể."

#: src/Statements/WithStatement.php:271
#, fuzzy
#| msgid "Unexpected beginning of statement."
msgid "Unexpected end of the WITH CTE."
msgstr "Gặp phần đầu mệnh đề không cần."

#~ msgid "error #1"
#~ msgstr "lỗi #1"

#~ msgid "strict error"
#~ msgstr "lỗi nghiêm ngặt"
