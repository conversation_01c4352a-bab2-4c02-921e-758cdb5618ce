# Automatically generated <>, 2010.
msgid ""
msgstr ""
"Project-Id-Version: SQL parser 5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2022-03-14 11:58+0000\n"
"Last-Translator: dingo thirteen <<EMAIL>>\n"
"Language-Team: Dutch <https://hosted.weblate.org/projects/phpmyadmin/"
"sql-parser/nl/>\n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.12-dev\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "Nog niet geïmplementeerd."

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr ""
"Een nieuwe opdracht werd gevonden, maar zonder scheidingsteken tussen deze "
"en de vorige opdracht."

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr "Ontbrekende komma voor de start van een wijzigingsbewerking."

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "Onbekende bewerking."

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "%1$d waarden werden verwacht, maar %2$d gevonden."

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr "Een openingshaakje gevolgd door een set met waardes werd verwacht."

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr "Een openingshaakje werd verwacht."

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "Onverwacht sleutelwoord."

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "Onverwacht einde van CASE expressie"

#: src/Components/CaseExpression.php:215
msgid "Potential duplicate alias of CASE expression."
msgstr "Potentiële dubbele alias van CASE expressie."

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr "Een alias werd verwacht na AS maar kreeg "

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "Een alias was eerder al gevonden."

#: src/Components/CaseExpression.php:255
msgid "An alias was expected after AS."
msgstr "Een alias werd verwacht na AS."

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""
"Een symbool naam werd verwacht! Een gereserveerd sleutelwoord kan niet als "
"kolomnaam gebruikt worden zonder achterwaartse aanhalingstekens."

#: src/Components/CreateDefinition.php:267
msgid "A symbol name was expected!"
msgstr "Een symbool naam werd verwacht!"

#: src/Components/CreateDefinition.php:299
msgid "A comma or a closing bracket was expected."
msgstr "Een komma of een sluitingshaakje werd verwacht."

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr "Een sluitingshaakje werd verwacht."

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "Onbekend gegevenstype."

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
msgid "An expression was expected."
msgstr "Een expressie werd verwacht."

#: src/Components/Expression.php:258 src/Components/Expression.php:419
msgid "An alias was expected."
msgstr "Een alias werd verwacht."

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "Onverwachte punt."

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "Onverwacht token."

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr "Een offset werd verwacht."

#: src/Components/LockExpression.php:94
msgid "Unexpected end of LOCK expression."
msgstr "Onverwacht einde van LOCK expressie."

#: src/Components/LockExpression.php:204
msgid "Unexpected end of Lock expression."
msgstr "Onverwacht einde van CASE expressie."

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "Deze optie conflicteert met \"%1$s\"."

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "De oude naam van de tabel werd verwacht."

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "Sleutelwoord \"TO\" werd verwacht."

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "De nieuwe naam van de tabel werd verwacht."

#: src/Components/RenameOperation.php:143
msgid "A rename operation was expected."
msgstr "Een hernoem bewerking werd verwacht."

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "Expressie ontbreekt."

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "Onverwachte letter."

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr "Spatie(s) werd(en) verwacht voor het scheidingsteken."

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr "Scheidingsteken verwacht."

#: src/Lexer.php:937
#, php-format
msgid "Ending quote %1$s was expected."
msgstr "Eindquote %1$s werd verwacht."

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "Naam van variabele werd verwacht."

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "Onverwacht begin van opdracht."

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "Opdrachttype niet herkend."

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "Er is geen transactie gestart."

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr "Dit type van voorwaarde is eerder uitgevoerd."

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "Sleutelwoord niet herkend."

#: src/Statement.php:398
msgid "Keyword at end of statement."
msgstr "Sleutelwoord aan eind van opdracht."

#: src/Statement.php:543
msgid "Unexpected ordering of clauses."
msgstr "Onverwachte volgorde van voorwaarden."

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr "De naam van de entiteit werd verwacht."

#: src/Statements/CreateStatement.php:601
msgid "A table name was expected."
msgstr "Een tabel naam werd verwacht."

#: src/Statements/CreateStatement.php:606
msgid "At least one column definition was expected."
msgstr "Er werd minimaal één kolomdefinitie verwacht."

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "Een \"RETURNS\" sleutelwoord werd verwacht."

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr "Dit type van voorwaarde is niet bruikbaar in multi-tabel queries."

#: src/Statements/LockStatement.php:123
msgid "Unexpected end of LOCK statement."
msgstr "Onverwacht einde van LOCK opdracht."

#: src/Statements/PurgeStatement.php:141
msgid "Unexpected keyword"
msgstr "Onverwacht sleutelwoord"

#: src/Statements/WithStatement.php:119
msgid "The name of the CTE was expected."
msgstr "De naam van de CTE werd verwacht."

#: src/Statements/WithStatement.php:138
msgid "AS keyword was expected."
msgstr "Het AS sleutelwoord werd verwacht."

#: src/Statements/WithStatement.php:154
msgid "Subquery of the CTE was expected."
msgstr "Subquery van de CTE werd verwacht."

#: src/Statements/WithStatement.php:271
msgid "Unexpected end of the WITH CTE."
msgstr "Onverwacht einde van WITH CTE opdracht."

#~ msgid "error #1"
#~ msgstr "fout #1"

#~ msgid "strict error"
#~ msgstr "strikte fout"
