# Automatically generated <>, 2010.
msgid ""
msgstr ""
"Project-Id-Version: SQL parser 5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2021-12-31 01:53+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Turkish <https://hosted.weblate.org/projects/phpmyadmin/"
"sql-parser/tr/>\n"
"Language: tr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.10.1\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "Henüz uygulanmadı."

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr ""
"Yeni bir ifade bulundu, ancak bu ve önceki arasında hiç sınırlayıcı yok."

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr "Yeni bir alter işlemi başlamadan önce eksik virgül."

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "Kategorilenmemiş değiştirme işlemi."

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "%1$d değer beklenmekte, ancak %2$d bulundu."

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr "Açık bir köşeli parantez ardından bir grup değer beklenmekte."

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr "Açık bir köşeli parantez beklenmekte."

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "Beklenmeyen anahtar kelime."

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "Beklenmeyen CASE ifadesi sonu"

#: src/Components/CaseExpression.php:215
msgid "Potential duplicate alias of CASE expression."
msgstr "CASE ifadesinin olası kopya kod adı."

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr "AS'den sonra beklenen ancak var olan bir kod adı "

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "Bir kodadı daha önce bulundu."

#: src/Components/CaseExpression.php:255
msgid "An alias was expected after AS."
msgstr "AS'den sonra bir kod adı beklenmekte."

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""
"Bir sembol adı beklenmekte! Ayrılmış bir anahtar kelime ters eğik tırnakları "
"olmadan bir sütun adı olarak kullanılamaz."

#: src/Components/CreateDefinition.php:267
msgid "A symbol name was expected!"
msgstr "Bir sembol adı beklenmekte!"

#: src/Components/CreateDefinition.php:299
msgid "A comma or a closing bracket was expected."
msgstr "Bir virgül ya da kapalı bir köşeli parantez beklenmekte."

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr "Kapalı bir köşeli parantez beklenmekte."

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "Tanınmayan veri türü."

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
msgid "An expression was expected."
msgstr "Bir ifade beklenmekte."

#: src/Components/Expression.php:258 src/Components/Expression.php:419
msgid "An alias was expected."
msgstr "Bir kodadı beklenmekte."

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "Beklenmeyen nokta."

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "Beklenmeyen belirteç."

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr "Bir karşılık beklenmekte."

#: src/Components/LockExpression.php:94
msgid "Unexpected end of LOCK expression."
msgstr "Beklenmeyen LOCK ifadesi sonu."

#: src/Components/LockExpression.php:204
msgid "Unexpected end of Lock expression."
msgstr "Beklenmeyen LOCK ifadesi sonu."

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "Bu seçenek \"%1$s\" ile çakışıyor."

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "Tablonun eski adı beklenmekte."

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "\"TO\" anahtar kelimesi beklenmekte."

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "Tablonun yeni adı beklenmekte."

#: src/Components/RenameOperation.php:143
msgid "A rename operation was expected."
msgstr "Yeniden adlandırma işlemi beklenmekte."

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "Eksik ifade."

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "Beklenmeyen karakter."

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr "Sınırlayıcıdan önce beklenen boşluk(lar)."

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr "Beklenen sınırlayıcı."

#: src/Lexer.php:937
#, php-format
msgid "Ending quote %1$s was expected."
msgstr "Sonlandırma tırnak işareti %1$s beklenmekte."

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "Değişken adı beklenmekte."

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "Beklenmeyen ifade başlangıcı."

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "Tanınmayan ifade türü."

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "Daha önce hiç işlem başlatılmadı."

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr "Bu tür yan tümce daha önce ayrıştırıldı."

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "Tanınmayan anahtar kelime."

#: src/Statement.php:398
msgid "Keyword at end of statement."
msgstr "İfade sonundaki anahtar kelime."

#: src/Statement.php:543
msgid "Unexpected ordering of clauses."
msgstr "Beklenmeyen yan tümce sıralaması."

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr "Varlığın adı beklenmekte."

#: src/Statements/CreateStatement.php:601
msgid "A table name was expected."
msgstr "Tablo adı beklenmekte."

#: src/Statements/CreateStatement.php:606
msgid "At least one column definition was expected."
msgstr "En az bir sütun tanımı beklenmekte."

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "\"RETURNS\" anahtar kelimesi beklenmekte."

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr "Bu tür yan tümce Çoklu tablo sorgularında geçerli değil."

#: src/Statements/LockStatement.php:123
msgid "Unexpected end of LOCK statement."
msgstr "Beklenmeyen LOCK deyimi sonu."

#: src/Statements/PurgeStatement.php:141
msgid "Unexpected keyword"
msgstr "Beklenmeyen anahtar kelime"

#: src/Statements/WithStatement.php:119
msgid "The name of the CTE was expected."
msgstr "CTE'nin adı beklenmekte."

#: src/Statements/WithStatement.php:138
msgid "AS keyword was expected."
msgstr "AS anahtar kelimesi beklenmekte."

#: src/Statements/WithStatement.php:154
msgid "Subquery of the CTE was expected."
msgstr "CTE'nin alt sorgusu beklenmekte."

#: src/Statements/WithStatement.php:271
msgid "Unexpected end of the WITH CTE."
msgstr "WITH CTE'nin beklenmeyen sonu."

#~ msgid "error #1"
#~ msgstr "hata #1"

#~ msgid "strict error"
#~ msgstr "kesin hata"
