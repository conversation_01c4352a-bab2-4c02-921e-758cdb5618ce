#
msgid ""
msgstr ""
"Project-Id-Version: SQL parser 5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2019-07-19 00:04+0000\n"
"Last-Translator: <PERSON><PERSON> Warorot <<EMAIL>>\n"
"Language-Team: Thai <https://hosted.weblate.org/projects/phpmyadmin/sql-"
"parser/th/>\n"
"Language: th\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 3.8-dev\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "ยังไม่มีการดำเนินการ"

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr "ตรวจพบประพจน์ใหม่ แต่ไม่มีตัวคั่นระหว่างข้อความนี้กับข้อความก่อนหน้า"

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr ""

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "ไม่รู้จักการปฏิบัติการนอกเหนือจากนี้"

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "ค่าของ %1$d ได้รับการคาดเดา แต่สิ่งที่พบคือ %2$d"

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr "เปิดวงเล็บเหลี่ยมแล้วตามด้วยชุดของค่าที่จะคาดเดา"

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr "วงเล็บเหลี่ยมเปิดได้รับการคาดเดา"

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "คำสำคัญที่ไม่ได้รับการคาดเดา"

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "จุดสิ้นสุดนิพจน์ CASE ไม่ได้รับการคาดเดา"

#: src/Components/CaseExpression.php:215
#, fuzzy
#| msgid "Unexpected end of CASE expression"
msgid "Potential duplicate alias of CASE expression."
msgstr "จุดสิ้นสุดนิพจน์ CASE ไม่ได้รับการคาดเดา"

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr ""

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "ชื่อ alias มีอยู่ก่อนแล้ว"

#: src/Components/CaseExpression.php:255
#, fuzzy
#| msgid "An alias was expected."
msgid "An alias was expected after AS."
msgstr "ตัวแทนได้รับการคาดเดา"

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""
"ชื่อสัญลักษณ์ได้รับการคาดเดาแล้ว "
"คำสำคัญที่สงวนไว้ไม่สามารถใช้เป็นชื่อคอลัมน์โดยไม่มีอัญประกาศกลับหลังได้"

#: src/Components/CreateDefinition.php:267
msgid "A symbol name was expected!"
msgstr "ชื่อสัญลักษณ์ได้รับการคาดเดา!"

#: src/Components/CreateDefinition.php:299
msgid "A comma or a closing bracket was expected."
msgstr "จุลภาค หรือ วงเล็บปิด ได้รับการคาดเดา"

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr "วงเล็บปิดได้รับการคาดเดา"

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "ไม่รู้จักชนิดข้อมูล"

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
msgid "An expression was expected."
msgstr "หนึ่งนิพจน์ได้รับการคาดเดา"

#: src/Components/Expression.php:258 src/Components/Expression.php:419
msgid "An alias was expected."
msgstr "ตัวแทนได้รับการคาดเดา"

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "จุด ไม่ได้รับการคาดเดา"

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "โทเค็นที่ไม่ได้คาดหวังไว้"

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr "หนึ่งออฟเซ็ตได้รับการคาดเดา"

#: src/Components/LockExpression.php:94
#, fuzzy
#| msgid "Unexpected end of CASE expression"
msgid "Unexpected end of LOCK expression."
msgstr "จุดสิ้นสุดนิพจน์ CASE ไม่ได้รับการคาดเดา"

#: src/Components/LockExpression.php:204
#, fuzzy
#| msgid "Unexpected end of CASE expression"
msgid "Unexpected end of Lock expression."
msgstr "จุดสิ้นสุดนิพจน์ CASE ไม่ได้รับการคาดเดา"

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "ตัวเลือกนี้ขัดแย้งกับ %1$s"

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "ชื่อเดิมของตารางได้รับการคาดเดา"

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "คำสำคัญ \"TO\" ได้รับการคาดเดา"

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "ชื่อตารางใหม่ได้รับการคาดเดา"

#: src/Components/RenameOperation.php:143
msgid "A rename operation was expected."
msgstr "การเปลี่ยนชื่อได้รับการคาดเดา"

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "นิพจน์ขาดหาย"

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "ตัวอักษรที่คาดเดาไม่ได้"

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr "วรรค(อาจจะมากกว่าหนึ่ง) ได้รับการคาดเดาก่อนตัวคั่น"

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr "ตัวคั่นได้รับการคาดเดา"

#: src/Lexer.php:937
#, php-format
msgid "Ending quote %1$s was expected."
msgstr "อัญประกาศปิด %1$s ได้รับการคาดเดา"

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "ชื่อตัวแปรได้รับการคาดเดา"

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "ไม่อาจคาดเดาจุดเริ่มต้นของประพจน์"

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "ไม่รู้จักชนิดของประพจน์"

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "ไม่มีข้อมูลเข้าออกได้เริ่มต้นมาก่อน"

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr "ชนิดของประโยคนี้ถูกวิเคราะห์มาแล้วก่อนหน้านี้"

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "ไม่รู้จักชนิดของคีย์เวิร์ด"

#: src/Statement.php:398
msgid "Keyword at end of statement."
msgstr "คีย์เวิร์ดในตอนท้ายของคำสั่ง"

#: src/Statement.php:543
msgid "Unexpected ordering of clauses."
msgstr "การเรียงของประโยคที่ไม่ได้คาดหวังไว้"

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr "ชื่อเอนทิติได้รับการคาดหวัง"

#: src/Statements/CreateStatement.php:601
msgid "A table name was expected."
msgstr "ชื่อตารางได้รับการคาดหวัง"

#: src/Statements/CreateStatement.php:606
msgid "At least one column definition was expected."
msgstr "การกำหนดคอลัมน์อย่างน้อยหนึ่งคอลัมน์ได้รับการคาดหวัง"

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "คีย์เวิร์ด \"RETURNS\" ได้รับการคาดหวัง"

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr "ประโยคชนิดนี้ไม่ถูกต้องในการเรียกใช้แบบหลายตาราง"

#: src/Statements/LockStatement.php:123
#, fuzzy
#| msgid "Unexpected beginning of statement."
msgid "Unexpected end of LOCK statement."
msgstr "ไม่อาจคาดเดาจุดเริ่มต้นของประพจน์"

#: src/Statements/PurgeStatement.php:141
#, fuzzy
#| msgid "Unexpected keyword."
msgid "Unexpected keyword"
msgstr "คำสำคัญที่ไม่ได้รับการคาดเดา"

#: src/Statements/WithStatement.php:119
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "The name of the CTE was expected."
msgstr "ชื่อเอนทิติได้รับการคาดหวัง"

#: src/Statements/WithStatement.php:138
#, fuzzy
#| msgid "A \"RETURNS\" keyword was expected."
msgid "AS keyword was expected."
msgstr "คีย์เวิร์ด \"RETURNS\" ได้รับการคาดหวัง"

#: src/Statements/WithStatement.php:154
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "Subquery of the CTE was expected."
msgstr "ชื่อเอนทิติได้รับการคาดหวัง"

#: src/Statements/WithStatement.php:271
#, fuzzy
#| msgid "Unexpected beginning of statement."
msgid "Unexpected end of the WITH CTE."
msgstr "ไม่อาจคาดเดาจุดเริ่มต้นของประพจน์"

#~ msgid "error #1"
#~ msgstr "ผิดพลาด #1"

#~ msgid "strict error"
#~ msgstr "ข้อผิดพลาดที่ครัดเคร้ง"
