#
msgid ""
msgstr ""
"Project-Id-Version: SQL parser 5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2022-05-03 10:14+0000\n"
"Last-Translator: dong heonhui <<EMAIL>>\n"
"Language-Team: Korean <https://hosted.weblate.org/projects/phpmyadmin/"
"sql-parser/ko/>\n"
"Language: ko\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 4.12.1\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "아직 구현되지 않은 기능입니다."

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr "새로운 문장이 발견되었지만, 이전 문장과의 구분기호가 없습니다."

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr "새 alter operation 시작 전에 쉼표가 누락되었습니다."

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "인식되지 않은 alter 작업입니다."

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "값 %1$d 이 예상되었지만, %2$d 가 발견되었습니다."

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr "일련의 값들에 의해 여는 괄호('(')가 예상됩니다."

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr "여는 괄호가 예상됩니다."

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "예상되지 않은 키워드입니다."

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "예기치 못한 CASE 표현식의 끝"

#: src/Components/CaseExpression.php:215
msgid "Potential duplicate alias of CASE expression."
msgstr "CASE 표현식의 잠재적 중복 별명입니다."

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr "AS뒤에 별명이 필요합니다. : "

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "이미 사용중인 별명입니다."

#: src/Components/CaseExpression.php:255
msgid "An alias was expected after AS."
msgstr "AS뒤에 별명이 필요합니다."

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""
"심볼 이름이 필요합니다! 예약어는 역따옴표 없이 열 이름으로 사용될 수 없습니"
"다."

#: src/Components/CreateDefinition.php:267
msgid "A symbol name was expected!"
msgstr "심볼 이름이 필요합니다!"

#: src/Components/CreateDefinition.php:299
msgid "A comma or a closing bracket was expected."
msgstr "쉼표 또는 닫는 대괄호가 필요합니다."

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr "닫는 괄호가 필요합니다."

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "인식할 수 없는 데이터 형식입니다."

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
msgid "An expression was expected."
msgstr "식이 필요합니다."

#: src/Components/Expression.php:258 src/Components/Expression.php:419
msgid "An alias was expected."
msgstr "별명이 필요합니다."

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "예기치 못한 점(.)입니다."

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "예기치 못한 토큰입니다."

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr "오프셋이 필요합니다."

#: src/Components/LockExpression.php:94
msgid "Unexpected end of LOCK expression."
msgstr "LOCK 표현식이 예기치 않게 끝났습니다."

#: src/Components/LockExpression.php:204
msgid "Unexpected end of Lock expression."
msgstr "Lock 표현식이 예기치 않게 끝났습니다."

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "이 옵션은 \"%1$s\"와(과) 충돌합니다."

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "테이블의 이전 이름이 팔요합니다."

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "키워드 \"TO\"가 팔요합니다."

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "테이블의 새로운 이름이 팔요합니다."

#: src/Components/RenameOperation.php:143
msgid "A rename operation was expected."
msgstr "이름 바꾸기 작업이 필요합니다."

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "누락된 표현입니다."

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "예상치 못한 문자입니다."

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr "구분기호 앞에 공백이 필요합니다."

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr "구분기호가 필요합니다."

#: src/Lexer.php:937
#, php-format
msgid "Ending quote %1$s was expected."
msgstr "닫는 따옴표 %1$s 가 팔요합니다."

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "변수명이 필요합니다."

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "명령문이 예기치 않게 시작되었습니다."

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "인식할 수 없는 명령문 유형입니다."

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "이전에 시작된 처리가 없습니다."

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr "이 유형의 절은 이전에 구문 분석되었습니다."

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "인식할 수 없는 키워드입니다."

#: src/Statement.php:398
msgid "Keyword at end of statement."
msgstr "명령문 끝에 있는 키워드."

#: src/Statement.php:543
msgid "Unexpected ordering of clauses."
msgstr "예기치 못한 문장(절) 순서입니다."

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr "개체 이름이 필요합니다."

#: src/Statements/CreateStatement.php:601
msgid "A table name was expected."
msgstr "테이블 이름이 필요합니다."

#: src/Statements/CreateStatement.php:606
msgid "At least one column definition was expected."
msgstr "최소 하나의 열 정의가 필요합니다."

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "\"RETURNS\"키워드가 필요합니다."

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr "이 유형의 문장(절)은 다중테이블 요청에서 유효하지 않습니다."

#: src/Statements/LockStatement.php:123
msgid "Unexpected end of LOCK statement."
msgstr "LOCK 표현식이 예기치 않게 끝났습니다."

#: src/Statements/PurgeStatement.php:141
msgid "Unexpected keyword"
msgstr "예상치 못한 키워드"

#: src/Statements/WithStatement.php:119
msgid "The name of the CTE was expected."
msgstr "CTE 이름이 필요합니다."

#: src/Statements/WithStatement.php:138
msgid "AS keyword was expected."
msgstr "AS 키워드가 필요합니다."

#: src/Statements/WithStatement.php:154
msgid "Subquery of the CTE was expected."
msgstr "CTE의 서브쿼리가 필요합니다."

#: src/Statements/WithStatement.php:271
msgid "Unexpected end of the WITH CTE."
msgstr "WITH CTE 표현식이 예기치 않게 끝났습니다."

#~ msgid "error #1"
#~ msgstr "오류 #1"

#~ msgid "strict error"
#~ msgstr "엄격한 오류"
