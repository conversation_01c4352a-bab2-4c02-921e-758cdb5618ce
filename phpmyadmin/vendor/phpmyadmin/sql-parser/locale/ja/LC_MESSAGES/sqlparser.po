#
msgid ""
msgstr ""
"Project-Id-Version: SQL parser 5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2021-12-31 01:53+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Japanese <https://hosted.weblate.org/projects/phpmyadmin/"
"sql-parser/ja/>\n"
"Language: ja\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 4.10.1\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "実装されていません。"

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr ""
"新しいステートメントが見つかりましたが、その前の文との間に区切り文字がありま"
"せん。"

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr "新しい ALTER 操作を開始する前にカンマがありません。"

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "認識できない alter 操作。"

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "値 %1$d があるべきですが、 %2$d が見つかりました。"

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr "左括弧とそれに続く一連の値があるべきです。"

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr "左括弧が必要です。"

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "予期しないキーワードです。"

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "予期しない CASE 式の終了"

#: src/Components/CaseExpression.php:215
msgid "Potential duplicate alias of CASE expression."
msgstr "CASE 式でエイリアスが重複している可能性があります。"

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr "AS の後にエイリアスが必要ですが、取得したものは "

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "エイリアスが前に見つかっています。"

#: src/Components/CaseExpression.php:255
msgid "An alias was expected after AS."
msgstr "AS の後にエイリアスが必要です。"

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""
"シンボル名が必要です。 予約語をバッククォートなしでカラム名として使用すること"
"はできません。"

#: src/Components/CreateDefinition.php:267
msgid "A symbol name was expected!"
msgstr "シンボル名が必要です!"

#: src/Components/CreateDefinition.php:299
msgid "A comma or a closing bracket was expected."
msgstr "カンマか閉じ括弧が必要です。"

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr "閉じる括弧があるべきです。"

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "認識できないデータ形式。"

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
msgid "An expression was expected."
msgstr "式が必要です。"

#: src/Components/Expression.php:258 src/Components/Expression.php:419
msgid "An alias was expected."
msgstr "エイリアスが必要です。"

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "予期しないドット。"

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "予期しないトークン。"

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr "オフセットがあるべきです。"

#: src/Components/LockExpression.php:94
msgid "Unexpected end of LOCK expression."
msgstr "予期しない LOCK 式の終了。"

#: src/Components/LockExpression.php:204
msgid "Unexpected end of Lock expression."
msgstr "予期しない Lock 式の終了。"

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "このオプションは \"%1$s\" と競合しています。"

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "テーブルの古い名前が必要です。"

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "キーワード \"TO\" が必要です。"

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "テーブルの新しい名前が必要です。"

#: src/Components/RenameOperation.php:143
msgid "A rename operation was expected."
msgstr "リネーム操作が必要です。"

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "式がありません。"

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "予期しない文字です。"

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr "区切り文字の前に空白が必要です。"

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr "区切り文字が必要です。"

#: src/Lexer.php:937
#, php-format
msgid "Ending quote %1$s was expected."
msgstr "終端クォート %1$s が必要です。"

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "変数名があるべきです。"

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "ここで文を始めることができません。"

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "認識されないステートメント形式です。"

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "開始されているトランザクションはありません。"

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr "この種類の句は以前に解釈済みです。"

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "認識できないキーワードです。"

#: src/Statement.php:398
msgid "Keyword at end of statement."
msgstr "ステートメントの末尾にキーワードがあります。"

#: src/Statement.php:543
msgid "Unexpected ordering of clauses."
msgstr "予期しない節の順序です。"

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr "エンティティの名前が予想されていました。"

#: src/Statements/CreateStatement.php:601
msgid "A table name was expected."
msgstr "テーブル名が必要です。"

#: src/Statements/CreateStatement.php:606
msgid "At least one column definition was expected."
msgstr "少なくとも 1 つのカラム定義が必要です。"

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "キーワード \"RETURNS\" が必要です。"

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr "この句の種類は、複数テーブルクエリでは無効です。"

#: src/Statements/LockStatement.php:123
msgid "Unexpected end of LOCK statement."
msgstr "LOCK ステートメントが予期せず終了しました。"

#: src/Statements/PurgeStatement.php:141
msgid "Unexpected keyword"
msgstr "予期しないキーワード"

#: src/Statements/WithStatement.php:119
msgid "The name of the CTE was expected."
msgstr "CTE の名前が必要です。"

#: src/Statements/WithStatement.php:138
msgid "AS keyword was expected."
msgstr "キーワード AS が必要です。"

#: src/Statements/WithStatement.php:154
msgid "Subquery of the CTE was expected."
msgstr "CTE のサブクエリが必要です。"

#: src/Statements/WithStatement.php:271
msgid "Unexpected end of the WITH CTE."
msgstr "WITH CTE が予期せず終了しました。"

#~ msgid "error #1"
#~ msgstr "エラー #1"

#~ msgid "strict error"
#~ msgstr "厳格なエラー"
