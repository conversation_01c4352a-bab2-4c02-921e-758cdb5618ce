# Automatically generated <>, 2010.
msgid ""
msgstr ""
"Project-Id-Version: SQL parser 5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2021-05-03 14:31+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Greek <https://hosted.weblate.org/projects/phpmyadmin/sql-"
"parser/el/>\n"
"Language: el\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.7-dev\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "Δεν εφαρμόστηκε ακόμα."

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr ""
"Βρέθηκε μια νέα δήλωση, αλλά χωρίς διαχωριστικό μεταξύ της και της "
"προηγούμενης."

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr "Λείπει κόμμα πριν από την έναρξη μιας νέας λειτουργίας αλλαγής."

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "Μη αναγνωρισμένη λειτουργία αλλαγής."

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "Αναμενόταν %1$d τιμές, αλλά βρέθηκαν %2$d."

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr "Αναμενόταν μια ανοιχτή αγκύλη ακολουθούμενη από ένα σύνολο τιμών."

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr "Αναμενόταν μια ανοιχτή αγκύλη."

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "Μη αναμενόμενη λέξη-κλειδί."

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "Μη αναμενόμενο τέλος της έκφρασης CASE"

#: src/Components/CaseExpression.php:215
msgid "Potential duplicate alias of CASE expression."
msgstr "Μη αναμενόμενο τέλος της έκφρασης CASE."

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr "Ένα ψευδώνυμο αναμενόταν μετά το AS αλλά πήρε "

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "Βρέθηκε μια ετικέτα προηγουμένως."

#: src/Components/CaseExpression.php:255
msgid "An alias was expected after AS."
msgstr "Ένα ψευδώνυμο αναμενόταν μετά το AS."

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""
"Αναμενόταν ένα όνομα συμβόλου! Μια δεσμευμένη λέξη-κλειδί δεν μπορεί να "
"χρησιμοποιηθεί ως όνομα στήλης χωρίς εισαγωγικά."

#: src/Components/CreateDefinition.php:267
msgid "A symbol name was expected!"
msgstr "Αναμενόταν όνομα συμβόλου!"

#: src/Components/CreateDefinition.php:299
msgid "A comma or a closing bracket was expected."
msgstr "Αναμενόταν ένα κόμμα ή μια κλειστή αγκύλη."

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr "Αναμενόταν κλείσιμο αγκύλης."

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "Μη αναγνωρισμένος τύπος δεδομένων."

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
msgid "An expression was expected."
msgstr "Αναμενόταν μια έκφραση."

#: src/Components/Expression.php:258 src/Components/Expression.php:419
msgid "An alias was expected."
msgstr "Αναμενόταν μια ετικέτα."

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "Μη αναμενόμενη τελεία."

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "Μη αναμενόμενο τεκμήριο."

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr "Αναμενόταν μια αντιστάθμιση."

#: src/Components/LockExpression.php:94
msgid "Unexpected end of LOCK expression."
msgstr "Μη αναμενόμενο τέλος της έκφρασης CASE."

#: src/Components/LockExpression.php:204
msgid "Unexpected end of Lock expression."
msgstr "Μη αναμενόμενο τέλος της έκφρασης CASE."

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "Αυτή η επιλογή έχει διαίνεξη με το «%1$s»."

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "Αναμενόταν το παλαιό όνομα του πίνακα."

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "Αναμενόταν η λέξη-κλειδί «TO»."

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "Αναμενόταν το νέο όνομα του πίνακα."

#: src/Components/RenameOperation.php:143
msgid "A rename operation was expected."
msgstr "Αναμενόταν μια λειτουργία μετονομασίας."

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "Απολεσθείσα έκφραση."

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "Μη αναμενόμενος χαρακτήρας."

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr "Αναμενόταν λευκό(ά) διάστημα(τα) πριν τον διαχωριστή."

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr "Αναμενόταν διαχωριστής."

#: src/Lexer.php:937
#, php-format
msgid "Ending quote %1$s was expected."
msgstr "Αναμενόταν τελικό εισαγωγικό %1$s."

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "Αναμενόταν όνομα μεταβλητής."

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "Μη αναμενόμενη έναρξη δήλωσης."

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "Μη αναγνωρισμένος τύπος δήλωσης."

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "Καμιά συναλλαγή δεν ξεκίνησε προηγουμένως."

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr "Αυτός ο τύπος ρύτρας έχει αναλυθεί προηγουμένως."

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "Μη αναγνωρισμένη λέξη κλειδί."

#: src/Statement.php:398
msgid "Keyword at end of statement."
msgstr "Λέξη-κλειδί στο τέλος της δήλωσης."

#: src/Statement.php:543
msgid "Unexpected ordering of clauses."
msgstr "Μη αναμενόμενη ταξινόμηση δηλώσεων."

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr "Αναμενόταν το όνομα της οντότητας."

#: src/Statements/CreateStatement.php:601
msgid "A table name was expected."
msgstr "Αναμενόταν όνομα πίνακα."

#: src/Statements/CreateStatement.php:606
msgid "At least one column definition was expected."
msgstr "Αναμενόταν τουλάχιστον ένας ορισμός πεδίου."

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "Αναμενόταν μια λέξη-κλειδί «RETURNS»."

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr "Αυτός ο τύπος ρύτρας δεν είναι έγκυρος σε ερωτήματα πολλαπλών πινάκων."

#: src/Statements/LockStatement.php:123
msgid "Unexpected end of LOCK statement."
msgstr "Μη αναμενόμενο τέλος της δήλωσης LOCK."

#: src/Statements/PurgeStatement.php:141
msgid "Unexpected keyword"
msgstr "Μη αναμενόμενη λέξη-κλειδί"

#: src/Statements/WithStatement.php:119
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "The name of the CTE was expected."
msgstr "Αναμενόταν το όνομα της οντότητας."

#: src/Statements/WithStatement.php:138
#, fuzzy
#| msgid "A \"RETURNS\" keyword was expected."
msgid "AS keyword was expected."
msgstr "Αναμενόταν μια λέξη-κλειδί «RETURNS»."

#: src/Statements/WithStatement.php:154
#, fuzzy
#| msgid "The name of the entity was expected."
msgid "Subquery of the CTE was expected."
msgstr "Αναμενόταν το όνομα της οντότητας."

#: src/Statements/WithStatement.php:271
#, fuzzy
#| msgid "Unexpected end of LOCK statement."
msgid "Unexpected end of the WITH CTE."
msgstr "Μη αναμενόμενο τέλος της δήλωσης LOCK."

#~ msgid "error #1"
#~ msgstr "σφάλμα #1"

#~ msgid "strict error"
#~ msgstr "περιορισμένο σφάλμα"
