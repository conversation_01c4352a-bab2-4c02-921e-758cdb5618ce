# Automatically generated <>, 2010.
msgid ""
msgstr ""
"Project-Id-Version: phpMyAdmin-docs 4.0.0-dev\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2022-02-17 09:56+0000\n"
"Last-Translator: <PERSON> <william<PERSON>@wdes.fr>\n"
"Language-Team: French <https://hosted.weblate.org/projects/phpmyadmin/"
"sql-parser/fr/>\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.11-dev\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "Pas encore mis en œuvre."

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr ""
"Un nouvel énoncé a été trouvé, mais il n'y a aucun délimiteur entre celui-ci "
"et le précédent."

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr "Virgule manquante avant le début d’une nouvelle opération ALTER."

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "Opération ALTER non reconnue."

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "%1$d valeurs étaient attendues, mais %2$d ont été trouvées."

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr "Une parenthèse gauche suivie d'un ensemble de valeurs étaient attendus."

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr "Une parenthèse gauche était attendue."

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "Mot clef inattendu."

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "Fin inattendue d'une expression CASE"

#: src/Components/CaseExpression.php:215
msgid "Potential duplicate alias of CASE expression."
msgstr "Potentiel duplicata de l'alias d'expression CASE."

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr "Un alias était attendu après AS mais obtenu : "

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "Un alias a été constaté précédemment."

#: src/Components/CaseExpression.php:255
msgid "An alias was expected after AS."
msgstr "Un alias était attendu après AS."

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""
"Un nom de symbole était attendu ! Un mot clé réservé ne peut pas servir "
"comme nom de colonne sans les apostrophes inverses."

#: src/Components/CreateDefinition.php:267
msgid "A symbol name was expected!"
msgstr "Un nom de variable était attendu !"

#: src/Components/CreateDefinition.php:299
msgid "A comma or a closing bracket was expected."
msgstr "Une virgule ou une parenthèse droite était attendus."

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr "Une parenthèse droite était attendue."

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "Type de données non reconnu."

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
msgid "An expression was expected."
msgstr "Une expression était attendue."

#: src/Components/Expression.php:258 src/Components/Expression.php:419
msgid "An alias was expected."
msgstr "Un alias était attendu."

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "Point inattendu."

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "Jeton inattendu."

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr "Un décalage était prévu."

#: src/Components/LockExpression.php:94
msgid "Unexpected end of LOCK expression."
msgstr "Fin inattendue de l'expression CASE."

#: src/Components/LockExpression.php:204
msgid "Unexpected end of Lock expression."
msgstr "Fin inattendue de l'expression LOCK."

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "Cette option entre en conflit avec « %1$s »."

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "L'ancien nom de la table était attendu."

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "Le mot clé « TO » était attendu."

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "Le nouveau nom de la table était attendu."

#: src/Components/RenameOperation.php:143
msgid "A rename operation was expected."
msgstr "Une opération de renommage était attendue."

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "Expression manquante."

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "Caractère inattendu."

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr "Des espaces ou tabulations étaient attendus avant le délimiteur."

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr "Un délimiteur était attendu."

#: src/Lexer.php:937
#, php-format
msgid "Ending quote %1$s was expected."
msgstr "Un guillemet %1$s était attendu."

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "Un nom de variable était attendu."

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "Début d'énoncé inattendu."

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "Type d'énoncé non reconnu."

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "Aucune transaction n'a été précédemment démarrée."

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr "Ce type de clause a été analysé précédemment."

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "Mot clé non reconnu."

#: src/Statement.php:398
msgid "Keyword at end of statement."
msgstr "Mot clé à la fin de l'énoncé."

#: src/Statement.php:543
msgid "Unexpected ordering of clauses."
msgstr "Classement inattendu des clauses."

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr "Le nom de l'entité était attendu."

#: src/Statements/CreateStatement.php:601
msgid "A table name was expected."
msgstr "Un nom de table était attendu."

#: src/Statements/CreateStatement.php:606
msgid "At least one column definition was expected."
msgstr "La définition d'au moins une colonne était attendue."

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "Le mot clé « RETURNS » était attendu."

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr "Ce type de clause a été analysé précédemment."

#: src/Statements/LockStatement.php:123
msgid "Unexpected end of LOCK statement."
msgstr "Fin inattendue de l'instruction LOCK."

#: src/Statements/PurgeStatement.php:141
msgid "Unexpected keyword"
msgstr "Mot clef inattendu"

#: src/Statements/WithStatement.php:119
msgid "The name of the CTE was expected."
msgstr "Le nom du CTE était attendu."

#: src/Statements/WithStatement.php:138
msgid "AS keyword was expected."
msgstr "Le mot clé AS était attendu."

#: src/Statements/WithStatement.php:154
msgid "Subquery of the CTE was expected."
msgstr "Une sous-requête du CTE était attendue."

#: src/Statements/WithStatement.php:271
msgid "Unexpected end of the WITH CTE."
msgstr "Fin inattendue du WITH CTE."

#~ msgid "error #1"
#~ msgstr "erreur #1"

#~ msgid "strict error"
#~ msgstr "erreur stricte"
