# Automatically generated <>, 2010.
msgid ""
msgstr ""
"Project-Id-Version: SQL parser 5\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2021-12-29 16:13-0300\n"
"PO-Revision-Date: 2022-06-18 10:16+0000\n"
"Last-Translator: AHOHNMYC <<EMAIL>>\n"
"Language-Team: Russian <https://hosted.weblate.org/projects/phpmyadmin/"
"sql-parser/ru/>\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"
"X-Generator: Weblate 4.13-dev\n"

#: src/Component.php:42 src/Component.php:62
msgid "Not implemented yet."
msgstr "Ещё не реализовано."

#: src/Components/AlterOperation.php:346 src/Statement.php:352
msgid ""
"A new statement was found, but no delimiter between it and the previous one."
msgstr ""
"Было найдено новое утверждение, но не было разделителя между ним и "
"предыдущим."

#: src/Components/AlterOperation.php:358
msgid "Missing comma before start of a new alter operation."
msgstr "Отсутствует запятая перед началом новой операции alter."

#: src/Components/AlterOperation.php:368
msgid "Unrecognized alter operation."
msgstr "Нераспознанная операция изменения."

#: src/Components/Array2d.php:91
#, php-format
msgid "%1$d values were expected, but found %2$d."
msgstr "Ожидалось %1$d значений, найдено %2$d."

#: src/Components/Array2d.php:111
msgid "An opening bracket followed by a set of values was expected."
msgstr "Ожидалась открывающая скобка и список значений."

#: src/Components/ArrayObj.php:109 src/Components/CreateDefinition.php:232
msgid "An opening bracket was expected."
msgstr "Ожидалась открывающая скобка."

#: src/Components/CaseExpression.php:136 src/Components/CaseExpression.php:164
#: src/Components/CaseExpression.php:175 src/Components/CaseExpression.php:187
#: src/Components/IndexHint.php:137 src/Components/IndexHint.php:167
#: src/Components/LockExpression.php:176 src/Components/LockExpression.php:183
#: src/Components/LockExpression.php:191 src/Statements/DeleteStatement.php:250
#: src/Statements/DeleteStatement.php:269
#: src/Statements/DeleteStatement.php:309
#: src/Statements/DeleteStatement.php:321
#: src/Statements/DeleteStatement.php:347
#: src/Statements/DeleteStatement.php:354
#: src/Statements/InsertStatement.php:191
#: src/Statements/InsertStatement.php:222 src/Statements/LoadStatement.php:265
#: src/Statements/LockStatement.php:88 src/Statements/ReplaceStatement.php:151
#: src/Statements/ReplaceStatement.php:180
msgid "Unexpected keyword."
msgstr "Неожиданное ключевое слово."

#: src/Components/CaseExpression.php:195
msgid "Unexpected end of CASE expression"
msgstr "Неожиданное окончание CASE выражения"

#: src/Components/CaseExpression.php:215
msgid "Potential duplicate alias of CASE expression."
msgstr "Потенциальный дубликат псевдонима в выражении CASE."

#: src/Components/CaseExpression.php:228
msgid "An alias expected after AS but got "
msgstr "После AS ожидается псевдоним, однако получено "

#: src/Components/CaseExpression.php:241 src/Components/Expression.php:353
#: src/Components/Expression.php:373 src/Components/Expression.php:408
msgid "An alias was previously found."
msgstr "Псевдоним был найден ранее."

#: src/Components/CaseExpression.php:255
msgid "An alias was expected after AS."
msgstr "После AS ожидался псевдоним."

#: src/Components/CreateDefinition.php:254
msgid ""
"A symbol name was expected! A reserved keyword can not be used as a column "
"name without backquotes."
msgstr ""
"Ожидалось имя символа! Зарезервированное ключевое слово не может "
"использоваться как имя столбца без обратных запросов."

#: src/Components/CreateDefinition.php:267
msgid "A symbol name was expected!"
msgstr "Ожидалось имя символа!"

#: src/Components/CreateDefinition.php:299
msgid "A comma or a closing bracket was expected."
msgstr "Ожидалась запятая или закрывающая скобка."

#: src/Components/CreateDefinition.php:312 src/Statements/WithStatement.php:333
msgid "A closing bracket was expected."
msgstr "Ожидалась закрывающая скобка."

#: src/Components/DataType.php:132
msgid "Unrecognized data type."
msgstr "Нераспознанный тип данных."

#: src/Components/ExpressionArray.php:108 src/Statements/WithStatement.php:202
msgid "An expression was expected."
msgstr "Ожидалось выражение."

#: src/Components/Expression.php:258 src/Components/Expression.php:419
msgid "An alias was expected."
msgstr "Ожидался псевдоним."

#: src/Components/Expression.php:387
msgid "Unexpected dot."
msgstr "Неожиданная точка."

#: src/Components/IndexHint.php:143 src/Components/IndexHint.php:173
#: src/Components/Key.php:260 src/Components/LockExpression.php:164
#: src/Components/SetOperation.php:141 src/Statement.php:249
#: src/Statements/DeleteStatement.php:264
#: src/Statements/DeleteStatement.php:316
#: src/Statements/InsertStatement.php:205
#: src/Statements/InsertStatement.php:242 src/Statements/LoadStatement.php:270
#: src/Statements/LoadStatement.php:291 src/Statements/LoadStatement.php:310
#: src/Statements/LockStatement.php:96 src/Statements/LockStatement.php:103
#: src/Statements/PurgeStatement.php:109 src/Statements/PurgeStatement.php:122
#: src/Statements/PurgeStatement.php:143
#: src/Statements/ReplaceStatement.php:165 src/Statements/WithStatement.php:133
msgid "Unexpected token."
msgstr "Неожиданный токен."

#: src/Components/Limit.php:81 src/Components/Limit.php:108
msgid "An offset was expected."
msgstr "Ожидалось смещение."

#: src/Components/LockExpression.php:94
msgid "Unexpected end of LOCK expression."
msgstr "Неожиданное окончание выражения LOCK."

#: src/Components/LockExpression.php:204
msgid "Unexpected end of Lock expression."
msgstr "Неожиданное окончание выражения Lock."

#: src/Components/OptionsArray.php:151
#, php-format
msgid "This option conflicts with \"%1$s\"."
msgstr "Этот параметр конфликтует с \"%1$s\"."

#: src/Components/RenameOperation.php:106
msgid "The old name of the table was expected."
msgstr "Ожидалось старое имя таблицы."

#: src/Components/RenameOperation.php:112
msgid "Keyword \"TO\" was expected."
msgstr "Ожидалось ключевое слово \"TO\"."

#: src/Components/RenameOperation.php:127
msgid "The new name of the table was expected."
msgstr "Ожидалось новое имя таблицы."

#: src/Components/RenameOperation.php:143
msgid "A rename operation was expected."
msgstr "Ожидалась операция переименования."

#: src/Components/SetOperation.php:124
msgid "Missing expression."
msgstr "Отсутствует выражение."

#: src/Lexer.php:251
msgid "Unexpected character."
msgstr "Неожиданный символ."

#: src/Lexer.php:290
msgid "Expected whitespace(s) before delimiter."
msgstr "Ожидался пробел перед разделителем."

#: src/Lexer.php:306 src/Lexer.php:325
msgid "Expected delimiter."
msgstr "Ожидается разделитель."

#: src/Lexer.php:937
#, php-format
msgid "Ending quote %1$s was expected."
msgstr "Ожидалась закрывающая кавычка %1$s."

#: src/Lexer.php:989
msgid "Variable name was expected."
msgstr "Ожидалось имя переменной."

#: src/Parser.php:453
msgid "Unexpected beginning of statement."
msgstr "Неожиданное начало выражения."

#: src/Parser.php:476
msgid "Unrecognized statement type."
msgstr "Неизвестный оператор."

#: src/Parser.php:563
msgid "No transaction was previously started."
msgstr "Ни одна транзакция не была ранее начата."

#: src/Statement.php:320
msgid "This type of clause was previously parsed."
msgstr "Этот тип предложения ранее анализировался."

#: src/Statement.php:387
msgid "Unrecognized keyword."
msgstr "Неизвестное ключевое слово."

#: src/Statement.php:398
msgid "Keyword at end of statement."
msgstr "Ключевое слово в конце инструкции."

#: src/Statement.php:543
msgid "Unexpected ordering of clauses."
msgstr "Неожиданный порядок предложений."

#: src/Statements/CreateStatement.php:552
msgid "The name of the entity was expected."
msgstr "Ожидалось имя сущности."

#: src/Statements/CreateStatement.php:601
msgid "A table name was expected."
msgstr "Ожидалось имя таблицы."

#: src/Statements/CreateStatement.php:606
msgid "At least one column definition was expected."
msgstr "Ожидалось объявление, по крайней мере, одного столбца."

#: src/Statements/CreateStatement.php:710
msgid "A \"RETURNS\" keyword was expected."
msgstr "Ожидалось ключевое слово \"RETURNS\"."

#: src/Statements/DeleteStatement.php:330
msgid "This type of clause is not valid in Multi-table queries."
msgstr "Такого рода выражения не поддерживаются в мульти-табличных запросах."

#: src/Statements/LockStatement.php:123
msgid "Unexpected end of LOCK statement."
msgstr "Неожиданное окончание инструкции LOCK."

#: src/Statements/PurgeStatement.php:141
msgid "Unexpected keyword"
msgstr "Неожиданное ключевое слово"

#: src/Statements/WithStatement.php:119
msgid "The name of the CTE was expected."
msgstr "Ожидалось имя CTE."

#: src/Statements/WithStatement.php:138
msgid "AS keyword was expected."
msgstr "Ожидалось ключевое слово AS."

#: src/Statements/WithStatement.php:154
msgid "Subquery of the CTE was expected."
msgstr "Ожидался подзапрос CTE."

#: src/Statements/WithStatement.php:271
msgid "Unexpected end of the WITH CTE."
msgstr "Неожиданное окончание CTE WITH."

#~ msgid "error #1"
#~ msgstr "ошибка #1"

#~ msgid "strict error"
#~ msgstr "строгая ошибка"
