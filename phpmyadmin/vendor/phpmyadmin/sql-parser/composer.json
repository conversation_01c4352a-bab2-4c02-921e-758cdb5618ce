{"name": "phpmyadmin/sql-parser", "description": "A validating SQL lexer and parser with a focus on MySQL dialect.", "license": "GPL-2.0-or-later", "keywords": ["sql", "lexer", "parser", "analysis", "sql syntax highlighter", "sql lexer", "sql tokenizer", "sql parser", "sql linter", "query linter"], "homepage": "https://github.com/phpmyadmin/sql-parser", "authors": [{"name": "The phpMyAdmin Team", "email": "<EMAIL>", "homepage": "https://www.phpmyadmin.net/team/"}], "support": {"issues": "https://github.com/phpmyadmin/sql-parser/issues", "source": "https://github.com/phpmyadmin/sql-parser"}, "funding": [{"type": "other", "url": "https://www.phpmyadmin.net/donate/"}], "require": {"php": "^7.2 || ^8.0", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"phpbench/phpbench": "^1.1", "phpmyadmin/coding-standard": "^3.0", "phpmyadmin/motranslator": "^4.0 || ^5.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.9.12", "phpstan/phpstan-phpunit": "^1.3.3", "phpunit/php-code-coverage": "*", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "^0.16.1", "vimeo/psalm": "^4.11", "zumba/json-serializer": "^3.0"}, "conflict": {"phpmyadmin/motranslator": "<3.0"}, "suggest": {"ext-mbstring": "For best performance", "phpmyadmin/motranslator": "Translate messages to your favorite locale"}, "bin": ["bin/highlight-query", "bin/lint-query", "bin/tokenize-query"], "autoload": {"psr-4": {"PhpMyAdmin\\SqlParser\\": "src"}}, "autoload-dev": {"psr-4": {"PhpMyAdmin\\SqlParser\\Tests\\": "tests"}}, "scripts": {"phpcbf": "@php phpcbf", "phpcs": "@php phpcs", "phpstan": "@php phpstan analyse", "psalm": "@php psalm --no-diff", "phpunit": "@php phpunit --color=always", "phpbench": "@php phpbench run tests/benchmarks --report=aggregate", "test": ["@phpcs", "@phpstan", "@psalm", "@phpunit"], "update:baselines": ["@php phpstan analyse --generate-baseline", "@php psalm --set-baseline=psalm-baseline.xml"]}, "config": {"sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "dealerdirect/phpcodesniffer-composer-installer": true, "phpstan/extension-installer": true}}, "archive": {"exclude": ["/tests", "/phpunit.xml.dist"]}}