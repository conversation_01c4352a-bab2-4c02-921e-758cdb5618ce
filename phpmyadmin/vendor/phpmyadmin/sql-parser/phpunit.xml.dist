<?xml version="1.0" encoding="UTF-8"?>
<phpunit backupGlobals="false"
         backupStaticAttributes="false"
         bootstrap="vendor/autoload.php"
         beStrictAboutOutputDuringTests="true"
         colors="true"
         verbose="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnFailure="false">
    <logging>
        <log type="coverage-clover" target="build/logs/clover.xml"/>
        <log type="coverage-xml" target="build/logs/coverage-xml"/>
        <log type="junit" target="build/logs/junit.xml"/>
    </logging>
    <testsuites>
        <testsuite name="Builder">
            <directory suffix=".php">./tests/Builder</directory>
        </testsuite>
        <testsuite name="Components">
            <directory suffix=".php">./tests/Components</directory>
        </testsuite>
        <testsuite name="Lexer">
            <directory suffix=".php">./tests/Lexer</directory>
        </testsuite>
        <testsuite name="Misc">
            <directory suffix=".php">./tests/Misc</directory>
        </testsuite>
        <testsuite name="Parser">
            <directory suffix=".php">./tests/Parser</directory>
        </testsuite>
        <testsuite name="Utils">
            <directory suffix=".php">./tests/Utils</directory>
        </testsuite>
        <testsuite name="Tools">
            <directory suffix=".php">./tests/Tools</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">src/</directory>
        </whitelist>
    </filter>
    <php>
        <ini name="display_errors" value="On" />
        <ini name="display_startup_errors" value="On" />
        <ini name="error_reporting" value="E_ALL" />
    </php>
</phpunit>
