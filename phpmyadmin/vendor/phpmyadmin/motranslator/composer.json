{"name": "phpmyadmin/motranslator", "description": "Translation API for PHP using Gettext MO files", "license": "GPL-2.0-or-later", "keywords": ["gettext", "mo", "translator", "i18n"], "homepage": "https://github.com/phpmyadmin/motranslator", "authors": [{"name": "The phpMyAdmin Team", "email": "<EMAIL>", "homepage": "https://www.phpmyadmin.net/team/"}], "support": {"issues": "https://github.com/phpmyadmin/motranslator/issues", "source": "https://github.com/phpmyadmin/motranslator"}, "scripts": {"phpcbf": "@php phpcbf", "phpcs": "@php phpcs", "phpstan": "@php phpstan", "phpunit": "@php phpunit", "test": ["@phpcs", "@phpstan", "@phpunit"]}, "require": {"php": "^7.1 || ^8.0", "symfony/expression-language": "^4.0 || ^5.0 || ^6.0"}, "require-dev": {"phpunit/phpunit": "^7.4 || ^8 || ^9", "phpmyadmin/coding-standard": "^3.0.0", "phpstan/phpstan": "^1.4.6"}, "autoload": {"psr-4": {"PhpMyAdmin\\MoTranslator\\": "src"}}, "autoload-dev": {"psr-4": {"PhpMyAdmin\\MoTranslator\\Tests\\": "tests"}}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "suggest": {"ext-apcu": "Needed for ACPu-backed translation cache"}}