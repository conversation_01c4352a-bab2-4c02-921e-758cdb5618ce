{"name": "amphp/amp", "homepage": "https://amphp.org/amp", "description": "A non-blocking concurrency framework for PHP applications.", "keywords": ["async", "asynchronous", "concurrency", "promise", "awaitable", "future", "non-blocking", "event", "event-loop"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.1"}, "require-dev": {"ext-json": "*", "amphp/phpunit-util": "^1", "amphp/php-cs-fixer-config": "dev-master", "react/promise": "^2", "phpunit/phpunit": "^7 | ^8 | ^9", "psalm/phar": "^3.11@dev", "jetbrains/phpstorm-stubs": "^2019.3"}, "autoload": {"psr-4": {"Amp\\": "lib"}, "files": ["lib/functions.php", "lib/Internal/functions.php"]}, "autoload-dev": {"psr-4": {"Amp\\Test\\": "test"}}, "support": {"issues": "https://github.com/amphp/amp/issues", "irc": "irc://irc.freenode.org/amphp"}, "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "scripts": {"test": "@php -dzend.assertions=1 -dassert.exception=1 ./vendor/bin/phpunit", "code-style": "@php ./vendor/bin/php-cs-fixer fix"}}