{"name": "amphp/byte-stream", "homepage": "http://amphp.org/byte-stream", "description": "A stream abstraction to make working with non-blocking I/O simple.", "support": {"issues": "https://github.com/amphp/byte-stream/issues", "irc": "irc://irc.freenode.org/amphp"}, "keywords": ["stream", "async", "non-blocking", "amp", "amphp", "io"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.1", "amphp/amp": "^2"}, "require-dev": {"amphp/phpunit-util": "^1.4", "phpunit/phpunit": "^6 || ^7 || ^8", "friendsofphp/php-cs-fixer": "^2.3", "amphp/php-cs-fixer-config": "dev-master", "psalm/phar": "^3.11.4", "jetbrains/phpstorm-stubs": "^2019.3"}, "autoload": {"psr-4": {"Amp\\ByteStream\\": "lib"}, "files": ["lib/functions.php"]}, "autoload-dev": {"psr-4": {"Amp\\ByteStream\\Test\\": "test"}}, "extra": {"branch-alias": {"dev-master": "1.x-dev"}}}