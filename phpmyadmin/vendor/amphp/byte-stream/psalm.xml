<?xml version="1.0"?>
<psalm
        totallyTyped="false"
        errorLevel="2"
        phpVersion="7.0"
        resolveFromConfigFile="true"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="https://getpsalm.org/schema/config"
        xsi:schemaLocation="https://getpsalm.org/schema/config vendor/vimeo/psalm/config.xsd"
>
    <projectFiles>
        <directory name="examples"/>
        <directory name="lib"/>
        <ignoreFiles>
            <directory name="vendor"/>
        </ignoreFiles>
    </projectFiles>

    <issueHandlers>
        <StringIncrement>
            <errorLevel type="suppress">
                <directory name="examples"/>
                <directory name="lib"/>
            </errorLevel>
        </StringIncrement>

        <RedundantConditionGivenDocblockType>
            <errorLevel type="suppress">
                <directory name="lib"/>
            </errorLevel>
        </RedundantConditionGivenDocblockType>

        <DocblockTypeContradiction>
            <errorLevel type="suppress">
                <directory name="lib"/>
            </errorLevel>
        </DocblockTypeContradiction>

        <MissingClosureParamType>
            <errorLevel type="suppress">
                <directory name="examples"/>
                <directory name="lib"/>
            </errorLevel>
        </MissingClosureParamType>

        <MissingClosureReturnType>
            <errorLevel type="suppress">
                <directory name="examples"/>
                <directory name="lib"/>
            </errorLevel>
        </MissingClosureReturnType>
    </issueHandlers>
</psalm>
