build: false
shallow_clone: true
platform: x86
clone_folder: c:\projects\webmozart\path-util

cache:
  - '%LOCALAPPDATA%\Composer\files'

init:
  - SET PATH=C:\Program Files\OpenSSL;c:\tools\php;%PATH%

environment:
  matrix:
    - COMPOSER_FLAGS: ""
    - COMPOSER_FLAGS: --prefer-lowest --prefer-stable

install:
  - cinst -y OpenSSL.Light
  - cinst -y php
  - cd c:\tools\php
  - copy php.ini-production php.ini /Y
  - echo date.timezone="UTC" >> php.ini
  - echo extension_dir=ext >> php.ini
  - echo extension=php_openssl.dll >> php.ini
  - echo extension=php_mbstring.dll >> php.ini
  - echo extension=php_fileinfo.dll >> php.ini
  - echo memory_limit=1G >> php.ini
  - cd c:\projects\webmozart\path-util
  - php -r "readfile('http://getcomposer.org/installer');" | php
  - php composer.phar update %COMPOSER_FLAGS% --no-interaction --no-progress

test_script:
  - cd c:\projects\webmozart\path-util
  - vendor\bin\phpunit.bat --verbose
