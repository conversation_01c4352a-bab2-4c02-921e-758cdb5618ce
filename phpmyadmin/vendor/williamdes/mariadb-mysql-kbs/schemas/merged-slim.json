{"$id": "merged-slim.json", "$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "definitions": {}, "properties": {"vars": {"type": "object", "patternProperties": {"^([a-zA-Z_]+)": {"type": "object", "properties": {"name": {"type": "string", "minLength": 2}, "scope": {"type": "array", "additionalItems": false, "items": {"type": "string", "enum": ["global", "session", "connection"]}}, "type": {"type": "string"}, "ids": {"type": "array", "additionalItems": false, "items": {"$ref": "linked-key-var-url.json"}}, "cli": {"type": "string", "minLength": 2}, "default": {"type": "string", "minLength": 0}, "dynamic": {"type": "boolean"}, "range": {"type": "object", "additionalProperties": false, "properties": {"from": {"type": "number"}, "to": {"oneOf": [{"type": "number"}, {"type": "string", "enum": ["upwards"]}]}}}, "validValues": {"type": "array", "additionalItems": false, "items": {"type": ["string", "number"]}}}, "additionalProperties": false}}, "additionalProperties": false}, "version": {"$ref": "doc-version.json"}, "urls": {"type": "array", "additionalItems": false, "items": {"$ref": "doc-url.json"}}, "types": {"$ref": "linked-key-var.json"}, "varTypes": {"$ref": "linked-key-var.json"}}, "additionalProperties": false}