{"vars": {"Aria_pagecache_blocks_not_flushed": {"t": 3, "a": [{"a": "aria_pagecache_blocks_not_flushed", "u": 0, "t": 2}]}, "Aria_pagecache_blocks_unused": {"t": 3, "a": [{"a": "aria_pagecache_blocks_unused", "u": 0, "t": 2}]}, "Aria_pagecache_blocks_used": {"t": 3, "a": [{"a": "aria_pagecache_blocks_used", "u": 0, "t": 2}]}, "Aria_pagecache_read_requests": {"t": 3, "a": [{"a": "aria_pagecache_read_requests", "u": 0, "t": 2}]}, "Aria_pagecache_reads": {"t": 3, "a": [{"a": "aria_pagecache_reads", "u": 0, "t": 2}]}, "Aria_pagecache_write_requests": {"t": 3, "a": [{"a": "aria_pagecache_write_requests", "u": 0, "t": 2}]}, "Aria_pagecache_writes": {"t": 3, "a": [{"a": "aria_page<PERSON>che_writes", "u": 0, "t": 2}]}, "Aria_transaction_log_syncs": {"t": 3, "a": [{"a": "aria_transaction_log_syncs", "u": 0, "t": 2}]}, "aria_block_size": {"d": false, "t": 3, "a": [{"a": "aria_block_size", "u": 1, "t": 2}]}, "aria_checkpoint_interval": {"d": true, "t": 3, "a": [{"a": "aria_checkpoint_interval", "u": 1, "t": 2}]}, "aria_checkpoint_log_activity": {"d": true, "t": 3, "a": [{"a": "aria_checkpoint_log_activity", "u": 1, "t": 2}]}, "aria_encrypt_tables": {"d": true, "t": 2, "a": [{"a": "aria_encrypt_tables", "u": 1, "t": 2}]}, "aria_force_start_after_recovery_failures": {"d": false, "t": 3, "a": [{"a": "aria_force_start_after_recovery_failures", "u": 1, "t": 2}]}, "aria_group_commit": {"d": false, "t": 1, "a": [{"a": "aria_group_commit", "u": 1, "t": 2}]}, "aria_group_commit_interval": {"d": false, "t": 3, "a": [{"a": "aria_group_commit_interval", "u": 1, "t": 2}]}, "aria_log_file_size": {"d": true, "t": 3, "a": [{"a": "aria_log_file_size", "u": 1, "t": 2}]}, "aria_log_purge_type": {"d": true, "t": 5, "a": [{"a": "aria_log_purge_type", "u": 1, "t": 2}]}, "aria_max_sort_file_size": {"d": true, "t": 3, "a": [{"a": "aria_max_sort_file_size", "u": 1, "t": 2}]}, "aria_page_checksum": {"d": true, "t": 2, "a": [{"a": "aria_page_checksum", "u": 1, "t": 2}]}, "aria_pagecache_age_threshold": {"d": true, "t": 3, "a": [{"a": "aria_pagecache_age_threshold", "u": 1, "t": 2}]}, "aria_pagecache_buffer_size": {"d": false, "t": 3, "a": [{"a": "aria_pagecache_buffer_size", "u": 1, "t": 2}]}, "aria_pagecache_division_limit": {"d": true, "t": 3, "a": [{"a": "aria_pagecache_division_limit", "u": 1, "t": 2}]}, "aria_pagecache_file_hash_size": {"d": false, "t": 3, "a": [{"a": "aria_pagecache_file_hash_size", "u": 1, "t": 2}]}, "aria_recover": {"a": [{"a": "aria_recover", "u": 1, "t": 2}]}, "aria_recover_options": {"d": true, "t": 5, "a": [{"a": "aria_recover_options", "u": 1, "t": 2}]}, "aria_repair_threads": {"d": true, "t": 3, "a": [{"a": "aria_repair_threads", "u": 1, "t": 2}]}, "aria_sort_buffer_size": {"d": true, "t": 3, "a": [{"a": "aria_sort_buffer_size", "u": 1, "t": 2}]}, "aria_stats_method": {"d": true, "t": 3, "a": [{"a": "aria_stats_method", "u": 1, "t": 2}]}, "aria_sync_log_dir": {"d": true, "t": 5, "a": [{"a": "aria_sync_log_dir", "u": 1, "t": 2}]}, "aria_used_for_temp_tables": {"d": false, "t": 2, "a": [{"a": "aria_used_for_temp_tables", "u": 1, "t": 2}]}, "deadlock_search_depth_long": {"d": true, "t": 3, "a": [{"a": "deadlock_search_depth_long", "u": 1, "t": 2}]}, "deadlock_search_depth_short": {"d": true, "t": 3, "a": [{"a": "deadlock_search_depth_short", "u": 1, "t": 2}]}, "deadlock_timeout_long": {"d": true, "t": 3, "a": [{"a": "deadlock_timeout_long", "u": 1, "t": 2}]}, "deadlock_timeout_short": {"d": true, "t": 3, "a": [{"a": "deadlock_timeout_short", "u": 1, "t": 2}]}, "gssapi_keytab_path": {"d": false, "t": 1, "a": [{"a": "gssapi_keytab_path", "u": 2, "t": 2}]}, "gssapi_principal_name": {"d": false, "t": 1, "a": [{"a": "gssapi_principal_name", "u": 2, "t": 2}]}, "gssapi_mech_name": {"d": false, "t": 5, "a": [{"a": "gssapi_mech_name", "u": 2, "t": 2}]}, "gssapi": {"t": 5, "a": [{"a": "gssapi", "u": 2, "t": 2}]}, "pam_debug": {"d": false, "t": 2, "a": [{"a": "pam_debug", "u": 3, "t": 2}]}, "pam_use_cleartext_plugin": {"d": false, "t": 2, "a": [{"a": "pam_use_cleartext_plugin", "u": 3, "t": 2}]}, "pam_winbind_workaround": {"d": true, "t": 2, "a": [{"a": "pam_winbind_workaround", "u": 3, "t": 2}]}, "pam": {"t": 5, "a": [{"a": "pam", "u": 3, "t": 2}]}, "aws_key_management_key_spec": {"d": false, "t": 5, "a": [{"a": "aws_key_management_key_spec", "u": 4, "t": 2}]}, "aws_key_management_log_level": {"d": false, "t": 5, "a": [{"a": "aws_key_management_log_level", "u": 4, "t": 2}]}, "aws_key_management_master_key_id": {"d": false, "t": 1, "a": [{"a": "aws_key_management_master_key_id", "u": 4, "t": 2}]}, "aws_key_management_mock": {"d": false, "t": 2, "a": [{"a": "aws_key_management_mock", "u": 4, "t": 2}]}, "aws_key_management_region": {"d": false, "t": 1, "a": [{"a": "aws_key_management_region", "u": 4, "t": 2}]}, "aws_key_management_request_timeout": {"d": false, "t": 3, "a": [{"a": "aws_key_management_request_timeout", "u": 4, "t": 2}]}, "aws_key_management_rotate_key": {"d": true, "t": 3, "a": [{"a": "aws_key_management_rotate_key", "u": 4, "t": 2}]}, "aws_key_management": {"t": 5, "a": [{"a": "aws_key_management", "u": 4, "t": 2}]}, "Cassandra_multiget_keys_scanned": {"t": 3, "a": [{"a": "cassandra_multiget_keys_scanned", "u": 5, "t": 2}]}, "Cassandra_multiget_reads": {"t": 3, "a": [{"a": "cassandra_multiget_reads", "u": 5, "t": 2}]}, "Cassandra_multiget_rows_read": {"t": 3, "a": [{"a": "cassandra_multiget_rows_read", "u": 5, "t": 2}]}, "Cassandra_network_exceptions": {"t": 3, "a": [{"a": "cassandra_network_exceptions", "u": 5, "t": 2}]}, "Cassandra_row_insert_batches": {"t": 3, "a": [{"a": "cassandra_row_insert_batches", "u": 5, "t": 2}]}, "Cassandra_row_inserts": {"t": 3, "a": [{"a": "cassandra_row_inserts", "u": 5, "t": 2}]}, "Cassandra_timeout_exceptions": {"t": 3, "a": [{"a": "cassandra_timeout_exceptions", "u": 5, "t": 2}]}, "Cassandra_unavailable_exceptions": {"t": 3, "a": [{"a": "cassandra_unavailable_exceptions", "u": 5, "t": 2}]}, "cassandra_default_thrift_host": {"d": true, "t": 1, "a": [{"a": "cassandra_default_thrift_host", "u": 6, "t": 2}]}, "cassandra_failure_retries": {"d": true, "t": 3, "a": [{"a": "cassandra_failure_retries", "u": 6, "t": 2}]}, "cassandra_insert_batch_size": {"d": true, "t": 3, "a": [{"a": "cassandra_insert_batch_size", "u": 6, "t": 2}]}, "cassandra_multiget_batch_size": {"d": true, "t": 3, "a": [{"a": "cassandra_multiget_batch_size", "u": 6, "t": 2}]}, "cassandra_read_consistency": {"a": [{"a": "cassandra_read_consistency", "u": 6, "t": 2}]}, "cassandra_rnd_batch_size": {"t": 3, "a": [{"a": "cassandra_rnd_batch_size", "u": 6, "t": 2}]}, "cassandra_write_consistency": {"a": [{"a": "cassandra_write_consistency", "u": 6, "t": 2}]}, "connect_class_path": {"d": false, "t": 1, "a": [{"a": "connect_class_path", "u": 7, "t": 2}]}, "connect_cond_push": {"d": true, "t": 2, "a": [{"a": "connect_cond_push", "u": 7, "t": 2}]}, "connect_conv_size": {"d": true, "t": 3, "a": [{"a": "connect_conv_size", "u": 7, "t": 2}]}, "connect_default_depth": {"d": true, "t": 3, "a": [{"a": "connect_default_depth", "u": 7, "t": 2}]}, "connect_default_prec": {"d": true, "t": 3, "a": [{"a": "connect_default_prec", "u": 7, "t": 2}]}, "connect_enable_mongo": {"d": false, "t": 2, "a": [{"a": "connect_enable_mongo", "u": 7, "t": 2}]}, "connect_exact_info": {"d": true, "t": 2, "a": [{"a": "connect_exact_info", "u": 7, "t": 2}]}, "connect_force_bson": {"d": true, "t": 2, "a": [{"a": "connect_force_bson", "u": 7, "t": 2}]}, "connect_indx_map": {"d": true, "t": 2, "a": [{"a": "connect_indx_map", "u": 7, "t": 2}]}, "connect_java_wrapper": {"d": true, "t": 1, "a": [{"a": "connect_java_wrapper", "u": 7, "t": 2}]}, "connect_json_all_path": {"d": true, "t": 2, "a": [{"a": "connect_json_all_path", "u": 7, "t": 2}]}, "connect_json_grp_size": {"d": true, "t": 3, "a": [{"a": "connect_json_grp_size", "u": 7, "t": 2}]}, "connect_json_null": {"d": true, "t": 1, "a": [{"a": "connect_json_null", "u": 7, "t": 2}]}, "connect_jvm_path": {"d": false, "t": 1, "a": [{"a": "connect_jvm_path", "u": 7, "t": 2}]}, "connect_type_conv": {"d": true, "t": 5, "a": [{"a": "connect_type_conv", "u": 7, "t": 2}]}, "connect_use_tempfile": {"d": true, "t": 5, "a": [{"a": "connect_use_tempfile", "u": 7, "t": 2}]}, "connect_work_size": {"d": true, "t": 3, "a": [{"a": "connect_work_size", "u": 7, "t": 2}]}, "connect_xtrace": {"d": true, "t": 6, "a": [{"a": "connect_xtrace", "u": 7, "t": 2}]}, "cracklib_password_check_dictionary": {"d": false, "t": 1, "a": [{"a": "cracklib_password_check_dictionary", "u": 8, "t": 2}]}, "cracklib_password_check": {"t": 5, "a": [{"a": "cracklib_password_check", "u": 8, "t": 2}]}, "disks": {"t": 5, "a": [{"a": "disks", "u": 9, "t": 2}]}, "feedback_http_proxy": {"t": 1, "a": [{"a": "feedback_http_proxy", "u": 10, "t": 2}]}, "feedback_send_retry_wait": {"d": true, "t": 3, "a": [{"a": "feedback_send_retry_wait", "u": 10, "t": 2}]}, "feedback_send_timeout": {"d": true, "t": 3, "a": [{"a": "feedback_send_timeout", "u": 10, "t": 2}]}, "feedback_server_uid": {"d": false, "t": 1, "a": [{"a": "feedback_server_uid", "u": 10, "t": 2}]}, "feedback_url": {"d": false, "t": 1, "a": [{"a": "feedback_url", "u": 10, "t": 2}]}, "feedback_user_info": {"d": false, "t": 1, "a": [{"a": "feedback_user_info", "u": 10, "t": 2}]}, "feedback": {"t": 5, "a": [{"a": "feedback", "u": 10, "t": 2}]}, "file_key_management_encryption_algorithm": {"d": false, "t": 5, "a": [{"a": "file_key_management_encryption_algorithm", "u": 11, "t": 2}]}, "file_key_management_filekey": {"d": false, "t": 1, "a": [{"a": "file_key_management_filekey", "u": 11, "t": 2}]}, "file_key_management_filename": {"d": false, "t": 1, "a": [{"a": "file_key_management_filename", "u": 11, "t": 2}]}, "file_key_management": {"t": 5, "a": [{"a": "file_key_management", "u": 11, "t": 2}]}, "wsrep_applier_thread_count": {"t": 3, "a": [{"a": "wsrep_applier_thread_count", "u": 12, "t": 2}]}, "wsrep_apply_oooe": {"a": [{"a": "wsrep_apply_oooe", "u": 12, "t": 2}]}, "wsrep_apply_oool": {"a": [{"a": "wsrep_apply_oool", "u": 12, "t": 2}]}, "wsrep_apply_window": {"a": [{"a": "wsrep_apply_window", "u": 12, "t": 2}]}, "wsrep_cert_deps_distance": {"a": [{"a": "wsrep_cert_deps_distance", "u": 12, "t": 2}]}, "wsrep_cert_index_size": {"t": 3, "a": [{"a": "wsrep_cert_index_size", "u": 12, "t": 2}]}, "wsrep_cert_interval": {"t": 3, "a": [{"a": "wsrep_cert_interval", "u": 12, "t": 2}]}, "wsrep_cluster_capabilities": {"a": [{"a": "wsrep_cluster_capabilities", "u": 12, "t": 2}]}, "wsrep_cluster_conf_id": {"t": 3, "a": [{"a": "wsrep_cluster_conf_id", "u": 12, "t": 2}]}, "wsrep_cluster_size": {"t": 3, "a": [{"a": "wsrep_cluster_size", "u": 12, "t": 2}]}, "wsrep_cluster_state_uuid": {"a": [{"a": "wsrep_cluster_state_uuid", "u": 12, "t": 2}]}, "wsrep_cluster_status": {"a": [{"a": "wsrep_cluster_status", "u": 12, "t": 2}]}, "wsrep_cluster_weight": {"a": [{"a": "wsrep_cluster_weight", "u": 12, "t": 2}]}, "wsrep_commit_oooe": {"a": [{"a": "wsrep_commit_oooe", "u": 12, "t": 2}]}, "wsrep_commit_oool": {"a": [{"a": "wsrep_commit_oool", "u": 12, "t": 2}]}, "wsrep_commit_window": {"a": [{"a": "wsrep_commit_window", "u": 12, "t": 2}]}, "wsrep_connected": {"a": [{"a": "wsrep_connected", "u": 12, "t": 2}]}, "wsrep_desync_count": {"t": 3, "a": [{"a": "wsrep_desync_count", "u": 12, "t": 2}]}, "wsrep_evs_delayed": {"a": [{"a": "wsrep_evs_delayed", "u": 12, "t": 2}]}, "wsrep_evs_evict_list": {"a": [{"a": "wsrep_evs_evict_list", "u": 12, "t": 2}]}, "wsrep_evs_repl_latency": {"t": 3, "a": [{"a": "wsrep_evs_repl_latency", "u": 12, "t": 2}]}, "wsrep_evs_state": {"a": [{"a": "wsrep_evs_state", "u": 12, "t": 2}]}, "wsrep_flow_control_paused": {"a": [{"a": "wsrep_flow_control_paused", "u": 12, "t": 2}]}, "wsrep_flow_control_paused_ns": {"a": [{"a": "wsrep_flow_control_paused_ns", "u": 12, "t": 2}]}, "wsrep_flow_control_recv": {"t": 3, "a": [{"a": "wsrep_flow_control_recv", "u": 12, "t": 2}]}, "wsrep_flow_control_sent": {"t": 3, "a": [{"a": "wsrep_flow_control_sent", "u": 12, "t": 2}]}, "wsrep_gcomm_uuid": {"a": [{"a": "wsrep_gcomm_uuid", "u": 12, "t": 2}]}, "wsrep_incoming_addresses": {"a": [{"a": "wsrep_incoming_addresses", "u": 12, "t": 2}]}, "wsrep_last_committed": {"t": 3, "a": [{"a": "wsrep_last_committed", "u": 12, "t": 2}]}, "wsrep_local_bf_aborts": {"t": 3, "a": [{"a": "wsrep_local_bf_aborts", "u": 12, "t": 2}]}, "wsrep_local_cached_downto": {"a": [{"a": "wsrep_local_cached_downto", "u": 12, "t": 2}]}, "wsrep_local_cert_failures": {"t": 3, "a": [{"a": "wsrep_local_cert_failures", "u": 12, "t": 2}]}, "wsrep_local_commits": {"t": 3, "a": [{"a": "wsrep_local_commits", "u": 12, "t": 2}]}, "wsrep_local_index": {"a": [{"a": "wsrep_local_index", "u": 12, "t": 2}]}, "wsrep_local_recv_queue": {"t": 3, "a": [{"a": "wsrep_local_recv_queue", "u": 12, "t": 2}]}, "wsrep_local_recv_queue_avg": {"a": [{"a": "wsrep_local_recv_queue_avg", "u": 12, "t": 2}]}, "wsrep_local_recv_queue_max": {"a": [{"a": "wsrep_local_recv_queue_max", "u": 12, "t": 2}]}, "wsrep_local_recv_queue_min": {"a": [{"a": "wsrep_local_recv_queue_min", "u": 12, "t": 2}]}, "wsrep_local_replays": {"t": 3, "a": [{"a": "wsrep_local_replays", "u": 12, "t": 2}]}, "wsrep_local_send_queue": {"t": 3, "a": [{"a": "wsrep_local_send_queue", "u": 12, "t": 2}]}, "wsrep_local_send_queue_avg": {"a": [{"a": "wsrep_local_send_queue_avg", "u": 12, "t": 2}]}, "wsrep_local_send_queue_max": {"a": [{"a": "wsrep_local_send_queue_max", "u": 12, "t": 2}]}, "wsrep_local_send_queue_min": {"a": [{"a": "wsrep_local_send_queue_min", "u": 12, "t": 2}]}, "wsrep_local_state": {"a": [{"a": "wsrep_local_state", "u": 12, "t": 2}]}, "wsrep_local_state_comment": {"a": [{"a": "wsrep_local_state_comment", "u": 12, "t": 2}]}, "wsrep_local_state_uuid": {"a": [{"a": "wsrep_local_state_uuid", "u": 12, "t": 2}]}, "wsrep_open_connections": {"t": 3, "a": [{"a": "wsrep_open_connections", "u": 12, "t": 2}]}, "wsrep_open_transactions": {"t": 3, "a": [{"a": "wsrep_open_transactions", "u": 12, "t": 2}]}, "wsrep_protocol_version": {"a": [{"a": "wsrep_protocol_version", "u": 12, "t": 2}]}, "wsrep_provider_name": {"a": [{"a": "wsrep_provider_name", "u": 12, "t": 2}]}, "wsrep_provider_vendor": {"a": [{"a": "wsrep_provider_vendor", "u": 12, "t": 2}]}, "wsrep_provider_version": {"t": 3, "a": [{"a": "wsrep_provider_version", "u": 12, "t": 2}]}, "wsrep_ready": {"a": [{"a": "wsrep_ready", "u": 12, "t": 2}]}, "wsrep_received": {"t": 3, "a": [{"a": "wsrep_received", "u": 12, "t": 2}]}, "wsrep_received_bytes": {"t": 9, "a": [{"a": "wsrep_received_bytes", "u": 12, "t": 2}]}, "wsrep_repl_data_bytes": {"t": 3, "a": [{"a": "wsrep_repl_data_bytes", "u": 12, "t": 2}]}, "wsrep_repl_keys": {"t": 3, "a": [{"a": "wsrep_repl_keys", "u": 12, "t": 2}]}, "wsrep_repl_keys_bytes": {"t": 3, "a": [{"a": "wsrep_repl_keys_bytes", "u": 12, "t": 2}]}, "wsrep_repl_other_bytes": {"t": 3, "a": [{"a": "wsrep_repl_other_bytes", "u": 12, "t": 2}]}, "wsrep_replicated": {"t": 3, "a": [{"a": "wsrep_replicated", "u": 12, "t": 2}]}, "wsrep_replicated_bytes": {"t": 9, "a": [{"a": "wsrep_replicated_bytes", "u": 12, "t": 2}]}, "wsrep_rollbacker_thread_count": {"t": 3, "a": [{"a": "wsrep_rollbacker_thread_count", "u": 12, "t": 2}]}, "wsrep_thread_count": {"t": 3, "a": [{"a": "wsrep_thread_count", "u": 12, "t": 2}]}, "wsrep_allowlist": {"d": false, "t": 1, "a": [{"a": "wsrep_allowlist", "u": 13, "t": 2}]}, "wsrep_auto_increment_control": {"d": true, "t": 2, "a": [{"a": "wsrep_auto_increment_control", "u": 13, "t": 2}]}, "wsrep_causal_reads": {"d": true, "t": 2, "a": [{"a": "wsrep_causal_reads", "u": 13, "t": 2}]}, "wsrep_certification_rules": {"d": true, "t": 5, "a": [{"a": "wsrep_certification_rules", "u": 13, "t": 2}]}, "wsrep_certify_nonPK": {"d": true, "t": 2, "a": [{"a": "wsrep_certify_nonpk", "u": 13, "t": 2}]}, "wsrep_cluster_address": {"d": false, "t": 1, "a": [{"a": "wsrep_cluster_address", "u": 13, "t": 2}]}, "wsrep_cluster_name": {"d": true, "t": 1, "a": [{"a": "wsrep_cluster_name", "u": 13, "t": 2}]}, "wsrep_convert_LOCK_to_trx": {"d": true, "t": 2, "a": [{"a": "wsrep_convert_lock_to_trx", "u": 13, "t": 2}]}, "wsrep_data_home_dir": {"d": false, "t": 1, "a": [{"a": "wsrep_data_home_dir", "u": 13, "t": 2}]}, "wsrep_dbug_option": {"d": true, "t": 1, "a": [{"a": "wsrep_dbug_option", "u": 13, "t": 2}]}, "wsrep_debug": {"d": true, "t": 5, "a": [{"a": "wsrep_debug", "u": 13, "t": 2}]}, "wsrep_desync": {"d": true, "t": 2, "a": [{"a": "wsrep_desync", "u": 13, "t": 2}]}, "wsrep_dirty_reads": {"d": true, "t": 2, "a": [{"a": "wsrep_dirty_reads", "u": 13, "t": 2}]}, "wsrep_drupal_282555_workaround": {"d": true, "t": 2, "a": [{"a": "wsrep_drupal_282555_workaround", "u": 13, "t": 2}]}, "wsrep_forced_binlog_format": {"d": true, "t": 5, "a": [{"a": "wsrep_forced_binlog_format", "u": 13, "t": 2}]}, "wsrep_gtid_domain_id": {"d": true, "t": 3, "a": [{"a": "wsrep_gtid_domain_id", "u": 13, "t": 2}]}, "wsrep_gtid_mode": {"d": true, "t": 2, "a": [{"a": "wsrep_gtid_mode", "u": 13, "t": 2}]}, "wsrep_gtid_seq_no": {"d": true, "t": 3, "a": [{"a": "wsrep_gtid_seq_no", "u": 13, "t": 2}]}, "wsrep_ignore_apply_errors": {"d": true, "t": 3, "a": [{"a": "wsrep_ignore_apply_errors", "u": 13, "t": 2}]}, "wsrep_load_data_splitting": {"d": true, "t": 2, "a": [{"a": "wsrep_load_data_splitting", "u": 13, "t": 2}]}, "wsrep_log_conflicts": {"d": true, "t": 2, "a": [{"a": "wsrep_log_conflicts", "u": 13, "t": 2}]}, "wsrep_max_ws_rows": {"d": true, "t": 3, "a": [{"a": "wsrep_max_ws_rows", "u": 13, "t": 2}]}, "wsrep_max_ws_size": {"d": true, "t": 3, "a": [{"a": "wsrep_max_ws_size", "u": 13, "t": 2}]}, "wsrep_mode": {"d": true, "t": 5, "a": [{"a": "wsrep_mode", "u": 13, "t": 2}]}, "wsrep_mysql_replication_bundle": {"d": false, "t": 3, "a": [{"a": "wsrep_mysql_replication_bundle", "u": 13, "t": 2}]}, "wsrep_node_address": {"d": false, "t": 1, "a": [{"a": "wsrep_node_address", "u": 13, "t": 2}]}, "wsrep_node_incoming_address": {"d": false, "t": 1, "a": [{"a": "wsrep_node_incoming_address", "u": 13, "t": 2}]}, "wsrep_node_name": {"d": true, "t": 1, "a": [{"a": "wsrep_node_name", "u": 13, "t": 2}]}, "wsrep_notify_cmd": {"d": false, "t": 1, "a": [{"a": "wsrep_notify_cmd", "u": 13, "t": 2}]}, "wsrep_on": {"d": true, "t": 2, "a": [{"a": "wsrep_on", "u": 13, "t": 2}]}, "wsrep_OSU_method": {"d": true, "t": 5, "a": [{"a": "wsrep_osu_method", "u": 13, "t": 2}]}, "wsrep_patch_version": {"d": false, "t": 1, "a": [{"a": "wsrep_patch_version", "u": 13, "t": 2}]}, "wsrep_provider": {"t": 1, "a": [{"a": "wsrep_provider", "u": 13, "t": 2}]}, "wsrep_provider_options": {"d": false, "t": 1, "a": [{"a": "wsrep_provider_options", "u": 13, "t": 2}]}, "wsrep_recover": {"d": false, "t": 2, "a": [{"a": "wsrep_recover", "u": 13, "t": 2}]}, "wsrep_reject_queries": {"d": true, "t": 5, "a": [{"a": "wsrep_reject_queries", "u": 13, "t": 2}]}, "wsrep_replicate_myisam": {"d": true, "t": 2, "a": [{"a": "wsrep_replicate_myisam", "u": 13, "t": 2}]}, "wsrep_restart_slave": {"d": true, "t": 2, "a": [{"a": "wsrep_restart_slave", "u": 13, "t": 2}]}, "wsrep_retry_autocommit": {"d": false, "t": 3, "a": [{"a": "wsrep_retry_autocommit", "u": 13, "t": 2}]}, "wsrep_slave_FK_checks": {"d": true, "t": 2, "a": [{"a": "wsrep_slave_fk_checks", "u": 13, "t": 2}]}, "wsrep_slave_threads": {"d": true, "t": 3, "a": [{"a": "wsrep_slave_threads", "u": 13, "t": 2}]}, "wsrep_slave_UK_checks": {"d": true, "t": 2, "a": [{"a": "wsrep_slave_uk_checks", "u": 13, "t": 2}]}, "wsrep_sr_store": {"d": false, "t": 5, "a": [{"a": "wsrep_sr_store", "u": 13, "t": 2}]}, "wsrep_sst_auth": {"d": true, "t": 1, "a": [{"a": "wsrep_sst_auth", "u": 13, "t": 2}]}, "wsrep_sst_donor": {"d": true, "t": 1, "a": [{"a": "wsrep_sst_donor", "u": 13, "t": 2}]}, "wsrep_sst_donor_rejects_queries": {"d": true, "t": 2, "a": [{"a": "wsrep_sst_donor_rejects_queries", "u": 13, "t": 2}]}, "wsrep_sst_method": {"d": true, "t": 1, "a": [{"a": "wsrep_sst_method", "u": 13, "t": 2}]}, "wsrep_sst_receive_address": {"d": true, "t": 1, "a": [{"a": "wsrep_sst_receive_address", "u": 13, "t": 2}]}, "wsrep_start_position": {"d": true, "t": 1, "a": [{"a": "wsrep_start_position", "u": 13, "t": 2}]}, "wsrep_status_file": {"d": false, "t": 1, "a": [{"a": "wsrep_status_file", "u": 13, "t": 2}]}, "wsrep_strict_ddl": {"d": true, "t": 2, "a": [{"a": "wsrep_strict_ddl", "u": 13, "t": 2}]}, "wsrep_sync_wait": {"d": true, "t": 3, "a": [{"a": "wsrep_sync_wait", "u": 13, "t": 2}]}, "wsrep_trx_fragment_size": {"d": true, "t": 3, "a": [{"a": "wsrep_trx_fragment_size", "u": 13, "t": 2}]}, "wsrep_trx_fragment_unit": {"d": true, "t": 5, "a": [{"a": "wsrep_trx_fragment_unit", "u": 13, "t": 2}]}, "gtid_slave_pos": {"d": true, "t": 1, "a": [{"a": "gtid_slave_pos", "u": 14, "t": 2}]}, "gtid_binlog_pos": {"d": false, "t": 1, "a": [{"a": "gtid_binlog_pos", "u": 14, "t": 2}]}, "gtid_binlog_state": {"d": true, "t": 1, "a": [{"a": "gtid_binlog_state", "u": 14, "t": 2}]}, "gtid_current_pos": {"d": false, "t": 1, "a": [{"a": "gtid_current_pos", "u": 14, "t": 2}]}, "gtid_strict_mode": {"d": true, "t": 2, "a": [{"a": "gtid_strict_mode", "u": 14, "t": 2}]}, "gtid_domain_id": {"d": true, "t": 3, "a": [{"a": "gtid_domain_id", "u": 14, "t": 2}]}, "last_gtid": {"d": false, "t": 1, "a": [{"a": "last_gtid", "u": 14, "t": 2}]}, "server_id": {"d": true, "t": 3, "a": [{"a": "server_id", "u": 14, "t": 2}, {"a": "server_id", "u": 15, "t": 2}, {"a": "sysvar_server_id", "u": 16, "t": 1}]}, "gtid_seq_no": {"d": true, "t": 3, "a": [{"a": "gtid_seq_no", "u": 14, "t": 2}]}, "gtid_ignore_duplicates": {"d": true, "t": 2, "a": [{"a": "gtid_ignore_duplicates", "u": 14, "t": 2}]}, "gtid_pos_auto_engines": {"d": true, "t": 1, "a": [{"a": "gtid_pos_auto_engines", "u": 14, "t": 2}]}, "gtid_cleanup_batch_size": {"d": true, "t": 3, "a": [{"a": "gtid_cleanup_batch_size", "u": 14, "t": 2}]}, "handlersocket_accept_balance": {"d": false, "t": 3, "a": [{"a": "handlersocket_accept_balance", "u": 17, "t": 2}]}, "handlersocket_address": {"d": false, "t": 1, "a": [{"a": "handlersocket_address", "u": 17, "t": 2}]}, "handlersocket_backlog": {"d": false, "t": 3, "a": [{"a": "handlersocket_backlog", "u": 17, "t": 2}]}, "handlersocket_epoll": {"d": false, "t": 3, "a": [{"a": "handlersocket_epoll", "u": 17, "t": 2}]}, "handlersocket_plain_secret": {"d": false, "t": 1, "a": [{"a": "handlersocket_plain_secret", "u": 17, "t": 2}]}, "handlersocket_plain_secret_wr": {"d": false, "t": 1, "a": [{"a": "handlersocket_plain_secret_wr", "u": 17, "t": 2}]}, "handlersocket_port": {"d": false, "t": 3, "a": [{"a": "handlersocket_port", "u": 17, "t": 2}]}, "handlersocket_port_wr": {"d": false, "t": 3, "a": [{"a": "handlersocket_port_wr", "u": 17, "t": 2}]}, "handlersocket_rcvbuf": {"d": false, "t": 3, "a": [{"a": "handlersocket_rcvbuf", "u": 17, "t": 2}]}, "handlersocket_readsize": {"d": false, "t": 3, "a": [{"a": "handlersocket_readsize", "u": 17, "t": 2}]}, "handlersocket_sndbuf": {"d": false, "t": 3, "a": [{"a": "handlersocket_sndbuf", "u": 17, "t": 2}]}, "handlersocket_threads": {"d": false, "t": 3, "a": [{"a": "handlersocket_threads", "u": 17, "t": 2}]}, "handlersocket_threads_wr": {"d": false, "t": 3, "a": [{"a": "handlersocket_threads_wr", "u": 17, "t": 2}]}, "handlersocket_timeout": {"d": false, "t": 3, "a": [{"a": "handlersocket_timeout", "u": 17, "t": 2}]}, "handlersocket_verbose": {"d": false, "t": 3, "a": [{"a": "handlersocket_verbose", "u": 17, "t": 2}]}, "handlersocket_wrlock_timeout": {"d": false, "t": 3, "a": [{"a": "handlersocket_wrlock_timeout", "u": 17, "t": 2}]}, "server_audit_events": {"d": true, "t": 1, "a": [{"a": "server_audit_events", "u": 18, "t": 2}, {"a": "server_audit_events", "u": 19, "t": 2}]}, "server_audit_excl_users": {"d": true, "t": 1, "a": [{"a": "server_audit_excl_users", "u": 18, "t": 2}, {"a": "server_audit_excl_users", "u": 19, "t": 2}]}, "server_audit_file_path": {"d": true, "t": 1, "a": [{"a": "server_audit_file_path", "u": 18, "t": 2}, {"a": "server_audit_file_path", "u": 19, "t": 2}]}, "server_audit_file_rotate_now": {"d": true, "t": 2, "a": [{"a": "server_audit_file_rotate_now", "u": 18, "t": 2}, {"a": "server_audit_file_rotate_now", "u": 19, "t": 2}]}, "server_audit_file_rotate_size": {"d": true, "t": 3, "a": [{"a": "server_audit_file_rotate_size", "u": 18, "t": 2}, {"a": "server_audit_file_rotate_size", "u": 19, "t": 2}]}, "server_audit_file_rotations": {"d": true, "t": 3, "a": [{"a": "server_audit_file_rotations", "u": 18, "t": 2}, {"a": "server_audit_file_rotations", "u": 19, "t": 2}]}, "server_audit_incl_users": {"d": true, "t": 1, "a": [{"a": "server_audit_incl_users", "u": 18, "t": 2}, {"a": "server_audit_incl_users", "u": 19, "t": 2}]}, "server_audit_loc_info": {"d": false, "t": 1, "a": [{"a": "server_audit_loc_info", "u": 18, "t": 2}, {"a": "server_audit_loc_info", "u": 19, "t": 2}]}, "server_audit_logging": {"d": true, "t": 2, "a": [{"a": "server_audit_logging", "u": 18, "t": 2}, {"a": "server_audit_logging", "u": 19, "t": 2}]}, "server_audit_mode": {"d": true, "t": 3, "a": [{"a": "server_audit_mode", "u": 18, "t": 2}, {"a": "server_audit_mode", "u": 19, "t": 2}]}, "server_audit_output_type": {"d": true, "t": 5, "a": [{"a": "server_audit_output_type", "u": 18, "t": 2}, {"a": "server_audit_output_type", "u": 19, "t": 2}]}, "server_audit_query_log_limit": {"d": true, "t": 3, "a": [{"a": "server_audit_query_log_limit", "u": 18, "t": 2}, {"a": "server_audit_query_log_limit", "u": 19, "t": 2}]}, "server_audit_syslog_facility": {"d": true, "t": 5, "a": [{"a": "server_audit_syslog_facility", "u": 18, "t": 2}, {"a": "server_audit_syslog_facility", "u": 19, "t": 2}]}, "server_audit_syslog_ident": {"d": true, "t": 1, "a": [{"a": "server_audit_syslog_ident", "u": 18, "t": 2}, {"a": "server_audit_syslog_ident", "u": 19, "t": 2}]}, "server_audit_syslog_info": {"d": true, "t": 1, "a": [{"a": "server_audit_syslog_info", "u": 18, "t": 2}, {"a": "server_audit_syslog_info", "u": 19, "t": 2}]}, "server_audit_syslog_priority": {"d": true, "t": 5, "a": [{"a": "server_audit_syslog_priority", "u": 18, "t": 2}, {"a": "server_audit_syslog_priority", "u": 19, "t": 2}]}, "server_audit": {"t": 5, "a": [{"a": "server_audit", "u": 18, "t": 2}, {"a": "server_audit", "u": 19, "t": 2}]}, "Server_audit_active": {"t": 2, "a": [{"a": "server_audit_active", "u": 20, "t": 2}]}, "Server_audit_current_log": {"t": 1, "a": [{"a": "server_audit_current_log", "u": 20, "t": 2}]}, "Server_audit_last_error": {"t": 1, "a": [{"a": "server_audit_last_error", "u": 20, "t": 2}]}, "Server_audit_writes_failed": {"t": 3, "a": [{"a": "server_audit_writes_failed", "u": 20, "t": 2}]}, "Mroonga_count_skip": {"t": 3, "a": [{"a": "m<PERSON>ga_count_skip", "u": 21, "t": 2}]}, "Mroonga_fast_order_limit": {"t": 3, "a": [{"a": "mroonga_fast_order_limit", "u": 21, "t": 2}]}, "mroonga_action_on_fulltext_query_error": {"d": true, "t": 5, "a": [{"a": "m<PERSON>ga_action_on_fulltext_query_error", "u": 22, "t": 2}]}, "mroonga_boolean_mode_syntax_flags": {"d": true, "t": 5, "a": [{"a": "mroonga_boolean_mode_syntax_flags", "u": 22, "t": 2}]}, "mroonga_database_path_prefix": {"d": true, "t": 1, "a": [{"a": "mroonga_database_path_prefix", "u": 22, "t": 2}]}, "mroonga_default_parser": {"d": true, "t": 1, "a": [{"a": "mroonga_default_parser", "u": 22, "t": 2}]}, "mroonga_default_tokenizer": {"d": true, "t": 1, "a": [{"a": "mroonga_default_tokenizer", "u": 22, "t": 2}]}, "mroonga_default_wrapper_engine": {"d": false, "t": 1, "a": [{"a": "mroonga_default_wrapper_engine", "u": 22, "t": 2}]}, "mroonga_dry_write": {"d": true, "t": 2, "a": [{"a": "m<PERSON><PERSON>_dry_write", "u": 22, "t": 2}]}, "mroonga_enable_operations_recording": {"d": true, "t": 2, "a": [{"a": "mroonga_enable_operations_recording", "u": 22, "t": 2}]}, "mroonga_enable_optimization": {"d": true, "t": 2, "a": [{"a": "mroonga_enable_optimization", "u": 22, "t": 2}]}, "mroonga_libgroonga_embedded": {"d": false, "t": 2, "a": [{"a": "mroonga_libgroonga_embedded", "u": 22, "t": 2}]}, "mroonga_libgroonga_support_lz4": {"d": false, "t": 2, "a": [{"a": "mroonga_libgroonga_support_lz4", "u": 22, "t": 2}]}, "mroonga_libgroonga_support_zlib": {"d": false, "t": 2, "a": [{"a": "mroonga_libgroonga_support_zlib", "u": 22, "t": 2}]}, "mroonga_libgroonga_support_zstd": {"d": false, "t": 2, "a": [{"a": "mroonga_libgroonga_support_zstd", "u": 22, "t": 2}]}, "mroonga_libgroonga_version": {"d": false, "t": 1, "a": [{"a": "mroonga_libgroonga_version", "u": 22, "t": 2}]}, "mroonga_lock_timeout": {"d": true, "t": 3, "a": [{"a": "m<PERSON>ga_lock_timeout", "u": 22, "t": 2}]}, "mroonga_log_file": {"d": true, "t": 1, "a": [{"a": "mroonga_log_file", "u": 22, "t": 2}]}, "mroonga_log_level": {"d": true, "t": 5, "a": [{"a": "mroonga_log_level", "u": 22, "t": 2}]}, "mroonga_match_escalation_threshold": {"d": true, "t": 3, "a": [{"a": "mroonga_match_escalation_threshold", "u": 22, "t": 2}]}, "mroonga_max_n_records_for_estimate": {"d": true, "t": 3, "a": [{"a": "mroonga_max_n_records_for_estimate", "u": 22, "t": 2}]}, "mroonga_query_log_file": {"d": true, "t": 1, "a": [{"a": "mroonga_query_log_file", "u": 22, "t": 2}]}, "mroonga_vector_column_delimiter": {"d": true, "t": 1, "a": [{"a": "mroonga_vector_column_delimiter", "u": 22, "t": 2}]}, "mroonga_version": {"d": false, "t": 1, "a": [{"a": "mroonga_version", "u": 22, "t": 2}]}, "key_buffer_size": {"d": true, "t": 3, "a": [{"a": "key_buffer_size", "u": 23, "t": 2}, {"a": "sysvar_key_buffer_size", "u": 24, "t": 1}]}, "key_cache_age_threshold": {"d": true, "t": 3, "a": [{"a": "key_cache_age_threshold", "u": 23, "t": 2}, {"a": "sysvar_key_cache_age_threshold", "u": 24, "t": 1}]}, "key_cache_block_size": {"d": true, "t": 3, "a": [{"a": "key_cache_block_size", "u": 23, "t": 2}, {"a": "sysvar_key_cache_block_size", "u": 24, "t": 1}]}, "key_cache_division_limit": {"d": true, "t": 3, "a": [{"a": "key_cache_division_limit", "u": 23, "t": 2}, {"a": "sysvar_key_cache_division_limit", "u": 24, "t": 1}]}, "key_cache_file_hash_size": {"d": true, "t": 3, "a": [{"a": "key_cache_file_hash_size", "u": 23, "t": 2}]}, "key_cache_segments": {"d": true, "t": 3, "a": [{"a": "key_cache_segments", "u": 23, "t": 2}]}, "myisam_block_size": {"t": 3, "a": [{"a": "myisam_block_size", "u": 23, "t": 2}, {"a": "option_mysqld_myisam-block-size", "u": 25, "t": 1}]}, "myisam_data_pointer_size": {"d": true, "t": 3, "a": [{"a": "myisam_data_pointer_size", "u": 23, "t": 2}, {"a": "sysvar_myisam_data_pointer_size", "u": 24, "t": 1}]}, "myisam_max_extra_sort_file_size": {"a": [{"a": "myisam_max_extra_sort_file_size", "u": 23, "t": 2}]}, "myisam_max_sort_file_size": {"d": true, "t": 3, "a": [{"a": "myisam_max_sort_file_size", "u": 23, "t": 2}, {"a": "sysvar_myisam_max_sort_file_size", "u": 24, "t": 1}]}, "myisam_mmap_size": {"t": 3, "a": [{"a": "myisam_mmap_size", "u": 23, "t": 2}, {"a": "sysvar_myisam_mmap_size", "u": 24, "t": 1}]}, "myisam_recover_options": {"d": false, "t": 5, "a": [{"a": "myisam_recover_options", "u": 23, "t": 2}, {"a": "sysvar_myisam_recover_options", "u": 24, "t": 1}]}, "myisam_repair_threads": {"d": true, "t": 3, "a": [{"a": "myisam_repair_threads", "u": 23, "t": 2}, {"a": "sysvar_myisam_repair_threads", "u": 24, "t": 1}]}, "myisam_sort_buffer_size": {"d": true, "t": 3, "a": [{"a": "myisam_sort_buffer_size", "u": 23, "t": 2}, {"a": "sysvar_myisam_sort_buffer_size", "u": 24, "t": 1}]}, "myisam_stats_method": {"d": true, "t": 5, "a": [{"a": "myisam_stats_method", "u": 23, "t": 2}, {"a": "sysvar_myisam_stats_method", "u": 24, "t": 1}]}, "myisam_use_mmap": {"d": true, "t": 2, "a": [{"a": "myisam_use_mmap", "u": 23, "t": 2}, {"a": "sysvar_myisam_use_mmap", "u": 24, "t": 1}]}, "Rocksdb_block_cache_add": {"t": 3, "a": [{"a": "rocksdb_block_cache_add", "u": 26, "t": 2}]}, "Rocksdb_block_cache_add_failures": {"t": 3, "a": [{"a": "rocksdb_block_cache_add_failures", "u": 26, "t": 2}]}, "Rocksdb_block_cache_bytes_read": {"t": 3, "a": [{"a": "rocksdb_block_cache_bytes_read", "u": 26, "t": 2}]}, "Rocksdb_block_cache_bytes_write": {"t": 3, "a": [{"a": "rocksdb_block_cache_bytes_write", "u": 26, "t": 2}]}, "Rocksdb_block_cache_data_add": {"t": 3, "a": [{"a": "rocksdb_block_cache_data_add", "u": 26, "t": 2}]}, "Rocksdb_block_cache_data_bytes_insert": {"t": 3, "a": [{"a": "rocksdb_block_cache_data_bytes_insert", "u": 26, "t": 2}]}, "Rocksdb_block_cache_data_hit": {"t": 3, "a": [{"a": "rocksdb_block_cache_data_hit", "u": 26, "t": 2}]}, "Rocksdb_block_cache_data_miss": {"t": 3, "a": [{"a": "rocksdb_block_cache_data_miss", "u": 26, "t": 2}]}, "Rocksdb_block_cache_filter_add": {"t": 3, "a": [{"a": "rocksdb_block_cache_filter_add", "u": 26, "t": 2}]}, "Rocksdb_block_cache_filter_bytes_evict": {"t": 3, "a": [{"a": "rocksdb_block_cache_filter_bytes_evict", "u": 26, "t": 2}]}, "Rocksdb_block_cache_filter_bytes_insert": {"t": 3, "a": [{"a": "rocksdb_block_cache_filter_bytes_insert", "u": 26, "t": 2}]}, "Rocksdb_block_cache_filter_hit": {"t": 3, "a": [{"a": "rocksdb_block_cache_filter_hit", "u": 26, "t": 2}]}, "Rocksdb_block_cache_filter_miss": {"t": 3, "a": [{"a": "rocksdb_block_cache_filter_miss", "u": 26, "t": 2}]}, "Rocksdb_block_cache_hit": {"t": 3, "a": [{"a": "rocksdb_block_cache_hit", "u": 26, "t": 2}]}, "Rocksdb_block_cache_index_add": {"t": 3, "a": [{"a": "rocksdb_block_cache_index_add", "u": 26, "t": 2}]}, "Rocksdb_block_cache_index_bytes_evict": {"t": 3, "a": [{"a": "rocksdb_block_cache_index_bytes_evict", "u": 26, "t": 2}]}, "Rocksdb_block_cache_index_bytes_insert": {"t": 3, "a": [{"a": "rocksdb_block_cache_index_bytes_insert", "u": 26, "t": 2}]}, "Rocksdb_block_cache_index_hit": {"t": 3, "a": [{"a": "rocksdb_block_cache_index_hit", "u": 26, "t": 2}]}, "Rocksdb_block_cache_index_miss": {"t": 3, "a": [{"a": "rocksdb_block_cache_index_miss", "u": 26, "t": 2}]}, "Rocksdb_block_cache_miss": {"t": 3, "a": [{"a": "rocksdb_block_cache_miss", "u": 26, "t": 2}]}, "Rocksdb_block_cachecompressed_hit": {"t": 3, "a": [{"a": "rocksdb_block_cachecompressed_hit", "u": 26, "t": 2}]}, "Rocksdb_block_cachecompressed_miss": {"t": 3, "a": [{"a": "rocksdb_block_cachecompressed_miss", "u": 26, "t": 2}]}, "Rocksdb_bloom_filter_full_positive": {"t": 3, "a": [{"a": "rocksdb_bloom_filter_full_positive", "u": 26, "t": 2}]}, "Rocksdb_bloom_filter_full_true_positive": {"t": 3, "a": [{"a": "rocksdb_bloom_filter_full_true_positive", "u": 26, "t": 2}]}, "Rocksdb_bloom_filter_prefix_checked": {"t": 3, "a": [{"a": "rocksdb_bloom_filter_prefix_checked", "u": 26, "t": 2}]}, "Rocksdb_bloom_filter_prefix_useful": {"t": 3, "a": [{"a": "rocksdb_bloom_filter_prefix_useful", "u": 26, "t": 2}]}, "Rocksdb_bloom_filter_useful": {"t": 3, "a": [{"a": "rocksdb_bloom_filter_useful", "u": 26, "t": 2}]}, "Rocksdb_bytes_read": {"t": 3, "a": [{"a": "rocksdb_bytes_read", "u": 26, "t": 2}]}, "Rocksdb_bytes_written": {"t": 3, "a": [{"a": "rocksdb_bytes_written", "u": 26, "t": 2}]}, "Rocksdb_compact_read_bytes": {"t": 3, "a": [{"a": "rocksdb_compact_read_bytes", "u": 26, "t": 2}]}, "Rocksdb_compact_write_bytes": {"t": 3, "a": [{"a": "rocksdb_compact_write_bytes", "u": 26, "t": 2}]}, "Rocksdb_compaction_key_drop_new": {"t": 3, "a": [{"a": "rocksdb_compaction_key_drop_new", "u": 26, "t": 2}]}, "Rocksdb_compaction_key_drop_obsolete": {"t": 3, "a": [{"a": "rocksdb_compaction_key_drop_obsolete", "u": 26, "t": 2}]}, "Rocksdb_compaction_key_drop_user": {"t": 3, "a": [{"a": "rocksdb_compaction_key_drop_user", "u": 26, "t": 2}]}, "Rocksdb_covered_secondary_key_lookups": {"t": 3, "a": [{"a": "rocksdb_covered_secondary_key_lookups", "u": 26, "t": 2}]}, "Rocksdb_flush_write_bytes": {"t": 3, "a": [{"a": "rocksdb_flush_write_bytes", "u": 26, "t": 2}]}, "Rocksdb_get_hit_l0": {"t": 3, "a": [{"a": "rocksdb_get_hit_l0", "u": 26, "t": 2}]}, "Rocksdb_get_hit_l1": {"t": 3, "a": [{"a": "rocksdb_get_hit_l1", "u": 26, "t": 2}]}, "Rocksdb_get_hit_l2_and_up": {"t": 3, "a": [{"a": "rocksdb_get_hit_l2_and_up", "u": 26, "t": 2}]}, "Rocksdb_getupdatessince_calls": {"t": 3, "a": [{"a": "rocksdb_getupdatessince_calls", "u": 26, "t": 2}]}, "Rocksdb_iter_bytes_read": {"t": 3, "a": [{"a": "rocksdb_iter_bytes_read", "u": 26, "t": 2}]}, "Rocksdb_l0_num_files_stall_micros": {"t": 3, "a": [{"a": "rocksdb_l0_num_files_stall_micros", "u": 26, "t": 2}]}, "Rocksdb_l0_slowdown_micros": {"t": 3, "a": [{"a": "rocksdb_l0_slowdown_micros", "u": 26, "t": 2}]}, "Rocksdb_manual_compactions_processed": {"t": 3, "a": [{"a": "rocksdb_manual_compactions_processed", "u": 26, "t": 2}]}, "Rocksdb_manual_compactions_running": {"t": 3, "a": [{"a": "rocksdb_manual_compactions_running", "u": 26, "t": 2}]}, "Rocksdb_memtable_compaction_micros": {"t": 3, "a": [{"a": "rocksdb_memtable_compaction_micros", "u": 26, "t": 2}]}, "Rocksdb_memtable_hit": {"t": 3, "a": [{"a": "rocksdb_memtable_hit", "u": 26, "t": 2}]}, "Rocksdb_memtable_miss": {"t": 3, "a": [{"a": "rocksdb_memtable_miss", "u": 26, "t": 2}]}, "Rocksdb_memtable_total": {"t": 3, "a": [{"a": "rocksdb_memtable_total", "u": 26, "t": 2}]}, "Rocksdb_memtable_unflushed": {"t": 3, "a": [{"a": "rocksdb_memtable_unflushed", "u": 26, "t": 2}]}, "Rocksdb_no_file_closes": {"t": 3, "a": [{"a": "rocksdb_no_file_closes", "u": 26, "t": 2}]}, "Rocksdb_no_file_errors": {"t": 3, "a": [{"a": "rocksdb_no_file_errors", "u": 26, "t": 2}]}, "Rocksdb_no_file_opens": {"t": 3, "a": [{"a": "rocksdb_no_file_opens", "u": 26, "t": 2}]}, "Rocksdb_num_iterators": {"t": 3, "a": [{"a": "rocksdb_num_iterators", "u": 26, "t": 2}]}, "Rocksdb_number_block_not_compressed": {"t": 3, "a": [{"a": "rocksdb_number_block_not_compressed", "u": 26, "t": 2}]}, "Rocksdb_number_db_next": {"t": 3, "a": [{"a": "rocksdb_number_db_next", "u": 26, "t": 2}]}, "Rocksdb_number_db_next_found": {"t": 3, "a": [{"a": "rocksdb_number_db_next_found", "u": 26, "t": 2}]}, "Rocksdb_number_db_prev": {"t": 3, "a": [{"a": "rocksdb_number_db_prev", "u": 26, "t": 2}]}, "Rocksdb_number_db_prev_found": {"t": 3, "a": [{"a": "rocksdb_number_db_prev_found", "u": 26, "t": 2}]}, "Rocksdb_number_db_seek": {"t": 3, "a": [{"a": "rocksdb_number_db_seek", "u": 26, "t": 2}]}, "Rocksdb_number_db_seek_found": {"t": 3, "a": [{"a": "rocksdb_number_db_seek_found", "u": 26, "t": 2}]}, "Rocksdb_number_deletes_filtered": {"t": 3, "a": [{"a": "rocksdb_number_deletes_filtered", "u": 26, "t": 2}]}, "Rocksdb_number_keys_read": {"t": 3, "a": [{"a": "rocksdb_number_keys_read", "u": 26, "t": 2}]}, "Rocksdb_number_keys_updated": {"t": 3, "a": [{"a": "rocksdb_number_keys_updated", "u": 26, "t": 2}]}, "Rocksdb_number_keys_written": {"t": 3, "a": [{"a": "rocksdb_number_keys_written", "u": 26, "t": 2}]}, "Rocksdb_number_merge_failures": {"t": 3, "a": [{"a": "rocksdb_number_merge_failures", "u": 26, "t": 2}]}, "Rocksdb_number_multiget_bytes_read": {"t": 3, "a": [{"a": "rocksdb_number_multiget_bytes_read", "u": 26, "t": 2}]}, "Rocksdb_number_multiget_get": {"t": 3, "a": [{"a": "rocksdb_number_multiget_get", "u": 26, "t": 2}]}, "Rocksdb_number_multiget_keys_read": {"t": 3, "a": [{"a": "rocksdb_number_multiget_keys_read", "u": 26, "t": 2}]}, "Rocksdb_number_reseeks_iteration": {"t": 3, "a": [{"a": "rocksdb_number_reseeks_iteration", "u": 26, "t": 2}]}, "Rocksdb_number_sst_entry_delete": {"t": 3, "a": [{"a": "rocksdb_number_sst_entry_delete", "u": 26, "t": 2}]}, "Rocksdb_number_sst_entry_merge": {"t": 3, "a": [{"a": "rocksdb_number_sst_entry_merge", "u": 26, "t": 2}]}, "Rocksdb_number_sst_entry_other": {"t": 3, "a": [{"a": "rocksdb_number_sst_entry_other", "u": 26, "t": 2}]}, "Rocksdb_number_sst_entry_put": {"t": 3, "a": [{"a": "rocksdb_number_sst_entry_put", "u": 26, "t": 2}]}, "Rocksdb_number_sst_entry_singledelete": {"t": 3, "a": [{"a": "rocksdb_number_sst_entry_singledelete", "u": 26, "t": 2}]}, "Rocksdb_number_superversion_acquires": {"t": 3, "a": [{"a": "rocksdb_number_superversion_acquires", "u": 26, "t": 2}]}, "Rocksdb_number_superversion_cleanups": {"t": 3, "a": [{"a": "rocksdb_number_superversion_cleanups", "u": 26, "t": 2}]}, "Rocksdb_number_superversion_releases": {"t": 3, "a": [{"a": "rocksdb_number_superversion_releases", "u": 26, "t": 2}]}, "Rocksdb_queries_point": {"t": 3, "a": [{"a": "rocksdb_queries_point", "u": 26, "t": 2}]}, "Rocksdb_queries_range": {"t": 3, "a": [{"a": "rocksdb_queries_range", "u": 26, "t": 2}]}, "Rocksdb_row_lock_deadlocks": {"t": 3, "a": [{"a": "rocksdb_row_lock_deadlocks", "u": 26, "t": 2}]}, "Rocksdb_row_lock_wait_timeouts": {"t": 3, "a": [{"a": "rocksdb_row_lock_wait_timeouts", "u": 26, "t": 2}]}, "Rocksdb_rows_deleted": {"t": 3, "a": [{"a": "rocksdb_rows_deleted", "u": 26, "t": 2}]}, "Rocksdb_rows_deleted_blind": {"t": 3, "a": [{"a": "rocksdb_rows_deleted_blind", "u": 26, "t": 2}]}, "Rocksdb_rows_expired": {"t": 3, "a": [{"a": "rocksdb_rows_expired", "u": 26, "t": 2}]}, "Rocksdb_rows_filtered": {"t": 3, "a": [{"a": "rocksdb_rows_filtered", "u": 26, "t": 2}]}, "Rocksdb_rows_inserted": {"t": 3, "a": [{"a": "rocksdb_rows_inserted", "u": 26, "t": 2}]}, "Rocksdb_rows_read": {"t": 3, "a": [{"a": "rocksdb_rows_read", "u": 26, "t": 2}]}, "Rocksdb_rows_updated": {"t": 3, "a": [{"a": "rocksdb_rows_updated", "u": 26, "t": 2}]}, "Rocksdb_snapshot_conflict_errors": {"t": 3, "a": [{"a": "rocksdb_snapshot_conflict_errors", "u": 26, "t": 2}]}, "Rocksdb_stall_l0_file_count_limit_slowdowns": {"t": 3, "a": [{"a": "rocksdb_stall_l0_file_count_limit_slowdowns", "u": 26, "t": 2}]}, "Rocksdb_stall_l0_file_count_limit_stops": {"t": 3, "a": [{"a": "rocksdb_stall_l0_file_count_limit_stops", "u": 26, "t": 2}]}, "Rocksdb_stall_locked_l0_file_count_limit_slowdowns": {"t": 3, "a": [{"a": "rocksdb_stall_locked_l0_file_count_limit_slowdowns", "u": 26, "t": 2}]}, "Rocksdb_stall_locked_l0_file_count_limit_stops": {"t": 3, "a": [{"a": "rocksdb_stall_locked_l0_file_count_limit_stops", "u": 26, "t": 2}]}, "Rocksdb_stall_memtable_limit_slowdowns": {"t": 3, "a": [{"a": "rocksdb_stall_memtable_limit_slowdowns", "u": 26, "t": 2}]}, "Rocksdb_stall_memtable_limit_stops": {"t": 3, "a": [{"a": "rocksdb_stall_memtable_limit_stops", "u": 26, "t": 2}]}, "Rocksdb_stall_micros": {"t": 3, "a": [{"a": "rocksdb_stall_micros", "u": 26, "t": 2}]}, "Rocksdb_stall_pending_compaction_limit_slowdowns": {"t": 3, "a": [{"a": "rocksdb_stall_pending_compaction_limit_slowdowns", "u": 26, "t": 2}]}, "Rocksdb_stall_pending_compaction_limit_stops": {"t": 3, "a": [{"a": "rocksdb_stall_pending_compaction_limit_stops", "u": 26, "t": 2}]}, "Rocksdb_stall_total_slowdowns": {"t": 3, "a": [{"a": "rocksdb_stall_total_slowdowns", "u": 26, "t": 2}]}, "Rocksdb_stall_total_stops": {"t": 3, "a": [{"a": "rocksdb_stall_total_stops", "u": 26, "t": 2}]}, "Rocksdb_system_rows_deleted": {"t": 3, "a": [{"a": "rocksdb_system_rows_deleted", "u": 26, "t": 2}]}, "Rocksdb_system_rows_inserted": {"t": 3, "a": [{"a": "rocksdb_system_rows_inserted", "u": 26, "t": 2}]}, "Rocksdb_system_rows_read": {"t": 3, "a": [{"a": "rocksdb_system_rows_read", "u": 26, "t": 2}]}, "Rocksdb_system_rows_updated": {"t": 3, "a": [{"a": "rocksdb_system_rows_updated", "u": 26, "t": 2}]}, "Rocksdb_wal_bytes": {"t": 3, "a": [{"a": "rocksdb_wal_bytes", "u": 26, "t": 2}]}, "Rocksdb_wal_group_syncs": {"t": 3, "a": [{"a": "rocksdb_wal_group_syncs", "u": 26, "t": 2}]}, "Rocksdb_wal_synced": {"t": 3, "a": [{"a": "rocksdb_wal_synced", "u": 26, "t": 2}]}, "Rocksdb_write_other": {"t": 3, "a": [{"a": "rocksdb_write_other", "u": 26, "t": 2}]}, "Rocksdb_write_self": {"t": 3, "a": [{"a": "rocksdb_write_self", "u": 26, "t": 2}]}, "Rocksdb_write_timedout": {"t": 3, "a": [{"a": "rocksdb_write_timedout", "u": 26, "t": 2}]}, "Rocksdb_write_wal": {"t": 3, "a": [{"a": "rocksdb_write_wal", "u": 26, "t": 2}]}, "rocksdb_access_hint_on_compaction_start": {"d": false, "t": 3, "a": [{"a": "rocksdb_access_hint_on_compaction_start", "u": 27, "t": 2}]}, "rocksdb_advise_random_on_open": {"d": false, "t": 2, "a": [{"a": "rocksdb_advise_random_on_open", "u": 27, "t": 2}]}, "rocksdb_allow_concurrent_memtable_write": {"d": false, "t": 2, "a": [{"a": "rocksdb_allow_concurrent_memtable_write", "u": 27, "t": 2}]}, "rocksdb_allow_mmap_reads": {"d": false, "t": 2, "a": [{"a": "rocksdb_allow_mmap_reads", "u": 27, "t": 2}]}, "rocksdb_allow_mmap_writes": {"d": false, "t": 2, "a": [{"a": "rocksdb_allow_mmap_writes", "u": 27, "t": 2}]}, "rocksdb_allow_to_start_after_corruption": {"d": false, "t": 2, "a": [{"a": "rocksdb_allow_to_start_after_corruption", "u": 27, "t": 2}]}, "rocksdb_background_sync": {"d": false, "t": 2, "a": [{"a": "rocksdb_background_sync", "u": 27, "t": 2}]}, "rocksdb_base_background_compactions": {"d": false, "t": 3, "a": [{"a": "rocksdb_base_background_compactions", "u": 27, "t": 2}]}, "rocksdb_blind_delete_primary_key": {"d": true, "t": 2, "a": [{"a": "rocksdb_blind_delete_primary_key", "u": 27, "t": 2}]}, "rocksdb_block_cache_size": {"d": true, "t": 3, "a": [{"a": "rocksdb_block_cache_size", "u": 27, "t": 2}]}, "rocksdb_block_restart_interval": {"d": false, "t": 3, "a": [{"a": "rocksdb_block_restart_interval", "u": 27, "t": 2}]}, "rocksdb_block_size": {"d": false, "t": 3, "a": [{"a": "rocksdb_block_size", "u": 27, "t": 2}]}, "rocksdb_block_size_deviation": {"d": false, "t": 3, "a": [{"a": "rocksdb_block_size_deviation", "u": 27, "t": 2}]}, "rocksdb_bulk_load": {"d": true, "t": 2, "a": [{"a": "rocksdb_bulk_load", "u": 27, "t": 2}]}, "rocksdb_bulk_load_allow_sk": {"d": true, "t": 2, "a": [{"a": "rocksdb_bulk_load_allow_sk", "u": 27, "t": 2}]}, "rocksdb_bulk_load_allow_unsorted": {"d": true, "t": 2, "a": [{"a": "rocksdb_bulk_load_allow_unsorted", "u": 27, "t": 2}]}, "rocksdb_bulk_load_size": {"d": true, "t": 3, "a": [{"a": "rocksdb_bulk_load_size", "u": 27, "t": 2}]}, "rocksdb_bytes_per_sync": {"d": true, "t": 3, "a": [{"a": "rocksdb_bytes_per_sync", "u": 27, "t": 2}]}, "rocksdb_cache_dump": {"d": true, "t": 2, "a": [{"a": "rocksdb_cache_dump", "u": 27, "t": 2}]}, "rocksdb_cache_high_pri_pool_ratio": {"d": true, "t": 3, "a": [{"a": "rocksdb_cache_high_pri_pool_ratio", "u": 27, "t": 2}]}, "rocksdb_cache_index_and_filter_blocks": {"d": false, "t": 2, "a": [{"a": "rocksdb_cache_index_and_filter_blocks", "u": 27, "t": 2}]}, "rocksdb_cache_index_and_filter_with_high_priority": {"d": false, "t": 2, "a": [{"a": "rocksdb_cache_index_and_filter_with_high_priority", "u": 27, "t": 2}]}, "rocksdb_checksums_pct": {"d": true, "t": 3, "a": [{"a": "rocksdb_checksums_pct", "u": 27, "t": 2}]}, "rocksdb_collect_sst_properties": {"d": false, "t": 2, "a": [{"a": "rocksdb_collect_sst_properties", "u": 27, "t": 2}]}, "rocksdb_commit_in_the_middle": {"d": true, "t": 2, "a": [{"a": "rocksdb_commit_in_the_middle", "u": 27, "t": 2}]}, "rocksdb_commit_time_batch_for_recovery": {"d": true, "t": 2, "a": [{"a": "rocksdb_commit_time_batch_for_recovery", "u": 27, "t": 2}]}, "rocksdb_compact_cf": {"d": true, "t": 1, "a": [{"a": "rocksdb_compact_cf", "u": 27, "t": 2}]}, "rocksdb_compaction_readahead_size": {"d": true, "t": 3, "a": [{"a": "rocksdb_compaction_readahead_size", "u": 27, "t": 2}]}, "rocksdb_compaction_sequential_deletes": {"d": true, "t": 3, "a": [{"a": "rocksdb_compaction_sequential_deletes", "u": 27, "t": 2}]}, "rocksdb_compaction_sequential_deletes_count_sd": {"d": true, "t": 2, "a": [{"a": "rocksdb_compaction_sequential_deletes_count_sd", "u": 27, "t": 2}]}, "rocksdb_compaction_sequential_deletes_file_size": {"d": true, "t": 3, "a": [{"a": "rocksdb_compaction_sequential_deletes_file_size", "u": 27, "t": 2}]}, "rocksdb_compaction_sequential_deletes_window": {"d": true, "t": 3, "a": [{"a": "rocksdb_compaction_sequential_deletes_window", "u": 27, "t": 2}]}, "rocksdb_concurrent_prepare": {"d": false, "t": 2, "a": [{"a": "rocksdb_concurrent_prepare", "u": 27, "t": 2}]}, "rocksdb_create_checkpoint": {"d": true, "t": 1, "a": [{"a": "rocksdb_create_checkpoint", "u": 27, "t": 2}]}, "rocksdb_create_if_missing": {"d": false, "t": 2, "a": [{"a": "rocksdb_create_if_missing", "u": 27, "t": 2}]}, "rocksdb_create_missing_column_families": {"d": false, "t": 2, "a": [{"a": "rocksdb_create_missing_column_families", "u": 27, "t": 2}]}, "rocksdb_datadir": {"d": false, "t": 1, "a": [{"a": "rocksdb_datadir", "u": 27, "t": 2}]}, "rocksdb_db_write_buffer_size": {"d": false, "t": 3, "a": [{"a": "rocksdb_db_write_buffer_size", "u": 27, "t": 2}]}, "rocksdb_deadlock_detect": {"d": true, "t": 2, "a": [{"a": "rocksdb_deadlock_detect", "u": 27, "t": 2}]}, "rocksdb_deadlock_detect_depth": {"d": true, "t": 3, "a": [{"a": "rocksdb_deadlock_detect_depth", "u": 27, "t": 2}]}, "rocksdb_debug_manual_compaction_delay": {"d": true, "t": 3, "a": [{"a": "rocksdb_debug_manual_compaction_delay", "u": 27, "t": 2}]}, "rocksdb_debug_optimizer_no_zero_cardinality": {"d": true, "t": 2, "a": [{"a": "rocksdb_debug_optimizer_no_zero_cardinality", "u": 27, "t": 2}]}, "rocksdb_debug_ttl_ignore_pk": {"d": true, "t": 2, "a": [{"a": "rocksdb_debug_ttl_ignore_pk", "u": 27, "t": 2}]}, "rocksdb_debug_ttl_read_filter_ts": {"d": true, "t": 3, "a": [{"a": "rocksdb_debug_ttl_read_filter_ts", "u": 27, "t": 2}]}, "rocksdb_debug_ttl_rec_ts": {"d": true, "t": 3, "a": [{"a": "rocksdb_debug_ttl_rec_ts", "u": 27, "t": 2}]}, "rocksdb_debug_ttl_snapshot_ts": {"d": true, "t": 3, "a": [{"a": "rocksdb_debug_ttl_snapshot_ts", "u": 27, "t": 2}]}, "rocksdb_default_cf_options": {"d": false, "t": 1, "a": [{"a": "rocksdb_default_cf_options", "u": 27, "t": 2}]}, "rocksdb_delayed_write_rate": {"d": true, "t": 3, "a": [{"a": "rocksdb_delayed_write_rate", "u": 27, "t": 2}]}, "rocksdb_delete_cf": {"d": false, "t": 1, "a": [{"a": "rocksdb_delete_cf", "u": 27, "t": 2}]}, "rocksdb_delete_obsolete_files_period_micros": {"d": false, "t": 3, "a": [{"a": "rocksdb_delete_obsolete_files_period_micros", "u": 27, "t": 2}]}, "rocksdb_enable_2pc": {"d": true, "t": 2, "a": [{"a": "rocksdb_enable_2pc", "u": 27, "t": 2}]}, "rocksdb_enable_bulk_load_api": {"d": false, "t": 2, "a": [{"a": "rocksdb_enable_bulk_load_api", "u": 27, "t": 2}]}, "rocksdb_enable_insert_with_update_caching": {"d": true, "t": 2, "a": [{"a": "rocksdb_enable_insert_with_update_caching", "u": 27, "t": 2}]}, "rocksdb_enable_thread_tracking": {"d": false, "t": 2, "a": [{"a": "rocksdb_enable_thread_tracking", "u": 27, "t": 2}]}, "rocksdb_enable_ttl": {"d": true, "t": 2, "a": [{"a": "rocksdb_enable_ttl", "u": 27, "t": 2}]}, "rocksdb_enable_ttl_read_filtering": {"d": true, "t": 2, "a": [{"a": "rocksdb_enable_ttl_read_filtering", "u": 27, "t": 2}]}, "rocksdb_enable_write_thread_adaptive_yield": {"d": false, "t": 2, "a": [{"a": "rocksdb_enable_write_thread_adaptive_yield", "u": 27, "t": 2}]}, "rocksdb_error_if_exists": {"d": false, "t": 2, "a": [{"a": "rocksdb_error_if_exists", "u": 27, "t": 2}]}, "rocksdb_error_on_suboptimal_collation": {"d": false, "t": 2, "a": [{"a": "rocksdb_error_on_suboptimal_collation", "u": 27, "t": 2}]}, "rocksdb_flush_log_at_trx_commit": {"d": true, "t": 3, "a": [{"a": "rocksdb_flush_log_at_trx_commit", "u": 27, "t": 2}]}, "rocksdb_flush_memtable_on_analyze": {"d": true, "t": 2, "a": [{"a": "rocksdb_flush_memtable_on_analyze", "u": 27, "t": 2}]}, "rocksdb_force_compute_memtable_stats": {"d": true, "t": 2, "a": [{"a": "rocksdb_force_compute_memtable_stats", "u": 27, "t": 2}]}, "rocksdb_force_compute_memtable_stats_cachetime": {"d": true, "t": 3, "a": [{"a": "rocksdb_force_compute_memtable_stats_cachetime", "u": 27, "t": 2}]}, "rocksdb_force_flush_memtable_and_lzero_now": {"d": true, "t": 2, "a": [{"a": "rocksdb_force_flush_memtable_and_lzero_now", "u": 27, "t": 2}]}, "rocksdb_force_flush_memtable_now": {"d": true, "t": 2, "a": [{"a": "rocksdb_force_flush_memtable_now", "u": 27, "t": 2}]}, "rocksdb_force_index_records_in_range": {"d": true, "t": 3, "a": [{"a": "rocksdb_force_index_records_in_range", "u": 27, "t": 2}]}, "rocksdb_git_hash": {"d": false, "t": 1, "a": [{"a": "rocksdb_git_hash", "u": 27, "t": 2}]}, "rocksdb_hash_index_allow_collision": {"d": false, "t": 2, "a": [{"a": "rocksdb_hash_index_allow_collision", "u": 27, "t": 2}]}, "rocksdb_ignore_unknown_options": {"d": false, "t": 2, "a": [{"a": "rocksdb_ignore_unknown_options", "u": 27, "t": 2}]}, "rocksdb_index_type": {"d": false, "t": 5, "a": [{"a": "rocksdb_index_type", "u": 27, "t": 2}]}, "rocksdb_info_log_level": {"d": true, "t": 5, "a": [{"a": "rocksdb_info_log_level", "u": 27, "t": 2}]}, "rocksdb_io_write_timeout": {"d": true, "t": 3, "a": [{"a": "rocksdb_io_write_timeout", "u": 27, "t": 2}]}, "rocksdb_is_fd_close_on_exec": {"d": false, "t": 2, "a": [{"a": "rocksdb_is_fd_close_on_exec", "u": 27, "t": 2}]}, "rocksdb_keep_log_file_num": {"d": false, "t": 3, "a": [{"a": "rocksdb_keep_log_file_num", "u": 27, "t": 2}]}, "rocksdb_large_prefix": {"d": true, "t": 2, "a": [{"a": "rocksdb_large_prefix", "u": 27, "t": 2}]}, "rocksdb_lock_scanned_rows": {"d": true, "t": 2, "a": [{"a": "rocksdb_lock_scanned_rows", "u": 27, "t": 2}]}, "rocksdb_lock_wait_timeout": {"d": true, "t": 3, "a": [{"a": "rocksdb_lock_wait_timeout", "u": 27, "t": 2}]}, "rocksdb_log_dir": {"d": false, "t": 1, "a": [{"a": "rocksdb_log_dir", "u": 27, "t": 2}]}, "rocksdb_log_file_time_to_roll": {"d": false, "t": 3, "a": [{"a": "rocksdb_log_file_time_to_roll", "u": 27, "t": 2}]}, "rocksdb_manifest_preallocation_size": {"d": false, "t": 3, "a": [{"a": "rocksdb_manifest_preallocation_size", "u": 27, "t": 2}]}, "rocksdb_manual_compaction_threads": {"d": true, "t": 3, "a": [{"a": "rocksdb_manual_compaction_threads", "u": 27, "t": 2}]}, "rocksdb_manual_wal_flush": {"d": false, "t": 2, "a": [{"a": "rocksdb_manual_wal_flush", "u": 27, "t": 2}]}, "rocksdb_master_skip_tx_api": {"d": true, "t": 2, "a": [{"a": "rocksdb_master_skip_tx_api", "u": 27, "t": 2}]}, "rocksdb_max_background_compactions": {"d": true, "t": 3, "a": [{"a": "rocksdb_max_background_compactions", "u": 27, "t": 2}]}, "rocksdb_max_background_flushes": {"d": false, "t": 3, "a": [{"a": "rocksdb_max_background_flushes", "u": 27, "t": 2}]}, "rocksdb_max_background_jobs": {"d": true, "t": 3, "a": [{"a": "rocksdb_max_background_jobs", "u": 27, "t": 2}]}, "rocksdb_max_latest_deadlocks": {"d": true, "t": 3, "a": [{"a": "rocksdb_max_latest_deadlocks", "u": 27, "t": 2}]}, "rocksdb_max_log_file_size": {"d": false, "t": 3, "a": [{"a": "rocksdb_max_log_file_size", "u": 27, "t": 2}]}, "rocksdb_max_manifest_file_size": {"d": false, "t": 3, "a": [{"a": "rocksdb_max_manifest_file_size", "u": 27, "t": 2}]}, "rocksdb_max_manual_compactions": {"d": true, "t": 3, "a": [{"a": "rocksdb_max_manual_compactions", "u": 27, "t": 2}]}, "rocksdb_max_open_files": {"d": false, "t": 3, "a": [{"a": "rocksdb_max_open_files", "u": 27, "t": 2}]}, "rocksdb_max_row_locks": {"d": true, "t": 3, "a": [{"a": "rocksdb_max_row_locks", "u": 27, "t": 2}]}, "rocksdb_max_subcompactions": {"d": false, "t": 3, "a": [{"a": "rocksdb_max_subcompactions", "u": 27, "t": 2}]}, "rocksdb_max_total_wal_size": {"d": false, "t": 3, "a": [{"a": "rocksdb_max_total_wal_size", "u": 27, "t": 2}]}, "rocksdb_merge_buf_size": {"d": true, "t": 3, "a": [{"a": "rocksdb_merge_buf_size", "u": 27, "t": 2}]}, "rocksdb_merge_combine_read_size": {"d": true, "t": 3, "a": [{"a": "rocksdb_merge_combine_read_size", "u": 27, "t": 2}]}, "rocksdb_merge_tmp_file_removal_delay_ms": {"d": true, "t": 3, "a": [{"a": "rocksdb_merge_tmp_file_removal_delay_ms", "u": 27, "t": 2}]}, "rocksdb_new_table_reader_for_compaction_inputs": {"d": false, "t": 2, "a": [{"a": "rocksdb_new_table_reader_for_compaction_inputs", "u": 27, "t": 2}]}, "rocksdb_no_block_cache": {"d": false, "t": 2, "a": [{"a": "rocksdb_no_block_cache", "u": 27, "t": 2}]}, "rocksdb_override_cf_options": {"d": false, "t": 1, "a": [{"a": "rocksdb_override_cf_options", "u": 27, "t": 2}]}, "rocksdb_paranoid_checks": {"d": false, "t": 2, "a": [{"a": "rocksdb_paranoid_checks", "u": 27, "t": 2}]}, "rocksdb_pause_background_work": {"d": true, "t": 2, "a": [{"a": "rocksdb_pause_background_work", "u": 27, "t": 2}]}, "rocksdb_perf_context_level": {"d": true, "t": 3, "a": [{"a": "rocksdb_perf_context_level", "u": 27, "t": 2}]}, "rocksdb_persistent_cache_path": {"d": false, "t": 1, "a": [{"a": "rocksdb_persistent_cache_path", "u": 27, "t": 2}]}, "rocksdb_persistent_cache_size_mb": {"d": false, "t": 3, "a": [{"a": "rocksdb_persistent_cache_size_mb", "u": 27, "t": 2}]}, "rocksdb_pin_l0_filter_and_index_blocks_in_cache": {"d": false, "t": 2, "a": [{"a": "rocksdb_pin_l0_filter_and_index_blocks_in_cache", "u": 27, "t": 2}]}, "rocksdb_print_snapshot_conflict_queries": {"d": true, "t": 2, "a": [{"a": "rocksdb_print_snapshot_conflict_queries", "u": 27, "t": 2}]}, "rocksdb_rate_limiter_bytes_per_sec": {"d": true, "t": 3, "a": [{"a": "rocksdb_rate_limiter_bytes_per_sec", "u": 27, "t": 2}]}, "rocksdb_read_free_rpl_tables": {"d": true, "t": 1, "a": [{"a": "rocksdb_read_free_rpl_tables", "u": 27, "t": 2}]}, "rocksdb_records_in_range": {"d": true, "t": 3, "a": [{"a": "rocksdb_records_in_range", "u": 27, "t": 2}]}, "rocksdb_remove_mariabackup_checkpoint": {"d": true, "t": 2, "a": [{"a": "rocksdb_remove_mariabackup_checkpoint", "u": 27, "t": 2}]}, "rocksdb_reset_stats": {"d": true, "t": 2, "a": [{"a": "rocksdb_reset_stats", "u": 27, "t": 2}]}, "rocksdb_rollback_on_timeout": {"d": true, "t": 2, "a": [{"a": "rocksdb_rollback_on_timeout", "u": 27, "t": 2}]}, "rocksdb_seconds_between_stat_computes": {"d": true, "t": 3, "a": [{"a": "rocksdb_seconds_between_stat_computes", "u": 27, "t": 2}]}, "rocksdb_signal_drop_index_thread": {"d": true, "t": 2, "a": [{"a": "rocksdb_signal_drop_index_thread", "u": 27, "t": 2}]}, "rocksdb_sim_cache_size": {"d": false, "t": 3, "a": [{"a": "rocksdb_sim_cache_size", "u": 27, "t": 2}]}, "rocksdb_skip_bloom_filter_on_read": {"d": true, "t": 2, "a": [{"a": "rocksdb_skip_bloom_filter_on_read", "u": 27, "t": 2}]}, "rocksdb_skip_fill_cache": {"d": true, "t": 2, "a": [{"a": "rocksdb_skip_fill_cache", "u": 27, "t": 2}]}, "rocksdb_skip_unique_check_tables": {"d": true, "t": 1, "a": [{"a": "rocksdb_skip_unique_check_tables", "u": 27, "t": 2}]}, "rocksdb_sst_mgr_rate_bytes_per_sec": {"d": true, "t": 3, "a": [{"a": "rocksdb_sst_mgr_rate_bytes_per_sec", "u": 27, "t": 2}]}, "rocksdb_stats_dump_period_sec": {"d": false, "t": 3, "a": [{"a": "rocksdb_stats_dump_period_sec", "u": 27, "t": 2}]}, "rocksdb_stats_level": {"d": true, "t": 3, "a": [{"a": "rocksdb_stats_level", "u": 27, "t": 2}]}, "rocksdb_stats_recalc_rate": {"d": true, "t": 3, "a": [{"a": "rocksdb_stats_recalc_rate", "u": 27, "t": 2}]}, "rocksdb_store_row_debug_checksums": {"d": true, "t": 2, "a": [{"a": "rocksdb_store_row_debug_checksums", "u": 27, "t": 2}]}, "rocksdb_strict_collation_check": {"d": true, "t": 2, "a": [{"a": "rocksdb_strict_collation_check", "u": 27, "t": 2}]}, "rocksdb_strict_collation_exceptions": {"d": true, "t": 1, "a": [{"a": "rocksdb_strict_collation_exceptions", "u": 27, "t": 2}]}, "rocksdb_supported_compression_types": {"d": false, "t": 1, "a": [{"a": "rocksdb_supported_compression_types", "u": 27, "t": 2}]}, "rocksdb_table_cache_numshardbits": {"d": false, "t": 3, "a": [{"a": "rocksdb_table_cache_numshardbits", "u": 27, "t": 2}]}, "rocksdb_table_stats_sampling_pct": {"d": true, "t": 3, "a": [{"a": "rocksdb_table_stats_sampling_pct", "u": 27, "t": 2}]}, "rocksdb_tmpdir": {"d": true, "t": 1, "a": [{"a": "rocksdb_tmpdir", "u": 27, "t": 2}]}, "rocksdb_trace_sst_api": {"d": true, "t": 2, "a": [{"a": "rocksdb_trace_sst_api", "u": 27, "t": 2}]}, "rocksdb_two_write_queues": {"d": false, "t": 2, "a": [{"a": "rocksdb_two_write_queues", "u": 27, "t": 2}]}, "rocksdb_unsafe_for_binlog": {"d": true, "t": 2, "a": [{"a": "rocksdb_unsafe_for_binlog", "u": 27, "t": 2}]}, "rocksdb_update_cf_options": {"d": true, "t": 1, "a": [{"a": "rocksdb_update_cf_options", "u": 27, "t": 2}]}, "rocksdb_use_adaptive_mutex": {"d": false, "t": 2, "a": [{"a": "rocksdb_use_adaptive_mutex", "u": 27, "t": 2}]}, "rocksdb_use_clock_cache": {"d": false, "t": 2, "a": [{"a": "rocksdb_use_clock_cache", "u": 27, "t": 2}]}, "rocksdb_use_direct_io_for_flush_and_compaction": {"d": false, "t": 2, "a": [{"a": "rocksdb_use_direct_io_for_flush_and_compaction", "u": 27, "t": 2}]}, "rocksdb_use_direct_reads": {"d": false, "t": 2, "a": [{"a": "rocksdb_use_direct_reads", "u": 27, "t": 2}]}, "rocksdb_use_direct_writes": {"d": false, "t": 2, "a": [{"a": "rocksdb_use_direct_writes", "u": 27, "t": 2}]}, "rocksdb_use_fsync": {"d": false, "t": 2, "a": [{"a": "rocksdb_use_fsync", "u": 27, "t": 2}]}, "rocksdb_validate_tables": {"d": false, "t": 3, "a": [{"a": "rocksdb_validate_tables", "u": 27, "t": 2}]}, "rocksdb_verify_row_debug_checksums": {"d": true, "t": 2, "a": [{"a": "rocksdb_verify_row_debug_checksums", "u": 27, "t": 2}]}, "rocksdb_wal_bytes_per_sync": {"d": true, "t": 3, "a": [{"a": "rocksdb_wal_bytes_per_sync", "u": 27, "t": 2}]}, "rocksdb_wal_dir": {"d": false, "t": 1, "a": [{"a": "rocksdb_wal_dir", "u": 27, "t": 2}]}, "rocksdb_wal_recovery_mode": {"d": true, "t": 3, "a": [{"a": "rocksdb_wal_recovery_mode", "u": 27, "t": 2}]}, "rocksdb_wal_size_limit_mb": {"d": false, "t": 3, "a": [{"a": "rocksdb_wal_size_limit_mb", "u": 27, "t": 2}]}, "rocksdb_wal_ttl_seconds": {"d": false, "t": 3, "a": [{"a": "rocksdb_wal_ttl_seconds", "u": 27, "t": 2}]}, "rocksdb_whole_key_filtering": {"d": false, "t": 2, "a": [{"a": "rocksdb_whole_key_filtering", "u": 27, "t": 2}]}, "rocksdb_write_batch_max_bytes": {"d": true, "t": 3, "a": [{"a": "rocksdb_write_batch_max_bytes", "u": 27, "t": 2}]}, "rocksdb_write_disable_wal": {"d": true, "t": 2, "a": [{"a": "rocksdb_write_disable_wal", "u": 27, "t": 2}]}, "rocksdb_write_ignore_missing_column_families": {"d": true, "t": 2, "a": [{"a": "rocksdb_write_ignore_missing_column_families", "u": 27, "t": 2}]}, "rocksdb_write_policy": {"d": false, "t": 5, "a": [{"a": "rocksdb_write_policy", "u": 27, "t": 2}]}, "oqgraph_allow_create_integer_latch": {"d": true, "t": 2, "a": [{"a": "oqgraph_allow_create_integer_latch", "u": 28, "t": 2}]}, "Oqgraph_boost_version": {"t": 1, "a": [{"a": "oqgraph_boost_version", "u": 28, "t": 2}]}, "Oqgraph_compat_mode": {"t": 1, "a": [{"a": "oqgraph_compat_mode", "u": 28, "t": 2}]}, "Oqgraph_verbose_debug": {"t": 1, "a": [{"a": "oqgraph_verbose_debug", "u": 28, "t": 2}]}, "pbxt_auto_increment_mode": {"a": [{"a": "pbxt_auto_increment_mode", "u": 29, "t": 2}]}, "pbxt_checkpoint_frequency": {"a": [{"a": "pbxt_checkpoint_frequency", "u": 29, "t": 2}]}, "pbxt_data_file_grow_size": {"t": 3, "a": [{"a": "pbxt_data_file_grow_size", "u": 29, "t": 2}]}, "pbxt_data_log_threshold": {"t": 3, "a": [{"a": "pbxt_data_log_threshold", "u": 29, "t": 2}]}, "pbxt_flush_log_at_trx_commit": {"a": [{"a": "pbxt_flush_log_at_trx_commit", "u": 29, "t": 2}]}, "pbxt_garbage_threshold": {"a": [{"a": "pbxt_garbage_threshold", "u": 29, "t": 2}]}, "pbxt_index_cache_size": {"a": [{"a": "pbxt_index_cache_size", "u": 29, "t": 2}]}, "pbxt_log_buffer_size": {"t": 3, "a": [{"a": "pbxt_log_buffer_size", "u": 29, "t": 2}]}, "pbxt_log_cache_size": {"t": 3, "a": [{"a": "pbxt_log_cache_size", "u": 29, "t": 2}]}, "pbxt_log_file_count": {"t": 3, "a": [{"a": "pbxt_log_file_count", "u": 29, "t": 2}]}, "pbxt_log_file_threshold": {"t": 3, "a": [{"a": "pbxt_log_file_threshold", "u": 29, "t": 2}]}, "pbxt_offline_log_function": {"a": [{"a": "pbxt_offline_log_function", "u": 29, "t": 2}]}, "pbxt_record_cache_size": {"a": [{"a": "pbxt_record_cache_size", "u": 29, "t": 2}]}, "pbxt_row_file_grow_size": {"t": 3, "a": [{"a": "pbxt_row_file_grow_size", "u": 29, "t": 2}]}, "pbxt_sweeper_priority": {"a": [{"a": "pbxt_sweeper_priority", "u": 29, "t": 2}]}, "pbxt_support_xa": {"a": [{"a": "pbxt_support_xa", "u": 29, "t": 2}]}, "pbxt_transaction_buffer_size": {"t": 3, "a": [{"a": "pbxt_transaction_buffer_size", "u": 29, "t": 2}]}, "Performance_schema_accounts_lost": {"t": 3, "a": [{"a": "performance_schema_accounts_lost", "u": 30, "t": 2}]}, "Performance_schema_cond_classes_lost": {"t": 3, "a": [{"a": "performance_schema_cond_classes_lost", "u": 30, "t": 2}]}, "Performance_schema_cond_instances_lost": {"t": 3, "a": [{"a": "performance_schema_cond_instances_lost", "u": 30, "t": 2}]}, "Performance_schema_digest_lost": {"t": 3, "a": [{"a": "performance_schema_digest_lost", "u": 30, "t": 2}]}, "Performance_schema_file_classes_lost": {"t": 3, "a": [{"a": "performance_schema_file_classes_lost", "u": 30, "t": 2}]}, "Performance_schema_file_handles_lost": {"t": 3, "a": [{"a": "performance_schema_file_handles_lost", "u": 30, "t": 2}]}, "Performance_schema_file_instances_lost": {"t": 3, "a": [{"a": "performance_schema_file_instances_lost", "u": 30, "t": 2}]}, "Performance_schema_hosts_lost": {"t": 3, "a": [{"a": "performance_schema_hosts_lost", "u": 30, "t": 2}]}, "Performance_schema_index_stat_lost": {"t": 3, "a": [{"a": "performance_schema_index_stat_lost", "u": 30, "t": 2}]}, "Performance_schema_locker_lost": {"t": 3, "a": [{"a": "performance_schema_locker_lost", "u": 30, "t": 2}]}, "Performance_schema_memory_classes_lost": {"t": 3, "a": [{"a": "performance_schema_memory_classes_lost", "u": 30, "t": 2}]}, "Performance_schema_metadata_lock_lost": {"t": 3, "a": [{"a": "performance_schema_metadata_lock_lost", "u": 30, "t": 2}]}, "Performance_schema_mutex_classes_lost": {"t": 3, "a": [{"a": "performance_schema_mutex_classes_lost", "u": 30, "t": 2}]}, "Performance_schema_mutex_instances_lost": {"t": 3, "a": [{"a": "performance_schema_mutex_instances_lost", "u": 30, "t": 2}]}, "Performance_schema_nested_statement_lost": {"t": 3, "a": [{"a": "performance_schema_nested_statement_lost", "u": 30, "t": 2}]}, "Performance_schema_prepared_statements_lost": {"t": 3, "a": [{"a": "performance_schema_prepared_statements_lost", "u": 30, "t": 2}]}, "Performance_schema_program_lost": {"t": 3, "a": [{"a": "performance_schema_program_lost", "u": 30, "t": 2}]}, "Performance_schema_rwlock_classes_lost": {"t": 3, "a": [{"a": "performance_schema_rwlock_classes_lost", "u": 30, "t": 2}]}, "Performance_schema_rwlock_instances_lost": {"t": 3, "a": [{"a": "performance_schema_rwlock_instances_lost", "u": 30, "t": 2}]}, "Performance_schema_session_connect_attrs_lost": {"t": 3, "a": [{"a": "performance_schema_session_connect_attrs_lost", "u": 30, "t": 2}]}, "Performance_schema_socket_classes_lost": {"t": 3, "a": [{"a": "performance_schema_socket_classes_lost", "u": 30, "t": 2}]}, "Performance_schema_socket_instances_lost": {"t": 3, "a": [{"a": "performance_schema_socket_instances_lost", "u": 30, "t": 2}]}, "Performance_schema_stage_classes_lost": {"t": 3, "a": [{"a": "performance_schema_stage_classes_lost", "u": 30, "t": 2}]}, "Performance_schema_statement_classes_lost": {"t": 3, "a": [{"a": "performance_schema_statement_classes_lost", "u": 30, "t": 2}]}, "Performance_schema_table_handles_lost": {"t": 3, "a": [{"a": "performance_schema_table_handles_lost", "u": 30, "t": 2}]}, "Performance_schema_table_instances_lost": {"t": 3, "a": [{"a": "performance_schema_table_instances_lost", "u": 30, "t": 2}]}, "Performance_schema_table_lock_stat_lost": {"t": 3, "a": [{"a": "performance_schema_table_lock_stat_lost", "u": 30, "t": 2}]}, "Performance_schema_thread_classes_lost": {"t": 3, "a": [{"a": "performance_schema_thread_classes_lost", "u": 30, "t": 2}]}, "Performance_schema_thread_instances_lost": {"t": 3, "a": [{"a": "performance_schema_thread_instances_lost", "u": 30, "t": 2}]}, "Performance_schema_users_lost": {"t": 3, "a": [{"a": "performance_schema_users_lost", "u": 30, "t": 2}]}, "performance_schema": {"d": false, "t": 2, "a": [{"a": "performance_schema", "u": 31, "t": 2}, {"a": "sysvar_performance_schema", "u": 32, "t": 1}]}, "performance_schema_accounts_size": {"d": false, "t": 3, "a": [{"a": "performance_schema_accounts_size", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_accounts_size", "u": 32, "t": 1}]}, "performance_schema_digests_size": {"d": false, "t": 3, "a": [{"a": "performance_schema_digests_size", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_digests_size", "u": 32, "t": 1}]}, "performance_schema_events_stages_history_long_size": {"d": false, "t": 3, "a": [{"a": "performance_schema_events_stages_history_long_size", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_events_stages_history_long_size", "u": 32, "t": 1}]}, "performance_schema_events_stages_history_size": {"d": false, "t": 3, "a": [{"a": "performance_schema_events_stages_history_size", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_events_stages_history_size", "u": 32, "t": 1}]}, "performance_schema_events_statements_history_long_size": {"d": false, "t": 3, "a": [{"a": "performance_schema_events_statements_history_long_size", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_events_statements_history_long_size", "u": 32, "t": 1}]}, "performance_schema_events_statements_history_size": {"d": false, "t": 3, "a": [{"a": "performance_schema_events_statements_history_size", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_events_statements_history_size", "u": 32, "t": 1}]}, "performance_schema_events_transactions_history_long_size": {"d": false, "t": 3, "a": [{"a": "performance_schema_events_transactions_history_long_size", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_events_transactions_history_long_size", "u": 32, "t": 1}]}, "performance_schema_events_transactions_history_size": {"d": false, "t": 3, "a": [{"a": "performance_schema_events_transactions_history_size", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_events_transactions_history_size", "u": 32, "t": 1}]}, "performance_schema_events_waits_history_long_size": {"d": false, "t": 3, "a": [{"a": "performance_schema_events_waits_history_long_size", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_events_waits_history_long_size", "u": 32, "t": 1}]}, "performance_schema_events_waits_history_size": {"d": false, "t": 3, "a": [{"a": "performance_schema_events_waits_history_size", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_events_waits_history_size", "u": 32, "t": 1}]}, "performance_schema_hosts_size": {"d": false, "t": 3, "a": [{"a": "performance_schema_hosts_size", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_hosts_size", "u": 32, "t": 1}]}, "performance_schema_max_cond_classes": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_cond_classes", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_cond_classes", "u": 32, "t": 1}]}, "performance_schema_max_cond_instances": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_cond_instances", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_cond_instances", "u": 32, "t": 1}]}, "performance_schema_max_digest_length": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_digest_length", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_digest_length", "u": 32, "t": 1}]}, "performance_schema_max_file_classes": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_file_classes", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_file_classes", "u": 32, "t": 1}]}, "performance_schema_max_file_handles": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_file_handles", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_file_handles", "u": 32, "t": 1}]}, "performance_schema_max_file_instances": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_file_instances", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_file_instances", "u": 32, "t": 1}]}, "performance_schema_max_index_stat": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_index_stat", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_index_stat", "u": 32, "t": 1}]}, "performance_schema_max_memory_classes": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_memory_classes", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_memory_classes", "u": 32, "t": 1}]}, "performance_schema_max_metadata_locks": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_metadata_locks", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_metadata_locks", "u": 32, "t": 1}]}, "performance_schema_max_mutex_classes": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_mutex_classes", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_mutex_classes", "u": 32, "t": 1}]}, "performance_schema_max_mutex_instances": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_mutex_instances", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_mutex_instances", "u": 32, "t": 1}]}, "performance_schema_max_prepared_statement_instances": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_prepared_statement_instances", "u": 31, "t": 2}]}, "performance_schema_max_program_instances": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_program_instances", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_program_instances", "u": 32, "t": 1}]}, "performance_schema_max_rwlock_classes": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_rwlock_classes", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_rwlock_classes", "u": 32, "t": 1}]}, "performance_schema_max_rwlock_instances": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_rwlock_instances", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_rwlock_instances", "u": 32, "t": 1}]}, "performance_schema_max_socket_classes": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_socket_classes", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_socket_classes", "u": 32, "t": 1}]}, "performance_schema_max_socket_instances": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_socket_instances", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_socket_instances", "u": 32, "t": 1}]}, "performance_schema_max_sql_text_length": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_sql_text_length", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_sql_text_length", "u": 32, "t": 1}]}, "performance_schema_max_stage_classes": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_stage_classes", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_stage_classes", "u": 32, "t": 1}]}, "performance_schema_max_statement_classes": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_statement_classes", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_statement_classes", "u": 32, "t": 1}]}, "performance_schema_max_statement_stack": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_statement_stack", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_statement_stack", "u": 32, "t": 1}]}, "performance_schema_max_table_handles": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_table_handles", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_table_handles", "u": 32, "t": 1}]}, "performance_schema_max_table_instances": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_table_instances", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_table_instances", "u": 32, "t": 1}]}, "performance_schema_max_table_lock_stat": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_table_lock_stat", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_table_lock_stat", "u": 32, "t": 1}]}, "performance_schema_max_thread_classes": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_thread_classes", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_thread_classes", "u": 32, "t": 1}]}, "performance_schema_max_thread_instances": {"d": false, "t": 3, "a": [{"a": "performance_schema_max_thread_instances", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_max_thread_instances", "u": 32, "t": 1}]}, "performance_schema_session_connect_attrs_size": {"d": false, "t": 3, "a": [{"a": "performance_schema_session_connect_attrs_size", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_session_connect_attrs_size", "u": 32, "t": 1}]}, "performance_schema_setup_actors_size": {"d": false, "t": 3, "a": [{"a": "performance_schema_setup_actors_size", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_setup_actors_size", "u": 32, "t": 1}]}, "performance_schema_setup_objects_size": {"d": false, "t": 3, "a": [{"a": "performance_schema_setup_objects_size", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_setup_objects_size", "u": 32, "t": 1}]}, "performance_schema_users_size": {"d": false, "t": 3, "a": [{"a": "performance_schema_users_size", "u": 31, "t": 2}, {"a": "sysvar_performance_schema_users_size", "u": 32, "t": 1}]}, "query_cache_info": {"t": 5, "a": [{"a": "query_cache_info", "u": 33, "t": 2}]}, "query_response_time_flush": {"d": true, "t": 2, "a": [{"a": "query_response_time_flush", "u": 34, "t": 2}]}, "query_response_time_range_base": {"d": true, "t": 3, "a": [{"a": "query_response_time_range_base", "u": 34, "t": 2}]}, "query_response_time_exec_time_debug": {"d": true, "t": 3, "a": [{"a": "query_response_time_exec_time_debug", "u": 34, "t": 2}]}, "query_response_time_stats": {"d": true, "t": 2, "a": [{"a": "query_response_time_stats", "u": 34, "t": 2}]}, "query_response_time": {"t": 5, "a": [{"a": "query_response_time", "u": 34, "t": 2}]}, "query_response_time_audit": {"t": 5, "a": [{"a": "query_response_time_audit", "u": 34, "t": 2}]}, "auto_increment_increment": {"d": true, "t": 3, "a": [{"a": "auto_increment_increment", "u": 15, "t": 2}, {"a": "sysvar_auto_increment_increment", "u": 35, "t": 1}]}, "auto_increment_offset": {"d": true, "t": 3, "a": [{"a": "auto_increment_offset", "u": 15, "t": 2}, {"a": "sysvar_auto_increment_offset", "u": 35, "t": 1}]}, "binlog_alter_two_phase": {"d": true, "t": 2, "a": [{"a": "binlog_alter_two_phase", "u": 15, "t": 2}]}, "binlog_annotate_row_events": {"d": true, "t": 2, "a": [{"a": "binlog_annotate_row_events", "u": 15, "t": 2}]}, "binlog_cache_size": {"d": true, "t": 3, "a": [{"a": "binlog_cache_size", "u": 15, "t": 2}, {"a": "sysvar_binlog_cache_size", "u": 36, "t": 1}, {"a": "sysvar_binlog_cache_size", "u": 37, "t": 1}]}, "binlog_checksum": {"d": true, "t": 1, "a": [{"a": "binlog_checksum", "u": 15, "t": 2}, {"a": "option_mysqld_binlog-checksum", "u": 36, "t": 1}, {"a": "sysvar_binlog_checksum", "u": 36, "t": 1}, {"a": "option_mysqld_binlog-checksum", "u": 37, "t": 1}, {"a": "sysvar_binlog_checksum", "u": 37, "t": 1}]}, "binlog_commit_wait_count": {"d": true, "t": 3, "a": [{"a": "binlog_commit_wait_count", "u": 15, "t": 2}]}, "binlog_commit_wait_usec": {"d": true, "t": 3, "a": [{"a": "binlog_commit_wait_usec", "u": 15, "t": 2}]}, "binlog_direct_non_transactional_updates": {"d": true, "t": 2, "a": [{"a": "binlog_direct_non_transactional_updates", "u": 15, "t": 2}, {"a": "sysvar_binlog_direct_non_transactional_updates", "u": 36, "t": 1}, {"a": "sysvar_binlog_direct_non_transactional_updates", "u": 37, "t": 1}]}, "binlog_expire_logs_seconds": {"d": true, "t": 3, "a": [{"a": "binlog_expire_logs_seconds", "u": 15, "t": 2}, {"a": "sysvar_binlog_expire_logs_seconds", "u": 36, "t": 1}]}, "binlog_file_cache_size": {"d": true, "t": 3, "a": [{"a": "binlog_file_cache_size", "u": 15, "t": 2}]}, "binlog_format": {"d": true, "t": 5, "a": [{"a": "binlog_format", "u": 15, "t": 2}, {"a": "sysvar_binlog_format", "u": 36, "t": 1}, {"a": "sysvar_binlog_format", "u": 37, "t": 1}]}, "binlog_optimize_thread_scheduling": {"d": false, "t": 2, "a": [{"a": "binlog_optimize_thread_scheduling", "u": 15, "t": 2}]}, "binlog_row_image": {"d": true, "t": 5, "a": [{"a": "binlog_row_image", "u": 15, "t": 2}, {"a": "sysvar_binlog_row_image", "u": 36, "t": 1}, {"a": "sysvar_binlog_row_image", "u": 37, "t": 1}]}, "binlog_row_metadata": {"d": true, "t": 5, "a": [{"a": "binlog_row_metadata", "u": 15, "t": 2}, {"a": "sysvar_binlog_row_metadata", "u": 36, "t": 1}]}, "binlog_stmt_cache_size": {"d": true, "t": 3, "a": [{"a": "binlog_stmt_cache_size", "u": 15, "t": 2}, {"a": "sysvar_binlog_stmt_cache_size", "u": 36, "t": 1}, {"a": "sysvar_binlog_stmt_cache_size", "u": 37, "t": 1}]}, "default_master_connection": {"d": true, "t": 1, "a": [{"a": "default_master_connection", "u": 15, "t": 2}]}, "encrypt_binlog": {"d": false, "t": 2, "a": [{"a": "encrypt_binlog", "u": 15, "t": 2}]}, "expire_logs_days": {"d": true, "t": 3, "a": [{"a": "expire_logs_days", "u": 15, "t": 2}, {"a": "sysvar_expire_logs_days", "u": 36, "t": 1}, {"a": "sysvar_expire_logs_days", "u": 37, "t": 1}]}, "init_slave": {"d": true, "t": 1, "a": [{"a": "init_slave", "u": 15, "t": 2}, {"a": "sysvar_init_slave", "u": 38, "t": 1}]}, "log_bin": {"d": false, "t": 2, "a": [{"a": "log_bin", "u": 15, "t": 2}, {"a": "option_mysqld_log-bin", "u": 36, "t": 1}, {"a": "sysvar_log_bin", "u": 36, "t": 1}, {"a": "option_mysqld_log-bin", "u": 37, "t": 1}, {"a": "sysvar_log_bin", "u": 37, "t": 1}]}, "log_bin_basename": {"d": false, "t": 8, "a": [{"a": "log_bin_basename", "u": 15, "t": 2}, {"a": "sysvar_log_bin_basename", "u": 36, "t": 1}, {"a": "sysvar_log_bin_basename", "u": 37, "t": 1}]}, "log_bin_compress": {"d": true, "t": 2, "a": [{"a": "log_bin_compress", "u": 15, "t": 2}]}, "log_bin_compress_min_len": {"d": true, "t": 3, "a": [{"a": "log_bin_compress_min_len", "u": 15, "t": 2}]}, "log_bin_index": {"d": false, "t": 8, "a": [{"a": "log_bin_index", "u": 15, "t": 2}, {"a": "option_mysqld_log-bin-index", "u": 36, "t": 1}, {"a": "sysvar_log_bin_index", "u": 36, "t": 1}, {"a": "option_mysqld_log-bin-index", "u": 37, "t": 1}, {"a": "sysvar_log_bin_index", "u": 37, "t": 1}]}, "log_bin_trust_function_creators": {"d": true, "t": 2, "a": [{"a": "log_bin_trust_function_creators", "u": 15, "t": 2}, {"a": "sysvar_log_bin_trust_function_creators", "u": 36, "t": 1}, {"a": "sysvar_log_bin_trust_function_creators", "u": 37, "t": 1}]}, "log_slow_slave_statements": {"d": true, "t": 2, "a": [{"a": "log_slow_slave_statements", "u": 15, "t": 2}, {"a": "sysvar_log_slow_slave_statements", "u": 38, "t": 1}]}, "log_slave_updates": {"d": false, "t": 2, "a": [{"a": "log_slave_updates", "u": 15, "t": 2}, {"a": "sysvar_log_slave_updates", "u": 36, "t": 1}, {"a": "sysvar_log_slave_updates", "u": 37, "t": 1}]}, "master_verify_checksum": {"d": true, "t": 2, "a": [{"a": "master_verify_checksum", "u": 15, "t": 2}, {"a": "sysvar_master_verify_checksum", "u": 36, "t": 1}, {"a": "sysvar_master_verify_checksum", "u": 37, "t": 1}]}, "max_binlog_cache_size": {"d": true, "t": 3, "a": [{"a": "max_binlog_cache_size", "u": 15, "t": 2}, {"a": "sysvar_max_binlog_cache_size", "u": 36, "t": 1}, {"a": "sysvar_max_binlog_cache_size", "u": 37, "t": 1}]}, "max_binlog_size": {"d": true, "t": 3, "a": [{"a": "max_binlog_size", "u": 15, "t": 2}, {"a": "sysvar_max_binlog_size", "u": 36, "t": 1}, {"a": "sysvar_max_binlog_size", "u": 37, "t": 1}]}, "max_binlog_stmt_cache_size": {"d": true, "t": 3, "a": [{"a": "max_binlog_stmt_cache_size", "u": 15, "t": 2}, {"a": "sysvar_max_binlog_stmt_cache_size", "u": 36, "t": 1}, {"a": "sysvar_max_binlog_stmt_cache_size", "u": 37, "t": 1}]}, "max_relay_log_size": {"d": true, "t": 3, "a": [{"a": "max_relay_log_size", "u": 15, "t": 2}, {"a": "option_mysqld_max-relay-log-size", "u": 38, "t": 1}, {"a": "sysvar_max_relay_log_size", "u": 38, "t": 1}]}, "read_binlog_speed_limit": {"d": true, "t": 3, "a": [{"a": "read_binlog_speed_limit", "u": 15, "t": 2}]}, "relay_log": {"d": false, "t": 8, "a": [{"a": "relay_log", "u": 15, "t": 2}, {"a": "sysvar_relay_log", "u": 38, "t": 1}]}, "relay_log_basename": {"d": false, "a": [{"a": "relay_log_basename", "u": 15, "t": 2}, {"a": "sysvar_relay_log_basename", "u": 38, "t": 1}]}, "relay_log_index": {"d": false, "a": [{"a": "relay_log_index", "u": 15, "t": 2}, {"a": "sysvar_relay_log_index", "u": 38, "t": 1}]}, "relay_log_info_file": {"d": false, "a": [{"a": "relay_log_info_file", "u": 15, "t": 2}, {"a": "sysvar_relay_log_info_file", "u": 38, "t": 1}]}, "relay_log_purge": {"d": true, "t": 2, "a": [{"a": "relay_log_purge", "u": 15, "t": 2}, {"a": "option_mysqld_relay-log-purge", "u": 38, "t": 1}, {"a": "sysvar_relay_log_purge", "u": 38, "t": 1}]}, "relay_log_recovery": {"t": 2, "a": [{"a": "relay_log_recovery", "u": 15, "t": 2}, {"a": "sysvar_relay_log_recovery", "u": 38, "t": 1}]}, "relay_log_space_limit": {"d": false, "t": 3, "a": [{"a": "relay_log_space_limit", "u": 15, "t": 2}, {"a": "option_mysqld_relay-log-space-limit", "u": 38, "t": 1}, {"a": "sysvar_relay_log_space_limit", "u": 38, "t": 1}]}, "replicate_annotate_row_events": {"d": false, "t": 2, "a": [{"a": "replicate_annotate_row_events", "u": 15, "t": 2}]}, "replicate_do_db": {"t": 1, "a": [{"a": "replicate_do_db", "u": 15, "t": 2}, {"a": "option_mysqld_replicate-do-db", "u": 38, "t": 1}]}, "replicate_do_table": {"t": 1, "a": [{"a": "replicate_do_table", "u": 15, "t": 2}, {"a": "option_mysqld_replicate-do-table", "u": 38, "t": 1}]}, "replicate_events_marked_for_skip": {"d": true, "t": 5, "a": [{"a": "replicate_events_marked_for_skip", "u": 15, "t": 2}]}, "replicate_ignore_db": {"t": 1, "a": [{"a": "replicate_ignore_db", "u": 15, "t": 2}, {"a": "option_mysqld_replicate-ignore-db", "u": 38, "t": 1}]}, "replicate_ignore_table": {"t": 1, "a": [{"a": "replicate_ignore_table", "u": 15, "t": 2}, {"a": "option_mysqld_replicate-ignore-table", "u": 38, "t": 1}]}, "replicate_rewrite_db": {"t": 1, "a": [{"a": "replicate_rewrite_db", "u": 15, "t": 2}, {"a": "option_mysqld_replicate-rewrite-db", "u": 38, "t": 1}]}, "replicate_wild_do_table": {"t": 1, "a": [{"a": "replicate_wild_do_table", "u": 15, "t": 2}, {"a": "option_mysqld_replicate-wild-do-table", "u": 38, "t": 1}]}, "replicate_wild_ignore_table": {"t": 1, "a": [{"a": "replicate_wild_ignore_table", "u": 15, "t": 2}, {"a": "option_mysqld_replicate-wild-ignore-table", "u": 38, "t": 1}]}, "report_host": {"d": false, "t": 1, "a": [{"a": "report_host", "u": 15, "t": 2}, {"a": "sysvar_report_host", "u": 38, "t": 1}]}, "report_password": {"d": false, "t": 1, "a": [{"a": "report_password", "u": 15, "t": 2}, {"a": "sysvar_report_password", "u": 38, "t": 1}]}, "report_port": {"d": false, "t": 3, "a": [{"a": "report_port", "u": 15, "t": 2}, {"a": "sysvar_report_port", "u": 38, "t": 1}]}, "report_user": {"d": false, "t": 1, "a": [{"a": "report_user", "u": 15, "t": 2}, {"a": "sysvar_report_user", "u": 38, "t": 1}]}, "skip_parallel_replication": {"d": true, "t": 2, "a": [{"a": "skip_parallel_replication", "u": 15, "t": 2}]}, "skip_replication": {"d": true, "t": 2, "a": [{"a": "skip_replication", "u": 15, "t": 2}]}, "slave_compressed_protocol": {"d": true, "t": 2, "a": [{"a": "slave_compressed_protocol", "u": 15, "t": 2}, {"a": "sysvar_slave_compressed_protocol", "u": 38, "t": 1}]}, "slave_ddl_exec_mode": {"d": true, "t": 5, "a": [{"a": "slave_ddl_exec_mode", "u": 15, "t": 2}]}, "slave_domain_parallel_threads": {"d": true, "t": 3, "a": [{"a": "slave_domain_parallel_threads", "u": 15, "t": 2}]}, "slave_exec_mode": {"d": true, "t": 5, "a": [{"a": "slave_exec_mode", "u": 15, "t": 2}, {"a": "sysvar_slave_exec_mode", "u": 38, "t": 1}]}, "slave_load_tmpdir": {"d": false, "a": [{"a": "slave_load_tmpdir", "u": 15, "t": 2}, {"a": "sysvar_slave_load_tmpdir", "u": 38, "t": 1}]}, "slave_max_allowed_packet": {"d": true, "t": 3, "a": [{"a": "slave_max_allowed_packet", "u": 15, "t": 2}, {"a": "sysvar_slave_max_allowed_packet", "u": 38, "t": 1}]}, "slave_net_timeout": {"d": true, "t": 3, "a": [{"a": "slave_net_timeout", "u": 15, "t": 2}, {"a": "sysvar_slave_net_timeout", "u": 38, "t": 1}]}, "slave_parallel_max_queued": {"d": true, "t": 3, "a": [{"a": "slave_parallel_max_queued", "u": 15, "t": 2}]}, "slave_parallel_mode": {"d": true, "t": 5, "a": [{"a": "slave_parallel_mode", "u": 15, "t": 2}]}, "slave_parallel_threads": {"d": true, "t": 3, "a": [{"a": "slave_parallel_threads", "u": 15, "t": 2}]}, "slave_parallel_workers": {"d": true, "t": 3, "a": [{"a": "slave_parallel_workers", "u": 15, "t": 2}, {"a": "sysvar_slave_parallel_workers", "u": 38, "t": 1}]}, "slave_run_triggers_for_rbr": {"t": 5, "a": [{"a": "slave_run_triggers_for_rbr", "u": 15, "t": 2}]}, "slave_skip_errors": {"d": false, "t": 1, "a": [{"a": "slave_skip_errors", "u": 15, "t": 2}, {"a": "option_mysqld_slave-skip-errors", "u": 38, "t": 1}, {"a": "sysvar_slave_skip_errors", "u": 38, "t": 1}]}, "slave_sql_verify_checksum": {"d": true, "t": 2, "a": [{"a": "slave_sql_verify_checksum", "u": 15, "t": 2}, {"a": "option_mysqld_slave-sql-verify-checksum", "u": 38, "t": 1}, {"a": "sysvar_slave_sql_verify_checksum", "u": 38, "t": 1}]}, "slave_transaction_retries": {"d": true, "t": 3, "a": [{"a": "slave_transaction_retries", "u": 15, "t": 2}, {"a": "sysvar_slave_transaction_retries", "u": 38, "t": 1}]}, "slave_transaction_retry_errors": {"d": false, "t": 1, "a": [{"a": "slave_transaction_retry_errors", "u": 15, "t": 2}]}, "slave_transaction_retry_interval": {"d": true, "t": 3, "a": [{"a": "slave_transaction_retry_interval", "u": 15, "t": 2}]}, "slave_type_conversions": {"d": true, "t": 6, "a": [{"a": "slave_type_conversions", "u": 15, "t": 2}, {"a": "sysvar_slave_type_conversions", "u": 38, "t": 1}]}, "sql_log_bin": {"d": true, "t": 2, "a": [{"a": "sql_log_bin", "u": 15, "t": 2}, {"a": "sysvar_sql_log_bin", "u": 36, "t": 1}, {"a": "sysvar_sql_log_bin", "u": 37, "t": 1}]}, "sql_slave_skip_counter": {"d": true, "t": 3, "a": [{"a": "sql_slave_skip_counter", "u": 15, "t": 2}, {"a": "sysvar_sql_slave_skip_counter", "u": 38, "t": 1}]}, "sync_binlog": {"d": true, "t": 3, "a": [{"a": "sync_binlog", "u": 15, "t": 2}, {"a": "sysvar_sync_binlog", "u": 36, "t": 1}, {"a": "sysvar_sync_binlog", "u": 37, "t": 1}]}, "sync_master_info": {"d": true, "t": 3, "a": [{"a": "sync_master_info", "u": 15, "t": 2}, {"a": "sysvar_sync_master_info", "u": 38, "t": 1}]}, "sync_relay_log": {"d": true, "t": 3, "a": [{"a": "sync_relay_log", "u": 15, "t": 2}, {"a": "sysvar_sync_relay_log", "u": 38, "t": 1}]}, "sync_relay_log_info": {"d": true, "t": 3, "a": [{"a": "sync_relay_log_info", "u": 15, "t": 2}, {"a": "sysvar_sync_relay_log_info", "u": 38, "t": 1}]}, "Binlog_bytes_written": {"t": 3, "a": [{"a": "binlog_bytes_written", "u": 39, "t": 2}]}, "Binlog_cache_disk_use": {"t": 3, "a": [{"a": "binlog_cache_disk_use", "u": 39, "t": 2}]}, "Binlog_cache_use": {"t": 3, "a": [{"a": "binlog_cache_use", "u": 39, "t": 2}]}, "Binlog_commits": {"t": 3, "a": [{"a": "binlog_commits", "u": 39, "t": 2}]}, "Binlog_group_commit_trigger_count": {"t": 3, "a": [{"a": "binlog_group_commit_trigger_count", "u": 39, "t": 2}]}, "Binlog_group_commit_trigger_lock_wait": {"t": 3, "a": [{"a": "binlog_group_commit_trigger_lock_wait", "u": 39, "t": 2}]}, "Binlog_group_commit_trigger_timeout": {"t": 3, "a": [{"a": "binlog_group_commit_trigger_timeout", "u": 39, "t": 2}]}, "Binlog_group_commits": {"t": 3, "a": [{"a": "binlog_group_commits", "u": 39, "t": 2}]}, "Binlog_snapshot_file": {"t": 1, "a": [{"a": "binlog_snapshot_file", "u": 39, "t": 2}]}, "Binlog_snapshot_position": {"t": 3, "a": [{"a": "binlog_snapshot_position", "u": 39, "t": 2}]}, "Binlog_stmt_cache_disk_use": {"t": 3, "a": [{"a": "binlog_stmt_cache_disk_use", "u": 39, "t": 2}]}, "Binlog_stmt_cache_use": {"t": 3, "a": [{"a": "binlog_stmt_cache_use", "u": 39, "t": 2}]}, "Com_change_master": {"t": 3, "a": [{"a": "com_change_master", "u": 39, "t": 2}]}, "Com_show_binlog_status": {"t": 3, "a": [{"a": "com_show_binlog_status", "u": 39, "t": 2}]}, "Com_show_master_status": {"t": 3, "a": [{"a": "com_show_master_status", "u": 39, "t": 2}]}, "Com_show_new_master": {"t": 3, "a": [{"a": "com_show_new_master", "u": 39, "t": 2}]}, "Com_show_slave_hosts": {"t": 3, "a": [{"a": "com_show_slave_hosts", "u": 39, "t": 2}]}, "Com_show_slave_status": {"t": 3, "a": [{"a": "com_show_slave_status", "u": 39, "t": 2}]}, "Com_slave_start": {"t": 3, "a": [{"a": "com_slave_start", "u": 39, "t": 2}]}, "Com_slave_stop": {"t": 3, "a": [{"a": "com_slave_stop", "u": 39, "t": 2}]}, "Com_start_all_slaves": {"t": 3, "a": [{"a": "com_start_all_slaves", "u": 39, "t": 2}]}, "Com_start_slave": {"t": 3, "a": [{"a": "com_start_slave", "u": 39, "t": 2}]}, "Com_stop_all_slaves": {"t": 3, "a": [{"a": "com_stop_all_slaves", "u": 39, "t": 2}]}, "Com_stop_slave": {"t": 3, "a": [{"a": "com_stop_slave", "u": 39, "t": 2}]}, "Master_gtid_wait_count": {"t": 3, "a": [{"a": "master_gtid_wait_count", "u": 39, "t": 2}]}, "Master_gtid_wait_time": {"t": 3, "a": [{"a": "master_gtid_wait_time", "u": 39, "t": 2}]}, "Master_gtid_wait_timeouts": {"t": 3, "a": [{"a": "master_gtid_wait_timeouts", "u": 39, "t": 2}]}, "Rpl_status": {"a": [{"a": "rpl_status", "u": 39, "t": 2}]}, "Rpl_transactions_multi_engine": {"t": 3, "a": [{"a": "rpl_transactions_multi_engine", "u": 39, "t": 2}]}, "Slave_connections": {"t": 3, "a": [{"a": "slave_connections", "u": 39, "t": 2}]}, "Slave_heartbeat_period": {"t": 3, "a": [{"a": "slave_heartbeat_period", "u": 39, "t": 2}]}, "Slave_open_temp_tables": {"t": 3, "a": [{"a": "slave_open_temp_tables", "u": 39, "t": 2}]}, "Slave_received_heartbeats": {"t": 3, "a": [{"a": "slave_received_heartbeats", "u": 39, "t": 2}]}, "Slave_retried_transactions": {"t": 3, "a": [{"a": "slave_retried_transactions", "u": 39, "t": 2}]}, "Slave_running": {"t": 3, "a": [{"a": "slave_running", "u": 39, "t": 2}]}, "Slave_skipped_errors": {"t": 3, "a": [{"a": "slave_skipped_errors", "u": 39, "t": 2}]}, "Slaves_connected": {"t": 3, "a": [{"a": "slaves_connected", "u": 39, "t": 2}]}, "Slaves_running": {"t": 3, "a": [{"a": "slaves_running", "u": 39, "t": 2}]}, "Transactions_gtid_foreign_engine": {"t": 3, "a": [{"a": "transactions_gtid_foreign_engine", "u": 39, "t": 2}]}, "Transactions_multi_engine": {"t": 3, "a": [{"a": "transactions_multi_engine", "u": 39, "t": 2}]}, "s3_access_key": {"d": false, "t": 1, "a": [{"a": "s3_access_key", "u": 40, "t": 2}]}, "s3_block_size": {"d": true, "t": 3, "a": [{"a": "s3_block_size", "u": 40, "t": 2}]}, "s3_bucket": {"d": false, "t": 1, "a": [{"a": "s3_bucket", "u": 40, "t": 2}]}, "s3_debug": {"d": false, "t": 2, "a": [{"a": "s3_debug", "u": 40, "t": 2}]}, "s3_host_name": {"d": false, "t": 1, "a": [{"a": "s3_host_name", "u": 40, "t": 2}]}, "s3_pagecache_age_threshold": {"d": true, "t": 3, "a": [{"a": "s3_pagecache_age_threshold", "u": 40, "t": 2}]}, "s3_pagecache_buffer_size": {"d": false, "t": 3, "a": [{"a": "s3_pagecache_buffer_size", "u": 40, "t": 2}]}, "s3_pagecache_division_limit": {"d": true, "t": 3, "a": [{"a": "s3_pagecache_division_limit", "u": 40, "t": 2}]}, "s3_pagecache_file_hash_size": {"d": false, "t": 3, "a": [{"a": "s3_pagecache_file_hash_size", "u": 40, "t": 2}]}, "s3_port": {"d": false, "t": 3, "a": [{"a": "s3_port", "u": 40, "t": 2}]}, "s3_protocol_version": {"d": true, "t": 5, "a": [{"a": "s3_protocol_version", "u": 40, "t": 2}]}, "s3_region": {"d": false, "t": 1, "a": [{"a": "s3_region", "u": 40, "t": 2}]}, "s3_replicate_alter_as_create_select": {"d": false, "t": 2, "a": [{"a": "s3_replicate_alter_as_create_select", "u": 40, "t": 2}]}, "s3_secret_key": {"d": false, "t": 1, "a": [{"a": "s3_secret_key", "u": 40, "t": 2}]}, "s3_slave_ignore_updates": {"d": false, "t": 2, "a": [{"a": "s3_slave_ignore_updates", "u": 40, "t": 2}]}, "s3_use_http": {"d": false, "t": 2, "a": [{"a": "s3_use_http", "u": 40, "t": 2}]}, "Rpl_semi_sync_master_clients": {"t": 3, "a": [{"a": "rpl_semi_sync_master_clients", "u": 41, "t": 2}]}, "Rpl_semi_sync_master_net_avg_wait_time": {"t": 3, "a": [{"a": "rpl_semi_sync_master_net_avg_wait_time", "u": 41, "t": 2}]}, "Rpl_semi_sync_master_net_wait_time": {"t": 3, "a": [{"a": "rpl_semi_sync_master_net_wait_time", "u": 41, "t": 2}]}, "Rpl_semi_sync_master_net_waits": {"t": 3, "a": [{"a": "rpl_semi_sync_master_net_waits", "u": 41, "t": 2}]}, "Rpl_semi_sync_master_no_times": {"t": 3, "a": [{"a": "rpl_semi_sync_master_no_times", "u": 41, "t": 2}]}, "Rpl_semi_sync_master_no_tx": {"t": 3, "a": [{"a": "rpl_semi_sync_master_no_tx", "u": 41, "t": 2}]}, "Rpl_semi_sync_master_status": {"t": 2, "a": [{"a": "rpl_semi_sync_master_status", "u": 41, "t": 2}]}, "Rpl_semi_sync_master_timefunc_failures": {"t": 3, "a": [{"a": "rpl_semi_sync_master_timefunc_failures", "u": 41, "t": 2}]}, "Rpl_semi_sync_master_tx_avg_wait_time": {"t": 3, "a": [{"a": "rpl_semi_sync_master_tx_avg_wait_time", "u": 41, "t": 2}]}, "Rpl_semi_sync_master_tx_wait_time": {"t": 3, "a": [{"a": "rpl_semi_sync_master_tx_wait_time", "u": 41, "t": 2}]}, "Rpl_semi_sync_master_tx_waits": {"t": 3, "a": [{"a": "rpl_semi_sync_master_tx_waits", "u": 41, "t": 2}]}, "Rpl_semi_sync_master_wait_pos_backtraverse": {"t": 3, "a": [{"a": "rpl_semi_sync_master_wait_pos_backtraverse", "u": 41, "t": 2}]}, "Rpl_semi_sync_master_wait_sessions": {"t": 3, "a": [{"a": "rpl_semi_sync_master_wait_sessions", "u": 41, "t": 2}]}, "Rpl_semi_sync_master_yes_tx": {"t": 3, "a": [{"a": "rpl_semi_sync_master_yes_tx", "u": 41, "t": 2}]}, "Rpl_semi_sync_slave_status": {"t": 2, "a": [{"a": "rpl_semi_sync_slave_status", "u": 41, "t": 2}]}, "rpl_semi_sync_master_enabled": {"d": true, "t": 2, "a": [{"a": "rpl_semi_sync_master_enabled", "u": 42, "t": 2}, {"a": "sysvar_rpl_semi_sync_master_enabled", "u": 35, "t": 1}]}, "rpl_semi_sync_master_timeout": {"d": true, "t": 3, "a": [{"a": "rpl_semi_sync_master_timeout", "u": 42, "t": 2}, {"a": "sysvar_rpl_semi_sync_master_timeout", "u": 35, "t": 1}]}, "rpl_semi_sync_master_trace_level": {"d": true, "t": 3, "a": [{"a": "rpl_semi_sync_master_trace_level", "u": 42, "t": 2}, {"a": "sysvar_rpl_semi_sync_master_trace_level", "u": 35, "t": 1}]}, "rpl_semi_sync_master_wait_no_slave": {"d": true, "t": 2, "a": [{"a": "rpl_semi_sync_master_wait_no_slave", "u": 42, "t": 2}, {"a": "sysvar_rpl_semi_sync_master_wait_no_slave", "u": 35, "t": 1}]}, "rpl_semi_sync_master_wait_point": {"d": true, "t": 5, "a": [{"a": "rpl_semi_sync_master_wait_point", "u": 42, "t": 2}, {"a": "sysvar_rpl_semi_sync_master_wait_point", "u": 35, "t": 1}]}, "rpl_semi_sync_slave_delay_master": {"d": true, "t": 2, "a": [{"a": "rpl_semi_sync_slave_delay_master", "u": 42, "t": 2}]}, "rpl_semi_sync_slave_enabled": {"d": true, "t": 2, "a": [{"a": "rpl_semi_sync_slave_enabled", "u": 42, "t": 2}, {"a": "sysvar_rpl_semi_sync_slave_enabled", "u": 38, "t": 1}]}, "rpl_semi_sync_slave_kill_conn_timeout": {"d": true, "t": 3, "a": [{"a": "rpl_semi_sync_slave_kill_conn_timeout", "u": 42, "t": 2}]}, "rpl_semi_sync_slave_trace_level": {"d": true, "t": 3, "a": [{"a": "rpl_semi_sync_slave_trace_level", "u": 42, "t": 2}, {"a": "sysvar_rpl_semi_sync_slave_trace_level", "u": 38, "t": 1}]}, "rpl_semi_sync_master": {"t": 5, "a": [{"a": "rpl_semi_sync_master", "u": 42, "t": 2}]}, "rpl_semi_sync_slave": {"t": 5, "a": [{"a": "rpl_semi_sync_slave", "u": 42, "t": 2}]}, "Aborted_clients": {"t": 3, "a": [{"a": "aborted_clients", "u": 43, "t": 2}]}, "Aborted_connects": {"t": 3, "a": [{"a": "aborted_connects", "u": 43, "t": 2}]}, "Aborted_connects_preauth": {"t": 3, "a": [{"a": "aborted_connects_preauth", "u": 43, "t": 2}]}, "Access_denied_errors": {"t": 3, "a": [{"a": "access_denied_errors", "u": 43, "t": 2}]}, "Acl_column_grants": {"t": 3, "a": [{"a": "acl_column_grants", "u": 43, "t": 2}]}, "Acl_database_grants": {"t": 3, "a": [{"a": "acl_database_grants", "u": 43, "t": 2}]}, "Acl_function_grants": {"t": 3, "a": [{"a": "acl_function_grants", "u": 43, "t": 2}]}, "Acl_package_body_grants": {"t": 3, "a": [{"a": "acl_package_body_grants", "u": 43, "t": 2}]}, "Acl_package_spec_grants": {"t": 3, "a": [{"a": "acl_package_spec_grants", "u": 43, "t": 2}]}, "Acl_procedure_grants": {"t": 3, "a": [{"a": "acl_procedure_grants", "u": 43, "t": 2}]}, "Acl_proxy_users": {"t": 3, "a": [{"a": "acl_proxy_users", "u": 43, "t": 2}]}, "Acl_role_grants": {"t": 3, "a": [{"a": "acl_role_grants", "u": 43, "t": 2}]}, "Acl_roles": {"t": 3, "a": [{"a": "acl_roles", "u": 43, "t": 2}]}, "Acl_table_grants": {"t": 3, "a": [{"a": "acl_table_grants", "u": 43, "t": 2}]}, "Acl_users": {"t": 3, "a": [{"a": "acl_users", "u": 43, "t": 2}]}, "Busy_time": {"t": 3, "a": [{"a": "busy_time", "u": 43, "t": 2}]}, "Bytes_received": {"t": 3, "a": [{"a": "bytes_received", "u": 43, "t": 2}]}, "Bytes_sent": {"t": 3, "a": [{"a": "bytes_sent", "u": 43, "t": 2}]}, "Com_admin_commands": {"t": 3, "a": [{"a": "com_admin_commands", "u": 43, "t": 2}]}, "Com_alter_db": {"t": 3, "a": [{"a": "com_alter_db", "u": 43, "t": 2}]}, "Com_alter_db_upgrade": {"t": 3, "a": [{"a": "com_alter_db_upgrade", "u": 43, "t": 2}]}, "Com_alter_event": {"t": 3, "a": [{"a": "com_alter_event", "u": 43, "t": 2}]}, "Com_alter_function": {"t": 3, "a": [{"a": "com_alter_function", "u": 43, "t": 2}]}, "Com_alter_procedure": {"t": 3, "a": [{"a": "com_alter_procedure", "u": 43, "t": 2}]}, "Com_alter_sequence": {"t": 3, "a": [{"a": "com_alter_sequence", "u": 43, "t": 2}]}, "Com_alter_server": {"t": 3, "a": [{"a": "com_alter_server", "u": 43, "t": 2}]}, "Com_alter_table": {"t": 3, "a": [{"a": "com_alter_table", "u": 43, "t": 2}]}, "Com_alter_tablespace": {"t": 3, "a": [{"a": "com_alter_tablespace", "u": 43, "t": 2}]}, "Com_alter_user": {"t": 3, "a": [{"a": "com_alter_user", "u": 43, "t": 2}]}, "Com_analyze": {"t": 3, "a": [{"a": "com_analyze", "u": 43, "t": 2}]}, "Com_assign_to_keycache": {"t": 3, "a": [{"a": "com_assign_to_keycache", "u": 43, "t": 2}]}, "Com_backup": {"t": 3, "a": [{"a": "com_backup", "u": 43, "t": 2}]}, "Com_backup_lock": {"t": 3, "a": [{"a": "com_backup_lock", "u": 43, "t": 2}]}, "Com_backup_table": {"t": 3, "a": [{"a": "com_backup_table", "u": 43, "t": 2}]}, "Com_begin": {"t": 3, "a": [{"a": "com_begin", "u": 43, "t": 2}]}, "Com_binlog": {"t": 3, "a": [{"a": "com_binlog", "u": 43, "t": 2}]}, "Com_call_procedure": {"t": 3, "a": [{"a": "com_call_procedure", "u": 43, "t": 2}]}, "Com_change_db": {"t": 3, "a": [{"a": "com_change_db", "u": 43, "t": 2}]}, "Com_check": {"t": 3, "a": [{"a": "com_check", "u": 43, "t": 2}]}, "Com_checksum": {"t": 3, "a": [{"a": "com_checksum", "u": 43, "t": 2}]}, "Com_commit": {"t": 3, "a": [{"a": "com_commit", "u": 43, "t": 2}]}, "Com_compound_sql": {"t": 3, "a": [{"a": "com_compound_sql", "u": 43, "t": 2}]}, "Com_create_db": {"t": 3, "a": [{"a": "com_create_db", "u": 43, "t": 2}]}, "Com_create_event": {"t": 3, "a": [{"a": "com_create_event", "u": 43, "t": 2}]}, "Com_create_function": {"t": 3, "a": [{"a": "com_create_function", "u": 43, "t": 2}]}, "Com_create_index": {"t": 3, "a": [{"a": "com_create_index", "u": 43, "t": 2}]}, "Com_create_package": {"t": 3, "a": [{"a": "com_create_package", "u": 43, "t": 2}]}, "Com_create_package_body": {"t": 3, "a": [{"a": "com_create_package_body", "u": 43, "t": 2}]}, "Com_create_procedure": {"t": 3, "a": [{"a": "com_create_procedure", "u": 43, "t": 2}]}, "Com_create_role": {"t": 3, "a": [{"a": "com_create_role", "u": 43, "t": 2}]}, "Com_create_sequence": {"t": 3, "a": [{"a": "com_create_sequence", "u": 43, "t": 2}]}, "Com_create_server": {"t": 3, "a": [{"a": "com_create_server", "u": 43, "t": 2}]}, "Com_create_table": {"t": 3, "a": [{"a": "com_create_table", "u": 43, "t": 2}]}, "Com_create_temporary_table": {"t": 3, "a": [{"a": "com_create_temporary_table", "u": 43, "t": 2}]}, "Com_create_trigger": {"t": 3, "a": [{"a": "com_create_trigger", "u": 43, "t": 2}]}, "Com_create_udf": {"t": 3, "a": [{"a": "com_create_udf", "u": 43, "t": 2}]}, "Com_create_user": {"t": 3, "a": [{"a": "com_create_user", "u": 43, "t": 2}]}, "Com_create_view": {"t": 3, "a": [{"a": "com_create_view", "u": 43, "t": 2}]}, "Com_dealloc_sql": {"t": 3, "a": [{"a": "com_dealloc_sql", "u": 43, "t": 2}]}, "Com_delete": {"t": 3, "a": [{"a": "com_delete", "u": 43, "t": 2}]}, "Com_delete_multi": {"t": 3, "a": [{"a": "com_delete_multi", "u": 43, "t": 2}]}, "Com_do": {"t": 3, "a": [{"a": "com_do", "u": 43, "t": 2}]}, "Com_drop_db": {"t": 3, "a": [{"a": "com_drop_db", "u": 43, "t": 2}]}, "Com_drop_event": {"t": 3, "a": [{"a": "com_drop_event", "u": 43, "t": 2}]}, "Com_drop_function": {"t": 3, "a": [{"a": "com_drop_function", "u": 43, "t": 2}]}, "Com_drop_index": {"t": 3, "a": [{"a": "com_drop_index", "u": 43, "t": 2}]}, "Com_drop_package": {"t": 3, "a": [{"a": "com_drop_package", "u": 43, "t": 2}]}, "Com_drop_package_body": {"t": 3, "a": [{"a": "com_drop_package_body", "u": 43, "t": 2}]}, "Com_drop_procedure": {"t": 3, "a": [{"a": "com_drop_procedure", "u": 43, "t": 2}]}, "Com_drop_role": {"t": 3, "a": [{"a": "com_drop_role", "u": 43, "t": 2}]}, "Com_drop_sequence": {"t": 3, "a": [{"a": "com_drop_sequence", "u": 43, "t": 2}]}, "Com_drop_server": {"t": 3, "a": [{"a": "com_drop_server", "u": 43, "t": 2}]}, "Com_drop_table": {"t": 3, "a": [{"a": "com_drop_table", "u": 43, "t": 2}]}, "Com_drop_temporary_table": {"t": 3, "a": [{"a": "com_drop_temporary_table", "u": 43, "t": 2}]}, "Com_drop_trigger": {"t": 3, "a": [{"a": "com_drop_trigger", "u": 43, "t": 2}]}, "Com_drop_user": {"t": 3, "a": [{"a": "com_drop_user", "u": 43, "t": 2}]}, "Com_drop_view": {"t": 3, "a": [{"a": "com_drop_view", "u": 43, "t": 2}]}, "Com_empty_query": {"t": 3, "a": [{"a": "com_empty_query", "u": 43, "t": 2}]}, "Com_execute_immediate": {"t": 3, "a": [{"a": "com_execute_immediate", "u": 43, "t": 2}]}, "Com_execute_sql": {"t": 3, "a": [{"a": "com_execute_sql", "u": 43, "t": 2}]}, "Com_flush": {"t": 3, "a": [{"a": "com_flush", "u": 43, "t": 2}]}, "Com_get_diagnostics": {"t": 3, "a": [{"a": "com_get_diagnostics", "u": 43, "t": 2}]}, "Com_grant": {"t": 3, "a": [{"a": "com_grant", "u": 43, "t": 2}]}, "Com_grant_role": {"t": 3, "a": [{"a": "com_grant_role", "u": 43, "t": 2}]}, "Com_ha_close": {"t": 3, "a": [{"a": "com_ha_close", "u": 43, "t": 2}]}, "Com_ha_open": {"t": 3, "a": [{"a": "com_ha_open", "u": 43, "t": 2}]}, "Com_ha_read": {"t": 3, "a": [{"a": "com_ha_read", "u": 43, "t": 2}]}, "Com_help": {"t": 3, "a": [{"a": "com_help", "u": 43, "t": 2}]}, "Com_insert": {"t": 3, "a": [{"a": "com_insert", "u": 43, "t": 2}]}, "Com_insert_select": {"t": 3, "a": [{"a": "com_insert_select", "u": 43, "t": 2}]}, "Com_install_plugin": {"t": 3, "a": [{"a": "com_install_plugin", "u": 43, "t": 2}]}, "Com_kill": {"t": 3, "a": [{"a": "com_kill", "u": 43, "t": 2}]}, "Com_load": {"t": 3, "a": [{"a": "com_load", "u": 43, "t": 2}]}, "Com_load_master_data": {"t": 3, "a": [{"a": "com_load_master_data", "u": 43, "t": 2}]}, "Com_load_master_table": {"t": 3, "a": [{"a": "com_load_master_table", "u": 43, "t": 2}]}, "Com_multi": {"t": 3, "a": [{"a": "com_multi", "u": 43, "t": 2}]}, "Com_lock_tables": {"t": 3, "a": [{"a": "com_lock_tables", "u": 43, "t": 2}]}, "Com_optimize": {"t": 3, "a": [{"a": "com_optimize", "u": 43, "t": 2}]}, "Com_preload_keys": {"t": 3, "a": [{"a": "com_preload_keys", "u": 43, "t": 2}]}, "Com_prepare_sql": {"t": 3, "a": [{"a": "com_prepare_sql", "u": 43, "t": 2}]}, "Com_purge": {"t": 3, "a": [{"a": "com_purge", "u": 43, "t": 2}]}, "Com_purge_before_date": {"t": 3, "a": [{"a": "com_purge_before_date", "u": 43, "t": 2}]}, "Com_release_savepoint": {"t": 3, "a": [{"a": "com_release_savepoint", "u": 43, "t": 2}]}, "Com_rename_table": {"t": 3, "a": [{"a": "com_rename_table", "u": 43, "t": 2}]}, "Com_rename_user": {"t": 3, "a": [{"a": "com_rename_user", "u": 43, "t": 2}]}, "Com_repair": {"t": 3, "a": [{"a": "com_repair", "u": 43, "t": 2}]}, "Com_replace": {"t": 3, "a": [{"a": "com_replace", "u": 43, "t": 2}]}, "Com_replace_select": {"t": 3, "a": [{"a": "com_replace_select", "u": 43, "t": 2}]}, "Com_reset": {"t": 3, "a": [{"a": "com_reset", "u": 43, "t": 2}]}, "Com_resignal": {"t": 3, "a": [{"a": "com_resignal", "u": 43, "t": 2}]}, "Com_restore_table": {"t": 3, "a": [{"a": "com_restore_table", "u": 43, "t": 2}]}, "Com_revoke": {"t": 3, "a": [{"a": "com_revoke", "u": 43, "t": 2}]}, "Com_revoke_all": {"t": 3, "a": [{"a": "com_revoke_all", "u": 43, "t": 2}]}, "Com_revoke_grant": {"t": 3, "a": [{"a": "com_revoke_grant", "u": 43, "t": 2}]}, "Com_rollback": {"t": 3, "a": [{"a": "com_rollback", "u": 43, "t": 2}]}, "Com_rollback_to_savepoint": {"t": 3, "a": [{"a": "com_rollback_to_savepoint", "u": 43, "t": 2}]}, "Com_savepoint": {"t": 3, "a": [{"a": "com_savepoint", "u": 43, "t": 2}]}, "Com_select": {"t": 3, "a": [{"a": "com_select", "u": 43, "t": 2}]}, "Com_set_option": {"t": 3, "a": [{"a": "com_set_option", "u": 43, "t": 2}]}, "Com_signal": {"t": 3, "a": [{"a": "com_signal", "u": 43, "t": 2}]}, "Com_show_authors": {"t": 3, "a": [{"a": "com_show_authors", "u": 43, "t": 2}]}, "Com_show_binlog_events": {"t": 3, "a": [{"a": "com_show_binlog_events", "u": 43, "t": 2}]}, "Com_show_binlogs": {"t": 3, "a": [{"a": "com_show_binlogs", "u": 43, "t": 2}]}, "Com_show_charsets": {"t": 3, "a": [{"a": "com_show_charsets", "u": 43, "t": 2}]}, "Com_show_client_statistics": {"t": 3, "a": [{"a": "com_show_client_statistics", "u": 43, "t": 2}]}, "Com_show_collations": {"t": 3, "a": [{"a": "com_show_collations", "u": 43, "t": 2}]}, "Com_show_column_types": {"t": 3, "a": [{"a": "com_show_column_types", "u": 43, "t": 2}]}, "Com_show_contributors": {"t": 3, "a": [{"a": "com_show_contributors", "u": 43, "t": 2}]}, "Com_show_create_db": {"t": 3, "a": [{"a": "com_show_create_db", "u": 43, "t": 2}]}, "Com_show_create_event": {"t": 3, "a": [{"a": "com_show_create_event", "u": 43, "t": 2}]}, "Com_show_create_func": {"t": 3, "a": [{"a": "com_show_create_func", "u": 43, "t": 2}]}, "Com_show_create_package": {"t": 3, "a": [{"a": "com_show_create_package", "u": 43, "t": 2}]}, "Com_show_create_package_body": {"t": 3, "a": [{"a": "com_show_create_package_body", "u": 43, "t": 2}]}, "Com_show_create_proc": {"t": 3, "a": [{"a": "com_show_create_proc", "u": 43, "t": 2}]}, "Com_show_create_table": {"t": 3, "a": [{"a": "com_show_create_table", "u": 43, "t": 2}]}, "Com_show_create_trigger": {"t": 3, "a": [{"a": "com_show_create_trigger", "u": 43, "t": 2}]}, "Com_show_create_user": {"t": 3, "a": [{"a": "com_show_create_user", "u": 43, "t": 2}]}, "Com_show_databases": {"t": 3, "a": [{"a": "com_show_databases", "u": 43, "t": 2}]}, "Com_show_engine_logs": {"t": 3, "a": [{"a": "com_show_engine_logs", "u": 43, "t": 2}]}, "Com_show_engine_mutex": {"t": 3, "a": [{"a": "com_show_engine_mutex", "u": 43, "t": 2}]}, "Com_show_engine_status": {"t": 3, "a": [{"a": "com_show_engine_status", "u": 43, "t": 2}]}, "Com_show_events": {"t": 3, "a": [{"a": "com_show_events", "u": 43, "t": 2}]}, "Com_show_errors": {"t": 3, "a": [{"a": "com_show_errors", "u": 43, "t": 2}]}, "Com_show_explain": {"t": 3, "a": [{"a": "com_show_explain", "u": 43, "t": 2}]}, "Com_show_fields": {"t": 3, "a": [{"a": "com_show_fields", "u": 43, "t": 2}]}, "Com_show_function_status": {"t": 3, "a": [{"a": "com_show_function_status", "u": 43, "t": 2}]}, "Com_show_generic": {"t": 3, "a": [{"a": "com_show_generic", "u": 43, "t": 2}]}, "Com_show_grants": {"t": 3, "a": [{"a": "com_show_grants", "u": 43, "t": 2}]}, "Com_show_keys": {"t": 3, "a": [{"a": "com_show_keys", "u": 43, "t": 2}]}, "Com_show_index_statistics": {"t": 3, "a": [{"a": "com_show_index_statistics", "u": 43, "t": 2}]}, "Com_show_open_tables": {"t": 3, "a": [{"a": "com_show_open_tables", "u": 43, "t": 2}]}, "Com_show_package_status": {"t": 3, "a": [{"a": "com_show_package_status", "u": 43, "t": 2}]}, "Com_show_package_body_status": {"t": 3, "a": [{"a": "com_show_package_body_status", "u": 43, "t": 2}]}, "Com_show_plugins": {"t": 3, "a": [{"a": "com_show_plugins", "u": 43, "t": 2}]}, "Com_show_privileges": {"t": 3, "a": [{"a": "com_show_privileges", "u": 43, "t": 2}]}, "Com_show_procedure_status": {"t": 3, "a": [{"a": "com_show_procedure_status", "u": 43, "t": 2}]}, "Com_show_processlist": {"t": 3, "a": [{"a": "com_show_processlist", "u": 43, "t": 2}]}, "Com_show_profile": {"t": 3, "a": [{"a": "com_show_profile", "u": 43, "t": 2}]}, "Com_show_profiles": {"t": 3, "a": [{"a": "com_show_profiles", "u": 43, "t": 2}]}, "Com_show_relaylog_events": {"t": 3, "a": [{"a": "com_show_relaylog_events", "u": 43, "t": 2}]}, "Com_show_status": {"t": 3, "a": [{"a": "com_show_status", "u": 43, "t": 2}]}, "Com_show_storage_engines": {"t": 3, "a": [{"a": "com_show_storage_engines", "u": 43, "t": 2}]}, "Com_show_table_statistics": {"t": 3, "a": [{"a": "com_show_table_statistics", "u": 43, "t": 2}]}, "Com_show_table_status": {"t": 3, "a": [{"a": "com_show_table_status", "u": 43, "t": 2}]}, "Com_show_tables": {"t": 3, "a": [{"a": "com_show_tables", "u": 43, "t": 2}]}, "Com_show_triggers": {"t": 3, "a": [{"a": "com_show_triggers", "u": 43, "t": 2}]}, "Com_show_user_statistics": {"t": 3, "a": [{"a": "com_show_user_statistics", "u": 43, "t": 2}]}, "Com_show_variable": {"t": 3, "a": [{"a": "com_show_variable", "u": 43, "t": 2}]}, "Com_show_warnings": {"t": 3, "a": [{"a": "com_show_warnings", "u": 43, "t": 2}]}, "Com_shutdown": {"t": 3, "a": [{"a": "com_shutdown", "u": 43, "t": 2}]}, "Com_stmt_close": {"t": 3, "a": [{"a": "com_stmt_close", "u": 43, "t": 2}]}, "Com_stmt_execute": {"t": 3, "a": [{"a": "com_stmt_execute", "u": 43, "t": 2}]}, "Com_stmt_fetch": {"t": 3, "a": [{"a": "com_stmt_fetch", "u": 43, "t": 2}]}, "Com_stmt_prepare": {"t": 3, "a": [{"a": "com_stmt_prepare", "u": 43, "t": 2}]}, "Com_stmt_reprepare": {"t": 3, "a": [{"a": "com_stmt_reprepare", "u": 43, "t": 2}]}, "Com_stmt_reset": {"t": 3, "a": [{"a": "com_stmt_reset", "u": 43, "t": 2}]}, "Com_stmt_send_long_data": {"t": 3, "a": [{"a": "com_stmt_send_long_data", "u": 43, "t": 2}]}, "Com_truncate": {"t": 3, "a": [{"a": "com_truncate", "u": 43, "t": 2}]}, "Com_uninstall_plugin": {"t": 3, "a": [{"a": "com_uninstall_plugin", "u": 43, "t": 2}]}, "Com_unlock_tables": {"t": 3, "a": [{"a": "com_unlock_tables", "u": 43, "t": 2}]}, "Com_update": {"t": 3, "a": [{"a": "com_update", "u": 43, "t": 2}]}, "Com_update_multi": {"t": 3, "a": [{"a": "com_update_multi", "u": 43, "t": 2}]}, "Com_xa_commit": {"t": 3, "a": [{"a": "com_xa_commit", "u": 43, "t": 2}]}, "Com_xa_end": {"t": 3, "a": [{"a": "com_xa_end", "u": 43, "t": 2}]}, "Com_xa_prepare": {"t": 3, "a": [{"a": "com_xa_prepare", "u": 43, "t": 2}]}, "Com_xa_recover": {"t": 3, "a": [{"a": "com_xa_recover", "u": 43, "t": 2}]}, "Com_xa_rollback": {"t": 3, "a": [{"a": "com_xa_rollback", "u": 43, "t": 2}]}, "Com_xa_start": {"t": 3, "a": [{"a": "com_xa_start", "u": 43, "t": 2}]}, "Compression": {"t": 2, "a": [{"a": "compression", "u": 43, "t": 2}]}, "Connection_errors_accept": {"t": 3, "a": [{"a": "connection_errors_accept", "u": 43, "t": 2}]}, "Connection_errors_internal": {"t": 3, "a": [{"a": "connection_errors_internal", "u": 43, "t": 2}]}, "Connection_errors_max_connections": {"t": 3, "a": [{"a": "connection_errors_max_connections", "u": 43, "t": 2}]}, "Connection_errors_peer_address": {"t": 3, "a": [{"a": "connection_errors_peer_address", "u": 43, "t": 2}]}, "Connection_errors_select": {"t": 3, "a": [{"a": "connection_errors_select", "u": 43, "t": 2}]}, "Connection_errors_tcpwrap": {"t": 3, "a": [{"a": "connection_errors_tcpwrap", "u": 43, "t": 2}]}, "Connections": {"t": 3, "a": [{"a": "connections", "u": 43, "t": 2}]}, "Cpu_time": {"t": 3, "a": [{"a": "cpu_time", "u": 43, "t": 2}]}, "Created_tmp_disk_tables": {"t": 3, "a": [{"a": "created_tmp_disk_tables", "u": 43, "t": 2}]}, "Created_tmp_files": {"t": 3, "a": [{"a": "created_tmp_files", "u": 43, "t": 2}]}, "Created_tmp_tables": {"t": 3, "a": [{"a": "created_tmp_tables", "u": 43, "t": 2}]}, "Delayed_errors": {"t": 3, "a": [{"a": "delayed_errors", "u": 43, "t": 2}]}, "Delayed_insert_threads": {"t": 3, "a": [{"a": "delayed_insert_threads", "u": 43, "t": 2}]}, "Delayed_writes": {"t": 3, "a": [{"a": "delayed_writes", "u": 43, "t": 2}]}, "Delete_scan": {"t": 3, "a": [{"a": "delete_scan", "u": 43, "t": 2}]}, "Empty_queries": {"t": 3, "a": [{"a": "empty_queries", "u": 43, "t": 2}]}, "Executed_events": {"t": 3, "a": [{"a": "executed_events", "u": 43, "t": 2}]}, "Executed_triggers": {"t": 3, "a": [{"a": "executed_triggers", "u": 43, "t": 2}]}, "Feature_application_time_periods": {"t": 3, "a": [{"a": "feature_application_time_periods", "u": 43, "t": 2}]}, "Feature_check_constraint": {"t": 3, "a": [{"a": "feature_check_constraint", "u": 43, "t": 2}]}, "Feature_custom_aggregate_functions": {"t": 3, "a": [{"a": "feature_custom_aggregate_functions", "u": 43, "t": 2}]}, "Feature_delay_key_write": {"t": 3, "a": [{"a": "feature_delay_key_write", "u": 43, "t": 2}]}, "Feature_dynamic_columns": {"t": 3, "a": [{"a": "feature_dynamic_columns", "u": 43, "t": 2}]}, "Feature_fulltext": {"t": 3, "a": [{"a": "feature_fulltext", "u": 43, "t": 2}]}, "Feature_gis": {"t": 3, "a": [{"a": "feature_gis", "u": 43, "t": 2}]}, "Feature_insert_returning": {"t": 3, "a": [{"a": "feature_insert_returning", "u": 43, "t": 2}]}, "Feature_invisible_columns": {"t": 3, "a": [{"a": "feature_invisible_columns", "u": 43, "t": 2}]}, "Feature_json": {"t": 3, "a": [{"a": "feature_json", "u": 43, "t": 2}]}, "Feature_locale": {"t": 3, "a": [{"a": "feature_locale", "u": 43, "t": 2}]}, "Feature_subquery": {"t": 3, "a": [{"a": "feature_subquery", "u": 43, "t": 2}]}, "Feature_system_versioning": {"t": 3, "a": [{"a": "feature_system_versioning", "u": 43, "t": 2}]}, "Feature_timezone": {"t": 3, "a": [{"a": "feature_timezone", "u": 43, "t": 2}]}, "Feature_trigger": {"t": 3, "a": [{"a": "feature_trigger", "u": 43, "t": 2}]}, "Feature_window_functions": {"t": 3, "a": [{"a": "feature_window_functions", "u": 43, "t": 2}]}, "Feature_xml": {"t": 3, "a": [{"a": "feature_xml", "u": 43, "t": 2}]}, "Flush_commands": {"t": 3, "a": [{"a": "flush_commands", "u": 43, "t": 2}]}, "Handler_commit": {"t": 3, "a": [{"a": "handler_commit", "u": 43, "t": 2}]}, "Handler_delete": {"t": 3, "a": [{"a": "handler_delete", "u": 43, "t": 2}]}, "Handler_discover": {"t": 3, "a": [{"a": "handler_discover", "u": 43, "t": 2}]}, "Handler_external_lock": {"t": 3, "a": [{"a": "handler_external_lock", "u": 43, "t": 2}]}, "Handler_icp_attempts": {"t": 3, "a": [{"a": "handler_icp_attempts", "u": 43, "t": 2}]}, "Handler_icp_match": {"t": 3, "a": [{"a": "handler_icp_match", "u": 43, "t": 2}]}, "Handler_mrr_init": {"t": 3, "a": [{"a": "handler_mrr_init", "u": 43, "t": 2}]}, "Handler_mrr_key_refills": {"t": 3, "a": [{"a": "handler_mrr_key_refills", "u": 43, "t": 2}]}, "Handler_mrr_rowid_refills": {"t": 3, "a": [{"a": "handler_mrr_rowid_refills", "u": 43, "t": 2}]}, "Handler_prepare": {"t": 3, "a": [{"a": "handler_prepare", "u": 43, "t": 2}]}, "Handler_read_first": {"t": 3, "a": [{"a": "handler_read_first", "u": 43, "t": 2}]}, "Handler_read_key": {"t": 3, "a": [{"a": "handler_read_key", "u": 43, "t": 2}]}, "Handler_read_last": {"t": 3, "a": [{"a": "handler_read_last", "u": 43, "t": 2}]}, "Handler_read_next": {"t": 3, "a": [{"a": "handler_read_next", "u": 43, "t": 2}]}, "Handler_read_prev": {"t": 3, "a": [{"a": "handler_read_prev", "u": 43, "t": 2}]}, "Handler_read_retry": {"t": 3, "a": [{"a": "handler_read_retry", "u": 43, "t": 2}]}, "Handler_read_rnd": {"t": 3, "a": [{"a": "handler_read_rnd", "u": 43, "t": 2}]}, "Handler_read_rnd_deleted": {"t": 3, "a": [{"a": "handler_read_rnd_deleted", "u": 43, "t": 2}]}, "Handler_read_rnd_next": {"t": 3, "a": [{"a": "handler_read_rnd_next", "u": 43, "t": 2}]}, "Handler_rollback": {"t": 3, "a": [{"a": "handler_rollback", "u": 43, "t": 2}]}, "Handler_savepoint": {"t": 3, "a": [{"a": "handler_savepoint", "u": 43, "t": 2}]}, "Handler_savepoint_rollback": {"t": 3, "a": [{"a": "handler_savepoint_rollback", "u": 43, "t": 2}]}, "Handler_tmp_delete": {"t": 3, "a": [{"a": "handler_tmp_delete", "u": 43, "t": 2}]}, "Handler_tmp_update": {"t": 3, "a": [{"a": "handler_tmp_update", "u": 43, "t": 2}]}, "Handler_tmp_write": {"t": 3, "a": [{"a": "handler_tmp_write", "u": 43, "t": 2}]}, "Handler_update": {"t": 3, "a": [{"a": "handler_update", "u": 43, "t": 2}]}, "Handler_write": {"t": 3, "a": [{"a": "handler_write", "u": 43, "t": 2}]}, "Key_blocks_not_flushed": {"t": 3, "a": [{"a": "key_blocks_not_flushed", "u": 43, "t": 2}]}, "Key_blocks_unused": {"t": 3, "a": [{"a": "key_blocks_unused", "u": 43, "t": 2}]}, "Key_blocks_used": {"t": 3, "a": [{"a": "key_blocks_used", "u": 43, "t": 2}]}, "Key_blocks_warm": {"t": 3, "a": [{"a": "key_blocks_warm", "u": 43, "t": 2}]}, "Key_read_requests": {"t": 3, "a": [{"a": "key_read_requests", "u": 43, "t": 2}]}, "Key_reads": {"t": 3, "a": [{"a": "key_reads", "u": 43, "t": 2}]}, "Key_write_requests": {"t": 3, "a": [{"a": "key_write_requests", "u": 43, "t": 2}]}, "Key_writes": {"t": 3, "a": [{"a": "key_writes", "u": 43, "t": 2}]}, "Last_query_cost": {"t": 3, "a": [{"a": "last_query_cost", "u": 43, "t": 2}]}, "Maria_*": {"a": [{"a": "maria_", "u": 43, "t": 2}]}, "Max_statement_time_exceeded": {"t": 3, "a": [{"a": "max_statement_time_exceeded", "u": 43, "t": 2}]}, "Max_used_connections": {"t": 3, "a": [{"a": "max_used_connections", "u": 43, "t": 2}]}, "Memory_used": {"t": 3, "a": [{"a": "memory_used", "u": 43, "t": 2}]}, "Memory_used_initial": {"t": 3, "a": [{"a": "memory_used_initial", "u": 43, "t": 2}]}, "Not_flushed_delayed_rows": {"t": 3, "a": [{"a": "not_flushed_delayed_rows", "u": 43, "t": 2}]}, "Open_files": {"t": 3, "a": [{"a": "open_files", "u": 43, "t": 2}]}, "Open_streams": {"t": 3, "a": [{"a": "open_streams", "u": 43, "t": 2}]}, "Open_table_definitions": {"t": 3, "a": [{"a": "open_table_definitions", "u": 43, "t": 2}]}, "Open_tables": {"t": 3, "a": [{"a": "open_tables", "u": 43, "t": 2}]}, "Opened_files": {"t": 3, "a": [{"a": "opened_files", "u": 43, "t": 2}]}, "Opened_plugin_libraries": {"t": 3, "a": [{"a": "opened_plugin_libraries", "u": 43, "t": 2}]}, "Opened_table_definitions": {"t": 3, "a": [{"a": "opened_table_definitions", "u": 43, "t": 2}]}, "Opened_tables": {"t": 3, "a": [{"a": "opened_tables", "u": 43, "t": 2}]}, "Opened_views": {"t": 3, "a": [{"a": "opened_views", "u": 43, "t": 2}]}, "Prepared_stmt_count": {"t": 3, "a": [{"a": "prepared_stmt_count", "u": 43, "t": 2}]}, "Qcache_free_blocks": {"t": 3, "a": [{"a": "qcache_free_blocks", "u": 43, "t": 2}]}, "Qcache_free_memory": {"t": 3, "a": [{"a": "qcache_free_memory", "u": 43, "t": 2}]}, "Qcache_hits": {"t": 3, "a": [{"a": "qcache_hits", "u": 43, "t": 2}]}, "Qcache_inserts": {"t": 3, "a": [{"a": "qcache_inserts", "u": 43, "t": 2}]}, "Qcache_lowmem_prunes": {"t": 3, "a": [{"a": "qcache_lowmem_prunes", "u": 43, "t": 2}]}, "Qcache_not_cached": {"t": 3, "a": [{"a": "qcache_not_cached", "u": 43, "t": 2}]}, "Qcache_queries_in_cache": {"t": 3, "a": [{"a": "qcache_queries_in_cache", "u": 43, "t": 2}]}, "Qcache_total_blocks": {"t": 3, "a": [{"a": "qcache_total_blocks", "u": 43, "t": 2}]}, "Queries": {"t": 3, "a": [{"a": "queries", "u": 43, "t": 2}]}, "Questions": {"t": 3, "a": [{"a": "questions", "u": 43, "t": 2}]}, "Resultset_metadata_skipped": {"t": 3, "a": [{"a": "resultset_metadata_skipped", "u": 43, "t": 2}]}, "Rows_read": {"t": 3, "a": [{"a": "rows_read", "u": 43, "t": 2}]}, "Rows_sent": {"t": 3, "a": [{"a": "rows_sent", "u": 43, "t": 2}]}, "Rows_tmp_read": {"t": 3, "a": [{"a": "rows_tmp_read", "u": 43, "t": 2}]}, "Select_full_join": {"t": 3, "a": [{"a": "select_full_join", "u": 43, "t": 2}]}, "Select_full_range_join": {"t": 3, "a": [{"a": "select_full_range_join", "u": 43, "t": 2}]}, "Select_range": {"t": 3, "a": [{"a": "select_range", "u": 43, "t": 2}]}, "Select_range_check": {"t": 3, "a": [{"a": "select_range_check", "u": 43, "t": 2}]}, "Select_scan": {"t": 3, "a": [{"a": "select_scan", "u": 43, "t": 2}]}, "Slow_launch_threads": {"t": 3, "a": [{"a": "slow_launch_threads", "u": 43, "t": 2}]}, "Slow_queries": {"t": 3, "a": [{"a": "slow_queries", "u": 43, "t": 2}]}, "Sort_merge_passes": {"t": 3, "a": [{"a": "sort_merge_passes", "u": 43, "t": 2}]}, "Sort_priority_queue_sorts": {"t": 3, "a": [{"a": "sort_priority_queue_sorts", "u": 43, "t": 2}]}, "Sort_range": {"t": 3, "a": [{"a": "sort_range", "u": 43, "t": 2}]}, "Sort_rows": {"t": 3, "a": [{"a": "sort_rows", "u": 43, "t": 2}]}, "Sort_scan": {"t": 3, "a": [{"a": "sort_scan", "u": 43, "t": 2}]}, "Subquery_cache_hit": {"t": 3, "a": [{"a": "subquery_cache_hit", "u": 43, "t": 2}]}, "Subquery_cache_miss": {"t": 3, "a": [{"a": "subquery_cache_miss", "u": 43, "t": 2}]}, "Syncs": {"t": 3, "a": [{"a": "syncs", "u": 43, "t": 2}]}, "Table_locks_immediate": {"t": 3, "a": [{"a": "table_locks_immediate", "u": 43, "t": 2}]}, "Table_locks_waited": {"t": 3, "a": [{"a": "table_locks_waited", "u": 43, "t": 2}]}, "Table_open_cache_active_instances": {"t": 3, "a": [{"a": "table_open_cache_active_instances", "u": 43, "t": 2}]}, "Table_open_cache_hits": {"t": 3, "a": [{"a": "table_open_cache_hits", "u": 43, "t": 2}]}, "Table_open_cache_misses": {"t": 3, "a": [{"a": "table_open_cache_misses", "u": 43, "t": 2}]}, "Table_open_cache_overflows": {"t": 3, "a": [{"a": "table_open_cache_overflows", "u": 43, "t": 2}]}, "Tc_log_max_pages_used": {"t": 3, "a": [{"a": "tc_log_max_pages_used", "u": 43, "t": 2}]}, "Tc_log_page_size": {"t": 3, "a": [{"a": "tc_log_page_size", "u": 43, "t": 2}]}, "Tc_log_page_waits": {"t": 3, "a": [{"a": "tc_log_page_waits", "u": 43, "t": 2}]}, "Threads_cached": {"t": 3, "a": [{"a": "threads_cached", "u": 43, "t": 2}]}, "Threads_connected": {"t": 3, "a": [{"a": "threads_connected", "u": 43, "t": 2}]}, "Threads_created": {"t": 3, "a": [{"a": "threads_created", "u": 43, "t": 2}]}, "Threads_running": {"t": 3, "a": [{"a": "threads_running", "u": 43, "t": 2}]}, "Update_scan": {"t": 3, "a": [{"a": "update_scan", "u": 43, "t": 2}]}, "Uptime": {"t": 3, "a": [{"a": "uptime", "u": 43, "t": 2}]}, "Uptime_since_flush_status": {"t": 3, "a": [{"a": "uptime_since_flush_status", "u": 43, "t": 2}]}, "alter_algorithm": {"d": true, "t": 5, "a": [{"a": "alter_algorithm", "u": 44, "t": 2}]}, "analyze_sample_percentage": {"d": true, "t": 3, "a": [{"a": "analyze_sample_percentage", "u": 44, "t": 2}]}, "autocommit": {"d": true, "t": 2, "a": [{"a": "autocommit", "u": 44, "t": 2}, {"a": "sysvar_autocommit", "u": 24, "t": 1}]}, "automatic_sp_privileges": {"d": true, "t": 2, "a": [{"a": "automatic_sp_privileges", "u": 44, "t": 2}, {"a": "sysvar_automatic_sp_privileges", "u": 24, "t": 1}]}, "back_log": {"d": false, "t": 3, "a": [{"a": "back_log", "u": 44, "t": 2}, {"a": "sysvar_back_log", "u": 24, "t": 1}]}, "basedir": {"d": false, "t": 7, "a": [{"a": "basedir", "u": 44, "t": 2}, {"a": "sysvar_basedir", "u": 25, "t": 1}, {"a": "sysvar_basedir", "u": 24, "t": 1}]}, "big_tables": {"d": true, "t": 2, "a": [{"a": "big_tables", "u": 44, "t": 2}, {"a": "sysvar_big_tables", "u": 24, "t": 1}]}, "bind_address": {"d": false, "t": 1, "a": [{"a": "bind_address", "u": 44, "t": 2}, {"a": "sysvar_bind_address", "u": 24, "t": 1}]}, "bulk_insert_buffer_size": {"d": true, "t": 3, "a": [{"a": "bulk_insert_buffer_size", "u": 44, "t": 2}, {"a": "sysvar_bulk_insert_buffer_size", "u": 24, "t": 1}]}, "character_set_client": {"d": true, "t": 1, "a": [{"a": "character_set_client", "u": 44, "t": 2}, {"a": "sysvar_character_set_client", "u": 24, "t": 1}]}, "character_set_connection": {"d": true, "t": 1, "a": [{"a": "character_set_connection", "u": 44, "t": 2}, {"a": "sysvar_character_set_connection", "u": 24, "t": 1}]}, "character_set_database": {"d": true, "t": 1, "a": [{"a": "character_set_database", "u": 44, "t": 2}, {"a": "sysvar_character_set_database", "u": 24, "t": 1}]}, "character_set_filesystem": {"d": true, "t": 1, "a": [{"a": "character_set_filesystem", "u": 44, "t": 2}, {"a": "sysvar_character_set_filesystem", "u": 24, "t": 1}]}, "character_set_results": {"d": true, "t": 1, "a": [{"a": "character_set_results", "u": 44, "t": 2}, {"a": "sysvar_character_set_results", "u": 24, "t": 1}]}, "character_set_server": {"d": true, "t": 1, "a": [{"a": "character_set_server", "u": 44, "t": 2}, {"a": "sysvar_character_set_server", "u": 24, "t": 1}]}, "character_set_system": {"d": false, "t": 1, "a": [{"a": "character_set_system", "u": 44, "t": 2}, {"a": "sysvar_character_set_system", "u": 24, "t": 1}]}, "character_sets_dir": {"d": false, "t": 7, "a": [{"a": "character_sets_dir", "u": 44, "t": 2}, {"a": "sysvar_character_sets_dir", "u": 24, "t": 1}]}, "check_constraint_checks": {"d": true, "t": 2, "a": [{"a": "check_constraint_checks", "u": 44, "t": 2}]}, "collation_connection": {"d": true, "t": 1, "a": [{"a": "collation_connection", "u": 44, "t": 2}, {"a": "sysvar_collation_connection", "u": 24, "t": 1}]}, "collation_database": {"d": true, "t": 1, "a": [{"a": "collation_database", "u": 44, "t": 2}, {"a": "sysvar_collation_database", "u": 24, "t": 1}]}, "collation_server": {"d": true, "t": 1, "a": [{"a": "collation_server", "u": 44, "t": 2}, {"a": "sysvar_collation_server", "u": 24, "t": 1}]}, "completion_type": {"d": true, "t": 5, "a": [{"a": "completion_type", "u": 44, "t": 2}, {"a": "sysvar_completion_type", "u": 24, "t": 1}]}, "concurrent_insert": {"d": true, "t": 5, "a": [{"a": "concurrent_insert", "u": 44, "t": 2}, {"a": "sysvar_concurrent_insert", "u": 24, "t": 1}]}, "connect_timeout": {"d": true, "t": 3, "a": [{"a": "connect_timeout", "u": 44, "t": 2}, {"a": "sysvar_connect_timeout", "u": 24, "t": 1}]}, "core_file": {"d": false, "t": 2, "a": [{"a": "core_file", "u": 44, "t": 2}, {"a": "option_mysqld_core-file", "u": 25, "t": 1}, {"a": "sysvar_core_file", "u": 24, "t": 1}]}, "datadir": {"d": false, "t": 7, "a": [{"a": "datadir", "u": 44, "t": 2}, {"a": "sysvar_datadir", "u": 25, "t": 1}, {"a": "sysvar_datadir", "u": 24, "t": 1}]}, "date_format": {"a": [{"a": "date_format", "u": 44, "t": 2}]}, "datetime_format": {"a": [{"a": "datetime_format", "u": 44, "t": 2}]}, "debug/debug_dbug": {"d": true, "t": 1, "a": [{"a": "debugdebug_dbug", "u": 44, "t": 2}]}, "debug_no_thread_alarm": {"d": false, "t": 2, "a": [{"a": "debug_no_thread_alarm", "u": 44, "t": 2}]}, "debug_sync": {"d": true, "t": 1, "a": [{"a": "debug_sync", "u": 44, "t": 2}, {"a": "sysvar_debug_sync", "u": 24, "t": 1}]}, "default_password_lifetime": {"d": true, "t": 3, "a": [{"a": "default_password_lifetime", "u": 44, "t": 2}, {"a": "sysvar_default_password_lifetime", "u": 24, "t": 1}]}, "default_regex_flags": {"d": true, "t": 5, "a": [{"a": "default_regex_flags", "u": 44, "t": 2}]}, "default_storage_engine": {"d": true, "t": 5, "a": [{"a": "default_storage_engine", "u": 44, "t": 2}, {"a": "sysvar_default_storage_engine", "u": 24, "t": 1}]}, "default_table_type": {"d": true, "a": [{"a": "default_table_type", "u": 44, "t": 2}]}, "default_tmp_storage_engine": {"d": true, "t": 5, "a": [{"a": "default_tmp_storage_engine", "u": 44, "t": 2}, {"a": "sysvar_default_tmp_storage_engine", "u": 24, "t": 1}]}, "default_week_format": {"d": true, "t": 3, "a": [{"a": "default_week_format", "u": 44, "t": 2}, {"a": "sysvar_default_week_format", "u": 24, "t": 1}]}, "delay_key_write": {"d": true, "t": 5, "a": [{"a": "delay_key_write", "u": 44, "t": 2}, {"a": "sysvar_delay_key_write", "u": 24, "t": 1}]}, "delayed_insert_limit": {"d": true, "t": 3, "a": [{"a": "delayed_insert_limit", "u": 44, "t": 2}, {"a": "sysvar_delayed_insert_limit", "u": 24, "t": 1}]}, "delayed_insert_timeout": {"d": true, "t": 3, "a": [{"a": "delayed_insert_timeout", "u": 44, "t": 2}, {"a": "sysvar_delayed_insert_timeout", "u": 24, "t": 1}]}, "delayed_queue_size": {"d": true, "t": 3, "a": [{"a": "delayed_queue_size", "u": 44, "t": 2}, {"a": "sysvar_delayed_queue_size", "u": 24, "t": 1}]}, "disconnect_on_expired_password": {"t": 2, "a": [{"a": "disconnect_on_expired_password", "u": 44, "t": 2}, {"a": "sysvar_disconnect_on_expired_password", "u": 24, "t": 1}]}, "div_precision_increment": {"d": true, "t": 3, "a": [{"a": "div_precision_increment", "u": 44, "t": 2}, {"a": "sysvar_div_precision_increment", "u": 24, "t": 1}]}, "encrypt_tmp_disk_tables": {"d": true, "t": 2, "a": [{"a": "encrypt_tmp_disk_tables", "u": 44, "t": 2}]}, "encrypt_tmp_files": {"d": false, "t": 2, "a": [{"a": "encrypt_tmp_files", "u": 44, "t": 2}]}, "encryption_algorithm": {"d": false, "t": 5, "a": [{"a": "encryption_algorithm", "u": 44, "t": 2}]}, "enforce_storage_engine": {"d": true, "t": 1, "a": [{"a": "enforce_storage_engine", "u": 44, "t": 2}]}, "engine_condition_pushdown": {"d": true, "t": 2, "a": [{"a": "engine_condition_pushdown", "u": 44, "t": 2}]}, "eq_range_index_dive_limit": {"d": true, "t": 3, "a": [{"a": "eq_range_index_dive_limit", "u": 44, "t": 2}, {"a": "sysvar_eq_range_index_dive_limit", "u": 24, "t": 1}]}, "error_count": {"d": true, "t": 3, "a": [{"a": "error_count", "u": 44, "t": 2}]}, "event_scheduler": {"d": true, "t": 5, "a": [{"a": "event_scheduler", "u": 44, "t": 2}, {"a": "sysvar_event_scheduler", "u": 24, "t": 1}]}, "expensive_subquery_limit": {"d": true, "t": 3, "a": [{"a": "expensive_subquery_limit", "u": 44, "t": 2}]}, "explicit_defaults_for_timestamp": {"t": 2, "a": [{"a": "explicit_defaults_for_timestamp", "u": 44, "t": 2}, {"a": "sysvar_explicit_defaults_for_timestamp", "u": 24, "t": 1}]}, "external_user": {"d": false, "t": 1, "a": [{"a": "external_user", "u": 44, "t": 2}, {"a": "sysvar_external_user", "u": 24, "t": 1}]}, "flush": {"d": true, "t": 2, "a": [{"a": "flush", "u": 44, "t": 2}, {"a": "option_mysqld_flush", "u": 25, "t": 1}, {"a": "sysvar_flush", "u": 24, "t": 1}]}, "flush_time": {"d": true, "t": 3, "a": [{"a": "flush_time", "u": 44, "t": 2}, {"a": "sysvar_flush_time", "u": 24, "t": 1}]}, "foreign_key_checks": {"d": true, "t": 2, "a": [{"a": "foreign_key_checks", "u": 44, "t": 2}, {"a": "sysvar_foreign_key_checks", "u": 24, "t": 1}]}, "ft_boolean_syntax": {"d": true, "t": 1, "a": [{"a": "ft_boolean_syntax", "u": 44, "t": 2}, {"a": "sysvar_ft_boolean_syntax", "u": 24, "t": 1}]}, "ft_max_word_len": {"d": false, "t": 3, "a": [{"a": "ft_max_word_len", "u": 44, "t": 2}, {"a": "sysvar_ft_max_word_len", "u": 24, "t": 1}]}, "ft_min_word_len": {"d": false, "t": 3, "a": [{"a": "ft_min_word_len", "u": 44, "t": 2}, {"a": "sysvar_ft_min_word_len", "u": 24, "t": 1}]}, "ft_query_expansion_limit": {"d": false, "t": 3, "a": [{"a": "ft_query_expansion_limit", "u": 44, "t": 2}, {"a": "sysvar_ft_query_expansion_limit", "u": 24, "t": 1}]}, "ft_stopword_file": {"d": false, "t": 8, "a": [{"a": "ft_stopword_file", "u": 44, "t": 2}, {"a": "sysvar_ft_stopword_file", "u": 24, "t": 1}]}, "general_log": {"d": true, "t": 2, "a": [{"a": "general_log", "u": 44, "t": 2}, {"a": "sysvar_general_log", "u": 24, "t": 1}]}, "general_log_file": {"d": true, "t": 8, "a": [{"a": "general_log_file", "u": 44, "t": 2}, {"a": "sysvar_general_log_file", "u": 24, "t": 1}]}, "group_concat_max_len": {"d": true, "t": 3, "a": [{"a": "group_concat_max_len", "u": 44, "t": 2}, {"a": "sysvar_group_concat_max_len", "u": 24, "t": 1}]}, "have_compress": {"d": false, "a": [{"a": "have_compress", "u": 44, "t": 2}]}, "have_crypt": {"d": false, "a": [{"a": "have_crypt", "u": 44, "t": 2}]}, "have_csv": {"d": false, "a": [{"a": "have_csv", "u": 44, "t": 2}]}, "have_dynamic_loading": {"d": false, "a": [{"a": "have_dynamic_loading", "u": 44, "t": 2}]}, "have_geometry": {"d": false, "a": [{"a": "have_geometry", "u": 44, "t": 2}]}, "have_ndbcluster": {"d": false, "a": [{"a": "have_ndbcluster", "u": 44, "t": 2}]}, "have_partitioning": {"d": false, "a": [{"a": "have_partitioning", "u": 44, "t": 2}]}, "have_profiling": {"d": false, "a": [{"a": "have_profiling", "u": 44, "t": 2}]}, "have_query_cache": {"d": false, "a": [{"a": "have_query_cache", "u": 44, "t": 2}]}, "have_rtree_keys": {"d": false, "a": [{"a": "have_rtree_keys", "u": 44, "t": 2}]}, "have_symlink": {"d": false, "a": [{"a": "have_symlink", "u": 44, "t": 2}]}, "histogram_size": {"d": true, "t": 3, "a": [{"a": "histogram_size", "u": 44, "t": 2}]}, "histogram_type": {"d": true, "t": 5, "a": [{"a": "histogram_type", "u": 44, "t": 2}]}, "host_cache_size": {"d": true, "t": 3, "a": [{"a": "host_cache_size", "u": 44, "t": 2}, {"a": "sysvar_host_cache_size", "u": 24, "t": 1}]}, "hostname": {"d": false, "t": 1, "a": [{"a": "hostname", "u": 44, "t": 2}, {"a": "sysvar_hostname", "u": 24, "t": 1}]}, "identity": {"a": [{"a": "identity", "u": 44, "t": 2}]}, "idle_readonly_transaction_timeout": {"d": true, "t": 3, "a": [{"a": "idle_readonly_transaction_timeout", "u": 44, "t": 2}]}, "idle_transaction_timeout": {"d": true, "t": 3, "a": [{"a": "idle_transaction_timeout", "u": 44, "t": 2}]}, "idle_write_transaction_timeout": {"d": true, "t": 3, "a": [{"a": "idle_write_transaction_timeout", "u": 44, "t": 2}]}, "ignore_db_dirs": {"d": false, "t": 1, "a": [{"a": "ignore_db_dirs", "u": 44, "t": 2}]}, "in_predicate_conversion_threshold": {"d": false, "t": 3, "a": [{"a": "in_predicate_conversion_threshold", "u": 44, "t": 2}]}, "in_transaction": {"d": false, "t": 2, "a": [{"a": "in_transaction", "u": 44, "t": 2}]}, "init_connect": {"d": true, "t": 1, "a": [{"a": "init_connect", "u": 44, "t": 2}, {"a": "sysvar_init_connect", "u": 24, "t": 1}]}, "init_file": {"d": false, "t": 8, "a": [{"a": "init_file", "u": 44, "t": 2}, {"a": "sysvar_init_file", "u": 24, "t": 1}]}, "insert_id": {"d": true, "t": 3, "a": [{"a": "insert_id", "u": 44, "t": 2}]}, "interactive_timeout": {"d": true, "t": 3, "a": [{"a": "interactive_timeout", "u": 44, "t": 2}, {"a": "sysvar_interactive_timeout", "u": 24, "t": 1}]}, "join_buffer_size": {"d": true, "t": 3, "a": [{"a": "join_buffer_size", "u": 44, "t": 2}, {"a": "sysvar_join_buffer_size", "u": 24, "t": 1}]}, "join_buffer_space_limit": {"d": true, "t": 3, "a": [{"a": "join_buffer_space_limit", "u": 44, "t": 2}]}, "join_cache_level": {"d": true, "t": 3, "a": [{"a": "join_cache_level", "u": 44, "t": 2}]}, "keep_files_on_create": {"d": true, "t": 2, "a": [{"a": "keep_files_on_create", "u": 44, "t": 2}, {"a": "sysvar_keep_files_on_create", "u": 24, "t": 1}]}, "large_files_support": {"d": false, "t": 2, "a": [{"a": "large_files_support", "u": 44, "t": 2}, {"a": "sysvar_large_files_support", "u": 24, "t": 1}]}, "large_page_size": {"d": false, "t": 3, "a": [{"a": "large_page_size", "u": 44, "t": 2}, {"a": "sysvar_large_page_size", "u": 24, "t": 1}]}, "large_pages": {"d": false, "t": 2, "a": [{"a": "large_pages", "u": 44, "t": 2}, {"a": "option_mysqld_large-pages", "u": 25, "t": 1}, {"a": "sysvar_large_pages", "u": 24, "t": 1}]}, "last_insert_id": {"d": true, "t": 3, "a": [{"a": "last_insert_id", "u": 44, "t": 2}]}, "lc_messages": {"d": true, "t": 1, "a": [{"a": "lc_messages", "u": 44, "t": 2}, {"a": "option_mysqld_lc-messages", "u": 25, "t": 1}, {"a": "sysvar_lc_messages", "u": 24, "t": 1}]}, "lc_messages_dir": {"d": false, "t": 7, "a": [{"a": "lc_messages_dir", "u": 44, "t": 2}, {"a": "option_mysqld_lc-messages-dir", "u": 25, "t": 1}, {"a": "sysvar_lc_messages_dir", "u": 24, "t": 1}]}, "lc_time_names": {"d": true, "t": 1, "a": [{"a": "lc_time_names", "u": 44, "t": 2}, {"a": "sysvar_lc_time_names", "u": 24, "t": 1}]}, "license": {"d": false, "t": 1, "a": [{"a": "license", "u": 44, "t": 2}, {"a": "sysvar_license", "u": 24, "t": 1}]}, "local_infile": {"d": true, "t": 2, "a": [{"a": "local_infile", "u": 44, "t": 2}, {"a": "sysvar_local_infile", "u": 24, "t": 1}]}, "lock_wait_timeout": {"d": true, "t": 3, "a": [{"a": "lock_wait_timeout", "u": 44, "t": 2}, {"a": "sysvar_lock_wait_timeout", "u": 24, "t": 1}]}, "locked_in_memory": {"d": false, "t": 2, "a": [{"a": "locked_in_memory", "u": 44, "t": 2}, {"a": "sysvar_locked_in_memory", "u": 24, "t": 1}]}, "log": {"d": true, "t": 1, "a": [{"a": "log", "u": 44, "t": 2}]}, "log_disabled_statements": {"d": false, "t": 6, "a": [{"a": "log_disabled_statements", "u": 44, "t": 2}]}, "log_error": {"d": false, "t": 8, "a": [{"a": "log_error", "u": 44, "t": 2}, {"a": "option_mysqld_log-error", "u": 25, "t": 1}, {"a": "sysvar_log_error", "u": 24, "t": 1}]}, "log_output": {"d": true, "t": 6, "a": [{"a": "log_output", "u": 44, "t": 2}, {"a": "sysvar_log_output", "u": 24, "t": 1}]}, "log_queries_not_using_indexes": {"d": true, "t": 2, "a": [{"a": "log_queries_not_using_indexes", "u": 44, "t": 2}, {"a": "sysvar_log_queries_not_using_indexes", "u": 24, "t": 1}]}, "log_slow_admin_statements": {"d": true, "t": 2, "a": [{"a": "log_slow_admin_statements", "u": 44, "t": 2}, {"a": "sysvar_log_slow_admin_statements", "u": 24, "t": 1}]}, "log_slow_disabled_statements": {"d": false, "t": 6, "a": [{"a": "log_slow_disabled_statements", "u": 44, "t": 2}]}, "log_slow_filter": {"d": true, "t": 5, "a": [{"a": "log_slow_filter", "u": 44, "t": 2}]}, "log_slow_min_examined_row_limit": {"d": true, "t": 3, "a": [{"a": "log_slow_min_examined_row_limit", "u": 44, "t": 2}]}, "log_slow_queries": {"d": true, "t": 2, "a": [{"a": "log_slow_queries", "u": 44, "t": 2}]}, "log_slow_query": {"d": true, "t": 2, "a": [{"a": "log_slow_query", "u": 44, "t": 2}]}, "log_slow_query_file": {"d": true, "t": 8, "a": [{"a": "log_slow_query_file", "u": 44, "t": 2}]}, "log_slow_query_time": {"d": true, "t": 3, "a": [{"a": "log_slow_query_time", "u": 44, "t": 2}]}, "log_slow_rate_limit": {"d": true, "t": 3, "a": [{"a": "log_slow_rate_limit", "u": 44, "t": 2}]}, "log_slow_verbosity": {"d": true, "t": 5, "a": [{"a": "log_slow_verbosity", "u": 44, "t": 2}]}, "log_tc_size": {"t": 3, "a": [{"a": "log_tc_size", "u": 44, "t": 2}, {"a": "option_mysqld_log-tc-size", "u": 25, "t": 1}]}, "log_warnings": {"d": true, "t": 3, "a": [{"a": "log_warnings", "u": 44, "t": 2}]}, "long_query_time": {"d": true, "t": 3, "a": [{"a": "long_query_time", "u": 44, "t": 2}, {"a": "sysvar_long_query_time", "u": 24, "t": 1}]}, "low_priority_updates": {"d": true, "t": 2, "a": [{"a": "low_priority_updates", "u": 44, "t": 2}, {"a": "sysvar_low_priority_updates", "u": 24, "t": 1}]}, "lower_case_file_system": {"d": false, "t": 2, "a": [{"a": "lower_case_file_system", "u": 44, "t": 2}, {"a": "sysvar_lower_case_file_system", "u": 24, "t": 1}]}, "lower_case_table_names": {"d": false, "t": 3, "a": [{"a": "lower_case_table_names", "u": 44, "t": 2}, {"a": "sysvar_lower_case_table_names", "u": 24, "t": 1}]}, "max_allowed_packet": {"t": 3, "a": [{"a": "max_allowed_packet", "u": 44, "t": 2}, {"a": "sysvar_max_allowed_packet", "u": 24, "t": 1}]}, "max_connect_errors": {"d": true, "t": 3, "a": [{"a": "max_connect_errors", "u": 44, "t": 2}, {"a": "sysvar_max_connect_errors", "u": 24, "t": 1}]}, "max_connections": {"d": true, "t": 3, "a": [{"a": "max_connections", "u": 44, "t": 2}, {"a": "sysvar_max_connections", "u": 24, "t": 1}]}, "max_delayed_threads": {"d": true, "t": 3, "a": [{"a": "max_delayed_threads", "u": 44, "t": 2}, {"a": "sysvar_max_delayed_threads", "u": 24, "t": 1}]}, "max_digest_length": {"t": 3, "a": [{"a": "max_digest_length", "u": 44, "t": 2}, {"a": "sysvar_max_digest_length", "u": 24, "t": 1}]}, "max_error_count": {"d": true, "t": 3, "a": [{"a": "max_error_count", "u": 44, "t": 2}, {"a": "sysvar_max_error_count", "u": 24, "t": 1}]}, "max_heap_table_size": {"d": true, "t": 3, "a": [{"a": "max_heap_table_size", "u": 44, "t": 2}, {"a": "sysvar_max_heap_table_size", "u": 24, "t": 1}]}, "max_insert_delayed_threads": {"d": true, "t": 3, "a": [{"a": "max_insert_delayed_threads", "u": 44, "t": 2}, {"a": "sysvar_max_insert_delayed_threads", "u": 24, "t": 1}]}, "max_join_size": {"d": true, "t": 3, "a": [{"a": "max_join_size", "u": 44, "t": 2}, {"a": "sysvar_max_join_size", "u": 24, "t": 1}]}, "max_length_for_sort_data": {"d": true, "t": 3, "a": [{"a": "max_length_for_sort_data", "u": 44, "t": 2}, {"a": "sysvar_max_length_for_sort_data", "u": 24, "t": 1}]}, "max_long_data_size": {"d": false, "t": 3, "a": [{"a": "max_long_data_size", "u": 44, "t": 2}]}, "max_password_errors": {"d": true, "t": 3, "a": [{"a": "max_password_errors", "u": 44, "t": 2}]}, "max_prepared_stmt_count": {"d": true, "t": 3, "a": [{"a": "max_prepared_stmt_count", "u": 44, "t": 2}, {"a": "sysvar_max_prepared_stmt_count", "u": 24, "t": 1}]}, "max_recursive_iterations": {"d": true, "t": 3, "a": [{"a": "max_recursive_iterations", "u": 44, "t": 2}]}, "max_rowid_filter_size": {"d": true, "t": 3, "a": [{"a": "max_rowid_filter_size", "u": 44, "t": 2}]}, "max_seeks_for_key": {"d": true, "t": 3, "a": [{"a": "max_seeks_for_key", "u": 44, "t": 2}, {"a": "sysvar_max_seeks_for_key", "u": 24, "t": 1}]}, "max_session_mem_used": {"d": true, "t": 3, "a": [{"a": "max_session_mem_used", "u": 44, "t": 2}]}, "max_sort_length": {"d": true, "t": 3, "a": [{"a": "max_sort_length", "u": 44, "t": 2}, {"a": "sysvar_max_sort_length", "u": 24, "t": 1}]}, "max_sp_recursion_depth": {"d": true, "t": 3, "a": [{"a": "max_sp_recursion_depth", "u": 44, "t": 2}, {"a": "sysvar_max_sp_recursion_depth", "u": 24, "t": 1}]}, "max_statement_time": {"d": true, "t": 3, "a": [{"a": "max_statement_time", "u": 44, "t": 2}]}, "max_tmp_tables": {"a": [{"a": "max_tmp_tables", "u": 44, "t": 2}]}, "max_user_connections": {"t": 3, "a": [{"a": "max_user_connections", "u": 44, "t": 2}, {"a": "sysvar_max_user_connections", "u": 24, "t": 1}]}, "max_write_lock_count": {"t": 3, "a": [{"a": "max_write_lock_count", "u": 44, "t": 2}, {"a": "sysvar_max_write_lock_count", "u": 24, "t": 1}]}, "metadata_locks_cache_size": {"d": false, "t": 3, "a": [{"a": "metadata_locks_cache_size", "u": 44, "t": 2}, {"a": "sysvar_metadata_locks_cache_size", "u": 24, "t": 1}]}, "metadata_locks_hash_instances": {"d": false, "t": 3, "a": [{"a": "metadata_locks_hash_instances", "u": 44, "t": 2}, {"a": "sysvar_metadata_locks_hash_instances", "u": 24, "t": 1}]}, "min_examined_row_limit": {"d": true, "t": 3, "a": [{"a": "min_examined_row_limit", "u": 44, "t": 2}, {"a": "sysvar_min_examined_row_limit", "u": 24, "t": 1}]}, "mrr_buffer_size": {"d": true, "t": 3, "a": [{"a": "mrr_buffer_size", "u": 44, "t": 2}]}, "multi_range_count": {"a": [{"a": "multi_range_count", "u": 44, "t": 2}]}, "mysql56_temporal_format": {"d": true, "t": 2, "a": [{"a": "mysql56_temporal_format", "u": 44, "t": 2}]}, "named_pipe": {"d": false, "t": 2, "a": [{"a": "named_pipe", "u": 44, "t": 2}, {"a": "sysvar_named_pipe", "u": 24, "t": 1}]}, "net_buffer_length": {"d": true, "t": 3, "a": [{"a": "net_buffer_length", "u": 44, "t": 2}, {"a": "sysvar_net_buffer_length", "u": 24, "t": 1}]}, "net_read_timeout": {"d": true, "t": 3, "a": [{"a": "net_read_timeout", "u": 44, "t": 2}, {"a": "sysvar_net_read_timeout", "u": 24, "t": 1}]}, "net_retry_count": {"d": true, "t": 3, "a": [{"a": "net_retry_count", "u": 44, "t": 2}, {"a": "sysvar_net_retry_count", "u": 24, "t": 1}]}, "net_write_timeout": {"d": true, "t": 3, "a": [{"a": "net_write_timeout", "u": 44, "t": 2}, {"a": "sysvar_net_write_timeout", "u": 24, "t": 1}]}, "old": {"t": 2, "a": [{"a": "old", "u": 44, "t": 2}, {"a": "sysvar_old", "u": 24, "t": 1}]}, "old_alter_table": {"d": true, "a": [{"a": "old_alter_table", "u": 44, "t": 2}, {"a": "sysvar_old_alter_table", "u": 24, "t": 1}]}, "old_mode": {"d": true, "t": 1, "a": [{"a": "old_mode", "u": 44, "t": 2}]}, "old_passwords": {"d": true, "t": 2, "a": [{"a": "old_passwords", "u": 44, "t": 2}]}, "open_files_limit": {"d": false, "t": 3, "a": [{"a": "open_files_limit", "u": 44, "t": 2}, {"a": "sysvar_open_files_limit", "u": 24, "t": 1}]}, "optimizer_extra_pruning_depth": {"d": true, "t": 3, "a": [{"a": "optimizer_extra_pruning_depth", "u": 44, "t": 2}]}, "optimizer_max_sel_arg_weight": {"d": true, "t": 3, "a": [{"a": "optimizer_max_sel_arg_weight", "u": 44, "t": 2}]}, "optimizer_prune_level": {"d": true, "t": 3, "a": [{"a": "optimizer_prune_level", "u": 44, "t": 2}, {"a": "sysvar_optimizer_prune_level", "u": 24, "t": 1}]}, "optimizer_search_depth": {"d": true, "t": 3, "a": [{"a": "optimizer_search_depth", "u": 44, "t": 2}, {"a": "sysvar_optimizer_search_depth", "u": 24, "t": 1}]}, "optimizer_selectivity_sampling_limit": {"d": true, "t": 3, "a": [{"a": "optimizer_selectivity_sampling_limit", "u": 44, "t": 2}]}, "optimizer_switch": {"d": true, "a": [{"a": "optimizer_switch", "u": 44, "t": 2}, {"a": "sysvar_optimizer_switch", "u": 24, "t": 1}]}, "optimizer_trace": {"d": true, "a": [{"a": "optimizer_trace", "u": 44, "t": 2}, {"a": "sysvar_optimizer_trace", "u": 24, "t": 1}]}, "optimizer_trace_max_mem_size": {"d": true, "t": 3, "a": [{"a": "optimizer_trace_max_mem_size", "u": 44, "t": 2}, {"a": "sysvar_optimizer_trace_max_mem_size", "u": 24, "t": 1}]}, "optimizer_use_condition_selectivity": {"d": true, "t": 3, "a": [{"a": "optimizer_use_condition_selectivity", "u": 44, "t": 2}]}, "pid_file": {"d": false, "t": 8, "a": [{"a": "pid_file", "u": 44, "t": 2}, {"a": "sysvar_pid_file", "u": 24, "t": 1}]}, "plugin_dir": {"d": false, "t": 7, "a": [{"a": "plugin_dir", "u": 44, "t": 2}, {"a": "sysvar_plugin_dir", "u": 24, "t": 1}]}, "plugin_maturity": {"d": false, "t": 5, "a": [{"a": "plugin_maturity", "u": 44, "t": 2}]}, "port": {"d": false, "t": 3, "a": [{"a": "port", "u": 44, "t": 2}, {"a": "option_mysqld_port", "u": 25, "t": 1}, {"a": "sysvar_port", "u": 24, "t": 1}]}, "preload_buffer_size": {"d": true, "t": 3, "a": [{"a": "preload_buffer_size", "u": 44, "t": 2}, {"a": "sysvar_preload_buffer_size", "u": 24, "t": 1}]}, "profiling": {"d": true, "t": 2, "a": [{"a": "profiling", "u": 44, "t": 2}]}, "profiling_history_size": {"d": true, "t": 3, "a": [{"a": "profiling_history_size", "u": 44, "t": 2}]}, "progress_report_time": {"d": true, "t": 3, "a": [{"a": "progress_report_time", "u": 44, "t": 2}]}, "protocol_version": {"d": false, "t": 3, "a": [{"a": "protocol_version", "u": 44, "t": 2}, {"a": "sysvar_protocol_version", "u": 24, "t": 1}]}, "proxy_protocol_networks": {"d": false, "t": 1, "a": [{"a": "proxy_protocol_networks", "u": 44, "t": 2}]}, "proxy_user": {"d": false, "t": 1, "a": [{"a": "proxy_user", "u": 44, "t": 2}, {"a": "sysvar_proxy_user", "u": 24, "t": 1}]}, "pseudo_slave_mode": {"d": true, "a": [{"a": "pseudo_slave_mode", "u": 44, "t": 2}, {"a": "sysvar_pseudo_slave_mode", "u": 24, "t": 1}]}, "pseudo_thread_id": {"d": true, "t": 3, "a": [{"a": "pseudo_thread_id", "u": 44, "t": 2}, {"a": "sysvar_pseudo_thread_id", "u": 24, "t": 1}]}, "query_alloc_block_size": {"d": true, "t": 3, "a": [{"a": "query_alloc_block_size", "u": 44, "t": 2}, {"a": "sysvar_query_alloc_block_size", "u": 24, "t": 1}]}, "query_cache_limit": {"d": true, "t": 3, "a": [{"a": "query_cache_limit", "u": 44, "t": 2}]}, "query_cache_min_res_unit": {"d": true, "t": 3, "a": [{"a": "query_cache_min_res_unit", "u": 44, "t": 2}]}, "query_cache_size": {"d": true, "t": 3, "a": [{"a": "query_cache_size", "u": 44, "t": 2}]}, "query_cache_strip_comments": {"d": true, "t": 2, "a": [{"a": "query_cache_strip_comments", "u": 44, "t": 2}]}, "query_cache_type": {"d": true, "t": 5, "a": [{"a": "query_cache_type", "u": 44, "t": 2}]}, "query_cache_wlock_invalidate": {"d": true, "t": 2, "a": [{"a": "query_cache_wlock_invalidate", "u": 44, "t": 2}]}, "query_prealloc_size": {"d": true, "t": 3, "a": [{"a": "query_prealloc_size", "u": 44, "t": 2}, {"a": "sysvar_query_prealloc_size", "u": 24, "t": 1}]}, "rand_seed1": {"d": true, "t": 3, "a": [{"a": "rand_seed1", "u": 44, "t": 2}, {"a": "sysvar_rand_seed1", "u": 24, "t": 1}]}, "rand_seed2": {"a": [{"a": "rand_seed2", "u": 44, "t": 2}]}, "range_alloc_block_size": {"d": true, "t": 3, "a": [{"a": "range_alloc_block_size", "u": 44, "t": 2}, {"a": "sysvar_range_alloc_block_size", "u": 24, "t": 1}]}, "read_buffer_size": {"d": true, "t": 3, "a": [{"a": "read_buffer_size", "u": 44, "t": 2}, {"a": "sysvar_read_buffer_size", "u": 24, "t": 1}]}, "read_only": {"d": true, "t": 2, "a": [{"a": "read_only", "u": 44, "t": 2}, {"a": "sysvar_read_only", "u": 24, "t": 1}]}, "read_rnd_buffer_size": {"d": true, "t": 3, "a": [{"a": "read_rnd_buffer_size", "u": 44, "t": 2}, {"a": "sysvar_read_rnd_buffer_size", "u": 24, "t": 1}]}, "require_secure_transport": {"d": true, "t": 2, "a": [{"a": "require_secure_transport", "u": 44, "t": 2}, {"a": "sysvar_require_secure_transport", "u": 24, "t": 1}]}, "rowid_merge_buff_size": {"d": true, "t": 3, "a": [{"a": "rowid_merge_buff_size", "u": 44, "t": 2}]}, "rpl_recovery_rank": {"a": [{"a": "rpl_recovery_rank", "u": 44, "t": 2}]}, "safe_show_database": {"d": true, "t": 2, "a": [{"a": "safe_show_database", "u": 44, "t": 2}]}, "secure_auth": {"d": true, "t": 2, "a": [{"a": "secure_auth", "u": 44, "t": 2}]}, "secure_file_priv": {"d": false, "a": [{"a": "secure_file_priv", "u": 44, "t": 2}, {"a": "sysvar_secure_file_priv", "u": 24, "t": 1}]}, "secure_timestamp": {"d": false, "t": 5, "a": [{"a": "secure_timestamp", "u": 44, "t": 2}]}, "session_track_schema": {"d": true, "t": 2, "a": [{"a": "session_track_schema", "u": 44, "t": 2}, {"a": "sysvar_session_track_schema", "u": 24, "t": 1}]}, "session_track_state_change": {"d": true, "t": 2, "a": [{"a": "session_track_state_change", "u": 44, "t": 2}, {"a": "sysvar_session_track_state_change", "u": 24, "t": 1}]}, "session_track_system_variables": {"d": true, "t": 1, "a": [{"a": "session_track_system_variables", "u": 44, "t": 2}, {"a": "sysvar_session_track_system_variables", "u": 24, "t": 1}]}, "session_track_transaction_info": {"d": true, "t": 5, "a": [{"a": "session_track_transaction_info", "u": 44, "t": 2}, {"a": "sysvar_session_track_transaction_info", "u": 24, "t": 1}]}, "shared_memory": {"d": false, "t": 2, "a": [{"a": "shared_memory", "u": 44, "t": 2}, {"a": "sysvar_shared_memory", "u": 24, "t": 1}]}, "shared_memory_base_name": {"d": false, "t": 1, "a": [{"a": "shared_memory_base_name", "u": 44, "t": 2}, {"a": "sysvar_shared_memory_base_name", "u": 24, "t": 1}]}, "skip_external_locking": {"d": false, "t": 2, "a": [{"a": "skip_external_locking", "u": 44, "t": 2}, {"a": "sysvar_skip_external_locking", "u": 24, "t": 1}]}, "skip_grant_tables": {"t": 2, "a": [{"a": "skip_grant_tables", "u": 44, "t": 2}, {"a": "option_mysqld_skip-grant-tables", "u": 25, "t": 1}]}, "skip_name_resolve": {"d": false, "t": 2, "a": [{"a": "skip_name_resolve", "u": 44, "t": 2}, {"a": "sysvar_skip_name_resolve", "u": 24, "t": 1}]}, "skip_networking": {"d": false, "t": 2, "a": [{"a": "skip_networking", "u": 44, "t": 2}, {"a": "sysvar_skip_networking", "u": 24, "t": 1}]}, "skip_show_database": {"d": false, "t": 2, "a": [{"a": "skip_show_database", "u": 44, "t": 2}, {"a": "option_mysqld_skip-show-database", "u": 25, "t": 1}, {"a": "sysvar_skip_show_database", "u": 24, "t": 1}]}, "slow_launch_time": {"d": true, "t": 3, "a": [{"a": "slow_launch_time", "u": 44, "t": 2}, {"a": "sysvar_slow_launch_time", "u": 24, "t": 1}]}, "slow_query_log": {"d": true, "t": 2, "a": [{"a": "slow_query_log", "u": 44, "t": 2}, {"a": "sysvar_slow_query_log", "u": 24, "t": 1}]}, "slow_query_log_file": {"d": true, "t": 8, "a": [{"a": "slow_query_log_file", "u": 44, "t": 2}, {"a": "sysvar_slow_query_log_file", "u": 24, "t": 1}]}, "socket": {"d": false, "t": 1, "a": [{"a": "socket", "u": 44, "t": 2}, {"a": "option_mysqld_socket", "u": 25, "t": 1}, {"a": "sysvar_socket", "u": 24, "t": 1}]}, "sort_buffer_size": {"d": true, "t": 3, "a": [{"a": "sort_buffer_size", "u": 44, "t": 2}, {"a": "sysvar_sort_buffer_size", "u": 24, "t": 1}]}, "sql_auto_is_null": {"d": true, "t": 2, "a": [{"a": "sql_auto_is_null", "u": 44, "t": 2}, {"a": "sysvar_sql_auto_is_null", "u": 24, "t": 1}]}, "sql_big_selects": {"d": true, "t": 2, "a": [{"a": "sql_big_selects", "u": 44, "t": 2}, {"a": "sysvar_sql_big_selects", "u": 24, "t": 1}]}, "sql_big_tables": {"d": true, "t": 2, "a": [{"a": "sql_big_tables", "u": 44, "t": 2}]}, "sql_buffer_result": {"d": true, "t": 2, "a": [{"a": "sql_buffer_result", "u": 44, "t": 2}, {"a": "sysvar_sql_buffer_result", "u": 24, "t": 1}]}, "sql_if_exists": {"d": true, "t": 2, "a": [{"a": "sql_if_exists", "u": 44, "t": 2}]}, "sql_log_off": {"d": true, "t": 2, "a": [{"a": "sql_log_off", "u": 44, "t": 2}, {"a": "sysvar_sql_log_off", "u": 24, "t": 1}]}, "sql_log_update": {"a": [{"a": "sql_log_update", "u": 44, "t": 2}]}, "sql_low_priority_updates": {"d": true, "t": 2, "a": [{"a": "sql_low_priority_updates", "u": 44, "t": 2}]}, "sql_max_join_size": {"a": [{"a": "sql_max_join_size", "u": 44, "t": 2}]}, "sql_mode": {"d": true, "t": 6, "a": [{"a": "sql_mode", "u": 44, "t": 2}, {"a": "option_mysqld_sql-mode", "u": 25, "t": 1}, {"a": "sysvar_sql_mode", "u": 24, "t": 1}]}, "sql_notes": {"d": true, "t": 2, "a": [{"a": "sql_notes", "u": 44, "t": 2}, {"a": "sysvar_sql_notes", "u": 24, "t": 1}]}, "sql_quote_show_create": {"d": true, "t": 2, "a": [{"a": "sql_quote_show_create", "u": 44, "t": 2}, {"a": "sysvar_sql_quote_show_create", "u": 24, "t": 1}]}, "sql_safe_updates": {"d": true, "t": 2, "a": [{"a": "sql_safe_updates", "u": 44, "t": 2}, {"a": "sysvar_sql_safe_updates", "u": 24, "t": 1}]}, "sql_select_limit": {"d": true, "t": 3, "a": [{"a": "sql_select_limit", "u": 44, "t": 2}, {"a": "sysvar_sql_select_limit", "u": 24, "t": 1}]}, "sql_warnings": {"d": true, "t": 2, "a": [{"a": "sql_warnings", "u": 44, "t": 2}, {"a": "sysvar_sql_warnings", "u": 24, "t": 1}]}, "storage_engine": {"a": [{"a": "storage_engine", "u": 44, "t": 2}]}, "standard_compliant_cte": {"d": true, "t": 2, "a": [{"a": "standard_compliant_cte", "u": 44, "t": 2}]}, "stored_program_cache": {"d": true, "t": 3, "a": [{"a": "stored_program_cache", "u": 44, "t": 2}, {"a": "sysvar_stored_program_cache", "u": 24, "t": 1}]}, "strict_password_validation": {"d": true, "t": 2, "a": [{"a": "strict_password_validation", "u": 44, "t": 2}]}, "sync_frm": {"d": true, "t": 2, "a": [{"a": "sync_frm", "u": 44, "t": 2}]}, "system_time_zone": {"d": false, "t": 1, "a": [{"a": "system_time_zone", "u": 44, "t": 2}, {"a": "sysvar_system_time_zone", "u": 24, "t": 1}]}, "table_definition_cache": {"d": true, "t": 3, "a": [{"a": "table_definition_cache", "u": 44, "t": 2}, {"a": "sysvar_table_definition_cache", "u": 24, "t": 1}]}, "table_lock_wait_timeout": {"d": true, "t": 3, "a": [{"a": "table_lock_wait_timeout", "u": 44, "t": 2}]}, "table_open_cache": {"d": true, "t": 3, "a": [{"a": "table_open_cache", "u": 44, "t": 2}, {"a": "sysvar_table_open_cache", "u": 24, "t": 1}]}, "table_open_cache_instances": {"d": false, "t": 3, "a": [{"a": "table_open_cache_instances", "u": 44, "t": 2}, {"a": "sysvar_table_open_cache_instances", "u": 24, "t": 1}]}, "table_type": {"a": [{"a": "table_type", "u": 44, "t": 2}]}, "tcp_keepalive_interval": {"d": true, "t": 3, "a": [{"a": "tcp_keepalive_interval", "u": 44, "t": 2}]}, "tcp_keepalive_probes": {"d": true, "t": 3, "a": [{"a": "tcp_keepalive_probes", "u": 44, "t": 2}]}, "tcp_keepalive_time": {"d": true, "t": 3, "a": [{"a": "tcp_keepalive_time", "u": 44, "t": 2}]}, "tcp_nodelay": {"d": true, "t": 2, "a": [{"a": "tcp_nodelay", "u": 44, "t": 2}]}, "thread_cache_size": {"d": true, "t": 3, "a": [{"a": "thread_cache_size", "u": 44, "t": 2}, {"a": "sysvar_thread_cache_size", "u": 24, "t": 1}]}, "thread_concurrency": {"d": false, "t": 3, "a": [{"a": "thread_concurrency", "u": 44, "t": 2}]}, "thread_stack": {"d": false, "t": 3, "a": [{"a": "thread_stack", "u": 44, "t": 2}, {"a": "sysvar_thread_stack", "u": 24, "t": 1}]}, "time_format": {"a": [{"a": "time_format", "u": 44, "t": 2}]}, "time_zone": {"d": true, "t": 1, "a": [{"a": "time_zone", "u": 44, "t": 2}, {"a": "sysvar_time_zone", "u": 24, "t": 1}]}, "timed_mutexes": {"d": true, "t": 2, "a": [{"a": "timed_mutexes", "u": 44, "t": 2}]}, "timestamp": {"d": true, "t": 4, "a": [{"a": "timestamp", "u": 44, "t": 2}, {"a": "sysvar_timestamp", "u": 24, "t": 1}]}, "tmp_disk_table_size": {"d": true, "t": 3, "a": [{"a": "tmp_disk_table_size", "u": 44, "t": 2}]}, "tmp_memory_table_size": {"a": [{"a": "tmp_memory_table_size", "u": 44, "t": 2}]}, "tmp_table_size": {"d": true, "t": 3, "a": [{"a": "tmp_table_size", "u": 44, "t": 2}, {"a": "sysvar_tmp_table_size", "u": 24, "t": 1}]}, "tmpdir": {"d": false, "t": 7, "a": [{"a": "tmpdir", "u": 44, "t": 2}, {"a": "option_mysqld_tmpdir", "u": 25, "t": 1}, {"a": "sysvar_tmpdir", "u": 24, "t": 1}]}, "transaction_alloc_block_size": {"d": true, "t": 3, "a": [{"a": "transaction_alloc_block_size", "u": 44, "t": 2}, {"a": "sysvar_transaction_alloc_block_size", "u": 24, "t": 1}]}, "transaction_prealloc_size": {"d": true, "t": 3, "a": [{"a": "transaction_prealloc_size", "u": 44, "t": 2}, {"a": "sysvar_transaction_prealloc_size", "u": 24, "t": 1}]}, "tx_isolation": {"d": true, "t": 5, "a": [{"a": "tx_isolation", "u": 44, "t": 2}]}, "tx_read_only": {"d": true, "t": 2, "a": [{"a": "tx_read_only", "u": 44, "t": 2}]}, "unique_checks": {"d": true, "t": 2, "a": [{"a": "unique_checks", "u": 44, "t": 2}, {"a": "sysvar_unique_checks", "u": 24, "t": 1}]}, "updatable_views_with_limit": {"d": true, "t": 2, "a": [{"a": "updatable_views_with_limit", "u": 44, "t": 2}, {"a": "sysvar_updatable_views_with_limit", "u": 24, "t": 1}]}, "use_stat_tables": {"d": true, "t": 5, "a": [{"a": "use_stat_tables", "u": 44, "t": 2}]}, "version": {"d": false, "t": 1, "a": [{"a": "version", "u": 44, "t": 2}]}, "version_comment": {"d": false, "t": 1, "a": [{"a": "version_comment", "u": 44, "t": 2}, {"a": "sysvar_version_comment", "u": 24, "t": 1}]}, "version_compile_machine": {"d": false, "t": 1, "a": [{"a": "version_compile_machine", "u": 44, "t": 2}, {"a": "sysvar_version_compile_machine", "u": 24, "t": 1}]}, "version_compile_os": {"d": false, "t": 1, "a": [{"a": "version_compile_os", "u": 44, "t": 2}, {"a": "sysvar_version_compile_os", "u": 24, "t": 1}]}, "version_malloc_library": {"d": false, "t": 1, "a": [{"a": "version_malloc_library", "u": 44, "t": 2}]}, "version_source_revision": {"d": false, "t": 1, "a": [{"a": "version_source_revision", "u": 44, "t": 2}]}, "wait_timeout": {"d": true, "t": 3, "a": [{"a": "wait_timeout", "u": 44, "t": 2}, {"a": "sysvar_wait_timeout", "u": 24, "t": 1}]}, "warning_count": {"d": false, "t": 3, "a": [{"a": "warning_count", "u": 44, "t": 2}]}, "simple_password_check_digits": {"d": true, "t": 3, "a": [{"a": "simple_password_check_digits", "u": 45, "t": 2}]}, "simple_password_check_letters_same_case": {"d": true, "t": 3, "a": [{"a": "simple_password_check_letters_same_case", "u": 45, "t": 2}]}, "simple_password_check_minimal_length": {"d": true, "t": 3, "a": [{"a": "simple_password_check_minimal_length", "u": 45, "t": 2}]}, "simple_password_check_other_characters": {"d": true, "t": 3, "a": [{"a": "simple_password_check_other_characters", "u": 45, "t": 2}]}, "simple_password_check": {"t": 5, "a": [{"a": "simple_password_check", "u": 45, "t": 2}]}, "Sphinx_error": {"t": 3, "a": [{"a": "sphinx_error", "u": 46, "t": 2}]}, "Sphinx_time": {"t": 3, "a": [{"a": "sphinx_time", "u": 46, "t": 2}]}, "Sphinx_total": {"t": 3, "a": [{"a": "sphinx_total", "u": 46, "t": 2}]}, "Sphinx_total_found": {"t": 3, "a": [{"a": "sphinx_total_found", "u": 46, "t": 2}]}, "Sphinx_word_count": {"t": 3, "a": [{"a": "sphinx_word_count", "u": 46, "t": 2}]}, "Sphinx_words": {"t": 3, "a": [{"a": "sphinx_words", "u": 46, "t": 2}]}, "Spider_direct_aggregate": {"t": 3, "a": [{"a": "spider_direct_aggregate", "u": 47, "t": 2}]}, "Spider_direct_delete": {"t": 3, "a": [{"a": "spider_direct_delete", "u": 47, "t": 2}]}, "Spider_direct_order_limit": {"t": 3, "a": [{"a": "spider_direct_order_limit", "u": 47, "t": 2}]}, "Spider_direct_update": {"t": 3, "a": [{"a": "spider_direct_update", "u": 47, "t": 2}]}, "Spider_mon_table_cache_version": {"t": 3, "a": [{"a": "spider_mon_table_cache_version", "u": 47, "t": 2}]}, "Spider_mon_table_cache_version_req": {"t": 3, "a": [{"a": "spider_mon_table_cache_version_req", "u": 47, "t": 2}]}, "Spider_parallel_search": {"t": 3, "a": [{"a": "spider_parallel_search", "u": 47, "t": 2}]}, "spider_auto_increment_mode": {"d": true, "t": 3, "a": [{"a": "spider_auto_increment_mode", "u": 48, "t": 2}]}, "spider_bgs_first_read": {"d": true, "t": 3, "a": [{"a": "spider_bgs_first_read", "u": 48, "t": 2}]}, "spider_bgs_mode": {"d": true, "t": 3, "a": [{"a": "spider_bgs_mode", "u": 48, "t": 2}]}, "spider_bgs_second_read": {"d": true, "t": 3, "a": [{"a": "spider_bgs_second_read", "u": 48, "t": 2}]}, "spider_bka_engine": {"d": true, "t": 1, "a": [{"a": "spider_bka_engine", "u": 48, "t": 2}]}, "spider_bka_mode": {"d": true, "t": 3, "a": [{"a": "spider_bka_mode", "u": 48, "t": 2}]}, "spider_bka_table_name_type": {"d": true, "t": 3, "a": [{"a": "spider_bka_table_name_type", "u": 48, "t": 2}]}, "spider_block_size": {"d": true, "t": 3, "a": [{"a": "spider_block_size", "u": 48, "t": 2}]}, "spider_buffer_size": {"d": true, "t": 3, "a": [{"a": "spider_buffer_size", "u": 48, "t": 2}]}, "spider_bulk_size": {"d": true, "t": 3, "a": [{"a": "spider_bulk_size", "u": 48, "t": 2}]}, "spider_bulk_update_mode": {"d": true, "t": 3, "a": [{"a": "spider_bulk_update_mode", "u": 48, "t": 2}]}, "spider_bulk_update_size": {"d": true, "t": 3, "a": [{"a": "spider_bulk_update_size", "u": 48, "t": 2}]}, "spider_casual_read": {"d": true, "t": 3, "a": [{"a": "spider_casual_read", "u": 48, "t": 2}]}, "spider_conn_recycle_mode": {"d": true, "t": 3, "a": [{"a": "spider_conn_recycle_mode", "u": 48, "t": 2}]}, "spider_conn_recycle_strict": {"d": true, "t": 3, "a": [{"a": "spider_conn_recycle_strict", "u": 48, "t": 2}]}, "spider_conn_wait_timeout": {"d": true, "t": 3, "a": [{"a": "spider_conn_wait_timeout", "u": 48, "t": 2}]}, "spider_connect_error_interval": {"d": true, "t": 3, "a": [{"a": "spider_connect_error_interval", "u": 48, "t": 2}]}, "spider_connect_mutex": {"d": true, "t": 2, "a": [{"a": "spider_connect_mutex", "u": 48, "t": 2}]}, "spider_connect_retry_count": {"d": true, "t": 3, "a": [{"a": "spider_connect_retry_count", "u": 48, "t": 2}]}, "spider_connect_retry_interval": {"d": true, "t": 3, "a": [{"a": "spider_connect_retry_interval", "u": 48, "t": 2}]}, "spider_connect_timeout": {"d": true, "t": 3, "a": [{"a": "spider_connect_timeout", "u": 48, "t": 2}]}, "spider_crd_bg_mode": {"d": true, "t": 3, "a": [{"a": "spider_crd_bg_mode", "u": 48, "t": 2}]}, "spider_crd_interval": {"d": true, "t": 3, "a": [{"a": "spider_crd_interval", "u": 48, "t": 2}]}, "spider_crd_mode": {"d": true, "t": 3, "a": [{"a": "spider_crd_mode", "u": 48, "t": 2}]}, "spider_crd_sync": {"d": true, "t": 3, "a": [{"a": "spider_crd_sync", "u": 48, "t": 2}]}, "spider_crd_type": {"d": true, "t": 3, "a": [{"a": "spider_crd_type", "u": 48, "t": 2}]}, "spider_crd_weight": {"d": true, "t": 3, "a": [{"a": "spider_crd_weight", "u": 48, "t": 2}]}, "spider_delete_all_rows_type": {"d": true, "t": 3, "a": [{"a": "spider_delete_all_rows_type", "u": 48, "t": 2}]}, "spider_direct_dup_insert": {"d": true, "t": 3, "a": [{"a": "spider_direct_dup_insert", "u": 48, "t": 2}]}, "spider_direct_order_limit": {"d": true, "t": 3, "a": [{"a": "spider_direct_order_limit", "u": 48, "t": 2}]}, "spider_dry_access": {"d": false, "t": 2, "a": [{"a": "spider_dry_access", "u": 48, "t": 2}]}, "spider_error_read_mode": {"d": true, "t": 3, "a": [{"a": "spider_error_read_mode", "u": 48, "t": 2}]}, "spider_error_write_mode": {"d": true, "t": 3, "a": [{"a": "spider_error_write_mode", "u": 48, "t": 2}]}, "spider_first_read": {"d": true, "t": 3, "a": [{"a": "spider_first_read", "u": 48, "t": 2}]}, "spider_force_commit": {"d": true, "t": 3, "a": [{"a": "spider_force_commit", "u": 48, "t": 2}]}, "spider_general_log": {"d": true, "t": 2, "a": [{"a": "spider_general_log", "u": 48, "t": 2}]}, "spider_index_hint_pushdown": {"d": true, "t": 2, "a": [{"a": "spider_index_hint_pushdown", "u": 48, "t": 2}]}, "spider_init_sql_alloc_size": {"d": true, "t": 3, "a": [{"a": "spider_init_sql_alloc_size", "u": 48, "t": 2}]}, "spider_internal_limit": {"d": true, "t": 3, "a": [{"a": "spider_internal_limit", "u": 48, "t": 2}]}, "spider_internal_offset": {"d": true, "t": 3, "a": [{"a": "spider_internal_offset", "u": 48, "t": 2}]}, "spider_internal_optimize": {"d": true, "t": 3, "a": [{"a": "spider_internal_optimize", "u": 48, "t": 2}]}, "spider_internal_optimize_local": {"a": [{"a": "spider_internal_optimize_local", "u": 48, "t": 2}]}, "spider_internal_sql_log_off": {"d": true, "a": [{"a": "spider_internal_sql_log_off", "u": 48, "t": 2}]}, "spider_internal_unlock": {"t": 2, "a": [{"a": "spider_internal_unlock", "u": 48, "t": 2}]}, "spider_internal_xa": {"d": true, "t": 2, "a": [{"a": "spider_internal_xa", "u": 48, "t": 2}]}, "spider_internal_xa_id_type": {"d": true, "t": 3, "a": [{"a": "spider_internal_xa_id_type", "u": 48, "t": 2}]}, "spider_internal_xa_snapshot": {"d": true, "t": 3, "a": [{"a": "spider_internal_xa_snapshot", "u": 48, "t": 2}]}, "spider_load_crd_at_startup": {"d": true, "t": 3, "a": [{"a": "spider_load_crd_at_startup", "u": 48, "t": 2}]}, "spider_load_sts_at_startup": {"d": true, "t": 2, "a": [{"a": "spider_load_sts_at_startup", "u": 48, "t": 2}]}, "spider_local_lock_table": {"d": true, "t": 2, "a": [{"a": "spider_local_lock_table", "u": 48, "t": 2}]}, "spider_lock_exchange": {"d": true, "t": 2, "a": [{"a": "spider_lock_exchange", "u": 48, "t": 2}]}, "spider_log_result_error_with_sql": {"d": true, "t": 3, "a": [{"a": "spider_log_result_error_with_sql", "u": 48, "t": 2}]}, "spider_log_result_errors": {"d": true, "t": 3, "a": [{"a": "spider_log_result_errors", "u": 48, "t": 2}]}, "spider_low_mem_read": {"t": 3, "a": [{"a": "spider_low_mem_read", "u": 48, "t": 2}]}, "spider_max_connections": {"d": true, "t": 3, "a": [{"a": "spider_max_connections", "u": 48, "t": 2}]}, "spider_max_order": {"d": true, "t": 3, "a": [{"a": "spider_max_order", "u": 48, "t": 2}]}, "spider_multi_split_read": {"d": true, "t": 3, "a": [{"a": "spider_multi_split_read", "u": 48, "t": 2}]}, "spider_net_read_timeout": {"d": true, "t": 3, "a": [{"a": "spider_net_read_timeout", "u": 48, "t": 2}]}, "spider_net_write_timeout": {"d": true, "t": 3, "a": [{"a": "spider_net_write_timeout", "u": 48, "t": 2}]}, "spider_ping_interval_at_trx_start": {"d": true, "t": 3, "a": [{"a": "spider_ping_interval_at_trx_start", "u": 48, "t": 2}]}, "spider_quick_mode": {"d": true, "t": 3, "a": [{"a": "spider_quick_mode", "u": 48, "t": 2}]}, "spider_quick_page_byte": {"d": true, "t": 3, "a": [{"a": "spider_quick_page_byte", "u": 48, "t": 2}]}, "spider_quick_page_size": {"d": true, "t": 3, "a": [{"a": "spider_quick_page_size", "u": 48, "t": 2}]}, "spider_read_only_mode": {"d": true, "t": 3, "a": [{"a": "spider_read_only_mode", "u": 48, "t": 2}]}, "spider_remote_access_charset": {"d": true, "t": 1, "a": [{"a": "spider_remote_access_charset", "u": 48, "t": 2}]}, "spider_remote_autocommit": {"d": true, "t": 3, "a": [{"a": "spider_remote_autocommit", "u": 48, "t": 2}]}, "spider_remote_default_database": {"d": true, "t": 1, "a": [{"a": "spider_remote_default_database", "u": 48, "t": 2}]}, "spider_remote_sql_log_off": {"d": true, "t": 3, "a": [{"a": "spider_remote_sql_log_off", "u": 48, "t": 2}]}, "spider_remote_time_zone": {"d": true, "t": 1, "a": [{"a": "spider_remote_time_zone", "u": 48, "t": 2}]}, "spider_remote_trx_isolation": {"d": true, "t": 3, "a": [{"a": "spider_remote_trx_isolation", "u": 48, "t": 2}]}, "spider_remote_wait_timeout": {"d": true, "t": 3, "a": [{"a": "spider_remote_wait_timeout", "u": 48, "t": 2}]}, "spider_reset_sql_alloc": {"d": true, "t": 3, "a": [{"a": "spider_reset_sql_alloc", "u": 48, "t": 2}]}, "spider_same_server_link": {"d": true, "t": 2, "a": [{"a": "spider_same_server_link", "u": 48, "t": 2}]}, "spider_second_read": {"d": true, "t": 3, "a": [{"a": "spider_second_read", "u": 48, "t": 2}]}, "spider_select_column_mode": {"d": true, "t": 3, "a": [{"a": "spider_select_column_mode", "u": 48, "t": 2}]}, "spider_selupd_lock_mode": {"d": true, "t": 3, "a": [{"a": "spider_selupd_lock_mode", "u": 48, "t": 2}]}, "spider_semi_split_read": {"d": true, "t": 3, "a": [{"a": "spider_semi_split_read", "u": 48, "t": 2}]}, "spider_semi_split_read_limit": {"d": true, "t": 3, "a": [{"a": "spider_semi_split_read_limit", "u": 48, "t": 2}]}, "spider_semi_table_lock": {"d": true, "t": 3, "a": [{"a": "spider_semi_table_lock", "u": 48, "t": 2}]}, "spider_semi_table_lock_connection": {"d": true, "t": 3, "a": [{"a": "spider_semi_table_lock_connection", "u": 48, "t": 2}]}, "spider_semi_trx": {"d": true, "t": 2, "a": [{"a": "spider_semi_trx", "u": 48, "t": 2}]}, "spider_semi_trx_isolation": {"d": true, "t": 3, "a": [{"a": "spider_semi_trx_isolation", "u": 48, "t": 2}]}, "spider_skip_default_condition": {"d": true, "t": 3, "a": [{"a": "spider_skip_default_condition", "u": 48, "t": 2}]}, "spider_skip_parallel_search": {"d": true, "t": 3, "a": [{"a": "spider_skip_parallel_search", "u": 48, "t": 2}]}, "spider_slave_trx_isolation": {"d": true, "t": 3, "a": [{"a": "spider_slave_trx_isolation", "u": 48, "t": 2}]}, "spider_split_read": {"d": true, "t": 3, "a": [{"a": "spider_split_read", "u": 48, "t": 2}]}, "spider_store_last_crd": {"d": true, "t": 3, "a": [{"a": "spider_store_last_crd", "u": 48, "t": 2}]}, "spider_store_last_sts": {"d": true, "t": 3, "a": [{"a": "spider_store_last_sts", "u": 48, "t": 2}]}, "spider_strict_group_by": {"d": true, "t": 3, "a": [{"a": "spider_strict_group_by", "u": 48, "t": 2}]}, "spider_sts_bg_mode": {"d": true, "t": 3, "a": [{"a": "spider_sts_bg_mode", "u": 48, "t": 2}]}, "spider_sts_interval": {"d": true, "t": 3, "a": [{"a": "spider_sts_interval", "u": 48, "t": 2}]}, "spider_sts_mode": {"d": true, "t": 3, "a": [{"a": "spider_sts_mode", "u": 48, "t": 2}]}, "spider_sts_sync": {"d": true, "t": 3, "a": [{"a": "spider_sts_sync", "u": 48, "t": 2}]}, "spider_support_xa": {"d": false, "t": 2, "a": [{"a": "spider_support_xa", "u": 48, "t": 2}]}, "spider_sync_autocommit": {"d": true, "t": 2, "a": [{"a": "spider_sync_autocommit", "u": 48, "t": 2}]}, "spider_sync_sql_mode": {"d": true, "t": 2, "a": [{"a": "spider_sync_sql_mode", "u": 48, "t": 2}]}, "spider_sync_time_zone": {"d": true, "t": 2, "a": [{"a": "spider_sync_time_zone", "u": 48, "t": 2}]}, "spider_sync_trx_isolation": {"d": true, "t": 2, "a": [{"a": "spider_sync_trx_isolation", "u": 48, "t": 2}]}, "spider_table_crd_thread_count": {"d": false, "t": 3, "a": [{"a": "spider_table_crd_thread_count", "u": 48, "t": 2}]}, "spider_table_init_error_interval": {"d": true, "t": 3, "a": [{"a": "spider_table_init_error_interval", "u": 48, "t": 2}]}, "spider_table_sts_thread_count": {"d": false, "t": 3, "a": [{"a": "spider_table_sts_thread_count", "u": 48, "t": 2}]}, "spider_udf_ct_bulk_insert_interval": {"d": true, "t": 3, "a": [{"a": "spider_udf_ct_bulk_insert_interval", "u": 48, "t": 2}]}, "spider_udf_ct_bulk_insert_rows": {"d": true, "t": 3, "a": [{"a": "spider_udf_ct_bulk_insert_rows", "u": 48, "t": 2}]}, "spider_udf_ds_bulk_insert_rows": {"d": true, "t": 3, "a": [{"a": "spider_udf_ds_bulk_insert_rows", "u": 48, "t": 2}]}, "spider_udf_ds_table_loop_mode": {"d": true, "t": 3, "a": [{"a": "spider_udf_ds_table_loop_mode", "u": 48, "t": 2}]}, "spider_udf_ds_use_real_table": {"d": true, "t": 3, "a": [{"a": "spider_udf_ds_use_real_table", "u": 48, "t": 2}]}, "spider_udf_table_lock_mutex_count": {"d": false, "t": 3, "a": [{"a": "spider_udf_table_lock_mutex_count", "u": 48, "t": 2}]}, "spider_udf_table_mon_mutex_count": {"d": false, "t": 3, "a": [{"a": "spider_udf_table_mon_mutex_count", "u": 48, "t": 2}]}, "spider_use_all_conns_snapshot": {"d": true, "t": 2, "a": [{"a": "spider_use_all_conns_snapshot", "u": 48, "t": 2}]}, "spider_use_cond_other_than_pk_for_update": {"d": true, "t": 3, "a": [{"a": "spider_use_cond_other_than_pk_for_update", "u": 48, "t": 2}]}, "spider_use_consistent_snapshot": {"d": true, "t": 2, "a": [{"a": "spider_use_consistent_snapshot", "u": 48, "t": 2}]}, "spider_use_default_database": {"d": true, "t": 2, "a": [{"a": "spider_use_default_database", "u": 48, "t": 2}]}, "spider_use_flash_logs": {"d": true, "t": 2, "a": [{"a": "spider_use_flash_logs", "u": 48, "t": 2}]}, "spider_use_handler": {"d": true, "t": 3, "a": [{"a": "spider_use_handler", "u": 48, "t": 2}]}, "spider_use_pushdown_udf": {"d": true, "t": 3, "a": [{"a": "spider_use_pushdown_udf", "u": 48, "t": 2}]}, "spider_use_snapshot_with_flush_tables": {"d": true, "t": 3, "a": [{"a": "spider_use_snapshot_with_flush_tables", "u": 48, "t": 2}]}, "spider_use_table_charset": {"d": true, "t": 3, "a": [{"a": "spider_use_table_charset", "u": 48, "t": 2}]}, "spider_version": {"d": false, "t": 1, "a": [{"a": "spider_version", "u": 48, "t": 2}]}, "spider_wait_timeout": {"d": true, "t": 3, "a": [{"a": "spider_wait_timeout", "u": 48, "t": 2}]}, "spider_xa_register_mode": {"d": true, "t": 3, "a": [{"a": "spider_xa_register_mode", "u": 48, "t": 2}]}, "sql_error_log_filename": {"d": false, "t": 1, "a": [{"a": "sql_error_log_filename", "u": 49, "t": 2}]}, "sql_error_log_rate": {"d": true, "t": 3, "a": [{"a": "sql_error_log_rate", "u": 49, "t": 2}]}, "sql_error_log_rotate": {"d": true, "t": 2, "a": [{"a": "sql_error_log_rotate", "u": 49, "t": 2}]}, "sql_error_log_rotations": {"d": false, "t": 3, "a": [{"a": "sql_error_log_rotations", "u": 49, "t": 2}]}, "sql_error_log_size_limit": {"d": false, "t": 3, "a": [{"a": "sql_error_log_size_limit", "u": 49, "t": 2}]}, "sql_error_log": {"t": 5, "a": [{"a": "sql_error_log", "u": 49, "t": 2}]}, "Ssl_accept_renegotiates": {"t": 3, "a": [{"a": "ssl_accept_renegotiates", "u": 50, "t": 2}]}, "Ssl_accepts": {"t": 3, "a": [{"a": "ssl_accepts", "u": 50, "t": 2}]}, "Ssl_callback_cache_hits": {"t": 3, "a": [{"a": "ssl_callback_cache_hits", "u": 50, "t": 2}]}, "Ssl_cipher": {"t": 1, "a": [{"a": "ssl_cipher", "u": 50, "t": 2}]}, "Ssl_cipher_list": {"t": 1, "a": [{"a": "ssl_cipher_list", "u": 50, "t": 2}]}, "Ssl_client_connects": {"t": 3, "a": [{"a": "ssl_client_connects", "u": 50, "t": 2}]}, "Ssl_connect_renegotiates": {"t": 3, "a": [{"a": "ssl_connect_renegotiates", "u": 50, "t": 2}]}, "Ssl_ctx_verify_depth": {"t": 3, "a": [{"a": "ssl_ctx_verify_depth", "u": 50, "t": 2}]}, "Ssl_ctx_verify_mode": {"t": 3, "a": [{"a": "ssl_ctx_verify_mode", "u": 50, "t": 2}]}, "Ssl_default_timeout": {"t": 3, "a": [{"a": "ssl_default_timeout", "u": 50, "t": 2}]}, "Ssl_finished_accepts": {"t": 3, "a": [{"a": "ssl_finished_accepts", "u": 50, "t": 2}]}, "Ssl_finished_connects": {"t": 3, "a": [{"a": "ssl_finished_connects", "u": 50, "t": 2}]}, "Ssl_server_not_after": {"t": 3, "a": [{"a": "ssl_server_not_after", "u": 50, "t": 2}]}, "Ssl_server_not_before": {"t": 3, "a": [{"a": "ssl_server_not_before", "u": 50, "t": 2}]}, "Ssl_session_cache_hits": {"t": 3, "a": [{"a": "ssl_session_cache_hits", "u": 50, "t": 2}]}, "Ssl_session_cache_misses": {"t": 3, "a": [{"a": "ssl_session_cache_misses", "u": 50, "t": 2}]}, "Ssl_session_cache_mode": {"t": 1, "a": [{"a": "ssl_session_cache_mode", "u": 50, "t": 2}]}, "Ssl_session_cache_overflows": {"t": 3, "a": [{"a": "ssl_session_cache_overflows", "u": 50, "t": 2}]}, "Ssl_session_cache_size": {"t": 3, "a": [{"a": "ssl_session_cache_size", "u": 50, "t": 2}]}, "Ssl_session_cache_timeouts": {"t": 3, "a": [{"a": "ssl_session_cache_timeouts", "u": 50, "t": 2}]}, "Ssl_sessions_reused": {"t": 3, "a": [{"a": "ssl_sessions_reused", "u": 50, "t": 2}]}, "Ssl_used_session_cache_entries": {"t": 3, "a": [{"a": "ssl_used_session_cache_entries", "u": 50, "t": 2}]}, "Ssl_verify_depth": {"t": 3, "a": [{"a": "ssl_verify_depth", "u": 50, "t": 2}]}, "Ssl_verify_mode": {"t": 3, "a": [{"a": "ssl_verify_mode", "u": 50, "t": 2}]}, "Ssl_version": {"t": 1, "a": [{"a": "ssl_version", "u": 50, "t": 2}]}, "have_openssl": {"d": false, "a": [{"a": "have_openssl", "u": 51, "t": 2}]}, "have_ssl": {"d": false, "t": 1, "a": [{"a": "have_ssl", "u": 51, "t": 2}, {"a": "sysvar_have_ssl", "u": 24, "t": 1}]}, "ssl_ca": {"t": 8, "a": [{"a": "ssl_ca", "u": 51, "t": 2}, {"a": "sysvar_ssl_ca", "u": 24, "t": 1}]}, "ssl_capath": {"t": 7, "a": [{"a": "ssl_capath", "u": 51, "t": 2}, {"a": "sysvar_ssl_capath", "u": 24, "t": 1}]}, "ssl_cert": {"t": 8, "a": [{"a": "ssl_cert", "u": 51, "t": 2}, {"a": "sysvar_ssl_cert", "u": 24, "t": 1}]}, "ssl_cipher": {"t": 1, "a": [{"a": "ssl_cipher", "u": 51, "t": 2}, {"a": "sysvar_ssl_cipher", "u": 24, "t": 1}]}, "ssl_crl": {"t": 8, "a": [{"a": "ssl_crl", "u": 51, "t": 2}, {"a": "sysvar_ssl_crl", "u": 24, "t": 1}]}, "ssl_crlpath": {"t": 7, "a": [{"a": "ssl_crlpath", "u": 51, "t": 2}, {"a": "sysvar_ssl_crlpath", "u": 24, "t": 1}]}, "ssl_key": {"a": [{"a": "ssl_key", "u": 51, "t": 2}, {"a": "sysvar_ssl_key", "u": 24, "t": 1}]}, "tls_version": {"a": [{"a": "tls_version", "u": 51, "t": 2}, {"a": "sysvar_tls_version", "u": 24, "t": 1}]}, "version_ssl_library": {"d": false, "t": 1, "a": [{"a": "version_ssl_library", "u": 51, "t": 2}]}, "column_compression_threshold": {"d": true, "t": 3, "a": [{"a": "column_compression_threshold", "u": 52, "t": 2}]}, "column_compression_zlib_level": {"d": true, "t": 3, "a": [{"a": "column_compression_zlib_level", "u": 52, "t": 2}]}, "column_compression_zlib_strategy": {"d": true, "t": 5, "a": [{"a": "column_compression_zlib_strategy", "u": 52, "t": 2}]}, "column_compression_zlib_wrap": {"d": true, "t": 2, "a": [{"a": "column_compression_zlib_wrap", "u": 52, "t": 2}]}, "Column_compressions": {"t": 3, "a": [{"a": "column_compressions", "u": 52, "t": 2}]}, "Column_decompressions": {"t": 3, "a": [{"a": "column_decompressions", "u": 52, "t": 2}]}, "extra_max_connections": {"d": true, "t": 3, "a": [{"a": "extra_max_connections", "u": 53, "t": 2}]}, "extra_port": {"d": false, "t": 3, "a": [{"a": "extra_port", "u": 53, "t": 2}]}, "thread_handling": {"d": false, "t": 5, "a": [{"a": "thread_handling", "u": 53, "t": 2}, {"a": "sysvar_thread_handling", "u": 24, "t": 1}]}, "thread_pool_dedicated_listener": {"d": false, "t": 2, "a": [{"a": "thread_pool_dedicated_listener", "u": 53, "t": 2}]}, "thread_pool_exact_stats": {"d": false, "t": 2, "a": [{"a": "thread_pool_exact_stats", "u": 53, "t": 2}]}, "thread_pool_idle_timeout": {"d": true, "t": 3, "a": [{"a": "thread_pool_idle_timeout", "u": 53, "t": 2}]}, "thread_pool_max_threads": {"d": true, "t": 3, "a": [{"a": "thread_pool_max_threads", "u": 53, "t": 2}]}, "thread_pool_min_threads": {"t": 3, "a": [{"a": "thread_pool_min_threads", "u": 53, "t": 2}]}, "thread_pool_oversubscribe": {"d": true, "t": 3, "a": [{"a": "thread_pool_oversubscribe", "u": 53, "t": 2}]}, "thread_pool_prio_kickup_timer": {"d": true, "t": 3, "a": [{"a": "thread_pool_prio_kickup_timer", "u": 53, "t": 2}, {"a": "sysvar_thread_pool_prio_kickup_timer", "u": 24, "t": 1}]}, "thread_pool_priority": {"t": 5, "a": [{"a": "thread_pool_priority", "u": 53, "t": 2}]}, "thread_pool_size": {"t": 3, "a": [{"a": "thread_pool_size", "u": 53, "t": 2}, {"a": "sysvar_thread_pool_size", "u": 24, "t": 1}]}, "thread_pool_stall_limit": {"d": true, "t": 3, "a": [{"a": "thread_pool_stall_limit", "u": 53, "t": 2}, {"a": "sysvar_thread_pool_stall_limit", "u": 24, "t": 1}]}, "Threadpool_idle_threads": {"t": 3, "a": [{"a": "threadpool_idle_threads", "u": 53, "t": 2}]}, "Threadpool_threads": {"t": 3, "a": [{"a": "threadpool_threads", "u": 53, "t": 2}]}, "Tokudb_basement_deserialization_fixed_key": {"t": 3, "a": [{"a": "tokudb_basement_deserialization_fixed_key", "u": 54, "t": 2}]}, "Tokudb_basement_deserialization_variable_key": {"t": 3, "a": [{"a": "tokudb_basement_deserialization_variable_key", "u": 54, "t": 2}]}, "Tokudb_basements_decompressed_for_write": {"t": 3, "a": [{"a": "tokudb_basements_decompressed_for_write", "u": 54, "t": 2}]}, "Tokudb_basements_decompressed_prefetch": {"t": 3, "a": [{"a": "tokudb_basements_decompressed_prefetch", "u": 54, "t": 2}]}, "Tokudb_basements_decompressed_prelocked_range": {"t": 3, "a": [{"a": "tokudb_basements_decompressed_prelocked_range", "u": 54, "t": 2}]}, "Tokudb_basements_decompressed_target_query": {"t": 3, "a": [{"a": "tokudb_basements_decompressed_target_query", "u": 54, "t": 2}]}, "Tokudb_basements_fetched_for_write": {"t": 3, "a": [{"a": "tokudb_basements_fetched_for_write", "u": 54, "t": 2}]}, "Tokudb_basements_fetched_for_write_bytes": {"a": [{"a": "tokudb_basements_fetched_for_write_bytes", "u": 54, "t": 2}]}, "Tokudb_basements_fetched_for_write_seconds": {"t": 3, "a": [{"a": "tokudb_basements_fetched_for_write_seconds", "u": 54, "t": 2}]}, "Tokudb_basements_fetched_prefetch": {"t": 3, "a": [{"a": "tokudb_basements_fetched_prefetch", "u": 54, "t": 2}]}, "Tokudb_basements_fetched_prefetch_bytes": {"a": [{"a": "tokudb_basements_fetched_prefetch_bytes", "u": 54, "t": 2}]}, "Tokudb_basements_fetched_prefetch_seconds": {"t": 3, "a": [{"a": "tokudb_basements_fetched_prefetch_seconds", "u": 54, "t": 2}]}, "Tokudb_basements_fetched_prelocked_range": {"t": 3, "a": [{"a": "tokudb_basements_fetched_prelocked_range", "u": 54, "t": 2}]}, "Tokudb_basements_fetched_prelocked_range_bytes": {"a": [{"a": "tokudb_basements_fetched_prelocked_range_bytes", "u": 54, "t": 2}]}, "Tokudb_basements_fetched_prelocked_range_seconds": {"t": 3, "a": [{"a": "tokudb_basements_fetched_prelocked_range_seconds", "u": 54, "t": 2}]}, "Tokudb_basements_fetched_target_query": {"t": 3, "a": [{"a": "tokudb_basements_fetched_target_query", "u": 54, "t": 2}]}, "Tokudb_basements_fetched_target_query_bytes": {"a": [{"a": "tokudb_basements_fetched_target_query_bytes", "u": 54, "t": 2}]}, "Tokudb_basements_fetched_target_query_seconds": {"t": 3, "a": [{"a": "tokudb_basements_fetched_target_query_seconds", "u": 54, "t": 2}]}, "Tokudb_broadcase_messages_injected_at_root": {"t": 3, "a": [{"a": "tokudb_broadcase_messages_injected_at_root", "u": 54, "t": 2}]}, "Tokudb_buffers_decompressed_for_write": {"t": 3, "a": [{"a": "tokudb_buffers_decompressed_for_write", "u": 54, "t": 2}]}, "Tokudb_buffers_decompressed_prefetch": {"t": 3, "a": [{"a": "tokudb_buffers_decompressed_prefetch", "u": 54, "t": 2}]}, "Tokudb_buffers_decompressed_prelocked_range": {"t": 3, "a": [{"a": "tokudb_buffers_decompressed_prelocked_range", "u": 54, "t": 2}]}, "Tokudb_buffers_decompressed_target_query": {"t": 3, "a": [{"a": "tokudb_buffers_decompressed_target_query", "u": 54, "t": 2}]}, "Tokudb_buffers_fetched_for_write": {"t": 3, "a": [{"a": "tokudb_buffers_fetched_for_write", "u": 54, "t": 2}]}, "Tokudb_buffers_fetched_for_write_bytes": {"a": [{"a": "tokudb_buffers_fetched_for_write_bytes", "u": 54, "t": 2}]}, "Tokudb_buffers_fetched_for_write_seconds": {"t": 3, "a": [{"a": "tokudb_buffers_fetched_for_write_seconds", "u": 54, "t": 2}]}, "Tokudb_buffers_fetched_prefetch": {"t": 3, "a": [{"a": "tokudb_buffers_fetched_prefetch", "u": 54, "t": 2}]}, "Tokudb_buffers_fetched_prefetch_bytes": {"a": [{"a": "tokudb_buffers_fetched_prefetch_bytes", "u": 54, "t": 2}]}, "Tokudb_buffers_fetched_prefetch_seconds": {"t": 3, "a": [{"a": "tokudb_buffers_fetched_prefetch_seconds", "u": 54, "t": 2}]}, "Tokudb_buffers_fetched_prelocked_range": {"t": 3, "a": [{"a": "tokudb_buffers_fetched_prelocked_range", "u": 54, "t": 2}]}, "Tokudb_buffers_fetched_prelocked_range_bytes": {"a": [{"a": "tokudb_buffers_fetched_prelocked_range_bytes", "u": 54, "t": 2}]}, "Tokudb_buffers_fetched_prelocked_range_seconds": {"t": 3, "a": [{"a": "tokudb_buffers_fetched_prelocked_range_seconds", "u": 54, "t": 2}]}, "Tokudb_buffers_fetched_target_query": {"t": 3, "a": [{"a": "tokudb_buffers_fetched_target_query", "u": 54, "t": 2}]}, "Tokudb_buffers_fetched_target_query_bytes": {"a": [{"a": "tokudb_buffers_fetched_target_query_bytes", "u": 54, "t": 2}]}, "Tokudb_buffers_fetched_target_query_seconds": {"t": 3, "a": [{"a": "tokudb_buffers_fetched_target_query_seconds", "u": 54, "t": 2}]}, "Tokudb_cachetable_cleaner_executions": {"t": 3, "a": [{"a": "tokudb_cachetable_cleaner_executions", "u": 54, "t": 2}]}, "Tokudb_cachetable_cleaner_iterations": {"t": 3, "a": [{"a": "tokudb_cachetable_cleaner_iterations", "u": 54, "t": 2}]}, "Tokudb_cachetable_cleaner_period": {"t": 3, "a": [{"a": "tokudb_cachetable_cleaner_period", "u": 54, "t": 2}]}, "Tokudb_cachetable_evictions": {"t": 3, "a": [{"a": "tokudb_cachetable_evictions", "u": 54, "t": 2}]}, "Tokudb_cachetable_long_wait_pressure_count": {"t": 3, "a": [{"a": "tokudb_cachetable_long_wait_pressure_count", "u": 54, "t": 2}]}, "Tokudb_cachetable_long_wait_pressure_time": {"t": 3, "a": [{"a": "tokudb_cachetable_long_wait_pressure_time", "u": 54, "t": 2}]}, "Tokudb_cachetable_miss": {"t": 3, "a": [{"a": "tokudb_cachetable_miss", "u": 54, "t": 2}]}, "Tokudb_cachetable_miss_time": {"t": 3, "a": [{"a": "tokudb_cachetable_miss_time", "u": 54, "t": 2}]}, "Tokudb_cachetable_prefetches": {"t": 3, "a": [{"a": "tokudb_cachetable_prefetches", "u": 54, "t": 2}]}, "Tokudb_cachetable_size_cachepressure": {"t": 9, "a": [{"a": "tokudb_cachetable_size_cachepressure", "u": 54, "t": 2}]}, "Tokudb_cachetable_size_cloned": {"t": 9, "a": [{"a": "tokudb_cachetable_size_cloned", "u": 54, "t": 2}]}, "Tokudb_cachetable_size_current": {"t": 9, "a": [{"a": "tokudb_cachetable_size_current", "u": 54, "t": 2}]}, "Tokudb_cachetable_size_leaf": {"t": 9, "a": [{"a": "tokudb_cachetable_size_leaf", "u": 54, "t": 2}]}, "Tokudb_cachetable_size_limit": {"t": 9, "a": [{"a": "tokudb_cachetable_size_limit", "u": 54, "t": 2}]}, "Tokudb_cachetable_size_nonleaf": {"t": 9, "a": [{"a": "tokudb_cachetable_size_nonleaf", "u": 54, "t": 2}]}, "Tokudb_cachetable_size_rollback": {"t": 9, "a": [{"a": "tokudb_cachetable_size_rollback", "u": 54, "t": 2}]}, "Tokudb_cachetable_size_writing": {"t": 9, "a": [{"a": "tokudb_cachetable_size_writing", "u": 54, "t": 2}]}, "Tokudb_cachetable_wait_pressure_count": {"t": 3, "a": [{"a": "tokudb_cachetable_wait_pressure_count", "u": 54, "t": 2}]}, "Tokudb_cachetable_wait_pressure_time": {"t": 3, "a": [{"a": "tokudb_cachetable_wait_pressure_time", "u": 54, "t": 2}]}, "Tokudb_checkpoint_begin_time": {"t": 3, "a": [{"a": "tokudb_checkpoint_begin_time", "u": 54, "t": 2}]}, "Tokudb_checkpoint_duration": {"t": 3, "a": [{"a": "tokudb_checkpoint_duration", "u": 54, "t": 2}]}, "Tokudb_checkpoint_duration_last": {"t": 3, "a": [{"a": "tokudb_checkpoint_duration_last", "u": 54, "t": 2}]}, "Tokudb_checkpoint_failed": {"t": 3, "a": [{"a": "tokudb_checkpoint_failed", "u": 54, "t": 2}]}, "Tokudb_checkpoint_last_began": {"a": [{"a": "tokudb_checkpoint_last_began", "u": 54, "t": 2}]}, "Tokudb_checkpoint_last_complete_began": {"a": [{"a": "tokudb_checkpoint_last_complete_began", "u": 54, "t": 2}]}, "Tokudb_checkpoint_last_complete_ended": {"a": [{"a": "tokudb_checkpoint_last_complete_ended", "u": 54, "t": 2}]}, "Tokudb_checkpoint_long_begin_count": {"t": 3, "a": [{"a": "tokudb_checkpoint_long_begin_count", "u": 54, "t": 2}]}, "Tokudb_checkpoint_long_begin_time": {"t": 3, "a": [{"a": "tokudb_checkpoint_long_begin_time", "u": 54, "t": 2}]}, "Tokudb_checkpoint_period": {"t": 3, "a": [{"a": "tokudb_checkpoint_period", "u": 54, "t": 2}]}, "Tokudb_checkpoint_taken": {"t": 3, "a": [{"a": "tokudb_checkpoint_taken", "u": 54, "t": 2}]}, "Tokudb_cursor_skip_deleted_leaf_entry": {"a": [{"a": "tokudb_cursor_skip_deleted_leaf_entry", "u": 54, "t": 2}]}, "Tokudb_db_closes": {"t": 3, "a": [{"a": "tokudb_db_closes", "u": 54, "t": 2}]}, "Tokudb_db_open_current": {"t": 3, "a": [{"a": "tokudb_db_open_current", "u": 54, "t": 2}]}, "Tokudb_db_open_max": {"t": 3, "a": [{"a": "tokudb_db_open_max", "u": 54, "t": 2}]}, "Tokudb_db_opens": {"t": 3, "a": [{"a": "tokudb_db_opens", "u": 54, "t": 2}]}, "Tokudb_descriptor_set": {"t": 3, "a": [{"a": "tokudb_descriptor_set", "u": 54, "t": 2}]}, "Tokudb_dictionary_broadcast_updates": {"t": 3, "a": [{"a": "tokudb_dictionary_broadcast_updates", "u": 54, "t": 2}]}, "Tokudb_dictionary_updates": {"t": 3, "a": [{"a": "tokudb_dictionary_updates", "u": 54, "t": 2}]}, "Tokudb_filesystem_fsync_num": {"t": 3, "a": [{"a": "tokudb_filesystem_fsync_num", "u": 54, "t": 2}]}, "Tokudb_filesystem_fsync_time": {"t": 3, "a": [{"a": "tokudb_filesystem_fsync_time", "u": 54, "t": 2}]}, "Tokudb_filesystem_long_fsync_num": {"t": 3, "a": [{"a": "tokudb_filesystem_long_fsync_num", "u": 54, "t": 2}]}, "Tokudb_filesystem_long_fsync_time": {"t": 3, "a": [{"a": "tokudb_filesystem_long_fsync_time", "u": 54, "t": 2}]}, "Tokudb_filesystem_threads_blocked_by_full_disk": {"t": 3, "a": [{"a": "tokudb_filesystem_threads_blocked_by_full_disk", "u": 54, "t": 2}]}, "Tokudb_leaf_compression_to_memory_seconds": {"t": 3, "a": [{"a": "tokudb_leaf_compression_to_memory_seconds", "u": 54, "t": 2}]}, "Tokudb_leaf_decompression_to_memory_seconds": {"t": 3, "a": [{"a": "tokudb_leaf_decompression_to_memory_seconds", "u": 54, "t": 2}]}, "Tokudb_leaf_deserialization_to_memory_seconds": {"t": 3, "a": [{"a": "tokudb_leaf_deserialization_to_memory_seconds", "u": 54, "t": 2}]}, "Tokudb_leaf_node_compression_ratio": {"a": [{"a": "tokudb_leaf_node_compression_ratio", "u": 54, "t": 2}]}, "Tokudb_leaf_node_full_evictions": {"t": 3, "a": [{"a": "tokudb_leaf_node_full_evictions", "u": 54, "t": 2}]}, "Tokudb_leaf_node_full_evictions_bytes": {"a": [{"a": "tokudb_leaf_node_full_evictions_bytes", "u": 54, "t": 2}]}, "Tokudb_leaf_node_partial_evictions": {"t": 3, "a": [{"a": "tokudb_leaf_node_partial_evictions", "u": 54, "t": 2}]}, "Tokudb_leaf_node_partial_evictions_bytes": {"a": [{"a": "tokudb_leaf_node_partial_evictions_bytes", "u": 54, "t": 2}]}, "Tokudb_leaf_nodes_created": {"t": 3, "a": [{"a": "tokudb_leaf_nodes_created", "u": 54, "t": 2}]}, "Tokudb_leaf_nodes_destroyed": {"t": 3, "a": [{"a": "tokudb_leaf_nodes_destroyed", "u": 54, "t": 2}]}, "Tokudb_leaf_nodes_flushed_checkpoint": {"t": 3, "a": [{"a": "tokudb_leaf_nodes_flushed_checkpoint", "u": 54, "t": 2}]}, "Tokudb_leaf_nodes_flushed_checkpoint_bytes": {"t": 9, "a": [{"a": "tokudb_leaf_nodes_flushed_checkpoint_bytes", "u": 54, "t": 2}]}, "Tokudb_leaf_nodes_flushed_checkpoint_seconds": {"t": 3, "a": [{"a": "tokudb_leaf_nodes_flushed_checkpoint_seconds", "u": 54, "t": 2}]}, "Tokudb_leaf_nodes_flushed_checkpoint_uncompressed_bytes": {"a": [{"a": "tokudb_leaf_nodes_flushed_checkpoint_uncompressed_bytes", "u": 54, "t": 2}]}, "Tokudb_leaf_nodes_flushed_not_checkpoint": {"t": 3, "a": [{"a": "tokudb_leaf_nodes_flushed_not_checkpoint", "u": 54, "t": 2}]}, "Tokudb_leaf_nodes_flushed_not_checkpoint_bytes": {"t": 9, "a": [{"a": "tokudb_leaf_nodes_flushed_not_checkpoint_bytes", "u": 54, "t": 2}]}, "Tokudb_leaf_nodes_flushed_not_checkpoint_seconds": {"t": 3, "a": [{"a": "tokudb_leaf_nodes_flushed_not_checkpoint_seconds", "u": 54, "t": 2}]}, "Tokudb_leaf_nodes_flushed_not_checkpoint_uncompressed_bytes": {"a": [{"a": "tokudb_leaf_nodes_flushed_not_checkpoint_uncompressed_bytes", "u": 54, "t": 2}]}, "Tokudb_leaf_serialization_to_memory_seconds": {"t": 3, "a": [{"a": "tokudb_leaf_serialization_to_memory_seconds", "u": 54, "t": 2}]}, "Tokudb_loader_num_created": {"t": 3, "a": [{"a": "tokudb_loader_num_created", "u": 54, "t": 2}]}, "Tokudb_loader_num_current": {"t": 3, "a": [{"a": "tokudb_loader_num_current", "u": 54, "t": 2}]}, "Tokudb_loader_num_max": {"t": 3, "a": [{"a": "tokudb_loader_num_max", "u": 54, "t": 2}]}, "Tokudb_locktree_escalation_num": {"t": 3, "a": [{"a": "tokudb_locktree_escalation_num", "u": 54, "t": 2}]}, "Tokudb_locktree_escalation_seconds": {"t": 3, "a": [{"a": "tokudb_locktree_escalation_seconds", "u": 54, "t": 2}]}, "Tokudb_locktree_latest_post_escalation_memory_size": {"t": 9, "a": [{"a": "tokudb_locktree_latest_post_escalation_memory_size", "u": 54, "t": 2}]}, "Tokudb_locktree_long_wait_count": {"t": 3, "a": [{"a": "tokudb_locktree_long_wait_count", "u": 54, "t": 2}]}, "Tokudb_locktree_long_wait_escalation_count": {"t": 3, "a": [{"a": "tokudb_locktree_long_wait_escalation_count", "u": 54, "t": 2}]}, "Tokudb_locktree_long_wait_escalation_time": {"t": 3, "a": [{"a": "tokudb_locktree_long_wait_escalation_time", "u": 54, "t": 2}]}, "Tokudb_locktree_long_wait_time": {"t": 3, "a": [{"a": "tokudb_locktree_long_wait_time", "u": 54, "t": 2}]}, "Tokudb_locktree_memory_size": {"t": 9, "a": [{"a": "tokudb_locktree_memory_size", "u": 54, "t": 2}]}, "Tokudb_locktree_memory_size_limit": {"t": 9, "a": [{"a": "tokudb_locktree_memory_size_limit", "u": 54, "t": 2}]}, "Tokudb_locktree_open_current": {"t": 3, "a": [{"a": "tokudb_locktree_open_current", "u": 54, "t": 2}]}, "Tokudb_locktree_pending_lock_requests": {"t": 3, "a": [{"a": "tokudb_locktree_pending_lock_requests", "u": 54, "t": 2}]}, "Tokudb_locktree_sto_eligible_num": {"t": 3, "a": [{"a": "tokudb_locktree_sto_eligible_num", "u": 54, "t": 2}]}, "Tokudb_locktree_sto_ended_num": {"t": 3, "a": [{"a": "tokudb_locktree_sto_ended_num", "u": 54, "t": 2}]}, "Tokudb_locktree_sto_ended_seconds": {"t": 3, "a": [{"a": "tokudb_locktree_sto_ended_seconds", "u": 54, "t": 2}]}, "Tokudb_locktree_timeout_count": {"t": 3, "a": [{"a": "tokudb_locktree_timeout_count", "u": 54, "t": 2}]}, "Tokudb_locktree_wait_count": {"t": 3, "a": [{"a": "tokudb_locktree_wait_count", "u": 54, "t": 2}]}, "Tokudb_locktree_wait_escalation_count": {"t": 3, "a": [{"a": "tokudb_locktree_wait_escalation_count", "u": 54, "t": 2}]}, "Tokudb_locktree_wait_escalation_time": {"t": 3, "a": [{"a": "tokudb_locktree_wait_escalation_time", "u": 54, "t": 2}]}, "Tokudb_locktree_wait_time": {"t": 3, "a": [{"a": "tokudb_locktree_wait_time", "u": 54, "t": 2}]}, "Tokudb_logger_wait_long": {"a": [{"a": "tokudb_logger_wait_long", "u": 54, "t": 2}]}, "Tokudb_logger_writes": {"t": 3, "a": [{"a": "tokudb_logger_writes", "u": 54, "t": 2}]}, "Tokudb_logger_writes_bytes": {"a": [{"a": "tokudb_logger_writes_bytes", "u": 54, "t": 2}]}, "Tokudb_logger_writes_seconds": {"t": 3, "a": [{"a": "tokudb_logger_writes_seconds", "u": 54, "t": 2}]}, "Tokudb_logger_writes_uncompressed_bytes": {"a": [{"a": "tokudb_logger_writes_uncompressed_bytes", "u": 54, "t": 2}]}, "Tokudb_mem_estimated_maximum_memory_footprint": {"a": [{"a": "tokudb_mem_estimated_maximum_memory_footprint", "u": 54, "t": 2}]}, "Tokudb_messages_flushed_from_h1_to_leaves_bytes": {"a": [{"a": "tokudb_messages_flushed_from_h1_to_leaves_bytes", "u": 54, "t": 2}]}, "Tokudb_messages_ignored_by_leaf_due_to_msn": {"t": 3, "a": [{"a": "tokudb_messages_ignored_by_leaf_due_to_msn", "u": 54, "t": 2}]}, "Tokudb_messages_in_trees_estimate_bytes": {"a": [{"a": "tokudb_messages_in_trees_estimate_bytes", "u": 54, "t": 2}]}, "Tokudb_messages_injected_at_root": {"t": 3, "a": [{"a": "tokudb_messages_injected_at_root", "u": 54, "t": 2}]}, "Tokudb_messages_injected_at_root_bytes": {"a": [{"a": "tokudb_messages_injected_at_root_bytes", "u": 54, "t": 2}]}, "Tokudb_nonleaf_compression_to_memory_seconds": {"t": 3, "a": [{"a": "tokudb_nonleaf_compression_to_memory_seconds", "u": 54, "t": 2}]}, "Tokudb_nonleaf_decompression_to_memory_seconds": {"t": 3, "a": [{"a": "tokudb_nonleaf_decompression_to_memory_seconds", "u": 54, "t": 2}]}, "Tokudb_nonleaf_deserialization_to_memory_seconds": {"t": 3, "a": [{"a": "tokudb_nonleaf_deserialization_to_memory_seconds", "u": 54, "t": 2}]}, "Tokudb_nonleaf_node_compression_ratio": {"a": [{"a": "tokudb_nonleaf_node_compression_ratio", "u": 54, "t": 2}]}, "Tokudb_nonleaf_node_full_evictions": {"t": 3, "a": [{"a": "tokudb_nonleaf_node_full_evictions", "u": 54, "t": 2}]}, "Tokudb_nonleaf_node_full_evictions_bytes": {"a": [{"a": "tokudb_nonleaf_node_full_evictions_bytes", "u": 54, "t": 2}]}, "Tokudb_nonleaf_node_partial_evictions": {"t": 3, "a": [{"a": "tokudb_nonleaf_node_partial_evictions", "u": 54, "t": 2}]}, "Tokudb_nonleaf_node_partial_evictions_bytes": {"a": [{"a": "tokudb_nonleaf_node_partial_evictions_bytes", "u": 54, "t": 2}]}, "Tokudb_nonleaf_nodes_created": {"t": 3, "a": [{"a": "tokudb_nonleaf_nodes_created", "u": 54, "t": 2}]}, "Tokudb_nonleaf_nodes_destroyed": {"t": 3, "a": [{"a": "tokudb_nonleaf_nodes_destroyed", "u": 54, "t": 2}]}, "Tokudb_nonleaf_nodes_flushed_to_disk_checkpoint": {"t": 3, "a": [{"a": "tokudb_nonleaf_nodes_flushed_to_disk_checkpoint", "u": 54, "t": 2}]}, "Tokudb_nonleaf_nodes_flushed_to_disk_checkpoint_bytes": {"t": 9, "a": [{"a": "tokudb_nonleaf_nodes_flushed_to_disk_checkpoint_bytes", "u": 54, "t": 2}]}, "Tokudb_nonleaf_nodes_flushed_to_disk_checkpoint_seconds": {"t": 3, "a": [{"a": "tokudb_nonleaf_nodes_flushed_to_disk_checkpoint_seconds", "u": 54, "t": 2}]}, "Tokudb_nonleaf_nodes_flushed_to_disk_checkpoint_uncompressed_bytes": {"a": [{"a": "tokudb_nonleaf_nodes_flushed_to_disk_checkpoint_uncompressed_bytes", "u": 54, "t": 2}]}, "Tokudb_nonleaf_nodes_flushed_to_disk_not_checkpoint": {"t": 3, "a": [{"a": "tokudb_nonleaf_nodes_flushed_to_disk_not_checkpoint", "u": 54, "t": 2}]}, "Tokudb_nonleaf_nodes_flushed_to_disk_not_checkpoint_bytes": {"t": 9, "a": [{"a": "tokudb_nonleaf_nodes_flushed_to_disk_not_checkpoint_bytes", "u": 54, "t": 2}]}, "Tokudb_nonleaf_nodes_flushed_to_disk_not_checkpoint_seconds": {"t": 3, "a": [{"a": "tokudb_nonleaf_nodes_flushed_to_disk_not_checkpoint_seconds", "u": 54, "t": 2}]}, "Tokudb_nonleaf_nodes_flushed_to_disk_not_checkpoint_uncompressed_bytes": {"a": [{"a": "tokudb_nonleaf_nodes_flushed_to_disk_not_checkpoint_uncompressed_bytes", "u": 54, "t": 2}]}, "Tokudb_nonleaf_serialization_to_memory_seconds": {"t": 3, "a": [{"a": "tokudb_nonleaf_serialization_to_memory_seconds", "u": 54, "t": 2}]}, "Tokudb_overall_node_compression_ratio": {"a": [{"a": "tokudb_overall_node_compression_ratio", "u": 54, "t": 2}]}, "Tokudb_pivots_fetched_for_query": {"t": 3, "a": [{"a": "tokudb_pivots_fetched_for_query", "u": 54, "t": 2}]}, "Tokudb_pivots_fetched_for_query_bytes": {"t": 9, "a": [{"a": "tokudb_pivots_fetched_for_query_bytes", "u": 54, "t": 2}]}, "Tokudb_pivots_fetched_for_query_seconds": {"t": 3, "a": [{"a": "tokudb_pivots_fetched_for_query_seconds", "u": 54, "t": 2}]}, "Tokudb_pivots_fetched_for_prefetch": {"t": 3, "a": [{"a": "tokudb_pivots_fetched_for_prefetch", "u": 54, "t": 2}]}, "Tokudb_pivots_fetched_for_prefetch_bytes": {"t": 9, "a": [{"a": "tokudb_pivots_fetched_for_prefetch_bytes", "u": 54, "t": 2}]}, "Tokudb_pivots_fetched_for_prefetch_seconds": {"t": 3, "a": [{"a": "tokudb_pivots_fetched_for_prefetch_seconds", "u": 54, "t": 2}]}, "Tokudb_pivots_fetched_for_write": {"t": 3, "a": [{"a": "tokudb_pivots_fetched_for_write", "u": 54, "t": 2}]}, "Tokudb_pivots_fetched_for_write_bytes": {"t": 9, "a": [{"a": "tokudb_pivots_fetched_for_write_bytes", "u": 54, "t": 2}]}, "Tokudb_pivots_fetched_for_write_seconds": {"t": 3, "a": [{"a": "tokudb_pivots_fetched_for_write_seconds", "u": 54, "t": 2}]}, "Tokudb_promotion_h1_roots_injected_into": {"t": 3, "a": [{"a": "tokudb_promotion_h1_roots_injected_into", "u": 54, "t": 2}]}, "Tokudb_promotion_injections_at_depth_0": {"t": 3, "a": [{"a": "tokudb_promotion_injections_at_depth_0", "u": 54, "t": 2}]}, "Tokudb_promotion_injections_at_depth_1": {"t": 3, "a": [{"a": "tokudb_promotion_injections_at_depth_1", "u": 54, "t": 2}]}, "Tokudb_promotion_injections_at_depth_2": {"t": 3, "a": [{"a": "tokudb_promotion_injections_at_depth_2", "u": 54, "t": 2}]}, "Tokudb_promotion_injections_at_depth_3": {"t": 3, "a": [{"a": "tokudb_promotion_injections_at_depth_3", "u": 54, "t": 2}]}, "Tokudb_promotion_injections_lower_than_depth_3": {"t": 3, "a": [{"a": "tokudb_promotion_injections_lower_than_depth_3", "u": 54, "t": 2}]}, "Tokudb_promotion_leaf_roots_injected_into": {"t": 3, "a": [{"a": "tokudb_promotion_leaf_roots_injected_into", "u": 54, "t": 2}]}, "Tokudb_promotion_roots_split": {"t": 3, "a": [{"a": "tokudb_promotion_roots_split", "u": 54, "t": 2}]}, "Tokudb_promotion_stopped_after_locking_child": {"t": 3, "a": [{"a": "tokudb_promotion_stopped_after_locking_child", "u": 54, "t": 2}]}, "Tokudb_promotion_stopped_at_height_1": {"t": 3, "a": [{"a": "tokudb_promotion_stopped_at_height_1", "u": 54, "t": 2}]}, "Tokudb_promotion_stopped_child_locked_or_not_in_memory": {"t": 3, "a": [{"a": "tokudb_promotion_stopped_child_locked_or_not_in_memory", "u": 54, "t": 2}]}, "Tokudb_promotion_stopped_child_not_fully_in_memory": {"t": 3, "a": [{"a": "tokudb_promotion_stopped_child_not_fully_in_memory", "u": 54, "t": 2}]}, "Tokudb_promotion_stopped_nonempty_buffer": {"t": 3, "a": [{"a": "tokudb_promotion_stopped_nonempty_buffer", "u": 54, "t": 2}]}, "Tokudb_txn_aborts": {"t": 3, "a": [{"a": "tokudb_txn_aborts", "u": 54, "t": 2}]}, "Tokudb_txn_begin": {"t": 3, "a": [{"a": "tokudb_txn_begin", "u": 54, "t": 2}]}, "Tokudb_txn_begin_read_only": {"t": 3, "a": [{"a": "tokudb_txn_begin_read_only", "u": 54, "t": 2}]}, "Tokudb_txn_commits": {"t": 3, "a": [{"a": "tokudb_txn_commits", "u": 54, "t": 2}]}, "tokudb_alter_print_error": {"d": true, "t": 2, "a": [{"a": "tokudb_alter_print_error", "u": 55, "t": 2}]}, "tokudb_analyze_time": {"d": true, "t": 3, "a": [{"a": "tokudb_analyze_time", "u": 55, "t": 2}]}, "tokudb_block_size": {"d": true, "t": 3, "a": [{"a": "tokudb_block_size", "u": 55, "t": 2}]}, "tokudb_bulk_fetch": {"d": true, "t": 2, "a": [{"a": "tokudb_bulk_fetch", "u": 55, "t": 2}]}, "tokudb_cache_size": {"d": false, "t": 3, "a": [{"a": "tokudb_cache_size", "u": 55, "t": 2}]}, "tokudb_check_jemalloc": {"d": true, "t": 3, "a": [{"a": "tokudb_check_jemalloc", "u": 55, "t": 2}]}, "tokudb_checkpoint_lock": {"d": true, "t": 2, "a": [{"a": "tokudb_checkpoint_lock", "u": 55, "t": 2}]}, "tokudb_checkpoint_on_flush_logs": {"d": true, "t": 2, "a": [{"a": "tokudb_checkpoint_on_flush_logs", "u": 55, "t": 2}]}, "tokudb_checkpointing_period": {"d": true, "t": 3, "a": [{"a": "tokudb_checkpointing_period", "u": 55, "t": 2}]}, "tokudb_cleaner_iterations": {"d": true, "t": 3, "a": [{"a": "tokudb_cleaner_iterations", "u": 55, "t": 2}]}, "tokudb_cleaner_period": {"d": true, "t": 3, "a": [{"a": "tokudb_cleaner_period", "u": 55, "t": 2}]}, "tokudb_commit_sync": {"d": true, "t": 2, "a": [{"a": "tokudb_commit_sync", "u": 55, "t": 2}]}, "tokudb_create_index_online": {"d": true, "t": 2, "a": [{"a": "tokudb_create_index_online", "u": 55, "t": 2}]}, "tokudb_data_dir": {"d": false, "t": 1, "a": [{"a": "tokudb_data_dir", "u": 55, "t": 2}]}, "tokudb_debug": {"d": true, "t": 3, "a": [{"a": "tokudb_debug", "u": 55, "t": 2}]}, "tokudb_directio": {"d": false, "t": 2, "a": [{"a": "tokudb_directio", "u": 55, "t": 2}]}, "tokudb_disable_hot_alter": {"d": true, "t": 2, "a": [{"a": "tokudb_disable_hot_alter", "u": 55, "t": 2}]}, "tokudb_disable_prefetching": {"d": true, "t": 2, "a": [{"a": "tokudb_disable_prefetching", "u": 55, "t": 2}]}, "tokudb_disable_slow_alter": {"d": true, "t": 2, "a": [{"a": "tokudb_disable_slow_alter", "u": 55, "t": 2}]}, "tokudb_empty_scan": {"d": true, "t": 5, "a": [{"a": "tokudb_empty_scan", "u": 55, "t": 2}]}, "tokudb_fs_reserve_percent": {"d": false, "t": 3, "a": [{"a": "tokudb_fs_reserve_percent", "u": 55, "t": 2}]}, "tokudb_fsync_log_period": {"d": true, "t": 3, "a": [{"a": "tokudb_fsync_log_period", "u": 55, "t": 2}]}, "tokudb_hide_default_row_format": {"d": true, "t": 2, "a": [{"a": "tokudb_hide_default_row_format", "u": 55, "t": 2}]}, "tokudb_killed_time": {"d": true, "t": 3, "a": [{"a": "tokudb_killed_time", "u": 55, "t": 2}]}, "tokudb_last_lock_timeout": {"d": true, "t": 1, "a": [{"a": "tokudb_last_lock_timeout", "u": 55, "t": 2}]}, "tokudb_load_save_space": {"d": true, "t": 2, "a": [{"a": "tokudb_load_save_space", "u": 55, "t": 2}]}, "tokudb_loader_memory_size": {"d": true, "t": 3, "a": [{"a": "tokudb_loader_memory_size", "u": 55, "t": 2}]}, "tokudb_lock_timeout": {"d": true, "t": 3, "a": [{"a": "tokudb_lock_timeout", "u": 55, "t": 2}]}, "tokudb_lock_timeout_debug": {"d": true, "t": 3, "a": [{"a": "tokudb_lock_timeout_debug", "u": 55, "t": 2}]}, "tokudb_log_dir": {"d": false, "t": 1, "a": [{"a": "tokudb_log_dir", "u": 55, "t": 2}]}, "tokudb_max_lock_memory": {"d": false, "t": 3, "a": [{"a": "tokudb_max_lock_memory", "u": 55, "t": 2}]}, "tokudb_optimize_index_fraction": {"d": true, "t": 3, "a": [{"a": "tokudb_optimize_index_fraction", "u": 55, "t": 2}]}, "tokudb_optimize_index_name": {"d": true, "t": 1, "a": [{"a": "tokudb_optimize_index_name", "u": 55, "t": 2}]}, "tokudb_optimize_throttle": {"d": true, "t": 3, "a": [{"a": "tokudb_optimize_throttle", "u": 55, "t": 2}]}, "tokudb_pk_insert_mode": {"d": true, "t": 5, "a": [{"a": "tokudb_pk_insert_mode", "u": 55, "t": 2}]}, "tokudb_prelock_empty": {"d": true, "t": 2, "a": [{"a": "tokudb_prelock_empty", "u": 55, "t": 2}]}, "tokudb_read_block_size": {"d": true, "t": 3, "a": [{"a": "tokudb_read_block_size", "u": 55, "t": 2}]}, "tokudb_read_buf_size": {"d": true, "t": 3, "a": [{"a": "tokudb_read_buf_size", "u": 55, "t": 2}]}, "tokudb_read_status_frequency": {"d": true, "t": 3, "a": [{"a": "tokudb_read_status_frequency", "u": 55, "t": 2}]}, "tokudb_row_format": {"d": true, "t": 5, "a": [{"a": "tokudb_row_format", "u": 55, "t": 2}]}, "tokudb_rpl_check_readonly": {"d": true, "t": 2, "a": [{"a": "tokudb_rpl_check_readonly", "u": 55, "t": 2}]}, "tokudb_rpl_lookup_rows": {"d": true, "t": 2, "a": [{"a": "tokudb_rpl_lookup_rows", "u": 55, "t": 2}]}, "tokudb_rpl_lookup_rows_delay": {"d": true, "t": 3, "a": [{"a": "tokudb_rpl_lookup_rows_delay", "u": 55, "t": 2}]}, "tokudb_rpl_unique_checks": {"d": true, "t": 2, "a": [{"a": "tokudb_rpl_unique_checks", "u": 55, "t": 2}]}, "tokudb_rpl_unique_checks_delay": {"d": true, "t": 3, "a": [{"a": "tokudb_rpl_unique_checks_delay", "u": 55, "t": 2}]}, "tokudb_support_xa": {"d": true, "t": 2, "a": [{"a": "tokudb_support_xa", "u": 55, "t": 2}]}, "tokudb_tmp_dir": {"d": false, "t": 7, "a": [{"a": "tokudb_tmp_dir", "u": 55, "t": 2}]}, "tokudb_version": {"d": false, "t": 1, "a": [{"a": "tokudb_version", "u": 55, "t": 2}]}, "tokudb_write_status_frequency": {"d": true, "t": 3, "a": [{"a": "tokudb_write_status_frequency", "u": 55, "t": 2}]}, "userstat": {"d": true, "t": 2, "a": [{"a": "userstat", "u": 56, "t": 2}]}, "system_versioning_alter_history": {"d": true, "t": 5, "a": [{"a": "system_versioning_alter_history", "u": 57, "t": 2}]}, "system_versioning_asof": {"d": true, "t": 1, "a": [{"a": "system_versioning_asof", "u": 57, "t": 2}]}, "system_versioning_innodb_algorithm_simple": {"d": true, "t": 2, "a": [{"a": "system_versioning_innodb_algorithm_simple", "u": 57, "t": 2}]}, "system_versioning_insert_history": {"d": true, "t": 2, "a": [{"a": "system_versioning_insert_history", "u": 57, "t": 2}]}, "Innodb_adaptive_hash_cells": {"t": 3, "a": [{"a": "innodb_adaptive_hash_cells", "u": 58, "t": 2}]}, "Innodb_adaptive_hash_hash_searches": {"t": 3, "a": [{"a": "innodb_adaptive_hash_hash_searches", "u": 58, "t": 2}]}, "Innodb_adaptive_hash_heap_buffers": {"t": 3, "a": [{"a": "innodb_adaptive_hash_heap_buffers", "u": 58, "t": 2}]}, "Innodb_adaptive_hash_non_hash_searches": {"t": 3, "a": [{"a": "innodb_adaptive_hash_non_hash_searches", "u": 58, "t": 2}]}, "Innodb_available_undo_logs": {"t": 3, "a": [{"a": "innodb_available_undo_logs", "u": 58, "t": 2}]}, "Innodb_background_log_sync": {"t": 3, "a": [{"a": "innodb_background_log_sync", "u": 58, "t": 2}]}, "Innodb_buffer_pool_bytes_data": {"t": 3, "a": [{"a": "innodb_buffer_pool_bytes_data", "u": 58, "t": 2}]}, "Innodb_buffer_pool_bytes_dirty": {"t": 3, "a": [{"a": "innodb_buffer_pool_bytes_dirty", "u": 58, "t": 2}]}, "Innodb_buffer_pool_dump_status": {"t": 1, "a": [{"a": "innodb_buffer_pool_dump_status", "u": 58, "t": 2}]}, "Innodb_buffer_pool_load_incomplete": {"t": 2, "a": [{"a": "innodb_buffer_pool_load_incomplete", "u": 58, "t": 2}]}, "Innodb_buffer_pool_load_status": {"t": 1, "a": [{"a": "innodb_buffer_pool_load_status", "u": 58, "t": 2}]}, "Innodb_buffer_pool_pages_data": {"t": 3, "a": [{"a": "innodb_buffer_pool_pages_data", "u": 58, "t": 2}]}, "Innodb_buffer_pool_pages_dirty": {"t": 3, "a": [{"a": "innodb_buffer_pool_pages_dirty", "u": 58, "t": 2}]}, "Innodb_buffer_pool_pages_flushed": {"t": 3, "a": [{"a": "innodb_buffer_pool_pages_flushed", "u": 58, "t": 2}]}, "Innodb_buffer_pool_pages_LRU_flushed": {"t": 3, "a": [{"a": "innodb_buffer_pool_pages_lru_flushed", "u": 58, "t": 2}]}, "Innodb_buffer_pool_pages_LRU_freed": {"t": 3, "a": [{"a": "innodb_buffer_pool_pages_lru_freed", "u": 58, "t": 2}]}, "Innodb_buffer_pool_pages_free": {"t": 3, "a": [{"a": "innodb_buffer_pool_pages_free", "u": 58, "t": 2}]}, "Innodb_buffer_pool_pages_made_not_young": {"t": 3, "a": [{"a": "innodb_buffer_pool_pages_made_not_young", "u": 58, "t": 2}]}, "Innodb_buffer_pool_pages_made_young": {"t": 3, "a": [{"a": "innodb_buffer_pool_pages_made_young", "u": 58, "t": 2}]}, "Innodb_buffer_pool_pages_misc": {"t": 3, "a": [{"a": "innodb_buffer_pool_pages_misc", "u": 58, "t": 2}]}, "Innodb_buffer_pool_pages_old": {"t": 3, "a": [{"a": "innodb_buffer_pool_pages_old", "u": 58, "t": 2}]}, "Innodb_buffer_pool_pages_total": {"t": 3, "a": [{"a": "innodb_buffer_pool_pages_total", "u": 58, "t": 2}]}, "Innodb_buffer_pool_read_ahead": {"t": 3, "a": [{"a": "innodb_buffer_pool_read_ahead", "u": 58, "t": 2}]}, "Innodb_buffer_pool_read_ahead_evicted": {"t": 3, "a": [{"a": "innodb_buffer_pool_read_ahead_evicted", "u": 58, "t": 2}]}, "Innodb_buffer_pool_read_ahead_rnd": {"t": 3, "a": [{"a": "innodb_buffer_pool_read_ahead_rnd", "u": 58, "t": 2}]}, "Innodb_buffer_pool_read_requests": {"t": 3, "a": [{"a": "innodb_buffer_pool_read_requests", "u": 58, "t": 2}]}, "Innodb_buffer_pool_reads": {"t": 3, "a": [{"a": "innodb_buffer_pool_reads", "u": 58, "t": 2}]}, "Innodb_buffer_pool_resize_status": {"t": 3, "a": [{"a": "innodb_buffer_pool_resize_status", "u": 58, "t": 2}]}, "Innodb_buffer_pool_wait_free": {"t": 3, "a": [{"a": "innodb_buffer_pool_wait_free", "u": 58, "t": 2}]}, "Innodb_buffer_pool_write_requests": {"t": 3, "a": [{"a": "innodb_buffer_pool_write_requests", "u": 58, "t": 2}]}, "Innodb_buffered_aio_submitted": {"t": 3, "a": [{"a": "innodb_buffered_aio_submitted", "u": 58, "t": 2}]}, "Innodb_checkpoint_age": {"t": 3, "a": [{"a": "innodb_checkpoint_age", "u": 58, "t": 2}]}, "Innodb_checkpoint_max_age": {"t": 3, "a": [{"a": "innodb_checkpoint_max_age", "u": 58, "t": 2}]}, "Innodb_checkpoint_target_age": {"t": 3, "a": [{"a": "innodb_checkpoint_target_age", "u": 58, "t": 2}]}, "Innodb_current_row_locks": {"t": 3, "a": [{"a": "innodb_current_row_locks", "u": 58, "t": 2}]}, "Innodb_data_fsyncs": {"t": 3, "a": [{"a": "innodb_data_fsyncs", "u": 58, "t": 2}]}, "Innodb_data_pending_fsyncs": {"t": 3, "a": [{"a": "innodb_data_pending_fsyncs", "u": 58, "t": 2}]}, "Innodb_data_pending_reads": {"t": 3, "a": [{"a": "innodb_data_pending_reads", "u": 58, "t": 2}]}, "Innodb_data_pending_writes": {"t": 3, "a": [{"a": "innodb_data_pending_writes", "u": 58, "t": 2}]}, "Innodb_data_read": {"t": 3, "a": [{"a": "innodb_data_read", "u": 58, "t": 2}]}, "Innodb_data_reads": {"t": 3, "a": [{"a": "innodb_data_reads", "u": 58, "t": 2}]}, "Innodb_data_writes": {"t": 3, "a": [{"a": "innodb_data_writes", "u": 58, "t": 2}]}, "Innodb_data_written": {"t": 3, "a": [{"a": "innodb_data_written", "u": 58, "t": 2}]}, "Innodb_dblwr_pages_written": {"t": 3, "a": [{"a": "innodb_dblwr_pages_written", "u": 58, "t": 2}]}, "Innodb_dblwr_writes": {"t": 3, "a": [{"a": "innodb_dblwr_writes", "u": 58, "t": 2}]}, "Innodb_deadlocks": {"t": 3, "a": [{"a": "innodb_deadlocks", "u": 58, "t": 2}]}, "Innodb_defragment_compression_failures": {"t": 3, "a": [{"a": "innodb_defragment_compression_failures", "u": 58, "t": 2}]}, "Innodb_defragment_count": {"t": 3, "a": [{"a": "innodb_defragment_count", "u": 58, "t": 2}]}, "Innodb_defragment_failures": {"t": 3, "a": [{"a": "innodb_defragment_failures", "u": 58, "t": 2}]}, "Innodb_dict_tables": {"t": 3, "a": [{"a": "innodb_dict_tables", "u": 58, "t": 2}]}, "Innodb_encryption_n_merge_blocks_decrypted": {"t": 3, "a": [{"a": "innodb_encryption_n_merge_blocks_decrypted", "u": 58, "t": 2}]}, "Innodb_encryption_n_merge_blocks_encrypted": {"t": 3, "a": [{"a": "innodb_encryption_n_merge_blocks_encrypted", "u": 58, "t": 2}]}, "Innodb_encryption_n_rowlog_blocks_decrypted": {"t": 3, "a": [{"a": "innodb_encryption_n_rowlog_blocks_decrypted", "u": 58, "t": 2}]}, "Innodb_encryption_n_rowlog_blocks_encrypted": {"t": 3, "a": [{"a": "innodb_encryption_n_rowlog_blocks_encrypted", "u": 58, "t": 2}]}, "Innodb_encryption_n_temp_blocks_decrypted": {"t": 3, "a": [{"a": "innodb_encryption_n_temp_blocks_decrypted", "u": 58, "t": 2}]}, "Innodb_encryption_n_temp_blocks_encrypted": {"t": 3, "a": [{"a": "innodb_encryption_n_temp_blocks_encrypted", "u": 58, "t": 2}]}, "Innodb_encryption_num_key_requests": {"t": 3, "a": [{"a": "innodb_encryption_num_key_requests", "u": 58, "t": 2}]}, "Innodb_encryption_rotation_estimated_iops": {"t": 3, "a": [{"a": "innodb_encryption_rotation_estimated_iops", "u": 58, "t": 2}]}, "Innodb_encryption_rotation_pages_flushed": {"t": 3, "a": [{"a": "innodb_encryption_rotation_pages_flushed", "u": 58, "t": 2}]}, "Innodb_encryption_rotation_pages_modified": {"t": 3, "a": [{"a": "innodb_encryption_rotation_pages_modified", "u": 58, "t": 2}]}, "Innodb_encryption_rotation_pages_read_from_cache": {"t": 3, "a": [{"a": "innodb_encryption_rotation_pages_read_from_cache", "u": 58, "t": 2}]}, "Innodb_encryption_rotation_pages_read_from_disk": {"t": 3, "a": [{"a": "innodb_encryption_rotation_pages_read_from_disk", "u": 58, "t": 2}]}, "Innodb_have_atomic_builtins": {"t": 2, "a": [{"a": "innodb_have_atomic_builtins", "u": 58, "t": 2}]}, "Innodb_have_bzip2": {"t": 2, "a": [{"a": "innodb_have_bzip2", "u": 58, "t": 2}]}, "Innodb_have_lz4": {"t": 2, "a": [{"a": "innodb_have_lz4", "u": 58, "t": 2}]}, "Innodb_have_lzma": {"t": 2, "a": [{"a": "innodb_have_lzma", "u": 58, "t": 2}]}, "Innodb_have_lzo": {"t": 2, "a": [{"a": "innodb_have_lzo", "u": 58, "t": 2}]}, "Innodb_have_punch_hole": {"t": 3, "a": [{"a": "innodb_have_punch_hole", "u": 58, "t": 2}]}, "Innodb_have_snappy": {"t": 2, "a": [{"a": "innodb_have_snappy", "u": 58, "t": 2}]}, "Innodb_history_list_length": {"t": 3, "a": [{"a": "innodb_history_list_length", "u": 58, "t": 2}]}, "Innodb_ibuf_discarded_delete_marks": {"t": 3, "a": [{"a": "innodb_ibuf_discarded_delete_marks", "u": 58, "t": 2}]}, "Innodb_ibuf_discarded_deletes": {"t": 3, "a": [{"a": "innodb_ibuf_discarded_deletes", "u": 58, "t": 2}]}, "Innodb_ibuf_discarded_inserts": {"t": 3, "a": [{"a": "innodb_ibuf_discarded_inserts", "u": 58, "t": 2}]}, "Innodb_ibuf_free_list": {"t": 3, "a": [{"a": "innodb_ibuf_free_list", "u": 58, "t": 2}]}, "Innodb_ibuf_merged_delete_marks": {"t": 3, "a": [{"a": "innodb_ibuf_merged_delete_marks", "u": 58, "t": 2}]}, "Innodb_ibuf_merged_deletes": {"t": 3, "a": [{"a": "innodb_ibuf_merged_deletes", "u": 58, "t": 2}]}, "Innodb_ibuf_merged_inserts": {"t": 3, "a": [{"a": "innodb_ibuf_merged_inserts", "u": 58, "t": 2}]}, "Innodb_ibuf_merges": {"t": 3, "a": [{"a": "innodb_ibuf_merges", "u": 58, "t": 2}]}, "Innodb_ibuf_segment_size": {"t": 3, "a": [{"a": "innodb_ibuf_segment_size", "u": 58, "t": 2}]}, "Innodb_ibuf_size": {"t": 3, "a": [{"a": "innodb_ibuf_size", "u": 58, "t": 2}]}, "Innodb_instant_alter_column": {"t": 3, "a": [{"a": "innodb_instant_alter_column", "u": 58, "t": 2}]}, "Innodb_log_waits": {"t": 3, "a": [{"a": "innodb_log_waits", "u": 58, "t": 2}]}, "Innodb_log_write_requests": {"t": 3, "a": [{"a": "innodb_log_write_requests", "u": 58, "t": 2}]}, "Innodb_log_writes": {"t": 3, "a": [{"a": "innodb_log_writes", "u": 58, "t": 2}]}, "Innodb_lsn_current": {"t": 3, "a": [{"a": "innodb_lsn_current", "u": 58, "t": 2}]}, "Innodb_lsn_flushed": {"t": 3, "a": [{"a": "innodb_lsn_flushed", "u": 58, "t": 2}]}, "Innodb_lsn_last_checkpoint": {"t": 3, "a": [{"a": "innodb_lsn_last_checkpoint", "u": 58, "t": 2}]}, "Innodb_master_thread_1_second_loops": {"t": 3, "a": [{"a": "innodb_master_thread_1_second_loops", "u": 58, "t": 2}]}, "Innodb_master_thread_10_second_loops": {"t": 3, "a": [{"a": "innodb_master_thread_10_second_loops", "u": 58, "t": 2}]}, "Innodb_master_thread_active_loops": {"t": 3, "a": [{"a": "innodb_master_thread_active_loops", "u": 58, "t": 2}]}, "Innodb_master_thread_background_loops": {"t": 3, "a": [{"a": "innodb_master_thread_background_loops", "u": 58, "t": 2}]}, "Innodb_master_thread_idle_loops": {"t": 3, "a": [{"a": "innodb_master_thread_idle_loops", "u": 58, "t": 2}]}, "Innodb_master_thread_main_flush_loops": {"t": 3, "a": [{"a": "innodb_master_thread_main_flush_loops", "u": 58, "t": 2}]}, "Innodb_master_thread_sleeps": {"t": 3, "a": [{"a": "innodb_master_thread_sleeps", "u": 58, "t": 2}]}, "Innodb_max_trx_id": {"t": 3, "a": [{"a": "innodb_max_trx_id", "u": 58, "t": 2}]}, "Innodb_mem_adaptive_hash": {"t": 3, "a": [{"a": "innodb_mem_adaptive_hash", "u": 58, "t": 2}]}, "Innodb_mem_dictionary": {"t": 3, "a": [{"a": "innodb_mem_dictionary", "u": 58, "t": 2}]}, "Innodb_mem_total": {"t": 3, "a": [{"a": "innodb_mem_total", "u": 58, "t": 2}]}, "Innodb_mutex_os_waits": {"t": 3, "a": [{"a": "innodb_mutex_os_waits", "u": 58, "t": 2}]}, "Innodb_mutex_spin_rounds": {"t": 3, "a": [{"a": "innodb_mutex_spin_rounds", "u": 58, "t": 2}]}, "Innodb_mutex_spin_waits": {"t": 3, "a": [{"a": "innodb_mutex_spin_waits", "u": 58, "t": 2}]}, "Innodb_num_index_pages_written": {"t": 3, "a": [{"a": "innodb_num_index_pages_written", "u": 58, "t": 2}]}, "Innodb_num_non_index_pages_written": {"t": 3, "a": [{"a": "innodb_num_non_index_pages_written", "u": 58, "t": 2}]}, "Innodb_num_open_files": {"t": 3, "a": [{"a": "innodb_num_open_files", "u": 58, "t": 2}]}, "Innodb_num_page_compressed_trim_op": {"t": 3, "a": [{"a": "innodb_num_page_compressed_trim_op", "u": 58, "t": 2}]}, "Innodb_num_page_compressed_trim_op_saved": {"t": 3, "a": [{"a": "innodb_num_page_compressed_trim_op_saved", "u": 58, "t": 2}]}, "Innodb_num_pages_decrypted": {"t": 3, "a": [{"a": "innodb_num_pages_decrypted", "u": 58, "t": 2}]}, "Innodb_num_pages_encrypted": {"t": 3, "a": [{"a": "innodb_num_pages_encrypted", "u": 58, "t": 2}]}, "Innodb_num_pages_page_compressed": {"t": 3, "a": [{"a": "innodb_num_pages_page_compressed", "u": 58, "t": 2}]}, "Innodb_num_pages_page_compression_error": {"t": 3, "a": [{"a": "innodb_num_pages_page_compression_error", "u": 58, "t": 2}]}, "Innodb_num_pages_page_decompressed": {"t": 3, "a": [{"a": "innodb_num_pages_page_decompressed", "u": 58, "t": 2}]}, "Innodb_num_pages_page_encryption_error": {"t": 3, "a": [{"a": "innodb_num_pages_page_encryption_error", "u": 58, "t": 2}]}, "Innodb_oldest_view_low_limit_trx_id": {"t": 3, "a": [{"a": "innodb_oldest_view_low_limit_trx_id", "u": 58, "t": 2}]}, "Innodb_onlineddl_pct_progress": {"t": 3, "a": [{"a": "innodb_onlineddl_pct_progress", "u": 58, "t": 2}]}, "Innodb_onlineddl_rowlog_pct_used": {"t": 3, "a": [{"a": "innodb_onlineddl_rowlog_pct_used", "u": 58, "t": 2}]}, "Innodb_onlineddl_rowlog_rows": {"t": 3, "a": [{"a": "innodb_onlineddl_rowlog_rows", "u": 58, "t": 2}]}, "Innodb_os_log_fsyncs": {"t": 3, "a": [{"a": "innodb_os_log_fsyncs", "u": 58, "t": 2}]}, "Innodb_os_log_pending_fsyncs": {"t": 3, "a": [{"a": "innodb_os_log_pending_fsyncs", "u": 58, "t": 2}]}, "Innodb_os_log_pending_writes": {"t": 3, "a": [{"a": "innodb_os_log_pending_writes", "u": 58, "t": 2}]}, "Innodb_os_log_written": {"t": 3, "a": [{"a": "innodb_os_log_written", "u": 58, "t": 2}]}, "Innodb_page_compression_saved": {"a": [{"a": "innodb_page_compression_saved", "u": 58, "t": 2}]}, "Innodb_page_compression_trim_sect512": {"t": 3, "a": [{"a": "innodb_page_compression_trim_sect512", "u": 58, "t": 2}]}, "Innodb_page_compression_trim_sect1024": {"t": 3, "a": [{"a": "innodb_page_compression_trim_sect1024", "u": 58, "t": 2}]}, "Innodb_page_compression_trim_sect2048": {"t": 3, "a": [{"a": "innodb_page_compression_trim_sect2048", "u": 58, "t": 2}]}, "Innodb_page_compression_trim_sect4096": {"t": 3, "a": [{"a": "innodb_page_compression_trim_sect4096", "u": 58, "t": 2}]}, "Innodb_page_compression_trim_sect8192": {"t": 3, "a": [{"a": "innodb_page_compression_trim_sect8192", "u": 58, "t": 2}]}, "Innodb_page_compression_trim_sect16384": {"t": 3, "a": [{"a": "innodb_page_compression_trim_sect16384", "u": 58, "t": 2}]}, "Innodb_page_compression_trim_sect32768": {"t": 3, "a": [{"a": "innodb_page_compression_trim_sect32768", "u": 58, "t": 2}]}, "Innodb_page_size": {"t": 3, "a": [{"a": "innodb_page_size", "u": 58, "t": 2}]}, "Innodb_pages_created": {"t": 3, "a": [{"a": "innodb_pages_created", "u": 58, "t": 2}]}, "Innodb_pages_read": {"t": 3, "a": [{"a": "innodb_pages_read", "u": 58, "t": 2}]}, "Innodb_pages0_read": {"t": 3, "a": [{"a": "innodb_pages0_read", "u": 58, "t": 2}]}, "Innodb_pages_written": {"t": 3, "a": [{"a": "innodb_pages_written", "u": 58, "t": 2}]}, "Innodb_purge_trx_id": {"t": 3, "a": [{"a": "innodb_purge_trx_id", "u": 58, "t": 2}]}, "Innodb_purge_undo_no": {"t": 3, "a": [{"a": "innodb_purge_undo_no", "u": 58, "t": 2}]}, "Innodb_read_views_memory": {"t": 3, "a": [{"a": "innodb_read_views_memory", "u": 58, "t": 2}]}, "Innodb_row_lock_current_waits": {"t": 3, "a": [{"a": "innodb_row_lock_current_waits", "u": 58, "t": 2}]}, "Innodb_row_lock_numbers": {"t": 3, "a": [{"a": "innodb_row_lock_numbers", "u": 58, "t": 2}]}, "Innodb_row_lock_time": {"t": 3, "a": [{"a": "innodb_row_lock_time", "u": 58, "t": 2}]}, "Innodb_row_lock_time_avg": {"t": 3, "a": [{"a": "innodb_row_lock_time_avg", "u": 58, "t": 2}]}, "Innodb_row_lock_time_max": {"t": 3, "a": [{"a": "innodb_row_lock_time_max", "u": 58, "t": 2}]}, "Innodb_row_lock_waits": {"t": 3, "a": [{"a": "innodb_row_lock_waits", "u": 58, "t": 2}]}, "Innodb_rows_deleted": {"t": 3, "a": [{"a": "innodb_rows_deleted", "u": 58, "t": 2}]}, "Innodb_rows_inserted": {"t": 3, "a": [{"a": "innodb_rows_inserted", "u": 58, "t": 2}]}, "Innodb_rows_read": {"t": 3, "a": [{"a": "innodb_rows_read", "u": 58, "t": 2}]}, "Innodb_rows_updated": {"t": 3, "a": [{"a": "innodb_rows_updated", "u": 58, "t": 2}]}, "Innodb_s_lock_os_waits": {"t": 3, "a": [{"a": "innodb_s_lock_os_waits", "u": 58, "t": 2}]}, "Innodb_s_lock_spin_rounds": {"t": 3, "a": [{"a": "innodb_s_lock_spin_rounds", "u": 58, "t": 2}]}, "Innodb_s_lock_spin_waits": {"t": 3, "a": [{"a": "innodb_s_lock_spin_waits", "u": 58, "t": 2}]}, "Innodb_scrub_background_page_reorganizations": {"t": 3, "a": [{"a": "innodb_scrub_background_page_reorganizations", "u": 58, "t": 2}]}, "Innodb_scrub_background_page_split_failures_missing_index": {"t": 3, "a": [{"a": "innodb_scrub_background_page_split_failures_missing_index", "u": 58, "t": 2}]}, "Innodb_scrub_background_page_split_failures_out_of_filespace": {"t": 3, "a": [{"a": "innodb_scrub_background_page_split_failures_out_of_filespace", "u": 58, "t": 2}]}, "Innodb_scrub_background_page_split_failures_underflow": {"t": 3, "a": [{"a": "innodb_scrub_background_page_split_failures_underflow", "u": 58, "t": 2}]}, "Innodb_scrub_background_page_split_failures_unknown": {"t": 3, "a": [{"a": "innodb_scrub_background_page_split_failures_unknown", "u": 58, "t": 2}]}, "Innodb_scrub_background_page_splits": {"t": 3, "a": [{"a": "innodb_scrub_background_page_splits", "u": 58, "t": 2}]}, "Innodb_scrub_log": {"t": 3, "a": [{"a": "innodb_scrub_log", "u": 58, "t": 2}]}, "Innodb_secondary_index_triggered_cluster_reads": {"t": 3, "a": [{"a": "innodb_secondary_index_triggered_cluster_reads", "u": 58, "t": 2}]}, "Innodb_secondary_index_triggered_cluster_reads_avoided": {"t": 3, "a": [{"a": "innodb_secondary_index_triggered_cluster_reads_avoided", "u": 58, "t": 2}]}, "Innodb_system_rows_deleted": {"t": 3, "a": [{"a": "innodb_system_rows_deleted", "u": 58, "t": 2}]}, "Innodb_system_rows_inserted": {"t": 3, "a": [{"a": "innodb_system_rows_inserted", "u": 58, "t": 2}]}, "Innodb_system_rows_read": {"t": 3, "a": [{"a": "innodb_system_rows_read", "u": 58, "t": 2}]}, "Innodb_system_rows_updated": {"t": 3, "a": [{"a": "innodb_system_rows_updated", "u": 58, "t": 2}]}, "Innodb_truncated_status_writes": {"t": 3, "a": [{"a": "innodb_truncated_status_writes", "u": 58, "t": 2}]}, "Innodb_undo_truncations": {"t": 3, "a": [{"a": "innodb_undo_truncations", "u": 58, "t": 2}]}, "Innodb_x_lock_os_waits": {"t": 3, "a": [{"a": "innodb_x_lock_os_waits", "u": 58, "t": 2}]}, "Innodb_x_lock_spin_rounds": {"t": 3, "a": [{"a": "innodb_x_lock_spin_rounds", "u": 58, "t": 2}]}, "Innodb_x_lock_spin_waits": {"t": 3, "a": [{"a": "innodb_x_lock_spin_waits", "u": 58, "t": 2}]}, "have_innodb": {"d": false, "a": [{"a": "have_innodb", "u": 59, "t": 2}]}, "ignore_builtin_innodb": {"d": false, "t": 2, "a": [{"a": "ignore_builtin_innodb", "u": 59, "t": 2}]}, "innodb_adaptive_checkpoint": {"d": true, "t": 1, "a": [{"a": "innodb_adaptive_checkpoint", "u": 59, "t": 2}]}, "innodb_adaptive_flushing": {"d": true, "t": 2, "a": [{"a": "innodb_adaptive_flushing", "u": 59, "t": 2}, {"a": "sysvar_innodb_adaptive_flushing", "u": 60, "t": 1}]}, "innodb_adaptive_flushing_lwm": {"d": true, "t": 3, "a": [{"a": "innodb_adaptive_flushing_lwm", "u": 59, "t": 2}, {"a": "sysvar_innodb_adaptive_flushing_lwm", "u": 60, "t": 1}]}, "innodb_adaptive_flushing_method": {"d": true, "t": 5, "a": [{"a": "innodb_adaptive_flushing_method", "u": 59, "t": 2}]}, "innodb_adaptive_hash_index": {"d": true, "t": 2, "a": [{"a": "innodb_adaptive_hash_index", "u": 59, "t": 2}, {"a": "sysvar_innodb_adaptive_hash_index", "u": 60, "t": 1}]}, "innodb_adaptive_hash_index_partitions": {"d": false, "t": 3, "a": [{"a": "innodb_adaptive_hash_index_partitions", "u": 59, "t": 2}]}, "innodb_adaptive_hash_index_parts": {"d": false, "t": 3, "a": [{"a": "innodb_adaptive_hash_index_parts", "u": 59, "t": 2}, {"a": "sysvar_innodb_adaptive_hash_index_parts", "u": 60, "t": 1}]}, "innodb_adaptive_max_sleep_delay": {"d": true, "t": 3, "a": [{"a": "innodb_adaptive_max_sleep_delay", "u": 59, "t": 2}, {"a": "sysvar_innodb_adaptive_max_sleep_delay", "u": 60, "t": 1}]}, "innodb_additional_mem_pool_size": {"d": false, "t": 3, "a": [{"a": "innodb_additional_mem_pool_size", "u": 59, "t": 2}]}, "innodb_api_bk_commit_interval": {"d": true, "t": 3, "a": [{"a": "innodb_api_bk_commit_interval", "u": 59, "t": 2}, {"a": "sysvar_innodb_api_bk_commit_interval", "u": 60, "t": 1}]}, "innodb_api_disable_rowlock": {"d": false, "t": 2, "a": [{"a": "innodb_api_disable_rowlock", "u": 59, "t": 2}, {"a": "sysvar_innodb_api_disable_rowlock", "u": 60, "t": 1}]}, "innodb_api_enable_binlog": {"d": false, "t": 2, "a": [{"a": "innodb_api_enable_binlog", "u": 59, "t": 2}, {"a": "sysvar_innodb_api_enable_binlog", "u": 60, "t": 1}]}, "innodb_api_enable_mdl": {"d": false, "t": 2, "a": [{"a": "innodb_api_enable_mdl", "u": 59, "t": 2}, {"a": "sysvar_innodb_api_enable_mdl", "u": 60, "t": 1}]}, "innodb_api_trx_level": {"d": true, "t": 3, "a": [{"a": "innodb_api_trx_level", "u": 59, "t": 2}, {"a": "sysvar_innodb_api_trx_level", "u": 60, "t": 1}]}, "innodb_auto_lru_dump": {"a": [{"a": "innodb_auto_lru_dump", "u": 59, "t": 2}]}, "innodb_autoextend_increment": {"d": true, "t": 3, "a": [{"a": "innodb_autoextend_increment", "u": 59, "t": 2}, {"a": "sysvar_innodb_autoextend_increment", "u": 60, "t": 1}]}, "innodb_autoinc_lock_mode": {"d": false, "t": 3, "a": [{"a": "innodb_autoinc_lock_mode", "u": 59, "t": 2}, {"a": "sysvar_innodb_autoinc_lock_mode", "u": 60, "t": 1}]}, "innodb_background_scrub_data_check_interval": {"d": true, "t": 3, "a": [{"a": "innodb_background_scrub_data_check_interval", "u": 59, "t": 2}]}, "innodb_background_scrub_data_compressed": {"d": true, "t": 2, "a": [{"a": "innodb_background_scrub_data_compressed", "u": 59, "t": 2}]}, "innodb_background_scrub_data_interval": {"d": true, "t": 3, "a": [{"a": "innodb_background_scrub_data_interval", "u": 59, "t": 2}]}, "innodb_background_scrub_data_uncompressed": {"d": true, "t": 2, "a": [{"a": "innodb_background_scrub_data_uncompressed", "u": 59, "t": 2}]}, "innodb_blocking_buffer_pool_restore": {"d": false, "t": 2, "a": [{"a": "innodb_blocking_buffer_pool_restore", "u": 59, "t": 2}]}, "innodb_buf_dump_status_frequency": {"d": true, "t": 3, "a": [{"a": "innodb_buf_dump_status_frequency", "u": 59, "t": 2}]}, "innodb_buffer_pool_chunk_size": {"d": false, "t": 3, "a": [{"a": "innodb_buffer_pool_chunk_size", "u": 59, "t": 2}, {"a": "sysvar_innodb_buffer_pool_chunk_size", "u": 60, "t": 1}]}, "innodb_buffer_pool_dump_at_shutdown": {"d": true, "t": 2, "a": [{"a": "innodb_buffer_pool_dump_at_shutdown", "u": 59, "t": 2}, {"a": "sysvar_innodb_buffer_pool_dump_at_shutdown", "u": 60, "t": 1}]}, "innodb_buffer_pool_dump_now": {"d": true, "t": 2, "a": [{"a": "innodb_buffer_pool_dump_now", "u": 59, "t": 2}, {"a": "sysvar_innodb_buffer_pool_dump_now", "u": 60, "t": 1}]}, "innodb_buffer_pool_dump_pct": {"d": true, "a": [{"a": "innodb_buffer_pool_dump_pct", "u": 59, "t": 2}, {"a": "sysvar_innodb_buffer_pool_dump_pct", "u": 60, "t": 1}]}, "innodb_buffer_pool_evict": {"d": true, "t": 1, "a": [{"a": "innodb_buffer_pool_evict", "u": 59, "t": 2}]}, "innodb_buffer_pool_filename": {"d": true, "a": [{"a": "innodb_buffer_pool_filename", "u": 59, "t": 2}, {"a": "sysvar_innodb_buffer_pool_filename", "u": 60, "t": 1}]}, "innodb_buffer_pool_instances": {"d": false, "t": 3, "a": [{"a": "innodb_buffer_pool_instances", "u": 59, "t": 2}, {"a": "sysvar_innodb_buffer_pool_instances", "u": 60, "t": 1}]}, "innodb_buffer_pool_load_abort": {"d": true, "t": 2, "a": [{"a": "innodb_buffer_pool_load_abort", "u": 59, "t": 2}, {"a": "sysvar_innodb_buffer_pool_load_abort", "u": 60, "t": 1}]}, "innodb_buffer_pool_load_at_startup": {"d": false, "t": 2, "a": [{"a": "innodb_buffer_pool_load_at_startup", "u": 59, "t": 2}, {"a": "sysvar_innodb_buffer_pool_load_at_startup", "u": 60, "t": 1}]}, "innodb_buffer_pool_load_now": {"d": true, "t": 2, "a": [{"a": "innodb_buffer_pool_load_now", "u": 59, "t": 2}, {"a": "sysvar_innodb_buffer_pool_load_now", "u": 60, "t": 1}]}, "innodb_buffer_pool_load_pages_abort": {"d": true, "t": 3, "a": [{"a": "innodb_buffer_pool_load_pages_abort", "u": 59, "t": 2}]}, "innodb_buffer_pool_populate": {"d": false, "t": 2, "a": [{"a": "innodb_buffer_pool_populate", "u": 59, "t": 2}]}, "innodb_buffer_pool_restore_at_startup": {"d": true, "t": 3, "a": [{"a": "innodb_buffer_pool_restore_at_startup", "u": 59, "t": 2}]}, "innodb_buffer_pool_shm_checksum": {"d": false, "t": 2, "a": [{"a": "innodb_buffer_pool_shm_checksum", "u": 59, "t": 2}]}, "innodb_buffer_pool_shm_key": {"d": false, "t": 2, "a": [{"a": "innodb_buffer_pool_shm_key", "u": 59, "t": 2}]}, "innodb_buffer_pool_size": {"t": 3, "a": [{"a": "innodb_buffer_pool_size", "u": 59, "t": 2}, {"a": "sysvar_innodb_buffer_pool_size", "u": 60, "t": 1}]}, "innodb_change_buffer_dump": {"d": false, "t": 2, "a": [{"a": "innodb_change_buffer_dump", "u": 59, "t": 2}]}, "innodb_change_buffer_max_size": {"d": true, "t": 3, "a": [{"a": "innodb_change_buffer_max_size", "u": 59, "t": 2}, {"a": "sysvar_innodb_change_buffer_max_size", "u": 60, "t": 1}]}, "innodb_change_buffering": {"d": true, "t": 5, "a": [{"a": "innodb_change_buffering", "u": 59, "t": 2}, {"a": "sysvar_innodb_change_buffering", "u": 60, "t": 1}]}, "innodb_change_buffering_debug": {"d": true, "t": 3, "a": [{"a": "innodb_change_buffering_debug", "u": 59, "t": 2}, {"a": "sysvar_innodb_change_buffering_debug", "u": 60, "t": 1}]}, "innodb_checkpoint_age_target": {"d": true, "t": 3, "a": [{"a": "innodb_checkpoint_age_target", "u": 59, "t": 2}]}, "innodb_checksum_algorithm": {"d": true, "t": 5, "a": [{"a": "innodb_checksum_algorithm", "u": 59, "t": 2}, {"a": "sysvar_innodb_checksum_algorithm", "u": 60, "t": 1}]}, "innodb_checksums": {"d": false, "t": 2, "a": [{"a": "innodb_checksums", "u": 59, "t": 2}]}, "innodb_cleaner_lsn_age_factor": {"d": true, "t": 5, "a": [{"a": "innodb_cleaner_lsn_age_factor", "u": 59, "t": 2}]}, "innodb_cmp_per_index_enabled": {"d": true, "t": 2, "a": [{"a": "innodb_cmp_per_index_enabled", "u": 59, "t": 2}, {"a": "sysvar_innodb_cmp_per_index_enabled", "u": 60, "t": 1}]}, "innodb_commit_concurrency": {"d": true, "t": 3, "a": [{"a": "innodb_commit_concurrency", "u": 59, "t": 2}, {"a": "sysvar_innodb_commit_concurrency", "u": 60, "t": 1}]}, "innodb_compression_algorithm": {"d": true, "t": 5, "a": [{"a": "innodb_compression_algorithm", "u": 59, "t": 2}]}, "innodb_compression_default": {"d": true, "t": 2, "a": [{"a": "innodb_compression_default", "u": 59, "t": 2}]}, "innodb_compression_failure_threshold_pct": {"d": true, "t": 3, "a": [{"a": "innodb_compression_failure_threshold_pct", "u": 59, "t": 2}, {"a": "sysvar_innodb_compression_failure_threshold_pct", "u": 60, "t": 1}]}, "innodb_compression_level": {"d": true, "t": 3, "a": [{"a": "innodb_compression_level", "u": 59, "t": 2}, {"a": "sysvar_innodb_compression_level", "u": 60, "t": 1}]}, "innodb_compression_pad_pct_max": {"d": true, "t": 3, "a": [{"a": "innodb_compression_pad_pct_max", "u": 59, "t": 2}, {"a": "sysvar_innodb_compression_pad_pct_max", "u": 60, "t": 1}]}, "innodb_concurrency_tickets": {"d": true, "t": 3, "a": [{"a": "innodb_concurrency_tickets", "u": 59, "t": 2}, {"a": "sysvar_innodb_concurrency_tickets", "u": 60, "t": 1}]}, "innodb_corrupt_table_action": {"d": true, "t": 5, "a": [{"a": "innodb_corrupt_table_action", "u": 59, "t": 2}]}, "innodb_data_file_path": {"d": false, "a": [{"a": "innodb_data_file_path", "u": 59, "t": 2}, {"a": "sysvar_innodb_data_file_path", "u": 60, "t": 1}]}, "innodb_data_home_dir": {"d": false, "t": 7, "a": [{"a": "innodb_data_home_dir", "u": 59, "t": 2}, {"a": "sysvar_innodb_data_home_dir", "u": 60, "t": 1}]}, "innodb_deadlock_detect": {"d": true, "t": 2, "a": [{"a": "innodb_deadlock_detect", "u": 59, "t": 2}, {"a": "sysvar_innodb_deadlock_detect", "u": 60, "t": 1}]}, "innodb_deadlock_report": {"d": true, "t": 5, "a": [{"a": "innodb_deadlock_report", "u": 59, "t": 2}]}, "innodb_default_page_encryption_key": {"d": true, "t": 3, "a": [{"a": "innodb_default_page_encryption_key", "u": 59, "t": 2}]}, "innodb_default_encryption_key_id": {"d": true, "t": 3, "a": [{"a": "innodb_default_encryption_key_id", "u": 59, "t": 2}]}, "innodb_default_row_format": {"d": true, "t": 5, "a": [{"a": "innodb_default_row_format", "u": 59, "t": 2}, {"a": "sysvar_innodb_default_row_format", "u": 60, "t": 1}]}, "innodb_defragment": {"d": true, "t": 2, "a": [{"a": "innodb_defragment", "u": 59, "t": 2}]}, "innodb_defragment_fill_factor": {"d": true, "t": 3, "a": [{"a": "innodb_defragment_fill_factor", "u": 59, "t": 2}]}, "innodb_defragment_fill_factor_n_recs": {"d": true, "t": 3, "a": [{"a": "innodb_defragment_fill_factor_n_recs", "u": 59, "t": 2}]}, "innodb_defragment_frequency": {"d": true, "t": 3, "a": [{"a": "innodb_defragment_frequency", "u": 59, "t": 2}]}, "innodb_defragment_n_pages": {"d": true, "t": 3, "a": [{"a": "innodb_defragment_n_pages", "u": 59, "t": 2}]}, "innodb_defragment_stats_accuracy": {"d": true, "t": 3, "a": [{"a": "innodb_defragment_stats_accuracy", "u": 59, "t": 2}]}, "innodb_dict_size_limit": {"d": true, "t": 3, "a": [{"a": "innodb_dict_size_limit", "u": 59, "t": 2}]}, "innodb_disable_sort_file_cache": {"d": true, "t": 2, "a": [{"a": "innodb_disable_sort_file_cache", "u": 59, "t": 2}, {"a": "sysvar_innodb_disable_sort_file_cache", "u": 60, "t": 1}]}, "innodb_disallow_writes": {"d": true, "t": 2, "a": [{"a": "innodb_disallow_writes", "u": 59, "t": 2}]}, "innodb_doublewrite": {"a": [{"a": "innodb_doublewrite", "u": 59, "t": 2}, {"a": "sysvar_innodb_doublewrite", "u": 60, "t": 1}]}, "innodb_doublewrite_file": {"d": false, "t": 8, "a": [{"a": "innodb_doublewrite_file", "u": 59, "t": 2}]}, "innodb_empty_free_list_algorithm": {"d": true, "t": 5, "a": [{"a": "innodb_empty_free_list_algorithm", "u": 59, "t": 2}]}, "innodb_enable_unsafe_group_commit": {"d": true, "t": 3, "a": [{"a": "innodb_enable_unsafe_group_commit", "u": 59, "t": 2}]}, "innodb_encrypt_log": {"d": false, "t": 2, "a": [{"a": "innodb_encrypt_log", "u": 59, "t": 2}]}, "innodb_encrypt_tables": {"d": true, "t": 2, "a": [{"a": "innodb_encrypt_tables", "u": 59, "t": 2}]}, "innodb_encrypt_temporary_tables": {"d": false, "t": 2, "a": [{"a": "innodb_encrypt_temporary_tables", "u": 59, "t": 2}]}, "innodb_encryption_rotate_key_age": {"d": true, "t": 3, "a": [{"a": "innodb_encryption_rotate_key_age", "u": 59, "t": 2}]}, "innodb_encryption_rotation_iops": {"d": true, "t": 3, "a": [{"a": "innodb_encryption_rotation_iops", "u": 59, "t": 2}]}, "innodb_encryption_threads": {"d": true, "t": 3, "a": [{"a": "innodb_encryption_threads", "u": 59, "t": 2}]}, "innodb_extra_rsegments": {"d": false, "t": 3, "a": [{"a": "innodb_extra_rsegments", "u": 59, "t": 2}]}, "innodb_extra_undoslots": {"d": false, "t": 2, "a": [{"a": "innodb_extra_undoslots", "u": 59, "t": 2}]}, "innodb_fake_changes": {"d": true, "t": 2, "a": [{"a": "innodb_fake_changes", "u": 59, "t": 2}]}, "innodb_fast_checksum": {"d": false, "t": 2, "a": [{"a": "innodb_fast_checksum", "u": 59, "t": 2}]}, "innodb_fast_shutdown": {"d": true, "t": 3, "a": [{"a": "innodb_fast_shutdown", "u": 59, "t": 2}, {"a": "sysvar_innodb_fast_shutdown", "u": 60, "t": 1}]}, "innodb_fatal_semaphore_wait_threshold": {"d": false, "t": 3, "a": [{"a": "innodb_fatal_semaphore_wait_threshold", "u": 59, "t": 2}]}, "innodb_file_format": {"d": true, "t": 1, "a": [{"a": "innodb_file_format", "u": 59, "t": 2}]}, "innodb_file_format_check": {"d": false, "t": 2, "a": [{"a": "innodb_file_format_check", "u": 59, "t": 2}]}, "innodb_file_format_max": {"d": true, "t": 1, "a": [{"a": "innodb_file_format_max", "u": 59, "t": 2}]}, "innodb_file_per_table": {"d": true, "t": 2, "a": [{"a": "innodb_file_per_table", "u": 59, "t": 2}, {"a": "sysvar_innodb_file_per_table", "u": 60, "t": 1}]}, "innodb_fill_factor": {"d": true, "t": 3, "a": [{"a": "innodb_fill_factor", "u": 59, "t": 2}, {"a": "sysvar_innodb_fill_factor", "u": 60, "t": 1}]}, "innodb_flush_log_at_timeout": {"d": true, "t": 3, "a": [{"a": "innodb_flush_log_at_timeout", "u": 59, "t": 2}, {"a": "sysvar_innodb_flush_log_at_timeout", "u": 60, "t": 1}]}, "innodb_flush_log_at_trx_commit": {"d": true, "t": 5, "a": [{"a": "innodb_flush_log_at_trx_commit", "u": 59, "t": 2}, {"a": "sysvar_innodb_flush_log_at_trx_commit", "u": 60, "t": 1}]}, "innodb_flush_method": {"d": false, "a": [{"a": "innodb_flush_method", "u": 59, "t": 2}, {"a": "sysvar_innodb_flush_method", "u": 60, "t": 1}]}, "innodb_flush_neighbor_pages": {"d": true, "t": 5, "a": [{"a": "innodb_flush_neighbor_pages", "u": 59, "t": 2}]}, "innodb_flush_neighbors": {"d": true, "t": 5, "a": [{"a": "innodb_flush_neighbors", "u": 59, "t": 2}, {"a": "sysvar_innodb_flush_neighbors", "u": 60, "t": 1}]}, "innodb_flush_sync": {"d": true, "t": 2, "a": [{"a": "innodb_flush_sync", "u": 59, "t": 2}, {"a": "sysvar_innodb_flush_sync", "u": 60, "t": 1}]}, "innodb_flushing_avg_loops": {"d": true, "t": 3, "a": [{"a": "innodb_flushing_avg_loops", "u": 59, "t": 2}, {"a": "sysvar_innodb_flushing_avg_loops", "u": 60, "t": 1}]}, "innodb_force_load_corrupted": {"d": false, "t": 2, "a": [{"a": "innodb_force_load_corrupted", "u": 59, "t": 2}, {"a": "sysvar_innodb_force_load_corrupted", "u": 60, "t": 1}]}, "innodb_force_primary_key": {"d": true, "t": 2, "a": [{"a": "innodb_force_primary_key", "u": 59, "t": 2}]}, "innodb_force_recovery": {"d": false, "a": [{"a": "innodb_force_recovery", "u": 59, "t": 2}, {"a": "sysvar_innodb_force_recovery", "u": 60, "t": 1}]}, "innodb_foreground_preflush": {"d": true, "t": 5, "a": [{"a": "innodb_foreground_preflush", "u": 59, "t": 2}]}, "innodb_ft_aux_table": {"d": true, "t": 1, "a": [{"a": "innodb_ft_aux_table", "u": 59, "t": 2}, {"a": "sysvar_innodb_ft_aux_table", "u": 60, "t": 1}]}, "innodb_ft_cache_size": {"d": false, "t": 3, "a": [{"a": "innodb_ft_cache_size", "u": 59, "t": 2}, {"a": "sysvar_innodb_ft_cache_size", "u": 60, "t": 1}]}, "innodb_ft_enable_diag_print": {"d": true, "t": 2, "a": [{"a": "innodb_ft_enable_diag_print", "u": 59, "t": 2}, {"a": "sysvar_innodb_ft_enable_diag_print", "u": 60, "t": 1}]}, "innodb_ft_enable_stopword": {"d": true, "t": 2, "a": [{"a": "innodb_ft_enable_stopword", "u": 59, "t": 2}, {"a": "sysvar_innodb_ft_enable_stopword", "u": 60, "t": 1}]}, "innodb_ft_max_token_size": {"d": false, "t": 3, "a": [{"a": "innodb_ft_max_token_size", "u": 59, "t": 2}, {"a": "sysvar_innodb_ft_max_token_size", "u": 60, "t": 1}]}, "innodb_ft_min_token_size": {"d": false, "t": 3, "a": [{"a": "innodb_ft_min_token_size", "u": 59, "t": 2}, {"a": "sysvar_innodb_ft_min_token_size", "u": 60, "t": 1}]}, "innodb_ft_num_word_optimize": {"d": true, "t": 3, "a": [{"a": "innodb_ft_num_word_optimize", "u": 59, "t": 2}, {"a": "sysvar_innodb_ft_num_word_optimize", "u": 60, "t": 1}]}, "innodb_ft_result_cache_limit": {"d": true, "t": 3, "a": [{"a": "innodb_ft_result_cache_limit", "u": 59, "t": 2}, {"a": "sysvar_innodb_ft_result_cache_limit", "u": 60, "t": 1}]}, "innodb_ft_server_stopword_table": {"d": true, "t": 1, "a": [{"a": "innodb_ft_server_stopword_table", "u": 59, "t": 2}, {"a": "sysvar_innodb_ft_server_stopword_table", "u": 60, "t": 1}]}, "innodb_ft_sort_pll_degree": {"d": false, "t": 3, "a": [{"a": "innodb_ft_sort_pll_degree", "u": 59, "t": 2}, {"a": "sysvar_innodb_ft_sort_pll_degree", "u": 60, "t": 1}]}, "innodb_ft_total_cache_size": {"d": false, "t": 3, "a": [{"a": "innodb_ft_total_cache_size", "u": 59, "t": 2}, {"a": "sysvar_innodb_ft_total_cache_size", "u": 60, "t": 1}]}, "innodb_ft_user_stopword_table": {"d": true, "t": 1, "a": [{"a": "innodb_ft_user_stopword_table", "u": 59, "t": 2}, {"a": "sysvar_innodb_ft_user_stopword_table", "u": 60, "t": 1}]}, "innodb_ibuf_accel_rate": {"d": true, "t": 3, "a": [{"a": "innodb_ibuf_accel_rate", "u": 59, "t": 2}]}, "innodb_ibuf_active_contract": {"d": true, "t": 3, "a": [{"a": "innodb_ibuf_active_contract", "u": 59, "t": 2}]}, "innodb_ibuf_max_size": {"d": false, "t": 3, "a": [{"a": "innodb_ibuf_max_size", "u": 59, "t": 2}]}, "innodb_idle_flush_pct": {"d": true, "t": 3, "a": [{"a": "innodb_idle_flush_pct", "u": 59, "t": 2}, {"a": "sysvar_innodb_idle_flush_pct", "u": 60, "t": 1}]}, "innodb_immediate_scrub_data_uncompressed": {"d": true, "t": 2, "a": [{"a": "innodb_immediate_scrub_data_uncompressed", "u": 59, "t": 2}]}, "innodb_import_table_from_xtrabackup": {"d": true, "t": 3, "a": [{"a": "innodb_import_table_from_xtrabackup", "u": 59, "t": 2}]}, "innodb_instant_alter_column_allowed": {"d": true, "t": 5, "a": [{"a": "innodb_instant_alter_column_allowed", "u": 59, "t": 2}]}, "innodb_instrument_semaphores": {"d": true, "t": 2, "a": [{"a": "innodb_instrument_semaphores", "u": 59, "t": 2}]}, "innodb_io_capacity": {"d": true, "t": 3, "a": [{"a": "innodb_io_capacity", "u": 59, "t": 2}, {"a": "sysvar_innodb_io_capacity", "u": 60, "t": 1}]}, "innodb_io_capacity_max": {"d": true, "t": 3, "a": [{"a": "innodb_io_capacity_max", "u": 59, "t": 2}, {"a": "sysvar_innodb_io_capacity_max", "u": 60, "t": 1}]}, "innodb_kill_idle_transaction": {"d": true, "t": 3, "a": [{"a": "innodb_kill_idle_transaction", "u": 59, "t": 2}]}, "innodb_large_prefix": {"d": true, "t": 2, "a": [{"a": "innodb_large_prefix", "u": 59, "t": 2}]}, "innodb_lazy_drop_table": {"d": true, "t": 2, "a": [{"a": "innodb_lazy_drop_table", "u": 59, "t": 2}]}, "innodb_lock_schedule_algorithm": {"d": false, "t": 5, "a": [{"a": "innodb_lock_schedule_algorithm", "u": 59, "t": 2}]}, "innodb_lock_wait_timeout": {"d": true, "t": 3, "a": [{"a": "innodb_lock_wait_timeout", "u": 59, "t": 2}, {"a": "sysvar_innodb_lock_wait_timeout", "u": 60, "t": 1}]}, "innodb_locking_fake_changes": {"d": true, "t": 2, "a": [{"a": "innodb_locking_fake_changes", "u": 59, "t": 2}]}, "innodb_locks_unsafe_for_binlog": {"d": false, "t": 2, "a": [{"a": "innodb_locks_unsafe_for_binlog", "u": 59, "t": 2}]}, "innodb_log_arch_dir": {"d": false, "t": 1, "a": [{"a": "innodb_log_arch_dir", "u": 59, "t": 2}]}, "innodb_log_arch_expire_sec": {"d": true, "t": 3, "a": [{"a": "innodb_log_arch_expire_sec", "u": 59, "t": 2}]}, "innodb_log_archive": {"d": true, "t": 2, "a": [{"a": "innodb_log_archive", "u": 59, "t": 2}]}, "innodb_log_block_size": {"d": false, "t": 3, "a": [{"a": "innodb_log_block_size", "u": 59, "t": 2}]}, "innodb_log_buffer_size": {"t": 3, "a": [{"a": "innodb_log_buffer_size", "u": 59, "t": 2}, {"a": "sysvar_innodb_log_buffer_size", "u": 60, "t": 1}]}, "innodb_log_checksum_algorithm": {"d": true, "t": 5, "a": [{"a": "innodb_log_checksum_algorithm", "u": 59, "t": 2}]}, "innodb_log_checksums": {"d": true, "t": 2, "a": [{"a": "innodb_log_checksums", "u": 59, "t": 2}, {"a": "sysvar_innodb_log_checksums", "u": 60, "t": 1}]}, "innodb_log_compressed_pages": {"d": true, "t": 2, "a": [{"a": "innodb_log_compressed_pages", "u": 59, "t": 2}, {"a": "sysvar_innodb_log_compressed_pages", "u": 60, "t": 1}]}, "innodb_log_file_buffering": {"d": true, "t": 2, "a": [{"a": "innodb_log_file_buffering", "u": 59, "t": 2}]}, "innodb_log_file_size": {"d": false, "t": 3, "a": [{"a": "innodb_log_file_size", "u": 59, "t": 2}, {"a": "sysvar_innodb_log_file_size", "u": 60, "t": 1}]}, "innodb_log_files_in_group": {"d": false, "t": 3, "a": [{"a": "innodb_log_files_in_group", "u": 59, "t": 2}, {"a": "sysvar_innodb_log_files_in_group", "u": 60, "t": 1}]}, "innodb_log_group_home_dir": {"d": false, "t": 7, "a": [{"a": "innodb_log_group_home_dir", "u": 59, "t": 2}, {"a": "sysvar_innodb_log_group_home_dir", "u": 60, "t": 1}]}, "innodb_log_optimize_ddl": {"d": true, "t": 2, "a": [{"a": "innodb_log_optimize_ddl", "u": 59, "t": 2}]}, "innodb_log_write_ahead_size": {"d": true, "t": 3, "a": [{"a": "innodb_log_write_ahead_size", "u": 59, "t": 2}, {"a": "sysvar_innodb_log_write_ahead_size", "u": 60, "t": 1}]}, "innodb_lru_flush_size": {"d": true, "t": 3, "a": [{"a": "innodb_lru_flush_size", "u": 59, "t": 2}]}, "innodb_lru_scan_depth": {"d": true, "t": 3, "a": [{"a": "innodb_lru_scan_depth", "u": 59, "t": 2}, {"a": "sysvar_innodb_lru_scan_depth", "u": 60, "t": 1}]}, "innodb_max_bitmap_file_size": {"d": true, "t": 3, "a": [{"a": "innodb_max_bitmap_file_size", "u": 59, "t": 2}]}, "innodb_max_changed_pages": {"d": true, "t": 3, "a": [{"a": "innodb_max_changed_pages", "u": 59, "t": 2}]}, "innodb_max_dirty_pages_pct": {"d": true, "t": 3, "a": [{"a": "innodb_max_dirty_pages_pct", "u": 59, "t": 2}, {"a": "sysvar_innodb_max_dirty_pages_pct", "u": 60, "t": 1}]}, "innodb_max_dirty_pages_pct_lwm": {"d": true, "t": 3, "a": [{"a": "innodb_max_dirty_pages_pct_lwm", "u": 59, "t": 2}, {"a": "sysvar_innodb_max_dirty_pages_pct_lwm", "u": 60, "t": 1}]}, "innodb_max_purge_lag": {"d": true, "t": 3, "a": [{"a": "innodb_max_purge_lag", "u": 59, "t": 2}, {"a": "sysvar_innodb_max_purge_lag", "u": 60, "t": 1}]}, "innodb_max_purge_lag_delay": {"d": true, "t": 3, "a": [{"a": "innodb_max_purge_lag_delay", "u": 59, "t": 2}, {"a": "sysvar_innodb_max_purge_lag_delay", "u": 60, "t": 1}]}, "innodb_max_purge_lag_wait": {"d": true, "t": 3, "a": [{"a": "innodb_max_purge_lag_wait", "u": 59, "t": 2}]}, "innodb_max_undo_log_size": {"d": true, "t": 3, "a": [{"a": "innodb_max_undo_log_size", "u": 59, "t": 2}, {"a": "sysvar_innodb_max_undo_log_size", "u": 60, "t": 1}]}, "innodb_merge_sort_block_size": {"d": true, "t": 3, "a": [{"a": "innodb_merge_sort_block_size", "u": 59, "t": 2}]}, "innodb_mirrored_log_groups": {"a": [{"a": "innodb_mirrored_log_groups", "u": 59, "t": 2}]}, "innodb_mtflush_threads": {"d": false, "t": 3, "a": [{"a": "innodb_mtflush_threads", "u": 59, "t": 2}]}, "innodb_monitor_disable": {"d": true, "t": 1, "a": [{"a": "innodb_monitor_disable", "u": 59, "t": 2}, {"a": "sysvar_innodb_monitor_disable", "u": 60, "t": 1}]}, "innodb_monitor_enable": {"d": true, "t": 1, "a": [{"a": "innodb_monitor_enable", "u": 59, "t": 2}, {"a": "sysvar_innodb_monitor_enable", "u": 60, "t": 1}]}, "innodb_monitor_reset": {"d": true, "a": [{"a": "innodb_monitor_reset", "u": 59, "t": 2}, {"a": "sysvar_innodb_monitor_reset", "u": 60, "t": 1}]}, "innodb_monitor_reset_all": {"d": true, "a": [{"a": "innodb_monitor_reset_all", "u": 59, "t": 2}, {"a": "sysvar_innodb_monitor_reset_all", "u": 60, "t": 1}]}, "innodb_numa_interleave": {"d": false, "t": 2, "a": [{"a": "innodb_numa_interleave", "u": 59, "t": 2}, {"a": "sysvar_innodb_numa_interleave", "u": 60, "t": 1}]}, "innodb_old_blocks_pct": {"d": true, "t": 3, "a": [{"a": "innodb_old_blocks_pct", "u": 59, "t": 2}, {"a": "sysvar_innodb_old_blocks_pct", "u": 60, "t": 1}]}, "innodb_old_blocks_time": {"d": true, "t": 3, "a": [{"a": "innodb_old_blocks_time", "u": 59, "t": 2}, {"a": "sysvar_innodb_old_blocks_time", "u": 60, "t": 1}]}, "innodb_online_alter_log_max_size": {"d": true, "t": 3, "a": [{"a": "innodb_online_alter_log_max_size", "u": 59, "t": 2}, {"a": "sysvar_innodb_online_alter_log_max_size", "u": 60, "t": 1}]}, "innodb_open_files": {"t": 3, "a": [{"a": "innodb_open_files", "u": 59, "t": 2}, {"a": "sysvar_innodb_open_files", "u": 60, "t": 1}]}, "innodb_optimize_fulltext_only": {"d": true, "t": 2, "a": [{"a": "innodb_optimize_fulltext_only", "u": 59, "t": 2}, {"a": "sysvar_innodb_optimize_fulltext_only", "u": 60, "t": 1}]}, "innodb_page_cleaners": {"d": false, "t": 3, "a": [{"a": "innodb_page_cleaners", "u": 59, "t": 2}, {"a": "sysvar_innodb_page_cleaners", "u": 60, "t": 1}]}, "innodb_page_size": {"d": false, "t": 5, "a": [{"a": "innodb_page_size", "u": 59, "t": 2}, {"a": "sysvar_innodb_page_size", "u": 60, "t": 1}]}, "innodb_pass_corrupt_table": {"a": [{"a": "innodb_pass_corrupt_table", "u": 59, "t": 2}]}, "innodb_prefix_index_cluster_optimization": {"d": true, "t": 2, "a": [{"a": "innodb_prefix_index_cluster_optimization", "u": 59, "t": 2}]}, "innodb_print_all_deadlocks": {"d": true, "t": 2, "a": [{"a": "innodb_print_all_deadlocks", "u": 59, "t": 2}, {"a": "sysvar_innodb_print_all_deadlocks", "u": 60, "t": 1}]}, "innodb_purge_batch_size": {"t": 3, "a": [{"a": "innodb_purge_batch_size", "u": 59, "t": 2}, {"a": "sysvar_innodb_purge_batch_size", "u": 60, "t": 1}]}, "innodb_purge_rseg_truncate_frequency": {"d": true, "t": 3, "a": [{"a": "innodb_purge_rseg_truncate_frequency", "u": 59, "t": 2}, {"a": "sysvar_innodb_purge_rseg_truncate_frequency", "u": 60, "t": 1}]}, "innodb_purge_threads": {"d": false, "t": 3, "a": [{"a": "innodb_purge_threads", "u": 59, "t": 2}, {"a": "sysvar_innodb_purge_threads", "u": 60, "t": 1}]}, "innodb_random_read_ahead": {"d": true, "t": 2, "a": [{"a": "innodb_random_read_ahead", "u": 59, "t": 2}, {"a": "sysvar_innodb_random_read_ahead", "u": 60, "t": 1}]}, "innodb_read_ahead": {"d": true, "t": 5, "a": [{"a": "innodb_read_ahead", "u": 59, "t": 2}]}, "innodb_read_ahead_threshold": {"d": true, "t": 3, "a": [{"a": "innodb_read_ahead_threshold", "u": 59, "t": 2}, {"a": "sysvar_innodb_read_ahead_threshold", "u": 60, "t": 1}]}, "innodb_read_io_threads": {"d": false, "t": 3, "a": [{"a": "innodb_read_io_threads", "u": 59, "t": 2}, {"a": "sysvar_innodb_read_io_threads", "u": 60, "t": 1}]}, "innodb_read_only": {"d": false, "t": 2, "a": [{"a": "innodb_read_only", "u": 59, "t": 2}, {"a": "sysvar_innodb_read_only", "u": 60, "t": 1}]}, "innodb_read_only_compressed": {"d": false, "t": 2, "a": [{"a": "innodb_read_only_compressed", "u": 59, "t": 2}]}, "innodb_recovery_stats": {"d": false, "t": 2, "a": [{"a": "innodb_recovery_stats", "u": 59, "t": 2}]}, "innodb_recovery_update_relay_log": {"d": false, "t": 2, "a": [{"a": "innodb_recovery_update_relay_log", "u": 59, "t": 2}]}, "innodb_replication_delay": {"d": true, "t": 3, "a": [{"a": "innodb_replication_delay", "u": 59, "t": 2}, {"a": "sysvar_innodb_replication_delay", "u": 60, "t": 1}]}, "innodb_rollback_on_timeout": {"d": false, "t": 2, "a": [{"a": "innodb_rollback_on_timeout", "u": 59, "t": 2}, {"a": "sysvar_innodb_rollback_on_timeout", "u": 60, "t": 1}]}, "innodb_rollback_segments": {"d": true, "t": 3, "a": [{"a": "innodb_rollback_segments", "u": 59, "t": 2}, {"a": "sysvar_innodb_rollback_segments", "u": 60, "t": 1}]}, "innodb_safe_truncate": {"d": false, "t": 2, "a": [{"a": "innodb_safe_truncate", "u": 59, "t": 2}]}, "innodb_scrub_log": {"d": false, "t": 2, "a": [{"a": "innodb_scrub_log", "u": 59, "t": 2}]}, "innodb_scrub_log_interval": {"d": true, "t": 3, "a": [{"a": "innodb_scrub_log_interval", "u": 59, "t": 2}]}, "innodb_scrub_log_speed": {"d": true, "t": 3, "a": [{"a": "innodb_scrub_log_speed", "u": 59, "t": 2}]}, "innodb_sched_priority_cleaner": {"d": true, "t": 3, "a": [{"a": "innodb_sched_priority_cleaner", "u": 59, "t": 2}]}, "innodb_show_locks_held": {"d": true, "t": 3, "a": [{"a": "innodb_show_locks_held", "u": 59, "t": 2}]}, "innodb_show_verbose_locks": {"d": true, "t": 3, "a": [{"a": "innodb_show_verbose_locks", "u": 59, "t": 2}]}, "innodb_simulate_comp_failures": {"d": true, "t": 3, "a": [{"a": "innodb_simulate_comp_failures", "u": 59, "t": 2}]}, "innodb_sort_buffer_size": {"d": false, "t": 3, "a": [{"a": "innodb_sort_buffer_size", "u": 59, "t": 2}, {"a": "sysvar_innodb_sort_buffer_size", "u": 60, "t": 1}]}, "innodb_spin_wait_delay": {"d": true, "t": 3, "a": [{"a": "innodb_spin_wait_delay", "u": 59, "t": 2}, {"a": "sysvar_innodb_spin_wait_delay", "u": 60, "t": 1}]}, "innodb_stats_auto_recalc": {"d": true, "t": 2, "a": [{"a": "innodb_stats_auto_recalc", "u": 59, "t": 2}, {"a": "sysvar_innodb_stats_auto_recalc", "u": 60, "t": 1}]}, "innodb_stats_auto_update": {"d": true, "t": 2, "a": [{"a": "innodb_stats_auto_update", "u": 59, "t": 2}]}, "innodb_stats_include_delete_marked": {"d": true, "t": 2, "a": [{"a": "innodb_stats_include_delete_marked", "u": 59, "t": 2}, {"a": "sysvar_innodb_stats_include_delete_marked", "u": 60, "t": 1}]}, "innodb_stats_method": {"d": true, "t": 5, "a": [{"a": "innodb_stats_method", "u": 59, "t": 2}, {"a": "sysvar_innodb_stats_method", "u": 60, "t": 1}]}, "innodb_stats_modified_counter": {"d": true, "t": 3, "a": [{"a": "innodb_stats_modified_counter", "u": 59, "t": 2}]}, "innodb_stats_on_metadata": {"d": true, "t": 2, "a": [{"a": "innodb_stats_on_metadata", "u": 59, "t": 2}, {"a": "sysvar_innodb_stats_on_metadata", "u": 60, "t": 1}]}, "innodb_stats_persistent": {"d": true, "t": 2, "a": [{"a": "innodb_stats_persistent", "u": 59, "t": 2}, {"a": "sysvar_innodb_stats_persistent", "u": 60, "t": 1}]}, "innodb_stats_persistent_sample_pages": {"d": true, "t": 3, "a": [{"a": "innodb_stats_persistent_sample_pages", "u": 59, "t": 2}, {"a": "sysvar_innodb_stats_persistent_sample_pages", "u": 60, "t": 1}]}, "innodb_stats_sample_pages": {"d": true, "t": 3, "a": [{"a": "innodb_stats_sample_pages", "u": 59, "t": 2}]}, "innodb_stats_traditional": {"d": true, "t": 2, "a": [{"a": "innodb_stats_traditional", "u": 59, "t": 2}]}, "innodb_stats_transient_sample_pages": {"d": true, "t": 3, "a": [{"a": "innodb_stats_transient_sample_pages", "u": 59, "t": 2}, {"a": "sysvar_innodb_stats_transient_sample_pages", "u": 60, "t": 1}]}, "innodb_stats_update_need_lock": {"d": true, "t": 2, "a": [{"a": "innodb_stats_update_need_lock", "u": 59, "t": 2}]}, "innodb_status_output": {"d": true, "t": 2, "a": [{"a": "innodb_status_output", "u": 59, "t": 2}, {"a": "sysvar_innodb_status_output", "u": 60, "t": 1}]}, "innodb_status_output_locks": {"d": true, "t": 2, "a": [{"a": "innodb_status_output_locks", "u": 59, "t": 2}, {"a": "sysvar_innodb_status_output_locks", "u": 60, "t": 1}]}, "innodb_strict_mode": {"d": true, "t": 2, "a": [{"a": "innodb_strict_mode", "u": 59, "t": 2}, {"a": "sysvar_innodb_strict_mode", "u": 60, "t": 1}]}, "innodb_support_xa": {"d": true, "t": 2, "a": [{"a": "innodb_support_xa", "u": 59, "t": 2}]}, "innodb_sync_array_size": {"d": false, "t": 3, "a": [{"a": "innodb_sync_array_size", "u": 59, "t": 2}, {"a": "sysvar_innodb_sync_array_size", "u": 60, "t": 1}]}, "innodb_sync_spin_loops": {"d": true, "t": 3, "a": [{"a": "innodb_sync_spin_loops", "u": 59, "t": 2}, {"a": "sysvar_innodb_sync_spin_loops", "u": 60, "t": 1}]}, "innodb_table_locks": {"d": true, "t": 2, "a": [{"a": "innodb_table_locks", "u": 59, "t": 2}, {"a": "sysvar_innodb_table_locks", "u": 60, "t": 1}]}, "innodb_thread_concurrency": {"d": true, "t": 3, "a": [{"a": "innodb_thread_concurrency", "u": 59, "t": 2}, {"a": "sysvar_innodb_thread_concurrency", "u": 60, "t": 1}]}, "innodb_thread_concurrency_timer_based": {"d": false, "t": 2, "a": [{"a": "innodb_thread_concurrency_timer_based", "u": 59, "t": 2}]}, "innodb_thread_sleep_delay": {"d": true, "t": 3, "a": [{"a": "innodb_thread_sleep_delay", "u": 59, "t": 2}, {"a": "sysvar_innodb_thread_sleep_delay", "u": 60, "t": 1}]}, "innodb_temp_data_file_path": {"d": false, "t": 1, "a": [{"a": "innodb_temp_data_file_path", "u": 59, "t": 2}, {"a": "sysvar_innodb_temp_data_file_path", "u": 60, "t": 1}]}, "innodb_tmpdir": {"d": true, "a": [{"a": "innodb_tmpdir", "u": 59, "t": 2}, {"a": "sysvar_innodb_tmpdir", "u": 60, "t": 1}]}, "innodb_track_changed_pages": {"d": false, "t": 2, "a": [{"a": "innodb_track_changed_pages", "u": 59, "t": 2}]}, "innodb_track_redo_log_now": {"d": true, "t": 2, "a": [{"a": "innodb_track_redo_log_now", "u": 59, "t": 2}]}, "innodb_undo_directory": {"d": false, "a": [{"a": "innodb_undo_directory", "u": 59, "t": 2}, {"a": "sysvar_innodb_undo_directory", "u": 60, "t": 1}]}, "innodb_undo_log_truncate": {"d": true, "t": 2, "a": [{"a": "innodb_undo_log_truncate", "u": 59, "t": 2}, {"a": "sysvar_innodb_undo_log_truncate", "u": 60, "t": 1}]}, "innodb_undo_logs": {"d": true, "t": 3, "a": [{"a": "innodb_undo_logs", "u": 59, "t": 2}]}, "innodb_undo_tablespaces": {"t": 3, "a": [{"a": "innodb_undo_tablespaces", "u": 59, "t": 2}, {"a": "sysvar_innodb_undo_tablespaces", "u": 60, "t": 1}]}, "innodb_use_atomic_writes": {"d": false, "t": 2, "a": [{"a": "innodb_use_atomic_writes", "u": 59, "t": 2}]}, "innodb_use_fallocate": {"d": false, "t": 2, "a": [{"a": "innodb_use_fallocate", "u": 59, "t": 2}]}, "innodb_use_global_flush_log_at_trx_commit": {"d": true, "t": 2, "a": [{"a": "innodb_use_global_flush_log_at_trx_commit", "u": 59, "t": 2}]}, "innodb_use_mtflush": {"d": false, "t": 2, "a": [{"a": "innodb_use_mtflush", "u": 59, "t": 2}]}, "innodb_use_native_aio": {"d": false, "t": 2, "a": [{"a": "innodb_use_native_aio", "u": 59, "t": 2}, {"a": "sysvar_innodb_use_native_aio", "u": 60, "t": 1}]}, "innodb_use_purge_thread": {"d": false, "t": 3, "a": [{"a": "innodb_use_purge_thread", "u": 59, "t": 2}]}, "innodb_use_stacktrace": {"d": false, "t": 2, "a": [{"a": "innodb_use_stacktrace", "u": 59, "t": 2}]}, "innodb_use_sys_malloc": {"d": false, "t": 2, "a": [{"a": "innodb_use_sys_malloc", "u": 59, "t": 2}]}, "innodb_use_sys_stats_table": {"d": false, "t": 2, "a": [{"a": "innodb_use_sys_stats_table", "u": 59, "t": 2}]}, "innodb_use_trim": {"d": false, "t": 2, "a": [{"a": "innodb_use_trim", "u": 59, "t": 2}]}, "innodb_version": {"d": false, "t": 1, "a": [{"a": "innodb_version", "u": 59, "t": 2}]}, "innodb_write_io_threads": {"d": false, "t": 3, "a": [{"a": "innodb_write_io_threads", "u": 59, "t": 2}, {"a": "sysvar_innodb_write_io_threads", "u": 60, "t": 1}]}, "audit_log": {"t": 5, "a": [{"a": "option_mysqld_audit-log", "u": 61, "t": 1}]}, "audit_log_buffer_size": {"d": false, "t": 3, "a": [{"a": "sysvar_audit_log_buffer_size", "u": 61, "t": 1}]}, "audit_log_disable": {"d": true, "t": 2, "a": [{"a": "sysvar_audit_log_disable", "u": 61, "t": 1}]}, "audit_log_compression": {"d": false, "t": 5, "a": [{"a": "sysvar_audit_log_compression", "u": 61, "t": 1}]}, "audit_log_connection_policy": {"d": true, "t": 5, "a": [{"a": "sysvar_audit_log_connection_policy", "u": 61, "t": 1}]}, "audit_log_current_session": {"d": false, "t": 2, "a": [{"a": "sysvar_audit_log_current_session", "u": 61, "t": 1}]}, "audit_log_encryption": {"d": false, "t": 5, "a": [{"a": "sysvar_audit_log_encryption", "u": 61, "t": 1}]}, "audit_log_exclude_accounts": {"d": true, "t": 1, "a": [{"a": "sysvar_audit_log_exclude_accounts", "u": 61, "t": 1}]}, "audit_log_file": {"d": false, "t": 8, "a": [{"a": "sysvar_audit_log_file", "u": 61, "t": 1}]}, "audit_log_filter_id": {"d": false, "t": 3, "a": [{"a": "sysvar_audit_log_filter_id", "u": 61, "t": 1}]}, "audit_log_flush": {"d": true, "t": 2, "a": [{"a": "sysvar_audit_log_flush", "u": 61, "t": 1}]}, "audit_log_format": {"d": false, "t": 5, "a": [{"a": "sysvar_audit_log_format", "u": 61, "t": 1}]}, "audit_log_format_unix_timestamp": {"d": true, "t": 2, "a": [{"a": "sysvar_audit_log_format_unix_timestamp", "u": 61, "t": 1}]}, "audit_log_include_accounts": {"d": true, "t": 1, "a": [{"a": "sysvar_audit_log_include_accounts", "u": 61, "t": 1}]}, "audit_log_max_size": {"d": true, "t": 3, "a": [{"a": "sysvar_audit_log_max_size", "u": 61, "t": 1}]}, "audit_log_password_history_keep_days": {"d": true, "t": 3, "a": [{"a": "sysvar_audit_log_password_history_keep_days", "u": 61, "t": 1}]}, "audit_log_policy": {"d": false, "t": 5, "a": [{"a": "sysvar_audit_log_policy", "u": 61, "t": 1}]}, "audit_log_prune_seconds": {"d": true, "t": 3, "a": [{"a": "sysvar_audit_log_prune_seconds", "u": 61, "t": 1}]}, "audit_log_read_buffer_size": {"t": 3, "a": [{"a": "sysvar_audit_log_read_buffer_size", "u": 61, "t": 1}]}, "audit_log_rotate_on_size": {"d": true, "t": 3, "a": [{"a": "sysvar_audit_log_rotate_on_size", "u": 61, "t": 1}]}, "audit_log_statement_policy": {"d": true, "t": 5, "a": [{"a": "sysvar_audit_log_statement_policy", "u": 61, "t": 1}]}, "audit_log_strategy": {"d": false, "t": 5, "a": [{"a": "sysvar_audit_log_strategy", "u": 61, "t": 1}]}, "innodb": {"t": 5, "a": [{"a": "option_mysqld_innodb", "u": 60, "t": 1}]}, "innodb_status_file": {"t": 2, "a": [{"a": "option_mysqld_innodb-status-file", "u": 60, "t": 1}]}, "daemon_memcached_enable_binlog": {"d": false, "t": 2, "a": [{"a": "sysvar_daemon_memcached_enable_binlog", "u": 60, "t": 1}]}, "daemon_memcached_engine_lib_name": {"d": false, "t": 8, "a": [{"a": "sysvar_daemon_memcached_engine_lib_name", "u": 60, "t": 1}]}, "daemon_memcached_engine_lib_path": {"d": false, "t": 7, "a": [{"a": "sysvar_daemon_memcached_engine_lib_path", "u": 60, "t": 1}]}, "daemon_memcached_option": {"d": false, "t": 1, "a": [{"a": "sysvar_daemon_memcached_option", "u": 60, "t": 1}]}, "daemon_memcached_r_batch_size": {"d": false, "t": 3, "a": [{"a": "sysvar_daemon_memcached_r_batch_size", "u": 60, "t": 1}]}, "daemon_memcached_w_batch_size": {"d": false, "t": 3, "a": [{"a": "sysvar_daemon_memcached_w_batch_size", "u": 60, "t": 1}]}, "innodb_background_drop_list_empty": {"d": true, "t": 2, "a": [{"a": "sysvar_innodb_background_drop_list_empty", "u": 60, "t": 1}]}, "innodb_buffer_pool_debug": {"d": false, "t": 2, "a": [{"a": "sysvar_innodb_buffer_pool_debug", "u": 60, "t": 1}]}, "innodb_buffer_pool_in_core_file": {"d": true, "t": 2, "a": [{"a": "sysvar_innodb_buffer_pool_in_core_file", "u": 60, "t": 1}]}, "innodb_checkpoint_disabled": {"d": true, "t": 2, "a": [{"a": "sysvar_innodb_checkpoint_disabled", "u": 60, "t": 1}]}, "innodb_compress_debug": {"d": true, "t": 5, "a": [{"a": "sysvar_innodb_compress_debug", "u": 60, "t": 1}]}, "innodb_ddl_buffer_size": {"d": true, "t": 3, "a": [{"a": "sysvar_innodb_ddl_buffer_size", "u": 60, "t": 1}]}, "innodb_ddl_log_crash_reset_debug": {"d": true, "t": 2, "a": [{"a": "sysvar_innodb_ddl_log_crash_reset_debug", "u": 60, "t": 1}]}, "innodb_ddl_threads": {"d": true, "t": 3, "a": [{"a": "sysvar_innodb_ddl_threads", "u": 60, "t": 1}]}, "innodb_dedicated_server": {"d": false, "t": 2, "a": [{"a": "sysvar_innodb_dedicated_server", "u": 60, "t": 1}]}, "innodb_directories": {"d": false, "t": 7, "a": [{"a": "sysvar_innodb_directories", "u": 60, "t": 1}]}, "innodb_doublewrite_batch_size": {"d": false, "t": 3, "a": [{"a": "sysvar_innodb_doublewrite_batch_size", "u": 60, "t": 1}]}, "innodb_doublewrite_dir": {"d": false, "t": 7, "a": [{"a": "sysvar_innodb_doublewrite_dir", "u": 60, "t": 1}]}, "innodb_doublewrite_files": {"d": false, "t": 3, "a": [{"a": "sysvar_innodb_doublewrite_files", "u": 60, "t": 1}]}, "innodb_doublewrite_pages": {"d": false, "t": 3, "a": [{"a": "sysvar_innodb_doublewrite_pages", "u": 60, "t": 1}]}, "innodb_extend_and_initialize": {"d": true, "t": 2, "a": [{"a": "sysvar_innodb_extend_and_initialize", "u": 60, "t": 1}]}, "innodb_fil_make_page_dirty_debug": {"d": true, "t": 3, "a": [{"a": "sysvar_innodb_fil_make_page_dirty_debug", "u": 60, "t": 1}]}, "innodb_fsync_threshold": {"d": true, "t": 3, "a": [{"a": "sysvar_innodb_fsync_threshold", "u": 60, "t": 1}]}, "innodb_limit_optimistic_insert_debug": {"d": true, "t": 3, "a": [{"a": "sysvar_innodb_limit_optimistic_insert_debug", "u": 60, "t": 1}]}, "innodb_log_checkpoint_fuzzy_now": {"d": true, "t": 2, "a": [{"a": "sysvar_innodb_log_checkpoint_fuzzy_now", "u": 60, "t": 1}]}, "innodb_log_checkpoint_now": {"d": true, "t": 2, "a": [{"a": "sysvar_innodb_log_checkpoint_now", "u": 60, "t": 1}]}, "innodb_log_spin_cpu_abs_lwm": {"d": true, "t": 3, "a": [{"a": "sysvar_innodb_log_spin_cpu_abs_lwm", "u": 60, "t": 1}]}, "innodb_log_spin_cpu_pct_hwm": {"d": true, "t": 3, "a": [{"a": "sysvar_innodb_log_spin_cpu_pct_hwm", "u": 60, "t": 1}]}, "innodb_log_wait_for_flush_spin_hwm": {"d": true, "t": 3, "a": [{"a": "sysvar_innodb_log_wait_for_flush_spin_hwm", "u": 60, "t": 1}]}, "innodb_log_writer_threads": {"d": true, "t": 2, "a": [{"a": "sysvar_innodb_log_writer_threads", "u": 60, "t": 1}]}, "innodb_merge_threshold_set_all_debug": {"d": true, "t": 3, "a": [{"a": "sysvar_innodb_merge_threshold_set_all_debug", "u": 60, "t": 1}]}, "innodb_parallel_read_threads": {"d": true, "t": 3, "a": [{"a": "sysvar_innodb_parallel_read_threads", "u": 60, "t": 1}]}, "innodb_print_ddl_logs": {"d": true, "t": 2, "a": [{"a": "sysvar_innodb_print_ddl_logs", "u": 60, "t": 1}]}, "innodb_redo_log_archive_dirs": {"d": true, "t": 1, "a": [{"a": "sysvar_innodb_redo_log_archive_dirs", "u": 60, "t": 1}]}, "innodb_redo_log_capacity": {"d": true, "t": 3, "a": [{"a": "sysvar_innodb_redo_log_capacity", "u": 60, "t": 1}]}, "innodb_redo_log_encrypt": {"d": true, "t": 2, "a": [{"a": "sysvar_innodb_redo_log_encrypt", "u": 60, "t": 1}]}, "innodb_saved_page_number_debug": {"d": true, "t": 3, "a": [{"a": "sysvar_innodb_saved_page_number_debug", "u": 60, "t": 1}]}, "innodb_segment_reserve_factor": {"d": true, "t": 4, "a": [{"a": "sysvar_innodb_segment_reserve_factor", "u": 60, "t": 1}]}, "innodb_spin_wait_pause_multiplier": {"d": true, "t": 3, "a": [{"a": "sysvar_innodb_spin_wait_pause_multiplier", "u": 60, "t": 1}]}, "innodb_sync_debug": {"d": false, "t": 2, "a": [{"a": "sysvar_innodb_sync_debug", "u": 60, "t": 1}]}, "innodb_temp_tablespaces_dir": {"d": false, "t": 7, "a": [{"a": "sysvar_innodb_temp_tablespaces_dir", "u": 60, "t": 1}]}, "innodb_trx_purge_view_update_only_debug": {"d": true, "t": 2, "a": [{"a": "sysvar_innodb_trx_purge_view_update_only_debug", "u": 60, "t": 1}]}, "innodb_trx_rseg_n_slots_debug": {"d": true, "t": 3, "a": [{"a": "sysvar_innodb_trx_rseg_n_slots_debug", "u": 60, "t": 1}]}, "innodb_undo_log_encrypt": {"d": true, "t": 2, "a": [{"a": "sysvar_innodb_undo_log_encrypt", "u": 60, "t": 1}]}, "innodb_use_fdatasync": {"d": true, "t": 2, "a": [{"a": "sysvar_innodb_use_fdatasync", "u": 60, "t": 1}]}, "innodb_validate_tablespace_paths": {"d": false, "t": 2, "a": [{"a": "sysvar_innodb_validate_tablespace_paths", "u": 60, "t": 1}]}, "ndbcluster": {"t": 2, "a": [{"a": "option_mysqld_ndbcluster", "u": 62, "t": 1}]}, "ndb_allow_copying_alter_table": {"d": true, "t": 2, "a": [{"a": "option_mysqld_ndb-allow-copying-alter-table", "u": 62, "t": 1}]}, "ndb_batch_size": {"d": false, "t": 3, "a": [{"a": "option_mysqld_ndb-batch-size", "u": 62, "t": 1}]}, "ndb_cluster_connection_pool": {"d": false, "t": 3, "a": [{"a": "option_mysqld_ndb-cluster-connection-pool", "u": 62, "t": 1}]}, "ndb_cluster_connection_pool_nodeids": {"t": 6, "a": [{"a": "option_mysqld_ndb-cluster-connection-pool-nodeids", "u": 62, "t": 1}]}, "ndb_blob_read_batch_bytes": {"d": true, "t": 3, "a": [{"a": "option_mysqld_ndb-blob-read-batch-bytes", "u": 62, "t": 1}]}, "ndb_blob_write_batch_bytes": {"d": true, "t": 3, "a": [{"a": "option_mysqld_ndb-blob-write-batch-bytes", "u": 62, "t": 1}]}, "ndb_connectstring": {"t": 1, "a": [{"a": "option_mysqld_ndb-connectstring", "u": 62, "t": 1}]}, "ndb_default_column_format": {"d": true, "t": 5, "a": [{"a": "option_mysqld_ndb-default-column-format", "u": 62, "t": 1}, {"a": "sysvar_ndb_default_column_format", "u": 62, "t": 1}]}, "ndb_deferred_constraints": {"d": true, "t": 3, "a": [{"a": "option_mysqld_ndb-deferred-constraints", "u": 62, "t": 1}, {"a": "sysvar_ndb_deferred_constraints", "u": 62, "t": 1}]}, "ndb_distribution": {"d": true, "t": 5, "a": [{"a": "option_mysqld_ndb-distribution", "u": 62, "t": 1}, {"a": "sysvar_ndb_distribution", "u": 62, "t": 1}]}, "ndb_log_apply_status": {"d": false, "t": 2, "a": [{"a": "option_mysqld_ndb-log-apply-status", "u": 62, "t": 1}, {"a": "sysvar_ndb_log_apply_status", "u": 62, "t": 1}]}, "ndb_log_empty_epochs": {"d": true, "t": 2, "a": [{"a": "option_mysqld_ndb-log-empty-epochs", "u": 62, "t": 1}, {"a": "sysvar_ndb_log_empty_epochs", "u": 62, "t": 1}]}, "ndb_log_empty_update": {"d": true, "t": 2, "a": [{"a": "option_mysqld_ndb-log-empty-update", "u": 62, "t": 1}, {"a": "sysvar_ndb_log_empty_update", "u": 62, "t": 1}]}, "ndb_log_exclusive_reads": {"d": true, "t": 2, "a": [{"a": "option_mysqld_ndb-log-exclusive-reads", "u": 62, "t": 1}, {"a": "sysvar_ndb_log_exclusive_reads", "u": 62, "t": 1}]}, "ndb_log_fail_terminate": {"d": false, "t": 2, "a": [{"a": "option_mysqld_ndb-log-fail-terminate", "u": 62, "t": 1}]}, "ndb_log_orig": {"d": false, "t": 2, "a": [{"a": "option_mysqld_ndb-log-orig", "u": 62, "t": 1}, {"a": "sysvar_ndb_log_orig", "u": 62, "t": 1}]}, "ndb_log_transaction_id": {"d": false, "t": 2, "a": [{"a": "option_mysqld_ndb-log-transaction-id", "u": 62, "t": 1}, {"a": "sysvar_ndb_log_transaction_id", "u": 62, "t": 1}]}, "ndb_log_update_as_write": {"d": true, "t": 2, "a": [{"a": "option_mysqld_ndb-log-update-as-write", "u": 62, "t": 1}]}, "ndb_log_updated_only": {"d": true, "t": 2, "a": [{"a": "option_mysqld_ndb-log-updated-only", "u": 62, "t": 1}]}, "ndb_log_update_minimal": {"d": true, "t": 2, "a": [{"a": "option_mysqld_ndb-log-update-minimal", "u": 62, "t": 1}]}, "ndb_mgmd_host": {"t": 1, "a": [{"a": "option_mysqld_ndb-mgmd-host", "u": 62, "t": 1}]}, "ndb_nodeid": {"d": false, "t": 3, "a": [{"a": "option_mysqld_ndb-nodeid", "u": 62, "t": 1}]}, "ndb_optimization_delay": {"d": true, "t": 3, "a": [{"a": "option_mysqld_ndb-optimization-delay", "u": 62, "t": 1}]}, "ndb_optimized_node_selection": {"d": false, "t": 3, "a": [{"a": "option_mysqld_ndb-optimized-node-selection", "u": 62, "t": 1}, {"a": "sysvar_ndb_optimized_node_selection", "u": 62, "t": 1}]}, "ndb_transid_mysql_connection_map": {"t": 5, "a": [{"a": "option_mysqld_ndb-transid-mysql-connection-map", "u": 62, "t": 1}]}, "ndb_wait_connected": {"d": false, "t": 3, "a": [{"a": "option_mysqld_ndb-wait-connected", "u": 62, "t": 1}]}, "ndb_wait_setup": {"d": false, "t": 3, "a": [{"a": "option_mysqld_ndb-wait-setup", "u": 62, "t": 1}]}, "skip_ndbcluster": {"a": [{"a": "option_mysqld_skip-ndbcluster", "u": 62, "t": 1}]}, "ndb_autoincrement_prefetch_sz": {"d": true, "t": 3, "a": [{"a": "sysvar_ndb_autoincrement_prefetch_sz", "u": 62, "t": 1}]}, "ndb_cache_check_time": {"d": true, "t": 3, "a": [{"a": "sysvar_ndb_cache_check_time", "u": 62, "t": 1}]}, "ndb_clear_apply_status": {"d": true, "t": 2, "a": [{"a": "sysvar_ndb_clear_apply_status", "u": 62, "t": 1}]}, "ndb_data_node_neighbour": {"d": true, "t": 3, "a": [{"a": "sysvar_ndb_data_node_neighbour", "u": 62, "t": 1}]}, "ndb_eventbuffer_free_percent": {"d": true, "t": 3, "a": [{"a": "sysvar_ndb_eventbuffer_free_percent", "u": 62, "t": 1}]}, "ndb_eventbuffer_max_alloc": {"d": true, "t": 3, "a": [{"a": "sysvar_ndb_eventbuffer_max_alloc", "u": 62, "t": 1}]}, "ndb_extra_logging": {"d": true, "t": 3, "a": [{"a": "sysvar_ndb_extra_logging", "u": 62, "t": 1}]}, "ndb_force_send": {"d": true, "t": 2, "a": [{"a": "sysvar_ndb_force_send", "u": 62, "t": 1}]}, "ndb_fully_replicated": {"d": true, "t": 2, "a": [{"a": "sysvar_ndb_fully_replicated", "u": 62, "t": 1}]}, "ndb_index_stat_enable": {"d": true, "t": 2, "a": [{"a": "sysvar_ndb_index_stat_enable", "u": 62, "t": 1}]}, "ndb_index_stat_option": {"d": true, "t": 1, "a": [{"a": "sysvar_ndb_index_stat_option", "u": 62, "t": 1}]}, "ndb_join_pushdown": {"d": true, "t": 2, "a": [{"a": "sysvar_ndb_join_pushdown", "u": 62, "t": 1}]}, "ndb_log_bin": {"d": false, "t": 2, "a": [{"a": "sysvar_ndb_log_bin", "u": 62, "t": 1}]}, "ndb_log_binlog_index": {"d": true, "t": 2, "a": [{"a": "sysvar_ndb_log_binlog_index", "u": 62, "t": 1}]}, "ndb_read_backup": {"d": true, "t": 2, "a": [{"a": "sysvar_ndb_read_backup", "u": 62, "t": 1}]}, "ndb_recv_thread_activation_threshold": {"d": true, "t": 3, "a": [{"a": "sysvar_ndb_recv_thread_activation_threshold", "u": 62, "t": 1}]}, "ndb_recv_thread_cpu_mask": {"d": true, "a": [{"a": "sysvar_ndb_recv_thread_cpu_mask", "u": 62, "t": 1}]}, "ndb_report_thresh_binlog_epoch_slip": {"d": true, "t": 3, "a": [{"a": "sysvar_ndb_report_thresh_binlog_epoch_slip", "u": 62, "t": 1}]}, "ndb_report_thresh_binlog_mem_usage": {"d": true, "t": 3, "a": [{"a": "sysvar_ndb_report_thresh_binlog_mem_usage", "u": 62, "t": 1}]}, "ndb_row_checksum": {"d": true, "t": 3, "a": [{"a": "sysvar_ndb_row_checksum", "u": 62, "t": 1}]}, "ndb_show_foreign_key_mock_tables": {"d": true, "t": 2, "a": [{"a": "sysvar_ndb_show_foreign_key_mock_tables", "u": 62, "t": 1}]}, "ndb_slave_conflict_role": {"d": true, "t": 5, "a": [{"a": "sysvar_ndb_slave_conflict_role", "u": 62, "t": 1}]}, "ndb_table_no_logging": {"d": true, "t": 2, "a": [{"a": "sysvar_ndb_table_no_logging", "u": 62, "t": 1}]}, "ndb_table_temporary": {"d": true, "t": 2, "a": [{"a": "sysvar_ndb_table_temporary", "u": 62, "t": 1}]}, "ndb_use_copying_alter_table": {"d": false, "a": [{"a": "sysvar_ndb_use_copying_alter_table", "u": 62, "t": 1}]}, "ndb_use_exact_count": {"d": true, "t": 2, "a": [{"a": "sysvar_ndb_use_exact_count", "u": 62, "t": 1}]}, "ndb_use_transactions": {"d": true, "t": 2, "a": [{"a": "sysvar_ndb_use_transactions", "u": 62, "t": 1}]}, "ndb_version": {"d": false, "t": 1, "a": [{"a": "sysvar_ndb_version", "u": 62, "t": 1}]}, "ndb_version_string": {"d": false, "t": 1, "a": [{"a": "sysvar_ndb_version_string", "u": 62, "t": 1}]}, "server_id_bits": {"d": false, "t": 3, "a": [{"a": "sysvar_server_id_bits", "u": 62, "t": 1}]}, "slave_allow_batching": {"d": true, "t": 2, "a": [{"a": "sysvar_slave_allow_batching", "u": 62, "t": 1}]}, "transaction_allow_batching": {"d": true, "t": 2, "a": [{"a": "sysvar_transaction_allow_batching", "u": 62, "t": 1}]}, "ndbinfo_database": {"d": false, "t": 1, "a": [{"a": "sysvar_ndbinfo_database", "u": 62, "t": 1}]}, "ndbinfo_max_bytes": {"d": true, "t": 3, "a": [{"a": "sysvar_ndbinfo_max_bytes", "u": 62, "t": 1}]}, "ndbinfo_max_rows": {"d": true, "t": 3, "a": [{"a": "sysvar_ndbinfo_max_rows", "u": 62, "t": 1}]}, "ndbinfo_offline": {"d": true, "t": 2, "a": [{"a": "sysvar_ndbinfo_offline", "u": 62, "t": 1}]}, "ndbinfo_show_hidden": {"d": true, "t": 2, "a": [{"a": "sysvar_ndbinfo_show_hidden", "u": 62, "t": 1}]}, "ndbinfo_table_prefix": {"d": false, "t": 1, "a": [{"a": "sysvar_ndbinfo_table_prefix", "u": 62, "t": 1}]}, "ndbinfo_version": {"d": false, "t": 1, "a": [{"a": "sysvar_ndbinfo_version", "u": 62, "t": 1}]}, "performance_schema_error_size": {"d": false, "t": 3, "a": [{"a": "sysvar_performance_schema_error_size", "u": 32, "t": 1}]}, "performance_schema_max_digest_sample_age": {"d": true, "t": 3, "a": [{"a": "sysvar_performance_schema_max_digest_sample_age", "u": 32, "t": 1}]}, "performance_schema_max_prepared_statements_instances": {"d": false, "t": 3, "a": [{"a": "sysvar_performance_schema_max_prepared_statements_instances", "u": 32, "t": 1}]}, "performance_schema_show_processlist": {"d": true, "t": 2, "a": [{"a": "sysvar_performance_schema_show_processlist", "u": 32, "t": 1}]}, "authentication_fido_rp_id": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_fido_rp_id", "u": 63, "t": 1}]}, "authentication_kerberos_service_key_tab": {"d": false, "t": 8, "a": [{"a": "sysvar_authentication_kerberos_service_key_tab", "u": 63, "t": 1}]}, "authentication_kerberos_service_principal": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_kerberos_service_principal", "u": 63, "t": 1}]}, "authentication_ldap_sasl_auth_method_name": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_ldap_sasl_auth_method_name", "u": 63, "t": 1}]}, "authentication_ldap_sasl_bind_base_dn": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_ldap_sasl_bind_base_dn", "u": 63, "t": 1}]}, "authentication_ldap_sasl_bind_root_dn": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_ldap_sasl_bind_root_dn", "u": 63, "t": 1}]}, "authentication_ldap_sasl_bind_root_pwd": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_ldap_sasl_bind_root_pwd", "u": 63, "t": 1}]}, "authentication_ldap_sasl_ca_path": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_ldap_sasl_ca_path", "u": 63, "t": 1}]}, "authentication_ldap_sasl_group_search_attr": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_ldap_sasl_group_search_attr", "u": 63, "t": 1}]}, "authentication_ldap_sasl_group_search_filter": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_ldap_sasl_group_search_filter", "u": 63, "t": 1}]}, "authentication_ldap_sasl_init_pool_size": {"d": true, "t": 3, "a": [{"a": "sysvar_authentication_ldap_sasl_init_pool_size", "u": 63, "t": 1}]}, "authentication_ldap_sasl_log_status": {"d": true, "t": 3, "a": [{"a": "sysvar_authentication_ldap_sasl_log_status", "u": 63, "t": 1}]}, "authentication_ldap_sasl_max_pool_size": {"d": true, "t": 3, "a": [{"a": "sysvar_authentication_ldap_sasl_max_pool_size", "u": 63, "t": 1}]}, "authentication_ldap_sasl_referral": {"d": true, "t": 2, "a": [{"a": "sysvar_authentication_ldap_sasl_referral", "u": 63, "t": 1}]}, "authentication_ldap_sasl_server_host": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_ldap_sasl_server_host", "u": 63, "t": 1}]}, "authentication_ldap_sasl_server_port": {"d": true, "t": 3, "a": [{"a": "sysvar_authentication_ldap_sasl_server_port", "u": 63, "t": 1}]}, "authentication_ldap_sasl_tls": {"d": true, "t": 2, "a": [{"a": "sysvar_authentication_ldap_sasl_tls", "u": 63, "t": 1}]}, "authentication_ldap_sasl_user_search_attr": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_ldap_sasl_user_search_attr", "u": 63, "t": 1}]}, "authentication_ldap_simple_auth_method_name": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_ldap_simple_auth_method_name", "u": 63, "t": 1}]}, "authentication_ldap_simple_bind_base_dn": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_ldap_simple_bind_base_dn", "u": 63, "t": 1}]}, "authentication_ldap_simple_bind_root_dn": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_ldap_simple_bind_root_dn", "u": 63, "t": 1}]}, "authentication_ldap_simple_bind_root_pwd": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_ldap_simple_bind_root_pwd", "u": 63, "t": 1}]}, "authentication_ldap_simple_ca_path": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_ldap_simple_ca_path", "u": 63, "t": 1}]}, "authentication_ldap_simple_group_search_attr": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_ldap_simple_group_search_attr", "u": 63, "t": 1}]}, "authentication_ldap_simple_group_search_filter": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_ldap_simple_group_search_filter", "u": 63, "t": 1}]}, "authentication_ldap_simple_init_pool_size": {"d": true, "t": 3, "a": [{"a": "sysvar_authentication_ldap_simple_init_pool_size", "u": 63, "t": 1}]}, "authentication_ldap_simple_log_status": {"d": true, "t": 3, "a": [{"a": "sysvar_authentication_ldap_simple_log_status", "u": 63, "t": 1}]}, "authentication_ldap_simple_max_pool_size": {"d": true, "t": 3, "a": [{"a": "sysvar_authentication_ldap_simple_max_pool_size", "u": 63, "t": 1}]}, "authentication_ldap_simple_referral": {"d": true, "t": 2, "a": [{"a": "sysvar_authentication_ldap_simple_referral", "u": 63, "t": 1}]}, "authentication_ldap_simple_server_host": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_ldap_simple_server_host", "u": 63, "t": 1}]}, "authentication_ldap_simple_server_port": {"d": true, "t": 3, "a": [{"a": "sysvar_authentication_ldap_simple_server_port", "u": 63, "t": 1}]}, "authentication_ldap_simple_tls": {"d": true, "t": 2, "a": [{"a": "sysvar_authentication_ldap_simple_tls", "u": 63, "t": 1}]}, "authentication_ldap_simple_user_search_attr": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_ldap_simple_user_search_attr", "u": 63, "t": 1}]}, "binlog_row_event_max_size": {"t": 3, "a": [{"a": "option_mysqld_binlog-row-event-max-size", "u": 36, "t": 1}, {"a": "sysvar_binlog_row_event_max_size", "u": 36, "t": 1}, {"a": "option_mysqld_binlog-row-event-max-size", "u": 37, "t": 1}]}, "binlog_do_db": {"t": 1, "a": [{"a": "option_mysqld_binlog-do-db", "u": 36, "t": 1}, {"a": "option_mysqld_binlog-do-db", "u": 37, "t": 1}]}, "binlog_ignore_db": {"t": 1, "a": [{"a": "option_mysqld_binlog-ignore-db", "u": 36, "t": 1}, {"a": "option_mysqld_binlog-ignore-db", "u": 37, "t": 1}]}, "max_binlog_dump_events": {"t": 3, "a": [{"a": "option_mysqld_max-binlog-dump-events", "u": 36, "t": 1}, {"a": "option_mysqld_max-binlog-dump-events", "u": 37, "t": 1}]}, "sporadic_binlog_dump_fail": {"t": 2, "a": [{"a": "option_mysqld_sporadic-binlog-dump-fail", "u": 36, "t": 1}, {"a": "option_mysqld_sporadic-binlog-dump-fail", "u": 37, "t": 1}]}, "binlog_encryption": {"d": true, "t": 2, "a": [{"a": "sysvar_binlog_encryption", "u": 36, "t": 1}]}, "binlog_error_action": {"d": true, "t": 5, "a": [{"a": "sysvar_binlog_error_action", "u": 36, "t": 1}, {"a": "sysvar_binlog_error_action", "u": 37, "t": 1}]}, "binlog_expire_logs_auto_purge": {"d": true, "t": 2, "a": [{"a": "sysvar_binlog_expire_logs_auto_purge", "u": 36, "t": 1}]}, "binlog_group_commit_sync_delay": {"d": true, "t": 3, "a": [{"a": "sysvar_binlog_group_commit_sync_delay", "u": 36, "t": 1}, {"a": "sysvar_binlog_group_commit_sync_delay", "u": 37, "t": 1}]}, "binlog_group_commit_sync_no_delay_count": {"d": true, "t": 3, "a": [{"a": "sysvar_binlog_group_commit_sync_no_delay_count", "u": 36, "t": 1}, {"a": "sysvar_binlog_group_commit_sync_no_delay_count", "u": 37, "t": 1}]}, "binlog_max_flush_queue_time": {"d": true, "t": 3, "a": [{"a": "sysvar_binlog_max_flush_queue_time", "u": 36, "t": 1}, {"a": "sysvar_binlog_max_flush_queue_time", "u": 37, "t": 1}]}, "binlog_order_commits": {"d": true, "t": 2, "a": [{"a": "sysvar_binlog_order_commits", "u": 36, "t": 1}, {"a": "sysvar_binlog_order_commits", "u": 37, "t": 1}]}, "binlog_rotate_encryption_master_key_at_startup": {"d": false, "t": 2, "a": [{"a": "sysvar_binlog_rotate_encryption_master_key_at_startup", "u": 36, "t": 1}]}, "binlog_row_value_options": {"d": true, "t": 6, "a": [{"a": "sysvar_binlog_row_value_options", "u": 36, "t": 1}]}, "binlog_rows_query_log_events": {"d": true, "t": 2, "a": [{"a": "sysvar_binlog_rows_query_log_events", "u": 36, "t": 1}, {"a": "sysvar_binlog_rows_query_log_events", "u": 37, "t": 1}]}, "binlog_transaction_compression": {"d": true, "t": 2, "a": [{"a": "sysvar_binlog_transaction_compression", "u": 36, "t": 1}]}, "binlog_transaction_compression_level_zstd": {"d": true, "t": 3, "a": [{"a": "sysvar_binlog_transaction_compression_level_zstd", "u": 36, "t": 1}]}, "binlog_transaction_dependency_tracking": {"d": true, "t": 5, "a": [{"a": "sysvar_binlog_transaction_dependency_tracking", "u": 36, "t": 1}, {"a": "sysvar_binlog_transaction_dependency_tracking", "u": 37, "t": 1}]}, "binlog_transaction_dependency_history_size": {"d": true, "t": 3, "a": [{"a": "sysvar_binlog_transaction_dependency_history_size", "u": 36, "t": 1}, {"a": "sysvar_binlog_transaction_dependency_history_size", "u": 37, "t": 1}]}, "log_bin_use_v1_row_events": {"d": true, "t": 2, "a": [{"a": "sysvar_log_bin_use_v1_row_events", "u": 36, "t": 1}, {"a": "sysvar_log_bin_use_v1_row_events", "u": 37, "t": 1}]}, "log_replica_updates": {"d": false, "t": 2, "a": [{"a": "sysvar_log_replica_updates", "u": 36, "t": 1}]}, "log_statements_unsafe_for_binlog": {"d": true, "t": 2, "a": [{"a": "sysvar_log_statements_unsafe_for_binlog", "u": 36, "t": 1}, {"a": "sysvar_log_statements_unsafe_for_binlog", "u": 37, "t": 1}]}, "original_commit_timestamp": {"d": true, "t": 4, "a": [{"a": "sysvar_original_commit_timestamp", "u": 36, "t": 1}]}, "source_verify_checksum": {"d": true, "t": 2, "a": [{"a": "sysvar_source_verify_checksum", "u": 36, "t": 1}]}, "transaction_write_set_extraction": {"d": true, "t": 5, "a": [{"a": "sysvar_transaction_write_set_extraction", "u": 36, "t": 1}, {"a": "sysvar_transaction_write_set_extraction", "u": 37, "t": 1}]}, "log_builtin_as_identified_by_password": {"d": true, "t": 2, "a": [{"a": "sysvar_log_builtin_as_identified_by_password", "u": 37, "t": 1}]}, "binlog_gtid_simple_recovery": {"d": false, "t": 2, "a": [{"a": "sysvar_binlog_gtid_simple_recovery", "u": 64, "t": 1}]}, "enforce_gtid_consistency": {"d": true, "t": 5, "a": [{"a": "sysvar_enforce_gtid_consistency", "u": 64, "t": 1}]}, "gtid_executed": {"d": false, "t": 1, "a": [{"a": "sysvar_gtid_executed", "u": 64, "t": 1}]}, "gtid_executed_compression_period": {"d": true, "t": 3, "a": [{"a": "sysvar_gtid_executed_compression_period", "u": 64, "t": 1}]}, "gtid_mode": {"d": true, "t": 5, "a": [{"a": "sysvar_gtid_mode", "u": 64, "t": 1}]}, "gtid_next": {"d": true, "t": 5, "a": [{"a": "sysvar_gtid_next", "u": 64, "t": 1}]}, "gtid_owned": {"d": false, "t": 1, "a": [{"a": "sysvar_gtid_owned", "u": 64, "t": 1}]}, "gtid_purged": {"d": true, "t": 1, "a": [{"a": "sysvar_gtid_purged", "u": 64, "t": 1}]}, "master_info_file": {"t": 8, "a": [{"a": "option_mysqld_master-info-file", "u": 38, "t": 1}]}, "master_retry_count": {"t": 3, "a": [{"a": "option_mysqld_master-retry-count", "u": 38, "t": 1}]}, "replicate_same_server_id": {"t": 2, "a": [{"a": "option_mysqld_replicate-same-server-id", "u": 38, "t": 1}]}, "skip_replica_start": {"d": false, "t": 2, "a": [{"a": "option_mysqld_skip-replica-start", "u": 38, "t": 1}, {"a": "sysvar_skip_replica_start", "u": 38, "t": 1}]}, "skip_slave_start": {"d": false, "t": 2, "a": [{"a": "option_mysqld_skip-slave-start", "u": 38, "t": 1}, {"a": "sysvar_skip_slave_start", "u": 38, "t": 1}]}, "abort_slave_event_count": {"t": 3, "a": [{"a": "option_mysqld_abort-slave-event-count", "u": 38, "t": 1}]}, "disconnect_slave_event_count": {"t": 3, "a": [{"a": "option_mysqld_disconnect-slave-event-count", "u": 38, "t": 1}]}, "init_replica": {"d": true, "t": 1, "a": [{"a": "sysvar_init_replica", "u": 38, "t": 1}]}, "log_slow_replica_statements": {"d": true, "t": 2, "a": [{"a": "sysvar_log_slow_replica_statements", "u": 38, "t": 1}]}, "master_info_repository": {"d": true, "t": 1, "a": [{"a": "sysvar_master_info_repository", "u": 38, "t": 1}]}, "relay_log_info_repository": {"d": true, "t": 1, "a": [{"a": "sysvar_relay_log_info_repository", "u": 38, "t": 1}]}, "replica_checkpoint_group": {"d": true, "t": 3, "a": [{"a": "sysvar_replica_checkpoint_group", "u": 38, "t": 1}]}, "replica_checkpoint_period": {"d": true, "t": 3, "a": [{"a": "sysvar_replica_checkpoint_period", "u": 38, "t": 1}]}, "replica_compressed_protocol": {"d": true, "t": 2, "a": [{"a": "sysvar_replica_compressed_protocol", "u": 38, "t": 1}]}, "replica_exec_mode": {"d": true, "t": 5, "a": [{"a": "sysvar_replica_exec_mode", "u": 38, "t": 1}]}, "replica_load_tmpdir": {"d": false, "t": 7, "a": [{"a": "sysvar_replica_load_tmpdir", "u": 38, "t": 1}]}, "replica_max_allowed_packet": {"d": true, "t": 3, "a": [{"a": "sysvar_replica_max_allowed_packet", "u": 38, "t": 1}]}, "replica_net_timeout": {"d": true, "t": 3, "a": [{"a": "sysvar_replica_net_timeout", "u": 38, "t": 1}]}, "replica_parallel_type": {"d": true, "t": 5, "a": [{"a": "sysvar_replica_parallel_type", "u": 38, "t": 1}]}, "replica_parallel_workers": {"d": true, "t": 3, "a": [{"a": "sysvar_replica_parallel_workers", "u": 38, "t": 1}]}, "replica_pending_jobs_size_max": {"d": true, "t": 3, "a": [{"a": "sysvar_replica_pending_jobs_size_max", "u": 38, "t": 1}]}, "replica_preserve_commit_order": {"d": true, "t": 2, "a": [{"a": "sysvar_replica_preserve_commit_order", "u": 38, "t": 1}]}, "replica_sql_verify_checksum": {"d": true, "t": 2, "a": [{"a": "sysvar_replica_sql_verify_checksum", "u": 38, "t": 1}]}, "replica_transaction_retries": {"d": true, "t": 3, "a": [{"a": "sysvar_replica_transaction_retries", "u": 38, "t": 1}]}, "replica_type_conversions": {"d": true, "t": 6, "a": [{"a": "sysvar_replica_type_conversions", "u": 38, "t": 1}]}, "replication_optimize_for_static_plugin_config": {"d": true, "t": 2, "a": [{"a": "sysvar_replication_optimize_for_static_plugin_config", "u": 38, "t": 1}]}, "replication_sender_observe_commit_only": {"d": true, "t": 2, "a": [{"a": "sysvar_replication_sender_observe_commit_only", "u": 38, "t": 1}]}, "rpl_read_size": {"d": true, "t": 3, "a": [{"a": "sysvar_rpl_read_size", "u": 38, "t": 1}]}, "rpl_semi_sync_replica_enabled": {"d": true, "t": 2, "a": [{"a": "sysvar_rpl_semi_sync_replica_enabled", "u": 38, "t": 1}]}, "rpl_semi_sync_replica_trace_level": {"d": true, "t": 3, "a": [{"a": "sysvar_rpl_semi_sync_replica_trace_level", "u": 38, "t": 1}]}, "rpl_stop_replica_timeout": {"d": true, "t": 3, "a": [{"a": "sysvar_rpl_stop_replica_timeout", "u": 38, "t": 1}]}, "rpl_stop_slave_timeout": {"d": true, "t": 3, "a": [{"a": "sysvar_rpl_stop_slave_timeout", "u": 38, "t": 1}]}, "slave_checkpoint_group": {"d": true, "t": 3, "a": [{"a": "sysvar_slave_checkpoint_group", "u": 38, "t": 1}]}, "slave_checkpoint_period": {"d": true, "t": 3, "a": [{"a": "sysvar_slave_checkpoint_period", "u": 38, "t": 1}]}, "slave_parallel_type": {"d": true, "t": 5, "a": [{"a": "sysvar_slave_parallel_type", "u": 38, "t": 1}]}, "slave_pending_jobs_size_max": {"d": true, "t": 3, "a": [{"a": "sysvar_slave_pending_jobs_size_max", "u": 38, "t": 1}]}, "slave_preserve_commit_order": {"d": true, "t": 2, "a": [{"a": "sysvar_slave_preserve_commit_order", "u": 38, "t": 1}]}, "slave_rows_search_algorithms": {"d": true, "t": 6, "a": [{"a": "sysvar_slave_rows_search_algorithms", "u": 38, "t": 1}]}, "replica_skip_errors": {"d": false, "t": 1, "a": [{"a": "sysvar_replica_skip_errors", "u": 38, "t": 1}]}, "sql_replica_skip_counter": {"d": true, "t": 3, "a": [{"a": "sysvar_sql_replica_skip_counter", "u": 38, "t": 1}]}, "sync_source_info": {"d": true, "t": 3, "a": [{"a": "sysvar_sync_source_info", "u": 38, "t": 1}]}, "terminology_use_previous": {"d": true, "t": 5, "a": [{"a": "sysvar_terminology_use_previous", "u": 38, "t": 1}]}, "show_replica_auth_info": {"t": 2, "a": [{"a": "option_mysqld_show-replica-auth-info", "u": 35, "t": 1}]}, "show_slave_auth_info": {"t": 2, "a": [{"a": "option_mysqld_show-slave-auth-info", "u": 35, "t": 1}]}, "immediate_server_version": {"d": true, "t": 3, "a": [{"a": "sysvar_immediate_server_version", "u": 35, "t": 1}]}, "original_server_version": {"d": true, "t": 3, "a": [{"a": "sysvar_original_server_version", "u": 35, "t": 1}]}, "rpl_semi_sync_master_wait_for_slave_count": {"d": true, "t": 3, "a": [{"a": "sysvar_rpl_semi_sync_master_wait_for_slave_count", "u": 35, "t": 1}]}, "rpl_semi_sync_source_enabled": {"d": true, "t": 2, "a": [{"a": "sysvar_rpl_semi_sync_source_enabled", "u": 35, "t": 1}]}, "rpl_semi_sync_source_timeout": {"d": true, "t": 3, "a": [{"a": "sysvar_rpl_semi_sync_source_timeout", "u": 35, "t": 1}]}, "rpl_semi_sync_source_trace_level": {"d": true, "t": 3, "a": [{"a": "sysvar_rpl_semi_sync_source_trace_level", "u": 35, "t": 1}]}, "rpl_semi_sync_source_wait_for_replica_count": {"d": true, "t": 3, "a": [{"a": "sysvar_rpl_semi_sync_source_wait_for_replica_count", "u": 35, "t": 1}]}, "rpl_semi_sync_source_wait_no_replica": {"d": true, "t": 2, "a": [{"a": "sysvar_rpl_semi_sync_source_wait_no_replica", "u": 35, "t": 1}]}, "rpl_semi_sync_source_wait_point": {"d": true, "t": 5, "a": [{"a": "sysvar_rpl_semi_sync_source_wait_point", "u": 35, "t": 1}]}, "server_uuid": {"d": false, "t": 1, "a": [{"a": "sysvar_server_uuid", "u": 16, "t": 1}]}, "help": {"a": [{"a": "option_mysqld_help", "u": 25, "t": 1}]}, "admin_ssl": {"t": 2, "a": [{"a": "option_mysqld_admin-ssl", "u": 25, "t": 1}]}, "allow_suspicious_udfs": {"t": 2, "a": [{"a": "option_mysqld_allow-suspicious-udfs", "u": 25, "t": 1}]}, "ansi": {"a": [{"a": "option_mysqld_ansi", "u": 25, "t": 1}]}, "character_set_client_handshake": {"t": 2, "a": [{"a": "option_mysqld_character-set-client-handshake", "u": 25, "t": 1}]}, "chroot": {"t": 7, "a": [{"a": "option_mysqld_chroot", "u": 25, "t": 1}]}, "console": {"a": [{"a": "option_mysqld_console", "u": 25, "t": 1}]}, "daemonize": {"t": 2, "a": [{"a": "option_mysqld_daemonize", "u": 25, "t": 1}]}, "debug": {"d": true, "t": 1, "a": [{"a": "option_mysqld_debug", "u": 25, "t": 1}, {"a": "sysvar_debug", "u": 24, "t": 1}]}, "debug_sync_timeout": {"t": 3, "a": [{"a": "option_mysqld_debug-sync-timeout", "u": 25, "t": 1}]}, "default_time_zone": {"t": 1, "a": [{"a": "option_mysqld_default-time-zone", "u": 25, "t": 1}]}, "early_plugin_load": {"t": 1, "a": [{"a": "option_mysqld_early-plugin-load", "u": 25, "t": 1}]}, "exit_info": {"t": 3, "a": [{"a": "option_mysqld_exit-info", "u": 25, "t": 1}]}, "external_locking": {"t": 2, "a": [{"a": "option_mysqld_external-locking", "u": 25, "t": 1}]}, "gdb": {"t": 2, "a": [{"a": "option_mysqld_gdb", "u": 25, "t": 1}]}, "initialize": {"t": 2, "a": [{"a": "option_mysqld_initialize", "u": 25, "t": 1}]}, "initialize_insecure": {"t": 2, "a": [{"a": "option_mysqld_initialize-insecure", "u": 25, "t": 1}]}, "install": {"a": [{"a": "option_mysqld_install", "u": 25, "t": 1}]}, "install_manual": {"a": [{"a": "option_mysqld_install-manual", "u": 25, "t": 1}]}, "language": {"d": false, "t": 7, "a": [{"a": "option_mysqld_language", "u": 25, "t": 1}]}, "local_service": {"a": [{"a": "option_mysqld_local-service", "u": 25, "t": 1}]}, "log_isam": {"t": 8, "a": [{"a": "option_mysqld_log-isam", "u": 25, "t": 1}]}, "log_raw": {"t": 2, "a": [{"a": "option_mysqld_log-raw", "u": 25, "t": 1}, {"a": "sysvar_log_raw", "u": 24, "t": 1}]}, "log_short_format": {"t": 2, "a": [{"a": "option_mysqld_log-short-format", "u": 25, "t": 1}]}, "log_tc": {"t": 8, "a": [{"a": "option_mysqld_log-tc", "u": 25, "t": 1}]}, "memlock": {"t": 2, "a": [{"a": "option_mysqld_memlock", "u": 25, "t": 1}]}, "no_dd_upgrade": {"t": 2, "a": [{"a": "option_mysqld_no-dd-upgrade", "u": 25, "t": 1}]}, "no_monitor": {"t": 2, "a": [{"a": "option_mysqld_no-monitor", "u": 25, "t": 1}]}, "old_style_user_limits": {"t": 2, "a": [{"a": "option_mysqld_old-style-user-limits", "u": 25, "t": 1}]}, "plugin_load": {"d": false, "t": 1, "a": [{"a": "option_mysqld_plugin-load", "u": 25, "t": 1}]}, "plugin_load_add": {"d": false, "t": 1, "a": [{"a": "option_mysqld_plugin-load-add", "u": 25, "t": 1}]}, "port_open_timeout": {"t": 3, "a": [{"a": "option_mysqld_port-open-timeout", "u": 25, "t": 1}]}, "remove": {"a": [{"a": "option_mysqld_remove", "u": 25, "t": 1}]}, "safe_user_create": {"t": 2, "a": [{"a": "option_mysqld_safe-user-create", "u": 25, "t": 1}]}, "skip_host_cache": {"a": [{"a": "option_mysqld_skip-host-cache", "u": 25, "t": 1}]}, "skip_new": {"a": [{"a": "option_mysqld_skip-new", "u": 25, "t": 1}]}, "skip_stack_trace": {"a": [{"a": "option_mysqld_skip-stack-trace", "u": 25, "t": 1}]}, "slow_start_timeout": {"t": 3, "a": [{"a": "option_mysqld_slow-start-timeout", "u": 25, "t": 1}]}, "ssl": {"t": 2, "a": [{"a": "option_mysqld_ssl", "u": 25, "t": 1}]}, "standalone": {"a": [{"a": "option_mysqld_standalone", "u": 25, "t": 1}]}, "super_large_pages": {"t": 2, "a": [{"a": "option_mysqld_super-large-pages", "u": 25, "t": 1}]}, "symbolic_links": {"t": 2, "a": [{"a": "option_mysqld_symbolic-links", "u": 25, "t": 1}]}, "sysdate_is_now": {"t": 2, "a": [{"a": "option_mysqld_sysdate-is-now", "u": 25, "t": 1}]}, "tc_heuristic_recover": {"t": 5, "a": [{"a": "option_mysqld_tc-heuristic-recover", "u": 25, "t": 1}]}, "transaction_isolation": {"d": true, "t": 5, "a": [{"a": "option_mysqld_transaction-isolation", "u": 25, "t": 1}, {"a": "sysvar_transaction_isolation", "u": 24, "t": 1}]}, "transaction_read_only": {"d": true, "t": 2, "a": [{"a": "option_mysqld_transaction-read-only", "u": 25, "t": 1}, {"a": "sysvar_transaction_read_only", "u": 24, "t": 1}]}, "upgrade": {"t": 5, "a": [{"a": "option_mysqld_upgrade", "u": 25, "t": 1}]}, "user": {"t": 1, "a": [{"a": "option_mysqld_user", "u": 25, "t": 1}]}, "validate_config": {"t": 2, "a": [{"a": "option_mysqld_validate-config", "u": 25, "t": 1}]}, "validate_user_plugins": {"t": 2, "a": [{"a": "option_mysqld_validate-user-plugins", "u": 25, "t": 1}]}, "activate_all_roles_on_login": {"d": true, "t": 2, "a": [{"a": "sysvar_activate_all_roles_on_login", "u": 24, "t": 1}]}, "admin_address": {"d": false, "t": 1, "a": [{"a": "sysvar_admin_address", "u": 24, "t": 1}]}, "admin_port": {"d": false, "t": 3, "a": [{"a": "sysvar_admin_port", "u": 24, "t": 1}]}, "admin_ssl_ca": {"d": true, "t": 8, "a": [{"a": "sysvar_admin_ssl_ca", "u": 24, "t": 1}]}, "admin_ssl_capath": {"d": true, "t": 7, "a": [{"a": "sysvar_admin_ssl_capath", "u": 24, "t": 1}]}, "admin_ssl_cert": {"d": true, "t": 8, "a": [{"a": "sysvar_admin_ssl_cert", "u": 24, "t": 1}]}, "admin_ssl_cipher": {"d": true, "t": 1, "a": [{"a": "sysvar_admin_ssl_cipher", "u": 24, "t": 1}]}, "admin_ssl_crl": {"d": true, "t": 8, "a": [{"a": "sysvar_admin_ssl_crl", "u": 24, "t": 1}]}, "admin_ssl_crlpath": {"d": true, "t": 7, "a": [{"a": "sysvar_admin_ssl_crlpath", "u": 24, "t": 1}]}, "admin_ssl_key": {"d": true, "t": 8, "a": [{"a": "sysvar_admin_ssl_key", "u": 24, "t": 1}]}, "admin_tls_ciphersuites": {"d": true, "t": 1, "a": [{"a": "sysvar_admin_tls_ciphersuites", "u": 24, "t": 1}]}, "admin_tls_version": {"d": true, "t": 1, "a": [{"a": "sysvar_admin_tls_version", "u": 24, "t": 1}]}, "authentication_policy": {"d": true, "t": 1, "a": [{"a": "sysvar_authentication_policy", "u": 24, "t": 1}]}, "authentication_windows_log_level": {"d": false, "t": 3, "a": [{"a": "sysvar_authentication_windows_log_level", "u": 24, "t": 1}]}, "authentication_windows_use_principal_name": {"d": false, "t": 2, "a": [{"a": "sysvar_authentication_windows_use_principal_name", "u": 24, "t": 1}]}, "auto_generate_certs": {"d": false, "t": 2, "a": [{"a": "sysvar_auto_generate_certs", "u": 24, "t": 1}]}, "avoid_temporal_upgrade": {"d": true, "t": 2, "a": [{"a": "sysvar_avoid_temporal_upgrade", "u": 24, "t": 1}]}, "block_encryption_mode": {"d": true, "t": 1, "a": [{"a": "sysvar_block_encryption_mode", "u": 24, "t": 1}]}, "build_id": {"d": false, "a": [{"a": "sysvar_build_id", "u": 24, "t": 1}]}, "caching_sha2_password_digest_rounds": {"d": false, "t": 3, "a": [{"a": "sysvar_caching_sha2_password_digest_rounds", "u": 24, "t": 1}]}, "caching_sha2_password_auto_generate_rsa_keys": {"d": false, "t": 2, "a": [{"a": "sysvar_caching_sha2_password_auto_generate_rsa_keys", "u": 24, "t": 1}]}, "caching_sha2_password_private_key_path": {"d": false, "t": 8, "a": [{"a": "sysvar_caching_sha2_password_private_key_path", "u": 24, "t": 1}]}, "caching_sha2_password_public_key_path": {"d": false, "t": 8, "a": [{"a": "sysvar_caching_sha2_password_public_key_path", "u": 24, "t": 1}]}, "check_proxy_users": {"d": true, "t": 2, "a": [{"a": "sysvar_check_proxy_users", "u": 24, "t": 1}]}, "connection_memory_chunk_size": {"d": true, "t": 3, "a": [{"a": "sysvar_connection_memory_chunk_size", "u": 24, "t": 1}]}, "connection_memory_limit": {"d": true, "t": 3, "a": [{"a": "sysvar_connection_memory_limit", "u": 24, "t": 1}]}, "create_admin_listener_thread": {"d": false, "t": 2, "a": [{"a": "sysvar_create_admin_listener_thread", "u": 24, "t": 1}]}, "cte_max_recursion_depth": {"d": true, "t": 3, "a": [{"a": "sysvar_cte_max_recursion_depth", "u": 24, "t": 1}]}, "default_authentication_plugin": {"d": false, "t": 5, "a": [{"a": "sysvar_default_authentication_plugin", "u": 24, "t": 1}]}, "default_collation_for_utf8mb4": {"d": true, "t": 5, "a": [{"a": "sysvar_default_collation_for_utf8mb4", "u": 24, "t": 1}]}, "default_table_encryption": {"d": true, "t": 2, "a": [{"a": "sysvar_default_table_encryption", "u": 24, "t": 1}]}, "disabled_storage_engines": {"d": false, "t": 1, "a": [{"a": "sysvar_disabled_storage_engines", "u": 24, "t": 1}]}, "dragnet.log_error_filter_rules": {"d": true, "t": 1, "a": [{"a": "sysvar_dragnet.log_error_filter_rules", "u": 24, "t": 1}]}, "enterprise_encryption.maximum_rsa_key_size": {"d": true, "t": 3, "a": [{"a": "sysvar_enterprise_encryption.maximum_rsa_key_size", "u": 24, "t": 1}]}, "enterprise_encryption.rsa_support_legacy_padding": {"d": true, "t": 2, "a": [{"a": "sysvar_enterprise_encryption.rsa_support_legacy_padding", "u": 24, "t": 1}]}, "end_markers_in_json": {"d": true, "t": 2, "a": [{"a": "sysvar_end_markers_in_json", "u": 24, "t": 1}]}, "generated_random_password_length": {"d": true, "t": 3, "a": [{"a": "sysvar_generated_random_password_length", "u": 24, "t": 1}]}, "global_connection_memory_limit": {"d": true, "t": 3, "a": [{"a": "sysvar_global_connection_memory_limit", "u": 24, "t": 1}]}, "global_connection_memory_tracking": {"d": true, "t": 2, "a": [{"a": "sysvar_global_connection_memory_tracking", "u": 24, "t": 1}]}, "have_statement_timeout": {"d": false, "t": 2, "a": [{"a": "sysvar_have_statement_timeout", "u": 24, "t": 1}]}, "histogram_generation_max_mem_size": {"d": true, "t": 3, "a": [{"a": "sysvar_histogram_generation_max_mem_size", "u": 24, "t": 1}]}, "information_schema_stats_expiry": {"d": true, "t": 3, "a": [{"a": "sysvar_information_schema_stats_expiry", "u": 24, "t": 1}]}, "internal_tmp_disk_storage_engine": {"d": true, "t": 5, "a": [{"a": "sysvar_internal_tmp_disk_storage_engine", "u": 24, "t": 1}]}, "internal_tmp_mem_storage_engine": {"d": true, "t": 5, "a": [{"a": "sysvar_internal_tmp_mem_storage_engine", "u": 24, "t": 1}]}, "log_error_services": {"d": true, "t": 1, "a": [{"a": "sysvar_log_error_services", "u": 24, "t": 1}]}, "log_error_suppression_list": {"d": true, "t": 1, "a": [{"a": "sysvar_log_error_suppression_list", "u": 24, "t": 1}]}, "log_error_verbosity": {"d": true, "t": 3, "a": [{"a": "sysvar_log_error_verbosity", "u": 24, "t": 1}]}, "log_slow_extra": {"d": true, "t": 2, "a": [{"a": "sysvar_log_slow_extra", "u": 24, "t": 1}]}, "log_syslog": {"d": true, "t": 2, "a": [{"a": "sysvar_log_syslog", "u": 24, "t": 1}]}, "log_syslog_facility": {"d": true, "t": 1, "a": [{"a": "sysvar_log_syslog_facility", "u": 24, "t": 1}]}, "log_syslog_include_pid": {"d": true, "t": 2, "a": [{"a": "sysvar_log_syslog_include_pid", "u": 24, "t": 1}]}, "log_syslog_tag": {"d": true, "t": 1, "a": [{"a": "sysvar_log_syslog_tag", "u": 24, "t": 1}]}, "log_timestamps": {"d": true, "t": 5, "a": [{"a": "sysvar_log_timestamps", "u": 24, "t": 1}]}, "log_throttle_queries_not_using_indexes": {"d": true, "t": 3, "a": [{"a": "sysvar_log_throttle_queries_not_using_indexes", "u": 24, "t": 1}]}, "mandatory_roles": {"d": true, "t": 1, "a": [{"a": "sysvar_mandatory_roles", "u": 24, "t": 1}]}, "max_execution_time": {"d": true, "t": 3, "a": [{"a": "sysvar_max_execution_time", "u": 24, "t": 1}]}, "max_points_in_geometry": {"d": true, "t": 3, "a": [{"a": "sysvar_max_points_in_geometry", "u": 24, "t": 1}]}, "mecab_rc_file": {"d": false, "t": 8, "a": [{"a": "sysvar_mecab_rc_file", "u": 24, "t": 1}]}, "mysql_native_password_proxy_users": {"d": true, "t": 2, "a": [{"a": "sysvar_mysql_native_password_proxy_users", "u": 24, "t": 1}]}, "named_pipe_full_access_group": {"d": false, "t": 1, "a": [{"a": "sysvar_named_pipe_full_access_group", "u": 24, "t": 1}]}, "new": {"d": true, "t": 2, "a": [{"a": "sysvar_new", "u": 24, "t": 1}]}, "ngram_token_size": {"d": false, "t": 3, "a": [{"a": "sysvar_ngram_token_size", "u": 24, "t": 1}]}, "offline_mode": {"d": true, "t": 2, "a": [{"a": "sysvar_offline_mode", "u": 24, "t": 1}]}, "optimizer_trace_features": {"d": true, "t": 1, "a": [{"a": "sysvar_optimizer_trace_features", "u": 24, "t": 1}]}, "optimizer_trace_limit": {"d": true, "t": 3, "a": [{"a": "sysvar_optimizer_trace_limit", "u": 24, "t": 1}]}, "optimizer_trace_offset": {"d": true, "t": 3, "a": [{"a": "sysvar_optimizer_trace_offset", "u": 24, "t": 1}]}, "parser_max_mem_size": {"d": true, "t": 3, "a": [{"a": "sysvar_parser_max_mem_size", "u": 24, "t": 1}]}, "partial_revokes": {"d": true, "t": 2, "a": [{"a": "sysvar_partial_revokes", "u": 24, "t": 1}]}, "password_history": {"d": true, "t": 3, "a": [{"a": "sysvar_password_history", "u": 24, "t": 1}]}, "password_require_current": {"d": true, "t": 2, "a": [{"a": "sysvar_password_require_current", "u": 24, "t": 1}]}, "password_reuse_interval": {"d": true, "t": 3, "a": [{"a": "sysvar_password_reuse_interval", "u": 24, "t": 1}]}, "persisted_globals_load": {"d": false, "t": 2, "a": [{"a": "sysvar_persisted_globals_load", "u": 24, "t": 1}]}, "persist_only_admin_x509_subject": {"d": false, "t": 1, "a": [{"a": "sysvar_persist_only_admin_x509_subject", "u": 24, "t": 1}]}, "persist_sensitive_variables_in_plaintext": {"d": false, "t": 2, "a": [{"a": "sysvar_persist_sensitive_variables_in_plaintext", "u": 24, "t": 1}]}, "print_identified_with_as_hex": {"d": true, "t": 2, "a": [{"a": "sysvar_print_identified_with_as_hex", "u": 24, "t": 1}]}, "protocol_compression_algorithms": {"d": true, "t": 6, "a": [{"a": "sysvar_protocol_compression_algorithms", "u": 24, "t": 1}]}, "pseudo_replica_mode": {"d": true, "t": 2, "a": [{"a": "sysvar_pseudo_replica_mode", "u": 24, "t": 1}]}, "range_optimizer_max_mem_size": {"d": true, "t": 3, "a": [{"a": "sysvar_range_optimizer_max_mem_size", "u": 24, "t": 1}]}, "rbr_exec_mode": {"d": true, "t": 5, "a": [{"a": "sysvar_rbr_exec_mode", "u": 24, "t": 1}]}, "regexp_stack_limit": {"d": true, "t": 3, "a": [{"a": "sysvar_regexp_stack_limit", "u": 24, "t": 1}]}, "regexp_time_limit": {"d": true, "t": 3, "a": [{"a": "sysvar_regexp_time_limit", "u": 24, "t": 1}]}, "require_row_format": {"d": true, "t": 2, "a": [{"a": "sysvar_require_row_format", "u": 24, "t": 1}]}, "resultset_metadata": {"d": true, "t": 5, "a": [{"a": "sysvar_resultset_metadata", "u": 24, "t": 1}]}, "secondary_engine_cost_threshold": {"d": true, "t": 4, "a": [{"a": "sysvar_secondary_engine_cost_threshold", "u": 24, "t": 1}]}, "schema_definition_cache": {"d": true, "t": 3, "a": [{"a": "sysvar_schema_definition_cache", "u": 24, "t": 1}]}, "select_into_buffer_size": {"d": true, "t": 3, "a": [{"a": "sysvar_select_into_buffer_size", "u": 24, "t": 1}]}, "select_into_disk_sync": {"d": true, "t": 2, "a": [{"a": "sysvar_select_into_disk_sync", "u": 24, "t": 1}]}, "select_into_disk_sync_delay": {"d": true, "t": 3, "a": [{"a": "sysvar_select_into_disk_sync_delay", "u": 24, "t": 1}]}, "session_track_gtids": {"d": true, "t": 5, "a": [{"a": "sysvar_session_track_gtids", "u": 24, "t": 1}]}, "sha256_password_auto_generate_rsa_keys": {"d": false, "t": 2, "a": [{"a": "sysvar_sha256_password_auto_generate_rsa_keys", "u": 24, "t": 1}]}, "sha256_password_private_key_path": {"d": false, "t": 8, "a": [{"a": "sysvar_sha256_password_private_key_path", "u": 24, "t": 1}]}, "sha256_password_proxy_users": {"d": true, "t": 2, "a": [{"a": "sysvar_sha256_password_proxy_users", "u": 24, "t": 1}]}, "sha256_password_public_key_path": {"d": false, "t": 8, "a": [{"a": "sysvar_sha256_password_public_key_path", "u": 24, "t": 1}]}, "show_create_table_skip_secondary_engine": {"d": true, "t": 2, "a": [{"a": "sysvar_show_create_table_skip_secondary_engine", "u": 24, "t": 1}]}, "show_create_table_verbosity": {"d": true, "t": 2, "a": [{"a": "sysvar_show_create_table_verbosity", "u": 24, "t": 1}]}, "show_gipk_in_create_table_and_information_schema": {"d": true, "t": 2, "a": [{"a": "sysvar_show_gipk_in_create_table_and_information_schema", "u": 24, "t": 1}]}, "show_old_temporals": {"d": true, "t": 2, "a": [{"a": "sysvar_show_old_temporals", "u": 24, "t": 1}]}, "sql_generate_invisible_primary_key": {"d": true, "t": 2, "a": [{"a": "sysvar_sql_generate_invisible_primary_key", "u": 24, "t": 1}]}, "sql_require_primary_key": {"d": true, "t": 2, "a": [{"a": "sysvar_sql_require_primary_key", "u": 24, "t": 1}]}, "ssl_fips_mode": {"d": true, "t": 5, "a": [{"a": "sysvar_ssl_fips_mode", "u": 24, "t": 1}]}, "ssl_session_cache_mode": {"d": true, "t": 2, "a": [{"a": "sysvar_ssl_session_cache_mode", "u": 24, "t": 1}]}, "ssl_session_cache_timeout": {"d": true, "t": 3, "a": [{"a": "sysvar_ssl_session_cache_timeout", "u": 24, "t": 1}]}, "stored_program_definition_cache": {"d": true, "t": 3, "a": [{"a": "sysvar_stored_program_definition_cache", "u": 24, "t": 1}]}, "super_read_only": {"d": true, "t": 2, "a": [{"a": "sysvar_super_read_only", "u": 24, "t": 1}]}, "syseventlog.facility": {"d": true, "t": 1, "a": [{"a": "sysvar_syseventlog.facility", "u": 24, "t": 1}]}, "syseventlog.include_pid": {"d": true, "t": 2, "a": [{"a": "sysvar_syseventlog.include_pid", "u": 24, "t": 1}]}, "syseventlog.tag": {"d": true, "t": 1, "a": [{"a": "sysvar_syseventlog.tag", "u": 24, "t": 1}]}, "table_encryption_privilege_check": {"d": true, "t": 2, "a": [{"a": "sysvar_table_encryption_privilege_check", "u": 24, "t": 1}]}, "tablespace_definition_cache": {"d": true, "t": 3, "a": [{"a": "sysvar_tablespace_definition_cache", "u": 24, "t": 1}]}, "temptable_max_mmap": {"d": true, "t": 3, "a": [{"a": "sysvar_temptable_max_mmap", "u": 24, "t": 1}]}, "temptable_max_ram": {"d": true, "t": 3, "a": [{"a": "sysvar_temptable_max_ram", "u": 24, "t": 1}]}, "temptable_use_mmap": {"d": true, "t": 2, "a": [{"a": "sysvar_temptable_use_mmap", "u": 24, "t": 1}]}, "thread_pool_algorithm": {"d": false, "t": 3, "a": [{"a": "sysvar_thread_pool_algorithm", "u": 24, "t": 1}]}, "thread_pool_dedicated_listeners": {"d": false, "t": 2, "a": [{"a": "sysvar_thread_pool_dedicated_listeners", "u": 24, "t": 1}]}, "thread_pool_high_priority_connection": {"d": true, "t": 3, "a": [{"a": "sysvar_thread_pool_high_priority_connection", "u": 24, "t": 1}]}, "thread_pool_max_active_query_threads": {"d": true, "t": 3, "a": [{"a": "sysvar_thread_pool_max_active_query_threads", "u": 24, "t": 1}]}, "thread_pool_max_transactions_limit": {"d": true, "t": 3, "a": [{"a": "sysvar_thread_pool_max_transactions_limit", "u": 24, "t": 1}]}, "thread_pool_max_unused_threads": {"d": true, "t": 3, "a": [{"a": "sysvar_thread_pool_max_unused_threads", "u": 24, "t": 1}]}, "thread_pool_query_threads_per_group": {"d": true, "t": 3, "a": [{"a": "sysvar_thread_pool_query_threads_per_group", "u": 24, "t": 1}]}, "thread_pool_transaction_delay": {"d": true, "t": 3, "a": [{"a": "sysvar_thread_pool_transaction_delay", "u": 24, "t": 1}]}, "tls_ciphersuites": {"d": true, "t": 1, "a": [{"a": "sysvar_tls_ciphersuites", "u": 24, "t": 1}]}, "use_secondary_engine": {"d": true, "t": 5, "a": [{"a": "sysvar_use_secondary_engine", "u": 24, "t": 1}]}, "version_compile_zlib": {"d": false, "t": 1, "a": [{"a": "sysvar_version_compile_zlib", "u": 24, "t": 1}]}, "windowing_use_high_precision": {"d": true, "t": 2, "a": [{"a": "sysvar_windowing_use_high_precision", "u": 24, "t": 1}]}, "xa_detach_on_prepare": {"d": true, "t": 2, "a": [{"a": "sysvar_xa_detach_on_prepare", "u": 24, "t": 1}]}, "version_tokens_session": {"d": true, "t": 1, "a": [{"a": "sysvar_version_tokens_session", "u": 65, "t": 1}]}, "version_tokens_session_number": {"d": false, "t": 3, "a": [{"a": "sysvar_version_tokens_session_number", "u": 65, "t": 1}]}, "mysqlx": {"t": 5, "a": [{"a": "option_mysqld_mysqlx", "u": 66, "t": 1}]}, "mysqlx_bind_address": {"d": false, "t": 1, "a": [{"a": "sysvar_mysqlx_bind_address", "u": 66, "t": 1}]}, "mysqlx_compression_algorithms": {"d": true, "t": 6, "a": [{"a": "sysvar_mysqlx_compression_algorithms", "u": 66, "t": 1}]}, "mysqlx_connect_timeout": {"d": true, "t": 3, "a": [{"a": "sysvar_mysqlx_connect_timeout", "u": 66, "t": 1}]}, "mysqlx_deflate_default_compression_level": {"d": true, "t": 3, "a": [{"a": "sysvar_mysqlx_deflate_default_compression_level", "u": 66, "t": 1}]}, "mysqlx_deflate_max_client_compression_level": {"d": true, "t": 3, "a": [{"a": "sysvar_mysqlx_deflate_max_client_compression_level", "u": 66, "t": 1}]}, "mysqlx_document_id_unique_prefix": {"d": true, "t": 3, "a": [{"a": "sysvar_mysqlx_document_id_unique_prefix", "u": 66, "t": 1}]}, "mysqlx_enable_hello_notice": {"d": true, "t": 2, "a": [{"a": "sysvar_mysqlx_enable_hello_notice", "u": 66, "t": 1}]}, "mysqlx_idle_worker_thread_timeout": {"d": true, "t": 3, "a": [{"a": "sysvar_mysqlx_idle_worker_thread_timeout", "u": 66, "t": 1}]}, "mysqlx_interactive_timeout": {"d": true, "t": 3, "a": [{"a": "sysvar_mysqlx_interactive_timeout", "u": 66, "t": 1}]}, "mysqlx_lz4_default_compression_level": {"d": true, "t": 3, "a": [{"a": "sysvar_mysqlx_lz4_default_compression_level", "u": 66, "t": 1}]}, "mysqlx_lz4_max_client_compression_level": {"d": true, "t": 3, "a": [{"a": "sysvar_mysqlx_lz4_max_client_compression_level", "u": 66, "t": 1}]}, "mysqlx_max_allowed_packet": {"d": true, "t": 3, "a": [{"a": "sysvar_mysqlx_max_allowed_packet", "u": 66, "t": 1}]}, "mysqlx_max_connections": {"d": true, "t": 3, "a": [{"a": "sysvar_mysqlx_max_connections", "u": 66, "t": 1}]}, "mysqlx_min_worker_threads": {"d": true, "t": 3, "a": [{"a": "sysvar_mysqlx_min_worker_threads", "u": 66, "t": 1}]}, "mysqlx_port": {"d": false, "t": 3, "a": [{"a": "sysvar_mysqlx_port", "u": 66, "t": 1}]}, "mysqlx_port_open_timeout": {"d": false, "t": 3, "a": [{"a": "sysvar_mysqlx_port_open_timeout", "u": 66, "t": 1}]}, "mysqlx_read_timeout": {"d": true, "t": 3, "a": [{"a": "sysvar_mysqlx_read_timeout", "u": 66, "t": 1}]}, "mysqlx_socket": {"d": false, "t": 1, "a": [{"a": "sysvar_mysqlx_socket", "u": 66, "t": 1}]}, "mysqlx_ssl_ca": {"d": false, "t": 8, "a": [{"a": "sysvar_mysqlx_ssl_ca", "u": 66, "t": 1}]}, "mysqlx_ssl_capath": {"d": false, "t": 7, "a": [{"a": "sysvar_mysqlx_ssl_capath", "u": 66, "t": 1}]}, "mysqlx_ssl_cert": {"d": false, "t": 8, "a": [{"a": "sysvar_mysqlx_ssl_cert", "u": 66, "t": 1}]}, "mysqlx_ssl_cipher": {"d": false, "t": 1, "a": [{"a": "sysvar_mysqlx_ssl_cipher", "u": 66, "t": 1}]}, "mysqlx_ssl_crl": {"d": false, "t": 8, "a": [{"a": "sysvar_mysqlx_ssl_crl", "u": 66, "t": 1}]}, "mysqlx_ssl_crlpath": {"d": false, "t": 7, "a": [{"a": "sysvar_mysqlx_ssl_crlpath", "u": 66, "t": 1}]}, "mysqlx_ssl_key": {"d": false, "t": 8, "a": [{"a": "sysvar_mysqlx_ssl_key", "u": 66, "t": 1}]}, "mysqlx_wait_timeout": {"d": true, "t": 3, "a": [{"a": "sysvar_mysqlx_wait_timeout", "u": 66, "t": 1}]}, "mysqlx_write_timeout": {"d": true, "t": 3, "a": [{"a": "sysvar_mysqlx_write_timeout", "u": 66, "t": 1}]}, "mysqlx_zstd_default_compression_level": {"d": true, "t": 3, "a": [{"a": "sysvar_mysqlx_zstd_default_compression_level", "u": 66, "t": 1}]}, "mysqlx_zstd_max_client_compression_level": {"d": true, "t": 3, "a": [{"a": "sysvar_mysqlx_zstd_max_client_compression_level", "u": 66, "t": 1}]}}, "version": 1, "urls": ["https://mariadb.com/kb/en/library/documentation/aria-server-status-variables/", "https://mariadb.com/kb/en/library/documentation/columns-storage-engines-and-plugins/storage-engines/aria/aria-system-variables/", "https://mariadb.com/kb/en/library/documentation/authentication-plugin-gssapi/", "https://mariadb.com/kb/en/library/documentation/authentication-plugin-pam/", "https://mariadb.com/kb/en/library/documentation/aws-key-management-encryption-plugin/", "https://mariadb.com/kb/en/library/documentation/cassandra-status-variables/", "https://mariadb.com/kb/en/library/documentation/columns-storage-engines-and-plugins/storage-engines/cassandra/cassandra-system-variables/", "https://mariadb.com/kb/en/library/documentation/columns-storage-engines-and-plugins/storage-engines/connect/connect-system-variables/", "https://mariadb.com/kb/en/library/documentation/cracklib-password-check-plugin/", "https://mariadb.com/kb/en/library/documentation/disks-plugin/", "https://mariadb.com/kb/en/library/documentation/feedback-plugin/", "https://mariadb.com/kb/en/library/documentation/file-key-management-encryption-plugin/", "https://mariadb.com/kb/en/library/documentation/galera-cluster-status-variables/", "https://mariadb.com/kb/en/library/documentation/columns-storage-engines-and-plugins/storage-engines/galera-cluster/galera-cluster-system-variables/", "https://mariadb.com/kb/en/library/documentation/gtid/", "https://mariadb.com/kb/en/library/documentation/replication-and-binary-log-server-system-variables/", "https://dev.mysql.com/doc/refman/8.0/en/replication-options.html", "https://mariadb.com/kb/en/library/documentation/handlersocket-configuration-options/", "https://mariadb.com/kb/en/library/documentation/mariadb-audit-plugin-options-and-system-variables/", "https://mariadb.com/kb/en/library/documentation/mariadb-audit-plugin-system-variables/", "https://mariadb.com/kb/en/library/documentation/mariadb-audit-plugin-status-variables/", "https://mariadb.com/kb/en/library/documentation/mroonga-status-variables/", "https://mariadb.com/kb/en/library/documentation/mroonga-system-variables/", "https://mariadb.com/kb/en/library/documentation/columns-storage-engines-and-plugins/storage-engines/myisam/myisam-system-variables/", "https://dev.mysql.com/doc/refman/8.0/en/server-system-variables.html", "https://dev.mysql.com/doc/refman/8.0/en/server-options.html", "https://mariadb.com/kb/en/library/documentation/myrocks-status-variables/", "https://mariadb.com/kb/en/library/documentation/columns-storage-engines-and-plugins/storage-engines/myrocks/myrocks-system-variables/", "https://mariadb.com/kb/en/library/documentation/oqgraph-system-and-status-variables/", "https://mariadb.com/kb/en/library/documentation/pbxt-system-variables/", "https://mariadb.com/kb/en/library/documentation/performance-schema-status-variables/", "https://mariadb.com/kb/en/library/documentation/performance-schema-system-variables/", "https://dev.mysql.com/doc/refman/8.0/en/performance-schema-system-variables.html", "https://mariadb.com/kb/en/library/documentation/query-cache-information-plugin/", "https://mariadb.com/kb/en/library/documentation/query-response-time-plugin/", "https://dev.mysql.com/doc/refman/8.0/en/replication-options-source.html", "https://dev.mysql.com/doc/refman/8.0/en/replication-options-binary-log.html", "https://dev.mysql.com/doc/refman/5.7/en/replication-options-binary-log.html", "https://dev.mysql.com/doc/refman/8.0/en/replication-options-replica.html", "https://mariadb.com/kb/en/library/documentation/replication-and-binary-log-status-variables/", "https://mariadb.com/kb/en/library/documentation/columns-storage-engines-and-plugins/storage-engines/s3-storage-engine/s3-storage-engine-system-variables/", "https://mariadb.com/kb/en/library/documentation/semisynchronous-replication-plugin-status-variables/", "https://mariadb.com/kb/en/library/documentation/semisynchronous-replication/", "https://mariadb.com/kb/en/library/documentation/server-status-variables/", "https://mariadb.com/kb/en/library/documentation/replication/optimization-and-tuning/system-variables/server-system-variables/", "https://mariadb.com/kb/en/library/documentation/simple-password-check-plugin/", "https://mariadb.com/kb/en/library/documentation/sphinx-status-variables/", "https://mariadb.com/kb/en/library/documentation/spider-server-status-variables/", "https://mariadb.com/kb/en/library/documentation/columns-storage-engines-and-plugins/storage-engines/spider/spider-server-system-variables/", "https://mariadb.com/kb/en/library/documentation/sql-error-log-plugin/", "https://mariadb.com/kb/en/library/documentation/ssltls-status-variables/", "https://mariadb.com/kb/en/library/documentation/ssltls-system-variables/", "https://mariadb.com/kb/en/library/documentation/storage-engine-independent-column-compression/", "https://mariadb.com/kb/en/library/documentation/thread-pool-system-and-status-variables/", "https://mariadb.com/kb/en/library/documentation/tokudb-status-variables/", "https://mariadb.com/kb/en/library/documentation/tokudb-system-variables/", "https://mariadb.com/kb/en/library/documentation/user-statistics/", "https://mariadb.com/kb/en/library/documentation/system-versioned-tables/", "https://mariadb.com/kb/en/library/documentation/xtradbinnodb-server-status-variables/", "https://mariadb.com/kb/en/library/documentation/xtradbinnodb-server-system-variables/", "https://dev.mysql.com/doc/refman/8.0/en/innodb-parameters.html", "https://dev.mysql.com/doc/refman/8.0/en/audit-log-reference.html", "https://dev.mysql.com/doc/refman/5.7/en/mysql-cluster-options-variables.html", "https://dev.mysql.com/doc/refman/8.0/en/pluggable-authentication-system-variables.html", "https://dev.mysql.com/doc/refman/8.0/en/replication-options-gtids.html", "https://dev.mysql.com/doc/refman/8.0/en/version-tokens-reference.html", "https://dev.mysql.com/doc/refman/8.0/en/x-plugin-options-system-variables.html"], "types": {"1": "MYSQL", "2": "MARIADB"}, "varTypes": {"1": "string", "2": "boolean", "3": "integer", "4": "numeric", "5": "enumeration", "6": "set", "7": "directory name", "8": "file name", "9": "byte"}}