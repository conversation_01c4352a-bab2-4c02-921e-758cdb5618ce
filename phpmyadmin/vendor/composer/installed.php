<?php return array(
    'root' => array(
        'pretty_version' => '5.2.1',
        'version' => '5.2.1.0',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => NULL,
        'name' => 'phpmyadmin/phpmyadmin',
        'dev' => true,
    ),
    'versions' => array(
        'amphp/amp' => array(
            'pretty_version' => 'v2.6.2',
            'version' => '2.6.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../amphp/amp',
            'aliases' => array(),
            'reference' => '9d5100cebffa729aaffecd3ad25dc5aeea4f13bb',
            'dev_requirement' => true,
        ),
        'amphp/byte-stream' => array(
            'pretty_version' => 'v1.8.1',
            'version' => '1.8.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../amphp/byte-stream',
            'aliases' => array(),
            'reference' => 'acbd8002b3536485c997c4e019206b3f10ca15bd',
            'dev_requirement' => true,
        ),
        'bacon/bacon-qr-code' => array(
            'pretty_version' => '2.0.8',
            'version' => '2.0.8.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../bacon/bacon-qr-code',
            'aliases' => array(),
            'reference' => '8674e51bb65af933a5ffaf1c308a660387c35c22',
            'dev_requirement' => false,
        ),
        'beberlei/assert' => array(
            'pretty_version' => 'v3.3.2',
            'version' => '3.3.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../beberlei/assert',
            'aliases' => array(),
            'reference' => 'cb70015c04be1baee6f5f5c953703347c0ac1655',
            'dev_requirement' => false,
        ),
        'brick/math' => array(
            'pretty_version' => '0.8.17',
            'version' => '0.8.17.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'reference' => 'e6f8e7d04346a95be89580f8c2c22d6c3fa65556',
            'dev_requirement' => false,
        ),
        'code-lts/u2f-php-server' => array(
            'pretty_version' => 'v1.2.1',
            'version' => '1.2.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../code-lts/u2f-php-server',
            'aliases' => array(),
            'reference' => '6931a00f5feb0d923ea28d3e4816272536f45077',
            'dev_requirement' => false,
        ),
        'composer/ca-bundle' => array(
            'pretty_version' => '1.3.5',
            'version' => '1.3.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/./ca-bundle',
            'aliases' => array(),
            'reference' => '74780ccf8c19d6acb8d65c5f39cd72110e132bbd',
            'dev_requirement' => false,
        ),
        'composer/package-versions-deprecated' => array(
            'pretty_version' => '1.11.99.5',
            'version' => '1.11.99.5',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/./package-versions-deprecated',
            'aliases' => array(),
            'reference' => 'b4f54f74ef3453349c24a845d22392cd31e65f1d',
            'dev_requirement' => true,
        ),
        'composer/pcre' => array(
            'pretty_version' => '2.1.0',
            'version' => '2.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/./pcre',
            'aliases' => array(),
            'reference' => '3fdb2807b31a78a40ad89570e30ec77466c98717',
            'dev_requirement' => true,
        ),
        'composer/semver' => array(
            'pretty_version' => '3.3.2',
            'version' => '3.3.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/./semver',
            'aliases' => array(),
            'reference' => '3953f23262f2bff1919fc82183ad9acb13ff62c9',
            'dev_requirement' => true,
        ),
        'composer/xdebug-handler' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/./xdebug-handler',
            'aliases' => array(),
            'reference' => 'ced299686f41dce890debac69273b47ffe98a40c',
            'dev_requirement' => true,
        ),
        'dasprid/enum' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dasprid/enum',
            'aliases' => array(),
            'reference' => '5abf82f213618696dda8e3bf6f64dd042d8542b2',
            'dev_requirement' => false,
        ),
        'dealerdirect/phpcodesniffer-composer-installer' => array(
            'pretty_version' => 'v0.7.2',
            'version' => '0.7.2.0',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../dealerdirect/phpcodesniffer-composer-installer',
            'aliases' => array(),
            'reference' => '1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db',
            'dev_requirement' => true,
        ),
        'dnoegel/php-xdg-base-dir' => array(
            'pretty_version' => 'v0.1.1',
            'version' => '0.1.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dnoegel/php-xdg-base-dir',
            'aliases' => array(),
            'reference' => '8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd',
            'dev_requirement' => true,
        ),
        'doctrine/coding-standard' => array(
            'pretty_version' => '9.0.2',
            'version' => '9.0.2.0',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../doctrine/coding-standard',
            'aliases' => array(),
            'reference' => '35a2452c6025cb739c3244b3348bcd1604df07d1',
            'dev_requirement' => true,
        ),
        'doctrine/instantiator' => array(
            'pretty_version' => '1.5.0',
            'version' => '1.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/instantiator',
            'aliases' => array(),
            'reference' => '0a0fa9780f5d4e507415a065172d26a98d02047b',
            'dev_requirement' => true,
        ),
        'facebook/webdriver' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'felixfbecker/advanced-json-rpc' => array(
            'pretty_version' => 'v3.2.1',
            'version' => '3.2.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../felixfbecker/advanced-json-rpc',
            'aliases' => array(),
            'reference' => 'b5f37dbff9a8ad360ca341f3240dc1c168b45447',
            'dev_requirement' => true,
        ),
        'felixfbecker/language-server-protocol' => array(
            'pretty_version' => 'v1.5.2',
            'version' => '1.5.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../felixfbecker/language-server-protocol',
            'aliases' => array(),
            'reference' => '6e82196ffd7c62f7794d778ca52b69feec9f2842',
            'dev_requirement' => true,
        ),
        'fgrosse/phpasn1' => array(
            'pretty_version' => 'v2.5.0',
            'version' => '2.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fgrosse/phpasn1',
            'aliases' => array(),
            'reference' => '42060ed45344789fb9f21f9f1864fc47b9e3507b',
            'dev_requirement' => false,
        ),
        'fig/http-message-util' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fig/http-message-util',
            'aliases' => array(),
            'reference' => '9d94dc0154230ac39e5bf89398b324a86f63f765',
            'dev_requirement' => false,
        ),
        'google/recaptcha' => array(
            'pretty_version' => '1.2.4',
            'version' => '1.2.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/recaptcha',
            'aliases' => array(),
            'reference' => '614f25a9038be4f3f2da7cbfd778dc5b357d2419',
            'dev_requirement' => false,
        ),
        'league/uri' => array(
            'pretty_version' => '6.4.0',
            'version' => '6.4.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/uri',
            'aliases' => array(),
            'reference' => '09da64118eaf4c5d52f9923a1e6a5be1da52fd9a',
            'dev_requirement' => false,
        ),
        'league/uri-interfaces' => array(
            'pretty_version' => '2.3.0',
            'version' => '2.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/uri-interfaces',
            'aliases' => array(),
            'reference' => '00e7e2943f76d8cb50c7dfdc2f6dee356e15e383',
            'dev_requirement' => false,
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'reference' => '14daed4296fae74d9e3201d2c4925d1acb7aa614',
            'dev_requirement' => true,
        ),
        'netresearch/jsonmapper' => array(
            'pretty_version' => 'v4.1.0',
            'version' => '4.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../netresearch/jsonmapper',
            'aliases' => array(),
            'reference' => 'cfa81ea1d35294d64adb9c68aa4cb9e92400e53f',
            'dev_requirement' => true,
        ),
        'nikic/fast-route' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/fast-route',
            'aliases' => array(),
            'reference' => '181d480e08d9476e61381e04a71b34dc0432e812',
            'dev_requirement' => false,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v4.15.3',
            'version' => '4.15.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'reference' => '570e980a201d8ed0236b0a62ddf2c9cbb2034039',
            'dev_requirement' => true,
        ),
        'ocramius/package-versions' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '1.11.99',
            ),
        ),
        'openlss/lib-array2xml' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../openlss/lib-array2xml',
            'aliases' => array(),
            'reference' => 'a91f18a8dfc69ffabe5f9b068bc39bb202c81d90',
            'dev_requirement' => true,
        ),
        'paragonie/constant_time_encoding' => array(
            'pretty_version' => 'v2.6.3',
            'version' => '2.6.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/constant_time_encoding',
            'aliases' => array(),
            'reference' => '58c3f47f650c94ec05a151692652a868995d2938',
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'dev_requirement' => false,
        ),
        'paragonie/sodium_compat' => array(
            'pretty_version' => 'v1.19.0',
            'version' => '1.19.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/sodium_compat',
            'aliases' => array(),
            'reference' => 'cb15e403ecbe6a6cc515f855c310eb6b1872a933',
            'dev_requirement' => false,
        ),
        'phar-io/manifest' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/manifest',
            'aliases' => array(),
            'reference' => '97803eca37d319dfa7826cc2437fc020857acb53',
            'dev_requirement' => true,
        ),
        'phar-io/version' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/version',
            'aliases' => array(),
            'reference' => '4f7fd7836c6f332bb2933569e566a0d6c4cbed74',
            'dev_requirement' => true,
        ),
        'php-webdriver/webdriver' => array(
            'pretty_version' => '1.13.1',
            'version' => '1.13.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-webdriver/webdriver',
            'aliases' => array(),
            'reference' => '6dfe5f814b796c1b5748850aa19f781b9274c36c',
            'dev_requirement' => true,
        ),
        'phpdocumentor/reflection-common' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpdocumentor/reflection-common',
            'aliases' => array(),
            'reference' => '1d01c49d4ed62f25aa84a747ad35d5a16924662b',
            'dev_requirement' => true,
        ),
        'phpdocumentor/reflection-docblock' => array(
            'pretty_version' => '5.3.0',
            'version' => '5.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpdocumentor/reflection-docblock',
            'aliases' => array(),
            'reference' => '622548b623e81ca6d78b721c5e029f4ce664f170',
            'dev_requirement' => true,
        ),
        'phpdocumentor/type-resolver' => array(
            'pretty_version' => '1.6.1',
            'version' => '1.6.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpdocumentor/type-resolver',
            'aliases' => array(),
            'reference' => '77a32518733312af16a44300404e945338981de3',
            'dev_requirement' => true,
        ),
        'phpmyadmin/coding-standard' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../phpmyadmin/coding-standard',
            'aliases' => array(),
            'reference' => 'd187e307c91518ce676ee91a81145dcc90b25d9f',
            'dev_requirement' => true,
        ),
        'phpmyadmin/motranslator' => array(
            'pretty_version' => '5.3.0',
            'version' => '5.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpmyadmin/motranslator',
            'aliases' => array(),
            'reference' => '87baa97809ec556c40e4cba4bdef998a2de2a003',
            'dev_requirement' => false,
        ),
        'phpmyadmin/phpmyadmin' => array(
            'pretty_version' => '5.2.1',
            'version' => '5.2.1.0',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => NULL,
            'dev_requirement' => false,
        ),
        'phpmyadmin/shapefile' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpmyadmin/shapefile',
            'aliases' => array(),
            'reference' => 'c232198ef49d3484f26acfe2d12cab103da9371a',
            'dev_requirement' => false,
        ),
        'phpmyadmin/sql-parser' => array(
            'pretty_version' => '5.7.0',
            'version' => '5.7.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpmyadmin/sql-parser',
            'aliases' => array(),
            'reference' => '0f5895aab2b6002d00b6831b60983523dea30bff',
            'dev_requirement' => false,
        ),
        'phpmyadmin/twig-i18n-extension' => array(
            'pretty_version' => 'v4.0.1',
            'version' => '4.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpmyadmin/twig-i18n-extension',
            'aliases' => array(),
            'reference' => 'c0d0dd171cd1c7733bf152fd44b61055843df052',
            'dev_requirement' => false,
        ),
        'phpstan/extension-installer' => array(
            'pretty_version' => '1.2.0',
            'version' => '1.2.0.0',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../phpstan/extension-installer',
            'aliases' => array(),
            'reference' => 'f06dbb052ddc394e7896fcd1cfcd533f9f6ace40',
            'dev_requirement' => true,
        ),
        'phpstan/phpdoc-parser' => array(
            'pretty_version' => '1.16.1',
            'version' => '1.16.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpstan/phpdoc-parser',
            'aliases' => array(),
            'reference' => 'e27e92d939e2e3636f0a1f0afaba59692c0bf571',
            'dev_requirement' => true,
        ),
        'phpstan/phpstan' => array(
            'pretty_version' => '1.9.16',
            'version' => '1.9.16.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpstan/phpstan',
            'aliases' => array(),
            'reference' => '922e2689bb180575d0f57de0443c431a5a698e8f',
            'dev_requirement' => true,
        ),
        'phpstan/phpstan-phpunit' => array(
            'pretty_version' => '1.3.3',
            'version' => '1.3.3.0',
            'type' => 'phpstan-extension',
            'install_path' => __DIR__ . '/../phpstan/phpstan-phpunit',
            'aliases' => array(),
            'reference' => '54a24bd23e9e80ee918cdc24f909d376c2e273f7',
            'dev_requirement' => true,
        ),
        'phpstan/phpstan-webmozart-assert' => array(
            'pretty_version' => '1.2.2',
            'version' => '1.2.2.0',
            'type' => 'phpstan-extension',
            'install_path' => __DIR__ . '/../phpstan/phpstan-webmozart-assert',
            'aliases' => array(),
            'reference' => '01259f5c85d175cbd380d91789ed80602c870ce9',
            'dev_requirement' => true,
        ),
        'phpunit/php-code-coverage' => array(
            'pretty_version' => '7.0.15',
            'version' => '7.0.15.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-code-coverage',
            'aliases' => array(),
            'reference' => '819f92bba8b001d4363065928088de22f25a3a48',
            'dev_requirement' => true,
        ),
        'phpunit/php-file-iterator' => array(
            'pretty_version' => '2.0.5',
            'version' => '2.0.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-file-iterator',
            'aliases' => array(),
            'reference' => '42c5ba5220e6904cbfe8b1a1bda7c0cfdc8c12f5',
            'dev_requirement' => true,
        ),
        'phpunit/php-text-template' => array(
            'pretty_version' => '1.2.1',
            'version' => '1.2.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-text-template',
            'aliases' => array(),
            'reference' => '31f8b717e51d9a2afca6c9f046f5d69fc27c8686',
            'dev_requirement' => true,
        ),
        'phpunit/php-timer' => array(
            'pretty_version' => '2.1.3',
            'version' => '2.1.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-timer',
            'aliases' => array(),
            'reference' => '2454ae1765516d20c4ffe103d85a58a9a3bd5662',
            'dev_requirement' => true,
        ),
        'phpunit/php-token-stream' => array(
            'pretty_version' => '3.1.3',
            'version' => '3.1.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-token-stream',
            'aliases' => array(),
            'reference' => '9c1da83261628cb24b6a6df371b6e312b3954768',
            'dev_requirement' => true,
        ),
        'phpunit/phpunit' => array(
            'pretty_version' => '8.5.32',
            'version' => '8.5.32.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/phpunit',
            'aliases' => array(),
            'reference' => '375686930d05c9fd7d20f6e5fc38121e8d7a9d55',
            'dev_requirement' => true,
        ),
        'pragmarx/google2fa' => array(
            'pretty_version' => 'v8.0.1',
            'version' => '8.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pragmarx/google2fa',
            'aliases' => array(),
            'reference' => '80c3d801b31fe165f8fe99ea085e0a37834e1be3',
            'dev_requirement' => false,
        ),
        'pragmarx/google2fa-qrcode' => array(
            'pretty_version' => 'v2.1.1',
            'version' => '2.1.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pragmarx/google2fa-qrcode',
            'aliases' => array(),
            'reference' => '0459a5d7bab06b11a09a365288d41a41d2afe63f',
            'dev_requirement' => false,
        ),
        'psalm/plugin-phpunit' => array(
            'pretty_version' => '0.16.1',
            'version' => '0.16.1.0',
            'type' => 'psalm-plugin',
            'install_path' => __DIR__ . '/../psalm/plugin-phpunit',
            'aliases' => array(),
            'reference' => '5dd3be04f37a857d52880ef6af2524a441dfef24',
            'dev_requirement' => true,
        ),
        'psalm/psalm' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '4.30.0',
            ),
        ),
        'psr/cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'reference' => 'd11b50ad223250cf17b86e38383413f5a6764bf8',
            'dev_requirement' => false,
        ),
        'psr/cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '1.1.1',
            'version' => '1.1.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'reference' => '8622567409010282b7aeebe4bb841fe98b58dcaf',
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'reference' => '2dfb5f6c5eff0e91e20e913f8c5452ed95b86621',
            'dev_requirement' => false,
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'reference' => '12ac7fcd07e5b077433f5f2bee95b3a771bf61be',
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'reference' => 'f6561bf28d520154e4b0ec72be95418abe6d9363',
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '1.0|2.0',
            ),
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'dev_requirement' => false,
        ),
        'ramsey/collection' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/collection',
            'aliases' => array(),
            'reference' => 'ab2237657ad99667a5143e32ba2683c8029563d4',
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '4.2.3',
            'version' => '4.2.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'reference' => 'fc9bb7fb5388691fd7373cd44dcb4d63bbcf24df',
            'dev_requirement' => false,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '4.2.3',
            ),
        ),
        'roave/security-advisories' => array(
            'pretty_version' => 'dev-latest',
            'version' => 'dev-latest',
            'type' => 'metapackage',
            'install_path' => NULL,
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'reference' => 'd6466bfc092e86f196c7e33846caed66b354d745',
            'dev_requirement' => true,
        ),
        'sebastian/code-unit-reverse-lookup' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit-reverse-lookup',
            'aliases' => array(),
            'reference' => '1de8cd5c010cb153fcd68b8d0f64606f523f7619',
            'dev_requirement' => true,
        ),
        'sebastian/comparator' => array(
            'pretty_version' => '3.0.5',
            'version' => '3.0.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/comparator',
            'aliases' => array(),
            'reference' => '1dc7ceb4a24aede938c7af2a9ed1de09609ca770',
            'dev_requirement' => true,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'reference' => '14f72dd46eaf2f2293cbe79c93cc0bc43161a211',
            'dev_requirement' => true,
        ),
        'sebastian/environment' => array(
            'pretty_version' => '4.2.4',
            'version' => '4.2.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/environment',
            'aliases' => array(),
            'reference' => 'd47bbbad83711771f167c72d4e3f25f7fcc1f8b0',
            'dev_requirement' => true,
        ),
        'sebastian/exporter' => array(
            'pretty_version' => '3.1.5',
            'version' => '3.1.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/exporter',
            'aliases' => array(),
            'reference' => '73a9676f2833b9a7c36968f9d882589cd75511e6',
            'dev_requirement' => true,
        ),
        'sebastian/global-state' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/global-state',
            'aliases' => array(),
            'reference' => 'de036ec91d55d2a9e0db2ba975b512cdb1c23921',
            'dev_requirement' => true,
        ),
        'sebastian/object-enumerator' => array(
            'pretty_version' => '3.0.4',
            'version' => '3.0.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-enumerator',
            'aliases' => array(),
            'reference' => 'e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2',
            'dev_requirement' => true,
        ),
        'sebastian/object-reflector' => array(
            'pretty_version' => '1.1.2',
            'version' => '1.1.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-reflector',
            'aliases' => array(),
            'reference' => '9b8772b9cbd456ab45d4a598d2dd1a1bced6363d',
            'dev_requirement' => true,
        ),
        'sebastian/recursion-context' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/recursion-context',
            'aliases' => array(),
            'reference' => '367dcba38d6e1977be014dc4b22f47a484dac7fb',
            'dev_requirement' => true,
        ),
        'sebastian/resource-operations' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/resource-operations',
            'aliases' => array(),
            'reference' => '31d35ca87926450c44eae7e2611d45a7a65ea8b3',
            'dev_requirement' => true,
        ),
        'sebastian/type' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/type',
            'aliases' => array(),
            'reference' => '0150cfbc4495ed2df3872fb31b26781e4e077eb4',
            'dev_requirement' => true,
        ),
        'sebastian/version' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/version',
            'aliases' => array(),
            'reference' => '99732be0ddb3361e16ad77b68ba41efc8e979019',
            'dev_requirement' => true,
        ),
        'slevomat/coding-standard' => array(
            'pretty_version' => '7.2.1',
            'version' => '7.2.1.0',
            'type' => 'phpcodesniffer-standard',
            'install_path' => __DIR__ . '/../slevomat/coding-standard',
            'aliases' => array(),
            'reference' => 'aff06ae7a84e4534bf6f821dc982a93a5d477c90',
            'dev_requirement' => true,
        ),
        'slim/psr7' => array(
            'pretty_version' => '1.4',
            'version' => '1.4.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../slim/psr7',
            'aliases' => array(),
            'reference' => '0dca983ca32a26f4a91fb11173b7b9eaee29e9d6',
            'dev_requirement' => false,
        ),
        'spomky-labs/base64url' => array(
            'pretty_version' => 'v2.0.4',
            'version' => '2.0.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spomky-labs/base64url',
            'aliases' => array(),
            'reference' => '7752ce931ec285da4ed1f4c5aa27e45e097be61d',
            'dev_requirement' => false,
        ),
        'spomky-labs/cbor-php' => array(
            'pretty_version' => 'v1.1.1',
            'version' => '1.1.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spomky-labs/cbor-php',
            'aliases' => array(),
            'reference' => 'b8e51e6a13606ab1dd8a64aa295651d8ad57ccd1',
            'dev_requirement' => false,
        ),
        'squizlabs/php_codesniffer' => array(
            'pretty_version' => '3.6.2',
            'version' => '3.6.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../squizlabs/php_codesniffer',
            'aliases' => array(),
            'reference' => '5e4e71592f69da17871dba6e80dd51bce74a351a',
            'dev_requirement' => true,
        ),
        'symfony/cache' => array(
            'pretty_version' => 'v5.4.19',
            'version' => '5.4.19.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/cache',
            'aliases' => array(),
            'reference' => 'e9147c89fdfdc5d5ef798bb7193f23726ad609f5',
            'dev_requirement' => false,
        ),
        'symfony/cache-contracts' => array(
            'pretty_version' => 'v2.5.2',
            'version' => '2.5.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/cache-contracts',
            'aliases' => array(),
            'reference' => '64be4a7acb83b6f2bf6de9a02cee6dad41277ebc',
            'dev_requirement' => false,
        ),
        'symfony/cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0',
            ),
        ),
        'symfony/config' => array(
            'pretty_version' => 'v5.4.19',
            'version' => '5.4.19.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/config',
            'aliases' => array(),
            'reference' => '9bd60843443cda9638efdca7c41eb82ed0026179',
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v5.4.19',
            'version' => '5.4.19.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'reference' => 'dccb8d251a9017d5994c988b034d3e18aaabf740',
            'dev_requirement' => true,
        ),
        'symfony/dependency-injection' => array(
            'pretty_version' => 'v5.4.20',
            'version' => '5.4.20.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/dependency-injection',
            'aliases' => array(),
            'reference' => '8185ed0df129005a26715902f1a53bad0fe67102',
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v2.5.2',
            'version' => '2.5.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'reference' => 'e8b495ea28c1d97b5e0c121748d6f9b53d075c66',
            'dev_requirement' => false,
        ),
        'symfony/expression-language' => array(
            'pretty_version' => 'v5.4.19',
            'version' => '5.4.19.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/expression-language',
            'aliases' => array(),
            'reference' => '5db17a4a1c41e2d43d9b84c2eb98a5f63b11c646',
            'dev_requirement' => false,
        ),
        'symfony/filesystem' => array(
            'pretty_version' => 'v5.4.19',
            'version' => '5.4.19.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/filesystem',
            'aliases' => array(),
            'reference' => '648bfaca6a494f3e22378123bcee2894045dc9d8',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.27.0',
            'version' => '1.27.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'reference' => '5bbc823adecdae860bb64756d639ecfec17b050a',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.27.0',
            'version' => '1.27.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'reference' => '511a08c03c1960e08a883f4cffcacd219b758354',
            'dev_requirement' => true,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.27.0',
            'version' => '1.27.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'reference' => '19bd1e4fcd5b91116f14d8533c57831ed00571b6',
            'dev_requirement' => true,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.27.0',
            'version' => '1.27.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'reference' => '8ad114f6b39e2c98a8b0e3bd907732c207c2b534',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php73' => array(
            'pretty_version' => 'v1.27.0',
            'version' => '1.27.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php73',
            'aliases' => array(),
            'reference' => '9e8ecb5f92152187c4799efd3c96b78ccab18ff9',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.27.0',
            'version' => '1.27.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'reference' => '7a6ff3f1959bb01aefccb463a0f2cd3d3d2fd936',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php81' => array(
            'pretty_version' => 'v1.27.0',
            'version' => '1.27.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php81',
            'aliases' => array(),
            'reference' => '707403074c8ea6e2edaf8794b0157a0bfa52157a',
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v5.4.19',
            'version' => '5.4.19.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'reference' => 'c5ba874c9b636dbccf761e22ce750e88ec3f55e1',
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v2.5.2',
            'version' => '2.5.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'reference' => '4b426aac47d6427cc1a1d0f7e2ac724627f5966c',
            'dev_requirement' => false,
        ),
        'symfony/service-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0',
            ),
        ),
        'symfony/string' => array(
            'pretty_version' => 'v5.4.19',
            'version' => '5.4.19.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'reference' => '0a01071610fd861cc160dfb7e2682ceec66064cb',
            'dev_requirement' => true,
        ),
        'symfony/var-exporter' => array(
            'pretty_version' => 'v5.4.19',
            'version' => '5.4.19.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-exporter',
            'aliases' => array(),
            'reference' => '2a1d06fcf2b30829d6c01dae8e6e188424d1f8f6',
            'dev_requirement' => false,
        ),
        'tecnickcom/tcpdf' => array(
            'pretty_version' => '6.6.2',
            'version' => '6.6.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tecnickcom/tcpdf',
            'aliases' => array(),
            'reference' => 'e3cffc9bcbc76e89e167e9eb0bbda0cab7518459',
            'dev_requirement' => false,
        ),
        'thecodingmachine/safe' => array(
            'pretty_version' => 'v1.3.3',
            'version' => '1.3.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../thecodingmachine/safe',
            'aliases' => array(),
            'reference' => 'a8ab0876305a4cdaef31b2350fcb9811b5608dbc',
            'dev_requirement' => false,
        ),
        'theseer/tokenizer' => array(
            'pretty_version' => '1.2.1',
            'version' => '1.2.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../theseer/tokenizer',
            'aliases' => array(),
            'reference' => '34a41e998c2183e22995f158c581e7b5e755ab9e',
            'dev_requirement' => true,
        ),
        'twig/twig' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '3.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../twig/twig',
            'aliases' => array(),
            'reference' => '3ffcf4b7d890770466da3b2666f82ac054e7ec72',
            'dev_requirement' => false,
        ),
        'vimeo/psalm' => array(
            'pretty_version' => '4.30.0',
            'version' => '4.30.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vimeo/psalm',
            'aliases' => array(),
            'reference' => 'd0bc6e25d89f649e4f36a534f330f8bb4643dd69',
            'dev_requirement' => true,
        ),
        'web-auth/cose-lib' => array(
            'pretty_version' => 'v3.3.12',
            'version' => '3.3.12.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../web-auth/cose-lib',
            'aliases' => array(),
            'reference' => 'efa6ec2ba4e840bc1316a493973c9916028afeeb',
            'dev_requirement' => false,
        ),
        'web-auth/metadata-service' => array(
            'pretty_version' => 'v3.3.12',
            'version' => '3.3.12.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../web-auth/metadata-service',
            'aliases' => array(),
            'reference' => 'ef40d2b7b68c4964247d13fab52e2fa8dbd65246',
            'dev_requirement' => false,
        ),
        'web-auth/webauthn-lib' => array(
            'pretty_version' => 'v3.3.12',
            'version' => '3.3.12.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../web-auth/webauthn-lib',
            'aliases' => array(),
            'reference' => '5ef9b21c8e9f8a817e524ac93290d08a9f065b33',
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
            'dev_requirement' => false,
        ),
        'webmozart/path-util' => array(
            'pretty_version' => '2.3.0',
            'version' => '2.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/path-util',
            'aliases' => array(),
            'reference' => 'd939f7edc24c9a1bb9c0dee5cb05d8e859490725',
            'dev_requirement' => true,
        ),
        'williamdes/mariadb-mysql-kbs' => array(
            'pretty_version' => 'v1.2.14',
            'version' => '1.2.14.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../williamdes/mariadb-mysql-kbs',
            'aliases' => array(),
            'reference' => 'd829a96ad07d79065fbc818a3bd01f2266c3890b',
            'dev_requirement' => false,
        ),
    ),
);
