{"name": "composer/pcre", "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "type": "library", "license": "MIT", "keywords": ["pcre", "regex", "preg", "regular expression"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"symfony/phpunit-bridge": "^5", "phpstan/phpstan": "^1.3", "phpstan/phpstan-strict-rules": "^1.1"}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "autoload-dev": {"psr-4": {"Composer\\Pcre\\": "tests"}}, "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "scripts": {"test": "SYMFONY_PHPUNIT_REMOVE_RETURN_TYPEHINT=1 vendor/bin/simple-phpunit", "phpstan": "phpstan analyse"}}