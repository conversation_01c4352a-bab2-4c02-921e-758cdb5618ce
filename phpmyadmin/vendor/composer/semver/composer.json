{"name": "composer/semver", "description": "Semver library that offers utilities, version constraint parsing and validation.", "type": "library", "license": "MIT", "keywords": ["semver", "semantic", "versioning", "validation"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues"}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"symfony/phpunit-bridge": "^4.2 || ^5", "phpstan/phpstan": "^1.4"}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "autoload-dev": {"psr-4": {"Composer\\Semver\\": "tests"}}, "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "scripts": {"test": "SYMFONY_PHPUNIT_REMOVE_RETURN_TYPEHINT=1 vendor/bin/simple-phpunit", "phpstan": "@php vendor/bin/phpstan analyse"}}