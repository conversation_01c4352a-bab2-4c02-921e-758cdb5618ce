{"name": "composer/ca-bundle", "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "type": "library", "license": "MIT", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues"}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"symfony/phpunit-bridge": "^4.2 || ^5", "phpstan/phpstan": "^0.12.55", "psr/log": "^1.0", "symfony/process": "^2.5 || ^3.0 || ^4.0 || ^5.0 || ^6.0"}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "autoload-dev": {"psr-4": {"Composer\\CaBundle\\": "tests"}}, "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "scripts": {"test": "SYMFONY_PHPUNIT_REMOVE_RETURN_TYPEHINT=1 vendor/bin/simple-phpunit", "phpstan": "vendor/bin/phpstan analyse"}}