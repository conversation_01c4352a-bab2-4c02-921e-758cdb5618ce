<?php

declare(strict_types=1);

namespace PackageVersions;

use Composer\InstalledVersions;
use OutOfBoundsException;

class_exists(InstalledVersions::class);

/**
 * This class is generated by composer/package-versions-deprecated, specifically by
 * @see \PackageVersions\Installer
 *
 * This file is overwritten at every run of `composer install` or `composer update`.
 *
 * @deprecated in favor of the Composer\InstalledVersions class provided by Composer 2. Require composer-runtime-api:^2 to ensure it is present.
 */
final class Versions
{
    /**
     * @deprecated please use {@see self::rootPackageName()} instead.
     *             This constant will be removed in version 2.0.0.
     */
    const ROOT_PACKAGE_NAME = 'phpmyadmin/phpmyadmin';

    /**
     * Array of all available composer packages.
     * Dont read this array from your calling code, but use the \PackageVersions\Versions::getVersion() method instead.
     *
     * @var array<string, string>
     * @internal
     */
    const VERSIONS          = array (
  'bacon/bacon-qr-code' => '2.0.8@8674e51bb65af933a5ffaf1c308a660387c35c22',
  'beberlei/assert' => 'v3.3.2@cb70015c04be1baee6f5f5c953703347c0ac1655',
  'brick/math' => '0.8.17@e6f8e7d04346a95be89580f8c2c22d6c3fa65556',
  'code-lts/u2f-php-server' => 'v1.2.1@6931a00f5feb0d923ea28d3e4816272536f45077',
  'composer/ca-bundle' => '1.3.5@74780ccf8c19d6acb8d65c5f39cd72110e132bbd',
  'dasprid/enum' => '1.0.3@5abf82f213618696dda8e3bf6f64dd042d8542b2',
  'fgrosse/phpasn1' => 'v2.5.0@42060ed45344789fb9f21f9f1864fc47b9e3507b',
  'fig/http-message-util' => '1.1.5@9d94dc0154230ac39e5bf89398b324a86f63f765',
  'google/recaptcha' => '1.2.4@614f25a9038be4f3f2da7cbfd778dc5b357d2419',
  'league/uri' => '6.4.0@09da64118eaf4c5d52f9923a1e6a5be1da52fd9a',
  'league/uri-interfaces' => '2.3.0@00e7e2943f76d8cb50c7dfdc2f6dee356e15e383',
  'nikic/fast-route' => 'v1.3.0@181d480e08d9476e61381e04a71b34dc0432e812',
  'paragonie/constant_time_encoding' => 'v2.6.3@58c3f47f650c94ec05a151692652a868995d2938',
  'paragonie/random_compat' => 'v9.99.100@996434e5492cb4c3edcb9168db6fbb1359ef965a',
  'paragonie/sodium_compat' => 'v1.19.0@cb15e403ecbe6a6cc515f855c310eb6b1872a933',
  'phpmyadmin/motranslator' => '5.3.0@87baa97809ec556c40e4cba4bdef998a2de2a003',
  'phpmyadmin/shapefile' => '3.0.1@c232198ef49d3484f26acfe2d12cab103da9371a',
  'phpmyadmin/sql-parser' => '5.7.0@0f5895aab2b6002d00b6831b60983523dea30bff',
  'phpmyadmin/twig-i18n-extension' => 'v4.0.1@c0d0dd171cd1c7733bf152fd44b61055843df052',
  'pragmarx/google2fa' => 'v8.0.1@80c3d801b31fe165f8fe99ea085e0a37834e1be3',
  'pragmarx/google2fa-qrcode' => 'v2.1.1@0459a5d7bab06b11a09a365288d41a41d2afe63f',
  'psr/cache' => '1.0.1@d11b50ad223250cf17b86e38383413f5a6764bf8',
  'psr/container' => '1.1.1@8622567409010282b7aeebe4bb841fe98b58dcaf',
  'psr/http-client' => '1.0.1@2dfb5f6c5eff0e91e20e913f8c5452ed95b86621',
  'psr/http-factory' => '1.0.1@12ac7fcd07e5b077433f5f2bee95b3a771bf61be',
  'psr/http-message' => '1.0.1@f6561bf28d520154e4b0ec72be95418abe6d9363',
  'psr/log' => '1.1.4@d49695b909c3b7628b6289db5479a1c204601f11',
  'ralouphie/getallheaders' => '3.0.3@120b605dfeb996808c31b6477290a714d356e822',
  'ramsey/collection' => '1.1.4@ab2237657ad99667a5143e32ba2683c8029563d4',
  'ramsey/uuid' => '4.2.3@fc9bb7fb5388691fd7373cd44dcb4d63bbcf24df',
  'slim/psr7' => '1.4@0dca983ca32a26f4a91fb11173b7b9eaee29e9d6',
  'spomky-labs/base64url' => 'v2.0.4@7752ce931ec285da4ed1f4c5aa27e45e097be61d',
  'spomky-labs/cbor-php' => 'v1.1.1@b8e51e6a13606ab1dd8a64aa295651d8ad57ccd1',
  'symfony/cache' => 'v5.4.19@e9147c89fdfdc5d5ef798bb7193f23726ad609f5',
  'symfony/cache-contracts' => 'v2.5.2@64be4a7acb83b6f2bf6de9a02cee6dad41277ebc',
  'symfony/config' => 'v5.4.19@9bd60843443cda9638efdca7c41eb82ed0026179',
  'symfony/dependency-injection' => 'v5.4.20@8185ed0df129005a26715902f1a53bad0fe67102',
  'symfony/deprecation-contracts' => 'v2.5.2@e8b495ea28c1d97b5e0c121748d6f9b53d075c66',
  'symfony/expression-language' => 'v5.4.19@5db17a4a1c41e2d43d9b84c2eb98a5f63b11c646',
  'symfony/filesystem' => 'v5.4.19@648bfaca6a494f3e22378123bcee2894045dc9d8',
  'symfony/polyfill-ctype' => 'v1.27.0@5bbc823adecdae860bb64756d639ecfec17b050a',
  'symfony/polyfill-mbstring' => 'v1.27.0@8ad114f6b39e2c98a8b0e3bd907732c207c2b534',
  'symfony/polyfill-php73' => 'v1.27.0@9e8ecb5f92152187c4799efd3c96b78ccab18ff9',
  'symfony/polyfill-php80' => 'v1.27.0@7a6ff3f1959bb01aefccb463a0f2cd3d3d2fd936',
  'symfony/polyfill-php81' => 'v1.27.0@707403074c8ea6e2edaf8794b0157a0bfa52157a',
  'symfony/process' => 'v5.4.19@c5ba874c9b636dbccf761e22ce750e88ec3f55e1',
  'symfony/service-contracts' => 'v2.5.2@4b426aac47d6427cc1a1d0f7e2ac724627f5966c',
  'symfony/var-exporter' => 'v5.4.19@2a1d06fcf2b30829d6c01dae8e6e188424d1f8f6',
  'tecnickcom/tcpdf' => '6.6.2@e3cffc9bcbc76e89e167e9eb0bbda0cab7518459',
  'thecodingmachine/safe' => 'v1.3.3@a8ab0876305a4cdaef31b2350fcb9811b5608dbc',
  'twig/twig' => 'v3.5.0@3ffcf4b7d890770466da3b2666f82ac054e7ec72',
  'web-auth/cose-lib' => 'v3.3.12@efa6ec2ba4e840bc1316a493973c9916028afeeb',
  'web-auth/metadata-service' => 'v3.3.12@ef40d2b7b68c4964247d13fab52e2fa8dbd65246',
  'web-auth/webauthn-lib' => 'v3.3.12@5ef9b21c8e9f8a817e524ac93290d08a9f065b33',
  'webmozart/assert' => '1.11.0@11cb2199493b2f8a3b53e7f19068fc6aac760991',
  'williamdes/mariadb-mysql-kbs' => 'v1.2.14@d829a96ad07d79065fbc818a3bd01f2266c3890b',
  'amphp/amp' => 'v2.6.2@9d5100cebffa729aaffecd3ad25dc5aeea4f13bb',
  'amphp/byte-stream' => 'v1.8.1@acbd8002b3536485c997c4e019206b3f10ca15bd',
  'composer/package-versions-deprecated' => '1.11.99.5@b4f54f74ef3453349c24a845d22392cd31e65f1d',
  'composer/pcre' => '2.1.0@3fdb2807b31a78a40ad89570e30ec77466c98717',
  'composer/semver' => '3.3.2@3953f23262f2bff1919fc82183ad9acb13ff62c9',
  'composer/xdebug-handler' => '3.0.3@ced299686f41dce890debac69273b47ffe98a40c',
  'dealerdirect/phpcodesniffer-composer-installer' => 'v0.7.2@1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db',
  'dnoegel/php-xdg-base-dir' => 'v0.1.1@8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd',
  'doctrine/coding-standard' => '9.0.2@35a2452c6025cb739c3244b3348bcd1604df07d1',
  'doctrine/instantiator' => '1.5.0@0a0fa9780f5d4e507415a065172d26a98d02047b',
  'felixfbecker/advanced-json-rpc' => 'v3.2.1@b5f37dbff9a8ad360ca341f3240dc1c168b45447',
  'felixfbecker/language-server-protocol' => 'v1.5.2@6e82196ffd7c62f7794d778ca52b69feec9f2842',
  'myclabs/deep-copy' => '1.11.0@14daed4296fae74d9e3201d2c4925d1acb7aa614',
  'netresearch/jsonmapper' => 'v4.1.0@cfa81ea1d35294d64adb9c68aa4cb9e92400e53f',
  'nikic/php-parser' => 'v4.15.3@570e980a201d8ed0236b0a62ddf2c9cbb2034039',
  'openlss/lib-array2xml' => '1.0.0@a91f18a8dfc69ffabe5f9b068bc39bb202c81d90',
  'phar-io/manifest' => '2.0.3@97803eca37d319dfa7826cc2437fc020857acb53',
  'phar-io/version' => '3.2.1@4f7fd7836c6f332bb2933569e566a0d6c4cbed74',
  'php-webdriver/webdriver' => '1.13.1@6dfe5f814b796c1b5748850aa19f781b9274c36c',
  'phpdocumentor/reflection-common' => '2.2.0@1d01c49d4ed62f25aa84a747ad35d5a16924662b',
  'phpdocumentor/reflection-docblock' => '5.3.0@622548b623e81ca6d78b721c5e029f4ce664f170',
  'phpdocumentor/type-resolver' => '1.6.1@77a32518733312af16a44300404e945338981de3',
  'phpmyadmin/coding-standard' => '3.0.0@d187e307c91518ce676ee91a81145dcc90b25d9f',
  'phpstan/extension-installer' => '1.2.0@f06dbb052ddc394e7896fcd1cfcd533f9f6ace40',
  'phpstan/phpdoc-parser' => '1.16.1@e27e92d939e2e3636f0a1f0afaba59692c0bf571',
  'phpstan/phpstan' => '1.9.16@922e2689bb180575d0f57de0443c431a5a698e8f',
  'phpstan/phpstan-phpunit' => '1.3.3@54a24bd23e9e80ee918cdc24f909d376c2e273f7',
  'phpstan/phpstan-webmozart-assert' => '1.2.2@01259f5c85d175cbd380d91789ed80602c870ce9',
  'phpunit/php-code-coverage' => '7.0.15@819f92bba8b001d4363065928088de22f25a3a48',
  'phpunit/php-file-iterator' => '2.0.5@42c5ba5220e6904cbfe8b1a1bda7c0cfdc8c12f5',
  'phpunit/php-text-template' => '1.2.1@31f8b717e51d9a2afca6c9f046f5d69fc27c8686',
  'phpunit/php-timer' => '2.1.3@2454ae1765516d20c4ffe103d85a58a9a3bd5662',
  'phpunit/php-token-stream' => '3.1.3@9c1da83261628cb24b6a6df371b6e312b3954768',
  'phpunit/phpunit' => '8.5.32@375686930d05c9fd7d20f6e5fc38121e8d7a9d55',
  'psalm/plugin-phpunit' => '0.16.1@5dd3be04f37a857d52880ef6af2524a441dfef24',
  'roave/security-advisories' => 'dev-latest@d6466bfc092e86f196c7e33846caed66b354d745',
  'sebastian/code-unit-reverse-lookup' => '1.0.2@1de8cd5c010cb153fcd68b8d0f64606f523f7619',
  'sebastian/comparator' => '3.0.5@1dc7ceb4a24aede938c7af2a9ed1de09609ca770',
  'sebastian/diff' => '3.0.3@14f72dd46eaf2f2293cbe79c93cc0bc43161a211',
  'sebastian/environment' => '4.2.4@d47bbbad83711771f167c72d4e3f25f7fcc1f8b0',
  'sebastian/exporter' => '3.1.5@73a9676f2833b9a7c36968f9d882589cd75511e6',
  'sebastian/global-state' => '3.0.2@de036ec91d55d2a9e0db2ba975b512cdb1c23921',
  'sebastian/object-enumerator' => '3.0.4@e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2',
  'sebastian/object-reflector' => '1.1.2@9b8772b9cbd456ab45d4a598d2dd1a1bced6363d',
  'sebastian/recursion-context' => '3.0.1@367dcba38d6e1977be014dc4b22f47a484dac7fb',
  'sebastian/resource-operations' => '2.0.2@31d35ca87926450c44eae7e2611d45a7a65ea8b3',
  'sebastian/type' => '1.1.4@0150cfbc4495ed2df3872fb31b26781e4e077eb4',
  'sebastian/version' => '2.0.1@99732be0ddb3361e16ad77b68ba41efc8e979019',
  'slevomat/coding-standard' => '7.2.1@aff06ae7a84e4534bf6f821dc982a93a5d477c90',
  'squizlabs/php_codesniffer' => '3.6.2@5e4e71592f69da17871dba6e80dd51bce74a351a',
  'symfony/console' => 'v5.4.19@dccb8d251a9017d5994c988b034d3e18aaabf740',
  'symfony/polyfill-intl-grapheme' => 'v1.27.0@511a08c03c1960e08a883f4cffcacd219b758354',
  'symfony/polyfill-intl-normalizer' => 'v1.27.0@19bd1e4fcd5b91116f14d8533c57831ed00571b6',
  'symfony/string' => 'v5.4.19@0a01071610fd861cc160dfb7e2682ceec66064cb',
  'theseer/tokenizer' => '1.2.1@****************************************',
  'vimeo/psalm' => '4.30.0@d0bc6e25d89f649e4f36a534f330f8bb4643dd69',
  'webmozart/path-util' => '2.3.0@d939f7edc24c9a1bb9c0dee5cb05d8e859490725',
  'phpmyadmin/phpmyadmin' => '5.2.1@',
);

    private function __construct()
    {
    }

    /**
     * @psalm-pure
     *
     * @psalm-suppress ImpureMethodCall we know that {@see InstalledVersions} interaction does not
     *                                  cause any side effects here.
     */
    public static function rootPackageName() : string
    {
        if (!self::composer2ApiUsable()) {
            return self::ROOT_PACKAGE_NAME;
        }

        return InstalledVersions::getRootPackage()['name'];
    }

    /**
     * @throws OutOfBoundsException If a version cannot be located.
     *
     * @psalm-param key-of<self::VERSIONS> $packageName
     * @psalm-pure
     *
     * @psalm-suppress ImpureMethodCall we know that {@see InstalledVersions} interaction does not
     *                                  cause any side effects here.
     */
    public static function getVersion(string $packageName): string
    {
        if (self::composer2ApiUsable()) {
            return InstalledVersions::getPrettyVersion($packageName)
                . '@' . InstalledVersions::getReference($packageName);
        }

        if (isset(self::VERSIONS[$packageName])) {
            return self::VERSIONS[$packageName];
        }

        throw new OutOfBoundsException(
            'Required package "' . $packageName . '" is not installed: check your ./vendor/composer/installed.json and/or ./composer.lock files'
        );
    }

    private static function composer2ApiUsable(): bool
    {
        if (!class_exists(InstalledVersions::class, false)) {
            return false;
        }

        if (method_exists(InstalledVersions::class, 'getAllRawData')) {
            $rawData = InstalledVersions::getAllRawData();
            if (count($rawData) === 1 && count($rawData[0]) === 0) {
                return false;
            }
        } else {
            $rawData = InstalledVersions::getRawData();
            if ($rawData === null || $rawData === []) {
                return false;
            }
        }

        return true;
    }
}
