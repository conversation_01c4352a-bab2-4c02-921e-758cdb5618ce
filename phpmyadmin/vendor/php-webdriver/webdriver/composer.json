{"name": "php-webdriver/webdriver", "description": "A PHP client for Selenium WebDriver. Previously facebook/webdriver.", "license": "MIT", "type": "library", "keywords": ["webdriver", "selenium", "php", "geckodriver", "chromedriver"], "homepage": "https://github.com/php-webdriver/php-webdriver", "require": {"php": "^5.6 || ~7.0 || ^8.0", "ext-curl": "*", "ext-json": "*", "ext-zip": "*", "symfony/polyfill-mbstring": "^1.12", "symfony/process": "^2.8 || ^3.1 || ^4.0 || ^5.0 || ^6.0"}, "require-dev": {"ondram/ci-detector": "^2.1 || ^3.5 || ^4.0", "php-coveralls/php-coveralls": "^2.4", "php-mock/php-mock-phpunit": "^1.1 || ^2.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpunit/phpunit": "^5.7 || ^7 || ^8 || ^9", "squizlabs/php_codesniffer": "^3.5", "symfony/var-dumper": "^3.3 || ^4.0 || ^5.0 || ^6.0"}, "replace": {"facebook/webdriver": "*"}, "suggest": {"ext-SimpleXML": "For Firefox profile creation"}, "minimum-stability": "dev", "autoload": {"psr-4": {"Facebook\\WebDriver\\": "lib/"}, "files": ["lib/Exception/TimeoutException.php"]}, "autoload-dev": {"psr-4": {"Facebook\\WebDriver\\": ["tests/unit", "tests/functional"]}, "classmap": ["tests/functional/"]}, "config": {"allow-plugins": {"ergebnis/composer-normalize": true}, "sort-packages": true}, "scripts": {"post-install-cmd": ["php -r 'if (PHP_VERSION_ID > 70103) { exit(1); }' || composer install --working-dir=tools/php-cs-fixer --no-progress --no-interaction"], "post-update-cmd": ["php -r 'if (PHP_VERSION_ID > 70103) { exit(1); }' || composer update --working-dir=tools/php-cs-fixer --no-progress --no-interaction"], "all": ["@lint", "@analyze", "@test"], "analyze": ["vendor/bin/phpstan analyze -c phpstan.neon --ansi", "tools/php-cs-fixer/vendor/bin/php-cs-fixer fix --diff --dry-run -vvv --ansi", "vendor/bin/phpcs --standard=PSR2 --ignore=*.js ./lib/ ./tests/"], "fix": ["@composer normalize", "tools/php-cs-fixer/vendor/bin/php-cs-fixer fix --diff -vvv || exit 0", "vendor/bin/phpcbf --standard=PSR2 --ignore=*.js ./lib/ ./tests/"], "lint": ["vendor/bin/parallel-lint -j 10 ./lib ./tests example.php", "@composer validate", "@composer normalize --dry-run"], "preinstall": ["@composer update --no-progress --no-interaction", "@composer require --dev phpstan/phpstan", "@composer require --dev ergeb<PERSON>/composer-normalize"], "test": ["vendor/bin/phpunit --colors=always"]}}