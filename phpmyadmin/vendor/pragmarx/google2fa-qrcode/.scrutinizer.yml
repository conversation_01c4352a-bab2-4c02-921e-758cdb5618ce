checks:
    php:
        remove_extra_empty_lines: true
        remove_php_closing_tag: true
        remove_trailing_whitespace: true
        fix_use_statements:
            remove_unused: true
            preserve_multiple: false
            preserve_blanklines: true
            order_alphabetically: true
        fix_php_opening_tag: true
        fix_linefeed: true
        fix_line_ending: true
        fix_identation_4spaces: true
        fix_doc_comments: true

filter:
     paths: [src/*]
     excluded_paths: [tests/*]

coding_style:
     php: {  }

build:
    tests:
        override:
        -
            command: 'vendor/bin/phpunit -c phpunit.xml'
            coverage:
                file: 'coverage/coverage-clover.xml'
                format: 'clover'
    nodes:
        analysis:
            tests:
                override:
                    - php-scrutinizer-run
        tests: true

tools:
    external_code_coverage: true
