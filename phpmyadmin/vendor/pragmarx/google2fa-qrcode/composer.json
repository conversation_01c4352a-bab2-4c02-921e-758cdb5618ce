{"name": "pragmarx/google2fa-qrcode", "description": "QR Code package for Google2FA", "keywords": ["authentication", "two factor authentication", "google2fa", "2fa", "QRCode", "qr code"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator & Designer"}], "require": {"php": ">=7.1", "pragmarx/google2fa": ">=4.0"}, "require-dev": {"phpunit/phpunit": "~4|~5|~6|~7|~8|~9", "khanamiryan/qrcode-detector-decoder": "^1.0", "bacon/bacon-qr-code": "^2.0", "chillerlan/php-qrcode": "^1.0|^2.0|^3.0|^4.0"}, "autoload": {"psr-4": {"PragmaRX\\Google2FAQRCode\\": "src/", "PragmaRX\\Google2FAQRCode\\Tests\\": "tests/"}}, "suggest": {"bacon/bacon-qr-code": "For QR Code generation, requires imagick", "chillerlan/php-qrcode": "For QR Code generation"}, "extra": {"component": "package", "branch-alias": {"dev-master": "1.0-dev"}}, "minimum-stability": "dev", "prefer-stable": true}