{"name": "sebastian/comparator", "description": "Provides the functionality to compare PHP values for equality", "keywords": ["comparator", "compare", "equality"], "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "prefer-stable": true, "require": {"php": ">=7.1", "sebastian/diff": "^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "config": {"optimize-autoloader": true, "sort-packages": true}, "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/_fixture"]}, "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}