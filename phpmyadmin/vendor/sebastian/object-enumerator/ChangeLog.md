# Change Log

All notable changes to `sebastianbergmann/object-enumerator` are documented in this file using the [Keep a CHANGELOG](http://keepachangelog.com/) principles.

## [3.0.4] - 2020-11-30

### Changed

* Changed PHP version constraint in `composer.json` from `^7.0` to `>=7.0`

## [3.0.3] - 2017-08-03

### Changed

* Bumped required version of `sebastian/object-reflector`

## [3.0.2] - 2017-03-12

### Changed

* `sebastian/object-reflector` is now a dependency

## [3.0.1] - 2017-03-12

### Fixed

* Objects aggregated in inherited attributes are not enumerated

## [3.0.0] - 2017-03-03

### Removed

* This component is no longer supported on PHP 5.6

## [2.0.1] - 2017-02-18

### Fixed

* Fixed [#2](https://github.com/sebastianbergmann/phpunit/pull/2): Exceptions in `ReflectionProperty::getValue()` are not handled

## [2.0.0] - 2016-11-19

### Changed

* This component is now compatible with `sebastian/recursion-context: ~1.0.4`

## 1.0.0 - 2016-02-04

### Added

* Initial release

[3.0.4]: https://github.com/sebastianbergmann/object-enumerator/compare/3.0.3...3.0.4
[3.0.3]: https://github.com/sebastianbergmann/object-enumerator/compare/3.0.2...3.0.3
[3.0.2]: https://github.com/sebastianbergmann/object-enumerator/compare/3.0.1...3.0.2
[3.0.1]: https://github.com/sebastianbergmann/object-enumerator/compare/3.0.0...3.0.1
[3.0.0]: https://github.com/sebastianbergmann/object-enumerator/compare/2.0...3.0.0
[2.0.1]: https://github.com/sebastianbergmann/object-enumerator/compare/2.0.0...2.0.1
[2.0.0]: https://github.com/sebastianbergmann/object-enumerator/compare/1.0...2.0.0

