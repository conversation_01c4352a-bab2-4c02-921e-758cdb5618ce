<?xml version="1.0" encoding="UTF-8"?>
<project name="type" default="setup">
    <target name="setup" depends="clean,composer"/>

    <target name="clean" description="Cleanup build artifacts">
        <delete dir="${basedir}/vendor"/>
        <delete file="${basedir}/composer.lock"/>
    </target>

    <target name="composer" depends="clean" description="Install dependencies with Composer">
        <exec executable="${basedir}/tools/composer" taskname="composer">
            <arg value="update"/>
            <arg value="--no-interaction"/>
            <arg value="--no-progress"/>
            <arg value="--no-ansi"/>
            <arg value="--no-suggest"/>
        </exec>
    </target>

    <target name="update-tools">
        <exec executable="phive" taskname="phive">
            <arg value="--no-progress"/>
            <arg value="update"/>
        </exec>

        <exec executable="${basedir}/tools/composer" taskname="composer">
            <arg value="self-update"/>
        </exec>
    </target>
</project>

