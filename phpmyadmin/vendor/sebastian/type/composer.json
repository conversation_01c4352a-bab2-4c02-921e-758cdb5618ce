{"name": "sebastian/type", "description": "Collection of value objects that represent the types of the PHP type system", "type": "library", "homepage": "https://github.com/sebastian<PERSON>mann/type", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues"}, "prefer-stable": true, "require": {"php": ">=7.2"}, "require-dev": {"phpunit/phpunit": "^8.2"}, "config": {"platform": {"php": "7.2.0"}, "optimize-autoloader": true, "sort-packages": true}, "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/_fixture"], "files": ["tests/_fixture/callback_function.php"]}, "extra": {"branch-alias": {"dev-master": "1.1-dev"}}}