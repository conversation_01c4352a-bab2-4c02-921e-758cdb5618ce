<?php declare(strict_types=1);
/*
 * This file is part of sebastian/type.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace <PERSON>\Type\TestFixture;

final class Iterator implements \Iterator
{
    public function current(): void
    {
    }

    public function next(): void
    {
    }

    public function key(): void
    {
    }

    public function valid(): void
    {
    }

    public function rewind(): void
    {
    }
}
