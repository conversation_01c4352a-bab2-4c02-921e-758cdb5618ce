<?php declare(strict_types=1);
/*
 * This file is part of sebastian/diff.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\Diff;

use PHPUnit\Framework\TestCase;

/**
 * @covers <PERSON><PERSON><PERSON><PERSON><PERSON>\Diff\InvalidArgumentException
 */
final class InvalidArgumentExceptionTest extends TestCase
{
    public function testInvalidArgumentException(): void
    {
        $previousException = new \LogicException();
        $message           = 'test';
        $code              = 123;

        $exception = new InvalidArgumentException($message, $code, $previousException);

        $this->assertInstanceOf(Exception::class, $exception);
        $this->assertSame($message, $exception->getMessage());
        $this->assertSame($code, $exception->getCode());
        $this->assertSame($previousException, $exception->getPrevious());
    }
}
