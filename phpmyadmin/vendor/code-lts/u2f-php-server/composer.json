{"name": "code-lts/u2f-php-server", "description": "Server side handling class for FIDO U2F registration and authentication", "license": "BSD-2-<PERSON><PERSON>", "homepage": "https://github.com/code-lts/U2F-php-server#readme", "authors": [{"name": "<PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "william<PERSON>@wdes.fr"}], "support": {"issues": "https://github.com/code-lts/U2F-php-server/issues", "source": "https://github.com/code-lts/U2F-php-server"}, "scripts": {"test": "@php phpunit", "phpcs": "@php phpcs", "phpcbf": "@php phpcbf"}, "require": {"php": "^7.1 || ^8.0", "ext-openssl": "*"}, "require-dev": {"phpunit/phpunit": "^7 || ^8 || ^9", "wdes/coding-standard": "^3.3"}, "autoload": {"psr-4": {"CodeLts\\U2F\\U2FServer\\": ["src/"]}}, "autoload-dev": {"psr-4": {"CodeLts\\U2F\\U2FServer\\Tests\\": ["test/"]}}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "archive": {"exclude": ["/test", "/phpunit.xml"]}}