<phpunit
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="http://schema.phpunit.de/7.0/phpunit.xsd"
        colors="true"
        backupGlobals="false"
        beStrictAboutOutputDuringTests="true"
        convertErrorsToExceptions="true"
        convertNoticesToExceptions="true"
        convertWarningsToExceptions="true"
        bootstrap="vendor/autoload.php"
>

    <filter>
        <whitelist>
            <directory>src</directory>
        </whitelist>
    </filter>

    <logging>
        <log type="coverage-html" target="build/coverage/html/"/>
        <log type="coverage-clover" target="build/logs/clover.xml"/>
    </logging>

    <testsuites>
        <testsuite name="Unit tests">
            <directory suffix=".php">test/</directory>
        </testsuite>
    </testsuites>

</phpunit>
