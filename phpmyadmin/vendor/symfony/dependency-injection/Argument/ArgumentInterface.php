<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\DependencyInjection\Argument;

/**
 * Represents a complex argument containing nested values.
 *
 * <AUTHOR> <<EMAIL>>
 */
interface ArgumentInterface
{
    /**
     * @return array
     */
    public function getValues();

    public function setValues(array $values);
}
