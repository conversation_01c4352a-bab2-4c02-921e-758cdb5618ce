{"name": "symfony/dependency-injection", "type": "library", "description": "Allows you to standardize and centralize the way objects are constructed in your application", "keywords": [], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=7.2.5", "psr/container": "^1.1.1", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22", "symfony/service-contracts": "^1.1.6|^2"}, "require-dev": {"symfony/yaml": "^4.4.26|^5.0|^6.0", "symfony/config": "^5.3|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/yaml": "", "symfony/config": "", "symfony/finder": "For using double-star glob patterns or when GLOB_BRACE portability is required", "symfony/expression-language": "For using expressions in service container configuration", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them"}, "conflict": {"ext-psr": "<1.1|>=2", "symfony/config": "<5.3", "symfony/finder": "<4.4", "symfony/proxy-manager-bridge": "<4.4", "symfony/yaml": "<4.4.26"}, "provide": {"psr/container-implementation": "1.0", "symfony/service-implementation": "1.0|2.0"}, "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "minimum-stability": "dev"}