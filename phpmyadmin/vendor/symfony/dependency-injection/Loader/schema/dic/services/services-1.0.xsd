<?xml version="1.0" encoding="UTF-8" ?>

<xsd:schema xmlns="http://symfony.com/schema/dic/services"
     xmlns:xsd="http://www.w3.org/2001/XMLSchema"
     targetNamespace="http://symfony.com/schema/dic/services"
     elementFormDefault="qualified">

  <xsd:annotation>
    <xsd:documentation><![CDATA[
      Symfony XML Services Schema, version 1.0
      Authors: <AUTHORS>

      This defines a way to describe PHP objects (services) and their
      dependencies.
    ]]></xsd:documentation>
  </xsd:annotation>

  <xsd:element name="container" type="container" />

  <xsd:complexType name="container">
    <xsd:annotation>
      <xsd:documentation><![CDATA[
        The root element of a service file.
      ]]></xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:group ref="foreign" />
      <xsd:sequence minOccurs="0">
        <xsd:element name="imports" type="imports" />
        <xsd:group ref="foreign" />
      </xsd:sequence>
      <xsd:sequence minOccurs="0">
        <xsd:element name="parameters" type="parameters" />
        <xsd:group ref="foreign" />
      </xsd:sequence>
      <xsd:sequence minOccurs="0">
        <xsd:element name="services" type="services" />
        <xsd:group ref="foreign" />
      </xsd:sequence>
      <xsd:sequence minOccurs="0" maxOccurs="unbounded">
        <xsd:element name="when" type="when" />
      </xsd:sequence>
    </xsd:sequence>
  </xsd:complexType>

  <xsd:complexType name="when">
    <xsd:sequence>
      <xsd:group ref="foreign" />
      <xsd:sequence minOccurs="0">
        <xsd:element name="imports" type="imports" />
        <xsd:group ref="foreign" />
      </xsd:sequence>
      <xsd:sequence minOccurs="0">
        <xsd:element name="parameters" type="parameters" />
        <xsd:group ref="foreign" />
      </xsd:sequence>
      <xsd:sequence minOccurs="0">
        <xsd:element name="services" type="services" />
        <xsd:group ref="foreign" />
      </xsd:sequence>
    </xsd:sequence>
    <xsd:attribute name="env" type="xsd:string" use="required" />
  </xsd:complexType>

  <xsd:group name="foreign">
    <xsd:sequence>
      <xsd:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded" />
    </xsd:sequence>
  </xsd:group>

  <xsd:complexType name="services">
    <xsd:annotation>
      <xsd:documentation><![CDATA[
        Enclosing element for the definition of all services
      ]]></xsd:documentation>
    </xsd:annotation>
    <xsd:choice maxOccurs="unbounded">
      <xsd:element name="service" type="service" minOccurs="1" />
      <xsd:element name="prototype" type="prototype" minOccurs="0" />
      <xsd:element name="defaults" type="defaults" minOccurs="0" maxOccurs="1" />
      <xsd:element name="instanceof" type="instanceof" minOccurs="0" />
      <xsd:element name="stack" type="stack" minOccurs="0" />
    </xsd:choice>
  </xsd:complexType>

  <xsd:complexType name="imports">
    <xsd:annotation>
      <xsd:documentation><![CDATA[
        Enclosing element for the import elements
      ]]></xsd:documentation>
    </xsd:annotation>
    <xsd:choice minOccurs="1" maxOccurs="unbounded">
      <xsd:element name="import" type="import" />
    </xsd:choice>
  </xsd:complexType>

  <xsd:complexType name="import">
    <xsd:annotation>
      <xsd:documentation><![CDATA[
        Import an external resource defining other services or parameters
      ]]></xsd:documentation>
    </xsd:annotation>
    <xsd:attribute name="resource" type="xsd:string" use="required" />
    <xsd:attribute name="ignore-errors" type="ignore_errors" />
    <xsd:attribute name="type" type="xsd:string" />
  </xsd:complexType>

  <xsd:complexType name="callable">
    <xsd:choice minOccurs="0" maxOccurs="1">
      <xsd:element name="service" type="service" minOccurs="0" maxOccurs="1" />
    </xsd:choice>
    <xsd:attribute name="service" type="xsd:string" />
    <xsd:attribute name="class" type="xsd:string" />
    <xsd:attribute name="method" type="xsd:string" />
    <xsd:attribute name="function" type="xsd:string" />
  </xsd:complexType>

  <xsd:complexType name="defaults">
    <xsd:annotation>
      <xsd:documentation><![CDATA[
        Enclosing element for the service definitions' defaults for the current file
      ]]></xsd:documentation>
    </xsd:annotation>
    <xsd:choice maxOccurs="unbounded">
      <xsd:element name="tag" type="tag" minOccurs="0" maxOccurs="unbounded" />
      <xsd:element name="bind" type="bind" minOccurs="0" maxOccurs="unbounded" />
    </xsd:choice>
    <xsd:attribute name="public" type="boolean" />
    <xsd:attribute name="autowire" type="boolean" />
    <xsd:attribute name="autoconfigure" type="boolean" />
  </xsd:complexType>

  <xsd:complexType name="service">
    <xsd:choice maxOccurs="unbounded">
      <xsd:element name="file" type="xsd:string" minOccurs="0" maxOccurs="1" />
      <xsd:element name="argument" type="argument" minOccurs="0" maxOccurs="unbounded" />
      <xsd:element name="configurator" type="callable" minOccurs="0" maxOccurs="1" />
      <xsd:element name="factory" type="callable" minOccurs="0" maxOccurs="1" />
      <xsd:element name="deprecated" type="deprecated" minOccurs="0" maxOccurs="1" />
      <xsd:element name="call" type="call" minOccurs="0" maxOccurs="unbounded" />
      <xsd:element name="tag" type="tag" minOccurs="0" maxOccurs="unbounded" />
      <xsd:element name="property" type="property" minOccurs="0" maxOccurs="unbounded" />
      <xsd:element name="bind" type="bind" minOccurs="0" maxOccurs="unbounded" />
    </xsd:choice>
    <xsd:attribute name="id" type="xsd:string" />
    <xsd:attribute name="class" type="xsd:string" />
    <xsd:attribute name="shared" type="boolean" />
    <xsd:attribute name="public" type="boolean" />
    <xsd:attribute name="synthetic" type="boolean" />
    <xsd:attribute name="lazy" type="xsd:string" />
    <xsd:attribute name="abstract" type="boolean" />
    <xsd:attribute name="alias" type="xsd:string" />
    <xsd:attribute name="parent" type="xsd:string" />
    <xsd:attribute name="decorates" type="xsd:string" />
    <xsd:attribute name="decoration-on-invalid" type="invalid_decorated_service_sequence" />
    <xsd:attribute name="decoration-inner-name" type="xsd:string" />
    <xsd:attribute name="decoration-priority" type="xsd:integer" />
    <xsd:attribute name="autowire" type="boolean" />
    <xsd:attribute name="autoconfigure" type="boolean" />
  </xsd:complexType>

  <xsd:complexType name="instanceof">
    <xsd:choice maxOccurs="unbounded">
      <xsd:element name="configurator" type="callable" minOccurs="0" maxOccurs="1" />
      <xsd:element name="call" type="call" minOccurs="0" maxOccurs="unbounded" />
      <xsd:element name="tag" type="tag" minOccurs="0" maxOccurs="unbounded" />
      <xsd:element name="property" type="property" minOccurs="0" maxOccurs="unbounded" />
      <xsd:element name="bind" type="bind" minOccurs="0" maxOccurs="unbounded" />
    </xsd:choice>
    <xsd:attribute name="id" type="xsd:string" use="required" />
    <xsd:attribute name="shared" type="boolean" />
    <xsd:attribute name="public" type="boolean" />
    <xsd:attribute name="lazy" type="xsd:string" />
    <xsd:attribute name="autowire" type="boolean" />
    <xsd:attribute name="autoconfigure" type="boolean" />
  </xsd:complexType>

  <xsd:complexType name="prototype">
    <xsd:choice maxOccurs="unbounded">
      <xsd:element name="argument" type="argument" minOccurs="0" maxOccurs="unbounded" />
      <xsd:element name="configurator" type="callable" minOccurs="0" maxOccurs="1" />
      <xsd:element name="factory" type="callable" minOccurs="0" maxOccurs="1" />
      <xsd:element name="deprecated" type="deprecated" minOccurs="0" maxOccurs="1" />
      <xsd:element name="call" type="call" minOccurs="0" maxOccurs="unbounded" />
      <xsd:element name="tag" type="tag" minOccurs="0" maxOccurs="unbounded" />
      <xsd:element name="property" type="property" minOccurs="0" maxOccurs="unbounded" />
      <xsd:element name="bind" type="bind" minOccurs="0" maxOccurs="unbounded" />
      <xsd:element name="exclude" type="xsd:string" minOccurs="0" maxOccurs="unbounded" />
    </xsd:choice>
    <xsd:attribute name="namespace" type="xsd:string" use="required" />
    <xsd:attribute name="resource" type="xsd:string" use="required" />
    <xsd:attribute name="exclude" type="xsd:string" />
    <xsd:attribute name="shared" type="boolean" />
    <xsd:attribute name="public" type="boolean" />
    <xsd:attribute name="lazy" type="xsd:string" />
    <xsd:attribute name="abstract" type="boolean" />
    <xsd:attribute name="parent" type="xsd:string" />
    <xsd:attribute name="autowire" type="boolean" />
    <xsd:attribute name="autoconfigure" type="boolean" />
  </xsd:complexType>

  <xsd:complexType name="stack">
    <xsd:choice maxOccurs="unbounded">
      <xsd:element name="service" type="service" minOccurs="1" />
      <xsd:element name="deprecated" type="deprecated" minOccurs="0" maxOccurs="1" />
    </xsd:choice>
    <xsd:attribute name="id" type="xsd:string" use="required" />
    <xsd:attribute name="public" type="boolean" />
  </xsd:complexType>

  <xsd:complexType name="tag">
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:anyAttribute namespace="##any" processContents="lax" />
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>

  <xsd:complexType name="deprecated">
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <!-- In Symfony 6, make these attributes required -->
        <xsd:attribute name="package" type="xsd:string" />
        <xsd:attribute name="version" type="xsd:string" />
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>

  <xsd:complexType name="parameters">
    <xsd:choice minOccurs="1" maxOccurs="unbounded">
      <xsd:element name="parameter" type="parameter" />
    </xsd:choice>
    <xsd:attribute name="type" type="parameter_type" />
    <xsd:attribute name="key" type="xsd:string" />
  </xsd:complexType>

  <xsd:complexType name="parameter" mixed="true">
    <xsd:choice minOccurs="0" maxOccurs="unbounded">
      <xsd:element name="parameter" type="parameter" />
    </xsd:choice>
    <xsd:attribute name="type" type="parameter_type" />
    <xsd:attribute name="id" type="xsd:string" />
    <xsd:attribute name="key" type="xsd:string" />
    <xsd:attribute name="on-invalid" type="invalid_sequence" />
  </xsd:complexType>

  <xsd:complexType name="property" mixed="true">
    <xsd:choice minOccurs="0">
      <xsd:element name="property" type="property" maxOccurs="unbounded" />
      <xsd:element name="service" type="service" />
    </xsd:choice>
    <xsd:attribute name="type" type="argument_type" />
    <xsd:attribute name="id" type="xsd:string" />
    <xsd:attribute name="key" type="xsd:string" />
    <xsd:attribute name="name" type="xsd:string" />
    <xsd:attribute name="on-invalid" type="invalid_sequence" />
    <xsd:attribute name="tag" type="xsd:string" />
  </xsd:complexType>

  <xsd:complexType name="bind" mixed="true">
    <xsd:choice maxOccurs="unbounded">
      <xsd:element name="bind" type="argument" minOccurs="0" maxOccurs="unbounded" />
      <xsd:element name="service" type="service" />
    </xsd:choice>
    <xsd:attribute name="type" type="argument_type" />
    <xsd:attribute name="id" type="xsd:string" />
    <xsd:attribute name="key" type="xsd:string" use="required" />
    <xsd:attribute name="on-invalid" type="invalid_sequence" />
    <xsd:attribute name="method" type="xsd:string" />
    <xsd:attribute name="tag" type="xsd:string" />
  </xsd:complexType>

  <xsd:complexType name="argument" mixed="true">
    <xsd:choice minOccurs="0">
      <xsd:element name="argument" type="argument" maxOccurs="unbounded" />
      <xsd:element name="service" type="service" />
    </xsd:choice>
    <xsd:attribute name="type" type="argument_type" />
    <xsd:attribute name="id" type="xsd:string" />
    <xsd:attribute name="key" type="xsd:string" />
    <xsd:attribute name="index" type="xsd:integer" />
    <xsd:attribute name="on-invalid" type="invalid_sequence" />
    <xsd:attribute name="tag" type="xsd:string" />
    <xsd:attribute name="index-by" type="xsd:string" />
    <xsd:attribute name="default-index-method" type="xsd:string" />
    <xsd:attribute name="default-priority-method" type="xsd:string" />
  </xsd:complexType>

  <xsd:complexType name="call">
    <xsd:choice minOccurs="0">
      <xsd:element name="argument" type="argument" maxOccurs="unbounded" />
    </xsd:choice>
    <xsd:attribute name="method" type="xsd:string" />
    <xsd:attribute name="returns-clone" type="boolean" />
  </xsd:complexType>

  <xsd:simpleType name="parameter_type">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="collection" />
      <xsd:enumeration value="string" />
      <xsd:enumeration value="constant" />
      <xsd:enumeration value="binary" />
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType name="argument_type">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="abstract" />
      <xsd:enumeration value="collection" />
      <xsd:enumeration value="service" />
      <xsd:enumeration value="expression" />
      <xsd:enumeration value="string" />
      <xsd:enumeration value="constant" />
      <xsd:enumeration value="binary" />
      <xsd:enumeration value="iterator" />
      <xsd:enumeration value="service_closure" />
      <xsd:enumeration value="service_locator" />
      <!-- "tagged" is an alias of "tagged_iterator", using "tagged_iterator" is preferred. -->
      <xsd:enumeration value="tagged" />
      <xsd:enumeration value="tagged_iterator" />
      <xsd:enumeration value="tagged_locator" />
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType name="ignore_errors">
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="(true|false|not_found)" />
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType name="invalid_sequence">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="null" />
      <xsd:enumeration value="ignore" />
      <xsd:enumeration value="exception" />
      <xsd:enumeration value="ignore_uninitialized" />
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType name="invalid_decorated_service_sequence">
    <xsd:restriction base="xsd:string">
      <xsd:enumeration value="null" />
      <xsd:enumeration value="ignore" />
      <xsd:enumeration value="exception" />
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType name="boolean">
    <xsd:restriction base="xsd:string">
      <xsd:pattern value="(%.+%|true|false)" />
    </xsd:restriction>
  </xsd:simpleType>
</xsd:schema>
