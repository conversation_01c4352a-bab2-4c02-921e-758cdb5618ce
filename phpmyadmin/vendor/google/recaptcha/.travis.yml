dist: trusty

language: php

sudo: false

php:
  - '5.5'
  - '5.6'
  - '7.0'
  - '7.1'
  - '7.2'
  - '7.3'

before_script:
  - composer install
  - phpenv version-name | grep ^5.[34] && echo "extension=apc.so" >> ~/.phpenv/versions/$(phpenv version-name)/etc/php.ini ; true
  - phpenv version-name | grep ^5.[34] && echo "apc.enable_cli=1" >> ~/.phpenv/versions/$(phpenv version-name)/etc/php.ini ; true

script:
  - mkdir -p build/logs
  - composer run-script lint
  - composer run-script test

after_success:
  - travis_retry php vendor/bin/php-coveralls

cache:
  directories:
    - "$HOME/.composer/cache/files"

git:
  depth: 5
