on: [push, pull_request]
name: CI
jobs:
  tests:
    name: Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        php-versions: ['7.0', '7.1', '7.2', '7.3', '7.4', '8.0', '8.1']

    steps:
    - name: Checkout
      uses: actions/checkout@v1

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-versions }}
        tools: "cs2pr"

    - name: "Cache dependencies installed with composer"
      uses: "actions/cache@v1"
      with:
        path: "~/.composer/cache"
        key: "php-${{ matrix.php-version }}-composer-locked-${{ hashFiles('composer.lock') }}"
        restore-keys: "php-${{ matrix.php-version }}-composer-locked-"

    - name: "Composer"
      run: "composer update --prefer-stable"

    - name: "PHPUnit"
      run: "php vendor/bin/phpunit"

# lint:
#   name: Lint
#   runs-on: ubuntu-latest

#   steps:
#   - name: Checkout
#     uses: actions/checkout@v1

#   - name: Setup PHP
#     uses: shivammathur/setup-php@v2
#     with:
#       php-version: 7.4

#   - name: "Cache dependencies installed with composer"
#     uses: "actions/cache@v1"
#     with:
#       path: "~/.composer/cache"
#       key: "php-${{ matrix.php-version }}-composer-locked-${{ hashFiles('composer.lock') }}"
#       restore-keys: "php-${{ matrix.php-version }}-composer-locked-"

#   - name: "Composer"
#     run: "composer update --prefer-stable"

#   - name: "assert:cs-lint"
#     run: "composer assert:cs-lint"

#   - name: "assert:sa-code"
#     run: "composer assert:sa-code"

#   - name: "assert:sa-tests"
#     run: "composer assert:sa-tests"
