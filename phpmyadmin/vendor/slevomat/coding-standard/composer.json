{"name": "slevomat/coding-standard", "description": "Slevomat Coding Standard for PHP_CodeSniffer complements Consistence Coding Standard by providing sniffs with additional checks.", "license": "MIT", "type": "phpcodesniffer-standard", "minimum-stability": "dev", "prefer-stable": true, "config": {"bin-dir": "bin", "platform": {"php": "7.4.0"}, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "require": {"php": "^7.2 || ^8.0", "dealerdirect/phpcodesniffer-composer-installer": "^0.6.2 || ^0.7", "phpstan/phpdoc-parser": "^1.5.1", "squizlabs/php_codesniffer": "^3.6.2"}, "require-dev": {"phing/phing": "2.17.3", "php-parallel-lint/php-parallel-lint": "1.3.2", "phpstan/phpstan": "1.4.10|1.7.1", "phpstan/phpstan-deprecation-rules": "1.0.0", "phpstan/phpstan-phpunit": "1.0.0|1.1.1", "phpstan/phpstan-strict-rules": "1.2.3", "phpunit/phpunit": "7.5.20|8.5.21|9.5.20"}, "autoload": {"psr-4": {"SlevomatCodingStandard\\": "SlevomatCodingStandard"}}, "autoload-dev": {"psr-4": {"SlevomatCodingStandard\\PHPStan\\": "build/PHPStan", "SlevomatCodingStandard\\": "tests"}}, "extra": {"branch-alias": {"dev-master": "7.x-dev"}}}