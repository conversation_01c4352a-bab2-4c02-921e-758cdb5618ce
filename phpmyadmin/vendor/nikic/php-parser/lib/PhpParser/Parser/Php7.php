<?php

namespace Php<PERSON>ars<PERSON>\Parser;

use <PERSON>p<PERSON><PERSON><PERSON>\Error;
use Php<PERSON><PERSON><PERSON>\Node;
use Php<PERSON>arser\Node\Expr;
use Php<PERSON>arser\Node\Name;
use PhpParser\Node\Scalar;
use Php<PERSON>arser\Node\Stmt;

/* This is an automatically GENERATED file, which should not be manually edited.
 * Instead edit one of the following:
 *  * the grammar files grammar/php5.y or grammar/php7.y
 *  * the skeleton file grammar/parser.template
 *  * the preprocessing script grammar/rebuildParsers.php
 */
class Php7 extends \PhpParser\ParserAbstract
{
    protected $tokenToSymbolMapSize = 396;
    protected $actionTableSize = 1223;
    protected $gotoTableSize = 626;

    protected $invalidSymbol = 168;
    protected $errorSymbol = 1;
    protected $defaultAction = -32766;
    protected $unexpectedTokenRule = 32767;

    protected $YY2TBLSTATE = 429;
    protected $numNonLeafStates = 726;

    protected $symbolToName = array(
        "EOF",
        "error",
        "T_THROW",
        "T_INCLUDE",
        "T_INCLUDE_ONCE",
        "T_EVAL",
        "T_REQUIRE",
        "T_REQUIRE_ONCE",
        "','",
        "T_LOGICAL_OR",
        "T_LOGICAL_XOR",
        "T_LOGICAL_AND",
        "T_PRINT",
        "T_YIELD",
        "T_DOUBLE_ARROW",
        "T_YIELD_FROM",
        "'='",
        "T_PLUS_EQUAL",
        "T_MINUS_EQUAL",
        "T_MUL_EQUAL",
        "T_DIV_EQUAL",
        "T_CONCAT_EQUAL",
        "T_MOD_EQUAL",
        "T_AND_EQUAL",
        "T_OR_EQUAL",
        "T_XOR_EQUAL",
        "T_SL_EQUAL",
        "T_SR_EQUAL",
        "T_POW_EQUAL",
        "T_COALESCE_EQUAL",
        "'?'",
        "':'",
        "T_COALESCE",
        "T_BOOLEAN_OR",
        "T_BOOLEAN_AND",
        "'|'",
        "'^'",
        "T_AMPERSAND_NOT_FOLLOWED_BY_VAR_OR_VARARG",
        "T_AMPERSAND_FOLLOWED_BY_VAR_OR_VARARG",
        "T_IS_EQUAL",
        "T_IS_NOT_EQUAL",
        "T_IS_IDENTICAL",
        "T_IS_NOT_IDENTICAL",
        "T_SPACESHIP",
        "'<'",
        "T_IS_SMALLER_OR_EQUAL",
        "'>'",
        "T_IS_GREATER_OR_EQUAL",
        "T_SL",
        "T_SR",
        "'+'",
        "'-'",
        "'.'",
        "'*'",
        "'/'",
        "'%'",
        "'!'",
        "T_INSTANCEOF",
        "'~'",
        "T_INC",
        "T_DEC",
        "T_INT_CAST",
        "T_DOUBLE_CAST",
        "T_STRING_CAST",
        "T_ARRAY_CAST",
        "T_OBJECT_CAST",
        "T_BOOL_CAST",
        "T_UNSET_CAST",
        "'@'",
        "T_POW",
        "'['",
        "T_NEW",
        "T_CLONE",
        "T_EXIT",
        "T_IF",
        "T_ELSEIF",
        "T_ELSE",
        "T_ENDIF",
        "T_LNUMBER",
        "T_DNUMBER",
        "T_STRING",
        "T_STRING_VARNAME",
        "T_VARIABLE",
        "T_NUM_STRING",
        "T_INLINE_HTML",
        "T_ENCAPSED_AND_WHITESPACE",
        "T_CONSTANT_ENCAPSED_STRING",
        "T_ECHO",
        "T_DO",
        "T_WHILE",
        "T_ENDWHILE",
        "T_FOR",
        "T_ENDFOR",
        "T_FOREACH",
        "T_ENDFOREACH",
        "T_DECLARE",
        "T_ENDDECLARE",
        "T_AS",
        "T_SWITCH",
        "T_MATCH",
        "T_ENDSWITCH",
        "T_CASE",
        "T_DEFAULT",
        "T_BREAK",
        "T_CONTINUE",
        "T_GOTO",
        "T_FUNCTION",
        "T_FN",
        "T_CONST",
        "T_RETURN",
        "T_TRY",
        "T_CATCH",
        "T_FINALLY",
        "T_USE",
        "T_INSTEADOF",
        "T_GLOBAL",
        "T_STATIC",
        "T_ABSTRACT",
        "T_FINAL",
        "T_PRIVATE",
        "T_PROTECTED",
        "T_PUBLIC",
        "T_READONLY",
        "T_VAR",
        "T_UNSET",
        "T_ISSET",
        "T_EMPTY",
        "T_HALT_COMPILER",
        "T_CLASS",
        "T_TRAIT",
        "T_INTERFACE",
        "T_ENUM",
        "T_EXTENDS",
        "T_IMPLEMENTS",
        "T_OBJECT_OPERATOR",
        "T_NULLSAFE_OBJECT_OPERATOR",
        "T_LIST",
        "T_ARRAY",
        "T_CALLABLE",
        "T_CLASS_C",
        "T_TRAIT_C",
        "T_METHOD_C",
        "T_FUNC_C",
        "T_LINE",
        "T_FILE",
        "T_START_HEREDOC",
        "T_END_HEREDOC",
        "T_DOLLAR_OPEN_CURLY_BRACES",
        "T_CURLY_OPEN",
        "T_PAAMAYIM_NEKUDOTAYIM",
        "T_NAMESPACE",
        "T_NS_C",
        "T_DIR",
        "T_NS_SEPARATOR",
        "T_ELLIPSIS",
        "T_NAME_FULLY_QUALIFIED",
        "T_NAME_QUALIFIED",
        "T_NAME_RELATIVE",
        "T_ATTRIBUTE",
        "';'",
        "']'",
        "'{'",
        "'}'",
        "'('",
        "')'",
        "'`'",
        "'\"'",
        "'$'"
    );

    protected $tokenToSymbol = array(
            0,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,   56,  166,  168,  167,   55,  168,  168,
          163,  164,   53,   50,    8,   51,   52,   54,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,   31,  159,
           44,   16,   46,   30,   68,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,   70,  168,  160,   36,  168,  165,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  161,   35,  162,   58,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,    1,    2,    3,    4,
            5,    6,    7,    9,   10,   11,   12,   13,   14,   15,
           17,   18,   19,   20,   21,   22,   23,   24,   25,   26,
           27,   28,   29,   32,   33,   34,   37,   38,   39,   40,
           41,   42,   43,   45,   47,   48,   49,   57,   59,   60,
           61,   62,   63,   64,   65,   66,   67,   69,   71,   72,
           73,   74,   75,   76,   77,   78,   79,   80,   81,   82,
           83,   84,   85,   86,   87,   88,   89,   90,   91,   92,
           93,   94,   95,   96,   97,   98,   99,  100,  101,  102,
          103,  104,  105,  106,  107,  108,  109,  110,  111,  112,
          113,  114,  115,  116,  117,  118,  119,  120,  121,  122,
          123,  124,  125,  126,  127,  128,  129,  130,  131,  132,
          133,  134,  135,  136,  137,  138,  139,  140,  141,  142,
          143,  144,  145,  146,  147,  148,  149,  150,  151,  152,
          153,  154,  155,  156,  157,  158
    );

    protected $action = array(
          132,  133,  134,  575,  135,  136,    0,  738,  739,  740,
          137,   37,  850,  825,  851,  476,-32766,-32766,-32766,-32767,
        -32767,-32767,-32767,  101,  102,  103,  104,  105, 1097, 1098,
         1099, 1096, 1095, 1094, 1100,  732,  731,-32766, 1289,-32766,
        -32766,-32766,-32766,-32766,-32766,-32766,-32767,-32767,-32767,-32767,
        -32767, 1022,  377,  376,    2,  741,-32766,-32766,-32766,-32766,
        -32766,  822,  417,-32766,-32766,-32766,-32766,-32766,-32766,  267,
          138,  399,  745,  746,  747,  748,  287,-32766,  423,-32766,
        -32766,-32766,-32766,-32766,-32766,  749,  750,  751,  752,  753,
          754,  755,  756,  757,  758,  759,  779,  576,  780,  781,
          782,  783,  771,  772,  340,  341,  774,  775,  760,  761,
          762,  764,  765,  766,  351,  806,  807,  808,  809,  810,
          577,  767,  768,  578,  579,  800,  791,  789,  790,  803,
          786,  787, -327,  423,  580,  581,  785,  582,  583,  584,
          585,  586,  587,  605, -590,  477,  -86,  814,  788,  588,
          589, -590,  139,-32766,-32766,-32766,  132,  133,  134,  575,
          135,  136, 1046,  738,  739,  740,  137,   37,  323, 1013,
          823,  824, 1334, 1324,-32766, 1335,-32766,-32766,-32766,-32766,
        -32766,-32766,-32766, 1097, 1098, 1099, 1096, 1095, 1094, 1100,
         -587,  732,  731,-32766,-32766,-32766,   12, -587,   81,-32766,
        -32766,-32766,  945,  946,  322,  927,   34,  947, 1224, 1223,
         1225,  741,  -86,  942,-32766, 1075,-32766,-32766,-32766,-32766,
        -32766,  239,-32766,-32766,-32766,  267,  138,  399,  745,  746,
          747,  748,  461,  462,  423,   35,  247,  103,  104,  105,
          128,  749,  750,  751,  752,  753,  754,  755,  756,  757,
          758,  759,  779,  576,  780,  781,  782,  783,  771,  772,
          340,  341,  774,  775,  760,  761,  762,  764,  765,  766,
          351,  806,  807,  808,  809,  810,  577,  767,  768,  578,
          579,  800,  791,  789,  790,  803,  786,  787, -327,  144,
          580,  581,  785,  582,  583,  584,  585,  586,  587, 1222,
           82,   83,   84, -590,  788,  588,  589, -590,  148,  763,
          733,  734,  735,  736,  737, 1309,  738,  739,  740,  776,
          777,   36, 1308,   85,   86,   87,   88,   89,   90,   91,
           92,   93,   94,   95,   96,   97,   98,   99,  100,  101,
          102,  103,  104,  105,  106,  107,  108,  288,  271, -587,
         -193,  375,  376, -587,  976,-32766, 1021,  453,  454,  455,
          109,  417,  945,  946,  741,  712,  819,  947,-32766,-32766,
        -32766, -271, 1073,  941, 1224, 1223, 1225,  288,  742,  743,
          744,  745,  746,  747,  748, -192, -365,  812, -365,-32766,
          599,-32766,-32766,  549,  749,  750,  751,  752,  753,  754,
          755,  756,  757,  758,  759,  779,  802,  780,  781,  782,
          783,  771,  772,  773,  801,  774,  775,  760,  761,  762,
          764,  765,  766,  805,  806,  807,  808,  809,  810,  811,
          767,  768,  769,  770,  800,  791,  789,  790,  803,  786,
          787,  251,  820,  778,  784,  785,  792,  793,  795,  794,
          796,  797,  732,  731, 1261, 1022, 1019,  788,  799,  798,
           49,   50,   51,  507,   52,   53, 1009, 1008, 1007, 1010,
           54,   55, -111,   56,  816, 1045,   14, -111, 1022, -111,
          287, 1305,  977,  306,  302, 1022,  238, -111, -111, -111,
         -111, -111, -111, -111, -111,  106,  107,  108, 1089,  271,
        -32766,-32766,-32766,  280,  284,  126, -193,  929,   57,   58,
          287,  109, 1019, -541,   59,  308,   60,  244,  245,   61,
           62,   63,   64,   65,   66,   67,   68, 1229,   27,  269,
           69,  439,  508, -341, 1022,  929, 1255, 1256,  509,  907,
          823, -192,  150,  907, 1253,   41,   24,  510,  352,  511,
          818,  512,  386,  513,   11,  699,  514,  515,  648,   25,
          814,   43,   44,  440,  372,  371,  907,   45,  516,  702,
         1220,  667,  668,  363,  334, -540,  357, -541, -541,  320,
         1215, 1249,  518,  519,  520, -581, 1074,  335,  724, -581,
         1019,-32766, -541,  336,  521,  522,  703, 1243, 1244, 1245,
         1246, 1240, 1241,  294, -541,  850, -547,  851,  823, 1247,
         1242,  365, 1022, 1224, 1223, 1225,  295, -153, -153, -153,
          369,   70,  897,  318,  319,  322,  897,  384,  149,  402,
          373,  374, -153,  435, -153,  436, -153,  280, -153, -540,
         -540,  141, 1220,  378,  379,  639,  640,  322,  370,  897,
          907,  437,  438,  829, -540,  -88,  151,  732,  731,  945,
          946,  153,  823,-32766,  517,  -51, -540,  154, -546,  883,
          941, -111, -111, -111,   31,  110,  111,  112,  113,  114,
          115,  116,  117,  118,  119,  120,  121,  122,  155,   74,
           27,  157,   32,  322,  -85,  123,  124,  909,  129,  697,
          130,  909,  823,  697, -153,  143, 1253,  158,-32766, -544,
         1229, -542,  159,  160, 1222,  161,  -79, 1134, 1136,  -75,
          285,-32766,-32766,-32766,  909,-32766,  697,-32766, -539,-32766,
         -301,  -73,-32766,  897,  -72,  -71, 1220,-32766,-32766,-32766,
          -16,  140, 1215,-32766,-32766,  732,  731,  322,  -70,-32766,
          414,  -69,   -4,  907,  -68,  -67,  521,  522,-32766, 1243,
         1244, 1245, 1246, 1240, 1241,  -66,  -47,  -18,  147,  270,
          281, 1247, 1242, -544, -544, -542, -542,  732,  731,  713,
          716,  906,-32766,   72,  146,  907,  319,  322, 1222, -297,
         -542,  823, -539, -539,  276,-32766,-32766,-32766,  277,-32766,
         -544,-32766, -542,-32766,  282,  283,-32766, -539,  909,  328,
          697,-32766,-32766,-32766,-32766,  704,  286,-32766,-32766, -539,
         1222,  923,  289,-32766,  414, 1220,  290,-32766,-32766,-32766,
          271,-32766,-32766,-32766,   47,-32766,  897, -111,-32766,  677,
          109,  814,  145,-32766,-32766,-32766,-32766,  823,  131,-32766,
        -32766, 1336,-32766,  654,  670,-32766,  414, 1104,  370,  637,
          430,  551,   73,   13,-32766,  293,  555,  295,  897,  945,
          946,  649,   74,  434,  517,  458,  322,  487,  690,  842,
          941, -111, -111, -111,  301, 1022,  561,  655,  671, 1260,
          300,-32766, -539,-32766,  907,  603,  303, 1222,  296,  297,
           39, 1262,    9,   40,-32766,-32766,-32766,    0,-32766,  907,
        -32766,  909,-32766,  697,   -4,-32766,    0, 1229,  907,    0,
        -32766,-32766,-32766,-32766,  307,  125,-32766,-32766,    0, 1222,
          907,    0,-32766,  414,    0,    0,-32766,-32766,-32766,  707,
        -32766,-32766,-32766,  962,-32766,  697, -505,-32766,  714, -495,
            7,  482,-32766,-32766,-32766,-32766, -539, -539,-32766,-32766,
           16, 1222,  567,  367,-32766,  414,  925,  295,-32766,-32766,
        -32766, -539,-32766,-32766,-32766,  822,-32766,  897,  721,-32766,
          722, -575,  888, -539,-32766,-32766,-32766,  986,  963,  970,
        -32766,-32766,  897, -249, -249, -249,-32766,  414,  823,  370,
          960,  897,  971,  886,  958,-32766, 1078, 1081,  718, 1082,
          945,  946, 1079,  897, 1080,  517, 1086,   33, 1250,  834,
          883,  941, -111, -111, -111,   27, 1275, 1293, 1327, -248,
         -248, -248, 1220,  642,  884,  370,  317,  823,  366,  698,
          701, 1253, 1331,  705, -111,  706,  945,  946,  708,  709,
          710,  517,  909,-32766,  697, -249,  883,  941, -111, -111,
         -111,  711,  715,  700, -509, 1333,  845,  909,   48,  697,
         -573, 1220,  844,  853,  295,  935,  909, 1215,  697,   74,
          978,  852, 1332,  322,  934,  932,  933,  936,  909, 1206,
          697, -248,  522,  916, 1243, 1244, 1245, 1246, 1240, 1241,
          926,  914,  968,  969, 1330, 1287, 1247, 1242, 1276, 1294,
        -32766, 1300, 1303, 1191, -547, -546, 1222, -545,   72, -489,
            1,  319,  322,-32766,-32766,-32766,   28,-32766,   29,-32766,
           38,-32766,  298,  299,-32766,   42,   46,   71,   75,-32766,
        -32766,-32766,   76,   77,   78,-32766,-32766,  368,   79,   80,
          142,-32766,  414,  152,  156,  243,  324,  352,  353,  127,
        -32766, -274,  354,  355,  356,  357,  358,  359,  360,  361,
          362,  364,  431,    0, -272, -271,   18,   19,   20,   21,
           23,  401,  478,  479,  486,  489,  490,  491,  492,  496,
          497,  498,  505,  684, 1233, 1174, 1251, 1048, 1047, 1028,
            0, 1210, 1024, -276, -103,   17,   22,   26,  292,  400,
          596,  600,  628,  689, 1178, 1228, 1175, 1306,    0,    0,
         1254,    0,  322
    );

    protected $actionCheck = array(
            2,    3,    4,    5,    6,    7,    0,    9,   10,   11,
           12,   13,  106,    1,  108,   31,    9,   10,   11,   44,
           45,   46,   47,   48,   49,   50,   51,   52,  116,  117,
          118,  119,  120,  121,  122,   37,   38,   30,    1,   32,
           33,   34,   35,   36,   37,   38,   39,   40,   41,   42,
           43,  138,  106,  107,    8,   57,    9,   10,   11,    9,
           10,  155,  116,    9,   10,   11,    9,   10,   11,   71,
           72,   73,   74,   75,   76,   77,  163,   30,   80,   32,
           33,   34,   35,   36,   30,   87,   88,   89,   90,   91,
           92,   93,   94,   95,   96,   97,   98,   99,  100,  101,
          102,  103,  104,  105,  106,  107,  108,  109,  110,  111,
          112,  113,  114,  115,  116,  117,  118,  119,  120,  121,
          122,  123,  124,  125,  126,  127,  128,  129,  130,  131,
          132,  133,    8,   80,  136,  137,  138,  139,  140,  141,
          142,  143,  144,   51,    1,  161,   31,   80,  150,  151,
          152,    8,  154,    9,   10,   11,    2,    3,    4,    5,
            6,    7,  164,    9,   10,   11,   12,   13,   70,    1,
           82,  159,   80,   85,   30,   83,   32,   33,   34,   35,
           36,   37,   38,  116,  117,  118,  119,  120,  121,  122,
            1,   37,   38,    9,   10,   11,    8,    8,  161,    9,
           10,   11,  117,  118,  167,    1,    8,  122,  155,  156,
          157,   57,   97,  128,   30,  162,   32,   33,   34,   35,
           30,   14,   32,   33,   34,   71,   72,   73,   74,   75,
           76,   77,  134,  135,   80,  147,  148,   50,   51,   52,
            8,   87,   88,   89,   90,   91,   92,   93,   94,   95,
           96,   97,   98,   99,  100,  101,  102,  103,  104,  105,
          106,  107,  108,  109,  110,  111,  112,  113,  114,  115,
          116,  117,  118,  119,  120,  121,  122,  123,  124,  125,
          126,  127,  128,  129,  130,  131,  132,  133,  164,    8,
          136,  137,  138,  139,  140,  141,  142,  143,  144,   80,
            9,   10,   11,  160,  150,  151,  152,  164,  154,    2,
            3,    4,    5,    6,    7,    1,    9,   10,   11,   12,
           13,   30,    8,   32,   33,   34,   35,   36,   37,   38,
           39,   40,   41,   42,   43,   44,   45,   46,   47,   48,
           49,   50,   51,   52,   53,   54,   55,   30,   57,  160,
            8,  106,  107,  164,   31,    9,  137,  129,  130,  131,
           69,  116,  117,  118,   57,  161,   80,  122,    9,   10,
           11,  164,    1,  128,  155,  156,  157,   30,   71,   72,
           73,   74,   75,   76,   77,    8,  106,   80,  108,   30,
            1,   32,   33,   85,   87,   88,   89,   90,   91,   92,
           93,   94,   95,   96,   97,   98,   99,  100,  101,  102,
          103,  104,  105,  106,  107,  108,  109,  110,  111,  112,
          113,  114,  115,  116,  117,  118,  119,  120,  121,  122,
          123,  124,  125,  126,  127,  128,  129,  130,  131,  132,
          133,    8,  156,  136,  137,  138,  139,  140,  141,  142,
          143,  144,   37,   38,  146,  138,  116,  150,  151,  152,
            2,    3,    4,    5,    6,    7,  119,  120,  121,  122,
           12,   13,  101,   15,   80,    1,  101,  106,  138,  108,
          163,    1,  159,    8,  113,  138,   97,  116,  117,  118,
          119,  120,  121,  122,  123,   53,   54,   55,  123,   57,
            9,   10,   11,  163,   30,   14,  164,  122,   50,   51,
          163,   69,  116,   70,   56,    8,   58,   59,   60,   61,
           62,   63,   64,   65,   66,   67,   68,    1,   70,   71,
           72,   73,   74,  162,  138,  122,   78,   79,   80,    1,
           82,  164,   14,    1,   86,   87,   88,   89,  163,   91,
          156,   93,  106,   95,  108,  161,   98,   99,   75,   76,
           80,  103,  104,  105,  106,  107,    1,  109,  110,   31,
          116,   75,   76,  115,  116,   70,  163,  134,  135,    8,
          122,    1,  124,  125,  126,  160,  159,    8,  161,  164,
          116,  137,  149,    8,  136,  137,   31,  139,  140,  141,
          142,  143,  144,  145,  161,  106,  163,  108,   82,  151,
          152,    8,  138,  155,  156,  157,  158,   75,   76,   77,
            8,  163,   84,  165,  166,  167,   84,    8,  101,  102,
          106,  107,   90,    8,   92,    8,   94,  163,   96,  134,
          135,  161,  116,  106,  107,  111,  112,  167,  106,   84,
            1,    8,    8,    8,  149,   31,   14,   37,   38,  117,
          118,   14,   82,  137,  122,   31,  161,   14,  163,  127,
          128,  129,  130,  131,   16,   17,   18,   19,   20,   21,
           22,   23,   24,   25,   26,   27,   28,   29,   14,  163,
           70,   14,   14,  167,   31,   16,   16,  159,   16,  161,
           16,  159,   82,  161,  162,   16,   86,   16,   74,   70,
            1,   70,   16,   16,   80,   16,   31,   59,   60,   31,
           37,   87,   88,   89,  159,   91,  161,   93,   70,   95,
           35,   31,   98,   84,   31,   31,  116,  103,  104,  105,
           31,  161,  122,  109,  110,   37,   38,  167,   31,  115,
          116,   31,    0,    1,   31,   31,  136,  137,  124,  139,
          140,  141,  142,  143,  144,   31,   31,   31,   31,   31,
           31,  151,  152,  134,  135,  134,  135,   37,   38,   31,
           31,   31,   74,  163,   31,    1,  166,  167,   80,   35,
          149,   82,  134,  135,   35,   87,   88,   89,   35,   91,
          161,   93,  161,   95,   35,   35,   98,  149,  159,   35,
          161,  103,  104,  105,   74,   31,   37,  109,  110,  161,
           80,   38,   37,  115,  116,  116,   37,   87,   88,   89,
           57,   91,  124,   93,   70,   95,   84,  128,   98,   77,
           69,   80,   70,  103,  104,  105,  137,   82,   31,  109,
          110,   83,   85,   96,   94,  115,  116,   82,  106,  113,
          108,   85,  154,   97,  124,  113,   89,  158,   84,  117,
          118,   90,  163,  128,  122,   97,  167,   97,   92,  127,
          128,  129,  130,  131,  133,  138,  153,  100,  100,  146,
          132,   74,   70,  137,    1,  153,  114,   80,  134,  135,
          159,  146,  150,  159,   87,   88,   89,   -1,   91,    1,
           93,  159,   95,  161,  162,   98,   -1,    1,    1,   -1,
          103,  104,  105,   74,  132,  161,  109,  110,   -1,   80,
            1,   -1,  115,  116,   -1,   -1,   87,   88,   89,   31,
           91,  124,   93,  159,   95,  161,  149,   98,   31,  149,
          149,  102,  103,  104,  105,   74,  134,  135,  109,  110,
          149,   80,   81,  149,  115,  116,  154,  158,   87,   88,
           89,  149,   91,  124,   93,  155,   95,   84,  159,   98,
          159,  163,  159,  161,  103,  104,  105,  159,  159,  159,
          109,  110,   84,  100,  101,  102,  115,  116,   82,  106,
          159,   84,  159,  159,  159,  124,  159,  159,  162,  159,
          117,  118,  159,   84,  159,  122,  159,  161,  160,  160,
          127,  128,  129,  130,  131,   70,  160,  160,  160,  100,
          101,  102,  116,  160,  162,  106,  161,   82,  161,  161,
          161,   86,  162,  161,  128,  161,  117,  118,  161,  161,
          161,  122,  159,  137,  161,  162,  127,  128,  129,  130,
          131,  161,  161,  161,  165,  162,  162,  159,   70,  161,
          163,  116,  162,  162,  158,  162,  159,  122,  161,  163,
          162,  162,  162,  167,  162,  162,  162,  162,  159,  162,
          161,  162,  137,  162,  139,  140,  141,  142,  143,  144,
          162,  162,  162,  162,  162,  162,  151,  152,  162,  162,
           74,  162,  162,  165,  163,  163,   80,  163,  163,  163,
          163,  166,  167,   87,   88,   89,  163,   91,  163,   93,
          163,   95,  134,  135,   98,  163,  163,  163,  163,  103,
          104,  105,  163,  163,  163,  109,  110,  149,  163,  163,
          163,  115,  116,  163,  163,  163,  163,  163,  163,  161,
          124,  164,  163,  163,  163,  163,  163,  163,  163,  163,
          163,  163,  163,   -1,  164,  164,  164,  164,  164,  164,
          164,  164,  164,  164,  164,  164,  164,  164,  164,  164,
          164,  164,  164,  164,  164,  164,  164,  164,  164,  164,
           -1,  164,  164,  164,  164,  164,  164,  164,  164,  164,
          164,  164,  164,  164,  164,  164,  164,  164,   -1,   -1,
          166,   -1,  167
    );

    protected $actionBase = array(
            0,   -2,  154,  542,  752,  893,  929,  580,   53,  394,
          855,  307,  307,   67,  307,  307,  307,  565,  908,  908,
          917,  908,  538,  784,  649,  649,  649,  708,  708,  708,
          708,  740,  740,  849,  849,  881,  817,  634, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036, 1036,
         1036, 1036,   12,  323,  389,  678, 1044, 1050, 1046, 1051,
         1042, 1041, 1045, 1047, 1052,  942,  943,  753,  946,  947,
          949,  950, 1048,  873, 1043, 1049,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  346,  491,   50,   57,   57,   57,   57,   57,
           57,   57,   57,   57,   57,   57,   57,   57,   57,   57,
           57,   57,   57,   57,   57,   54,   54,   54,  620,  620,
          359,  190,  184,  955,  955,  955,  955,  955,  955,  955,
          955,  955,  955,  658,   47,  144,  144,    7,    7,    7,
            7,    7,  371,  -25,  -25,  -25,  -25,  709,  347,  916,
          474,  526,  375,  280,  317,  245,  340,  340,  187,  187,
          396,  396,  -87,  -87,  396,  396,  396,  747,  747,  747,
          747,  443,  505,  -94,  308,  454,  480,  480,  480,  480,
          454,  454,  454,  454,  755, 1054,  454,  454,  454,  641,
          822,  822,  998,  442,  442,  442,  822,  499,  776,   88,
          499,   88,   37,   92,  756,   85,  -54,  425,  756,  639,
          764,  189,  143,  820,  524,  820, 1040,  385,  767,  413,
          735,  688,  857,  902, 1053,  787,  940,  788,  941,  228,
           98,  685, 1039, 1039, 1039, 1039, 1039, 1039, 1039, 1039,
         1039, 1039, 1039, 1055,  415, 1040,  286, 1055, 1055, 1055,
          415,  415,  415,  415,  415,  415,  415,  415,  415,  415,
          534,  286,  483,  496,  286,  774,  415,   12,  800,   12,
           12,   12,   12,   12,   12,   12,   12,   12,   12,  736,
          -16,   12,  323,  204,  204,  427,  168,  204,  204,  204,
          204,   12,   12,   12,  524,  773,  733,  537,  742,  377,
          773,  773,  773,  115,  124,  207,  342,  695,  754,  446,
          761,  761,  775,  957,  957,  761,  765,  761,  775,  973,
          761,  761,  957,  957,  809,  232,  625,  579,  612,  627,
          957,  475,  761,  761,  761,  761,  792,  643,  761,  433,
          281,  761,  761,  792,  758,  739,   46,  751,  957,  957,
          957,  792,  603,  751,  751,  751,  819,  821,  746,  738,
          571,  507,  645,  198,  783,  738,  738,  761,  619,  746,
          738,  746,  738,  812,  738,  738,  738,  746,  738,  765,
          585,  738,  691,  644,  188,  738,    6,  974,  975,  624,
          979,  967,  980, 1009,  981,  985,  878,  956,  992,  972,
          986,  965,  963,  750,  679,  680,  801,  797,  954,  771,
          771,  771,  951,  771,  771,  771,  771,  771,  771,  771,
          771,  679,  858,  814,  745,  777,  995,  682,  684,  743,
          872,  899,  948,  994, 1030,  987,  741,  689, 1016,  999,
          846,  875, 1000, 1001, 1017, 1031, 1032,  880,  772,  903,
          904,  859, 1003,  879,  771,  974,  985,  663,  972,  986,
          965,  963,  734,  724,  720,  723,  717,  704,  700,  703,
          737, 1033,  907,  818,  866, 1002,  952,  679,  867, 1012,
          856, 1018, 1019,  877,  778,  768,  868,  910, 1004, 1005,
         1006,  882, 1034,  884,  744, 1013,  997, 1020,  780,  911,
         1021, 1022, 1023, 1024,  887,  913,  888,  889,  823,  781,
         1010,  757,  918,  528,  769,  770,  789, 1008,  642,  993,
          900,  919,  920, 1025, 1026, 1027,  922,  923,  990,  828,
         1014,  760, 1015, 1011,  829,  830,  647,  785, 1035,  759,
          763,  779,  653,  674,  924,  925,  927,  991,  748,  762,
          841,  843, 1037,  683, 1038,  931,  677,  844,  696,  938,
         1029,  697,  699,  786,  901,  811,  782,  766, 1007,  749,
          845,  939,  847,  848,  850, 1028,  853,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,  458,  458,  458,
          458,  458,  458,  307,  307,  307,  307,    0,    0,  307,
            0,    0,    0,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  458,  458,
          458,  458,  458,  458,  458,  458,  458,  458,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  291,  291,  291,  291,  291,  291,
          291,  291,  291,  291,  219,  219,  291,  291,  291,  219,
          219,  219,  219,  219,  219,  219,  219,  219,  219,    0,
          291,  291,  291,  291,  291,  291,  291,  291,  809,  442,
          442,  442,  442,  219,  219,  219,  219,  219,  -88,  -88,
          219,  809,  219,  219,  442,  442,  219,  219,  219,  219,
          219,  219,  219,  219,  219,  219,  219,    0,    0,  286,
           88,  219,  765,  765,  765,  765,  219,  219,  219,  219,
           88,   88,  219,  219,  219,    0,    0,    0,    0,    0,
            0,    0,    0,  286,   88,    0,  286,    0,  765,  765,
          219,    0,  809,  314,  219,    0,    0,    0,    0,  286,
          765,  286,  415,  761,   88,  761,  415,  415,  204,   12,
          314,  527,  527,  527,  527,    0,    0,  524,  809,  809,
          809,  809,  809,  809,  809,  809,  809,  809,  809,  765,
            0,  809,    0,  765,  765,  765,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,  765,    0,    0,  957,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,  973,    0,    0,    0,    0,
            0,    0,  765,    0,    0,    0,    0,    0,    0,    0,
            0,  771,  778,    0,  778,    0,  771,  771,  771,    0,
            0,    0,    0,  785,  683
    );

    protected $actionDefault = array(
            3,32767,  103,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,  101,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,  593,  593,  593,
          593,32767,32767,  253,  103,32767,32767,  467,  385,  385,
          385,32767,32767,  537,  537,  537,  537,  537,  537,32767,
        32767,32767,32767,32767,32767,  467,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,  101,32767,
        32767,32767,   37,    7,    8,   10,   11,   50,   17,  323,
        32767,32767,32767,32767,  103,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,  586,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,  471,  450,  451,  453,
          454,  384,  538,  592,  326,  589,  383,  146,  338,  328,
          241,  329,  257,  472,  258,  473,  476,  477,  214,  286,
          380,  150,  414,  468,  416,  466,  470,  415,  390,  395,
          396,  397,  398,  399,  400,  401,  402,  403,  404,  405,
          406,  407,  388,  389,  469,  447,  446,  445,32767,32767,
          412,  413,  417,32767,32767,32767,32767,32767,32767,32767,
        32767,  103,32767,  387,  420,  418,  419,  436,  437,  434,
          435,  438,32767,  439,  440,  441,  442,32767,  315,32767,
        32767,32767,  364,  362,  315,  112,32767,32767,  427,  428,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,  531,  444,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,  103,32767,  101,  533,
          409,  411,  501,  422,  423,  421,  391,32767,  508,32767,
          103,  510,32767,32767,32767,32767,32767,32767,32767,  532,
        32767,  539,  539,32767,  494,  101,  194,32767,32767,32767,
          194,  194,32767,32767,32767,32767,32767,32767,32767,32767,
          600,  494,  111,  111,  111,  111,  111,  111,  111,  111,
          111,  111,  111,32767,  194,  111,32767,32767,32767,  101,
          194,  194,  194,  194,  194,  194,  194,  194,  194,  194,
          189,32767,  267,  269,  103,  554,  194,32767,  513,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  506,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,  494,  432,  139,32767,  139,  539,
          424,  425,  426,  496,  539,  539,  539,  311,  288,32767,
        32767,32767,32767,  511,  511,  101,  101,  101,  101,  506,
        32767,32767,32767,32767,  112,  100,  100,  100,  100,  100,
          104,  102,32767,32767,32767,32767,  222,  100,32767,  102,
          102,32767,32767,  222,  224,  211,  102,  226,32767,  558,
          559,  222,  102,  226,  226,  226,  246,  246,  483,  317,
          102,  100,  102,  102,  196,  317,  317,32767,  102,  483,
          317,  483,  317,  198,  317,  317,  317,  483,  317,32767,
          102,  317,  213,  100,  100,  317,32767,32767,32767,  496,
        32767,32767,32767,32767,32767,32767,32767,  221,32767,32767,
        32767,32767,32767,32767,32767,  526,32767,  543,  556,  430,
          431,  433,  541,  455,  456,  457,  458,  459,  460,  461,
          463,  588,32767,  500,32767,32767,32767,32767,  337,  598,
        32767,  598,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,  599,32767,  539,
        32767,32767,32767,32767,  429,    9,   76,  489,   43,   44,
           52,   58,  517,  518,  519,  520,  514,  515,  521,  516,
        32767,32767,  522,  564,32767,32767,  540,  591,32767,32767,
        32767,32767,32767,32767,  139,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,  526,32767,  137,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
          539,32767,32767,32767,32767,  313,  310,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,  539,32767,32767,32767,32767,32767,  290,
        32767,  307,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,  285,32767,
        32767,  379,32767,32767,32767,32767,  358,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,  152,  152,    3,
            3,  340,  152,  152,  152,  340,  340,  152,  340,  340,
          340,  152,  152,  152,  152,  152,  152,  279,  184,  261,
          264,  246,  246,  152,  350,  152
    );

    protected $goto = array(
          194,  194,  685,  425,  653,  346,  614,  650,  419,  310,
          311,  331,  569,  316,  424,  332,  426,  630, 1200,  930,
          693, 1051, 1201, 1204,  931, 1205,  165,  165,  165,  165,
          218,  195,  191,  191,  175,  177,  213,  191,  191,  191,
          191,  191,  192,  192,  192,  192,  192,  192,  186,  187,
          188,  189,  190,  215,  213,  216,  529,  530,  415,  531,
          533,  534,  535,  536,  537,  538,  539,  540, 1120,  166,
          167,  168,  193,  169,  170,  171,  164,  172,  173,  174,
          176,  212,  214,  217,  235,  240,  241,  242,  254,  255,
          256,  257,  258,  259,  260,  261,  263,  264,  265,  266,
          278,  279,  313,  314,  315,  420,  421,  422,  574,  219,
          220,  221,  222,  223,  224,  225,  226,  227,  228,  229,
          230,  231,  232,  233,  178,  234,  179,  196,  197,  198,
          236,  186,  187,  188,  189,  190,  215, 1120,  199,  180,
          181,  182,  200,  196,  183,  237,  201,  199,  163,  202,
          203,  184,  204,  205,  206,  185,  207,  208,  209,  210,
          211,  275,  275,  275,  275,  843,  593,  646,  647,  560,
          664,  665,  666,  720,  629,  631,  840,  418,  651,  604,
          841,  350,  675,  679,  996,  683,  691,  992,  616,  616,
          817,  350,  350, 1252, 1252, 1252, 1252, 1252, 1252, 1252,
         1252, 1252, 1252, 1092, 1093,  350,  350,  874,  350,  848,
         1337,  896,  891,  892,  905,  849,  893,  846,  894,  895,
          847,  548,  900,  899,  901,  350,  391,  394,  554,  594,
          598, 1270, 1270, 1072, 1068, 1069, 1270, 1270, 1270, 1270,
         1270, 1270, 1270, 1270, 1270, 1270, 1268, 1268,  815,  347,
          348, 1268, 1268, 1268, 1268, 1268, 1268, 1268, 1268, 1268,
         1268, 1221, 1020, 1221, 1020, 1221,  836,    5, 1020,    6,
         1020, 1020, 1281,  961, 1020, 1020, 1020, 1020, 1020, 1020,
         1020, 1020, 1020, 1020, 1020,  349,  349,  349,  349, 1221,
          460,  460,  566,  678, 1221, 1221, 1221, 1221,  344,  460,
         1221, 1221, 1221, 1302, 1302, 1302, 1302,  602,  617,  620,
          621,  622,  623,  643,  644,  645,  695,  836,  912,  553,
          546, 1310,  913,  548,  532,  532,  821,  856,  982,  532,
          532,  532,  532,  532,  532,  532,  532,  532,  532,  543,
          473,  543,  868,  543,  928,  855,  928,  389,  475,  337,
          546,  553,  562,  563,  339,  572,  595,  609,  610, 1320,
         1320,  249,  249, 1026, 1025,   15,  821,  450,  821,  494,
          565,  495,  955,  955,  955,  955, 1320,  501,  450,  949,
          956,  839,  652, 1321, 1321, 1169, 1214,  246,  246,  246,
          246,  248,  250, 1323,  985,  959,  959,  957,  959,  719,
         1321,  545,  994,  989,  470, 1295, 1296,  953,  405,  692,
          917, 1108,  432,  541,  541,  541,  541,  612,  597,  452,
          444, 1029, 1030, 1001,  658,  444, 1292,  444, 1292,  674,
         1292,  860,  833,  656,  980,  836,  861,  547,  557,  854,
          321,  305,  547,  333,  557, 1297, 1298,  392,  456,  570,
          607, 1211,  944,  398,  858, 1304, 1304, 1304, 1304,  463,
          573,  464,  465,  608, 1004,  866,  403,  404, 1328, 1329,
         1057,  662, 1212,  663,  471,  407,  408,  409,  723,  676,
          870, 1288,  410,  624,  626,  627,  342,  427, 1216,  869,
          857, 1056, 1060,  427,  864, 1061, 1103,  966,    0,    0,
          964, 1027, 1027,    0,    0,    0,  657, 1038, 1034, 1035,
          444,  444,  444,  444,  444,  444,  444,  444,  444,  444,
          444,    0, 1059,  444,  954,    0, 1290, 1290, 1059,  592,
         1085,    0,  696,  682,  682,    0,  502,  688, 1083,    0,
            0,    0, 1217, 1218,  272,  428, 1101,  873,    0,  544,
          831,  544,    0,    0,    0,  673,  938,    0,    0, 1015,
         1031, 1032,    0,    0,    0,    0,    0,    0, 1219, 1278,
         1279,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,  252,  252,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,  999,  999
    );

    protected $gotoCheck = array(
           42,   42,   72,   65,   65,   96,   55,   55,   65,   65,
           65,   65,   65,   65,   65,   65,   65,   65,   78,   78,
            9,  126,   78,   78,   78,   78,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   23,   23,   23,   23,   15,  129,   85,   85,   48,
           85,   85,   85,   48,   48,   48,   26,   13,   48,   13,
           27,   14,   48,   48,   48,   48,   48,   48,  107,  107,
            7,   14,   14,  107,  107,  107,  107,  107,  107,  107,
          107,  107,  107,  143,  143,   14,   14,   45,   14,   15,
           14,   15,   15,   15,   15,   15,   15,   15,   15,   15,
           15,   14,   64,   15,   64,   14,   58,   58,   58,   58,
           58,  168,  168,   15,   15,   15,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  169,  169,    6,   96,
           96,  169,  169,  169,  169,  169,  169,  169,  169,  169,
          169,   72,   72,   72,   72,   72,   22,   46,   72,   46,
           72,   72,   14,   49,   72,   72,   72,   72,   72,   72,
           72,   72,   72,   72,   72,   24,   24,   24,   24,   72,
          148,  148,  170,   14,   72,   72,   72,   72,  177,  148,
           72,   72,   72,    9,    9,    9,    9,   80,   80,   80,
           80,   80,   80,   80,   80,   80,   80,   22,   72,   75,
           75,  179,   72,   14,  171,  171,   12,   35,  102,  171,
          171,  171,  171,  171,  171,  171,  171,  171,  171,   19,
           83,   19,   35,   19,    9,   35,    9,   61,   83,   75,
           75,   75,   75,   75,   75,   75,   75,   75,   75,  180,
          180,    5,    5,  117,  117,   75,   12,   19,   12,  154,
          103,  154,   19,   19,   19,   19,  180,  154,   19,   19,
           19,   25,   63,  181,  181,  150,   14,    5,    5,    5,
            5,    5,    5,  180,   25,   25,   25,   25,   25,   25,
          181,   25,   25,   25,  174,  174,  174,   92,   92,   92,
           17,   17,  112,  106,  106,  106,  106,   17,  106,   82,
           23,  118,  118,   17,  119,   23,  129,   23,  129,  115,
          129,   17,   18,   17,   17,   22,   39,    9,    9,   17,
          167,  167,    9,   29,    9,  176,  176,    9,    9,    2,
            2,   17,   91,   28,   37,  129,  129,  129,  129,    9,
            9,    9,    9,   79,  109,    9,   81,   81,    9,    9,
          128,   81,  159,   81,  156,   81,   81,   81,   98,   81,
           41,  129,   81,   84,   84,   84,   81,  116,   20,   16,
           16,   16,   16,  116,    9,  131,  146,   95,   -1,   -1,
           16,  116,  116,   -1,   -1,   -1,  116,  116,  116,  116,
           23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
           23,   -1,  129,   23,   16,   -1,  129,  129,  129,    8,
            8,   -1,    8,    8,    8,   -1,    8,    8,    8,   -1,
           -1,   -1,   20,   20,   24,   88,   16,   16,   -1,   24,
           20,   24,   -1,   -1,   -1,   88,   88,   -1,   -1,   88,
           88,   88,   -1,   -1,   -1,   -1,   -1,   -1,   20,   20,
           20,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,    5,    5,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,  106,  106
    );

    protected $gotoBase = array(
            0,    0, -250,    0,    0,  360,  235,  181,  522,    7,
            0,    0,   33, -156, -113, -178,   43,  -49,  126,   72,
          100,    0,   -9,  158,  282,  377,  172,  176,  120,  150,
            0,    0,    0,    0,    0,  -39,    0,  119,    0,  116,
            0,   45,   -1,    0,    0,  195, -456,    0, -529,  250,
            0,    0,    0,    0,    0,  -33,    0,    0,  182,    0,
            0,  306,    0,  143,  203, -235,    0,    0,    0,    0,
            0,    0,   -6,    0,    0,  -21,    0,    0, -385,  124,
          -46,  -19,  144, -123,   10, -538,    0,    0,  275,    0,
            0,  127,  106,    0,    0,   60, -472,    0,   76,    0,
            0,    0,  294,  328,    0,    0,  386,  -50,    0,   99,
            0,    0,  138,    0,    0,  149,  219,   87,  139,  137,
            0,    0,    0,    0,    0,    0,   19,    0,  101,  159,
            0,   59,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,  -69,    0,    0,   58,    0,  257,    0,
          114,    0,    0,    0, -120,    0,   40,    0,    0,  108,
            0,    0,    0,    0,    0,    0,    0,  122,   -7,    8,
          264,   86,    0,    0,  107,    0,   78,  269,    0,  291,
           55,   79,    0,    0
    );

    protected $gotoDefault = array(
        -32768,  506,  727,    4,  728,  921,  804,  813,  590,  523,
          694,  343,  618,  416, 1286,  898, 1107,  571,  832, 1230,
         1238,  451,  835,  326,  717,  880,  881,  882,  395,  381,
          387,  393,  641,  619,  488,  867,  447,  859,  480,  862,
          446,  871,  162,  413,  504,  875,    3,  877,  550,  908,
          382,  885,  383,  669,  887,  556,  889,  890,  390,  396,
          397, 1112,  564,  615,  902,  253,  558,  903,  380,  904,
          911,  385,  388,  680,  459,  499,  493,  406, 1087,  559,
          601,  638,  441,  467,  613,  625,  611,  474, 1023,  411,
          325,  943,  951,  481,  457,  965,  345,  973,  725, 1119,
          632,  483,  981,  633,  988,  991,  524,  525,  472, 1003,
          268, 1006,  484, 1044,  659, 1017, 1018,  660,  634, 1040,
          635,  661,  636, 1042,  466,  591, 1050,  448, 1058, 1274,
          449, 1062,  262, 1065,  274,  412,  429, 1070, 1071,    8,
         1077,  686,  687,   10,  273,  503, 1102,  681,  445, 1118,
          433, 1188, 1190,  552,  485, 1208, 1207,  672,  500, 1213,
          442, 1277,  443,  526,  468,  312,  527,  304,  329,  309,
          542,  291,  330,  528,  469, 1283, 1291,  327,   30, 1311,
         1322,  338,  568,  606
    );

    protected $ruleToNonTerminal = array(
            0,    1,    3,    3,    2,    5,    5,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    7,    7,
            7,    7,    7,    7,    7,    7,    8,    8,    9,   10,
           11,   11,   11,   12,   12,   13,   13,   14,   15,   15,
           16,   16,   17,   17,   18,   18,   21,   21,   22,   23,
           23,   24,   24,    4,    4,    4,    4,    4,    4,    4,
            4,    4,    4,    4,   29,   29,   30,   30,   32,   34,
           34,   28,   36,   36,   33,   38,   38,   35,   35,   37,
           37,   39,   39,   31,   40,   40,   41,   43,   44,   44,
           45,   46,   46,   48,   47,   47,   47,   47,   49,   49,
           49,   49,   49,   49,   49,   49,   49,   49,   49,   49,
           49,   49,   49,   49,   49,   49,   49,   49,   49,   49,
           49,   49,   25,   25,   68,   68,   71,   71,   70,   69,
           69,   62,   74,   74,   75,   75,   76,   76,   77,   77,
           78,   78,   79,   79,   26,   26,   27,   27,   27,   27,
           27,   87,   87,   89,   89,   82,   82,   90,   90,   91,
           91,   91,   83,   83,   86,   86,   84,   84,   92,   93,
           93,   56,   56,   64,   64,   67,   67,   67,   66,   94,
           94,   95,   57,   57,   57,   57,   96,   96,   97,   97,
           98,   98,   99,  100,  100,  101,  101,  102,  102,   54,
           54,   50,   50,  104,   52,   52,  105,   51,   51,   53,
           53,   63,   63,   63,   63,   80,   80,  108,  108,  110,
          110,  111,  111,  111,  111,  109,  109,  109,  113,  113,
          113,  113,   88,   88,  116,  116,  116,  117,  117,  114,
          114,  118,  118,  120,  120,  121,  121,  115,  122,  122,
          119,  123,  123,  123,  123,  112,  112,   81,   81,   81,
           20,   20,   20,  125,  124,  124,  126,  126,  126,  126,
           59,  127,  127,  128,   60,  130,  130,  131,  131,  132,
          132,   85,  133,  133,  133,  133,  133,  133,  138,  138,
          139,  139,  140,  140,  140,  140,  140,  141,  142,  142,
          137,  137,  134,  134,  136,  136,  144,  144,  143,  143,
          143,  143,  143,  143,  143,  135,  145,  145,  147,  146,
          146,   61,  103,  148,  148,   55,   55,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
          155,  149,  149,  154,  154,  157,  158,  158,  159,  160,
          161,  161,  161,  161,   19,   19,   72,   72,   72,   72,
          150,  150,  150,  150,  163,  163,  151,  151,  153,  153,
          153,  156,  156,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  169,  169,  107,  171,  171,  171,  171,  152,
          152,  152,  152,  152,  152,  152,  152,   58,   58,  166,
          166,  166,  166,  172,  172,  162,  162,  162,  173,  173,
          173,  173,  173,  173,   73,   73,   65,   65,   65,   65,
          129,  129,  129,  129,  176,  175,  165,  165,  165,  165,
          165,  165,  165,  164,  164,  164,  174,  174,  174,  174,
          106,  170,  178,  178,  177,  177,  179,  179,  179,  179,
          179,  179,  179,  179,  167,  167,  167,  167,  181,  182,
          180,  180,  180,  180,  180,  180,  180,  180,  183,  183,
          183,  183
    );

    protected $ruleToLength = array(
            1,    1,    2,    0,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            0,    1,    0,    1,    1,    2,    1,    3,    4,    1,
            2,    0,    1,    1,    1,    1,    1,    3,    5,    4,
            3,    4,    2,    3,    1,    1,    7,    6,    2,    3,
            1,    2,    3,    1,    2,    3,    1,    1,    3,    1,
            3,    1,    2,    2,    3,    1,    3,    2,    3,    1,
            3,    2,    0,    1,    1,    1,    1,    1,    3,    7,
           10,    5,    7,    9,    5,    3,    3,    3,    3,    3,
            3,    1,    2,    5,    7,    9,    6,    5,    6,    3,
            2,    1,    1,    1,    0,    2,    1,    3,    8,    0,
            4,    2,    1,    3,    0,    1,    0,    1,    0,    1,
            3,    1,    1,    1,    8,    9,    7,    8,    7,    6,
            8,    0,    2,    0,    2,    1,    2,    1,    2,    1,
            1,    1,    0,    2,    0,    2,    0,    2,    2,    1,
            3,    1,    4,    1,    4,    1,    1,    4,    2,    1,
            3,    3,    3,    4,    4,    5,    0,    2,    4,    3,
            1,    1,    7,    0,    2,    1,    3,    3,    4,    1,
            4,    0,    2,    5,    0,    2,    6,    0,    2,    0,
            3,    1,    2,    1,    1,    2,    0,    1,    3,    0,
            2,    1,    1,    1,    1,    6,    8,    6,    1,    2,
            1,    1,    1,    1,    1,    1,    1,    1,    3,    3,
            3,    1,    3,    3,    3,    3,    3,    1,    3,    3,
            1,    1,    2,    1,    1,    0,    1,    0,    2,    2,
            2,    4,    3,    1,    1,    3,    1,    2,    2,    3,
            2,    3,    1,    1,    2,    3,    1,    1,    3,    2,
            0,    1,    5,    5,   10,    3,    5,    1,    1,    3,
            0,    2,    4,    5,    4,    4,    4,    3,    1,    1,
            1,    1,    1,    1,    0,    1,    1,    2,    1,    1,
            1,    1,    1,    1,    1,    2,    1,    3,    1,    1,
            3,    2,    2,    3,    1,    0,    1,    1,    3,    3,
            3,    4,    1,    1,    2,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    2,    2,
            2,    2,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            2,    2,    2,    2,    3,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    5,    4,    3,    4,    4,
            2,    2,    4,    2,    2,    2,    2,    2,    2,    2,
            2,    2,    2,    2,    1,    3,    2,    1,    2,    4,
            2,    2,    8,    9,    8,    9,    9,   10,    9,   10,
            8,    3,    2,    0,    4,    2,    1,    3,    2,    1,
            2,    2,    2,    4,    1,    1,    1,    1,    1,    1,
            1,    1,    3,    1,    1,    1,    0,    3,    0,    1,
            1,    0,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    3,    3,    3,    4,    1,    1,    3,    1,
            1,    1,    1,    1,    3,    2,    3,    0,    1,    1,
            3,    1,    1,    1,    1,    1,    3,    1,    1,    4,
            4,    1,    4,    4,    0,    1,    1,    1,    3,    3,
            1,    4,    2,    2,    1,    3,    1,    4,    4,    3,
            3,    3,    3,    1,    3,    1,    1,    3,    1,    1,
            4,    1,    1,    1,    3,    1,    1,    2,    1,    3,
            4,    3,    2,    0,    2,    2,    1,    2,    1,    1,
            1,    4,    3,    3,    3,    3,    6,    3,    1,    1,
            2,    1
    );

    protected function initReduceCallbacks() {
        $this->reduceCallbacks = [
            0 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            1 => function ($stackPos) {
                 $this->semValue = $this->handleNamespaces($this->semStack[$stackPos-(1-1)]);
            },
            2 => function ($stackPos) {
                 if (is_array($this->semStack[$stackPos-(2-2)])) { $this->semValue = array_merge($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)]); } else { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)]; };
            },
            3 => function ($stackPos) {
                 $this->semValue = array();
            },
            4 => function ($stackPos) {
                 $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop($this->createCommentNopAttributes($startAttributes['comments'])); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            5 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            6 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            7 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            8 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            9 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            10 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            11 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            12 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            13 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            14 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            15 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            16 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            17 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            18 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            19 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            20 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            21 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            22 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            23 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            24 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            25 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            26 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            27 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            28 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            29 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            30 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            31 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            32 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            33 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            34 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            35 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            36 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            37 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            38 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            39 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            40 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            41 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            42 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            43 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            44 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            45 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            46 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            47 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            48 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            49 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            50 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            51 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            52 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            53 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            54 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            55 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            56 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            57 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            58 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            59 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            60 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            61 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            62 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            63 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            64 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            65 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            66 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            67 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            68 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            69 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            70 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            71 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            72 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            73 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            74 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            75 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            76 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            77 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            78 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            79 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            80 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            81 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            82 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            83 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            84 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            85 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            86 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            87 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            88 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            89 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            90 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            91 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            92 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            93 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            94 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            95 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            96 => function ($stackPos) {
                 $this->semValue = new Name(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            97 => function ($stackPos) {
                 $this->semValue = new Expr\Variable(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            98 => function ($stackPos) {
                 /* nothing */
            },
            99 => function ($stackPos) {
                 /* nothing */
            },
            100 => function ($stackPos) {
                 /* nothing */
            },
            101 => function ($stackPos) {
                 $this->emitError(new Error('A trailing comma is not allowed here', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes));
            },
            102 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            103 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            104 => function ($stackPos) {
                 $this->semValue = new Node\Attribute($this->semStack[$stackPos-(1-1)], [], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            105 => function ($stackPos) {
                 $this->semValue = new Node\Attribute($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            106 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            107 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            108 => function ($stackPos) {
                 $this->semValue = new Node\AttributeGroup($this->semStack[$stackPos-(4-2)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            109 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            110 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            111 => function ($stackPos) {
                 $this->semValue = [];
            },
            112 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            113 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            114 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            115 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            116 => function ($stackPos) {
                 $this->semValue = new Stmt\HaltCompiler($this->lexer->handleHaltCompiler(), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            117 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_($this->semStack[$stackPos-(3-2)], null, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_SEMICOLON);
            $this->checkNamespace($this->semValue);
            },
            118 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_($this->semStack[$stackPos-(5-2)], $this->semStack[$stackPos-(5-4)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $this->checkNamespace($this->semValue);
            },
            119 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_(null, $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $this->checkNamespace($this->semValue);
            },
            120 => function ($stackPos) {
                 $this->semValue = new Stmt\Use_($this->semStack[$stackPos-(3-2)], Stmt\Use_::TYPE_NORMAL, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            121 => function ($stackPos) {
                 $this->semValue = new Stmt\Use_($this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-2)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            122 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            123 => function ($stackPos) {
                 $this->semValue = new Stmt\Const_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            124 => function ($stackPos) {
                 $this->semValue = Stmt\Use_::TYPE_FUNCTION;
            },
            125 => function ($stackPos) {
                 $this->semValue = Stmt\Use_::TYPE_CONSTANT;
            },
            126 => function ($stackPos) {
                 $this->semValue = new Stmt\GroupUse($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-6)], $this->semStack[$stackPos-(7-2)], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            127 => function ($stackPos) {
                 $this->semValue = new Stmt\GroupUse($this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-5)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            128 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            129 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            130 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            131 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            132 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            133 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            134 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            135 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            136 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            137 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(1-1));
            },
            138 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(3-3));
            },
            139 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(1-1));
            },
            140 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(3-3));
            },
            141 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)]; $this->semValue->type = Stmt\Use_::TYPE_NORMAL;
            },
            142 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)]; $this->semValue->type = $this->semStack[$stackPos-(2-1)];
            },
            143 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            144 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            145 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            146 => function ($stackPos) {
                 $this->semValue = new Node\Const_($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            147 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            148 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            149 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            150 => function ($stackPos) {
                 $this->semValue = new Node\Const_($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            151 => function ($stackPos) {
                 if (is_array($this->semStack[$stackPos-(2-2)])) { $this->semValue = array_merge($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)]); } else { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)]; };
            },
            152 => function ($stackPos) {
                 $this->semValue = array();
            },
            153 => function ($stackPos) {
                 $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop($this->createCommentNopAttributes($startAttributes['comments'])); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            154 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            155 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            156 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            157 => function ($stackPos) {
                 throw new Error('__HALT_COMPILER() can only be used from the outermost scope', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            158 => function ($stackPos) {

        if ($this->semStack[$stackPos-(3-2)]) {
            $this->semValue = $this->semStack[$stackPos-(3-2)]; $attrs = $this->startAttributeStack[$stackPos-(3-1)]; $stmts = $this->semValue; if (!empty($attrs['comments'])) {$stmts[0]->setAttribute('comments', array_merge($attrs['comments'], $stmts[0]->getAttribute('comments', []))); };
        } else {
            $startAttributes = $this->startAttributeStack[$stackPos-(3-1)]; if (isset($startAttributes['comments'])) { $this->semValue = new Stmt\Nop($startAttributes + $this->endAttributes); } else { $this->semValue = null; };
            if (null === $this->semValue) { $this->semValue = array(); }
        }

            },
            159 => function ($stackPos) {
                 $this->semValue = new Stmt\If_($this->semStack[$stackPos-(7-3)], ['stmts' => is_array($this->semStack[$stackPos-(7-5)]) ? $this->semStack[$stackPos-(7-5)] : array($this->semStack[$stackPos-(7-5)]), 'elseifs' => $this->semStack[$stackPos-(7-6)], 'else' => $this->semStack[$stackPos-(7-7)]], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            160 => function ($stackPos) {
                 $this->semValue = new Stmt\If_($this->semStack[$stackPos-(10-3)], ['stmts' => $this->semStack[$stackPos-(10-6)], 'elseifs' => $this->semStack[$stackPos-(10-7)], 'else' => $this->semStack[$stackPos-(10-8)]], $this->startAttributeStack[$stackPos-(10-1)] + $this->endAttributes);
            },
            161 => function ($stackPos) {
                 $this->semValue = new Stmt\While_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            162 => function ($stackPos) {
                 $this->semValue = new Stmt\Do_($this->semStack[$stackPos-(7-5)], is_array($this->semStack[$stackPos-(7-2)]) ? $this->semStack[$stackPos-(7-2)] : array($this->semStack[$stackPos-(7-2)]), $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            163 => function ($stackPos) {
                 $this->semValue = new Stmt\For_(['init' => $this->semStack[$stackPos-(9-3)], 'cond' => $this->semStack[$stackPos-(9-5)], 'loop' => $this->semStack[$stackPos-(9-7)], 'stmts' => $this->semStack[$stackPos-(9-9)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            164 => function ($stackPos) {
                 $this->semValue = new Stmt\Switch_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            165 => function ($stackPos) {
                 $this->semValue = new Stmt\Break_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            166 => function ($stackPos) {
                 $this->semValue = new Stmt\Continue_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            167 => function ($stackPos) {
                 $this->semValue = new Stmt\Return_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            168 => function ($stackPos) {
                 $this->semValue = new Stmt\Global_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            169 => function ($stackPos) {
                 $this->semValue = new Stmt\Static_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            170 => function ($stackPos) {
                 $this->semValue = new Stmt\Echo_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            171 => function ($stackPos) {
                 $this->semValue = new Stmt\InlineHTML($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            172 => function ($stackPos) {

        $e = $this->semStack[$stackPos-(2-1)];
        if ($e instanceof Expr\Throw_) {
            // For backwards-compatibility reasons, convert throw in statement position into
            // Stmt\Throw_ rather than Stmt\Expression(Expr\Throw_).
            $this->semValue = new Stmt\Throw_($e->expr, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
        } else {
            $this->semValue = new Stmt\Expression($e, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
        }

            },
            173 => function ($stackPos) {
                 $this->semValue = new Stmt\Unset_($this->semStack[$stackPos-(5-3)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            174 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-5)][0], ['keyVar' => null, 'byRef' => $this->semStack[$stackPos-(7-5)][1], 'stmts' => $this->semStack[$stackPos-(7-7)]], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            175 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(9-3)], $this->semStack[$stackPos-(9-7)][0], ['keyVar' => $this->semStack[$stackPos-(9-5)], 'byRef' => $this->semStack[$stackPos-(9-7)][1], 'stmts' => $this->semStack[$stackPos-(9-9)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            176 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(6-3)], new Expr\Error($this->startAttributeStack[$stackPos-(6-4)] + $this->endAttributeStack[$stackPos-(6-4)]), ['stmts' => $this->semStack[$stackPos-(6-6)]], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            177 => function ($stackPos) {
                 $this->semValue = new Stmt\Declare_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            178 => function ($stackPos) {
                 $this->semValue = new Stmt\TryCatch($this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-5)], $this->semStack[$stackPos-(6-6)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes); $this->checkTryCatch($this->semValue);
            },
            179 => function ($stackPos) {
                 $this->semValue = new Stmt\Goto_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            180 => function ($stackPos) {
                 $this->semValue = new Stmt\Label($this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            181 => function ($stackPos) {
                 $this->semValue = array(); /* means: no statement */
            },
            182 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            183 => function ($stackPos) {
                 $startAttributes = $this->startAttributeStack[$stackPos-(1-1)]; if (isset($startAttributes['comments'])) { $this->semValue = new Stmt\Nop($startAttributes + $this->endAttributes); } else { $this->semValue = null; };
            if ($this->semValue === null) $this->semValue = array(); /* means: no statement */
            },
            184 => function ($stackPos) {
                 $this->semValue = array();
            },
            185 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            186 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            187 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            188 => function ($stackPos) {
                 $this->semValue = new Stmt\Catch_($this->semStack[$stackPos-(8-3)], $this->semStack[$stackPos-(8-4)], $this->semStack[$stackPos-(8-7)], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            189 => function ($stackPos) {
                 $this->semValue = null;
            },
            190 => function ($stackPos) {
                 $this->semValue = new Stmt\Finally_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            191 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            192 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            193 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            194 => function ($stackPos) {
                 $this->semValue = false;
            },
            195 => function ($stackPos) {
                 $this->semValue = true;
            },
            196 => function ($stackPos) {
                 $this->semValue = false;
            },
            197 => function ($stackPos) {
                 $this->semValue = true;
            },
            198 => function ($stackPos) {
                 $this->semValue = false;
            },
            199 => function ($stackPos) {
                 $this->semValue = true;
            },
            200 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            201 => function ($stackPos) {
                 $this->semValue = [];
            },
            202 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            203 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            204 => function ($stackPos) {
                 $this->semValue = new Stmt\Function_($this->semStack[$stackPos-(8-3)], ['byRef' => $this->semStack[$stackPos-(8-2)], 'params' => $this->semStack[$stackPos-(8-5)], 'returnType' => $this->semStack[$stackPos-(8-7)], 'stmts' => $this->semStack[$stackPos-(8-8)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            205 => function ($stackPos) {
                 $this->semValue = new Stmt\Function_($this->semStack[$stackPos-(9-4)], ['byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-6)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => $this->semStack[$stackPos-(9-1)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            206 => function ($stackPos) {
                 $this->semValue = new Stmt\Class_($this->semStack[$stackPos-(7-2)], ['type' => $this->semStack[$stackPos-(7-1)], 'extends' => $this->semStack[$stackPos-(7-3)], 'implements' => $this->semStack[$stackPos-(7-4)], 'stmts' => $this->semStack[$stackPos-(7-6)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            $this->checkClass($this->semValue, $stackPos-(7-2));
            },
            207 => function ($stackPos) {
                 $this->semValue = new Stmt\Class_($this->semStack[$stackPos-(8-3)], ['type' => $this->semStack[$stackPos-(8-2)], 'extends' => $this->semStack[$stackPos-(8-4)], 'implements' => $this->semStack[$stackPos-(8-5)], 'stmts' => $this->semStack[$stackPos-(8-7)], 'attrGroups' => $this->semStack[$stackPos-(8-1)]], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            $this->checkClass($this->semValue, $stackPos-(8-3));
            },
            208 => function ($stackPos) {
                 $this->semValue = new Stmt\Interface_($this->semStack[$stackPos-(7-3)], ['extends' => $this->semStack[$stackPos-(7-4)], 'stmts' => $this->semStack[$stackPos-(7-6)], 'attrGroups' => $this->semStack[$stackPos-(7-1)]], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            $this->checkInterface($this->semValue, $stackPos-(7-3));
            },
            209 => function ($stackPos) {
                 $this->semValue = new Stmt\Trait_($this->semStack[$stackPos-(6-3)], ['stmts' => $this->semStack[$stackPos-(6-5)], 'attrGroups' => $this->semStack[$stackPos-(6-1)]], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            210 => function ($stackPos) {
                 $this->semValue = new Stmt\Enum_($this->semStack[$stackPos-(8-3)], ['scalarType' => $this->semStack[$stackPos-(8-4)], 'implements' => $this->semStack[$stackPos-(8-5)], 'stmts' => $this->semStack[$stackPos-(8-7)], 'attrGroups' => $this->semStack[$stackPos-(8-1)]], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            $this->checkEnum($this->semValue, $stackPos-(8-3));
            },
            211 => function ($stackPos) {
                 $this->semValue = null;
            },
            212 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            213 => function ($stackPos) {
                 $this->semValue = null;
            },
            214 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            215 => function ($stackPos) {
                 $this->semValue = 0;
            },
            216 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            217 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            218 => function ($stackPos) {
                 $this->checkClassModifier($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $this->semValue = $this->semStack[$stackPos-(2-1)] | $this->semStack[$stackPos-(2-2)];
            },
            219 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_ABSTRACT;
            },
            220 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_FINAL;
            },
            221 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_READONLY;
            },
            222 => function ($stackPos) {
                 $this->semValue = null;
            },
            223 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            224 => function ($stackPos) {
                 $this->semValue = array();
            },
            225 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            226 => function ($stackPos) {
                 $this->semValue = array();
            },
            227 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            228 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            229 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            230 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            231 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            232 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            233 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            234 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            235 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            236 => function ($stackPos) {
                 $this->semValue = null;
            },
            237 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            238 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            239 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            240 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            241 => function ($stackPos) {
                 $this->semValue = new Stmt\DeclareDeclare($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            242 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            243 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-3)];
            },
            244 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            245 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(5-3)];
            },
            246 => function ($stackPos) {
                 $this->semValue = array();
            },
            247 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            248 => function ($stackPos) {
                 $this->semValue = new Stmt\Case_($this->semStack[$stackPos-(4-2)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            249 => function ($stackPos) {
                 $this->semValue = new Stmt\Case_(null, $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            250 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            251 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            252 => function ($stackPos) {
                 $this->semValue = new Expr\Match_($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-6)], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            253 => function ($stackPos) {
                 $this->semValue = [];
            },
            254 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            255 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            256 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            257 => function ($stackPos) {
                 $this->semValue = new Node\MatchArm($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            258 => function ($stackPos) {
                 $this->semValue = new Node\MatchArm(null, $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            259 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            260 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            261 => function ($stackPos) {
                 $this->semValue = array();
            },
            262 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            263 => function ($stackPos) {
                 $this->semValue = new Stmt\ElseIf_($this->semStack[$stackPos-(5-3)], is_array($this->semStack[$stackPos-(5-5)]) ? $this->semStack[$stackPos-(5-5)] : array($this->semStack[$stackPos-(5-5)]), $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            264 => function ($stackPos) {
                 $this->semValue = array();
            },
            265 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            266 => function ($stackPos) {
                 $this->semValue = new Stmt\ElseIf_($this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-6)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            267 => function ($stackPos) {
                 $this->semValue = null;
            },
            268 => function ($stackPos) {
                 $this->semValue = new Stmt\Else_(is_array($this->semStack[$stackPos-(2-2)]) ? $this->semStack[$stackPos-(2-2)] : array($this->semStack[$stackPos-(2-2)]), $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            269 => function ($stackPos) {
                 $this->semValue = null;
            },
            270 => function ($stackPos) {
                 $this->semValue = new Stmt\Else_($this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            271 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)], false);
            },
            272 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(2-2)], true);
            },
            273 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)], false);
            },
            274 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)], false);
            },
            275 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            276 => function ($stackPos) {
                 $this->semValue = array();
            },
            277 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            278 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            279 => function ($stackPos) {
                 $this->semValue = 0;
            },
            280 => function ($stackPos) {
                 $this->checkModifier($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $this->semValue = $this->semStack[$stackPos-(2-1)] | $this->semStack[$stackPos-(2-2)];
            },
            281 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PUBLIC;
            },
            282 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PROTECTED;
            },
            283 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PRIVATE;
            },
            284 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_READONLY;
            },
            285 => function ($stackPos) {
                 $this->semValue = new Node\Param($this->semStack[$stackPos-(6-6)], null, $this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-4)], $this->semStack[$stackPos-(6-5)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes, $this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-1)]);
            $this->checkParam($this->semValue);
            },
            286 => function ($stackPos) {
                 $this->semValue = new Node\Param($this->semStack[$stackPos-(8-6)], $this->semStack[$stackPos-(8-8)], $this->semStack[$stackPos-(8-3)], $this->semStack[$stackPos-(8-4)], $this->semStack[$stackPos-(8-5)], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes, $this->semStack[$stackPos-(8-2)], $this->semStack[$stackPos-(8-1)]);
            $this->checkParam($this->semValue);
            },
            287 => function ($stackPos) {
                 $this->semValue = new Node\Param(new Expr\Error($this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes), null, $this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-4)], $this->semStack[$stackPos-(6-5)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes, $this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-1)]);
            },
            288 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            289 => function ($stackPos) {
                 $this->semValue = new Node\NullableType($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            290 => function ($stackPos) {
                 $this->semValue = new Node\UnionType($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            291 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            292 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            293 => function ($stackPos) {
                 $this->semValue = new Node\Name('static', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            294 => function ($stackPos) {
                 $this->semValue = $this->handleBuiltinTypes($this->semStack[$stackPos-(1-1)]);
            },
            295 => function ($stackPos) {
                 $this->semValue = new Node\Identifier('array', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            296 => function ($stackPos) {
                 $this->semValue = new Node\Identifier('callable', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            297 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            298 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            299 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            300 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            301 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            302 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            303 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            304 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            305 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            306 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            307 => function ($stackPos) {
                 $this->semValue = new Node\IntersectionType($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            308 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            309 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            310 => function ($stackPos) {
                 $this->semValue = new Node\IntersectionType($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            311 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            312 => function ($stackPos) {
                 $this->semValue = new Node\NullableType($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            313 => function ($stackPos) {
                 $this->semValue = new Node\UnionType($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            314 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            315 => function ($stackPos) {
                 $this->semValue = null;
            },
            316 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            317 => function ($stackPos) {
                 $this->semValue = null;
            },
            318 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            319 => function ($stackPos) {
                 $this->semValue = null;
            },
            320 => function ($stackPos) {
                 $this->semValue = array();
            },
            321 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            322 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-2)]);
            },
            323 => function ($stackPos) {
                 $this->semValue = new Node\VariadicPlaceholder($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            324 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            325 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            326 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(1-1)], false, false, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            327 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(2-2)], true, false, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            328 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(2-2)], false, true, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            329 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(3-3)], false, false, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes, $this->semStack[$stackPos-(3-1)]);
            },
            330 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            331 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            332 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            333 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            334 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            335 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            336 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            337 => function ($stackPos) {
                 $this->semValue = new Stmt\StaticVar($this->semStack[$stackPos-(1-1)], null, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            338 => function ($stackPos) {
                 $this->semValue = new Stmt\StaticVar($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            339 => function ($stackPos) {
                 if ($this->semStack[$stackPos-(2-2)] !== null) { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)]; }
            },
            340 => function ($stackPos) {
                 $this->semValue = array();
            },
            341 => function ($stackPos) {
                 $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop($this->createCommentNopAttributes($startAttributes['comments'])); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            342 => function ($stackPos) {
                 $this->semValue = new Stmt\Property($this->semStack[$stackPos-(5-2)], $this->semStack[$stackPos-(5-4)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes, $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-1)]);
            $this->checkProperty($this->semValue, $stackPos-(5-2));
            },
            343 => function ($stackPos) {
                 $this->semValue = new Stmt\ClassConst($this->semStack[$stackPos-(5-4)], $this->semStack[$stackPos-(5-2)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes, $this->semStack[$stackPos-(5-1)]);
            $this->checkClassConst($this->semValue, $stackPos-(5-2));
            },
            344 => function ($stackPos) {
                 $this->semValue = new Stmt\ClassMethod($this->semStack[$stackPos-(10-5)], ['type' => $this->semStack[$stackPos-(10-2)], 'byRef' => $this->semStack[$stackPos-(10-4)], 'params' => $this->semStack[$stackPos-(10-7)], 'returnType' => $this->semStack[$stackPos-(10-9)], 'stmts' => $this->semStack[$stackPos-(10-10)], 'attrGroups' => $this->semStack[$stackPos-(10-1)]], $this->startAttributeStack[$stackPos-(10-1)] + $this->endAttributes);
            $this->checkClassMethod($this->semValue, $stackPos-(10-2));
            },
            345 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUse($this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            346 => function ($stackPos) {
                 $this->semValue = new Stmt\EnumCase($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-4)], $this->semStack[$stackPos-(5-1)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            347 => function ($stackPos) {
                 $this->semValue = null; /* will be skipped */
            },
            348 => function ($stackPos) {
                 $this->semValue = array();
            },
            349 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            350 => function ($stackPos) {
                 $this->semValue = array();
            },
            351 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            352 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Precedence($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            353 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(5-1)][0], $this->semStack[$stackPos-(5-1)][1], $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-4)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            354 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], $this->semStack[$stackPos-(4-3)], null, $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            355 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], null, $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            356 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], null, $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            357 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            358 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            359 => function ($stackPos) {
                 $this->semValue = array(null, $this->semStack[$stackPos-(1-1)]);
            },
            360 => function ($stackPos) {
                 $this->semValue = null;
            },
            361 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            362 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            363 => function ($stackPos) {
                 $this->semValue = 0;
            },
            364 => function ($stackPos) {
                 $this->semValue = 0;
            },
            365 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            366 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            367 => function ($stackPos) {
                 $this->checkModifier($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $this->semValue = $this->semStack[$stackPos-(2-1)] | $this->semStack[$stackPos-(2-2)];
            },
            368 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PUBLIC;
            },
            369 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PROTECTED;
            },
            370 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PRIVATE;
            },
            371 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_STATIC;
            },
            372 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_ABSTRACT;
            },
            373 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_FINAL;
            },
            374 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_READONLY;
            },
            375 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            376 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            377 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            378 => function ($stackPos) {
                 $this->semValue = new Node\VarLikeIdentifier(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            379 => function ($stackPos) {
                 $this->semValue = new Stmt\PropertyProperty($this->semStack[$stackPos-(1-1)], null, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            380 => function ($stackPos) {
                 $this->semValue = new Stmt\PropertyProperty($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            381 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            382 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            383 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            384 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            385 => function ($stackPos) {
                 $this->semValue = array();
            },
            386 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            387 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            388 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            389 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            390 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            391 => function ($stackPos) {
                 $this->semValue = new Expr\AssignRef($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            392 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            393 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            394 => function ($stackPos) {
                 $this->semValue = new Expr\Clone_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            395 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Plus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            396 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Minus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            397 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Mul($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            398 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Div($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            399 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Concat($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            400 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Mod($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            401 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            402 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            403 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            404 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\ShiftLeft($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            405 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\ShiftRight($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            406 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Pow($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            407 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Coalesce($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            408 => function ($stackPos) {
                 $this->semValue = new Expr\PostInc($this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            409 => function ($stackPos) {
                 $this->semValue = new Expr\PreInc($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            410 => function ($stackPos) {
                 $this->semValue = new Expr\PostDec($this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            411 => function ($stackPos) {
                 $this->semValue = new Expr\PreDec($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            412 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BooleanOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            413 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BooleanAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            414 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            415 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            416 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            417 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            418 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            419 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            420 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            421 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Concat($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            422 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Plus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            423 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Minus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            424 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Mul($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            425 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Div($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            426 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Mod($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            427 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\ShiftLeft($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            428 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\ShiftRight($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            429 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Pow($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            430 => function ($stackPos) {
                 $this->semValue = new Expr\UnaryPlus($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            431 => function ($stackPos) {
                 $this->semValue = new Expr\UnaryMinus($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            432 => function ($stackPos) {
                 $this->semValue = new Expr\BooleanNot($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            433 => function ($stackPos) {
                 $this->semValue = new Expr\BitwiseNot($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            434 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Identical($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            435 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\NotIdentical($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            436 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Equal($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            437 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\NotEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            438 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Spaceship($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            439 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Smaller($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            440 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\SmallerOrEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            441 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Greater($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            442 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\GreaterOrEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            443 => function ($stackPos) {
                 $this->semValue = new Expr\Instanceof_($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            444 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            445 => function ($stackPos) {
                 $this->semValue = new Expr\Ternary($this->semStack[$stackPos-(5-1)], $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            446 => function ($stackPos) {
                 $this->semValue = new Expr\Ternary($this->semStack[$stackPos-(4-1)], null, $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            447 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Coalesce($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            448 => function ($stackPos) {
                 $this->semValue = new Expr\Isset_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            449 => function ($stackPos) {
                 $this->semValue = new Expr\Empty_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            450 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            451 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE_ONCE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            452 => function ($stackPos) {
                 $this->semValue = new Expr\Eval_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            453 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            454 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE_ONCE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            455 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Int_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            456 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes;
            $attrs['kind'] = $this->getFloatCastKind($this->semStack[$stackPos-(2-1)]);
            $this->semValue = new Expr\Cast\Double($this->semStack[$stackPos-(2-2)], $attrs);
            },
            457 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\String_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            458 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Array_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            459 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Object_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            460 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Bool_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            461 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Unset_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            462 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes;
            $attrs['kind'] = strtolower($this->semStack[$stackPos-(2-1)]) === 'exit' ? Expr\Exit_::KIND_EXIT : Expr\Exit_::KIND_DIE;
            $this->semValue = new Expr\Exit_($this->semStack[$stackPos-(2-2)], $attrs);
            },
            463 => function ($stackPos) {
                 $this->semValue = new Expr\ErrorSuppress($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            464 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            465 => function ($stackPos) {
                 $this->semValue = new Expr\ShellExec($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            466 => function ($stackPos) {
                 $this->semValue = new Expr\Print_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            467 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_(null, null, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            468 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_($this->semStack[$stackPos-(2-2)], null, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            469 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_($this->semStack[$stackPos-(4-4)], $this->semStack[$stackPos-(4-2)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            470 => function ($stackPos) {
                 $this->semValue = new Expr\YieldFrom($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            471 => function ($stackPos) {
                 $this->semValue = new Expr\Throw_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            472 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => false, 'byRef' => $this->semStack[$stackPos-(8-2)], 'params' => $this->semStack[$stackPos-(8-4)], 'returnType' => $this->semStack[$stackPos-(8-6)], 'expr' => $this->semStack[$stackPos-(8-8)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            473 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => true, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'returnType' => $this->semStack[$stackPos-(9-7)], 'expr' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            474 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => false, 'byRef' => $this->semStack[$stackPos-(8-2)], 'params' => $this->semStack[$stackPos-(8-4)], 'uses' => $this->semStack[$stackPos-(8-6)], 'returnType' => $this->semStack[$stackPos-(8-7)], 'stmts' => $this->semStack[$stackPos-(8-8)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            475 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => true, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'uses' => $this->semStack[$stackPos-(9-7)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => []], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            476 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => false, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'returnType' => $this->semStack[$stackPos-(9-7)], 'expr' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => $this->semStack[$stackPos-(9-1)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            477 => function ($stackPos) {
                 $this->semValue = new Expr\ArrowFunction(['static' => true, 'byRef' => $this->semStack[$stackPos-(10-4)], 'params' => $this->semStack[$stackPos-(10-6)], 'returnType' => $this->semStack[$stackPos-(10-8)], 'expr' => $this->semStack[$stackPos-(10-10)], 'attrGroups' => $this->semStack[$stackPos-(10-1)]], $this->startAttributeStack[$stackPos-(10-1)] + $this->endAttributes);
            },
            478 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => false, 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-5)], 'uses' => $this->semStack[$stackPos-(9-7)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)], 'attrGroups' => $this->semStack[$stackPos-(9-1)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            479 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => true, 'byRef' => $this->semStack[$stackPos-(10-4)], 'params' => $this->semStack[$stackPos-(10-6)], 'uses' => $this->semStack[$stackPos-(10-8)], 'returnType' => $this->semStack[$stackPos-(10-9)], 'stmts' => $this->semStack[$stackPos-(10-10)], 'attrGroups' => $this->semStack[$stackPos-(10-1)]], $this->startAttributeStack[$stackPos-(10-1)] + $this->endAttributes);
            },
            480 => function ($stackPos) {
                 $this->semValue = array(new Stmt\Class_(null, ['type' => 0, 'extends' => $this->semStack[$stackPos-(8-4)], 'implements' => $this->semStack[$stackPos-(8-5)], 'stmts' => $this->semStack[$stackPos-(8-7)], 'attrGroups' => $this->semStack[$stackPos-(8-1)]], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes), $this->semStack[$stackPos-(8-3)]);
            $this->checkClass($this->semValue[0], -1);
            },
            481 => function ($stackPos) {
                 $this->semValue = new Expr\New_($this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            482 => function ($stackPos) {
                 list($class, $ctorArgs) = $this->semStack[$stackPos-(2-2)]; $this->semValue = new Expr\New_($class, $ctorArgs, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            483 => function ($stackPos) {
                 $this->semValue = array();
            },
            484 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-3)];
            },
            485 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            486 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            487 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            488 => function ($stackPos) {
                 $this->semValue = new Expr\ClosureUse($this->semStack[$stackPos-(2-2)], $this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            489 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            490 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            491 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            492 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            493 => function ($stackPos) {
                 $this->semValue = new Expr\StaticCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            494 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            495 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            496 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            497 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            498 => function ($stackPos) {
                 $this->semValue = new Name\FullyQualified(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            499 => function ($stackPos) {
                 $this->semValue = new Name\Relative(substr($this->semStack[$stackPos-(1-1)], 10), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            500 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            501 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            502 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            503 => function ($stackPos) {
                 $this->semValue = new Expr\Error($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->errorState = 2;
            },
            504 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            505 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            506 => function ($stackPos) {
                 $this->semValue = null;
            },
            507 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            508 => function ($stackPos) {
                 $this->semValue = array();
            },
            509 => function ($stackPos) {
                 $this->semValue = array(new Scalar\EncapsedStringPart(Scalar\String_::parseEscapeSequences($this->semStack[$stackPos-(1-1)], '`'), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes));
            },
            510 => function ($stackPos) {
                 foreach ($this->semStack[$stackPos-(1-1)] as $s) { if ($s instanceof Node\Scalar\EncapsedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '`', true); } }; $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            511 => function ($stackPos) {
                 $this->semValue = array();
            },
            512 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            513 => function ($stackPos) {
                 $this->semValue = new Expr\ConstFetch($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            514 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Line($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            515 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\File($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            516 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Dir($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            517 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Class_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            518 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Trait_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            519 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Method($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            520 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Function_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            521 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Namespace_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            522 => function ($stackPos) {
                 $this->semValue = new Expr\ClassConstFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            523 => function ($stackPos) {
                 $this->semValue = new Expr\ClassConstFetch($this->semStack[$stackPos-(3-1)], new Expr\Error($this->startAttributeStack[$stackPos-(3-3)] + $this->endAttributeStack[$stackPos-(3-3)]), $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes); $this->errorState = 2;
            },
            524 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes; $attrs['kind'] = Expr\Array_::KIND_SHORT;
            $this->semValue = new Expr\Array_($this->semStack[$stackPos-(3-2)], $attrs);
            },
            525 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes; $attrs['kind'] = Expr\Array_::KIND_LONG;
            $this->semValue = new Expr\Array_($this->semStack[$stackPos-(4-3)], $attrs);
            },
            526 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            527 => function ($stackPos) {
                 $this->semValue = Scalar\String_::fromString($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            528 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes; $attrs['kind'] = Scalar\String_::KIND_DOUBLE_QUOTED;
            foreach ($this->semStack[$stackPos-(3-2)] as $s) { if ($s instanceof Node\Scalar\EncapsedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '"', true); } }; $this->semValue = new Scalar\Encapsed($this->semStack[$stackPos-(3-2)], $attrs);
            },
            529 => function ($stackPos) {
                 $this->semValue = $this->parseLNumber($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            530 => function ($stackPos) {
                 $this->semValue = Scalar\DNumber::fromString($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            531 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            532 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            533 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            534 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes, $this->startAttributeStack[$stackPos-(3-3)] + $this->endAttributeStack[$stackPos-(3-3)], true);
            },
            535 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(2-1)], '', $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes, $this->startAttributeStack[$stackPos-(2-2)] + $this->endAttributeStack[$stackPos-(2-2)], true);
            },
            536 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes, $this->startAttributeStack[$stackPos-(3-3)] + $this->endAttributeStack[$stackPos-(3-3)], true);
            },
            537 => function ($stackPos) {
                 $this->semValue = null;
            },
            538 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            539 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            540 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            541 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            542 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            543 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            544 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            545 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            546 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            547 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            548 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            549 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            550 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            551 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            552 => function ($stackPos) {
                 $this->semValue = new Expr\MethodCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            553 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafeMethodCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            554 => function ($stackPos) {
                 $this->semValue = null;
            },
            555 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            556 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            557 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            558 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            559 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafePropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            560 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            561 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            562 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            563 => function ($stackPos) {
                 $this->semValue = new Expr\Variable(new Expr\Error($this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes), $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes); $this->errorState = 2;
            },
            564 => function ($stackPos) {
                 $var = $this->semStack[$stackPos-(1-1)]->name; $this->semValue = \is_string($var) ? new Node\VarLikeIdentifier($var, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes) : $var;
            },
            565 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            566 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            567 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            568 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            569 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            570 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafePropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            571 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            572 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            573 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            574 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            575 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            576 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            577 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            578 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            579 => function ($stackPos) {
                 $this->semValue = new Expr\Error($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->errorState = 2;
            },
            580 => function ($stackPos) {
                 $this->semValue = new Expr\List_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            581 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)]; $end = count($this->semValue)-1; if ($this->semValue[$end] === null) array_pop($this->semValue);
            },
            582 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            583 => function ($stackPos) {
                 /* do nothing -- prevent default action of $$=$this->semStack[$1]. See $551. */
            },
            584 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            585 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            586 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(1-1)], null, false, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            587 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(2-2)], null, true, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            588 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(1-1)], null, false, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            589 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(3-3)], $this->semStack[$stackPos-(3-1)], false, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            590 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(4-4)], $this->semStack[$stackPos-(4-1)], true, $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            591 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(3-3)], $this->semStack[$stackPos-(3-1)], false, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            592 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(2-2)], null, false, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes, true, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            593 => function ($stackPos) {
                 $this->semValue = null;
            },
            594 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            595 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            596 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            597 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)]);
            },
            598 => function ($stackPos) {
                 $this->semValue = new Scalar\EncapsedStringPart($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            599 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            600 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            601 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            602 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            603 => function ($stackPos) {
                 $this->semValue = new Expr\NullsafePropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            604 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            605 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            606 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-4)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            607 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            608 => function ($stackPos) {
                 $this->semValue = new Scalar\String_($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            609 => function ($stackPos) {
                 $this->semValue = $this->parseNumString($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            610 => function ($stackPos) {
                 $this->semValue = $this->parseNumString('-' . $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            611 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
        ];
    }
}
