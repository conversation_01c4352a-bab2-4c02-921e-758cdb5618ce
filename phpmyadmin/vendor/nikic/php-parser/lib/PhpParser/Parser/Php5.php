<?php

namespace Php<PERSON>ars<PERSON>\Parser;

use <PERSON>p<PERSON><PERSON><PERSON>\Error;
use Php<PERSON><PERSON><PERSON>\Node;
use Php<PERSON>arser\Node\Expr;
use Php<PERSON>arser\Node\Name;
use PhpParser\Node\Scalar;
use Php<PERSON>arser\Node\Stmt;

/* This is an automatically GENERATED file, which should not be manually edited.
 * Instead edit one of the following:
 *  * the grammar files grammar/php5.y or grammar/php7.y
 *  * the skeleton file grammar/parser.template
 *  * the preprocessing script grammar/rebuildParsers.php
 */
class Php5 extends \PhpParser\ParserAbstract
{
    protected $tokenToSymbolMapSize = 396;
    protected $actionTableSize = 1099;
    protected $gotoTableSize = 640;

    protected $invalidSymbol = 168;
    protected $errorSymbol = 1;
    protected $defaultAction = -32766;
    protected $unexpectedTokenRule = 32767;

    protected $YY2TBLSTATE = 415;
    protected $numNonLeafStates = 663;

    protected $symbolToName = array(
        "EOF",
        "error",
        "T_THROW",
        "T_INCLUDE",
        "T_INCLUDE_ONCE",
        "T_EVAL",
        "T_REQUIRE",
        "T_REQUIRE_ONCE",
        "','",
        "T_LOGICAL_OR",
        "T_LOGICAL_XOR",
        "T_LOGICAL_AND",
        "T_PRINT",
        "T_YIELD",
        "T_DOUBLE_ARROW",
        "T_YIELD_FROM",
        "'='",
        "T_PLUS_EQUAL",
        "T_MINUS_EQUAL",
        "T_MUL_EQUAL",
        "T_DIV_EQUAL",
        "T_CONCAT_EQUAL",
        "T_MOD_EQUAL",
        "T_AND_EQUAL",
        "T_OR_EQUAL",
        "T_XOR_EQUAL",
        "T_SL_EQUAL",
        "T_SR_EQUAL",
        "T_POW_EQUAL",
        "T_COALESCE_EQUAL",
        "'?'",
        "':'",
        "T_COALESCE",
        "T_BOOLEAN_OR",
        "T_BOOLEAN_AND",
        "'|'",
        "'^'",
        "T_AMPERSAND_NOT_FOLLOWED_BY_VAR_OR_VARARG",
        "T_AMPERSAND_FOLLOWED_BY_VAR_OR_VARARG",
        "T_IS_EQUAL",
        "T_IS_NOT_EQUAL",
        "T_IS_IDENTICAL",
        "T_IS_NOT_IDENTICAL",
        "T_SPACESHIP",
        "'<'",
        "T_IS_SMALLER_OR_EQUAL",
        "'>'",
        "T_IS_GREATER_OR_EQUAL",
        "T_SL",
        "T_SR",
        "'+'",
        "'-'",
        "'.'",
        "'*'",
        "'/'",
        "'%'",
        "'!'",
        "T_INSTANCEOF",
        "'~'",
        "T_INC",
        "T_DEC",
        "T_INT_CAST",
        "T_DOUBLE_CAST",
        "T_STRING_CAST",
        "T_ARRAY_CAST",
        "T_OBJECT_CAST",
        "T_BOOL_CAST",
        "T_UNSET_CAST",
        "'@'",
        "T_POW",
        "'['",
        "T_NEW",
        "T_CLONE",
        "T_EXIT",
        "T_IF",
        "T_ELSEIF",
        "T_ELSE",
        "T_ENDIF",
        "T_LNUMBER",
        "T_DNUMBER",
        "T_STRING",
        "T_STRING_VARNAME",
        "T_VARIABLE",
        "T_NUM_STRING",
        "T_INLINE_HTML",
        "T_ENCAPSED_AND_WHITESPACE",
        "T_CONSTANT_ENCAPSED_STRING",
        "T_ECHO",
        "T_DO",
        "T_WHILE",
        "T_ENDWHILE",
        "T_FOR",
        "T_ENDFOR",
        "T_FOREACH",
        "T_ENDFOREACH",
        "T_DECLARE",
        "T_ENDDECLARE",
        "T_AS",
        "T_SWITCH",
        "T_MATCH",
        "T_ENDSWITCH",
        "T_CASE",
        "T_DEFAULT",
        "T_BREAK",
        "T_CONTINUE",
        "T_GOTO",
        "T_FUNCTION",
        "T_FN",
        "T_CONST",
        "T_RETURN",
        "T_TRY",
        "T_CATCH",
        "T_FINALLY",
        "T_USE",
        "T_INSTEADOF",
        "T_GLOBAL",
        "T_STATIC",
        "T_ABSTRACT",
        "T_FINAL",
        "T_PRIVATE",
        "T_PROTECTED",
        "T_PUBLIC",
        "T_READONLY",
        "T_VAR",
        "T_UNSET",
        "T_ISSET",
        "T_EMPTY",
        "T_HALT_COMPILER",
        "T_CLASS",
        "T_TRAIT",
        "T_INTERFACE",
        "T_EXTENDS",
        "T_IMPLEMENTS",
        "T_OBJECT_OPERATOR",
        "T_LIST",
        "T_ARRAY",
        "T_CALLABLE",
        "T_CLASS_C",
        "T_TRAIT_C",
        "T_METHOD_C",
        "T_FUNC_C",
        "T_LINE",
        "T_FILE",
        "T_START_HEREDOC",
        "T_END_HEREDOC",
        "T_DOLLAR_OPEN_CURLY_BRACES",
        "T_CURLY_OPEN",
        "T_PAAMAYIM_NEKUDOTAYIM",
        "T_NAMESPACE",
        "T_NS_C",
        "T_DIR",
        "T_NS_SEPARATOR",
        "T_ELLIPSIS",
        "T_NAME_FULLY_QUALIFIED",
        "T_NAME_QUALIFIED",
        "T_NAME_RELATIVE",
        "';'",
        "'{'",
        "'}'",
        "'('",
        "')'",
        "'$'",
        "'`'",
        "']'",
        "'\"'",
        "T_ENUM",
        "T_NULLSAFE_OBJECT_OPERATOR",
        "T_ATTRIBUTE"
    );

    protected $tokenToSymbol = array(
            0,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,   56,  164,  168,  161,   55,  168,  168,
          159,  160,   53,   50,    8,   51,   52,   54,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,   31,  156,
           44,   16,   46,   30,   68,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,   70,  168,  163,   36,  168,  162,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  157,   35,  158,   58,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,    1,    2,    3,    4,
            5,    6,    7,    9,   10,   11,   12,   13,   14,   15,
           17,   18,   19,   20,   21,   22,   23,   24,   25,   26,
           27,   28,   29,   32,   33,   34,   37,   38,   39,   40,
           41,   42,   43,   45,   47,   48,   49,   57,   59,   60,
           61,   62,   63,   64,   65,   66,   67,   69,   71,   72,
           73,   74,   75,   76,   77,   78,   79,   80,   81,   82,
           83,   84,   85,   86,   87,   88,   89,   90,   91,   92,
           93,   94,   95,   96,   97,   98,   99,  100,  101,  102,
          103,  104,  105,  106,  107,  108,  109,  110,  111,  112,
          113,  114,  115,  116,  117,  118,  119,  120,  121,  122,
          123,  124,  125,  126,  127,  128,  129,  130,  165,  131,
          132,  133,  166,  134,  135,  136,  137,  138,  139,  140,
          141,  142,  143,  144,  145,  146,  147,  148,  149,  150,
          151,  152,  153,  154,  155,  167
    );

    protected $action = array(
          700,  670,  671,  672,  673,  674,  286,  675,  676,  677,
          713,  714,  223,  224,  225,  226,  227,  228,  229,  230,
          231,  232,    0,  233,  234,  235,  236,  237,  238,  239,
          240,  241,  242,  243,  244,-32766,-32766,-32766,-32766,-32766,
        -32766,-32766,-32766,-32766,-32767,-32767,-32767,-32767,  245,  246,
          242,  243,  244,-32766,-32766,  678,-32766,-32766,-32766,-32766,
        -32766,-32766,-32766,-32766,-32766, 1229,  245,  246, 1230,  679,
          680,  681,  682,  683,  684,  685,  899,  900,  747,-32766,
        -32766,-32766,-32766,-32766,-32766,  686,  687,  688,  689,  690,
          691,  692,  693,  694,  695,  696,  716,  739,  717,  718,
          719,  720,  708,  709,  710,  738,  711,  712,  697,  698,
          699,  701,  702,  703,  741,  742,  743,  744,  745,  746,
          875,  704,  705,  706,  707,  737,  728,  726,  727,  723,
          724, 1046,  715,  721,  722,  729,  730,  732,  731,  733,
          734,   55,   56,  425,   57,   58,  725,  736,  735,  755,
           59,   60, -226,   61,-32766,-32766,-32766,-32766,-32766,-32766,
        -32766,-32766,-32766,-32766,  337,-32767,-32767,-32767,-32767,   29,
          107,  108,  109,  110,  111,  112,  113,  114,  115,  116,
          117,  118,  119,  620,-32766,-32766,-32766,-32766,   62,   63,
         1046,-32766,-32766,-32766,   64,  419,   65,  294,  295,   66,
           67,   68,   69,   70,   71,   72,   73,  823,   25,  302,
           74,  418,  984,  986,  669,  668, 1100, 1101, 1078,  755,
          755,  767, 1220,  768,  470,-32766,-32766,-32766,  341,  749,
          824,   54,-32767,-32767,-32767,-32767,   98,   99,  100,  101,
          102,  220,  221,  222,  362,  876,-32766,   27,-32766,-32766,
        -32766,-32766,-32766, 1046,  493,  126, 1080, 1079, 1081,  370,
         1068,  930,  207,  478,  479,  952,  953,  954,  951,  950,
          949,  128,  480,  481,  803, 1106, 1107, 1108, 1109, 1103,
         1104,  319,   32,  297,   10,  211, -515, 1110, 1105,  669,
          668, 1080, 1079, 1081,  220,  221,  222,   41,  364,  341,
          334,  421,  336,  426, -128, -128, -128,  313, 1046,  469,
           -4,  824,   54,  812,  770,  207,   40,   21,  427, -128,
          471, -128,  472, -128,  473, -128, 1046,  428,  220,  221,
          222,-32766,   33,   34,  429,  361,  327,   52,   35,  474,
        -32766,-32766,-32766,  342,  357,  358,  475,  476,   48,  207,
          249,  669,  668,  477,  443,  300,  795,  846,  430,  431,
           28,-32766,  814,-32766,-32766,-32766,-32766,-32766,-32766,-32766,
        -32767,-32767,-32767,-32767,-32767,  952,  953,  954,  951,  950,
          949,  422,  755,  424,  426,  826,  634, -128,-32766,-32766,
          469,  824,   54,  288,  812, 1151,  755,   40,   21,  427,
          317,  471,  345,  472,  129,  473,    9, 1186,  428,  769,
          360,  324,  905,   33,   34,  429,  361, 1046,  415,   35,
          474,  944, 1068,  315,  125,  357,  358,  475,  476,-32766,
        -32766,-32766,  926,  302,  477,  121, 1068,  759,  846,  430,
          431,  669,  668,  423,  755, 1152,  809, 1046,  480,  766,
        -32766,  805,-32766,-32766,-32766,-32766, -261,  127,  347,  436,
          841,  341, 1078, 1200,  426,  446,  826,  634,   -4,  807,
          469,  824,   54,  436,  812,  341,  755,   40,   21,  427,
          444,  471,  130,  472, 1068,  473,  346,  767,  428,  768,
         -211, -211, -211,   33,   34,  429,  361,  308, 1076,   35,
          474,-32766,-32766,-32766, 1046,  357,  358,  475,  476,-32766,
        -32766,-32766,  906,  120,  477,  539, 1068,  795,  846,  430,
          431,  436,-32766,  341,-32766,-32766,-32766, 1046,  480,  810,
        -32766,  925,-32766,-32766,  754, 1080, 1079, 1081,   49,-32766,
        -32766,-32766,  749,  751,  426, 1201,  826,  634, -211,   30,
          469,  669,  668,  436,  812,  341,   75,   40,   21,  427,
        -32766,  471, 1064,  472,  124,  473,  669,  668,  428,  212,
         -210, -210, -210,   33,   34,  429,  361,   51, 1186,   35,
          474,  755,-32766,-32766,-32766,  357,  358,  475,  476,  213,
          824,   54,  221,  222,  477,   20,  581,  795,  846,  430,
          431,  220,  221,  222,  755,  222,  247,   78,   79,   80,
           81,  341,  207,  517,  103,  104,  105,  752,  307,  131,
          637, 1068,  207,  341,  207,  122,  826,  634, -210,   36,
          106,   82,   83,   84,   85,   86,   87,   88,   89,   90,
           91,   92,   93,   94,   95,   96,   97,   98,   99,  100,
          101,  102,  103,  104,  105, 1112,  307,  346,  436,  214,
          341,  824,   54,  426,  123,  250,  129,  134,  106,  469,
        -32766,  572, 1112,  812,  245,  246,   40,   21,  427,  251,
          471,  252,  472,  341,  473,  453,   22,  428,  207,  899,
          900,  638,   33,   34,  429,  824,   54,  -86,   35,  474,
          220,  221,  222,  314,  357,  358,  100,  101,  102,  239,
          240,  241,  645,  477, -230,  458,  589,  135,  374,  596,
          597,  207,  760,  640,  648,  642,  941,  654,  929,  662,
          822,  133,  307,  837,  426,-32766,  106,  749,   43,   44,
          469,   45,  442,   46,  812,  826,  634,   40,   21,  427,
           47,  471,   50,  472,   53,  473,  132,  608,  428,  302,
          604, -280,-32766,   33,   34,  429,  824,   54,  426,   35,
          474,  755,  957,  -84,  469,  357,  358,  521,  812,  628,
          363,   40,   21,  427,  477,  471,  575,  472, -515,  473,
          847,  616,  428, -423,-32766,   11,  646,   33,   34,  429,
          824,   54,  445,   35,  474,  462,  285,  578, 1111,  357,
          358,  593,  369,  848,  594,  290,  826,  634,  477,    0,
            0,  532,    0,    0,  325,    0,    0,    0,    0,    0,
          651,    0,    0,    0,  322,  326,    0,    0,    0,  426,
            0,    0,    0,    0,  323,  469,  316,  318, -516,  812,
          862,  634,   40,   21,  427,    0,  471,    0,  472,    0,
          473, 1158,    0,  428,    0, -414,    6,    7,   33,   34,
          429,  824,   54,  426,   35,  474,   12,   14,  373,  469,
          357,  358, -424,  812,  563,  754,   40,   21,  427,  477,
          471,  248,  472,  839,  473,   38,   39,  428,  657,  658,
          765,  813,   33,   34,  429,  821,  800,  815,   35,  474,
          215,  216,  878,  869,  357,  358,  217,  870,  218,  798,
          863,  826,  634,  477,  860,  858,  936,  937,  934,  820,
          209,  804,  806,  808,  811,  933,  763,  764, 1100, 1101,
          935,  659,   78,  335,  426,  359, 1102,  635,  639,  641,
          469,  643,  644,  647,  812,  826,  634,   40,   21,  427,
          649,  471,  650,  472,  652,  473,  653,  636,  428,  796,
         1226, 1228,  762,   33,   34,  429,  215,  216,  845,   35,
          474,  761,  217,  844,  218,  357,  358, 1227,  843, 1060,
          831, 1048,  842, 1049,  477,  559,  209, 1106, 1107, 1108,
         1109, 1103, 1104,  398, 1100, 1101,  829,  942,  867, 1110,
         1105,  868, 1102,  457, 1225, 1194, 1192, 1177, 1157,  219,
         1190, 1091,  917, 1198, 1188,    0,  826,  634,   24, -433,
           26,   31,   37,   42,   76,   77,  210,  287,  292,  293,
          308,  309,  310,  311,  339,  356,  416,    0, -227, -226,
           16,   17,   18,  393,  454,  461,  463,  467,  553,  625,
         1051,  559, 1054, 1106, 1107, 1108, 1109, 1103, 1104,  398,
          907, 1116, 1050, 1026,  564, 1110, 1105, 1025, 1093, 1055,
            0, 1044,    0, 1057, 1056,  219, 1059, 1058, 1075,    0,
         1191, 1176, 1172, 1189, 1090, 1223, 1117, 1171,  600
    );

    protected $actionCheck = array(
            2,    3,    4,    5,    6,    7,   14,    9,   10,   11,
           12,   13,   33,   34,   35,   36,   37,   38,   39,   40,
           41,   42,    0,   44,   45,   46,   47,   48,   49,   50,
           51,   52,   53,   54,   55,    9,   10,   11,   33,   34,
           35,   36,   37,   38,   39,   40,   41,   42,   69,   70,
           53,   54,   55,    9,   10,   57,   30,  116,   32,   33,
           34,   35,   36,   37,   38,   80,   69,   70,   83,   71,
           72,   73,   74,   75,   76,   77,  135,  136,   80,   33,
           34,   35,   36,   37,   38,   87,   88,   89,   90,   91,
           92,   93,   94,   95,   96,   97,   98,   99,  100,  101,
          102,  103,  104,  105,  106,  107,  108,  109,  110,  111,
          112,  113,  114,  115,  116,  117,  118,  119,  120,  121,
           31,  123,  124,  125,  126,  127,  128,  129,  130,  131,
          132,   13,  134,  135,  136,  137,  138,  139,  140,  141,
          142,    3,    4,    5,    6,    7,  148,  149,  150,   82,
           12,   13,  160,   15,   33,   34,   35,   36,   37,   38,
           39,   40,   41,   42,    8,   44,   45,   46,   47,   16,
           17,   18,   19,   20,   21,   22,   23,   24,   25,   26,
           27,   28,   29,   80,   33,   34,   35,   36,   50,   51,
           13,    9,   10,   11,   56,  128,   58,   59,   60,   61,
           62,   63,   64,   65,   66,   67,   68,    1,   70,   71,
           72,   73,   59,   60,   37,   38,   78,   79,   80,   82,
           82,  106,   85,  108,   86,    9,   10,   11,  161,   80,
            1,    2,   44,   45,   46,   47,   48,   49,   50,   51,
           52,    9,   10,   11,  106,  156,   30,    8,   32,   33,
           34,   35,   36,   13,  116,    8,  153,  154,  155,    8,
          122,  158,   30,  125,  126,  116,  117,  118,  119,  120,
          121,   31,  134,  135,  156,  137,  138,  139,  140,  141,
          142,  143,  145,  146,    8,    8,  133,  149,  150,   37,
           38,  153,  154,  155,    9,   10,   11,  159,    8,  161,
          162,    8,  164,   74,   75,   76,   77,    8,   13,   80,
            0,    1,    2,   84,  158,   30,   87,   88,   89,   90,
           91,   92,   93,   94,   95,   96,   13,   98,    9,   10,
           11,    9,  103,  104,  105,  106,    8,   70,  109,  110,
            9,   10,   11,    8,  115,  116,  117,  118,   70,   30,
           31,   37,   38,  124,   31,    8,  127,  128,  129,  130,
            8,   30,  156,   32,   33,   34,   35,   36,   37,   38,
           39,   40,   41,   42,   43,  116,  117,  118,  119,  120,
          121,    8,   82,    8,   74,  156,  157,  158,   33,   34,
           80,    1,    2,    8,   84,  163,   82,   87,   88,   89,
          133,   91,   70,   93,  152,   95,  108,   82,   98,  158,
            8,  113,  160,  103,  104,  105,  106,   13,  108,  109,
          110,  123,  122,  113,  157,  115,  116,  117,  118,    9,
           10,   11,  156,   71,  124,  157,  122,  127,  128,  129,
          130,   37,   38,    8,   82,  160,  156,   13,  134,  156,
           30,  156,   32,   33,   34,   35,  158,  157,  148,  159,
          122,  161,   80,    1,   74,  133,  156,  157,  158,  156,
           80,    1,    2,  159,   84,  161,   82,   87,   88,   89,
          157,   91,  157,   93,  122,   95,  161,  106,   98,  108,
          100,  101,  102,  103,  104,  105,  106,  159,  116,  109,
          110,    9,   10,   11,   13,  115,  116,  117,  118,    9,
           10,   11,  160,   16,  124,   81,  122,  127,  128,  129,
          130,  159,   30,  161,   32,   33,   34,   13,  134,  156,
           30,  156,   32,   33,  153,  153,  154,  155,   70,    9,
           10,   11,   80,   80,   74,  160,  156,  157,  158,   14,
           80,   37,   38,  159,   84,  161,  152,   87,   88,   89,
           30,   91,  160,   93,   14,   95,   37,   38,   98,   16,
          100,  101,  102,  103,  104,  105,  106,   70,   82,  109,
          110,   82,   33,   34,   35,  115,  116,  117,  118,   16,
            1,    2,   10,   11,  124,  160,   85,  127,  128,  129,
          130,    9,   10,   11,   82,   11,   14,  157,    9,   10,
           11,  161,   30,   85,   53,   54,   55,  154,   57,  157,
           31,  122,   30,  161,   30,  157,  156,  157,  158,   30,
           69,   32,   33,   34,   35,   36,   37,   38,   39,   40,
           41,   42,   43,   44,   45,   46,   47,   48,   49,   50,
           51,   52,   53,   54,   55,  144,   57,  161,  159,   16,
          161,    1,    2,   74,  157,   16,  152,  157,   69,   80,
          116,  161,  144,   84,   69,   70,   87,   88,   89,   16,
           91,   16,   93,  161,   95,   75,   76,   98,   30,  135,
          136,   31,  103,  104,  105,    1,    2,   31,  109,  110,
            9,   10,   11,   31,  115,  116,   50,   51,   52,   50,
           51,   52,   31,  124,  160,   75,   76,  101,  102,  111,
          112,   30,  156,  157,   31,   31,  156,  157,  156,  157,
           31,   31,   57,   38,   74,   33,   69,   80,   70,   70,
           80,   70,   89,   70,   84,  156,  157,   87,   88,   89,
           70,   91,   70,   93,   70,   95,   70,   96,   98,   71,
           77,   82,   85,  103,  104,  105,    1,    2,   74,  109,
          110,   82,   82,   97,   80,  115,  116,   85,   84,   92,
          106,   87,   88,   89,  124,   91,   90,   93,  133,   95,
          128,   94,   98,  147,  116,   97,   31,  103,  104,  105,
            1,    2,   97,  109,  110,   97,   97,  100,  144,  115,
          116,  100,  106,  128,  113,  161,  156,  157,  124,   -1,
           -1,  151,   -1,   -1,  114,   -1,   -1,   -1,   -1,   -1,
           31,   -1,   -1,   -1,  131,  131,   -1,   -1,   -1,   74,
           -1,   -1,   -1,   -1,  132,   80,  133,  133,  133,   84,
          156,  157,   87,   88,   89,   -1,   91,   -1,   93,   -1,
           95,  144,   -1,   98,   -1,  147,  147,  147,  103,  104,
          105,    1,    2,   74,  109,  110,  147,  147,  147,   80,
          115,  116,  147,   84,  151,  153,   87,   88,   89,  124,
           91,   31,   93,  152,   95,  156,  156,   98,  156,  156,
          156,  156,  103,  104,  105,  156,  156,  156,  109,  110,
           50,   51,  156,  156,  115,  116,   56,  156,   58,  156,
          156,  156,  157,  124,  156,  156,  156,  156,  156,  156,
           70,  156,  156,  156,  156,  156,  156,  156,   78,   79,
          156,  158,  157,  157,   74,  157,   86,  157,  157,  157,
           80,  157,  157,  157,   84,  156,  157,   87,   88,   89,
          157,   91,  157,   93,  157,   95,  157,  157,   98,  158,
          158,  158,  158,  103,  104,  105,   50,   51,  158,  109,
          110,  158,   56,  158,   58,  115,  116,  158,  158,  158,
          158,  158,  158,  158,  124,  135,   70,  137,  138,  139,
          140,  141,  142,  143,   78,   79,  158,  158,  158,  149,
          150,  158,   86,  158,  158,  158,  158,  158,  164,  159,
          158,  158,  158,  158,  158,   -1,  156,  157,  159,  162,
          159,  159,  159,  159,  159,  159,  159,  159,  159,  159,
          159,  159,  159,  159,  159,  159,  159,   -1,  160,  160,
          160,  160,  160,  160,  160,  160,  160,  160,  160,  160,
          160,  135,  160,  137,  138,  139,  140,  141,  142,  143,
          160,  160,  160,  160,  160,  149,  150,  160,  160,  163,
           -1,  162,   -1,  163,  163,  159,  163,  163,  163,   -1,
          163,  163,  163,  163,  163,  163,  163,  163,  163
    );

    protected $actionBase = array(
            0,  229,  310,  390,  470,  103,  325,  325,  784,   -2,
           -2,  149,   -2,   -2,   -2,  660,  765,  799,  765,  589,
          694,  870,  870,  870,  252,  404,  404,  404,  514,  177,
          177,  918,  434,  118,  295,  313,  240,  491,  491,  491,
          491,  138,  138,  491,  491,  491,  491,  491,  491,  491,
          491,  491,  491,  491,  491,  491,  491,  491,  491,  491,
          491,  491,  491,  491,  491,  491,  491,  491,  491,  491,
          491,  491,  491,  491,  491,  491,  491,  491,  491,  491,
          491,  491,  491,  491,  491,  491,  491,  491,  491,  491,
          491,  491,  491,  491,  491,  491,  491,  491,  491,  491,
          491,  491,  491,  491,  491,  491,  491,  491,  491,  491,
          491,  491,  491,  491,  491,  491,  491,  491,  491,  491,
          491,  491,  491,  491,  491,  491,  491,  491,  491,  491,
          491,  491,  491,  491,  491,  491,   89,  206,  773,  550,
          535,  775,  776,  777,  912,  709,  913,  856,  857,  700,
          858,  859,  862,  863,  864,  855,  865,  935,  866,  599,
          599,  599,  599,  599,  599,  599,  599,  599,  599,  599,
          599,  322,  592,  285,  319,  232,   44,  691,  691,  691,
          691,  691,  691,  691,  182,  182,  182,  182,  182,  182,
          182,  182,  182,  182,  182,  182,  182,  182,  182,  182,
          182,  182,  582,  530,  530,  530,  594,  860,  658,  926,
          926,  926,  926,  926,  926,  926,  926,  926,  926,  926,
          926,  926,  926,  926,  926,  926,  926,  926,  926,  926,
          926,  926,  926,  926,  926,  926,  926,  926,  926,  926,
          926,  926,  926,  926,  926,  926,  926,  926,  926,  926,
          926,  926,  926,  500,  -21,  -21,  492,  702,  420,  355,
          216,  549,  151,   26,   26,  331,  331,  331,  331,  331,
           46,   46,    5,    5,    5,    5,  153,  188,  188,  188,
          188,  121,  121,  121,  121,  314,  314,  394,  394,  362,
          300,  298,  499,  499,  499,  499,  499,  499,  499,  499,
          499,  499,   67,  656,  656,  659,  659,  522,  554,  554,
          554,  554,  679,  -59,  -59,  381,  462,  462,  462,  528,
          717,  854,  382,  382,  382,  382,  382,  382,  561,  561,
          561,   -3,   -3,   -3,  692,  115,  137,  115,  137,  678,
          732,  450,  732,  338,  677,  -15,  510,  810,  468,  707,
          850,  711,  853,  572,  735,  267,  529,  654,  674,  463,
          529,  529,  529,  529,  654,  610,  640,  608,  463,  529,
          463,  718,  323,  496,   89,  570,  507,  675,  778,  293,
          670,  780,  290,  373,  332,  566,  278,  435,  733,  781,
          914,  917,  385,  715,  675,  675,  675,  352,  511,  278,
           -8,  605,  605,  605,  605,  156,  605,  605,  605,  605,
          251,  276,  375,  402,  779,  657,  657,  690,  872,  869,
          869,  657,  689,  657,  690,  874,  874,  874,  874,  657,
          657,  657,  657,  869,  869,  869,  688,  869,  239,  703,
          704,  704,  874,  742,  743,  657,  657,  712,  869,  869,
          869,  712,  695,  874,  701,  741,  277,  869,  874,  672,
          689,  672,  657,  701,  672,  689,  689,  672,   22,  666,
          668,  873,  875,  887,  790,  662,  685,  879,  880,  876,
          878,  871,  699,  744,  745,  497,  669,  671,  673,  680,
          719,  682,  713,  674,  667,  667,  667,  655,  720,  655,
          667,  667,  667,  667,  667,  667,  667,  667,  916,  646,
          731,  714,  653,  749,  553,  573,  791,  664,  811,  900,
          893,  867,  919,  881,  898,  655,  920,  739,  247,  643,
          882,  783,  786,  655,  883,  655,  792,  655,  902,  812,
          686,  813,  814,  667,  910,  921,  923,  924,  925,  927,
          928,  929,  930,  684,  931,  750,  696,  894,  299,  877,
          718,  729,  705,  788,  751,  820,  328,  932,  823,  655,
          655,  794,  785,  655,  795,  756,  740,  890,  757,  895,
          933,  664,  708,  896,  655,  706,  825,  934,  328,  681,
          683,  888,  661,  761,  886,  911,  885,  796,  649,  663,
          829,  830,  831,  693,  763,  891,  892,  889,  764,  803,
          665,  805,  697,  832,  807,  884,  768,  833,  834,  899,
          676,  730,  710,  698,  687,  809,  835,  897,  769,  770,
          771,  848,  772,  849,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,  138,  138,  138,  138,   -2,   -2,
           -2,   -2,    0,    0,   -2,    0,    0,    0,  138,  138,
          138,  138,  138,  138,  138,  138,  138,  138,  138,  138,
          138,  138,  138,  138,  138,  138,  138,  138,  138,  138,
          138,  138,  138,  138,    0,    0,  138,  138,  138,  138,
          138,  138,  138,  138,  138,  138,  138,  138,  138,  138,
          138,  138,  138,  138,  138,  138,  138,  138,  138,  138,
          138,  138,  138,  138,  138,  138,  138,  138,  138,  138,
          138,  138,  138,  138,  138,  138,  138,  138,  138,  138,
          138,  138,  138,  138,  138,  138,  138,  138,  138,  138,
          138,  138,  138,  138,  138,  138,  138,  138,  138,  138,
          138,  138,  138,  138,  138,  138,  138,  138,  138,  138,
          138,  138,  138,  138,  138,  138,  138,  138,  138,  138,
          138,  138,  138,  138,  138,  138,  138,  138,  138,  599,
          599,  599,  599,  599,  599,  599,  599,  599,  599,  599,
          599,  599,  599,  599,  599,  599,  599,  599,  599,  599,
          599,  599,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,  599,  -21,  -21,  -21,  -21,  599,
          -21,  -21,  -21,  -21,  -21,  -21,  -21,  599,  599,  599,
          599,  599,  599,  599,  599,  599,  599,  599,  599,  599,
          599,  599,  599,  599,  599,  -21,  599,  599,  599,  -21,
          382,  -21,  382,  382,  382,  382,  382,  382,  382,  382,
          382,  382,  382,  382,  382,  382,  382,  382,  382,  382,
          382,  382,  382,  382,  382,  382,  382,  382,  382,  382,
          382,  382,  382,  382,  382,  382,  382,  382,  382,  382,
          382,  382,  382,  382,  382,  382,  599,    0,    0,  599,
          -21,  599,  -21,  599,  -21,  -21,  599,  599,  599,  599,
          599,  599,  599,  -21,  -21,  -21,  -21,  -21,  -21,    0,
          561,  561,  561,  561,  -21,  -21,  -21,  -21,  382,  382,
          382,  382,  382,  382,  259,  382,  382,  382,  382,  382,
          382,  382,  382,  382,  382,  382,  561,  561,   -3,   -3,
          382,  382,  382,  382,  382,  259,  382,  382,  463,  689,
          689,  689,  137,  137,  137,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,  137,  463,    0,
          463,    0,  382,  463,  689,  463,  657,  137,  689,  689,
          463,  869,  616,  616,  616,  616,  328,  278,    0,    0,
          689,  689,    0,    0,    0,    0,    0,  689,    0,    0,
            0,    0,    0,    0,  869,    0,    0,    0,    0,    0,
          667,  247,    0,  705,  335,    0,    0,    0,    0,    0,
            0,  705,  335,  347,  347,    0,  684,  667,  667,  667,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,  328
    );

    protected $actionDefault = array(
            3,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,  544,  544,  499,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,  299,  299,  299,
        32767,32767,32767,  532,  532,  532,  532,  532,  532,  532,
          532,  532,  532,  532,32767,32767,32767,32767,32767,32767,
          383,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  389,
          549,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  364,
          365,  367,  368,  298,  552,  533,  247,  390,  548,  297,
          249,  327,  503,32767,32767,32767,  329,  122,  258,  203,
          502,  125,  296,  234,  382,  384,  328,  303,  308,  309,
          310,  311,  312,  313,  314,  315,  316,  317,  318,  319,
          320,  302,  458,  361,  360,  359,  460,32767,  459,  496,
          496,  499,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,  325,  487,  486,  326,  456,  330,  457,
          333,  461,  464,  331,  332,  349,  350,  347,  348,  351,
          462,  463,  480,  481,  478,  479,  301,  352,  353,  354,
          355,  482,  483,  484,  485,32767,32767,  543,  543,32767,
        32767,  282,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,  340,  341,  471,  472,32767,  238,  238,
          238,  238,  283,  238,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,  335,  336,
          334,  466,  467,  465,  432,32767,32767,32767,  434,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,  504,32767,
        32767,32767,32767,32767,  517,  421,  171,32767,  413,32767,
          171,  171,  171,  171,32767,  222,  224,  167,32767,  171,
        32767,  490,32767,32767,32767,32767,  522,  345,32767,32767,
          116,32767,32767,32767,  559,32767,  517,32767,  116,32767,
        32767,32767,32767,  358,  337,  338,  339,32767,32767,  521,
          515,  474,  475,  476,  477,32767,  468,  469,  470,  473,
        32767,32767,32767,32767,32767,32767,32767,32767,  429,  435,
          435,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,  520,  519,32767,  414,  498,  188,
          186,  186,32767,  208,  208,32767,32767,  190,  491,  510,
        32767,  190,  173,32767,  400,  175,  498,32767,32767,  240,
        32767,  240,32767,  400,  240,32767,32767,  240,32767,  415,
          439,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,  379,  380,  493,  506,
        32767,  507,32767,  413,  343,  344,  346,  322,32767,  324,
          369,  370,  371,  372,  373,  374,  375,  377,32767,  419,
        32767,  422,32767,32767,32767,  257,32767,  557,32767,32767,
          306,  557,32767,32767,32767,  551,32767,32767,  300,32767,
        32767,32767,32767,  253,32767,  169,32767,  541,32767,  558,
        32767,  515,32767,  342,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,  516,32767,32767,32767,32767,  229,32767,
          452,32767,  116,32767,32767,32767,  189,32767,32767,  304,
          248,32767,32767,  550,32767,32767,32767,32767,32767,32767,
        32767,32767,  114,32767,  170,32767,32767,32767,  191,32767,
        32767,  515,32767,32767,32767,32767,32767,32767,32767,  295,
        32767,32767,32767,32767,32767,32767,32767,  515,32767,32767,
          233,32767,32767,32767,32767,32767,32767,32767,32767,32767,
          415,32767,  276,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,  127,  127,    3,  127,  127,  260,
            3,  260,  127,  260,  260,  127,  127,  127,  127,  127,
          127,  127,  127,  127,  127,  216,  219,  208,  208,  164,
          127,  127,  268
    );

    protected $goto = array(
          166,  140,  140,  140,  166,  187,  168,  144,  147,  141,
          142,  143,  149,  163,  163,  163,  163,  144,  144,  165,
          165,  165,  165,  165,  165,  165,  165,  165,  165,  165,
          138,  159,  160,  161,  162,  184,  139,  185,  494,  495,
          377,  496,  500,  501,  502,  503,  504,  505,  506,  507,
          970,  164,  145,  146,  148,  171,  176,  186,  203,  253,
          256,  258,  260,  263,  264,  265,  266,  267,  268,  269,
          277,  278,  279,  280,  303,  304,  328,  329,  330,  394,
          395,  396,  543,  188,  189,  190,  191,  192,  193,  194,
          195,  196,  197,  198,  199,  200,  201,  150,  151,  152,
          167,  153,  169,  154,  204,  170,  155,  156,  157,  205,
          158,  136,  621,  561,  757,  561,  561,  561,  561,  561,
          561,  561,  561,  561,  561,  561,  561,  561,  561,  561,
          561,  561,  561,  561,  561,  561,  561,  561,  561,  561,
          561,  561,  561,  561,  561,  561,  561,  561,  561,  561,
          561,  561,  561,  561,  561,  561,  561,  561,  561, 1113,
          629, 1113, 1113, 1113, 1113, 1113, 1113, 1113, 1113, 1113,
         1113, 1113, 1113, 1113, 1113, 1113, 1113, 1113, 1113, 1113,
         1113, 1113, 1113, 1113, 1113, 1113, 1113, 1113, 1113, 1113,
         1113, 1113, 1113, 1113, 1113, 1113, 1113, 1113, 1113, 1113,
         1113, 1113, 1113, 1113, 1113,  758,  520,  531,  509,  656,
          556, 1183,  750,  509,  592,  786, 1183,  888,  612,  613,
          884,  617,  618,  624,  626,  631,  633,  817,  855,  855,
          855,  855,  850,  856,  174,  891,  891, 1205, 1205,  177,
          178,  179,  401,  402,  403,  404,  173,  202,  206,  208,
          257,  259,  261,  262,  270,  271,  272,  273,  274,  275,
          281,  282,  283,  284,  305,  306,  331,  332,  333,  406,
          407,  408,  409,  175,  180,  254,  255,  181,  182,  183,
          498,  498,  498,  498,  498,  498,  861,  498,  498,  498,
          498,  498,  498,  498,  498,  498,  498,  510,  586,  538,
          601,  602,  510,  545,  546,  547,  548,  549,  550,  551,
          552,  554,  587, 1209,  560,  350,  560,  560,  560,  560,
          560,  560,  560,  560,  560,  560,  560,  560,  560,  560,
          560,  560,  560,  560,  560,  560,  560,  560,  560,  560,
          560,  560,  560,  560,  560,  560,  560,  560,  560,  560,
          560,  560,  560,  560,  560,  560,  560,  560,  560,  560,
          400,  607,  537,  537,  569,  533,  909,  535,  535,  497,
          499,  525,  541,  570,  573,  584,  591,  298,  296,  296,
          296,  298,  289,  299,  611,  378,  511,  614,  595,  947,
          375,  511,  437,  437,  437,  437,  437,  437, 1163,  437,
          437,  437,  437,  437,  437,  437,  437,  437,  437, 1077,
          948,  338, 1175,  321, 1077,  898,  898,  898,  898,  606,
          898,  898, 1217, 1217, 1202,  753,  576,  605,  756, 1077,
         1077, 1077, 1077, 1077, 1077, 1069,  384,  384,  384,  391,
         1217,  877,  859,  857,  859,  655,  466,  512,  886,  881,
          753,  384,  753,  384,  968,  384,  895,  385,  588,  353,
          414,  384, 1231, 1019,  542, 1197, 1197, 1197,  568, 1094,
          386,  386,  386,  904,  915,  515, 1029,   19,   15,  372,
          389,  915,  940,  448,  450,  632,  340, 1216, 1216, 1114,
          615,  938,  840,  555,  775,  386,  913, 1070, 1073, 1074,
          399, 1069, 1182,  660,   23, 1216,  773, 1182,  544,  603,
         1066, 1219, 1071, 1174, 1071,  519, 1199, 1199, 1199, 1089,
         1088, 1072,  343,  523,  534,  519,  519,  772,  351,  352,
           13,  579,  583,  627, 1061,  388,  782,  562,  771,  515,
          783, 1181,    3,    4,  918,  956,  865,  451,  574, 1160,
          464,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,  514,  529,    0,    0,    0,    0,
          514,    0,  529,    0,    0,    0,    0,  610,  513,  516,
          439,  440, 1067,  619,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,  780, 1224,    0,    0,    0,    0,
            0,  524,    0,    0,    0,    0,    0,    0,    0,    0,
            0,  778,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,  301,  301
    );

    protected $gotoCheck = array(
           43,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   57,   69,   15,   69,   69,   69,   69,   69,
           69,   69,   69,   69,   69,   69,   69,   69,   69,   69,
           69,   69,   69,   69,   69,   69,   69,   69,   69,   69,
           69,   69,   69,   69,   69,   69,   69,   69,   69,   69,
           69,   69,   69,   69,   69,   69,   69,   69,   69,  128,
            9,  128,  128,  128,  128,  128,  128,  128,  128,  128,
          128,  128,  128,  128,  128,  128,  128,  128,  128,  128,
          128,  128,  128,  128,  128,  128,  128,  128,  128,  128,
          128,  128,  128,  128,  128,  128,  128,  128,  128,  128,
          128,  128,  128,  128,  128,   16,  102,   32,   69,   32,
           32,  120,    6,   69,   32,   29,  120,   32,   32,   32,
           32,   32,   32,   32,   32,   32,   32,   50,   69,   69,
           69,   69,   69,   69,   27,   77,   77,   77,   77,   27,
           27,   27,   27,   27,   27,   27,   27,   27,   27,   27,
           27,   27,   27,   27,   27,   27,   27,   27,   27,   27,
           27,   27,   27,   27,   27,   27,   27,   27,   27,   27,
           27,   27,   27,   27,   27,   27,   27,   27,   27,   27,
          119,  119,  119,  119,  119,  119,   33,  119,  119,  119,
          119,  119,  119,  119,  119,  119,  119,  119,   67,  110,
           67,   67,  119,  111,  111,  111,  111,  111,  111,  111,
          111,  111,  111,  142,   57,   72,   57,   57,   57,   57,
           57,   57,   57,   57,   57,   57,   57,   57,   57,   57,
           57,   57,   57,   57,   57,   57,   57,   57,   57,   57,
           57,   57,   57,   57,   57,   57,   57,   57,   57,   57,
           57,   57,   57,   57,   57,   57,   57,   57,   57,   57,
           51,   51,   51,   51,   51,   51,   84,   51,   51,   51,
           51,   51,   51,   51,   51,   51,   51,    5,    5,    5,
            5,    5,    5,    5,   63,   46,  124,   63,  129,   98,
           63,  124,   57,   57,   57,   57,   57,   57,  133,   57,
           57,   57,   57,   57,   57,   57,   57,   57,   57,   57,
           98,  127,   82,  127,   57,   57,   57,   57,   57,   49,
           57,   57,  144,  144,  140,   11,   40,   40,   14,   57,
           57,   57,   57,   57,   57,   82,   13,   13,   13,   48,
          144,   14,   14,   14,   14,   14,   57,   14,   14,   14,
           11,   13,   11,   13,  102,   13,   79,   11,   70,   70,
           70,   13,   13,  103,    2,    9,    9,    9,    2,   34,
          125,  125,  125,   81,   13,   13,   34,   34,   34,   34,
           17,   13,    8,    8,    8,    8,   18,  143,  143,    8,
            8,    8,    9,   34,   25,  125,   85,   82,   82,   82,
          125,   82,  121,   74,   34,  143,   24,  121,   47,   34,
          116,  143,   82,   82,   82,   47,  121,  121,  121,  126,
          126,   82,   58,   58,   58,   47,   47,   23,   72,   72,
           58,   62,   62,   62,  114,   12,   23,   12,   23,   13,
           26,  121,   30,   30,   86,  100,   71,   65,   66,  132,
          109,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,    9,    9,   -1,   -1,   -1,   -1,
            9,   -1,    9,   -1,   -1,   -1,   -1,   13,    9,    9,
            9,    9,   13,   13,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,    9,    9,   -1,   -1,   -1,   -1,
           -1,  102,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,    9,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,    5,    5
    );

    protected $gotoBase = array(
            0,    0, -172,    0,    0,  353,  201,    0,  477,  149,
            0,  110,  195,  117,  426,  112,  203,  140,  171,    0,
            0,    0,    0,  168,  164,  157,  119,   27,    0,  205,
         -118,    0, -428,  266,   51,    0,    0,    0,    0,    0,
          388,    0,    0,  -24,    0,    0,  345,  484,  146,  133,
          209,   75,    0,    0,    0,    0,    0,  107,  161,    0,
            0,    0,  222,  -77,    0,  106,   97, -343,    0,  -94,
          135,  123, -129,    0,  129,    0,    0,  -50,    0,  143,
            0,  159,   64,    0,  338,  132,  122,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,   98,    0,
          121,    0,  165,  156,    0,    0,    0,    0,    0,   87,
          273,  259,    0,    0,  114,    0,  150,    0,    0,   -5,
          -91,  200,    0,    0,   84,  154,  202,   77,  -48,  178,
            0,    0,   93,  187,    0,    0,    0,    0,    0,    0,
          136,    0,  286,  167,  102,    0,    0
    );

    protected $gotoDefault = array(
        -32768,  468,  664,    2,  665,  835,  740,  748,  598,  482,
          630,  582,  380, 1193,  792,  793,  794,  381,  368,  483,
          379,  410,  405,  781,  774,  776,  784,  172,  411,  787,
            1,  789,  518,  825, 1020,  365,  797,  366,  590,  799,
          527,  801,  802,  137,  382,  383,  528,  484,  390,  577,
          816,  276,  387,  818,  367,  819,  828,  371,  465,  455,
          460,  530,  557,  609,  432,  447,  571,  565,  536, 1086,
          566,  864,  349,  872,  661,  880,  883,  485,  558,  894,
          452,  902, 1099,  397,  908,  914,  919,  291,  922,  417,
          412,  585,  927,  928,    5,  932,  622,  623,    8,  312,
          955,  599,  969,  420, 1039, 1041,  486,  487,  522,  459,
          508,  526,  488, 1062,  441,  413, 1065,  433,  489,  490,
          434,  435, 1083,  355, 1168,  354,  449,  320, 1155,  580,
         1118,  456, 1208, 1164,  348,  491,  492,  376, 1187,  392,
         1203,  438, 1210, 1218,  344,  540,  567
    );

    protected $ruleToNonTerminal = array(
            0,    1,    3,    3,    2,    5,    5,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    7,    7,    7,
            7,    7,    7,    7,    8,    8,    9,   10,   11,   11,
           12,   12,   13,    4,    4,    4,    4,    4,    4,    4,
            4,    4,    4,    4,   18,   18,   19,   19,   21,   21,
           17,   17,   22,   22,   23,   23,   24,   24,   25,   25,
           20,   20,   26,   28,   28,   29,   30,   30,   32,   31,
           31,   31,   31,   33,   33,   33,   33,   33,   33,   33,
           33,   33,   33,   33,   33,   33,   33,   33,   33,   33,
           33,   33,   33,   33,   33,   33,   33,   33,   33,   33,
           33,   33,   14,   14,   54,   54,   56,   55,   55,   48,
           48,   58,   58,   59,   59,   60,   60,   61,   61,   15,
           16,   16,   16,   64,   64,   64,   65,   65,   68,   68,
           66,   66,   70,   70,   41,   41,   50,   50,   53,   53,
           53,   52,   52,   71,   42,   42,   42,   42,   72,   72,
           73,   73,   74,   74,   39,   39,   35,   35,   75,   37,
           37,   76,   36,   36,   38,   38,   49,   49,   49,   62,
           62,   78,   78,   79,   79,   81,   81,   81,   80,   80,
           63,   63,   82,   82,   82,   83,   83,   84,   84,   84,
           44,   44,   85,   85,   85,   45,   45,   86,   86,   87,
           87,   67,   88,   88,   88,   88,   93,   93,   94,   94,
           95,   95,   95,   95,   95,   96,   97,   97,   92,   92,
           89,   89,   91,   91,   99,   99,   98,   98,   98,   98,
           98,   98,   90,   90,  101,  100,  100,   46,   46,   40,
           40,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   43,   43,   43,   43,   43,   43,   43,   43,
           43,   43,   43,   43,   43,   43,   43,   34,   34,   47,
           47,  106,  106,  107,  107,  107,  107,  113,  102,  102,
          109,  109,  115,  115,  116,  117,  118,  118,  118,  118,
          118,  118,  118,   69,   69,   57,   57,   57,   57,  103,
          103,  122,  122,  119,  119,  123,  123,  123,  123,  104,
          104,  104,  108,  108,  108,  114,  114,  128,  128,  128,
          128,  128,  128,  128,  128,  128,  128,  128,  128,  128,
           27,   27,   27,   27,   27,   27,  130,  130,  130,  130,
          130,  130,  130,  130,  130,  130,  130,  130,  130,  130,
          130,  130,  130,  130,  130,  130,  130,  130,  130,  130,
          130,  130,  130,  130,  130,  130,  130,  130,  130,  130,
          112,  112,  105,  105,  105,  105,  129,  129,  132,  132,
          131,  131,  133,  133,   51,   51,   51,   51,  135,  135,
          134,  134,  134,  134,  134,  136,  136,  121,  121,  124,
          124,  120,  120,  138,  137,  137,  137,  137,  125,  125,
          125,  125,  111,  111,  126,  126,  126,  126,   77,  139,
          139,  140,  140,  140,  110,  110,  141,  141,  142,  142,
          142,  142,  142,  127,  127,  127,  127,  144,  145,  143,
          143,  143,  143,  143,  143,  143,  146,  146,  146
    );

    protected $ruleToLength = array(
            1,    1,    2,    0,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    3,    5,    4,
            3,    4,    2,    3,    1,    1,    7,    6,    3,    1,
            3,    1,    3,    1,    1,    3,    1,    3,    1,    2,
            3,    1,    3,    3,    1,    3,    2,    0,    1,    1,
            1,    1,    1,    3,    5,    8,    3,    5,    9,    3,
            2,    3,    2,    3,    2,    3,    3,    3,    3,    1,
            2,    2,    5,    7,    9,    5,    6,    3,    3,    2,
            2,    1,    1,    1,    0,    2,    8,    0,    4,    1,
            3,    0,    1,    0,    1,    0,    1,    1,    1,   10,
            7,    6,    5,    1,    2,    2,    0,    2,    0,    2,
            0,    2,    1,    3,    1,    4,    1,    4,    1,    1,
            4,    1,    3,    3,    3,    4,    4,    5,    0,    2,
            4,    3,    1,    1,    1,    4,    0,    2,    3,    0,
            2,    4,    0,    2,    0,    3,    1,    2,    1,    1,
            0,    1,    3,    4,    6,    1,    1,    1,    0,    1,
            0,    2,    2,    3,    3,    1,    3,    1,    2,    2,
            3,    1,    1,    2,    4,    3,    1,    1,    3,    2,
            0,    1,    3,    3,    9,    3,    1,    3,    0,    2,
            4,    5,    4,    4,    4,    3,    1,    1,    1,    3,
            1,    1,    0,    1,    1,    2,    1,    1,    1,    1,
            1,    1,    1,    3,    1,    1,    3,    3,    1,    0,
            1,    1,    3,    3,    4,    4,    1,    2,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    2,    2,    2,    2,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    3,    3,    2,    2,    2,    2,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    1,    3,    5,
            4,    3,    4,    4,    2,    2,    2,    2,    2,    2,
            2,    2,    2,    2,    2,    2,    2,    2,    1,    1,
            1,    3,    2,    1,    2,   10,   11,    3,    3,    2,
            4,    4,    3,    4,    4,    4,    4,    7,    3,    2,
            0,    4,    1,    3,    2,    1,    2,    2,    4,    6,
            2,    2,    4,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    3,    3,    4,    4,    0,
            2,    1,    0,    1,    1,    0,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    3,    2,
            1,    3,    1,    4,    3,    1,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    2,    2,    2,    2,    3,    3,
            3,    3,    3,    3,    3,    3,    5,    4,    4,    3,
            1,    3,    1,    1,    3,    3,    0,    2,    0,    1,
            3,    1,    3,    1,    1,    1,    1,    1,    6,    4,
            3,    4,    2,    4,    4,    1,    3,    1,    2,    1,
            1,    4,    1,    1,    3,    6,    4,    4,    4,    4,
            1,    4,    0,    1,    1,    3,    1,    1,    4,    3,
            1,    1,    1,    0,    0,    2,    3,    1,    3,    1,
            4,    2,    2,    2,    2,    1,    2,    1,    1,    1,
            4,    3,    3,    3,    6,    3,    1,    1,    1
    );

    protected function initReduceCallbacks() {
        $this->reduceCallbacks = [
            0 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            1 => function ($stackPos) {
                 $this->semValue = $this->handleNamespaces($this->semStack[$stackPos-(1-1)]);
            },
            2 => function ($stackPos) {
                 if (is_array($this->semStack[$stackPos-(2-2)])) { $this->semValue = array_merge($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)]); } else { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)]; };
            },
            3 => function ($stackPos) {
                 $this->semValue = array();
            },
            4 => function ($stackPos) {
                 $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop($this->createCommentNopAttributes($startAttributes['comments'])); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            5 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            6 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            7 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            8 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            9 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            10 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            11 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            12 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            13 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            14 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            15 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            16 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            17 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            18 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            19 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            20 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            21 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            22 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            23 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            24 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            25 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            26 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            27 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            28 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            29 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            30 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            31 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            32 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            33 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            34 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            35 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            36 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            37 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            38 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            39 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            40 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            41 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            42 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            43 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            44 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            45 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            46 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            47 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            48 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            49 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            50 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            51 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            52 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            53 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            54 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            55 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            56 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            57 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            58 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            59 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            60 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            61 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            62 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            63 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            64 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            65 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            66 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            67 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            68 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            69 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            70 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            71 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            72 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            73 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            74 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            75 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            76 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            77 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            78 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            79 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            80 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            81 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            82 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            83 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            84 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            85 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            86 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            87 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            88 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            89 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            90 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            91 => function ($stackPos) {
                 $this->semValue = new Name(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            92 => function ($stackPos) {
                 $this->semValue = new Expr\Variable(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            93 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            94 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            95 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            96 => function ($stackPos) {
                 $this->semValue = new Stmt\HaltCompiler($this->lexer->handleHaltCompiler(), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            97 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_($this->semStack[$stackPos-(3-2)], null, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_SEMICOLON);
            $this->checkNamespace($this->semValue);
            },
            98 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_($this->semStack[$stackPos-(5-2)], $this->semStack[$stackPos-(5-4)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $this->checkNamespace($this->semValue);
            },
            99 => function ($stackPos) {
                 $this->semValue = new Stmt\Namespace_(null, $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            $this->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $this->checkNamespace($this->semValue);
            },
            100 => function ($stackPos) {
                 $this->semValue = new Stmt\Use_($this->semStack[$stackPos-(3-2)], Stmt\Use_::TYPE_NORMAL, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            101 => function ($stackPos) {
                 $this->semValue = new Stmt\Use_($this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-2)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            102 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            103 => function ($stackPos) {
                 $this->semValue = new Stmt\Const_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            104 => function ($stackPos) {
                 $this->semValue = Stmt\Use_::TYPE_FUNCTION;
            },
            105 => function ($stackPos) {
                 $this->semValue = Stmt\Use_::TYPE_CONSTANT;
            },
            106 => function ($stackPos) {
                 $this->semValue = new Stmt\GroupUse($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-6)], $this->semStack[$stackPos-(7-2)], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            107 => function ($stackPos) {
                 $this->semValue = new Stmt\GroupUse($this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-5)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            108 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            109 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            110 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            111 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            112 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            113 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            114 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(1-1));
            },
            115 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(3-3));
            },
            116 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(1-1));
            },
            117 => function ($stackPos) {
                 $this->semValue = new Stmt\UseUse($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes); $this->checkUseUse($this->semValue, $stackPos-(3-3));
            },
            118 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)]; $this->semValue->type = Stmt\Use_::TYPE_NORMAL;
            },
            119 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)]; $this->semValue->type = $this->semStack[$stackPos-(2-1)];
            },
            120 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            121 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            122 => function ($stackPos) {
                 $this->semValue = new Node\Const_($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            123 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            124 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            125 => function ($stackPos) {
                 $this->semValue = new Node\Const_($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            126 => function ($stackPos) {
                 if (is_array($this->semStack[$stackPos-(2-2)])) { $this->semValue = array_merge($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)]); } else { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)]; };
            },
            127 => function ($stackPos) {
                 $this->semValue = array();
            },
            128 => function ($stackPos) {
                 $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop($this->createCommentNopAttributes($startAttributes['comments'])); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            129 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            130 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            131 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            132 => function ($stackPos) {
                 throw new Error('__HALT_COMPILER() can only be used from the outermost scope', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            133 => function ($stackPos) {

        if ($this->semStack[$stackPos-(3-2)]) {
            $this->semValue = $this->semStack[$stackPos-(3-2)]; $attrs = $this->startAttributeStack[$stackPos-(3-1)]; $stmts = $this->semValue; if (!empty($attrs['comments'])) {$stmts[0]->setAttribute('comments', array_merge($attrs['comments'], $stmts[0]->getAttribute('comments', []))); };
        } else {
            $startAttributes = $this->startAttributeStack[$stackPos-(3-1)]; if (isset($startAttributes['comments'])) { $this->semValue = new Stmt\Nop($startAttributes + $this->endAttributes); } else { $this->semValue = null; };
            if (null === $this->semValue) { $this->semValue = array(); }
        }

            },
            134 => function ($stackPos) {
                 $this->semValue = new Stmt\If_($this->semStack[$stackPos-(5-2)], ['stmts' => is_array($this->semStack[$stackPos-(5-3)]) ? $this->semStack[$stackPos-(5-3)] : array($this->semStack[$stackPos-(5-3)]), 'elseifs' => $this->semStack[$stackPos-(5-4)], 'else' => $this->semStack[$stackPos-(5-5)]], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            135 => function ($stackPos) {
                 $this->semValue = new Stmt\If_($this->semStack[$stackPos-(8-2)], ['stmts' => $this->semStack[$stackPos-(8-4)], 'elseifs' => $this->semStack[$stackPos-(8-5)], 'else' => $this->semStack[$stackPos-(8-6)]], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            136 => function ($stackPos) {
                 $this->semValue = new Stmt\While_($this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            137 => function ($stackPos) {
                 $this->semValue = new Stmt\Do_($this->semStack[$stackPos-(5-4)], is_array($this->semStack[$stackPos-(5-2)]) ? $this->semStack[$stackPos-(5-2)] : array($this->semStack[$stackPos-(5-2)]), $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            138 => function ($stackPos) {
                 $this->semValue = new Stmt\For_(['init' => $this->semStack[$stackPos-(9-3)], 'cond' => $this->semStack[$stackPos-(9-5)], 'loop' => $this->semStack[$stackPos-(9-7)], 'stmts' => $this->semStack[$stackPos-(9-9)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            139 => function ($stackPos) {
                 $this->semValue = new Stmt\Switch_($this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            140 => function ($stackPos) {
                 $this->semValue = new Stmt\Break_(null, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            141 => function ($stackPos) {
                 $this->semValue = new Stmt\Break_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            142 => function ($stackPos) {
                 $this->semValue = new Stmt\Continue_(null, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            143 => function ($stackPos) {
                 $this->semValue = new Stmt\Continue_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            144 => function ($stackPos) {
                 $this->semValue = new Stmt\Return_(null, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            145 => function ($stackPos) {
                 $this->semValue = new Stmt\Return_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            146 => function ($stackPos) {
                 $this->semValue = new Stmt\Global_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            147 => function ($stackPos) {
                 $this->semValue = new Stmt\Static_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            148 => function ($stackPos) {
                 $this->semValue = new Stmt\Echo_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            149 => function ($stackPos) {
                 $this->semValue = new Stmt\InlineHTML($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            150 => function ($stackPos) {
                 $this->semValue = new Stmt\Expression($this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            151 => function ($stackPos) {
                 $this->semValue = new Stmt\Expression($this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            152 => function ($stackPos) {
                 $this->semValue = new Stmt\Unset_($this->semStack[$stackPos-(5-3)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            153 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(7-3)], $this->semStack[$stackPos-(7-5)][0], ['keyVar' => null, 'byRef' => $this->semStack[$stackPos-(7-5)][1], 'stmts' => $this->semStack[$stackPos-(7-7)]], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            },
            154 => function ($stackPos) {
                 $this->semValue = new Stmt\Foreach_($this->semStack[$stackPos-(9-3)], $this->semStack[$stackPos-(9-7)][0], ['keyVar' => $this->semStack[$stackPos-(9-5)], 'byRef' => $this->semStack[$stackPos-(9-7)][1], 'stmts' => $this->semStack[$stackPos-(9-9)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            },
            155 => function ($stackPos) {
                 $this->semValue = new Stmt\Declare_($this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            156 => function ($stackPos) {
                 $this->semValue = new Stmt\TryCatch($this->semStack[$stackPos-(6-3)], $this->semStack[$stackPos-(6-5)], $this->semStack[$stackPos-(6-6)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes); $this->checkTryCatch($this->semValue);
            },
            157 => function ($stackPos) {
                 $this->semValue = new Stmt\Throw_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            158 => function ($stackPos) {
                 $this->semValue = new Stmt\Goto_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            159 => function ($stackPos) {
                 $this->semValue = new Stmt\Label($this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            160 => function ($stackPos) {
                 $this->semValue = new Stmt\Expression($this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            161 => function ($stackPos) {
                 $this->semValue = array(); /* means: no statement */
            },
            162 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            163 => function ($stackPos) {
                 $startAttributes = $this->startAttributeStack[$stackPos-(1-1)]; if (isset($startAttributes['comments'])) { $this->semValue = new Stmt\Nop($startAttributes + $this->endAttributes); } else { $this->semValue = null; };
            if ($this->semValue === null) $this->semValue = array(); /* means: no statement */
            },
            164 => function ($stackPos) {
                 $this->semValue = array();
            },
            165 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            166 => function ($stackPos) {
                 $this->semValue = new Stmt\Catch_(array($this->semStack[$stackPos-(8-3)]), $this->semStack[$stackPos-(8-4)], $this->semStack[$stackPos-(8-7)], $this->startAttributeStack[$stackPos-(8-1)] + $this->endAttributes);
            },
            167 => function ($stackPos) {
                 $this->semValue = null;
            },
            168 => function ($stackPos) {
                 $this->semValue = new Stmt\Finally_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            169 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            170 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            171 => function ($stackPos) {
                 $this->semValue = false;
            },
            172 => function ($stackPos) {
                 $this->semValue = true;
            },
            173 => function ($stackPos) {
                 $this->semValue = false;
            },
            174 => function ($stackPos) {
                 $this->semValue = true;
            },
            175 => function ($stackPos) {
                 $this->semValue = false;
            },
            176 => function ($stackPos) {
                 $this->semValue = true;
            },
            177 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            178 => function ($stackPos) {
                 $this->semValue = new Node\Identifier($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            179 => function ($stackPos) {
                 $this->semValue = new Stmt\Function_($this->semStack[$stackPos-(10-3)], ['byRef' => $this->semStack[$stackPos-(10-2)], 'params' => $this->semStack[$stackPos-(10-5)], 'returnType' => $this->semStack[$stackPos-(10-7)], 'stmts' => $this->semStack[$stackPos-(10-9)]], $this->startAttributeStack[$stackPos-(10-1)] + $this->endAttributes);
            },
            180 => function ($stackPos) {
                 $this->semValue = new Stmt\Class_($this->semStack[$stackPos-(7-2)], ['type' => $this->semStack[$stackPos-(7-1)], 'extends' => $this->semStack[$stackPos-(7-3)], 'implements' => $this->semStack[$stackPos-(7-4)], 'stmts' => $this->semStack[$stackPos-(7-6)]], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes);
            $this->checkClass($this->semValue, $stackPos-(7-2));
            },
            181 => function ($stackPos) {
                 $this->semValue = new Stmt\Interface_($this->semStack[$stackPos-(6-2)], ['extends' => $this->semStack[$stackPos-(6-3)], 'stmts' => $this->semStack[$stackPos-(6-5)]], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            $this->checkInterface($this->semValue, $stackPos-(6-2));
            },
            182 => function ($stackPos) {
                 $this->semValue = new Stmt\Trait_($this->semStack[$stackPos-(5-2)], ['stmts' => $this->semStack[$stackPos-(5-4)]], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            183 => function ($stackPos) {
                 $this->semValue = 0;
            },
            184 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_ABSTRACT;
            },
            185 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_FINAL;
            },
            186 => function ($stackPos) {
                 $this->semValue = null;
            },
            187 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            188 => function ($stackPos) {
                 $this->semValue = array();
            },
            189 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            190 => function ($stackPos) {
                 $this->semValue = array();
            },
            191 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            192 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            193 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            194 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            195 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            196 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            197 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            198 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            199 => function ($stackPos) {
                 $this->semValue = null;
            },
            200 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            201 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            202 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            203 => function ($stackPos) {
                 $this->semValue = new Stmt\DeclareDeclare($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            204 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            205 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-3)];
            },
            206 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            207 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(5-3)];
            },
            208 => function ($stackPos) {
                 $this->semValue = array();
            },
            209 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            210 => function ($stackPos) {
                 $this->semValue = new Stmt\Case_($this->semStack[$stackPos-(4-2)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            211 => function ($stackPos) {
                 $this->semValue = new Stmt\Case_(null, $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            212 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            213 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            214 => function ($stackPos) {
                 $this->semValue = is_array($this->semStack[$stackPos-(1-1)]) ? $this->semStack[$stackPos-(1-1)] : array($this->semStack[$stackPos-(1-1)]);
            },
            215 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-2)];
            },
            216 => function ($stackPos) {
                 $this->semValue = array();
            },
            217 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            218 => function ($stackPos) {
                 $this->semValue = new Stmt\ElseIf_($this->semStack[$stackPos-(3-2)], is_array($this->semStack[$stackPos-(3-3)]) ? $this->semStack[$stackPos-(3-3)] : array($this->semStack[$stackPos-(3-3)]), $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            219 => function ($stackPos) {
                 $this->semValue = array();
            },
            220 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            221 => function ($stackPos) {
                 $this->semValue = new Stmt\ElseIf_($this->semStack[$stackPos-(4-2)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            222 => function ($stackPos) {
                 $this->semValue = null;
            },
            223 => function ($stackPos) {
                 $this->semValue = new Stmt\Else_(is_array($this->semStack[$stackPos-(2-2)]) ? $this->semStack[$stackPos-(2-2)] : array($this->semStack[$stackPos-(2-2)]), $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            224 => function ($stackPos) {
                 $this->semValue = null;
            },
            225 => function ($stackPos) {
                 $this->semValue = new Stmt\Else_($this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            226 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)], false);
            },
            227 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(2-2)], true);
            },
            228 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)], false);
            },
            229 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            230 => function ($stackPos) {
                 $this->semValue = array();
            },
            231 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            232 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            233 => function ($stackPos) {
                 $this->semValue = new Node\Param($this->semStack[$stackPos-(4-4)], null, $this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-2)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes); $this->checkParam($this->semValue);
            },
            234 => function ($stackPos) {
                 $this->semValue = new Node\Param($this->semStack[$stackPos-(6-4)], $this->semStack[$stackPos-(6-6)], $this->semStack[$stackPos-(6-1)], $this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-3)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes); $this->checkParam($this->semValue);
            },
            235 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            236 => function ($stackPos) {
                 $this->semValue = new Node\Identifier('array', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            237 => function ($stackPos) {
                 $this->semValue = new Node\Identifier('callable', $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            238 => function ($stackPos) {
                 $this->semValue = null;
            },
            239 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            240 => function ($stackPos) {
                 $this->semValue = null;
            },
            241 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-2)];
            },
            242 => function ($stackPos) {
                 $this->semValue = array();
            },
            243 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            244 => function ($stackPos) {
                 $this->semValue = array(new Node\Arg($this->semStack[$stackPos-(3-2)], false, false, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes));
            },
            245 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            246 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            247 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(1-1)], false, false, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            248 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(2-2)], true, false, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            249 => function ($stackPos) {
                 $this->semValue = new Node\Arg($this->semStack[$stackPos-(2-2)], false, true, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            250 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            251 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            252 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            253 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            254 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            255 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            256 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            257 => function ($stackPos) {
                 $this->semValue = new Stmt\StaticVar($this->semStack[$stackPos-(1-1)], null, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            258 => function ($stackPos) {
                 $this->semValue = new Stmt\StaticVar($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            259 => function ($stackPos) {
                 if ($this->semStack[$stackPos-(2-2)] !== null) { $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)]; }
            },
            260 => function ($stackPos) {
                 $this->semValue = array();
            },
            261 => function ($stackPos) {
                 $startAttributes = $this->lookaheadStartAttributes; if (isset($startAttributes['comments'])) { $nop = new Stmt\Nop($this->createCommentNopAttributes($startAttributes['comments'])); } else { $nop = null; };
            if ($nop !== null) { $this->semStack[$stackPos-(1-1)][] = $nop; } $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            262 => function ($stackPos) {
                 $this->semValue = new Stmt\Property($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes); $this->checkProperty($this->semValue, $stackPos-(3-1));
            },
            263 => function ($stackPos) {
                 $this->semValue = new Stmt\ClassConst($this->semStack[$stackPos-(3-2)], 0, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            264 => function ($stackPos) {
                 $this->semValue = new Stmt\ClassMethod($this->semStack[$stackPos-(9-4)], ['type' => $this->semStack[$stackPos-(9-1)], 'byRef' => $this->semStack[$stackPos-(9-3)], 'params' => $this->semStack[$stackPos-(9-6)], 'returnType' => $this->semStack[$stackPos-(9-8)], 'stmts' => $this->semStack[$stackPos-(9-9)]], $this->startAttributeStack[$stackPos-(9-1)] + $this->endAttributes);
            $this->checkClassMethod($this->semValue, $stackPos-(9-1));
            },
            265 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUse($this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            266 => function ($stackPos) {
                 $this->semValue = array();
            },
            267 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            268 => function ($stackPos) {
                 $this->semValue = array();
            },
            269 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            270 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Precedence($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            271 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(5-1)][0], $this->semStack[$stackPos-(5-1)][1], $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-4)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            272 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], $this->semStack[$stackPos-(4-3)], null, $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            273 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], null, $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            274 => function ($stackPos) {
                 $this->semValue = new Stmt\TraitUseAdaptation\Alias($this->semStack[$stackPos-(4-1)][0], $this->semStack[$stackPos-(4-1)][1], null, $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            275 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)]);
            },
            276 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            277 => function ($stackPos) {
                 $this->semValue = array(null, $this->semStack[$stackPos-(1-1)]);
            },
            278 => function ($stackPos) {
                 $this->semValue = null;
            },
            279 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            280 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            281 => function ($stackPos) {
                 $this->semValue = 0;
            },
            282 => function ($stackPos) {
                 $this->semValue = 0;
            },
            283 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            284 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            285 => function ($stackPos) {
                 $this->checkModifier($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $this->semValue = $this->semStack[$stackPos-(2-1)] | $this->semStack[$stackPos-(2-2)];
            },
            286 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PUBLIC;
            },
            287 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PROTECTED;
            },
            288 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_PRIVATE;
            },
            289 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_STATIC;
            },
            290 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_ABSTRACT;
            },
            291 => function ($stackPos) {
                 $this->semValue = Stmt\Class_::MODIFIER_FINAL;
            },
            292 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            293 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            294 => function ($stackPos) {
                 $this->semValue = new Node\VarLikeIdentifier(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            295 => function ($stackPos) {
                 $this->semValue = new Stmt\PropertyProperty($this->semStack[$stackPos-(1-1)], null, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            296 => function ($stackPos) {
                 $this->semValue = new Stmt\PropertyProperty($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            297 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            298 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            299 => function ($stackPos) {
                 $this->semValue = array();
            },
            300 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            301 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            302 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            303 => function ($stackPos) {
                 $this->semValue = new Expr\Assign($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            304 => function ($stackPos) {
                 $this->semValue = new Expr\AssignRef($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            305 => function ($stackPos) {
                 $this->semValue = new Expr\AssignRef($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            306 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            307 => function ($stackPos) {
                 $this->semValue = new Expr\Clone_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            308 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Plus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            309 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Minus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            310 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Mul($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            311 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Div($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            312 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Concat($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            313 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Mod($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            314 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            315 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            316 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\BitwiseXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            317 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\ShiftLeft($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            318 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\ShiftRight($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            319 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Pow($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            320 => function ($stackPos) {
                 $this->semValue = new Expr\AssignOp\Coalesce($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            321 => function ($stackPos) {
                 $this->semValue = new Expr\PostInc($this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            322 => function ($stackPos) {
                 $this->semValue = new Expr\PreInc($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            323 => function ($stackPos) {
                 $this->semValue = new Expr\PostDec($this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            324 => function ($stackPos) {
                 $this->semValue = new Expr\PreDec($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            325 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BooleanOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            326 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BooleanAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            327 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            328 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            329 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            330 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            331 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            332 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            333 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            334 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Concat($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            335 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Plus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            336 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Minus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            337 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Mul($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            338 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Div($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            339 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Mod($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            340 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\ShiftLeft($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            341 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\ShiftRight($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            342 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Pow($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            343 => function ($stackPos) {
                 $this->semValue = new Expr\UnaryPlus($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            344 => function ($stackPos) {
                 $this->semValue = new Expr\UnaryMinus($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            345 => function ($stackPos) {
                 $this->semValue = new Expr\BooleanNot($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            346 => function ($stackPos) {
                 $this->semValue = new Expr\BitwiseNot($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            347 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Identical($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            348 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\NotIdentical($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            349 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Equal($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            350 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\NotEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            351 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Spaceship($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            352 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Smaller($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            353 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\SmallerOrEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            354 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Greater($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            355 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\GreaterOrEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            356 => function ($stackPos) {
                 $this->semValue = new Expr\Instanceof_($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            357 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            358 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            359 => function ($stackPos) {
                 $this->semValue = new Expr\Ternary($this->semStack[$stackPos-(5-1)], $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            360 => function ($stackPos) {
                 $this->semValue = new Expr\Ternary($this->semStack[$stackPos-(4-1)], null, $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            361 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Coalesce($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            362 => function ($stackPos) {
                 $this->semValue = new Expr\Isset_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            363 => function ($stackPos) {
                 $this->semValue = new Expr\Empty_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            364 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            365 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE_ONCE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            366 => function ($stackPos) {
                 $this->semValue = new Expr\Eval_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            367 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            368 => function ($stackPos) {
                 $this->semValue = new Expr\Include_($this->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE_ONCE, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            369 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Int_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            370 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes;
            $attrs['kind'] = $this->getFloatCastKind($this->semStack[$stackPos-(2-1)]);
            $this->semValue = new Expr\Cast\Double($this->semStack[$stackPos-(2-2)], $attrs);
            },
            371 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\String_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            372 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Array_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            373 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Object_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            374 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Bool_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            375 => function ($stackPos) {
                 $this->semValue = new Expr\Cast\Unset_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            376 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes;
            $attrs['kind'] = strtolower($this->semStack[$stackPos-(2-1)]) === 'exit' ? Expr\Exit_::KIND_EXIT : Expr\Exit_::KIND_DIE;
            $this->semValue = new Expr\Exit_($this->semStack[$stackPos-(2-2)], $attrs);
            },
            377 => function ($stackPos) {
                 $this->semValue = new Expr\ErrorSuppress($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            378 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            379 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            380 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            381 => function ($stackPos) {
                 $this->semValue = new Expr\ShellExec($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            382 => function ($stackPos) {
                 $this->semValue = new Expr\Print_($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            383 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_(null, null, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            384 => function ($stackPos) {
                 $this->semValue = new Expr\YieldFrom($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            385 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => false, 'byRef' => $this->semStack[$stackPos-(10-2)], 'params' => $this->semStack[$stackPos-(10-4)], 'uses' => $this->semStack[$stackPos-(10-6)], 'returnType' => $this->semStack[$stackPos-(10-7)], 'stmts' => $this->semStack[$stackPos-(10-9)]], $this->startAttributeStack[$stackPos-(10-1)] + $this->endAttributes);
            },
            386 => function ($stackPos) {
                 $this->semValue = new Expr\Closure(['static' => true, 'byRef' => $this->semStack[$stackPos-(11-3)], 'params' => $this->semStack[$stackPos-(11-5)], 'uses' => $this->semStack[$stackPos-(11-7)], 'returnType' => $this->semStack[$stackPos-(11-8)], 'stmts' => $this->semStack[$stackPos-(11-10)]], $this->startAttributeStack[$stackPos-(11-1)] + $this->endAttributes);
            },
            387 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            388 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            389 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_($this->semStack[$stackPos-(2-2)], null, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            390 => function ($stackPos) {
                 $this->semValue = new Expr\Yield_($this->semStack[$stackPos-(4-4)], $this->semStack[$stackPos-(4-2)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            391 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes; $attrs['kind'] = Expr\Array_::KIND_LONG;
            $this->semValue = new Expr\Array_($this->semStack[$stackPos-(4-3)], $attrs);
            },
            392 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes; $attrs['kind'] = Expr\Array_::KIND_SHORT;
            $this->semValue = new Expr\Array_($this->semStack[$stackPos-(3-2)], $attrs);
            },
            393 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            394 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch(Scalar\String_::fromString($this->semStack[$stackPos-(4-1)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes), $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            395 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            396 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            397 => function ($stackPos) {
                 $this->semValue = array(new Stmt\Class_(null, ['type' => 0, 'extends' => $this->semStack[$stackPos-(7-3)], 'implements' => $this->semStack[$stackPos-(7-4)], 'stmts' => $this->semStack[$stackPos-(7-6)]], $this->startAttributeStack[$stackPos-(7-1)] + $this->endAttributes), $this->semStack[$stackPos-(7-2)]);
            $this->checkClass($this->semValue[0], -1);
            },
            398 => function ($stackPos) {
                 $this->semValue = new Expr\New_($this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            399 => function ($stackPos) {
                 list($class, $ctorArgs) = $this->semStack[$stackPos-(2-2)]; $this->semValue = new Expr\New_($class, $ctorArgs, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            400 => function ($stackPos) {
                 $this->semValue = array();
            },
            401 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(4-3)];
            },
            402 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            403 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            404 => function ($stackPos) {
                 $this->semValue = new Expr\ClosureUse($this->semStack[$stackPos-(2-2)], $this->semStack[$stackPos-(2-1)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            405 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            406 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            407 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            408 => function ($stackPos) {
                 $this->semValue = new Expr\StaticCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            409 => function ($stackPos) {
                 $this->semValue = new Expr\StaticCall($this->semStack[$stackPos-(6-1)], $this->semStack[$stackPos-(6-4)], $this->semStack[$stackPos-(6-6)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            410 => function ($stackPos) {
                 $this->semValue = $this->fixupPhp5StaticPropCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            411 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            412 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            413 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            414 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            415 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            416 => function ($stackPos) {
                 $this->semValue = new Name($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            417 => function ($stackPos) {
                 $this->semValue = new Name\FullyQualified(substr($this->semStack[$stackPos-(1-1)], 1), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            418 => function ($stackPos) {
                 $this->semValue = new Name\Relative(substr($this->semStack[$stackPos-(1-1)], 10), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            419 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            420 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            421 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            422 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            423 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            424 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            425 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            426 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            427 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            428 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            429 => function ($stackPos) {
                 $this->semValue = null;
            },
            430 => function ($stackPos) {
                 $this->semValue = null;
            },
            431 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            432 => function ($stackPos) {
                 $this->semValue = array();
            },
            433 => function ($stackPos) {
                 $this->semValue = array(new Scalar\EncapsedStringPart(Scalar\String_::parseEscapeSequences($this->semStack[$stackPos-(1-1)], '`', false), $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes));
            },
            434 => function ($stackPos) {
                 foreach ($this->semStack[$stackPos-(1-1)] as $s) { if ($s instanceof Node\Scalar\EncapsedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '`', false); } }; $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            435 => function ($stackPos) {
                 $this->semValue = array();
            },
            436 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            437 => function ($stackPos) {
                 $this->semValue = $this->parseLNumber($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes, true);
            },
            438 => function ($stackPos) {
                 $this->semValue = Scalar\DNumber::fromString($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            439 => function ($stackPos) {
                 $this->semValue = Scalar\String_::fromString($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes, false);
            },
            440 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Line($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            441 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\File($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            442 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Dir($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            443 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Class_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            444 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Trait_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            445 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Method($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            446 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Function_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            447 => function ($stackPos) {
                 $this->semValue = new Scalar\MagicConst\Namespace_($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            448 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes, $this->startAttributeStack[$stackPos-(3-3)] + $this->endAttributeStack[$stackPos-(3-3)], false);
            },
            449 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(2-1)], '', $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes, $this->startAttributeStack[$stackPos-(2-2)] + $this->endAttributeStack[$stackPos-(2-2)], false);
            },
            450 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            451 => function ($stackPos) {
                 $this->semValue = new Expr\ClassConstFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            452 => function ($stackPos) {
                 $this->semValue = new Expr\ConstFetch($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            453 => function ($stackPos) {
                 $this->semValue = new Expr\Array_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            454 => function ($stackPos) {
                 $this->semValue = new Expr\Array_($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            455 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            456 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BooleanOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            457 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BooleanAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            458 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            459 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            460 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\LogicalXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            461 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseOr($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            462 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            463 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseAnd($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            464 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\BitwiseXor($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            465 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Concat($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            466 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Plus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            467 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Minus($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            468 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Mul($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            469 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Div($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            470 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Mod($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            471 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\ShiftLeft($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            472 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\ShiftRight($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            473 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Pow($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            474 => function ($stackPos) {
                 $this->semValue = new Expr\UnaryPlus($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            475 => function ($stackPos) {
                 $this->semValue = new Expr\UnaryMinus($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            476 => function ($stackPos) {
                 $this->semValue = new Expr\BooleanNot($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            477 => function ($stackPos) {
                 $this->semValue = new Expr\BitwiseNot($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            478 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Identical($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            479 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\NotIdentical($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            480 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Equal($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            481 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\NotEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            482 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Smaller($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            483 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\SmallerOrEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            484 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\Greater($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            485 => function ($stackPos) {
                 $this->semValue = new Expr\BinaryOp\GreaterOrEqual($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            486 => function ($stackPos) {
                 $this->semValue = new Expr\Ternary($this->semStack[$stackPos-(5-1)], $this->semStack[$stackPos-(5-3)], $this->semStack[$stackPos-(5-5)], $this->startAttributeStack[$stackPos-(5-1)] + $this->endAttributes);
            },
            487 => function ($stackPos) {
                 $this->semValue = new Expr\Ternary($this->semStack[$stackPos-(4-1)], null, $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            488 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            489 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            490 => function ($stackPos) {
                 $this->semValue = new Expr\ConstFetch($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            491 => function ($stackPos) {
                 $this->semValue = new Expr\ClassConstFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            492 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            493 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            494 => function ($stackPos) {
                 $attrs = $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes; $attrs['kind'] = Scalar\String_::KIND_DOUBLE_QUOTED;
            foreach ($this->semStack[$stackPos-(3-2)] as $s) { if ($s instanceof Node\Scalar\EncapsedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '"', true); } }; $this->semValue = new Scalar\Encapsed($this->semStack[$stackPos-(3-2)], $attrs);
            },
            495 => function ($stackPos) {
                 $this->semValue = $this->parseDocString($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-2)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes, $this->startAttributeStack[$stackPos-(3-3)] + $this->endAttributeStack[$stackPos-(3-3)], true);
            },
            496 => function ($stackPos) {
                 $this->semValue = array();
            },
            497 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            498 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            499 => function ($stackPos) {
                $this->semValue = $this->semStack[$stackPos];
            },
            500 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            501 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            502 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(3-3)], $this->semStack[$stackPos-(3-1)], false, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            503 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(1-1)], null, false, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            504 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            505 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            506 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            507 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            508 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-5)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            509 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            510 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            511 => function ($stackPos) {
                 $this->semValue = new Expr\MethodCall($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            512 => function ($stackPos) {
                 $this->semValue = new Expr\FuncCall($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            513 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            514 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            515 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            516 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            517 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            518 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(2-2)], $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            519 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            520 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            521 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-4)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            522 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            523 => function ($stackPos) {
                 $var = substr($this->semStack[$stackPos-(1-1)], 1); $this->semValue = \is_string($var) ? new Node\VarLikeIdentifier($var, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes) : $var;
            },
            524 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            525 => function ($stackPos) {
                 $this->semValue = new Expr\StaticPropertyFetch($this->semStack[$stackPos-(6-1)], $this->semStack[$stackPos-(6-5)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            526 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            527 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            528 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            529 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            530 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            531 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            532 => function ($stackPos) {
                 $this->semValue = null;
            },
            533 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            534 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            535 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            536 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            537 => function ($stackPos) {
                 $this->semValue = new Expr\Error($this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes); $this->errorState = 2;
            },
            538 => function ($stackPos) {
                 $this->semValue = new Expr\List_($this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            539 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            540 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            541 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(1-1)], null, false, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            542 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(1-1)], null, false, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            543 => function ($stackPos) {
                 $this->semValue = null;
            },
            544 => function ($stackPos) {
                 $this->semValue = array();
            },
            545 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            546 => function ($stackPos) {
                 $this->semStack[$stackPos-(3-1)][] = $this->semStack[$stackPos-(3-3)]; $this->semValue = $this->semStack[$stackPos-(3-1)];
            },
            547 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            548 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(3-3)], $this->semStack[$stackPos-(3-1)], false, $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            549 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(1-1)], null, false, $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            550 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(4-4)], $this->semStack[$stackPos-(4-1)], true, $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            551 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(2-2)], null, true, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            552 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayItem($this->semStack[$stackPos-(2-2)], null, false, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes, true, $this->startAttributeStack[$stackPos-(2-1)] + $this->endAttributes);
            },
            553 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            554 => function ($stackPos) {
                 $this->semStack[$stackPos-(2-1)][] = $this->semStack[$stackPos-(2-2)]; $this->semValue = $this->semStack[$stackPos-(2-1)];
            },
            555 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(1-1)]);
            },
            556 => function ($stackPos) {
                 $this->semValue = array($this->semStack[$stackPos-(2-1)], $this->semStack[$stackPos-(2-2)]);
            },
            557 => function ($stackPos) {
                 $this->semValue = new Scalar\EncapsedStringPart($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            558 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            559 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
            560 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(4-1)], $this->semStack[$stackPos-(4-3)], $this->startAttributeStack[$stackPos-(4-1)] + $this->endAttributes);
            },
            561 => function ($stackPos) {
                 $this->semValue = new Expr\PropertyFetch($this->semStack[$stackPos-(3-1)], $this->semStack[$stackPos-(3-3)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            562 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            563 => function ($stackPos) {
                 $this->semValue = new Expr\Variable($this->semStack[$stackPos-(3-2)], $this->startAttributeStack[$stackPos-(3-1)] + $this->endAttributes);
            },
            564 => function ($stackPos) {
                 $this->semValue = new Expr\ArrayDimFetch($this->semStack[$stackPos-(6-2)], $this->semStack[$stackPos-(6-4)], $this->startAttributeStack[$stackPos-(6-1)] + $this->endAttributes);
            },
            565 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(3-2)];
            },
            566 => function ($stackPos) {
                 $this->semValue = new Scalar\String_($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            567 => function ($stackPos) {
                 $this->semValue = $this->parseNumString($this->semStack[$stackPos-(1-1)], $this->startAttributeStack[$stackPos-(1-1)] + $this->endAttributes);
            },
            568 => function ($stackPos) {
                 $this->semValue = $this->semStack[$stackPos-(1-1)];
            },
        ];
    }
}
