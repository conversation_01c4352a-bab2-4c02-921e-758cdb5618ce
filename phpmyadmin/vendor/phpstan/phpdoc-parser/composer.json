{"name": "phpstan/phpdoc-parser", "description": "PHPDoc parser with support for nullable, intersection and generic types", "license": "MIT", "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}, "config": {"platform": {"php": "7.4.6"}, "sort-packages": true, "allow-plugins": {"phpstan/extension-installer": true}}, "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "autoload-dev": {"psr-4": {"PHPStan\\PhpDocParser\\": ["tests/PHPStan"]}}, "minimum-stability": "dev", "prefer-stable": true}