{"name": "phpstan/phpstan-webmozart-assert", "type": "phpstan-extension", "description": "PHPStan webmozart/assert extension", "license": ["MIT"], "require": {"php": "^7.2 || ^8.0", "phpstan/phpstan": "^1.9.0"}, "require-dev": {"nikic/php-parser": "^4.13.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpstan-phpunit": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "webmozart/assert": "^1.11.0"}, "config": {"platform": {"php": "7.4.6"}, "sort-packages": true}, "extra": {"phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"PHPStan\\": "src/"}}, "autoload-dev": {"classmap": ["tests/"]}, "minimum-stability": "dev", "prefer-stable": true}