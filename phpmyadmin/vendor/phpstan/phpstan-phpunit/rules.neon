rules:
	- PHPStan\Rules\PHPUnit\AssertSameBooleanExpectedRule
	- PHPStan\Rules\PHPUnit\AssertSameNullExpectedRule
	- PHPStan\Rules\PHPUnit\AssertSameWithCountRule
	- PHPStan\Rules\PHPUnit\MockMethodCallRule
	- PHPStan\Rules\PHPUnit\ShouldCallParentMethodsRule

services:
	- class: PHPStan\Rules\PHPUnit\ClassCoversExistsRule
	- class: PHPStan\Rules\PHPUnit\ClassMethodCoversExistsRule
	-
		class: PHPStan\Rules\PHPUnit\DataProviderDeclarationRule
		arguments:
			checkFunctionNameCase: %checkFunctionNameCase%
			deprecationRulesInstalled: %deprecationRulesInstalled%
	- class: PHPStan\Rules\PHPUnit\NoMissingSpaceInClassAnnotationRule
	- class: PHPStan\Rules\PHPUnit\NoMissingSpaceInMethodAnnotationRule

conditionalTags:
	PHPStan\Rules\PHPUnit\ClassCoversExistsRule:
		phpstan.rules.rule: %featureToggles.bleedingEdge%
	PHPStan\Rules\PHPUnit\ClassMethodCoversExistsRule:
		phpstan.rules.rule: %featureToggles.bleedingEdge%
	PHPStan\Rules\PHPUnit\DataProviderDeclarationRule:
		phpstan.rules.rule: %featureToggles.bleedingEdge%
	PHPStan\Rules\PHPUnit\NoMissingSpaceInClassAnnotationRule:
		phpstan.rules.rule: %featureToggles.bleedingEdge%
	PHPStan\Rules\PHPUnit\NoMissingSpaceInMethodAnnotationRule:
		phpstan.rules.rule: %featureToggles.bleedingEdge%
