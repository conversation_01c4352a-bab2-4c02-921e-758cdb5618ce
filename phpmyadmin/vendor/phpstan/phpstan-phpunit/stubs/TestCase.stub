<?php

namespace PHPUnit\Framework;

use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\MockObject\MockBuilder;
use PHPUnit\Framework\MockObject\Stub;

class TestCase
{

	/**
	 * @template T
	 * @phpstan-param class-string<T> $originalClassName
	 * @phpstan-return Stub&T
	 */
	public function createStub($originalClassName) {}

	/**
	 * @template T
	 * @phpstan-param class-string<T> $originalClassName
	 * @phpstan-return MockObject&T
	 */
	public function createMock($originalClassName) {}

	/**
	 * @template T
	 * @phpstan-param class-string<T> $className
	 * @phpstan-return MockBuilder<T>
	 */
	public function getMockBuilder(string $className) {}

	/**
	 * @template T
	 * @phpstan-param class-string<T> $originalClassName
	 * @phpstan-return MockObject&T
	 */
	public function createConfiguredMock($originalClassName) {}

	/**
	 * @template T
	 * @phpstan-param class-string<T> $originalClassName
	 * @phpstan-param string[] $methods
	 * @phpstan-return MockObject&T
	 */
	public function createPartialMock($originalClassName, array $methods) {}

	/**
	 * @template T
	 * @phpstan-param class-string<T> $originalClassName
	 * @phpstan-return MockObject&T
	 */
	public function createTestProxy($originalClassName) {}

	/**
	 * @template T
	 * @phpstan-param class-string<T> $originalClassName
	 * @phpstan-param mixed[] $arguments
	 * @phpstan-param string $mockClassName
	 * @phpstan-param bool $callOriginalConstructor
	 * @phpstan-param bool $callOriginalClone
	 * @phpstan-param bool $callAutoload
	 * @phpstan-param string[] $mockedMethods
	 * @phpstan-param bool $cloneArguments
	 * @phpstan-return MockObject&T
	 */
	protected function getMockForAbstractClass($originalClassName, array $arguments = [], $mockClassName = '', $callOriginalConstructor = true, $callOriginalClone = true, $callAutoload = true, $mockedMethods = [], $cloneArguments = false) {}

	/**
	 * @template T
	 * @phpstan-param string $wsdlFile
	 * @phpstan-param class-string<T> $originalClassName
	 * @phpstan-param string $mockClassName
	 * @phpstan-param string[] $methods
	 * @phpstan-param bool $callOriginalConstructor
	 * @phpstan-param mixed[] $options
	 * @phpstan-return MockObject&T
	 */
	protected function getMockFromWsdl($wsdlFile, $originalClassName = '', $mockClassName = '', array $methods = [], $callOriginalConstructor = true, array $options = []) {}

}
