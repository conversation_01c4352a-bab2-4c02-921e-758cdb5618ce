<?php

namespace PHPUnit\Framework\MockObject;

use PHPUnit\Framework\TestCase;

/**
 * @template TMockedClass
 */
class MockBuilder
{

	/**
	 * @phpstan-param TestCase $testCase
	 * @phpstan-param class-string<TMockedClass> $type
	 */
	public function __construct(TestCase $testCase, $type) {}

	/**
	 * @phpstan-return MockObject&TMockedClass
	 */
	public function getMock() {}

	/**
	 * @phpstan-return MockObject&TMockedClass
	 */
	public function getMockForAbstractClass() {}

}
