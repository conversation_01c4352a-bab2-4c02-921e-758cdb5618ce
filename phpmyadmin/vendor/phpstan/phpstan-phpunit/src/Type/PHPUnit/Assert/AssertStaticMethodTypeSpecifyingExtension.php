<?php declare(strict_types = 1);

namespace PHPStan\Type\PHPUnit\Assert;

use <PERSON>p<PERSON><PERSON><PERSON>\Node\Expr\StaticCall;
use PHPStan\Analyser\Scope;
use P<PERSON><PERSON><PERSON>\Analyser\SpecifiedTypes;
use <PERSON><PERSON><PERSON><PERSON>\Analyser\TypeSpecifier;
use P<PERSON><PERSON>tan\Analyser\TypeSpecifierAwareExtension;
use PHPStan\Analyser\TypeSpecifierContext;
use PHPStan\Reflection\MethodReflection;
use PHPStan\Type\StaticMethodTypeSpecifyingExtension;

class AssertStaticMethodTypeSpecifyingExtension implements StaticMethodTypeSpecifyingExtension, TypeSpecifierAwareExtension
{

	/** @var TypeSpecifier */
	private $typeSpecifier;

	public function setTypeSpecifier(TypeSpecifier $typeSpecifier): void
	{
		$this->typeSpecifier = $typeSpecifier;
	}

	public function getClass(): string
	{
		return 'PHPUnit\Framework\Assert';
	}

	public function isStaticMethodSupported(
		MethodReflection $methodReflection,
		StaticCall $node,
		TypeSpecifierContext $context
	): bool
	{
		return AssertTypeSpecifyingExtensionHelper::isSupported(
			$methodReflection->getName(),
			$node->getArgs()
		);
	}

	public function specifyTypes(
		MethodReflection $functionReflection,
		StaticCall $node,
		Scope $scope,
		TypeSpecifierContext $context
	): SpecifiedTypes
	{
		return AssertTypeSpecifyingExtensionHelper::specifyTypes(
			$this->typeSpecifier,
			$scope,
			$functionReflection->getName(),
			$node->getArgs()
		);
	}

}
