<?php declare(strict_types = 1);

namespace PHPStan\Type\PHPUnit\Assert;

use <PERSON>p<PERSON><PERSON><PERSON>\Node\Expr\FuncCall;
use PHPStan\Analyser\Scope;
use P<PERSON><PERSON><PERSON>\Analyser\SpecifiedTypes;
use <PERSON><PERSON><PERSON><PERSON>\Analyser\TypeSpecifier;
use P<PERSON><PERSON>tan\Analyser\TypeSpecifierAwareExtension;
use PHPStan\Analyser\TypeSpecifierContext;
use PHPStan\Reflection\FunctionReflection;
use PHPStan\Type\FunctionTypeSpecifyingExtension;
use function strlen;
use function strpos;
use function substr;

class AssertFunctionTypeSpecifyingExtension implements FunctionTypeSpecifyingExtension, TypeSpecifierAwareExtension
{

	/** @var TypeSpecifier */
	private $typeSpecifier;

	public function setTypeSpecifier(TypeSpecifier $typeSpecifier): void
	{
		$this->typeSpecifier = $typeSpecifier;
	}

	public function isFunctionSupported(
		FunctionReflection $functionReflection,
		FuncCall $node,
		TypeSpecifierContext $context
	): bool
	{
		return AssertTypeSpecifyingExtensionHelper::isSupported(
			$this->trimName($functionReflection->getName()),
			$node->getArgs()
		);
	}

	public function specifyTypes(
		FunctionReflection $functionReflection,
		FuncCall $node,
		Scope $scope,
		TypeSpecifierContext $context
	): SpecifiedTypes
	{
		return AssertTypeSpecifyingExtensionHelper::specifyTypes(
			$this->typeSpecifier,
			$scope,
			$this->trimName($functionReflection->getName()),
			$node->getArgs()
		);
	}

	private function trimName(string $functionName): string
	{
		$prefix = 'PHPUnit\\Framework\\';
		if (strpos($functionName, $prefix) === 0) {
			return substr($functionName, strlen($prefix));
		}

		return $functionName;
	}

}
