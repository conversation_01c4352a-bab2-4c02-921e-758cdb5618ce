parameters:
	phpunit:
		convertUnionToIntersectionType: true
	additionalConstructors:
		- PHPUnit\Framework\TestCase::setUp
	earlyTerminatingMethodCalls:
		PHPUnit\Framework\Assert:
			- fail
			- markTestIncomplete
			- markTestSkipped
	stubFiles:
		- stubs/InvocationMocker.stub
		- stubs/MockBuilder.stub
		- stubs/MockObject.stub
		- stubs/Stub.stub
		- stubs/TestCase.stub
	exceptions:
		uncheckedExceptionRegexes:
			- '#^PHPUnit\\#'
			- '#^<PERSON><PERSON><PERSON>gmann\\#'

parametersSchema:
	phpunit: structure([
		convertUnionToIntersectionType: bool()
	])

services:
	-
		class: PHPStan\PhpDoc\PHPUnit\MockObjectTypeNodeResolverExtension
	-
		class: PHPStan\Type\PHPUnit\Assert\AssertFunctionTypeSpecifyingExtension
		tags:
			- phpstan.typeSpecifier.functionTypeSpecifyingExtension
	-
		class: PHPStan\Type\PHPUnit\Assert\AssertMethodTypeSpecifyingExtension
		tags:
			- phpstan.typeSpecifier.methodTypeSpecifyingExtension
	-
		class: PHPStan\Type\PHPUnit\Assert\AssertStaticMethodTypeSpecifyingExtension
		tags:
			- phpstan.typeSpecifier.staticMethodTypeSpecifyingExtension
	-
		class: PHPStan\Type\PHPUnit\InvocationMockerDynamicReturnTypeExtension
		tags:
			- phpstan.broker.dynamicMethodReturnTypeExtension
	-
		class: PHPStan\Type\PHPUnit\MockBuilderDynamicReturnTypeExtension
		tags:
			- phpstan.broker.dynamicMethodReturnTypeExtension
	-
		class: PHPStan\Type\PHPUnit\MockObjectDynamicReturnTypeExtension
		tags:
			- phpstan.broker.dynamicMethodReturnTypeExtension
	-
		class: PHPStan\Rules\PHPUnit\CoversHelper
	-
		class: PHPStan\Rules\PHPUnit\AnnotationHelper
	-
		class: PHPStan\Rules\PHPUnit\DataProviderHelper
		factory: @PHPStan\Rules\PHPUnit\DataProviderHelperFactory::create()
	-
		class: PHPStan\Rules\PHPUnit\DataProviderHelperFactory

conditionalTags:
	PHPStan\PhpDoc\PHPUnit\MockObjectTypeNodeResolverExtension:
		phpstan.phpDoc.typeNodeResolverExtension: %phpunit.convertUnionToIntersectionType%
