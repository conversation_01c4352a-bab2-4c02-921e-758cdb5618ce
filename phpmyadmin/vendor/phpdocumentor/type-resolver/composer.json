{"name": "phpdocumentor/type-resolver", "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*", "psalm/phar": "^4.8"}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "autoload-dev": {"psr-4": {"phpDocumentor\\Reflection\\": ["tests/unit", "tests/benchmark"]}}, "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}}