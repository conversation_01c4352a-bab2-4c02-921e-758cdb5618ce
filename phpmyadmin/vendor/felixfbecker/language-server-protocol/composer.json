{"name": "felixfbecker/language-server-protocol", "description": "PHP classes for the Language Server Protocol", "license": "ISC", "keywords": ["php", "language", "server", "microsoft"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.1"}, "require-dev": {"phpstan/phpstan": "*", "squizlabs/php_codesniffer": "^3.1", "vimeo/psalm": "^4.0"}, "autoload": {"psr-4": {"LanguageServerProtocol\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"phpstan": "phpstan analyse -c phpstan.neon --ansi --level=7 -vvv src", "psalm": "psalm", "phpcs": "phpcs", "phpcbf": "phpcbf"}}