# Language Server Protocol for PHP

[![packagist](https://img.shields.io/packagist/v/felixfbecker/language-server-protocol.svg)](https://packagist.org/packages/felixfbecker/language-server-protocol)
[![build](https://travis-ci.org/felixfbecker/php-language-server-protocol.svg?branch=master)](https://travis-ci.org/felixfbecker/php-language-server-protocol)
[![php](https://img.shields.io/badge/php-%3E%3D%207.0-8892BF.svg)](https://php.net/)
[![license](https://img.shields.io/packagist/l/felixfbecker/language-server-protocol.svg)](https://github.com/felixfbecker/php-language-server-protocol/blob/master/LICENSE)

Protocol classes for the [Language Server Protocol](https://microsoft.github.io/language-server-protocol/) in PHP

## Installation

```
composer require felixfbecker/language-server-protocol 
```

## Releases

Releases are done automatically in CI by analyzing commit messages.
Make sure to follow the [Conventional Commits Convention](https://www.conventionalcommits.org/en/v1.0.0-beta.2/).
