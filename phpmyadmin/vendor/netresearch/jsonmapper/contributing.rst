**************************************
How to add your features to JsonMapper
**************************************

If you want to add a new feature or a fix to this library, please consider these aspects:

- Respect the original code style and continue using it - it uses `PEAR Coding Standards`__.
- Pull requests fixing a bug should include a test case that illustrates the wrong behaviour.
- Pull requests adding a new feature should also include a test for the new feature.

 __ http://pear.php.net/manual/en/standards.php

Having test cases included in your pull request greatly helps reviewing it and will increase the chance of it being merged.
