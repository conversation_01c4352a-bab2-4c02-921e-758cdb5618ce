{"name": "league/uri-interfaces", "description": "Common interface for URI representation", "keywords": ["url", "uri", "rfc3986", "rfc3987"], "license": "MIT", "homepage": "http://github.com/thephpleague/uri-interfaces", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "funding": [{"type": "github", "url": "https://github.com/sponsors/nyamsprod"}], "require": {"php": "^7.2 || ^8.0", "ext-json": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.19", "phpstan/phpstan": "^0.12.90", "phpstan/phpstan-strict-rules": "^0.12.9", "phpstan/phpstan-phpunit": "^0.12.19", "phpunit/phpunit": "^8.5.15 || ^9.5"}, "autoload": {"psr-4": {"League\\Uri\\": "src/"}}, "scripts": {"phpunit": "phpunit --coverage-text", "phpcs": "php-cs-fixer fix --dry-run --diff -vvv --allow-risky=yes --ansi", "phpcs:fix": "php-cs-fixer fix -vvv --allow-risky=yes --ansi", "phpstan": "phpstan analyse -l max -c phpstan.neon src --ansi --memory-limit 192M", "test": ["@phpunit", "@phpstan", "@phpcs:fix"]}, "scripts-descriptions": {"phpunit": "Runs package test suite", "phpstan": "Runs complete codebase static analysis", "phpcs": "Runs coding style testing", "phpcs:fix": "Fix coding style issues", "test": "Runs all tests"}, "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "suggest": {"ext-intl": "to use the IDNA feature", "symfony/intl": "to use the IDNA feature via Symfony Polyfill"}}