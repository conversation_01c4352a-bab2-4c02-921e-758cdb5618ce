# Maintainers

There aren't many rules for maintainers of Slim-Psr7 to remember; what we have is listed here.

## We don't merge our own PRs

Our code is better if more than one set of eyes looks at it. Therefore we do not merge our own pull requests unless there is an exceptional circumstance. This helps to spot errors in the patch and also enables us to share information about the project around the maintainer team.

## PRs tagged `[WIP]` are not ready to be merged

Sometimes it's helpful to collaborate on a patch before it's ready to be merged. We use the `[WIP]` tag (for _Work in Progress_) in the title to mark these PRs. 

If a PR has `[WIP]` in its title, then it is not to be merged. The person who raised the PR will remove the `[WIP]` tag when they are ready for a full review and merge.

## Assign a merged PR to a milestone

By ensuring that all merged PRs are assigned to a milestone, we can easily find which PRs were in which release.
