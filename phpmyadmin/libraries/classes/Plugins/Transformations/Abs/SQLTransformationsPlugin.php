<?php
/**
 * Abstract class for the SQL transformations plugins
 */

declare(strict_types=1);

namespace Ph<PERSON><PERSON>y<PERSON><PERSON><PERSON>\Plugins\Transformations\Abs;

use PhpMyAdmin\FieldMetadata;
use PhpMyAdmin\Html\Generator;
use Php<PERSON>yAdmin\Plugins\TransformationsPlugin;

use function __;

/**
 * Provides common methods for all of the SQL transformations plugins.
 */
abstract class SQLTransformationsPlugin extends TransformationsPlugin
{
    /**
     * Gets the transformation description of the specific plugin
     *
     * @return string
     */
    public static function getInfo()
    {
        return __('Formats text as SQL query with syntax highlighting.');
    }

    /**
     * Does the actual work of each specific transformations plugin.
     *
     * @param string             $buffer  text to be transformed
     * @param array              $options transformation options
     * @param FieldMetadata|null $meta    meta information
     *
     * @return string
     */
    public function applyTransformation($buffer, array $options = [], ?FieldMetadata $meta = null)
    {
        return Generator::formatSql($buffer);
    }

    /* ~~~~~~~~~~~~~~~~~~~~ Get<PERSON> and <PERSON><PERSON> ~~~~~~~~~~~~~~~~~~~~ */

    /**
     * Gets the transformation name of the specific plugin
     *
     * @return string
     */
    public static function getName()
    {
        return 'S<PERSON>';
    }
}
