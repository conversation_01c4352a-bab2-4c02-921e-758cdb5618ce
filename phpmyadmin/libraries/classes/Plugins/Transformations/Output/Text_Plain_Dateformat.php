<?php
/**
 * Text Plain Date Format Transformations plugin for phpMyAdmin
 */

declare(strict_types=1);

namespace Php<PERSON>yAdmin\Plugins\Transformations\Output;

use PhpMyAdmin\Plugins\Transformations\Abs\DateFormatTransformationsPlugin;

/**
 * Handles the date format transformation for text plain
 */
class Text_Plain_Dateformat extends DateFormatTransformationsPlugin
{
    /**
     * Gets the plugin`s MIME type
     *
     * @return string
     */
    public static function getMIMEType()
    {
        return 'Text';
    }

    /**
     * Gets the plugin`s MIME subtype
     *
     * @return string
     */
    public static function getMIMESubtype()
    {
        return 'Plain';
    }
}
