<?php
/**
 * Image JPEG Link Transformations plugin for phpMyAdmin
 */

declare(strict_types=1);

namespace Php<PERSON>yAdmin\Plugins\Transformations\Output;

use PhpMyAdmin\Plugins\Transformations\Abs\ImageLinkTransformationsPlugin;

/**
 * Handles the link transformation for image jpeg
 */
class Image_JPEG_Link extends ImageLinkTransformationsPlugin
{
    /**
     * Gets the plugin`s MIME type
     *
     * @return string
     */
    public static function getMIMEType()
    {
        return 'Image';
    }

    /**
     * Gets the plugin`s MIME subtype
     *
     * @return string
     */
    public static function getMIMESubtype()
    {
        return 'JPEG';
    }
}
