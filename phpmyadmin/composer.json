{"name": "phpmyadmin/phpmyadmin", "type": "project", "description": "A web interface for MySQL and MariaDB", "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "mysql", "web"], "homepage": "https://www.phpmyadmin.net/", "support": {"forum": "https://www.phpmyadmin.net/support/", "issues": "https://github.com/phpmyadmin/phpmyadmin/issues", "wiki": "https://wiki.phpmyadmin.net/", "docs": "https://docs.phpmyadmin.net/", "source": "https://github.com/phpmyadmin/phpmyadmin"}, "license": "GPL-2.0-only", "authors": [{"name": "The phpMyAdmin Team", "email": "<EMAIL>", "homepage": "https://www.phpmyadmin.net/team/"}], "non-feature-branches": ["RELEASE_.*"], "autoload": {"psr-4": {"PhpMyAdmin\\": "libraries/classes"}, "files": ["vendor/phpmyadmin/motranslator/src/functions.php"], "exclude-from-classmap": ["/test/", "/vendor/tecnickcom/tcpdf/tcpdf_barcodes_*.php", "/vendor/tecnickcom/tcpdf/tcpdf_import.php", "/vendor/tecnickcom/tcpdf/tcpdf_parser.php", "/vendor/tecnickcom/tcpdf/include/tcpdf_filters.php", "/vendor/tecnickcom/tcpdf/include/barcodes"]}, "autoload-dev": {"psr-4": {"PhpMyAdmin\\Tests\\": "test/classes", "PhpMyAdmin\\Tests\\Selenium\\": "test/selenium/"}}, "repositories": [{"type": "composer", "url": "https://www.phpmyadmin.net"}], "require": {"php": "^7.2.5 || ^8.0", "ext-hash": "*", "ext-iconv": "*", "ext-json": "*", "ext-mysqli": "*", "ext-openssl": "*", "ext-pcre": "*", "ext-xml": "*", "composer/ca-bundle": "^1.2", "google/recaptcha": "^1.1", "nikic/fast-route": "^1.3", "paragonie/sodium_compat": "^1.17", "phpmyadmin/motranslator": "^5.0", "phpmyadmin/shapefile": "^3.0.1", "phpmyadmin/sql-parser": "^5.6.0", "phpmyadmin/twig-i18n-extension": "^4.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "slim/psr7": "^1.4", "symfony/config": "^5.2.3", "symfony/dependency-injection": "^5.2.3", "symfony/expression-language": "^5.2.3", "symfony/polyfill-ctype": "^1.17.0", "symfony/polyfill-mbstring": "^1.17.0", "symfony/polyfill-php80": "^1.16", "twig/twig": "^3.3.5", "webmozart/assert": "^1.10", "williamdes/mariadb-mysql-kbs": "^1.2"}, "conflict": {"bacon/bacon-qr-code": "<2.0", "pragmarx/google2fa-qrcode": "<2.1", "tecnickcom/tcpdf": "<6.4.4"}, "suggest": {"ext-curl": "Updates checking", "ext-opcache": "Better performance", "ext-zlib": "For gz import and export", "ext-bz2": "For bzip2 import and export", "ext-zip": "For zip import and export", "ext-gd2": "For image transformations", "ext-mbstring": "For best performance", "ext-sodium": "Better encryption performance", "tecnickcom/tcpdf": "For PDF support", "pragmarx/google2fa-qrcode": "^2.1 - For 2FA authentication", "bacon/bacon-qr-code": "^2.0 - For 2FA authentication", "code-lts/u2f-php-server": "For FIDO U2F authentication", "web-auth/webauthn-lib": "For better WebAuthn/FIDO2 authentication support"}, "require-dev": {"bacon/bacon-qr-code": "^2.0", "code-lts/u2f-php-server": "^1.2", "php-webdriver/webdriver": "^1.13", "phpmyadmin/coding-standard": "^3.0.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.4.8", "phpstan/phpstan-phpunit": "^1.0", "phpstan/phpstan-webmozart-assert": "^1.0", "phpunit/phpunit": "^8.5 || ^9.5", "pragmarx/google2fa-qrcode": "^2.1", "psalm/plugin-phpunit": "^0.16.1", "roave/security-advisories": "dev-latest", "squizlabs/php_codesniffer": "~3.6.0", "symfony/console": "^5.2.3", "tecnickcom/tcpdf": "^6.4.4", "vimeo/psalm": "^4.22", "web-auth/webauthn-lib": "^3.3"}, "extra": {"branch-alias": {"dev-master": "5.2.x-dev"}}, "scripts": {"phpcbf": "@php phpcbf", "phpcs": "@php phpcs", "phpstan": "@php phpstan analyse", "psalm": "@php psalm --no-diff", "phpunit": "@php phpunit --color=always", "test": ["@phpcs", "@phpstan", "@psalm", "@phpunit"], "update:baselines": ["@php phpstan analyse --generate-baseline", "@php psalm --set-baseline=psalm-baseline.xml"], "twig-lint": "@php scripts/console lint:twig --ansi --show-deprecations"}, "config": {"sort-packages": true, "discard-changes": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "phpstan/extension-installer": true, "composer/package-versions-deprecated": true}}, "version": "5.2.1"}