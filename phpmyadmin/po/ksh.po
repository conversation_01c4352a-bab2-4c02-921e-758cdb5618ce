# phpMyAdmin translation.
# Copyright (C) 2003 - 2014 phpMyAdmin devel team
# This file is distributed under the same license as the phpMyAdmin package.
# Automatically generated, 2014.
#
msgid ""
msgstr ""
"Project-Id-Version: phpMyAdmin 5.2.1-dev\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2023-01-21 15:49+0000\n"
"PO-Revision-Date: 2019-04-27 17:19+0000\n"
"Last-Translator: <PERSON> <william<PERSON>@wdes.fr>\n"
"Language-Team: Colognian <https://hosted.weblate.org/projects/phpmyadmin/"
"master/ksh/>\n"
"Language: ksh\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=n==0 ? 0 : n==1 ? 1 : 2;\n"
"X-Generator: Weblate 3.6.1\n"

#: libraries/advisory_rules_generic.php:9
msgid "Uptime below one day"
msgstr ""

#: libraries/advisory_rules_generic.php:12
msgid "Uptime is less than 1 day, performance tuning may not be accurate."
msgstr ""

#: libraries/advisory_rules_generic.php:14
msgid ""
"To have more accurate averages it is recommended to let the server run for "
"longer than a day before running this analyzer"
msgstr ""

#: libraries/advisory_rules_generic.php:17
#, php-format
msgid "The uptime is only %s"
msgstr ""

#: libraries/advisory_rules_generic.php:22
msgid "Questions below 1,000"
msgstr ""

#: libraries/advisory_rules_generic.php:26
msgid ""
"Fewer than 1,000 questions have been run against this server. The "
"recommendations may not be accurate."
msgstr ""

#: libraries/advisory_rules_generic.php:30
msgid ""
"Let the server run for a longer time until it has executed a greater amount "
"of queries."
msgstr ""

#: libraries/advisory_rules_generic.php:32
#, php-format
msgid "Current amount of Questions: %s"
msgstr ""

#: libraries/advisory_rules_generic.php:37
msgid "Percentage of slow queries"
msgstr ""

#: libraries/advisory_rules_generic.php:41
msgid ""
"There is a lot of slow queries compared to the overall amount of Queries."
msgstr ""

#: libraries/advisory_rules_generic.php:43
#: libraries/advisory_rules_generic.php:56
msgid ""
"You might want to increase {long_query_time} or optimize the queries listed "
"in the slow query log"
msgstr ""

#: libraries/advisory_rules_generic.php:45
#, php-format
msgid "The slow query rate should be below 5%%, your value is %s%%."
msgstr ""

#: libraries/advisory_rules_generic.php:50
msgid "Slow query rate"
msgstr ""

#: libraries/advisory_rules_generic.php:54
msgid ""
"There is a high percentage of slow queries compared to the server uptime."
msgstr ""

#: libraries/advisory_rules_generic.php:58
#, php-format
msgid ""
"You have a slow query rate of %s per hour, you should have less than 1%% per "
"hour."
msgstr ""

#: libraries/advisory_rules_generic.php:63
msgid "Long query time"
msgstr ""

#: libraries/advisory_rules_generic.php:67
msgid ""
"{long_query_time} is set to 10 seconds or more, thus only slow queries that "
"take above 10 seconds are logged."
msgstr ""

#: libraries/advisory_rules_generic.php:71
msgid ""
"It is suggested to set {long_query_time} to a lower value, depending on your "
"environment. Usually a value of 1-5 seconds is suggested."
msgstr ""

#: libraries/advisory_rules_generic.php:74
#, php-format
msgid "long_query_time is currently set to %ds."
msgstr ""

#: libraries/advisory_rules_generic.php:79
#: libraries/advisory_rules_generic.php:92
msgid "Slow query logging"
msgstr ""

#: libraries/advisory_rules_generic.php:83
#: libraries/advisory_rules_generic.php:96
msgid "The slow query log is disabled."
msgstr ""

#: libraries/advisory_rules_generic.php:85
msgid ""
"Enable slow query logging by setting {log_slow_queries} to 'ON'. This will "
"help troubleshooting badly performing queries."
msgstr ""

#: libraries/advisory_rules_generic.php:88
msgid "log_slow_queries is set to 'OFF'"
msgstr ""

#: libraries/advisory_rules_generic.php:98
msgid ""
"Enable slow query logging by setting {slow_query_log} to 'ON'. This will "
"help troubleshooting badly performing queries."
msgstr ""

#: libraries/advisory_rules_generic.php:101
msgid "slow_query_log is set to 'OFF'"
msgstr ""

#: libraries/advisory_rules_generic.php:106
msgid "Release Series"
msgstr ""

#: libraries/advisory_rules_generic.php:109
msgid "The MySQL server version less than 5.1."
msgstr ""

#: libraries/advisory_rules_generic.php:111
msgid ""
"You should upgrade, as MySQL 5.1 has improved performance, and MySQL 5.5 "
"even more so."
msgstr ""

#: libraries/advisory_rules_generic.php:113
#: libraries/advisory_rules_generic.php:127
#: libraries/advisory_rules_generic.php:138
#, php-format
msgid "Current version: %s"
msgstr ""

#: libraries/advisory_rules_generic.php:118
#: libraries/advisory_rules_generic.php:132
msgid "Minor Version"
msgstr ""

#: libraries/advisory_rules_generic.php:122
msgid "Version less than 5.1.30 (the first GA release of 5.1)."
msgstr ""

#: libraries/advisory_rules_generic.php:124
msgid ""
"You should upgrade, as recent versions of MySQL 5.1 have improved "
"performance and MySQL 5.5 even more so."
msgstr ""

#: libraries/advisory_rules_generic.php:136
msgid "Version less than 5.5.8 (the first GA release of 5.5)."
msgstr ""

#: libraries/advisory_rules_generic.php:137
msgid "You should upgrade, to a stable version of MySQL 5.5."
msgstr ""

#: libraries/advisory_rules_generic.php:143
#: libraries/advisory_rules_generic.php:156
msgid "Distribution"
msgstr ""

#: libraries/advisory_rules_generic.php:146
msgid "Version is compiled from source, not a MySQL official binary."
msgstr ""

#: libraries/advisory_rules_generic.php:148
msgid ""
"If you did not compile from source, you may be using a package modified by a "
"distribution. The MySQL manual only is accurate for official MySQL binaries, "
"not any package distributions (such as RedHat, Debian/Ubuntu etc)."
msgstr ""

#: libraries/advisory_rules_generic.php:152
msgid "'source' found in version_comment"
msgstr ""

#: libraries/advisory_rules_generic.php:159
msgid "The MySQL manual only is accurate for official MySQL binaries."
msgstr ""

#: libraries/advisory_rules_generic.php:161
msgid ""
"Percona documentation is at <a href=\"https://www.percona.com/software/"
"documentation/\">https://www.percona.com/software/documentation/</a>"
msgstr ""

#: libraries/advisory_rules_generic.php:164
msgid "'percona' found in version_comment"
msgstr ""

#: libraries/advisory_rules_generic.php:168
msgid "MySQL Architecture"
msgstr ""

#: libraries/advisory_rules_generic.php:172
msgid "MySQL is not compiled as a 64-bit package."
msgstr ""

#: libraries/advisory_rules_generic.php:174
msgid ""
"Your memory capacity is above 3 GiB (assuming the Server is on localhost), "
"so MySQL might not be able to access all of your memory. You might want to "
"consider installing the 64-bit version of MySQL."
msgstr ""

#: libraries/advisory_rules_generic.php:178
#, php-format
msgid "Available memory on this host: %s"
msgstr ""

#: libraries/advisory_rules_generic.php:184
msgid "Query caching method"
msgstr ""

#: libraries/advisory_rules_generic.php:188
msgid "Suboptimal caching method."
msgstr ""

#: libraries/advisory_rules_generic.php:190
msgid ""
"You are using the MySQL Query cache with a fairly high traffic database. It "
"might be worth considering to use <a href=\"https://dev.mysql.com/doc/"
"refman/5.6/en/ha-memcached.html\">memcached</a> instead of the MySQL Query "
"cache, especially if you have multiple replicas."
msgstr ""

#: libraries/advisory_rules_generic.php:196
#, php-format
msgid ""
"The query cache is enabled and the server receives %d queries per second. "
"This rule fires if there is more than 100 queries per second."
msgstr ""

#: libraries/advisory_rules_generic.php:204
msgid "Percentage of sorts that cause temporary tables"
msgstr ""

#: libraries/advisory_rules_generic.php:208
#: libraries/advisory_rules_generic.php:221
msgid "Too many sorts are causing temporary tables."
msgstr ""

#: libraries/advisory_rules_generic.php:210
#: libraries/advisory_rules_generic.php:223
msgid ""
"Consider increasing {sort_buffer_size} and/or {read_rnd_buffer_size}, "
"depending on your system memory limits."
msgstr ""

#: libraries/advisory_rules_generic.php:213
#, php-format
msgid ""
"%s%% of all sorts cause temporary tables, this value should be lower than "
"10%%."
msgstr ""

#: libraries/advisory_rules_generic.php:218
msgid "Rate of sorts that cause temporary tables"
msgstr ""

#: libraries/advisory_rules_generic.php:226
#, php-format
msgid ""
"Temporary tables average: %s, this value should be less than 1 per hour."
msgstr ""

#: libraries/advisory_rules_generic.php:231
msgid "Sort rows"
msgstr ""

#: libraries/advisory_rules_generic.php:234
msgid "There are lots of rows being sorted."
msgstr ""

#: libraries/advisory_rules_generic.php:236
msgid ""
"While there is nothing wrong with a high amount of row sorting, you might "
"want to make sure that the queries which require a lot of sorting use "
"indexed columns in the ORDER BY clause, as this will result in much faster "
"sorting."
msgstr ""

#: libraries/advisory_rules_generic.php:240
#, php-format
msgid "Sorted rows average: %s"
msgstr ""

#: libraries/advisory_rules_generic.php:246
msgid "Rate of joins without indexes"
msgstr ""

#: libraries/advisory_rules_generic.php:249
msgid "There are too many joins without indexes."
msgstr ""

#: libraries/advisory_rules_generic.php:251
msgid ""
"This means that joins are doing full table scans. Adding indexes for the "
"columns being used in the join conditions will greatly speed up table joins."
msgstr ""

#: libraries/advisory_rules_generic.php:254
#, php-format
msgid "Table joins average: %s, this value should be less than 1 per hour"
msgstr ""

#: libraries/advisory_rules_generic.php:259
msgid "Rate of reading first index entry"
msgstr ""

#: libraries/advisory_rules_generic.php:262
msgid "The rate of reading the first index entry is high."
msgstr ""

#: libraries/advisory_rules_generic.php:264
msgid ""
"This usually indicates frequent full index scans. Full index scans are "
"faster than table scans but require lots of CPU cycles in big tables, if "
"those tables that have or had high volumes of UPDATEs and DELETEs, running "
"'OPTIMIZE TABLE' might reduce the amount of and/or speed up full index "
"scans. Other than that full index scans can only be reduced by rewriting "
"queries."
msgstr ""

#: libraries/advisory_rules_generic.php:270
#, php-format
msgid "Index scans average: %s, this value should be less than 1 per hour"
msgstr ""

#: libraries/advisory_rules_generic.php:275
msgid "Rate of reading fixed position"
msgstr ""

#: libraries/advisory_rules_generic.php:278
msgid "The rate of reading data from a fixed position is high."
msgstr ""

#: libraries/advisory_rules_generic.php:280
msgid ""
"This indicates that many queries need to sort results and/or do a full table "
"scan, including join queries that do not use indexes. Add indexes where "
"applicable."
msgstr ""

#: libraries/advisory_rules_generic.php:283
#, php-format
msgid ""
"Rate of reading fixed position average: %s, this value should be less than 1 "
"per hour"
msgstr ""

#: libraries/advisory_rules_generic.php:288
msgid "Rate of reading next table row"
msgstr ""

#: libraries/advisory_rules_generic.php:291
msgid "The rate of reading the next table row is high."
msgstr ""

#: libraries/advisory_rules_generic.php:293
msgid ""
"This indicates that many queries are doing full table scans. Add indexes "
"where applicable."
msgstr ""

#: libraries/advisory_rules_generic.php:295
#, php-format
msgid ""
"Rate of reading next table row: %s, this value should be less than 1 per hour"
msgstr ""

#: libraries/advisory_rules_generic.php:301
msgid "Different tmp_table_size and max_heap_table_size"
msgstr ""

#: libraries/advisory_rules_generic.php:304
msgid "{tmp_table_size} and {max_heap_table_size} are not the same."
msgstr ""

#: libraries/advisory_rules_generic.php:306
msgid ""
"If you have deliberately changed one of either: The server uses the lower "
"value of either to determine the maximum size of in-memory tables. So if you "
"wish to increase the in-memory table limit you will have to increase the "
"other value as well."
msgstr ""

#: libraries/advisory_rules_generic.php:310
#, php-format
msgid "Current values are tmp_table_size: %s, max_heap_table_size: %s"
msgstr ""

#: libraries/advisory_rules_generic.php:316
msgid "Percentage of temp tables on disk"
msgstr ""

#: libraries/advisory_rules_generic.php:320
#: libraries/advisory_rules_generic.php:341
msgid ""
"Many temporary tables are being written to disk instead of being kept in "
"memory."
msgstr ""

#: libraries/advisory_rules_generic.php:322
msgid ""
"Increasing {max_heap_table_size} and {tmp_table_size} might help. However "
"some temporary tables are always being written to disk, independent of the "
"value of these variables. To eliminate these you will have to rewrite your "
"queries to avoid those conditions (Within a temporary table: Presence of a "
"BLOB or TEXT column or presence of a column bigger than 512 bytes) as "
"mentioned in the beginning of an <a href=\"https://www.facebook.com/note.php?"
"note_id=10150111255065841&comments\">Article by the Pythian Group</a>"
msgstr ""

#: libraries/advisory_rules_generic.php:331
#, php-format
msgid ""
"%s%% of all temporary tables are being written to disk, this value should be "
"below 25%%"
msgstr ""

#: libraries/advisory_rules_generic.php:337
msgid "Temp disk rate"
msgstr ""

#: libraries/advisory_rules_generic.php:343
msgid ""
"Increasing {max_heap_table_size} and {tmp_table_size} might help. However "
"some temporary tables are always being written to disk, independent of the "
"value of these variables. To eliminate these you will have to rewrite your "
"queries to avoid those conditions (Within a temporary table: Presence of a "
"BLOB or TEXT column or presence of a column bigger than 512 bytes) as "
"mentioned in the <a href=\"https://dev.mysql.com/doc/refman/8.0/en/internal-"
"temporary-tables.html\">MySQL Documentation</a>"
msgstr ""

#: libraries/advisory_rules_generic.php:352
#, php-format
msgid ""
"Rate of temporary tables being written to disk: %s, this value should be "
"less than 1 per hour"
msgstr ""

#: libraries/advisory_rules_generic.php:359
msgid "MyISAM key buffer size"
msgstr ""

#: libraries/advisory_rules_generic.php:362
msgid "Key buffer is not initialized. No MyISAM indexes will be cached."
msgstr ""

#: libraries/advisory_rules_generic.php:364
msgid ""
"Set {key_buffer_size} depending on the size of your MyISAM indexes. 64M is a "
"good start."
msgstr ""

#: libraries/advisory_rules_generic.php:366
msgid "key_buffer_size is 0"
msgstr ""

#: libraries/advisory_rules_generic.php:371
#, no-php-format
msgid "Max % MyISAM key buffer ever used"
msgstr ""

#: libraries/advisory_rules_generic.php:376
#: libraries/advisory_rules_generic.php:393
#, no-php-format
msgid "MyISAM key buffer (index cache) % used is low."
msgstr ""

#: libraries/advisory_rules_generic.php:378
#: libraries/advisory_rules_generic.php:395
msgid ""
"You may need to decrease the size of {key_buffer_size}, re-examine your "
"tables to see if indexes have been removed, or examine queries and "
"expectations about what indexes are being used."
msgstr ""

#: libraries/advisory_rules_generic.php:382
#, php-format
msgid ""
"max %% MyISAM key buffer ever used: %s%%, this value should be above 95%%"
msgstr ""

#: libraries/advisory_rules_generic.php:387
msgid "Percentage of MyISAM key buffer used"
msgstr ""

#: libraries/advisory_rules_generic.php:399
#, php-format
msgid "%% MyISAM key buffer used: %s%%, this value should be above 95%%"
msgstr ""

#: libraries/advisory_rules_generic.php:404
msgid "Percentage of index reads from memory"
msgstr ""

#: libraries/advisory_rules_generic.php:409
#, no-php-format
msgid "The % of indexes that use the MyISAM key buffer is low."
msgstr ""

#: libraries/advisory_rules_generic.php:410
msgid "You may need to increase {key_buffer_size}."
msgstr ""

#: libraries/advisory_rules_generic.php:411
#, php-format
msgid "Index reads from memory: %s%%, this value should be above 95%%"
msgstr ""

#: libraries/advisory_rules_generic.php:417
msgid "Rate of table open"
msgstr ""

#: libraries/advisory_rules_generic.php:420
msgid "The rate of opening tables is high."
msgstr ""

#: libraries/advisory_rules_generic.php:422
msgid ""
"Opening tables requires disk I/O which is costly. Increasing "
"{table_open_cache} might avoid this."
msgstr ""

#: libraries/advisory_rules_generic.php:424
#, php-format
msgid "Opened table rate: %s, this value should be less than 10 per hour"
msgstr ""

#: libraries/advisory_rules_generic.php:429
msgid "Percentage of used open files limit"
msgstr ""

#: libraries/advisory_rules_generic.php:433
msgid ""
"The number of open files is approaching the max number of open files. You "
"may get a \"Too many open files\" error."
msgstr ""

#: libraries/advisory_rules_generic.php:437
#: libraries/advisory_rules_generic.php:450
msgid ""
"Consider increasing {open_files_limit}, and check the error log when "
"restarting after changing {open_files_limit}."
msgstr ""

#: libraries/advisory_rules_generic.php:440
#, php-format
msgid ""
"The number of opened files is at %s%% of the limit. It should be below 85%%"
msgstr ""

#: libraries/advisory_rules_generic.php:445
msgid "Rate of open files"
msgstr ""

#: libraries/advisory_rules_generic.php:448
msgid "The rate of opening files is high."
msgstr ""

#: libraries/advisory_rules_generic.php:453
#, php-format
msgid "Opened files rate: %s, this value should be less than 5 per hour"
msgstr ""

#: libraries/advisory_rules_generic.php:459
#, no-php-format
msgid "Immediate table locks %"
msgstr ""

#: libraries/advisory_rules_generic.php:463
#: libraries/advisory_rules_generic.php:473
msgid "Too many table locks were not granted immediately."
msgstr ""

#: libraries/advisory_rules_generic.php:464
#: libraries/advisory_rules_generic.php:474
msgid "Optimize queries and/or use InnoDB to reduce lock wait."
msgstr ""

#: libraries/advisory_rules_generic.php:465
#, php-format
msgid "Immediate table locks: %s%%, this value should be above 95%%"
msgstr ""

#: libraries/advisory_rules_generic.php:470
msgid "Table lock wait rate"
msgstr ""

#: libraries/advisory_rules_generic.php:475
#, php-format
msgid "Table lock wait rate: %s, this value should be less than 1 per hour"
msgstr ""

#: libraries/advisory_rules_generic.php:480
msgid "Thread cache"
msgstr ""

#: libraries/advisory_rules_generic.php:483
msgid ""
"Thread cache is disabled, resulting in more overhead from new connections to "
"MySQL."
msgstr ""

#: libraries/advisory_rules_generic.php:484
msgid "Enable the thread cache by setting {thread_cache_size} > 0."
msgstr ""

#: libraries/advisory_rules_generic.php:485
msgid "The thread cache is set to 0"
msgstr ""

#: libraries/advisory_rules_generic.php:490
#, no-php-format
msgid "Thread cache hit rate %"
msgstr ""

#: libraries/advisory_rules_generic.php:494
msgid "Thread cache is not efficient."
msgstr ""

#: libraries/advisory_rules_generic.php:495
msgid "Increase {thread_cache_size}."
msgstr ""

#: libraries/advisory_rules_generic.php:496
#, php-format
msgid "Thread cache hitrate: %s%%, this value should be above 80%%"
msgstr ""

#: libraries/advisory_rules_generic.php:501
msgid "Threads that are slow to launch"
msgstr ""

#: libraries/advisory_rules_generic.php:505
msgid "There are too many threads that are slow to launch."
msgstr ""

#: libraries/advisory_rules_generic.php:507
msgid ""
"This generally happens in case of general system overload as it is pretty "
"simple operations. You might want to monitor your system load carefully."
msgstr ""

#: libraries/advisory_rules_generic.php:510
#, php-format
msgid "%s thread(s) took longer than %s seconds to start, it should be 0"
msgstr ""

#: libraries/advisory_rules_generic.php:515
msgid "Slow launch time"
msgstr ""

#: libraries/advisory_rules_generic.php:518
msgid "Slow_launch_time is above 2s."
msgstr ""

#: libraries/advisory_rules_generic.php:520
msgid ""
"Set {slow_launch_time} to 1s or 2s to correctly count threads that are slow "
"to launch."
msgstr ""

#: libraries/advisory_rules_generic.php:522
#, php-format
msgid "slow_launch_time is set to %s"
msgstr ""

#: libraries/advisory_rules_generic.php:528
msgid "Percentage of used connections"
msgstr ""

#: libraries/advisory_rules_generic.php:531
msgid ""
"The maximum amount of used connections is getting close to the value of "
"{max_connections}."
msgstr ""

#: libraries/advisory_rules_generic.php:533
msgid ""
"Increase {max_connections}, or decrease {wait_timeout} so that connections "
"that do not close database handlers properly get killed sooner. Make sure "
"the code closes database handlers properly."
msgstr ""

#: libraries/advisory_rules_generic.php:537
#, php-format
msgid ""
"Max_used_connections is at %s%% of max_connections, it should be below 80%%"
msgstr ""

#: libraries/advisory_rules_generic.php:542
msgid "Percentage of aborted connections"
msgstr ""

#: libraries/advisory_rules_generic.php:545
#: libraries/advisory_rules_generic.php:559
msgid "Too many connections are aborted."
msgstr ""

#: libraries/advisory_rules_generic.php:547
#: libraries/advisory_rules_generic.php:561
msgid ""
"Connections are usually aborted when they cannot be authorized. <a "
"href=\"https://www.percona.com/blog/2008/08/23/how-to-track-down-the-source-"
"of-aborted_connects/\">This article</a> might help you track down the source."
msgstr ""

#: libraries/advisory_rules_generic.php:551
#, php-format
msgid "%s%% of all connections are aborted. This value should be below 1%%"
msgstr ""

#: libraries/advisory_rules_generic.php:556
msgid "Rate of aborted connections"
msgstr ""

#: libraries/advisory_rules_generic.php:565
#, php-format
msgid ""
"Aborted connections rate is at %s, this value should be less than 1 per hour"
msgstr ""

#: libraries/advisory_rules_generic.php:570
msgid "Percentage of aborted clients"
msgstr ""

#: libraries/advisory_rules_generic.php:573
#: libraries/advisory_rules_generic.php:587
msgid "Too many clients are aborted."
msgstr ""

#: libraries/advisory_rules_generic.php:575
#: libraries/advisory_rules_generic.php:589
msgid ""
"Clients are usually aborted when they did not close their connection to "
"MySQL properly. This can be due to network issues or code not closing a "
"database handler properly. Check your network and code."
msgstr ""

#: libraries/advisory_rules_generic.php:579
#, php-format
msgid "%s%% of all clients are aborted. This value should be below 2%%"
msgstr ""

#: libraries/advisory_rules_generic.php:584
msgid "Rate of aborted clients"
msgstr ""

#: libraries/advisory_rules_generic.php:593
#, php-format
msgid "Aborted client rate is at %s, this value should be less than 1 per hour"
msgstr ""

#: libraries/advisory_rules_generic.php:599
msgid "Is InnoDB disabled?"
msgstr ""

#: libraries/advisory_rules_generic.php:603
msgid "You do not have InnoDB enabled."
msgstr ""

#: libraries/advisory_rules_generic.php:604
msgid "InnoDB is usually the better choice for table engines."
msgstr ""

#: libraries/advisory_rules_generic.php:605
msgid "have_innodb is set to 'value'"
msgstr ""

#: libraries/advisory_rules_generic.php:609
#: libraries/advisory_rules_generic.php:633
msgid "InnoDB log size"
msgstr ""

#: libraries/advisory_rules_generic.php:613
#: libraries/advisory_rules_generic.php:640
msgid ""
"The InnoDB log file size is not an appropriate size, in relation to the "
"InnoDB buffer pool."
msgstr ""

#: libraries/advisory_rules_generic.php:615
#: libraries/advisory_rules_generic.php:642
#, no-php-format
msgid ""
"Especially on a system with a lot of writes to InnoDB tables you should set "
"{innodb_log_file_size} to 25% of {innodb_buffer_pool_size}. However the "
"bigger this value, the longer the recovery time will be when database "
"crashes, so this value should not be set much higher than 256 MiB. Please "
"note however that you cannot simply change the value of this variable. You "
"need to shutdown the server, remove the InnoDB log files, set the new value "
"in my.cnf, start the server, then check the error logs if everything went "
"fine. See also <a href=\"https://mysqldatabaseadministration.blogspot."
"com/2007/01/increase-innodblogfilesize-proper-way.html\">this blog entry</a>"
msgstr ""

#: libraries/advisory_rules_generic.php:626
#: libraries/advisory_rules_generic.php:653
#, php-format
msgid ""
"Your InnoDB log size is at %s%% in relation to the InnoDB buffer pool size, "
"it should not be below 20%%"
msgstr ""

#: libraries/advisory_rules_generic.php:660
msgid "Max InnoDB log size"
msgstr ""

#: libraries/advisory_rules_generic.php:664
msgid "The InnoDB log file size is inadequately large."
msgstr ""

#: libraries/advisory_rules_generic.php:666
#, no-php-format
msgid ""
"It is usually sufficient to set {innodb_log_file_size} to 25% of the size of "
"{innodb_buffer_pool_size}. A very big {innodb_log_file_size} slows down the "
"recovery time after a database crash considerably. See also <a "
"href=\"https://www.percona.com/blog/2006/07/03/choosing-proper-"
"innodb_log_file_size/\">this Article</a>. You need to shutdown the server, "
"remove the InnoDB log files, set the new value in my.cnf, start the server, "
"then check the error logs if everything went fine. See also <a "
"href=\"https://mysqldatabaseadministration.blogspot.com/2007/01/increase-"
"innodblogfilesize-proper-way.html\">this blog entry</a>"
msgstr ""

#: libraries/advisory_rules_generic.php:676
#, php-format
msgid "Your absolute InnoDB log size is %s MiB"
msgstr ""

#: libraries/advisory_rules_generic.php:681
msgid "InnoDB buffer pool size"
msgstr ""

#: libraries/advisory_rules_generic.php:685
msgid "Your InnoDB buffer pool is fairly small."
msgstr ""

#: libraries/advisory_rules_generic.php:687
#, no-php-format
msgid ""
"The InnoDB buffer pool has a profound impact on performance for InnoDB "
"tables. Assign all your remaining memory to this buffer. For database "
"servers that use solely InnoDB as storage engine and have no other services "
"(e.g. a web server) running, you may set this as high as 80% of your "
"available memory. If that is not the case, you need to carefully assess the "
"memory consumption of your other services and non-InnoDB-Tables and set this "
"variable accordingly. If it is set too high, your system will start "
"swapping, which decreases performance significantly. See also <a "
"href=\"https://www.percona.com/blog/2007/11/03/choosing-"
"innodb_buffer_pool_size/\">this article</a>"
msgstr ""

#: libraries/advisory_rules_generic.php:697
#, php-format
msgid ""
"You are currently using %s%% of your memory for the InnoDB buffer pool. This "
"rule fires if you are assigning less than 60%%, however this might be "
"perfectly adequate for your system if you don't have much InnoDB tables or "
"other services running on the same machine."
msgstr ""

#: libraries/advisory_rules_generic.php:707
msgid "MyISAM concurrent inserts"
msgstr ""

#: libraries/advisory_rules_generic.php:710
msgid "Enable {concurrent_insert} by setting it to 1"
msgstr ""

#: libraries/advisory_rules_generic.php:712
msgid ""
"Setting {concurrent_insert} to 1 reduces contention between readers and "
"writers for a given table. See also <a href=\"https://dev.mysql.com/doc/"
"refman/5.5/en/concurrent-inserts.html\">MySQL Documentation</a>"
msgstr ""

#: libraries/advisory_rules_generic.php:716
msgid "concurrent_insert is set to 0"
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:9
msgid "Query cache disabled"
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:12
msgid "The query cache is not enabled."
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:14
msgid ""
"The query cache is known to greatly improve performance if configured "
"correctly. Enable it by setting {query_cache_size} to a 2 digit MiB value "
"and setting {query_cache_type} to 'ON'. <b>Note:</b> If you are using "
"memcached, ignore this recommendation."
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:18
msgid "query_cache_size is set to 0 or query_cache_type is set to 'OFF'"
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:23
#, no-php-format
msgid "Query cache efficiency (%)"
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:27
msgid "Query cache not running efficiently, it has a low hit rate."
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:28
msgid "Consider increasing {query_cache_limit}."
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:29
#, php-format
msgid "The current query cache hit rate of %s%% is below 20%%"
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:34
msgid "Query Cache usage"
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:39
#, no-php-format
msgid "Less than 80% of the query cache is being utilized."
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:41
msgid ""
"This might be caused by {query_cache_limit} being too low. Flushing the "
"query cache might help as well."
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:45
#, php-format
msgid ""
"The current ratio of free query cache memory to total query cache size is "
"%s%%. It should be above 80%%"
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:52
msgid "Query cache fragmentation"
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:56
msgid "The query cache is considerably fragmented."
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:58
msgid ""
"Severe fragmentation is likely to (further) increase Qcache_lowmem_prunes. "
"This might be caused by many Query cache low memory prunes due to "
"{query_cache_size} being too small. For a immediate but short lived fix you "
"can flush the query cache (might lock the query cache for a long time). "
"Carefully adjusting {query_cache_min_res_unit} to a lower value might help "
"too, e.g. you can set it to the average size of your queries in the cache "
"using this formula: (query_cache_size - qcache_free_memory) / "
"qcache_queries_in_cache"
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:66
#, php-format
msgid ""
"The cache is currently fragmented by %s%% , with 100%% fragmentation meaning "
"that the query cache is an alternating pattern of free and used blocks. This "
"value should be below 20%%."
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:73
msgid "Query cache low memory prunes"
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:77
msgid ""
"Cached queries are removed due to low query cache memory from the query "
"cache."
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:79
msgid ""
"You might want to increase {query_cache_size}, however keep in mind that the "
"overhead of maintaining the cache is likely to increase with its size, so do "
"this in small increments and monitor the results."
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:84
#, php-format
msgid ""
"The ratio of removed queries to inserted queries is %s%%. The lower this "
"value is, the better (This rules firing limit: 0.1%%)"
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:91
msgid "Query cache max size"
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:96
msgid ""
"The query cache size is above 128 MiB. Big query caches may cause "
"significant overhead that is required to maintain the cache."
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:100
msgid ""
"Depending on your environment, it might be performance increasing to reduce "
"this value."
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:102
#, php-format
msgid "Current query cache size: %s"
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:107
msgid "Query cache min result size"
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:111
msgid ""
"The max size of the result set in the query cache is the default of 1 MiB."
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:113
msgid ""
"Changing {query_cache_limit} (usually by increasing) may increase "
"efficiency. This variable determines the maximum size a query result may "
"have to be inserted into the query cache. If there are many query results "
"above 1 MiB that are well cacheable (many reads, little writes) then "
"increasing {query_cache_limit} will increase efficiency. Whereas in the case "
"of many query results being above 1 MiB that are not very well cacheable "
"(often invalidated due to table updates) increasing {query_cache_limit} "
"might reduce efficiency."
msgstr ""

#: libraries/advisory_rules_mysql_before80003.php:120
msgid "query_cache_limit is set to 1 MiB"
msgstr ""

#: libraries/classes/Advisor.php:234
#, php-format
msgid "Error when evaluating: %s"
msgstr ""

#: libraries/classes/Advisor.php:261
#, php-format
msgid "Failed evaluating precondition for rule '%s'."
msgstr ""

#: libraries/classes/Advisor.php:281
#, php-format
msgid "Failed calculating value for rule '%s'."
msgstr ""

#: libraries/classes/Advisor.php:300
#, php-format
msgid "Failed running test for rule '%s'."
msgstr ""

#: libraries/classes/Advisor.php:328
#, php-format
msgid "Failed formatting string for rule '%s'."
msgstr ""

#: libraries/classes/Advisor.php:413
msgid "per second"
msgstr ""

#: libraries/classes/Advisor.php:416
msgid "per minute"
msgstr ""

#: libraries/classes/Advisor.php:419
msgid "per hour"
msgstr ""

#: libraries/classes/Advisor.php:422
msgid "per day"
msgstr ""

#: libraries/classes/BrowseForeigners.php:200
msgid "Search:"
msgstr ""

#: libraries/classes/BrowseForeigners.php:206
#: libraries/classes/Controllers/JavaScriptMessagesController.php:324
#: libraries/classes/Controllers/Server/PrivilegesController.php:329
#: libraries/classes/Normalization.php:255
#: libraries/classes/Normalization.php:978 libraries/classes/Tracking.php:327
#: libraries/classes/Tracking.php:481
#: templates/columns_definitions/column_definitions_form.twig:43
#: templates/database/designer/main.twig:1102
#: templates/database/events/editor_form.twig:115
#: templates/database/operations/index.twig:19
#: templates/database/operations/index.twig:75
#: templates/database/operations/index.twig:185
#: templates/database/operations/index.twig:225
#: templates/database/routines/editor_form.twig:176
#: templates/database/routines/execute_form.twig:55
#: templates/database/search/main.twig:74
#: templates/database/triggers/editor_form.twig:69
#: templates/display/results/table.twig:190
#: templates/gis_data_editor_form.twig:211 templates/header_location.twig:18
#: templates/modals/create_view.twig:10
#: templates/modals/enum_set_editor.twig:10
#: templates/modals/index_dialog_modal.twig:10
#: templates/preferences/manage/main.twig:46
#: templates/preferences/manage/main.twig:108
#: templates/server/binlog/index.twig:34
#: templates/server/privileges/add_user.twig:36
#: templates/server/privileges/change_password.twig:70
#: templates/server/privileges/edit_routine_privileges.twig:102
#: templates/server/privileges/privileges_summary.twig:102
#: templates/server/privileges/privileges_table.twig:858
#: templates/server/privileges/user_properties.twig:149
#: templates/server/privileges/users_overview.twig:159
#: templates/server/replication/change_primary.twig:33
#: templates/server/replication/primary_add_replica_user.twig:81
#: templates/server/replication/primary_configuration.twig:30
#: templates/server/replication/replica_configuration.twig:16
#: templates/server/replication/replica_configuration.twig:88
#: templates/server/user_groups/edit_user_groups.twig:21
#: templates/sql/query.twig:147 templates/sql/query.twig:198
#: templates/table/find_replace/index.twig:62
#: templates/table/index_form.twig:245
#: templates/table/insert/actions_panel.twig:37
#: templates/table/insert/get_head_and_foot_of_insert_row_table.twig:15
#: templates/table/operations/index.twig:38
#: templates/table/operations/index.twig:86
#: templates/table/operations/index.twig:241
#: templates/table/operations/index.twig:328
#: templates/table/operations/index.twig:505
#: templates/table/operations/view.twig:20
#: templates/table/search/index.twig:172 templates/table/search/index.twig:196
#: templates/table/start_and_number_of_rows_fieldset.twig:15
#: templates/table/structure/display_structure.twig:343
#: templates/table/structure/display_structure.twig:454
#: templates/table/structure/display_structure.twig:572
#: templates/table/zoom_search/index.twig:152 templates/view_create.twig:116
msgid "Go"
msgstr ""

#: libraries/classes/BrowseForeigners.php:221
#: libraries/classes/BrowseForeigners.php:225
#: templates/database/data_dictionary/index.twig:73 templates/indexes.twig:16
#: templates/table/structure/display_structure.twig:475
#: templates/table/tracking/structure_snapshot_indexes.twig:5
msgid "Keyname"
msgstr ""

#: libraries/classes/BrowseForeigners.php:222
#: libraries/classes/BrowseForeigners.php:224
#: templates/server/engines/index.twig:14
#: templates/server/plugins/index.twig:27
#: templates/server/status/variables/index.twig:77
msgid "Description"
msgstr ""

#: libraries/classes/BrowseForeigners.php:321
#: libraries/classes/Language.php:204 libraries/classes/Pdf.php:81
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:496
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:530
#: templates/list_navigator.twig:4
msgid "Page number:"
msgstr ""

#: libraries/classes/BrowseForeigners.php:335
#: libraries/classes/Controllers/JavaScriptMessagesController.php:561
#: templates/display/results/table.twig:24
#: templates/server/privileges/initials_row.twig:17
#: templates/table/browse_foreigners/show_all.twig:4
msgid "Show all"
msgstr ""

#: libraries/classes/Charsets/Collation.php:170
#: libraries/classes/Charsets.php:176 libraries/classes/Charsets.php:177
msgid "Unknown"
msgstr ""

#: libraries/classes/Charsets/Collation.php:203
#: libraries/classes/Charsets/Collation.php:502
msgctxt "Collation"
msgid "German (phone book order)"
msgstr ""

#: libraries/classes/Charsets/Collation.php:207
#: libraries/classes/Charsets/Collation.php:499
msgctxt "Collation"
msgid "German (dictionary order)"
msgstr ""

#: libraries/classes/Charsets/Collation.php:216
#: libraries/classes/Charsets/Collation.php:577
msgctxt "Collation"
msgid "Spanish (traditional)"
msgstr ""

#: libraries/classes/Charsets/Collation.php:220
#: libraries/classes/Charsets/Collation.php:570
msgctxt "Collation"
msgid "Spanish (modern)"
msgstr ""

#: libraries/classes/Charsets/Collation.php:298
msgctxt "Collation variant"
msgid "case-insensitive"
msgstr ""

#: libraries/classes/Charsets/Collation.php:301
msgctxt "Collation variant"
msgid "case-sensitive"
msgstr ""

#: libraries/classes/Charsets/Collation.php:304
msgctxt "Collation variant"
msgid "accent-insensitive"
msgstr ""

#: libraries/classes/Charsets/Collation.php:307
msgctxt "Collation variant"
msgid "accent-sensitive"
msgstr ""

#: libraries/classes/Charsets/Collation.php:310
msgctxt "Collation variant"
msgid "kana-sensitive"
msgstr ""

#: libraries/classes/Charsets/Collation.php:314
msgctxt "Collation variant"
msgid "multi-level"
msgstr ""

#: libraries/classes/Charsets/Collation.php:317
msgctxt "Collation variant"
msgid "binary"
msgstr ""

#: libraries/classes/Charsets/Collation.php:320
msgctxt "Collation variant"
msgid "no-pad"
msgstr ""

#: libraries/classes/Charsets/Collation.php:339
msgctxt "Collation"
msgid "Binary"
msgstr ""

#: libraries/classes/Charsets/Collation.php:352
#: libraries/classes/Charsets/Collation.php:601
msgctxt "Collation"
msgid "Unicode"
msgstr ""

#: libraries/classes/Charsets/Collation.php:362
#: libraries/classes/Charsets/Collation.php:548
msgctxt "Collation"
msgid "West European"
msgstr ""

#: libraries/classes/Charsets/Collation.php:369
msgctxt "Collation"
msgid "Central European"
msgstr ""

#: libraries/classes/Charsets/Collation.php:374
#: libraries/classes/Charsets/Collation.php:555
msgctxt "Collation"
msgid "Russian"
msgstr ""

#: libraries/classes/Charsets/Collation.php:379
msgctxt "Collation"
msgid "Simplified Chinese"
msgstr ""

#: libraries/classes/Charsets/Collation.php:382
msgctxt "Collation"
msgid "Traditional Chinese"
msgstr ""

#: libraries/classes/Charsets/Collation.php:385
#: libraries/classes/Charsets/Collation.php:470
msgctxt "Collation"
msgid "Chinese"
msgstr ""

#: libraries/classes/Charsets/Collation.php:393
#: libraries/classes/Charsets/Collation.php:519
msgctxt "Collation"
msgid "Japanese"
msgstr ""

#: libraries/classes/Charsets/Collation.php:398
msgctxt "Collation"
msgid "Baltic"
msgstr ""

#: libraries/classes/Charsets/Collation.php:403
msgctxt "Collation"
msgid "Armenian"
msgstr ""

#: libraries/classes/Charsets/Collation.php:406
msgctxt "Collation"
msgid "Cyrillic"
msgstr ""

#: libraries/classes/Charsets/Collation.php:409
msgctxt "Collation"
msgid "Arabic"
msgstr ""

#: libraries/classes/Charsets/Collation.php:412
#: libraries/classes/Charsets/Collation.php:534
msgctxt "Collation"
msgid "Korean"
msgstr ""

#: libraries/classes/Charsets/Collation.php:415
msgctxt "Collation"
msgid "Hebrew"
msgstr ""

#: libraries/classes/Charsets/Collation.php:418
msgctxt "Collation"
msgid "Georgian"
msgstr ""

#: libraries/classes/Charsets/Collation.php:421
msgctxt "Collation"
msgid "Greek"
msgstr ""

#: libraries/classes/Charsets/Collation.php:424
msgctxt "Collation"
msgid "Czech-Slovak"
msgstr ""

#: libraries/classes/Charsets/Collation.php:427
#: libraries/classes/Charsets/Collation.php:593
msgctxt "Collation"
msgid "Ukrainian"
msgstr ""

#: libraries/classes/Charsets/Collation.php:430
#: libraries/classes/Charsets/Collation.php:589
msgctxt "Collation"
msgid "Turkish"
msgstr ""

#: libraries/classes/Charsets/Collation.php:433
#: libraries/classes/Charsets/Collation.php:581
msgctxt "Collation"
msgid "Swedish"
msgstr ""

#: libraries/classes/Charsets/Collation.php:436
#: libraries/classes/Charsets/Collation.php:585
msgctxt "Collation"
msgid "Thai"
msgstr ""

#: libraries/classes/Charsets/Collation.php:439
msgctxt "Collation"
msgid "Unknown"
msgstr ""

#: libraries/classes/Charsets/Collation.php:464
msgctxt "Collation"
msgid "Bulgarian"
msgstr ""

#: libraries/classes/Charsets/Collation.php:476
msgctxt "Collation"
msgid "Croatian"
msgstr ""

#: libraries/classes/Charsets/Collation.php:480
msgctxt "Collation"
msgid "Czech"
msgstr ""

#: libraries/classes/Charsets/Collation.php:484
msgctxt "Collation"
msgid "Danish"
msgstr ""

#: libraries/classes/Charsets/Collation.php:488
msgctxt "Collation"
msgid "English"
msgstr ""

#: libraries/classes/Charsets/Collation.php:492
msgctxt "Collation"
msgid "Esperanto"
msgstr ""

#: libraries/classes/Charsets/Collation.php:496
msgctxt "Collation"
msgid "Estonian"
msgstr ""

#: libraries/classes/Charsets/Collation.php:511
msgctxt "Collation"
msgid "Hungarian"
msgstr ""

#: libraries/classes/Charsets/Collation.php:515
msgctxt "Collation"
msgid "Icelandic"
msgstr ""

#: libraries/classes/Charsets/Collation.php:522
msgctxt "Collation"
msgid "Classical Latin"
msgstr ""

#: libraries/classes/Charsets/Collation.php:526
msgctxt "Collation"
msgid "Latvian"
msgstr ""

#: libraries/classes/Charsets/Collation.php:530
msgctxt "Collation"
msgid "Lithuanian"
msgstr ""

#: libraries/classes/Charsets/Collation.php:538
msgctxt "Collation"
msgid "Burmese"
msgstr ""

#: libraries/classes/Charsets/Collation.php:541
#, fuzzy
#| msgid "Versions"
msgctxt "Collation"
msgid "Persian"
msgstr "Väsjohne"

#: libraries/classes/Charsets/Collation.php:545
msgctxt "Collation"
msgid "Polish"
msgstr ""

#: libraries/classes/Charsets/Collation.php:552
msgctxt "Collation"
msgid "Romanian"
msgstr ""

#: libraries/classes/Charsets/Collation.php:559
msgctxt "Collation"
msgid "Sinhalese"
msgstr ""

#: libraries/classes/Charsets/Collation.php:563
msgctxt "Collation"
msgid "Slovak"
msgstr ""

#: libraries/classes/Charsets/Collation.php:567
msgctxt "Collation"
msgid "Slovenian"
msgstr ""

#: libraries/classes/Charsets/Collation.php:597
msgctxt "Collation"
msgid "Vietnamese"
msgstr ""

#: libraries/classes/Common.php:245
#, php-format
msgid "You should upgrade to %s %s or later."
msgstr ""

#: libraries/classes/Common.php:277
msgid "Error: Token mismatch"
msgstr ""

#: libraries/classes/Common.php:491
msgid ""
"Failed to set session cookie. Maybe you are using HTTP instead of HTTPS to "
"access phpMyAdmin."
msgstr ""

#: libraries/classes/Common.php:552
msgid ""
"You have enabled mbstring.func_overload in your PHP configuration. This "
"option is incompatible with phpMyAdmin and might cause some data to be "
"corrupted!"
msgstr ""

#: libraries/classes/Common.php:569
msgid ""
"The ini_get and/or ini_set functions are disabled in php.ini. phpMyAdmin "
"requires these functions!"
msgstr ""

#: libraries/classes/Common.php:580
msgid "GLOBALS overwrite attempt"
msgstr ""

#: libraries/classes/Common.php:590
msgid "possible exploit"
msgstr ""

#: libraries/classes/Config/Descriptions.php:69
#: libraries/classes/Config/Descriptions.php:70
#: libraries/classes/Config/Descriptions.php:71
msgid "Users cannot set a higher value"
msgstr ""

#: libraries/classes/Config/Descriptions.php:89
msgid ""
"If enabled, user can enter any MySQL server in login form for cookie auth."
msgstr ""

#: libraries/classes/Config/Descriptions.php:92
msgid ""
"Restricts the MySQL servers the user can enter when a login to an arbitrary "
"MySQL server is enabled by matching the IP or hostname of the MySQL server "
"to the given regular expression."
msgstr ""

#: libraries/classes/Config/Descriptions.php:97
msgid ""
"Enabling this allows a page located on a different domain to call phpMyAdmin "
"inside a frame, and is a potential [strong]security hole[/strong] allowing "
"cross-frame scripting (XSS) attacks."
msgstr ""

#: libraries/classes/Config/Descriptions.php:102
msgid ""
"Secret passphrase used for encrypting cookies in [kbd]cookie[/kbd] "
"authentication."
msgstr ""

#: libraries/classes/Config/Descriptions.php:104
msgid "Enable bzip2 compression for import operations."
msgstr ""

#: libraries/classes/Config/Descriptions.php:105
msgid "Enter the URL for your reCAPTCHA v2 compatible API."
msgstr ""

#: libraries/classes/Config/Descriptions.php:106
msgid ""
"Enter the Content-Security-Policy snippet for your reCAPTCHA v2 compatible "
"API."
msgstr ""

#: libraries/classes/Config/Descriptions.php:107
msgid "Enter the request parameter used by your reCAPTCHA v2 compatible API."
msgstr ""

#: libraries/classes/Config/Descriptions.php:108
msgid "Enter the response parameter used by your reCAPTCHA v2 compatible API."
msgstr ""

#: libraries/classes/Config/Descriptions.php:109
msgid "Enter your public key for the reCAPTCHA service on your domain."
msgstr ""

#: libraries/classes/Config/Descriptions.php:110
msgid "Enter your private key for your domain reCAPTCHA service."
msgstr ""

#: libraries/classes/Config/Descriptions.php:111
msgid "Enter your siteverify URL for your reCAPTCHA service."
msgstr ""

#: libraries/classes/Config/Descriptions.php:113
msgid ""
"Defines which type of editing controls should be used for CHAR and VARCHAR "
"columns; [kbd]input[/kbd] - allows limiting of input length, [kbd]textarea[/"
"kbd] - allows newlines in columns."
msgstr ""

#: libraries/classes/Config/Descriptions.php:118
msgid ""
"Use user-friendly editor for editing SQL queries (CodeMirror) with syntax "
"highlighting and line numbers."
msgstr ""

#: libraries/classes/Config/Descriptions.php:123
msgid ""
"Find any errors in the query before executing it. Requires CodeMirror to be "
"enabled."
msgstr ""

#: libraries/classes/Config/Descriptions.php:126
msgid ""
"Defines the minimum size for input fields generated for CHAR and VARCHAR "
"columns."
msgstr ""

#: libraries/classes/Config/Descriptions.php:129
msgid ""
"Defines the maximum size for input fields generated for CHAR and VARCHAR "
"columns."
msgstr ""

#: libraries/classes/Config/Descriptions.php:132
msgid ""
"Compress gzip exports on the fly without the need for much memory; if you "
"encounter problems with created gzip files disable this feature."
msgstr ""

#: libraries/classes/Config/Descriptions.php:136
msgid ""
"Whether a warning (\"Are your really sure…\") should be displayed when "
"you're about to lose data."
msgstr ""

#: libraries/classes/Config/Descriptions.php:139
msgid "Autocomplete of the table and column names in the SQL queries."
msgstr ""

#: libraries/classes/Config/Descriptions.php:142
#: libraries/classes/Config/Descriptions.php:146
#: libraries/classes/Config/Descriptions.php:150
#: libraries/classes/Config/Descriptions.php:154
#: libraries/classes/Config/Descriptions.php:158
#: libraries/classes/Config/Descriptions.php:162
#: libraries/classes/Config/Descriptions.php:166
#: libraries/classes/Config/Descriptions.php:170
#: libraries/classes/Config/Descriptions.php:174
msgid ""
"Values for options list for default transformations. These will be "
"overwritten if transformation is filled in at table structure page."
msgstr ""

#: libraries/classes/Config/Descriptions.php:178
msgid ""
"Disable the table maintenance mass operations, like optimizing or repairing "
"the selected tables of a database."
msgstr ""

#: libraries/classes/Config/Descriptions.php:182
msgid ""
"Set the number of seconds a script is allowed to run ([kbd]0[/kbd] for no "
"limit)."
msgstr ""

#: libraries/classes/Config/Descriptions.php:184
#: libraries/classes/Config/Descriptions.php:728
#: libraries/classes/Plugins/Export/ExportSql.php:345
msgid "Exclude definition of current user"
msgstr ""

#: libraries/classes/Config/Descriptions.php:186
msgid ""
"Sort order for items in a foreign-key dropdown box; [kbd]content[/kbd] is "
"the referenced data, [kbd]id[/kbd] is the key value."
msgstr ""

#: libraries/classes/Config/Descriptions.php:190
msgid ""
"Specify browser's title bar text. Refer to [doc@faq6-27]documentation[/doc] "
"for magic strings that can be used to get special values."
msgstr ""

#: libraries/classes/Config/Descriptions.php:195
msgid ""
"Please note that phpMyAdmin is just a user interface and its features do not "
"limit MySQL."
msgstr ""

#: libraries/classes/Config/Descriptions.php:198
msgid ""
"Advanced server configuration, do not change these options unless you know "
"what they are for."
msgstr ""

#: libraries/classes/Config/Descriptions.php:201
msgid ""
"Configure phpMyAdmin configuration storage to gain access to additional "
"features, see [doc@linked-tables]phpMyAdmin configuration storage[/doc] in "
"documentation."
msgstr ""

#: libraries/classes/Config/Descriptions.php:206
msgid ""
"Tracking of changes made in database. Requires the phpMyAdmin configuration "
"storage."
msgstr ""

#: libraries/classes/Config/Descriptions.php:209
msgid "Customize browse mode."
msgstr ""

#: libraries/classes/Config/Descriptions.php:210
#: libraries/classes/Config/Descriptions.php:211
#: libraries/classes/Config/Descriptions.php:218
#: libraries/classes/Config/Descriptions.php:224
#: libraries/classes/Config/Descriptions.php:225
#: libraries/classes/Config/Descriptions.php:230
msgid "Customize default options."
msgstr ""

#: libraries/classes/Config/Descriptions.php:212
msgid "Settings for phpMyAdmin developers."
msgstr ""

#: libraries/classes/Config/Descriptions.php:213
msgid "Customize edit mode."
msgstr ""

#: libraries/classes/Config/Descriptions.php:214
msgid "Customize default export options."
msgstr ""

#: libraries/classes/Config/Descriptions.php:215
msgid "Set some commonly used options."
msgstr ""

#: libraries/classes/Config/Descriptions.php:216
msgid "Customize default common import options."
msgstr ""

#: libraries/classes/Config/Descriptions.php:217
msgid "Set import and export directories and compression options."
msgstr ""

#: libraries/classes/Config/Descriptions.php:219
msgid "Databases display options."
msgstr ""

#: libraries/classes/Config/Descriptions.php:220
msgid "Customize appearance of the navigation panel."
msgstr ""

#: libraries/classes/Config/Descriptions.php:221
msgid "Customize the navigation tree."
msgstr ""

#: libraries/classes/Config/Descriptions.php:222
msgid "Servers display options."
msgstr ""

#: libraries/classes/Config/Descriptions.php:223
msgid "Tables display options."
msgstr ""

#: libraries/classes/Config/Descriptions.php:226
msgid "Settings that didn't fit anywhere else."
msgstr ""

#: libraries/classes/Config/Descriptions.php:227
msgid "Authentication settings."
msgstr ""

#: libraries/classes/Config/Descriptions.php:228
msgid "Enter server connection parameters."
msgstr ""

#: libraries/classes/Config/Descriptions.php:229
msgid "Customize links shown in SQL Query boxes."
msgstr ""

#: libraries/classes/Config/Descriptions.php:231
msgid "SQL queries settings."
msgstr ""

#: libraries/classes/Config/Descriptions.php:232
msgid "Customize startup page."
msgstr ""

#: libraries/classes/Config/Descriptions.php:233
msgid ""
"Choose which details to show in the database structure (list of tables)."
msgstr ""

#: libraries/classes/Config/Descriptions.php:234
msgid "Settings for the table structure (list of columns)."
msgstr ""

#: libraries/classes/Config/Descriptions.php:235
msgid "Choose how you want tabs to work."
msgstr ""

#: libraries/classes/Config/Descriptions.php:236
msgid "Customize text input fields."
msgstr ""

#: libraries/classes/Config/Descriptions.php:237
msgid "Customize default options"
msgstr ""

#: libraries/classes/Config/Descriptions.php:238
msgid "Disable some of the warnings shown by phpMyAdmin."
msgstr ""

#: libraries/classes/Config/Descriptions.php:240
msgid "Enable gzip compression for import and export operations."
msgstr ""

#: libraries/classes/Config/Descriptions.php:242
msgid ""
"If enabled, phpMyAdmin continues computing multiple-statement queries even "
"if one of the queries failed."
msgstr ""

#: libraries/classes/Config/Descriptions.php:246
msgid ""
"Allow interrupt of import in case script detects it is close to time limit. "
"This might be a good way to import large files, however it can break "
"transactions."
msgstr ""

#: libraries/classes/Config/Descriptions.php:251
msgid ""
"Default format; be aware that this list depends on location (database, "
"table) and only SQL is always available."
msgstr ""

#: libraries/classes/Config/Descriptions.php:254
#: libraries/classes/Config/Descriptions.php:255
msgid "Update data when duplicate keys found on import"
msgstr ""

#: libraries/classes/Config/Descriptions.php:256
msgid "Number of queries to skip from start."
msgstr ""

#: libraries/classes/Config/Descriptions.php:258
msgid ""
"If TRUE, logout deletes cookies for all servers; when set to FALSE, logout "
"only occurs for the current server. Setting this to FALSE makes it easy to "
"forget to log out from other servers when connected to multiple servers."
msgstr ""

#: libraries/classes/Config/Descriptions.php:263
msgid ""
"Define whether the previous login should be recalled or not in [kbd]cookie[/"
"kbd] authentication mode."
msgstr ""

#: libraries/classes/Config/Descriptions.php:267
msgid ""
"Defines how long (in seconds) a login cookie should be stored in browser. "
"The default of 0 means that it will be kept for the existing session only, "
"and will be deleted as soon as you close the browser window. This is "
"recommended for non-trusted environments."
msgstr ""

#: libraries/classes/Config/Descriptions.php:273
msgid "Maximum number of characters used when a SQL query is displayed."
msgstr ""

#: libraries/classes/Config/Descriptions.php:276
msgid ""
"The number of items that can be displayed on each page on the first level of "
"the navigation tree."
msgstr ""

#: libraries/classes/Config/Descriptions.php:279
msgid ""
"The number of items that can be displayed on each page of the navigation "
"tree."
msgstr ""

#: libraries/classes/Config/Descriptions.php:282
msgid ""
"Number of rows displayed when browsing a result set. If the result set "
"contains more rows, \"Previous\" and \"Next\" links will be shown."
msgstr ""

#: libraries/classes/Config/Descriptions.php:287
msgid ""
"The number of bytes a script is allowed to allocate, e.g. [kbd]32M[/kbd] "
"([kbd]-1[/kbd] for no limit and [kbd]0[/kbd] for no change)."
msgstr ""

#: libraries/classes/Config/Descriptions.php:291
msgid "In the navigation panel, replaces the database tree with a selector"
msgstr ""

#: libraries/classes/Config/Descriptions.php:294
msgid "Link with main panel by highlighting the current database or table."
msgstr ""

#: libraries/classes/Config/Descriptions.php:297
msgid ""
"Open the linked page in the main window ([code]main[/code]) or in a new one "
"([code]new[/code])."
msgstr ""

#: libraries/classes/Config/Descriptions.php:300
msgid ""
"Defines the minimum number of items (tables, views, routines and events) to "
"display a filter box."
msgstr ""

#: libraries/classes/Config/Descriptions.php:303
msgid ""
"Group items in the navigation tree (determined by the separator defined in "
"the Databases and Tables tabs above)."
msgstr ""

#: libraries/classes/Config/Descriptions.php:307
msgid ""
"Whether to offer the possibility of tree expansion in the navigation panel."
msgstr ""

#: libraries/classes/Config/Descriptions.php:310
msgid "Whether to show procedures under database in the navigation tree"
msgstr ""

#: libraries/classes/Config/Descriptions.php:313
msgid "Whether to expand single database in the navigation tree automatically."
msgstr ""

#: libraries/classes/Config/Descriptions.php:315
msgid "Set to 0 to collapse navigation panel."
msgstr ""

#: libraries/classes/Config/Descriptions.php:316
msgid "Show logo in navigation panel."
msgstr ""

#: libraries/classes/Config/Descriptions.php:317
msgid "URL where logo in the navigation panel will point to."
msgstr ""

#: libraries/classes/Config/Descriptions.php:318
msgid "Display server choice at the top of the navigation panel."
msgstr ""

#: libraries/classes/Config/Descriptions.php:319
msgid "String that separates databases into different tree levels."
msgstr ""

#: libraries/classes/Config/Descriptions.php:320
msgid "String that separates tables into different tree levels."
msgstr ""

#: libraries/classes/Config/Descriptions.php:321
msgid "Highlight server under the mouse cursor."
msgstr ""

#: libraries/classes/Config/Descriptions.php:322
msgid "Whether to show tables under database in the navigation tree"
msgstr ""

#: libraries/classes/Config/Descriptions.php:323
msgid "Whether to show views under database in the navigation tree"
msgstr ""

#: libraries/classes/Config/Descriptions.php:324
msgid "Whether to show functions under database in the navigation tree"
msgstr ""

#: libraries/classes/Config/Descriptions.php:325
msgid "Whether to show events under database in the navigation tree"
msgstr ""

#: libraries/classes/Config/Descriptions.php:327
msgid ""
"[kbd]SMART[/kbd] - i.e. descending order for columns of type TIME, DATE, "
"DATETIME and TIMESTAMP, ascending order otherwise."
msgstr ""

#: libraries/classes/Config/Descriptions.php:331
msgid ""
"Disable the default warning that is displayed on the database details "
"Structure page if any of the required tables for the phpMyAdmin "
"configuration storage could not be found."
msgstr ""

#: libraries/classes/Config/Descriptions.php:336
msgid ""
"Disable the default warning that is displayed on the Structure page if "
"column names in a table are reserved MySQL words."
msgstr ""

#: libraries/classes/Config/Descriptions.php:340
msgid ""
"Enable if you want DB-based query history (requires phpMyAdmin configuration "
"storage). If disabled, this utilizes JS-routines to display query history "
"(lost by window close)."
msgstr ""

#: libraries/classes/Config/Descriptions.php:345
msgid ""
"Sets the effective timezone; possibly different than the one from your "
"database server"
msgstr ""

#: libraries/classes/Config/Descriptions.php:348
msgid ""
"Leave blank for no [doc@bookmarks@]bookmark[/doc] support, suggested: "
"[kbd]pma__bookmark[/kbd]"
msgstr ""

#: libraries/classes/Config/Descriptions.php:351
msgid ""
"Leave blank for no column comments/media types, suggested: "
"[kbd]pma__column_info[/kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:354
msgid ""
"A special MySQL user configured with limited permissions, more information "
"available on [doc@linked-tables]documentation[/doc]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:358
msgid ""
"An alternate host to hold the configuration storage; leave blank to use the "
"already defined host."
msgstr ""

#: libraries/classes/Config/Descriptions.php:361
msgid ""
"An alternate port to connect to the host that holds the configuration "
"storage; leave blank to use the default port, or the already defined port, "
"if the controlhost equals host."
msgstr ""

#: libraries/classes/Config/Descriptions.php:366
msgid ""
"More information on [a@https://github.com/phpmyadmin/phpmyadmin/"
"issues/8970]phpMyAdmin issue tracker[/a] and [a@https://bugs.mysql."
"com/19588]MySQL Bugs[/a]"
msgstr ""

#: libraries/classes/Config/Descriptions.php:370
msgid ""
"Leave blank for no SQL query history support, suggested: [kbd]pma__history[/"
"kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:373
msgid ""
"Limits number of table preferences which are stored in database, the oldest "
"records are automatically removed."
msgstr ""

#: libraries/classes/Config/Descriptions.php:377
msgid ""
"Leave blank for no QBE saved searches support, suggested: "
"[kbd]pma__savedsearches[/kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:380
msgid ""
"Leave blank for no export template support, suggested: "
"[kbd]pma__export_templates[/kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:383
msgid ""
"Leave blank for no central columns support, suggested: "
"[kbd]pma__central_columns[/kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:386
msgid ""
"You can use MySQL wildcard characters (% and _), escape them if you want to "
"use their literal instances, i.e. use [kbd]'my\\_db'[/kbd] and not "
"[kbd]'my_db'[/kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:391
msgid ""
"Leave blank for no PDF schema support, suggested: [kbd]pma__pdf_pages[/kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:394
msgid ""
"Database used for relations, bookmarks, and PDF features. See [doc@linked-"
"tables]pmadb[/doc] for complete information. Leave blank for no support. "
"Suggested: [kbd]phpmyadmin[/kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:399
msgid ""
"Leave blank for no \"persistent\" recently used tables across sessions, "
"suggested: [kbd]pma__recent[/kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:403
msgid ""
"Leave blank for no \"persistent\" favorite tables across sessions, "
"suggested: [kbd]pma__favorite[/kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:407
msgid ""
"Leave blank for no [doc@relations@]relation-links[/doc] support, suggested: "
"[kbd]pma__relation[/kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:412
msgid ""
"See [doc@authentication-modes]authentication types[/doc] for an example."
msgstr ""

#: libraries/classes/Config/Descriptions.php:415
msgid ""
"Leave blank for no PDF schema support, suggested: [kbd]pma__table_coords[/"
"kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:418
msgid ""
"Table to describe the display columns, leave blank for no support; "
"suggested: [kbd]pma__table_info[/kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:422
msgid ""
"Leave blank for no \"persistent\" tables' UI preferences across sessions, "
"suggested: [kbd]pma__table_uiprefs[/kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:426
msgid ""
"Whether a DROP DATABASE IF EXISTS statement will be added as first line to "
"the log when creating a database."
msgstr ""

#: libraries/classes/Config/Descriptions.php:430
msgid ""
"Whether a DROP TABLE IF EXISTS statement will be added as first line to the "
"log when creating a table."
msgstr ""

#: libraries/classes/Config/Descriptions.php:434
msgid ""
"Whether a DROP VIEW IF EXISTS statement will be added as first line to the "
"log when creating a view."
msgstr ""

#: libraries/classes/Config/Descriptions.php:438
msgid "Defines the list of statements the auto-creation uses for new versions."
msgstr ""

#: libraries/classes/Config/Descriptions.php:441
msgid ""
"Leave blank for no SQL query tracking support, suggested: "
"[kbd]pma__tracking[/kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:444
msgid ""
"Whether the tracking mechanism creates versions for tables and views "
"automatically."
msgstr ""

#: libraries/classes/Config/Descriptions.php:447
msgid ""
"Leave blank for no user preferences storage in database, suggested: "
"[kbd]pma__userconfig[/kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:450
msgid ""
"Both this table and the user groups table are required to enable the "
"configurable menus feature; leaving either one of them blank will disable "
"this feature, suggested: [kbd]pma__users[/kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:455
msgid ""
"Both this table and the users table are required to enable the configurable "
"menus feature; leaving either one of them blank will disable this feature, "
"suggested: [kbd]pma__usergroups[/kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:460
msgid ""
"Leave blank to disable the feature to hide and show navigation items, "
"suggested: [kbd]pma__navigationhiding[/kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:464
msgid ""
"A user-friendly description of this server. Leave blank to display the "
"hostname instead."
msgstr ""

#: libraries/classes/Config/Descriptions.php:466
msgid "Leave blank if not used."
msgstr ""

#: libraries/classes/Config/Descriptions.php:467
msgid "Leave blank for defaults."
msgstr ""

#: libraries/classes/Config/Descriptions.php:468
msgid "HTTP Basic Auth Realm name to display when doing HTTP Auth."
msgstr ""

#: libraries/classes/Config/Descriptions.php:469
msgid "Authentication method to use."
msgstr ""

#: libraries/classes/Config/Descriptions.php:470
msgid "Compress connection to MySQL server."
msgstr ""

#: libraries/classes/Config/Descriptions.php:471
msgid "Hide databases matching regular expression (PCRE)."
msgstr ""

#: libraries/classes/Config/Descriptions.php:472
msgid "Hostname where MySQL server is running."
msgstr ""

#: libraries/classes/Config/Descriptions.php:473
#: libraries/classes/Config/Descriptions.php:477
msgid "Leave empty if not using config auth."
msgstr ""

#: libraries/classes/Config/Descriptions.php:474
msgid "Port on which MySQL server is listening, leave empty for default."
msgstr ""

#: libraries/classes/Config/Descriptions.php:475
msgid "Socket on which MySQL server is listening, leave empty for default."
msgstr ""

#: libraries/classes/Config/Descriptions.php:476
msgid "Enable SSL for connection to MySQL server."
msgstr ""

#: libraries/classes/Config/Descriptions.php:479
msgid ""
"Please note that enabling this has no effect with [kbd]config[/kbd] "
"authentication mode because the password is hard coded in the configuration "
"file; this does not limit the ability to execute the same command directly."
msgstr ""

#: libraries/classes/Config/Descriptions.php:484
msgid "Show or hide a column displaying the Creation timestamp for all tables."
msgstr ""

#: libraries/classes/Config/Descriptions.php:487
msgid ""
"Show or hide a column displaying the Last update timestamp for all tables."
msgstr ""

#: libraries/classes/Config/Descriptions.php:490
msgid ""
"Show or hide a column displaying the Last check timestamp for all tables."
msgstr ""

#: libraries/classes/Config/Descriptions.php:493
msgid ""
"Defines whether or not type fields should be initially displayed in edit/"
"insert mode."
msgstr ""

#: libraries/classes/Config/Descriptions.php:496
msgid ""
"Shows link to [a@https://www.php.net/manual/en/function.phpinfo.php]phpinfo()"
"[/a] output."
msgstr ""

#: libraries/classes/Config/Descriptions.php:498
msgid "Whether a user should be displayed a \"show all (rows)\" button."
msgstr ""

#: libraries/classes/Config/Descriptions.php:499
msgid "Show or hide a column displaying the comments for all tables."
msgstr ""

#: libraries/classes/Config/Descriptions.php:500
msgid "Show or hide a column displaying the charset for all tables."
msgstr ""

#: libraries/classes/Config/Descriptions.php:501
msgid "Display the function fields in edit/insert mode."
msgstr ""

#: libraries/classes/Config/Descriptions.php:502
msgid "Whether to show hint or not."
msgstr ""

#: libraries/classes/Config/Descriptions.php:503
msgid ""
"Defines whether SQL queries generated by phpMyAdmin should be displayed."
msgstr ""

#: libraries/classes/Config/Descriptions.php:504
msgid "Allow to display database and table statistics (e.g. space usage)."
msgstr ""

#: libraries/classes/Config/Descriptions.php:506
msgid ""
"Mark used tables and make it possible to show databases with locked tables."
msgstr ""

#: libraries/classes/Config/Descriptions.php:509
msgid ""
"Disable the default warning that is displayed on the main page if Suhosin is "
"detected."
msgstr ""

#: libraries/classes/Config/Descriptions.php:512
msgid ""
"Disable the default warning that is displayed on the main page if the value "
"of the PHP setting session.gc_maxlifetime is less than the value of "
"`LoginCookieValidity`."
msgstr ""

#: libraries/classes/Config/Descriptions.php:517
msgid ""
"Textarea size (columns) in edit mode, this value will be emphasized for SQL "
"query textareas (*2)."
msgstr ""

#: libraries/classes/Config/Descriptions.php:520
msgid ""
"Textarea size (rows) in edit mode, this value will be emphasized for SQL "
"query textareas (*2)."
msgstr ""

#: libraries/classes/Config/Descriptions.php:523
msgid ""
"Input proxies as [kbd]IP: trusted HTTP header[/kbd]. The following example "
"specifies that phpMyAdmin should trust a HTTP_X_FORWARDED_FOR (X-Forwarded-"
"For) header coming from the proxy *******:[br][kbd]*******: "
"HTTP_X_FORWARDED_FOR[/kbd]."
msgstr ""

#: libraries/classes/Config/Descriptions.php:529
msgid ""
"When disabled, users cannot set any of the options below, regardless of the "
"checkbox on the right."
msgstr ""

#: libraries/classes/Config/Descriptions.php:532
msgid ""
"The URL of the proxy to be used when retrieving the information about the "
"latest version of phpMyAdmin, or when submitting error reports. You need "
"this if the server where phpMyAdmin is installed does not have direct access "
"to the Internet. The format is: \"hostname:portnumber\"."
msgstr ""

#: libraries/classes/Config/Descriptions.php:538
msgid ""
"The username for authenticating with the proxy. By default, no "
"authentication is performed. If a username is supplied, Basic Authentication "
"will be performed. No other types of authentication are currently supported."
msgstr ""

#: libraries/classes/Config/Descriptions.php:544
msgid ""
"Queries are executed by pressing Enter (instead of Ctrl+Enter). New lines "
"will be inserted with Shift+Enter."
msgstr ""

#: libraries/classes/Config/Descriptions.php:548
msgid ""
"Enable Zero Configuration mode which lets you set up phpMyAdmin "
"configuration storage tables automatically."
msgstr ""

#: libraries/classes/Config/Descriptions.php:551
msgid "Highlight selected rows."
msgstr ""

#: libraries/classes/Config/Descriptions.php:552
msgid "Highlight row pointed by the mouse cursor."
msgstr ""

#: libraries/classes/Config/Descriptions.php:553
msgid "Number of columns for CHAR/VARCHAR textareas."
msgstr ""

#: libraries/classes/Config/Descriptions.php:554
msgid "Number of rows for CHAR/VARCHAR textareas."
msgstr ""

#: libraries/classes/Config/Descriptions.php:555
msgid ""
"Log SQL queries and their execution time, to be displayed in the console"
msgstr ""

#: libraries/classes/Config/Descriptions.php:556
msgid "Tab that is displayed when entering a database."
msgstr ""

#: libraries/classes/Config/Descriptions.php:557
msgid "Tab that is displayed when entering a server."
msgstr ""

#: libraries/classes/Config/Descriptions.php:558
msgid "Tab that is displayed when entering a table."
msgstr ""

#: libraries/classes/Config/Descriptions.php:559
msgid "Whether the table structure actions should be hidden."
msgstr ""

#: libraries/classes/Config/Descriptions.php:560
msgid "Whether column comments should be shown in table structure view"
msgstr ""

#: libraries/classes/Config/Descriptions.php:561
msgid "Show server listing as a list instead of a drop down."
msgstr ""

#: libraries/classes/Config/Descriptions.php:562
msgid "A dropdown will be used if fewer items are present."
msgstr ""

#: libraries/classes/Config/Descriptions.php:563
msgid "Default value for foreign key checks checkbox for some queries."
msgstr ""

#: libraries/classes/Config/Descriptions.php:564
msgid "Uncheck the checkbox to disable drag and drop import"
msgstr ""

#: libraries/classes/Config/Descriptions.php:565
msgid "How many rows can be inserted at one time."
msgstr ""

#: libraries/classes/Config/Descriptions.php:566
msgid ""
"Maximum number of characters shown in any non-numeric column on browse view."
msgstr ""

#: libraries/classes/Config/Descriptions.php:567
msgid "Define how long (in seconds) a login cookie is valid."
msgstr ""

#: libraries/classes/Config/Descriptions.php:568
msgid "Double size of textarea for LONGTEXT columns."
msgstr ""

#: libraries/classes/Config/Descriptions.php:569
msgid "Maximum number of databases displayed in database list."
msgstr ""

#: libraries/classes/Config/Descriptions.php:570
msgid "Maximum number of tables displayed in table list."
msgstr ""

#: libraries/classes/Config/Descriptions.php:571
msgid "Maximum number of recently used tables; set 0 to disable."
msgstr ""

#: libraries/classes/Config/Descriptions.php:572
msgid "Maximum number of favorite tables; set 0 to disable."
msgstr ""

#: libraries/classes/Config/Descriptions.php:573
msgid "These are Edit, Copy and Delete links."
msgstr ""

#: libraries/classes/Config/Descriptions.php:574
msgid "Whether to show row links even in the absence of a unique key."
msgstr ""

#: libraries/classes/Config/Descriptions.php:575
#: libraries/classes/Config/Descriptions.php:858
msgid "Disable shortcut keys"
msgstr ""

#: libraries/classes/Config/Descriptions.php:576
msgid "Use natural order for sorting table and database names."
msgstr ""

#: libraries/classes/Config/Descriptions.php:577
#: libraries/classes/Config/Descriptions.php:580
#: libraries/classes/Config/Descriptions.php:581
msgid "Use only icons, only text or both."
msgstr ""

#: libraries/classes/Config/Descriptions.php:578
msgid "Use GZip output buffering for increased speed in HTTP transfers."
msgstr ""

#: libraries/classes/Config/Descriptions.php:579
msgid "Use persistent connections to MySQL databases."
msgstr ""

#: libraries/classes/Config/Descriptions.php:582
msgid "Disallow BLOB and BINARY columns from editing."
msgstr ""

#: libraries/classes/Config/Descriptions.php:583
msgid "How many queries are kept in history."
msgstr ""

#: libraries/classes/Config/Descriptions.php:584
msgid "Select which functions will be used for character set conversion."
msgstr ""

#: libraries/classes/Config/Descriptions.php:585
msgid "When browsing tables, the sorting of each table is remembered."
msgstr ""

#: libraries/classes/Config/Descriptions.php:586
msgid "Default sort order for tables with a primary key."
msgstr ""

#: libraries/classes/Config/Descriptions.php:587
msgid ""
"Repeat the headers every X cells, [kbd]0[/kbd] deactivates this feature."
msgstr ""

#: libraries/classes/Config/Descriptions.php:588
msgid "For display Options"
msgstr ""

#: libraries/classes/Config/Descriptions.php:589
msgid "Directory where exports can be saved on server."
msgstr ""

#: libraries/classes/Config/Descriptions.php:590
msgid ""
"Defines whether the query box should stay on-screen after its submission."
msgstr ""

#: libraries/classes/Config/Descriptions.php:591
msgid "Title of browser window when a database is selected."
msgstr ""

#: libraries/classes/Config/Descriptions.php:592
msgid "Title of browser window when nothing is selected."
msgstr ""

#: libraries/classes/Config/Descriptions.php:593
msgid "Title of browser window when a server is selected."
msgstr ""

#: libraries/classes/Config/Descriptions.php:594
msgid "Title of browser window when a table is selected."
msgstr ""

#: libraries/classes/Config/Descriptions.php:595
msgid "Directory on server where you can upload files for import."
msgstr ""

#: libraries/classes/Config/Descriptions.php:596
msgid "Allow for searching inside the entire database."
msgstr ""

#: libraries/classes/Config/Descriptions.php:597
msgid "Enables check for latest version on main phpMyAdmin page."
msgstr ""

#: libraries/classes/Config/Descriptions.php:598
msgid "The password for authenticating with the proxy."
msgstr ""

#: libraries/classes/Config/Descriptions.php:599
msgid "Enable ZIP compression for import and export operations."
msgstr ""

#: libraries/classes/Config/Descriptions.php:600
msgid "Choose the default action when sending error reports."
msgstr ""

#: libraries/classes/Config/Descriptions.php:617
msgid "Allow login to any MySQL server"
msgstr ""

#: libraries/classes/Config/Descriptions.php:618
msgid "Restrict login to MySQL server"
msgstr ""

#: libraries/classes/Config/Descriptions.php:619
msgid "Allow third party framing"
msgstr ""

#: libraries/classes/Config/Descriptions.php:620
msgid "Show \"Drop database\" link to normal users"
msgstr ""

#: libraries/classes/Config/Descriptions.php:621
msgid "Blowfish secret"
msgstr ""

#: libraries/classes/Config/Descriptions.php:622
msgid "Row marker"
msgstr ""

#: libraries/classes/Config/Descriptions.php:623
msgid "Highlight pointer"
msgstr ""

#: libraries/classes/Config/Descriptions.php:624
msgid "Bzip2"
msgstr ""

#: libraries/classes/Config/Descriptions.php:625
msgid "CHAR columns editing"
msgstr ""

#: libraries/classes/Config/Descriptions.php:626
msgid "Enable CodeMirror"
msgstr ""

#: libraries/classes/Config/Descriptions.php:627
#, fuzzy
#| msgid "Table"
msgid "Enable linter"
msgstr "Tabäll"

#: libraries/classes/Config/Descriptions.php:628
msgid "Minimum size for input field"
msgstr ""

#: libraries/classes/Config/Descriptions.php:629
msgid "Maximum size for input field"
msgstr ""

#: libraries/classes/Config/Descriptions.php:630
msgid "CHAR textarea columns"
msgstr ""

#: libraries/classes/Config/Descriptions.php:631
msgid "CHAR textarea rows"
msgstr ""

#: libraries/classes/Config/Descriptions.php:632
msgid "Check config file permissions"
msgstr ""

#: libraries/classes/Config/Descriptions.php:633
msgid "Compress on the fly"
msgstr ""

#: libraries/classes/Config/Descriptions.php:634
msgid "Confirm DROP queries"
msgstr ""

#: libraries/classes/Config/Descriptions.php:635
#: templates/console/display.twig:11 templates/console/display.twig:64
msgid "Debug SQL"
msgstr ""

#: libraries/classes/Config/Descriptions.php:636
#: libraries/classes/Plugins/Schema/SchemaDia.php:66
#: libraries/classes/Plugins/Schema/SchemaPdf.php:76
msgid "Paper size"
msgstr ""

#: libraries/classes/Config/Descriptions.php:637
msgid "Default database tab"
msgstr ""

#: libraries/classes/Config/Descriptions.php:638
msgid "Default server tab"
msgstr ""

#: libraries/classes/Config/Descriptions.php:639
msgid "Default table tab"
msgstr ""

#: libraries/classes/Config/Descriptions.php:640
msgid "Enable autocomplete for table and column names"
msgstr ""

#: libraries/classes/Config/Descriptions.php:641
msgid "Show column comments"
msgstr ""

#: libraries/classes/Config/Descriptions.php:642
msgid "Hide table structure actions"
msgstr ""

#: libraries/classes/Config/Descriptions.php:643
msgid "Default transformations for Hex"
msgstr ""

#: libraries/classes/Config/Descriptions.php:644
msgid "Default transformations for Substring"
msgstr ""

#: libraries/classes/Config/Descriptions.php:645
msgid "Default transformations for Bool2Text"
msgstr ""

#: libraries/classes/Config/Descriptions.php:646
msgid "Default transformations for External"
msgstr ""

#: libraries/classes/Config/Descriptions.php:647
msgid "Default transformations for PreApPend"
msgstr ""

#: libraries/classes/Config/Descriptions.php:648
msgid "Default transformations for DateFormat"
msgstr ""

#: libraries/classes/Config/Descriptions.php:649
msgid "Default transformations for Inline"
msgstr ""

#: libraries/classes/Config/Descriptions.php:650
msgid "Default transformations for TextImageLink"
msgstr ""

#: libraries/classes/Config/Descriptions.php:651
msgid "Default transformations for TextLink"
msgstr ""

#: libraries/classes/Config/Descriptions.php:652
msgid "Display servers as a list"
msgstr ""

#: libraries/classes/Config/Descriptions.php:653
msgid "Disable multi table maintenance"
msgstr ""

#: libraries/classes/Config/Descriptions.php:654
msgid "Maximum execution time"
msgstr ""

#: libraries/classes/Config/Descriptions.php:655
msgid "Use [code]LOCK TABLES[/code] statement"
msgstr ""

#: libraries/classes/Config/Descriptions.php:656
msgid "Save as file"
msgstr ""

#: libraries/classes/Config/Descriptions.php:657
#: libraries/classes/Config/Descriptions.php:792
msgid "Character set of the file"
msgstr ""

#: libraries/classes/Config/Descriptions.php:658
#: libraries/classes/Config/Descriptions.php:674 templates/import.twig:165
#: templates/import.twig:167 templates/sql/query.twig:40
#: templates/table/structure/display_table_stats.twig:84
msgid "Format"
msgstr ""

#: libraries/classes/Config/Descriptions.php:659
msgid "Compression"
msgstr ""

#: libraries/classes/Config/Descriptions.php:660
#: libraries/classes/Config/Descriptions.php:667
#: libraries/classes/Config/Descriptions.php:675
#: libraries/classes/Config/Descriptions.php:679
#: libraries/classes/Config/Descriptions.php:692
#: libraries/classes/Config/Descriptions.php:694
#: libraries/classes/Config/Descriptions.php:741
#: libraries/classes/Plugins/Export/ExportCsv.php:88
#: libraries/classes/Plugins/Export/ExportExcel.php:61
#: libraries/classes/Plugins/Export/ExportHtmlword.php:85
#: libraries/classes/Plugins/Export/ExportOds.php:70
#: libraries/classes/Plugins/Export/ExportOdt.php:127
#: libraries/classes/Plugins/Export/ExportTexytext.php:79
msgid "Put columns names in the first row"
msgstr ""

#: libraries/classes/Config/Descriptions.php:661
#: libraries/classes/Config/Descriptions.php:794
#: libraries/classes/Config/Descriptions.php:800
#: libraries/classes/Plugins/Import/ImportCsv.php:666
msgid "Columns enclosed with"
msgstr ""

#: libraries/classes/Config/Descriptions.php:662
#: libraries/classes/Config/Descriptions.php:795
#: libraries/classes/Config/Descriptions.php:801
#: libraries/classes/Plugins/Import/ImportCsv.php:677
msgid "Columns escaped with"
msgstr ""

#: libraries/classes/Config/Descriptions.php:663
#: libraries/classes/Config/Descriptions.php:669
#: libraries/classes/Config/Descriptions.php:676
#: libraries/classes/Config/Descriptions.php:685
#: libraries/classes/Config/Descriptions.php:693
#: libraries/classes/Config/Descriptions.php:697
#: libraries/classes/Config/Descriptions.php:742
msgid "Replace NULL with"
msgstr ""

#: libraries/classes/Config/Descriptions.php:664
#: libraries/classes/Config/Descriptions.php:670
msgid "Remove CRLF characters within columns"
msgstr ""

#: libraries/classes/Config/Descriptions.php:665
#: libraries/classes/Config/Descriptions.php:798
#: libraries/classes/Config/Descriptions.php:805
#: libraries/classes/Plugins/Import/ImportCsv.php:651
msgid "Columns terminated with"
msgstr ""

#: libraries/classes/Config/Descriptions.php:666
#: libraries/classes/Config/Descriptions.php:793
#: libraries/classes/Plugins/Import/ImportCsv.php:684
msgid "Lines terminated with"
msgstr ""

#: libraries/classes/Config/Descriptions.php:668
msgid "Excel edition"
msgstr ""

#: libraries/classes/Config/Descriptions.php:671
msgid "Database name template"
msgstr ""

#: libraries/classes/Config/Descriptions.php:672
msgid "Server name template"
msgstr ""

#: libraries/classes/Config/Descriptions.php:673
msgid "Table name template"
msgstr ""

#: libraries/classes/Config/Descriptions.php:677
#: libraries/classes/Config/Descriptions.php:690
#: libraries/classes/Config/Descriptions.php:699
#: libraries/classes/Config/Descriptions.php:737
#: libraries/classes/Config/Descriptions.php:743
#: libraries/classes/Plugins/Export/ExportHtmlword.php:56
#: libraries/classes/Plugins/Export/ExportLatex.php:88
#: libraries/classes/Plugins/Export/ExportMediawiki.php:55
#: libraries/classes/Plugins/Export/ExportMediawiki.php:61
#: libraries/classes/Plugins/Export/ExportOdt.php:69
#: libraries/classes/Plugins/Export/ExportPdf.php:86
#: libraries/classes/Plugins/Export/ExportSql.php:237
#: libraries/classes/Plugins/Export/ExportTexytext.php:55
msgid "Dump table"
msgstr ""

#: libraries/classes/Config/Descriptions.php:678
#: libraries/classes/Plugins/Export/ExportLatex.php:79
msgid "Include table caption"
msgstr ""

#: libraries/classes/Config/Descriptions.php:680
#: libraries/classes/Config/Descriptions.php:695
#: libraries/classes/Config/Descriptions.php:731
#: libraries/classes/Plugins/Export/ExportHtmlword.php:386
#: libraries/classes/Plugins/Export/ExportLatex.php:551
#: libraries/classes/Plugins/Export/ExportOdt.php:495
#: libraries/classes/Plugins/Export/ExportTexytext.php:395
#: libraries/classes/Plugins/Export/Helpers/Pdf.php:548
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:685
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:708
#: templates/columns_definitions/table_fields_definitions.twig:54
#: templates/database/data_dictionary/index.twig:29
#: templates/table/structure/display_structure.twig:28
msgid "Comments"
msgstr ""

#: libraries/classes/Config/Descriptions.php:681
#: libraries/classes/Config/Descriptions.php:687
msgid "Table caption"
msgstr ""

#: libraries/classes/Config/Descriptions.php:682
#: libraries/classes/Config/Descriptions.php:688
msgid "Continued table caption"
msgstr ""

#: libraries/classes/Config/Descriptions.php:683
#: libraries/classes/Config/Descriptions.php:689
msgid "Label key"
msgstr ""

#: libraries/classes/Config/Descriptions.php:684
#: libraries/classes/Config/Descriptions.php:696
#: libraries/classes/Config/Descriptions.php:734
#: libraries/classes/Plugins/Export/ExportHtmlword.php:393
#: libraries/classes/Plugins/Export/ExportOdt.php:502
#: libraries/classes/Plugins/Export/ExportTexytext.php:400
#: libraries/classes/Plugins/Export/Helpers/Pdf.php:556
#: templates/columns_definitions/table_fields_definitions.twig:71
#: templates/database/data_dictionary/index.twig:31
msgid "Media type"
msgstr ""

#: libraries/classes/Config/Descriptions.php:686
#: libraries/classes/Config/Descriptions.php:698
#: libraries/classes/Config/Descriptions.php:736
msgid "Relationships"
msgstr ""

#: libraries/classes/Config/Descriptions.php:691
msgid "Export method"
msgstr ""

#: libraries/classes/Config/Descriptions.php:700
#: libraries/classes/Config/Descriptions.php:703
msgid "Save on server"
msgstr ""

#: libraries/classes/Config/Descriptions.php:701
#: libraries/classes/Config/Descriptions.php:704 templates/export.twig:182
#: templates/export.twig:377
msgid "Overwrite existing file(s)"
msgstr ""

#: libraries/classes/Config/Descriptions.php:702
msgid "Export as separate files"
msgstr ""

#: libraries/classes/Config/Descriptions.php:705
#, fuzzy
#| msgid "Select All"
msgid "Remember filename template"
msgstr "Alle ußwähle"

#: libraries/classes/Config/Descriptions.php:706
msgid "Remove DEFINER clause from definitions"
msgstr ""

#: libraries/classes/Config/Descriptions.php:707
#: templates/database/operations/index.twig:160
#: templates/table/operations/index.twig:72
#: templates/table/operations/index.twig:300
msgid "Add AUTO_INCREMENT value"
msgstr ""

#: libraries/classes/Config/Descriptions.php:708
msgid "Enclose table and column names with backquotes"
msgstr ""

#: libraries/classes/Config/Descriptions.php:709
#: libraries/classes/Config/Descriptions.php:811
msgid "SQL compatibility mode"
msgstr ""

#: libraries/classes/Config/Descriptions.php:710
msgid "Creation/Update/Check dates"
msgstr ""

#: libraries/classes/Config/Descriptions.php:711
msgid "Use delayed inserts"
msgstr ""

#: libraries/classes/Config/Descriptions.php:712
#: libraries/classes/Plugins/Export/ExportSql.php:183
msgid "Disable foreign key checks"
msgstr ""

#: libraries/classes/Config/Descriptions.php:713
#: libraries/classes/Plugins/Export/ExportSql.php:197
msgid "Export views as tables"
msgstr ""

#: libraries/classes/Config/Descriptions.php:714
msgid "Export related metadata from phpMyAdmin configuration storage"
msgstr ""

#: libraries/classes/Config/Descriptions.php:715
#: libraries/classes/Config/Descriptions.php:716
#: libraries/classes/Config/Descriptions.php:718
#: libraries/classes/Config/Descriptions.php:721
#: libraries/classes/Config/Descriptions.php:722
#: libraries/classes/Config/Descriptions.php:723
#: libraries/classes/Config/Descriptions.php:735
#: templates/database/operations/index.twig:155
#: templates/table/operations/index.twig:295
#, php-format
msgid "Add %s"
msgstr ""

#: libraries/classes/Config/Descriptions.php:724
msgid "Use hexadecimal for BINARY & BLOB"
msgstr ""

#: libraries/classes/Config/Descriptions.php:726
msgid ""
"Add IF NOT EXISTS (less efficient as indexes will be generated during table "
"creation)"
msgstr ""

#: libraries/classes/Config/Descriptions.php:729
#: libraries/classes/Plugins/Export/ExportSql.php:351
#, php-format
msgid "%s view"
msgstr ""

#: libraries/classes/Config/Descriptions.php:730
msgid "Use ignore inserts"
msgstr ""

#: libraries/classes/Config/Descriptions.php:732
msgid "Syntax to use when inserting data"
msgstr ""

#: libraries/classes/Config/Descriptions.php:733
#: libraries/classes/Plugins/Export/ExportSql.php:488
msgid "Maximal length of created query"
msgstr ""

#: libraries/classes/Config/Descriptions.php:738
msgid "Export type"
msgstr ""

#: libraries/classes/Config/Descriptions.php:739
#: libraries/classes/Plugins/Export/ExportSql.php:169
msgid "Enclose export in a transaction"
msgstr ""

#: libraries/classes/Config/Descriptions.php:740
msgid "Export time in UTC"
msgstr ""

#: libraries/classes/Config/Descriptions.php:744
msgid "Foreign key dropdown order"
msgstr ""

#: libraries/classes/Config/Descriptions.php:745
msgid "Foreign key limit"
msgstr ""

#: libraries/classes/Config/Descriptions.php:746
msgid "Foreign key checks"
msgstr ""

#: libraries/classes/Config/Descriptions.php:747
msgid "First day of calendar"
msgstr ""

#: libraries/classes/Config/Descriptions.php:748
#: libraries/classes/Config/Descriptions.php:759 libraries/classes/Menu.php:480
#: libraries/classes/Util.php:1960 libraries/config.values.php:155
#: templates/navigation/tree/database_select.twig:10
#: templates/server/databases/index.twig:3 templates/server/export/index.twig:7
#: templates/server/export/index.twig:14
#: templates/server/privileges/user_properties.twig:20
msgid "Databases"
msgstr ""

#: libraries/classes/Config/Descriptions.php:749
msgid "Browse mode"
msgstr ""

#: libraries/classes/Config/Descriptions.php:751
#: libraries/classes/Config/Forms/User/ExportForm.php:82
#: libraries/classes/Config/Forms/User/ImportForm.php:35
msgid "CSV"
msgstr ""

#: libraries/classes/Config/Descriptions.php:752
msgid "Developer"
msgstr ""

#: libraries/classes/Config/Descriptions.php:753
msgid "Edit mode"
msgstr ""

#: libraries/classes/Config/Descriptions.php:754
msgid "Export defaults"
msgstr ""

#: libraries/classes/Config/Descriptions.php:755
msgid "General"
msgstr ""

#: libraries/classes/Config/Descriptions.php:756
msgid "Import defaults"
msgstr ""

#: libraries/classes/Config/Descriptions.php:757
msgid "Import / export"
msgstr ""

#: libraries/classes/Config/Descriptions.php:758
msgid "LaTeX"
msgstr ""

#: libraries/classes/Config/Descriptions.php:760
#: libraries/classes/Config/Forms/User/NaviForm.php:68
#: templates/preferences/header.twig:30
msgid "Navigation panel"
msgstr ""

#: libraries/classes/Config/Descriptions.php:761
msgid "Navigation tree"
msgstr ""

#: libraries/classes/Config/Descriptions.php:762
#: templates/server/select/index.twig:9 templates/setup/home/<USER>
msgid "Servers"
msgstr ""

#: libraries/classes/Config/Descriptions.php:763
#: libraries/classes/Controllers/Server/DatabasesController.php:307
#: libraries/classes/Navigation/Nodes/NodeTableContainer.php:25
#: libraries/classes/Navigation/Nodes/NodeTableContainer.php:26
#: libraries/classes/Plugins/Export/ExportXml.php:115
#: libraries/classes/Server/Status/Data.php:159
#: templates/database/export/index.twig:22
#: templates/database/structure/show_create.twig:6
msgid "Tables"
msgstr ""

#: libraries/classes/Config/Descriptions.php:764
#: libraries/classes/Config/Forms/User/MainForm.php:90
#: templates/preferences/header.twig:36
msgid "Main panel"
msgstr ""

#: libraries/classes/Config/Descriptions.php:765
msgid "Microsoft Office"
msgstr ""

#: libraries/classes/Config/Descriptions.php:767
msgid "Other core settings"
msgstr ""

#: libraries/classes/Config/Descriptions.php:768
msgid "Page titles"
msgstr ""

#: libraries/classes/Config/Descriptions.php:769
msgid "Security"
msgstr ""

#: libraries/classes/Config/Descriptions.php:770
msgid "Basic settings"
msgstr ""

#: libraries/classes/Config/Descriptions.php:771
msgid "Authentication"
msgstr ""

#: libraries/classes/Config/Descriptions.php:772
msgid "Server configuration"
msgstr ""

#: libraries/classes/Config/Descriptions.php:773
msgid "Configuration storage"
msgstr ""

#: libraries/classes/Config/Descriptions.php:774
msgid "Changes tracking"
msgstr ""

#: libraries/classes/Config/Descriptions.php:775 libraries/classes/Menu.php:255
#: libraries/classes/Menu.php:362 libraries/classes/Menu.php:485
#: libraries/classes/Navigation/Nodes/NodeTable.php:310
#: libraries/classes/Util.php:1486 libraries/classes/Util.php:1961
#: libraries/classes/Util.php:1976 libraries/classes/Util.php:1993
#: libraries/config.values.php:62 libraries/config.values.php:76
#: libraries/config.values.php:167 libraries/config.values.php:177
msgid "SQL"
msgstr ""

#: libraries/classes/Config/Descriptions.php:776
msgid "SQL Query box"
msgstr ""

#: libraries/classes/Config/Descriptions.php:777
#: libraries/classes/Config/Forms/User/SqlForm.php:48
#: templates/preferences/header.twig:24
msgid "SQL queries"
msgstr ""

#: libraries/classes/Config/Descriptions.php:778
msgid "Startup"
msgstr ""

#: libraries/classes/Config/Descriptions.php:779
msgid "Database structure"
msgstr ""

#: libraries/classes/Config/Descriptions.php:780
#: libraries/classes/Plugins/Export/ExportPdf.php:267
#: templates/table/page_with_secondary_tabs.twig:5
msgid "Table structure"
msgstr ""

#: libraries/classes/Config/Descriptions.php:781
msgid "Tabs"
msgstr ""

#: libraries/classes/Config/Descriptions.php:782
msgid "Display relational schema"
msgstr ""

#: libraries/classes/Config/Descriptions.php:783
msgid "Text fields"
msgstr ""

#: libraries/classes/Config/Descriptions.php:784
msgid "Texy! text"
msgstr ""

#: libraries/classes/Config/Descriptions.php:785
msgid "Warnings"
msgstr ""

#: libraries/classes/Config/Descriptions.php:786
#: templates/console/display.twig:4
msgid "Console"
msgstr ""

#: libraries/classes/Config/Descriptions.php:787
msgid "GZip"
msgstr ""

#: libraries/classes/Config/Descriptions.php:788
msgid "Extra parameters for iconv"
msgstr ""

#: libraries/classes/Config/Descriptions.php:789
msgid "Ignore multiple statement errors"
msgstr ""

#: libraries/classes/Config/Descriptions.php:790
msgid "Enable drag and drop import"
msgstr ""

#: libraries/classes/Config/Descriptions.php:791
msgid "Partial import: allow interrupt"
msgstr ""

#: libraries/classes/Config/Descriptions.php:796
#: libraries/classes/Config/Descriptions.php:802
#: libraries/classes/Plugins/Import/ImportCsv.php:141
#: libraries/classes/Plugins/Import/ImportLdi.php:68
msgid "Do not abort on INSERT error"
msgstr ""

#: libraries/classes/Config/Descriptions.php:797
#: libraries/classes/Config/Descriptions.php:804
msgid "Add ON DUPLICATE KEY UPDATE"
msgstr ""

#: libraries/classes/Config/Descriptions.php:799
msgid "Format of imported file"
msgstr ""

#: libraries/classes/Config/Descriptions.php:803
#: libraries/classes/Plugins/Import/ImportLdi.php:74
msgid "Use LOCAL keyword"
msgstr ""

#: libraries/classes/Config/Descriptions.php:806
msgid "Column names in first row"
msgstr ""

#: libraries/classes/Config/Descriptions.php:807
#: libraries/classes/Plugins/Import/ImportOds.php:74
msgid "Do not import empty rows"
msgstr ""

#: libraries/classes/Config/Descriptions.php:808
msgid "Import currencies ($5.00 to 5.00)"
msgstr ""

#: libraries/classes/Config/Descriptions.php:809
msgid "Import percentages as proper decimals (12.00% to .12)"
msgstr ""

#: libraries/classes/Config/Descriptions.php:810
msgid "Partial import: skip queries"
msgstr ""

#: libraries/classes/Config/Descriptions.php:812
msgid "Do not use AUTO_INCREMENT for zero values"
msgstr ""

#: libraries/classes/Config/Descriptions.php:813
msgid "Read as multibytes"
msgstr ""

#: libraries/classes/Config/Descriptions.php:814
msgid "Initial state for sliders"
msgstr ""

#: libraries/classes/Config/Descriptions.php:815
msgid "Number of inserted rows"
msgstr ""

#: libraries/classes/Config/Descriptions.php:816
msgid "Limit column characters"
msgstr ""

#: libraries/classes/Config/Descriptions.php:817
msgid "Delete all cookies on logout"
msgstr ""

#: libraries/classes/Config/Descriptions.php:818
msgid "Recall user name"
msgstr ""

#: libraries/classes/Config/Descriptions.php:819
msgid "Login cookie store"
msgstr ""

#: libraries/classes/Config/Descriptions.php:820
msgid "Login cookie validity"
msgstr ""

#: libraries/classes/Config/Descriptions.php:821
msgid "Bigger textarea for LONGTEXT"
msgstr ""

#: libraries/classes/Config/Descriptions.php:822
msgid "Maximum displayed SQL length"
msgstr ""

#: libraries/classes/Config/Descriptions.php:823
msgid "Maximum databases"
msgstr ""

#: libraries/classes/Config/Descriptions.php:824
msgid "Maximum items on first level"
msgstr ""

#: libraries/classes/Config/Descriptions.php:825
msgid "Maximum items in branch"
msgstr ""

#: libraries/classes/Config/Descriptions.php:826
msgid "Maximum number of rows to display"
msgstr ""

#: libraries/classes/Config/Descriptions.php:827
msgid "Maximum tables"
msgstr ""

#: libraries/classes/Config/Descriptions.php:828
msgid "Memory limit"
msgstr ""

#: libraries/classes/Config/Descriptions.php:829
msgid "Show databases navigation as tree"
msgstr ""

#: libraries/classes/Config/Descriptions.php:830
msgid "Navigation panel width"
msgstr ""

#: libraries/classes/Config/Descriptions.php:831
#: libraries/classes/Controllers/JavaScriptMessagesController.php:601
#: libraries/classes/Navigation/NavigationTree.php:1352
msgid "Link with main panel"
msgstr ""

#: libraries/classes/Config/Descriptions.php:832
msgid "Display logo"
msgstr ""

#: libraries/classes/Config/Descriptions.php:833
msgid "Logo link URL"
msgstr ""

#: libraries/classes/Config/Descriptions.php:834
msgid "Logo link target"
msgstr ""

#: libraries/classes/Config/Descriptions.php:835
msgid "Display servers selection"
msgstr ""

#: libraries/classes/Config/Descriptions.php:836
msgid "Target for quick access icon"
msgstr ""

#: libraries/classes/Config/Descriptions.php:837
msgid "Target for second quick access icon"
msgstr ""

#: libraries/classes/Config/Descriptions.php:838
msgid "Minimum number of items to display the filter box"
msgstr ""

#: libraries/classes/Config/Descriptions.php:840
msgid "Minimum number of databases to display the database filter box"
msgstr ""

#: libraries/classes/Config/Descriptions.php:842
msgid "Group items in the tree"
msgstr ""

#: libraries/classes/Config/Descriptions.php:843
msgid "Database tree separator"
msgstr ""

#: libraries/classes/Config/Descriptions.php:844
msgid "Table tree separator"
msgstr ""

#: libraries/classes/Config/Descriptions.php:845
msgid "Maximum table tree depth"
msgstr ""

#: libraries/classes/Config/Descriptions.php:846
msgid "Enable highlighting"
msgstr ""

#: libraries/classes/Config/Descriptions.php:847
msgid "Enable navigation tree expansion"
msgstr ""

#: libraries/classes/Config/Descriptions.php:848
msgid "Show tables in tree"
msgstr ""

#: libraries/classes/Config/Descriptions.php:849
msgid "Show views in tree"
msgstr ""

#: libraries/classes/Config/Descriptions.php:850
msgid "Show functions in tree"
msgstr ""

#: libraries/classes/Config/Descriptions.php:851
msgid "Show procedures in tree"
msgstr ""

#: libraries/classes/Config/Descriptions.php:852
msgid "Show events in tree"
msgstr ""

#: libraries/classes/Config/Descriptions.php:853
#, fuzzy
#| msgid "Select All"
msgid "Expand single database"
msgstr "Alle ußwähle"

#: libraries/classes/Config/Descriptions.php:854
msgid "Recently used tables"
msgstr ""

#: libraries/classes/Config/Descriptions.php:855
#: libraries/classes/RecentFavoriteTable.php:251
msgid "Favorite tables"
msgstr ""

#: libraries/classes/Config/Descriptions.php:856
msgid "Where to show the table row links"
msgstr ""

#: libraries/classes/Config/Descriptions.php:857
msgid "Show row links anyway"
msgstr ""

#: libraries/classes/Config/Descriptions.php:859
msgid "Natural order"
msgstr ""

#: libraries/classes/Config/Descriptions.php:860
msgid "Table navigation bar"
msgstr ""

#: libraries/classes/Config/Descriptions.php:861
msgid "GZip output buffering"
msgstr ""

#: libraries/classes/Config/Descriptions.php:862
msgid "Default sorting order"
msgstr ""

#: libraries/classes/Config/Descriptions.php:863
msgid "Persistent connections"
msgstr ""

#: libraries/classes/Config/Descriptions.php:864
msgid "Missing phpMyAdmin configuration storage tables"
msgstr ""

#: libraries/classes/Config/Descriptions.php:865
msgid "MySQL reserved word warning"
msgstr ""

#: libraries/classes/Config/Descriptions.php:866
msgid "How to display the menu tabs"
msgstr ""

#: libraries/classes/Config/Descriptions.php:867
msgid "How to display various action links"
msgstr ""

#: libraries/classes/Config/Descriptions.php:868
msgid "Protect binary columns"
msgstr ""

#: libraries/classes/Config/Descriptions.php:869
msgid "Permanent query history"
msgstr ""

#: libraries/classes/Config/Descriptions.php:870
msgid "Query history length"
msgstr ""

#: libraries/classes/Config/Descriptions.php:871
msgid "Recoding engine"
msgstr ""

#: libraries/classes/Config/Descriptions.php:872
msgid "Remember table's sorting"
msgstr ""

#: libraries/classes/Config/Descriptions.php:873
msgid "Primary key default sort order"
msgstr ""

#: libraries/classes/Config/Descriptions.php:874
msgid "Repeat headers"
msgstr ""

#: libraries/classes/Config/Descriptions.php:875
msgid "Grid editing: trigger action"
msgstr ""

#: libraries/classes/Config/Descriptions.php:876
msgid "Relational display"
msgstr ""

#: libraries/classes/Config/Descriptions.php:877
msgid "Grid editing: save all edited cells at once"
msgstr ""

#: libraries/classes/Config/Descriptions.php:878
msgid "Save directory"
msgstr ""

#: libraries/classes/Config/Descriptions.php:879
msgid "Host authorization order"
msgstr ""

#: libraries/classes/Config/Descriptions.php:880
msgid "Host authorization rules"
msgstr ""

#: libraries/classes/Config/Descriptions.php:881
msgid "Allow logins without a password"
msgstr ""

#: libraries/classes/Config/Descriptions.php:882
msgid "Allow root login"
msgstr ""

#: libraries/classes/Config/Descriptions.php:883
msgid "Session timezone"
msgstr ""

#: libraries/classes/Config/Descriptions.php:884
msgid "HTTP Realm"
msgstr ""

#: libraries/classes/Config/Descriptions.php:885
#: templates/setup/home/<USER>
msgid "Authentication type"
msgstr ""

#: libraries/classes/Config/Descriptions.php:886
msgid "Bookmark table"
msgstr ""

#: libraries/classes/Config/Descriptions.php:887
msgid "Column information table"
msgstr ""

#: libraries/classes/Config/Descriptions.php:888
msgid "Compress connection"
msgstr ""

#: libraries/classes/Config/Descriptions.php:889
msgid "Control user password"
msgstr ""

#: libraries/classes/Config/Descriptions.php:890
msgid "Control user"
msgstr ""

#: libraries/classes/Config/Descriptions.php:891
msgid "Control host"
msgstr ""

#: libraries/classes/Config/Descriptions.php:892
msgid "Control port"
msgstr ""

#: libraries/classes/Config/Descriptions.php:893
msgid "Disable use of INFORMATION_SCHEMA"
msgstr ""

#: libraries/classes/Config/Descriptions.php:894
msgid "Hide databases"
msgstr ""

#: libraries/classes/Config/Descriptions.php:895
msgid "SQL query history table"
msgstr ""

#: libraries/classes/Config/Descriptions.php:896
msgid "Server hostname"
msgstr ""

#: libraries/classes/Config/Descriptions.php:897
msgid "Logout URL"
msgstr ""

#: libraries/classes/Config/Descriptions.php:898
msgid "Maximal number of table preferences to store"
msgstr ""

#: libraries/classes/Config/Descriptions.php:899
msgid "QBE saved searches table"
msgstr ""

#: libraries/classes/Config/Descriptions.php:900
msgid "Export templates table"
msgstr ""

#: libraries/classes/Config/Descriptions.php:901
msgid "Central columns table"
msgstr ""

#: libraries/classes/Config/Descriptions.php:902
msgid "Show only listed databases"
msgstr ""

#: libraries/classes/Config/Descriptions.php:903
msgid "Password for config auth"
msgstr ""

#: libraries/classes/Config/Descriptions.php:904
msgid "PDF schema: pages table"
msgstr ""

#: libraries/classes/Config/Descriptions.php:905
#: templates/database/operations/index.twig:122
#: templates/server/databases/index.twig:24
msgid "Database name"
msgstr ""

#: libraries/classes/Config/Descriptions.php:906
msgid "Server port"
msgstr ""

#: libraries/classes/Config/Descriptions.php:907
msgid "Recently used table"
msgstr ""

#: libraries/classes/Config/Descriptions.php:908
#, fuzzy
msgid "Favorites table"
msgstr "Ein Tabäll"

#: libraries/classes/Config/Descriptions.php:909
msgid "Relation table"
msgstr ""

#: libraries/classes/Config/Descriptions.php:910
msgid "Signon session name"
msgstr ""

#: libraries/classes/Config/Descriptions.php:911
msgid "Signon URL"
msgstr ""

#: libraries/classes/Config/Descriptions.php:912
msgid "Server socket"
msgstr ""

#: libraries/classes/Config/Descriptions.php:913
msgid "Use SSL"
msgstr ""

#: libraries/classes/Config/Descriptions.php:914
msgid "Designer and PDF schema: table coordinates"
msgstr ""

#: libraries/classes/Config/Descriptions.php:915
msgid "Display columns table"
msgstr ""

#: libraries/classes/Config/Descriptions.php:916
msgid "UI preferences table"
msgstr ""

#: libraries/classes/Config/Descriptions.php:917
msgid "Add DROP DATABASE"
msgstr ""

#: libraries/classes/Config/Descriptions.php:918
#: templates/database/structure/copy_form.twig:34
msgid "Add DROP TABLE"
msgstr ""

#: libraries/classes/Config/Descriptions.php:919
msgid "Add DROP VIEW"
msgstr ""

#: libraries/classes/Config/Descriptions.php:920
msgid "Statements to track"
msgstr ""

#: libraries/classes/Config/Descriptions.php:921
msgid "SQL query tracking table"
msgstr ""

#: libraries/classes/Config/Descriptions.php:922
msgid "Automatically create versions"
msgstr ""

#: libraries/classes/Config/Descriptions.php:923
msgid "User preferences storage table"
msgstr ""

#: libraries/classes/Config/Descriptions.php:924
msgid "Users table"
msgstr ""

#: libraries/classes/Config/Descriptions.php:925
msgid "User groups table"
msgstr ""

#: libraries/classes/Config/Descriptions.php:926
msgid "Hidden navigation items table"
msgstr ""

#: libraries/classes/Config/Descriptions.php:927
msgid "User for config auth"
msgstr ""

#: libraries/classes/Config/Descriptions.php:928
msgid "Verbose name of this server"
msgstr ""

#: libraries/classes/Config/Descriptions.php:929
msgid "Allow to display all the rows"
msgstr ""

#: libraries/classes/Config/Descriptions.php:930
msgid "Show password change form"
msgstr ""

#: libraries/classes/Config/Descriptions.php:931
msgid "Show create database form"
msgstr ""

#: libraries/classes/Config/Descriptions.php:932
msgid "Show table comments"
msgstr ""

#: libraries/classes/Config/Descriptions.php:933
msgid "Show creation timestamp"
msgstr ""

#: libraries/classes/Config/Descriptions.php:934
msgid "Show last update timestamp"
msgstr ""

#: libraries/classes/Config/Descriptions.php:935
msgid "Show last check timestamp"
msgstr ""

#: libraries/classes/Config/Descriptions.php:936
#, fuzzy
#| msgid "not active"
msgid "Show table charset"
msgstr "nit aktief"

#: libraries/classes/Config/Descriptions.php:937
msgid "Show field types"
msgstr ""

#: libraries/classes/Config/Descriptions.php:938
msgid "Show function fields"
msgstr ""

#: libraries/classes/Config/Descriptions.php:939
msgid "Show hint"
msgstr ""

#: libraries/classes/Config/Descriptions.php:940
msgid "Show phpinfo() link"
msgstr ""

#: libraries/classes/Config/Descriptions.php:941
msgid "Show detailed MySQL server information"
msgstr ""

#: libraries/classes/Config/Descriptions.php:942
msgid "Show SQL queries"
msgstr ""

#: libraries/classes/Config/Descriptions.php:943 templates/sql/query.twig:127
msgid "Retain query box"
msgstr ""

#: libraries/classes/Config/Descriptions.php:944
msgid "Show statistics"
msgstr ""

#: libraries/classes/Config/Descriptions.php:945
msgid "Skip locked tables"
msgstr ""

#: libraries/classes/Config/Descriptions.php:946
#: libraries/classes/ConfigStorage/UserGroups.php:124
#: libraries/classes/Controllers/JavaScriptMessagesController.php:388
#: libraries/classes/Display/Results.php:2967
#: libraries/classes/Html/Generator.php:657
#: libraries/classes/Html/Generator.php:908
#: templates/console/bookmark_content.twig:7 templates/console/display.twig:31
#: templates/console/display.twig:175
#: templates/database/central_columns/main.twig:265
#: templates/database/central_columns/main.twig:376
#: templates/database/central_columns/main.twig:377
#: templates/database/events/index.twig:74
#: templates/database/events/index.twig:77
#: templates/database/events/row.twig:23 templates/database/events/row.twig:26
#: templates/database/routines/row.twig:24
#: templates/database/routines/row.twig:27
#: templates/database/structure/structure_table_row.twig:66
#: templates/database/triggers/row.twig:28
#: templates/database/triggers/row.twig:31
#: templates/display/results/table.twig:227
#: templates/display/results/table.twig:228 templates/indexes.twig:34
#: templates/server/variables/index.twig:41
#: templates/server/variables/index.twig:44 templates/setup/home/<USER>
#: templates/table/structure/display_structure.twig:497
msgid "Edit"
msgstr ""

#: libraries/classes/Config/Descriptions.php:947
#: libraries/classes/Html/Generator.php:637
msgid "Explain SQL"
msgstr ""

#: libraries/classes/Config/Descriptions.php:948
#: libraries/classes/Export.php:594 libraries/classes/Html/Generator.php:705
#: templates/console/display.twig:99
#: templates/server/status/processes/index.twig:19
#: templates/server/status/variables/index.twig:42
msgid "Refresh"
msgstr ""

#: libraries/classes/Config/Descriptions.php:949
#: libraries/classes/Html/Generator.php:689
#, fuzzy
#| msgid "Created"
msgid "Create PHP code"
msgstr "Aanjelaat"

#: libraries/classes/Config/Descriptions.php:950
msgid "Suhosin warning"
msgstr ""

#: libraries/classes/Config/Descriptions.php:951
msgid "Login cookie validity warning"
msgstr ""

#: libraries/classes/Config/Descriptions.php:952
msgid "Textarea columns"
msgstr ""

#: libraries/classes/Config/Descriptions.php:953
msgid "Textarea rows"
msgstr ""

#: libraries/classes/Config/Descriptions.php:954
#: libraries/classes/Plugins/Export/ExportHtmlword.php:141
#: libraries/classes/Plugins/Export/ExportOdt.php:185
#: libraries/classes/Plugins/Export/ExportTexytext.php:125
#: libraries/classes/Server/Privileges.php:1812
#: libraries/classes/Server/Privileges.php:1814
#: libraries/classes/Server/Status/Processes.php:122
#: templates/console/bookmark_content.twig:7 templates/console/display.twig:40
#: templates/console/display.twig:184 templates/server/databases/index.twig:106
#: templates/server/privileges/edit_routine_privileges.twig:17
#: templates/server/privileges/privileges_table.twig:257
#: templates/server/privileges/user_properties.twig:22
#: templates/server/privileges/user_properties.twig:71
#: templates/table/operations/index.twig:56
#: templates/table/operations/index.twig:62
#: templates/table/operations/index.twig:257
#: templates/table/operations/index.twig:263
#: templates/table/relation/common_form.twig:36
#: templates/table/relation/common_form.twig:175
#: templates/table/relation/foreign_key_row.twig:97
msgid "Database"
msgstr "Dahtebangk"

#: libraries/classes/Config/Descriptions.php:955
msgid "Default title"
msgstr ""

#: libraries/classes/Config/Descriptions.php:956
#: templates/server/status/base.twig:6
msgid "Server"
msgstr ""

#: libraries/classes/Config/Descriptions.php:957
#: libraries/classes/Plugins/Export/ExportXml.php:466
#: libraries/classes/Server/Privileges.php:1818
#: libraries/classes/Server/Privileges.php:1820
#: templates/database/structure/show_create.twig:10
#: templates/database/structure/table_header.twig:8
#: templates/database/tracking/tables.twig:13
#: templates/database/tracking/tables.twig:153
#: templates/database/triggers/editor_form.twig:27
#: templates/database/triggers/list.twig:45
#: templates/server/privileges/privileges_table.twig:6
#: templates/server/privileges/privileges_table.twig:259
#: templates/server/privileges/user_properties.twig:37
#: templates/server/privileges/user_properties.twig:82
#: templates/table/maintenance/checksum.twig:12
#: templates/table/operations/index.twig:65
#: templates/table/operations/index.twig:266
#: templates/table/relation/common_form.twig:37
#: templates/table/relation/common_form.twig:182
#: templates/table/relation/foreign_key_row.twig:108
msgid "Table"
msgstr "Tabäll"

#: libraries/classes/Config/Descriptions.php:958
msgid "List of trusted proxies for IP allow/deny"
msgstr ""

#: libraries/classes/Config/Descriptions.php:959
msgid "Upload directory"
msgstr ""

#: libraries/classes/Config/Descriptions.php:960
msgid "Use database search"
msgstr ""

#: libraries/classes/Config/Descriptions.php:961
msgid "Enable the Developer tab in settings"
msgstr ""

#: libraries/classes/Config/Descriptions.php:962
#: libraries/classes/Setup/Index.php:128 libraries/classes/Setup/Index.php:151
#: libraries/classes/Setup/Index.php:163 libraries/classes/Setup/Index.php:176
#: libraries/classes/Setup/Index.php:185 libraries/classes/Setup/Index.php:193
msgid "Version check"
msgstr ""

#: libraries/classes/Config/Descriptions.php:963
msgid "Proxy URL"
msgstr ""

#: libraries/classes/Config/Descriptions.php:964
msgid "Proxy username"
msgstr ""

#: libraries/classes/Config/Descriptions.php:965
msgid "Proxy password"
msgstr ""

#: libraries/classes/Config/Descriptions.php:966
msgid "ZIP"
msgstr ""

#: libraries/classes/Config/Descriptions.php:967
msgid "URL for reCAPTCHA v2 API"
msgstr ""

#: libraries/classes/Config/Descriptions.php:968
msgid "Content-Security-Policy snippet for reCAPTCHA v2 API"
msgstr ""

#: libraries/classes/Config/Descriptions.php:969
msgid "Request parameter for reCAPTCHA v2 API"
msgstr ""

#: libraries/classes/Config/Descriptions.php:970
msgid "Response parameter for reCAPTCHA v2 API"
msgstr ""

#: libraries/classes/Config/Descriptions.php:971
msgid "Public key for reCAPTCHA"
msgstr ""

#: libraries/classes/Config/Descriptions.php:972
msgid "Private key for reCAPTCHA"
msgstr ""

#: libraries/classes/Config/Descriptions.php:973
msgid "URL for reCAPTCHA siteverify"
msgstr ""

#: libraries/classes/Config/Descriptions.php:974
msgid "Send error reports"
msgstr ""

#: libraries/classes/Config/Descriptions.php:975
msgid "Enter executes queries in console"
msgstr ""

#: libraries/classes/Config/Descriptions.php:976
msgid "Enable Zero Configuration mode"
msgstr ""

#: libraries/classes/Config/Descriptions.php:977
#: templates/console/display.twig:153
msgid "Show query history at start"
msgstr ""

#: libraries/classes/Config/Descriptions.php:978
#: templates/console/display.twig:149
msgid "Always expand query messages"
msgstr ""

#: libraries/classes/Config/Descriptions.php:979
#: templates/console/display.twig:157
msgid "Show current browsing query"
msgstr ""

#: libraries/classes/Config/Descriptions.php:980
msgid "Execute queries on Enter and insert new line with Shift+Enter"
msgstr ""

#: libraries/classes/Config/Descriptions.php:981
#: templates/console/display.twig:168
msgid "Switch to dark theme"
msgstr ""

#: libraries/classes/Config/Descriptions.php:982
msgid "Console height"
msgstr ""

#: libraries/classes/Config/Descriptions.php:983
msgid "Console mode"
msgstr ""

#: libraries/classes/Config/Descriptions.php:984
#: templates/console/display.twig:64
msgid "Group queries"
msgstr ""

#: libraries/classes/Config/Descriptions.php:985
#: templates/sql/profiling_chart.twig:9
msgid "Order"
msgstr ""

#: libraries/classes/Config/Descriptions.php:986
msgid "Order by"
msgstr ""

#: libraries/classes/Config/Descriptions.php:987
msgid "Server connection collation"
msgstr ""

#: libraries/classes/Config/FormDisplay.php:605
#, php-format
msgid "Missing data for %s"
msgstr ""

#: libraries/classes/Config/FormDisplay.php:646
#: libraries/classes/Config/Validator.php:590
#: templates/config/form_display/display.twig:67
msgid "Incorrect value!"
msgstr ""

#: libraries/classes/Config/FormDisplay.php:797
#: libraries/classes/Config/FormDisplay.php:806
msgid "unavailable"
msgstr ""

#: libraries/classes/Config/FormDisplay.php:799
#: libraries/classes/Config/FormDisplay.php:808
#, php-format
msgid "\"%s\" requires %s extension"
msgstr ""

#: libraries/classes/Config/FormDisplay.php:839
#, php-format
msgid "Compressed import will not work due to missing function %s."
msgstr ""

#: libraries/classes/Config/FormDisplay.php:848
#, php-format
msgid "Compressed export will not work due to missing function %s."
msgstr ""

#: libraries/classes/Config/FormDisplay.php:867
#, php-format
msgid "maximum %s"
msgstr ""

#: libraries/classes/Config/Forms/Setup/ServersForm.php:39
msgid "Config authentication"
msgstr ""

#: libraries/classes/Config/Forms/Setup/ServersForm.php:43
msgid "HTTP authentication"
msgstr ""

#: libraries/classes/Config/Forms/Setup/ServersForm.php:46
msgid "Signon authentication"
msgstr ""

#: libraries/classes/Config/Forms/User/ExportForm.php:25
msgid "Quick"
msgstr ""

#: libraries/classes/Config/Forms/User/ExportForm.php:29
msgid "Custom"
msgstr ""

#: libraries/classes/Config/Forms/User/ExportForm.php:55
#: libraries/classes/Config/Forms/User/ExportForm.php:100
#: libraries/classes/Config/Forms/User/ExportForm.php:128
#: libraries/classes/Controllers/JavaScriptMessagesController.php:710
#: libraries/classes/Import.php:1299 libraries/classes/Menu.php:247
#: libraries/classes/Menu.php:357
#: libraries/classes/Navigation/Nodes/NodeColumn.php:42
#: libraries/classes/Navigation/Nodes/NodeDatabase.php:50
#: libraries/classes/Navigation/Nodes/NodeTable.php:301
#: libraries/classes/Util.php:1485 libraries/classes/Util.php:1975
#: libraries/classes/Util.php:1992 libraries/config.values.php:60
#: libraries/config.values.php:74 libraries/config.values.php:165
#: libraries/config.values.php:175
#: templates/columns_definitions/table_fields_definitions.twig:4
#: templates/database/central_columns/edit.twig:3
#: templates/database/export/index.twig:23
#: templates/database/structure/structure_table_row.twig:48
#: templates/server/privileges/privileges_table.twig:352
#: templates/table/tracking/structure_snapshot_columns.twig:1
msgid "Structure"
msgstr ""

#: libraries/classes/Config/Forms/User/ExportForm.php:71
#: libraries/classes/Config/Forms/User/ExportForm.php:108
#: libraries/classes/Config/Forms/User/ExportForm.php:133
#: libraries/classes/Config/Forms/User/ExportForm.php:139
#: libraries/classes/Controllers/Server/DatabasesController.php:317
#: templates/database/export/index.twig:24
#: templates/server/privileges/privileges_table.twig:278
#: templates/table/structure/display_table_stats.twig:17
msgid "Data"
msgstr ""

#: libraries/classes/Config/Forms/User/ExportForm.php:91
msgid "CSV for MS Excel"
msgstr ""

#: libraries/classes/Config/Forms/User/ExportForm.php:116
msgid "Microsoft Word 2000"
msgstr ""

#: libraries/classes/Config/Forms/User/ExportForm.php:122
#: libraries/classes/Config/Forms/User/ImportForm.php:52
msgid "OpenDocument Spreadsheet"
msgstr ""

#: libraries/classes/Config/Forms/User/ExportForm.php:126
msgid "OpenDocument Text"
msgstr ""

#: libraries/classes/Config/Forms/User/ExportForm.php:153
#: libraries/classes/Controllers/JavaScriptMessagesController.php:373
#: libraries/classes/Menu.php:277 libraries/classes/Menu.php:382
#: libraries/classes/Menu.php:513 libraries/classes/Server/Privileges.php:1506
#: libraries/classes/Util.php:1964 libraries/classes/Util.php:1979
#: libraries/classes/Util.php:1996 templates/database/events/index.twig:16
#: templates/database/events/index.twig:17
#: templates/database/events/index.twig:86
#: templates/database/events/row.twig:36
#: templates/database/privileges/index.twig:91
#: templates/database/privileges/index.twig:116
#: templates/database/privileges/index.twig:117
#: templates/database/routines/index.twig:16
#: templates/database/routines/index.twig:17
#: templates/database/routines/row.twig:64
#: templates/database/routines/row.twig:67
#: templates/database/structure/check_all_tables.twig:12
#: templates/database/triggers/list.twig:16
#: templates/database/triggers/list.twig:17
#: templates/database/triggers/row.twig:41
#: templates/display/results/table.twig:240
#: templates/display/results/table.twig:241
#: templates/display/results/table.twig:272 templates/export.twig:3
#: templates/export.twig:499 templates/preferences/header.twig:42
#: templates/preferences/manage/main.twig:68
#: templates/server/privileges/new_user_ajax.twig:63
#: templates/server/privileges/users_overview.twig:95
#: templates/server/privileges/users_overview.twig:125
#: templates/server/privileges/users_overview.twig:126
#: templates/server/status/monitor/index.twig:81
#: templates/table/privileges/index.twig:95
#: templates/table/privileges/index.twig:120
#: templates/table/privileges/index.twig:121
msgid "Export"
msgstr ""

#: libraries/classes/Config/Forms/User/FeaturesForm.php:90
#: templates/preferences/header.twig:18
msgid "Features"
msgstr ""

#: libraries/classes/Config/Forms/User/ImportForm.php:43
msgid "CSV using LOAD DATA"
msgstr ""

#: libraries/classes/Config/Forms/User/ImportForm.php:67
#: libraries/classes/Controllers/JavaScriptMessagesController.php:310
#: libraries/classes/Menu.php:286 libraries/classes/Menu.php:392
#: libraries/classes/Menu.php:518 libraries/classes/Util.php:1965
#: libraries/classes/Util.php:1980 libraries/classes/Util.php:1997
#: templates/import.twig:3 templates/import.twig:199
#: templates/preferences/header.twig:48
#: templates/preferences/manage/main.twig:11
#: templates/server/status/monitor/index.twig:78
msgid "Import"
msgstr ""

#: libraries/classes/Config/Forms/User/MainForm.php:37
msgid "Default transformations"
msgstr ""

#: libraries/classes/Config/PageSettings.php:135
msgid "Cannot save settings, submitted configuration form contains errors!"
msgstr ""

#: libraries/classes/Config.php:652
#, php-format
msgid "Existing configuration file (%s) is not readable."
msgstr ""

#: libraries/classes/Config.php:690
msgid "Wrong permissions on configuration file, should not be world writable!"
msgstr ""

#: libraries/classes/Config.php:705
msgid "Failed to read configuration file!"
msgstr ""

#: libraries/classes/Config.php:707
msgid ""
"This usually means there is a syntax error in it, please check any errors "
"shown below."
msgstr ""

#: libraries/classes/Config.php:1234
#, php-format
msgid "Invalid server index: %s"
msgstr ""

#: libraries/classes/Config.php:1247
#, php-format
msgid "Server %d"
msgstr ""

#: libraries/classes/Config/ServerConfigChecks.php:67
#, php-format
msgid ""
"This %soption%s should be disabled as it allows attackers to bruteforce "
"login to any MySQL server. If you feel this is necessary, use %srestrict "
"login to MySQL server%s or %strusted proxies list%s. However, IP-based "
"protection with trusted proxies list may not be reliable if your IP belongs "
"to an ISP where thousands of users, including you, are connected to."
msgstr ""

#: libraries/classes/Config/ServerConfigChecks.php:92
msgid ""
"This value should be double checked to ensure that this directory is neither "
"world accessible nor readable or writable by other users on your server."
msgstr ""

#: libraries/classes/Config/ServerConfigChecks.php:162
msgid "You should use SSL connections if your database server supports it."
msgstr ""

#: libraries/classes/Config/ServerConfigChecks.php:169
#, php-format
msgid ""
"If you feel this is necessary, use additional protection settings - %1$shost "
"authentication%2$s settings and %3$strusted proxies list%4$s. However, IP-"
"based protection may not be reliable if your IP belongs to an ISP where "
"thousands of users, including you, are connected to."
msgstr ""

#: libraries/classes/Config/ServerConfigChecks.php:195
#, php-format
msgid ""
"You set the [kbd]config[/kbd] authentication type and included username and "
"password for auto-login, which is not a desirable option for live hosts. "
"Anyone who knows or guesses your phpMyAdmin URL can directly access your "
"phpMyAdmin panel. Set %1$sauthentication type%2$s to [kbd]cookie[/kbd] or "
"[kbd]http[/kbd]."
msgstr ""

#: libraries/classes/Config/ServerConfigChecks.php:224
msgid "You allow for connecting to the server without a password."
msgstr ""

#: libraries/classes/Config/ServerConfigChecks.php:307
#, php-format
msgid ""
"%sZip decompression%s requires functions (%s) which are unavailable on this "
"system."
msgstr ""

#: libraries/classes/Config/ServerConfigChecks.php:328
#, php-format
msgid ""
"%sZip compression%s requires functions (%s) which are unavailable on this "
"system."
msgstr ""

#: libraries/classes/Config/ServerConfigChecks.php:361
msgid ""
"You didn't have blowfish secret set and have enabled [kbd]cookie[/kbd] "
"authentication, so a key was automatically generated for you. It is used to "
"encrypt cookies; you don't need to remember it."
msgstr ""

#: libraries/classes/Config/ServerConfigChecks.php:385
#, php-format
msgid ""
"%1$sLogin cookie validity%2$s greater than %3$ssession.gc_maxlifetime%4$s "
"may cause random session invalidation (currently session.gc_maxlifetime is "
"%5$d)."
msgstr ""

#: libraries/classes/Config/ServerConfigChecks.php:407
#, php-format
msgid ""
"%sLogin cookie validity%s should be set to 1800 seconds (30 minutes) at "
"most. Values larger than 1800 may pose a security risk such as impersonation."
msgstr ""

#: libraries/classes/Config/ServerConfigChecks.php:433
#, php-format
msgid ""
"If using [kbd]cookie[/kbd] authentication and %sLogin cookie store%s is not "
"0, %sLogin cookie validity%s must be set to a value less or equal to it."
msgstr ""

#: libraries/classes/Config/ServerConfigChecks.php:472
#, php-format
msgid ""
"%1$sBzip2 compression and decompression%2$s requires functions (%3$s) which "
"are unavailable on this system."
msgstr ""

#: libraries/classes/Config/ServerConfigChecks.php:503
#, php-format
msgid ""
"%1$sGZip compression and decompression%2$s requires functions (%3$s) which "
"are unavailable on this system."
msgstr ""

#: libraries/classes/ConfigStorage/Relation.php:1362
msgid "no description"
msgstr ""

#: libraries/classes/ConfigStorage/Relation.php:1564
#, php-format
msgid ""
"You do not have necessary privileges to create a database named '%s'. You "
"may go to 'Operations' tab of any database to set up the phpMyAdmin "
"configuration storage there."
msgstr ""

#: libraries/classes/ConfigStorage/UserGroups.php:114
msgid "View users"
msgstr ""

#: libraries/classes/ConfigStorage/UserGroups.php:247
msgid "Server-level tabs"
msgstr ""

#: libraries/classes/ConfigStorage/UserGroups.php:252
msgid "Database-level tabs"
msgstr ""

#: libraries/classes/ConfigStorage/UserGroups.php:257
msgid "Table-level tabs"
msgstr ""

#: libraries/classes/Config/Validator.php:243
msgid "Could not connect to the database server!"
msgstr ""

#: libraries/classes/Config/Validator.php:279
msgid "Invalid authentication type!"
msgstr ""

#: libraries/classes/Config/Validator.php:284
msgid "Empty username while using [kbd]config[/kbd] authentication method!"
msgstr ""

#: libraries/classes/Config/Validator.php:290
msgid ""
"Empty signon session name while using [kbd]signon[/kbd] authentication "
"method!"
msgstr ""

#: libraries/classes/Config/Validator.php:297
msgid "Empty signon URL while using [kbd]signon[/kbd] authentication method!"
msgstr ""

#: libraries/classes/Config/Validator.php:351
msgid ""
"Empty phpMyAdmin control user while using phpMyAdmin configuration storage!"
msgstr ""

#: libraries/classes/Config/Validator.php:358
msgid ""
"Empty phpMyAdmin control user password while using phpMyAdmin configuration "
"storage!"
msgstr ""

#: libraries/classes/Config/Validator.php:449
msgid "Incorrect value:"
msgstr ""

#: libraries/classes/Config/Validator.php:460
#, php-format
msgid "Incorrect IP address: %s"
msgstr ""

#: libraries/classes/Config/Validator.php:523
#: templates/config/form_display/display.twig:66
msgid "Not a valid port number!"
msgstr ""

#: libraries/classes/Config/Validator.php:545
#: templates/config/form_display/display.twig:64
msgid "Not a positive number!"
msgstr ""

#: libraries/classes/Config/Validator.php:567
#: templates/config/form_display/display.twig:65
msgid "Not a non-negative number!"
msgstr ""

#: libraries/classes/Config/Validator.php:608
#: templates/config/form_display/display.twig:68
#, php-format
msgid "Value must be less than or equal to %s!"
msgstr ""

#: libraries/classes/Console.php:92
#, php-format
msgid "Showing %1$d bookmark (both private and shared)"
msgid_plural "Showing %1$d bookmarks (both private and shared)"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: libraries/classes/Console.php:99
msgid "No bookmarks"
msgstr ""

#: libraries/classes/Console.php:128
msgid "SQL Query Console"
msgstr ""

#: libraries/classes/Controllers/AbstractController.php:73
#: libraries/classes/Controllers/JavaScriptMessagesController.php:343
#: libraries/classes/Controllers/Server/Databases/DestroyController.php:68
#: libraries/classes/DbTableExists.php:47
msgid "No databases selected."
msgstr ""

#: libraries/classes/Controllers/ChangeLogController.php:38
#: libraries/classes/Controllers/LicenseController.php:36
#, fuzzy, php-format
#| msgid ""
#| "The %s file is not available on this system, please visit www.phpmyadmin."
#| "net for more information."
msgid ""
"The %s file is not available on this system, please visit %s for more "
"information."
msgstr ""
"De %s-Dattei es nit op heh dämm Rääschner. Loor op %s - doh jidd-et nih "
"Enfommazjuhne."

#: libraries/classes/Controllers/Database/CentralColumnsController.php:140
#, fuzzy, php-format
#| msgid "Showing row(s) %1$s - %2$s"
msgid "Showing rows %1$s - %2$s."
msgstr "De Reihje vun %1$s bes %2$s."

#. l10n: The user tries to save a page with an existing name in Designer
#: libraries/classes/Controllers/Database/DesignerController.php:92
#, php-format
msgid ""
"There already exists a page named \"%s\" please rename it to something else."
msgstr ""

#: libraries/classes/Controllers/Database/ExportController.php:81
#: libraries/classes/Controllers/Database/TrackingController.php:130
#: libraries/classes/Controllers/Export/ExportController.php:442
#: libraries/classes/Database/Qbe.php:322
#: templates/database/structure/index.twig:25
#: templates/navigation/tree/path.twig:9
msgid "No tables found in database."
msgstr ""

#: libraries/classes/Controllers/Database/ExportController.php:143
#: libraries/classes/Controllers/Server/ExportController.php:73
#: libraries/classes/Controllers/Table/ExportController.php:104
msgid "Could not load export plugins, please check your installation!"
msgstr ""

#: libraries/classes/Controllers/Database/ImportController.php:72
#: libraries/classes/Controllers/Import/ImportController.php:570
#: libraries/classes/Controllers/Server/ImportController.php:58
#: libraries/classes/Controllers/Table/ImportController.php:69
msgid "Could not load import plugins, please check your installation!"
msgstr ""

#: libraries/classes/Controllers/Database/Operations/CollationController.php:48
#: libraries/classes/Controllers/Table/OperationsController.php:258
msgid "No collation provided."
msgstr ""

#: libraries/classes/Controllers/Database/OperationsController.php:90
#: libraries/classes/Controllers/Table/CreateController.php:72
msgid "The database name is empty!"
msgstr "Kein Name för di Dahtebangk!"

#: libraries/classes/Controllers/Database/OperationsController.php:99
msgid "Cannot copy database to the same name. Change the name and try again."
msgstr ""

#: libraries/classes/Controllers/Database/OperationsController.php:177
#, php-format
msgid "Database %1$s has been renamed to %2$s."
msgstr ""

#: libraries/classes/Controllers/Database/OperationsController.php:187
#, php-format
msgid "Database %1$s has been copied to %2$s."
msgstr ""

#: libraries/classes/Controllers/Database/OperationsController.php:296
#, php-format
msgid ""
"The phpMyAdmin configuration storage has been deactivated. %sFind out why%s."
msgstr ""

#: libraries/classes/Controllers/Database/SearchController.php:47
#: libraries/classes/Plugins/Auth/AuthenticationConfig.php:90
#: libraries/classes/Plugins/Auth/AuthenticationConfig.php:103
#: libraries/classes/Plugins/Auth/AuthenticationHttp.php:82
#: libraries/classes/Plugins/AuthenticationPlugin.php:184
msgid "Access denied!"
msgstr "Zohjang afjelehnt!"

#: libraries/classes/Controllers/Database/Structure/AddPrefixController.php:21
#: libraries/classes/Controllers/Database/Structure/CentralColumns/AddController.php:45
#: libraries/classes/Controllers/Database/Structure/CentralColumns/MakeConsistentController.php:45
#: libraries/classes/Controllers/Database/Structure/CentralColumns/RemoveController.php:45
#: libraries/classes/Controllers/Database/Structure/ChangePrefixFormController.php:22
#: libraries/classes/Controllers/Database/Structure/CopyFormController.php:21
#: libraries/classes/Controllers/Database/Structure/DropFormController.php:37
#: libraries/classes/Controllers/Database/Structure/EmptyFormController.php:24
#: libraries/classes/Controllers/Database/Structure/ShowCreateController.php:32
#: libraries/classes/Controllers/Export/TablesController.php:29
#: libraries/classes/Controllers/JavaScriptMessagesController.php:344
#: libraries/classes/Controllers/Table/Maintenance/AnalyzeController.php:54
#: libraries/classes/Controllers/Table/Maintenance/CheckController.php:54
#: libraries/classes/Controllers/Table/Maintenance/ChecksumController.php:54
#: libraries/classes/Controllers/Table/Maintenance/OptimizeController.php:54
#: libraries/classes/Controllers/Table/Maintenance/RepairController.php:54
#, fuzzy
#| msgid "The database name is empty!"
msgid "No table selected."
msgstr "Kein Name för di Dahtebangk!"

#: libraries/classes/Controllers/Database/Structure/CentralColumns/AddController.php:53
#: libraries/classes/Controllers/Database/Structure/CentralColumns/MakeConsistentController.php:53
#: libraries/classes/Controllers/Database/Structure/CentralColumns/RemoveController.php:53
msgid "Success!"
msgstr ""

#: libraries/classes/Controllers/Database/StructureController.php:366
#: templates/table/operations/index.twig:451
#: templates/table/operations/view.twig:32
#, php-format
msgid "View %s has been dropped."
msgstr ""

#: libraries/classes/Controllers/Database/StructureController.php:367
#: templates/table/operations/index.twig:451
#, php-format
msgid "Table %s has been dropped."
msgstr ""

#: libraries/classes/Controllers/Database/StructureController.php:414
#: templates/table/operations/index.twig:410
#: templates/table/operations/index.twig:428
#, php-format
msgid "Table %s has been emptied."
msgstr ""

#: libraries/classes/Controllers/Database/StructureController.php:577
#: libraries/classes/Display/Results.php:3980
#, php-format
msgid ""
"This view has at least this number of rows. Please refer to "
"%sdocumentation%s."
msgstr ""

#: libraries/classes/Controllers/Database/StructureController.php:766
msgid "unknown"
msgstr ""

#: libraries/classes/Controllers/Database/Structure/DropTableController.php:56
#: libraries/classes/Controllers/Database/Structure/EmptyTableController.php:72
#: libraries/classes/Controllers/JavaScriptMessagesController.php:488
#: libraries/classes/Controllers/Table/DeleteRowsController.php:56
#: libraries/classes/Controllers/Table/DropColumnController.php:57
#: libraries/classes/Controllers/Table/Structure/PrimaryController.php:58
#: libraries/classes/Controllers/Table/Structure/PrimaryController.php:82
#: libraries/classes/IndexColumn.php:164 libraries/classes/Index.php:525
#: libraries/classes/Plugins/Export/ExportHtmlword.php:633
#: libraries/classes/Plugins/Export/ExportLatex.php:632
#: libraries/classes/Plugins/Export/ExportOdt.php:788
#: libraries/classes/Plugins/Export/ExportTexytext.php:626
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:754
#: templates/config/form_display/input.twig:42
#: templates/database/central_columns/main.twig:356
#: templates/database/data_dictionary/index.twig:47
#: templates/database/data_dictionary/index.twig:91
#: templates/database/designer/main.twig:1117
#: templates/database/privileges/index.twig:69
#: templates/database/structure/drop_form.twig:19
#: templates/database/structure/empty_form.twig:19 templates/indexes.twig:65
#: templates/preferences/autoload.twig:11
#: templates/preferences/manage/error.twig:18
#: templates/server/privileges/new_user_ajax.twig:20
#: templates/server/privileges/new_user_ajax.twig:34
#: templates/server/privileges/privileges_summary.twig:37
#: templates/server/privileges/privileges_summary.twig:40
#: templates/server/privileges/privileges_summary.twig:42
#: templates/server/privileges/users_overview.twig:53
#: templates/server/privileges/users_overview.twig:65
#: templates/table/delete/confirm.twig:28
#: templates/table/privileges/index.twig:73
#: templates/table/structure/display_structure.twig:80
#: templates/table/structure/display_structure.twig:532
#: templates/table/structure/drop_confirm.twig:19
#: templates/table/structure/primary.twig:22
#: templates/table/tracking/structure_snapshot_columns.twig:33
#: templates/table/tracking/structure_snapshot_indexes.twig:23
#: templates/table/tracking/structure_snapshot_indexes.twig:24
msgid "Yes"
msgstr "Joh"

#: libraries/classes/Controllers/Database/Structure/DropTableController.php:57
#: libraries/classes/Controllers/Database/Structure/EmptyTableController.php:73
#: libraries/classes/Controllers/Table/DropColumnController.php:74
#: libraries/classes/Controllers/Table/OperationsController.php:318
#: libraries/classes/Controllers/Table/ReplaceController.php:415
#: libraries/classes/Core.php:717 templates/preview_sql.twig:3
msgid "No change"
msgstr ""

#: libraries/classes/Controllers/Database/Structure/FavoriteTableController.php:105
msgid "Favorite List is full!"
msgstr ""

#: libraries/classes/Controllers/Database/TrackingController.php:83
#: libraries/classes/Controllers/Database/TrackingController.php:103
msgid "Tracking data deleted successfully."
msgstr ""

#: libraries/classes/Controllers/Database/TrackingController.php:90
#, php-format
msgid ""
"Version %1$s was created for selected tables, tracking is active for them."
msgstr ""

#: libraries/classes/Controllers/Database/TrackingController.php:120
msgid "No tables selected."
msgstr ""

#: libraries/classes/Controllers/Database/TrackingController.php:155
msgid "Database Log"
msgstr ""

#: libraries/classes/Controllers/ErrorReportController.php:106
msgid ""
"An error has been detected and an error report has been automatically "
"submitted based on your settings."
msgstr ""

#: libraries/classes/Controllers/ErrorReportController.php:110
msgid "Thank you for submitting this report."
msgstr ""

#: libraries/classes/Controllers/ErrorReportController.php:114
msgid ""
"An error has been detected and an error report has been generated but failed "
"to be sent."
msgstr ""

#: libraries/classes/Controllers/ErrorReportController.php:117
msgid "If you experience any problems please submit a bug report manually."
msgstr ""

#: libraries/classes/Controllers/ErrorReportController.php:120
msgid "You may want to refresh the page."
msgstr ""

#: libraries/classes/Controllers/Export/ExportController.php:239
#: libraries/classes/Export.php:1338
msgid "Bad type!"
msgstr ""

#: libraries/classes/Controllers/Export/ExportController.php:333
msgid "Bad parameters!"
msgstr ""

#: libraries/classes/Controllers/HomeController.php:98
msgid ""
"You were logged out from one server, to logout completely from phpMyAdmin, "
"you need to logout from all servers."
msgstr ""

#: libraries/classes/Controllers/HomeController.php:195
#, php-format
msgid ""
"The phpMyAdmin configuration storage is not completely configured, some "
"extended features have been deactivated. %sFind out why%s. "
msgstr ""

#: libraries/classes/Controllers/HomeController.php:201
msgid ""
"Or alternately go to 'Operations' tab of any database to set it up there."
msgstr ""

#: libraries/classes/Controllers/HomeController.php:265
msgid ""
"Your PHP parameter [a@https://www.php.net/manual/en/session.configuration."
"php#ini.session.gc-maxlifetime@_blank]session.gc_maxlifetime[/a] is lower "
"than cookie validity configured in phpMyAdmin, because of this, your login "
"might expire sooner than configured in phpMyAdmin."
msgstr ""

#: libraries/classes/Controllers/HomeController.php:282
msgid ""
"Login cookie store is lower than cookie validity configured in phpMyAdmin, "
"because of this, your login will expire sooner than configured in phpMyAdmin."
msgstr ""

#: libraries/classes/Controllers/HomeController.php:301
msgid ""
"Your server is running with default values for the controluser and password "
"(controlpass) and is open to intrusion; you really should fix this security "
"weakness by changing the password for controluser 'pma'."
msgstr ""

#: libraries/classes/Controllers/HomeController.php:318
msgid ""
"The configuration file needs a valid key for cookie encryption. A temporary "
"key was automatically generated for you. Please refer to the "
"[doc@cfg_blowfish_secret]documentation[/doc]."
msgstr ""

#: libraries/classes/Controllers/HomeController.php:328
#, php-format
msgid ""
"The cookie encryption key in the configuration file is longer than "
"necessary. It should only be %d bytes long. Please refer to the "
"[doc@cfg_blowfish_secret]documentation[/doc]."
msgstr ""

#: libraries/classes/Controllers/HomeController.php:346
msgid ""
"Directory [code]config[/code], which is used by the setup script, still "
"exists in your phpMyAdmin directory. It is strongly recommended to remove it "
"once phpMyAdmin has been configured. Otherwise the security of your server "
"may be compromised by unauthorized people downloading your configuration."
msgstr ""

#: libraries/classes/Controllers/HomeController.php:367
#, php-format
msgid ""
"Server running with Suhosin. Please refer to %sdocumentation%s for possible "
"issues."
msgstr ""

#: libraries/classes/Controllers/HomeController.php:381
#, php-format
msgid ""
"The $cfg['TempDir'] (%s) is not accessible. phpMyAdmin is not able to cache "
"templates and will be slow because of this."
msgstr ""

#: libraries/classes/Controllers/HomeController.php:438
msgid ""
"The mbstring PHP extension was not found and you seem to be using a "
"multibyte charset. Without the mbstring extension phpMyAdmin is unable to "
"split strings correctly and it may result in unexpected results."
msgstr ""

#: libraries/classes/Controllers/HomeController.php:456
msgid ""
"The curl extension was not found and allow_url_fopen is disabled. Due to "
"this some features such as error reporting or version check are disabled."
msgstr ""

#: libraries/classes/Controllers/Import/ImportController.php:109
msgid "Incomplete params"
msgstr ""

#: libraries/classes/Controllers/Import/ImportController.php:123
#, fuzzy
#| msgid "Access denied!"
msgid "Succeeded"
msgstr "Zohjang afjelehnt!"

#: libraries/classes/Controllers/Import/ImportController.php:127
#: libraries/classes/Controllers/JavaScriptMessagesController.php:575
msgid "Failed"
msgstr ""

#: libraries/classes/Controllers/Import/ImportController.php:217
#, php-format
msgid ""
"You probably tried to upload a file that is too large. Please refer to "
"%sdocumentation%s for a workaround for this limit."
msgstr ""

#: libraries/classes/Controllers/Import/ImportController.php:372
#: libraries/classes/Controllers/Import/ImportController.php:610
msgid "Showing bookmark"
msgstr ""

#: libraries/classes/Controllers/Import/ImportController.php:393
#: libraries/classes/Controllers/Import/ImportController.php:606
msgid "The bookmark has been deleted."
msgstr ""

#: libraries/classes/Controllers/Import/ImportController.php:516
msgid ""
"No data was received to import. Either no file name was submitted, or the "
"file size exceeded the maximum size permitted by your PHP configuration. See "
"[doc@faq1-16]FAQ 1.16[/doc]."
msgstr ""

#: libraries/classes/Controllers/Import/ImportController.php:618
#, php-format
msgid "Import has been successfully finished, %d query executed."
msgid_plural "Import has been successfully finished, %d queries executed."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: libraries/classes/Controllers/Import/ImportController.php:655
#, php-format
msgid ""
"Script timeout passed, if you want to finish import, please %sresubmit the "
"same file%s and import will resume."
msgstr ""

#: libraries/classes/Controllers/Import/ImportController.php:665
msgid ""
"However on last run no data has been parsed, this usually means phpMyAdmin "
"won't be able to finish this import unless you increase php time limits."
msgstr ""

#: libraries/classes/Controllers/Import/ImportController.php:736
#: libraries/classes/Controllers/Sql/SqlController.php:162
msgid "\"DROP DATABASE\" statements are disabled."
msgstr ""

#: libraries/classes/Controllers/Import/SimulateDmlController.php:38
msgid "Only single-table UPDATE and DELETE queries can be simulated."
msgstr ""

#: libraries/classes/Controllers/Import/StatusController.php:66
msgid "Could not load the progress of the import."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:39
#: templates/server/databases/index.twig:318
msgid "Confirm"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:40
#, php-format
msgid "Do you really want to execute \"%s\"?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:41
msgid "You are about to DESTROY a complete database!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:43
msgid "Cannot rename database to the same name. Change the name and try again"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:45
msgid "You are about to DESTROY a complete table!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:46
msgid "You are about to TRUNCATE a complete table!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:47
msgid "You are about to DELETE all the rows of the table!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:48
msgid "Delete tracking data for this table?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:49
msgid "Delete tracking data for these tables?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:50
msgid "Delete tracking data for this version?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:51
msgid "Delete tracking data for these versions?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:52
msgid "Delete entry from tracking report?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:53
msgid "Deleting tracking data"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:54
msgid "Dropping Primary Key/Index"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:55
msgid "Dropping Foreign key."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:56
msgid "This operation could take a long time. Proceed anyway?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:57
#, php-format
msgid "Do you really want to delete user group \"%s\"?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:58
#, php-format
msgid "Do you really want to delete the search \"%s\"?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:59
msgid "You have unsaved changes; are you sure you want to leave this page?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:61
msgid ""
"You are trying to reduce the number of rows, but have already entered data "
"in those rows which will be lost. Do you wish to continue?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:64
msgid "Do you really want to revoke the selected user(s) ?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:65
msgid "Do you really want to delete this central column?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:66
msgid "Do you really want to delete the selected items?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:68
msgid ""
"Do you really want to DROP the selected partition(s)? This will also DELETE "
"the data related to the selected partition(s)!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:71
msgid "Do you really want to TRUNCATE the selected partition(s)?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:72
msgid "Do you really want to remove partitioning?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:73
msgid "Do you really want to reset the replica (RESET REPLICA)?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:75
msgid ""
"This operation will attempt to convert your data to the new collation. In "
"rare cases, especially where a character doesn't exist in the new collation, "
"this process could cause the data to appear incorrectly under the new "
"collation; in this case we suggest you revert to the original collation and "
"refer to the tips at "
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:81
msgid "Garbled Data"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:83
msgid "Are you sure you wish to change the collation and convert the data?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:86
msgid ""
"Through this operation, MySQL attempts to map the data values between "
"collations. If the character sets are incompatible, there may be data loss "
"and this lost data may <b>NOT</b> be recoverable simply by changing back the "
"column collation(s). <b>To convert existing data, it is suggested to use the "
"column(s) editing feature (the \"Change\" Link) on the table structure page. "
"</b>"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:94
msgid ""
"Are you sure you wish to change all the column collations and convert the "
"data?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:97
#: templates/export.twig:324
msgid "Save & close"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:98
#: templates/config/form_display/display.twig:47
#: templates/preferences/manage/main.twig:114
#: templates/preferences/manage/main.twig:122
#: templates/table/insert/actions_panel.twig:36
msgid "Reset"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:99
#, fuzzy
#| msgid "Select All"
msgid "Reset all"
msgstr "Alle ußwähle"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:102
msgid "Missing value in the form!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:103
msgid "Select at least one of the options!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:104
msgid "Please enter a valid number!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:105
msgid "Please enter a valid length!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:106
#, fuzzy
#| msgid "Add a point"
msgid "Add index"
msgstr "Donn ene Pungk derbei"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:107
msgid "Edit index"
msgstr ""

#. l10n: Rename a table Index
#: libraries/classes/Controllers/JavaScriptMessagesController.php:109
#, fuzzy
#| msgid "Select All"
msgid "Rename index"
msgstr "Alle ußwähle"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:110
#: templates/table/index_form.twig:237
#, php-format
msgid "Add %s column(s) to index"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:111
msgid "Create single-column index"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:112
msgid "Create composite index"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:113
msgid "Composite with:"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:114
msgid "Please select column(s) for the index."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:117
#: templates/columns_definitions/column_definitions_form.twig:157
#: templates/modals/index_dialog_modal.twig:11
#: templates/modals/index_dialog_modal.twig:21
#: templates/table/index_form.twig:246
#: templates/table/index_rename_form.twig:28
#: templates/table/insert/actions_panel.twig:35
#: templates/table/relation/common_form.twig:224
#: templates/table/structure/display_structure.twig:344
#: templates/table/structure/display_structure.twig:354
msgid "Preview SQL"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:120
#: templates/sql/query.twig:218
msgid "Simulate query"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:121
msgid "Matched rows:"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:122
#: libraries/classes/Html/Generator.php:880 templates/export.twig:67
msgid "SQL query:"
msgstr ""

#. l10n: Default label for the y-Axis of Charts
#: libraries/classes/Controllers/JavaScriptMessagesController.php:126
msgid "Y values"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:129
msgid "Please enter the SQL query first."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:132
msgid "The host name is empty!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:133
msgid "The user name is empty!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:134
#: libraries/classes/Server/Privileges.php:943
#: libraries/classes/UserPassword.php:42
msgid "The password is empty!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:135
#: libraries/classes/Server/Privileges.php:941
#: libraries/classes/UserPassword.php:46
msgid "The passwords aren't the same!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:136
msgid "Removing Selected Users"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:137
#: libraries/classes/Tracking.php:237 libraries/classes/Tracking.php:641
#: templates/columns_definitions/column_definitions_form.twig:176
#: templates/columns_definitions/column_definitions_form.twig:181
#: templates/error/report_modal.twig:14 templates/export_modal.twig:6
#: templates/export_modal.twig:10 templates/export.twig:82
#: templates/export.twig:92 templates/export.twig:194
#: templates/home/<USER>/home/<USER>
#: templates/modals/build_query.twig:6 templates/modals/build_query.twig:16
#: templates/modals/create_view.twig:6 templates/modals/create_view.twig:11
#: templates/modals/enum_set_editor.twig:6
#: templates/modals/enum_set_editor.twig:11
#: templates/modals/index_dialog_modal.twig:6
#: templates/modals/index_dialog_modal.twig:12
#: templates/modals/index_dialog_modal.twig:22
#: templates/modals/preview_sql_confirmation.twig:6
#: templates/modals/preview_sql_confirmation.twig:15
#: templates/modals/preview_sql_modal.twig:6
#: templates/modals/preview_sql_modal.twig:10
#: templates/modals/unhide_nav_item.twig:6
#: templates/modals/unhide_nav_item.twig:10
#: templates/server/privileges/users_overview.twig:169
#: templates/server/privileges/users_overview.twig:177
#: templates/server/status/advisor/index.twig:20
#: templates/server/status/advisor/index.twig:45
#: templates/server/status/monitor/index.twig:129
#: templates/server/status/monitor/index.twig:218
#: templates/server/user_groups/user_groups.twig:51
#: templates/server/user_groups/user_groups.twig:55
#: templates/sql/query.twig:219 templates/sql/query.twig:224
#: templates/table/insert/actions_panel.twig:47
#: templates/table/insert/actions_panel.twig:52
#: templates/table/relation/common_form.twig:234
#: templates/table/relation/common_form.twig:239
#: templates/table/search/index.twig:182
#: templates/table/structure/display_structure.twig:329
#: templates/table/structure/display_structure.twig:345
#: templates/table/structure/display_structure.twig:355
#: templates/table/structure/display_structure.twig:373
#: templates/table/zoom_search/result_form.twig:27
#: templates/table/zoom_search/result_form.twig:79
#: templates/table/zoom_search/result_form.twig:96
msgid "Close"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:138
#: templates/server/privileges/users_overview.twig:105
msgctxt "Lock the account."
msgid "Lock"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:139
#: templates/server/privileges/users_overview.twig:102
msgctxt "Unlock the account."
msgid "Unlock"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:140
#: templates/server/privileges/users_overview.twig:100
#, fuzzy
#| msgid "Select All"
msgid "Lock this account."
msgstr "Alle ußwähle"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:141
#: templates/server/privileges/users_overview.twig:100
msgid "Unlock this account."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:144
msgid "Template was created."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:145
msgid "Template was loaded."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:146
msgid "Template was updated."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:147
msgid "Template was deleted."
msgstr ""

#. l10n: Other, small valued, queries
#: libraries/classes/Controllers/JavaScriptMessagesController.php:150
#: libraries/classes/Controllers/Server/Status/QueriesController.php:95
#: libraries/classes/Server/Status/Data.php:163
msgid "Other"
msgstr ""

#. l10n: Thousands separator
#: libraries/classes/Controllers/JavaScriptMessagesController.php:152
#: libraries/classes/Util.php:549 libraries/classes/Util.php:581
msgid ","
msgstr ""

#. l10n: Decimal separator
#: libraries/classes/Controllers/JavaScriptMessagesController.php:154
#: libraries/classes/Util.php:547 libraries/classes/Util.php:579
msgid "."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:156
msgid "Connections / Processes"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:159
msgid "Local monitor configuration incompatible!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:161
msgid ""
"The chart arrangement configuration in your browsers local storage is not "
"compatible anymore to the newer version of the monitor dialog. It is very "
"likely that your current configuration will not work anymore. Please reset "
"your configuration to default in the <i>Settings</i> menu."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:167
msgid "Query cache efficiency"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:168
msgid "Query cache usage"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:169
msgid "Query cache used"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:171
msgid "System CPU usage"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:172
msgid "System memory"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:173
msgid "System swap"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:175
msgid "Average load"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:176
msgid "Total memory"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:177
msgid "Cached memory"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:178
msgid "Buffered memory"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:179
msgid "Free memory"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:180
msgid "Used memory"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:182
msgid "Total swap"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:183
msgid "Cached swap"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:184
msgid "Used swap"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:185
msgid "Free swap"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:187
msgid "Bytes sent"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:188
msgid "Bytes received"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:189
#: templates/server/status/status/index.twig:36
msgid "Connections"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:190
#: templates/server/status/base.twig:11
#: templates/server/status/processes/list.twig:5
msgid "Processes"
msgstr ""

#. l10n: shortcuts for Byte
#: libraries/classes/Controllers/JavaScriptMessagesController.php:193
#: libraries/classes/Util.php:457
msgid "B"
msgstr ""

#. l10n: shortcuts for Kilobyte
#: libraries/classes/Controllers/JavaScriptMessagesController.php:194
#: libraries/classes/Util.php:459
#: templates/server/status/monitor/index.twig:186
msgid "KiB"
msgstr ""

#. l10n: shortcuts for Megabyte
#: libraries/classes/Controllers/JavaScriptMessagesController.php:195
#: libraries/classes/Util.php:461
#: templates/server/status/monitor/index.twig:187
msgid "MiB"
msgstr ""

#. l10n: shortcuts for Gigabyte
#: libraries/classes/Controllers/JavaScriptMessagesController.php:196
#: libraries/classes/Util.php:463
msgid "GiB"
msgstr ""

#. l10n: shortcuts for Terabyte
#: libraries/classes/Controllers/JavaScriptMessagesController.php:197
#: libraries/classes/Util.php:465
msgid "TiB"
msgstr ""

#. l10n: shortcuts for Petabyte
#: libraries/classes/Controllers/JavaScriptMessagesController.php:198
#: libraries/classes/Util.php:467
msgid "PiB"
msgstr ""

#. l10n: shortcuts for Exabyte
#: libraries/classes/Controllers/JavaScriptMessagesController.php:199
#: libraries/classes/Util.php:469
msgid "EiB"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:200
#, php-format
msgid "%d table(s)"
msgstr ""

#. l10n: Questions is the name of a MySQL Status variable
#: libraries/classes/Controllers/JavaScriptMessagesController.php:203
msgid "Questions"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:204
#: templates/server/status/status/index.twig:14
msgid "Traffic"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:205
#: libraries/classes/Menu.php:523 libraries/classes/Util.php:1966
#: templates/server/status/monitor/index.twig:12
msgid "Settings"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:206
#: templates/server/status/monitor/index.twig:217
msgid "Add chart to grid"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:207
msgid "Please add at least one variable to the series!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:208
#: libraries/classes/Display/Results.php:1266
#: libraries/classes/Plugins/Export/ExportSql.php:2233
#: libraries/classes/Plugins/Schema/SchemaPdf.php:99
#: libraries/config.values.php:111
#: templates/columns_definitions/column_attributes.twig:217
#: templates/columns_definitions/column_attributes.twig:240
#: templates/database/central_columns/main.twig:305
#: templates/database/designer/main.twig:588 templates/export.twig:433
#: templates/server/privileges/privileges_summary.twig:30
#: templates/server/status/monitor/index.twig:211
#: templates/server/status/processes/list.twig:72
#: templates/table/zoom_search/index.twig:62
#: templates/table/zoom_search/index.twig:122
msgid "None"
msgstr ""

#. l10n: SQL Query on modal to show exported query
#: libraries/classes/Controllers/JavaScriptMessagesController.php:210
msgid "SQL Query"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:211
msgid "Resume monitor"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:212
msgid "Pause monitor"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:213
#: templates/server/status/processes/index.twig:57
msgid "Start auto refresh"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:214
msgid "Stop auto refresh"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:216
msgid "general_log and slow_query_log are enabled."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:217
msgid "general_log is enabled."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:218
msgid "slow_query_log is enabled."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:219
msgid "slow_query_log and general_log are disabled."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:220
msgid "log_output is not set to TABLE."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:221
msgid "log_output is set to TABLE."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:223
#, php-format
msgid ""
"slow_query_log is enabled, but the server logs only queries that take longer "
"than %d seconds. It is advisable to set this long_query_time 0-2 seconds, "
"depending on your system."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:227
#, php-format
msgid "long_query_time is set to %d second(s)."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:229
msgid ""
"Following settings will be applied globally and reset to default on server "
"restart:"
msgstr ""

#. l10n: %s is FILE or TABLE
#: libraries/classes/Controllers/JavaScriptMessagesController.php:232
#, php-format
msgid "Set log_output to %s"
msgstr ""

#. l10n: Enable in this context means setting a status variable to ON
#: libraries/classes/Controllers/JavaScriptMessagesController.php:234
#, php-format
msgid "Enable %s"
msgstr ""

#. l10n: Disable in this context means setting a status variable to OFF
#: libraries/classes/Controllers/JavaScriptMessagesController.php:236
#, php-format
msgid "Disable %s"
msgstr ""

#. l10n: %d seconds
#: libraries/classes/Controllers/JavaScriptMessagesController.php:238
#, php-format
msgid "Set long_query_time to %d seconds."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:240
msgid ""
"You can't change these variables. Please log in as root or contact your "
"database administrator."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:242
msgid "Change settings"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:243
msgid "Current settings"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:245
msgid "Chart title"
msgstr ""

#. l10n: As in differential values
#: libraries/classes/Controllers/JavaScriptMessagesController.php:247
msgid "Differential"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:248
#, php-format
msgid "Divided by %s"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:249
msgid "Unit"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:251
msgid "From slow log"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:252
msgid "From general log"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:253
msgid "The database name is not known for this query in the server's logs."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:254
msgid "Analysing logs"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:255
msgid "Analysing & loading logs. This may take a while."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:256
msgid "Cancel request"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:258
msgid ""
"This column shows the amount of identical queries that are grouped together. "
"However only the SQL query itself has been used as a grouping criteria, so "
"the other attributes of queries, such as start time, may differ."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:263
msgid ""
"Since grouping of INSERTs queries has been selected, INSERT queries into the "
"same table are also being grouped together, disregarding of the inserted "
"data."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:267
msgid "Log data loaded. Queries executed in this time span:"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:269
msgid "Jump to Log table"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:270
msgid "No data found"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:271
msgid "Log analysed, but no data found in this time span."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:273
msgid "Analyzing…"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:274
msgid "Explain output"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:275
#: libraries/classes/Menu.php:490
#: libraries/classes/Server/Status/Processes.php:134
#: libraries/classes/Util.php:1962 libraries/config.values.php:157
#: templates/database/events/editor_form.twig:25
#: templates/database/events/index.twig:44
#: templates/database/tracking/tables.twig:17
#: templates/table/tracking/main.twig:31
msgid "Status"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:276
#: libraries/classes/Plugins/Export/ExportHtmlword.php:474
#: libraries/classes/Plugins/Export/ExportOdt.php:602
#: libraries/classes/Plugins/Export/ExportTexytext.php:462
#: libraries/classes/Plugins/Export/Helpers/Pdf.php:361
#: libraries/classes/Server/Status/Processes.php:130
#: templates/database/triggers/list.twig:47
#: templates/javascript/variables.twig:81 templates/sql/profiling_chart.twig:17
msgid "Time"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:277
msgid "Total time:"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:278
msgid "Profiling results"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:279
msgctxt "Display format"
msgid "Table"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:280
msgid "Chart"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:282
#: templates/export.twig:210
#, fuzzy
#| msgid "Database"
msgctxt "Alias"
msgid "Database"
msgstr "Dahtebangk"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:283
#: templates/export.twig:224
#, fuzzy
#| msgid "Table"
msgctxt "Alias"
msgid "Table"
msgstr "Tabäll"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:284
#: templates/export.twig:237
#, fuzzy
#| msgid "Column"
msgctxt "Alias"
msgid "Column"
msgstr "Kolonn"

#. l10n: A collection of available filters
#: libraries/classes/Controllers/JavaScriptMessagesController.php:287
msgid "Log table filter options"
msgstr ""

#. l10n: Filter as in "Start Filtering"
#: libraries/classes/Controllers/JavaScriptMessagesController.php:289
msgid "Filter"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:290
msgid "Filter queries by word/regexp:"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:291
msgid "Group queries, ignoring variable data in WHERE clauses"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:292
msgid "Sum of grouped rows:"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:293
#: libraries/classes/Engines/Innodb.php:140
#: templates/server/databases/index.twig:253
msgid "Total:"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:295
msgid "Loading logs"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:296
msgid "Monitor refresh failed"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:298
msgid ""
"While requesting new chart data the server returned an invalid response. "
"This is most likely because your session expired. Reloading the page and "
"reentering your credentials should help."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:302
msgid "Reload page"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:304
msgid "Affected rows:"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:306
msgid "Failed parsing config file. It doesn't seem to be valid JSON code."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:308
msgid ""
"Failed building chart grid with imported config. Resetting to default config…"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:311
msgid "Import monitor configuration"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:312
msgid "Please select the file you want to import:"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:313
#, fuzzy
#| msgid "Select All"
msgid "Please enter a valid table name."
msgstr "Alle ußwähle"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:314
#, fuzzy
#| msgid "Select All"
msgid "Please enter a valid database name."
msgstr "Alle ußwähle"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:315
msgid "No files available on server for import!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:317
msgid "Analyse query"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:320
msgid "Formatting SQL…"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:321
msgid "No parameters found!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:325
#: templates/database/central_columns/main.twig:269
#: templates/database/designer/main.twig:339
#: templates/database/designer/main.twig:390
#: templates/database/designer/main.twig:668
#: templates/database/designer/main.twig:734
#: templates/database/designer/main.twig:873
#: templates/database/designer/main.twig:958
#: templates/database/designer/main.twig:1063
#: templates/database/designer/main.twig:1098
#: templates/database/designer/main.twig:1103
#: templates/database/designer/main.twig:1113
#: templates/database/designer/main.twig:1119
#: templates/database/structure/bulk_action_modal.twig:7
#: templates/database/structure/bulk_action_modal.twig:11
#: templates/database/structure/check_all_tables.twig:49
#: templates/database/structure/check_all_tables.twig:55
#: templates/error/report_modal.twig:6 templates/error/report_modal.twig:11
#: templates/server/databases/index.twig:319
#: templates/server/databases/index.twig:323
#: templates/server/variables/index.twig:15
#: templates/table/search/index.twig:197
msgid "Cancel"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:328
#: templates/header.twig:44
msgid "Page-related settings"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:329
#: templates/config/form_display/display.twig:46
msgid "Apply"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:332
#: templates/home/<USER>/modals/index_dialog_modal.twig:26
#: templates/navigation/main.twig:58
#: templates/server/privileges/users_overview.twig:173
#: templates/server/status/monitor/index.twig:95
#: templates/table/structure/display_structure.twig:359
msgid "Loading…"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:333
msgid "Request aborted!!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:334
msgid "Processing request"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:335
msgid "Request failed!!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:336
#: libraries/classes/Controllers/Sql/EnumValuesController.php:51
#: libraries/classes/Controllers/Sql/SetValuesController.php:54
msgid "Error in processing request"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:337
#, php-format
msgid "Error code: %s"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:338
#, php-format
msgid "Error text: %s"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:340
msgid ""
"It seems that the connection to server has been lost. Please check your "
"network connectivity and server status."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:345
msgid "No accounts selected."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:346
msgid "Dropping column"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:347
msgid "Adding primary key"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:348
#: templates/console/display.twig:129 templates/database/designer/main.twig:337
#: templates/database/designer/main.twig:666
#: templates/database/designer/main.twig:730
#: templates/database/designer/main.twig:869
#: templates/database/designer/main.twig:954
#: templates/database/designer/main.twig:1061
#: templates/modals/preview_sql_confirmation.twig:14
#: templates/table/structure/display_structure.twig:378
msgid "OK"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:349
msgid "Click to dismiss this notification"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:352
#, fuzzy
#| msgid "Select All"
msgid "Renaming databases"
msgstr "Alle ußwähle"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:353
#, fuzzy
#| msgid "Select All"
msgid "Copying database"
msgstr "Alle ußwähle"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:354
msgid "Changing charset"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:355
#: libraries/classes/IndexColumn.php:161 libraries/classes/Index.php:498
#: libraries/classes/Index.php:526
#: libraries/classes/Plugins/Export/ExportHtmlword.php:632
#: libraries/classes/Plugins/Export/ExportLatex.php:632
#: libraries/classes/Plugins/Export/ExportOdt.php:787
#: libraries/classes/Plugins/Export/ExportTexytext.php:626
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:753
#: templates/config/form_display/input.twig:42
#: templates/database/central_columns/main.twig:356
#: templates/database/data_dictionary/index.twig:47
#: templates/database/data_dictionary/index.twig:91
#: templates/database/designer/main.twig:1118
#: templates/database/privileges/index.twig:69
#: templates/database/structure/drop_form.twig:20
#: templates/database/structure/empty_form.twig:20 templates/indexes.twig:65
#: templates/preferences/autoload.twig:12
#: templates/preferences/manage/error.twig:19
#: templates/server/privileges/new_user_ajax.twig:22
#: templates/server/privileges/new_user_ajax.twig:34
#: templates/server/privileges/privileges_summary.twig:37
#: templates/server/privileges/privileges_summary.twig:40
#: templates/server/privileges/privileges_summary.twig:42
#: templates/server/privileges/users_overview.twig:55
#: templates/server/privileges/users_overview.twig:65
#: templates/table/delete/confirm.twig:29
#: templates/table/privileges/index.twig:73
#: templates/table/structure/display_structure.twig:80
#: templates/table/structure/display_structure.twig:532
#: templates/table/structure/drop_confirm.twig:20
#: templates/table/structure/primary.twig:23
#: templates/table/tracking/structure_snapshot_columns.twig:33
#: templates/table/tracking/structure_snapshot_indexes.twig:23
#: templates/table/tracking/structure_snapshot_indexes.twig:24
msgid "No"
msgstr "Nä"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:358
#: templates/database/structure/drop_form.twig:16
#: templates/database/structure/empty_form.twig:16 templates/import.twig:159
#: templates/sql/query.twig:142 templates/table/delete/confirm.twig:25
msgid "Enable foreign key checks"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:361
msgid "Failed to get real row count."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:364
msgid "Searching"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:365
msgid "Hide search results"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:366
msgid "Show search results"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:367
msgid "Browsing"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:368
msgid "Deleting"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:369
#, php-format
msgid "Delete the matches for the %s table?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:372
msgid "The definition of a stored function must contain a RETURN statement!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:374
msgid "No routine is exportable. Required privileges may be lacking."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:377
#, php-format
msgid "Values for column %s"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:378
msgid "Values for a new column"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:379
msgid "Enter each value in a separate field."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:380
#, php-format
msgid "Add %d value(s)"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:383
msgid ""
"Note: If the file contains multiple tables, they will be combined into one."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:386
msgid "Hide query box"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:387
msgid "Show query box"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:389
#: libraries/classes/Display/Results.php:3035
#: libraries/classes/Tracking.php:260 templates/console/bookmark_content.twig:7
#: templates/database/central_columns/main.twig:268
#: templates/database/central_columns/main.twig:380
#: templates/database/central_columns/main.twig:381
#: templates/database/designer/main.twig:388
#: templates/database/qbe/ins_del_and_or_cell.twig:18
#: templates/database/search/results.twig:43
#: templates/display/results/table.twig:235
#: templates/display/results/table.twig:236 templates/export.twig:54
#: templates/server/user_groups/user_groups.twig:37
#: templates/server/user_groups/user_groups.twig:56
#: templates/setup/home/<USER>/sql/query.twig:186
msgid "Delete"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:390
#, php-format
msgid "%d is not valid row number."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:391
#: templates/sql/relational_column_dropdown.twig:3
#: templates/table/insert/column_row.twig:48
#: templates/table/search/input_box.twig:27
msgid "Browse foreign values"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:392
msgid "No previously auto-saved query is available. Loading default query."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:394
msgid ""
"You have a previously saved query. Click Get auto-saved query to load the "
"query."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:396
#, php-format
msgid "Variable %d:"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:399
#: libraries/classes/Normalization.php:1051
msgid "Pick"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:400
msgid "Column selector"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:401
msgid "Search this list"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:403
#, php-format
msgid ""
"No columns in the central list. Make sure the Central columns list for "
"database %s has columns that are not present in the current table."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:406
msgid "See more"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:409
msgid "Add primary key"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:410
msgid "Primary key added."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:411
#: libraries/classes/Normalization.php:282
msgid "Taking you to next step…"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:412
#, php-format
msgid "The first step of normalization is complete for table '%s'."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:413
#: libraries/classes/Normalization.php:540
#: libraries/classes/Normalization.php:602
#: libraries/classes/Normalization.php:699
#: libraries/classes/Normalization.php:775
msgid "End of step"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:414
msgid "Second step of normalization (2NF)"
msgstr ""

#. l10n: Display text for calendar close link
#: libraries/classes/Controllers/JavaScriptMessagesController.php:415
#: libraries/classes/Normalization.php:375
#: templates/javascript/variables.twig:15
msgid "Done"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:416
msgid "Confirm partial dependencies"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:417
msgid "Selected partial dependencies are as follows:"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:419
msgid ""
"Note: a, b -> d,f implies values of columns a and b combined together can "
"determine values of column d and column f."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:422
msgid "No partial dependencies selected!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:423
#: libraries/classes/Export.php:589 libraries/classes/Html/Generator.php:980
#: libraries/classes/Plugins/Schema/ExportRelationSchema.php:292
#: templates/import_status.twig:2 templates/user_password.twig:2
msgid "Back"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:424
msgid "Show me the possible partial dependencies based on data in the table"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:425
msgid "Hide partial dependencies list"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:427
msgid ""
"Sit tight! It may take few seconds depending on data size and column count "
"of the table."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:429
msgid "Step"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:430
msgid "The following actions will be performed:"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:431
#, php-format
msgid "DROP columns %s from the table %s"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:432
msgid "Create the following table"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:435
msgid "Third step of normalization (3NF)"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:436
msgid "Confirm transitive dependencies"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:437
msgid "Selected dependencies are as follows:"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:438
msgid "No dependencies selected!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:441
#: templates/columns_definitions/column_definitions_form.twig:160
#: templates/database/central_columns/edit.twig:22
#: templates/database/central_columns/main.twig:119
#: templates/database/central_columns/main.twig:272
#: templates/server/variables/index.twig:12
#: templates/table/gis_visualization/gis_visualization.twig:40
#: templates/table/insert/actions_panel.twig:7
#: templates/table/relation/common_form.twig:225
#: templates/table/structure/partition_definition_form.twig:12
#: templates/table/zoom_search/result_form.twig:78
msgid "Save"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:444
msgid "Hide search criteria"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:445
msgid "Show search criteria"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:446
msgid "Column maximum:"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:447
msgid "Column minimum:"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:450
msgid "Hide find and replace criteria"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:451
msgid "Show find and replace criteria"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:455
msgid "Each point represents a data row."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:457
msgid "Hovering over a point will show its label."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:459
msgid "To zoom in, select a section of the plot with the mouse."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:461
msgid "Click reset zoom button to come back to original state."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:463
msgid "Click a data point to view and possibly edit the data row."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:465
msgid "The plot can be resized by dragging it along the bottom right corner."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:468
msgid "Select two columns"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:470
msgid "Select two different columns"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:472
msgid "Data point content"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:475
#: libraries/classes/Controllers/JavaScriptMessagesController.php:631
#: libraries/classes/ErrorHandler.php:446 libraries/classes/InsertEdit.php:1996
#: templates/table/index_form.twig:167 templates/table/index_form.twig:203
msgid "Ignore"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:476
#: libraries/classes/Display/Results.php:2971
#: libraries/classes/Html/Generator.php:75
#: templates/display/results/table.twig:231
#: templates/display/results/table.twig:232
msgid "Copy"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:477
#: templates/gis_data_editor_form.twig:74
#: templates/gis_data_editor_form.twig:94
#: templates/gis_data_editor_form.twig:135
#: templates/gis_data_editor_form.twig:187
msgid "X"
msgstr "X"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:478
#: templates/gis_data_editor_form.twig:76
#: templates/gis_data_editor_form.twig:96
#: templates/gis_data_editor_form.twig:137
#: templates/gis_data_editor_form.twig:189
msgid "Y"
msgstr "Y"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:479
msgid "Point"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:480
#, php-format
msgid "Point %d"
msgstr "Pungk %d"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:481
msgid "Linestring"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:482
msgid "Polygon"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:483
#: templates/display/results/table.twig:174
msgid "Geometry"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:484
msgid "Inner ring"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:485
msgid "Outer ring"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:486
#: templates/gis_data_editor_form.twig:99
#: templates/gis_data_editor_form.twig:140
#: templates/gis_data_editor_form.twig:192
msgid "Add a point"
msgstr "Donn ene Pungk derbei"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:487
#: templates/gis_data_editor_form.twig:144
#: templates/gis_data_editor_form.twig:195
msgid "Add an inner ring"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:489
msgid "Do you want to copy encryption key?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:490
msgid "Encryption key"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:493
msgid ""
"The HEX function will treat the integer as a string while calculating the "
"hexadecimal value"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:498
msgid ""
"MySQL accepts additional values not selectable by the slider; key in those "
"values directly if desired"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:504
msgid ""
"MySQL accepts additional values not selectable by the datepicker; key in "
"those values directly if desired"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:510
msgid ""
"Indicates that you have made changes to this page; you will be prompted for "
"confirmation before abandoning changes"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:515
msgid "Select referenced key"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:516
msgid "Select Foreign Key"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:517
msgid "Please select the primary key or a unique key!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:518
#: templates/database/designer/main.twig:98
#: templates/database/designer/main.twig:101
msgid "Choose column to display"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:520
msgid ""
"You haven't saved the changes in the layout. They will be lost if you don't "
"save them. Do you want to continue?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:523
msgid "value/subQuery is empty"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:524
#: templates/database/designer/main.twig:40
#: templates/database/designer/main.twig:43
msgid "Add tables from other databases"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:525
msgid "Page name"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:526
#: templates/database/designer/main.twig:63
#: templates/database/designer/main.twig:66
msgid "Save page"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:527
#: templates/database/designer/main.twig:70
#: templates/database/designer/main.twig:73
msgid "Save page as"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:528
#: templates/database/designer/main.twig:56
#: templates/database/designer/main.twig:59
msgid "Open page"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:529
msgid "Delete page"
msgstr ""

#. l10n: When the user opens a page saved in the Designer
#: libraries/classes/Controllers/JavaScriptMessagesController.php:531
msgid "Some tables saved in this page might have been renamed or deleted."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:532
#: templates/database/designer/main.twig:10
msgid "Untitled"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:533
msgid "Please select a page to continue"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:534
msgid "Please enter a valid page name"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:535
msgid "Do you want to save the changes to the current page?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:536
msgid "Successfully deleted the page"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:537
msgid "Export relational schema"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:538
msgid "Modifications have been saved"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:541
#, php-format
msgid "%d object(s) created."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:542
#, fuzzy
#| msgid "Column"
msgid "Column name"
msgstr "Kolonn"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:543
#: templates/modals/build_query.twig:17 templates/sql/query.twig:178
msgid "Submit"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:546
msgid "Press escape to cancel editing.<br>- Shift+Enter for a newline."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:548
msgid ""
"You have edited some data and they have not been saved. Are you sure you "
"want to leave this page before saving the data?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:551
msgid "Drag to reorder."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:552
msgid "Click to sort results by this column."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:554
msgid ""
"Shift+Click to add this column to ORDER BY clause or to toggle ASC/DESC."
"<br>- Ctrl+Click or Alt+Click (Mac: Shift+Option+Click) to remove column "
"from ORDER BY clause"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:558
msgid "Click to mark/unmark."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:559
msgid "Double-click to copy column name."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:560
msgid "Click the drop-down arrow<br>to toggle column's visibility."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:563
msgid ""
"This table does not contain a unique column. Features related to the grid "
"edit, checkbox, Edit, Copy and Delete links may not work after saving."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:566
msgid "Please enter a valid hexadecimal string. Valid characters are 0-9, A-F."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:568
msgid ""
"Do you really want to see all of the rows? For a big table this could crash "
"the browser."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:570
msgid "Original length"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:573
msgid "cancel"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:574
#: libraries/classes/Controllers/Server/Status/StatusController.php:193
msgid "Aborted"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:576
msgid "Success"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:577
msgid "Import status"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:578
#: templates/navigation/main.twig:84
msgid "Drop files here"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:579
#, fuzzy
#| msgid "Select All"
msgid "Select database first"
msgstr "Alle ußwähle"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:583
msgid "You can also edit most values<br>by double-clicking directly on them."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:585
msgid "You can also edit most values<br>by clicking directly on them."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:588
msgid "Go to link:"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:591
msgid "Generate password"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:592
#: templates/server/replication/primary_add_replica_user.twig:73
msgid "Generate"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:593
#: libraries/classes/Controllers/UserPasswordController.php:84
#: templates/home/<USER>
#: templates/server/privileges/change_password.twig:10
msgid "Change password"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:596
#: templates/table/structure/display_structure.twig:130
msgid "More"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:599
#, fuzzy
#| msgid "not active"
msgid "Show panel"
msgstr "nit aktief"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:600
msgid "Hide panel"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:602
#: libraries/classes/Navigation/NavigationTree.php:1355
msgid "Unlink from main panel"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:606
#: libraries/classes/Setup/Index.php:177
#, php-format
msgid ""
"A newer version of phpMyAdmin is available and you should consider "
"upgrading. The newest version is %s, released on %s."
msgstr ""

#. l10n: Latest available phpMyAdmin version
#: libraries/classes/Controllers/JavaScriptMessagesController.php:610
msgid ", latest stable version:"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:611
msgid "up to date"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:614
msgid ""
"A fatal JavaScript error has occurred. Would you like to send an error "
"report?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:615
msgid "Change report settings"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:616
msgid "Show report details"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:617
msgid ""
"Your export is incomplete, due to a low execution time limit at the PHP "
"level!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:620
#, php-format
msgid ""
"Warning: a form on this page has more than %d fields. On submission, some of "
"the fields might be ignored, due to PHP's max_input_vars configuration."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:626
#: libraries/classes/Controllers/JavaScriptMessagesController.php:639
msgid "Some errors have been detected on the server!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:628
msgid "Please look at the bottom of this window."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:634
#: libraries/classes/ErrorHandler.php:451
msgid "Ignore All"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:641
msgid ""
"As per your settings, they are being submitted currently, please be patient."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:647
msgid "Column name successfully copied to clipboard!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:648
msgid "Column name copying to clipboard failed!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:649
msgid "Successfully copied!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:650
#, fuzzy
#| msgid "Select All"
msgid "Copying failed!"
msgstr "Alle ußwähle"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:653
msgid "Execute this query again?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:654
msgid "Do you really want to delete this bookmark?"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:655
msgid "Some error occurred while getting SQL debug info."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:656
#, php-format
msgid "%s queries executed %s times in %s seconds."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:657
#, php-format
msgid "%s argument(s) passed"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:658
msgid "Show arguments"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:659
msgid "Hide arguments"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:660
msgid "Time taken:"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:662
msgid ""
"There was a problem accessing your browser storage, some features may not "
"work properly for you. It is likely that the browser doesn't support storage "
"or the quota limit has been reached. In Firefox, corrupted storage can also "
"cause such a problem, clearing your \"Offline Website Data\" might help. In "
"Safari, such problem is commonly caused by \"Private Mode Browsing\"."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:669
#, fuzzy
#| msgid "Table"
msgid "Copy tables to"
msgstr "Tabäll"

#: libraries/classes/Controllers/JavaScriptMessagesController.php:670
msgid "Add table prefix"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:671
msgid "Replace table with prefix"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:672
#: templates/database/structure/check_all_tables.twig:28
msgid "Copy table with prefix"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:675
msgid "Extremely weak"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:676
msgid "Very weak"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:677
msgid "Weak"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:678
msgid "Good"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:679
msgid "Strong"
msgstr ""

#. l10n: error code 5 (from U2F API)
#: libraries/classes/Controllers/JavaScriptMessagesController.php:683
msgctxt "U2F error"
msgid "Timed out waiting for security key activation."
msgstr ""

#. l10n: error code 2 (from U2F API)
#: libraries/classes/Controllers/JavaScriptMessagesController.php:685
msgctxt "U2F error"
msgid "Invalid request sent to security key."
msgstr ""

#. l10n: unknown error code (from U2F API)
#: libraries/classes/Controllers/JavaScriptMessagesController.php:687
msgctxt "U2F error"
msgid "Unknown security key error."
msgstr ""

#. l10n: error code 3 (from U2F API)
#: libraries/classes/Controllers/JavaScriptMessagesController.php:689
msgctxt "U2F error"
msgid "Client does not support security key."
msgstr ""

#. l10n: error code 4 (from U2F API) on register
#: libraries/classes/Controllers/JavaScriptMessagesController.php:691
msgctxt "U2F error"
msgid "Failed security key activation."
msgstr ""

#. l10n: error code 4 (from U2F API) on authanticate
#: libraries/classes/Controllers/JavaScriptMessagesController.php:693
msgctxt "U2F error"
msgid "Invalid security key."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:696
msgid ""
"WebAuthn is not available. Please use a supported browser in a secure "
"context (HTTPS)."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:701
msgid ""
"You can not open, save or delete your page layout, as IndexedDB is not "
"working in your browser and your phpMyAdmin configuration storage is not "
"configured for this."
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:706
#, php-format
msgctxt ""
"The table already exists in the designer and can not be added once more."
msgid "Table %s already exists!"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:708
#: libraries/classes/InsertEdit.php:326
#: libraries/classes/Navigation/Nodes/NodeDatabaseChild.php:53
msgid "Hide"
msgstr ""

#: libraries/classes/Controllers/JavaScriptMessagesController.php:709
#: libraries/classes/Controllers/Table/ChangeController.php:187
#: templates/database/multi_table_query/form.twig:49
#: templates/database/tracking/tables.twig:19
#: templates/table/tracking/main.twig:33
msgid "Show"
msgstr ""

#: libraries/classes/Controllers/NavigationController.php:46
msgid "Fatal error: The navigation can only be accessed via AJAX"
msgstr ""

#: libraries/classes/Controllers/NormalizationController.php:40
#: libraries/classes/Normalization.php:245
#, fuzzy
#| msgid "Select All"
msgid "Select one…"
msgstr "Alle ußwähle"

#: libraries/classes/Controllers/NormalizationController.php:41
#: libraries/classes/Normalization.php:246
msgid "No such column"
msgstr ""

#: libraries/classes/Controllers/NormalizationController.php:46
#: libraries/classes/Normalization.php:250 libraries/classes/Types.php:843
msgctxt "string types"
msgid "String"
msgstr ""

#: libraries/classes/Controllers/Preferences/ManageController.php:94
msgid "phpMyAdmin configuration snippet"
msgstr ""

#: libraries/classes/Controllers/Preferences/ManageController.php:95
msgid "Paste it to your config.inc.php"
msgstr ""

#: libraries/classes/Controllers/Preferences/ManageController.php:144
msgid "Could not import configuration"
msgstr ""

#: libraries/classes/Controllers/Preferences/TwoFactorController.php:52
msgid "Two-factor authentication has been removed."
msgstr ""

#: libraries/classes/Controllers/Preferences/TwoFactorController.php:63
msgid "Two-factor authentication has been configured."
msgstr ""

#: libraries/classes/Controllers/Server/DatabasesController.php:312
#: libraries/classes/Import.php:144
#: templates/database/structure/table_header.twig:26
#: templates/table/structure/display_partitions.twig:28
#: templates/table/structure/display_structure.twig:230
#: templates/table/structure/display_table_stats.twig:119
msgid "Rows"
msgstr "Reije"

#: libraries/classes/Controllers/Server/DatabasesController.php:322
#: libraries/classes/Navigation/Nodes/NodeIndexContainer.php:25
#: libraries/classes/Navigation/Nodes/NodeIndexContainer.php:26
#: templates/database/data_dictionary/index.twig:68 templates/indexes.twig:3
#: templates/table/structure/display_structure.twig:462
#: templates/table/tracking/structure_snapshot_indexes.twig:1
msgid "Indexes"
msgstr ""

#: libraries/classes/Controllers/Server/DatabasesController.php:327
#: libraries/classes/Controllers/Server/Status/StatusController.php:147
#: libraries/classes/Controllers/Server/Status/StatusController.php:199
#: templates/table/structure/display_table_stats.twig:45
msgid "Total"
msgstr ""

#: libraries/classes/Controllers/Server/DatabasesController.php:332
#: templates/database/structure/table_header.twig:38
#: templates/table/structure/display_table_stats.twig:32
msgid "Overhead"
msgstr ""

#: libraries/classes/Controllers/Server/Databases/CreateController.php:87
#, php-format
msgid "Database %1$s has been created."
msgstr "Di Dahtebangk %1$s wood aanjelaat."

#: libraries/classes/Controllers/Server/Databases/DestroyController.php:93
#, php-format
msgid "%1$d database has been dropped successfully."
msgid_plural "%1$d databases have been dropped successfully."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: libraries/classes/Controllers/Server/Privileges/AccountLockController.php:47
#, php-format
msgid "The account %s@%s has been successfully locked."
msgstr ""

#: libraries/classes/Controllers/Server/Privileges/AccountUnlockController.php:47
#, php-format
msgid "The account %s@%s has been successfully unlocked."
msgstr ""

#: libraries/classes/Controllers/Server/PrivilegesController.php:152
#: libraries/classes/Controllers/Server/UserGroupsController.php:53
msgid "No Privileges"
msgstr ""

#: libraries/classes/Controllers/Server/PrivilegesController.php:161
msgid "You do not have the privileges to administrate the users!"
msgstr ""

#: libraries/classes/Controllers/Server/PrivilegesController.php:176
msgid ""
"Username and hostname didn't change. If you only want to change the "
"password, 'Change password' tab should be used."
msgstr ""

#: libraries/classes/Controllers/Server/PrivilegesController.php:405
msgid "User has been added."
msgstr ""

#: libraries/classes/Controllers/Server/Status/Processes/KillController.php:42
#, php-format
msgid "Thread %s was successfully killed."
msgstr ""

#: libraries/classes/Controllers/Server/Status/Processes/KillController.php:48
#, php-format
msgid ""
"phpMyAdmin was unable to kill thread %s. It probably has already been closed."
msgstr ""

#: libraries/classes/Controllers/Server/Status/StatusController.php:137
msgid "Received"
msgstr ""

#: libraries/classes/Controllers/Server/Status/StatusController.php:142
msgid "Sent"
msgstr ""

#: libraries/classes/Controllers/Server/Status/StatusController.php:181
msgid "Max. concurrent connections"
msgstr ""

#: libraries/classes/Controllers/Server/Status/StatusController.php:187
msgid "Failed attempts"
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:238
msgid ""
"The number of connections that were aborted because the client died without "
"closing the connection properly."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:241
msgid "The number of failed attempts to connect to the MySQL server."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:243
msgid ""
"The number of transactions that used the temporary binary log cache but that "
"exceeded the value of binlog_cache_size and used a temporary file to store "
"statements from the transaction."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:247
msgid "The number of transactions that used the temporary binary log cache."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:248
msgid ""
"The number of connection attempts (successful or not) to the MySQL server."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:250
msgid ""
"The number of temporary tables on disk created automatically by the server "
"while executing statements. If Created_tmp_disk_tables is big, you may want "
"to increase the tmp_table_size  value to cause temporary tables to be memory-"
"based instead of disk-based."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:256
msgid "How many temporary files mysqld has created."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:258
msgid ""
"The number of in-memory temporary tables created automatically by the server "
"while executing statements."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:262
msgid ""
"The number of rows written with INSERT DELAYED for which some error occurred "
"(probably duplicate key)."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:266
msgid ""
"The number of INSERT DELAYED handler threads in use. Every different table "
"on which one uses INSERT DELAYED gets its own thread."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:270
msgid "The number of INSERT DELAYED rows written."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:271
msgid "The number of executed FLUSH statements."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:272
msgid "The number of internal COMMIT statements."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:273
msgid "The number of times a row was deleted from a table."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:275
msgid ""
"The MySQL server can ask the NDB Cluster storage engine if it knows about a "
"table with a given name. This is called discovery. Handler_discover "
"indicates the number of time tables have been discovered."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:281
msgid ""
"The number of times the first entry was read from an index. If this is high, "
"it suggests that the server is doing a lot of full index scans; for example, "
"SELECT col1 FROM foo, assuming that col1 is indexed."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:287
msgid ""
"The number of requests to read a row based on a key. If this is high, it is "
"a good indication that your queries and tables are properly indexed."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:292
msgid ""
"The number of requests to read the next row in key order. This is "
"incremented if you are querying an index column with a range constraint or "
"if you are doing an index scan."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:297
msgid ""
"The number of requests to read the previous row in key order. This read "
"method is mainly used to optimize ORDER BY … DESC."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:301
msgid ""
"The number of requests to read a row based on a fixed position. This is high "
"if you are doing a lot of queries that require sorting of the result. You "
"probably have a lot of queries that require MySQL to scan whole tables or "
"you have joins that don't use keys properly."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:308
msgid ""
"The number of requests to read the next row in the data file. This is high "
"if you are doing a lot of table scans. Generally this suggests that your "
"tables are not properly indexed or that your queries are not written to take "
"advantage of the indexes you have."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:314
msgid "The number of internal ROLLBACK statements."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:315
msgid "The number of requests to update a row in a table."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:316
msgid "The number of requests to insert a row in a table."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:317
msgid "The number of pages containing data (dirty or clean)."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:318
msgid "The number of pages currently dirty."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:320
msgid "The number of buffer pool pages that have been requested to be flushed."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:322
msgid "The number of free pages."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:324
msgid ""
"The number of latched pages in InnoDB buffer pool. These are pages currently "
"being read or written or that can't be flushed or removed for some other "
"reason."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:329
msgid ""
"The number of pages busy because they have been allocated for administrative "
"overhead such as row locks or the adaptive hash index. This value can also "
"be calculated as Innodb_buffer_pool_pages_total - "
"Innodb_buffer_pool_pages_free - Innodb_buffer_pool_pages_data."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:335
msgid "Total size of buffer pool, in pages."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:337
msgid ""
"The number of \"random\" read-aheads InnoDB initiated. This happens when a "
"query is to scan a large portion of a table but in random order."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:342
msgid ""
"The number of sequential read-aheads InnoDB initiated. This happens when "
"InnoDB does a sequential full table scan."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:345
msgid "The number of logical read requests InnoDB has done."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:347
msgid ""
"The number of logical reads that InnoDB could not satisfy from buffer pool "
"and had to do a single-page read."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:351
msgid ""
"Normally, writes to the InnoDB buffer pool happen in the background. "
"However, if it's necessary to read or create a page and no clean pages are "
"available, it's necessary to wait for pages to be flushed first. This "
"counter counts instances of these waits. If the buffer pool size was set "
"properly, this value should be small."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:358
msgid "The number writes done to the InnoDB buffer pool."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:359
msgid "The number of fsync() operations so far."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:360
msgid "The current number of pending fsync() operations."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:361
msgid "The current number of pending reads."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:362
msgid "The current number of pending writes."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:363
msgid "The amount of data read so far, in bytes."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:364
msgid "The total number of data reads."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:365
msgid "The total number of data writes."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:366
msgid "The amount of data written so far, in bytes."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:368
msgid "The number of pages that have been written for doublewrite operations."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:370
msgid "The number of doublewrite operations that have been performed."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:372
msgid ""
"The number of waits we had because log buffer was too small and we had to "
"wait for it to be flushed before continuing."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:375
msgid "The number of log write requests."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:376
msgid "The number of physical writes to the log file."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:377
msgid "The number of fsync() writes done to the log file."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:378
msgid "The number of pending log file fsyncs."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:379
msgid "Pending log file writes."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:380
msgid "The number of bytes written to the log file."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:381
msgid "The number of pages created."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:383
msgid ""
"The compiled-in InnoDB page size (default 16KB). Many values are counted in "
"pages; the page size allows them to be easily converted to bytes."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:387
msgid "The number of pages read."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:388
msgid "The number of pages written."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:389
msgid "The number of row locks currently being waited for."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:390
msgid "The average time to acquire a row lock, in milliseconds."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:391
msgid "The total time spent in acquiring row locks, in milliseconds."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:392
msgid "The maximum time to acquire a row lock, in milliseconds."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:393
msgid "The number of times a row lock had to be waited for."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:394
msgid "The number of rows deleted from InnoDB tables."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:395
msgid "The number of rows inserted in InnoDB tables."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:396
msgid "The number of rows read from InnoDB tables."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:397
msgid "The number of rows updated in InnoDB tables."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:399
msgid ""
"The number of key blocks in the key cache that have changed but haven't yet "
"been flushed to disk. It used to be known as Not_flushed_key_blocks."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:404
msgid ""
"The number of unused blocks in the key cache. You can use this value to "
"determine how much of the key cache is in use."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:408
msgid ""
"The number of used blocks in the key cache. This value is a high-water mark "
"that indicates the maximum number of blocks that have ever been in use at "
"one time."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:412
msgid "Percentage of used key cache (calculated value)"
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:413
msgid "The number of requests to read a key block from the cache."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:415
msgid ""
"The number of physical reads of a key block from disk. If Key_reads is big, "
"then your key_buffer_size value is probably too small. The cache miss rate "
"can be calculated as Key_reads/Key_read_requests."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:421
msgid ""
"Key cache miss calculated as rate of physical reads compared to read "
"requests (calculated value)"
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:423
msgid "The number of requests to write a key block to the cache."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:424
msgid "The number of physical writes of a key block to disk."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:425
msgid ""
"Percentage of physical writes compared to write requests (calculated value)"
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:427
msgid ""
"The total cost of the last compiled query as computed by the query "
"optimizer. Useful for comparing the cost of different query plans for the "
"same query. The default value of 0 means that no query has been compiled yet."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:433
msgid ""
"The maximum number of connections that have been in use simultaneously since "
"the server started."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:435
msgid "The number of rows waiting to be written in INSERT DELAYED queues."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:437
msgid ""
"The number of tables that have been opened. If opened tables is big, your "
"table_open_cache value is probably too small."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:440
msgid "The number of files that are open."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:441
msgid "The number of streams that are open (used mainly for logging)."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:442
msgid "The number of tables that are open."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:444
msgid ""
"The number of free memory blocks in query cache. High numbers can indicate "
"fragmentation issues, which may be solved by issuing a FLUSH QUERY CACHE "
"statement."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:448
msgid "The amount of free memory for query cache."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:449
msgid "The number of cache hits."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:450
msgid "The number of queries added to the cache."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:452
msgid ""
"The number of queries that have been removed from the cache to free up "
"memory for caching new queries. This information can help you tune the query "
"cache size. The query cache uses a least recently used (LRU) strategy to "
"decide which queries to remove from the cache."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:459
msgid ""
"The number of non-cached queries (not cachable, or not cached due to the "
"query_cache_type setting)."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:461
msgid "The number of queries registered in the cache."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:462
msgid "The total number of blocks in the query cache."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:463
msgid "The status of failsafe replication (not yet implemented)."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:465
msgid ""
"The number of joins that do not use indexes. If this value is not 0, you "
"should carefully check the indexes of your tables."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:468
msgid "The number of joins that used a range search on a reference table."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:470
msgid ""
"The number of joins without keys that check for key usage after each row. "
"(If this is not 0, you should carefully check the indexes of your tables.)"
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:475
msgid ""
"The number of joins that used ranges on the first table. (It's normally not "
"critical even if this is big.)"
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:478
msgid "The number of joins that did a full scan of the first table."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:479
msgid ""
"The number of temporary tables currently open by the replica SQL thread."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:481
msgid ""
"Total (since startup) number of times the replication replica SQL thread has "
"retried transactions."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:483
msgid "This is ON if this server is a replica that is connected to a primary."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:485
msgid ""
"The number of threads that have taken more than slow_launch_time seconds to "
"create."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:487
msgid ""
"The number of queries that have taken more than long_query_time seconds."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:489
msgid ""
"The number of merge passes the sort algorithm has had to do. If this value "
"is large, you should consider increasing the value of the sort_buffer_size "
"system variable."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:493
msgid "The number of sorts that were done with ranges."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:494
msgid "The number of sorted rows."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:495
msgid "The number of sorts that were done by scanning the table."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:496
msgid "The number of times that a table lock was acquired immediately."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:498
msgid ""
"The number of times that a table lock could not be acquired immediately and "
"a wait was needed. If this is high, and you have performance problems, you "
"should first optimize your queries, and then either split your table or "
"tables or use replication."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:504
msgid ""
"The number of threads in the thread cache. The cache hit rate can be "
"calculated as Threads_created/Connections. If this value is red you should "
"raise your thread_cache_size."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:508
msgid "The number of currently open connections."
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:510
msgid ""
"The number of threads created to handle connections. If Threads_created is "
"big, you may want to increase the thread_cache_size value. (Normally this "
"doesn't give a notable performance improvement if you have a good thread "
"implementation.)"
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:516
msgid "Thread cache hit rate (calculated value)"
msgstr ""

#: libraries/classes/Controllers/Server/Status/VariablesController.php:517
msgid "The number of threads that are not sleeping."
msgstr ""

#: libraries/classes/Controllers/Server/UserGroupsFormController.php:46
#: libraries/classes/Util.php:836
msgid "Missing parameter:"
msgstr ""

#: libraries/classes/Controllers/Server/UserGroupsFormController.php:60
msgid "User groups management is not enabled."
msgstr ""

#: libraries/classes/Controllers/Server/Variables/SetVariableController.php:93
msgid "Setting variable failed"
msgstr ""

#: libraries/classes/Controllers/Setup/FormController.php:32
msgid "Incorrect form specified!"
msgstr ""

#: libraries/classes/Controllers/Setup/HomeController.php:42
msgid ""
"You are not using a secure connection; all data (including potentially "
"sensitive information, like passwords) is transferred unencrypted!"
msgstr ""

#: libraries/classes/Controllers/Setup/HomeController.php:47
msgid ""
"If your server is also configured to accept HTTPS requests follow this link "
"to use a secure connection."
msgstr ""

#: libraries/classes/Controllers/Setup/HomeController.php:51
msgid "Insecure connection"
msgstr ""

#: libraries/classes/Controllers/Sql/SqlController.php:235
#: libraries/classes/Sql.php:1153
#, php-format
msgid "Bookmark %s has been created."
msgstr ""

#: libraries/classes/Controllers/Sql/SqlController.php:242
msgid "Bookmark not created!"
msgstr ""

#: libraries/classes/Controllers/Table/AddFieldController.php:151
#: libraries/classes/Controllers/Table/Structure/PartitioningController.php:275
#: libraries/classes/Controllers/Table/Structure/SaveController.php:223
#: libraries/classes/Table/Indexes.php:85
#, php-format
msgid "Table %1$s has been altered successfully."
msgstr ""

#: libraries/classes/Controllers/Table/ChangeRowsController.php:36
#: libraries/classes/Controllers/Table/DeleteConfirmController.php:25
#: libraries/classes/Controllers/Table/ExportRowsController.php:36
msgid "No row selected."
msgstr ""

#: libraries/classes/Controllers/Table/ChartController.php:60
#: libraries/classes/Controllers/Table/GisVisualizationController.php:70
msgid "No SQL query was set to fetch data."
msgstr ""

#: libraries/classes/Controllers/Table/ChartController.php:145
msgid "No numeric columns present in the table to plot."
msgstr ""

#: libraries/classes/Controllers/Table/ChartController.php:208
msgid "No data to display"
msgstr ""

#: libraries/classes/Controllers/Table/CreateController.php:84
#: libraries/classes/Controllers/Table/GetFieldController.php:55
#, php-format
msgid "'%s' database does not exist."
msgstr ""

#: libraries/classes/Controllers/Table/CreateController.php:94
#, php-format
msgid "Table %s already exists!"
msgstr ""

#: libraries/classes/Controllers/Table/DeleteRowsController.php:77
#: libraries/classes/Controllers/Table/FindReplaceController.php:181
#: libraries/classes/Controllers/Table/Maintenance/AnalyzeController.php:83
#: libraries/classes/Controllers/Table/Maintenance/CheckController.php:83
#: libraries/classes/Controllers/Table/Maintenance/ChecksumController.php:83
#: libraries/classes/Controllers/Table/Maintenance/OptimizeController.php:83
#: libraries/classes/Controllers/Table/Maintenance/RepairController.php:83
#: libraries/classes/Controllers/Table/Partition/AnalyzeController.php:55
#: libraries/classes/Controllers/Table/Partition/CheckController.php:55
#: libraries/classes/Controllers/Table/Partition/DropController.php:56
#: libraries/classes/Controllers/Table/Partition/OptimizeController.php:55
#: libraries/classes/Controllers/Table/Partition/RebuildController.php:56
#: libraries/classes/Controllers/Table/Partition/RepairController.php:55
#: libraries/classes/Controllers/Table/Partition/TruncateController.php:56
#: libraries/classes/Controllers/Table/RelationController.php:241
#: libraries/classes/Controllers/View/OperationsController.php:87
#: libraries/classes/Database/Routines.php:1224
#: libraries/classes/Display/Results.php:3693 libraries/classes/Message.php:172
#: templates/sql/query.twig:7
msgid "Your SQL query has been executed successfully."
msgstr ""

#: libraries/classes/Controllers/Table/DropColumnConfirmationController.php:23
#: libraries/classes/Controllers/Table/DropColumnController.php:51
#: libraries/classes/Controllers/Table/Structure/AddIndexController.php:47
#: libraries/classes/Controllers/Table/Structure/BrowseController.php:34
#: libraries/classes/Controllers/Table/Structure/CentralColumnsAddController.php:45
#: libraries/classes/Controllers/Table/Structure/CentralColumnsRemoveController.php:45
#: libraries/classes/Controllers/Table/Structure/ChangeController.php:59
#: libraries/classes/Controllers/Table/Structure/FulltextController.php:47
#: libraries/classes/Controllers/Table/Structure/PrimaryController.php:50
#: libraries/classes/Controllers/Table/Structure/SpatialController.php:47
#: libraries/classes/Controllers/Table/Structure/UniqueController.php:47
msgid "No column selected."
msgstr ""

#: libraries/classes/Controllers/Table/DropColumnController.php:80
#, php-format
msgid "%1$d column has been dropped successfully."
msgid_plural "%1$d columns have been dropped successfully."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: libraries/classes/Controllers/Table/GetFieldController.php:63
msgid "Invalid table name"
msgstr ""

#. l10n: In case a SQL query did not pass a security check
#: libraries/classes/Controllers/Table/GetFieldController.php:72
#: libraries/classes/Controllers/Transformation/WrapperController.php:103
msgid "There is an issue with your request."
msgstr ""

#: libraries/classes/Controllers/Table/GetFieldController.php:86
#: libraries/classes/Database/Routines.php:1243
#: libraries/classes/Import.php:149 libraries/classes/InsertEdit.php:220
#: libraries/classes/Sql.php:967
msgid "MySQL returned an empty result set (i.e. zero rows)."
msgstr ""

#: libraries/classes/Controllers/Table/Maintenance/AnalyzeController.php:75
#: libraries/classes/Controllers/Table/Maintenance/CheckController.php:75
#: libraries/classes/Controllers/Table/Maintenance/ChecksumController.php:75
#: libraries/classes/Controllers/Table/Maintenance/OptimizeController.php:75
#: libraries/classes/Controllers/Table/Maintenance/RepairController.php:75
msgid "Maintenance operations on multiple tables are disabled."
msgstr ""

#: libraries/classes/Controllers/Table/OperationsController.php:109
#: libraries/classes/Controllers/Table/OperationsController.php:298
#: libraries/classes/Controllers/Table/StructureController.php:100
#: libraries/classes/Navigation/Nodes/NodeView.php:28
#: templates/database/structure/show_create.twig:32
#: templates/database/structure/structure_table_row.twig:122
#: templates/database/structure/structure_table_row.twig:186
msgid "View"
msgstr ""

#: libraries/classes/Controllers/Table/Partition/DropController.php:62
#: libraries/classes/Controllers/Table/Partition/RebuildController.php:62
#: libraries/classes/Controllers/Table/Partition/TruncateController.php:62
#: libraries/classes/Controllers/View/OperationsController.php:90
#: libraries/classes/Html/Generator.php:856 libraries/classes/Import.php:133
#: libraries/classes/InsertEdit.php:715 libraries/classes/Message.php:192
#: templates/error/generic.twig:37
#: templates/table/structure/display_structure.twig:372
msgid "Error"
msgstr ""

#: libraries/classes/Controllers/Table/RelationController.php:182
msgid "Display column was successfully updated."
msgstr ""

#: libraries/classes/Controllers/Table/RelationController.php:275
msgid "Internal relationships were successfully updated."
msgstr ""

#: libraries/classes/Controllers/Table/ReplaceController.php:294
#, php-format
msgid "Row: %1$s, Column: %2$s, Error: %3$s"
msgstr ""

#: libraries/classes/Controllers/Table/Structure/ChangeController.php:91
#, php-format
msgid "Failed to get description of column %s!"
msgstr ""

#: libraries/classes/Controllers/Table/StructureController.php:226
#: templates/columns_definitions/column_attributes.twig:112
#: templates/database/data_dictionary/index.twig:41
#: templates/table/structure/display_structure.twig:137
#: templates/table/structure/display_structure.twig:145
#: templates/table/structure/display_structure.twig:295
#: templates/table/tracking/structure_snapshot_columns.twig:25
msgid "Primary"
msgstr ""

#: libraries/classes/Controllers/Table/StructureController.php:230
#: libraries/classes/Navigation/Nodes/NodeIndex.php:28
#: templates/columns_definitions/column_attributes.twig:120
#: templates/columns_definitions/table_fields_definitions.twig:46
#: templates/table/structure/display_structure.twig:167
#: templates/table/structure/display_structure.twig:175
#: templates/table/structure/display_structure.twig:301
#: templates/table/structure/display_table_stats.twig:24
#: templates/table/tracking/structure_snapshot_columns.twig:27
msgid "Index"
msgstr ""

#: libraries/classes/Controllers/Table/Structure/MoveColumnsController.php:178
msgid "The columns have been moved successfully."
msgstr ""

#: libraries/classes/Controllers/Table/Structure/PartitioningController.php:267
#: libraries/classes/Controllers/Table/Structure/SaveController.php:273
#: libraries/classes/Tracking.php:776
msgid "Query error"
msgstr ""

#: libraries/classes/Controllers/Table/Structure/ReservedWordCheckController.php:48
#, php-format
msgid "The name '%s' is a MySQL reserved keyword."
msgid_plural "The names '%s' are MySQL reserved keywords."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: libraries/classes/Controllers/Table/Structure/SaveController.php:218
#, php-format
msgid ""
"Table %1$s has been altered successfully. Privileges have been adjusted."
msgstr ""

#: libraries/classes/Controllers/Table/TrackingController.php:69
#, php-format
msgid "Tracking of %s is activated."
msgstr ""

#: libraries/classes/Controllers/Table/TrackingController.php:140
msgid "Tracking versions deleted successfully."
msgstr ""

#: libraries/classes/Controllers/Table/TrackingController.php:145
msgid "No versions selected."
msgstr ""

#: libraries/classes/Controllers/Table/TrackingController.php:174
msgid "SQL statements executed."
msgstr ""

#: libraries/classes/Controllers/UserPasswordController.php:54
msgid "You don't have sufficient privileges to be here right now!"
msgstr ""

#: libraries/classes/Controllers/View/CreateController.php:81
msgid "View name can not be empty!"
msgstr ""

#: libraries/classes/Core.php:203 libraries/classes/ZipExtension.php:62
#, php-format
msgid "The %s extension is missing. Please check your PHP configuration."
msgstr ""

#: libraries/classes/Database/CentralColumns.php:293
#: libraries/classes/Database/CentralColumns.php:399
#: libraries/classes/Database/CentralColumns.php:611
msgid ""
"The configuration storage is not ready for the central list of columns "
"feature."
msgstr ""

#: libraries/classes/Database/CentralColumns.php:351
#, php-format
msgid "Could not add %1$s as they already exist in central list!"
msgstr ""

#: libraries/classes/Database/CentralColumns.php:367
msgid "Could not add columns!"
msgstr ""

#: libraries/classes/Database/CentralColumns.php:451
#, php-format
msgid ""
"Couldn't remove Column(s) %1$s as they don't exist in central columns list!"
msgstr ""

#: libraries/classes/Database/CentralColumns.php:464
msgid "Could not remove columns!"
msgstr ""

#: libraries/classes/Database/CentralColumns.php:625
msgid "YES"
msgstr ""

#: libraries/classes/Database/CentralColumns.php:625
msgid "NO"
msgstr ""

#: libraries/classes/Database/Designer/Common.php:528
msgctxt ""
"phpMyAdmin configuration storage is not configured for \"Display Features\" "
"on designer when user tries to set a display field."
msgid ""
"phpMyAdmin configuration storage is not configured for \"Display Features\"."
msgstr ""

#: libraries/classes/Database/Designer/Common.php:573
msgid "Error: relationship already exists."
msgstr ""

#: libraries/classes/Database/Designer/Common.php:625
msgid "FOREIGN KEY relationship has been added."
msgstr ""

#: libraries/classes/Database/Designer/Common.php:633
msgid "Error: FOREIGN KEY relationship could not be added!"
msgstr ""

#: libraries/classes/Database/Designer/Common.php:640
msgid "Error: Missing index on column(s)."
msgstr ""

#: libraries/classes/Database/Designer/Common.php:648
#: libraries/classes/Database/Designer/Common.php:727
msgid "Error: Relational features are disabled!"
msgstr ""

#: libraries/classes/Database/Designer/Common.php:672
msgid "Internal relationship has been added."
msgstr ""

#: libraries/classes/Database/Designer/Common.php:680
msgid "Error: Internal relationship could not be added!"
msgstr ""

#: libraries/classes/Database/Designer/Common.php:718
msgid "FOREIGN KEY relationship has been removed."
msgstr ""

#: libraries/classes/Database/Designer/Common.php:749
msgid "Error: Internal relationship could not be removed!"
msgstr ""

#: libraries/classes/Database/Designer/Common.php:755
msgid "Internal relationship has been removed."
msgstr ""

#: libraries/classes/Database/Designer.php:136
msgid "Could not load schema plugins, please check your installation!"
msgstr ""

#: libraries/classes/Database/Events.php:104
#: libraries/classes/Database/Events.php:113
#: libraries/classes/Database/Events.php:139
#: libraries/classes/Database/Routines.php:225
#: libraries/classes/Database/Routines.php:246
#: libraries/classes/Database/Routines.php:348
#: libraries/classes/Database/Routines.php:1250
#: libraries/classes/Database/Triggers.php:117
#: libraries/classes/Database/Triggers.php:126
#: libraries/classes/Database/Triggers.php:153
#, php-format
msgid "The following query has failed: \"%s\""
msgstr ""

#: libraries/classes/Database/Events.php:108
#: libraries/classes/Database/Events.php:117
#: libraries/classes/Database/Events.php:143
#: libraries/classes/Database/Events.php:505
#: libraries/classes/Database/Routines.php:229
#: libraries/classes/Database/Routines.php:250
#: libraries/classes/Database/Routines.php:352
#: libraries/classes/Database/Routines.php:1254
#: libraries/classes/Database/Routines.php:1540
#: libraries/classes/Database/Triggers.php:121
#: libraries/classes/Database/Triggers.php:130
#: libraries/classes/Database/Triggers.php:157
#: libraries/classes/Database/Triggers.php:437
#: libraries/classes/Html/Generator.php:924
msgid "MySQL said: "
msgstr ""

#: libraries/classes/Database/Events.php:126
#, php-format
msgid "Event %1$s has been modified."
msgstr ""

#: libraries/classes/Database/Events.php:146
#, php-format
msgid "Event %1$s has been created."
msgstr ""

#: libraries/classes/Database/Events.php:160
#: libraries/classes/Database/Routines.php:266
#: libraries/classes/Database/Triggers.php:174
msgid "One or more errors have occurred while processing your request:"
msgstr ""

#: libraries/classes/Database/Events.php:239
msgid "Add event"
msgstr ""

#: libraries/classes/Database/Events.php:243
msgid "Edit event"
msgstr ""

#: libraries/classes/Database/Events.php:404
#: libraries/classes/Database/Routines.php:984
#: libraries/classes/Database/Triggers.php:381
msgid "The definer must be in the \"username@hostname\" format!"
msgstr ""

#: libraries/classes/Database/Events.php:412
msgid "You must provide an event name!"
msgstr ""

#: libraries/classes/Database/Events.php:426
msgid "You must provide a valid interval value for the event."
msgstr ""

#: libraries/classes/Database/Events.php:446
msgid "You must provide a valid execution time for the event."
msgstr ""

#: libraries/classes/Database/Events.php:450
msgid "You must provide a valid type for the event."
msgstr ""

#: libraries/classes/Database/Events.php:476
msgid "You must provide an event definition."
msgstr ""

#: libraries/classes/Database/Events.php:502
msgid "Sorry, we failed to restore the dropped event."
msgstr ""

#: libraries/classes/Database/Events.php:503
#: libraries/classes/Database/Routines.php:1538
#: libraries/classes/Database/Triggers.php:435
msgid "The backed up query was:"
msgstr ""

#: libraries/classes/Database/Events.php:534
#: libraries/classes/Database/Routines.php:151
#: libraries/classes/Database/Routines.php:1143
#: libraries/classes/Database/Routines.php:1312
#: libraries/classes/Database/Triggers.php:466
msgid "Error in processing request:"
msgstr ""

#: libraries/classes/Database/Events.php:536
#, php-format
msgid "No event with name %1$s found in database %2$s."
msgstr ""

#: libraries/classes/Database/Events.php:568
#, php-format
msgid "Export of event %s"
msgstr ""

#: libraries/classes/Database/Events.php:589
#, php-format
msgid ""
"Error in processing request: No event with name %1$s found in database %2$s."
msgstr ""

#: libraries/classes/DatabaseInterface.php:1121
#, php-format
msgid ""
"Unable to use timezone \"%1$s\" for server %2$d. Please check your "
"configuration setting for [em]$cfg['Servers'][%3$d]['SessionTimeZone'][/em]. "
"phpMyAdmin is currently using the default time zone of the database server."
msgstr ""

#: libraries/classes/DatabaseInterface.php:1166
msgid "Failed to set configured collation connection!"
msgstr ""

#: libraries/classes/DatabaseInterface.php:1859
msgid "Missing connection parameters!"
msgstr ""

#: libraries/classes/DatabaseInterface.php:1884
msgid "Connection for controluser as defined in your configuration failed."
msgstr ""

#: libraries/classes/DatabaseInterface.php:2305
#, php-format
msgid "See %sour documentation%s for more information."
msgstr ""

#: libraries/classes/Database/Qbe.php:756
#: templates/database/qbe/ins_del_and_or_cell.twig:21
msgid "Or:"
msgstr ""

#: libraries/classes/Database/Qbe.php:760
#: templates/database/qbe/ins_del_and_or_cell.twig:9
msgid "And:"
msgstr ""

#: libraries/classes/Database/Qbe.php:766
msgid "Ins"
msgstr ""

#: libraries/classes/Database/Qbe.php:769
msgid "Del"
msgstr ""

#: libraries/classes/Database/Qbe.php:1672
msgid "Saved bookmarked search:"
msgstr ""

#: libraries/classes/Database/Qbe.php:1674
msgid "New bookmark"
msgstr ""

#: libraries/classes/Database/Qbe.php:1701
msgid "Create bookmark"
msgstr ""

#: libraries/classes/Database/Qbe.php:1704
msgid "Update bookmark"
msgstr ""

#: libraries/classes/Database/Qbe.php:1706
msgid "Delete bookmark"
msgstr ""

#: libraries/classes/Database/Routines.php:118
msgid "Add routine"
msgstr ""

#: libraries/classes/Database/Routines.php:122
msgid "Edit routine"
msgstr ""

#: libraries/classes/Database/Routines.php:154
#, php-format
msgid ""
"No routine with name %1$s found in database %2$s. You might be lacking the "
"necessary privileges to edit this routine."
msgstr ""

#: libraries/classes/Database/Routines.php:206
#: libraries/classes/Database/Routines.php:992
#, php-format
msgid "Invalid routine type: \"%s\""
msgstr ""

#: libraries/classes/Database/Routines.php:253
#, php-format
msgid "Routine %1$s has been created."
msgstr ""

#: libraries/classes/Database/Routines.php:413
#, php-format
msgid "Routine %1$s has been modified. Privileges have been adjusted."
msgstr ""

#: libraries/classes/Database/Routines.php:418
#, php-format
msgid "Routine %1$s has been modified."
msgstr ""

#: libraries/classes/Database/Routines.php:825
msgid "You must provide a name and a type for each routine parameter."
msgstr ""

#: libraries/classes/Database/Routines.php:843
#, php-format
msgid "Invalid direction \"%s\" given for parameter."
msgstr ""

#: libraries/classes/Database/Routines.php:863
#: libraries/classes/Database/Routines.php:930
msgid ""
"You must provide length/values for routine parameters of type ENUM, SET, "
"VARCHAR and VARBINARY."
msgstr ""

#: libraries/classes/Database/Routines.php:912
msgid "You must provide a valid return type for the routine."
msgstr ""

#: libraries/classes/Database/Routines.php:1000
msgid "You must provide a routine name!"
msgstr ""

#: libraries/classes/Database/Routines.php:1065
msgid "You must provide a routine definition."
msgstr ""

#: libraries/classes/Database/Routines.php:1145
#: libraries/classes/Database/Routines.php:1314
#, php-format
msgid "No routine with name %1$s found in database %2$s."
msgstr ""

#: libraries/classes/Database/Routines.php:1184
#, php-format
msgid "Execution results of routine %s"
msgstr ""

#: libraries/classes/Database/Routines.php:1232
#, php-format
msgid "%d row affected by the last statement inside the procedure."
msgid_plural "%d rows affected by the last statement inside the procedure."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: libraries/classes/Database/Routines.php:1297
#: libraries/classes/Database/Routines.php:1304
msgid "Execute routine"
msgstr ""

#: libraries/classes/Database/Routines.php:1537
msgid "Sorry, we failed to restore the dropped routine."
msgstr ""

#: libraries/classes/Database/Routines.php:1567
#, php-format
msgid "Export of routine %s"
msgstr ""

#: libraries/classes/Database/Routines.php:1589
#, php-format
msgid ""
"Error in processing request: No routine with name %1$s found in database "
"%2$s. You might be lacking the necessary privileges to view/export this "
"routine."
msgstr ""

#: libraries/classes/Database/Search.php:103
#: templates/database/search/main.twig:19
msgid "at least one of the words"
msgstr ""

#: libraries/classes/Database/Search.php:104
#: templates/database/search/main.twig:23
msgid "all of the words"
msgstr ""

#: libraries/classes/Database/Search.php:105
#: templates/database/search/main.twig:27
msgid "the exact phrase as substring"
msgstr ""

#: libraries/classes/Database/Search.php:106
#: templates/database/search/main.twig:31
msgid "the exact phrase as whole field"
msgstr ""

#: libraries/classes/Database/Search.php:107
#: templates/database/search/main.twig:35
msgid "as regular expression"
msgstr ""

#: libraries/classes/Database/Triggers.php:140
#, php-format
msgid "Trigger %1$s has been modified."
msgstr ""

#: libraries/classes/Database/Triggers.php:160
#, php-format
msgid "Trigger %1$s has been created."
msgstr ""

#: libraries/classes/Database/Triggers.php:255
msgid "Add trigger"
msgstr ""

#: libraries/classes/Database/Triggers.php:259
msgid "Edit trigger"
msgstr ""

#: libraries/classes/Database/Triggers.php:389
msgid "You must provide a trigger name!"
msgstr ""

#: libraries/classes/Database/Triggers.php:395
msgid "You must provide a valid timing for the trigger!"
msgstr ""

#: libraries/classes/Database/Triggers.php:401
msgid "You must provide a valid event for the trigger!"
msgstr ""

#: libraries/classes/Database/Triggers.php:408
msgid "You must provide a valid table name!"
msgstr ""

#: libraries/classes/Database/Triggers.php:415
msgid "You must provide a trigger definition."
msgstr ""

#: libraries/classes/Database/Triggers.php:434
msgid "Sorry, we failed to restore the dropped trigger."
msgstr ""

#: libraries/classes/Database/Triggers.php:468
#, php-format
msgid "No trigger with name %1$s found in database %2$s."
msgstr ""

#: libraries/classes/Database/Triggers.php:502
#: templates/database/triggers/export.twig:2
#, php-format
msgid "Export of trigger %s"
msgstr ""

#: libraries/classes/Database/Triggers.php:520
#, php-format
msgid ""
"Error in processing request: No trigger with name %1$s found in database "
"%2$s."
msgstr ""

#: libraries/classes/Dbal/DbiMysqli.php:152
msgid "SSL connection enforced by server, automatically enabling it."
msgstr ""

#: libraries/classes/Dbal/DbiMysqli.php:164
#, php-format
msgid ""
"Error 1045: Access denied for user. Additional error information may be "
"available, but is being hidden by the %s configuration directive."
msgstr ""

#: libraries/classes/Display/Results.php:897 templates/list_navigator.twig:8
#: templates/list_navigator.twig:13
msgctxt "First page"
msgid "Begin"
msgstr ""

#: libraries/classes/Display/Results.php:904 templates/list_navigator.twig:16
#: templates/list_navigator.twig:21 templates/server/binlog/index.twig:47
#: templates/server/binlog/index.twig:52
msgctxt "Previous page"
msgid "Previous"
msgstr ""

#: libraries/classes/Display/Results.php:930 templates/list_navigator.twig:33
#: templates/list_navigator.twig:35 templates/server/binlog/index.twig:72
#: templates/server/binlog/index.twig:77
msgctxt "Next page"
msgid "Next"
msgstr ""

#: libraries/classes/Display/Results.php:960 templates/list_navigator.twig:42
#: templates/list_navigator.twig:44
msgctxt "Last page"
msgid "End"
msgstr ""

#: libraries/classes/Display/Results.php:1473
#: templates/display/results/table.twig:129
msgid "Partial texts"
msgstr ""

#: libraries/classes/Display/Results.php:1477
#: templates/display/results/table.twig:133
msgid "Full texts"
msgstr ""

#: libraries/classes/Display/Results.php:1818
#: libraries/classes/Display/Results.php:1844 libraries/classes/Util.php:2595
#: libraries/classes/Util.php:2618 libraries/config.values.php:113
#: templates/database/multi_table_query/form.twig:69
#: templates/database/qbe/sort_select_cell.twig:7
#: templates/server/databases/index.twig:111
#: templates/server/databases/index.twig:128
#: templates/server/databases/index.twig:147
#: templates/server/status/processes/list.twig:12
#: templates/table/operations/index.twig:31
#: templates/table/search/index.twig:163
msgid "Descending"
msgstr ""

#: libraries/classes/Display/Results.php:1826
#: libraries/classes/Display/Results.php:1836 libraries/classes/Util.php:2587
#: libraries/classes/Util.php:2610 libraries/config.values.php:112
#: templates/database/multi_table_query/form.twig:68
#: templates/database/qbe/sort_select_cell.twig:5
#: templates/server/databases/index.twig:109
#: templates/server/databases/index.twig:126
#: templates/server/databases/index.twig:145
#: templates/server/status/processes/list.twig:14
#: templates/table/operations/index.twig:27
#: templates/table/search/index.twig:159
msgid "Ascending"
msgstr ""

#: libraries/classes/Display/Results.php:3011
#: libraries/classes/Display/Results.php:3026
msgid "The row has been deleted."
msgstr ""

#: libraries/classes/Display/Results.php:3058
#: templates/server/status/processes/list.twig:44
msgid "Kill"
msgstr ""

#: libraries/classes/Display/Results.php:3626
msgid "May be approximate. See [doc@faq3-11]FAQ 3.11[/doc]."
msgstr ""

#: libraries/classes/Display/Results.php:3989
#, php-format
msgid "Showing rows %1s - %2s"
msgstr ""

#: libraries/classes/Display/Results.php:4003
#, php-format
msgid "%1$d total, %2$d in query"
msgstr ""

#: libraries/classes/Display/Results.php:4008
#, php-format
msgid "%d total"
msgstr ""

#: libraries/classes/Display/Results.php:4021 libraries/classes/Sql.php:973
#, php-format
msgid "Query took %01.4f seconds."
msgstr ""

#: libraries/classes/Display/Results.php:4352
msgid "Link not found!"
msgstr ""

#: libraries/classes/Engines/Bdb.php:28
msgid "Version information"
msgstr ""

#: libraries/classes/Engines/Innodb.php:31
msgid "Data home directory"
msgstr ""

#: libraries/classes/Engines/Innodb.php:32
msgid "The common part of the directory path for all InnoDB data files."
msgstr ""

#: libraries/classes/Engines/Innodb.php:35
msgid "Data files"
msgstr ""

#: libraries/classes/Engines/Innodb.php:38
msgid "Autoextend increment"
msgstr ""

#: libraries/classes/Engines/Innodb.php:40
msgid ""
"The increment size for extending the size of an autoextending tablespace "
"when it becomes full."
msgstr ""

#: libraries/classes/Engines/Innodb.php:45
msgid "Buffer pool size"
msgstr ""

#: libraries/classes/Engines/Innodb.php:46
msgid ""
"The size of the memory buffer InnoDB uses to cache data and indexes of its "
"tables."
msgstr ""

#: libraries/classes/Engines/Innodb.php:107
msgid "Buffer Pool"
msgstr ""

#: libraries/classes/Engines/Innodb.php:108
#: libraries/classes/Server/Status/Data.php:233
msgid "InnoDB Status"
msgstr ""

#: libraries/classes/Engines/Innodb.php:135
msgid "Buffer Pool Usage"
msgstr ""

#: libraries/classes/Engines/Innodb.php:142
msgid "pages"
msgstr ""

#: libraries/classes/Engines/Innodb.php:150
msgid "Free pages"
msgstr ""

#: libraries/classes/Engines/Innodb.php:156
msgid "Dirty pages"
msgstr ""

#: libraries/classes/Engines/Innodb.php:162
msgid "Pages containing data"
msgstr ""

#: libraries/classes/Engines/Innodb.php:168
msgid "Pages to be flushed"
msgstr ""

#: libraries/classes/Engines/Innodb.php:174
msgid "Busy pages"
msgstr ""

#: libraries/classes/Engines/Innodb.php:183
msgid "Latched pages"
msgstr ""

#: libraries/classes/Engines/Innodb.php:194
msgid "Buffer Pool Activity"
msgstr ""

#: libraries/classes/Engines/Innodb.php:198
msgid "Read requests"
msgstr ""

#: libraries/classes/Engines/Innodb.php:204
msgid "Write requests"
msgstr ""

#: libraries/classes/Engines/Innodb.php:210
msgid "Read misses"
msgstr ""

#: libraries/classes/Engines/Innodb.php:216
msgid "Write waits"
msgstr ""

#: libraries/classes/Engines/Innodb.php:222
msgid "Read misses in %"
msgstr ""

#: libraries/classes/Engines/Innodb.php:237
msgid "Write waits in %"
msgstr ""

#: libraries/classes/Engines/Myisam.php:28
msgid "Data pointer size"
msgstr ""

#: libraries/classes/Engines/Myisam.php:30
msgid ""
"The default pointer size in bytes, to be used by CREATE TABLE for MyISAM "
"tables when no MAX_ROWS option is specified."
msgstr ""

#: libraries/classes/Engines/Myisam.php:36
msgid "Automatic recovery mode"
msgstr ""

#: libraries/classes/Engines/Myisam.php:38
msgid ""
"The mode for automatic recovery of crashed MyISAM tables, as set via the --"
"myisam-recover server startup option."
msgstr ""

#: libraries/classes/Engines/Myisam.php:43
msgid "Maximum size for temporary sort files"
msgstr ""

#: libraries/classes/Engines/Myisam.php:45
msgid ""
"The maximum size of the temporary file MySQL is allowed to use while re-"
"creating a MyISAM index (during REPAIR TABLE, ALTER TABLE, or LOAD DATA "
"INFILE)."
msgstr ""

#: libraries/classes/Engines/Myisam.php:52
msgid "Maximum size for temporary files on index creation"
msgstr ""

#: libraries/classes/Engines/Myisam.php:54
msgid ""
"If the temporary file used for fast MyISAM index creation would be larger "
"than using the key cache by the amount specified here, prefer the key cache "
"method."
msgstr ""

#: libraries/classes/Engines/Myisam.php:61
msgid "Repair threads"
msgstr ""

#: libraries/classes/Engines/Myisam.php:63
msgid ""
"If this value is greater than 1, MyISAM table indexes are created in "
"parallel (each index in its own thread) during the repair by sorting process."
msgstr ""

#: libraries/classes/Engines/Myisam.php:70
msgid "Sort buffer size"
msgstr ""

#: libraries/classes/Engines/Myisam.php:72
msgid ""
"The buffer that is allocated when sorting MyISAM indexes during a REPAIR "
"TABLE or when creating indexes with CREATE INDEX or ALTER TABLE."
msgstr ""

#: libraries/classes/Engines/Pbxt.php:33
msgid "Index cache size"
msgstr ""

#: libraries/classes/Engines/Pbxt.php:35
msgid ""
"This is the amount of memory allocated to the index cache. Default value is "
"32MB. The memory allocated here is used only for caching index pages."
msgstr ""

#: libraries/classes/Engines/Pbxt.php:42
msgid "Record cache size"
msgstr ""

#: libraries/classes/Engines/Pbxt.php:44
msgid ""
"This is the amount of memory allocated to the record cache used to cache "
"table data. The default value is 32MB. This memory is used to cache changes "
"to the handle data (.xtd) and row pointer (.xtr) files."
msgstr ""

#: libraries/classes/Engines/Pbxt.php:52
msgid "Log cache size"
msgstr ""

#: libraries/classes/Engines/Pbxt.php:54
msgid ""
"The amount of memory allocated to the transaction log cache used to cache on "
"transaction log data. The default is 16MB."
msgstr ""

#: libraries/classes/Engines/Pbxt.php:61
msgid "Log file threshold"
msgstr ""

#: libraries/classes/Engines/Pbxt.php:63
msgid ""
"The size of a transaction log before rollover, and a new log is created. The "
"default value is 16MB."
msgstr ""

#: libraries/classes/Engines/Pbxt.php:69
msgid "Transaction buffer size"
msgstr ""

#: libraries/classes/Engines/Pbxt.php:71
msgid ""
"The size of the global transaction log buffer (the engine allocates 2 "
"buffers of this size). The default is 1MB."
msgstr ""

#: libraries/classes/Engines/Pbxt.php:78
msgid "Checkpoint frequency"
msgstr ""

#: libraries/classes/Engines/Pbxt.php:80
msgid ""
"The amount of data written to the transaction log before a checkpoint is "
"performed. The default value is 24MB."
msgstr ""

#: libraries/classes/Engines/Pbxt.php:87
msgid "Data log threshold"
msgstr ""

#: libraries/classes/Engines/Pbxt.php:89
msgid ""
"The maximum size of a data log file. The default value is 64MB. PBXT can "
"create a maximum of 32000 data logs, which are used by all tables. So the "
"value of this variable can be increased to increase the total amount of data "
"that can be stored in the database."
msgstr ""

#: libraries/classes/Engines/Pbxt.php:98
msgid "Garbage threshold"
msgstr ""

#: libraries/classes/Engines/Pbxt.php:100
msgid ""
"The percentage of garbage in a data log file before it is compacted. This is "
"a value between 1 and 99. The default is 50."
msgstr ""

#: libraries/classes/Engines/Pbxt.php:107
msgid "Log buffer size"
msgstr ""

#: libraries/classes/Engines/Pbxt.php:109
msgid ""
"The size of the buffer used when writing a data log. The default is 256MB. "
"The engine allocates one buffer per thread, but only if the thread is "
"required to write a data log."
msgstr ""

#: libraries/classes/Engines/Pbxt.php:117
msgid "Data file grow size"
msgstr ""

#: libraries/classes/Engines/Pbxt.php:118
msgid "The grow size of the handle data (.xtd) files."
msgstr ""

#: libraries/classes/Engines/Pbxt.php:122
msgid "Row file grow size"
msgstr ""

#: libraries/classes/Engines/Pbxt.php:123
msgid "The grow size of the row pointer (.xtr) files."
msgstr ""

#: libraries/classes/Engines/Pbxt.php:127
msgid "Log file count"
msgstr ""

#: libraries/classes/Engines/Pbxt.php:129
msgid ""
"This is the number of transaction log files (pbxt/system/xlog*.xt) the "
"system will maintain. If the number of logs exceeds this value then old logs "
"will be deleted, otherwise they are renamed and given the next highest "
"number."
msgstr ""

#: libraries/classes/Engines/Pbxt.php:169
#: libraries/classes/Html/Generator.php:785
#: libraries/classes/Html/MySQLDocumentation.php:50
#: libraries/classes/Sanitize.php:203
#: templates/config/form_display/input.twig:10 templates/home/<USER>
#: templates/server/variables/index.twig:17 templates/setup/home/<USER>
#: templates/setup/home/<USER>
msgid "Documentation"
msgstr ""

#: libraries/classes/Engines/Pbxt.php:185
#, php-format
msgid ""
"Documentation and further information about PBXT can be found on the "
"%sPrimeBase XT Home Page%s."
msgstr ""

#: libraries/classes/ErrorHandler.php:105
msgid "Too many error messages, some are not displayed."
msgstr ""

#: libraries/classes/ErrorHandler.php:435
msgid "Report"
msgstr ""

#: libraries/classes/ErrorHandler.php:440 templates/error/report_form.twig:25
msgid "Automatically send report next time"
msgstr ""

#: libraries/classes/Export.php:160 libraries/classes/Export.php:196
#: libraries/classes/Export.php:470
#, php-format
msgid "Insufficient space to save the file %s."
msgstr ""

#: libraries/classes/Export.php:419
#, php-format
msgid ""
"File %s already exists on server, change filename or check overwrite option."
msgstr ""

#: libraries/classes/Export.php:426 libraries/classes/Export.php:436
#, php-format
msgid "The web server does not have permission to save the file %s."
msgstr ""

#: libraries/classes/Export.php:476
#, php-format
msgid "Dump has been saved to file %s."
msgstr ""

#. l10n: A query written by the user is a "raw query" that could be using no tables or databases in particular
#: libraries/classes/Export.php:985
msgid "Exporting a raw query is not supported for this export method."
msgstr ""

#: libraries/classes/File.php:231
msgid "File was not an uploaded file."
msgstr ""

#: libraries/classes/File.php:266
msgid "The uploaded file exceeds the upload_max_filesize directive in php.ini."
msgstr ""

#: libraries/classes/File.php:271
msgid ""
"The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in "
"the HTML form."
msgstr ""

#: libraries/classes/File.php:276
msgid "The uploaded file was only partially uploaded."
msgstr ""

#: libraries/classes/File.php:280
msgid "Missing a temporary folder."
msgstr ""

#: libraries/classes/File.php:283
msgid "Failed to write file to disk."
msgstr ""

#: libraries/classes/File.php:286
msgid "File upload stopped by extension."
msgstr ""

#: libraries/classes/File.php:289
msgid "Unknown error in file upload."
msgstr ""

#: libraries/classes/File.php:422
msgid "File is a symbolic link"
msgstr ""

#: libraries/classes/File.php:429 libraries/classes/File.php:520
msgid "File could not be read!"
msgstr ""

#: libraries/classes/File.php:465
msgid "Error moving the uploaded file, see [doc@faq1-11]FAQ 1.11[/doc]."
msgstr ""

#: libraries/classes/File.php:485
msgid "Error while moving uploaded file."
msgstr ""

#: libraries/classes/File.php:494
msgid "Cannot read uploaded file."
msgstr ""

#: libraries/classes/File.php:571
#, php-format
msgid ""
"You attempted to load file with unsupported compression (%s). Either support "
"for it is not implemented or disabled by your configuration."
msgstr ""

#: libraries/classes/FlashMessages.php:24
msgid "Session not found."
msgstr ""

#: libraries/classes/Html/Generator.php:146
#, php-format
msgid "Jump to database “%s”."
msgstr ""

#: libraries/classes/Html/Generator.php:174
#, php-format
msgid "The %s functionality is affected by a known bug, see %s"
msgstr ""

#: libraries/classes/Html/Generator.php:242
msgid "SSL is not being used"
msgstr ""

#: libraries/classes/Html/Generator.php:247
msgid "SSL is used with disabled verification"
msgstr ""

#: libraries/classes/Html/Generator.php:249
msgid "SSL is used without certification authority"
msgstr ""

#: libraries/classes/Html/Generator.php:252
msgid "SSL is used"
msgstr ""

#: libraries/classes/Html/Generator.php:362
msgid "The PHP function password_hash() with default options."
msgstr ""

#: libraries/classes/Html/Generator.php:363
msgid "password_hash() PHP function"
msgstr ""

#: libraries/classes/Html/Generator.php:645
msgid "Skip Explain SQL"
msgstr ""

#: libraries/classes/Html/Generator.php:671
#, fuzzy
#| msgid "Created"
msgid "Without PHP code"
msgstr "Aanjelaat"

#: libraries/classes/Html/Generator.php:679
#: templates/database/multi_table_query/form.twig:175
#: templates/database/qbe/selection_form.twig:115
msgid "Submit query"
msgstr ""

#: libraries/classes/Html/Generator.php:726 templates/console/display.twig:31
#: templates/console/display.twig:175 templates/sql/profiling_chart.twig:2
msgid "Profiling"
msgstr ""

#: libraries/classes/Html/Generator.php:739
msgctxt "Inline edit query"
msgid "Edit inline"
msgstr ""

#: libraries/classes/Html/Generator.php:863
msgid "Static analysis:"
msgstr ""

#: libraries/classes/Html/Generator.php:866
#, php-format
msgid "%d errors were found during analysis."
msgstr ""

#: libraries/classes/Import.php:287 libraries/classes/Sql.php:981
msgid "[ROLLBACK occurred.]"
msgstr ""

#: libraries/classes/Import.php:1258
msgid ""
"The following structures have either been created or altered. Here you can:"
msgstr ""

#: libraries/classes/Import.php:1260
msgid "View a structure's contents by clicking on its name."
msgstr ""

#: libraries/classes/Import.php:1261
msgid ""
"Change any of its settings by clicking the corresponding \"Options\" link."
msgstr ""

#: libraries/classes/Import.php:1262
msgid "Edit structure by following the \"Structure\" link."
msgstr ""

#: libraries/classes/Import.php:1266 libraries/classes/Import.php:1300
#: libraries/classes/Plugins/Export/ExportCodegen.php:66
#: libraries/classes/Plugins/Export/ExportCsv.php:46
#: libraries/classes/Plugins/Export/ExportExcel.php:39
#: libraries/classes/Plugins/Export/ExportHtmlword.php:46
#: libraries/classes/Plugins/Export/ExportJson.php:72
#: libraries/classes/Plugins/Export/ExportLatex.php:67
#: libraries/classes/Plugins/Export/ExportMediawiki.php:45
#: libraries/classes/Plugins/Export/ExportOds.php:53
#: libraries/classes/Plugins/Export/ExportOdt.php:59
#: libraries/classes/Plugins/Export/ExportPdf.php:65
#: libraries/classes/Plugins/Export/ExportPhparray.php:45
#: libraries/classes/Plugins/Export/ExportSql.php:115
#: libraries/classes/Plugins/Export/ExportTexytext.php:45
#: libraries/classes/Plugins/Export/ExportXml.php:76
#: libraries/classes/Plugins/Export/ExportYaml.php:44
#: libraries/classes/Plugins/Import/ImportCsv.php:70
#: libraries/classes/Plugins/Import/ImportLdi.php:51
#: libraries/classes/Plugins/Import/ImportMediawiki.php:59
#: libraries/classes/Plugins/Import/ImportOds.php:53
#: libraries/classes/Plugins/Import/ImportShp.php:70
#: libraries/classes/Plugins/Import/ImportSql.php:46
#: libraries/classes/Plugins/Import/ImportXml.php:51
#: templates/console/display.twig:7 templates/console/display.twig:140
#: templates/database/designer/database_tables.twig:114
#: templates/database/routines/editor_form.twig:53
#: templates/table/structure/display_table_stats.twig:97
msgid "Options"
msgstr ""

#: libraries/classes/Import.php:1269
#, php-format
msgid "Go to database: %s"
msgstr ""

#: libraries/classes/Import.php:1275 libraries/classes/Import.php:1318
#, php-format
msgid "Edit settings for %s"
msgstr ""

#: libraries/classes/Import.php:1303
#, php-format
msgid "Go to table: %s"
msgstr ""

#: libraries/classes/Import.php:1311
#, php-format
msgid "Structure of %s"
msgstr ""

#: libraries/classes/Import.php:1329
#, php-format
msgid "Go to view: %s"
msgstr ""

#: libraries/classes/Import.php:1357
msgid ""
"Only INSERT, UPDATE, DELETE and REPLACE SQL queries containing transactional "
"engine tables can be rolled back."
msgstr ""

#: libraries/classes/Index.php:620
#, php-format
msgid ""
"The indexes %1$s and %2$s seem to be equal and one of them could possibly be "
"removed."
msgstr ""

#: libraries/classes/InsertEdit.php:342
#: libraries/classes/Navigation/Nodes/NodeFunction.php:28
#: templates/database/routines/execute_form.twig:18
#: templates/table/search/index.twig:36
#: templates/table/zoom_search/index.twig:34
msgid "Function"
msgstr ""

#: libraries/classes/InsertEdit.php:345
#: libraries/classes/Plugins/Export/ExportHtmlword.php:275
#: libraries/classes/Plugins/Export/ExportHtmlword.php:370
#: libraries/classes/Plugins/Export/ExportLatex.php:543
#: libraries/classes/Plugins/Export/ExportOdt.php:375
#: libraries/classes/Plugins/Export/ExportOdt.php:479
#: libraries/classes/Plugins/Export/ExportTexytext.php:295
#: libraries/classes/Plugins/Export/ExportTexytext.php:387
#: libraries/classes/Plugins/Export/Helpers/Pdf.php:527
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:668
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:702
#: templates/columns_definitions/table_fields_definitions.twig:12
#: templates/database/central_columns/edit.twig:9
#: templates/database/central_columns/main.twig:20
#: templates/database/central_columns/main.twig:224
#: templates/database/data_dictionary/index.twig:23
#: templates/database/data_dictionary/index.twig:74
#: templates/database/events/index.twig:45
#: templates/database/privileges/index.twig:19
#: templates/database/routines/editor_form.twig:26
#: templates/database/routines/editor_form.twig:51
#: templates/database/routines/execute_form.twig:16
#: templates/database/routines/index.twig:50
#: templates/database/structure/table_header.twig:30 templates/indexes.twig:17
#: templates/table/privileges/index.twig:21
#: templates/table/search/index.twig:39
#: templates/table/structure/display_structure.twig:22
#: templates/table/structure/display_structure.twig:476
#: templates/table/tracking/structure_snapshot_columns.twig:7
#: templates/table/tracking/structure_snapshot_indexes.twig:6
#: templates/table/zoom_search/index.twig:37
msgid "Type"
msgstr "Zoot"

#: libraries/classes/InsertEdit.php:716 templates/import.twig:60
#: templates/import.twig:85
msgid "The directory you set for upload work cannot be reached."
msgstr ""

#: libraries/classes/InsertEdit.php:724
#: templates/database/qbe/ins_del_and_or_cell.twig:24
#: templates/server/privileges/privileges_table.twig:32
#: templates/server/privileges/privileges_table.twig:59
#: templates/server/privileges/privileges_table.twig:86
#: templates/server/privileges/privileges_table.twig:113
#: templates/table/search/index.twig:129
msgid "Or"
msgstr ""

#: libraries/classes/InsertEdit.php:725
msgid "web server upload directory:"
msgstr ""

#: libraries/classes/InsertEdit.php:1332 libraries/classes/Sql.php:964
msgid "Showing SQL query"
msgstr ""

#: libraries/classes/InsertEdit.php:1356 libraries/classes/Sql.php:944
#, php-format
msgid "Inserted row id: %1$d"
msgstr ""

#: libraries/classes/LanguageManager.php:979
msgid "Ignoring unsupported language code."
msgstr ""

#: libraries/classes/Linter.php:108
msgid ""
"Linting is disabled for this query because it exceeds the maximum length."
msgstr ""

#: libraries/classes/Linter.php:162
#, php-format
msgid "%1$s (near <code>%2$s</code>)"
msgstr ""

#: libraries/classes/Menu.php:240
#: libraries/classes/Navigation/Nodes/NodeTable.php:313
#: libraries/classes/Util.php:1489 libraries/classes/Util.php:1991
#: libraries/config.values.php:68 libraries/config.values.php:82
#: libraries/config.values.php:183 templates/database/search/results.twig:34
#: templates/database/structure/structure_table_row.twig:43
#: templates/table/structure/display_structure.twig:282
msgid "Browse"
msgstr ""

#: libraries/classes/Menu.php:259 libraries/classes/Menu.php:366
#: libraries/classes/Navigation/Nodes/NodeTable.php:304
#: libraries/classes/Util.php:1487 libraries/classes/Util.php:1977
#: libraries/classes/Util.php:1994 libraries/config.values.php:64
#: libraries/config.values.php:78 libraries/config.values.php:169
#: libraries/config.values.php:179 templates/database/routines/index.twig:27
#: templates/database/routines/index.twig:28
#: templates/database/structure/structure_table_row.twig:53
#: templates/server/databases/index.twig:76
#: templates/server/databases/index.twig:77
msgid "Search"
msgstr ""

#: libraries/classes/Menu.php:270
#: libraries/classes/Navigation/Nodes/NodeTable.php:307
#: libraries/classes/Util.php:1488 libraries/classes/Util.php:1995
#: libraries/config.values.php:66 libraries/config.values.php:80
#: libraries/config.values.php:181
#: templates/database/qbe/ins_del_and_or_cell.twig:6
#: templates/database/structure/structure_table_row.twig:59
#: templates/sql/query.twig:75
msgid "Insert"
msgstr ""

#: libraries/classes/Menu.php:296 libraries/classes/Menu.php:406
#: libraries/classes/Server/Privileges.php:2842 libraries/classes/Util.php:1982
#: libraries/classes/Util.php:1998 libraries/config.values.php:161
#: templates/database/privileges/index.twig:20
#: templates/server/privileges/privileges_summary.twig:15
#: templates/server/sub_page_header.twig:2
#: templates/table/privileges/index.twig:22
msgid "Privileges"
msgstr ""

#: libraries/classes/Menu.php:307 libraries/classes/Menu.php:317
#: libraries/classes/Menu.php:397 libraries/classes/Util.php:1490
#: libraries/classes/Util.php:1981 libraries/classes/Util.php:1999
#: libraries/config.values.php:171 templates/table/operations/view.twig:8
msgid "Operations"
msgstr ""

#: libraries/classes/Menu.php:323 libraries/classes/Menu.php:432
#: libraries/classes/Util.php:1986 libraries/classes/Util.php:2000
msgid "Tracking"
msgstr ""

#: libraries/classes/Menu.php:330 libraries/classes/Menu.php:425
#: libraries/classes/Navigation/Nodes/NodeTriggerContainer.php:25
#: libraries/classes/Navigation/Nodes/NodeTriggerContainer.php:26
#: libraries/classes/Plugins/Export/ExportHtmlword.php:558
#: libraries/classes/Plugins/Export/ExportOdt.php:708
#: libraries/classes/Plugins/Export/ExportPdf.php:270
#: libraries/classes/Plugins/Export/ExportSql.php:2104
#: libraries/classes/Plugins/Export/ExportTexytext.php:548
#: libraries/classes/Plugins/Export/ExportXml.php:120
#: libraries/classes/Util.php:1985 libraries/classes/Util.php:2001
#: templates/database/triggers/list.twig:3
msgid "Triggers"
msgstr ""

#: libraries/classes/Menu.php:371 libraries/classes/Menu.php:379
#: libraries/classes/Menu.php:387
msgid "Database seems to be empty!"
msgstr ""

#: libraries/classes/Menu.php:374 libraries/classes/Util.php:1978
msgid "Query"
msgstr ""

#: libraries/classes/Menu.php:412 libraries/classes/Util.php:1983
#: templates/database/routines/index.twig:3
msgid "Routines"
msgstr ""

#: libraries/classes/Menu.php:418
#: libraries/classes/Navigation/Nodes/NodeEventContainer.php:25
#: libraries/classes/Navigation/Nodes/NodeEventContainer.php:26
#: libraries/classes/Plugins/Export/ExportSql.php:1014
#: libraries/classes/Plugins/Export/ExportXml.php:100
#: libraries/classes/Util.php:1984 templates/database/events/index.twig:3
msgid "Events"
msgstr ""

#: libraries/classes/Menu.php:439 libraries/classes/Util.php:1987
msgid "Designer"
msgstr ""

#: libraries/classes/Menu.php:446 libraries/classes/Util.php:1988
#: templates/database/structure/check_all_tables.twig:32
msgid "Central columns"
msgstr ""

#: libraries/classes/Menu.php:503
msgid "User accounts"
msgstr ""

#: libraries/classes/Menu.php:538 libraries/classes/Server/Status/Data.php:152
#: libraries/classes/Util.php:1967 templates/server/binlog/index.twig:3
msgid "Binary log"
msgstr ""

#: libraries/classes/Menu.php:545 libraries/classes/Server/Status/Data.php:157
#: libraries/classes/Util.php:1968
#: templates/database/structure/body_for_table_summary.twig:11
#: templates/database/structure/table_header.twig:10
#: templates/server/replication/index.twig:5
msgid "Replication"
msgstr ""

#: libraries/classes/Menu.php:551 libraries/classes/Server/Status/Data.php:229
#: libraries/classes/Util.php:1969 libraries/config.values.php:159
#: templates/server/engines/show.twig:18 templates/server/engines/show.twig:21
#: templates/sql/query.twig:191
msgid "Variables"
msgstr ""

#: libraries/classes/Menu.php:556 libraries/classes/Util.php:1970
msgid "Charsets"
msgstr ""

#: libraries/classes/Menu.php:561 libraries/classes/Util.php:1972
msgid "Engines"
msgstr ""

#: libraries/classes/Menu.php:566 libraries/classes/Util.php:1971
#: templates/server/plugins/index.twig:4
msgid "Plugins"
msgstr ""

#: libraries/classes/Message.php:252
#, php-format
msgid "%1$d row affected."
msgid_plural "%1$d rows affected."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: libraries/classes/Message.php:273
#, php-format
msgid "%1$d row deleted."
msgid_plural "%1$d rows deleted."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: libraries/classes/Message.php:294
#, php-format
msgid "%1$d row inserted."
msgid_plural "%1$d rows inserted."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: libraries/classes/Navigation/Navigation.php:231
msgid "Groups:"
msgstr ""

#: libraries/classes/Navigation/Navigation.php:232
msgid "Events:"
msgstr ""

#: libraries/classes/Navigation/Navigation.php:233
msgid "Functions:"
msgstr ""

#: libraries/classes/Navigation/Navigation.php:234
msgid "Procedures:"
msgstr ""

#: libraries/classes/Navigation/Navigation.php:235
#: templates/database/export/index.twig:14
msgid "Tables:"
msgstr ""

#: libraries/classes/Navigation/Navigation.php:236
msgid "Views:"
msgstr ""

#: libraries/classes/Navigation/NavigationTree.php:758
msgid ""
"There are large item groups in navigation panel which may affect the "
"performance. Consider disabling item grouping in the navigation panel."
msgstr ""

#: libraries/classes/Navigation/NavigationTree.php:832
msgid "Groups"
msgstr ""

#: libraries/classes/Navigation/NavigationTree.php:945
#, php-format
msgid "%s result found"
msgid_plural "%s results found"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: libraries/classes/Navigation/NavigationTree.php:1346
msgid "Collapse all"
msgstr ""

#. l10n: The word "Node" must not be translated here
#: libraries/classes/Navigation/NodeFactory.php:43
#, php-format
msgid "Invalid class name \"%1$s\", using default of \"Node\""
msgstr ""

#: libraries/classes/Navigation/NodeFactory.php:73
#, php-format
msgid "Could not load class \"%1$s\""
msgstr ""

#: libraries/classes/Navigation/Nodes/NodeColumnContainer.php:25
#: libraries/classes/Navigation/Nodes/NodeColumnContainer.php:26
#: templates/sql/query.twig:62
msgid "Columns"
msgstr ""

#: libraries/classes/Navigation/Nodes/NodeColumnContainer.php:33
msgctxt "Create new column"
msgid "New"
msgstr ""

#: libraries/classes/Navigation/Nodes/NodeColumn.php:32
#: libraries/classes/Plugins/Export/ExportHtmlword.php:272
#: libraries/classes/Plugins/Export/ExportHtmlword.php:367
#: libraries/classes/Plugins/Export/ExportLatex.php:542
#: libraries/classes/Plugins/Export/ExportOdt.php:372
#: libraries/classes/Plugins/Export/ExportOdt.php:476
#: libraries/classes/Plugins/Export/ExportTexytext.php:294
#: libraries/classes/Plugins/Export/ExportTexytext.php:386
#: libraries/classes/Plugins/Export/Helpers/Pdf.php:525
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:667
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:701
#: templates/columns_definitions/column_name.twig:16
#: templates/database/data_dictionary/index.twig:22
#: templates/database/data_dictionary/index.twig:77 templates/indexes.twig:20
#: templates/table/index_form.twig:141
#: templates/table/insert/get_head_and_foot_of_insert_row_table.twig:5
#: templates/table/operations/index.twig:13
#: templates/table/relation/common_form.twig:18
#: templates/table/relation/common_form.twig:23
#: templates/table/relation/common_form.twig:38
#: templates/table/relation/common_form.twig:120
#: templates/table/relation/common_form.twig:189
#: templates/table/relation/foreign_key_row.twig:120
#: templates/table/relation/foreign_key_row.twig:130
#: templates/table/search/index.twig:38
#: templates/table/structure/display_structure.twig:479
#: templates/table/tracking/structure_snapshot_columns.twig:6
#: templates/table/tracking/structure_snapshot_indexes.twig:9
#: templates/table/zoom_search/index.twig:36
#: templates/table/zoom_search/result_form.twig:35
msgid "Column"
msgstr "Kolonn"

#: libraries/classes/Navigation/Nodes/NodeDatabaseContainer.php:38
msgctxt "Create new database"
msgid "New"
msgstr ""

#: libraries/classes/Navigation/Nodes/NodeDatabase.php:42
msgid "Database operations"
msgstr ""

#: libraries/classes/Navigation/Nodes/NodeDatabase.php:664
msgid "Show hidden items"
msgstr ""

#: libraries/classes/Navigation/Nodes/NodeEventContainer.php:33
msgctxt "Create new event"
msgid "New"
msgstr ""

#: libraries/classes/Navigation/Nodes/NodeEvent.php:28
#: libraries/classes/Plugins/Export/ExportHtmlword.php:475
#: libraries/classes/Plugins/Export/ExportOdt.php:605
#: libraries/classes/Plugins/Export/ExportTexytext.php:463
#: libraries/classes/Plugins/Export/Helpers/Pdf.php:363
#: templates/database/triggers/editor_form.twig:47
#: templates/database/triggers/list.twig:48
msgid "Event"
msgstr ""

#: libraries/classes/Navigation/Nodes/NodeFunctionContainer.php:25
#: libraries/classes/Navigation/Nodes/NodeFunctionContainer.php:26
#: libraries/classes/Plugins/Export/ExportSql.php:634
#: libraries/classes/Plugins/Export/ExportXml.php:105
msgid "Functions"
msgstr ""

#: libraries/classes/Navigation/Nodes/NodeFunctionContainer.php:33
msgctxt "Create new function"
msgid "New"
msgstr ""

#: libraries/classes/Navigation/Nodes/NodeIndexContainer.php:33
msgctxt "Create new index"
msgid "New"
msgstr ""

#: libraries/classes/Navigation/Nodes/Node.php:630
msgid "Expand/Collapse"
msgstr ""

#: libraries/classes/Navigation/Nodes/NodeProcedureContainer.php:25
#: libraries/classes/Navigation/Nodes/NodeProcedureContainer.php:26
#: libraries/classes/Plugins/Export/ExportSql.php:623
#: libraries/classes/Plugins/Export/ExportXml.php:110
msgid "Procedures"
msgstr ""

#: libraries/classes/Navigation/Nodes/NodeProcedureContainer.php:33
msgctxt "Create new procedure"
msgid "New"
msgstr ""

#: libraries/classes/Navigation/Nodes/NodeProcedure.php:28
msgid "Procedure"
msgstr ""

#: libraries/classes/Navigation/Nodes/NodeTableContainer.php:34
msgctxt "Create new table"
msgid "New"
msgstr ""

#: libraries/classes/Navigation/Nodes/NodeTriggerContainer.php:33
msgctxt "Create new trigger"
msgid "New"
msgstr ""

#: libraries/classes/Navigation/Nodes/NodeTrigger.php:28
msgid "Trigger"
msgstr ""

#: libraries/classes/Navigation/Nodes/NodeViewContainer.php:25
#: libraries/classes/Navigation/Nodes/NodeViewContainer.php:26
#: libraries/classes/Plugins/Export/ExportXml.php:125
#: templates/database/structure/show_create.twig:28
msgid "Views"
msgstr ""

#: libraries/classes/Navigation/Nodes/NodeViewContainer.php:34
msgctxt "Create new view"
msgid "New"
msgstr ""

#: libraries/classes/Normalization.php:223
msgid "Make all columns atomic"
msgstr ""

#: libraries/classes/Normalization.php:225
#: libraries/classes/Normalization.php:961
msgid "First step of normalization (1NF)"
msgstr ""

#: libraries/classes/Normalization.php:228
#: libraries/classes/Normalization.php:278
#: libraries/classes/Normalization.php:324
#: libraries/classes/Normalization.php:361
msgid "Step 1."
msgstr ""

#: libraries/classes/Normalization.php:230
msgid ""
"Do you have any column which can be split into more than one column? For "
"example: address can be split into street, city, country and zip."
msgstr ""

#: libraries/classes/Normalization.php:236
msgid "Show me the central list of columns that are not already in this table"
msgstr ""

#: libraries/classes/Normalization.php:238
msgid ""
"Select a column which can be split into more than one (on select of 'no such "
"column', it'll move to next step)."
msgstr ""

#: libraries/classes/Normalization.php:253
msgid "split into "
msgstr ""

#: libraries/classes/Normalization.php:275
msgid "Have a primary key"
msgstr ""

#: libraries/classes/Normalization.php:281
msgid "Primary key already exists."
msgstr ""

#: libraries/classes/Normalization.php:286
msgid ""
"There is no primary key; please add one.<br>Hint: A primary key is a column "
"(or combination of columns) that uniquely identify all rows."
msgstr ""

#: libraries/classes/Normalization.php:294
msgid "Add a primary key on existing column(s)"
msgstr ""

#: libraries/classes/Normalization.php:298
msgid ""
"If it's not possible to make existing column combinations as primary key"
msgstr ""

#: libraries/classes/Normalization.php:300
msgid "+ Add a new primary key column"
msgstr ""

#: libraries/classes/Normalization.php:323
msgid "Remove redundant columns"
msgstr ""

#: libraries/classes/Normalization.php:326
msgid ""
"Do you have a group of columns which on combining gives an existing column? "
"For example, if you have first_name, last_name and full_name then combining "
"first_name and last_name gives full_name which is redundant."
msgstr ""

#: libraries/classes/Normalization.php:332
msgid ""
"Check the columns which are redundant and click on remove. If no redundant "
"column, click on 'No redundant column'"
msgstr ""

#: libraries/classes/Normalization.php:337
msgid "Remove selected"
msgstr ""

#: libraries/classes/Normalization.php:338
msgid "No redundant column"
msgstr ""

#: libraries/classes/Normalization.php:360
msgid "Move repeating groups"
msgstr ""

#: libraries/classes/Normalization.php:363
msgid ""
"Do you have a group of two or more columns that are closely related and are "
"all repeating the same attribute? For example, a table that holds data on "
"books might have columns such as book_id, author1, author2, author3 and so "
"on which form a repeating group. In this case a new table (book_id, author) "
"should be created."
msgstr ""

#: libraries/classes/Normalization.php:371
msgid ""
"Check the columns which form a repeating group. If no such group, click on "
"'No repeating group'"
msgstr ""

#: libraries/classes/Normalization.php:376
msgid "No repeating group"
msgstr ""

#: libraries/classes/Normalization.php:404
msgid "Step 2."
msgstr ""

#: libraries/classes/Normalization.php:404
msgid "Find partial dependencies"
msgstr ""

#: libraries/classes/Normalization.php:425
#, php-format
msgid ""
"No partial dependencies possible as no non-primary column exists since "
"primary key ( %1$s ) is composed of all the columns in the table."
msgstr ""

#: libraries/classes/Normalization.php:431
#: libraries/classes/Normalization.php:473
msgid "Table is already in second normal form."
msgstr ""

#: libraries/classes/Normalization.php:436
#, php-format
msgid ""
"The primary key ( %1$s ) consists of more than one column so we need to find "
"the partial dependencies."
msgstr ""

#: libraries/classes/Normalization.php:440
#: libraries/classes/Normalization.php:870
msgid ""
"Please answer the following question(s) carefully to obtain a correct "
"normalization."
msgstr ""

#: libraries/classes/Normalization.php:442
msgid "+ Show me the possible partial dependencies based on data in the table"
msgstr ""

#: libraries/classes/Normalization.php:445
msgid ""
"For each column below, please select the <b>minimal set</b> of columns among "
"given set whose values combined together are sufficient to determine the "
"value of the column."
msgstr ""

#: libraries/classes/Normalization.php:458
#: libraries/classes/Normalization.php:912
#, php-format
msgid "'%1$s' depends on:"
msgstr ""

#: libraries/classes/Normalization.php:469
#, php-format
msgid ""
"No partial dependencies possible as the primary key ( %1$s ) has just one "
"column."
msgstr ""

#: libraries/classes/Normalization.php:497
#, php-format
msgid ""
"In order to put the original table '%1$s' into Second normal form we need to "
"create the following tables:"
msgstr ""

#: libraries/classes/Normalization.php:535
#, php-format
msgid "The second step of normalization is complete for table '%1$s'."
msgstr ""

#: libraries/classes/Normalization.php:591
#: libraries/classes/Normalization.php:764
#: libraries/classes/Normalization.php:842
msgid "Error in processing!"
msgstr ""

#: libraries/classes/Normalization.php:638
#, php-format
msgid ""
"In order to put the original table '%1$s' into Third normal form we need to "
"create the following tables:"
msgstr ""

#: libraries/classes/Normalization.php:695
msgid "The third step of normalization is complete."
msgstr ""

#: libraries/classes/Normalization.php:815
#, php-format
msgid "Selected repeating group has been moved to the table '%s'"
msgstr ""

#: libraries/classes/Normalization.php:868
msgid "Step 3."
msgstr ""

#: libraries/classes/Normalization.php:868
msgid "Find transitive dependencies"
msgstr ""

#: libraries/classes/Normalization.php:872
msgid ""
"For each column below, please select the <b>minimal set</b> of columns among "
"given set whose values combined together are sufficient to determine the "
"value of the column.<br>Note: A column may have no transitive dependency, in "
"that case you don't have to select any."
msgstr ""

#: libraries/classes/Normalization.php:926
msgid ""
"No Transitive dependencies possible as the table doesn't have any non "
"primary key columns"
msgstr ""

#: libraries/classes/Normalization.php:929
msgid "Table is already in Third normal form!"
msgstr ""

#: libraries/classes/Normalization.php:955
msgid "Improve table structure (Normalization):"
msgstr ""

#: libraries/classes/Normalization.php:956
msgid "Select up to what step you want to normalize"
msgstr ""

#: libraries/classes/Normalization.php:966
msgid "Second step of normalization (1NF+2NF)"
msgstr ""

#: libraries/classes/Normalization.php:971
msgid "Third step of normalization (1NF+2NF+3NF)"
msgstr ""

#: libraries/classes/Normalization.php:976
msgid ""
"Hint: Please follow the procedure carefully in order to obtain correct "
"normalization"
msgstr ""

#: libraries/classes/Normalization.php:1047
msgid ""
"This list is based on a subset of the table's data and is not necessarily "
"accurate. "
msgstr ""

#: libraries/classes/Normalization.php:1062
msgid "No partial dependencies found!"
msgstr ""

#: libraries/classes/Operations.php:555
#: templates/table/structure/display_partitions.twig:84
msgid "Analyze"
msgstr ""

#: libraries/classes/Operations.php:556
#: templates/table/structure/display_partitions.twig:94
msgid "Check"
msgstr ""

#: libraries/classes/Operations.php:557
#: templates/table/structure/display_partitions.twig:104
msgid "Optimize"
msgstr ""

#: libraries/classes/Operations.php:558
#: templates/table/structure/display_partitions.twig:114
msgid "Rebuild"
msgstr ""

#: libraries/classes/Operations.php:559
#: templates/table/structure/display_partitions.twig:124
msgid "Repair"
msgstr ""

#: libraries/classes/Operations.php:560
#: templates/table/structure/display_partitions.twig:134
msgid "Truncate"
msgstr ""

#: libraries/classes/Operations.php:572 templates/database/events/index.twig:19
#: templates/database/events/index.twig:20
#: templates/database/events/index.twig:96
#: templates/database/events/index.twig:102
#: templates/database/events/row.twig:47 templates/database/events/row.twig:53
#: templates/database/routines/index.twig:19
#: templates/database/routines/index.twig:20
#: templates/database/routines/parameter_row.twig:51
#: templates/database/routines/row.twig:77
#: templates/database/structure/check_all_tables.twig:16
#: templates/database/structure/structure_table_row.twig:87
#: templates/database/triggers/list.twig:19
#: templates/database/triggers/list.twig:20
#: templates/database/triggers/row.twig:52
#: templates/database/triggers/row.twig:58 templates/indexes.twig:58
#: templates/server/databases/index.twig:67
#: templates/server/databases/index.twig:68
#: templates/server/databases/index.twig:324
#: templates/table/relation/foreign_key_row.twig:25
#: templates/table/structure/display_partitions.twig:145
#: templates/table/structure/display_structure.twig:120
#: templates/table/structure/display_structure.twig:290
#: templates/table/structure/display_structure.twig:525
msgid "Drop"
msgstr "Falle lohße"

#: libraries/classes/Operations.php:574
msgid "Coalesce"
msgstr ""

#: libraries/classes/Operations.php:930
msgid "Can't move table to same one!"
msgstr ""

#: libraries/classes/Operations.php:932
msgid "Can't copy table to same one!"
msgstr ""

#: libraries/classes/Operations.php:956
#, php-format
msgid "Table %s has been moved to %s. Privileges have been adjusted."
msgstr ""

#: libraries/classes/Operations.php:962
#, php-format
msgid "Table %s has been copied to %s. Privileges have been adjusted."
msgstr ""

#: libraries/classes/Operations.php:969
#, php-format
msgid "Table %s has been moved to %s."
msgstr ""

#: libraries/classes/Operations.php:973
#, php-format
msgid "Table %s has been copied to %s."
msgstr ""

#: libraries/classes/Operations.php:997
msgid "The table name is empty!"
msgstr ""

#: libraries/classes/Pdf.php:136
msgid "Error while creating PDF:"
msgstr ""

#: libraries/classes/Plugins/Auth/AuthenticationConfig.php:81
msgid "Cannot connect: invalid settings."
msgstr ""

#: libraries/classes/Plugins/Auth/AuthenticationConfig.php:95
#: libraries/classes/Plugins/Auth/AuthenticationHttp.php:87
#: templates/login/header.twig:10
#, php-format
msgid "Welcome to %s"
msgstr ""

#: libraries/classes/Plugins/Auth/AuthenticationConfig.php:109
#, php-format
msgid ""
"You probably did not create a configuration file. You might want to use the "
"%1$ssetup script%2$s to create one."
msgstr ""

#: libraries/classes/Plugins/Auth/AuthenticationConfig.php:130
msgid ""
"phpMyAdmin tried to connect to the MySQL server, and the server rejected the "
"connection. You should check the host, username and password in your "
"configuration and make sure that they correspond to the information given by "
"the administrator of the MySQL server."
msgstr ""

#: libraries/classes/Plugins/Auth/AuthenticationConfig.php:151
msgid "Retry to connect"
msgstr ""

#: libraries/classes/Plugins/Auth/AuthenticationCookie.php:133
msgid "Your session has expired. Please log in again."
msgstr ""

#: libraries/classes/Plugins/Auth/AuthenticationCookie.php:252
msgid "Missing reCAPTCHA verification, maybe it has been blocked by adblock?"
msgstr ""

#: libraries/classes/Plugins/Auth/AuthenticationCookie.php:287
msgid "Failed to connect to the reCAPTCHA service!"
msgstr ""

#: libraries/classes/Plugins/Auth/AuthenticationCookie.php:289
msgid "Entered captcha is wrong, try again!"
msgstr ""

#: libraries/classes/Plugins/Auth/AuthenticationCookie.php:301
msgid ""
"Your password is too long. To prevent denial-of-service attacks, phpMyAdmin "
"restricts passwords to less than 1000 characters."
msgstr ""

#: libraries/classes/Plugins/Auth/AuthenticationCookie.php:320
msgid "You are not allowed to log in to this MySQL server!"
msgstr ""

#: libraries/classes/Plugins/Auth/AuthenticationHttp.php:92
msgid "Wrong username/password. Access denied."
msgstr ""

#: libraries/classes/Plugins/Auth/AuthenticationSignon.php:149
msgid "Can not find signon authentication script:"
msgstr ""

#: libraries/classes/Plugins/AuthenticationPlugin.php:180
msgid ""
"Login without a password is forbidden by configuration (see AllowNoPassword)"
msgstr ""

#: libraries/classes/Plugins/AuthenticationPlugin.php:189
#, php-format
msgid ""
"You have been automatically logged out due to inactivity of %s seconds. Once "
"you log in again, you should be able to resume the work where you left off."
msgstr ""

#: libraries/classes/Plugins/AuthenticationPlugin.php:202
#: libraries/classes/Plugins/AuthenticationPlugin.php:205
msgid "Cannot log in to the MySQL server"
msgstr ""

#: libraries/classes/Plugins/AuthenticationPlugin.php:348
msgid "You have enabled two factor authentication, please confirm your login."
msgstr ""

#: libraries/classes/Plugins/Export/ExportCodegen.php:80
#: templates/export.twig:119
msgid "Format:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportCsv.php:58
#: libraries/classes/Plugins/Import/AbstractImportCsv.php:36
msgid "Columns separated with:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportCsv.php:63
#: libraries/classes/Plugins/Import/AbstractImportCsv.php:42
msgid "Columns enclosed with:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportCsv.php:68
#: libraries/classes/Plugins/Import/AbstractImportCsv.php:49
msgid "Columns escaped with:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportCsv.php:73
#: libraries/classes/Plugins/Import/AbstractImportCsv.php:56
msgid "Lines terminated with:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportCsv.php:78
#: libraries/classes/Plugins/Export/ExportExcel.php:51
#: libraries/classes/Plugins/Export/ExportHtmlword.php:80
#: libraries/classes/Plugins/Export/ExportLatex.php:187
#: libraries/classes/Plugins/Export/ExportOds.php:65
#: libraries/classes/Plugins/Export/ExportOdt.php:132
#: libraries/classes/Plugins/Export/ExportTexytext.php:84
msgid "Replace NULL with:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportCsv.php:83
#: libraries/classes/Plugins/Export/ExportExcel.php:56
msgid "Remove carriage return/line feed characters within columns"
msgstr ""

#: libraries/classes/Plugins/Export/ExportExcel.php:66
msgid "Excel edition:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportHtmlword.php:62
#: libraries/classes/Plugins/Export/ExportLatex.php:94
#: libraries/classes/Plugins/Export/ExportMediawiki.php:66
#: libraries/classes/Plugins/Export/ExportOdt.php:75
#: libraries/classes/Plugins/Export/ExportPdf.php:91
#: libraries/classes/Plugins/Export/ExportSql.php:242
#: libraries/classes/Plugins/Export/ExportTexytext.php:61
#: libraries/config.values.php:254 libraries/config.values.php:318
#: libraries/config.values.php:334 libraries/config.values.php:342
#: libraries/config.values.php:347
msgid "structure"
msgstr ""

#: libraries/classes/Plugins/Export/ExportHtmlword.php:63
#: libraries/classes/Plugins/Export/ExportLatex.php:95
#: libraries/classes/Plugins/Export/ExportMediawiki.php:67
#: libraries/classes/Plugins/Export/ExportOdt.php:76
#: libraries/classes/Plugins/Export/ExportPdf.php:92
#: libraries/classes/Plugins/Export/ExportSql.php:243
#: libraries/classes/Plugins/Export/ExportTexytext.php:62
#: libraries/config.values.php:255 libraries/config.values.php:319
#: libraries/config.values.php:335 libraries/config.values.php:343
#: libraries/config.values.php:348
msgid "data"
msgstr ""

#: libraries/classes/Plugins/Export/ExportHtmlword.php:64
#: libraries/classes/Plugins/Export/ExportLatex.php:96
#: libraries/classes/Plugins/Export/ExportMediawiki.php:68
#: libraries/classes/Plugins/Export/ExportOdt.php:77
#: libraries/classes/Plugins/Export/ExportPdf.php:93
#: libraries/classes/Plugins/Export/ExportSql.php:244
#: libraries/classes/Plugins/Export/ExportTexytext.php:63
#: libraries/config.values.php:256 libraries/config.values.php:320
#: libraries/config.values.php:336 libraries/config.values.php:344
#: libraries/config.values.php:349
msgid "structure and data"
msgstr ""

#: libraries/classes/Plugins/Export/ExportHtmlword.php:74
#: libraries/classes/Plugins/Export/ExportLatex.php:158
#: libraries/classes/Plugins/Export/ExportOdt.php:121
#: libraries/classes/Plugins/Export/ExportTexytext.php:73
#: libraries/classes/Plugins/Export/ExportXml.php:133
msgid "Data dump options"
msgstr ""

#: libraries/classes/Plugins/Export/ExportHtmlword.php:194
#: libraries/classes/Plugins/Export/ExportOdt.php:245
#: libraries/classes/Plugins/Export/ExportSql.php:2354
#: libraries/classes/Plugins/Export/ExportTexytext.php:178
msgid "Dumping data for table"
msgstr ""

#: libraries/classes/Plugins/Export/ExportHtmlword.php:278
#: libraries/classes/Plugins/Export/ExportHtmlword.php:373
#: libraries/classes/Plugins/Export/ExportLatex.php:544
#: libraries/classes/Plugins/Export/ExportOdt.php:378
#: libraries/classes/Plugins/Export/ExportOdt.php:482
#: libraries/classes/Plugins/Export/ExportTexytext.php:296
#: libraries/classes/Plugins/Export/ExportTexytext.php:388
#: libraries/classes/Plugins/Export/Helpers/Pdf.php:529
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:670
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:704
#: templates/columns_definitions/table_fields_definitions.twig:30
#: templates/database/central_columns/edit.twig:14
#: templates/database/central_columns/main.twig:40
#: templates/database/central_columns/main.twig:244
#: templates/database/data_dictionary/index.twig:24
#: templates/database/data_dictionary/index.twig:80 templates/indexes.twig:23
#: templates/table/insert/get_head_and_foot_of_insert_row_table.twig:8
#: templates/table/structure/display_structure.twig:25
#: templates/table/structure/display_structure.twig:482
#: templates/table/tracking/structure_snapshot_columns.twig:9
#: templates/table/tracking/structure_snapshot_indexes.twig:12
#: templates/table/zoom_search/result_form.twig:36
msgid "Null"
msgstr "Null"

#: libraries/classes/Plugins/Export/ExportHtmlword.php:281
#: libraries/classes/Plugins/Export/ExportHtmlword.php:376
#: libraries/classes/Plugins/Export/ExportLatex.php:545
#: libraries/classes/Plugins/Export/ExportOdt.php:381
#: libraries/classes/Plugins/Export/ExportOdt.php:485
#: libraries/classes/Plugins/Export/ExportTexytext.php:297
#: libraries/classes/Plugins/Export/ExportTexytext.php:389
#: libraries/classes/Plugins/Export/Helpers/Pdf.php:531
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:671
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:705
#: templates/columns_definitions/table_fields_definitions.twig:20
#: templates/database/central_columns/edit.twig:11
#: templates/database/central_columns/main.twig:28
#: templates/database/central_columns/main.twig:232
#: templates/database/data_dictionary/index.twig:25
#: templates/database/structure/body_for_table_summary.twig:52
#: templates/server/replication/replica_configuration.twig:9
#: templates/table/structure/display_structure.twig:26
#: templates/table/tracking/structure_snapshot_columns.twig:10
msgid "Default"
msgstr "Schtandatt"

#: libraries/classes/Plugins/Export/ExportHtmlword.php:380
#: libraries/classes/Plugins/Export/ExportLatex.php:547
#: libraries/classes/Plugins/Export/ExportOdt.php:489
#: libraries/classes/Plugins/Export/ExportTexytext.php:391
#: libraries/classes/Plugins/Export/Helpers/Pdf.php:540
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:673
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:707
#: templates/database/data_dictionary/index.twig:27
msgid "Links to"
msgstr "Verlengk noh"

#: libraries/classes/Plugins/Export/ExportHtmlword.php:473
#: libraries/classes/Plugins/Export/ExportOdt.php:599
#: libraries/classes/Plugins/Export/ExportTexytext.php:461
#: libraries/classes/Plugins/Export/Helpers/Pdf.php:359
#: templates/columns_definitions/table_fields_definitions.twig:9
#: templates/database/central_columns/edit.twig:8
#: templates/database/central_columns/main.twig:16
#: templates/database/central_columns/main.twig:220
#: templates/database/events/index.twig:43
#: templates/database/routines/editor_form.twig:50
#: templates/database/routines/execute_form.twig:15
#: templates/database/routines/index.twig:49
#: templates/database/triggers/list.twig:43 templates/setup/home/<USER>
#: templates/table/structure/display_structure.twig:21
msgid "Name"
msgstr ""

#: libraries/classes/Plugins/Export/ExportHtmlword.php:476
#: libraries/classes/Plugins/Export/ExportOdt.php:608
#: libraries/classes/Plugins/Export/ExportTexytext.php:464
#: libraries/classes/Plugins/Export/Helpers/Pdf.php:365
#: templates/database/events/editor_form.twig:82
#: templates/database/routines/editor_form.twig:109
#: templates/database/triggers/editor_form.twig:57
msgid "Definition"
msgstr ""

#: libraries/classes/Plugins/Export/ExportHtmlword.php:548
#: libraries/classes/Plugins/Export/ExportOdt.php:686
#: libraries/classes/Plugins/Export/ExportSql.php:2090
#: libraries/classes/Plugins/Export/ExportTexytext.php:528
msgid "Table structure for table"
msgstr ""

#: libraries/classes/Plugins/Export/ExportHtmlword.php:566
#: libraries/classes/Plugins/Export/ExportOdt.php:718
#: libraries/classes/Plugins/Export/ExportSql.php:2141
#: libraries/classes/Plugins/Export/ExportTexytext.php:554
msgid "Structure for view"
msgstr ""

#: libraries/classes/Plugins/Export/ExportHtmlword.php:572
#: libraries/classes/Plugins/Export/ExportOdt.php:738
#: libraries/classes/Plugins/Export/ExportSql.php:2173
#: libraries/classes/Plugins/Export/ExportTexytext.php:570
msgid "Stand-in structure for view"
msgstr ""

#: libraries/classes/Plugins/Export/ExportJson.php:87
msgid "Output pretty-printed JSON (Use human-readable formatting)"
msgstr ""

#: libraries/classes/Plugins/Export/ExportJson.php:93
msgid "Output unicode characters unescaped"
msgstr ""

#: libraries/classes/Plugins/Export/ExportLatex.php:50
msgid "Content of table @TABLE@"
msgstr ""

#: libraries/classes/Plugins/Export/ExportLatex.php:51
msgid "(continued)"
msgstr ""

#: libraries/classes/Plugins/Export/ExportLatex.php:52
msgid "Structure of table @TABLE@"
msgstr ""

#: libraries/classes/Plugins/Export/ExportLatex.php:107
#: libraries/classes/Plugins/Export/ExportOdt.php:88
#: libraries/classes/Plugins/Export/ExportSql.php:257
msgid "Object creation options"
msgstr ""

#: libraries/classes/Plugins/Export/ExportLatex.php:113
#: libraries/classes/Plugins/Export/ExportLatex.php:169
msgid "Table caption:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportLatex.php:119
#: libraries/classes/Plugins/Export/ExportLatex.php:175
msgid "Table caption (continued):"
msgstr ""

#: libraries/classes/Plugins/Export/ExportLatex.php:125
#: libraries/classes/Plugins/Export/ExportLatex.php:181
msgid "Label key:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportLatex.php:133
#: libraries/classes/Plugins/Export/ExportOdt.php:96
#: libraries/classes/Plugins/Export/ExportSql.php:151
msgid "Display foreign key relationships"
msgstr ""

#: libraries/classes/Plugins/Export/ExportLatex.php:140
#: libraries/classes/Plugins/Export/ExportOdt.php:103
msgid "Display comments"
msgstr ""

#: libraries/classes/Plugins/Export/ExportLatex.php:146
#: libraries/classes/Plugins/Export/ExportOdt.php:109
#: libraries/classes/Plugins/Export/ExportSql.php:159
msgid "Display media types"
msgstr ""

#: libraries/classes/Plugins/Export/ExportLatex.php:164
msgid "Put columns names in the first row:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportLatex.php:210
#: libraries/classes/Plugins/Export/ExportSql.php:754
#: libraries/classes/Plugins/Export/ExportXml.php:232
#: templates/server/replication/change_primary.twig:22
#: templates/server/replication/primary_add_replica_user.twig:29
msgid "Host:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportLatex.php:216
#: libraries/classes/Plugins/Export/ExportSql.php:761
#: libraries/classes/Plugins/Export/ExportXml.php:238
msgid "Generation Time:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportLatex.php:218
#: libraries/classes/Plugins/Export/ExportSql.php:765
#: libraries/classes/Plugins/Export/ExportXml.php:240
#: templates/home/<USER>
msgid "Server version:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportLatex.php:219
#: libraries/classes/Plugins/Export/ExportSql.php:767
#: libraries/classes/Plugins/Export/ExportXml.php:241
msgid "PHP Version:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportLatex.php:246
#: libraries/classes/Plugins/Export/ExportSql.php:947
#: libraries/classes/Plugins/Export/ExportXml.php:390
#: libraries/classes/Plugins/Export/Helpers/Pdf.php:201
#: templates/database/structure/copy_form.twig:5
#: templates/menu/breadcrumbs.twig:16
msgid "Database:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportLatex.php:312
#: libraries/classes/Plugins/Export/ExportSql.php:2233
msgid "Data:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportLatex.php:520
msgid "Structure:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportMediawiki.php:77
msgid "Export table names"
msgstr ""

#: libraries/classes/Plugins/Export/ExportMediawiki.php:84
msgid "Export table headers"
msgstr ""

#: libraries/classes/Plugins/Export/ExportOdt.php:246
#: libraries/classes/Plugins/Export/ExportTexytext.php:179
msgid "Dumping data for query result"
msgstr ""

#: libraries/classes/Plugins/Export/ExportPdf.php:77
msgid "Report title:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportPdf.php:192
msgid "Dumping data"
msgstr ""

#: libraries/classes/Plugins/Export/ExportPdf.php:213
msgid "Query result data"
msgstr ""

#: libraries/classes/Plugins/Export/ExportPdf.php:273
msgid "View structure"
msgstr ""

#: libraries/classes/Plugins/Export/ExportPdf.php:276
msgid "Stand in"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:130
msgid ""
"Display comments <i>(includes info such as export timestamp, PHP version, "
"and server version)</i>"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:137
msgid "Additional custom header comment (\\n splits lines):"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:143
msgid ""
"Include a timestamp of when databases were created, last updated, and last "
"checked"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:204
msgid "Export metadata"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:219
msgid ""
"Database system or older MySQL server to maximize output compatibility with:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:265
msgid "Add statements:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:273
#: libraries/classes/Plugins/Export/ExportSql.php:282
#: libraries/classes/Plugins/Export/ExportSql.php:299
#: libraries/classes/Plugins/Export/ExportSql.php:308
#: libraries/classes/Plugins/Export/ExportSql.php:332
#: libraries/classes/Plugins/Export/ExportSql.php:360
#: libraries/classes/Plugins/Export/ExportSql.php:369
#, php-format
msgid "Add %s statement"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:315
msgid "(less efficient as indexes will be generated during table creation)"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:322
#, php-format
msgid "%s value"
msgstr ""

#. l10n: Allow simplifying exported view syntax to only "CREATE VIEW"
#: libraries/classes/Plugins/Export/ExportSql.php:339
msgid "Use simple view export"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:378
msgid ""
"Enclose table and column names with backquotes <i>(Protects column and table "
"names formed with special characters or keywords)</i>"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:393
msgid "Data creation options"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:398
#: libraries/classes/Plugins/Export/ExportSql.php:2311
msgid "Truncate table before insert"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:405
msgid "Instead of <code>INSERT</code> statements, use:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:411
msgid "<code>INSERT DELAYED</code> statements"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:423
#: libraries/classes/Plugins/Export/ExportSql.php:457
msgid "<code>INSERT IGNORE</code> statements"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:437
msgid "Function to use when dumping data:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:452
msgid "Syntax to use when inserting data:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:462
msgid ""
"include column names in every <code>INSERT</code> statement <br> &nbsp; "
"&nbsp; &nbsp; Example: <code>INSERT INTO tbl_name (col_A,col_B,col_C) VALUES "
"(1,2,3)</code>"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:467
msgid ""
"insert multiple rows in every <code>INSERT</code> statement<br> &nbsp; "
"&nbsp; &nbsp; Example: <code>INSERT INTO tbl_name VALUES (1,2,3), (4,5,6), "
"(7,8,9)</code>"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:472
msgid ""
"both of the above<br> &nbsp; &nbsp; &nbsp; Example: <code>INSERT INTO "
"tbl_name (col_A,col_B,col_C) VALUES (1,2,3), (4,5,6), (7,8,9)</code>"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:477
msgid ""
"neither of the above<br> &nbsp; &nbsp; &nbsp; Example: <code>INSERT INTO "
"tbl_name VALUES (1,2,3)</code>"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:496
msgid ""
"Dump binary columns in hexadecimal notation <i>(for example, \"abc\" becomes "
"0x616263)</i>"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:505
msgid ""
"Dump TIMESTAMP columns in UTC <i>(enables TIMESTAMP columns to be dumped and "
"reloaded between servers in different time zones)</i>"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:582
msgid "It appears your database uses routines;"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:585
#: libraries/classes/Plugins/Export/ExportSql.php:1614
#: libraries/classes/Plugins/Export/ExportSql.php:2129
msgid "alias export may not work reliably in all cases."
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:1066
msgid "Metadata"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:1134
#, php-format
msgid "Metadata for table %s"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:1141
#, php-format
msgid "Metadata for database %s"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:1458
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:631
msgid "Creation:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:1468
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:642
msgid "Last update:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:1478
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:653
msgid "Last check:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:1529
#, php-format
msgid "Error reading structure for table %s:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:1611
msgid "It appears your database uses views;"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:1778
msgid "Constraints for dumped tables"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:1779
msgid "Constraints for table"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:1806
msgid "Indexes for dumped tables"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:1807
msgid "Indexes for table"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:1843
msgid "AUTO_INCREMENT for dumped tables"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:1844
msgid "AUTO_INCREMENT for table"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:1919
msgid "MEDIA TYPES FOR TABLE"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:1944
msgid "RELATIONSHIPS FOR TABLE"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:2126
msgid "It appears your table uses triggers;"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:2156
#, php-format
msgid "Structure for view %s exported as a table"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:2176
msgid "(See below for the actual view)"
msgstr ""

#: libraries/classes/Plugins/Export/ExportSql.php:2244
#, php-format
msgid "Error reading data for table %s:"
msgstr ""

#: libraries/classes/Plugins/Export/ExportXml.php:94
msgid "Object creation options (all are recommended)"
msgstr ""

#: libraries/classes/Plugins/Export/ExportXml.php:138
msgid "Export contents"
msgstr ""

#: libraries/classes/Plugins/Export/Helpers/Pdf.php:202
#: templates/menu/breadcrumbs.twig:29
msgid "Table:"
msgstr ""

#: libraries/classes/Plugins/Export/Helpers/Pdf.php:203
msgid "Purpose:"
msgstr ""

#: libraries/classes/Plugins/Import/AbstractImportCsv.php:30
msgid ""
"Update data when duplicate keys found on import (add ON DUPLICATE KEY UPDATE)"
msgstr ""

#: libraries/classes/Plugins/Import/ImportCsv.php:83
msgid "Name of the new table (optional):"
msgstr ""

#: libraries/classes/Plugins/Import/ImportCsv.php:92
msgid "Name of the new database (optional):"
msgstr ""

#: libraries/classes/Plugins/Import/ImportCsv.php:101
#: libraries/classes/Plugins/Import/ImportCsv.php:119
msgid "Import these many number of rows (optional):"
msgstr ""

#: libraries/classes/Plugins/Import/ImportCsv.php:109
#: libraries/classes/Plugins/Import/ImportOds.php:66
msgid ""
"The first line of the file contains the table column names <i>(if this is "
"unchecked, the first line will become part of the data)</i>"
msgstr ""

#: libraries/classes/Plugins/Import/ImportCsv.php:126
msgid ""
"If the data in each row of the file is not in the same order as in the "
"database, list the corresponding column names here. Column names must be "
"separated by commas and not enclosed in quotations."
msgstr ""

#: libraries/classes/Plugins/Import/ImportCsv.php:134
#, fuzzy
#| msgid "Column"
msgid "Column names:"
msgstr "Kolonn"

#: libraries/classes/Plugins/Import/ImportCsv.php:272
#: libraries/classes/Plugins/Import/ImportCsv.php:631
#, php-format
msgid "Invalid format of CSV input on line %d."
msgstr ""

#: libraries/classes/Plugins/Import/ImportCsv.php:490
#, php-format
msgid "Invalid column count in CSV input on line %d."
msgstr ""

#: libraries/classes/Plugins/Import/ImportCsv.php:649
#: libraries/classes/Plugins/Import/ImportCsv.php:664
#: libraries/classes/Plugins/Import/ImportCsv.php:675
#: libraries/classes/Plugins/Import/ImportCsv.php:682
#, php-format
msgid "Invalid parameter for CSV import: %s"
msgstr ""

#: libraries/classes/Plugins/Import/ImportCsv.php:813
#, php-format
msgid ""
"Invalid column (%s) specified! Ensure that columns names are spelled "
"correctly, separated by commas, and not enclosed in quotes."
msgstr ""

#: libraries/classes/Plugins/Import/ImportLdi.php:62
msgid "Column names: "
msgstr ""

#: libraries/classes/Plugins/Import/ImportLdi.php:106
msgid "This plugin does not support compressed imports!"
msgstr ""

#: libraries/classes/Plugins/Import/ImportMediawiki.php:56
msgid "MediaWiki Table"
msgstr ""

#: libraries/classes/Plugins/Import/ImportMediawiki.php:275
#, php-format
msgid "Invalid format of mediawiki input on line: <br>%s."
msgstr ""

#: libraries/classes/Plugins/Import/ImportOds.php:80
msgid "Import percentages as proper decimals <i>(ex. 12.00% to .12)</i>"
msgstr ""

#: libraries/classes/Plugins/Import/ImportOds.php:86
msgid "Import currencies <i>(ex. $5.00 to 5.00)</i>"
msgstr ""

#: libraries/classes/Plugins/Import/ImportOds.php:153
#: libraries/classes/Plugins/Import/ImportXml.php:112
#: libraries/classes/Plugins/Import/ImportXml.php:175
msgid ""
"The XML file specified was either malformed or incomplete. Please correct "
"the issue and try again."
msgstr ""

#: libraries/classes/Plugins/Import/ImportOds.php:163
msgid "Could not parse OpenDocument Spreadsheet!"
msgstr ""

#: libraries/classes/Plugins/Import/ImportShp.php:68
msgid "ESRI Shape File"
msgstr ""

#: libraries/classes/Plugins/Import/ImportShp.php:101
#: libraries/classes/Plugins/Import/ImportShp.php:163
#, php-format
msgid "There was an error importing the ESRI shape file: \"%s\"."
msgstr ""

#: libraries/classes/Plugins/Import/ImportShp.php:193
#, php-format
msgid "MySQL Spatial Extension does not support ESRI type \"%s\"."
msgstr ""

#: libraries/classes/Plugins/Import/ImportShp.php:242
msgid "The imported file does not contain any data!"
msgstr ""

#: libraries/classes/Plugins/Import/ImportSql.php:65
msgid "SQL compatibility mode:"
msgstr ""

#: libraries/classes/Plugins/Import/ImportSql.php:77
msgid "Do not use <code>AUTO_INCREMENT</code> for zero values"
msgstr ""

#: libraries/classes/Plugins/Import/ImportXml.php:48
msgid "XML"
msgstr ""

#: libraries/classes/Plugins.php:589
msgid "This format has no options"
msgstr ""

#: libraries/classes/Plugins.php:607
msgid "Invalid authentication method set in configuration:"
msgstr ""

#: libraries/classes/Plugins/Schema/Dia/TableStatsDia.php:68
#: libraries/classes/Plugins/Schema/Eps/TableStatsEps.php:87
#: libraries/classes/Plugins/Schema/Pdf/TableStatsPdf.php:90
#: libraries/classes/Plugins/Schema/Svg/TableStatsSvg.php:87
#, php-format
msgid "The %s table doesn't exist!"
msgstr ""

#: libraries/classes/Plugins/Schema/Eps/EpsRelationSchema.php:62
#: libraries/classes/Plugins/Schema/Svg/SvgRelationSchema.php:78
#, php-format
msgid "Schema of the %s database - Page %s"
msgstr ""

#: libraries/classes/Plugins/Schema/ExportRelationSchema.php:278
msgid "SCHEMA ERROR: "
msgstr ""

#: libraries/classes/Plugins/Schema/Pdf/Pdf.php:272
msgid "PDF export page"
msgstr ""

#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:135
#, fuzzy, php-format
#| msgid "Select All"
msgid "Schema of the %s database"
msgstr "Alle ußwähle"

#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:163
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:541
msgid "Relational schema"
msgstr ""

#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:486
msgid "Table of contents"
msgstr ""

#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:620
#: templates/columns_definitions/column_definitions_form.twig:72
#: templates/database/data_dictionary/index.twig:16
#: templates/table/structure/display_table_stats.twig:6
msgid "Table comments:"
msgstr ""

#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:669
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:703
#: templates/columns_definitions/table_fields_definitions.twig:27
#: templates/database/central_columns/edit.twig:13
#: templates/table/structure/display_structure.twig:24
msgid "Attributes"
msgstr ""

#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:672
#: libraries/classes/Plugins/Schema/Pdf/PdfRelationSchema.php:706
#: templates/table/structure/display_structure.twig:30
#: templates/table/tracking/structure_snapshot_columns.twig:11
msgid "Extra"
msgstr ""

#: libraries/classes/Plugins/SchemaPlugin.php:74
msgid "Show color"
msgstr ""

#: libraries/classes/Plugins/SchemaPlugin.php:76
msgid "Only show keys"
msgstr ""

#: libraries/classes/Plugins/Schema/SchemaDia.php:54
#: libraries/classes/Plugins/Schema/SchemaEps.php:62
#: libraries/classes/Plugins/Schema/SchemaPdf.php:64
msgid "Orientation"
msgstr ""

#: libraries/classes/Plugins/Schema/SchemaDia.php:58
#: libraries/classes/Plugins/Schema/SchemaEps.php:66
#: libraries/classes/Plugins/Schema/SchemaPdf.php:68
msgid "Landscape"
msgstr ""

#: libraries/classes/Plugins/Schema/SchemaDia.php:59
#: libraries/classes/Plugins/Schema/SchemaEps.php:67
#: libraries/classes/Plugins/Schema/SchemaPdf.php:69
msgid "Portrait"
msgstr ""

#: libraries/classes/Plugins/Schema/SchemaEps.php:56
#: libraries/classes/Plugins/Schema/SchemaPdf.php:58
#: libraries/classes/Plugins/Schema/SchemaSvg.php:55
msgid "Same width for all tables"
msgstr ""

#: libraries/classes/Plugins/Schema/SchemaPdf.php:83
msgid "Show grid"
msgstr ""

#: libraries/classes/Plugins/Schema/SchemaPdf.php:89
#: templates/database/structure/index.twig:21
msgid "Data dictionary"
msgstr ""

#: libraries/classes/Plugins/Schema/SchemaPdf.php:95
msgid "Order of the tables"
msgstr ""

#: libraries/classes/Plugins/Schema/SchemaPdf.php:100
msgid "Name (Ascending)"
msgstr ""

#: libraries/classes/Plugins/Schema/SchemaPdf.php:101
msgid "Name (Descending)"
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/Bool2TextTransformationsPlugin.php:28
msgid ""
"Converts Boolean values to text (default 'T' and 'F'). First option is for "
"TRUE, second for FALSE. Nonzero=true."
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/DateFormatTransformationsPlugin.php:39
msgid ""
"Displays a TIME, TIMESTAMP, DATETIME or numeric unix timestamp column as "
"formatted date. The first option is the offset (in hours) which will be "
"added to the timestamp (Default: 0). Use second option to specify a "
"different date/time format string. Third option determines whether you want "
"to see local date or UTC one (use \"local\" or \"utc\" strings) for that. "
"According to that, date format has different value - for \"local\" see the "
"documentation for PHP's strftime() function and for \"utc\" it is done using "
"gmdate() function."
msgstr ""

#. l10n: See https://www.php.net/manual/en/function.strftime.php
#: libraries/classes/Plugins/Transformations/Abs/DateFormatTransformationsPlugin.php:72
#: libraries/classes/Util.php:708
msgid "%B %d, %Y at %I:%M %p"
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/DownloadTransformationsPlugin.php:31
msgid ""
"Displays a link to download the binary data of the column. You can use the "
"first option to specify the filename, or use the second option as the name "
"of a column which contains the filename. If you use the second option, you "
"need to set the first option to the empty string."
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/ExternalTransformationsPlugin.php:42
msgid ""
"LINUX ONLY: Launches an external application and feeds it the column data "
"via standard input. Returns the standard output of the application. The "
"default is Tidy, to pretty-print HTML code. For security reasons, you have "
"to manually edit the file libraries/classes/Plugins/Transformations/Abs/"
"ExternalTransformationsPlugin.php and list the tools you want to make "
"available. The first option is then the number of the program you want to "
"use. The second option should be blank for historical reasons. The third "
"option, if set to 1, will convert the output using htmlspecialchars() "
"(Default 1). The fourth option, if set to 1, will prevent wrapping and "
"ensure that the output appears all on one line (Default 1)."
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/ExternalTransformationsPlugin.php:122
#, php-format
msgid ""
"You are using the external transformation command line options field, which "
"has been deprecated for security reasons. Add all command line options "
"directly to the definition in %s."
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/FormattedTransformationsPlugin.php:29
msgid ""
"Displays the contents of the column as-is, without running it through "
"htmlspecialchars(). That is, the column is assumed to contain valid HTML."
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/HexTransformationsPlugin.php:31
msgid ""
"Displays hexadecimal representation of data. Optional first parameter "
"specifies how often space will be added (defaults to 2 nibbles)."
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/ImageLinkTransformationsPlugin.php:29
msgid "Displays a link to download this image."
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/ImageUploadTransformationsPlugin.php:31
msgid ""
"Image upload functionality which also displays a thumbnail. The options are "
"the width and height of the thumbnail in pixels. Defaults to 100 X 100."
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/ImageUploadTransformationsPlugin.php:91
msgid "Image preview here"
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/InlineTransformationsPlugin.php:31
msgid ""
"Displays a clickable thumbnail. The options are the maximum width and height "
"in pixels. The original aspect ratio is preserved."
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/LongToIPv4TransformationsPlugin.php:30
msgid ""
"Converts an (IPv4) Internet network address stored as a BIGINT into a string "
"in Internet standard dotted format."
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/PreApPendTransformationsPlugin.php:29
msgid ""
"Prepends and/or Appends text to a string. First option is text to be "
"prepended, second is appended (enclosed in single quotes, default empty "
"string)."
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/RegexValidationTransformationsPlugin.php:32
msgid ""
"Validates the string using regular expression and performs insert only if "
"string matches it. The first option is the Regular Expression."
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/RegexValidationTransformationsPlugin.php:54
#, php-format
msgid "Validation failed for the input string %s."
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/SQLTransformationsPlugin.php:28
msgid "Formats text as SQL query with syntax highlighting."
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/SubstringTransformationsPlugin.php:31
msgid ""
"Displays a part of a string. The first option is the number of characters to "
"skip from the beginning of the string (Default 0). The second option is the "
"number of characters to return (Default: until end of string). The third "
"option is the string to append and/or prepend when truncation occurs "
"(Default: \"…\")."
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/TextFileUploadTransformationsPlugin.php:29
msgid ""
"File upload functionality for TEXT columns. It does not have a textarea for "
"input."
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/TextImageLinkTransformationsPlugin.php:31
msgid ""
"Displays an image and a link; the column contains the filename. The first "
"option is a URL prefix like \"https://www.example.com/\". The second and "
"third options are the width and the height in pixels."
msgstr ""

#: libraries/classes/Plugins/Transformations/Abs/TextLinkTransformationsPlugin.php:30
msgid ""
"Displays a link; the column contains the filename. The first option is a URL "
"prefix like \"https://www.example.com/\". The second option is a title for "
"the link."
msgstr ""

#: libraries/classes/Plugins/Transformations/Input/Text_Plain_Iptobinary.php:32
msgid "Converts an Internet network address in (IPv4/IPv6) format to binary"
msgstr ""

#: libraries/classes/Plugins/Transformations/Input/Text_Plain_Iptolong.php:29
msgid ""
"Converts an Internet network address in (IPv4/IPv6) format into a long "
"integer."
msgstr ""

#: libraries/classes/Plugins/Transformations/Input/Text_Plain_JsonEditor.php:26
msgid "Syntax highlighted CodeMirror editor for JSON."
msgstr ""

#: libraries/classes/Plugins/Transformations/Input/Text_Plain_SqlEditor.php:26
msgid "Syntax highlighted CodeMirror editor for SQL."
msgstr ""

#: libraries/classes/Plugins/Transformations/Input/Text_Plain_XmlEditor.php:26
msgid "Syntax highlighted CodeMirror editor for XML (and HTML)."
msgstr ""

#: libraries/classes/Plugins/Transformations/Output/Text_Plain_Binarytoip.php:29
msgid ""
"Converts an Internet network address stored as a binary string into a string "
"in Internet standard (IPv4/IPv6) format."
msgstr ""

#: libraries/classes/Plugins/Transformations/Output/Text_Plain_Json.php:44
msgid "Formats text as JSON with syntax highlighting."
msgstr ""

#: libraries/classes/Plugins/Transformations/Output/Text_Plain_Xml.php:44
msgid "Formats text as XML with syntax highlighting."
msgstr ""

#: libraries/classes/Plugins/TwoFactor/Application.php:135
msgid "Authentication Application (2FA)"
msgstr ""

#: libraries/classes/Plugins/TwoFactor/Application.php:146
msgid ""
"Provides authentication using HOTP and TOTP applications such as FreeOTP, "
"Google Authenticator or Authy."
msgstr ""

#: libraries/classes/Plugins/TwoFactor/Key.php:208
msgid "Hardware Security Key (FIDO U2F)"
msgstr ""

#: libraries/classes/Plugins/TwoFactor/Key.php:218
msgid ""
"Provides authentication using hardware security tokens supporting FIDO U2F, "
"such as a YubiKey."
msgstr ""

#: libraries/classes/Plugins/TwoFactorPlugin.php:73
#, php-format
msgid "Two-factor authentication failed: %s"
msgstr ""

#: libraries/classes/Plugins/TwoFactorPlugin.php:78
msgid "Two-factor authentication failed."
msgstr ""

#: libraries/classes/Plugins/TwoFactorPlugin.php:128
msgid "No Two-Factor Authentication"
msgstr ""

#: libraries/classes/Plugins/TwoFactorPlugin.php:138
msgid "Login using password only."
msgstr ""

#: libraries/classes/Plugins/TwoFactor/Simple.php:49
msgid "Simple two-factor authentication"
msgstr ""

#: libraries/classes/Plugins/TwoFactor/Simple.php:59
msgid "For testing purposes only!"
msgstr ""

#: libraries/classes/Plugins/TwoFactor/WebAuthn.php:180
msgid "Hardware Security Key (WebAuthn/FIDO2)"
msgstr ""

#: libraries/classes/Plugins/TwoFactor/WebAuthn.php:186
msgid ""
"Provides authentication using hardware security tokens supporting the "
"WebAuthn/FIDO2 protocol, such as a YubiKey."
msgstr ""

#: libraries/classes/Query/Utilities.php:97
msgid ""
"The server is not responding (or the local server's socket is not correctly "
"configured)."
msgstr ""

#: libraries/classes/Query/Utilities.php:100
msgid "The server is not responding."
msgstr ""

#: libraries/classes/Query/Utilities.php:104
msgid "Logout and try as another user."
msgstr ""

#: libraries/classes/Query/Utilities.php:109
msgid "Please check privileges of directory containing database."
msgstr ""

#: libraries/classes/Query/Utilities.php:118
msgid "Details…"
msgstr ""

#: libraries/classes/RecentFavoriteTable.php:159
msgid "Could not save recent table!"
msgstr ""

#: libraries/classes/RecentFavoriteTable.php:163
msgid "Could not save favorite table!"
msgstr ""

#: libraries/classes/RecentFavoriteTable.php:247
msgid "Recent tables"
msgstr ""

#: libraries/classes/RecentFavoriteTable.php:249
msgid "Recent"
msgstr ""

#: libraries/classes/RecentFavoriteTable.php:253
msgid "Favorites"
msgstr ""

#: libraries/classes/ReplicationGui.php:438
msgid ""
"Connection to server is disabled, please enable $cfg['AllowArbitraryServer'] "
"in phpMyAdmin configuration."
msgstr ""

#: libraries/classes/ReplicationGui.php:449
msgid "Replication started successfully."
msgstr ""

#: libraries/classes/ReplicationGui.php:450
msgid "Error starting replication."
msgstr ""

#: libraries/classes/ReplicationGui.php:453
msgid "Replication stopped successfully."
msgstr ""

#: libraries/classes/ReplicationGui.php:454
msgid "Error stopping replication."
msgstr ""

#: libraries/classes/ReplicationGui.php:457
msgid "Replication resetting successfully."
msgstr ""

#: libraries/classes/ReplicationGui.php:458
msgid "Error resetting replication."
msgstr ""

#: libraries/classes/ReplicationGui.php:461
msgid "Success."
msgstr ""

#: libraries/classes/ReplicationGui.php:462
msgid "Error."
msgstr ""

#: libraries/classes/ReplicationGui.php:507
msgid "Unknown error"
msgstr ""

#: libraries/classes/ReplicationGui.php:520
#, php-format
msgid "Unable to connect to primary %s."
msgstr ""

#: libraries/classes/ReplicationGui.php:530
msgid ""
"Unable to read primary log position. Possible privilege problem on primary."
msgstr ""

#: libraries/classes/ReplicationGui.php:548
msgid "Unable to change primary!"
msgstr ""

#: libraries/classes/ReplicationGui.php:552
#, php-format
msgid "Primary server changed successfully to %s."
msgstr ""

#: libraries/classes/Routing.php:103
#, php-format
msgid ""
"The routing cache could not be written, you need to adjust permissions on "
"the folder/file \"%s\""
msgstr ""

#: libraries/classes/Routing.php:162
#, php-format
msgid "Error 404! The page %s was not found."
msgstr ""

#: libraries/classes/Routing.php:173
msgid "Error 405! Request method not allowed."
msgstr ""

#: libraries/classes/SavedSearches.php:239
msgid "Please provide a name for this bookmarked search."
msgstr ""

#: libraries/classes/SavedSearches.php:255
msgid "Missing information to save the bookmarked search."
msgstr ""

#: libraries/classes/SavedSearches.php:276
#: libraries/classes/SavedSearches.php:310
msgid "An entry with this name already exists."
msgstr ""

#: libraries/classes/SavedSearches.php:338
msgid "Missing information to delete the search."
msgstr ""

#: libraries/classes/SavedSearches.php:365
msgid "Missing information to load the search."
msgstr ""

#: libraries/classes/SavedSearches.php:385
msgid "Error while loading the search."
msgstr ""

#: libraries/classes/Server/Plugins.php:68
#: libraries/classes/Server/Privileges.php:788
#: libraries/classes/Server/Privileges.php:3719
msgid "Native MySQL authentication"
msgstr ""

#: libraries/classes/Server/Plugins.php:73
msgid "SHA256 password authentication"
msgstr ""

#: libraries/classes/Server/Plugins.php:78
msgid "Caching sha2 authentication"
msgstr ""

#: libraries/classes/Server/Plugins.php:83
msgid "Unix Socket based authentication"
msgstr ""

#: libraries/classes/Server/Plugins.php:88
msgid "Old MySQL-4.0 authentication"
msgstr ""

#: libraries/classes/Server/Privileges/AccountLocking.php:30
#: libraries/classes/Server/Privileges/AccountLocking.php:51
msgid "Account locking is not supported."
msgstr ""

#: libraries/classes/Server/Privileges.php:262
msgid "No privileges."
msgstr ""

#: libraries/classes/Server/Privileges.php:270
msgid "Includes all privileges except GRANT."
msgstr ""

#: libraries/classes/Server/Privileges.php:292
#: libraries/classes/Server/Privileges.php:359
#: templates/server/privileges/privileges_table.twig:124
#: templates/server/privileges/privileges_table.twig:127
#: templates/server/privileges/privileges_table.twig:323
#: templates/server/privileges/privileges_table.twig:326
msgid "Allows deleting data."
msgstr ""

#: libraries/classes/Server/Privileges.php:297
#: templates/server/privileges/privileges_table.twig:136
#: templates/server/privileges/privileges_table.twig:139
#: templates/server/privileges/privileges_table.twig:361
#: templates/server/privileges/privileges_table.twig:369
msgid "Allows creating new tables."
msgstr ""

#: libraries/classes/Server/Privileges.php:302
#: templates/server/privileges/privileges_table.twig:148
#: templates/server/privileges/privileges_table.twig:151
#: templates/server/privileges/privileges_table.twig:409
#: templates/server/privileges/privileges_table.twig:417
msgid "Allows dropping tables."
msgstr ""

#: libraries/classes/Server/Privileges.php:307
#: libraries/classes/Server/Privileges.php:399
#: templates/server/privileges/privileges_table.twig:173
#: templates/server/privileges/privileges_table.twig:176
#: templates/server/privileges/privileges_table.twig:393
#: templates/server/privileges/privileges_table.twig:396
msgid "Allows creating and dropping indexes."
msgstr ""

#: libraries/classes/Server/Privileges.php:312
#: libraries/classes/Server/Privileges.php:404
#: templates/server/privileges/privileges_table.twig:185
#: templates/server/privileges/privileges_table.twig:188
#: templates/server/privileges/privileges_table.twig:380
#: templates/server/privileges/privileges_table.twig:383
msgid "Allows altering the structure of existing tables."
msgstr ""

#: libraries/classes/Server/Privileges.php:317
#: libraries/classes/Server/Privileges.php:443
#: libraries/classes/Server/Privileges.php:459
#: templates/server/privileges/privileges_table.twig:197
#: templates/server/privileges/privileges_table.twig:200
#: templates/server/privileges/privileges_table.twig:494
#: templates/server/privileges/privileges_table.twig:497
#: templates/server/privileges/privileges_table.twig:509
#: templates/server/privileges/privileges_table.twig:512
msgid "Allows creating new views."
msgstr ""

#: libraries/classes/Server/Privileges.php:322
#: libraries/classes/Server/Privileges.php:464
#: libraries/classes/Server/Privileges.php:470
#: templates/server/privileges/privileges_table.twig:209
#: templates/server/privileges/privileges_table.twig:212
#: templates/server/privileges/privileges_table.twig:441
#: templates/server/privileges/privileges_table.twig:444
msgid "Allows performing SHOW CREATE VIEW queries."
msgstr ""

#: libraries/classes/Server/Privileges.php:327
#: libraries/classes/Server/Privileges.php:453
#: templates/server/privileges/privileges_table.twig:221
#: templates/server/privileges/privileges_table.twig:224
#: templates/server/privileges/privileges_table.twig:537
#: templates/server/privileges/privileges_table.twig:540
msgid "Allows creating and dropping triggers."
msgstr ""

#: libraries/classes/Server/Privileges.php:344
#: templates/server/privileges/privileges_table.twig:15
#: templates/server/privileges/privileges_table.twig:284
#: templates/server/privileges/privileges_table.twig:287
msgid "Allows reading data."
msgstr ""

#: libraries/classes/Server/Privileges.php:349
#: templates/server/privileges/privileges_table.twig:42
#: templates/server/privileges/privileges_table.twig:297
#: templates/server/privileges/privileges_table.twig:300
msgid "Allows inserting and replacing data."
msgstr ""

#: libraries/classes/Server/Privileges.php:354
#: templates/server/privileges/privileges_table.twig:69
#: templates/server/privileges/privileges_table.twig:310
#: templates/server/privileges/privileges_table.twig:313
msgid "Allows changing data."
msgstr ""

#: libraries/classes/Server/Privileges.php:364
#: templates/server/privileges/privileges_table.twig:359
#: templates/server/privileges/privileges_table.twig:367
msgid "Allows creating new databases and tables."
msgstr ""

#: libraries/classes/Server/Privileges.php:369
#: templates/server/privileges/privileges_table.twig:407
#: templates/server/privileges/privileges_table.twig:415
msgid "Allows dropping databases and tables."
msgstr ""

#: libraries/classes/Server/Privileges.php:374
#: templates/server/privileges/privileges_table.twig:599
#: templates/server/privileges/privileges_table.twig:602
msgid "Allows reloading server settings and flushing the server's caches."
msgstr ""

#: libraries/classes/Server/Privileges.php:379
#: templates/server/privileges/privileges_table.twig:612
#: templates/server/privileges/privileges_table.twig:615
msgid "Allows shutting down the server."
msgstr ""

#: libraries/classes/Server/Privileges.php:384
#: templates/server/privileges/privileges_table.twig:586
#: templates/server/privileges/privileges_table.twig:589
msgid "Allows viewing processes of all users."
msgstr ""

#: libraries/classes/Server/Privileges.php:389
#: templates/server/privileges/privileges_table.twig:337
#: templates/server/privileges/privileges_table.twig:340
msgid "Allows importing data from and exporting data into files."
msgstr ""

#: libraries/classes/Server/Privileges.php:394
#: templates/server/privileges/privileges_table.twig:96
#: templates/server/privileges/privileges_table.twig:666
msgid "Has no effect in this MySQL version."
msgstr ""

#: libraries/classes/Server/Privileges.php:409
#: templates/server/privileges/privileges_table.twig:625
#: templates/server/privileges/privileges_table.twig:628
msgid "Gives access to the complete list of databases."
msgstr ""

#: libraries/classes/Server/Privileges.php:415
#: templates/server/privileges/privileges_table.twig:572
#: templates/server/privileges/privileges_table.twig:576
msgid ""
"Allows connecting, even if maximum number of connections is reached; "
"required for most administrative operations like setting global variables or "
"killing threads of other users."
msgstr ""

#: libraries/classes/Server/Privileges.php:423
#: templates/server/privileges/privileges_table.twig:428
#: templates/server/privileges/privileges_table.twig:431
msgid "Allows creating temporary tables."
msgstr ""

#: libraries/classes/Server/Privileges.php:428
#: templates/server/privileges/privileges_table.twig:653
#: templates/server/privileges/privileges_table.twig:656
msgid "Allows locking tables for the current thread."
msgstr ""

#: libraries/classes/Server/Privileges.php:433
#: templates/server/privileges/privileges_table.twig:694
#: templates/server/privileges/privileges_table.twig:697
msgid "Needed for the replication replicas."
msgstr ""

#: libraries/classes/Server/Privileges.php:438
#: templates/server/privileges/privileges_table.twig:681
#: templates/server/privileges/privileges_table.twig:684
msgid "Allows the user to ask where the replicas / primaries are."
msgstr ""

#: libraries/classes/Server/Privileges.php:448
#: templates/server/privileges/privileges_table.twig:524
#: templates/server/privileges/privileges_table.twig:527
msgid "Allows to set up events for the event scheduler."
msgstr ""

#. l10n: https://mariadb.com/kb/en/library/grant/#table-privileges "Remove historical rows from a table using the DELETE HISTORY statement"
#: libraries/classes/Server/Privileges.php:477
#: libraries/classes/Server/Privileges.php:486
#: templates/server/privileges/privileges_table.twig:234
#: templates/server/privileges/privileges_table.twig:238
msgid "Allows deleting historical rows."
msgstr ""

#: libraries/classes/Server/Privileges.php:491
#: templates/server/privileges/privileges_table.twig:454
#: templates/server/privileges/privileges_table.twig:457
msgid "Allows creating stored routines."
msgstr ""

#: libraries/classes/Server/Privileges.php:496
#: templates/server/privileges/privileges_table.twig:467
#: templates/server/privileges/privileges_table.twig:470
msgid "Allows altering and dropping stored routines."
msgstr ""

#: libraries/classes/Server/Privileges.php:501
#: templates/server/privileges/privileges_table.twig:707
#: templates/server/privileges/privileges_table.twig:710
msgid "Allows creating, dropping and renaming user accounts."
msgstr ""

#: libraries/classes/Server/Privileges.php:506
#: templates/server/privileges/privileges_table.twig:480
#: templates/server/privileges/privileges_table.twig:483
msgid "Allows executing stored routines."
msgstr ""

#: libraries/classes/Server/Privileges.php:1076
#, php-format
msgid "The password for %s was changed successfully."
msgstr ""

#: libraries/classes/Server/Privileges.php:1124
#, php-format
msgid "You have revoked the privileges for %s."
msgstr ""

#: libraries/classes/Server/Privileges.php:1436
#: templates/database/privileges/index.twig:124
#: templates/table/privileges/index.twig:127
msgid "Not enough privilege to view users."
msgstr ""

#: libraries/classes/Server/Privileges.php:1498
#: templates/database/privileges/index.twig:80
#: templates/server/privileges/new_user_ajax.twig:45
#: templates/server/privileges/users_overview.twig:75
#: templates/table/privileges/index.twig:84
msgid "Edit privileges"
msgstr ""

#: libraries/classes/Server/Privileges.php:1502
msgid "Revoke"
msgstr ""

#: libraries/classes/Server/Privileges.php:1813
#: templates/server/privileges/privileges_table.twig:264
msgid "Database-specific privileges"
msgstr ""

#: libraries/classes/Server/Privileges.php:1819
#: templates/server/privileges/privileges_summary.twig:18
#: templates/server/privileges/privileges_table.twig:7
#: templates/server/privileges/privileges_table.twig:266
msgid "Table-specific privileges"
msgstr ""

#: libraries/classes/Server/Privileges.php:1824
#: libraries/classes/Server/Privileges.php:1826
#: templates/server/privileges/edit_routine_privileges.twig:29
#: templates/server/privileges/edit_routine_privileges.twig:46
#: templates/server/privileges/edit_routine_privileges.twig:58
msgid "Routine"
msgstr ""

#: libraries/classes/Server/Privileges.php:1825
#: templates/server/privileges/edit_routine_privileges.twig:47
msgid "Routine-specific privileges"
msgstr ""

#: libraries/classes/Server/Privileges.php:2170
msgid "No users selected for deleting!"
msgstr ""

#: libraries/classes/Server/Privileges.php:2173
msgid "Reloading the privileges"
msgstr ""

#: libraries/classes/Server/Privileges.php:2198
msgid "The selected users have been deleted successfully."
msgstr ""

#: libraries/classes/Server/Privileges.php:2275
#, php-format
msgid "You have updated the privileges for %s."
msgstr ""

#: libraries/classes/Server/Privileges.php:2362
#: templates/database/privileges/index.twig:102
#: templates/table/privileges/index.twig:106
msgid "No user found."
msgstr ""

#: libraries/classes/Server/Privileges.php:2449
#, php-format
msgid "Deleting %s"
msgstr ""

#: libraries/classes/Server/Privileges.php:2480
msgid "The privileges were reloaded successfully."
msgstr ""

#: libraries/classes/Server/Privileges.php:2584
#, php-format
msgid "The user %s already exists!"
msgstr ""

#: libraries/classes/Server/Privileges.php:2859
#, php-format
msgid "Privileges for %s"
msgstr ""

#: libraries/classes/Server/Privileges.php:2868
#: libraries/classes/Server/Status/Processes.php:114
#: templates/server/user_groups/user_listings.twig:9
msgid "User"
msgstr ""

#: libraries/classes/Server/Privileges.php:2982
msgid ""
"A user account allowing any user from localhost to connect is present. This "
"will prevent other users from connecting if the host part of their account "
"allows a connection from any (%) host."
msgstr ""

#: libraries/classes/Server/Privileges.php:3016
#, php-format
msgid ""
"Note: phpMyAdmin gets the users’ privileges directly from MySQL’s privilege "
"tables. The content of these tables may differ from the privileges the "
"server uses, if they have been changed manually. In this case, you should "
"%sreload the privileges%s before you continue."
msgstr ""

#: libraries/classes/Server/Privileges.php:3032
msgid ""
"Note: phpMyAdmin gets the users’ privileges directly from MySQL’s privilege "
"tables. The content of these tables may differ from the privileges the "
"server uses, if they have been changed manually. In this case, the "
"privileges have to be reloaded but currently, you don't have the RELOAD "
"privilege."
msgstr ""

#: libraries/classes/Server/Privileges.php:3346
msgid "You have added a new user."
msgstr ""

#: libraries/classes/Server/Status/Data.php:146
#: libraries/classes/Server/Status/Processes.php:147
#: templates/database/qbe/selection_form.twig:109 templates/sql/query.twig:20
msgid "SQL query"
msgstr ""

#: libraries/classes/Server/Status/Data.php:149
msgid "Handler"
msgstr ""

#: libraries/classes/Server/Status/Data.php:150
msgid "Query cache"
msgstr ""

#: libraries/classes/Server/Status/Data.php:151
msgid "Threads"
msgstr ""

#: libraries/classes/Server/Status/Data.php:153
msgid "Temporary data"
msgstr ""

#: libraries/classes/Server/Status/Data.php:154
msgid "Delayed inserts"
msgstr ""

#: libraries/classes/Server/Status/Data.php:155
msgid "Key cache"
msgstr ""

#: libraries/classes/Server/Status/Data.php:156
msgid "Joins"
msgstr ""

#: libraries/classes/Server/Status/Data.php:158
msgid "Sorting"
msgstr ""

#: libraries/classes/Server/Status/Data.php:160
msgid "Transaction coordinator"
msgstr ""

#: libraries/classes/Server/Status/Data.php:161
#: templates/server/binlog/index.twig:27
msgid "Files"
msgstr ""

#: libraries/classes/Server/Status/Data.php:180
msgid "Flush (close) all tables"
msgstr ""

#: libraries/classes/Server/Status/Data.php:184
msgid "Show open tables"
msgstr ""

#: libraries/classes/Server/Status/Data.php:193
msgid "Show replica hosts"
msgstr ""

#: libraries/classes/Server/Status/Data.php:200
#: templates/server/replication/primary_replication.twig:9
msgid "Show primary status"
msgstr ""

#: libraries/classes/Server/Status/Data.php:207
msgid "Show replica status"
msgstr ""

#: libraries/classes/Server/Status/Data.php:215
msgid "Flush query cache"
msgstr ""

#: libraries/classes/Server/Status/Processes.php:110
msgid "ID"
msgstr ""

#: libraries/classes/Server/Status/Processes.php:118
#: templates/server/replication/primary_add_replica_user.twig:32
#: templates/server/replication/primary_add_replica_user.twig:43
#: templates/server/replication/primary_replication.twig:25
msgid "Host"
msgstr ""

#: libraries/classes/Server/Status/Processes.php:126
msgid "Command"
msgstr ""

#: libraries/classes/Server/Status/Processes.php:141
msgid "Progress"
msgstr ""

#: libraries/classes/Setup/Index.php:130
msgid ""
"Reading of version failed. Maybe you're offline or the upgrade server does "
"not respond."
msgstr ""

#: libraries/classes/Setup/Index.php:152
msgid "Got invalid version string from server"
msgstr ""

#: libraries/classes/Setup/Index.php:164
msgid "Unparsable version string"
msgstr ""

#: libraries/classes/Setup/Index.php:186
#, php-format
msgid ""
"You are using Git version, run [kbd]git pull[/kbd] :-)[br]The latest stable "
"version is %s, released on %s."
msgstr ""

#: libraries/classes/Setup/Index.php:194
msgid "No newer stable version is available"
msgstr ""

#: libraries/classes/Sql.php:497
#, php-format
msgid "Using bookmark \"%s\" as default browse query."
msgstr ""

#: libraries/classes/Sql.php:961
msgid "Showing as PHP code"
msgstr ""

#: libraries/classes/Sql.php:1336
#, php-format
msgid ""
"Current selection does not contain a unique column. Grid edit, checkbox, "
"Edit, Copy and Delete features are not available. %s"
msgstr ""

#: libraries/classes/Sql.php:1350
#, php-format
msgid ""
"Current selection does not contain a unique column. Grid edit, Edit, Copy "
"and Delete features may result in undesired behavior. %s"
msgstr ""

#: libraries/classes/SqlQueryForm.php:149
#, php-format
msgid "Run SQL query/queries on server “%s”"
msgstr ""

#: libraries/classes/SqlQueryForm.php:165
#, php-format
msgid "Run SQL query/queries on database %s"
msgstr ""

#: libraries/classes/SqlQueryForm.php:180
#, php-format
msgid "Run SQL query/queries on table %s"
msgstr ""

#: libraries/classes/StorageEngine.php:368
msgid ""
"There is no detailed status information available for this storage engine."
msgstr ""

#: libraries/classes/StorageEngine.php:470
#: templates/database/structure/body_for_table_summary.twig:46
#, php-format
msgid "%s is the default storage engine on this MySQL server."
msgstr ""

#: libraries/classes/StorageEngine.php:473
#, php-format
msgid "%s is available on this MySQL server."
msgstr ""

#: libraries/classes/StorageEngine.php:476
#, php-format
msgid "%s has been disabled for this MySQL server."
msgstr ""

#: libraries/classes/StorageEngine.php:480
#, php-format
msgid "This MySQL server does not support the %s storage engine."
msgstr ""

#: libraries/classes/Table/Indexes.php:58 libraries/classes/Table.php:2106
msgid "The name of the primary key must be \"PRIMARY\"!"
msgstr ""

#: libraries/classes/Table/Maintenance.php:122
#, php-format
msgid "Problems with indexes of table `%s`"
msgstr ""

#: libraries/classes/Table.php:348
msgid "Unknown table status:"
msgstr ""

#: libraries/classes/Table.php:1011
#, php-format
msgid "Source database `%s` was not found!"
msgstr ""

#: libraries/classes/Table.php:1020
#, php-format
msgid "Target database `%s` was not found!"
msgstr ""

#: libraries/classes/Table.php:1478
msgid "Invalid database:"
msgstr ""

#: libraries/classes/Table.php:1496
msgid "Invalid table name:"
msgstr ""

#: libraries/classes/Table.php:1536
#, php-format
msgid "Failed to rename table %1$s to %2$s!"
msgstr ""

#: libraries/classes/Table.php:1553
#, php-format
msgid "Table %1$s has been renamed to %2$s."
msgstr ""

#: libraries/classes/Table.php:1798
msgid "Could not save table UI preferences!"
msgstr ""

#: libraries/classes/Table.php:1824
#, php-format
msgid ""
"Failed to cleanup table UI preferences (see $cfg['Servers'][$i]"
"['MaxTableUiprefs'] %s)"
msgstr ""

#: libraries/classes/Table.php:1959
#, php-format
msgid ""
"Cannot save UI property \"%s\". The changes made will not be persistent "
"after you refresh this page. Please check if the table structure has been "
"changed."
msgstr ""

#: libraries/classes/Table.php:2118
msgid "Can't rename index to PRIMARY!"
msgstr ""

#: libraries/classes/Table.php:2144
msgid "No index parts defined!"
msgstr ""

#: libraries/classes/Table.php:2440
#, php-format
msgid "Error creating foreign key on %1$s (check data types)"
msgstr ""

#: libraries/classes/Template.php:135
#, php-format
msgid "Error while working with template cache: %s"
msgstr ""

#: libraries/classes/ThemeManager.php:83
#, php-format
msgid "Default theme %s not found!"
msgstr ""

#: libraries/classes/ThemeManager.php:142
#, php-format
msgid "Theme %s not found!"
msgstr ""

#: libraries/classes/Theme.php:168
#, php-format
msgid "No valid image path for theme %s found!"
msgstr ""

#: libraries/classes/Tracking.php:236
#: templates/database/tracking/tables.twig:115
#: templates/table/tracking/main.twig:73
msgid "Tracking report"
msgstr ""

#: libraries/classes/Tracking.php:240
msgid "Tracking statements"
msgstr ""

#: libraries/classes/Tracking.php:255
msgid "Delete tracking data row from report"
msgstr ""

#: libraries/classes/Tracking.php:267
msgid "No data"
msgstr ""

#: libraries/classes/Tracking.php:312
#: templates/database/operations/index.twig:131
#: templates/database/structure/copy_form.twig:19
#: templates/table/operations/index.twig:275
msgid "Structure only"
msgstr ""

#: libraries/classes/Tracking.php:315
#: templates/database/operations/index.twig:143
#: templates/database/structure/copy_form.twig:29
#: templates/table/operations/index.twig:287
msgid "Data only"
msgstr ""

#: libraries/classes/Tracking.php:318
#: templates/database/operations/index.twig:137
#: templates/database/structure/copy_form.twig:24
#: templates/table/operations/index.twig:281
msgid "Structure and data"
msgstr ""

#: libraries/classes/Tracking.php:383 libraries/classes/Tracking.php:451
#, php-format
msgid "Show %1$s with dates from %2$s to %3$s by user %4$s %5$s"
msgstr ""

#: libraries/classes/Tracking.php:472
msgid "SQL dump (file download)"
msgstr ""

#: libraries/classes/Tracking.php:474
msgid "SQL dump"
msgstr ""

#: libraries/classes/Tracking.php:477
msgid "This option will replace your table and contained data."
msgstr ""

#: libraries/classes/Tracking.php:479
msgid "SQL execution"
msgstr ""

#: libraries/classes/Tracking.php:483
#, php-format
msgid "Export as %s"
msgstr ""

#: libraries/classes/Tracking.php:521
msgid "Data manipulation statement"
msgstr ""

#: libraries/classes/Tracking.php:557
msgid "Data definition statement"
msgstr ""

#: libraries/classes/Tracking.php:640
#: templates/database/tracking/tables.twig:126
#: templates/table/tracking/main.twig:80
msgid "Structure snapshot"
msgstr ""

#: libraries/classes/Tracking.php:658
#, php-format
msgid "Version %s snapshot (SQL code)"
msgstr ""

#: libraries/classes/Tracking.php:728
msgid "Tracking data definition successfully deleted"
msgstr ""

#: libraries/classes/Tracking.php:740
msgid "Tracking data manipulation successfully deleted"
msgstr ""

#: libraries/classes/Tracking.php:797
msgid ""
"You can execute the dump by creating and using a temporary database. Please "
"ensure that you have the privileges to do so."
msgstr ""

#: libraries/classes/Tracking.php:801
msgid "Comment out these two lines if you do not need them."
msgstr ""

#: libraries/classes/Tracking.php:812
msgid "SQL statements exported. Please copy the dump or execute it."
msgstr ""

#: libraries/classes/Tracking.php:845
#, php-format
msgid "Tracking report for table `%s`"
msgstr ""

#: libraries/classes/Tracking.php:877
#, php-format
msgid "Tracking for %1$s was activated at version %2$s."
msgstr ""

#: libraries/classes/Tracking.php:880
#, php-format
msgid "Tracking for %1$s was deactivated at version %2$s."
msgstr ""

#: libraries/classes/Tracking.php:979
#, php-format
msgid "Version %1$s of %2$s was deleted."
msgstr ""

#: libraries/classes/Tracking.php:1010
#, php-format
msgid "Version %1$s was created, tracking for %2$s is active."
msgstr ""

#: libraries/classes/Types.php:231
msgid ""
"A 1-byte integer, signed range is -128 to 127, unsigned range is 0 to 255"
msgstr ""

#: libraries/classes/Types.php:234
msgid ""
"A 2-byte integer, signed range is -32,768 to 32,767, unsigned range is 0 to "
"65,535"
msgstr ""

#: libraries/classes/Types.php:238
msgid ""
"A 3-byte integer, signed range is -8,388,608 to 8,388,607, unsigned range is "
"0 to 16,777,215"
msgstr ""

#: libraries/classes/Types.php:243
msgid ""
"A 4-byte integer, signed range is -2,147,483,648 to 2,147,483,647, unsigned "
"range is 0 to 4,294,967,295"
msgstr ""

#: libraries/classes/Types.php:250
msgid ""
"An 8-byte integer, signed range is -9,223,372,036,854,775,808 to "
"9,223,372,036,854,775,807, unsigned range is 0 to 18,446,744,073,709,551,615"
msgstr ""

#: libraries/classes/Types.php:257
msgid ""
"A fixed-point number (M, D) - the maximum number of digits (M) is 65 "
"(default 10), the maximum number of decimals (D) is 30 (default 0)"
msgstr ""

#: libraries/classes/Types.php:264
msgid ""
"A small floating-point number, allowable values are -3.402823466E+38 to "
"-1.175494351E-38, 0, and 1.175494351E-38 to 3.402823466E+38"
msgstr ""

#: libraries/classes/Types.php:271
msgid ""
"A double-precision floating-point number, allowable values are "
"-1.7976931348623157E+308 to -2.2250738585072014E-308, 0, and "
"2.2250738585072014E-308 to 1.7976931348623157E+308"
msgstr ""

#: libraries/classes/Types.php:277
msgid ""
"Synonym for DOUBLE (exception: in REAL_AS_FLOAT SQL mode it is a synonym for "
"FLOAT)"
msgstr ""

#: libraries/classes/Types.php:280
msgid ""
"A bit-field type (M), storing M of bits per value (default is 1, maximum is "
"64)"
msgstr ""

#: libraries/classes/Types.php:284
msgid ""
"A synonym for TINYINT(1), a value of zero is considered false, nonzero "
"values are considered true"
msgstr ""

#: libraries/classes/Types.php:288
msgid "An alias for BIGINT UNSIGNED NOT NULL AUTO_INCREMENT UNIQUE"
msgstr ""

#: libraries/classes/Types.php:292
#, php-format
msgid "A date, supported range is %1$s to %2$s"
msgstr ""

#: libraries/classes/Types.php:299
#, php-format
msgid "A date and time combination, supported range is %1$s to %2$s"
msgstr ""

#: libraries/classes/Types.php:306
msgid ""
"A timestamp, range is 1970-01-01 00:00:01 UTC to 2038-01-09 03:14:07 UTC, "
"stored as the number of seconds since the epoch (1970-01-01 00:00:00 UTC)"
msgstr ""

#: libraries/classes/Types.php:313
#, php-format
msgid "A time, range is %1$s to %2$s"
msgstr ""

#: libraries/classes/Types.php:320
msgid ""
"A year in four-digit (4, default) or two-digit (2) format, the allowable "
"values are 70 (1970) to 69 (2069) or 1901 to 2155 and 0000"
msgstr ""

#: libraries/classes/Types.php:327
msgid ""
"A fixed-length (0-255, default 1) string that is always right-padded with "
"spaces to the specified length when stored"
msgstr ""

#: libraries/classes/Types.php:334
#, php-format
msgid ""
"A variable-length (%s) string, the effective maximum length is subject to "
"the maximum row size"
msgstr ""

#: libraries/classes/Types.php:341
msgid ""
"A TEXT column with a maximum length of 255 (2^8 - 1) characters, stored with "
"a one-byte prefix indicating the length of the value in bytes"
msgstr ""

#: libraries/classes/Types.php:348
msgid ""
"A TEXT column with a maximum length of 65,535 (2^16 - 1) characters, stored "
"with a two-byte prefix indicating the length of the value in bytes"
msgstr ""

#: libraries/classes/Types.php:355
msgid ""
"A TEXT column with a maximum length of 16,777,215 (2^24 - 1) characters, "
"stored with a three-byte prefix indicating the length of the value in bytes"
msgstr ""

#: libraries/classes/Types.php:362
msgid ""
"A TEXT column with a maximum length of 4,294,967,295 or 4GiB (2^32 - 1) "
"characters, stored with a four-byte prefix indicating the length of the "
"value in bytes"
msgstr ""

#: libraries/classes/Types.php:369
msgid ""
"Similar to the CHAR type, but stores binary byte strings rather than non-"
"binary character strings"
msgstr ""

#: libraries/classes/Types.php:374
msgid ""
"Similar to the VARCHAR type, but stores binary byte strings rather than non-"
"binary character strings"
msgstr ""

#: libraries/classes/Types.php:380
msgid ""
"A BLOB column with a maximum length of 255 (2^8 - 1) bytes, stored with a "
"one-byte prefix indicating the length of the value"
msgstr ""

#: libraries/classes/Types.php:386
msgid ""
"A BLOB column with a maximum length of 16,777,215 (2^24 - 1) bytes, stored "
"with a three-byte prefix indicating the length of the value"
msgstr ""

#: libraries/classes/Types.php:393
msgid ""
"A BLOB column with a maximum length of 65,535 (2^16 - 1) bytes, stored with "
"a two-byte prefix indicating the length of the value"
msgstr ""

#: libraries/classes/Types.php:399
msgid ""
"A BLOB column with a maximum length of 4,294,967,295 or 4GiB (2^32 - 1) "
"bytes, stored with a four-byte prefix indicating the length of the value"
msgstr ""

#: libraries/classes/Types.php:406
msgid ""
"An enumeration, chosen from the list of up to 65,535 values or the special "
"'' error value"
msgstr ""

#: libraries/classes/Types.php:410
msgid "A single value chosen from a set of up to 64 members"
msgstr ""

#: libraries/classes/Types.php:413
msgid "A type that can store a geometry of any type"
msgstr ""

#: libraries/classes/Types.php:416
msgid "A point in 2-dimensional space"
msgstr ""

#: libraries/classes/Types.php:419
msgid "A curve with linear interpolation between points"
msgstr ""

#: libraries/classes/Types.php:422
msgid "A polygon"
msgstr ""

#: libraries/classes/Types.php:425
msgid "A collection of points"
msgstr ""

#: libraries/classes/Types.php:428
msgid "A collection of curves with linear interpolation between points"
msgstr ""

#: libraries/classes/Types.php:431
msgid "A collection of polygons"
msgstr ""

#: libraries/classes/Types.php:434
msgid "A collection of geometry objects of any type"
msgstr ""

#: libraries/classes/Types.php:437
msgid ""
"Stores and enables efficient access to data in JSON (JavaScript Object "
"Notation) documents"
msgstr ""

#: libraries/classes/Types.php:440
msgid ""
"Intended for storage of IPv6 addresses, as well as IPv4 addresses assuming "
"conventional mapping of IPv4 addresses into IPv6 addresses"
msgstr ""

#: libraries/classes/Types.php:445
msgid "128-bit UUID (Universally Unique Identifier)"
msgstr ""

#: libraries/classes/Types.php:791
msgctxt "numeric types"
msgid "Numeric"
msgstr ""

#: libraries/classes/Types.php:809
msgctxt "date and time types"
msgid "Date and time"
msgstr ""

#: libraries/classes/Types.php:845
msgctxt "spatial types"
msgid "Spatial"
msgstr ""

#: libraries/classes/UrlRedirector.php:55
msgid "Taking you to the target site."
msgstr ""

#: libraries/classes/UserPassword.php:38
msgid "The profile has been updated."
msgstr ""

#: libraries/classes/UserPassword.php:50
msgid "Password is too long!"
msgstr ""

#: libraries/classes/UserPreferences.php:181
msgid "Could not save configuration"
msgstr ""

#: libraries/classes/UserPreferences.php:192
msgid "The phpMyAdmin configuration storage database could not be accessed."
msgstr ""

#: libraries/classes/Util.php:132
#, php-format
msgid "Max: %s%s"
msgstr ""

#. l10n: Short month name
#. l10n: Short month name for January
#: libraries/classes/Util.php:665 templates/javascript/variables.twig:34
msgid "Jan"
msgstr ""

#. l10n: Short month name
#. l10n: Short month name for February
#: libraries/classes/Util.php:667 templates/javascript/variables.twig:35
msgid "Feb"
msgstr ""

#. l10n: Short month name
#. l10n: Short month name for March
#: libraries/classes/Util.php:669 templates/javascript/variables.twig:36
msgid "Mar"
msgstr ""

#. l10n: Short month name
#. l10n: Short month name for April
#: libraries/classes/Util.php:671 templates/javascript/variables.twig:37
msgid "Apr"
msgstr ""

#. l10n: Short month name
#: libraries/classes/Util.php:673
msgctxt "Short month name"
msgid "May"
msgstr ""

#. l10n: Short month name
#. l10n: Short month name for June
#: libraries/classes/Util.php:675 templates/javascript/variables.twig:39
msgid "Jun"
msgstr ""

#. l10n: Short month name
#. l10n: Short month name for July
#: libraries/classes/Util.php:677 templates/javascript/variables.twig:40
msgid "Jul"
msgstr ""

#. l10n: Short month name
#. l10n: Short month name for August
#: libraries/classes/Util.php:679 templates/javascript/variables.twig:41
msgid "Aug"
msgstr ""

#. l10n: Short month name
#. l10n: Short month name for September
#: libraries/classes/Util.php:681 templates/javascript/variables.twig:42
msgid "Sep"
msgstr ""

#. l10n: Short month name
#. l10n: Short month name for October
#: libraries/classes/Util.php:683 templates/javascript/variables.twig:43
msgid "Oct"
msgstr ""

#. l10n: Short month name
#. l10n: Short month name for November
#: libraries/classes/Util.php:685 templates/javascript/variables.twig:44
msgid "Nov"
msgstr ""

#. l10n: Short month name
#. l10n: Short month name for December
#: libraries/classes/Util.php:687 templates/javascript/variables.twig:45
msgid "Dec"
msgstr ""

#. l10n: Short week day name for Sunday
#: libraries/classes/Util.php:691
msgctxt "Short week day name for Sunday"
msgid "Sun"
msgstr ""

#. l10n: Short week day name for Monday
#: libraries/classes/Util.php:693 templates/javascript/variables.twig:58
msgid "Mon"
msgstr ""

#. l10n: Short week day name for Tuesday
#: libraries/classes/Util.php:695 templates/javascript/variables.twig:59
msgid "Tue"
msgstr ""

#. l10n: Short week day name for Wednesday
#: libraries/classes/Util.php:697 templates/javascript/variables.twig:60
msgid "Wed"
msgstr ""

#. l10n: Short week day name for Thursday
#: libraries/classes/Util.php:699 templates/javascript/variables.twig:61
msgid "Thu"
msgstr ""

#. l10n: Short week day name for Friday
#: libraries/classes/Util.php:701 templates/javascript/variables.twig:62
msgid "Fri"
msgstr ""

#. l10n: Short week day name for Saturday
#: libraries/classes/Util.php:703 templates/javascript/variables.twig:63
msgid "Sat"
msgstr ""

#: libraries/classes/Util.php:729
msgctxt "AM/PM indication in time"
msgid "PM"
msgstr ""

#: libraries/classes/Util.php:731
msgctxt "AM/PM indication in time"
msgid "AM"
msgstr ""

#: libraries/classes/Util.php:802
#, php-format
msgid "%s days, %s hours, %s minutes and %s seconds"
msgstr ""

#: libraries/classes/Util.php:1963
msgid "Users"
msgstr ""

#: libraries/classes/Util.php:2579
#: templates/database/multi_table_query/form.twig:67
msgid "Sort"
msgstr ""

#: libraries/classes/ZipExtension.php:73 libraries/classes/ZipExtension.php:119
msgid "Error in ZIP archive:"
msgstr ""

#: libraries/classes/ZipExtension.php:83
msgid "No files found inside ZIP archive!"
msgstr ""

#: libraries/config.values.php:88 libraries/config.values.php:126
#: libraries/config.values.php:138
msgid "Icons"
msgstr ""

#: libraries/config.values.php:89 libraries/config.values.php:127
#: libraries/config.values.php:139
#: templates/database/multi_table_query/form.twig:118
msgid "Text"
msgstr ""

#: libraries/config.values.php:90 libraries/config.values.php:108
#: libraries/config.values.php:128 libraries/config.values.php:140
msgid "Both"
msgstr ""

#: libraries/config.values.php:105
msgid "Nowhere"
msgstr ""

#: libraries/config.values.php:106
msgid "Left"
msgstr ""

#: libraries/config.values.php:107
msgid "Right"
msgstr ""

#: libraries/config.values.php:143
msgid "Click"
msgstr ""

#: libraries/config.values.php:144
msgid "Double click"
msgstr ""

#: libraries/config.values.php:145 libraries/config.values.php:188
#: templates/config/form_display/input.twig:16
#: templates/relation/check_relations.twig:15
#: templates/relation/check_relations.twig:66
#: templates/relation/check_relations.twig:91
#: templates/relation/check_relations.twig:129
#: templates/relation/check_relations.twig:154
#: templates/relation/check_relations.twig:164
#: templates/relation/check_relations.twig:199
#: templates/relation/check_relations.twig:224
#: templates/relation/check_relations.twig:249
#: templates/relation/check_relations.twig:274
#: templates/relation/check_relations.twig:299
#: templates/relation/check_relations.twig:324
#: templates/relation/check_relations.twig:349
#: templates/relation/check_relations.twig:387
#: templates/relation/check_relations.twig:412
#: templates/relation/check_relations.twig:437
#: templates/relation/check_relations.twig:462
#: templates/relation/check_relations.twig:487
#: templates/relation/check_relations.twig:512
msgid "Disabled"
msgstr ""

#: libraries/config.values.php:148
msgid "key"
msgstr ""

#: libraries/config.values.php:149
#, fuzzy
#| msgid "Select All"
msgid "display column"
msgstr "Alle ußwähle"

#: libraries/config.values.php:153
msgid "Welcome"
msgstr ""

#: libraries/config.values.php:186
msgid "Open"
msgstr ""

#: libraries/config.values.php:187
msgid "Closed"
msgstr ""

#: libraries/config.values.php:191 templates/javascript/variables.twig:49
msgid "Monday"
msgstr ""

#: libraries/config.values.php:192 templates/javascript/variables.twig:50
msgid "Tuesday"
msgstr ""

#: libraries/config.values.php:193 templates/javascript/variables.twig:51
msgid "Wednesday"
msgstr ""

#: libraries/config.values.php:194 templates/javascript/variables.twig:52
msgid "Thursday"
msgstr ""

#: libraries/config.values.php:195 templates/javascript/variables.twig:53
msgid "Friday"
msgstr ""

#: libraries/config.values.php:196 templates/javascript/variables.twig:54
msgid "Saturday"
msgstr ""

#: libraries/config.values.php:197 templates/javascript/variables.twig:48
msgid "Sunday"
msgstr ""

#: libraries/config.values.php:200
msgid "Ask before sending error reports"
msgstr ""

#: libraries/config.values.php:201
msgid "Always send error reports"
msgstr ""

#: libraries/config.values.php:202
msgid "Never send error reports"
msgstr ""

#: libraries/config.values.php:205
#, fuzzy
#| msgid "Default"
msgid "Server default"
msgstr "Schtandatt"

#: libraries/config.values.php:206
#, fuzzy
#| msgid "Table"
msgid "Enable"
msgstr "Tabäll"

#: libraries/config.values.php:207
#, fuzzy
#| msgid "Table"
msgid "Disable"
msgstr "Tabäll"

#: libraries/config.values.php:259
msgid "Quick - display only the minimal options to configure"
msgstr ""

#: libraries/config.values.php:260
msgid "Custom - display all possible options to configure"
msgstr ""

#: libraries/config.values.php:261
msgid "Custom - like above, but without the quick/custom choice"
msgstr ""

#: libraries/config.values.php:328
msgid "complete inserts"
msgstr ""

#: libraries/config.values.php:329
msgid "extended inserts"
msgstr ""

#: libraries/config.values.php:330
msgid "both of the above"
msgstr ""

#: libraries/config.values.php:331
msgid "neither of the above"
msgstr ""

#: setup/index.php:33
msgid "Configuration already exists, setup is disabled!"
msgstr ""

#: setup/validate.php:31
msgid "Wrong data"
msgstr ""

#: setup/validate.php:38
#, php-format
msgid "Wrong data or no validation for %s"
msgstr ""

#: templates/columns_definitions/column_attributes.twig:31
#: templates/database/central_columns/edit_table_row.twig:23
#: templates/database/central_columns/main.twig:71
#: templates/database/central_columns/main.twig:298
msgid "Edit ENUM/SET values"
msgstr ""

#: templates/columns_definitions/column_attributes.twig:38
#: templates/database/central_columns/edit_table_row.twig:30
#: templates/database/central_columns/main.twig:76
#: templates/database/central_columns/main.twig:309
msgctxt "for default"
msgid "None"
msgstr ""

#: templates/columns_definitions/column_attributes.twig:41
#: templates/database/central_columns/edit_table_row.twig:33
#: templates/database/central_columns/main.twig:77
#: templates/database/central_columns/main.twig:312
msgid "As defined:"
msgstr ""

#: templates/columns_definitions/column_attributes.twig:102
msgid ""
"You don't have sufficient privileges to perform this operation; Please refer "
"to the documentation for more details"
msgstr ""

#: templates/columns_definitions/column_attributes.twig:116
#: templates/database/data_dictionary/index.twig:75 templates/indexes.twig:18
#: templates/table/structure/display_structure.twig:152
#: templates/table/structure/display_structure.twig:160
#: templates/table/structure/display_structure.twig:298
#: templates/table/structure/display_structure.twig:477
#: templates/table/tracking/structure_snapshot_indexes.twig:7
msgid "Unique"
msgstr ""

#: templates/columns_definitions/column_attributes.twig:124
#: templates/table/structure/display_structure.twig:219
#: templates/table/structure/display_structure.twig:222
#: templates/table/structure/display_structure.twig:307
msgid "Fulltext"
msgstr ""

#: templates/columns_definitions/column_attributes.twig:128
#: templates/table/structure/display_structure.twig:192
#: templates/table/structure/display_structure.twig:200
#: templates/table/structure/display_structure.twig:304
msgid "Spatial"
msgstr ""

#: templates/columns_definitions/column_attributes.twig:165
#: templates/table/structure/display_partitions.twig:26
msgid "Expression"
msgstr ""

#: templates/columns_definitions/column_attributes.twig:186
msgid "first"
msgstr ""

#: templates/columns_definitions/column_attributes.twig:191
#: templates/table/structure/display_structure.twig:450
#, php-format
msgid "after %s"
msgstr ""

#: templates/columns_definitions/column_definitions_form.twig:23
#: templates/database/create_table.twig:6
#: templates/database/operations/index.twig:30
msgid "Table name"
msgstr ""

#: templates/columns_definitions/column_definitions_form.twig:32
#: templates/console/display.twig:99
#: templates/database/central_columns/main.twig:196 templates/export.twig:285
#: templates/export.twig:301 templates/export.twig:317
msgid "Add"
msgstr ""

#: templates/columns_definitions/column_definitions_form.twig:40
#, fuzzy
#| msgid "Column"
msgid "column(s)"
msgstr "Kolonn"

#: templates/columns_definitions/column_definitions_form.twig:74
msgid "Collation:"
msgstr ""

#: templates/columns_definitions/column_definitions_form.twig:77
msgid "Storage Engine:"
msgstr ""

#: templates/columns_definitions/column_definitions_form.twig:82
msgid "Connection:"
msgstr ""

#: templates/columns_definitions/column_definitions_form.twig:113
#: templates/columns_definitions/partitions.twig:137
#: templates/table/operations/index.twig:133
msgid "Storage engine"
msgstr ""

#: templates/columns_definitions/column_definitions_form.twig:136
msgid "PARTITION definition:"
msgstr ""

#: templates/columns_definitions/column_definitions_form.twig:154
msgctxt "Online transaction part of the SQL DDL for InnoDB"
msgid "Online transaction"
msgstr ""

#: templates/columns_definitions/column_definitions_form.twig:166
msgid ""
"The column width of integer types is ignored in your MySQL version unless "
"defining a TINYINT(1) column"
msgstr ""

#: templates/columns_definitions/column_definitions_form.twig:175
#: templates/database/designer/main.twig:1097
#: templates/database/designer/main.twig:1112 templates/export_modal.twig:5
#: templates/export.twig:81 templates/modals/build_query.twig:5
#: templates/modals/index_dialog_modal.twig:5
#: templates/modals/preview_sql_confirmation.twig:5
#: templates/modals/preview_sql_modal.twig:5
#: templates/table/insert/actions_panel.twig:46
#: templates/table/relation/common_form.twig:233
#: templates/table/zoom_search/result_form.twig:26
#: templates/table/zoom_search/result_form.twig:95
msgid "Loading"
msgstr ""

#: templates/columns_definitions/column_name.twig:4
#, php-format
msgid "Referenced by %s."
msgstr ""

#: templates/columns_definitions/column_name.twig:12
msgid "Is a foreign key."
msgstr ""

#: templates/columns_definitions/column_name.twig:39
msgid "Pick from Central Columns"
msgstr ""

#: templates/columns_definitions/partitions.twig:17
msgid "Partition by:"
msgstr ""

#: templates/columns_definitions/partitions.twig:32
#: templates/columns_definitions/partitions.twig:60
msgid "Expression or column list"
msgstr ""

#: templates/columns_definitions/partitions.twig:37
msgid "Partitions:"
msgstr ""

#: templates/columns_definitions/partitions.twig:45
msgid "Subpartition by:"
msgstr ""

#: templates/columns_definitions/partitions.twig:65
msgid "Subpartitions:"
msgstr ""

#: templates/columns_definitions/partitions.twig:76
#: templates/table/operations/index.twig:480
#: templates/table/structure/display_partitions.twig:24
msgid "Partition"
msgstr ""

#: templates/columns_definitions/partitions.twig:78
msgid "Values"
msgstr ""

#: templates/columns_definitions/partitions.twig:82
msgid "Subpartition"
msgstr ""

#: templates/columns_definitions/partitions.twig:84
msgid "Engine"
msgstr ""

#: templates/columns_definitions/partitions.twig:85
#: templates/config/form_display/input.twig:53
#: templates/database/data_dictionary/index.twig:81
#: templates/database/events/editor_form.twig:102
#: templates/database/routines/editor_form.twig:163
#: templates/database/structure/table_header.twig:46 templates/indexes.twig:24
#: templates/table/structure/display_partitions.twig:31
#: templates/table/structure/display_structure.twig:483
#: templates/table/tracking/structure_snapshot_columns.twig:12
#: templates/table/tracking/structure_snapshot_indexes.twig:13
msgid "Comment"
msgstr ""

#: templates/columns_definitions/partitions.twig:86
msgid "Data directory"
msgstr ""

#: templates/columns_definitions/partitions.twig:87
msgid "Index directory"
msgstr ""

#: templates/columns_definitions/partitions.twig:88
msgid "Max rows"
msgstr ""

#: templates/columns_definitions/partitions.twig:89
msgid "Min rows"
msgstr ""

#: templates/columns_definitions/partitions.twig:90
#, fuzzy
#| msgid "Table"
msgid "Table space"
msgstr "Tabäll"

#: templates/columns_definitions/partitions.twig:91
msgid "Node group"
msgstr ""

#: templates/columns_definitions/table_fields_definitions.twig:16
#: templates/database/central_columns/edit.twig:10
#: templates/database/routines/editor_form.twig:52
msgid "Length/Values"
msgstr ""

#: templates/columns_definitions/table_fields_definitions.twig:17
msgid ""
"If column type is \"enum\" or \"set\", please enter the values using this "
"format: 'a','b','c'…<br>If you ever need to put a backslash (\"\\\") or a "
"single quote (\"'\") amongst those values, precede it with a backslash (for "
"example '\\\\xyz' or 'a\\'b')."
msgstr ""

#: templates/columns_definitions/table_fields_definitions.twig:21
msgid ""
"For default values, please enter just a single value, without backslash "
"escaping or quotes, using this format: a"
msgstr ""

#: templates/columns_definitions/table_fields_definitions.twig:24
#: templates/database/central_columns/edit.twig:12
#: templates/database/central_columns/main.twig:32
#: templates/database/central_columns/main.twig:236
#: templates/database/data_dictionary/index.twig:79
#: templates/database/operations/index.twig:194
#: templates/database/operations/index.twig:198
#: templates/database/structure/table_header.twig:31
#: templates/home/<USER>/indexes.twig:22
#: templates/server/databases/index.twig:29
#: templates/server/databases/index.twig:30
#: templates/server/databases/index.twig:123
#: templates/table/operations/index.twig:151
#: templates/table/search/index.twig:40
#: templates/table/structure/display_structure.twig:23
#: templates/table/structure/display_structure.twig:481
#: templates/table/structure/display_table_stats.twig:108
#: templates/table/tracking/structure_snapshot_columns.twig:8
#: templates/table/tracking/structure_snapshot_indexes.twig:11
#: templates/table/zoom_search/index.twig:38
msgid "Collation"
msgstr ""

#: templates/columns_definitions/table_fields_definitions.twig:36
#: templates/database/operations/index.twig:68
#: templates/database/operations/index.twig:173
#: templates/database/routines/editor_form.twig:124
#: templates/database/structure/copy_form.twig:50
#: templates/table/operations/index.twig:79
#: templates/table/operations/index.twig:115
#: templates/table/operations/index.twig:315
msgid "Adjust privileges"
msgstr ""

#: templates/columns_definitions/table_fields_definitions.twig:59
msgid "Virtuality"
msgstr ""

#: templates/columns_definitions/table_fields_definitions.twig:65
msgid "Move column"
msgstr ""

#: templates/columns_definitions/table_fields_definitions.twig:75
#: templates/columns_definitions/table_fields_definitions.twig:86
msgid "List of available transformations and their options"
msgstr ""

#: templates/columns_definitions/table_fields_definitions.twig:77
#: templates/transformation_overview.twig:18
msgid "Browser display transformation"
msgstr ""

#: templates/columns_definitions/table_fields_definitions.twig:81
msgid "Browser display transformation options"
msgstr ""

#: templates/columns_definitions/table_fields_definitions.twig:82
#: templates/columns_definitions/table_fields_definitions.twig:93
msgid ""
"Please enter the values for transformation options using this format: 'a', "
"100, b,'c'…<br>If you ever need to put a backslash (\"\\\") or a single "
"quote (\"'\") amongst those values, precede it with a backslash (for example "
"'\\\\xyz' or 'a\\'b')."
msgstr ""

#: templates/columns_definitions/table_fields_definitions.twig:88
#: templates/transformation_overview.twig:37
msgid "Input transformation"
msgstr ""

#: templates/columns_definitions/table_fields_definitions.twig:92
msgid "Input transformation options"
msgstr ""

#: templates/config/form_display/input.twig:15
msgid "This setting is disabled, it will not be applied to your configuration."
msgstr ""

#: templates/config/form_display/input.twig:57
#: templates/config/form_display/input.twig:58
#, php-format
msgid "Set value: %s"
msgstr ""

#: templates/config/form_display/input.twig:63
#: templates/config/form_display/input.twig:64
msgid "Restore default value"
msgstr ""

#: templates/config/form_display/input.twig:79
#: templates/config/form_display/input.twig:80
msgid "Allow users to customize this value"
msgstr ""

#: templates/console/bookmark_content.twig:7 templates/console/display.twig:31
#: templates/console/display.twig:84 templates/console/display.twig:175
msgid "Collapse"
msgstr ""

#: templates/console/bookmark_content.twig:7 templates/console/display.twig:31
#: templates/console/display.twig:84 templates/console/display.twig:175
msgid "Expand"
msgstr ""

#: templates/console/bookmark_content.twig:7 templates/console/display.twig:31
#: templates/console/display.twig:175
msgid "Requery"
msgstr ""

#: templates/console/display.twig:7 templates/setup/home/<USER>
#: templates/sql/query.twig:38
msgid "Clear"
msgstr ""

#: templates/console/display.twig:7
msgid "History"
msgstr ""

#: templates/console/display.twig:11 templates/console/display.twig:99
msgid "Bookmarks"
msgstr ""

#: templates/console/display.twig:20
msgid "Press Ctrl+Enter to execute query"
msgstr ""

#: templates/console/display.twig:23
msgid "Press Enter to execute query"
msgstr ""

#: templates/console/display.twig:31 templates/console/display.twig:175
msgid "Explain"
msgstr ""

#: templates/console/display.twig:40 templates/console/display.twig:184
msgid "Bookmark"
msgstr ""

#: templates/console/display.twig:40 templates/console/display.twig:184
msgid "Query failed"
msgstr ""

#: templates/console/display.twig:42 templates/console/display.twig:184
msgid "Queried time"
msgstr ""

#: templates/console/display.twig:47
msgid "During current session"
msgstr ""

#: templates/console/display.twig:64
msgid "ascending"
msgstr ""

#: templates/console/display.twig:64
msgid "descending"
msgstr ""

#: templates/console/display.twig:64
msgid "Order:"
msgstr ""

#: templates/console/display.twig:64 templates/console/display.twig:84
#: templates/table/find_replace/replace_preview.twig:16
msgid "Count"
msgstr ""

#: templates/console/display.twig:64
msgid "Execution order"
msgstr ""

#: templates/console/display.twig:64 templates/console/display.twig:84
msgid "Time taken"
msgstr ""

#: templates/console/display.twig:64 templates/table/search/index.twig:146
msgid "Order by:"
msgstr ""

#: templates/console/display.twig:64
msgid "Ungroup queries"
msgstr ""

#: templates/console/display.twig:84
#, fuzzy
#| msgid "not active"
msgid "Show trace"
msgstr "nit aktief"

#: templates/console/display.twig:84
msgid "Hide trace"
msgstr ""

#: templates/console/display.twig:112
msgid "Add bookmark"
msgstr ""

#: templates/console/display.twig:121
msgid "Label"
msgstr ""

#: templates/console/display.twig:124
#, fuzzy
#| msgid "Select All"
msgid "Target database"
msgstr "Alle ußwähle"

#: templates/console/display.twig:127
msgid "Share this bookmark"
msgstr ""

#: templates/console/display.twig:140
#, fuzzy
#| msgid "Default"
msgid "Set default"
msgstr "Schtandatt"

#: templates/console/display.twig:162
msgid ""
"Execute queries on Enter and insert new line with Shift+Enter. To make this "
"permanent, view settings."
msgstr ""

#: templates/create_tracking_version.twig:10
#, php-format
msgid "Create version %1$s of %2$s"
msgstr ""

#: templates/create_tracking_version.twig:15
#, php-format
msgid "Create version %1$s"
msgstr ""

#: templates/create_tracking_version.twig:21
msgid "Track these data definition statements:"
msgstr ""

#: templates/create_tracking_version.twig:60
msgid "Track these data manipulation statements:"
msgstr ""

#: templates/create_tracking_version.twig:77
msgid "Create version"
msgstr ""

#: templates/database/central_columns/edit.twig:15
msgctxt "Auto Increment"
msgid "A_I"
msgstr ""

#: templates/database/central_columns/main.twig:4
msgid "Add new column"
msgstr ""

#: templates/database/central_columns/main.twig:24
#: templates/database/central_columns/main.twig:228
msgid "Length/Value"
msgstr ""

#: templates/database/central_columns/main.twig:36
#: templates/database/central_columns/main.twig:240
msgid "Attribute"
msgstr ""

#: templates/database/central_columns/main.twig:44
#: templates/database/central_columns/main.twig:248
msgid "A_I"
msgstr ""

#: templates/database/central_columns/main.twig:129
#, fuzzy
#| msgid "The central list of columns for the current database is empty."
msgid "The central list of columns for the current database is empty"
msgstr "De Houpleß met de Kolonne för heh di Dahtebangk es läddesch."

#: templates/database/central_columns/main.twig:166
#: templates/display/results/table.twig:62
msgid "Filter rows"
msgstr ""

#: templates/database/central_columns/main.twig:167
#: templates/display/results/table.twig:64
msgid "Search this table"
msgstr ""

#: templates/database/central_columns/main.twig:178
#: templates/table/structure/display_structure.twig:435
msgid "Add column"
msgstr ""

#: templates/database/central_columns/main.twig:187
#, fuzzy
#| msgid "Select All"
msgid "Select a table"
msgstr "Alle ußwähle"

#: templates/database/central_columns/main.twig:194
#, fuzzy
#| msgid "Select All"
msgid "Select a column."
msgstr "Alle ußwähle"

#: templates/database/central_columns/main.twig:213
msgid "Click to sort."
msgstr "Aanklecke för ze zoteere."

#: templates/database/central_columns/main.twig:218
#: templates/database/privileges/index.twig:22
#: templates/database/structure/table_header.twig:22
#: templates/database/tracking/tables.twig:18
#: templates/database/tracking/tables.twig:154 templates/indexes.twig:15
#: templates/server/databases/index.twig:163
#: templates/server/privileges/privileges_summary.twig:22
#: templates/server/privileges/users_overview.twig:22
#: templates/server/user_groups/user_groups.twig:21
#: templates/server/variables/index.twig:30
#: templates/table/privileges/index.twig:24
#: templates/table/structure/display_partitions.twig:33
#: templates/table/structure/display_structure.twig:34
#: templates/table/structure/display_structure.twig:474
#: templates/table/tracking/main.twig:32
#: templates/table/tracking/report_table.twig:8
msgid "Action"
msgstr ""

#: templates/database/create_table.twig:3
#: templates/database/operations/index.twig:27
#, fuzzy
#| msgid "Created"
msgid "Create new table"
msgstr "Aanjelaat"

#: templates/database/create_table.twig:10
#: templates/database/operations/index.twig:34
msgid "Number of columns"
msgstr ""

#: templates/database/create_table.twig:14
#: templates/database/operations/index.twig:39 templates/export.twig:27
#: templates/server/databases/index.twig:46
msgid "Create"
msgstr ""

#: templates/database/data_dictionary/index.twig:4
#, fuzzy
#| msgid "Database"
msgid "Database comment:"
msgstr "Dahtebangk"

#: templates/database/data_dictionary/index.twig:8
#: templates/database/data_dictionary/index.twig:124
#: templates/database/structure/index.twig:19
#: templates/display/results/table.twig:258
#: templates/table/structure/display_structure.twig:393
msgid "Print"
msgstr ""

#: templates/database/data_dictionary/index.twig:76 templates/indexes.twig:19
#: templates/table/structure/display_structure.twig:478
#: templates/table/tracking/structure_snapshot_indexes.twig:8
msgid "Packed"
msgstr ""

#: templates/database/data_dictionary/index.twig:78 templates/indexes.twig:21
#: templates/table/structure/display_structure.twig:480
#: templates/table/tracking/structure_snapshot_indexes.twig:10
msgid "Cardinality"
msgstr ""

#: templates/database/data_dictionary/index.twig:117 templates/indexes.twig:92
#: templates/table/structure/display_structure.twig:559
msgid "No index defined!"
msgstr ""

#: templates/database/designer/database_tables.twig:31
#: templates/database/export/index.twig:28
#: templates/database/search/main.twig:43 templates/server/export/index.twig:10
#: templates/server/privileges/privileges_table.twig:28
#: templates/server/privileges/privileges_table.twig:55
#: templates/server/privileges/privileges_table.twig:82
#: templates/server/privileges/privileges_table.twig:109
#: templates/server/replication/database_multibox.twig:7
#, fuzzy
#| msgid "Select All"
msgid "Select all"
msgstr "Alle ußwähle"

#: templates/database/designer/database_tables.twig:37
msgid "Show/hide columns"
msgstr ""

#: templates/database/designer/database_tables.twig:46
msgid "See table structure"
msgstr ""

#: templates/database/designer/database_tables.twig:91
#, fuzzy, php-format
#| msgid "Select All"
msgid "Select \"%s\""
msgstr "Alle ußwähle"

#: templates/database/designer/database_tables.twig:109
#, php-format
msgid "Add an option for column \"%s\"."
msgstr ""

#: templates/database/designer/edit_delete_pages.twig:6
msgid "Page to open"
msgstr ""

#: templates/database/designer/edit_delete_pages.twig:6
msgid "Page to delete"
msgstr ""

#: templates/database/designer/main.twig:19
#: templates/database/designer/main.twig:25
msgid "Show/Hide tables list"
msgstr ""

#: templates/database/designer/main.twig:29
#: templates/database/designer/main.twig:35
#: templates/database/designer/main.twig:36
msgid "View in fullscreen"
msgstr ""

#: templates/database/designer/main.twig:34
msgid "Exit fullscreen"
msgstr ""

#: templates/database/designer/main.twig:48
#: templates/database/designer/main.twig:52
msgid "New page"
msgstr ""

#: templates/database/designer/main.twig:77
#: templates/database/designer/main.twig:80
msgid "Delete pages"
msgstr ""

#: templates/database/designer/main.twig:84
#: templates/database/designer/main.twig:87
#: templates/database/structure/show_create.twig:11
msgid "Create table"
msgstr ""

#: templates/database/designer/main.twig:91
#: templates/database/designer/main.twig:94
#: templates/database/designer/main.twig:271
msgid "Create relationship"
msgstr ""

#: templates/database/designer/main.twig:105
#: templates/database/designer/main.twig:108
msgid "Reload"
msgstr ""

#: templates/database/designer/main.twig:112
#: templates/database/designer/main.twig:115
msgid "Help"
msgstr ""

#: templates/database/designer/main.twig:120
#: templates/database/designer/main.twig:123
msgid "Angular links"
msgstr ""

#: templates/database/designer/main.twig:120
#: templates/database/designer/main.twig:123
msgid "Direct links"
msgstr ""

#: templates/database/designer/main.twig:127
#: templates/database/designer/main.twig:129
msgid "Snap to grid"
msgstr ""

#: templates/database/designer/main.twig:133
#: templates/database/designer/main.twig:139
msgid "Small/Big All"
msgstr ""

#: templates/database/designer/main.twig:143
#: templates/database/designer/main.twig:146
msgid "Toggle small/big"
msgstr ""

#: templates/database/designer/main.twig:150
#: templates/database/designer/main.twig:153
msgid "Toggle relationship lines"
msgstr ""

#: templates/database/designer/main.twig:158
#: templates/database/designer/main.twig:161
msgid "Export schema"
msgstr ""

#: templates/database/designer/main.twig:169
#: templates/database/designer/main.twig:172
msgid "Build Query"
msgstr ""

#: templates/database/designer/main.twig:177
#: templates/database/designer/main.twig:181
msgid "Move Menu"
msgstr ""

#: templates/database/designer/main.twig:185
#: templates/database/designer/main.twig:190
msgid "Pin text"
msgstr ""

#: templates/database/designer/main.twig:202
msgid "Hide/Show all"
msgstr ""

#: templates/database/designer/main.twig:212
msgid "Hide/Show tables with no relationship"
msgstr ""

#: templates/database/designer/main.twig:223
msgid "Number of tables:"
msgstr ""

#: templates/database/designer/main.twig:381
msgid "Delete relationship"
msgstr ""

#: templates/database/designer/main.twig:445
#: templates/database/designer/main.twig:610
msgid "Relationship operator"
msgstr ""

#: templates/database/designer/main.twig:474
#: templates/database/designer/main.twig:639
#: templates/database/designer/main.twig:845
#: templates/database/designer/main.twig:1038
msgid "Except"
msgstr ""

#: templates/database/designer/main.twig:484
#: templates/database/designer/main.twig:649
#: templates/database/designer/main.twig:855
#: templates/database/designer/main.twig:1048
#: templates/database/routines/execute_form.twig:20
#: templates/server/replication/status_table.twig:17
#: templates/server/status/variables/index.twig:76
#: templates/server/variables/index.twig:32
#: templates/table/insert/get_head_and_foot_of_insert_row_table.twig:9
#: templates/table/search/index.twig:42
#: templates/table/zoom_search/index.twig:40
#: templates/table/zoom_search/result_form.twig:37
msgid "Value"
msgstr ""

#: templates/database/designer/main.twig:486
#: templates/database/designer/main.twig:651
#: templates/database/designer/main.twig:857
#: templates/database/designer/main.twig:1050
msgid "subquery"
msgstr ""

#: templates/database/designer/main.twig:495
#: templates/database/designer/main.twig:711
msgid "Rename to"
msgstr ""

#: templates/database/designer/main.twig:501
#: templates/database/designer/main.twig:719
msgid "New name"
msgstr ""

#: templates/database/designer/main.twig:510
#: templates/database/designer/main.twig:916
msgid "Aggregate"
msgstr ""

#: templates/database/designer/main.twig:516
#: templates/database/designer/main.twig:580
#: templates/database/designer/main.twig:785
#: templates/database/designer/main.twig:816
#: templates/database/designer/main.twig:924
#: templates/database/designer/main.twig:1009
#: templates/table/search/index.twig:41
#: templates/table/zoom_search/index.twig:39
msgid "Operator"
msgstr ""

#: templates/database/designer/main.twig:1090
msgid "Active options"
msgstr ""

#: templates/database/designer/page_save_as.twig:19
msgid "Save to selected page"
msgstr ""

#: templates/database/designer/page_save_as.twig:23
msgid "Create a page and save to it"
msgstr ""

#: templates/database/designer/page_save_as.twig:29
msgid "New page name"
msgstr ""

#: templates/database/designer/page_selector.twig:2
msgid "Select page"
msgstr ""

#: templates/database/designer/schema_export.twig:4
msgid "Select Export Relational Type"
msgstr ""

#: templates/database/events/editor_form.twig:10
#: templates/database/routines/editor_form.twig:11
#: templates/database/triggers/editor_form.twig:14 templates/view_create.twig:8
msgid "Details"
msgstr ""

#: templates/database/events/editor_form.twig:19
#, fuzzy
#| msgid "The database name is empty!"
msgid "Event name"
msgstr "Kein Name för di Dahtebangk!"

#: templates/database/events/editor_form.twig:35
#: templates/server/binlog/index.twig:86
msgid "Event type"
msgstr ""

#: templates/database/events/editor_form.twig:48
#: templates/database/routines/editor_form.twig:38
#, php-format
msgid "Change to %s"
msgstr ""

#: templates/database/events/editor_form.twig:53
msgid "Execute at"
msgstr ""

#: templates/database/events/editor_form.twig:59
msgid "Execute every"
msgstr ""

#: templates/database/events/editor_form.twig:70
msgctxt "Start of recurring event"
msgid "Start"
msgstr ""

#: templates/database/events/editor_form.twig:76
msgctxt "End of recurring event"
msgid "End"
msgstr ""

#: templates/database/events/editor_form.twig:90
msgid "On completion preserve"
msgstr ""

#: templates/database/events/editor_form.twig:96
#: templates/database/routines/editor_form.twig:138
#: templates/database/triggers/editor_form.twig:61
#: templates/view_create.twig:45
msgid "Definer"
msgstr ""

#: templates/database/events/index.twig:13
#: templates/database/privileges/index.twig:113
#: templates/database/privileges/index.twig:114
#: templates/database/routines/index.twig:13
#: templates/database/structure/check_all_tables.twig:3
#: templates/database/structure/check_all_tables.twig:4
#: templates/database/triggers/list.twig:13
#: templates/display/results/table.twig:223
#: templates/display/results/table.twig:224 templates/select_all.twig:4
#: templates/select_all.twig:5 templates/server/databases/index.twig:64
#: templates/server/privileges/edit_routine_privileges.twig:57
#: templates/server/privileges/privileges_table.twig:268
#: templates/server/privileges/privileges_table.twig:269
#: templates/server/privileges/privileges_table.twig:277
#: templates/server/privileges/privileges_table.twig:351
#: templates/server/privileges/privileges_table.twig:551
#: templates/server/privileges/users_overview.twig:121
#: templates/server/privileges/users_overview.twig:122
#: templates/server/user_groups/edit_user_groups.twig:11
#: templates/table/privileges/index.twig:117
#: templates/table/privileges/index.twig:118
#, fuzzy
msgid "Check all"
msgstr "Ein Tabäll"

#: templates/database/events/index.twig:27
#, fuzzy
#| msgid "Created"
msgid "Create new event"
msgstr "Aanjelaat"

#: templates/database/events/index.twig:36
msgid "There are no events to display."
msgstr ""

#: templates/database/events/index.twig:112
msgid "Event scheduler status"
msgstr ""

#: templates/database/events/index.twig:117
#: templates/database/tracking/tables.twig:47
msgid "Click to toggle"
msgstr ""

#: templates/database/events/index.twig:130
msgid "ON"
msgstr ""

#: templates/database/events/index.twig:141
msgid "OFF"
msgstr ""

#: templates/database/export/index.twig:61
msgid ""
"@SERVER@ will become the server name and @DATABASE@ will become the database "
"name."
msgstr ""

#. l10n: A query that the user has written freely
#: templates/database/export/index.twig:5 templates/table/export/index.twig:5
msgid "Exporting a raw query"
msgstr ""

#: templates/database/export/index.twig:7
#, php-format
msgid "Exporting tables from \"%s\" database"
msgstr ""

#: templates/database/export/index.twig:30
msgid "Export the structure of all tables."
msgstr ""

#: templates/database/export/index.twig:33
msgid "Export the data of all tables."
msgstr ""

#: templates/database/import/index.twig:3
#, php-format
msgid "Importing into the database \"%s\""
msgstr ""

#: templates/database/multi_table_query/form.twig:4
#: templates/database/qbe/index.twig:4
msgid "Multi-table query"
msgstr ""

#: templates/database/multi_table_query/form.twig:10
#: templates/database/qbe/index.twig:10
msgid "Query by example"
msgstr ""

#: templates/database/multi_table_query/form.twig:17
msgid "Query window"
msgstr ""

#: templates/database/multi_table_query/form.twig:38
#: templates/database/multi_table_query/form.twig:128
#, fuzzy
#| msgid "Select All"
msgid "select table"
msgstr "Alle ußwähle"

#: templates/database/multi_table_query/form.twig:45
#: templates/database/multi_table_query/form.twig:136
#, fuzzy
#| msgid "Select All"
msgid "select column"
msgstr "Alle ußwähle"

#: templates/database/multi_table_query/form.twig:51
#, fuzzy
#| msgid "Table"
msgid "Table alias"
msgstr "Tabäll"

#: templates/database/multi_table_query/form.twig:52
#, fuzzy
#| msgid "Column"
msgid "Column alias"
msgstr "Kolonn"

#: templates/database/multi_table_query/form.twig:55
msgid "Use this column in criteria"
msgstr ""

#: templates/database/multi_table_query/form.twig:59
msgid "criteria"
msgstr ""

#: templates/database/multi_table_query/form.twig:73
msgid "Add as"
msgstr ""

#: templates/database/multi_table_query/form.twig:119
msgid "Another column"
msgstr ""

#: templates/database/multi_table_query/form.twig:146
msgid "Enter criteria as free text"
msgstr ""

#: templates/database/multi_table_query/form.twig:153
#, fuzzy
#| msgid "Select All"
msgid "Remove this column"
msgstr "Alle ußwähle"

#: templates/database/multi_table_query/form.twig:159
#: templates/table/relation/foreign_key_row.twig:85
msgid "+ Add column"
msgstr ""

#: templates/database/multi_table_query/form.twig:174
#: templates/database/qbe/selection_form.twig:80
#: templates/database/qbe/selection_form.twig:96
msgid "Update query"
msgstr ""

#: templates/database/operations/index.twig:9
#: templates/database/operations/index.twig:13
msgid "Database comment"
msgstr ""

#: templates/database/operations/index.twig:54
msgid "Rename database to"
msgstr ""

#: templates/database/operations/index.twig:58 templates/export.twig:282
#, fuzzy
#| msgid "The database name is empty!"
msgid "New database name"
msgstr "Kein Name för di Dahtebangk!"

#: templates/database/operations/index.twig:66
#: templates/database/operations/index.twig:171
#: templates/table/operations/index.twig:77
#: templates/table/operations/index.twig:113
#: templates/table/operations/index.twig:313
msgid ""
"You don't have sufficient privileges to perform this operation; Please refer "
"to the documentation for more details."
msgstr ""

#: templates/database/operations/index.twig:83
msgid "Remove database"
msgstr ""

#: templates/database/operations/index.twig:89
#, php-format
msgid "Database %s has been dropped."
msgstr ""

#: templates/database/operations/index.twig:94
msgid "Drop the database (DROP)"
msgstr ""

#: templates/database/operations/index.twig:118
msgid "Copy database to"
msgstr ""

#: templates/database/operations/index.twig:150
msgid "CREATE DATABASE before copying"
msgstr ""

#: templates/database/operations/index.twig:165
#: templates/database/structure/copy_form.twig:44
#: templates/table/operations/index.twig:306
msgid "Add constraints"
msgstr ""

#: templates/database/operations/index.twig:180
msgid "Switch to copied database"
msgstr ""

#: templates/database/operations/index.twig:216
msgid "Change all tables collations"
msgstr ""

#: templates/database/operations/index.twig:220
msgid "Change all tables columns collations"
msgstr ""

#: templates/database/privileges/index.twig:9
#: templates/table/privileges/index.twig:8
#, php-format
msgid "Users having access to \"%s\""
msgstr ""

#: templates/database/privileges/index.twig:17
#: templates/server/privileges/login_information_fields.twig:6
#: templates/server/privileges/login_information_fields.twig:11
#: templates/server/privileges/users_overview.twig:8
#: templates/server/replication/change_primary.twig:15
#: templates/server/replication/primary_add_replica_user.twig:19
#: templates/server/replication/primary_add_replica_user.twig:24
#: templates/table/privileges/index.twig:19
msgid "User name"
msgstr ""

#: templates/database/privileges/index.twig:18
#: templates/server/privileges/login_information_fields.twig:25
#: templates/server/privileges/login_information_fields.twig:37
#: templates/server/privileges/users_overview.twig:9
#: templates/table/privileges/index.twig:20
msgid "Host name"
msgstr ""

#: templates/database/privileges/index.twig:21
#: templates/server/privileges/privileges_summary.twig:16
#: templates/server/privileges/users_overview.twig:18
#: templates/table/privileges/index.twig:23
msgid "Grant"
msgstr ""

#: templates/database/privileges/index.twig:36
#: templates/server/privileges/new_user_ajax.twig:9
#: templates/server/privileges/users_overview.twig:36
#: templates/table/privileges/index.twig:38
msgid "Any"
msgstr ""

#: templates/database/privileges/index.twig:47
#: templates/table/privileges/index.twig:49
msgid "global"
msgstr ""

#: templates/database/privileges/index.twig:50
#: templates/table/privileges/index.twig:52
msgid "database-specific"
msgstr ""

#: templates/database/privileges/index.twig:52
#: templates/table/privileges/index.twig:54
msgid "wildcard"
msgstr ""

#: templates/database/privileges/index.twig:55
#: templates/table/privileges/index.twig:59
msgid "routine"
msgstr ""

#: templates/database/privileges/index.twig:112
#: templates/database/privileges/index.twig:115
#: templates/database/structure/check_all_tables.twig:2
#: templates/database/structure/check_all_tables.twig:9
#: templates/display/results/table.twig:222
#: templates/display/results/table.twig:225 templates/select_all.twig:2
#: templates/select_all.twig:6
#: templates/server/privileges/users_overview.twig:120
#: templates/server/privileges/users_overview.twig:123
#: templates/table/privileges/index.twig:116
#: templates/table/privileges/index.twig:119
msgid "With selected:"
msgstr ""

#: templates/database/privileges/index.twig:131
#: templates/server/privileges/add_user_fieldset.twig:4
#: templates/server/privileges/user_overview.twig:22
#: templates/server/privileges/users_overview.twig:137
#: templates/table/privileges/index.twig:134
msgctxt "Create new user"
msgid "New"
msgstr ""

#: templates/database/privileges/index.twig:136
#: templates/server/privileges/add_user_fieldset.twig:9
#: templates/server/privileges/add_user.twig:3
#: templates/server/privileges/user_overview.twig:24
#: templates/server/privileges/users_overview.twig:140
#: templates/table/privileges/index.twig:140
msgid "Add user account"
msgstr ""

#: templates/database/qbe/index.twig:16
#, php-format
msgid "Switch to %svisual builder%s"
msgstr ""

#: templates/database/qbe/index.twig:20
msgid "You have to choose at least one column to display!"
msgstr "Do moß winnischsdens ein Kolonn zom Aanzeije ußsöhke!"

#: templates/database/qbe/ins_del_and_or_cell.twig:5
msgid "Ins:"
msgstr ""

#: templates/database/qbe/ins_del_and_or_cell.twig:12
msgid "And"
msgstr ""

#: templates/database/qbe/ins_del_and_or_cell.twig:17
msgid "Del:"
msgstr ""

#: templates/database/qbe/selection_form.twig:12
#: templates/table/find_replace/index.twig:41
msgid "Column:"
msgstr ""

#: templates/database/qbe/selection_form.twig:17
msgid "Alias:"
msgstr ""

#: templates/database/qbe/selection_form.twig:22
msgid "Show:"
msgstr ""

#: templates/database/qbe/selection_form.twig:27
msgid "Sort:"
msgstr ""

#: templates/database/qbe/selection_form.twig:32
msgid "Sort order:"
msgstr ""

#: templates/database/qbe/selection_form.twig:37
msgid "Criteria:"
msgstr ""

#: templates/database/qbe/selection_form.twig:44
msgid "Modify:"
msgstr ""

#: templates/database/qbe/selection_form.twig:54
msgid "Add/Delete criteria rows:"
msgstr ""

#: templates/database/qbe/selection_form.twig:67
#, fuzzy
#| msgid "Select All"
msgid "Add/Delete columns:"
msgstr "Alle ußwähle"

#: templates/database/qbe/selection_form.twig:86
#: templates/database/qbe/selection_form.twig:88
#, fuzzy
msgid "Use tables"
msgstr "Ein Tabäll"

#: templates/database/qbe/selection_form.twig:107
#, php-format
msgid "SQL query on database <b>%s</b>:"
msgstr ""

#: templates/database/routines/editor_form.twig:20
msgid "Routine name"
msgstr ""

#: templates/database/routines/editor_form.twig:43
msgid "Parameters"
msgstr ""

#: templates/database/routines/editor_form.twig:49
msgid "Direction"
msgstr ""

#: templates/database/routines/editor_form.twig:66
msgid "Add parameter"
msgstr ""

#: templates/database/routines/editor_form.twig:67
msgid "Remove last parameter"
msgstr ""

#: templates/database/routines/editor_form.twig:71
msgid "Return type"
msgstr ""

#: templates/database/routines/editor_form.twig:79
msgid "Return length/values"
msgstr ""

#: templates/database/routines/editor_form.twig:86
msgid "Return options"
msgstr ""

#: templates/database/routines/editor_form.twig:90
#: templates/database/routines/parameter_row.twig:31
#: templates/database/structure/table_header.twig:42
msgid "Charset"
msgstr ""

#: templates/database/routines/editor_form.twig:115
msgid "Is deterministic"
msgstr ""

#: templates/database/routines/editor_form.twig:131
msgid ""
"You do not have sufficient privileges to perform this operation; Please "
"refer to the documentation for more details."
msgstr ""

#: templates/database/routines/editor_form.twig:144
msgid "Security type"
msgstr ""

#: templates/database/routines/editor_form.twig:153
msgid "SQL data access"
msgstr ""

#: templates/database/routines/execute_form.twig:7
#: templates/database/routines/execute_form.twig:12
msgid "Routine parameters"
msgstr ""

#: templates/database/routines/index.twig:33
#, fuzzy
#| msgid "Created"
msgid "Create new routine"
msgstr "Aanjelaat"

#: templates/database/routines/index.twig:42
msgid "There are no routines to display."
msgstr ""

#: templates/database/routines/index.twig:51
msgid "Returns"
msgstr ""

#: templates/database/routines/parameter_row.twig:24
#: templates/modals/enum_set_editor.twig:5
msgid "ENUM/SET editor"
msgstr ""

#: templates/database/routines/row.twig:38
#: templates/database/routines/row.twig:48
#: templates/database/routines/row.twig:52
msgid "Execute"
msgstr ""

#: templates/database/search/main.twig:5
msgid "Search in database"
msgstr ""

#: templates/database/search/main.twig:8
msgid "Words or values to search for (wildcard: \"%\"):"
msgstr ""

#: templates/database/search/main.twig:15
#: templates/table/find_replace/index.twig:31
msgid "Find:"
msgstr ""

#: templates/database/search/main.twig:19
#: templates/database/search/main.twig:23
msgid "Words are separated by a space character (\" \")."
msgstr ""

#: templates/database/search/main.twig:40
msgid "Inside tables:"
msgstr ""

#: templates/database/search/main.twig:46 templates/server/export/index.twig:11
#: templates/server/replication/database_multibox.twig:8
#, fuzzy
#| msgid "Unselect All"
msgid "Unselect all"
msgstr "Alle nit mieh ußwähle"

#: templates/database/search/main.twig:67
msgid "Inside column:"
msgstr ""

#: templates/database/search/results.twig:12
#, php-format
msgid "%1$s match in <strong>%2$s</strong>"
msgid_plural "%1$s matches in <strong>%2$s</strong>"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: templates/database/search/results.twig:56
msgid "<strong>Total:</strong> <em>%count%</em> match"
msgid_plural "<strong>Total:</strong> <em>%count%</em> matches"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: templates/database/structure/add_prefix.twig:7
#: templates/database/structure/add_prefix.twig:9
msgid "Add prefix"
msgstr ""

#: templates/database/structure/body_for_table_summary.twig:6
#, fuzzy, php-format
msgid "%s table"
msgid_plural "%s tables"
msgstr[0] "Ein Tabäll"
msgstr[1] "%s Tabälle"
msgstr[2] ""

#: templates/database/structure/body_for_table_summary.twig:17
msgid "Sum"
msgstr ""

#: templates/database/structure/bulk_action_modal.twig:12
#: templates/database/structure/check_all_tables.twig:56
msgid "Continue"
msgstr ""

#: templates/database/structure/change_prefix_form.twig:7
msgid "From"
msgstr ""

#: templates/database/structure/change_prefix_form.twig:13
msgid "To"
msgstr ""

#: templates/database/structure/check_all_tables.twig:6
msgid "Check tables having overhead"
msgstr ""

#: templates/database/structure/check_all_tables.twig:10
#, fuzzy
#| msgid "Select All"
msgid "Copy table"
msgstr "Alle ußwähle"

#: templates/database/structure/check_all_tables.twig:11
msgid "Show create"
msgstr ""

#: templates/database/structure/check_all_tables.twig:14
#: templates/table/operations/index.twig:403
#: templates/table/operations/view.twig:26
msgid "Delete data or table"
msgstr ""

#: templates/database/structure/check_all_tables.twig:15
#: templates/database/structure/structure_table_row.twig:74
msgid "Empty"
msgstr ""

#: templates/database/structure/check_all_tables.twig:18
#: templates/table/operations/index.twig:334
msgid "Table maintenance"
msgstr ""

#: templates/database/structure/check_all_tables.twig:19
#: templates/table/maintenance/analyze.twig:3
#: templates/table/operations/index.twig:339
msgid "Analyze table"
msgstr ""

#: templates/database/structure/check_all_tables.twig:20
#: templates/table/maintenance/check.twig:3
#: templates/table/operations/index.twig:348
msgid "Check table"
msgstr ""

#: templates/database/structure/check_all_tables.twig:21
#: templates/table/maintenance/checksum.twig:3
#: templates/table/operations/index.twig:356
#, fuzzy
msgid "Checksum table"
msgstr "Ein Tabäll"

#: templates/database/structure/check_all_tables.twig:22
#: templates/table/maintenance/optimize.twig:3
#: templates/table/operations/index.twig:384
#: templates/table/structure/display_table_stats.twig:68
msgid "Optimize table"
msgstr ""

#: templates/database/structure/check_all_tables.twig:23
#: templates/table/maintenance/repair.twig:3
#: templates/table/operations/index.twig:393
msgid "Repair table"
msgstr ""

#: templates/database/structure/check_all_tables.twig:25
msgid "Prefix"
msgstr ""

#: templates/database/structure/check_all_tables.twig:26
msgid "Add prefix to table"
msgstr ""

#: templates/database/structure/check_all_tables.twig:27
msgid "Replace table prefix"
msgstr ""

#: templates/database/structure/check_all_tables.twig:33
msgid "Add columns to central list"
msgstr ""

#: templates/database/structure/check_all_tables.twig:34
msgid "Remove columns from central list"
msgstr ""

#: templates/database/structure/check_all_tables.twig:35
msgid "Make consistent with central list"
msgstr ""

#: templates/database/structure/check_all_tables.twig:48
msgid "Are you sure?"
msgstr ""

#: templates/database/structure/check_all_tables.twig:52
msgid ""
"This action may change some of the columns definition.[br]Are you sure you "
"want to continue?"
msgstr ""

#: templates/database/structure/copy_form.twig:14
msgid "Options:"
msgstr ""

#: templates/database/structure/copy_form.twig:39
msgid "Add AUTO INCREMENT value"
msgstr ""

#: templates/database/structure/drop_form.twig:6
#: templates/database/structure/empty_form.twig:6
#: templates/table/delete/confirm.twig:12
#: templates/table/structure/drop_confirm.twig:6
#: templates/table/structure/primary.twig:6
msgid "Do you really want to execute the following query?"
msgstr ""

#: templates/database/structure/favorite_anchor.twig:4
#: templates/recent_favorite_table_favorite.twig:6
msgid "Remove from Favorites"
msgstr ""

#: templates/database/structure/favorite_anchor.twig:4
msgid "Add to Favorites"
msgstr ""

#: templates/database/structure/show_create.twig:2
msgid "Showing create queries"
msgstr ""

#: templates/database/structure/show_create.twig:33
#: templates/display/results/table.twig:297 templates/modals/create_view.twig:5
#: templates/sql/no_results_returned.twig:13 templates/view_create.twig:11
msgid "Create view"
msgstr ""

#: templates/database/structure/structure_table_row.twig:17
#: templates/server/databases/index.twig:219
#: templates/server/databases/index.twig:231
msgid "Not replicated"
msgstr ""

#: templates/database/structure/structure_table_row.twig:18
#: templates/server/databases/index.twig:215
#: templates/server/databases/index.twig:227
msgid "Replicated"
msgstr ""

#: templates/database/structure/structure_table_row.twig:223
msgid "in use"
msgstr "weed jebruch"

#: templates/database/structure/table_header.twig:27
msgid ""
"May be approximate. Click on the number to get the exact count. See "
"[doc@faq3-11]FAQ 3.11[/doc]."
msgstr ""

#: templates/database/structure/table_header.twig:36
#: templates/table/index_form.twig:144
msgid "Size"
msgstr "Ömfang"

#: templates/database/structure/table_header.twig:51
#: templates/table/structure/display_table_stats.twig:154
msgid "Creation"
msgstr ""

#: templates/database/structure/table_header.twig:56
#: templates/table/structure/display_table_stats.twig:161
msgid "Last update"
msgstr ""

#: templates/database/structure/table_header.twig:61
#: templates/table/structure/display_table_stats.twig:168
msgid "Last check"
msgstr ""

#: templates/database/structure/tracking_icon.twig:3
msgid "Tracking is active."
msgstr ""

#: templates/database/structure/tracking_icon.twig:5
msgid "Tracking is not active."
msgstr ""

#: templates/database/tracking/tables.twig:4
msgid "Tracked tables"
msgstr ""

#: templates/database/tracking/tables.twig:14
msgid "Last version"
msgstr ""

#: templates/database/tracking/tables.twig:15
#: templates/table/tracking/main.twig:29
msgid "Created"
msgstr "Aanjelaat"

#: templates/database/tracking/tables.twig:16
#: templates/table/tracking/main.twig:30
msgid "Updated"
msgstr ""

#: templates/database/tracking/tables.twig:61
#: templates/table/tracking/main.twig:10 templates/table/tracking/main.twig:53
msgid "active"
msgstr "aktief"

#: templates/database/tracking/tables.twig:73
#: templates/table/tracking/main.twig:12 templates/table/tracking/main.twig:56
msgid "not active"
msgstr "nit aktief"

#: templates/database/tracking/tables.twig:93
#: templates/database/tracking/tables.twig:138
#: templates/database/tracking/tables.twig:139
msgid "Delete tracking"
msgstr ""

#: templates/database/tracking/tables.twig:104
msgid "Versions"
msgstr "Väsjohne"

#: templates/database/tracking/tables.twig:145
msgid "Untracked tables"
msgstr ""

#: templates/database/tracking/tables.twig:176
#: templates/database/tracking/tables.twig:188
#: templates/database/tracking/tables.twig:189
#: templates/table/structure/display_structure.twig:413
msgid "Track table"
msgstr ""

#: templates/database/triggers/editor_form.twig:23
msgid "Trigger name"
msgstr ""

#: templates/database/triggers/editor_form.twig:37
msgctxt "Trigger action time"
msgid "Time"
msgstr ""

#: templates/database/triggers/list.twig:27
#, fuzzy
#| msgid "Created"
msgid "Create new trigger"
msgstr "Aanjelaat"

#: templates/database/triggers/list.twig:36
msgid "There are no triggers to display."
msgstr ""

#: templates/display/results/table.twig:32
msgid "Save edited data"
msgstr ""

#: templates/display/results/table.twig:38
msgid "Restore column order"
msgstr ""

#: templates/display/results/table.twig:49 templates/export.twig:149
#: templates/table/start_and_number_of_rows_fieldset.twig:9
msgid "Number of rows:"
msgstr ""

#: templates/display/results/table.twig:52
msgid "All"
msgstr ""

#: templates/display/results/table.twig:70
msgid "Sort by key:"
msgstr ""

#: templates/display/results/table.twig:119
#: templates/table/search/index.twig:102
msgid "Extra options"
msgstr ""

#: templates/display/results/table.twig:141
msgid "Relational key"
msgstr ""

#: templates/display/results/table.twig:145
#, fuzzy
#| msgid "Select All"
msgid "Display column for relationships"
msgstr "Alle ußwähle"

#: templates/display/results/table.twig:153
msgid "Show binary contents"
msgstr ""

#: templates/display/results/table.twig:157
msgid "Show BLOB contents"
msgstr ""

#: templates/display/results/table.twig:167
msgid "Hide browser transformation"
msgstr ""

#: templates/display/results/table.twig:179
msgid "Well Known Text"
msgstr ""

#: templates/display/results/table.twig:183
msgid "Well Known Binary"
msgstr ""

#: templates/display/results/table.twig:255
#: templates/sql/no_results_returned.twig:9
msgid "Query results operations"
msgstr ""

#: templates/display/results/table.twig:260
msgid "Copy to clipboard"
msgstr ""

#: templates/display/results/table.twig:279
#: templates/table/chart/tbl_chart.twig:2
msgid "Display chart"
msgstr ""

#: templates/display/results/table.twig:287
msgid "Visualize GIS data"
msgstr ""

#: templates/encoding/kanji_encoding_form.twig:6
msgctxt "None encoding conversion"
msgid "None"
msgstr ""

#: templates/encoding/kanji_encoding_form.twig:17
msgid "Convert to Kana"
msgstr ""

#: templates/error/report_form.twig:3
msgid ""
"This report automatically includes data about the error and information "
"about relevant configuration settings. It will be sent to the phpMyAdmin "
"team for debugging the error."
msgstr ""

#: templates/error/report_form.twig:11
msgid ""
"Can you tell us the steps leading to this error? It decisively helps in "
"debugging:"
msgstr ""

#: templates/error/report_form.twig:18
msgid "You may examine the data in the error report:"
msgstr ""

#: templates/error/report_modal.twig:5
msgid "Submit error report"
msgstr ""

#: templates/error/report_modal.twig:12
msgid "Send error report"
msgstr ""

#: templates/export/template_options.twig:1 templates/export.twig:42
#, fuzzy
#| msgid "Select All"
msgid "Select a template"
msgstr "Alle ußwähle"

#: templates/export.twig:14
msgid "Export templates:"
msgstr ""

#: templates/export.twig:18
msgid "New template:"
msgstr ""

#: templates/export.twig:21 templates/export.twig:24
#, fuzzy
#| msgid "The database name is empty!"
msgid "Template name"
msgstr "Kein Name för di Dahtebangk!"

#: templates/export.twig:35
msgid "Existing templates:"
msgstr ""

#: templates/export.twig:38
msgid "Template:"
msgstr ""

#: templates/export.twig:51
msgid "Update"
msgstr ""

#: templates/export.twig:72
msgid "Show SQL query"
msgstr ""

#: templates/export.twig:104
msgid "Export method:"
msgstr ""

#: templates/export.twig:108
msgid "Quick - display only the minimal options"
msgstr ""

#: templates/export.twig:112
msgid "Custom - display all possible options"
msgstr ""

#: templates/export.twig:121
msgid "File format to export"
msgstr ""

#: templates/export.twig:137
msgid "Rows:"
msgstr ""

#: templates/export.twig:141
msgid "Dump all rows"
msgstr ""

#: templates/export.twig:145
msgid "Dump some row(s)"
msgstr ""

#: templates/export.twig:160
msgid "Row to begin at:"
msgstr ""

#: templates/export.twig:170 templates/export.twig:332
msgid "Output:"
msgstr ""

#: templates/export.twig:175 templates/export.twig:370
#, php-format
msgid "Save on server in the directory <strong>%s</strong>"
msgstr ""

#: templates/export.twig:193 templates/export.twig:337
msgid "Rename exported databases/tables/columns"
msgstr ""

#: templates/export.twig:201
msgid "Defined aliases"
msgstr ""

#: templates/export.twig:216 templates/export.twig:230
#: templates/export.twig:243 templates/export.twig:260
msgid "Remove"
msgstr ""

#: templates/export.twig:269
msgid "Define new aliases"
msgstr ""

#: templates/export.twig:274
#, fuzzy
#| msgid "Select All"
msgid "Select database:"
msgstr "Alle ußwähle"

#: templates/export.twig:290
#, fuzzy
#| msgid "Select All"
msgid "Select table:"
msgstr "Alle ußwähle"

#: templates/export.twig:298
msgid "New table name"
msgstr ""

#: templates/export.twig:306
#, fuzzy
#| msgid "Select All"
msgid "Select column:"
msgstr "Alle ußwähle"

#: templates/export.twig:314
msgid "New column name"
msgstr ""

#: templates/export.twig:347
#, php-format
msgid "Use %s statement"
msgstr ""

#: templates/export.twig:356
msgid "View output as text"
msgstr ""

#: templates/export.twig:360
msgid "Save output to a file"
msgstr ""

#: templates/export.twig:387
msgid "File name template:"
msgstr ""

#: templates/export.twig:388
#, php-format
msgid ""
"This value is interpreted using the 'strftime' function, so you can use time "
"formatting strings. Additionally the following transformations will happen: "
"%s Other text will be kept as is. See the FAQ 6.27 for details."
msgstr ""

#: templates/export.twig:398
msgid "Use this for future exports"
msgstr ""

#: templates/export.twig:409 templates/import.twig:103
msgid "Character set of the file:"
msgstr ""

#: templates/export.twig:429
msgid "Compression:"
msgstr ""

#: templates/export.twig:437
msgid "zipped"
msgstr ""

#: templates/export.twig:443
msgid "gzipped"
msgstr ""

#: templates/export.twig:461
msgid "Export databases as separate files"
msgstr ""

#: templates/export.twig:463
msgid "Export tables as separate files"
msgstr ""

#: templates/export.twig:474
msgid "Skip tables larger than:"
msgstr ""

#: templates/export.twig:476
msgid "The size is measured in MiB."
msgstr ""

#: templates/export.twig:482 templates/import.twig:182
msgid "Format-specific options:"
msgstr ""

#: templates/export.twig:491 templates/import.twig:191
msgid "Encoding Conversion:"
msgstr ""

#: templates/filter.twig:2 templates/server/status/processes/index.twig:6
#: templates/server/status/variables/index.twig:7
msgid "Filters"
msgstr ""

#: templates/filter.twig:4 templates/server/status/variables/index.twig:12
msgid "Containing the word:"
msgstr ""

#: templates/footer.twig:7 templates/footer.twig:9 templates/footer.twig:11
msgid "Open new phpMyAdmin window"
msgstr ""

#: templates/footer.twig:26 templates/home/<USER>
#: templates/login/form.twig:5
msgid "phpMyAdmin Demo Server"
msgstr ""

#: templates/footer.twig:34
#, php-format
msgid "Currently running Git revision %1$s from the %2$s branch."
msgstr ""

#: templates/footer.twig:36
msgid "Git information missing!"
msgstr ""

#: templates/gis_data_editor_form.twig:4
#, php-format
msgid "Value for the column \"%s\""
msgstr ""

#: templates/gis_data_editor_form.twig:22
#: templates/table/gis_visualization/gis_visualization.twig:34
msgid "Use OpenStreetMaps as Base Layer"
msgstr ""

#: templates/gis_data_editor_form.twig:38
msgctxt "Spatial Reference System Identifier"
msgid "SRID:"
msgstr ""

#: templates/gis_data_editor_form.twig:53
#, php-format
msgid "Geometry %d:"
msgstr ""

#: templates/gis_data_editor_form.twig:73
msgid "Point:"
msgstr "Pungk:"

#: templates/gis_data_editor_form.twig:93
#: templates/gis_data_editor_form.twig:134
#: templates/gis_data_editor_form.twig:186
#, fuzzy, php-format
#| msgid "Point %d"
msgid "Point %d:"
msgstr "Pungk %d"

#: templates/gis_data_editor_form.twig:113
#, php-format
msgid "Linestring %d:"
msgstr ""

#: templates/gis_data_editor_form.twig:115
#: templates/gis_data_editor_form.twig:170
msgid "Outer ring:"
msgstr ""

#: templates/gis_data_editor_form.twig:117
#: templates/gis_data_editor_form.twig:172
#, php-format
msgid "Inner ring %d:"
msgstr ""

#: templates/gis_data_editor_form.twig:144
msgid "Add a linestring"
msgstr ""

#: templates/gis_data_editor_form.twig:157
#, php-format
msgid "Polygon %d:"
msgstr ""

#: templates/gis_data_editor_form.twig:199
msgid "Add a polygon"
msgstr ""

#: templates/gis_data_editor_form.twig:205
msgid "Add geometry"
msgstr ""

#: templates/gis_data_editor_form.twig:214
msgid "Output"
msgstr ""

#: templates/gis_data_editor_form.twig:216
msgid ""
"Choose \"GeomFromText\" from the \"Function\" column and paste the string "
"below into the \"Value\" field."
msgstr ""

#: templates/header.twig:35 templates/login/header.twig:13
msgid "Javascript must be enabled past this point!"
msgstr ""

#: templates/header.twig:46
msgid "Click on the bar to scroll to top of page"
msgstr ""

#: templates/home/<USER>
msgid "Git revision:"
msgstr ""

#: templates/home/<USER>
msgid "no branch"
msgstr ""

#: templates/home/<USER>/home/<USER>
#, php-format
msgid "from %s branch"
msgstr ""

#: templates/home/<USER>
#, php-format
msgid "committed on %s by %s"
msgstr ""

#: templates/home/<USER>
#, php-format
msgid "authored on %s by %s"
msgstr ""

#: templates/home/<USER>/login/form.twig:8
#, php-format
msgid ""
"You are using the demo server. You can do anything here, but please do not "
"change root, debian-sys-maint and pma users. More information is available "
"at %s."
msgstr ""

#: templates/home/<USER>
msgid "General settings"
msgstr ""

#: templates/home/<USER>
msgid "Server connection collation:"
msgstr ""

#: templates/home/<USER>/preferences/manage/main.twig:56
msgid "More settings"
msgstr ""

#: templates/home/<USER>
msgid "Appearance settings"
msgstr ""

#: templates/home/<USER>/home/<USER>
#: templates/login/form.twig:23 templates/login/form.twig:24
#: templates/setup/home/<USER>/setup/home/<USER>
msgid "Language"
msgstr ""

#: templates/home/<USER>
msgid "Theme"
msgstr ""

#: templates/home/<USER>
msgctxt "View all themes"
msgid "View all"
msgstr ""

#: templates/home/<USER>
msgid "Database server"
msgstr ""

#: templates/home/<USER>/login/form.twig:65
#: templates/menu/breadcrumbs.twig:7
msgid "Server:"
msgstr ""

#: templates/home/<USER>
msgid "Server type:"
msgstr ""

#: templates/home/<USER>
msgid "Server connection:"
msgstr ""

#: templates/home/<USER>
msgid "Protocol version:"
msgstr ""

#: templates/home/<USER>
msgid "User:"
msgstr ""

#: templates/home/<USER>
msgid "Server charset:"
msgstr ""

#: templates/home/<USER>
msgid "Web server"
msgstr ""

#: templates/home/<USER>
msgid "Database client version:"
msgstr ""

#: templates/home/<USER>
msgid "PHP extension:"
msgstr ""

#: templates/home/<USER>
#, fuzzy
#| msgid "Versions"
msgid "PHP version:"
msgstr "Väsjohne"

#: templates/home/<USER>
msgid "Show PHP information"
msgstr ""

#: templates/home/<USER>
msgid "Version information:"
msgstr ""

#: templates/home/<USER>
msgid "Official Homepage"
msgstr ""

#: templates/home/<USER>
msgid "Contribute"
msgstr ""

#: templates/home/<USER>
msgid "Get support"
msgstr ""

#: templates/home/<USER>
msgid "List of changes"
msgstr ""

#: templates/home/<USER>/server/plugins/index.twig:30
msgid "License"
msgstr ""

#: templates/home/<USER>/setup/error.twig:2
msgid "Warning"
msgstr ""

#: templates/home/<USER>
msgid "Notice"
msgstr ""

#: templates/home/<USER>
msgid "phpMyAdmin Themes"
msgstr ""

#: templates/home/<USER>
msgid "Get more themes!"
msgstr ""

#: templates/home/<USER>
#, fuzzy, php-format
#| msgid "Select All"
msgid "Screenshot of the %s theme."
msgstr "Alle ußwähle"

#. l10n: Choose the theme button in the themes list modal
#: templates/home/<USER>
msgid "Take it"
msgstr ""

#: templates/import/javascript.twig:12
msgid ""
"The file being uploaded is probably larger than the maximum allowed size or "
"this is a known bug in webkit based (Safari, Google Chrome, Arora etc.) "
"browsers."
msgstr ""

#: templates/import/javascript.twig:13
#, php-format
msgid "%s of %s"
msgstr ""

#: templates/import/javascript.twig:14
#, php-format
msgid "%s/sec."
msgstr ""

#: templates/import/javascript.twig:15
msgid "About %MIN min. %SEC sec. remaining."
msgstr ""

#: templates/import/javascript.twig:16
msgid "About %SEC sec. remaining."
msgstr ""

#: templates/import/javascript.twig:17
msgid "The file is being processed, please be patient."
msgstr ""

#: templates/import/javascript.twig:29
msgid "Uploading your import file…"
msgstr ""

#: templates/import/javascript.twig:152
msgid ""
"Please be patient, the file is being uploaded. Details about the upload are "
"not available."
msgstr ""

#: templates/import.twig:26
msgid "File to import:"
msgstr ""

#: templates/import.twig:31
#, php-format
msgid "File may be compressed (%s) or uncompressed."
msgstr ""

#: templates/import.twig:32
msgid ""
"A compressed file's name must end in <strong>.[format].[compression]</"
"strong>. Example: <strong>.sql.zip</strong>"
msgstr ""

#: templates/import.twig:40
msgid "Upload a file"
msgstr ""

#: templates/import.twig:43
#, fuzzy
#| msgid "Select All"
msgid "Select file to import"
msgstr "Alle ußwähle"

#: templates/import.twig:50 templates/import.twig:77
#: templates/preferences/manage/main.twig:22
msgid "Browse your computer:"
msgstr ""

#: templates/import.twig:55 templates/import.twig:82
msgid "You may also drag and drop a file on any page."
msgstr ""

#: templates/import.twig:62 templates/import.twig:88
#, php-format
msgid "Select from the web server upload directory [strong]%s[/strong]:"
msgstr ""

#: templates/import.twig:69 templates/import.twig:96
#, fuzzy
#| msgid "Select All"
msgid "There are no files to import!"
msgstr "Alle ußwähle"

#: templates/import.twig:100
msgid "File uploads are not allowed on this server."
msgstr ""

#: templates/import.twig:126
msgid "Partial import:"
msgstr ""

#: templates/import.twig:131
#, php-format
msgid ""
"Previous import timed out, after resubmitting will continue from position %d."
msgstr ""

#: templates/import.twig:138
msgid ""
"Allow the interruption of an import in case the script detects it is close "
"to the PHP timeout limit."
msgstr ""

#: templates/import.twig:140
msgid ""
"This might be a good way to import large files, however it can break "
"transactions."
msgstr ""

#: templates/import.twig:144
msgid "Skip this number of queries (for SQL) starting from the first one:"
msgstr ""

#: templates/import.twig:154
msgid "Other options"
msgstr ""

#: templates/indexes.twig:39
#: templates/table/structure/display_structure.twig:506
msgid "Rename"
msgstr ""

#: templates/indexes.twig:45
#: templates/table/structure/display_structure.twig:512
msgid "The primary key has been dropped."
msgstr ""

#: templates/indexes.twig:50
#: templates/table/structure/display_structure.twig:517
#, php-format
msgid "Index %s has been dropped."
msgstr ""

#. l10n: Month-year order for calendar, use either "calendar-month-year" or "calendar-year-month".
#: templates/javascript/variables.twig:8
msgid "calendar-month-year"
msgstr "calendar-month-year"

#. l10n: Year suffix for calendar, "none" is empty.
#: templates/javascript/variables.twig:11
msgid "none"
msgstr ""

#. l10n: Previous month. Display text for previous month link in calendar
#: templates/javascript/variables.twig:16
msgid "Prev"
msgstr ""

#. l10n: Next month. Display text for next month link in calendar
#: templates/javascript/variables.twig:17
msgid "Next"
msgstr ""

#. l10n: Display text for current month link in calendar
#: templates/javascript/variables.twig:18
msgid "Today"
msgstr ""

#: templates/javascript/variables.twig:20
msgid "January"
msgstr ""

#: templates/javascript/variables.twig:21
msgid "February"
msgstr ""

#: templates/javascript/variables.twig:22
msgid "March"
msgstr ""

#: templates/javascript/variables.twig:23
msgid "April"
msgstr ""

#. l10n: Short month name for May
#: templates/javascript/variables.twig:24
#: templates/javascript/variables.twig:38
msgid "May"
msgstr ""

#: templates/javascript/variables.twig:25
msgid "June"
msgstr ""

#: templates/javascript/variables.twig:26
msgid "July"
msgstr ""

#: templates/javascript/variables.twig:27
msgid "August"
msgstr ""

#: templates/javascript/variables.twig:28
msgid "September"
msgstr ""

#: templates/javascript/variables.twig:29
msgid "October"
msgstr ""

#: templates/javascript/variables.twig:30
msgid "November"
msgstr ""

#: templates/javascript/variables.twig:31
msgid "December"
msgstr ""

#. l10n: Short week day name for Sunday
#: templates/javascript/variables.twig:57
msgid "Sun"
msgstr ""

#. l10n: Minimal week day name for Sunday
#: templates/javascript/variables.twig:66
msgid "Su"
msgstr ""

#. l10n: Minimal week day name for Monday
#: templates/javascript/variables.twig:67
msgid "Mo"
msgstr ""

#. l10n: Minimal week day name for Tuesday
#: templates/javascript/variables.twig:68
msgid "Tu"
msgstr ""

#. l10n: Minimal week day name for Wednesday
#: templates/javascript/variables.twig:69
msgid "We"
msgstr ""

#. l10n: Minimal week day name for Thursday
#: templates/javascript/variables.twig:70
msgid "Th"
msgstr ""

#. l10n: Minimal week day name for Friday
#: templates/javascript/variables.twig:71
msgid "Fr"
msgstr ""

#. l10n: Minimal week day name for Saturday
#: templates/javascript/variables.twig:72
msgid "Sa"
msgstr ""

#. l10n: Column header for week of the year in calendar
#: templates/javascript/variables.twig:74
msgid "Wk"
msgstr ""

#: templates/javascript/variables.twig:82
msgid "Hour"
msgstr ""

#: templates/javascript/variables.twig:83
msgid "Minute"
msgstr ""

#: templates/javascript/variables.twig:84
msgid "Second"
msgstr ""

#: templates/javascript/variables.twig:90
msgid "This field is required"
msgstr ""

#: templates/javascript/variables.twig:91
msgid "Please fix this field"
msgstr ""

#: templates/javascript/variables.twig:92
msgid "Please enter a valid email address"
msgstr ""

#: templates/javascript/variables.twig:93
msgid "Please enter a valid URL"
msgstr ""

#: templates/javascript/variables.twig:94
#, fuzzy
#| msgid "Select All"
msgid "Please enter a valid date"
msgstr "Alle ußwähle"

#: templates/javascript/variables.twig:95
msgid "Please enter a valid date ( ISO )"
msgstr ""

#: templates/javascript/variables.twig:96
msgid "Please enter a valid number"
msgstr ""

#: templates/javascript/variables.twig:97
msgid "Please enter a valid credit card number"
msgstr ""

#: templates/javascript/variables.twig:98
msgid "Please enter only digits"
msgstr ""

#: templates/javascript/variables.twig:99
msgid "Please enter the same value again"
msgstr ""

#: templates/javascript/variables.twig:100
msgid "Please enter no more than {0} characters"
msgstr ""

#: templates/javascript/variables.twig:101
msgid "Please enter at least {0} characters"
msgstr ""

#: templates/javascript/variables.twig:102
msgid "Please enter a value between {0} and {1} characters long"
msgstr ""

#: templates/javascript/variables.twig:103
msgid "Please enter a value between {0} and {1}"
msgstr ""

#: templates/javascript/variables.twig:104
msgid "Please enter a value less than or equal to {0}"
msgstr ""

#: templates/javascript/variables.twig:105
msgid "Please enter a value greater than or equal to {0}"
msgstr ""

#: templates/javascript/variables.twig:106
msgid "Please enter a valid date or time"
msgstr ""

#: templates/javascript/variables.twig:107
msgid "Please enter a valid HEX input"
msgstr ""

#. l10n: To validate the usage of a MD5 function on the column
#: templates/javascript/variables.twig:108
msgid "This column can not contain a 32 chars value"
msgstr ""

#. l10n: To validate the usage of a AES_ENCRYPT/DES_ENCRYPT function on the column
#: templates/javascript/variables.twig:109
msgid ""
"These functions are meant to return a binary result; to avoid inconsistent "
"results you should store it in a BINARY, VARBINARY, or BLOB column."
msgstr ""

#: templates/login/form.twig:58 templates/login/form.twig:117
#: templates/login/form.twig:121 templates/login/form.twig:124
msgid "Log in"
msgstr ""

#: templates/login/form.twig:64 templates/login/form.twig:69
msgid "You can enter hostname/IP address and port separated by space."
msgstr ""

#: templates/login/form.twig:76
msgid "Username:"
msgstr ""

#: templates/login/form.twig:85
#: templates/server/privileges/change_password.twig:21
#: templates/server/privileges/login_information_fields.twig:43
#: templates/server/replication/change_primary.twig:18
#: templates/server/replication/primary_add_replica_user.twig:49
msgid "Password:"
msgstr ""

#: templates/login/form.twig:95
msgid "Server choice:"
msgstr ""

#: templates/login/header.twig:17
msgid ""
"There is a mismatch between HTTPS indicated on the server and client. This "
"can lead to a non working phpMyAdmin or a security risk. Please fix your "
"server configuration to indicate HTTPS properly."
msgstr ""

#: templates/login/twofactor/application_configure.twig:3
msgid ""
"Please scan following QR code into the two-factor authentication app on your "
"device and enter authentication code it generates."
msgstr ""

#: templates/login/twofactor/application_configure.twig:13
msgid "Secret/key:"
msgstr ""

#: templates/login/twofactor/application_configure.twig:16
#: templates/login/twofactor/application.twig:2
msgid "Authentication code:"
msgstr ""

#: templates/login/twofactor/application.twig:5
msgid ""
"Open the two-factor authentication app on your device to view your "
"authentication code and verify your identity."
msgstr ""

#: templates/login/twofactor/invalid.twig:2
msgid ""
"The configured two factor authentication is not available, please install "
"missing dependencies."
msgstr ""

#: templates/login/twofactor/key_configure.twig:3
#: templates/login/twofactor/key.twig:3
#: templates/preferences/two_factor/main.twig:56
#, fuzzy
#| msgid "Created"
msgid "Deprecated!"
msgstr "Aanjelaat"

#: templates/login/twofactor/key_configure.twig:4
#: templates/login/twofactor/key.twig:4
#: templates/preferences/two_factor/main.twig:57
#: templates/preferences/two_factor/main.twig:90
msgid ""
"The FIDO U2F API has been deprecated in favor of the Web Authentication API "
"(WebAuthn)."
msgstr ""

#: templates/login/twofactor/key_configure.twig:6
#: templates/login/twofactor/key.twig:6
#: templates/preferences/two_factor/main.twig:59
msgid ""
"You can still use Firefox to authenticate your account using the FIDO U2F "
"API, however it's recommended that you use the WebAuthn authentication "
"instead."
msgstr ""

#: templates/login/twofactor/key_configure.twig:10
msgid ""
"Please connect your FIDO U2F device into your computer's USB port. Then "
"confirm registration on the device."
msgstr ""

#: templates/login/twofactor/key-https-warning.twig:3
msgid ""
"You are not using https to access phpMyAdmin, therefore FIDO U2F device will "
"most likely refuse to authenticate you."
msgstr ""

#: templates/login/twofactor/key.twig:10
msgid ""
"Please connect your FIDO U2F device into your computer's USB port. Then "
"confirm login on the device."
msgstr ""

#: templates/login/twofactor.twig:10
msgid "Verify"
msgstr ""

#: templates/login/twofactor/webauthn_creation.twig:1
msgid ""
"Please connect your WebAuthn/FIDO2 device. Then confirm registration on the "
"device."
msgstr ""

#: templates/login/twofactor/webauthn_request.twig:1
msgid ""
"Please connect your WebAuthn/FIDO2 device. Then confirm login on the device."
msgstr ""

#: templates/menu/breadcrumbs.twig:27
msgid "View:"
msgstr ""

#: templates/modals/index_dialog_modal.twig:30
#: templates/table/structure/display_structure.twig:363
msgid "Go back"
msgstr ""

#: templates/modals/unhide_nav_item.twig:5
msgid "Show hidden navigation tree items."
msgstr ""

#: templates/navigation/item_unhide_dialog.twig:20
msgid "Unhide"
msgstr ""

#: templates/navigation/main.twig:25 templates/navigation/main.twig:26
msgid "Home"
msgstr ""

#: templates/navigation/main.twig:30 templates/navigation/main.twig:31
msgid "Empty session data"
msgstr ""

#: templates/navigation/main.twig:30 templates/navigation/main.twig:31
msgid "Log out"
msgstr ""

#: templates/navigation/main.twig:35 templates/navigation/main.twig:36
msgid "phpMyAdmin documentation"
msgstr ""

#: templates/navigation/main.twig:39 templates/navigation/main.twig:40
msgid "MariaDB Documentation"
msgstr ""

#: templates/navigation/main.twig:39 templates/navigation/main.twig:40
msgid "MySQL Documentation"
msgstr ""

#: templates/navigation/main.twig:43 templates/navigation/main.twig:44
msgid "Navigation panel settings"
msgstr ""

#: templates/navigation/main.twig:47 templates/navigation/main.twig:48
msgid "Reload navigation panel"
msgstr ""

#: templates/navigation/main.twig:67
msgid "An error has occurred while loading the navigation display"
msgstr ""

#: templates/navigation/main.twig:88
msgid "SQL upload"
msgstr ""

#: templates/navigation/tree/fast_filter.twig:11
#: templates/navigation/tree/fast_filter.twig:12
msgid "Type to filter these, Enter to search all"
msgstr ""

#: templates/navigation/tree/fast_filter.twig:16
msgid "Clear fast filter"
msgstr ""

#: templates/preferences/autoload.twig:7
msgid ""
"Your browser has phpMyAdmin configuration for this domain. Would you like to "
"import it for current session?"
msgstr ""

#: templates/preferences/autoload.twig:13
msgid "Delete settings"
msgstr ""

#: templates/preferences/forms/main.twig:4
msgid "Cannot save settings, submitted form contains errors!"
msgstr ""

#: templates/preferences/header.twig:6
msgid "Manage your settings"
msgstr ""

#: templates/preferences/header.twig:12
msgid "Two-factor authentication"
msgstr ""

#: templates/preferences/header.twig:55
#: templates/preferences/manage/main.twig:72
msgid "Configuration has been saved."
msgstr ""

#: templates/preferences/header.twig:60
#, php-format
msgid ""
"Your preferences will be saved for current session only. Storing them "
"permanently requires %sphpMyAdmin configuration storage%s."
msgstr ""

#: templates/preferences/manage/error.twig:1
msgid "Configuration contains incorrect data for some fields."
msgstr ""

#: templates/preferences/manage/error.twig:16
msgid "Do you want to import remaining settings?"
msgstr ""

#: templates/preferences/manage/main.twig:3
#: templates/preferences/manage/main.twig:33
msgid "Saved on: @DATE@"
msgstr ""

#: templates/preferences/manage/main.twig:20
msgid "Import from file"
msgstr ""

#: templates/preferences/manage/main.twig:27
msgid "Import from browser's storage"
msgstr ""

#: templates/preferences/manage/main.twig:30
msgid "Settings will be imported from your browser's local storage."
msgstr ""

#: templates/preferences/manage/main.twig:36
msgid "You have no saved settings!"
msgstr ""

#: templates/preferences/manage/main.twig:40
#: templates/preferences/manage/main.twig:104
msgid "This feature is not supported by your web browser"
msgstr ""

#: templates/preferences/manage/main.twig:44
msgid "Merge with current configuration"
msgstr ""

#: templates/preferences/manage/main.twig:59
#, php-format
msgid ""
"You can set more settings by modifying config.inc.php, eg. by using %sSetup "
"script%s."
msgstr ""

#: templates/preferences/manage/main.twig:81
msgid "Save as JSON file"
msgstr ""

#: templates/preferences/manage/main.twig:85
msgid "Save as PHP file"
msgstr ""

#: templates/preferences/manage/main.twig:90
msgid "Save to browser's storage"
msgstr ""

#: templates/preferences/manage/main.twig:96
msgid "Settings will be saved in your browser's local storage."
msgstr ""

#: templates/preferences/manage/main.twig:99
msgid "Existing settings will be overwritten!"
msgstr ""

#: templates/preferences/manage/main.twig:120
msgid "You can reset all your settings and restore them to default values."
msgstr ""

#: templates/preferences/two_factor/configure.twig:5
#: templates/preferences/two_factor/main.twig:77
#: templates/preferences/two_factor/main.twig:98
msgid "Configure two-factor authentication"
msgstr ""

#: templates/preferences/two_factor/configure.twig:12
msgid "Enable two-factor authentication"
msgstr ""

#: templates/preferences/two_factor/confirm.twig:5
msgid "Confirm disabling two-factor authentication"
msgstr ""

#: templates/preferences/two_factor/confirm.twig:9
msgid ""
"By disabling two factor authentication you will be again able to login using "
"password only."
msgstr ""

#: templates/preferences/two_factor/confirm.twig:13
#: templates/preferences/two_factor/main.twig:66
msgid "Disable two-factor authentication"
msgstr ""

#: templates/preferences/two_factor/main.twig:5
msgid "Two-factor authentication status"
msgstr ""

#: templates/preferences/two_factor/main.twig:11
msgid ""
"Two-factor authentication is not available, please install optional "
"dependencies to enable authentication backends."
msgstr ""

#: templates/preferences/two_factor/main.twig:12
#: templates/preferences/two_factor/main.twig:27
msgid "Following composer packages are missing:"
msgstr ""

#: templates/preferences/two_factor/main.twig:20
msgid "Two-factor authentication is available and configured for this account."
msgstr ""

#: templates/preferences/two_factor/main.twig:22
msgid ""
"Two-factor authentication is available, but not configured for this account."
msgstr ""

#: templates/preferences/two_factor/main.twig:26
msgid ""
"Please install optional dependencies to enable more authentication backends."
msgstr ""

#: templates/preferences/two_factor/main.twig:37
msgid ""
"Two-factor authentication is not available, enable phpMyAdmin configuration "
"storage to use it."
msgstr ""

#: templates/preferences/two_factor/main.twig:52
msgid "You have enabled two factor authentication."
msgstr ""

#: templates/recent_favorite_table_no_tables.twig:3
msgid "There are no recent tables."
msgstr ""

#: templates/recent_favorite_table_no_tables.twig:5
msgid "There are no favorite tables."
msgstr ""

#: templates/relation/check_relations.twig:3
msgid "phpMyAdmin configuration storage"
msgstr ""

#: templates/relation/check_relations.twig:9
msgid "Configuration of pmadb…"
msgstr ""

#: templates/relation/check_relations.twig:10
#: templates/relation/check_relations.twig:56
#: templates/relation/check_relations.twig:81
#: templates/relation/check_relations.twig:106
#: templates/relation/check_relations.twig:119
#: templates/relation/check_relations.twig:144
#: templates/relation/check_relations.twig:189
#: templates/relation/check_relations.twig:214
#: templates/relation/check_relations.twig:239
#: templates/relation/check_relations.twig:264
#: templates/relation/check_relations.twig:289
#: templates/relation/check_relations.twig:314
#: templates/relation/check_relations.twig:339
#: templates/relation/check_relations.twig:364
#: templates/relation/check_relations.twig:377
#: templates/relation/check_relations.twig:402
#: templates/relation/check_relations.twig:427
#: templates/relation/check_relations.twig:452
#: templates/relation/check_relations.twig:477
#: templates/relation/check_relations.twig:502
msgid "not OK"
msgstr ""

#: templates/relation/check_relations.twig:14
msgid "General relation features"
msgstr ""

#: templates/relation/check_relations.twig:20
#, php-format
msgid ""
"%sCreate%s a database named '%s' and setup the phpMyAdmin configuration "
"storage there."
msgstr ""

#: templates/relation/check_relations.twig:24
#, php-format
msgid ""
"%sCreate%s the phpMyAdmin configuration storage in the current database."
msgstr ""

#: templates/relation/check_relations.twig:31
#, php-format
msgid "%sCreate%s missing phpMyAdmin configuration storage tables."
msgstr ""

#: templates/relation/check_relations.twig:42
#: templates/relation/check_relations.twig:54
#: templates/relation/check_relations.twig:79
#: templates/relation/check_relations.twig:104
#: templates/relation/check_relations.twig:117
#: templates/relation/check_relations.twig:142
#: templates/relation/check_relations.twig:187
#: templates/relation/check_relations.twig:212
#: templates/relation/check_relations.twig:237
#: templates/relation/check_relations.twig:262
#: templates/relation/check_relations.twig:287
#: templates/relation/check_relations.twig:312
#: templates/relation/check_relations.twig:337
#: templates/relation/check_relations.twig:362
#: templates/relation/check_relations.twig:375
#: templates/relation/check_relations.twig:400
#: templates/relation/check_relations.twig:425
#: templates/relation/check_relations.twig:450
#: templates/relation/check_relations.twig:475
#: templates/relation/check_relations.twig:500
msgctxt "Correctly working"
msgid "OK"
msgstr ""

#: templates/relation/check_relations.twig:62
msgid "General relation features:"
msgstr ""

#: templates/relation/check_relations.twig:64
#: templates/relation/check_relations.twig:89
#: templates/relation/check_relations.twig:127
#: templates/relation/check_relations.twig:152
#: templates/relation/check_relations.twig:162
#: templates/relation/check_relations.twig:197
#: templates/relation/check_relations.twig:222
#: templates/relation/check_relations.twig:247
#: templates/relation/check_relations.twig:272
#: templates/relation/check_relations.twig:297
#: templates/relation/check_relations.twig:322
#: templates/relation/check_relations.twig:347
#: templates/relation/check_relations.twig:385
#: templates/relation/check_relations.twig:410
#: templates/relation/check_relations.twig:435
#: templates/relation/check_relations.twig:460
#: templates/relation/check_relations.twig:485
#: templates/relation/check_relations.twig:510
msgid "Enabled"
msgstr ""

#: templates/relation/check_relations.twig:87
msgid "Display features:"
msgstr ""

#: templates/relation/check_relations.twig:125
msgid "Designer and creation of PDFs:"
msgstr ""

#: templates/relation/check_relations.twig:150
#, fuzzy
#| msgid "Select All"
msgid "Displaying column comments:"
msgstr "Alle ußwähle"

#: templates/relation/check_relations.twig:160
msgid "Browser transformation:"
msgstr ""

#: templates/relation/check_relations.twig:172
msgid "Please see the documentation on how to update your column_info table."
msgstr ""

#: templates/relation/check_relations.twig:195
msgid "Bookmarked SQL query:"
msgstr ""

#: templates/relation/check_relations.twig:220
msgid "SQL history:"
msgstr ""

#: templates/relation/check_relations.twig:245
msgid "Persistent recently used tables:"
msgstr ""

#: templates/relation/check_relations.twig:270
#, fuzzy
msgid "Persistent favorite tables:"
msgstr "Ein Tabäll"

#: templates/relation/check_relations.twig:295
msgid "Persistent tables' UI preferences:"
msgstr ""

#: templates/relation/check_relations.twig:320
msgid "Tracking:"
msgstr ""

#: templates/relation/check_relations.twig:345
msgid "User preferences:"
msgstr ""

#: templates/relation/check_relations.twig:383
msgid "Configurable menus:"
msgstr ""

#: templates/relation/check_relations.twig:408
msgid "Hide/show navigation items:"
msgstr ""

#: templates/relation/check_relations.twig:433
msgid "Saving Query-By-Example searches:"
msgstr ""

#: templates/relation/check_relations.twig:458
msgid "Managing central list of columns:"
msgstr ""

#: templates/relation/check_relations.twig:483
msgid "Remembering designer settings:"
msgstr ""

#: templates/relation/check_relations.twig:508
msgid "Saving export templates:"
msgstr ""

#: templates/relation/check_relations.twig:519
msgid "Quick steps to set up advanced features:"
msgstr ""

#: templates/relation/check_relations.twig:523
#, php-format
msgid "Create the needed tables with the <code>%screate_tables.sql</code>."
msgstr ""

#: templates/relation/check_relations.twig:527
msgid "Create a pma user and give access to these tables."
msgstr ""

#: templates/relation/check_relations.twig:531
msgid ""
"Enable advanced features in configuration file (<code>config.inc.php</"
"code>), for example by starting from <code>config.sample.inc.php</code>."
msgstr ""

#: templates/relation/check_relations.twig:535
msgid "Re-login to phpMyAdmin to load the updated configuration file."
msgstr ""

#: templates/server/binlog/index.twig:10
msgid "Select binary log to view"
msgstr ""

#: templates/server/binlog/index.twig:59 templates/server/binlog/index.twig:60
#: templates/server/status/processes/list.twig:20
msgid "Truncate shown queries"
msgstr ""

#: templates/server/binlog/index.twig:63 templates/server/binlog/index.twig:64
#: templates/server/status/processes/list.twig:26
msgid "Show full queries"
msgstr ""

#: templates/server/binlog/index.twig:84
msgid "Log name"
msgstr ""

#: templates/server/binlog/index.twig:85
msgid "Position"
msgstr ""

#: templates/server/binlog/index.twig:87
#: templates/server/replication/primary_replication.twig:24
msgid "Server ID"
msgstr ""

#: templates/server/binlog/index.twig:88
msgid "Original position"
msgstr ""

#: templates/server/binlog/index.twig:89
#: templates/table/structure/display_table_stats.twig:3
msgid "Information"
msgstr ""

#: templates/server/collations/index.twig:4
msgid "Character sets and collations"
msgstr ""

#: templates/server/collations/index.twig:23
#, fuzzy
#| msgid "Default"
msgctxt "The collation is the default one"
msgid "default"
msgstr "Schtandatt"

#: templates/server/databases/index.twig:3
msgid "Databases statistics"
msgstr ""

#: templates/server/databases/index.twig:9
msgid "Create database"
msgstr ""

#: templates/server/databases/index.twig:50
msgid "No privileges to create databases"
msgstr ""

#: templates/server/databases/index.twig:156
#: templates/server/replication/index.twig:18
#: templates/server/replication/primary_replication.twig:3
msgid "Primary replication"
msgstr ""

#: templates/server/databases/index.twig:160
#: templates/server/replication/replica_configuration.twig:2
msgid "Replica replication"
msgstr ""

#: templates/server/databases/index.twig:181
#, php-format
msgid "Jump to database '%s'"
msgstr ""

#: templates/server/databases/index.twig:242
#, php-format
msgid "Check privileges for database \"%s\"."
msgstr ""

#: templates/server/databases/index.twig:243
msgid "Check privileges"
msgstr ""

#: templates/server/databases/index.twig:298
msgid ""
"Note: Enabling the database statistics here might cause heavy traffic "
"between the web server and the MySQL server."
msgstr ""

#: templates/server/databases/index.twig:300
#: templates/server/databases/index.twig:301
#, fuzzy
#| msgid "Table"
msgid "Enable statistics"
msgstr "Tabäll"

#: templates/server/databases/index.twig:308
msgid "No databases"
msgstr ""

#: templates/server/engines/index.twig:5 templates/server/engines/show.twig:3
msgid "Storage engines"
msgstr ""

#: templates/server/engines/index.twig:13
msgid "Storage Engine"
msgstr ""

#: templates/server/engines/show.twig:45
msgid "Unknown storage engine."
msgstr ""

#: templates/server/export/index.twig:26
msgid "@SERVER@ will become the server name."
msgstr ""

#: templates/server/export/index.twig:3
msgid "Exporting databases from the current server"
msgstr ""

#: templates/server/import/index.twig:3
msgid "Importing into the current server"
msgstr ""

#: templates/server/plugins/index.twig:26
msgid "Plugin"
msgstr ""

#: templates/server/plugins/index.twig:28 templates/table/tracking/main.twig:28
msgid "Version"
msgstr ""

#: templates/server/plugins/index.twig:29
msgid "Author"
msgstr ""

#: templates/server/plugins/index.twig:41
#, fuzzy
#| msgid "active"
msgid "inactive"
msgstr "aktief"

#: templates/server/plugins/index.twig:43
msgid "disabled"
msgstr ""

#: templates/server/plugins/index.twig:45
msgid "deleting"
msgstr ""

#: templates/server/plugins/index.twig:47
msgid "deleted"
msgstr ""

#: templates/server/privileges/add_user.twig:12
msgid "Database for user account"
msgstr ""

#: templates/server/privileges/add_user.twig:15
msgid "Create database with same name and grant all privileges."
msgstr ""

#: templates/server/privileges/add_user.twig:19
msgid "Grant all privileges on wildcard name (username\\_%)."
msgstr ""

#: templates/server/privileges/add_user.twig:24
#, php-format
msgid "Grant all privileges on database %s."
msgstr ""

#: templates/server/privileges/change_password.twig:15
msgid "No Password"
msgstr ""

#: templates/server/privileges/change_password.twig:24
msgid "Enter:"
msgstr ""

#: templates/server/privileges/change_password.twig:28
#: templates/server/privileges/login_information_fields.twig:54
msgctxt "Password strength"
msgid "Strength:"
msgstr ""

#: templates/server/privileges/change_password.twig:32
#: templates/server/privileges/login_information_fields.twig:60
#: templates/server/replication/primary_add_replica_user.twig:62
msgid "Re-type:"
msgstr ""

#: templates/server/privileges/change_password.twig:40
msgid "Password Hashing:"
msgstr ""

#: templates/server/privileges/change_password.twig:58
msgid ""
"This method requires using an '<i>SSL connection</i>' or an '<i>unencrypted "
"connection that encrypts the password using RSA</i>'; while connecting to "
"the server."
msgstr ""

#: templates/server/privileges/choose_user_group.twig:5
msgid "User group:"
msgstr ""

#: templates/server/privileges/edit_routine_privileges.twig:4
#: templates/server/privileges/user_properties.twig:4
msgid "Edit privileges:"
msgstr ""

#: templates/server/privileges/edit_routine_privileges.twig:5
#: templates/server/privileges/user_properties.twig:5
msgid "User account"
msgstr ""

#: templates/server/privileges/edit_routine_privileges.twig:34
#: templates/server/privileges/user_properties.twig:53
msgid ""
"Note: You are attempting to edit privileges of the user with which you are "
"currently logged in."
msgstr ""

#: templates/server/privileges/edit_routine_privileges.twig:51
#: templates/server/privileges/privileges_table.twig:10
#: templates/server/privileges/privileges_table.twig:272
msgid "Note: MySQL privilege names are expressed in English."
msgstr ""

#: templates/server/privileges/edit_routine_privileges.twig:63
#: templates/server/privileges/edit_routine_privileges.twig:67
msgid ""
"Allows user to give to other users or remove from other users privileges "
"that user possess on this routine."
msgstr ""

#: templates/server/privileges/edit_routine_privileges.twig:76
#: templates/server/privileges/edit_routine_privileges.twig:79
msgid "Allows altering and dropping this routine."
msgstr ""

#: templates/server/privileges/edit_routine_privileges.twig:88
#: templates/server/privileges/edit_routine_privileges.twig:91
msgid "Allows executing this routine."
msgstr ""

#: templates/server/privileges/initials_row.twig:1
msgid "Pagination of user accounts"
msgstr ""

#: templates/server/privileges/login_information_fields.twig:2
#: templates/server/privileges/user_properties.twig:106
msgid "Login Information"
msgstr ""

#: templates/server/privileges/login_information_fields.twig:4
#: templates/server/replication/change_primary.twig:14
#: templates/server/replication/primary_add_replica_user.twig:16
msgid "User name:"
msgstr ""

#: templates/server/privileges/login_information_fields.twig:7
#: templates/server/replication/primary_add_replica_user.twig:20
msgid "Any user"
msgstr ""

#: templates/server/privileges/login_information_fields.twig:8
#: templates/server/privileges/login_information_fields.twig:33
#: templates/server/privileges/login_information_fields.twig:50
#: templates/server/privileges/privileges_summary.twig:75
#: templates/server/privileges/privileges_summary.twig:90
msgid "Use text field"
msgstr ""

#: templates/server/privileges/login_information_fields.twig:16
msgid ""
"An account already exists with the same username but possibly a different "
"hostname."
msgstr ""

#: templates/server/privileges/login_information_fields.twig:22
msgid "Host name:"
msgstr ""

#: templates/server/privileges/login_information_fields.twig:27
#: templates/server/replication/primary_add_replica_user.twig:34
msgid "Any host"
msgstr ""

#: templates/server/privileges/login_information_fields.twig:28
#: templates/server/replication/primary_add_replica_user.twig:35
msgid "Local"
msgstr ""

#: templates/server/privileges/login_information_fields.twig:30
#: templates/server/replication/primary_add_replica_user.twig:37
msgid "This host"
msgstr ""

#: templates/server/privileges/login_information_fields.twig:32
#: templates/server/replication/primary_add_replica_user.twig:39
#, fuzzy
#| msgid "Select All"
msgid "Use host table"
msgstr "Alle ußwähle"

#: templates/server/privileges/login_information_fields.twig:39
#: templates/server/replication/primary_add_replica_user.twig:44
msgid ""
"When Host table is used, this field is ignored and values stored in Host "
"table are used instead."
msgstr ""

#: templates/server/privileges/login_information_fields.twig:45
#: templates/server/privileges/login_information_fields.twig:53
#: templates/server/privileges/users_overview.twig:10
#: templates/server/replication/change_primary.twig:19
#: templates/server/replication/primary_add_replica_user.twig:52
#: templates/server/replication/primary_add_replica_user.twig:57
msgid "Password"
msgstr ""

#: templates/server/privileges/login_information_fields.twig:47
msgid "Do not change the password"
msgstr ""

#: templates/server/privileges/login_information_fields.twig:49
#: templates/server/replication/primary_add_replica_user.twig:53
msgid "No password"
msgstr ""

#: templates/server/privileges/login_information_fields.twig:62
#: templates/server/replication/primary_add_replica_user.twig:65
msgid "Re-type"
msgstr ""

#: templates/server/privileges/login_information_fields.twig:68
msgid "Authentication plugin"
msgstr ""

#: templates/server/privileges/login_information_fields.twig:70
msgid "Password hashing method"
msgstr ""

#: templates/server/privileges/login_information_fields.twig:83
msgid ""
"This method requires using an '<em>SSL connection</em>' or an "
"'<em>unencrypted connection that encrypts the password using RSA</em>'; "
"while connecting to the server."
msgstr ""

#: templates/server/privileges/new_user_ajax.twig:52
#: templates/server/privileges/users_overview.twig:83
#: templates/server/privileges/users_overview.twig:168
msgid "Edit user group"
msgstr ""

#: templates/server/privileges/privileges_summary.twig:20
msgid "Column-specific privileges"
msgstr ""

#: templates/server/privileges/privileges_summary.twig:54
msgid "Add privileges on the following database(s):"
msgstr ""

#: templates/server/privileges/privileges_summary.twig:67
msgid "Wildcards % and _ should be escaped with a \\ to use them literally."
msgstr ""

#: templates/server/privileges/privileges_summary.twig:71
msgid "Add privileges on the following table:"
msgstr ""

#: templates/server/privileges/privileges_summary.twig:86
msgid "Add privileges on the following routine:"
msgstr ""

#: templates/server/privileges/privileges_table.twig:35
#: templates/server/privileges/privileges_table.twig:36
#: templates/server/privileges/privileges_table.twig:62
#: templates/server/privileges/privileges_table.twig:63
#: templates/server/privileges/privileges_table.twig:89
#: templates/server/privileges/privileges_table.twig:90
#: templates/server/privileges/privileges_table.twig:116
#: templates/server/privileges/privileges_table.twig:117
msgctxt "None privileges"
msgid "None"
msgstr ""

#: templates/server/privileges/privileges_table.twig:160
#: templates/server/privileges/privileges_table.twig:164
#: templates/server/privileges/privileges_table.twig:638
#: templates/server/privileges/privileges_table.twig:642
msgid ""
"Allows user to give to other users or remove from other users the privileges "
"that user possess yourself."
msgstr ""

#: templates/server/privileges/privileges_table.twig:255
msgid "Global"
msgstr ""

#: templates/server/privileges/privileges_table.twig:262
#: templates/server/privileges/users_overview.twig:12
msgid "Global privileges"
msgstr ""

#: templates/server/privileges/privileges_table.twig:552
msgid "Administration"
msgstr ""

#: templates/server/privileges/privileges_table.twig:559
#: templates/server/privileges/privileges_table.twig:562
msgid ""
"Allows adding users and privileges without reloading the privilege tables."
msgstr ""

#: templates/server/privileges/privileges_table.twig:670
msgid "Allows creating foreign key relations."
msgstr ""

#: templates/server/privileges/privileges_table.twig:670
msgid "Not used on MariaDB."
msgstr ""

#: templates/server/privileges/privileges_table.twig:670
msgid "Not used for this MySQL version."
msgstr ""

#: templates/server/privileges/privileges_table.twig:721
msgid "Resource limits"
msgstr ""

#: templates/server/privileges/privileges_table.twig:723
msgid "Note: Setting these options to 0 (zero) removes the limit."
msgstr ""

#: templates/server/privileges/privileges_table.twig:729
#: templates/server/privileges/privileges_table.twig:736
msgid "Limits the number of queries the user may send to the server per hour."
msgstr ""

#: templates/server/privileges/privileges_table.twig:742
#: templates/server/privileges/privileges_table.twig:749
msgid ""
"Limits the number of commands that change any table or database the user may "
"execute per hour."
msgstr ""

#: templates/server/privileges/privileges_table.twig:755
#: templates/server/privileges/privileges_table.twig:762
msgid "Limits the number of new connections the user may open per hour."
msgstr ""

#: templates/server/privileges/privileges_table.twig:768
#: templates/server/privileges/privileges_table.twig:775
msgid "Limits the number of simultaneous connections the user may have."
msgstr ""

#: templates/server/privileges/privileges_table.twig:784
msgid "Does not require SSL-encrypted connections."
msgstr ""

#: templates/server/privileges/privileges_table.twig:793
msgid "Requires SSL-encrypted connections."
msgstr ""

#: templates/server/privileges/privileges_table.twig:802
msgid "Requires a valid X509 certificate."
msgstr ""

#: templates/server/privileges/privileges_table.twig:823
msgid "Requires that a specific cipher method be used for a connection."
msgstr ""

#: templates/server/privileges/privileges_table.twig:832
msgid "Requires that a valid X509 certificate issued by this CA be presented."
msgstr ""

#: templates/server/privileges/privileges_table.twig:841
msgid "Requires that a valid X509 certificate with this subject be presented."
msgstr ""

#: templates/server/privileges/subnav.twig:5
#: templates/server/privileges/user_overview.twig:5
msgid "User accounts overview"
msgstr ""

#: templates/server/privileges/subnav.twig:11
#: templates/server/user_groups/user_groups.twig:1
#: templates/server/user_groups/user_groups.twig:9
msgid "User groups"
msgstr ""

#: templates/server/privileges/user_properties.twig:57
#: templates/server/privileges/users_overview.twig:57
msgid "The selected user was not found in the privilege table."
msgstr ""

#: templates/server/privileges/user_properties.twig:107
msgid "Change login information / Copy user account"
msgstr ""

#: templates/server/privileges/user_properties.twig:114
msgid "Create a new user account with the same privileges and …"
msgstr ""

#: templates/server/privileges/user_properties.twig:120
msgid "… keep the old one."
msgstr ""

#: templates/server/privileges/user_properties.twig:127
msgid "… delete the old one from the user tables."
msgstr ""

#: templates/server/privileges/user_properties.twig:134
msgid ""
"… revoke all active privileges from the old one and delete it afterwards."
msgstr ""

#: templates/server/privileges/user_properties.twig:141
msgid ""
"… delete the old one from the user tables and reload the privileges "
"afterwards."
msgstr ""

#: templates/server/privileges/users_overview.twig:16
msgid "User group"
msgstr ""

#: templates/server/privileges/users_overview.twig:147
msgid "Remove selected user accounts"
msgstr ""

#: templates/server/privileges/users_overview.twig:149
msgid "Revoke all active privileges from the users and delete them afterwards."
msgstr ""

#: templates/server/privileges/users_overview.twig:153
msgid "Drop the databases that have the same names as the users."
msgstr ""

#: templates/server/privileges/users_overview.twig:178
msgid "Save changes"
msgstr ""

#: templates/server/replication/change_primary.twig:5
msgid "Replica configuration"
msgstr ""

#: templates/server/replication/change_primary.twig:6
#: templates/server/replication/replica_configuration.twig:97
msgid "Change or reconfigure primary server"
msgstr ""

#: templates/server/replication/change_primary.twig:9
msgid ""
"Make sure you have a unique server-id in your configuration file (my.cnf). "
"If not, please add the following line into [mysqld] section:"
msgstr ""

#: templates/server/replication/change_primary.twig:26
msgid "Port:"
msgstr ""

#: templates/server/replication/index.twig:21
#, php-format
msgid ""
"This server is not configured as primary in a replication process. Would you "
"like to %sconfigure%s it?"
msgstr ""

#: templates/server/replication/index.twig:43
msgid "No privileges"
msgstr ""

#: templates/server/replication/primary_add_replica_user.twig:6
#: templates/server/replication/primary_replication.twig:44
msgid "Add replica replication user"
msgstr ""

#: templates/server/replication/primary_add_replica_user.twig:21
#: templates/server/replication/primary_add_replica_user.twig:40
#: templates/server/replication/primary_add_replica_user.twig:54
msgid "Use text field:"
msgstr ""

#: templates/server/replication/primary_add_replica_user.twig:70
msgid "Generate password:"
msgstr ""

#: templates/server/replication/primary_configuration.twig:2
msgid "Primary configuration"
msgstr ""

#: templates/server/replication/primary_configuration.twig:4
msgid ""
"This server is not configured as a primary server in a replication process. "
"You can choose from either replicating all databases and ignoring some of "
"them (useful if you want to replicate a majority of the databases) or you "
"can choose to ignore all databases by default and allow only certain "
"databases to be replicated. Please select the mode:"
msgstr ""

#: templates/server/replication/primary_configuration.twig:9
msgid "Replicate all databases; Ignore:"
msgstr ""

#: templates/server/replication/primary_configuration.twig:10
msgid "Ignore all databases; Replicate:"
msgstr ""

#: templates/server/replication/primary_configuration.twig:12
msgid "Please select databases:"
msgstr ""

#: templates/server/replication/primary_configuration.twig:15
msgid ""
"Now, add the following lines at the end of [mysqld] section in your my.cnf "
"and please restart the MySQL server afterwards."
msgstr ""

#: templates/server/replication/primary_configuration.twig:21
msgid ""
"Once you restarted MySQL server, please click on Go button. Afterwards, you "
"should see a message informing you, that this server <strong>is</strong> "
"configured as primary."
msgstr ""

#: templates/server/replication/primary_replication.twig:5
msgid "This server is configured as primary in a replication process."
msgstr ""

#: templates/server/replication/primary_replication.twig:16
msgid "Show connected replicas"
msgstr ""

#: templates/server/replication/primary_replication.twig:38
msgid ""
"Only replicas started with the --report-host=host_name option are visible in "
"this list."
msgstr ""

#: templates/server/replication/replica_configuration.twig:5
msgid "Primary connection:"
msgstr ""

#: templates/server/replication/replica_configuration.twig:25
msgid "Replica SQL Thread not running!"
msgstr ""

#: templates/server/replication/replica_configuration.twig:28
msgid "Replica IO Thread not running!"
msgstr ""

#: templates/server/replication/replica_configuration.twig:31
msgid ""
"Server is configured as replica in a replication process. Would you like to:"
msgstr ""

#: templates/server/replication/replica_configuration.twig:34
#, fuzzy
#| msgid "Select All"
msgid "See replica status table"
msgstr "Alle ußwähle"

#: templates/server/replication/replica_configuration.twig:38
msgid "Control replica:"
msgstr ""

#: templates/server/replication/replica_configuration.twig:48
#, fuzzy
#| msgid "Select All"
msgid "Reset replica"
msgstr "Alle ußwähle"

#: templates/server/replication/replica_configuration.twig:54
msgid "Start SQL Thread only"
msgstr ""

#: templates/server/replication/replica_configuration.twig:56
msgid "Stop SQL Thread only"
msgstr ""

#: templates/server/replication/replica_configuration.twig:63
msgid "Start IO Thread only"
msgstr ""

#: templates/server/replication/replica_configuration.twig:65
msgid "Stop IO Thread only"
msgstr ""

#: templates/server/replication/replica_configuration.twig:74
msgid "Error management:"
msgstr ""

#: templates/server/replication/replica_configuration.twig:77
msgid "Skipping errors might lead into unsynchronized primary and replica!"
msgstr ""

#: templates/server/replication/replica_configuration.twig:81
msgid "Skip current error"
msgstr ""

#: templates/server/replication/replica_configuration.twig:87
#, php-format
msgid "Skip next %s errors."
msgstr ""

#: templates/server/replication/replica_configuration.twig:107
#, php-format
msgid ""
"This server is not configured as replica in a replication process. Would you "
"like to %sconfigure%s it?"
msgstr ""

#: templates/server/replication/status_table.twig:6
msgid "Primary status"
msgstr ""

#: templates/server/replication/status_table.twig:8
msgid "Replica status"
msgstr ""

#: templates/server/replication/status_table.twig:16
#: templates/server/status/variables/index.twig:75
#: templates/server/variables/index.twig:31
msgid "Variable"
msgstr ""

#: templates/server/select/index.twig:7 templates/server/select/index.twig:19
msgid "Current server:"
msgstr ""

#: templates/server/status/advisor/index.twig:6
#: templates/server/status/advisor/index.twig:19
msgid "Advisor system"
msgstr ""

#: templates/server/status/advisor/index.twig:9
msgid "Not enough privilege to view the advisor."
msgstr ""

#: templates/server/status/advisor/index.twig:12
msgid "Instructions"
msgstr ""

#: templates/server/status/advisor/index.twig:24
msgid ""
"The Advisor system can provide recommendations on server variables by "
"analyzing the server status variables."
msgstr ""

#: templates/server/status/advisor/index.twig:29
msgid ""
"Do note however that this system provides recommendations based on simple "
"calculations and by rule of thumb which may not necessarily apply to your "
"system."
msgstr ""

#: templates/server/status/advisor/index.twig:34
msgid ""
"Prior to changing any of the configuration, be sure to know what you are "
"changing (by reading the documentation) and how to undo the change. Wrong "
"tuning can have a very negative effect on performance."
msgstr ""

#: templates/server/status/advisor/index.twig:39
msgid ""
"The best way to tune your system would be to change only one setting at a "
"time, observe or benchmark your database, and undo the change if there was "
"no clearly measurable improvement."
msgstr ""

#: templates/server/status/advisor/index.twig:53
msgid "Errors occurred while executing rule expressions:"
msgstr ""

#: templates/server/status/advisor/index.twig:63
msgid "Possible performance issues"
msgstr ""

#: templates/server/status/advisor/index.twig:76
msgid "Issue:"
msgstr ""

#: templates/server/status/advisor/index.twig:79
msgid "Recommendation:"
msgstr ""

#: templates/server/status/advisor/index.twig:82
msgid "Justification:"
msgstr ""

#: templates/server/status/advisor/index.twig:85
msgid "Used variable / formula:"
msgstr ""

#: templates/server/status/advisor/index.twig:88
msgid "Test:"
msgstr ""

#: templates/server/status/base.twig:16
msgid "Query statistics"
msgstr ""

#: templates/server/status/base.twig:21
msgid "All status variables"
msgstr ""

#: templates/server/status/base.twig:26
msgid "Monitor"
msgstr ""

#: templates/server/status/base.twig:31
msgid "Advisor"
msgstr ""

#: templates/server/status/monitor/index.twig:8
msgid "Start Monitor"
msgstr ""

#: templates/server/status/monitor/index.twig:16
msgid "Instructions/Setup"
msgstr ""

#: templates/server/status/monitor/index.twig:20
msgid "Done dragging (rearranging) charts"
msgstr ""

#: templates/server/status/monitor/index.twig:27
msgid "Add chart"
msgstr ""

#: templates/server/status/monitor/index.twig:31
msgid "Enable charts dragging"
msgstr ""

#: templates/server/status/monitor/index.twig:36
#: templates/server/status/processes/index.twig:33
msgid "Refresh rate"
msgstr ""

#: templates/server/status/monitor/index.twig:43
#: templates/server/status/processes/index.twig:40
#, fuzzy, php-format
#| msgid "Select All"
msgid "%d second"
msgstr "Alle ußwähle"

#: templates/server/status/monitor/index.twig:45
#: templates/server/status/processes/index.twig:42
#, fuzzy, php-format
#| msgid "Select All"
msgid "%d seconds"
msgstr "Alle ußwähle"

#: templates/server/status/monitor/index.twig:49
#: templates/server/status/processes/index.twig:46
#, fuzzy, php-format
#| msgid "Select All"
msgid "%d minute"
msgstr "Alle ußwähle"

#: templates/server/status/monitor/index.twig:51
#: templates/server/status/processes/index.twig:48
#, fuzzy, php-format
#| msgid "Select All"
msgid "%d minutes"
msgstr "Alle ußwähle"

#: templates/server/status/monitor/index.twig:61
msgid "Chart columns"
msgstr ""

#: templates/server/status/monitor/index.twig:74
msgid "Chart arrangement"
msgstr ""

#: templates/server/status/monitor/index.twig:75
msgid ""
"The arrangement of the charts is stored to the browsers local storage. You "
"may want to export it if you have a complicated set up."
msgstr ""

#: templates/server/status/monitor/index.twig:84
msgid "Reset to default"
msgstr ""

#: templates/server/status/monitor/index.twig:89
msgid "Monitor Instructions"
msgstr ""

#: templates/server/status/monitor/index.twig:91
msgid ""
"The phpMyAdmin Monitor can assist you in optimizing the server configuration "
"and track down time intensive queries. For the latter you will need to set "
"log_output to 'TABLE' and have either the slow_query_log or general_log "
"enabled. Note however, that the general_log produces a lot of data and "
"increases server load by up to 15%."
msgstr ""

#: templates/server/status/monitor/index.twig:101
msgid "Using the monitor:"
msgstr ""

#: templates/server/status/monitor/index.twig:103
msgid ""
"Your browser will refresh all displayed charts in a regular interval. You "
"may add charts and change the refresh rate under 'Settings', or remove any "
"chart using the cog icon on each respective chart."
msgstr ""

#: templates/server/status/monitor/index.twig:108
msgid ""
"To display queries from the logs, select the relevant time span on any chart "
"by holding down the left mouse button and panning over the chart. Once "
"confirmed, this will load a table of grouped queries, there you may click on "
"any occurring SELECT statements to further analyze them."
msgstr ""

#: templates/server/status/monitor/index.twig:114
msgid "Please note:"
msgstr ""

#: templates/server/status/monitor/index.twig:117
msgid ""
"Enabling the general_log may increase the server load by 5-15%. Also be "
"aware that generating statistics from the logs is a load intensive task, so "
"it is advisable to select only a small time span and to disable the "
"general_log and empty its table once monitoring is not required any more."
msgstr ""

#: templates/server/status/monitor/index.twig:128
#: templates/server/status/monitor/index.twig:134
msgid "Chart Title"
msgstr ""

#: templates/server/status/monitor/index.twig:138
msgid "Preset chart"
msgstr ""

#: templates/server/status/monitor/index.twig:144
msgid "Status variable(s)"
msgstr ""

#: templates/server/status/monitor/index.twig:149
msgid "Select series:"
msgstr ""

#: templates/server/status/monitor/index.twig:152
msgid "Commonly monitored"
msgstr ""

#: templates/server/status/monitor/index.twig:170
msgid "or type variable name:"
msgstr ""

#: templates/server/status/monitor/index.twig:177
msgid "Display as differential value"
msgstr ""

#: templates/server/status/monitor/index.twig:182
msgid "Apply a divisor"
msgstr ""

#: templates/server/status/monitor/index.twig:193
msgid "Append unit to data values"
msgstr ""

#: templates/server/status/monitor/index.twig:201
msgid "Add this series"
msgstr ""

#: templates/server/status/monitor/index.twig:204
msgid "Clear series"
msgstr ""

#: templates/server/status/monitor/index.twig:208
msgid "Series in chart:"
msgstr ""

#: templates/server/status/monitor/index.twig:224
msgid "Log statistics"
msgstr ""

#: templates/server/status/monitor/index.twig:226
msgid "Selected time range:"
msgstr ""

#: templates/server/status/monitor/index.twig:234
msgid "Only retrieve SELECT,INSERT,UPDATE and DELETE Statements"
msgstr ""

#: templates/server/status/monitor/index.twig:240
msgid "Remove variable data in INSERT statements for better grouping"
msgstr ""

#: templates/server/status/monitor/index.twig:244
msgid "Choose from which log you want the statistics to be generated from."
msgstr ""

#: templates/server/status/monitor/index.twig:247
msgid "Results are grouped by query text."
msgstr ""

#: templates/server/status/monitor/index.twig:251
msgid "Query analyzer"
msgstr ""

#: templates/server/status/processes/index.twig:14
#, fuzzy
#| msgid "not active"
msgid "Show only active"
msgstr "nit aktief"

#: templates/server/status/processes/index.twig:28
msgid ""
"Note: Enabling the auto refresh here might cause heavy traffic between the "
"web server and the MySQL server."
msgstr ""

#. l10n: Questions is the name of a MySQL Status variable
#: templates/server/status/queries/index.twig:8
msgid "Questions since startup:"
msgstr ""

#: templates/server/status/queries/index.twig:20
msgid "per hour:"
msgstr ""

#: templates/server/status/queries/index.twig:21
msgid "per minute:"
msgstr ""

#: templates/server/status/queries/index.twig:23
msgid "per second:"
msgstr ""

#: templates/server/status/queries/index.twig:37
msgid "Statements"
msgstr ""

#. l10n: # = Amount of queries
#: templates/server/status/queries/index.twig:38
msgid "#"
msgstr ""

#: templates/server/status/queries/index.twig:39
#: templates/server/status/status/index.twig:18
#: templates/server/status/status/index.twig:38
msgid "ø per hour"
msgstr ""

#: templates/server/status/queries/index.twig:59
msgid "Not enough privilege to view query statistics."
msgstr ""

#: templates/server/status/status/index.twig:6
#, php-format
msgid "Network traffic since startup: %s"
msgstr ""

#: templates/server/status/status/index.twig:7
#, php-format
msgid "This MySQL server has been running for %1$s. It started up on %2$s."
msgstr ""

#: templates/server/status/status/index.twig:15
msgid ""
"On a busy server, the byte counters may overrun, so those statistics as "
"reported by the MySQL server may be incorrect."
msgstr ""

#: templates/server/status/status/index.twig:59
msgid ""
"This MySQL server works as <b>primary</b> and <b>replica</b> in "
"<b>replication</b> process."
msgstr ""

#: templates/server/status/status/index.twig:61
msgid ""
"This MySQL server works as <b>primary</b> in <b>replication</b> process."
msgstr ""

#: templates/server/status/status/index.twig:63
msgid ""
"This MySQL server works as <b>replica</b> in <b>replication</b> process."
msgstr ""

#: templates/server/status/status/index.twig:69
msgid "Replication status"
msgstr ""

#: templates/server/status/status/index.twig:75
msgid "Not enough privilege to view server status."
msgstr ""

#: templates/server/status/variables/index.twig:20
msgid "Show only alert values"
msgstr ""

#: templates/server/status/variables/index.twig:25
#: templates/server/status/variables/index.twig:27
msgid "Filter by category…"
msgstr ""

#: templates/server/status/variables/index.twig:37
msgid "Show unformatted values"
msgstr ""

#: templates/server/status/variables/index.twig:50
msgid "Related links:"
msgstr ""

#: templates/server/status/variables/index.twig:139
msgid "Not enough privilege to view status variables."
msgstr ""

#: templates/server/user_groups/edit_user_groups.twig:2
#: templates/server/user_groups/user_groups.twig:64
msgid "Add user group"
msgstr ""

#: templates/server/user_groups/edit_user_groups.twig:4
#, php-format
msgid "Edit user group: '%s'"
msgstr ""

#: templates/server/user_groups/edit_user_groups.twig:9
msgid "User group menu assignments"
msgstr ""

#: templates/server/user_groups/edit_user_groups.twig:14
msgid "Group name:"
msgstr ""

#: templates/server/user_groups/user_groups.twig:12
msgid "Server level tabs"
msgstr ""

#: templates/server/user_groups/user_groups.twig:15
msgid "Database level tabs"
msgstr ""

#: templates/server/user_groups/user_groups.twig:18
msgid "Table level tabs"
msgstr ""

#: templates/server/user_groups/user_groups.twig:50
msgid "Delete user group"
msgstr ""

#: templates/server/user_groups/user_listings.twig:1
#, php-format
msgid "Users of '%s' user group"
msgstr ""

#: templates/server/user_groups/user_listings.twig:3
msgid "No users were found belonging to this user group."
msgstr ""

#: templates/server/variables/index.twig:5
msgid "Server variables and settings"
msgstr ""

#: templates/server/variables/index.twig:43
msgid "This is a read-only variable and can not be edited"
msgstr ""

#: templates/server/variables/index.twig:69
msgid "Session value"
msgstr ""

#: templates/server/variables/index.twig:80
#, php-format
msgid "Not enough privilege to view server variables and settings. %s"
msgstr ""

#: templates/setup/base.twig:29 templates/setup/home/<USER>
msgid "Overview"
msgstr ""

#: templates/setup/config/index.twig:4 templates/setup/home/<USER>
msgid "Configuration file"
msgstr ""

#: templates/setup/config/index.twig:16
msgid "Generated configuration file"
msgstr ""

#: templates/setup/config/index.twig:22 templates/setup/home/<USER>
msgid "Download"
msgstr ""

#: templates/setup/error.twig:3
msgid "Submitted form contains errors"
msgstr ""

#: templates/setup/error.twig:6
msgid "Try to revert erroneous fields to their default values"
msgstr ""

#: templates/setup/error.twig:14
msgid "Ignore errors"
msgstr ""

#: templates/setup/error.twig:18
msgid "Show form"
msgstr ""

#: templates/setup/home/<USER>
msgid "Show hidden messages"
msgstr ""

#: templates/setup/home/<USER>
msgid "There are no configured servers"
msgstr ""

#: templates/setup/home/<USER>
msgid "New server"
msgstr ""

#: templates/setup/home/<USER>
msgid "Default language"
msgstr ""

#: templates/setup/home/<USER>
msgid "Default server"
msgstr ""

#: templates/setup/home/<USER>
msgid "let the user choose"
msgstr ""

#: templates/setup/home/<USER>
msgid "- none -"
msgstr ""

#: templates/setup/home/<USER>
msgid "End of line"
msgstr ""

#: templates/setup/home/<USER>
msgid "Display"
msgstr ""

#: templates/setup/home/<USER>
msgid "phpMyAdmin homepage"
msgstr ""

#: templates/setup/home/<USER>
msgid "Donate"
msgstr ""

#: templates/setup/home/<USER>
msgid "Check for latest version"
msgstr ""

#: templates/setup/servers/index.twig:6
msgid "Edit server"
msgstr ""

#: templates/setup/servers/index.twig:11
msgid "Add a new server"
msgstr ""

#: templates/setup/servers/index.twig:17
msgid "Something went wrong."
msgstr ""

#: templates/sql/bookmark.twig:11 templates/sql/bookmark.twig:29
msgid "Bookmark this SQL query"
msgstr ""

#: templates/sql/bookmark.twig:15
msgid "Label:"
msgstr ""

#: templates/sql/bookmark.twig:22 templates/sql/query.twig:93
msgid "Let every user access this bookmark"
msgstr ""

#: templates/sql/profiling_chart.twig:4
msgid "Detailed profile"
msgstr ""

#: templates/sql/profiling_chart.twig:13 templates/sql/profiling_chart.twig:43
msgid "State"
msgstr ""

#: templates/sql/profiling_chart.twig:38
msgid "Summary by state"
msgstr ""

#: templates/sql/profiling_chart.twig:47
msgid "Total Time"
msgstr ""

#: templates/sql/profiling_chart.twig:51
msgid "% Time"
msgstr ""

#: templates/sql/profiling_chart.twig:55
msgid "Calls"
msgstr ""

#: templates/sql/profiling_chart.twig:59
msgid "ø Time"
msgstr ""

#: templates/sql/query.twig:44
msgid "Get auto-saved query"
msgstr ""

#. l10n: Bind parameters in the SQL query using :parameterName format
#: templates/sql/query.twig:51
msgid "Bind parameters"
msgstr ""

#: templates/sql/query.twig:84
msgid "Bookmark this SQL query:"
msgstr ""

#: templates/sql/query.twig:100
msgid "Replace existing bookmark of same name"
msgstr ""

#: templates/sql/query.twig:110 templates/sql/query.twig:111
msgid "Delimiter"
msgstr ""

#: templates/sql/query.twig:119
msgid "Show this query here again"
msgstr ""

#: templates/sql/query.twig:134
msgid "Rollback when finished"
msgstr ""

#: templates/sql/query.twig:156
msgid "Bookmarked SQL query"
msgstr ""

#: templates/sql/query.twig:160
msgid "Bookmark:"
msgstr ""

#: templates/sql/query.twig:169
msgid "shared"
msgstr ""

#: templates/sql/query.twig:182
msgid "View only"
msgstr ""

#: templates/table/browse_foreigners/column_element.twig:4
msgid "Use this value"
msgstr ""

#: templates/table/chart/tbl_chart.twig:10
msgid "Chart type"
msgstr ""

#: templates/table/chart/tbl_chart.twig:13
msgctxt "Chart type"
msgid "Bar"
msgstr ""

#: templates/table/chart/tbl_chart.twig:17
msgctxt "Chart type"
msgid "Column"
msgstr ""

#: templates/table/chart/tbl_chart.twig:21
msgctxt "Chart type"
msgid "Line"
msgstr ""

#: templates/table/chart/tbl_chart.twig:25
msgctxt "Chart type"
msgid "Spline"
msgstr ""

#: templates/table/chart/tbl_chart.twig:29
msgctxt "Chart type"
msgid "Area"
msgstr ""

#: templates/table/chart/tbl_chart.twig:33
msgctxt "Chart type"
msgid "Pie"
msgstr ""

#: templates/table/chart/tbl_chart.twig:37
msgctxt "Chart type"
msgid "Timeline"
msgstr ""

#: templates/table/chart/tbl_chart.twig:41
msgctxt "Chart type"
msgid "Scatter"
msgstr ""

#: templates/table/chart/tbl_chart.twig:47
msgid "Stacked"
msgstr ""

#: templates/table/chart/tbl_chart.twig:51
msgid "Chart title:"
msgstr ""

#: templates/table/chart/tbl_chart.twig:57
msgid "X-Axis:"
msgstr ""

#: templates/table/chart/tbl_chart.twig:73
msgid "Series:"
msgstr ""

#: templates/table/chart/tbl_chart.twig:101
msgid "X-Axis label:"
msgstr ""

#: templates/table/chart/tbl_chart.twig:102
msgid "X Values"
msgstr ""

#: templates/table/chart/tbl_chart.twig:106
msgid "Y-Axis label:"
msgstr ""

#: templates/table/chart/tbl_chart.twig:107
msgid "Y Values"
msgstr ""

#: templates/table/chart/tbl_chart.twig:112
msgid "Series names are in a column"
msgstr ""

#: templates/table/chart/tbl_chart.twig:116
msgid "Series column:"
msgstr ""

#: templates/table/chart/tbl_chart.twig:126
#, fuzzy
#| msgid "Column"
msgid "Value Column:"
msgstr "Kolonn"

#: templates/table/chart/tbl_chart.twig:147
msgid "Save chart as image"
msgstr ""

#: templates/table/export/index.twig:12
msgid ""
"@SERVER@ will become the server name, @DATABASE@ will become the database "
"name and @TABLE@ will become the table name."
msgstr ""

#: templates/table/export/index.twig:7
#, php-format
msgid "Exporting rows from \"%s\" table"
msgstr ""

#: templates/table/find_replace/index.twig:4
#: templates/table/search/index.twig:4 templates/table/zoom_search/index.twig:4
msgid "Table search"
msgstr ""

#: templates/table/find_replace/index.twig:10
#: templates/table/search/index.twig:10
#: templates/table/zoom_search/index.twig:10
msgid "Zoom search"
msgstr ""

#: templates/table/find_replace/index.twig:16
#: templates/table/find_replace/index.twig:27
#: templates/table/search/index.twig:16
#: templates/table/zoom_search/index.twig:16
msgid "Find and replace"
msgstr ""

#: templates/table/find_replace/index.twig:36
msgid "Replace with:"
msgstr ""

#: templates/table/find_replace/index.twig:57
msgid "Use regular expression"
msgstr ""

#: templates/table/find_replace/replace_preview.twig:10
msgid "Find and replace - preview"
msgstr ""

#: templates/table/find_replace/replace_preview.twig:17
msgid "Original string"
msgstr ""

#: templates/table/find_replace/replace_preview.twig:18
msgid "Replaced string"
msgstr ""

#: templates/table/find_replace/replace_preview.twig:36
msgid "Replace"
msgstr ""

#: templates/table/gis_visualization/gis_visualization.twig:2
msgid "Display GIS Visualization"
msgstr ""

#: templates/table/gis_visualization/gis_visualization.twig:10
msgid "Label column"
msgstr ""

#: templates/table/gis_visualization/gis_visualization.twig:12
msgid "-- None --"
msgstr ""

#: templates/table/gis_visualization/gis_visualization.twig:21
msgid "Spatial column"
msgstr ""

#: templates/table/import/index.twig:3
#, php-format
msgid "Importing into the table \"%s\""
msgstr ""

#: templates/table/index_form.twig:18 templates/table/index_rename_form.twig:11
msgid "Index name:"
msgstr ""

#: templates/table/index_form.twig:19 templates/table/index_rename_form.twig:12
msgid ""
"\"PRIMARY\" <b>must</b> be the name of and <b>only of</b> a primary key!"
msgstr ""

#: templates/table/index_form.twig:37
msgid "Index choice:"
msgstr ""

#: templates/table/index_form.twig:57
msgid "Advanced options"
msgstr ""

#: templates/table/index_form.twig:67
msgid "Key block size:"
msgstr ""

#: templates/table/index_form.twig:84
msgid "Index type:"
msgstr ""

#: templates/table/index_form.twig:101
msgid "Parser:"
msgstr ""

#: templates/table/index_form.twig:117
msgid "Comment:"
msgstr ""

#: templates/table/index_form.twig:162 templates/table/index_form.twig:199
msgid "Drag to reorder"
msgstr ""

#: templates/table/insert/actions_panel.twig:9
msgid "Insert as new row"
msgstr ""

#: templates/table/insert/actions_panel.twig:10
msgid "Insert as new row and ignore errors"
msgstr ""

#: templates/table/insert/actions_panel.twig:11
msgid "Show insert query"
msgstr ""

#: templates/table/insert/actions_panel.twig:15
msgid "and then"
msgstr ""

#: templates/table/insert/actions_panel.twig:19
msgid "Go back to previous page"
msgstr ""

#: templates/table/insert/actions_panel.twig:20
msgid "Insert another new row"
msgstr ""

#: templates/table/insert/actions_panel.twig:22
msgid "Go back to this page"
msgstr ""

#: templates/table/insert/actions_panel.twig:24
msgid "Edit next row"
msgstr ""

#: templates/table/insert/actions_panel.twig:32
msgid ""
"Use TAB key to move from value to value, or CTRL+arrows to move anywhere."
msgstr ""

#: templates/table/insert/column_row.twig:15
msgid "Binary"
msgstr ""

#: templates/table/insert/column_row.twig:30
msgid "Use the NULL value for this column."
msgstr ""

#: templates/table/insert/column_row.twig:65
msgid "Because of its length,<br> this column might not be editable."
msgstr ""

#: templates/table/insert/column_row.twig:93
msgid "Binary - do not edit"
msgstr ""

#: templates/table/insert/column_row.twig:122
#: templates/table/search/input_box.twig:37
msgid "Edit/Insert"
msgstr ""

#: templates/table/insert/continue_insertion_form.twig:18
#, php-format
msgid "Continue insertion with %s rows"
msgstr ""

#: templates/table/maintenance/checksum.twig:13
#, fuzzy
msgid "Checksum"
msgstr "Ein Tabäll"

#: templates/table/operations/index.twig:9
msgid "Alter table order by"
msgstr ""

#: templates/table/operations/index.twig:20
msgctxt "Alter table order by a single field."
msgid "(singly)"
msgstr ""

#: templates/table/operations/index.twig:50
msgid "Move table to (database.table)"
msgstr ""

#: templates/table/operations/index.twig:101
msgid "Table options"
msgstr ""

#: templates/table/operations/index.twig:105
msgid "Rename table to"
msgstr ""

#: templates/table/operations/index.twig:123
msgid "Table comments"
msgstr ""

#: templates/table/operations/index.twig:170
msgid "Change all column collations"
msgstr ""

#: templates/table/operations/index.twig:251
msgid "Copy table to (database.table)"
msgstr ""

#: templates/table/operations/index.twig:322
msgid "Switch to copied table"
msgstr ""

#: templates/table/operations/index.twig:364
msgid "Defragment table"
msgstr ""

#: templates/table/operations/index.twig:372
#, php-format
msgid "Table %s has been flushed."
msgstr ""

#: templates/table/operations/index.twig:376
msgid "Flush the table (FLUSH)"
msgstr ""

#: templates/table/operations/index.twig:413
msgid "Empty the table (TRUNCATE)"
msgstr ""

#: templates/table/operations/index.twig:431
msgid "Empty the table (DELETE FROM)"
msgstr ""

#: templates/table/operations/index.twig:452
msgid "Delete the table (DROP)"
msgstr ""

#: templates/table/operations/index.twig:474
msgid "Partition maintenance"
msgstr ""

#: templates/table/operations/index.twig:500
#: templates/table/structure/display_partitions.twig:201
msgid "Remove partitioning"
msgstr ""

#: templates/table/operations/index.twig:513
msgid "Check referential integrity"
msgstr ""

#: templates/table/operations/view.twig:12
msgid "Rename view to"
msgstr ""

#: templates/table/operations/view.twig:37
msgid "Delete the view (DROP)"
msgstr ""

#: templates/table/page_with_secondary_tabs.twig:11
msgid "Relation view"
msgstr ""

#: templates/table/partition/analyze.twig:2
msgid "Analyze partition"
msgstr ""

#: templates/table/partition/check.twig:2
msgid "Check partition"
msgstr ""

#: templates/table/partition/drop.twig:2
msgid "Drop partition"
msgstr ""

#: templates/table/partition/optimize.twig:2
msgid "Optimize partition"
msgstr ""

#: templates/table/partition/rebuild.twig:2
msgid "Rebuild partition"
msgstr ""

#: templates/table/partition/repair.twig:2
msgid "Repair partition"
msgstr ""

#: templates/table/partition/truncate.twig:2
msgid "Truncate partition"
msgstr ""

#: templates/table/privileges/index.twig:57
msgid "table-specific"
msgstr ""

#: templates/table/relation/common_form.twig:9
msgid "Foreign key constraints"
msgstr ""

#: templates/table/relation/common_form.twig:14
msgid "Actions"
msgstr ""

#: templates/table/relation/common_form.twig:15
msgid "Constraint properties"
msgstr ""

#: templates/table/relation/common_form.twig:19
msgid ""
"Creating a foreign key over a non-indexed column would automatically create "
"an index on it. Alternatively, you can define an index below, before "
"creating the foreign key."
msgstr ""

#: templates/table/relation/common_form.twig:24
msgid ""
"Only columns with index will be displayed. You can define an index below."
msgstr ""

#: templates/table/relation/common_form.twig:28
msgid "Foreign key constraint"
msgstr ""

#: templates/table/relation/common_form.twig:93
msgid "+ Add constraint"
msgstr ""

#: templates/table/relation/common_form.twig:106
#: templates/table/relation/common_form.twig:114
msgid "Internal relationships"
msgstr ""

#: templates/table/relation/common_form.twig:122
msgid "Internal relation"
msgstr ""

#: templates/table/relation/common_form.twig:124
msgid ""
"An internal relation is not necessary when a corresponding FOREIGN KEY "
"relation exists."
msgstr ""

#: templates/table/relation/common_form.twig:208
msgid "Choose column to display:"
msgstr ""

#: templates/table/relation/foreign_key_row.twig:15
#, php-format
msgid "Foreign key constraint %s has been dropped"
msgstr ""

#: templates/table/relation/foreign_key_row.twig:33
msgid "Constraint name"
msgstr ""

#: templates/table/search/index.twig:27
msgid "Do a \"query by example\" (wildcard: \"%\")"
msgstr ""

#: templates/table/search/index.twig:111
msgid "Select columns (at least one):"
msgstr ""

#: templates/table/search/index.twig:130
msgid "Add search conditions (body of the \"where\" clause):"
msgstr ""

#: templates/table/search/index.twig:138
msgid "Number of rows per page"
msgstr ""

#: templates/table/search/index.twig:144
msgid "Display order:"
msgstr ""

#: templates/table/search/index.twig:181
msgid "Range search"
msgstr ""

#: templates/table/search/index.twig:187
msgid "Minimum value:"
msgstr ""

#: templates/table/search/index.twig:190
msgid "Maximum value:"
msgstr ""

#: templates/table/start_and_number_of_rows_fieldset.twig:3
msgid "Start row:"
msgstr ""

#: templates/table/structure/display_partitions.twig:4
#: templates/table/structure/display_structure.twig:597
msgid "Partitions"
msgstr ""

#: templates/table/structure/display_partitions.twig:8
msgid "No partitioning defined!"
msgstr ""

#: templates/table/structure/display_partitions.twig:11
msgid "Partitioned by:"
msgstr ""

#: templates/table/structure/display_partitions.twig:16
msgid "Sub partitioned by:"
msgstr ""

#: templates/table/structure/display_partitions.twig:29
msgid "Data length"
msgstr ""

#: templates/table/structure/display_partitions.twig:30
msgid "Index length"
msgstr ""

#: templates/table/structure/display_partitions.twig:194
#, fuzzy
msgid "Partition table"
msgstr "Ein Tabäll"

#: templates/table/structure/display_partitions.twig:207
#: templates/table/structure/partition_definition_form.twig:5
msgid "Edit partitioning"
msgstr ""

#: templates/table/structure/display_structure.twig:70
msgid "Media type:"
msgstr ""

#: templates/table/structure/display_structure.twig:91
#: templates/table/tracking/structure_snapshot_columns.twig:47
msgctxt "None for default"
msgid "None"
msgstr ""

#: templates/table/structure/display_structure.twig:108
#: templates/table/structure/display_structure.twig:287
msgid "Change"
msgstr ""

#: templates/table/structure/display_structure.twig:116
#, php-format
msgid "Column %s has been dropped."
msgstr ""

#: templates/table/structure/display_structure.twig:142
#, php-format
msgid "A primary key has been added on %s."
msgstr ""

#: templates/table/structure/display_structure.twig:157
#: templates/table/structure/display_structure.twig:172
#: templates/table/structure/display_structure.twig:197
#: templates/table/structure/display_structure.twig:216
#, php-format
msgid "An index has been added on %s."
msgstr ""

#: templates/table/structure/display_structure.twig:238
msgid "Distinct values"
msgstr ""

#: templates/table/structure/display_structure.twig:249
#: templates/table/structure/display_structure.twig:315
msgid "Remove from central columns"
msgstr ""

#: templates/table/structure/display_structure.twig:257
#: templates/table/structure/display_structure.twig:312
msgid "Add to central columns"
msgstr ""

#: templates/table/structure/display_structure.twig:328
#: templates/table/structure/display_structure.twig:332
#: templates/table/structure/display_structure.twig:417
msgid "Move columns"
msgstr ""

#: templates/table/structure/display_structure.twig:333
msgid "Move the columns by dragging them up and down."
msgstr ""

#: templates/table/structure/display_structure.twig:389
#: templates/view_create.twig:13
msgid "Edit view"
msgstr ""

#: templates/table/structure/display_structure.twig:403
msgid "Propose table structure"
msgstr ""

#: templates/table/structure/display_structure.twig:420
msgid "Normalize"
msgstr ""

#: templates/table/structure/display_structure.twig:426
msgid "Track view"
msgstr ""

#: templates/table/structure/display_structure.twig:440
#, php-format
msgid "Add %s column(s)"
msgstr ""

#: templates/table/structure/display_structure.twig:445
msgid "at beginning of table"
msgstr ""

#: templates/table/structure/display_structure.twig:569
#, php-format
msgid "Create an index on %s columns"
msgstr ""

#: templates/table/structure/display_table_stats.twig:14
msgid "Space usage"
msgstr ""

#: templates/table/structure/display_table_stats.twig:37
msgid "Effective"
msgstr ""

#: templates/table/structure/display_table_stats.twig:80
msgid "Row statistics"
msgstr ""

#: templates/table/structure/display_table_stats.twig:86
msgid "static"
msgstr ""

#: templates/table/structure/display_table_stats.twig:88
msgid "dynamic"
msgstr ""

#: templates/table/structure/display_table_stats.twig:99
msgid "partitioned"
msgstr ""

#: templates/table/structure/display_table_stats.twig:128
msgid "Row length"
msgstr ""

#: templates/table/structure/display_table_stats.twig:140
msgid "Row size"
msgstr ""

#: templates/table/structure/display_table_stats.twig:147
msgid "Next autoindex"
msgstr ""

#: templates/table/tracking/main.twig:64 templates/table/tracking/main.twig:91
#: templates/table/tracking/main.twig:92
msgid "Delete version"
msgstr ""

#: templates/table/tracking/main.twig:102
#, php-format
msgid "Activate tracking for %s"
msgstr ""

#: templates/table/tracking/main.twig:104
msgid "Activate now"
msgstr ""

#: templates/table/tracking/main.twig:106
#, php-format
msgid "Deactivate tracking for %s"
msgstr ""

#: templates/table/tracking/main.twig:108
msgid "Deactivate now"
msgstr ""

#: templates/table/tracking/report_table.twig:4
#: templates/table/tracking/structure_snapshot_columns.twig:5
msgctxt "Number"
msgid "#"
msgstr ""

#: templates/table/tracking/report_table.twig:5
msgid "Date"
msgstr ""

#: templates/table/tracking/report_table.twig:6
msgid "Username"
msgstr ""

#: templates/table/zoom_search/index.twig:27
msgid "Do a \"query by example\" (wildcard: \"%\") for two different columns"
msgstr ""

#: templates/table/zoom_search/index.twig:54
msgid "Additional search criteria"
msgstr ""

#: templates/table/zoom_search/index.twig:116
msgid "Use this column to label each point"
msgstr ""

#: templates/table/zoom_search/index.twig:141
msgid "Maximum rows to plot"
msgstr ""

#: templates/table/zoom_search/result_form.twig:7
msgid "Browse/Edit the points"
msgstr ""

#: templates/table/zoom_search/result_form.twig:14
msgid "How to use"
msgstr ""

#: templates/table/zoom_search/result_form.twig:17
msgid "Reset zoom"
msgstr ""

#. l10n: Show or hide the menu using the hamburger style button
#: templates/top_menu.twig:4
msgid "Toggle navigation"
msgstr ""

#. l10n: Current page
#: templates/top_menu.twig:14
msgid "(current)"
msgstr ""

#: templates/transformation_overview.twig:1
msgid "Available media types"
msgstr ""

#: templates/transformation_overview.twig:13
msgid "Available browser display transformations"
msgstr ""

#: templates/transformation_overview.twig:19
#: templates/transformation_overview.twig:38
msgctxt "for media type transformation"
msgid "Description"
msgstr ""

#: templates/transformation_overview.twig:32
msgid "Available input transformations"
msgstr ""

#: templates/view_create.twig:65
msgid "VIEW name"
msgstr ""

#: templates/view_create.twig:79
msgid "Column names"
msgstr ""

#, fuzzy
#~| msgid "Database"
#~ msgid "Databases:"
#~ msgstr "Dahtebangk"

#, fuzzy
#~| msgid "Created"
#~ msgid "Create %s"
#~ msgstr "Aanjelaat"

#~ msgid "Select All"
#~ msgstr "Alle ußwähle"

#, fuzzy
#~| msgid "Select All"
#~ msgid "Select one ..."
#~ msgstr "Alle ußwähle"
