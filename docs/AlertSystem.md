# Sistema Centralizado de Alertas

El sistema centralizado de alertas proporciona una interfaz unificada para mostrar alertas de SweetAlert2 en toda la aplicación de manera consistente.

## Componentes del Sistema

### 1. AlertService (`src/Service/AlertService.php`)
Servicio principal que maneja la lógica de creación de alertas.

### 2. AlertExtension (`src/Twig/AlertExtension.php`)
Extensión de Twig que expone las funciones de alertas en las plantillas.

### 3. TraspasoAlertService (`src/Service/TraspasoAlertService.php`)
Servicio especializado que extiende AlertService para funcionalidades específicas de traspasos.

## Uso en Controladores

### Inyección del Servicio
```php
use App\Service\AlertService;

class MiController extends AbstractController
{
    private AlertService $alertService;

    public function __construct(AlertService $alertService)
    {
        $this->alertService = $alertService;
    }
}
```

### Ejemplos de Uso en Controladores

#### Alerta de Éxito
```php
public function guardar()
{
    // ... lógica de guardado ...
    
    $alertConfig = $this->alertService->success(
        'Guardado Exitoso',
        'Los datos se han guardado correctamente'
    );
    
    return $this->render('template.html.twig', [
        'alert' => $alertConfig
    ]);
}
```

#### Alerta de Error
```php
public function procesar()
{
    try {
        // ... lógica que puede fallar ...
    } catch (\Exception $e) {
        $alertConfig = $this->alertService->error(
            'Error de Procesamiento',
            $e->getMessage()
        );
        
        return $this->render('template.html.twig', [
            'alert' => $alertConfig
        ]);
    }
}
```

#### Alerta de Confirmación
```php
public function eliminar()
{
    $alertConfig = $this->alertService->confirmDelete('usuario');
    
    return $this->render('confirm.html.twig', [
        'alert' => $alertConfig
    ]);
}
```

## Uso en Plantillas Twig

### Funciones Básicas

#### Alerta de Éxito
```twig
{{ alert_success('Operación Exitosa', 'Los datos se guardaron correctamente') }}
```

#### Alerta de Error
```twig
{{ alert_error('Error', 'No se pudo completar la operación') }}
```

#### Alerta de Advertencia
```twig
{{ alert_warning('Advertencia', 'Algunos datos pueden estar desactualizados') }}
```

#### Alerta de Información
```twig
{{ alert_info('Información', 'El proceso puede tardar unos minutos') }}
```

#### Alerta de Confirmación
```twig
{{ alert_confirm('¿Continuar?', '¿Está seguro de realizar esta acción?') }}
```

### Funciones Especializadas

#### Confirmación de Eliminación
```twig
{{ alert_confirm_delete('usuario', {}, 'eliminarUsuario()') }}
```

#### Operación Exitosa
```twig
{{ alert_operation_success('Guardado') }}
```

#### Error de Operación
```twig
{{ alert_operation_error('Guardado', 'Error de conexión a la base de datos') }}
```

#### Error de Validación
```twig
{{ alert_validation_error(['El nombre es requerido', 'El email no es válido']) }}
```

#### Acceso Denegado
```twig
{{ alert_access_denied('página') }}
```

#### Sesión Expirada
```twig
{{ alert_session_expired() }}
```

#### Alerta de Carga
```twig
{{ alert_loading('Procesando datos...') }}
```

#### Toast (Notificación pequeña)
```twig
{{ alert_toast('Datos guardados', 'success') }}
```

### Uso con Callbacks

#### Con Callback Simple
```twig
{{ alert_confirm('¿Eliminar?', 'Esta acción no se puede deshacer', {}, 'window.location.href="/eliminar"') }}
```

#### Con Configuración Personalizada
```twig
{% set config = {
    'title': 'Título Personalizado',
    'text': 'Mensaje personalizado',
    'icon': 'warning',
    'showCancelButton': true,
    'confirmButtonText': 'Aceptar',
    'cancelButtonText': 'Cancelar'
} %}
{{ alert_script(config) }}
```

## Uso en AJAX

### Generar JSON para Respuestas AJAX
```twig
{# En el controlador #}
$alertJson = $this->alertService->toJson(
    $this->alertService->success('Éxito', 'Operación completada')
);

return new JsonResponse(['alert' => $alertJson]);
```

```javascript
// En JavaScript
$.post('/api/endpoint', data)
    .done(function(response) {
        if (response.alert) {
            Swal.fire(JSON.parse(response.alert));
        }
    });
```

### Usando Funciones de Twig para JSON
```twig
{# Generar JSON directamente en Twig #}
var alertConfig = {{ alert_json('success', 'Título', 'Mensaje') }};
Swal.fire(alertConfig);
```

## Tipos de Alerta Disponibles

### Básicos
- `success`: Operaciones exitosas
- `error`: Errores y fallos
- `warning`: Advertencias
- `info`: Información general
- `confirm`: Confirmaciones

### Especializados
- `confirm_delete`: Confirmación de eliminación
- `operation_success`: Éxito de operación
- `operation_error`: Error de operación
- `validation_error`: Errores de validación
- `access_denied`: Acceso denegado
- `session_expired`: Sesión expirada
- `loading`: Indicador de carga
- `toast`: Notificación pequeña

## Personalización

### Opciones Comunes
```php
$options = [
    'timer' => 5000,                    // Auto-cerrar después de 5 segundos
    'showConfirmButton' => true,        // Mostrar botón de confirmación
    'confirmButtonText' => 'Aceptar',   // Texto del botón
    'confirmButtonColor' => '#007bff',  // Color del botón
    'allowOutsideClick' => false,       // Permitir cerrar haciendo clic fuera
    'allowEscapeKey' => false,          // Permitir cerrar con Escape
    'footer' => 'Información adicional' // Pie de la alerta
];
```

### Configuración Avanzada
```php
$config = $this->alertService->custom([
    'title' => 'Título',
    'html' => '<div>Contenido HTML personalizado</div>',
    'imageUrl' => '/images/custom-icon.png',
    'imageWidth' => 400,
    'imageHeight' => 200,
    'backdrop' => 'rgba(0,0,123,0.4)',
    'position' => 'top-end',
    'grow' => 'row'
]);
```

## Migración desde TraspasoAlertService

El `TraspasoAlertService` ahora extiende `AlertService`, por lo que todas las funciones existentes siguen funcionando. Sin embargo, se recomienda migrar gradualmente:

### Antes
```php
$config = $this->traspasoAlertService->successAlert('Título', 'Mensaje');
```

### Después
```php
$config = $this->alertService->success('Título', 'Mensaje');
```

## Mejores Prácticas

1. **Consistencia**: Usa siempre el sistema centralizado para mantener consistencia visual
2. **Mensajes Claros**: Proporciona mensajes descriptivos y útiles para el usuario
3. **Confirmaciones**: Usa confirmaciones para acciones destructivas
4. **Feedback**: Proporciona feedback inmediato para todas las acciones del usuario
5. **Accesibilidad**: Los mensajes deben ser claros y comprensibles

## Ejemplos Completos

### Controlador con Validación
```php
public function guardarUsuario(Request $request)
{
    $errors = $this->validateUser($request);
    
    if (!empty($errors)) {
        $alertConfig = $this->alertService->validationError($errors);
        return $this->render('usuario/form.html.twig', [
            'alert' => $alertConfig
        ]);
    }
    
    try {
        $this->userService->save($request->request->all());
        $alertConfig = $this->alertService->operationSuccess('Guardado');
    } catch (\Exception $e) {
        $alertConfig = $this->alertService->operationError('Guardado', $e->getMessage());
    }
    
    return $this->render('usuario/form.html.twig', [
        'alert' => $alertConfig
    ]);
}
```

### Plantilla con Múltiples Alertas
```twig
{# Mostrar alerta si existe #}
{% if alert is defined %}
    {{ alert_script(alert) }}
{% endif %}

{# Botón con confirmación #}
<button onclick="confirmarEliminacion()">Eliminar Usuario</button>

<script>
function confirmarEliminacion() {
    {{ alert_confirm_delete('usuario', {}, 'eliminarUsuario()') }}
}

function eliminarUsuario() {
    // Lógica de eliminación
    fetch('/eliminar-usuario', {method: 'DELETE'})
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                {{ alert_operation_success('Eliminación') }}
            } else {
                {{ alert_operation_error('Eliminación', 'No se pudo eliminar el usuario') }}
            }
        });
}
</script>
```
