<?php

namespace App\Exception;

/**
 * Exception for Twilio Verify API errors
 */
class TwilioVerifyException extends PhoneVerificationException
{
    /**
     * @var int
     */
    private $twilioErrorCode;

    /**
     * TwilioVerifyException constructor.
     *
     * @param string $message
     * @param string|null $status
     * @param int $twilioErrorCode
     * @param int $code
     * @param \Throwable|null $previous
     */
    public function __construct(string $message = "", ?string $status = null, int $twilioErrorCode = 0, int $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $status, $code, $previous);
        $this->twilioErrorCode = $twilioErrorCode;
    }

    /**
     * @return int
     */
    public function getTwilioErrorCode(): int
    {
        return $this->twilioErrorCode;
    }

    /**
     * Create from Twilio exception
     *
     * @param \Twilio\Exceptions\TwilioException $exception
     * @return self
     */
    public static function fromTwilioException(\Twilio\Exceptions\TwilioException $exception): self
    {
        $message = $exception->getMessage();
        $code = $exception->getCode();
        $status = 'failed';
        
        // Handle rate limiting (429)
        if ($code === 20429 || strpos($message, 'Too many requests') !== false) {
            $message = 'Demasiadas solicitudes. Por favor, intente de nuevo más tarde.';
            $status = 'failed';
        }
        
        return new self($message, $status, $code, 0, $exception);
    }
}