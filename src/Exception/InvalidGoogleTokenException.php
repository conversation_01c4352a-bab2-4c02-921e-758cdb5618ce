<?php

namespace App\Exception;

use Exception;

class InvalidGoogleTokenException extends \RuntimeException
{
    protected $message = 'Invalid Google ID token';
    private $errorDetails;

    public function __construct(string $message = 'Invalid Google ID token', string $platform = null, array $errorDetails = [], int $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->errorDetails = $errorDetails;
    }

    public function getErrorDetails(): array
    {
        return $this->errorDetails;
    }
}
