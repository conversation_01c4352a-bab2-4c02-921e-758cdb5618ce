<?php

namespace App\Exception;

/**
 * Base exception for phone verification
 */
class PhoneVerificationException extends \Exception
{
    /**
     * @var string|null
     */
    private $status;

    /**
     * PhoneVerificationException constructor.
     *
     * @param string $message
     * @param string|null $status
     * @param int $code
     * @param \Throwable|null $previous
     */
    public function __construct(string $message = "", ?string $status = null, int $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->status = $status;
    }

    /**
     * @return string|null
     */
    public function getStatus(): ?string
    {
        return $this->status;
    }
}