<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Framecolor
 *
 * @ORM\Table(name="frameColor")
 * @ORM\Entity
 */
class Framecolor
{
    /**
     * @var int
     *
     * @ORM\Column(name="idframeColor", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idframecolor;

    /**
     * @var string|null
     *
     * @ORM\Column(name="color", type="string", length=45, nullable=true)
     */
    private $color;

    /**
     * @var string
     *
     * @ORM\Column(name="colorCode", type="string", length=6, nullable=false, options={"default"="000000","fixed"=true})
     */
    private $colorcode = '000000';

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    public function getIdframecolor(): ?int
    {
        return $this->idframecolor;
    }

    public function getColor(): ?string
    {
        return $this->color;
    }

    public function setColor(?string $color): self
    {
        $this->color = $color;

        return $this;
    }

    public function getColorcode(): ?string
    {
        return $this->colorcode;
    }

    public function setColorcode(string $colorcode): self
    {
        $this->colorcode = $colorcode;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }


}
