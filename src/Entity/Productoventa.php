<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Productoventa
 *
 * @ORM\Table(name="productoVenta", indexes={@ORM\Index(name="fk_productoVenta_venta1_idx", columns={"venta_idventa"}), @ORM\Index(name="fk_productoVenta_producto1_idx", columns={"producto_idproducto"})})
 * @ORM\Entity
 */
class Productoventa
{
    /**
     * @var int
     *
     * @ORM\Column(name="idproductoVenta", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idproductoventa;

    /**
     * @var int
     *
     * @ORM\Column(name="cantidad", type="integer", nullable=false)
     */
    private $cantidad;

    /**
     * @var string
     *
     * @ORM\Column(name="precio", type="decimal", precision=15, scale=2, nullable=false)
     */
    private $precio;

    /**
     * @var string
     *
     * @ORM\Column(name="costo", type="decimal", precision=15, scale=2, nullable=false)
     */
    private $costo;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=true)
     */
    private $creacion;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="modificacion", type="datetime", nullable=true)
     */
    private $modificacion;

    /**
     * @var float|null
     *
     * @ORM\Column(name="porcentajeComision", type="float", precision=10, scale=0, nullable=true)
     */
    private $porcentajecomision;

    /**
     * @var string|null
     *
     * @ORM\Column(name="comision", type="decimal", precision=9, scale=2, nullable=true)
     */
    private $comision;

    /**
     * @var float|null
     *
     * @ORM\Column(name="porcentajeDescuento", type="float", precision=10, scale=0, nullable=true)
     */
    private $porcentajedescuento;

    /**
     * @var string|null
     *
     * @ORM\Column(name="precioFinal", type="decimal", precision=9, scale=2, nullable=true)
     */
    private $preciofinal;

    /**
     * @var \Producto
     *
     * @ORM\ManyToOne(targetEntity="Producto")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="producto_idproducto", referencedColumnName="idproducto")
     * })
     */
    private $productoIdproducto;

    /**
     * @var \Venta
     *
     * @ORM\ManyToOne(targetEntity="Venta")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="venta_idventa", referencedColumnName="idventa")
     * })
     */
    private $ventaIdventa;

    public function getIdproductoventa(): ?int
    {
        return $this->idproductoventa;
    }

    public function getCantidad(): ?int
    {
        return $this->cantidad;
    }

    public function setCantidad(int $cantidad): self
    {
        $this->cantidad = $cantidad;

        return $this;
    }

    public function getPrecio(): ?string
    {
        return $this->precio;
    }

    public function setPrecio(string $precio): self
    {
        $this->precio = $precio;

        return $this;
    }

    public function getCosto(): ?string
    {
        return $this->costo;
    }

    public function setCosto(string $costo): self
    {
        $this->costo = $costo;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(?\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getModificacion(): ?\DateTimeInterface
    {
        return $this->modificacion;
    }

    public function setModificacion(?\DateTimeInterface $modificacion): self
    {
        $this->modificacion = $modificacion;

        return $this;
    }

    public function getPorcentajecomision(): ?float
    {
        return $this->porcentajecomision;
    }

    public function setPorcentajecomision(?float $porcentajecomision): self
    {
        $this->porcentajecomision = $porcentajecomision;

        return $this;
    }

    public function getComision(): ?string
    {
        return $this->comision;
    }

    public function setComision(?string $comision): self
    {
        $this->comision = $comision;

        return $this;
    }

    public function getPorcentajedescuento(): ?float
    {
        return $this->porcentajedescuento;
    }

    public function setPorcentajedescuento(?float $porcentajedescuento): self
    {
        $this->porcentajedescuento = $porcentajedescuento;

        return $this;
    }

    public function getPreciofinal(): ?string
    {
        return $this->preciofinal;
    }

    public function setPreciofinal(?string $preciofinal): self
    {
        $this->preciofinal = $preciofinal;

        return $this;
    }

    public function getProductoIdproducto(): ?Producto
    {
        return $this->productoIdproducto;
    }

    public function setProductoIdproducto(?Producto $productoIdproducto): self
    {
        $this->productoIdproducto = $productoIdproducto;

        return $this;
    }

    public function getVentaIdventa(): ?Venta
    {
        return $this->ventaIdventa;
    }

    public function setVentaIdventa(?Venta $ventaIdventa): self
    {
        $this->ventaIdventa = $ventaIdventa;

        return $this;
    }


}
