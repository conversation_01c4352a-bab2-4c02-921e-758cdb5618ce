<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Customflowstageinstance
 *
 * @ORM\Table(name="customFlowStageInstance", indexes={@ORM\Index(name="fk_customFlowStageInstance_customFlowStage1_idx", columns={"customFlowStage_idcustomFlowStage"}), @ORM\Index(name="fk_customFlowStageInstance_usuario1_idx", columns={"usuario_idusuario"})})
 * @ORM\Entity
 */
class Customflowstageinstance
{
    /**
     * @var int
     *
     * @ORM\Column(name="idcustomFlowStageInstance", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idcustomflowstageinstance;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="createdAt", type="datetime", nullable=false)
     */
    private $createdat;

    /**
     * @var string|null
     *
     * @ORM\Column(name="response", type="text", length=0, nullable=true)
     */
    private $response;

    /**
     * @var string|null
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=true)
     */
    private $status;

    /**
     * @var \Customflowstage
     *
     * @ORM\ManyToOne(targetEntity="Customflowstage")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="customFlowStage_idcustomFlowStage", referencedColumnName="idcustomFlowStage")
     * })
     */
    private $customflowstageIdcustomflowstage;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    public function getIdcustomflowstageinstance(): ?int
    {
        return $this->idcustomflowstageinstance;
    }

    public function getCreatedat(): ?\DateTimeInterface
    {
        return $this->createdat;
    }

    public function setCreatedat(\DateTimeInterface $createdat): self
    {
        $this->createdat = $createdat;

        return $this;
    }

    public function getResponse(): ?string
    {
        return $this->response;
    }

    public function setResponse(?string $response): self
    {
        $this->response = $response;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getCustomflowstageIdcustomflowstage(): ?Customflowstage
    {
        return $this->customflowstageIdcustomflowstage;
    }

    public function setCustomflowstageIdcustomflowstage(?Customflowstage $customflowstageIdcustomflowstage): self
    {
        $this->customflowstageIdcustomflowstage = $customflowstageIdcustomflowstage;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }


}
