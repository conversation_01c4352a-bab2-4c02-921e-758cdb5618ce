<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Unidad
 *
 * @ORM\Table(name="unidad", indexes={@ORM\Index(name="fk_unidad_empresaCliente1_idx", columns={"empresaCliente_idempresaCliente"})})
 * @ORM\Entity
 */
class Unidad
{
    /**
     * @var int
     *
     * @ORM\Column(name="idunidad", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idunidad;

    /**
     * @var string
     *
     * @ORM\Column(name="nombre", type="string", length=500, nullable=false)
     */
    private $nombre;

    /**
     * @var string|null
     *
     * @ORM\Column(name="direccion", type="text", length=16777215, nullable=true)
     */
    private $direccion;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \Empresacliente
     *
     * @ORM\ManyToOne(targetEntity="Empresacliente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="empresaCliente_idempresaCliente", referencedColumnName="idempresaCliente")
     * })
     */
    private $empresaclienteIdempresacliente;

    public function getIdunidad(): ?int
    {
        return $this->idunidad;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(string $nombre): self
    {
        $this->nombre = $nombre;

        return $this;
    }

    public function getDireccion(): ?string
    {
        return $this->direccion;
    }

    public function setDireccion(?string $direccion): self
    {
        $this->direccion = $direccion;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getEmpresaclienteIdempresacliente(): ?Empresacliente
    {
        return $this->empresaclienteIdempresacliente;
    }

    public function setEmpresaclienteIdempresacliente(?Empresacliente $empresaclienteIdempresacliente): self
    {
        $this->empresaclienteIdempresacliente = $empresaclienteIdempresacliente;

        return $this;
    }

    public function __toString()
    {
        return $this->nombre;
    }


}
