<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Stockapartado
 *
 * @ORM\Table(name="stockApartado", indexes={@ORM\Index(name="fk_stockApartado_stock1_idx", columns={"stock_idstock"}), @ORM\Index(name="fk_stockApartado_venta1_idx", columns={"venta_idventa"})})
 * @ORM\Entity
 */
class Stockapartado
{
    /**
     * @var int
     *
     * @ORM\Column(name="idstockApartado", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idstockapartado;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="fechaApartado", type="datetime", nullable=false)
     */
    private $fechaapartado;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="actualizacion", type="datetime", nullable=true)
     */
    private $actualizacion;

    /**
     * @var int
     *
     * @ORM\Column(name="cantidad", type="integer", nullable=false)
     */
    private $cantidad;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechasDesapartado", type="datetime", nullable=true)
     */
    private $fechasdesapartado;

    /**
     * @var \Venta
     *
     * @ORM\ManyToOne(targetEntity="Venta")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="venta_idventa", referencedColumnName="idventa")
     * })
     */
    private $ventaIdventa;

    /**
     * @var \Stock
     *
     * @ORM\ManyToOne(targetEntity="Stock")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="stock_idstock", referencedColumnName="idstock")
     * })
     */
    private $stockIdstock;

    public function getIdstockapartado(): ?int
    {
        return $this->idstockapartado;
    }

    public function getFechaapartado(): ?\DateTimeInterface
    {
        return $this->fechaapartado;
    }

    public function setFechaapartado(\DateTimeInterface $fechaapartado): self
    {
        $this->fechaapartado = $fechaapartado;

        return $this;
    }

    public function getActualizacion(): ?\DateTimeInterface
    {
        return $this->actualizacion;
    }

    public function setActualizacion(?\DateTimeInterface $actualizacion): self
    {
        $this->actualizacion = $actualizacion;

        return $this;
    }

    public function getCantidad(): ?int
    {
        return $this->cantidad;
    }

    public function setCantidad(int $cantidad): self
    {
        $this->cantidad = $cantidad;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getFechasdesapartado(): ?\DateTimeInterface
    {
        return $this->fechasdesapartado;
    }

    public function setFechasdesapartado(?\DateTimeInterface $fechasdesapartado): self
    {
        $this->fechasdesapartado = $fechasdesapartado;

        return $this;
    }

    public function getVentaIdventa(): ?Venta
    {
        return $this->ventaIdventa;
    }

    public function setVentaIdventa(?Venta $ventaIdventa): self
    {
        $this->ventaIdventa = $ventaIdventa;

        return $this;
    }

    public function getStockIdstock(): ?Stock
    {
        return $this->stockIdstock;
    }

    public function setStockIdstock(?Stock $stockIdstock): self
    {
        $this->stockIdstock = $stockIdstock;

        return $this;
    }


}
