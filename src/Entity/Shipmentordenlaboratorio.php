<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Shipmentordenlaboratorio
 *
 * @ORM\Table(name="shipmentOrdenLaboratorio", indexes={@ORM\Index(name="fk_shipmentOrdenLaboratorio_ordenLaboratorio1_idx", columns={"ordenLaboratorio_idordenLaboratorio"}), @ORM\Index(name="fk_shipmentOrdenLaboratorio_shipment1_idx", columns={"shipment_idshipment"}), @ORM\Index(name="fk_shipmentOrdenLaboratorio_sucursal1_idx", columns={"destination"}), @ORM\Index(name="fk_shipmentOrdenLaboratorio_usuario1_idx", columns={"usuario_idusuario"})})
 * @ORM\Entity
 */
class Shipmentordenlaboratorio
{
    /**
     * @var int
     *
     * @ORM\Column(name="idshipmentOrdenLaboratorio", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idshipmentordenlaboratorio;

    /**
     * @var string
     *
     * @ORM\Column(name="accepted", type="string", length=1, nullable=false, options={"fixed"=true})
     */
    private $accepted;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="deliveryTime", type="datetime", nullable=true)
     */
    private $deliverytime;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="acceptationDate", type="datetime", nullable=true)
     */
    private $acceptationdate;

    /**
     * @var string|null
     *
     * @ORM\Column(name="delivered", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $delivered;

    /**
     * @var \Ordenlaboratorio
     *
     * @ORM\ManyToOne(targetEntity="Ordenlaboratorio")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="ordenLaboratorio_idordenLaboratorio", referencedColumnName="idordenLaboratorio")
     * })
     */
    private $ordenlaboratorioIdordenlaboratorio;

    /**
     * @var \Shipment
     *
     * @ORM\ManyToOne(targetEntity="Shipment")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="shipment_idshipment", referencedColumnName="idshipment")
     * })
     */
    private $shipmentIdshipment;

    /**
     * @var \Sucursal
     *
     * @ORM\ManyToOne(targetEntity="Sucursal")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="destination", referencedColumnName="idsucursal")
     * })
     */
    private $destination;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    public function getIdshipmentordenlaboratorio(): ?int
    {
        return $this->idshipmentordenlaboratorio;
    }

    public function getAccepted(): ?string
    {
        return $this->accepted;
    }

    public function setAccepted(string $accepted): self
    {
        $this->accepted = $accepted;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getDeliverytime(): ?\DateTimeInterface
    {
        return $this->deliverytime;
    }

    public function setDeliverytime(?\DateTimeInterface $deliverytime): self
    {
        $this->deliverytime = $deliverytime;

        return $this;
    }

    public function getAcceptationdate(): ?\DateTimeInterface
    {
        return $this->acceptationdate;
    }

    public function setAcceptationdate(?\DateTimeInterface $acceptationdate): self
    {
        $this->acceptationdate = $acceptationdate;

        return $this;
    }

    public function getDelivered(): ?string
    {
        return $this->delivered;
    }

    public function setDelivered(?string $delivered): self
    {
        $this->delivered = $delivered;

        return $this;
    }

    public function getOrdenlaboratorioIdordenlaboratorio(): ?Ordenlaboratorio
    {
        return $this->ordenlaboratorioIdordenlaboratorio;
    }

    public function setOrdenlaboratorioIdordenlaboratorio(?Ordenlaboratorio $ordenlaboratorioIdordenlaboratorio): self
    {
        $this->ordenlaboratorioIdordenlaboratorio = $ordenlaboratorioIdordenlaboratorio;

        return $this;
    }

    public function getShipmentIdshipment(): ?Shipment
    {
        return $this->shipmentIdshipment;
    }

    public function setShipmentIdshipment(?Shipment $shipmentIdshipment): self
    {
        $this->shipmentIdshipment = $shipmentIdshipment;

        return $this;
    }

    public function getDestination(): ?Sucursal
    {
        return $this->destination;
    }

    public function setDestination(?Sucursal $destination): self
    {
        $this->destination = $destination;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }


}
