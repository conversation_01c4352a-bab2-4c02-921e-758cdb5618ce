<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Proveedor
 *
 * @ORM\Table(name="proveedor", uniqueConstraints={@ORM\UniqueConstraint(name="rfc_UNIQUE", columns={"rfc"})}, indexes={@ORM\Index(name="fk_proveedor_areaProveedor1_idx", columns={"areaProveedor_idareaProveedor"})})
 * @ORM\Entity
 */
class Proveedor
{
    /**
     * @var int
     *
     * @ORM\Column(name="idproveedor", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idproveedor;

    /**
     * @var string
     *
     * @ORM\Column(name="nombre", type="string", length=45, nullable=false)
     */
    private $nombre;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="calle", type="string", length=250, nullable=true)
     */
    private $calle;

    /**
     * @var string|null
     *
     * @ORM\Column(name="calle2", type="string", length=250, nullable=true)
     */
    private $calle2;

    /**
     * @var string|null
     *
     * @ORM\Column(name="ciudad", type="string", length=250, nullable=true)
     */
    private $ciudad;

    /**
     * @var string|null
     *
     * @ORM\Column(name="estado", type="string", length=250, nullable=true)
     */
    private $estado;

    /**
     * @var string|null
     *
     * @ORM\Column(name="codigoPostal", type="string", length=50, nullable=true)
     */
    private $codigopostal;

    /**
     * @var string|null
     *
     * @ORM\Column(name="pais", type="string", length=250, nullable=true)
     */
    private $pais;

    /**
     * @var string|null
     *
     * @ORM\Column(name="telefono", type="string", length=45, nullable=true)
     */
    private $telefono;

    /**
     * @var string|null
     *
     * @ORM\Column(name="movil", type="string", length=45, nullable=true)
     */
    private $movil;

    /**
     * @var string|null
     *
     * @ORM\Column(name="correoElectronico", type="string", length=100, nullable=true)
     */
    private $correoelectronico;

    /**
     * @var string|null
     *
     * @ORM\Column(name="sitioweb", type="string", length=155, nullable=true)
     */
    private $sitioweb;

    /**
     * @var string|null
     *
     * @ORM\Column(name="notas", type="text", length=16777215, nullable=true)
     */
    private $notas;

    /**
     * @var string|null
     *
     * @ORM\Column(name="numeroExterior", type="string", length=45, nullable=true)
     */
    private $numeroexterior;

    /**
     * @var string|null
     *
     * @ORM\Column(name="numeroInterior", type="string", length=45, nullable=true)
     */
    private $numerointerior;

    /**
     * @var string|null
     *
     * @ORM\Column(name="colonia", type="string", length=500, nullable=true)
     */
    private $colonia;

    /**
     * @var string|null
     *
     * @ORM\Column(name="municipio", type="string", length=500, nullable=true)
     */
    private $municipio;

    /**
     * @var string|null
     *
     * @ORM\Column(name="rfc", type="string", length=45, nullable=true)
     */
    private $rfc;

    /**
     * @var string|null
     *
     * @ORM\Column(name="razonSocial", type="string", length=45, nullable=true)
     */
    private $razonsocial;

    /**
     * @var string|null
     *
     * @ORM\Column(name="sendGlasses", type="string", length=45, nullable=true)
     */
    private $sendglasses = '0';

    /**
     * @var \Areaproveedor
     *
     * @ORM\ManyToOne(targetEntity="Areaproveedor")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="areaProveedor_idareaProveedor", referencedColumnName="idareaProveedor")
     * })
     */
    private $areaproveedorIdareaproveedor;

    public function __toString(): string
    {
        return (string) $this->getNombre();
    }

    public function getIdproveedor(): ?int
    {
        return $this->idproveedor;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(string $nombre): self
    {
        $this->nombre = $nombre;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getCalle(): ?string
    {
        return $this->calle;
    }

    public function setCalle(?string $calle): self
    {
        $this->calle = $calle;

        return $this;
    }

    public function getCalle2(): ?string
    {
        return $this->calle2;
    }

    public function setCalle2(?string $calle2): self
    {
        $this->calle2 = $calle2;

        return $this;
    }

    public function getCiudad(): ?string
    {
        return $this->ciudad;
    }

    public function setCiudad(?string $ciudad): self
    {
        $this->ciudad = $ciudad;

        return $this;
    }

    public function getEstado(): ?string
    {
        return $this->estado;
    }

    public function setEstado(?string $estado): self
    {
        $this->estado = $estado;

        return $this;
    }

    public function getCodigopostal(): ?string
    {
        return $this->codigopostal;
    }

    public function setCodigopostal(?string $codigopostal): self
    {
        $this->codigopostal = $codigopostal;

        return $this;
    }

    public function getPais(): ?string
    {
        return $this->pais;
    }

    public function setPais(?string $pais): self
    {
        $this->pais = $pais;

        return $this;
    }

    public function getTelefono(): ?string
    {
        return $this->telefono;
    }

    public function setTelefono(?string $telefono): self
    {
        $this->telefono = $telefono;

        return $this;
    }

    public function getMovil(): ?string
    {
        return $this->movil;
    }

    public function setMovil(?string $movil): self
    {
        $this->movil = $movil;

        return $this;
    }

    public function getCorreoelectronico(): ?string
    {
        return $this->correoelectronico;
    }

    public function setCorreoelectronico(?string $correoelectronico): self
    {
        $this->correoelectronico = $correoelectronico;

        return $this;
    }

    public function getSitioweb(): ?string
    {
        return $this->sitioweb;
    }

    public function setSitioweb(?string $sitioweb): self
    {
        $this->sitioweb = $sitioweb;

        return $this;
    }

    public function getNotas(): ?string
    {
        return $this->notas;
    }

    public function setNotas(?string $notas): self
    {
        $this->notas = $notas;

        return $this;
    }

    public function getNumeroexterior(): ?string
    {
        return $this->numeroexterior;
    }

    public function setNumeroexterior(?string $numeroexterior): self
    {
        $this->numeroexterior = $numeroexterior;

        return $this;
    }

    public function getNumerointerior(): ?string
    {
        return $this->numerointerior;
    }

    public function setNumerointerior(?string $numerointerior): self
    {
        $this->numerointerior = $numerointerior;

        return $this;
    }

    public function getColonia(): ?string
    {
        return $this->colonia;
    }

    public function setColonia(?string $colonia): self
    {
        $this->colonia = $colonia;

        return $this;
    }

    public function getMunicipio(): ?string
    {
        return $this->municipio;
    }

    public function setMunicipio(?string $municipio): self
    {
        $this->municipio = $municipio;

        return $this;
    }

    public function getRfc(): ?string
    {
        return $this->rfc;
    }

    public function setRfc(?string $rfc): self
    {
        $this->rfc = $rfc;

        return $this;
    }

    public function getRazonsocial(): ?string
    {
        return $this->razonsocial;
    }

    public function setRazonsocial(?string $razonsocial): self
    {
        $this->razonsocial = $razonsocial;

        return $this;
    }

    public function getSendglasses(): ?string
    {
        return $this->sendglasses;
    }

    public function setSendglasses(?string $sendglasses): self
    {
        $this->sendglasses = $sendglasses;

        return $this;
    }

    public function getAreaproveedorIdareaproveedor(): ?Areaproveedor
    {
        return $this->areaproveedorIdareaproveedor;
    }

    public function setAreaproveedorIdareaproveedor(?Areaproveedor $areaproveedorIdareaproveedor): self
    {
        $this->areaproveedorIdareaproveedor = $areaproveedorIdareaproveedor;

        return $this;
    }


}
