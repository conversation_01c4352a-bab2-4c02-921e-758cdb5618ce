<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Tipoventa
 *
 * @ORM\Table(name="tipoVenta", indexes={@ORM\Index(name="fk_tipoVenta_empresa1_idx", columns={"empresa_idempresa"}), @ORM\Index(name="fk_tipoVenta_paymentType1_idx", columns={"paymentType_idpaymentType"})})
 * @ORM\Entity
 */
class Tipoventa
{
    /**
     * @var int
     *
     * @ORM\Column(name="idtipoVenta", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idtipoventa;

    /**
     * @var string|null
     *
     * @ORM\Column(name="nombre", type="string", length=350, nullable=true)
     */
    private $nombre;

    /**
     * @var string|null
     *
     * @ORM\Column(name="apartarArmazon", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $apartararmazon;

    /**
     * @var string|null
     *
     * @ORM\Column(name="nota", type="text", length=65535, nullable=true)
     */
    private $nota;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var float|null
     *
     * @ORM\Column(name="descuentoProductosAlmacenables", type="float", precision=10, scale=0, nullable=true)
     */
    private $descuentoproductosalmacenables;

    /**
     * @var float|null
     *
     * @ORM\Column(name="descuentoServicios", type="float", precision=10, scale=0, nullable=true)
     */
    private $descuentoservicios;

    /**
     * @var string|null
     *
     * @ORM\Column(name="mostrarTopesAutorizados", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $mostrartopesautorizados;

    /**
     * @var string|null
     *
     * @ORM\Column(name="mostrarDetallePrecio", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $mostrardetalleprecio;

    /**
     * @var string|null
     *
     * @ORM\Column(name="notaGarantia", type="text", length=65535, nullable=true)
     */
    private $notagarantia;

    /**
     * @var string|null
     *
     * @ORM\Column(name="mostrarNumeroEmpleado", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $mostrarnumeroempleado;

    /**
     * @var string|null
     *
     * @ORM\Column(name="mostrarUnidadProcedencia", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $mostrarunidadprocedencia;

    /**
     * @var string|null
     *
     * @ORM\Column(name="mostrarBeneficiario", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $mostrarbeneficiario;

    /**
     * @var string|null
     *
     * @ORM\Column(name="precioFijoProductosAlmacenables", type="string", length=45, nullable=true)
     */
    private $preciofijoproductosalmacenables;

    /**
     * @var string|null
     *
     * @ORM\Column(name="precioFijoServicios", type="string", length=45, nullable=true)
     */
    private $preciofijoservicios;

    /**
     * @var string|null
     *
     * @ORM\Column(name="textoTopesAutorizados", type="text", length=65535, nullable=true)
     */
    private $textotopesautorizados;

    /**
     * @var string|null
     *
     * @ORM\Column(name="totalVentaConIva", type="decimal", precision=9, scale=2, nullable=true)
     */
    private $totalventaconiva;

    /**
     * @var string|null
     *
     * @ORM\Column(name="documentoObligatorioCerrarVenta", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $documentoobligatoriocerrarventa;

    /**
     * @var string|null
     *
     * @ORM\Column(name="mostrarCampoGarantia", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $mostrarcampogarantia;

    /**
     * @var string|null
     *
     * @ORM\Column(name="subirDocumentoParaCerrarVenta", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $subirdocumentoparacerrarventa;

    /**
     * @var string|null
     *
     * @ORM\Column(name="precioBase", type="string", length=50, nullable=true, options={"fixed"=true})
     */
    private $preciobase;

    /**
     * @var int
     *
     * @ORM\Column(name="diasCredito", type="integer", nullable=false, options={"unsigned"=true})
     */
    private $diascredito;

    /**
     * @var string
     *
     * @ORM\Column(name="onlineStore", type="string", length=1, nullable=false, options={"fixed"=true})
     */
    private $onlinestore = '0';

    /**
     * @var string
     *
     * @ORM\Column(name="customerBilling", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $customerbilling = '1';

    /**
     * @var array|null
     *
     * @ORM\Column(name="ventaMsgArray", type="json", nullable=true)
     */
    private $ventamsgarray;

    /**
     * @var string|null
     *
     * @ORM\Column(name="fixProducts", type="text", length=65535, nullable=true)
     */
    private $fixproducts;

    /**
     * @var string|null
     *
     * @ORM\Column(name="pagoAlFinal", type="string", length=1, nullable=true)
     */
    private $pagoalfinal = '0';

    /**
     * @var \Empresa
     *
     * @ORM\ManyToOne(targetEntity="Empresa")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="empresa_idempresa", referencedColumnName="idempresa")
     * })
     */
    private $empresaIdempresa;

    /**
     * @var \Paymenttype
     *
     * @ORM\ManyToOne(targetEntity="Paymenttype")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="paymentType_idpaymentType", referencedColumnName="idpaymentType")
     * })
     */
    private $paymenttypeIdpaymenttype;

    public function getIdtipoventa(): ?int
    {
        return $this->idtipoventa;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(?string $nombre): self
    {
        $this->nombre = $nombre;

        return $this;
    }

    public function getApartararmazon(): ?string
    {
        return $this->apartararmazon;
    }

    public function setApartararmazon(?string $apartararmazon): self
    {
        $this->apartararmazon = $apartararmazon;

        return $this;
    }

    public function getNota(): ?string
    {
        return $this->nota;
    }

    public function setNota(?string $nota): self
    {
        $this->nota = $nota;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getDescuentoproductosalmacenables(): ?float
    {
        return $this->descuentoproductosalmacenables;
    }

    public function setDescuentoproductosalmacenables(?float $descuentoproductosalmacenables): self
    {
        $this->descuentoproductosalmacenables = $descuentoproductosalmacenables;

        return $this;
    }

    public function getDescuentoservicios(): ?float
    {
        return $this->descuentoservicios;
    }

    public function setDescuentoservicios(?float $descuentoservicios): self
    {
        $this->descuentoservicios = $descuentoservicios;

        return $this;
    }

    public function getMostrartopesautorizados(): ?string
    {
        return $this->mostrartopesautorizados;
    }

    public function setMostrartopesautorizados(?string $mostrartopesautorizados): self
    {
        $this->mostrartopesautorizados = $mostrartopesautorizados;

        return $this;
    }

    public function getMostrardetalleprecio(): ?string
    {
        return $this->mostrardetalleprecio;
    }

    public function setMostrardetalleprecio(?string $mostrardetalleprecio): self
    {
        $this->mostrardetalleprecio = $mostrardetalleprecio;

        return $this;
    }

    public function getNotagarantia(): ?string
    {
        return $this->notagarantia;
    }

    public function setNotagarantia(?string $notagarantia): self
    {
        $this->notagarantia = $notagarantia;

        return $this;
    }

    public function getMostrarnumeroempleado(): ?string
    {
        return $this->mostrarnumeroempleado;
    }

    public function setMostrarnumeroempleado(?string $mostrarnumeroempleado): self
    {
        $this->mostrarnumeroempleado = $mostrarnumeroempleado;

        return $this;
    }

    public function getMostrarunidadprocedencia(): ?string
    {
        return $this->mostrarunidadprocedencia;
    }

    public function setMostrarunidadprocedencia(?string $mostrarunidadprocedencia): self
    {
        $this->mostrarunidadprocedencia = $mostrarunidadprocedencia;

        return $this;
    }

    public function getMostrarbeneficiario(): ?string
    {
        return $this->mostrarbeneficiario;
    }

    public function setMostrarbeneficiario(?string $mostrarbeneficiario): self
    {
        $this->mostrarbeneficiario = $mostrarbeneficiario;

        return $this;
    }

    public function getPreciofijoproductosalmacenables(): ?string
    {
        return $this->preciofijoproductosalmacenables;
    }

    public function setPreciofijoproductosalmacenables(?string $preciofijoproductosalmacenables): self
    {
        $this->preciofijoproductosalmacenables = $preciofijoproductosalmacenables;

        return $this;
    }

    public function getPreciofijoservicios(): ?string
    {
        return $this->preciofijoservicios;
    }

    public function setPreciofijoservicios(?string $preciofijoservicios): self
    {
        $this->preciofijoservicios = $preciofijoservicios;

        return $this;
    }

    public function getTextotopesautorizados(): ?string
    {
        return $this->textotopesautorizados;
    }

    public function setTextotopesautorizados(?string $textotopesautorizados): self
    {
        $this->textotopesautorizados = $textotopesautorizados;

        return $this;
    }

    public function getTotalventaconiva(): ?string
    {
        return $this->totalventaconiva;
    }

    public function setTotalventaconiva(?string $totalventaconiva): self
    {
        $this->totalventaconiva = $totalventaconiva;

        return $this;
    }

    public function getDocumentoobligatoriocerrarventa(): ?string
    {
        return $this->documentoobligatoriocerrarventa;
    }

    public function setDocumentoobligatoriocerrarventa(?string $documentoobligatoriocerrarventa): self
    {
        $this->documentoobligatoriocerrarventa = $documentoobligatoriocerrarventa;

        return $this;
    }

    public function getMostrarcampogarantia(): ?string
    {
        return $this->mostrarcampogarantia;
    }

    public function setMostrarcampogarantia(?string $mostrarcampogarantia): self
    {
        $this->mostrarcampogarantia = $mostrarcampogarantia;

        return $this;
    }

    public function getSubirdocumentoparacerrarventa(): ?string
    {
        return $this->subirdocumentoparacerrarventa;
    }

    public function setSubirdocumentoparacerrarventa(?string $subirdocumentoparacerrarventa): self
    {
        $this->subirdocumentoparacerrarventa = $subirdocumentoparacerrarventa;

        return $this;
    }

    public function getPreciobase(): ?string
    {
        return $this->preciobase;
    }

    public function setPreciobase(?string $preciobase): self
    {
        $this->preciobase = $preciobase;

        return $this;
    }

    public function getDiascredito(): ?int
    {
        return $this->diascredito;
    }

    public function setDiascredito(int $diascredito): self
    {
        $this->diascredito = $diascredito;

        return $this;
    }

    public function getOnlinestore(): ?string
    {
        return $this->onlinestore;
    }

    public function setOnlinestore(string $onlinestore): self
    {
        $this->onlinestore = $onlinestore;

        return $this;
    }

    public function getCustomerbilling(): ?string
    {
        return $this->customerbilling;
    }

    public function setCustomerbilling(string $customerbilling): self
    {
        $this->customerbilling = $customerbilling;

        return $this;
    }

    public function getVentamsgarray(): ?array
    {
        return $this->ventamsgarray;
    }

    public function setVentamsgarray(?array $ventamsgarray): self
    {
        $this->ventamsgarray = $ventamsgarray;

        return $this;
    }

    public function getFixproducts(): ?string
    {
        return $this->fixproducts;
    }

    public function setFixproducts(?string $fixproducts): self
    {
        $this->fixproducts = $fixproducts;

        return $this;
    }

    public function getPagoalfinal(): ?string
    {
        return $this->pagoalfinal;
    }

    public function setPagoalfinal(?string $pagoalfinal): self
    {
        $this->pagoalfinal = $pagoalfinal;

        return $this;
    }

    public function getEmpresaIdempresa(): ?Empresa
    {
        return $this->empresaIdempresa;
    }

    public function setEmpresaIdempresa(?Empresa $empresaIdempresa): self
    {
        $this->empresaIdempresa = $empresaIdempresa;

        return $this;
    }

    public function getPaymenttypeIdpaymenttype(): ?Paymenttype
    {
        return $this->paymenttypeIdpaymenttype;
    }

    public function setPaymenttypeIdpaymenttype(?Paymenttype $paymenttypeIdpaymenttype): self
    {
        $this->paymenttypeIdpaymenttype = $paymenttypeIdpaymenttype;

        return $this;
    }
    function  __toString()
    {
        return $this->getNombre();
    }


}
