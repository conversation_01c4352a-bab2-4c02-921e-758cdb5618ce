<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Productopaquete
 *
 * @ORM\Table(name="productoPaquete", indexes={@ORM\Index(name="fk_productoPaquete_paquetes1_idx", columns={"paquete_idpaquete"}), @ORM\Index(name="fk_productoPaquete_producto1_idx", columns={"producto_idproducto"})})
 * @ORM\Entity
 */
class Productopaquete
{
    /**
     * @var int
     *
     * @ORM\Column(name="idproductoPaquete", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idproductopaquete;

    /**
     * @var string|null
     *
     * @ORM\Column(name="precio", type="string", length=45, nullable=true)
     */
    private $precio;

    /**
     * @var \Producto
     *
     * @ORM\ManyToOne(targetEntity="Producto")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="producto_idproducto", referencedColumnName="idproducto")
     * })
     */
    private $productoIdproducto;

    /**
     * @var \Paquete
     *
     * @ORM\ManyToOne(targetEntity="Paquete")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="paquete_idpaquete", referencedColumnName="idpaquete")
     * })
     */
    private $paqueteIdpaquete;

    public function getIdproductopaquete(): ?int
    {
        return $this->idproductopaquete;
    }

    public function getPrecio(): ?string
    {
        return $this->precio;
    }

    public function setPrecio(?string $precio): self
    {
        $this->precio = $precio;

        return $this;
    }

    public function getProductoIdproducto(): ?Producto
    {
        return $this->productoIdproducto;
    }

    public function setProductoIdproducto(?Producto $productoIdproducto): self
    {
        $this->productoIdproducto = $productoIdproducto;

        return $this;
    }

    public function getPaqueteIdpaquete(): ?Paquete
    {
        return $this->paqueteIdpaquete;
    }

    public function setPaqueteIdpaquete(?Paquete $paqueteIdpaquete): self
    {
        $this->paqueteIdpaquete = $paqueteIdpaquete;

        return $this;
    }


}
