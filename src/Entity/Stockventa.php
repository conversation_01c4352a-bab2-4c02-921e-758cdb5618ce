<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Stockventa
 *
 * @ORM\Table(name="stockVenta", indexes={@ORM\Index(name="fk_stockVenta_venta1_idx", columns={"venta_idventa"}), @ORM\Index(name="fk_stockVenta_stock1_idx", columns={"stock_idstock"}), @ORM\Index(name="fk_stockVenta_stock2_idx", columns={"stock_garantia"}), @ORM\Index(name="fk_stockVenta_producto1_idx", columns={"producto_idproducto"}), @ORM\Index(name="fk_stockVenta_usuario1_idx", columns={"UsuarioResponsablecancelacion"})})
 * @ORM\Entity
 */
class Stockventa
{
    /**
     * @var int
     *
     * @ORM\Column(name="idstockVenta", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idstockventa;

    /**
     * @var float|null
     *
     * @ORM\Column(name="cantidad", type="float", precision=10, scale=0, nullable=true)
     */
    private $cantidad;

    /**
     * @var string|null
     *
     * @ORM\Column(name="precio", type="decimal", precision=9, scale=2, nullable=true)
     */
    private $precio;

    /**
     * @var string|null
     *
     * @ORM\Column(name="costo", type="decimal", precision=9, scale=2, nullable=true)
     */
    private $costo;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=true)
     */
    private $creacion;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="modificacion", type="datetime", nullable=true)
     */
    private $modificacion;

    /**
     * @var float|null
     *
     * @ORM\Column(name="porcentajeComision", type="float", precision=10, scale=0, nullable=true)
     */
    private $porcentajecomision;

    /**
     * @var string|null
     *
     * @ORM\Column(name="comision", type="decimal", precision=9, scale=2, nullable=true)
     */
    private $comision;

    /**
     * @var float|null
     *
     * @ORM\Column(name="porcentajeDescuento", type="float", precision=10, scale=0, nullable=true)
     */
    private $porcentajedescuento;

    /**
     * @var string|null
     *
     * @ORM\Column(name="precioFinal", type="decimal", precision=9, scale=2, nullable=true)
     */
    private $preciofinal;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="garantia", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $garantia;

    /**
     * @var string|null
     *
     * @ORM\Column(name="estaApartado", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $estaapartado;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaApartado", type="datetime", nullable=true)
     */
    private $fechaapartado;

    /**
     * @var string|null
     *
     * @ORM\Column(name="precioBase", type="string", length=45, nullable=true)
     */
    private $preciobase;

    /**
     * @var string|null
     *
     * @ORM\Column(name="fixProduct", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $fixproduct = '0';

    /**
     * @var string|null
     *
     * @ORM\Column(name="isOmittable", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $isomittable = '0';

    /**
     * @var \Stock
     *
     * @ORM\ManyToOne(targetEntity="Stock")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="stock_garantia", referencedColumnName="idstock")
     * })
     */
    private $stockGarantia;

    /**
     * @var \Venta
     *
     * @ORM\ManyToOne(targetEntity="Venta")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="venta_idventa", referencedColumnName="idventa")
     * })
     */
    private $ventaIdventa;

    /**
     * @var \Stock
     *
     * @ORM\ManyToOne(targetEntity="Stock")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="stock_idstock", referencedColumnName="idstock")
     * })
     */
    private $stockIdstock;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="UsuarioResponsablecancelacion", referencedColumnName="idusuario")
     * })
     */
    private $usuarioresponsablecancelacion;

    /**
     * @var \Producto
     *
     * @ORM\ManyToOne(targetEntity="Producto")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="producto_idproducto", referencedColumnName="idproducto")
     * })
     */
    private $productoIdproducto;

    public function getIdstockventa(): ?int
    {
        return $this->idstockventa;
    }

    public function getCantidad(): ?float
    {
        return $this->cantidad;
    }

    public function setCantidad(?float $cantidad): self
    {
        $this->cantidad = $cantidad;

        return $this;
    }

    public function getPrecio(): ?string
    {
        return $this->precio;
    }

    public function setPrecio(?string $precio): self
    {
        $this->precio = $precio;

        return $this;
    }

    public function getCosto(): ?string
    {
        return $this->costo;
    }

    public function setCosto(?string $costo): self
    {
        $this->costo = $costo;

        return $this;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(?\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getModificacion(): ?\DateTimeInterface
    {
        return $this->modificacion;
    }

    public function setModificacion(?\DateTimeInterface $modificacion): self
    {
        $this->modificacion = $modificacion;

        return $this;
    }

    public function getPorcentajecomision(): ?float
    {
        return $this->porcentajecomision;
    }

    public function setPorcentajecomision(?float $porcentajecomision): self
    {
        $this->porcentajecomision = $porcentajecomision;

        return $this;
    }

    public function getComision(): ?string
    {
        return $this->comision;
    }

    public function setComision(?string $comision): self
    {
        $this->comision = $comision;

        return $this;
    }

    public function getPorcentajedescuento(): ?float
    {
        return $this->porcentajedescuento;
    }

    public function setPorcentajedescuento(?float $porcentajedescuento): self
    {
        $this->porcentajedescuento = $porcentajedescuento;

        return $this;
    }

    public function getPreciofinal(): ?string
    {
        return $this->preciofinal;
    }

    public function setPreciofinal(?string $preciofinal): self
    {
        $this->preciofinal = $preciofinal;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getGarantia(): ?string
    {
        return $this->garantia;
    }

    public function setGarantia(?string $garantia): self
    {
        $this->garantia = $garantia;

        return $this;
    }

    public function getEstaapartado(): ?string
    {
        return $this->estaapartado;
    }

    public function setEstaapartado(?string $estaapartado): self
    {
        $this->estaapartado = $estaapartado;

        return $this;
    }

    public function getFechaapartado(): ?\DateTimeInterface
    {
        return $this->fechaapartado;
    }

    public function setFechaapartado(?\DateTimeInterface $fechaapartado): self
    {
        $this->fechaapartado = $fechaapartado;

        return $this;
    }

    public function getPreciobase(): ?string
    {
        return $this->preciobase;
    }

    public function setPreciobase(?string $preciobase): self
    {
        $this->preciobase = $preciobase;

        return $this;
    }

    public function getFixproduct(): ?string
    {
        return $this->fixproduct;
    }

    public function setFixproduct(?string $fixproduct): self
    {
        $this->fixproduct = $fixproduct;

        return $this;
    }

    public function getIsomittable(): ?string
    {
        return $this->isomittable;
    }

    public function setIsomittable(?string $isomittable): self
    {
        $this->isomittable = $isomittable;

        return $this;
    }

    public function getStockGarantia(): ?Stock
    {
        return $this->stockGarantia;
    }

    public function setStockGarantia(?Stock $stockGarantia): self
    {
        $this->stockGarantia = $stockGarantia;

        return $this;
    }

    public function getVentaIdventa(): ?Venta
    {
        return $this->ventaIdventa;
    }

    public function setVentaIdventa(?Venta $ventaIdventa): self
    {
        $this->ventaIdventa = $ventaIdventa;

        return $this;
    }

    public function getStockIdstock(): ?Stock
    {
        return $this->stockIdstock;
    }

    public function setStockIdstock(?Stock $stockIdstock): self
    {
        $this->stockIdstock = $stockIdstock;

        return $this;
    }

    public function getUsuarioresponsablecancelacion(): ?Usuario
    {
        return $this->usuarioresponsablecancelacion;
    }

    public function setUsuarioresponsablecancelacion(?Usuario $usuarioresponsablecancelacion): self
    {
        $this->usuarioresponsablecancelacion = $usuarioresponsablecancelacion;

        return $this;
    }

    public function getProductoIdproducto(): ?Producto
    {
        return $this->productoIdproducto;
    }

    public function setProductoIdproducto(?Producto $productoIdproducto): self
    {
        $this->productoIdproducto = $productoIdproducto;

        return $this;
    }

    function getGerente(){

        return $this->ventaIdventa->getGerente()->getNombre()." ".$this->ventaIdventa->getGerente()->getApellidopaterno()." ".$this->ventaIdventa->getGerente()->getApellidomaterno();
    }

    function getVendedorNombre(){

        return $this->ventaIdventa->getUsuarioIdusuario()->getNombre()." ".$this->ventaIdventa->getUsuarioIdusuario()->getApellidopaterno()." ".$this->ventaIdventa->getUsuarioIdusuario()->getApellidomaterno();
    }

    function getIva(){

        $porcentajeIva = $this->ventaIdventa->getPorcentajeiva();
        $iva = ($porcentajeIva * $this->getPreciofinal()) / (100 + $porcentajeIva);
        return $iva;
    }

    function precioNeto(){//precio sin iva

        $porcentajeIva = $this->ventaIdventa->getPorcentajeiva();
        $precioFinal = (100 * $this->getPreciofinal()) / (100 + $porcentajeIva);
        return $precioFinal;
    }

    function montoTotal() {
        $precioUnitario = $this -> getPreciofinal();
        $cantidad = $this -> getCantidad();
        $precioTotal = $precioUnitario * $cantidad;

        return $precioTotal;
    }

    public function getFormattedEstaApartado(){
        $getFormattedEstaApartado = ($this->estaapartado == '1') ? "Sí" : "No";
        return $getFormattedEstaApartado;
    }

    public function __toString() {
        $valor=$this->getStockIdstock()->getCodigobarras();
        if(!$valor){
            $valor="";
        }
        return $valor;
    }

    
}
