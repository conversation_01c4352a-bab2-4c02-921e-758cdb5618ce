<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Documentoventa
 *
 * @ORM\Table(name="documentoVenta", indexes={@ORM\Index(name="fk_documentoVenta_usuario1_idx", columns={"usuario_idusuario"}), @ORM\Index(name="fk_documentoVenta_venta1_idx", columns={"venta_idventa"})})
 * @ORM\Table(name="documentoVenta", indexes={@ORM\Index(name="fk_documentoVenta_venta1_idx", columns={"venta_idventa"}), @ORM\Index(name="fk_documentoVenta_usuario1_idx", columns={"usuario_idusuario"})})
 * @ORM\Entity
 */
class Documentoventa
{
    /**
     * @var int
     *
     * @ORM\Column(name="iddocumentoVenta", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $iddocumentoventa;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=false)
     */
    private $creacion;

    /**
     * @var string
     *
     * @ORM\Column(name="tipoDocumento", type="string", length=45, nullable=false)
     */
    private $tipodocumento;

    /**
     * @var string
     *
     * @ORM\Column(name="nombreDocumento", type="string", length=45, nullable=false)
     */
    private $nombredocumento;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    /**
     * @var \Venta
     *
     * @ORM\ManyToOne(targetEntity="Venta")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="venta_idventa", referencedColumnName="idventa")
     * })
     */
    private $ventaIdventa;

    public function getIddocumentoventa(): ?int
    {
        return $this->iddocumentoventa;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getTipodocumento(): ?string
    {
        return $this->tipodocumento;
    }

    public function setTipodocumento(string $tipodocumento): self
    {
        $this->tipodocumento = $tipodocumento;

        return $this;
    }

    public function getNombredocumento(): ?string
    {
        return $this->nombredocumento;
    }

    public function setNombredocumento(string $nombredocumento): self
    {
        $this->nombredocumento = $nombredocumento;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getVentaIdventa(): ?Venta
    {
        return $this->ventaIdventa;
    }

    public function setVentaIdventa(?Venta $ventaIdventa): self
    {
        $this->ventaIdventa = $ventaIdventa;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }


    public function findLatestByVentaAndTipo($idVenta)
    {
        return $this->createQueryBuilder('d')
            ->where('d.venta = :idVenta')
            ->andWhere('d.tipoDocumento = :tipo')
            ->setParameter('idVenta', $idVenta)
            ->setParameter('tipo', 'Autorización UAM')
            ->orderBy('d.id', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }
    

}
