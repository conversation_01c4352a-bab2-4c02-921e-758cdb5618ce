<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Salelog
 *
 * @ORM\Table(name="saleLog", indexes={@ORM\Index(name="fk_saleLog_authStage1_idx", columns={"authStage_idauthStage"}), @ORM\Index(name="fk_saleLog_usuario1_idx", columns={"usuario_idusuario"}), @ORM\Index(name="fk_saleLog_venta1_idx", columns={"venta_idventa"})})
 * @ORM\Entity
 */
class Salelog
{
    /**
     * @var int
     *
     * @ORM\Column(name="idsaleLog", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idsalelog;

    /**
     * @var string|null
     *
     * @ORM\Column(name="name", type="string", length=45, nullable=true)
     */
    private $name;

    /**
     * @var string|null
     *
     * @ORM\Column(name="comment", type="text", length=65535, nullable=true)
     */
    private $comment;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="date", type="datetime", nullable=false)
     */
    private $date;

    /**
     * @var string|null
     *
     * @ORM\Column(name="saleLogcol", type="string", length=45, nullable=true)
     */
    private $salelogcol;

    /**
     * @var \Authstage
     *
     * @ORM\ManyToOne(targetEntity="Authstage")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="authStage_idauthStage", referencedColumnName="idauthStage")
     * })
     */
    private $authstageIdauthstage;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    /**
     * @var \Venta
     *
     * @ORM\ManyToOne(targetEntity="Venta")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="venta_idventa", referencedColumnName="idventa")
     * })
     */
    private $ventaIdventa;

    public function getIdsalelog(): ?int
    {
        return $this->idsalelog;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getSalelogcol(): ?string
    {
        return $this->salelogcol;
    }

    public function setSalelogcol(?string $salelogcol): self
    {
        $this->salelogcol = $salelogcol;

        return $this;
    }

    public function getAuthstageIdauthstage(): ?Authstage
    {
        return $this->authstageIdauthstage;
    }

    public function setAuthstageIdauthstage(?Authstage $authstageIdauthstage): self
    {
        $this->authstageIdauthstage = $authstageIdauthstage;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }

    public function getVentaIdventa(): ?Venta
    {
        return $this->ventaIdventa;
    }

    public function setVentaIdventa(?Venta $ventaIdventa): self
    {
        $this->ventaIdventa = $ventaIdventa;

        return $this;
    }


}
