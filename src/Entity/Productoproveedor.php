<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Productoproveedor
 *
 * @ORM\Table(name="productoProveedor", indexes={@ORM\Index(name="fk_productoProveedor_producto1_idx", columns={"producto_idproducto"}), @ORM\Index(name="fk_productoProveedor_proveedor1_idx", columns={"proveedor_idproveedor"})})
 * @ORM\Entity
 */
class Productoproveedor
{
    /**
     * @var int
     *
     * @ORM\Column(name="idproductoProveedor", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idproductoproveedor;

    /**
     * @var int|null
     *
     * @ORM\Column(name="plazoEntrega", type="integer", nullable=true)
     */
    private $plazoentrega;

    /**
     * @var float|null
     *
     * @ORM\Column(name="cantidadMinima", type="float", precision=10, scale=0, nullable=true)
     */
    private $cantidadminima;

    /**
     * @var string|null
     *
     * @ORM\Column(name="precio", type="decimal", precision=9, scale=2, nullable=true)
     */
    private $precio;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \Producto
     *
     * @ORM\ManyToOne(targetEntity="Producto")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="producto_idproducto", referencedColumnName="idproducto")
     * })
     */
    private $productoIdproducto;

    /**
     * @var \Proveedor
     *
     * @ORM\ManyToOne(targetEntity="Proveedor")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="proveedor_idproveedor", referencedColumnName="idproveedor")
     * })
     */
    private $proveedorIdproveedor;

    public function getIdproductoproveedor(): ?int
    {
        return $this->idproductoproveedor;
    }

    public function getPlazoentrega(): ?int
    {
        return $this->plazoentrega;
    }

    public function setPlazoentrega(?int $plazoentrega): self
    {
        $this->plazoentrega = $plazoentrega;

        return $this;
    }

    public function getCantidadminima(): ?float
    {
        return $this->cantidadminima;
    }

    public function setCantidadminima(?float $cantidadminima): self
    {
        $this->cantidadminima = $cantidadminima;

        return $this;
    }

    public function getPrecio(): ?string
    {
        return $this->precio;
    }

    public function setPrecio(?string $precio): self
    {
        $this->precio = $precio;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getProductoIdproducto(): ?Producto
    {
        return $this->productoIdproducto;
    }

    public function setProductoIdproducto(?Producto $productoIdproducto): self
    {
        $this->productoIdproducto = $productoIdproducto;

        return $this;
    }

    public function getProveedorIdproveedor(): ?Proveedor
    {
        return $this->proveedorIdproveedor;
    }

    public function setProveedorIdproveedor(?Proveedor $proveedorIdproveedor): self
    {
        $this->proveedorIdproveedor = $proveedorIdproveedor;

        return $this;
    }


}
