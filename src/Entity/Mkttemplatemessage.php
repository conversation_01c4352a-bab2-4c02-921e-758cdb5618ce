<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Mkttemplatemessage
 *
 * @ORM\Table(name="mktTemplateMessage", indexes={@ORM\Index(name="fk_mktTemplateMessage_mktMessage1_idx", columns={"mktMessage_idmktMessage"}), @ORM\Index(name="fk_mktTemplateMessage_mktTemplate1_idx", columns={"mktTemplate_idmktTemplate"})})
 * @ORM\Entity
 */
class Mkttemplatemessage
{
    /**
     * @var int
     *
     * @ORM\Column(name="idmktTemplateMessage", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idmkttemplatemessage;

    /**
     * @var \Mktmessage
     *
     * @ORM\ManyToOne(targetEntity="Mktmessage")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="mktMessage_idmktMessage", referencedColumnName="idmktMessage")
     * })
     */
    private $mktmessageIdmktmessage;

    /**
     * @var \Mkttemplate
     *
     * @ORM\ManyToOne(targetEntity="Mkttemplate")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="mktTemplate_idmktTemplate", referencedColumnName="idmktTemplate")
     * })
     */
    private $mkttemplateIdmkttemplate;

    public function getIdmkttemplatemessage(): ?int
    {
        return $this->idmkttemplatemessage;
    }

    public function getMktmessageIdmktmessage(): ?Mktmessage
    {
        return $this->mktmessageIdmktmessage;
    }

    public function setMktmessageIdmktmessage(?Mktmessage $mktmessageIdmktmessage): self
    {
        $this->mktmessageIdmktmessage = $mktmessageIdmktmessage;

        return $this;
    }

    public function getMkttemplateIdmkttemplate(): ?Mkttemplate
    {
        return $this->mkttemplateIdmkttemplate;
    }

    public function setMkttemplateIdmkttemplate(?Mkttemplate $mkttemplateIdmkttemplate): self
    {
        $this->mkttemplateIdmkttemplate = $mkttemplateIdmkttemplate;

        return $this;
    }


}
