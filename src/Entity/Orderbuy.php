<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Orderbuy
 *
 * @ORM\Table(name="orderBuy", indexes={@ORM\Index(name="fk_orderBuy_proveedor1_idx", columns={"proveedor_idproveedor"}), @ORM\Index(name="fk_orderBuy_usuario1_idx", columns={"usuario_idusuario"})})
 * @ORM\Entity
 */
class Orderbuy
{
    /**
     * @var int
     *
     * @ORM\Column(name="idorderBuy", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idorderbuy;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="creation", type="datetime", nullable=false)
     */
    private $creation;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="update", type="datetime", nullable=false)
     */
    private $update;

    /**
     * @var string
     *
     * @ORM\Column(name="stage", type="string", length=300, nullable=false)
     */
    private $stage;

    /**
     * @var string|null
     *
     * @ORM\Column(name="invoiceDocument", type="string", length=300, nullable=true)
     */
    private $invoicedocument;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    /**
     * @var \Proveedor
     *
     * @ORM\ManyToOne(targetEntity="Proveedor")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="proveedor_idproveedor", referencedColumnName="idproveedor")
     * })
     */
    private $proveedorIdproveedor;

    public function getIdorderbuy(): ?int
    {
        return $this->idorderbuy;
    }

    public function getCreation(): ?\DateTimeInterface
    {
        return $this->creation;
    }

    public function setCreation(\DateTimeInterface $creation): self
    {
        $this->creation = $creation;

        return $this;
    }

    public function getUpdate(): ?\DateTimeInterface
    {
        return $this->update;
    }

    public function setUpdate(\DateTimeInterface $update): self
    {
        $this->update = $update;

        return $this;
    }

    public function getStage(): ?string
    {
        return $this->stage;
    }

    public function setStage(string $stage): self
    {
        $this->stage = $stage;

        return $this;
    }

    public function getInvoicedocument(): ?string
    {
        return $this->invoicedocument;
    }

    public function setInvoicedocument(?string $invoicedocument): self
    {
        $this->invoicedocument = $invoicedocument;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }

    public function getProveedorIdproveedor(): ?Proveedor
    {
        return $this->proveedorIdproveedor;
    }

    public function setProveedorIdproveedor(?Proveedor $proveedorIdproveedor): self
    {
        $this->proveedorIdproveedor = $proveedorIdproveedor;

        return $this;
    }


}
