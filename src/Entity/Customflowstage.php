<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Customflowstage
 *
 * @ORM\Table(name="customFlowStage", indexes={@ORM\Index(name="fk_customFlowStage_customFlow1_idx", columns={"customFlow_idcustomFlow"}), @ORM\Index(name="fk_customFlowStage_usuario1_idx", columns={"usuario_idusuario"})})
 * @ORM\Entity
 */
class Customflowstage
{
    /**
     * @var int
     *
     * @ORM\Column(name="idcustomFlowStage", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idcustomflowstage;

    /**
     * @var string|null
     *
     * @ORM\Column(name="name", type="string", length=300, nullable=true)
     */
    private $name;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="createdAt", type="datetime", nullable=false)
     */
    private $createdat;

    /**
     * @var int|null
     *
     * @ORM\Column(name="stageOrder", type="integer", nullable=true)
     */
    private $stageorder = '0';

    /**
     * @var string|null
     *
     * @ORM\Column(name="question", type="text", length=0, nullable=true)
     */
    private $question;

    /**
     * @var string|null
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=true, options={"default"="1"})
     */
    private $status = '1';

    /**
     * @var \Customflow
     *
     * @ORM\ManyToOne(targetEntity="Customflow")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="customFlow_idcustomFlow", referencedColumnName="idcustomFlow")
     * })
     */
    private $customflowIdcustomflow;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    public function getIdcustomflowstage(): ?int
    {
        return $this->idcustomflowstage;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getCreatedat(): ?\DateTimeInterface
    {
        return $this->createdat;
    }

    public function setCreatedat(\DateTimeInterface $createdat): self
    {
        $this->createdat = $createdat;

        return $this;
    }

    public function getStageorder(): ?int
    {
        return $this->stageorder;
    }

    public function setStageorder(?int $stageorder): self
    {
        $this->stageorder = $stageorder;

        return $this;
    }

    public function getQuestion(): ?string
    {
        return $this->question;
    }

    public function setQuestion(?string $question): self
    {
        $this->question = $question;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getCustomflowIdcustomflow(): ?Customflow
    {
        return $this->customflowIdcustomflow;
    }

    public function setCustomflowIdcustomflow(?Customflow $customflowIdcustomflow): self
    {
        $this->customflowIdcustomflow = $customflowIdcustomflow;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }


}
