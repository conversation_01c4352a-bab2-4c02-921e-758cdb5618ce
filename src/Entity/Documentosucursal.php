<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Documentosucursal
 *
 * @ORM\Table(name="documentoSucursal", indexes={@ORM\Index(name="fk_documentosSucursal_documentos1_idx", columns={"documentos_iddocumentos"}), @ORM\Index(name="fk_documentosSucursal_sucursal1_idx", columns={"sucursal_idsucursal"})})
 * @ORM\Entity
 */
class Documentosucursal
{
    /**
     * @var int
     *
     * @ORM\Column(name="iddocumentosSucursal", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $iddocumentossucursal;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=false)
     */
    private $creacion;

    /**
     * @var \Documentos
     *
     * @ORM\ManyToOne(targetEntity="Documentos")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="documentos_iddocumentos", referencedColumnName="iddocumentos")
     * })
     */
    private $documentosIddocumentos;

    /**
     * @var \Sucursal
     *
     * @ORM\ManyToOne(targetEntity="Sucursal")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="sucursal_idsucursal", referencedColumnName="idsucursal")
     * })
     */
    private $sucursalIdsucursal;

    public function getIddocumentossucursal(): ?int
    {
        return $this->iddocumentossucursal;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getDocumentosIddocumentos(): ?Documentos
    {
        return $this->documentosIddocumentos;
    }

    public function setDocumentosIddocumentos(?Documentos $documentosIddocumentos): self
    {
        $this->documentosIddocumentos = $documentosIddocumentos;

        return $this;
    }

    public function getSucursalIdsucursal(): ?Sucursal
    {
        return $this->sucursalIdsucursal;
    }

    public function setSucursalIdsucursal(?Sucursal $sucursalIdsucursal): self
    {
        $this->sucursalIdsucursal = $sucursalIdsucursal;

        return $this;
    }


}
