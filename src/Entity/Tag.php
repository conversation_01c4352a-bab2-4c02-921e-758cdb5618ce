<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Tag
 *
 * @ORM\Table(name="tag", indexes={@ORM\Index(name="fk_tag_empresa1_idx", columns={"empresa_idempresa"}), @ORM\Index(name="fk_tag_TagsGroup1_idx", columns={"TagsGroup_idTagsGroup"})})
 * @ORM\Entity
 */
class Tag
{
    /**
     * @var int
     *
     * @ORM\Column(name="idtag", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idtag;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=45, nullable=false)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \Empresa
     *
     * @ORM\ManyToOne(targetEntity="Empresa")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="empresa_idempresa", referencedColumnName="idempresa")
     * })
     */
    private $empresaIdempresa;

    /**
     * @var \Tagsgroup
     *
     * @ORM\ManyToOne(targetEntity="Tagsgroup")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="TagsGroup_idTagsGroup", referencedColumnName="idTagsGroup")
     * })
     */
    private $tagsgroupIdtagsgroup;

    public function getIdtag(): ?int
    {
        return $this->idtag;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getEmpresaIdempresa(): ?Empresa
    {
        return $this->empresaIdempresa;
    }

    public function setEmpresaIdempresa(?Empresa $empresaIdempresa): self
    {
        $this->empresaIdempresa = $empresaIdempresa;

        return $this;
    }

    public function getTagsgroupIdtagsgroup(): ?Tagsgroup
    {
        return $this->tagsgroupIdtagsgroup;
    }

    public function setTagsgroupIdtagsgroup(?Tagsgroup $tagsgroupIdtagsgroup): self
    {
        $this->tagsgroupIdtagsgroup = $tagsgroupIdtagsgroup;

        return $this;
    }


}
