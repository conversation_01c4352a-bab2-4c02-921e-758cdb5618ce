<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Ordencompraitem
 *
 * @ORM\Table(name="ordenCompraItem", indexes={@ORM\Index(name="fk_ordenCompraItem_orderBuy1_idx", columns={"orderBuy_idorderBuy"}), @ORM\Index(name="fk_ordenCompraItem_stockVentaOrdenLaboratorio1_idx", columns={"stockVentaOrdenLaboratorio_idstockVentaOrdenLaboratorio"})})
 * @ORM\Entity
 */
class Ordencompraitem
{
    /**
     * @var int
     *
     * @ORM\Column(name="idordenCompraItem", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idordencompraitem;

    /**
     * @var \Stockventaordenlaboratorio
     *
     * @ORM\ManyToOne(targetEntity="Stockventaordenlaboratorio")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="stockVentaOrdenLaboratorio_idstockVentaOrdenLaboratorio", referencedColumnName="idstockVentaOrdenLaboratorio")
     * })
     */
    private $stockventaordenlaboratorioIdstockventaordenlaboratorio;

    /**
     * @var \Orderbuy
     *
     * @ORM\ManyToOne(targetEntity="Orderbuy")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="orderBuy_idorderBuy", referencedColumnName="idorderBuy")
     * })
     */
    private $orderbuyIdorderbuy;

    public function getIdordencompraitem(): ?int
    {
        return $this->idordencompraitem;
    }

    public function getStockventaordenlaboratorioIdstockventaordenlaboratorio(): ?Stockventaordenlaboratorio
    {
        return $this->stockventaordenlaboratorioIdstockventaordenlaboratorio;
    }

    public function setStockventaordenlaboratorioIdstockventaordenlaboratorio(?Stockventaordenlaboratorio $stockventaordenlaboratorioIdstockventaordenlaboratorio): self
    {
        $this->stockventaordenlaboratorioIdstockventaordenlaboratorio = $stockventaordenlaboratorioIdstockventaordenlaboratorio;

        return $this;
    }

    public function getOrderbuyIdorderbuy(): ?Orderbuy
    {
        return $this->orderbuyIdorderbuy;
    }

    public function setOrderbuyIdorderbuy(?Orderbuy $orderbuyIdorderbuy): self
    {
        $this->orderbuyIdorderbuy = $orderbuyIdorderbuy;

        return $this;
    }


}
