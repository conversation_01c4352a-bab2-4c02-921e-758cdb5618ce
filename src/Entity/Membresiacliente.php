<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Membresiacliente
 *
 * @ORM\Table(name="membresiaCliente", indexes={@ORM\Index(name="fk_membresiaCliente_cliente1_idx", columns={"cliente_idcliente"}), @ORM\Index(name="fk_membresiaCliente_membresia1_idx", columns={"membresia_idmembresia"})})
 * @ORM\Entity
 */
class Membresiacliente
{
    /**
     * @var int
     *
     * @ORM\Column(name="idmembresiaCliente", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idmembresiacliente;

    /**
     * @var string|null
     *
     * @ORM\Column(name="descuento", type="string", length=45, nullable=true)
     */
    private $descuento;

    /**
     * @var string|null
     *
     * @ORM\Column(name="montoDescuento", type="string", length=45, nullable=true)
     */
    private $montodescuento;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \Cliente
     *
     * @ORM\ManyToOne(targetEntity="Cliente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="cliente_idcliente", referencedColumnName="idcliente")
     * })
     */
    private $clienteIdcliente;

    /**
     * @var \Membresia
     *
     * @ORM\ManyToOne(targetEntity="Membresia")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="membresia_idmembresia", referencedColumnName="idmembresia")
     * })
     */
    private $membresiaIdmembresia;

    public function getIdmembresiacliente(): ?int
    {
        return $this->idmembresiacliente;
    }

    public function getDescuento(): ?string
    {
        return $this->descuento;
    }

    public function setDescuento(?string $descuento): self
    {
        $this->descuento = $descuento;

        return $this;
    }

    public function getMontodescuento(): ?string
    {
        return $this->montodescuento;
    }

    public function setMontodescuento(?string $montodescuento): self
    {
        $this->montodescuento = $montodescuento;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getClienteIdcliente(): ?Cliente
    {
        return $this->clienteIdcliente;
    }

    public function setClienteIdcliente(?Cliente $clienteIdcliente): self
    {
        $this->clienteIdcliente = $clienteIdcliente;

        return $this;
    }

    public function getMembresiaIdmembresia(): ?Membresia
    {
        return $this->membresiaIdmembresia;
    }

    public function setMembresiaIdmembresia(?Membresia $membresiaIdmembresia): self
    {
        $this->membresiaIdmembresia = $membresiaIdmembresia;

        return $this;
    }


}
