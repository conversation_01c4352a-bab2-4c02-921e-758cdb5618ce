<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Mkttemplatelist
 *
 * @ORM\Table(name="mktTemplateList", indexes={@ORM\Index(name="fk_mktTemplateList_mktList1_idx", columns={"mktList_idmktList"}), @ORM\Index(name="fk_mktTemplateList_mktTemplate1_idx", columns={"mktTemplate_idmktTemplate"})})
 * @ORM\Entity
 */
class Mkttemplatelist
{
    /**
     * @var int
     *
     * @ORM\Column(name="idmktTemplateList", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idmkttemplatelist;

    /**
     * @var \Mktlist
     *
     * @ORM\ManyToOne(targetEntity="Mktlist")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="mktList_idmktList", referencedColumnName="idmktList")
     * })
     */
    private $mktlistIdmktlist;

    /**
     * @var \Mkttemplate
     *
     * @ORM\ManyToOne(targetEntity="Mkttemplate")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="mktTemplate_idmktTemplate", referencedColumnName="idmktTemplate")
     * })
     */
    private $mkttemplateIdmkttemplate;

    public function getIdmkttemplatelist(): ?int
    {
        return $this->idmkttemplatelist;
    }

    public function getMktlistIdmktlist(): ?Mktlist
    {
        return $this->mktlistIdmktlist;
    }

    public function setMktlistIdmktlist(?Mktlist $mktlistIdmktlist): self
    {
        $this->mktlistIdmktlist = $mktlistIdmktlist;

        return $this;
    }

    public function getMkttemplateIdmkttemplate(): ?Mkttemplate
    {
        return $this->mkttemplateIdmkttemplate;
    }

    public function setMkttemplateIdmkttemplate(?Mkttemplate $mkttemplateIdmkttemplate): self
    {
        $this->mkttemplateIdmkttemplate = $mkttemplateIdmkttemplate;

        return $this;
    }


}
