<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Proveedortelefono
 *
 * @ORM\Table(name="proveedorTelefono", indexes={@ORM\Index(name="fk_proveedorTelefono_proveedorContacto1_idx", columns={"proveedorContacto_idproveedorContacto"})})
 * @ORM\Entity
 */
class Proveedortelefono
{
    /**
     * @var int
     *
     * @ORM\Column(name="idproveedorTelefono", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idproveedortelefono;

    /**
     * @var string
     *
     * @ORM\Column(name="telefono", type="string", length=200, nullable=false)
     */
    private $telefono;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=true)
     */
    private $creacion;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="actualizacion", type="datetime", nullable=true)
     */
    private $actualizacion;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="nombre", type="string", length=50, nullable=true, options={"fixed"=true})
     */
    private $nombre;

    /**
     * @var \Proveedorcontacto
     *
     * @ORM\ManyToOne(targetEntity="Proveedorcontacto")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="proveedorContacto_idproveedorContacto", referencedColumnName="idproveedorContacto")
     * })
     */
    private $proveedorcontactoIdproveedorcontacto;

    public function getIdproveedortelefono(): ?int
    {
        return $this->idproveedortelefono;
    }

    public function getTelefono(): ?string
    {
        return $this->telefono;
    }

    public function setTelefono(string $telefono): self
    {
        $this->telefono = $telefono;

        return $this;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(?\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getActualizacion(): ?\DateTimeInterface
    {
        return $this->actualizacion;
    }

    public function setActualizacion(?\DateTimeInterface $actualizacion): self
    {
        $this->actualizacion = $actualizacion;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(?string $nombre): self
    {
        $this->nombre = $nombre;

        return $this;
    }

    public function getProveedorcontactoIdproveedorcontacto(): ?Proveedorcontacto
    {
        return $this->proveedorcontactoIdproveedorcontacto;
    }

    public function setProveedorcontactoIdproveedorcontacto(?Proveedorcontacto $proveedorcontactoIdproveedorcontacto): self
    {
        $this->proveedorcontactoIdproveedorcontacto = $proveedorcontactoIdproveedorcontacto;

        return $this;
    }


}
