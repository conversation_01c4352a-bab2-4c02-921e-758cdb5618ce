<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * Empresa
 *
 * @ORM\Table(name="empresa")
 * @ORM\Entity
 */
class Empresa
{
    /**
     * @var int
     *
     * @ORM\Column(name="idempresa", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idempresa;

    /**
     * @var string
     *
     * @ORM\Column(name="nombre", type="string", length=250, nullable=false)
     */
    private $nombre;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="logoImagen", type="string", length=45, nullable=true)
     */
    private $logoimagen;

    /**
     * @var string|null
     *
     * @ORM\Column(name="logo64", type="text", length=0, nullable=true)
     */
    private $logo64;

    /**
     * @var string|null
     *
     * @ORM\Column(name="pieTicket", type="text", length=0, nullable=true)
     */
    private $pieticket;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaCreacion", type="datetime", nullable=true)
     */
    private $fechacreacion;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaActualizacion", type="datetime", nullable=true)
     */
    private $fechaactualizacion;

    /**
     * @var string|null
     *
     * @ORM\Column(name="imagenEmailFacturacion", type="string", length=45, nullable=true)
     */
    private $imagenemailfacturacion;

    /**
     * @var string|null
     *
     * @ORM\Column(name="prefijoTickets", type="string", length=45, nullable=true)
     */
    private $prefijotickets;

    /**
     * @var string|null
     *
     * @ORM\Column(name="razonSocial", type="string", length=45, nullable=true)
     */
    private $razonsocial;

    /**
     * @var string|null
     *
     * @ORM\Column(name="rfc", type="string", length=45, nullable=true)
     */
    private $rfc;

    /**
     * @var string|null
     *
     * @ORM\Column(name="emailFacturacion", type="string", length=150, nullable=true)
     */
    private $emailfacturacion;

    /**
     * @var string|null
     *
     * @ORM\Column(name="configuracionServidorCorreo", type="string", length=45, nullable=true)
     */
    private $configuracionservidorcorreo;

    /**
     * @var string
     *
     * @ORM\Column(name="ivaPercentage", type="decimal", precision=5, scale=2, nullable=false, options={"default"="0.00"})
     */
    private $ivapercentage = '0.00';

    public function getIdempresa(): ?int
    {
        return $this->idempresa;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(string $nombre): self
    {
        $this->nombre = $nombre;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getLogoimagen(): ?string
    {
        return $this->logoimagen;
    }

    public function setLogoimagen(?string $logoimagen): self
    {
        $this->logoimagen = $logoimagen;

        return $this;
    }

    public function getLogo64(): ?string
    {
        return $this->logo64;
    }

    public function setLogo64(?string $logo64): self
    {
        $this->logo64 = $logo64;

        return $this;
    }

    public function getPieticket(): ?string
    {
        return $this->pieticket;
    }

    public function setPieticket(?string $pieticket): self
    {
        $this->pieticket = $pieticket;

        return $this;
    }

    public function getFechacreacion(): ?\DateTimeInterface
    {
        return $this->fechacreacion;
    }

    public function setFechacreacion(?\DateTimeInterface $fechacreacion): self
    {
        $this->fechacreacion = $fechacreacion;

        return $this;
    }

    public function getFechaactualizacion(): ?\DateTimeInterface
    {
        return $this->fechaactualizacion;
    }

    public function setFechaactualizacion(?\DateTimeInterface $fechaactualizacion): self
    {
        $this->fechaactualizacion = $fechaactualizacion;

        return $this;
    }

    public function getImagenemailfacturacion(): ?string
    {
        return $this->imagenemailfacturacion;
    }

    public function setImagenemailfacturacion(?string $imagenemailfacturacion): self
    {
        $this->imagenemailfacturacion = $imagenemailfacturacion;

        return $this;
    }

    public function getPrefijotickets(): ?string
    {
        return $this->prefijotickets;
    }

    public function setPrefijotickets(?string $prefijotickets): self
    {
        $this->prefijotickets = $prefijotickets;

        return $this;
    }

    public function getRazonsocial(): ?string
    {
        return $this->razonsocial;
    }

    public function setRazonsocial(?string $razonsocial): self
    {
        $this->razonsocial = $razonsocial;

        return $this;
    }

    public function getRfc(): ?string
    {
        return $this->rfc;
    }

    public function setRfc(?string $rfc): self
    {
        $this->rfc = $rfc;

        return $this;
    }

    public function getEmailfacturacion(): ?string
    {
        return $this->emailfacturacion;
    }

    public function setEmailfacturacion(?string $emailfacturacion): self
    {
        $this->emailfacturacion = $emailfacturacion;

        return $this;
    }

    public function getConfiguracionservidorcorreo(): ?string
    {
        return $this->configuracionservidorcorreo;
    }

    public function setConfiguracionservidorcorreo(?string $configuracionservidorcorreo): self
    {
        $this->configuracionservidorcorreo = $configuracionservidorcorreo;

        return $this;
    }

    public function getIvapercentage(): ?string
    {
        return $this->ivapercentage;
    }

    public function setIvapercentage(string $ivapercentage): self
    {
        $this->ivapercentage = $ivapercentage;

        return $this;
    }

    public function __toString()
    {
        return $this->getNombre();
    }

    private ?UploadedFile $file = null;

    private ?UploadedFile $publicidad = null;

    const SERVER_PATH_TO_IMAGE_FOLDER = "/uploads/logos/";

    public function setFile(?UploadedFile $file = null): void
    {
        $this->file = $file;
    }

    public function getFile(): ?UploadedFile
    {
        return $this->file;
    }

    public function setPublicidad(?UploadedFile $publicidad = null): void
    {
        $this->publicidad = $publicidad;
    }

    public function getPublicidad(): ?UploadedFile
    {
        return $this->publicidad;
    }

    public function upload(string $tipo): void
    {

        // the file property can be empty if the field is not required
        if (null === $this->getFile() && $tipo == "logo") {
            return;
        }

        if (null === $this->getPublicidad() && $tipo == "publicidad") {
            return;
        }

        if ($tipo == "logo") $file = $this->getFile();
        else if ($tipo == "publicidad") $file = $this->getPublicidad();

        $originalFilename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $safeFilename = transliterator_transliterate('Any-Latin; Latin-ASCII; [^A-Za-z0-9_] remove; Lower()', $originalFilename);
        $fileName = $safeFilename.'-'.uniqid().'.'.$file->guessExtension();

        // we use the original file name here but you should
        // sanitize it at least to avoid any security issues

        // move takes the target directory and target filename as params

        if ($tipo == "logo"){

            $carpeta = "uploads/logos/";

            $this->getFile()->move($carpeta, $fileName);

            $this->setLogoimagen($fileName);

            $imagedata = file_get_contents($carpeta.$fileName);

            $base64 = base64_encode($imagedata);

            $ext = $file->getClientOriginalExtension();

            $this->setLogo64("data:image/".$ext.";base64,".$base64);

            $this->setFile(null);

        }
        else if ($tipo == "publicidad"){

            $carpeta = "uploads/publicidades/".$this->getNombre();

            $this->getPublicidad()->move($carpeta, $fileName);

            $this->setImagenemailfacturacion($fileName);
            $this->getPublicidad(null);

        }




        // set the path property to the filename where you've saved the file
        //$this->filename = $this->getFile()->getClientOriginalName();

        // clean up the file property as you won't need it anymore

    }



}
