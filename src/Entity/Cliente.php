<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Security\Core\User\UserInterface;


/**
 * Cliente
 *
 * @ORM\Table(name="cliente", indexes={@ORM\Index(name="fk_cliente_cliente1_idx", columns={"holder"}), @ORM\Index(name="fk_cliente_empresaCliente1_idx", columns={"empresaCliente_idempresaCliente"}), @ORM\Index(name="fk_cliente_sellreference1_idx", columns={"sellreference_idsellReference"}), @ORM\Index(name="fk_cliente_unidad1_idx", columns={"unidad_idunidad"})})
 * @ORM\Entity
 */

class Cliente implements UserInterface
{
    /**
     * @var int
     *
     * @ORM\Column(name="idcliente", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idcliente;

    /**
     * @var string
     *
     * @ORM\Column(name="nombre", type="string", length=500, nullable=false)
     */
    private $nombre;

    /**
     * @var string|null
     *
     * @ORM\Column(name="telefono", type="string", length=45, nullable=true)
     */
    private $telefono;

    /**
     * @var string|null
     *
     * @ORM\Column(name="email", type="string", length=45, nullable=true)
     */
    private $email;

    /**
     * @var string|null
     *
     * @ORM\Column(name="comentarios", type="text", length=16777215, nullable=true)
     */
    private $comentarios;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="iut", type="string", length=45, nullable=true)
     */
    private $iut;

    /**
     * @var string|null
     *
     * @ORM\Column(name="numeroTarjeta", type="string", length=45, nullable=true)
     */
    private $numerotarjeta;

    /**
     * @var string|null
     *
     * @ORM\Column(name="numeroEmpleado", type="string", length=45, nullable=true)
     */
    private $numeroempleado;

    /**
     * @var string|null
     *
     * @ORM\Column(name="tipoTarjeta", type="string", length=45, nullable=true)
     */
    private $tipotarjeta;

    /**
     * @var string|null
     *
     * @ORM\Column(name="tipoCliente", type="string", length=45, nullable=true, options={"default"="uam","comment"="puede ser uan o externo"})
     */
    private $tipocliente = 'uam';

    /**
     * @var string|null
     *
     * @ORM\Column(name="comoNosConocio", type="string", length=500, nullable=true)
     */
    private $comonosconocio;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaNacimiento", type="date", nullable=true)
     */
    private $fechanacimiento;

    /**
     * @var float|null
     *
     * @ORM\Column(name="edad", type="float", precision=10, scale=0, nullable=true)
     */
    private $edad;

    /**
     * @var string|null
     *
     * @ORM\Column(name="apellidoPaterno", type="string", length=45, nullable=true)
     */
    private $apellidopaterno;

    /**
     * @var string|null
     *
     * @ORM\Column(name="apellidoMaterno", type="string", length=45, nullable=true)
     */
    private $apellidomaterno;

    /**
     * @var string|null
     *
     * @ORM\Column(name="ocupacion", type="string", length=45, nullable=true)
     */
    private $ocupacion;

    /**
     * @var string|null
     *
     * @ORM\Column(name="genero", type="string", length=45, nullable=true)
     */
    private $genero;

    /**
     * @var string|null
     *
     * @ORM\Column(name="alcaldia", type="string", length=45, nullable=true)
     */
    private $alcaldia;

    /**
     * @var string|null
     *
     * @ORM\Column(name="tipoCalle", type="string", length=45, nullable=true)
     */
    private $tipocalle;

    /**
     * @var string|null
     *
     * @ORM\Column(name="calle", type="string", length=100, nullable=true)
     */
    private $calle;

    /**
     * @var string|null
     *
     * @ORM\Column(name="numero", type="string", length=45, nullable=true)
     */
    private $numero;

    /**
     * @var string|null
     *
     * @ORM\Column(name="codigoPostal", type="string", length=45, nullable=true)
     */
    private $codigopostal;

    /**
     * @var string|null
     *
     * @ORM\Column(name="colonia", type="string", length=50, nullable=true)
     */
    private $colonia;

    /**
     * @var string|null
     *
     * @ORM\Column(name="entidadFederativa", type="string", length=45, nullable=true)
     */
    private $entidadfederativa;

    /**
     * @var string|null
     *
     * @ORM\Column(name="localidad", type="string", length=45, nullable=true)
     */
    private $localidad;

    /**
     * @var int
     *
     * @ORM\Column(name="diasCredito", type="integer", nullable=false, options={"default"="30","unsigned"=true})
     */
    private $diascredito = 30;

    /**
     * @var string|null
     *
     * @ORM\Column(name="password", type="string", length=120, nullable=true)
     */
    private $password;

    /**
     * @var string|null
     *
     * @ORM\Column(name="role", type="string", length=45, nullable=true)
     */
    private $role;

    /**
     * @var bool|null
     *
     * @ORM\Column(name="isVerified", type="boolean", nullable=true)
     */
    private $isverified;

    /**
     * @var string|null
     *
     * @ORM\Column(name="groupClient", type="string", length=45, nullable=true)
     */
    private $groupclient;

    /**
     * @var string|null
     *
     * @ORM\Column(name="schoolYearSemester", type="string", length=45, nullable=true)
     */
    private $schoolyearsemester;

    /**
     * @var string|null
     *
     * @ORM\Column(name="scholarlId", type="string", length=45, nullable=true)
     */
    private $scholarlid;

    /**
     * @var string|null
     *
     * @ORM\Column(name="scholarity", type="string", length=45, nullable=true)
     */
    private $scholarity;

    /**
     * @var string|null
     *
     * @ORM\Column(name="schedule", type="string", length=45, nullable=true)
     */
    private $schedule;

    /**
     * @var string|null
     *
     * @ORM\Column(name="beneficiaryType", type="string", length=45, nullable=true)
     */
    private $beneficiarytype;

    /**
     * @var string
     *
     * @ORM\Column(name="authorizedCredit", type="decimal", precision=9, scale=2, nullable=false, options={"default"="0.00"})
     */
    private $authorizedcredit = '0.00';

    /**
     * @var string
     *
     * @ORM\Column(name="debt", type="decimal", precision=9, scale=2, nullable=false, options={"default"="0.00"})
     */
    private $debt = '0.00';

    /**
     * @var string|null
     *
     * @ORM\Column(name="doctorName", type="string", length=45, nullable=true)
     */
    private $doctorname;

    /**
     * @var string|null
     *
     * @ORM\Column(name="googleAuthToken", type="string", length=300, nullable=true)
     */
    private $googleauthtoken;

    /**
     * @var string|null
     *
     * @ORM\Column(name="hostedDomain", type="string", length=300, nullable=true)
     */
    private $hosteddomain;

    /**
     * @var string
     *
     * @ORM\Column(name="balanceInFavor", type="decimal", precision=9, scale=2, nullable=false, options={"default"="0.00"})
     */
    private $balanceinfavor = '0.00';

    /**
     * @var string
     *
     * @ORM\Column(name="isStudent", type="string", length=1, nullable=false, options={"fixed"=true})
     */
    private $isstudent = '0';

    /**
     * @var \Cliente
     *
     * @ORM\ManyToOne(targetEntity="Cliente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="holder", referencedColumnName="idcliente")
     * })
     */
    private $holder;

    /**
     * @var \Sellreference
     *
     * @ORM\ManyToOne(targetEntity="Sellreference")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="sellreference_idsellReference", referencedColumnName="idsellReference")
     * })
     */
    private $sellreferenceIdsellreference;

    /**
     * @var \Unidad
     *
     * @ORM\ManyToOne(targetEntity="Unidad")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="unidad_idunidad", referencedColumnName="idunidad")
     * })
     */
    private $unidadIdunidad;

    /**
     * @var \Empresacliente
     *
     * @ORM\ManyToOne(targetEntity="Empresacliente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="empresaCliente_idempresaCliente", referencedColumnName="idempresaCliente")
     * })
     */
    private $empresaclienteIdempresacliente;

    public function getIdcliente(): ?int
    {
        return $this->idcliente;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(string $nombre): self
    {
        $this->nombre = $nombre;

        return $this;
    }

    public function getTelefono(): ?string
    {
        return $this->telefono;
    }

    public function setTelefono(?string $telefono): self
    {
        $this->telefono = $telefono;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getComentarios(): ?string
    {
        return $this->comentarios;
    }

    public function setComentarios(?string $comentarios): self
    {
        $this->comentarios = $comentarios;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getIut(): ?string
    {
        return $this->iut;
    }

    public function setIut(?string $iut): self
    {
        $this->iut = $iut;

        return $this;
    }

    public function getNumerotarjeta(): ?string
    {
        return $this->numerotarjeta;
    }

    public function setNumerotarjeta(?string $numerotarjeta): self
    {
        $this->numerotarjeta = $numerotarjeta;

        return $this;
    }

    public function getNumeroempleado(): ?string
    {
        return $this->numeroempleado;
    }

    public function setNumeroempleado(?string $numeroempleado): self
    {
        $this->numeroempleado = $numeroempleado;

        return $this;
    }

    public function getTipotarjeta(): ?string
    {
        return $this->tipotarjeta;
    }

    public function setTipotarjeta(?string $tipotarjeta): self
    {
        $this->tipotarjeta = $tipotarjeta;

        return $this;
    }

    public function getTipocliente(): ?string
    {
        return $this->tipocliente;
    }

    public function setTipocliente(?string $tipocliente): self
    {
        $this->tipocliente = $tipocliente;

        return $this;
    }

    public function getComonosconocio(): ?string
    {
        return $this->comonosconocio;
    }

    public function setComonosconocio(?string $comonosconocio): self
    {
        $this->comonosconocio = $comonosconocio;

        return $this;
    }

    public function getFechanacimiento(): ?\DateTimeInterface
    {
        return $this->fechanacimiento;
    }

    public function setFechanacimiento(?\DateTimeInterface $fechanacimiento): self
    {
        $this->fechanacimiento = $fechanacimiento;

        return $this;
    }

    public function getEdad(): ?float
    {
        return $this->edad;
    }

    public function setEdad(?float $edad): self
    {
        $this->edad = $edad;

        return $this;
    }

    public function getApellidopaterno(): ?string
    {
        return $this->apellidopaterno;
    }

    public function setApellidopaterno(?string $apellidopaterno): self
    {
        $this->apellidopaterno = $apellidopaterno;

        return $this;
    }

    public function getApellidomaterno(): ?string
    {
        return $this->apellidomaterno;
    }

    public function setApellidomaterno(?string $apellidomaterno): self
    {
        $this->apellidomaterno = $apellidomaterno;

        return $this;
    }

    public function getOcupacion(): ?string
    {
        return $this->ocupacion;
    }

    public function setOcupacion(?string $ocupacion): self
    {
        $this->ocupacion = $ocupacion;

        return $this;
    }

    public function getGenero(): ?string
    {
        return $this->genero;
    }

    public function setGenero(?string $genero): self
    {
        $this->genero = $genero;

        return $this;
    }

    public function getAlcaldia(): ?string
    {
        return $this->alcaldia;
    }

    public function setAlcaldia(?string $alcaldia): self
    {
        $this->alcaldia = $alcaldia;

        return $this;
    }

    public function getTipocalle(): ?string
    {
        return $this->tipocalle;
    }

    public function setTipocalle(?string $tipocalle): self
    {
        $this->tipocalle = $tipocalle;

        return $this;
    }

    public function getCalle(): ?string
    {
        return $this->calle;
    }

    public function setCalle(?string $calle): self
    {
        $this->calle = $calle;

        return $this;
    }

    public function getNumero(): ?string
    {
        return $this->numero;
    }

    public function setNumero(?string $numero): self
    {
        $this->numero = $numero;

        return $this;
    }

    public function getCodigopostal(): ?string
    {
        return $this->codigopostal;
    }

    public function setCodigopostal(?string $codigopostal): self
    {
        $this->codigopostal = $codigopostal;

        return $this;
    }

    public function getColonia(): ?string
    {
        return $this->colonia;
    }

    public function setColonia(?string $colonia): self
    {
        $this->colonia = $colonia;

        return $this;
    }

    public function getEntidadfederativa(): ?string
    {
        return $this->entidadfederativa;
    }

    public function setEntidadfederativa(?string $entidadfederativa): self
    {
        $this->entidadfederativa = $entidadfederativa;

        return $this;
    }

    public function getLocalidad(): ?string
    {
        return $this->localidad;
    }

    public function setLocalidad(?string $localidad): self
    {
        $this->localidad = $localidad;

        return $this;
    }

    public function getDiascredito(): ?int
    {
        return $this->diascredito;
    }

    public function setDiascredito(int $diascredito): self
    {
        $this->diascredito = $diascredito;

        return $this;
    }

    public function getPassword(): ?string
    {
        return $this->password;
    }

    public function setPassword(?string $password): self
    {
        $this->password = $password;

        return $this;
    }

    public function getRole(): ?string
    {
        return $this->role;
    }

    public function setRole(?string $role): self
    {
        $this->role = $role;

        return $this;
    }

    public function getIsverified(): ?bool
    {
        return $this->isverified;
    }

    public function setIsverified(?bool $isverified): self
    {
        $this->isverified = $isverified;

        return $this;
    }

    public function getGroupclient(): ?string
    {
        return $this->groupclient;
    }

    public function setGroupclient(?string $groupclient): self
    {
        $this->groupclient = $groupclient;

        return $this;
    }

    public function getSchoolyearsemester(): ?string
    {
        return $this->schoolyearsemester;
    }

    public function setSchoolyearsemester(?string $schoolyearsemester): self
    {
        $this->schoolyearsemester = $schoolyearsemester;

        return $this;
    }

    public function getScholarlid(): ?string
    {
        return $this->scholarlid;
    }

    public function setScholarlid(?string $scholarlid): self
    {
        $this->scholarlid = $scholarlid;

        return $this;
    }

    public function getScholarity(): ?string
    {
        return $this->scholarity;
    }

    public function setScholarity(?string $scholarity): self
    {
        $this->scholarity = $scholarity;

        return $this;
    }

    public function getSchedule(): ?string
    {
        return $this->schedule;
    }

    public function setSchedule(?string $schedule): self
    {
        $this->schedule = $schedule;

        return $this;
    }

    public function getBeneficiarytype(): ?string
    {
        return $this->beneficiarytype;
    }

    public function setBeneficiarytype(?string $beneficiarytype): self
    {
        $this->beneficiarytype = $beneficiarytype;

        return $this;
    }

    public function getAuthorizedcredit(): ?string
    {
        return $this->authorizedcredit;
    }

    public function setAuthorizedcredit(string $authorizedcredit): self
    {
        $this->authorizedcredit = $authorizedcredit;

        return $this;
    }

    public function getDebt(): ?string
    {
        return $this->debt;
    }

    public function setDebt(string $debt): self
    {
        $this->debt = $debt;

        return $this;
    }

    public function getDoctorname(): ?string
    {
        return $this->doctorname;
    }

    public function setDoctorname(?string $doctorname): self
    {
        $this->doctorname = $doctorname;

        return $this;
    }

    public function getGoogleauthtoken(): ?string
    {
        return $this->googleauthtoken;
    }

    public function setGoogleauthtoken(?string $googleauthtoken): self
    {
        $this->googleauthtoken = $googleauthtoken;

        return $this;
    }

    public function getHosteddomain(): ?string
    {
        return $this->hosteddomain;
    }

    public function setHosteddomain(?string $hosteddomain): self
    {
        $this->hosteddomain = $hosteddomain;

        return $this;
    }

    public function getBalanceinfavor(): ?string
    {
        return $this->balanceinfavor;
    }

    public function setBalanceinfavor(string $balanceinfavor): self
    {
        $this->balanceinfavor = $balanceinfavor;

        return $this;
    }

    public function getIsstudent(): ?string
    {
        return $this->isstudent;
    }

    public function setIsstudent(string $isstudent): self
    {
        $this->isstudent = $isstudent;

        return $this;
    }

    public function getHolder(): ?self
    {
        return $this->holder;
    }

    public function setHolder(?self $holder): self
    {
        $this->holder = $holder;

        return $this;
    }

    public function getSellreferenceIdsellreference(): ?Sellreference
    {
        return $this->sellreferenceIdsellreference;
    }

    public function setSellreferenceIdsellreference(?Sellreference $sellreferenceIdsellreference): self
    {
        $this->sellreferenceIdsellreference = $sellreferenceIdsellreference;

        return $this;
    }

    public function getUnidadIdunidad(): ?Unidad
    {
        return $this->unidadIdunidad;
    }

    public function setUnidadIdunidad(?Unidad $unidadIdunidad): self
    {
        $this->unidadIdunidad = $unidadIdunidad;

        return $this;
    }

    public function getEmpresaclienteIdempresacliente(): ?Empresacliente
    {
        return $this->empresaclienteIdempresacliente;
    }

    public function setEmpresaclienteIdempresacliente(?Empresacliente $empresaclienteIdempresacliente): self
    {
        $this->empresaclienteIdempresacliente = $empresaclienteIdempresacliente;

        return $this;
    }

 public function __toString()
{
    return trim($this->getNombre() . ' ' . $this->getApellidoPaterno() . ' ' . $this->getApellidoMaterno());
}

    public function getUsername()
    {
        return (string) $this->email;
    }



    public function getRoles(): array
    {
        return ['ROLE_CLIENT'];
    }

    public function getSalt()
    {
        // No se necesita salt cuando se usa bcrypt o argon2i
        return null;
    }

    public function eraseCredentials()
    {

    }


    public function getNombreCompleto()

    {
        return trim($this->nombre) . ' ' . trim($this->apellidopaterno) . ' ' . trim($this->apellidomaterno);
    }

    public function getIsBeneficiario()

    {
        return $this->getNombreCompleto();
        if($this->holder) {return $this->holder->getNombreCompleto();}
        else if (!$this->holder)

        {return $this->getNombreCompleto();}

        return "NAN";
    }

    public function getIsMainCliente()

    {
        if(!$this->holder) {return $this->getNombreCompleto();}

        return "Sin Beneficiario";
    }

    public function __construct(string $_email = null, string $_telefono = null, string $_nombre= null,string $_apellidopaterno= null, string $_apellidomaterno= null)
    {
        // Initialize any default values for properties or perform other setup here
        $this->status = '1';
        $this->diascredito = 30;
        $this->authorizedcredit = '0.00';
        $this->debt = '0.00';
        $this->isverified = false;
        $this->role = "ROLE_VSCOSTUMER";

        $this->email = $_email;
        $this->telefono = $_telefono;
        $this->nombre = $_nombre??"";
        $this->apellidopaterno = $_apellidopaterno;
        $this->apellidomaterno = $_apellidomaterno;
    }

    /**
     * Retorna el nombre completo del cliente asociado.
     *
     * @return string
     */
    public function getFullName(): string
    {
        return trim(sprintf(
            '%s %s %s',
            $this->getNombre(),
            $this->getApellidopaterno(),
            $this->getApellidomaterno()
        ));
    }

}
