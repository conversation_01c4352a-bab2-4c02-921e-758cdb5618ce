<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Flowsigneddocuments
 *
 * @ORM\Table(name="FlowSignedDocuments", indexes={@ORM\Index(name="fk_FlowSignedDocuments_flujoExpediente1_idx", columns={"flujoExpediente_idflujoExpediente"})})
 * @ORM\Entity
 */
class Flowsigneddocuments
{
    /**
     * @var int
     *
     * @ORM\Column(name="idFlowSignedDocuments", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idflowsigneddocuments;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="creation", type="datetime", nullable=false)
     */
    private $creation;

    /**
     * @var string
     *
     * @ORM\Column(name="filename", type="string", length=45, nullable=false)
     */
    private $filename;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \Flujoexpediente
     *
     * @ORM\ManyToOne(targetEntity="Flujoexpediente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="flujoExpediente_idflujoExpediente", referencedColumnName="idflujoExpediente")
     * })
     */
    private $flujoexpedienteIdflujoexpediente;

    public function getIdflowsigneddocuments(): ?int
    {
        return $this->idflowsigneddocuments;
    }

    public function getCreation(): ?\DateTimeInterface
    {
        return $this->creation;
    }

    public function setCreation(\DateTimeInterface $creation): self
    {
        $this->creation = $creation;

        return $this;
    }

    public function getFilename(): ?string
    {
        return $this->filename;
    }

    public function setFilename(string $filename): self
    {
        $this->filename = $filename;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getFlujoexpedienteIdflujoexpediente(): ?Flujoexpediente
    {
        return $this->flujoexpedienteIdflujoexpediente;
    }

    public function setFlujoexpedienteIdflujoexpediente(?Flujoexpediente $flujoexpedienteIdflujoexpediente): self
    {
        $this->flujoexpedienteIdflujoexpediente = $flujoexpedienteIdflujoexpediente;

        return $this;
    }


}
