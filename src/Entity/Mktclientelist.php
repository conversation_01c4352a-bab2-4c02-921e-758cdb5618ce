<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Mktclientelist
 *
 * @ORM\Table(name="mktClienteList", indexes={@ORM\Index(name="fk_mktClienteList_mktList1_idx", columns={"mktList_idmktList"}), @ORM\Index(name="fk_mktClienteList_cliente1_idx", columns={"cliente_idcliente"})})
 * @ORM\Entity
 */
class Mktclientelist
{
    /**
     * @var int
     *
     * @ORM\Column(name="idmktClienteList", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idmktclientelist;

    /**
     * @var \Cliente
     *
     * @ORM\ManyToOne(targetEntity="Cliente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="cliente_idcliente", referencedColumnName="idcliente")
     * })
     */
    private $clienteIdcliente;

    /**
     * @var \Mktlist
     *
     * @ORM\ManyToOne(targetEntity="Mktlist")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="mktList_idmktList", referencedColumnName="idmktList")
     * })
     */
    private $mktlistIdmktlist;

    public function getIdmktclientelist(): ?int
    {
        return $this->idmktclientelist;
    }

    public function getClienteIdcliente(): ?Cliente
    {
        return $this->clienteIdcliente;
    }

    public function setClienteIdcliente(?Cliente $clienteIdcliente): self
    {
        $this->clienteIdcliente = $clienteIdcliente;

        return $this;
    }

    public function getMktlistIdmktlist(): ?Mktlist
    {
        return $this->mktlistIdmktlist;
    }

    public function setMktlistIdmktlist(?Mktlist $mktlistIdmktlist): self
    {
        $this->mktlistIdmktlist = $mktlistIdmktlist;

        return $this;
    }


}
