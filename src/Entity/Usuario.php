<?php

namespace App\Entity;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Security\Core\User\UserInterface;
use <PERSON><PERSON><PERSON>\TwoFactorBundle\Model\Google\TwoFactorInterface;
use Sc<PERSON>b\TwoFactorBundle\Model\TrustedDeviceInterface;
use Doctrine\ORM\Mapping as ORM;

/**
 * Usuario
 *
 * @ORM\Table(name="usuario", indexes={@ORM\Index(name="fk_usuario_sucursal1_idx", columns={"sucursal_idsucursal"})})
 * @ORM\Entity
 */
class Usuario implements UserInterface, TwoFactorInterface, TrustedDeviceInterface
{
    /**
     * @var int
     *
     * @ORM\Column(name="idusuario", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idusuario;

    /**
     * @var string
     *
     * @ORM\Column(name="nombre", type="string", length=45, nullable=false)
     */
    private $nombre;

    /**
     * @var string
     *
     * @ORM\Column(name="apellidoPaterno", type="string", length=45, nullable=false)
     */
    private $apellidopaterno;

    /**
     * @var string|null
     *
     * @ORM\Column(name="apellidoMaterno", type="string", length=45, nullable=true)
     */
    private $apellidomaterno;

    /**
     * @var string|null
     *
     * @ORM\Column(name="email", type="string", length=45, nullable=true)
     */
    private $email;

    /**
     * @var string
     *
     * @ORM\Column(name="rol", type="string", length=45, nullable=false)
     */
    private $rol;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string
     *
     * @ORM\Column(name="contrasena", type="string", length=500, nullable=false)
     * 
     * @Assert\NotBlank(message="La contraseña no puede estar vacía.")
     * @Assert\Length(
     *     min=8,
     *     minMessage="La contraseña debe tener al menos {{ limit }} caracteres."
     * )
     * @Assert\Regex(
     *     pattern="/[A-Z]/",
     *     message="La contraseña debe contener al menos una letra mayúscula."
     * )
     * @Assert\Regex(
     *     pattern="/[a-z]/",
     *     message="La contraseña debe contener al menos una letra minúscula."
     * )
     * @Assert\Regex(
     *     pattern="/\d/",
     *     message="La contraseña debe contener al menos un número."
     * )
     * @Assert\Regex(
     *     pattern="/[^a-zA-Z\d]/",
     *     message="La contraseña debe contener al menos un carácter especial."
     * )
     */
    private $contrasena;

    /**
     * @var string|null
     *
     * @ORM\Column(name="usuario", type="string", length=45, nullable=true)
     */
    private $usuario;

    /**
     * @var string|null
     *
     * @ORM\Column(name="puesto", type="string", length=150, nullable=true)
     */
    private $puesto;

    /**
     * @var string|null
     *
     * @ORM\Column(name="googleAuthToken", type="string", length=300, nullable=true)
     */
    private $googleauthtoken;

    /**
     * @var array|null
     *
     * @ORM\Column(name="entrypoints", type="json", nullable=true)
     */
    private $entrypoints;

    /**
     * @var \Sucursal
     *
     * @ORM\ManyToOne(targetEntity="Sucursal")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="sucursal_idsucursal", referencedColumnName="idsucursal")
     * })
     */
    private $sucursalIdsucursal;

    /**
     * @var string|null
     *
     * @ORM\Column(name="profilePicture", type="string", length=45, nullable=true)
     */
    private $profilepicture;

    /**
     * @ORM\Column(name="trustedVersion", type="integer", nullable=false)
     */
    private $trustedVersion = 0;

    public function getIdusuario(): ?int
    {
        return $this->idusuario;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(string $nombre): self
    {
        $this->nombre = $nombre;

        return $this;
    }

    public function getApellidopaterno(): ?string
    {
        return $this->apellidopaterno;
    }

    public function setApellidopaterno(string $apellidopaterno): self
    {
        $this->apellidopaterno = $apellidopaterno;

        return $this;
    }

    public function getApellidomaterno(): ?string
    {
        return $this->apellidomaterno;
    }

    public function setApellidomaterno(?string $apellidomaterno): self
    {
        $this->apellidomaterno = $apellidomaterno;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getRol(): ?string
    {
        return $this->rol;
    }

    public function setRol(string $rol): self
    {
        $this->rol = $rol;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getContrasena(): ?string
    {
        return $this->contrasena;
    }

    public function setContrasena(string $contrasena): self
    {
        $this->contrasena = $contrasena;

        return $this;
    }

    public function getUsuario(): ?string
    {
        return $this->usuario;
    }

    public function setUsuario(?string $usuario): self
    {
        $this->usuario = $usuario;

        return $this;
    }

    public function getPuesto(): ?string
    {
        return $this->puesto;
    }

    public function setPuesto(?string $puesto): self
    {
        $this->puesto = $puesto;

        return $this;
    }

    public function getGoogleauthtoken(): ?string
    {
        return $this->googleauthtoken;
    }

    public function setGoogleauthtoken(?string $googleauthtoken): self
    {
        $this->googleauthtoken = $googleauthtoken;

        return $this;
    }

    public function getEntrypoints(): ?array
    {
        return $this->entrypoints;
    }

    public function setEntrypoints(?array $entrypoints): self
    {
        $this->entrypoints = $entrypoints;

        return $this;
    }

    public function getSucursalIdsucursal(): ?Sucursal
    {
        return $this->sucursalIdsucursal;
    }

    public function setSucursalIdsucursal(?Sucursal $sucursalIdsucursal): self
    {
        $this->sucursalIdsucursal = $sucursalIdsucursal;

        return $this;
    }

    public function getProfilepicture(): ?string
    {
        return $this->profilepicture;
    }

    public function setProfilepicture(?string $profilepicture): self
    {
        $this->profilepicture = $profilepicture;

        return $this;
    }

    public function __construct()
    {
        $this->isActive = true;
        // may not be needed, see section on salt below
        // $this->salt = md5(uniqid('', true));
    }

    public function getUsername()
    {
        return $this->email;
    }

    public function getSalt()
    {
        // you *may* need a real salt depending on your encoder
        // see section on salt below
        return null;
    }

    public function getPassword()
    {
        return $this->contrasena;
    }

    public function getRoles()
    {
        $roles[] = $this->rol;
        return array_unique($roles);
    }

    public function eraseCredentials()
    {
    }

    /** @see \Serializable::serialize() */
    public function serialize()
    {
        return serialize(array(
            $this->idusuario,
            $this->email,
            $this->contrasena,
            // see section on salt below
            // $this->salt,
        ));
    }

    /** @see \Serializable::unserialize() */
    public function unserialize($serialized)
    {
        list(
            $this->idusuario,
            $this->email,
            $this->contrasena,
            // see section on salt below
            // $this->salt
        ) = unserialize($serialized, array('allowed_classes' => false));
    }

    function  __toString()
    {
        return $this->getNombre() . " " . $this->getApellidopaterno();
    }

    public function getNombreVendedor()
    {
        return $this->nombre . ' ' . $this->apellidopaterno . ' ' . $this->apellidomaterno;
    }

    /**
     * @ORM\Column(name="googleAuthenticatorSecret", type="string", nullable=true)
     */
    private $googleAuthenticatorSecret;

    // [...]

    public function isGoogleAuthenticatorEnabled(): bool
    {
        return null !== $this->googleAuthenticatorSecret;
    }

    public function getGoogleAuthenticatorUsername(): string
    {
        return $this->email;
    }

    public function getGoogleAuthenticatorSecret(): ?string
    {
        return $this->googleAuthenticatorSecret;
    }

    public function setGoogleAuthenticatorSecret(?string $googleAuthenticatorSecret): void
    {
        $this->googleAuthenticatorSecret = $googleAuthenticatorSecret;
    }

    private ?UploadedFile $file = null;

    const SERVER_PATH_TO_IMAGE_FOLDER = "/uploads/logos/";

    public function setFile(?UploadedFile $file = null): void
    {
        $this->file = $file;
    }

    public function getFile(): ?UploadedFile
    {
        return $this->file;
    }

    public function upload(): void
    {

        if (null === $this->getFile()) return;


        $file = $this->getFile();

        $originalFilename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $safeFilename = transliterator_transliterate('Any-Latin; Latin-ASCII; [^A-Za-z0-9_] remove; Lower()', $originalFilename);
        $fileName = $safeFilename.'-'.uniqid().'.'.$file->guessExtension();
        $folder = "uploads/profilePics/".$this->getIdusuario().'/';

        if (!is_dir($folder)) {
            mkdir($folder);
        }

        $this->getFile()->move($folder, $fileName);

        $this->setProfilepicture($fileName);

        $this->setFile(null);

    }

    public function getTrustedTokenVersion(): int
    {
        return $this->trustedVersion;
    }

    public function setTrustedTokenVersion(int $trustedVersion): self
    {
        $this->trustedVersion = $trustedVersion;

        return $this;
    }

    public function getTrustedVersion(): ?int
    {
        return $this->trustedVersion;
    }

    public function setTrustedVersion(int $trustedVersion): self
    {
        $this->trustedVersion = $trustedVersion;

        return $this;
    }

}
