<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Departurecause
 *
 * @ORM\Table(name="departureCause", indexes={@ORM\Index(name="fk_departureCause_departureType1_idx", columns={"departureType_iddepartureType"})})
 * @ORM\Entity
 */
class Departurecause
{
    /**
     * @var int
     *
     * @ORM\Column(name="iddepartureCause", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $iddeparturecause;

    /**
     * @var string|null
     *
     * @ORM\Column(name="name", type="string", length=45, nullable=true)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \Departuretype
     *
     * @ORM\ManyToOne(targetEntity="Departuretype")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="departureType_iddepartureType", referencedColumnName="iddepartureType")
     * })
     */
    private $departuretypeIddeparturetype;

    public function getIddeparturecause(): ?int
    {
        return $this->iddeparturecause;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getDeparturetypeIddeparturetype(): ?Departuretype
    {
        return $this->departuretypeIddeparturetype;
    }

    public function setDeparturetypeIddeparturetype(?Departuretype $departuretypeIddeparturetype): self
    {
        $this->departuretypeIddeparturetype = $departuretypeIddeparturetype;

        return $this;
    }


}
