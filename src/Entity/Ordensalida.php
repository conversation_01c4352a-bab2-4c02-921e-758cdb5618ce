<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Ordensalida
 *
 * @ORM\Table(name="ordenSalida", indexes={@ORM\Index(name="fk_ordenSalida_usuario1_idx", columns={"responsableSalida"}), @ORM\Index(name="fk_ordenSalida_usuario2_idx", columns={"responsableEntrada"}), @ORM\Index(name="fk_ordenSalida_transpasoAlmacen1_idx", columns={"transpasoAlmacen_idtranspasoAlmacen"})})
 * @ORM\Entity
 */
class Ordensalida
{
    /**
     * @var int
     *
     * @ORM\Column(name="idordenSalida", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idordensalida;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=false)
     */
    private $creacion;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaSalida", type="datetime", nullable=true)
     */
    private $fechasalida;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaRecibido", type="datetime", nullable=true)
     */
    private $fecharecibido;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="aceptada", type="string", length=1, nullable=true, options={"default"="2","fixed"=true})
     */
    private $aceptada = '2';

    /**
     * @var string|null
     *
     * @ORM\Column(name="nota", type="string", length=45, nullable=true)
     */
    private $nota;

    /**
     * @var string|null
     *
     * @ORM\Column(name="archivoOrdenSalida", type="string", length=120, nullable=true)
     */
    private $archivoordensalida;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="responsableEntrada", referencedColumnName="idusuario")
     * })
     */
    private $responsableentrada;

    /**
     * @var \Transpasoalmacen
     *
     * @ORM\ManyToOne(targetEntity="Transpasoalmacen")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="transpasoAlmacen_idtranspasoAlmacen", referencedColumnName="idtranspasoAlmacen")
     * })
     */
    private $transpasoalmacenIdtranspasoalmacen;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="responsableSalida", referencedColumnName="idusuario")
     * })
     */
    private $responsablesalida;

    public function getIdordensalida(): ?int
    {
        return $this->idordensalida;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getFechasalida(): ?\DateTimeInterface
    {
        return $this->fechasalida;
    }

    public function setFechasalida(?\DateTimeInterface $fechasalida): self
    {
        $this->fechasalida = $fechasalida;

        return $this;
    }

    public function getFecharecibido(): ?\DateTimeInterface
    {
        return $this->fecharecibido;
    }

    public function setFecharecibido(?\DateTimeInterface $fecharecibido): self
    {
        $this->fecharecibido = $fecharecibido;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getAceptada(): ?string
    {
        return $this->aceptada;
    }

    public function setAceptada(?string $aceptada): self
    {
        $this->aceptada = $aceptada;

        return $this;
    }

    public function getNota(): ?string
    {
        return $this->nota;
    }

    public function setNota(?string $nota): self
    {
        $this->nota = $nota;

        return $this;
    }

    public function getArchivoordensalida(): ?string
    {
        return $this->archivoordensalida;
    }

    public function setArchivoordensalida(?string $archivoordensalida): self
    {
        $this->archivoordensalida = $archivoordensalida;

        return $this;
    }

    public function getResponsableentrada(): ?Usuario
    {
        return $this->responsableentrada;
    }

    public function setResponsableentrada(?Usuario $responsableentrada): self
    {
        $this->responsableentrada = $responsableentrada;

        return $this;
    }

    public function getTranspasoalmacenIdtranspasoalmacen(): ?Transpasoalmacen
    {
        return $this->transpasoalmacenIdtranspasoalmacen;
    }

    public function setTranspasoalmacenIdtranspasoalmacen(?Transpasoalmacen $transpasoalmacenIdtranspasoalmacen): self
    {
        $this->transpasoalmacenIdtranspasoalmacen = $transpasoalmacenIdtranspasoalmacen;

        return $this;
    }

    public function getResponsablesalida(): ?Usuario
    {
        return $this->responsablesalida;
    }

    public function setResponsablesalida(?Usuario $responsablesalida): self
    {
        $this->responsablesalida = $responsablesalida;

        return $this;
    }
    public function getAceptadaText()
    {
        switch ($this->aceptada) {
            case '2':
                return 'Pendiente';
            case '1':
                return 'Aceptado';
            case '0':
                return 'Negado';
            default:
                return 'Desconocido'; 
        }
    }
}