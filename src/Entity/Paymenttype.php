<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Paymenttype
 *
 * @ORM\Table(name="paymentType")
 * @ORM\Entity
 */
class Paymenttype
{
    /**
     * @var int
     *
     * @ORM\Column(name="idpaymentType", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idpaymenttype;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=45, nullable=false)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string
     *
     * @ORM\Column(name="applyBalanceInFavor", type="string", length=1, nullable=false, options={"fixed"=true})
     */
    private $applybalanceinfavor = '0';

    /**
     * @var string|null
     *
     * @ORM\Column(name="claveFacturama", type="string", length=45, nullable=true)
     */
    private $clavefacturama;

    public function getIdpaymenttype(): ?int
    {
        return $this->idpaymenttype;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getApplybalanceinfavor(): ?string
    {
        return $this->applybalanceinfavor;
    }

    public function setApplybalanceinfavor(string $applybalanceinfavor): self
    {
        $this->applybalanceinfavor = $applybalanceinfavor;

        return $this;
    }

    public function getClavefacturama(): ?string
    {
        return $this->clavefacturama;
    }

    public function setClavefacturama(?string $clavefacturama): self
    {
        $this->clavefacturama = $clavefacturama;

        return $this;
    }

    public function __toString(): string
    {
        return (string) $this->getName();
    }


}