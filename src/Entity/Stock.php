<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Stock
 *
 * @ORM\Table(name="stock", indexes={@ORM\Index(name="fk_stock_producto1_idx", columns={"producto_idproducto"}), @ORM\Index(name="fk_stock_stockState1_idx", columns={"stockState_idstockState"}), @ORM\Index(name="fk_stock_sucursal1_idx", columns={"sucursal_idsucursal"})})
 * @ORM\Entity
 */
class Stock
{
    /**
     * @var int
     *
     * @ORM\Column(name="idstock", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idstock;

    /**
     * @var int
     *
     * @ORM\Column(name="cantidad", type="integer", nullable=false)
     */
    private $cantidad;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=false)
     */
    private $creacion;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="modificacion", type="datetime", nullable=false)
     */
    private $modificacion;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=45, nullable=false, options={"default"="1"})
     */
    private $status = '1';

    /**
     * @var int|null
     *
     * @ORM\Column(name="apartados", type="integer", nullable=true)
     */
    private $apartados;

    /**
     * @var string|null
     *
     * @ORM\Column(name="apartado", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $apartado;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaApartado", type="datetime", nullable=true)
     */
    private $fechaapartado;

    /**
     * @var string|null
     *
     * @ORM\Column(name="codigoBarras", type="string", length=100, nullable=true)
     */
    private $codigobarras;

    /**
     * @var string|null
     *
     * @ORM\Column(name="serie", type="string", length=45, nullable=true)
     */
    private $serie;

    /**
     * @var string
     *
     * @ORM\Column(name="defective", type="string", length=45, nullable=false, options={"default"="DISPONIBLE"})
     */
    private $defective = 'DISPONIBLE';

    /**
     * @var string
     *
     * @ORM\Column(name="tipo", type="string", length=1, nullable=false)
     */
    private $tipo = '0';

    /**
     * @var string|null
     *
     * @ORM\Column(name="justificacion", type="string", length=45, nullable=true)
     */
    private $justificacion;

    /**
     * @var string|null
     *
     * @ORM\Column(name="precio", type="decimal", precision=15, scale=2, nullable=true, options={"default"="0.00"})
     */
    private $precio = '0.00';

    /**
     * @var string|null
     *
     * @ORM\Column(name="costo", type="decimal", precision=15, scale=2, nullable=true, options={"default"="0.00"})
     */
    private $costo = '0.00';

    /**
     * @var \Sucursal
     *
     * @ORM\ManyToOne(targetEntity="Sucursal")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="sucursal_idsucursal", referencedColumnName="idsucursal")
     * })
     */
    private $sucursalIdsucursal;

    /**
     * @var \Sucursal
     *
     * @ORM\ManyToOne(targetEntity="Sucursal")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="sucursal_idsucursal", referencedColumnName="idsucursal")
     * })
     */
    private $campana;

    /**
     * @var \Sucursal
     *
     * @ORM\ManyToOne(targetEntity="Sucursal")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="sucursal_idsucursal", referencedColumnName="idsucursal")
     * })
     */
    private $bodega;

    /**
     * @var \Producto
     *
     * @ORM\ManyToOne(targetEntity="Producto")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="producto_idproducto", referencedColumnName="idproducto")
     * })
     */
    private $productoIdproducto;

    /**
     * @var \Stockstate
     *
     * @ORM\ManyToOne(targetEntity="Stockstate")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="stockState_idstockState", referencedColumnName="idstockState")
     * })
     */
    private $stockstateIdstockstate;

    public function getIdstock(): ?int
    {
        return $this->idstock;
    }

    public function getCantidad(): ?int
    {
        return $this->cantidad;
    }

    public function setCantidad(int $cantidad): self
    {
        $this->cantidad = $cantidad;

        return $this;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getModificacion(): ?\DateTimeInterface
    {
        return $this->modificacion;
    }

    public function setModificacion(\DateTimeInterface $modificacion): self
    {
        $this->modificacion = $modificacion;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getApartados(): ?int
    {
        return $this->apartados;
    }

    public function setApartados(?int $apartados): self
    {
        $this->apartados = $apartados;

        return $this;
    }

    public function getApartado(): ?string
    {
        return $this->apartado;
    }

    public function setApartado(?string $apartado): self
    {
        $this->apartado = $apartado;

        return $this;
    }

    public function getFechaapartado(): ?\DateTimeInterface
    {
        return $this->fechaapartado;
    }

    public function setFechaapartado(?\DateTimeInterface $fechaapartado): self
    {
        $this->fechaapartado = $fechaapartado;

        return $this;
    }

    public function getCodigobarras(): ?string
    {
        return $this->codigobarras;
    }

    public function setCodigobarras(?string $codigobarras): self
    {
        $this->codigobarras = $codigobarras;

        return $this;
    }

    public function getSerie(): ?string
    {
        return $this->serie;
    }

    public function setSerie(?string $serie): self
    {
        $this->serie = $serie;

        return $this;
    }

    public function getDefective(): ?string
    {
        return $this->defective;
    }

    public function setDefective(string $defective): self
    {
        $this->defective = $defective;

        return $this;
    }

    public function getTipo(): ?string
    {
        return $this->tipo;
    }

    public function setTipo(string $tipo): self
    {
        $this->tipo = $tipo;

        return $this;
    }

    public function getJustificacion(): ?string
    {
        return $this->justificacion;
    }

    public function setJustificacion(?string $justificacion): self
    {
        $this->justificacion = $justificacion;

        return $this;
    }

    public function getPrecio(): ?string
    {
        return $this->precio;
    }

    public function setPrecio(?string $precio): self
    {
        $this->precio = $precio;

        return $this;
    }

    public function getCosto(): ?string
    {
        return $this->costo;
    }

    public function setCosto(?string $costo): self
    {
        $this->costo = $costo;

        return $this;
    }

    public function getSucursalIdsucursal(): ?Sucursal
    {
        return $this->sucursalIdsucursal;
    }

    public function setSucursalIdsucursal(?Sucursal $sucursalIdsucursal): self
    {
        $this->bodega = $sucursalIdsucursal;
        $this->campana = $sucursalIdsucursal;
        $this->sucursalIdsucursal = $sucursalIdsucursal;

        return $this;
    }

    public function getCampana(): ?Sucursal
    {
        return $this->campana;
    }

    public function setCampana(?Sucursal $campana): self
    {
        $this->campana = $campana;

        return $this;
    }
    public function getBodega(): ?Sucursal
    {
        return $this->bodega;
    }

    public function setBodega(?Sucursal $bodega): self
    {
        $this->bodega = $bodega;

        return $this;
    }

    public function getProductoIdproducto(): ?Producto
    {
        return $this->productoIdproducto;
    }

    public function setProductoIdproducto(?Producto $productoIdproducto): self
    {
        $this->productoIdproducto = $productoIdproducto;

        return $this;
    }

    public function getStockstateIdstockstate(): ?Stockstate
    {
        return $this->stockstateIdstockstate;
    }

    public function setStockstateIdstockstate(?Stockstate $stockstateIdstockstate): self
    {
        $this->stockstateIdstockstate = $stockstateIdstockstate;

        return $this;
    }

    public function getPrecioMasIva(): float
    {
        $precioStock = (float)$this->getPrecio();
        /*
         * Si el preciostock es diferente a 0
         * se le pone el precio de stock
         * y después se suma con el iva, este precio es de STOCK
         * */
        if ($precioStock != 0.00) {
            $iva = $precioStock * 0.16;
            return round($precioStock + $iva, 2);

        } elseif($precioStock === 0.00) {
            /*
             * Y si no usaremos el de producto
             * */

            $precio = (float)$this->getProductoIdproducto()->getPrecio();
            $iva = $precio * 0.16;
            return round($precio + $iva, 2);
        }
        return round("NA");
    }

    public function getIvaPrecio(): float
    {
        $precio = $this->getProductoIdproducto()->getPrecio();
        $iva = $precio * .16;
        return round($iva,2);
    }

    public function __toString() {
        return $this->getProductoIdproducto()->getModelo();
    }

    public function getCreatedAtForExport()
    {
        return $this->creacion->format('d/m/Y H:i');
    }
    
    public function getStockStateName()
    {
        return $this->stockstateIdstockstate ? $this->stockstateIdstockstate->getName() : 'N/A';
    }
}