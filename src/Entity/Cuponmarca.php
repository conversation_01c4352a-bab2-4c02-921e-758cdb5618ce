<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Cuponmarca
 *
 * @ORM\Table(name="cuponMarca", indexes={@ORM\Index(name="fk_cuponMarca_marca1_idx", columns={"marca_idmarca"}), @ORM\Index(name="fk_cuponMarca_cupon1_idx", columns={"cupon_idcupon"})})
 * @ORM\Entity
 */
class Cuponmarca
{
    /**
     * @var int
     *
     * @ORM\Column(name="idcuponMarca", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idcuponmarca;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=45, nullable=false, options={"default"="1"})
     */
    private $status = '1';

    /**
     * @var \Cupon
     *
     * @ORM\ManyToOne(targetEntity="Cupon")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="cupon_idcupon", referencedColumnName="idcupon")
     * })
     */
    private $cuponIdcupon;

    /**
     * @var \Marca
     *
     * @ORM\ManyToOne(targetEntity="Marca")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="marca_idmarca", referencedColumnName="idmarca")
     * })
     */
    private $marcaIdmarca;

    public function getIdcuponmarca(): ?int
    {
        return $this->idcuponmarca;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getCuponIdcupon(): ?Cupon
    {
        return $this->cuponIdcupon;
    }

    public function setCuponIdcupon(?Cupon $cuponIdcupon): self
    {
        $this->cuponIdcupon = $cuponIdcupon;

        return $this;
    }

    public function getMarcaIdmarca(): ?Marca
    {
        return $this->marcaIdmarca;
    }

    public function setMarcaIdmarca(?Marca $marcaIdmarca): self
    {
        $this->marcaIdmarca = $marcaIdmarca;

        return $this;
    }


}
