<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Clientefacturadatos
 *
 * @ORM\Table(name="clienteFacturaDatos", indexes={@ORM\Index(name="fk_facturaDatos_cliente1_idx", columns={"cliente_idcliente"})})
 * @ORM\Entity
 */
class Clientefacturadatos
{
    /**
     * @var int
     *
     * @ORM\Column(name="idclienteFacturaDatos", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idclientefacturadatos;

    /**
     * @var string
     *
     * @ORM\Column(name="razonSocial", type="string", length=500, nullable=false)
     */
    private $razonsocial;

    /**
     * @var string
     *
     * @ORM\Column(name="rfc", type="string", length=45, nullable=false)
     */
    private $rfc;

    /**
     * @var string
     *
     * @ORM\Column(name="email", type="string", length=150, nullable=false)
     */
    private $email;

    /**
     * @var string
     *
     * @ORM\Column(name="codigoPostal", type="string", length=45, nullable=false)
     */
    private $codigopostal;

    /**
     * @var string
     *
     * @ORM\Column(name="regimenFiscal", type="string", length=250, nullable=false)
     */
    private $regimenfiscal;

    /**
     * @var string
     *
     * @ORM\Column(name="usoCfdi", type="string", length=250, nullable=false)
     */
    private $usocfdi;

    /**
     * @var string|null
     *
     * @ORM\Column(name="constanciaSituacionFiscal", type="string", length=100, nullable=true)
     */
    private $constanciasituacionfiscal;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=false)
     */
    private $creacion;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="modificacion", type="datetime", nullable=false)
     */
    private $modificacion;

    /**
     * @var string
     *
     * @ORM\Column(name="tipoPersona", type="string", length=1, nullable=false, options={"fixed"=true})
     */
    private $tipopersona;

    /**
     * @var string|null
     *
     * @ORM\Column(name="idfacturama", type="string", length=255, nullable=true)
     */
    private $idfacturama;

    /**
     * @var \Cliente
     *
     * @ORM\ManyToOne(targetEntity="Cliente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="cliente_idcliente", referencedColumnName="idcliente")
     * })
     */
    private $clienteIdcliente;

    public function getIdclientefacturadatos(): ?int
    {
        return $this->idclientefacturadatos;
    }

    public function getRazonsocial(): ?string
    {
        return $this->razonsocial;
    }

    public function setRazonsocial(string $razonsocial): self
    {
        $this->razonsocial = $razonsocial;

        return $this;
    }

    public function getRfc(): ?string
    {
        return $this->rfc;
    }

    public function setRfc(string $rfc): self
    {
        $this->rfc = $rfc;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getCodigopostal(): ?string
    {
        return $this->codigopostal;
    }

    public function setCodigopostal(string $codigopostal): self
    {
        $this->codigopostal = $codigopostal;

        return $this;
    }

    public function getRegimenfiscal(): ?string
    {
        return $this->regimenfiscal;
    }

    public function setRegimenfiscal(string $regimenfiscal): self
    {
        $this->regimenfiscal = $regimenfiscal;

        return $this;
    }

    public function getUsocfdi(): ?string
    {
        return $this->usocfdi;
    }

    public function setUsocfdi(string $usocfdi): self
    {
        $this->usocfdi = $usocfdi;

        return $this;
    }

    public function getConstanciasituacionfiscal(): ?string
    {
        return $this->constanciasituacionfiscal;
    }

    public function setConstanciasituacionfiscal(?string $constanciasituacionfiscal): self
    {
        $this->constanciasituacionfiscal = $constanciasituacionfiscal;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getModificacion(): ?\DateTimeInterface
    {
        return $this->modificacion;
    }

    public function setModificacion(\DateTimeInterface $modificacion): self
    {
        $this->modificacion = $modificacion;

        return $this;
    }

    public function getTipopersona(): ?string
    {
        return $this->tipopersona;
    }

    public function setTipopersona(string $tipopersona): self
    {
        $this->tipopersona = $tipopersona;

        return $this;
    }

    public function getIdfacturama(): ?string
    {
        return $this->idfacturama;
    }

    public function setIdfacturama(?string $idfacturama): self
    {
        $this->idfacturama = $idfacturama;

        return $this;
    }

    public function getClienteIdcliente(): ?Cliente
    {
        return $this->clienteIdcliente;
    }

    public function setClienteIdcliente(?Cliente $clienteIdcliente): self
    {
        $this->clienteIdcliente = $clienteIdcliente;

        return $this;
    }


}
