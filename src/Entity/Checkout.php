<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Checkout
 *
 * @ORM\Table(name="checkout", indexes={@ORM\Index(name="fk_checkout_cart1_idx", columns={"cart_idcart"})})
 * @ORM\Entity
 */
class Checkout
{
    /**
     * @var int
     *
     * @ORM\Column(name="idcheckout", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idcheckout;

    /**
     * @var string
     *
     * @ORM\Column(name="data", type="text", length=65535, nullable=false)
     */
    private $data;

    /**
     * @var \Cart
     *
     * @ORM\ManyToOne(targetEntity="Cart")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="cart_idcart", referencedColumnName="idcart")
     * })
     */
    private $cartIdcart;

    public function getIdcheckout(): ?int
    {
        return $this->idcheckout;
    }

    public function getData(): ?string
    {
        return $this->data;
    }

    public function setData(string $data): self
    {
        $this->data = $data;

        return $this;
    }

    public function getCartIdcart(): ?Cart
    {
        return $this->cartIdcart;
    }

    public function setCartIdcart(?Cart $cartIdcart): self
    {
        $this->cartIdcart = $cartIdcart;

        return $this;
    }


}
