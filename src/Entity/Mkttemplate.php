<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Mkttemplate
 *
 * @ORM\Table(name="mktTemplate")
 * @ORM\Entity
 */
class Mkttemplate
{
    /**
     * @var int
     *
     * @ORM\Column(name="idmktTemplate", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idmkttemplate;

    /**
     * @var string|null
     *
     * @ORM\Column(name="name", type="string", length=45, nullable=true)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="messageTypes", type="text", length=65535, nullable=true)
     */
    private $messagetypes;

    public function getIdmkttemplate(): ?int
    {
        return $this->idmkttemplate;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getMessagetypes(): ?string
    {
        return $this->messagetypes;
    }

    public function setMessagetypes(?string $messagetypes): self
    {
        $this->messagetypes = $messagetypes;

        return $this;
    }


}
