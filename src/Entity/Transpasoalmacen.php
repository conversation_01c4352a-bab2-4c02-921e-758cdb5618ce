<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Transpasoalmacen
 *
 * @ORM\Table(name="transpasoAlmacen", indexes={@ORM\Index(name="fk_transpasoAlmacen_sucursal2_idx", columns={"sucursal_idsucursalDestino"}), @ORM\Index(name="fk_transpasoAlmacen_sucursal1_idx", columns={"sucursal_idsucursalOrigen"}), @ORM\Index(name="fk_transpasoAlmacen_usuario1_idx", columns={"usuario_idusuario"})})
 * @ORM\Entity
 */
class Transpasoalmacen
{
    /**
     * @var int
     *
     * @ORM\Column(name="idtranspasoAlmacen", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idtranspasoalmacen;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=true)
     */
    private $creacion;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="modificacion", type="datetime", nullable=true)
     */
    private $modificacion;

    /**
     * @var string|null
     *
     * @ORM\Column(name="notas", type="text", length=16777215, nullable=true)
     */
    private $notas;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string
     *
     * @ORM\Column(name="estado", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $estado = '1';

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    /**
     * @var \Sucursal
     *
     * @ORM\ManyToOne(targetEntity="Sucursal")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="sucursal_idsucursalOrigen", referencedColumnName="idsucursal")
     * })
     */
    private $sucursalIdsucursalorigen; 

    /**
     * @var \Sucursal
     *
     * @ORM\ManyToOne(targetEntity="Sucursal")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="sucursal_idsucursalDestino", referencedColumnName="idsucursal")
     * })
     */
    private $sucursalIdsucursaldestino;

    public function getIdtranspasoalmacen(): ?int
    {
        return $this->idtranspasoalmacen;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(?\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getModificacion(): ?\DateTimeInterface
    {
        return $this->modificacion;
    }

    public function setModificacion(?\DateTimeInterface $modificacion): self
    {
        $this->modificacion = $modificacion;

        return $this;
    }

    public function getNotas(): ?string
    {
        return $this->notas;
    }

    public function setNotas(?string $notas): self
    {
        $this->notas = $notas;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getEstado(): ?string
    {
        return $this->estado;
    }

    public function setEstado(string $estado): self
    {
        $this->estado = $estado;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }

    public function getSucursalIdsucursalorigen(): ?Sucursal
    {
        return $this->sucursalIdsucursalorigen;
    }

    public function setSucursalIdsucursalorigen(?Sucursal $sucursalIdsucursalorigen): self
    {
        $this->sucursalIdsucursalorigen = $sucursalIdsucursalorigen;

        return $this;
    }

    public function getSucursalIdsucursaldestino(): ?Sucursal
    {
        return $this->sucursalIdsucursaldestino;
    }

    public function setSucursalIdsucursaldestino(?Sucursal $sucursalIdsucursaldestino): self
    {
        $this->sucursalIdsucursaldestino = $sucursalIdsucursaldestino;

        return $this;
    }


}
