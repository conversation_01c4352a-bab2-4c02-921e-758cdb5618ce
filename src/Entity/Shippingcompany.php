<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Shippingcompany
 *
 * @ORM\Table(name="shippingCompany")
 * @ORM\Entity
 */
class Shippingcompany
{
    /**
     * @var int
     *
     * @ORM\Column(name="idshippingCompany", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idshippingcompany;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=45, nullable=false)
     */
    private $name;

    /**
     * @var int
     *
     * @ORM\Column(name="guideNumber", type="integer", nullable=false)
     */
    private $guidenumber = '0';

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="creationdate", type="datetime", nullable=false)
     */
    private $creationdate;

    /**
     * @var string|null
     *
     * @ORM\Column(name="trackingLink", type="string", length=100, nullable=true)
     */
    private $trackinglink;

    public function getIdshippingcompany(): ?int
    {
        return $this->idshippingcompany;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getGuidenumber(): ?int
    {
        return $this->guidenumber;
    }

    public function setGuidenumber(int $guidenumber): self
    {
        $this->guidenumber = $guidenumber;

        return $this;
    }

    public function getCreationdate(): ?\DateTimeInterface
    {
        return $this->creationdate;
    }

    public function setCreationdate(\DateTimeInterface $creationdate): self
    {
        $this->creationdate = $creationdate;

        return $this;
    }

    public function getTrackinglink(): ?string
    {
        return $this->trackinglink;
    }

    public function setTrackinglink(?string $trackinglink): self
    {
        $this->trackinglink = $trackinglink;

        return $this;
    }


}
