<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Membresiaproducto
 *
 * @ORM\Table(name="membresiaProducto", indexes={@ORM\Index(name="fk_membresiaProducto_producto1_idx", columns={"producto_idproducto"}), @ORM\Index(name="fk_membresiaProducto_membresiaCliente1_idx", columns={"membresiaCliente_idmembresiaCliente"})})
 * @ORM\Entity
 */
class Membresiaproducto
{
    /**
     * @var int
     *
     * @ORM\Column(name="idmembresiaProducto", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idmembresiaproducto;

    /**
     * @var string|null
     *
     * @ORM\Column(name="cantidad", type="string", length=45, nullable=true)
     */
    private $cantidad;

    /**
     * @var string|null
     *
     * @ORM\Column(name="descuento", type="string", length=45, nullable=true)
     */
    private $descuento;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \Membresiacliente
     *
     * @ORM\ManyToOne(targetEntity="Membresiacliente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="membresiaCliente_idmembresiaCliente", referencedColumnName="idmembresiaCliente")
     * })
     */
    private $membresiaclienteIdmembresiacliente;

    /**
     * @var \Producto
     *
     * @ORM\ManyToOne(targetEntity="Producto")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="producto_idproducto", referencedColumnName="idproducto")
     * })
     */
    private $productoIdproducto;

    public function getIdmembresiaproducto(): ?int
    {
        return $this->idmembresiaproducto;
    }

    public function getCantidad(): ?string
    {
        return $this->cantidad;
    }

    public function setCantidad(?string $cantidad): self
    {
        $this->cantidad = $cantidad;

        return $this;
    }

    public function getDescuento(): ?string
    {
        return $this->descuento;
    }

    public function setDescuento(?string $descuento): self
    {
        $this->descuento = $descuento;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getMembresiaclienteIdmembresiacliente(): ?Membresiacliente
    {
        return $this->membresiaclienteIdmembresiacliente;
    }

    public function setMembresiaclienteIdmembresiacliente(?Membresiacliente $membresiaclienteIdmembresiacliente): self
    {
        $this->membresiaclienteIdmembresiacliente = $membresiaclienteIdmembresiacliente;

        return $this;
    }

    public function getProductoIdproducto(): ?Producto
    {
        return $this->productoIdproducto;
    }

    public function setProductoIdproducto(?Producto $productoIdproducto): self
    {
        $this->productoIdproducto = $productoIdproducto;

        return $this;
    }


}
