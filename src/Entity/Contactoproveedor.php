<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Contactoproveedor
 *
 * @ORM\Table(name="contactoProveedor", indexes={@ORM\Index(name="fk_contactoProveedor_proveedor1_idx", columns={"proveedor_idproveedor"})})
 * @ORM\Entity
 */
class Contactoproveedor
{
    /**
     * @var int
     *
     * @ORM\Column(name="idcontactoProveedor", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idcontactoproveedor;

    /**
     * @var string|null
     *
     * @ORM\Column(name="nombre", type="string", length=250, nullable=true)
     */
    private $nombre;

    /**
     * @var string|null
     *
     * @ORM\Column(name="telefono", type="string", length=150, nullable=true)
     */
    private $telefono;

    /**
     * @var string|null
     *
     * @ORM\Column(name="email", type="string", length=150, nullable=true)
     */
    private $email;

    /**
     * @var string|null
     *
     * @ORM\Column(name="notas", type="text", length=16777215, nullable=true)
     */
    private $notas;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \Proveedor
     *
     * @ORM\ManyToOne(targetEntity="Proveedor")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="proveedor_idproveedor", referencedColumnName="idproveedor")
     * })
     */
    private $proveedorIdproveedor;

    public function getIdcontactoproveedor(): ?int
    {
        return $this->idcontactoproveedor;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(?string $nombre): self
    {
        $this->nombre = $nombre;

        return $this;
    }

    public function getTelefono(): ?string
    {
        return $this->telefono;
    }

    public function setTelefono(?string $telefono): self
    {
        $this->telefono = $telefono;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getNotas(): ?string
    {
        return $this->notas;
    }

    public function setNotas(?string $notas): self
    {
        $this->notas = $notas;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getProveedorIdproveedor(): ?Proveedor
    {
        return $this->proveedorIdproveedor;
    }

    public function setProveedorIdproveedor(?Proveedor $proveedorIdproveedor): self
    {
        $this->proveedorIdproveedor = $proveedorIdproveedor;

        return $this;
    }


}
