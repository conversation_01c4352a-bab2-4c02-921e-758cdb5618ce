<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Empresacliente
 *
 * @ORM\Table(name="empresaCliente")
 * @ORM\Entity
 */
class Empresacliente
{
    /**
     * @var int
     *
     * @ORM\Column(name="idempresaCliente", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idempresacliente;

    /**
     * @var string
     *
     * @ORM\Column(name="nombre", type="string", length=45, nullable=false)
     */
    private $nombre;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="enterpriseClassification", type="string", length=45, nullable=true)
     */
    private $enterpriseclassification;

    public function getIdempresacliente(): ?int
    {
        return $this->idempresacliente;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(string $nombre): self
    {
        $this->nombre = $nombre;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getEnterpriseclassification(): ?string
    {
        return $this->enterpriseclassification;
    }

    public function setEnterpriseclassification(?string $enterpriseclassification): self
    {
        $this->enterpriseclassification = $enterpriseclassification;

        return $this;
    }

    public function __toString() {
        return $this->nombre;
    }


}
