<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Productimages
 *
 * @ORM\Table(name="productImages", indexes={@ORM\Index(name="fk_ProductImages_producto1_idx", columns={"producto_idproducto"})})
 * @ORM\Entity
 */
class Productimages
{
    /**
     * @var int
     *
     * @ORM\Column(name="idProductImages", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idproductimages;

    /**
     * @var int
     *
     * @ORM\Column(name="`order`", type="integer", nullable=false)
     */
    private $order = '0';

    /**
     * @var string
     *
     * @ORM\Column(name="fileName", type="string", length=200, nullable=false)
     */
    private $filename;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string
     *
     * @ORM\Column(name="mainImage", type="string", length=1, nullable=false, options={"fixed"=true})
     */
    private $mainimage = '0';

    /**
     * @var \Producto
     *
     * @ORM\ManyToOne(targetEntity="Producto")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="producto_idproducto", referencedColumnName="idproducto")
     * })
     */
    private $productoIdproducto;

    public function getIdproductimages(): ?int
    {
        return $this->idproductimages;
    }

    public function getOrder(): ?int
    {
        return $this->order;
    }

    public function setOrder(int $order): self
    {
        $this->order = $order;

        return $this;
    }

    public function getFilename(): ?string
    {
        return $this->filename;
    }

    public function setFilename(string $filename): self
    {
        $this->filename = $filename;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getMainimage(): ?string
    {
        return $this->mainimage;
    }

    public function setMainimage(string $mainimage): self
    {
        $this->mainimage = $mainimage;

        return $this;
    }

    public function getProductoIdproducto(): ?Producto
    {
        return $this->productoIdproducto;
    }

    public function setProductoIdproducto(?Producto $productoIdproducto): self
    {
        $this->productoIdproducto = $productoIdproducto;

        return $this;
    }


}
