<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * Documentos
 *
 * @ORM\Table(name="documentos", indexes={@ORM\Index(name="fk_documentos_categoriaDocumentos1_idx", columns={"categoriaDocumentos_idcategoriaDocumentos"}), @ORM\Index(name="fk_documentos_usuario1_idx", columns={"usuario_idusuario"})})
 * @ORM\Entity
 */
class Documentos
{
    private ?UploadedFile $file = null;
    const SERVER_PATH_TO_IMAGE_FOLDER = 'uploads/documentos';

    /**
     * @var int
     *
     * @ORM\Column(name="iddocumentos", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $iddocumentos;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="fechaCreacion", type="datetime", nullable=false)
     */
    private $fechacreacion;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="fechaActualizacion", type="datetime", nullable=false)
     */
    private $fechaactualizacion;

    /**
     * @var string
     *
     * @ORM\Column(name="nombreDocumento", type="string", length=45, nullable=false)
     */
    private $nombredocumento;

    /**
     * @var string
     *
     * @ORM\Column(name="archivo", type="string", length=45, nullable=false)
     */
    private $archivo;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \Categoriadocumentos
     *
     * @ORM\ManyToOne(targetEntity="Categoriadocumentos")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="categoriaDocumentos_idcategoriaDocumentos", referencedColumnName="idcategoriaDocumentos")
     * })
     */
    private $categoriadocumentosIdcategoriadocumentos;

    /**
     * Manages the copying of the file to the relevant place on the server
     */
    public function upload(): void
    {
        // the file property can be empty if the field is not required
        if (null === $this->getFile()) {
            return;
        }

        // we use the original file name here but you should
        // sanitize it at least to avoid any security issues

        // move takes the target directory and target filename as params
        $this->getFile()->move(
            self::SERVER_PATH_TO_IMAGE_FOLDER,
            $this->getFile()->getClientOriginalName()
        );

        // set the path property to the filename where you've saved the file
        $this->setArchivo($this->getFile()->getClientOriginalName());

        // clean up the file property as you won't need it anymore
        $this->setFile(null);
    }

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    public function getIddocumentos(): ?int
    {
        return $this->iddocumentos;
    }

    public function getFechacreacion(): ?\DateTimeInterface
    {
        return $this->fechacreacion;
    }

    public function setFechacreacion(\DateTimeInterface $fechacreacion): self
    {
        $this->fechacreacion = $fechacreacion;

        return $this;
    }

    public function getFechaactualizacion(): ?\DateTimeInterface
    {
        return $this->fechaactualizacion;
    }

    public function setFechaactualizacion(\DateTimeInterface $fechaactualizacion): self
    {
        $this->fechaactualizacion = $fechaactualizacion;

        return $this;
    }

    public function getNombredocumento(): ?string
    {
        return $this->nombredocumento;
    }

    public function setNombredocumento(string $nombredocumento): self
    {
        $this->nombredocumento = $nombredocumento;

        return $this;
    }

    public function getArchivo(): ?string
    {
        return $this->archivo;
    }

    public function setArchivo(string $archivo): self
    {
        $this->archivo = $archivo;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getCategoriadocumentosIdcategoriadocumentos(): ?Categoriadocumentos
    {
        return $this->categoriadocumentosIdcategoriadocumentos;
    }

    public function setCategoriadocumentosIdcategoriadocumentos(?Categoriadocumentos $categoriadocumentosIdcategoriadocumentos): self
    {
        $this->categoriadocumentosIdcategoriadocumentos = $categoriadocumentosIdcategoriadocumentos;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }

    public function getFile()
    {
        return $this->file;
    }

    public function setFile($file)
    {
        $this->file = $file;

        return $this;
    }

    public function isFile()
    {
        return $this->file != null;
    }




}