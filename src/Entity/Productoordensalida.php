<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Productoordensalida
 *
 * @ORM\Table(name="productoOrdenSalida", indexes={@ORM\Index(name="fk_productoOrdenSalida_ordenSalida1_idx", columns={"ordenSalida_idordenSalida"}), @ORM\Index(name="fk_productoOrdenSalida_stock1_idx", columns={"stock_idstock"})})
 * @ORM\Entity
 */
class Productoordensalida
{
    /**
     * @var int
     *
     * @ORM\Column(name="idproductoOrdenSalida", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idproductoordensalida;

    /**
     * @var int|null
     *
     * @ORM\Column(name="cantidad", type="integer", nullable=true)
     */
    private $cantidad;

    /**
     * @var \Ordensalida
     *
     * @ORM\ManyToOne(targetEntity="Ordensalida")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="ordenSalida_idordenSalida", referencedColumnName="idordenSalida")
     * })
     */
    private $ordensalidaIdordensalida;

    /**
     * @var \Stock
     *
     * @ORM\ManyToOne(targetEntity="Stock")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="stock_idstock", referencedColumnName="idstock")
     * })
     */
    private $stockIdstock;

    public function getIdproductoordensalida(): ?int
    {
        return $this->idproductoordensalida;
    }

    public function getCantidad(): ?int
    {
        return $this->cantidad;
    }

    public function setCantidad(?int $cantidad): self
    {
        $this->cantidad = $cantidad;

        return $this;
    }

    public function getOrdensalidaIdordensalida(): ?Ordensalida
    {
        return $this->ordensalidaIdordensalida;
    }

    public function setOrdensalidaIdordensalida(?Ordensalida $ordensalidaIdordensalida): self
    {
        $this->ordensalidaIdordensalida = $ordensalidaIdordensalida;

        return $this;
    }

    public function getStockIdstock(): ?Stock
    {
        return $this->stockIdstock;
    }

    public function setStockIdstock(?Stock $stockIdstock): self
    {
        $this->stockIdstock = $stockIdstock;

        return $this;
    }


}
