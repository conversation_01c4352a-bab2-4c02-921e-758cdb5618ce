<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Mktlist
 *
 * @ORM\Table(name="mktList")
 * @ORM\Entity
 */
class Mktlist
{
    /**
     * @var int
     *
     * @ORM\Column(name="idmktList", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idmktlist;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=45, nullable=false)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="createdAt", type="datetime", nullable=false)
     */
    private $createdat;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="updatedAt", type="datetime", nullable=false)
     */
    private $updatedat;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="specificUpdateDate", type="datetime", nullable=true)
     */
    private $specificupdatedate;

    /**
     * @var string|null
     *
     * @ORM\Column(name="updateEvery", type="text", length=65535, nullable=true)
     */
    private $updateevery;

    /**
     * @var string
     *
     * @ORM\Column(name="queryParameters", type="text", length=65535, nullable=false)
     */
    private $queryparameters;

    /**
     * @var string|null
     *
     * @ORM\Column(name="clientJson", type="text", length=0, nullable=true)
     */
    private $clientjson;

    public function getIdmktlist(): ?int
    {
        return $this->idmktlist;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getCreatedat(): ?\DateTimeInterface
    {
        return $this->createdat;
    }

    public function setCreatedat(\DateTimeInterface $createdat): self
    {
        $this->createdat = $createdat;

        return $this;
    }

    public function getUpdatedat(): ?\DateTimeInterface
    {
        return $this->updatedat;
    }

    public function setUpdatedat(\DateTimeInterface $updatedat): self
    {
        $this->updatedat = $updatedat;

        return $this;
    }

    public function getSpecificupdatedate(): ?\DateTimeInterface
    {
        return $this->specificupdatedate;
    }

    public function setSpecificupdatedate(?\DateTimeInterface $specificupdatedate): self
    {
        $this->specificupdatedate = $specificupdatedate;

        return $this;
    }

    public function getUpdateevery(): ?string
    {
        return $this->updateevery;
    }

    public function setUpdateevery(?string $updateevery): self
    {
        $this->updateevery = $updateevery;

        return $this;
    }

    public function getQueryparameters(): ?string
    {
        return $this->queryparameters;
    }

    public function setQueryparameters(string $queryparameters): self
    {
        $this->queryparameters = $queryparameters;

        return $this;
    }

    public function getClientjson(): ?string
    {
        return $this->clientjson;
    }

    public function setClientjson(?string $clientjson): self
    {
        $this->clientjson = $clientjson;

        return $this;
    }


}
