<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Documentosgraduacion
 *
 * @ORM\Table(name="documentosGraduacion", indexes={@ORM\Index(name="fk_documentosGraduacion_graduacion1_idx", columns={"graduacion_idgraduacion"})})
 * @ORM\Entity
 */
class Documentosgraduacion
{
    /**
     * @var int
     *
     * @ORM\Column(name="iddocumentosGraduacion", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $iddocumentosgraduacion;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="creation", type="datetime", nullable=false)
     */
    private $creation;

    /**
     * @var string
     *
     * @ORM\Column(name="documentType", type="string", length=45, nullable=false)
     */
    private $documenttype;

    /**
     * @var string
     *
     * @ORM\Column(name="filename", type="string", length=45, nullable=false)
     */
    private $filename;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \Graduacion
     *
     * @ORM\ManyToOne(targetEntity="Graduacion")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="graduacion_idgraduacion", referencedColumnName="idgraduacion")
     * })
     */
    private $graduacionIdgraduacion;

    public function getIddocumentosgraduacion(): ?int
    {
        return $this->iddocumentosgraduacion;
    }

    public function getCreation(): ?\DateTimeInterface
    {
        return $this->creation;
    }

    public function setCreation(\DateTimeInterface $creation): self
    {
        $this->creation = $creation;

        return $this;
    }

    public function getDocumenttype(): ?string
    {
        return $this->documenttype;
    }

    public function setDocumenttype(string $documenttype): self
    {
        $this->documenttype = $documenttype;

        return $this;
    }

    public function getFilename(): ?string
    {
        return $this->filename;
    }

    public function setFilename(string $filename): self
    {
        $this->filename = $filename;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getGraduacionIdgraduacion(): ?Graduacion
    {
        return $this->graduacionIdgraduacion;
    }

    public function setGraduacionIdgraduacion(?Graduacion $graduacionIdgraduacion): self
    {
        $this->graduacionIdgraduacion = $graduacionIdgraduacion;

        return $this;
    }


}
