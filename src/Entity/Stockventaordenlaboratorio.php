<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM; 

/**
 * Stockventaordenlaboratorio
 *
 * @ORM\Table(name="stockVentaOrdenLaboratorio", indexes={@ORM\Index(name="fk_stockVentaOrdenLaboratorio_ordenLaboratorio1_idx", columns={"ordenLaboratorio_idordenLaboratorio"}), @ORM\Index(name="fk_stockVentaOrdenLaboratorio_stockVenta1_idx", columns={"stockVenta_idstockVenta"}), @ORM\Index(name="fk_stockVentaOrdenLaboratorio_stockVentaOrdenLaboratorio1_idx", columns={"stockVentaOrdenLaboratorio_idstockVentaOrdenLaboratorio"})})
 * @ORM\Entity
 */
class Stockventaordenlaboratorio
{
    /**
     * @var int
     *
     * @ORM\Column(name="idstockVentaOrdenLaboratorio", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idstockventaordenlaboratorio;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=true)
     */
    private $creacion;

    /**
     * @var string
     *
     * @ORM\Column(name="alreadySet", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $alreadyset = '1';

    /**
     * @var string
     *
     * @ORM\Column(name="mainProduct", type="string", length=2, nullable=false, options={"default"="1","fixed"=true})
     */
    private $mainproduct = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="revision", type="text", length=65535, nullable=true)
     */
    private $revision;

    /**
     * @var string|null
     *
     * @ORM\Column(name="wasDelivered", type="string", length=1, nullable=true)
     */
    private $wasdelivered = '0';

    /**
     * @var \Ordenlaboratorio
     *
     * @ORM\ManyToOne(targetEntity="Ordenlaboratorio", inversedBy="stockventaordenlaboratorios")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="ordenLaboratorio_idordenLaboratorio", referencedColumnName="idordenLaboratorio")
     * })
     */
    private $ordenlaboratorioIdordenlaboratorio;

    /**
     * @var \Stockventa
     *
     * @ORM\ManyToOne(targetEntity="Stockventa")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="stockVenta_idstockVenta", referencedColumnName="idstockVenta")
     * })
     */
    private $stockventaIdstockventa;

    /**
     * @var \Stockventaordenlaboratorio
     *
     * @ORM\ManyToOne(targetEntity="Stockventaordenlaboratorio")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="stockVentaOrdenLaboratorio_idstockVentaOrdenLaboratorio", referencedColumnName="idstockVentaOrdenLaboratorio")
     * })
     */
    private $stockventaordenlaboratorioIdstockventaordenlaboratorio;

    public function getIdstockventaordenlaboratorio(): ?int
    {
        return $this->idstockventaordenlaboratorio;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(?\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getAlreadyset(): ?string
    {
        return $this->alreadyset;
    }

    public function setAlreadyset(string $alreadyset): self
    {
        $this->alreadyset = $alreadyset;

        return $this;
    }

    public function getMainproduct(): ?string
    {
        return $this->mainproduct;
    }

    public function setMainproduct(string $mainproduct): self
    {
        $this->mainproduct = $mainproduct;

        return $this;
    }

    public function getRevision(): ?string
    {
        return $this->revision;
    }

    public function setRevision(?string $revision): self
    {
        $this->revision = $revision;

        return $this;
    }

    public function getWasdelivered(): ?string
    {
        return $this->wasdelivered;
    }

    public function setWasdelivered(?string $wasdelivered): self
    {
        $this->wasdelivered = $wasdelivered;

        return $this;
    }

    public function getOrdenlaboratorioIdordenlaboratorio(): ?Ordenlaboratorio
    {
        return $this->ordenlaboratorioIdordenlaboratorio;
    }

    public function setOrdenlaboratorioIdordenlaboratorio(?Ordenlaboratorio $ordenlaboratorioIdordenlaboratorio): self
    {
        $this->ordenlaboratorioIdordenlaboratorio = $ordenlaboratorioIdordenlaboratorio;

        return $this;
    }

    public function getStockventaIdstockventa(): ?Stockventa
    {
        return $this->stockventaIdstockventa;
    }

    public function setStockventaIdstockventa(?Stockventa $stockventaIdstockventa): self
    {
        $this->stockventaIdstockventa = $stockventaIdstockventa;

        return $this;
    }

    public function getStockventaordenlaboratorioIdstockventaordenlaboratorio(): ?self
    {
        return $this->stockventaordenlaboratorioIdstockventaordenlaboratorio;
    }

    public function setStockventaordenlaboratorioIdstockventaordenlaboratorio(?self $stockventaordenlaboratorioIdstockventaordenlaboratorio): self
    {
        $this->stockventaordenlaboratorioIdstockventaordenlaboratorio = $stockventaordenlaboratorioIdstockventaordenlaboratorio;

        return $this;
    }


}
