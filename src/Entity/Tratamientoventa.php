<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Tratamientoventa
 *
 * @ORM\Table(name="tratamientoVenta", indexes={@ORM\Index(name="fk_tratamientoVenta_tratamiento1_idx", columns={"tratamiento_idtratamiento"}), @ORM\Index(name="fk_tratamientoVenta_venta1_idx", columns={"venta_idventa"})})
 * @ORM\Entity
 */
class Tratamientoventa
{
    /**
     * @var int
     *
     * @ORM\Column(name="idtratamientoVenta", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idtratamientoventa;

    /**
     * @var string
     *
     * @ORM\Column(name="precio", type="decimal", precision=9, scale=2, nullable=false)
     */
    private $precio;

    /**
     * @var string|null
     *
     * @ORM\Column(name="costo", type="decimal", precision=9, scale=2, nullable=true)
     */
    private $costo;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"fixed"=true})
     */
    private $status;

    /**
     * @var \Tratamiento
     *
     * @ORM\ManyToOne(targetEntity="Tratamiento")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="tratamiento_idtratamiento", referencedColumnName="idtratamiento")
     * })
     */
    private $tratamientoIdtratamiento;

    /**
     * @var \Venta
     *
     * @ORM\ManyToOne(targetEntity="Venta")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="venta_idventa", referencedColumnName="idventa")
     * })
     */
    private $ventaIdventa;

    public function getIdtratamientoventa(): ?int
    {
        return $this->idtratamientoventa;
    }

    public function getPrecio(): ?string
    {
        return $this->precio;
    }

    public function setPrecio(string $precio): self
    {
        $this->precio = $precio;

        return $this;
    }

    public function getCosto(): ?string
    {
        return $this->costo;
    }

    public function setCosto(?string $costo): self
    {
        $this->costo = $costo;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getTratamientoIdtratamiento(): ?Tratamiento
    {
        return $this->tratamientoIdtratamiento;
    }

    public function setTratamientoIdtratamiento(?Tratamiento $tratamientoIdtratamiento): self
    {
        $this->tratamientoIdtratamiento = $tratamientoIdtratamiento;

        return $this;
    }

    public function getVentaIdventa(): ?Venta
    {
        return $this->ventaIdventa;
    }

    public function setVentaIdventa(?Venta $ventaIdventa): self
    {
        $this->ventaIdventa = $ventaIdventa;

        return $this;
    }


}
