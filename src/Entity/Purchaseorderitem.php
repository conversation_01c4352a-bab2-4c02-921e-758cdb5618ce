<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Purchaseorderitem
 *
 * @ORM\Table(name="purchaseOrderItem", indexes={@ORM\Index(name="fk_purchaseOrderItem_purchaseOrder1_idx", columns={"purchaseOrder_idPurchaseOrder"}), @ORM\Index(name="fk_purchaseOrderItem_stockventaordenlaboratorio1_idx", columns={"stockventaordenlaboratorio_idstockVentaOrdenLaboratorio"})})
 * @ORM\Entity
 */
class Purchaseorderitem
{
    /**
     * @var int
     *
     * @ORM\Column(name="idpurchaseOrderItem", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idpurchaseorderitem;

    /**
     * @var \Purchaseorder
     *
     * @ORM\ManyToOne(targetEntity="Purchaseorder")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="purchaseOrder_idPurchaseOrder", referencedColumnName="idPurchaseOrder")
     * })
     */
    private $purchaseorderIdpurchaseorder;

    /**
     * @var \Stockventaordenlaboratorio
     *
     * @ORM\ManyToOne(targetEntity="Stockventaordenlaboratorio")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="stockventaordenlaboratorio_idstockVentaOrdenLaboratorio", referencedColumnName="idstockVentaOrdenLaboratorio")
     * })
     */
    private $stockventaordenlaboratorioIdstockventaordenlaboratorio;

    public function getIdpurchaseorderitem(): ?int
    {
        return $this->idpurchaseorderitem;
    }

    public function getPurchaseorderIdpurchaseorder(): ?Purchaseorder
    {
        return $this->purchaseorderIdpurchaseorder;
    }

    public function setPurchaseorderIdpurchaseorder(?Purchaseorder $purchaseorderIdpurchaseorder): self
    {
        $this->purchaseorderIdpurchaseorder = $purchaseorderIdpurchaseorder;

        return $this;
    }

    public function getStockventaordenlaboratorioIdstockventaordenlaboratorio(): ?Stockventaordenlaboratorio
    {
        return $this->stockventaordenlaboratorioIdstockventaordenlaboratorio;
    }

    public function setStockventaordenlaboratorioIdstockventaordenlaboratorio(?Stockventaordenlaboratorio $stockventaordenlaboratorioIdstockventaordenlaboratorio): self
    {
        $this->stockventaordenlaboratorioIdstockventaordenlaboratorio = $stockventaordenlaboratorioIdstockventaordenlaboratorio;

        return $this;
    }


}
