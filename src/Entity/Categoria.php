<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Categoria
 *
 * @ORM\Table(name="categoria", indexes={@ORM\Index(name="fk_categoria_clase1_idx", columns={"clase_idclase"})})
 * @ORM\Entity
 */
class Categoria
{
    /**
     * @var int
     *
     * @ORM\Column(name="idcategoria", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idcategoria;

    /**
     * @var string|null
     *
     * @ORM\Column(name="codigo", type="string", length=45, nullable=true)
     */
    private $codigo;

    /**
     * @var string
     *
     * @ORM\Column(name="nombre", type="string", length=45, nullable=false)
     */
    private $nombre;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="sat", type="string", length=300, nullable=true)
     */
    private $sat;

    /**
     * @var \Clase
     *
     * @ORM\ManyToOne(targetEntity="Clase")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="clase_idclase", referencedColumnName="idclase")
     * })
     */
    private $claseIdclase;

    public function getIdcategoria(): ?int
    {
        return $this->idcategoria;
    }

    public function getCodigo(): ?string
    {
        return $this->codigo;
    }

    public function setCodigo(?string $codigo): self
    {
        $this->codigo = $codigo;

        return $this;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(string $nombre): self
    {
        $this->nombre = $nombre;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getSat(): ?string
    {
        return $this->sat;
    }

    public function setSat(?string $sat): self
    {
        $this->sat = $sat;

        return $this;
    }

    public function getClaseIdclase(): ?Clase
    {
        return $this->claseIdclase;
    }

    public function setClaseIdclase(?Clase $claseIdclase): self
    {
        $this->claseIdclase = $claseIdclase;

        return $this;
    }

    public function  __toString(){
        return $this->getNombre();
    }

}
