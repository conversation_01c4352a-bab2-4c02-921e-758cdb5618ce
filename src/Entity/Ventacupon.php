<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Ventacupon
 *
 * @ORM\Table(name="ventaCupon", indexes={@ORM\Index(name="fk_ventaCupon_venta1_idx", columns={"venta_idventa"}), @ORM\Index(name="fk_ventaCupon_cupon1_idx", columns={"cupon_idcupon"})})
 * @ORM\Entity
 */
class Ventacupon
{
    /**
     * @var int
     *
     * @ORM\Column(name="idventaCupon", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idventacupon;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaCreacion", type="datetime", nullable=true)
     */
    private $fechacreacion;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaActualizacion", type="datetime", nullable=true)
     */
    private $fechaactualizacion;

    /**
     * @var float|null
     *
     * @ORM\Column(name="porcentajeDescuento", type="float", precision=10, scale=0, nullable=true)
     */
    private $porcentajedescuento;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \Cupon
     *
     * @ORM\ManyToOne(targetEntity="Cupon")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="cupon_idcupon", referencedColumnName="idcupon")
     * })
     */
    private $cuponIdcupon;

    /**
     * @var \Venta
     *
     * @ORM\ManyToOne(targetEntity="Venta")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="venta_idventa", referencedColumnName="idventa")
     * })
     */
    private $ventaIdventa;

    public function getIdventacupon(): ?int
    {
        return $this->idventacupon;
    }

    public function getFechacreacion(): ?\DateTimeInterface
    {
        return $this->fechacreacion;
    }

    public function setFechacreacion(?\DateTimeInterface $fechacreacion): self
    {
        $this->fechacreacion = $fechacreacion;

        return $this;
    }

    public function getFechaactualizacion(): ?\DateTimeInterface
    {
        return $this->fechaactualizacion;
    }

    public function setFechaactualizacion(?\DateTimeInterface $fechaactualizacion): self
    {
        $this->fechaactualizacion = $fechaactualizacion;

        return $this;
    }

    public function getPorcentajedescuento(): ?float
    {
        return $this->porcentajedescuento;
    }

    public function setPorcentajedescuento(?float $porcentajedescuento): self
    {
        $this->porcentajedescuento = $porcentajedescuento;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getCuponIdcupon(): ?Cupon
    {
        return $this->cuponIdcupon;
    }

    public function setCuponIdcupon(?Cupon $cuponIdcupon): self
    {
        $this->cuponIdcupon = $cuponIdcupon;

        return $this;
    }

    public function getVentaIdventa(): ?Venta
    {
        return $this->ventaIdventa;
    }

    public function setVentaIdventa(?Venta $ventaIdventa): self
    {
        $this->ventaIdventa = $ventaIdventa;

        return $this;
    }


}
