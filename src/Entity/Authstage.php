<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Authstage
 *
 * @ORM\Table(name="authStage")
 * @ORM\Entity
 */
class Authstage
{
    /**
     * @var int
     *
     * @ORM\Column(name="idauthStage", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idauthstage;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=45, nullable=false)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var int
     *
     * @ORM\Column(name="stageOrder", type="integer", nullable=false)
     */
    private $stageorder;

    public function getIdauthstage(): ?int
    {
        return $this->idauthstage;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getStageorder(): ?int
    {
        return $this->stageorder;
    }

    public function setStageorder(int $stageorder): self
    {
        $this->stageorder = $stageorder;

        return $this;
    }


}
