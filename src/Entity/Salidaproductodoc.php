<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Salidaproductodoc
 *
 * @ORM\Table(name="salidaProductoDoc", indexes={@ORM\Index(name="fk_salidaProductoDoc_merma1_idx", columns={"merma_idMerma"}), @ORM\Index(name="fk_salidaProductoDoc_usuario1_idx", columns={"usuario_idusuario"})})
 * @ORM\Entity
 */
class Salidaproductodoc
{
    /**
     * @var int
     *
     * @ORM\Column(name="idsalidaProductoDoc", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idsalidaproductodoc;

    /**
     * @var string|null
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=true, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="filemerma", type="string", length=300, nullable=true)
     */
    private $filemerma;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=true)
     */
    private $creacion;

    /**
     * @var string
     *
     * @ORM\Column(name="tipo", type="string", length=100, nullable=false)
     */
    private $tipo;

    /**
     * @var string|null
     *
     * @ORM\Column(name="nombre", type="string", length=150, nullable=true)
     */
    private $nombre;

    /**
     * @var \Merma
     *
     * @ORM\ManyToOne(targetEntity="Merma")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="merma_idMerma", referencedColumnName="idMerma")
     * })
     */
    private $mermaIdmerma;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    public function getIdsalidaproductodoc(): ?int
    {
        return $this->idsalidaproductodoc;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getFilemerma(): ?string
    {
        return $this->filemerma;
    }

    public function setFilemerma(?string $filemerma): self
    {
        $this->filemerma = $filemerma;

        return $this;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(?\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getTipo(): ?string
    {
        return $this->tipo;
    }

    public function setTipo(string $tipo): self
    {
        $this->tipo = $tipo;

        return $this;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(?string $nombre): self
    {
        $this->nombre = $nombre;

        return $this;
    }

    public function getMermaIdmerma(): ?Merma
    {
        return $this->mermaIdmerma;
    }

    public function setMermaIdmerma(?Merma $mermaIdmerma): self
    {
        $this->mermaIdmerma = $mermaIdmerma;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }


}
