<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Venta
 *
 * @ORM\Table(name="venta", indexes={@ORM\Index(name="fk_venta_authStage1_idx", columns={"authStage_idauthStage"}), @ORM\Index(name="fk_venta_cliente1_idx", columns={"cliente_idcliente"}), @ORM\Index(name="fk_venta_sucursal1_idx", columns={"sucursal_idsucursal"}), @ORM\Index(name="fk_venta_tipoVenta1_idx", columns={"tipoVenta_idtipoVenta"}), @ORM\Index(name="fk_venta_unidad1_idx", columns={"unidad_idunidad"}), @ORM\Index(name="fk_venta_usuario1_idx", columns={"gerente"}), @ORM\Index(name="fk_venta_usuario2_idx", columns={"usuario_responsableCancelacion"}), @ORM\Index(name="fk_venta_usuario_idx", columns={"usuario_idusuario"}), @ORM\Index(name="fk_venta_venta1_idx", columns={"venta_garantia"}), @ORM\Index(name="fk_venta_ventaGroup1_idx", columns={"ventaGroup_idventaGroup"})})
 * @ORM\Entity
 */
class Venta
{
    /**
     * @var int
     *
     * @ORM\Column(name="idventa", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idventa;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="fecha", type="datetime", nullable=false)
     */
    private $fecha;

    /**
     * @var string
     *
     * @ORM\Column(name="total", type="decimal", precision=15, scale=2, nullable=false)
     */
    private $total;

    /**
     * @var float
     *
     * @ORM\Column(name="porcentajeIva", type="float", precision=10, scale=0, nullable=false)
     */
    private $porcentajeiva;

    /**
     * @var string
     *
     * @ORM\Column(name="iva", type="decimal", precision=9, scale=2, nullable=false)
     */
    private $iva;

    /**
     * @var float|null
     *
     * @ORM\Column(name="descuento", type="float", precision=10, scale=0, nullable=true)
     */
    private $descuento;

    /**
     * @var string|null
     *
     * @ORM\Column(name="pagado", type="decimal", precision=15, scale=2, nullable=true)
     */
    private $pagado;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="beneficiario", type="string", length=850, nullable=true)
     */
    private $beneficiario;

    /**
     * @var int
     *
     * @ORM\Column(name="folio", type="integer", nullable=false, options={"unsigned"=true})
     */
    private $folio;

    /**
     * @var string|null
     *
     * @ORM\Column(name="ticketPdf", type="string", length=250, nullable=true)
     */
    private $ticketpdf;

    /**
     * @var string
     *
     * @ORM\Column(name="cotizacion", type="string", length=1, nullable=false, options={"fixed"=true})
     */
    private $cotizacion = '0';

    /**
     * @var string|null
     *
     * @ORM\Column(name="tipoPago", type="string", length=100, nullable=true)
     */
    private $tipopago;

    /**
     * @var string|null
     *
     * @ORM\Column(name="seDescontoDeInventario", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $sedescontodeinventario;

    /**
     * @var string|null
     *
     * @ORM\Column(name="pidioFactura", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $pidiofactura;

    /**
     * @var string|null
     *
     * @ORM\Column(name="convenio", type="string", length=150, nullable=true)
     */
    private $convenio;

    /**
     * @var string|null
     *
     * @ORM\Column(name="seApartoArmazon", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $seapartoarmazon;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaCreacion", type="datetime", nullable=true)
     */
    private $fechacreacion;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaVenta", type="datetime", nullable=true)
     */
    private $fechaventa;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaActualizacion", type="datetime", nullable=true)
     */
    private $fechaactualizacion;

    /**
     * @var string|null
     *
     * @ORM\Column(name="porqueSeCancelo", type="text", length=16777215, nullable=true)
     */
    private $porquesecancelo;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaCancelacion", type="datetime", nullable=true)
     */
    private $fechacancelacion;

    /**
     * @var string|null
     *
     * @ORM\Column(name="archivoAutorizacion", type="string", length=150, nullable=true)
     */
    private $archivoautorizacion;

    /**
     * @var string|null
     *
     * @ORM\Column(name="notas", type="text", length=65535, nullable=true)
     */
    private $notas;

    /**
     * @var string|null
     *
     * @ORM\Column(name="ventaproductodebajodelcosto", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $ventaproductodebajodelcosto;

    /**
     * @var string
     *
     * @ORM\Column(name="liquidada", type="string", length=1, nullable=false, options={"fixed"=true})
     */
    private $liquidada;

    /**
     * @var string
     *
     * @ORM\Column(name="credito", type="string", length=1, nullable=false, options={"fixed"=true})
     */
    private $credito;

    /**
     * @var int
     *
     * @ORM\Column(name="diasCredito", type="integer", nullable=false, options={"unsigned"=true})
     */
    private $diascredito;

    /**
     * @var string|null
     *
     * @ORM\Column(name="authorizationNumber", type="string", length=50, nullable=true)
     */
    private $authorizationnumber;

    /**
     * @var string|null
     *
     * @ORM\Column(name="onlinePaymentReference", type="string", length=100, nullable=true)
     */
    private $onlinepaymentreference;

    /**
     * @var string|null
     *
     * @ORM\Column(name="autorizacionState", type="string", length=2, nullable=true)
     */
    private $autorizacionstate = '0';

    /**
     * @var string|null
     *
     * @ORM\Column(name="facturacionState", type="string", length=45, nullable=true)
     */
    private $facturacionstate = '0';

    /**
     * @var string|null
     *
     * @ORM\Column(name="authScan", type="text", length=65535, nullable=true)
     */
    private $authscan;

    /**
     * @var string|null
     *
     * @ORM\Column(name="tickerPdfEspecial", type="string", length=250, nullable=true)
     */
    private $tickerpdfespecial;

    /**
     * @var string|null
     *
     * @ORM\Column(name="deuda", type="decimal", precision=15, scale=2, nullable=true, options={"default"="0.00"})
     */
    private $deuda = '0.00';

    /**
     * @var string|null
     *
     * @ORM\Column(name="pagadoTotal", type="decimal", precision=15, scale=2, nullable=true)
     */
    private $pagadototal;

    /**
     * @var string|null
     *
     * @ORM\Column(name="products", type="text", length=65535, nullable=true)
     */
    private $products;

    /**
     * @var \Unidad
     *
     * @ORM\ManyToOne(targetEntity="Unidad")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="unidad_idunidad", referencedColumnName="idunidad")
     * })
     */
    private $unidadIdunidad;

    /**
     * @var \Sucursal
     *
     * @ORM\ManyToOne(targetEntity="Sucursal")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="sucursal_idsucursal", referencedColumnName="idsucursal")
     * })
     */
    private $sucursalIdsucursal;

    /**
     * @var \Sucursal
     *
     * @ORM\ManyToOne(targetEntity="Sucursal")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="sucursal_idsucursal", referencedColumnName="idsucursal")
     * })
     */
    private $campana;

    /**
     * @var \Sucursal
     *
     * @ORM\ManyToOne(targetEntity="Sucursal")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="sucursal_idsucursal", referencedColumnName="idsucursal")
     * })
     */
    private $bodega;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="gerente", referencedColumnName="idusuario")
     * })
     */
    private $gerente;

    /**
     * @var \Cliente
     *
     * @ORM\ManyToOne(targetEntity="Cliente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="cliente_idcliente", referencedColumnName="idcliente")
     * })
     */
    private $clienteIdcliente;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_responsableCancelacion", referencedColumnName="idusuario")
     * })
     */
    private $usuarioResponsablecancelacion;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    /**
     * @var \Tipoventa
     *
     * @ORM\ManyToOne(targetEntity="Tipoventa")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="tipoVenta_idtipoVenta", referencedColumnName="idtipoVenta")
     * })
     */
    private $tipoventaIdtipoventa;

    /**
     * @var \Venta
     *
     * @ORM\ManyToOne(targetEntity="Venta")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="venta_garantia", referencedColumnName="idventa")
     * })
     */
    private $ventaGarantia;

    /**
     * @var \Authstage
     *
     * @ORM\ManyToOne(targetEntity="Authstage")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="authStage_idauthStage", referencedColumnName="idauthStage")
     * })
     */
    private $authstageIdauthstage;

    /**
     * @var \Ventagroup
     *
     * @ORM\ManyToOne(targetEntity="Ventagroup")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="ventaGroup_idventaGroup", referencedColumnName="idventaGroup")
     * })
     */
    private $ventagroupIdventagroup;

    /**
     * @var string|null
     *
     * @ORM\Column(name="ticketGraduacion", type="string", length=250, nullable=true)
     */
    private $ticketgraduacion;

    public function getIdventa(): ?int
    {
        return $this->idventa;
    }

    public function getFecha(): ?\DateTimeInterface
    {
        return $this->fecha;
    }

    public function setFecha(\DateTimeInterface $fecha): self
    {
        $this->fecha = $fecha;

        return $this;
    }

    public function getTotal(): ?string
    {
        return $this->total;
    }

    public function setTotal(string $total): self
    {
        $this->total = $total;

        return $this;
    }

    public function getPorcentajeiva(): ?float
    {
        return $this->porcentajeiva;
    }

    public function setPorcentajeiva(float $porcentajeiva): self
    {
        $this->porcentajeiva = $porcentajeiva;

        return $this;
    }

    public function getIva(): ?string
    {
        return $this->iva;
    }

    public function setIva(string $iva): self
    {
        $this->iva = $iva;

        return $this;
    }

    public function getDescuento(): ?float
    {
        return $this->descuento;
    }

    public function setDescuento(?float $descuento): self
    {
        $this->descuento = $descuento;

        return $this;
    }

    public function getPagado(): ?string
    {
        return $this->pagado;
    }

    public function setPagado(string $pagado): self
    {
        $this->pagado = $pagado;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getBeneficiario(): ?string
    {
        return $this->beneficiario;
    }

    public function setBeneficiario(?string $beneficiario): self
    {
        $this->beneficiario = $beneficiario;

        return $this;
    }

    public function getFolio(): ?int
    {
        return $this->folio;
    }

    public function setFolio(int $folio): self
    {
        $this->folio = $folio;

        return $this;
    }

    public function getTicketpdf(): ?string
    {
        return $this->ticketpdf;
    }

    public function setTicketpdf(?string $ticketpdf): self
    {
        $this->ticketpdf = $ticketpdf;

        return $this;
    }

    public function getPagadototal(): ?string
    {
        return $this->pagadototal;
    }

    public function setPagadototal(?string $pagadototal): self
    {
        $this->pagadototal = $pagadototal;

        return $this;
    }

    public function getDeuda(): ?string
    {
        return $this->deuda;
    }

    public function setDeuda(string $deuda): self
    {
        $this->deuda = $deuda;

        return $this;
    }

    public function getCotizacion(): ?string
    {
        return $this->cotizacion;
    }

    public function setCotizacion(string $cotizacion): self
    {
        $this->cotizacion = $cotizacion;

        return $this;
    }

    public function getTipopago(): ?string
    {
        return $this->tipopago;
    }

    public function setTipopago(?string $tipopago): self
    {
        $this->tipopago = $tipopago;

        return $this;
    }

    public function getSedescontodeinventario(): ?string
    {
        return $this->sedescontodeinventario;
    }

    public function setSedescontodeinventario(?string $sedescontodeinventario): self
    {
        $this->sedescontodeinventario = $sedescontodeinventario;

        return $this;
    }

    public function getPidiofactura(): ?string
    {
        return $this->pidiofactura;
    }

    public function setPidiofactura(?string $pidiofactura): self
    {
        $this->pidiofactura = $pidiofactura;

        return $this;
    }

    public function getConvenio(): ?string
    {
        return $this->convenio;
    }

    public function setConvenio(?string $convenio): self
    {
        $this->convenio = $convenio;

        return $this;
    }

    public function getSeapartoarmazon(): ?string
    {
        return $this->seapartoarmazon;
    }

    public function setSeapartoarmazon(?string $seapartoarmazon): self
    {
        $this->seapartoarmazon = $seapartoarmazon;

        return $this;
    }

    public function getFechacreacion(): ?\DateTimeInterface
    {
        return $this->fechacreacion;
    }

    public function setFechacreacion(?\DateTimeInterface $fechacreacion): self
    {
        $this->fechacreacion = $fechacreacion;

        return $this;
    }

    public function getFechaventa(): ?\DateTimeInterface
    {
        return $this->fechaventa;
    }

    public function setFechaventa(?\DateTimeInterface $fechaventa): self
    {
        $this->fechaventa = $fechaventa;

        return $this;
    }

    public function getFechaactualizacion(): ?\DateTimeInterface
    {
        return $this->fechaactualizacion;
    }

    public function setFechaactualizacion(?\DateTimeInterface $fechaactualizacion): self
    {
        $this->fechaactualizacion = $fechaactualizacion;

        return $this;
    }

    public function getPorquesecancelo(): ?string
    {
        return $this->porquesecancelo;
    }

    public function setPorquesecancelo(?string $porquesecancelo): self
    {
        $this->porquesecancelo = $porquesecancelo;

        return $this;
    }

    public function getFechacancelacion(): ?\DateTimeInterface
    {
        return $this->fechacancelacion;
    }

    public function setFechacancelacion(?\DateTimeInterface $fechacancelacion): self
    {
        $this->fechacancelacion = $fechacancelacion;

        return $this;
    }

    public function getArchivoautorizacion(): ?string
    {
        return $this->archivoautorizacion;
    }

    public function setArchivoautorizacion(?string $archivoautorizacion): self
    {
        $this->archivoautorizacion = $archivoautorizacion;

        return $this;
    }

    public function getNotas(): ?string
    {
        return $this->notas;
    }

    public function setNotas(?string $notas): self
    {
        $this->notas = $notas;

        return $this;
    }

    public function getVentaproductodebajodelcosto(): ?string
    {
        return $this->ventaproductodebajodelcosto;
    }

    public function setVentaproductodebajodelcosto(?string $ventaproductodebajodelcosto): self
    {
        $this->ventaproductodebajodelcosto = $ventaproductodebajodelcosto;

        return $this;
    }

    public function getLiquidada(): ?string
    {
        return $this->liquidada;
    }

    public function setLiquidada(string $liquidada): self
    {
        $this->liquidada = $liquidada;

        return $this;
    }

    public function getCredito(): ?string
    {
        return $this->credito;
    }

    public function setCredito(string $credito): self
    {
        $this->credito = $credito;

        return $this;
    }

    public function getDiascredito(): ?int
    {
        return $this->diascredito;
    }

    public function setDiascredito(int $diascredito): self
    {
        $this->diascredito = $diascredito;

        return $this;
    }

    public function getAuthorizationnumber(): ?string
    {
        return $this->authorizationnumber;
    }

    public function setAuthorizationnumber(?string $authorizationnumber): self
    {
        $this->authorizationnumber = $authorizationnumber;

        return $this;
    }

    public function getOnlinepaymentreference(): ?string
    {
        return $this->onlinepaymentreference;
    }

    public function setOnlinepaymentreference(?string $onlinepaymentreference): self
    {
        $this->onlinepaymentreference = $onlinepaymentreference;

        return $this;
    }

    public function getAutorizacionstate(): ?string
    {
        return $this->autorizacionstate;
    }

    public function setAutorizacionstate(?string $autorizacionstate): self
    {
        $this->autorizacionstate = $autorizacionstate;

        return $this;
    }

    public function getFacturacionstate(): ?string
    {
        return $this->facturacionstate;
    }

    public function setFacturacionstate(?string $facturacionstate): self
    {
        $this->facturacionstate = $facturacionstate;

        return $this;
    }

    public function getAuthscan(): ?string
    {
        return $this->authscan;
    }

    public function setAuthscan(?string $authscan): self
    {
        $this->authscan = $authscan;

        return $this;
    }

    public function getTickerpdfespecial(): ?string
    {
        return $this->tickerpdfespecial;
    }

    public function setTickerpdfespecial(?string $tickerpdfespecial): self
    {
        $this->tickerpdfespecial = $tickerpdfespecial;

        return $this;
    }

    public function getUnidadIdunidad(): ?Unidad
    {
        return $this->unidadIdunidad;
    }

    public function setUnidadIdunidad(?Unidad $unidadIdunidad): self
    {
        $this->unidadIdunidad = $unidadIdunidad;

        return $this;
    }

    public function getSucursalIdsucursal(): ?Sucursal
    {
        return $this->sucursalIdsucursal;
    }

    public function setSucursalIdsucursal(?Sucursal $sucursalIdsucursal): self
    {
        $this->bodega = $sucursalIdsucursal;
        $this->campana = $sucursalIdsucursal;
        $this->sucursalIdsucursal = $sucursalIdsucursal;
        return $this;
    }

    public function getGerente(): ?Usuario
    {
        return $this->gerente;
    }

    public function setGerente(?Usuario $gerente): self
    {
        $this->gerente = $gerente;

        return $this;
    }

    public function getClienteIdcliente(): ?Cliente
    {
        return $this->clienteIdcliente;
    }

    public function setClienteIdcliente(?Cliente $clienteIdcliente): self
    {
        $this->clienteIdcliente = $clienteIdcliente;

        return $this;
    }

    public function getUsuarioResponsablecancelacion(): ?Usuario
    {
        return $this->usuarioResponsablecancelacion;
    }

    public function setUsuarioResponsablecancelacion(?Usuario $usuarioResponsablecancelacion): self
    {
        $this->usuarioResponsablecancelacion = $usuarioResponsablecancelacion;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }

    public function getTipoventaIdtipoventa(): ?Tipoventa
    {
        return $this->tipoventaIdtipoventa;
    }

    public function setTipoventaIdtipoventa(?Tipoventa $tipoventaIdtipoventa): self
    {
        $this->tipoventaIdtipoventa = $tipoventaIdtipoventa;

        return $this;
    }

    public function getVentaGarantia(): ?self
    {
        return $this->ventaGarantia;
    }

    public function setVentaGarantia(?self $ventaGarantia): self
    {
        $this->ventaGarantia = $ventaGarantia;

        return $this;
    }

    public function getAuthstageIdauthstage(): ?Authstage
    {
        return $this->authstageIdauthstage;
    }

    public function setAuthstageIdauthstage(?Authstage $authstageIdauthstage): self
    {
        $this->authstageIdauthstage = $authstageIdauthstage;

        return $this;
    }

    public function getVentagroupIdventagroup(): ?Ventagroup
    {
        return $this->ventagroupIdventagroup;
    }

    public function setVentagroupIdventagroup(?Ventagroup $ventagroupIdventagroup): self
    {
        $this->ventagroupIdventagroup = $ventagroupIdventagroup;

        return $this;
    }

    private $serviceProducts;

    public function getServiceProducts(): ?int
    {
        return $this->serviceProducts;
    }

    public function setServiceProducts(int $serviceProducts): self
    {
        $this->serviceProducts = $serviceProducts;

        return $this;
    }

    private $storableProducts;

    public function getStorableProducts(): ?int
    {
        return $this->storableProducts;
    }

    public function setStorableProducts(int $storableProducts): self
    {
        $this->storableProducts = $storableProducts;

        return $this;
    }

    public function getCampana(): ?Sucursal
    {
        return $this->campana;
    }

    public function setCampana(?Sucursal $campana): self
    {
        $this->campana = $campana;

        return $this;
    }
    public function getBodega(): ?Sucursal
    {
        return $this->bodega;
    }

    public function setBodega(?Sucursal $bodega): self
    {
        $this->bodega = $bodega;

        return $this;
    }

    private $liquidatedDate;

    public function getLiquidatedDate(): ?string
    {
        return $this->liquidatedDate;
    }

    public function setLiquidatedDate(string $liquidatedDate): self
    {
        $this->liquidatedDate = $liquidatedDate;

        return $this;
    }

    public function getTicketgraduacion(): ?string
    {
        return $this->ticketgraduacion;
    }

    public function setTicketgraduacion(?string $ticketgraduacion): self
    {
        $this->ticketgraduacion = $ticketgraduacion;

        return $this;
    }

    public function __toString()
    {
        return strval($this->folio);
    }

    public function canceladoActivo()
    {
        $result = "";
        if ($this->getStatus() == "1") {
            $result = "Activo";
        } else {
            $result = "Cancelada";
        }
        return $result;
    }
    public function esCotizacionVenta()
    {
        $result = "";
        if ($this->getCotizacion() == "1") {
            $result = "Cotizacion";
        } else {
            $result = "Venta";
        }
        return $result;
    }

    public function getFormattedSaleQuotation()
    {
        $formattedType = ($this->cotizacion == '1') ? "Cotización" : "Venta";
        $formattedType = ($this->status == '1') ? $formattedType : "Cancelada";
        return $formattedType;
    }

    public function getPagoConIva()
    {
        return $this->total + $this->iva;
    }
    public function getFechaVentaForExport()
    {
        $formattedDate = ($this->fechaventa) ? $this->fechaventa->format('d/m/Y') : '';
        return $formattedDate;
    }

    public function getFechaCotizacionForExport()
    {
        $formattedDate = ($this->fechacreacion) ? $this->fechacreacion->format('d/m/Y') : '';
        return $formattedDate;
    }

    public function getFormattedStatus()
    {
        $formattedStatus = ($this->status == '1') ? "Activa" : "Cancelada";
        return $formattedStatus;
    }

    public function getFormattedQuotation()
    {
        $formattedStatus = ($this->cotizacion == '1') ? "Cotización" : "Venta";
        return $formattedStatus;
    }

    public function getFormattedLiquidada()
    {
        $formattedLiquidada = ($this->liquidada == '1') ? "Sí" : "No";
        return $formattedLiquidada;
    }

    public function getFechaCreacionForExport()
    {
        $formattedDateC = ($this->fechacreacion) ? $this->fechacreacion->format('d/m/Y') : '';
        return $formattedDateC;
    }

    public function getFechaCancelacionForExport()
    {
        $formattedDateC = ($this->fechacancelacion) ? $this->fechacancelacion->format('d/m/Y') : '';
        return $formattedDateC;
    }

    public function getProducts(): string
    {
        // Regresar los productos separados por coma
        if ($this->products !== null) {
            $productsJson = json_decode($this->products);
            $productsString = "";
            $index = 1;

            foreach ($productsJson as $product) {
                $productsString .= "{$index}. Modelo: {$product->modelo}, ";
                $productsString .= "Código de Barras: {$product->codigobarras}, ";
                $productsString .= "Código Universal: {$product->codigobarrasuniversal}\n";
                $index++;
            }

            return trim($productsString);
        }

        return "";
    }

    public function setProducts(?string $products): self
    {
        $this->products = $products;

        return $this;
    }

    /**
     * Get the subtotal (total without IVA)
     *
     * @return string
     */

    public function getSubtotal(): string
    {
        $totalFloat = is_numeric($this->total) ? floatval($this->total) : 0.0;
        $ivaFloat = is_numeric($this->iva) ? floatval($this->iva) : 0.0;

        $subtotal = $totalFloat - $ivaFloat;

        // Evitar subtotal negativo (opcional)
        if ($subtotal < 0) {
            $subtotal = 0;
        }

        return number_format($subtotal, 2, '.', '');
    }

}