<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Documentosexamenvisual
 *
 * @ORM\Table(name="documentosExamenVisual", indexes={@ORM\Index(name="fk_documentosExamenVisual_flujoExpediente1_idx", columns={"flujoExpediente_idflujoExpediente"})})
 * @ORM\Entity
 */
class Documentosexamenvisual
{
    /**
     * @var int
     *
     * @ORM\Column(name="iddocumentosExamenVisual", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $iddocumentosexamenvisual;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=false)
     */
    private $creacion;

    /**
     * @var string
     *
     * @ORM\Column(name="tipoDocumento", type="string", length=45, nullable=false)
     */
    private $tipodocumento;

    /**
     * @var string
     *
     * @ORM\Column(name="nombreDocumento", type="string", length=45, nullable=false)
     */
    private $nombredocumento;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \Flujoexpediente
     *
     * @ORM\ManyToOne(targetEntity="Flujoexpediente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="flujoExpediente_idflujoExpediente", referencedColumnName="idflujoExpediente")
     * })
     */
    private $flujoexpedienteIdflujoexpediente;

    public function getIddocumentosexamenvisual(): ?int
    {
        return $this->iddocumentosexamenvisual;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getTipodocumento(): ?string
    {
        return $this->tipodocumento;
    }

    public function setTipodocumento(string $tipodocumento): self
    {
        $this->tipodocumento = $tipodocumento;

        return $this;
    }

    public function getNombredocumento(): ?string
    {
        return $this->nombredocumento;
    }

    public function setNombredocumento(string $nombredocumento): self
    {
        $this->nombredocumento = $nombredocumento;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getFlujoexpedienteIdflujoexpediente(): ?Flujoexpediente
    {
        return $this->flujoexpedienteIdflujoexpediente;
    }

    public function setFlujoexpedienteIdflujoexpediente(?Flujoexpediente $flujoexpedienteIdflujoexpediente): self
    {
        $this->flujoexpedienteIdflujoexpediente = $flujoexpedienteIdflujoexpediente;

        return $this;
    }


}
