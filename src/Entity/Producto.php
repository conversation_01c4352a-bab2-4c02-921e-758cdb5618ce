<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM; 

/**
 * Producto
 *
 * @ORM\Table(name="producto", uniqueConstraints={@ORM\UniqueConstraint(name="codigo_UNIQUE", columns={"codigo"})}, indexes={@ORM\Index(name="fk_producto_categoria1_idx", columns={"categoria_idcategoria"}), @ORM\Index(name="fk_producto_marca1_idx", columns={"marca_idmarca"}), @ORM\Index(name="fk_producto_medida1_idx", columns={"medida_idmedida"}), @ORM\Index(name="fk_producto_frameColor1_idx", columns={"frameColor_idframeColor"}), @ORM\Index(name="fk_producto_frameMaterial1_idx", columns={"frameMaterial_idframeMaterial"})})
 * @ORM\Entity
 */
class Producto
{
    /**
     * @var int
     *
     * @ORM\Column(name="idproducto", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idproducto;

    /**
     * @var string|null
     *
     * @ORM\Column(name="codigo", type="string", length=45, nullable=true)
     */
    private $codigo;

    /**
     * @var string|null
     *
     * @ORM\Column(name="nombre", type="string", length=500, nullable=true)
     */
    private $nombre;

    /**
     * @var string|null
     *
     * @ORM\Column(name="imagen", type="string", length=45, nullable=true)
     */
    private $imagen;

    /**
     * @var string|null
     *
     * @ORM\Column(name="tipo", type="string", length=45, nullable=true)
     */
    private $tipo;

    /**
     * @var int
     *
     * @ORM\Column(name="cantidad", type="integer", nullable=false)
     */
    private $cantidad;

    /**
     * @var string
     *
     * @ORM\Column(name="costo", type="decimal", precision=15, scale=2, nullable=false, options={"default"="0.00"})
     */
    private $costo = '0.00';

    /**
     * @var string
     *
     * @ORM\Column(name="precio", type="decimal", precision=15, scale=2, nullable=false, options={"default"="0.00"})
     */
    private $precio = '0.00';

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="barcode", type="string", length=150, nullable=true)
     */
    private $barcode;

    /**
     * @var string|null
     *
     * @ORM\Column(name="descripcion", type="text", length=16777215, nullable=true)
     */
    private $descripcion; 

    /**
     * @var string|null
     *
     * @ORM\Column(name="codigoColor", type="string", length=100, nullable=true)
     */
    private $codigocolor;

    /**
     * @var string|null
     *
     * @ORM\Column(name="color", type="string", length=500, nullable=true)
     */
    private $color;

    /**
     * @var string|null
     *
     * @ORM\Column(name="codigoFinal", type="string", length=100, nullable=true)
     */
    private $codigofinal;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=true)
     */
    private $creacion;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="modificacion", type="datetime", nullable=true)
     */
    private $modificacion;

    /**
     * @var string|null
     *
     * @ORM\Column(name="unidadMedida", type="string", length=45, nullable=true)
     */
    private $unidadmedida;

    /**
     * @var string|null
     *
     * @ORM\Column(name="disponibilidadInventario", type="string", length=45, nullable=true)
     */
    private $disponibilidadinventario;

    /**
     * @var string
     *
     * @ORM\Column(name="tipoProducto", type="string", length=45, nullable=false)
     */
    private $tipoproducto;

    /**
     * @var float|null
     *
     * @ORM\Column(name="porcentajeComision", type="float", precision=10, scale=0, nullable=true)
     */
    private $porcentajecomision;

    /**
     * @var string|null
     *
     * @ORM\Column(name="tipoMaterial", type="string", length=45, nullable=true)
     */
    private $tipomaterial;

    /**
     * @var string|null
     *
     * @ORM\Column(name="modelo", type="string", length=1000, nullable=true)
     */
    private $modelo;

    /**
     * @var string|null
     *
     * @ORM\Column(name="sobrepuesto", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $sobrepuesto;

    /**
     * @var string|null
     *
     * @ORM\Column(name="codigoBarrasUniversal", type="string", length=100, nullable=true)
     */
    private $codigobarrasuniversal;

    /**
     * @var string|null
     *
     * @ORM\Column(name="masivoUnico", type="string", length=500, nullable=true, options={"default"="1"})
     */
    private $masivounico = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="precioEspecial", type="decimal", precision=9, scale=2, nullable=true)
     */
    private $precioespecial;

    /**
     * @var string|null
     *
     * @ORM\Column(name="precioSubdistribuidor", type="decimal", precision=9, scale=2, nullable=true)
     */
    private $preciosubdistribuidor;

    /**
     * @var string|null
     *
     * @ORM\Column(name="precioDistribuidor", type="decimal", precision=9, scale=2, nullable=true)
     */
    private $preciodistribuidor;

    /**
     * @var string|null
     *
     * @ORM\Column(name="ordenLaboratorio", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $ordenlaboratorio;

    /**
     * @var string|null
     *
     * @ORM\Column(name="gender", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $gender;

    /**
     * @var string|null
     *
     * @ORM\Column(name="descriptionOnlineStore", type="text", length=65535, nullable=true)
     */
    private $descriptiononlinestore;

    /**
     * @var string|null
     *
     * @ORM\Column(name="measurementImage", type="string", length=80, nullable=true, options={"fixed"=true})
     */
    private $measurementimage;

    /**
     * @var bool|null
     *
     * @ORM\Column(name="showOnlineStore", type="boolean", nullable=true)
     */
    private $showonlinestore = '0';

    /**
     * @var int|null
     *
     * @ORM\Column(name="priority", type="integer", nullable=true)
     */
    private $priority;

    /**
     * @var string|null
     *
     * @ORM\Column(name="esfera", type="string", length=45, nullable=true)
     */
    private $esfera;

    /**
     * @var string|null
     *
     * @ORM\Column(name="cilindro", type="string", length=45, nullable=true)
     */
    private $cilindro;

    /**
     * @var string|null
     *
     * @ORM\Column(name="eje", type="string", length=45, nullable=true)
     */
    private $eje;

    /**
     * @var string|null
     *
     * @ORM\Column(name="adicion", type="string", length=45, nullable=true)
     */
    private $adicion;

    /**
     * @var string|null
     *
     * @ORM\Column(name="base", type="string", length=45, nullable=true)
     */
    private $base;

    /**
     * @var string|null
     *
     * @ORM\Column(name="design", type="string", length=45, nullable=true)
     */
    private $design;

    /**
     * @var string|null
     *
     * @ORM\Column(name="activo", type="string", length=1, nullable=true)
     */
    private $activo = '0';

    /**
     * @var string|null
     *
     * @ORM\Column(name="clave", type="string", length=100, nullable=true)
     */
    private $clave;

    /**
     * @var string|null
     *
     * @ORM\Column(name="moreInfo", type="text", length=0, nullable=true)
     */
    private $moreinfo;

    /**
     * @var string
     *
     * @ORM\Column(name="graduable", type="string", length=1, nullable=false, options={"fixed"=true})
     */
    private $graduable = '0';

    /**
     * @var string|null
     *
     * @ORM\Column(name="idfacturama", type="string", length=255, nullable=true)
     */
    private $idfacturama;

    /**
     * @var \Categoria
     *
     * @ORM\ManyToOne(targetEntity="Categoria")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="categoria_idcategoria", referencedColumnName="idcategoria")
     * })
     */
    private $categoriaIdcategoria;

    /**
     * @var \Marca
     *
     * @ORM\ManyToOne(targetEntity="Marca")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="marca_idmarca", referencedColumnName="idmarca")
     * })
     */
    private $marcaIdmarca;

    /**
     * @var \Medida
     *
     * @ORM\ManyToOne(targetEntity="Medida")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="medida_idmedida", referencedColumnName="idmedida")
     * })
     */
    private $medidaIdmedida;

    /**
     * @var \Framecolor
     *
     * @ORM\ManyToOne(targetEntity="Framecolor")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="frameColor_idframeColor", referencedColumnName="idframeColor")
     * })
     */
    private $framecolorIdframecolor;

    /**
     * @var \Framematerial
     *
     * @ORM\ManyToOne(targetEntity="Framematerial")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="frameMaterial_idframeMaterial", referencedColumnName="idframeMaterial")
     * })
     */
    private $framematerialIdframematerial;

    public function getIdproducto(): ?int
    {
        return $this->idproducto;
    }

    public function getCodigo(): ?string
    {
        return $this->codigo;
    }

    public function setCodigo(?string $codigo): self
    {
        $this->codigo = $codigo;

        return $this;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(?string $nombre): self
    {
        $this->nombre = $nombre;

        return $this;
    }

    public function getImagen(): ?string
    {
        return $this->imagen;
    }

    public function setImagen(?string $imagen): self
    {
        $this->imagen = $imagen;

        return $this;
    }

    public function getTipo(): ?string
    {
        return $this->tipo;
    }

    public function setTipo(?string $tipo): self
    {
        $this->tipo = $tipo;

        return $this;
    }

    public function getCantidad(): ?int
    {
        return $this->cantidad;
    }

    public function setCantidad(int $cantidad): self
    {
        $this->cantidad = $cantidad;

        return $this;
    }

    public function getCosto(): ?string
    {
        return $this->costo;
    }

    public function setCosto(string $costo): self
    {
        $this->costo = $costo;

        return $this;
    }

    public function getPrecio(): ?string
    {
        return $this->precio;
    }

    public function setPrecio(string $precio): self
    {
        $this->precio = $precio;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getBarcode(): ?string
    {
        return $this->barcode;
    }

    public function setBarcode(?string $barcode): self
    {
        $this->barcode = $barcode;

        return $this;
    }

    public function getDescripcion(): ?string
    {
        return $this->descripcion;
    }

    public function setDescripcion(?string $descripcion): self
    {
        $this->descripcion = $descripcion;

        return $this;
    }

    public function getCodigocolor(): ?string
    {
        return $this->codigocolor;
    }

    public function setCodigocolor(?string $codigocolor): self
    {
        $this->codigocolor = $codigocolor;

        return $this;
    }

    public function getColor(): ?string
    {
        return $this->color;
    }

    public function setColor(?string $color): self
    {
        $this->color = $color;

        return $this;
    }

    public function getCodigofinal(): ?string
    {
        return $this->codigofinal;
    }

    public function setCodigofinal(?string $codigofinal): self
    {
        $this->codigofinal = $codigofinal;

        return $this;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(?\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getModificacion(): ?\DateTimeInterface
    {
        return $this->modificacion;
    }

    public function setModificacion(?\DateTimeInterface $modificacion): self
    {
        $this->modificacion = $modificacion;

        return $this;
    }

    public function getUnidadmedida(): ?string
    {
        return $this->unidadmedida;
    }

    public function setUnidadmedida(?string $unidadmedida): self
    {
        $this->unidadmedida = $unidadmedida;

        return $this;
    }

    public function getDisponibilidadinventario(): ?string
    {
        return $this->disponibilidadinventario;
    }

    public function setDisponibilidadinventario(?string $disponibilidadinventario): self
    {
        $this->disponibilidadinventario = $disponibilidadinventario;

        return $this;
    }

    public function getTipoproducto(): ?string
    {
        return $this->tipoproducto;
    }

    public function setTipoproducto(string $tipoproducto): self
    {
        $this->tipoproducto = $tipoproducto;

        return $this;
    }

    public function getPorcentajecomision(): ?float
    {
        return $this->porcentajecomision;
    }

    public function setPorcentajecomision(?float $porcentajecomision): self
    {
        $this->porcentajecomision = $porcentajecomision;

        return $this;
    }

    public function getTipomaterial(): ?string
    {
        return $this->tipomaterial;
    }

    public function setTipomaterial(?string $tipomaterial): self
    {
        $this->tipomaterial = $tipomaterial;

        return $this;
    }

    public function getModelo(): ?string
    {
        return $this->modelo;
    }

    public function setModelo(?string $modelo): self
    {
        $this->modelo = $modelo;

        return $this;
    }

    public function getSobrepuesto(): ?string
    {
        return $this->sobrepuesto;
    }

    public function setSobrepuesto(?string $sobrepuesto): self
    {
        $this->sobrepuesto = $sobrepuesto;

        return $this;
    }

    public function getCodigobarrasuniversal(): ?string
    {
        return $this->codigobarrasuniversal;
    }

    public function setCodigobarrasuniversal(?string $codigobarrasuniversal): self
    {
        $this->codigobarrasuniversal = $codigobarrasuniversal;

        return $this;
    }

    public function getMasivounico(): ?string
    {
        return $this->masivounico;
    }

    public function setMasivounico(?string $masivounico): self
    {
        $this->masivounico = $masivounico;

        return $this;
    }

    public function getPrecioespecial(): ?string
    {
        return $this->precioespecial;
    }

    public function setPrecioespecial(?string $precioespecial): self
    {
        $this->precioespecial = $precioespecial;

        return $this;
    }

    public function getPreciosubdistribuidor(): ?string
    {
        return $this->preciosubdistribuidor;
    }

    public function setPreciosubdistribuidor(?string $preciosubdistribuidor): self
    {
        $this->preciosubdistribuidor = $preciosubdistribuidor;

        return $this;
    }

    public function getPreciodistribuidor(): ?string
    {
        return $this->preciodistribuidor;
    }

    public function setPreciodistribuidor(?string $preciodistribuidor): self
    {
        $this->preciodistribuidor = $preciodistribuidor;

        return $this;
    }

    public function getOrdenlaboratorio(): ?string
    {
        return $this->ordenlaboratorio;
    }

    public function setOrdenlaboratorio(?string $ordenlaboratorio): self
    {
        $this->ordenlaboratorio = $ordenlaboratorio;

        return $this;
    }

    public function getGender(): ?string
    {
        return $this->gender;
    }

    public function setGender(?string $gender): self
    {
        $this->gender = $gender;

        return $this;
    }

    public function getDescriptiononlinestore(): ?string
    {
        return $this->descriptiononlinestore;
    }

    public function setDescriptiononlinestore(?string $descriptiononlinestore): self
    {
        $this->descriptiononlinestore = $descriptiononlinestore;

        return $this;
    }

    public function getMeasurementimage(): ?string
    {
        return $this->measurementimage;
    }

    public function setMeasurementimage(?string $measurementimage): self
    {
        $this->measurementimage = $measurementimage;

        return $this;
    }

    public function getShowonlinestore(): ?bool
    {
        return $this->showonlinestore;
    }

    public function setShowonlinestore(?bool $showonlinestore): self
    {
        $this->showonlinestore = $showonlinestore;

        return $this;
    }

    public function getPriority(): ?int
    {
        return $this->priority;
    }

    public function setPriority(?int $priority): self
    {
        $this->priority = $priority;

        return $this;
    }

    public function getEsfera(): ?string
    {
        return $this->esfera;
    }

    public function setEsfera(?string $esfera): self
    {
        $this->esfera = $esfera;

        return $this;
    }

    public function getCilindro(): ?string
    {
        return $this->cilindro;
    }

    public function setCilindro(?string $cilindro): self
    {
        $this->cilindro = $cilindro;

        return $this;
    }

    public function getEje(): ?string
    {
        return $this->eje;
    }

    public function setEje(?string $eje): self
    {
        $this->eje = $eje;

        return $this;
    }

    public function getAdicion(): ?string
    {
        return $this->adicion;
    }

    public function setAdicion(?string $adicion): self
    {
        $this->adicion = $adicion;

        return $this;
    }

    public function getBase(): ?string
    {
        return $this->base;
    }

    public function setBase(?string $base): self
    {
        $this->base = $base;

        return $this;
    }

    public function getDesign(): ?string
    {
        return $this->design;
    }

    public function setDesign(?string $design): self
    {
        $this->design = $design;

        return $this;
    }

    public function getActivo(): ?string
    {
        return $this->activo;
    }

    public function setActivo(?string $activo): self
    {
        $this->activo = $activo;

        return $this;
    }

    public function getClave(): ?string
    {
        return $this->clave;
    }

    public function setClave(?string $clave): self
    {
        $this->clave = $clave;

        return $this;
    }

    public function getMoreinfo(): ?string
    {
        return $this->moreinfo;
    }

    public function setMoreinfo(?string $moreinfo): self
    {
        $this->moreinfo = $moreinfo;

        return $this;
    }

    public function getGraduable(): ?string
    {
        return $this->graduable;
    }

    public function setGraduable(string $graduable): self
    {
        $this->graduable = $graduable;

        return $this;
    }

    public function getIdfacturama(): ?string
    {
        return $this->idfacturama;
    }

    public function setIdfacturama(?string $idfacturama): self
    {
        $this->idfacturama = $idfacturama;

        return $this;
    }

    public function getCategoriaIdcategoria(): ?Categoria
    {
        return $this->categoriaIdcategoria;
    }

    public function setCategoriaIdcategoria(?Categoria $categoriaIdcategoria): self
    {
        $this->categoriaIdcategoria = $categoriaIdcategoria;

        return $this;
    }

    public function getMarcaIdmarca(): ?Marca
    {
        return $this->marcaIdmarca;
    }

    public function setMarcaIdmarca(?Marca $marcaIdmarca): self
    {
        $this->marcaIdmarca = $marcaIdmarca;

        return $this;
    }

    public function getMedidaIdmedida(): ?Medida
    {
        return $this->medidaIdmedida;
    }

    public function setMedidaIdmedida(?Medida $medidaIdmedida): self
    {
        $this->medidaIdmedida = $medidaIdmedida;

        return $this;
    }

    public function getFramecolorIdframecolor(): ?Framecolor
    {
        return $this->framecolorIdframecolor;
    }

    public function setFramecolorIdframecolor(?Framecolor $framecolorIdframecolor): self
    {
        $this->framecolorIdframecolor = $framecolorIdframecolor;

        return $this;
    }

    public function getFramematerialIdframematerial(): ?Framematerial
    {
        return $this->framematerialIdframematerial;
    }

    public function setFramematerialIdframematerial(?Framematerial $framematerialIdframematerial): self
    {
        $this->framematerialIdframematerial = $framematerialIdframematerial;

        return $this;
    }

    public function __toString()
    {
        return $this->nombre ?: '';
    }

}
