<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Merma
 *
 * @ORM\Table(name="merma", uniqueConstraints={@ORM\UniqueConstraint(name="folio_UNIQUE", columns={"folio"})}, indexes={@ORM\Index(name="fk_Merma_usuario1_idx", columns={"usuario_idusuario"}), @ORM\Index(name="fk_Merma_stock1_idx", columns={"stock_idstock"}), @ORM\Index(name="fk_merma_usuario2_idx", columns={"usuarioResponsableBorrar"}), @ORM\Index(name="fk_merma_departureCause1_idx", columns={"departureCause_iddepartureCause"}), @ORM\Index(name="fk_merma_stockVenta1_idx", columns={"stockVenta_idstockVenta"})})
 * @ORM\Entity
 */
class Merma
{
    /**
     * @var int
     *
     * @ORM\Column(name="idMerma", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idmerma;

    /**
     * @var string|null
     *
     * @ORM\Column(name="tipo", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $tipo;

    /**
     * @var string
     *
     * @ORM\Column(name="detalleIncidencia", type="text", length=16777215, nullable=false)
     */
    private $detalleincidencia;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="fechaIncidencia", type="datetime", nullable=false)
     */
    private $fechaincidencia;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="fechaCreacion", type="datetime", nullable=false)
     */
    private $fechacreacion;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var int
     *
     * @ORM\Column(name="cantidad", type="integer", nullable=false, options={"unsigned"=true})
     */
    private $cantidad;

    /**
     * @var string|null
     *
     * @ORM\Column(name="detalleBorrado", type="text", length=16777215, nullable=true)
     */
    private $detalleborrado;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaBorrado", type="datetime", nullable=true)
     */
    private $fechaborrado;

    /**
     * @var string|null
     *
     * @ORM\Column(name="folio", type="string", length=45, nullable=true)
     */
    private $folio;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    /**
     * @var \Stock
     *
     * @ORM\ManyToOne(targetEntity="Stock")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="stock_idstock", referencedColumnName="idstock")
     * })
     */
    private $stockIdstock;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuarioResponsableBorrar", referencedColumnName="idusuario")
     * })
     */
    private $usuarioresponsableborrar;

    /**
     * @var \Departurecause
     *
     * @ORM\ManyToOne(targetEntity="Departurecause")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="departureCause_iddepartureCause", referencedColumnName="iddepartureCause")
     * })
     */
    private $departurecauseIddeparturecause;

    /**
     * @var \Stockventa
     *
     * @ORM\ManyToOne(targetEntity="Stockventa")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="stockVenta_idstockVenta", referencedColumnName="idstockVenta")
     * })
     */
    private $stockventaIdstockventa;

    public function getIdmerma(): ?int
    {
        return $this->idmerma;
    }

    public function getTipo(): ?string
    {
        return $this->tipo;
    }

    public function setTipo(?string $tipo): self
    {
        $this->tipo = $tipo;

        return $this;
    }

    public function getDetalleincidencia(): ?string
    {
        return $this->detalleincidencia;
    }

    public function setDetalleincidencia(string $detalleincidencia): self
    {
        $this->detalleincidencia = $detalleincidencia;

        return $this;
    }

    public function getFechaincidencia(): ?\DateTimeInterface
    {
        return $this->fechaincidencia;
    }

    public function setFechaincidencia(\DateTimeInterface $fechaincidencia): self
    {
        $this->fechaincidencia = $fechaincidencia;

        return $this;
    }

    public function getFechacreacion(): ?\DateTimeInterface
    {
        return $this->fechacreacion;
    }

    public function setFechacreacion(\DateTimeInterface $fechacreacion): self
    {
        $this->fechacreacion = $fechacreacion;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getCantidad(): ?int
    {
        return $this->cantidad;
    }

    public function setCantidad(int $cantidad): self
    {
        $this->cantidad = $cantidad;

        return $this;
    }

    public function getDetalleborrado(): ?string
    {
        return $this->detalleborrado;
    }

    public function setDetalleborrado(?string $detalleborrado): self
    {
        $this->detalleborrado = $detalleborrado;

        return $this;
    }

    public function getFechaborrado(): ?\DateTimeInterface
    {
        return $this->fechaborrado;
    }

    public function setFechaborrado(?\DateTimeInterface $fechaborrado): self
    {
        $this->fechaborrado = $fechaborrado;

        return $this;
    }

    public function getFolio(): ?string
    {
        return $this->folio;
    }

    public function setFolio(?string $folio): self
    {
        $this->folio = $folio;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }

    public function getStockIdstock(): ?Stock
    {
        return $this->stockIdstock;
    }

    public function setStockIdstock(?Stock $stockIdstock): self
    {
        $this->stockIdstock = $stockIdstock;

        return $this;
    }

    public function getUsuarioresponsableborrar(): ?Usuario
    {
        return $this->usuarioresponsableborrar;
    }

    public function setUsuarioresponsableborrar(?Usuario $usuarioresponsableborrar): self
    {
        $this->usuarioresponsableborrar = $usuarioresponsableborrar;

        return $this;
    }

    public function getDeparturecauseIddeparturecause(): ?Departurecause
    {
        return $this->departurecauseIddeparturecause;
    }

    public function setDeparturecauseIddeparturecause(?Departurecause $departurecauseIddeparturecause): self
    {
        $this->departurecauseIddeparturecause = $departurecauseIddeparturecause;

        return $this;
    }

    public function getStockventaIdstockventa(): ?Stockventa
    {
        return $this->stockventaIdstockventa;
    }

    public function setStockventaIdstockventa(?Stockventa $stockventaIdstockventa): self
    {
        $this->stockventaIdstockventa = $stockventaIdstockventa;

        return $this;
    }


}
