<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Stockhistory
 *
 * @ORM\Table(name="stockHistory", indexes={@ORM\Index(name="fk_stockHistory_stock1_idx", columns={"stock_idstock"}), @ORM\Index(name="fk_stockHistory_stockState1_idx", columns={"stockState_idstockState"}), @ORM\Index(name="fk_stockHistory_usuario1_idx", columns={"usuario_idusuario"})})
 * @ORM\Entity
 */
class Stockhistory
{
    /**
     * @var int
     *
     * @ORM\Column(name="idstockHistory", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idstockhistory;

    /**
     * @var string|null
     *
     * @ORM\Column(name="comments", type="text", length=65535, nullable=true)
     */
    private $comments;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="date", type="datetime", nullable=false)
     */
    private $date;

    /**
     * @var \Stock
     *
     * @ORM\ManyToOne(targetEntity="Stock")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="stock_idstock", referencedColumnName="idstock")
     * })
     */
    private $stockIdstock;

    /**
     * @var \Stockstate
     *
     * @ORM\ManyToOne(targetEntity="Stockstate")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="stockState_idstockState", referencedColumnName="idstockState")
     * })
     */
    private $stockstateIdstockstate;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    public function getIdstockhistory(): ?int
    {
        return $this->idstockhistory;
    }

    public function getComments(): ?string
    {
        return $this->comments;
    }

    public function setComments(?string $comments): self
    {
        $this->comments = $comments;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getStockIdstock(): ?Stock
    {
        return $this->stockIdstock;
    }

    public function setStockIdstock(?Stock $stockIdstock): self
    {
        $this->stockIdstock = $stockIdstock;

        return $this;
    }

    public function getStockstateIdstockstate(): ?Stockstate
    {
        return $this->stockstateIdstockstate;
    }

    public function setStockstateIdstockstate(?Stockstate $stockstateIdstockstate): self
    {
        $this->stockstateIdstockstate = $stockstateIdstockstate;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }


}
