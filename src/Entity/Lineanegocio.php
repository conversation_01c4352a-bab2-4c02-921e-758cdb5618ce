<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Lineanegocio
 *
 * @ORM\Table(name="lineaNegocio")
 * @ORM\Entity
 */
class Lineanegocio
{
    /**
     * @var int
     *
     * @ORM\Column(name="idlineaNegocio", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idlineanegocio;

    /**
     * @var string
     *
     * @ORM\Column(name="nombre", type="string", length=500, nullable=false)
     */
    private $nombre;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    public function getIdlineanegocio(): ?int
    {
        return $this->idlineanegocio;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(string $nombre): self
    {
        $this->nombre = $nombre;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }


}
