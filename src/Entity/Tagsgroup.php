<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Tagsgroup
 *
 * @ORM\Table(name="tagsGroup")
 * @ORM\Entity
 */
class Tagsgroup
{
    /**
     * @var int
     *
     * @ORM\Column(name="idTagsGroup", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idtagsgroup;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=45, nullable=false, options={"default"="no name"})
     */
    private $name = 'no name';

    /**
     * @var string|null
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=true, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="description", type="string", length=45, nullable=true)
     */
    private $description;

    /**
     * @var string|null
     *
     * @ORM\Column(name="onlineStore", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $onlinestore = '0';

    public function getIdtagsgroup(): ?int
    {
        return $this->idtagsgroup;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getOnlinestore(): ?string
    {
        return $this->onlinestore;
    }

    public function setOnlinestore(?string $onlinestore): self
    {
        $this->onlinestore = $onlinestore;

        return $this;
    }


}
