<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Customflowinstance
 *
 * @ORM\Table(name="customFlowInstance", indexes={@ORM\Index(name="fk_customFlowInstance_customFlow1_idx", columns={"customFlow_idcustomFlow"}), @ORM\Index(name="fk_customFlowInstance_usuario1_idx", columns={"usuario_idusuario"})})
 * @ORM\Entity
 */
class Customflowinstance
{
    /**
     * @var int
     *
     * @ORM\Column(name="idcustomFlowInstance", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idcustomflowinstance;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="createdAt", type="datetime", nullable=false)
     */
    private $createdat;

    /**
     * @var string|null
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=true, options={"default"="1"})
     */
    private $status = '1';

    /**
     * @var \Customflow
     *
     * @ORM\ManyToOne(targetEntity="Customflow")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="customFlow_idcustomFlow", referencedColumnName="idcustomFlow")
     * })
     */
    private $customflowIdcustomflow;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    public function getIdcustomflowinstance(): ?int
    {
        return $this->idcustomflowinstance;
    }

    public function getCreatedat(): ?\DateTimeInterface
    {
        return $this->createdat;
    }

    public function setCreatedat(\DateTimeInterface $createdat): self
    {
        $this->createdat = $createdat;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getCustomflowIdcustomflow(): ?Customflow
    {
        return $this->customflowIdcustomflow;
    }

    public function setCustomflowIdcustomflow(?Customflow $customflowIdcustomflow): self
    {
        $this->customflowIdcustomflow = $customflowIdcustomflow;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }


}
