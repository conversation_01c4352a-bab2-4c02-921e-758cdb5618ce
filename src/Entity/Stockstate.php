<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Stockstate
 *
 * @ORM\Table(name="stockState")
 * @ORM\Entity
 */
class Stockstate
{
    /**
     * @var int
     *
     * @ORM\Column(name="idstockState", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idstockstate;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=45, nullable=false)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    public function getIdstockstate(): ?int
    {
        return $this->idstockstate;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function __toString() {
        return $this->getName();
    }

}