<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Cupon
 *
 * @ORM\Table(name="cupon", uniqueConstraints={@ORM\UniqueConstraint(name="codigo_UNIQUE", columns={"codigo"})})
 * @ORM\Entity
 */
class Cupon
{
    /**
     * @var int
     *
     * @ORM\Column(name="idcupon", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idcupon;

    /**
     * @var string
     *
     * @ORM\Column(name="codigo", type="string", length=250, nullable=false)
     */
    private $codigo;

    /**
     * @var string|null
     *
     * @ORM\Column(name="nombre", type="string", length=500, nullable=true)
     */
    private $nombre;

    /**
     * @var float
     *
     * @ORM\Column(name="porcentajeDescuento", type="float", precision=10, scale=0, nullable=false)
     */
    private $porcentajedescuento;

    /**
     * @var int
     *
     * @ORM\Column(name="numeroCupones", type="integer", nullable=false)
     */
    private $numerocupones;

    /**
     * @var int|null
     *
     * @ORM\Column(name="cuponesUsados", type="integer", nullable=true)
     */
    private $cuponesusados;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="fechaCreacion", type="datetime", nullable=false)
     */
    private $fechacreacion;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="fechaActualizacion", type="datetime", nullable=false)
     */
    private $fechaactualizacion;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    public function getIdcupon(): ?int
    {
        return $this->idcupon;
    }

    public function getCodigo(): ?string
    {
        return $this->codigo;
    }

    public function setCodigo(string $codigo): self
    {
        $this->codigo = $codigo;

        return $this;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(?string $nombre): self
    {
        $this->nombre = $nombre;

        return $this;
    }

    public function getPorcentajedescuento(): ?float
    {
        return $this->porcentajedescuento;
    }

    public function setPorcentajedescuento(float $porcentajedescuento): self
    {
        $this->porcentajedescuento = $porcentajedescuento;

        return $this;
    }

    public function getNumerocupones(): ?int
    {
        return $this->numerocupones;
    }

    public function setNumerocupones(int $numerocupones): self
    {
        $this->numerocupones = $numerocupones;

        return $this;
    }

    public function getCuponesusados(): ?int
    {
        return $this->cuponesusados;
    }

    public function setCuponesusados(?int $cuponesusados): self
    {
        $this->cuponesusados = $cuponesusados;

        return $this;
    }

    public function getFechacreacion(): ?\DateTimeInterface
    {
        return $this->fechacreacion;
    }

    public function setFechacreacion(\DateTimeInterface $fechacreacion): self
    {
        $this->fechacreacion = $fechacreacion;

        return $this;
    }

    public function getFechaactualizacion(): ?\DateTimeInterface
    {
        return $this->fechaactualizacion;
    }

    public function setFechaactualizacion(\DateTimeInterface $fechaactualizacion): self
    {
        $this->fechaactualizacion = $fechaactualizacion;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }


    public function __toString(): string
    {
        return $this->nombre ?? '';
    }


}
