<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Proveedoremail
 *
 * @ORM\Table(name="proveedorEmail", indexes={@ORM\Index(name="fk_proveedorEmail_proveedorContacto1_idx", columns={"proveedorContacto_idproveedorContacto"})})
 * @ORM\Entity
 */
class Proveedoremail
{
    /**
     * @var int
     *
     * @ORM\Column(name="idproveedorEmail", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idproveedoremail;

    /**
     * @var string
     *
     * @ORM\Column(name="email", type="string", length=500, nullable=false)
     */
    private $email;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=true)
     */
    private $creacion;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="actualizacion", type="datetime", nullable=true)
     */
    private $actualizacion;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \Proveedorcontacto
     *
     * @ORM\ManyToOne(targetEntity="Proveedorcontacto")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="proveedorContacto_idproveedorContacto", referencedColumnName="idproveedorContacto")
     * })
     */
    private $proveedorcontactoIdproveedorcontacto;

    public function getIdproveedoremail(): ?int
    {
        return $this->idproveedoremail;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(?\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getActualizacion(): ?\DateTimeInterface
    {
        return $this->actualizacion;
    }

    public function setActualizacion(?\DateTimeInterface $actualizacion): self
    {
        $this->actualizacion = $actualizacion;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getProveedorcontactoIdproveedorcontacto(): ?Proveedorcontacto
    {
        return $this->proveedorcontactoIdproveedorcontacto;
    }

    public function setProveedorcontactoIdproveedorcontacto(?Proveedorcontacto $proveedorcontactoIdproveedorcontacto): self
    {
        $this->proveedorcontactoIdproveedorcontacto = $proveedorcontactoIdproveedorcontacto;

        return $this;
    }


}
