<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM; 

/**
 * @ORM\Table(name="productosTranspasoAlmacen", indexes={@ORM\Index(name="fk_productosTranspasoAlmacen_stock1_idx", columns={"stock_idstock"}), @ORM\Index(name="fk_productosTranspasoAlmacen_transpasoAlmacen1_idx", columns={"transpasoAlmacen_idtranspasoAlmacen"})})
 * @ORM\Entity(repositoryClass="App\Repository\ProductoTranspasoAlmacenRepository")
 */
class Productostranspasoalmacen
{
    /**
     * @var int
     *
     * @ORM\Column(name="idproductosTranspasoAlmacen", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idproductostranspasoalmacen;

    /**
     * @var float
     *
     * @ORM\Column(name="cantidad", type="float", precision=10, scale=0, nullable=false)
     */
    private $cantidad;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="aceptada", type="string", length=45, nullable=true)
     */
    private $aceptada;

    /**
     * @var string|null
     *
     * @ORM\Column(name="nota", type="string", length=45, nullable=true)
     */
    private $nota;

    /**
     * @var \Stock
     *
     * @ORM\ManyToOne(targetEntity="Stock")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="stock_idstock", referencedColumnName="idstock")
     * })
     */
    private $stockIdstock;

    /**
     * @var \Transpasoalmacen
     *
     * @ORM\ManyToOne(targetEntity="Transpasoalmacen")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="transpasoAlmacen_idtranspasoAlmacen", referencedColumnName="idtranspasoAlmacen")
     * })
     */
    private $transpasoalmacenIdtranspasoalmacen;

    public function getIdproductostranspasoalmacen(): ?int
    {
        return $this->idproductostranspasoalmacen;
    }

    public function getCantidad(): ?float
    {
        return $this->cantidad;
    }

    public function setCantidad(float $cantidad): self
    {
        $this->cantidad = $cantidad;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getAceptada(): ?string
    {
        return $this->aceptada;
    }

    public function setAceptada(?string $aceptada): self
    {
        $this->aceptada = $aceptada;

        return $this;
    }

    public function getNota(): ?string
    {
        return $this->nota;
    }

    public function setNota(?string $nota): self
    {
        $this->nota = $nota;

        return $this;
    }

    public function getStockIdstock(): ?Stock
    {
        return $this->stockIdstock;
    }

    public function setStockIdstock(?Stock $stockIdstock): self
    {
        $this->stockIdstock = $stockIdstock;

        return $this;
    }

    public function getTranspasoalmacenIdtranspasoalmacen(): ?Transpasoalmacen
    {
        return $this->transpasoalmacenIdtranspasoalmacen;
    }

    public function setTranspasoalmacenIdtranspasoalmacen(?Transpasoalmacen $transpasoalmacenIdtranspasoalmacen): self
    {
        $this->transpasoalmacenIdtranspasoalmacen = $transpasoalmacenIdtranspasoalmacen;

        return $this;
    }



}
