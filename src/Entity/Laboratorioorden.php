<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Laboratorioorden
 *
 * @ORM\Table(name="laboratorioOrden", indexes={@ORM\Index(name="fk_laboratorioOrden_stockVenta1_idx", columns={"stockVenta_idstockVenta"}), @ORM\Index(name="fk_laboratorioOrden_usuario1_idx", columns={"usuario_quienEnviaLaboratorio"}), @ORM\Index(name="fk_laboratorioOrden_graduacion1_idx", columns={"graduacion_idgraduacion"}), @ORM\Index(name="fk_laboratorioOrden_usuario2_idx", columns={"usuario_quienRecibeLaboratorio"})})
 * @ORM\Entity
 */
class Laboratorioorden
{
    /**
     * @var int
     *
     * @ORM\Column(name="idlaboratorioOrden", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idlaboratorioorden;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=false)
     */
    private $creacion;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="actualizacion", type="datetime", nullable=false)
     */
    private $actualizacion;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaEnvioLaboratorio", type="datetime", nullable=true)
     */
    private $fechaenviolaboratorio;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaRecibidoLaboratorio", type="datetime", nullable=true)
     */
    private $fecharecibidolaboratorio;

    /**
     * @var string|null
     *
     * @ORM\Column(name="trabajoCorrecto", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $trabajocorrecto;

    /**
     * @var string|null
     *
     * @ORM\Column(name="observacionesTrabajo", type="text", length=65535, nullable=true)
     */
    private $observacionestrabajo;

    /**
     * @var \Stockventa
     *
     * @ORM\ManyToOne(targetEntity="Stockventa")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="stockVenta_idstockVenta", referencedColumnName="idstockVenta")
     * })
     */
    private $stockventaIdstockventa;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_quienEnviaLaboratorio", referencedColumnName="idusuario")
     * })
     */
    private $usuarioQuienenvialaboratorio;

    /**
     * @var \Graduacion
     *
     * @ORM\ManyToOne(targetEntity="Graduacion")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="graduacion_idgraduacion", referencedColumnName="idgraduacion")
     * })
     */
    private $graduacionIdgraduacion;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_quienRecibeLaboratorio", referencedColumnName="idusuario")
     * })
     */
    private $usuarioQuienrecibelaboratorio;

    public function getIdlaboratorioorden(): ?int
    {
        return $this->idlaboratorioorden;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getActualizacion(): ?\DateTimeInterface
    {
        return $this->actualizacion;
    }

    public function setActualizacion(\DateTimeInterface $actualizacion): self
    {
        $this->actualizacion = $actualizacion;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getFechaenviolaboratorio(): ?\DateTimeInterface
    {
        return $this->fechaenviolaboratorio;
    }

    public function setFechaenviolaboratorio(?\DateTimeInterface $fechaenviolaboratorio): self
    {
        $this->fechaenviolaboratorio = $fechaenviolaboratorio;

        return $this;
    }

    public function getFecharecibidolaboratorio(): ?\DateTimeInterface
    {
        return $this->fecharecibidolaboratorio;
    }

    public function setFecharecibidolaboratorio(?\DateTimeInterface $fecharecibidolaboratorio): self
    {
        $this->fecharecibidolaboratorio = $fecharecibidolaboratorio;

        return $this;
    }

    public function getTrabajocorrecto(): ?string
    {
        return $this->trabajocorrecto;
    }

    public function setTrabajocorrecto(?string $trabajocorrecto): self
    {
        $this->trabajocorrecto = $trabajocorrecto;

        return $this;
    }

    public function getObservacionestrabajo(): ?string
    {
        return $this->observacionestrabajo;
    }

    public function setObservacionestrabajo(?string $observacionestrabajo): self
    {
        $this->observacionestrabajo = $observacionestrabajo;

        return $this;
    }

    public function getStockventaIdstockventa(): ?Stockventa
    {
        return $this->stockventaIdstockventa;
    }

    public function setStockventaIdstockventa(?Stockventa $stockventaIdstockventa): self
    {
        $this->stockventaIdstockventa = $stockventaIdstockventa;

        return $this;
    }

    public function getUsuarioQuienenvialaboratorio(): ?Usuario
    {
        return $this->usuarioQuienenvialaboratorio;
    }

    public function setUsuarioQuienenvialaboratorio(?Usuario $usuarioQuienenvialaboratorio): self
    {
        $this->usuarioQuienenvialaboratorio = $usuarioQuienenvialaboratorio;

        return $this;
    }

    public function getGraduacionIdgraduacion(): ?Graduacion
    {
        return $this->graduacionIdgraduacion;
    }

    public function setGraduacionIdgraduacion(?Graduacion $graduacionIdgraduacion): self
    {
        $this->graduacionIdgraduacion = $graduacionIdgraduacion;

        return $this;
    }

    public function getUsuarioQuienrecibelaboratorio(): ?Usuario
    {
        return $this->usuarioQuienrecibelaboratorio;
    }

    public function setUsuarioQuienrecibelaboratorio(?Usuario $usuarioQuienrecibelaboratorio): self
    {
        $this->usuarioQuienrecibelaboratorio = $usuarioQuienrecibelaboratorio;

        return $this;
    }


}
