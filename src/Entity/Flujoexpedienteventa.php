<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Flujoexpedienteventa
 *
 * @ORM\Table(name="flujoExpedienteVenta", indexes={@ORM\Index(name="fk_flujoExpedienteVenta_flujoExpediente1_idx", columns={"flujoExpediente_idflujoExpediente"}), @ORM\Index(name="fk_flujoExpedienteVenta_venta1_idx", columns={"venta_idventa"})})
 * @ORM\Entity
 */
class Flujoexpedienteventa
{
    /**
     * @var int
     *
     * @ORM\Column(name="idflujoExpedienteVenta", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idflujoexpedienteventa;

    /**
     * @var \Flujoexpediente
     *
     * @ORM\ManyToOne(targetEntity="Flujoexpediente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="flujoExpediente_idflujoExpediente", referencedColumnName="idflujoExpediente")
     * })
     */
    private $flujoexpedienteIdflujoexpediente;

    /**
     * @var \Venta
     *
     * @ORM\ManyToOne(targetEntity="Venta")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="venta_idventa", referencedColumnName="idventa")
     * })
     */
    private $ventaIdventa;

    public function getIdflujoexpedienteventa(): ?int
    {
        return $this->idflujoexpedienteventa;
    }

    public function getFlujoexpedienteIdflujoexpediente(): ?Flujoexpediente
    {
        return $this->flujoexpedienteIdflujoexpediente;
    }

    public function setFlujoexpedienteIdflujoexpediente(?Flujoexpediente $flujoexpedienteIdflujoexpediente): self
    {
        $this->flujoexpedienteIdflujoexpediente = $flujoexpedienteIdflujoexpediente;

        return $this;
    }

    public function getVentaIdventa(): ?Venta
    {
        return $this->ventaIdventa;
    }

    public function setVentaIdventa(?Venta $ventaIdventa): self
    {
        $this->ventaIdventa = $ventaIdventa;

        return $this;
    }


}
