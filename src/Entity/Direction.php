<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Direction
 *
 * @ORM\Table(name="direction", indexes={@ORM\Index(name="fk_direction_cliente1_idx", columns={"cliente_idcliente"})})
 * @ORM\Entity
 */
class Direction
{
    /**
     * @var int
     *
     * @ORM\Column(name="idDirection", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $iddirection;

    /**
     * @var string
     *
     * @ORM\Column(name="city", type="string", length=45, nullable=false)
     */
    private $city = '';

    /**
     * @var string
     *
     * @ORM\Column(name="zip", type="string", length=45, nullable=false)
     */
    private $zip = '';

    /**
     * @var string
     *
     * @ORM\Column(name="country", type="string", length=45, nullable=false)
     */
    private $country = '';

    /**
     * @var string
     *
     * @ORM\Column(name="district", type="string", length=45, nullable=false)
     */
    private $district = '';

    /**
     * @var string
     *
     * @ORM\Column(name="state", type="string", length=45, nullable=false)
     */
    private $state = '';

    /**
     * @var string
     *
     * @ORM\Column(name="outNum", type="string", length=45, nullable=false)
     */
    private $outnum = '';

    /**
     * @var string
     *
     * @ORM\Column(name="inNum", type="string", length=45, nullable=false)
     */
    private $innum = '';

    /**
     * @var string
     *
     * @ORM\Column(name="betweenSt1", type="string", length=45, nullable=false)
     */
    private $betweenst1 = '';

    /**
     * @var string
     *
     * @ORM\Column(name="betweenSt2", type="string", length=45, nullable=false)
     */
    private $betweenst2 = '';

    /**
     * @var string|null
     *
     * @ORM\Column(name="dirref", type="string", length=45, nullable=true)
     */
    private $dirref = '';

    /**
     * @var string|null
     *
     * @ORM\Column(name="preWhoRecives", type="string", length=45, nullable=true)
     */
    private $prewhorecives = '';

    /**
     * @var string|null
     *
     * @ORM\Column(name="schedule", type="string", length=45, nullable=true)
     */
    private $schedule = '';

    /**
     * @var string|null
     *
     * @ORM\Column(name="typeOfAddress", type="string", length=45, nullable=true)
     */
    private $typeofaddress = '';

    /**
     * @var bool|null
     *
     * @ORM\Column(name="status", type="boolean", nullable=true, options={"default"="1"})
     */
    private $status = true;

    /**
     * @var string|null
     *
     * @ORM\Column(name="tel1", type="string", length=45, nullable=true)
     */
    private $tel1 = '';

    /**
     * @var string|null
     *
     * @ORM\Column(name="tel2", type="string", length=45, nullable=true)
     */
    private $tel2 = '';

    /**
     * @var string|null
     *
     * @ORM\Column(name="street", type="string", length=45, nullable=true)
     */
    private $street = '';

    /**
     * @var string|null
     *
     * @ORM\Column(name="alias", type="string", length=45, nullable=true, options={"default"="Direccion"})
     */
    private $alias = 'Direccion';

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="regisTime", type="datetime", nullable=true)
     */
    private $registime;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="modifTime", type="datetime", nullable=true)
     */
    private $modiftime;

    /**
     * @var \Cliente
     *
     * @ORM\ManyToOne(targetEntity="Cliente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="cliente_idcliente", referencedColumnName="idcliente")
     * })
     */
    private $clienteIdcliente;

    public function getIddirection(): ?int
    {
        return $this->iddirection;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(string $city): self
    {
        $this->city = $city;

        return $this;
    }

    public function getZip(): ?string
    {
        return $this->zip;
    }

    public function setZip(string $zip): self
    {
        $this->zip = $zip;

        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(string $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function getDistrict(): ?string
    {
        return $this->district;
    }

    public function setDistrict(string $district): self
    {
        $this->district = $district;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function getOutnum(): ?string
    {
        return $this->outnum;
    }

    public function setOutnum(string $outnum): self
    {
        $this->outnum = $outnum;

        return $this;
    }

    public function getInnum(): ?string
    {
        return $this->innum;
    }

    public function setInnum(string $innum): self
    {
        $this->innum = $innum;

        return $this;
    }

    public function getBetweenst1(): ?string
    {
        return $this->betweenst1;
    }

    public function setBetweenst1(string $betweenst1): self
    {
        $this->betweenst1 = $betweenst1;

        return $this;
    }

    public function getBetweenst2(): ?string
    {
        return $this->betweenst2;
    }

    public function setBetweenst2(string $betweenst2): self
    {
        $this->betweenst2 = $betweenst2;

        return $this;
    }

    public function getDirref(): ?string
    {
        return $this->dirref;
    }

    public function setDirref(?string $dirref): self
    {
        $this->dirref = $dirref;

        return $this;
    }

    public function getPrewhorecives(): ?string
    {
        return $this->prewhorecives;
    }

    public function setPrewhorecives(?string $prewhorecives): self
    {
        $this->prewhorecives = $prewhorecives;

        return $this;
    }

    public function getSchedule(): ?string
    {
        return $this->schedule;
    }

    public function setSchedule(?string $schedule): self
    {
        $this->schedule = $schedule;

        return $this;
    }

    public function getTypeofaddress(): ?string
    {
        return $this->typeofaddress;
    }

    public function setTypeofaddress(?string $typeofaddress): self
    {
        $this->typeofaddress = $typeofaddress;

        return $this;
    }

    public function getStatus(): ?bool
    {
        return $this->status;
    }

    public function setStatus(?bool $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getTel1(): ?string
    {
        return $this->tel1;
    }

    public function setTel1(?string $tel1): self
    {
        $this->tel1 = $tel1;

        return $this;
    }

    public function getTel2(): ?string
    {
        return $this->tel2;
    }

    public function setTel2(?string $tel2): self
    {
        $this->tel2 = $tel2;

        return $this;
    }

    public function getStreet(): ?string
    {
        return $this->street;
    }

    public function setStreet(?string $street): self
    {
        $this->street = $street;

        return $this;
    }

    public function getAlias(): ?string
    {
        return $this->alias;
    }

    public function setAlias(?string $alias): self
    {
        $this->alias = $alias;

        return $this;
    }

    public function getRegistime(): ?\DateTimeInterface
    {
        return $this->registime;
    }

    public function setRegistime(?\DateTimeInterface $registime): self
    {
        $this->registime = $registime;

        return $this;
    }

    public function getModiftime(): ?\DateTimeInterface
    {
        return $this->modiftime;
    }

    public function setModiftime(?\DateTimeInterface $modiftime): self
    {
        $this->modiftime = $modiftime;

        return $this;
    }

    public function getClienteIdcliente(): ?Cliente
    {
        return $this->clienteIdcliente;
    }

    public function setClienteIdcliente(?Cliente $clienteIdcliente): self
    {
        $this->clienteIdcliente = $clienteIdcliente;

        return $this;
    }


}
