<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Mktcampaign
 *
 * @ORM\Table(name="mktCampaign", indexes={@ORM\Index(name="fk_mktCampaign_mktTemplate1_idx", columns={"mktTemplate_idmktTemplate"}), @ORM\Index(name="fk_mktCampaign_usuario1_idx", columns={"usuario_idusuario"})})
 * @ORM\Entity
 */
class Mktcampaign
{
    /**
     * @var int
     *
     * @ORM\Column(name="idmktCampaign", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idmktcampaign;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=45, nullable=false)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="createdAt", type="datetime", nullable=false)
     */
    private $createdat;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="updatedAt", type="datetime", nullable=false)
     */
    private $updatedat;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="startDate", type="datetime", nullable=false)
     */
    private $startdate;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="endDate", type="datetime", nullable=false)
     */
    private $enddate;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="specificSendDate", type="datetime", nullable=true)
     */
    private $specificsenddate;

    /**
     * @var string|null
     *
     * @ORM\Column(name="sendEvery", type="text", length=65535, nullable=true)
     */
    private $sendevery;

    /**
     * @var \Mkttemplate
     *
     * @ORM\ManyToOne(targetEntity="Mkttemplate")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="mktTemplate_idmktTemplate", referencedColumnName="idmktTemplate")
     * })
     */
    private $mkttemplateIdmkttemplate;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    public function getIdmktcampaign(): ?int
    {
        return $this->idmktcampaign;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getCreatedat(): ?\DateTimeInterface
    {
        return $this->createdat;
    }

    public function setCreatedat(\DateTimeInterface $createdat): self
    {
        $this->createdat = $createdat;

        return $this;
    }

    public function getUpdatedat(): ?\DateTimeInterface
    {
        return $this->updatedat;
    }

    public function setUpdatedat(\DateTimeInterface $updatedat): self
    {
        $this->updatedat = $updatedat;

        return $this;
    }

    public function getStartdate(): ?\DateTimeInterface
    {
        return $this->startdate;
    }

    public function setStartdate(\DateTimeInterface $startdate): self
    {
        $this->startdate = $startdate;

        return $this;
    }

    public function getEnddate(): ?\DateTimeInterface
    {
        return $this->enddate;
    }

    public function setEnddate(\DateTimeInterface $enddate): self
    {
        $this->enddate = $enddate;

        return $this;
    }

    public function getSpecificsenddate(): ?\DateTimeInterface
    {
        return $this->specificsenddate;
    }

    public function setSpecificsenddate(?\DateTimeInterface $specificsenddate): self
    {
        $this->specificsenddate = $specificsenddate;

        return $this;
    }

    public function getSendevery(): ?string
    {
        return $this->sendevery;
    }

    public function setSendevery(?string $sendevery): self
    {
        $this->sendevery = $sendevery;

        return $this;
    }

    public function getMkttemplateIdmkttemplate(): ?Mkttemplate
    {
        return $this->mkttemplateIdmkttemplate;
    }

    public function setMkttemplateIdmkttemplate(?Mkttemplate $mkttemplateIdmkttemplate): self
    {
        $this->mkttemplateIdmkttemplate = $mkttemplateIdmkttemplate;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }


}
