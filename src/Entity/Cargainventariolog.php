<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Cargainventariolog
 *
 * @ORM\Table(name="cargaInventarioLog", indexes={@ORM\Index(name="fk_cargaInventarioLog_stock1_idx", columns={"stock_idstock"}), @ORM\Index(name="fk_cargaInventarioLog_usuario1_idx", columns={"usuario_idusuario"}), @ORM\Index(name="fk_cargaInventarioLog_proveedor1_idx", columns={"proveedor_idproveedor"})})
 * @ORM\Entity
 */
class Cargainventariolog
{
    /**
     * @var int
     *
     * @ORM\Column(name="idcargaInventarioLog", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idcargainventariolog;

    /**
     * @var float|null
     *
     * @ORM\Column(name="cantidad", type="float", precision=10, scale=0, nullable=true)
     */
    private $cantidad;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fecha", type="datetime", nullable=true)
     */
    private $fecha;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="conExcel", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $conexcel;

    /**
     * @var string|null
     *
     * @ORM\Column(name="archivoExcel", type="string", length=450, nullable=true)
     */
    private $archivoexcel;

    /**
     * @var string|null
     *
     * @ORM\Column(name="numeroFactura", type="string", length=45, nullable=true)
     */
    private $numerofactura;

    /**
     * @var string|null
     *
     * @ORM\Column(name="tipo", type="string", length=1, nullable=true)
     */
    private $tipo = '0';

    /**
     * @var string|null
     *
     * @ORM\Column(name="justificacion", type="string", length=45, nullable=true)
     */
    private $justificacion;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    /**
     * @var \Stock
     *
     * @ORM\ManyToOne(targetEntity="Stock")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="stock_idstock", referencedColumnName="idstock")
     * })
     */
    private $stockIdstock;

    /**
     * @var \Proveedor
     *
     * @ORM\ManyToOne(targetEntity="Proveedor")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="proveedor_idproveedor", referencedColumnName="idproveedor")
     * })
     */
    private $proveedorIdproveedor;

    public function getIdcargainventariolog(): ?int
    {
        return $this->idcargainventariolog;
    }

    public function getCantidad(): ?float
    {
        return $this->cantidad;
    }

    public function setCantidad(?float $cantidad): self
    {
        $this->cantidad = $cantidad;

        return $this;
    }

    public function getFecha(): ?\DateTimeInterface
    {
        return $this->fecha;
    }

    public function setFecha(?\DateTimeInterface $fecha): self
    {
        $this->fecha = $fecha;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getConexcel(): ?string
    {
        return $this->conexcel;
    }

    public function setConexcel(?string $conexcel): self
    {
        $this->conexcel = $conexcel;

        return $this;
    }

    public function getArchivoexcel(): ?string
    {
        return $this->archivoexcel;
    }

    public function setArchivoexcel(?string $archivoexcel): self
    {
        $this->archivoexcel = $archivoexcel;

        return $this;
    }

    public function getNumerofactura(): ?string
    {
        return $this->numerofactura;
    }

    public function setNumerofactura(?string $numerofactura): self
    {
        $this->numerofactura = $numerofactura;

        return $this;
    }

    public function getTipo(): ?string
    {
        return $this->tipo;
    }

    public function setTipo(?string $tipo): self
    {
        $this->tipo = $tipo;

        return $this;
    }

    public function getJustificacion(): ?string
    {
        return $this->justificacion;
    }

    public function setJustificacion(?string $justificacion): self
    {
        $this->justificacion = $justificacion;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }

    public function getStockIdstock(): ?Stock
    {
        return $this->stockIdstock;
    }

    public function setStockIdstock(?Stock $stockIdstock): self
    {
        $this->stockIdstock = $stockIdstock;

        return $this;
    }

    public function getProveedorIdproveedor(): ?Proveedor
    {
        return $this->proveedorIdproveedor;
    }

    public function setProveedorIdproveedor(?Proveedor $proveedorIdproveedor): self
    {
        $this->proveedorIdproveedor = $proveedorIdproveedor;

        return $this;
    }


}
