<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Mktmessageclient
 *
 * @ORM\Table(name="mktMessageClient", indexes={@ORM\Index(name="fk_mktMessageClient_cliente1_idx", columns={"cliente_idcliente"}), @ORM\Index(name="fk_mktMessageClient_mktMessage1_idx", columns={"mktMessage_idmktMessage"})})
 * @ORM\Entity
 */
class Mktmessageclient
{
    /**
     * @var int
     *
     * @ORM\Column(name="idmktMessageClient", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idmktmessageclient;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="sentAt", type="datetime", nullable=false)
     */
    private $sentat;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="openedAt", type="datetime", nullable=true)
     */
    private $openedat;

    /**
     * @var string
     *
     * @ORM\Column(name="body", type="text", length=65535, nullable=false)
     */
    private $body;

    /**
     * @var string|null
     *
     * @ORM\Column(name="messageId", type="string", length=45, nullable=true)
     */
    private $messageid;

    /**
     * @var string
     *
     * @ORM\Column(name="curStatus", type="string", length=45, nullable=false)
     */
    private $curstatus;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="clickedAt", type="datetime", nullable=true)
     */
    private $clickedat;

    /**
     * @var string|null
     *
     * @ORM\Column(name="email", type="string", length=45, nullable=true)
     */
    private $email;

    /**
     * @var string|null
     *
     * @ORM\Column(name="phone", type="string", length=45, nullable=true)
     */
    private $phone;

    /**
     * @var \Cliente
     *
     * @ORM\ManyToOne(targetEntity="Cliente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="cliente_idcliente", referencedColumnName="idcliente")
     * })
     */
    private $clienteIdcliente;

    /**
     * @var \Mktmessage
     *
     * @ORM\ManyToOne(targetEntity="Mktmessage")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="mktMessage_idmktMessage", referencedColumnName="idmktMessage")
     * })
     */
    private $mktmessageIdmktmessage;

    public function getIdmktmessageclient(): ?int
    {
        return $this->idmktmessageclient;
    }

    public function getSentat(): ?\DateTimeInterface
    {
        return $this->sentat;
    }

    public function setSentat(\DateTimeInterface $sentat): self
    {
        $this->sentat = $sentat;

        return $this;
    }

    public function getOpenedat(): ?\DateTimeInterface
    {
        return $this->openedat;
    }

    public function setOpenedat(?\DateTimeInterface $openedat): self
    {
        $this->openedat = $openedat;

        return $this;
    }

    public function getBody(): ?string
    {
        return $this->body;
    }

    public function setBody(string $body): self
    {
        $this->body = $body;

        return $this;
    }

    public function getMessageid(): ?string
    {
        return $this->messageid;
    }

    public function setMessageid(?string $messageid): self
    {
        $this->messageid = $messageid;

        return $this;
    }

    public function getCurstatus(): ?string
    {
        return $this->curstatus;
    }

    public function setCurstatus(string $curstatus): self
    {
        $this->curstatus = $curstatus;

        return $this;
    }

    public function getClickedat(): ?\DateTimeInterface
    {
        return $this->clickedat;
    }

    public function setClickedat(?\DateTimeInterface $clickedat): self
    {
        $this->clickedat = $clickedat;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setPhone(?string $phone): self
    {
        $this->phone = $phone;

        return $this;
    }

    public function getClienteIdcliente(): ?Cliente
    {
        return $this->clienteIdcliente;
    }

    public function setClienteIdcliente(?Cliente $clienteIdcliente): self
    {
        $this->clienteIdcliente = $clienteIdcliente;

        return $this;
    }

    public function getMktmessageIdmktmessage(): ?Mktmessage
    {
        return $this->mktmessageIdmktmessage;
    }

    public function setMktmessageIdmktmessage(?Mktmessage $mktmessageIdmktmessage): self
    {
        $this->mktmessageIdmktmessage = $mktmessageIdmktmessage;

        return $this;
    }


}
