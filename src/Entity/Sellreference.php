<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Sellreference
 *
 * @ORM\Table(name="sellReference")
 * @ORM\Entity
 */
class Sellreference
{
    /**
     * @var int
     *
     * @ORM\Column(name="idsellReference", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idsellreference;

    /**
     * @var string
     *
     * @ORM\Column(name="name", type="string", length=100, nullable=false)
     */
    private $name;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string
     *
     * @ORM\Column(name="commissionStorablePercentage", type="decimal", precision=5, scale=2, nullable=false)
     */
    private $commissionstorablepercentage;

    /**
     * @var string
     *
     * @ORM\Column(name="commissionServicePercentage", type="decimal", precision=5, scale=2, nullable=false)
     */
    private $commissionservicepercentage;

    public function getIdsellreference(): ?int
    {
        return $this->idsellreference;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getCommissionstorablepercentage(): ?string
    {
        return $this->commissionstorablepercentage;
    }

    public function setCommissionstorablepercentage(string $commissionstorablepercentage): self
    {
        $this->commissionstorablepercentage = $commissionstorablepercentage;

        return $this;
    }

    public function getCommissionservicepercentage(): ?string
    {
        return $this->commissionservicepercentage;
    }

    public function setCommissionservicepercentage(string $commissionservicepercentage): self
    {
        $this->commissionservicepercentage = $commissionservicepercentage;

        return $this;
    }

    public function __toString() {
        return strval($this->name);
    }
}
