<?php

namespace App\Entity;
use App\Entity\Producto;
use App\Entity\Sucursal;



use Doctrine\ORM\Mapping as ORM;

/**
 * Reglaabastecimiento
 *
 * @ORM\Table(name="reglaAbastecimiento", indexes={@ORM\Index(name="fk_reglaAbastecimiento_producto1_idx", columns={"producto_idproducto"}), @ORM\Index(name="fk_reglaAbastecimiento_sucursal1_idx", columns={"sucursal_idsucursal"})})
 * @ORM\Entity
 */
class Reglaabastecimiento
{
    /**
     * @var int
     *
     * @ORM\Column(name="idreglaAbastecimiento", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idreglaabastecimiento;

    /**
     * @var float
     *
     * @ORM\Column(name="cantidadMinima", type="float", precision=10, scale=0, nullable=false)
     */
    private $cantidadminima;

    /**
     * @var float
     *
     * @ORM\Column(name="cantidadMaxima", type="float", precision=10, scale=0, nullable=false)
     */
    private $cantidadmaxima;

    /**
     * @var \Producto
     *
     * @ORM\ManyToOne(targetEntity="Producto")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="producto_idproducto", referencedColumnName="idproducto")
     * })
     */
    private $productoIdproducto;

    /**
     * @var \Sucursal
     *
     * @ORM\ManyToOne(targetEntity="Sucursal")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="sucursal_idsucursal", referencedColumnName="idsucursal")
     * })
     */
    private $sucursalIdsucursal;

    public function getIdreglaabastecimiento(): ?int
    {
        return $this->idreglaabastecimiento;
    }

    public function getCantidadminima(): ?float
    {
        return $this->cantidadminima;
    }

    public function setCantidadminima(float $cantidadminima): self
    {
        $this->cantidadminima = $cantidadminima;

        return $this;
    }

    public function getCantidadmaxima(): ?float
    {
        return $this->cantidadmaxima;
    }

    public function setCantidadmaxima(float $cantidadmaxima): self
    {
        $this->cantidadmaxima = $cantidadmaxima;

        return $this;
    }

    public function getProductoIdproducto(): ?Producto
    {
        return $this->productoIdproducto;
    }

    public function setProductoIdproducto(?Producto $productoIdproducto): self
    {
        $this->productoIdproducto = $productoIdproducto;

        return $this;
    }

    public function getSucursalIdsucursal(): ?Sucursal
    {
        return $this->sucursalIdsucursal;
    }

    public function setSucursalIdsucursal(?Sucursal $sucursalIdsucursal): self
    {
        $this->sucursalIdsucursal = $sucursalIdsucursal;

        return $this;
    }

}
