<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Mktlistcliente
 *
 * @ORM\Table(name="mktListCliente", indexes={@ORM\Index(name="fk_mktListCliente_cliente1_idx", columns={"cliente_idcliente"}), @ORM\Index(name="fk_mktListCliente_mktList1_idx", columns={"mktList_idmktList"})})
 * @ORM\Entity
 */
class Mktlistcliente
{
    /**
     * @var int
     *
     * @ORM\Column(name="idmktListCliente", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idmktlistcliente;

    /**
     * @var \Cliente
     *
     * @ORM\ManyToOne(targetEntity="Cliente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="cliente_idcliente", referencedColumnName="idcliente")
     * })
     */
    private $clienteIdcliente;

    /**
     * @var \Mktlist
     *
     * @ORM\ManyToOne(targetEntity="Mktlist")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="mktList_idmktList", referencedColumnName="idmktList")
     * })
     */
    private $mktlistIdmktlist;

    public function getIdmktlistcliente(): ?int
    {
        return $this->idmktlistcliente;
    }

    public function getClienteIdcliente(): ?Cliente
    {
        return $this->clienteIdcliente;
    }

    public function setClienteIdcliente(?Cliente $clienteIdcliente): self
    {
        $this->clienteIdcliente = $clienteIdcliente;

        return $this;
    }

    public function getMktlistIdmktlist(): ?Mktlist
    {
        return $this->mktlistIdmktlist;
    }

    public function setMktlistIdmktlist(?Mktlist $mktlistIdmktlist): self
    {
        $this->mktlistIdmktlist = $mktlistIdmktlist;

        return $this;
    }


}
