<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Pago
 *
 * @ORM\Table(name="pago", indexes={@ORM\Index(name="fk_anticipo_venta1_idx", columns={"venta_idventa"}), @ORM\Index(name="fk_pago_usuario1_idx", columns={"usuario_valido"}), @ORM\Index(name="fk_pago_usuario2_idx", columns={"usuario_cobro"}), @ORM\Index(name="fk_pago_paymentType1_idx", columns={"paymentType_idpaymentType"})})
 * @ORM\Entity
 */
class Pago
{
    /**
     * @var int
     *
     * @ORM\Column(name="idpago", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idpago;

    /**
     * @var string
     *
     * @ORM\Column(name="monto", type="decimal", precision=9, scale=2, nullable=false)
     */
    private $monto;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="fecha", type="datetime", nullable=false)
     */
    private $fecha;

    /**
     * @var string|null
     *
     * @ORM\Column(name="total", type="decimal", precision=9, scale=2, nullable=true)
     */
    private $total;

    /**
     * @var string|null
     *
     * @ORM\Column(name="restan", type="decimal", precision=9, scale=2, nullable=true)
     */
    private $restan;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="tipoPago", type="string", length=150, nullable=true)
     */
    private $tipopago;

    /**
     * @var string|null
     *
     * @ORM\Column(name="validado", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $validado;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaValidacion", type="datetime", nullable=true)
     */
    private $fechavalidacion;

    /**
     * @var int|null
     *
     * @ORM\Column(name="mesesIntereses", type="integer", nullable=true)
     */
    private $mesesintereses;

    /**
     * @var string|null
     *
     * @ORM\Column(name="verified", type="string", length=1, nullable=true)
     */
    private $verified = '0';

    /**
     * @var string|null
     *
     * @ORM\Column(name="automatic", type="string", length=1, nullable=true)
     */
    private $automatic = '0';

    /**
     * @var string|null
     *
     * @ORM\Column(name="justification", type="string", length=2000, nullable=true)
     */
    private $justification;

    /**
     * @var \Venta
     *
     * @ORM\ManyToOne(targetEntity="Venta")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="venta_idventa", referencedColumnName="idventa")
     * })
     */
    private $ventaIdventa;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_valido", referencedColumnName="idusuario")
     * })
     */
    private $usuarioValido;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_cobro", referencedColumnName="idusuario")
     * })
     */
    private $usuarioCobro;

    /**
     * @var \Paymenttype
     *
     * @ORM\ManyToOne(targetEntity="Paymenttype")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="paymentType_idpaymentType", referencedColumnName="idpaymentType")
     * })
     */
    private $paymenttypeIdpaymenttype;

    public function getIdpago(): ?int
    {
        return $this->idpago;
    }

    public function getMonto(): ?string
    {
        return $this->monto;
    }

    public function setMonto(string $monto): self
    {
        $this->monto = $monto;

        return $this;
    }

    public function getFecha(): ?\DateTimeInterface
    {
        return $this->fecha;
    }

    public function setFecha(\DateTimeInterface $fecha): self
    {
        $this->fecha = $fecha;

        return $this;
    }

    public function getTotal(): ?string
    {
        return $this->total;
    }

    public function setTotal(?string $total): self
    {
        $this->total = $total;

        return $this;
    }

    public function getRestan(): ?string
    {
        return $this->restan;
    }

    public function setRestan(?string $restan): self
    {
        $this->restan = $restan;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getTipopago(): ?string
    {
        return $this->tipopago;
    }

    public function setTipopago(?string $tipopago): self
    {
        $this->tipopago = $tipopago;

        return $this;
    }

    public function getValidado(): ?string
    {
        return $this->validado;
    }

    public function setValidado(?string $validado): self
    {
        $this->validado = $validado;

        return $this;
    }

    public function getFechavalidacion(): ?\DateTimeInterface
    {
        return $this->fechavalidacion;
    }

    public function setFechavalidacion(?\DateTimeInterface $fechavalidacion): self
    {
        $this->fechavalidacion = $fechavalidacion;

        return $this;
    }

    public function getMesesintereses(): ?int
    {
        return $this->mesesintereses;
    }

    public function setMesesintereses(?int $mesesintereses): self
    {
        $this->mesesintereses = $mesesintereses;

        return $this;
    }

    public function getVerified(): ?string
    {
        return $this->verified;
    }

    public function setVerified(?string $verified): self
    {
        $this->verified = $verified;

        return $this;
    }

    public function getAutomatic(): ?string
    {
        return $this->automatic;
    }

    public function setAutomatic(?string $automatic): self
    {
        $this->automatic = $automatic;

        return $this;
    }

    public function getJustification(): ?string
    {
        return $this->justification;
    }

    public function setJustification(?string $justification): self
    {
        $this->justification = $justification;

        return $this;
    }

    public function getVentaIdventa(): ?Venta
    {
        return $this->ventaIdventa;
    }

    public function setVentaIdventa(?Venta $ventaIdventa): self
    {
        $this->ventaIdventa = $ventaIdventa;

        return $this;
    }

    public function getUsuarioValido(): ?Usuario
    {
        return $this->usuarioValido;
    }

    public function setUsuarioValido(?Usuario $usuarioValido): self
    {
        $this->usuarioValido = $usuarioValido;

        return $this;
    }

    public function getUsuarioCobro(): ?Usuario
    {
        return $this->usuarioCobro;
    }

    public function setUsuarioCobro(?Usuario $usuarioCobro): self
    {
        $this->usuarioCobro = $usuarioCobro;

        return $this;
    }

    public function getPaymenttypeIdpaymenttype(): ?Paymenttype
    {
        return $this->paymenttypeIdpaymenttype;
    }

    public function setPaymenttypeIdpaymenttype(?Paymenttype $paymenttypeIdpaymenttype): self
    {
        $this->paymenttypeIdpaymenttype = $paymenttypeIdpaymenttype;

        return $this;
    }


}
