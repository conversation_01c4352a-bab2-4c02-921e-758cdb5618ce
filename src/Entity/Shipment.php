<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Shipment
 *
 * @ORM\Table(name="shipment", indexes={@ORM\Index(name="fk_shipment_shippingCompany1_idx", columns={"shippingCompany_idshippingCompany"}), @ORM\Index(name="fk_shipment_venta1_idx", columns={"venta_idventa"})})
 * @ORM\Entity
 */
class Shipment
{
    /**
     * @var int
     *
     * @ORM\Column(name="idshipment", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idshipment;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="trackingNumber", type="string", length=45, nullable=true)
     */
    private $trackingnumber;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="creationDate", type="datetime", nullable=false)
     */
    private $creationdate;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="updateDate", type="datetime", nullable=false)
     */
    private $updatedate;

    /**
     * @var string
     *
     * @ORM\Column(name="shipmentStage", type="string", length=45, nullable=false, options={"default"="Orden creada"})
     */
    private $shipmentstage = 'Orden creada';

    /**
     * @var string|null
     *
     * @ORM\Column(name="shippingAddress", type="text", length=65535, nullable=true)
     */
    private $shippingaddress;

    /**
     * @var string
     *
     * @ORM\Column(name="onlineStore", type="string", length=1, nullable=false, options={"fixed"=true})
     */
    private $onlinestore = '0';

    /**
     * @var string|null
     *
     * @ORM\Column(name="comments", type="text", length=65535, nullable=true)
     */
    private $comments;

    /**
     * @var string
     *
     * @ORM\Column(name="allAccepted", type="string", length=45, nullable=false)
     */
    private $allaccepted = '0';

    /**
     * @var \Shippingcompany
     *
     * @ORM\ManyToOne(targetEntity="Shippingcompany")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="shippingCompany_idshippingCompany", referencedColumnName="idshippingCompany")
     * })
     */
    private $shippingcompanyIdshippingcompany;

    /**
     * @var \Venta
     *
     * @ORM\ManyToOne(targetEntity="Venta")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="venta_idventa", referencedColumnName="idventa")
     * })
     */
    private $ventaIdventa;

    public function getIdshipment(): ?int
    {
        return $this->idshipment;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getTrackingnumber(): ?string
    {
        return $this->trackingnumber;
    }

    public function setTrackingnumber(?string $trackingnumber): self
    {
        $this->trackingnumber = $trackingnumber;

        return $this;
    }

    public function getCreationdate(): ?\DateTimeInterface
    {
        return $this->creationdate;
    }

    public function setCreationdate(\DateTimeInterface $creationdate): self
    {
        $this->creationdate = $creationdate;

        return $this;
    }

    public function getUpdatedate(): ?\DateTimeInterface
    {
        return $this->updatedate;
    }

    public function setUpdatedate(\DateTimeInterface $updatedate): self
    {
        $this->updatedate = $updatedate;

        return $this;
    }

    public function getShipmentstage(): ?string
    {
        return $this->shipmentstage;
    }

    public function setShipmentstage(string $shipmentstage): self
    {
        $this->shipmentstage = $shipmentstage;

        return $this;
    }

    public function getShippingaddress(): ?string
    {
        return $this->shippingaddress;
    }

    public function setShippingaddress(?string $shippingaddress): self
    {
        $this->shippingaddress = $shippingaddress;

        return $this;
    }

    public function getOnlinestore(): ?string
    {
        return $this->onlinestore;
    }

    public function setOnlinestore(string $onlinestore): self
    {
        $this->onlinestore = $onlinestore;

        return $this;
    }

    public function getComments(): ?string
    {
        return $this->comments;
    }

    public function setComments(?string $comments): self
    {
        $this->comments = $comments;

        return $this;
    }

    public function getAllaccepted(): ?string
    {
        return $this->allaccepted;
    }

    public function setAllaccepted(string $allaccepted): self
    {
        $this->allaccepted = $allaccepted;

        return $this;
    }

    public function getShippingcompanyIdshippingcompany(): ?Shippingcompany
    {
        return $this->shippingcompanyIdshippingcompany;
    }

    public function setShippingcompanyIdshippingcompany(?Shippingcompany $shippingcompanyIdshippingcompany): self
    {
        $this->shippingcompanyIdshippingcompany = $shippingcompanyIdshippingcompany;

        return $this;
    }

    public function getVentaIdventa(): ?Venta
    {
        return $this->ventaIdventa;
    }

    public function setVentaIdventa(?Venta $ventaIdventa): self
    {
        $this->ventaIdventa = $ventaIdventa;

        return $this;
    }


}
