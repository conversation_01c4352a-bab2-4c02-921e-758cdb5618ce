<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Ventagroup
 *
 * @ORM\Table(name="ventaGroup", indexes={@ORM\Index(name="fk_ventaGroup_ventaFactura1_idx", columns={"ventaFactura_idventaFactura"})})
 * @ORM\Entity
 */
class Ventagroup
{
    /**
     * @var int
     *
     * @ORM\Column(name="idventaGroup", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idventagroup;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="creationDate", type="datetime", nullable=true)
     */
    private $creationdate;

    /**
     * @var string|null
     *
     * @ORM\Column(name="paymentFile", type="text", length=65535, nullable=true)
     */
    private $paymentfile;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1"})
     */
    private $status = '1';

    /**
     * @var \Ventafactura
     *
     * @ORM\ManyToOne(targetEntity="Ventafactura")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="ventaFactura_idventaFactura", referencedColumnName="idventaFactura")
     * })
     */
    private $ventafacturaIdventafactura;

    public function getIdventagroup(): ?int
    {
        return $this->idventagroup;
    }

    public function getCreationdate(): ?\DateTimeInterface
    {
        return $this->creationdate;
    }

    public function setCreationdate(?\DateTimeInterface $creationdate): self
    {
        $this->creationdate = $creationdate;

        return $this;
    }

    public function getPaymentfile(): ?string
    {
        return $this->paymentfile;
    }

    public function setPaymentfile(?string $paymentfile): self
    {
        $this->paymentfile = $paymentfile;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getVentafacturaIdventafactura(): ?Ventafactura
    {
        return $this->ventafacturaIdventafactura;
    }

    public function setVentafacturaIdventafactura(?Ventafactura $ventafacturaIdventafactura): self
    {
        $this->ventafacturaIdventafactura = $ventafacturaIdventafactura;

        return $this;
    }


}
