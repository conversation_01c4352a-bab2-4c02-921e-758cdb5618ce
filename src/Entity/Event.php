<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Event
 *
 * @ORM\Table(name="event", indexes={@ORM\Index(name="fk_event_cliente1_idx", columns={"cliente_idcliente"}), @ORM\Index(name="fk_event_eventType1_idx", columns={"eventType_ideventType"}), @ORM\Index(name="fk_event_ordenLaboratorio1_idx", columns={"ordenLaboratorio_idordenLaboratorio"}), @ORM\Index(name="fk_event_shipment1_idx", columns={"shipment_idshipment"}), @ORM\Index(name="fk_event_sucursal1_idx", columns={"sucursal_idsucursal"}), @ORM\Index(name="fk_event_usuario1_idx", columns={"usuario_idusuario"}), @ORM\Index(name="fk_event_venta1_idx", columns={"venta_idventa"})})
 * @ORM\Entity
 */
class Event
{
    /**
     * @var int
     *
     * @ORM\Column(name="idevent", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idevent;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="eventDate", type="datetime", nullable=true)
     */
    private $eventdate;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="creationDate", type="datetime", nullable=false)
     */
    private $creationdate;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="updateDate", type="datetime", nullable=false)
     */
    private $updatedate;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string
     *
     * @ORM\Column(name="urgency", type="string", length=1, nullable=false, options={"fixed"=true})
     */
    private $urgency;

    /**
     * @var string|null
     *
     * @ORM\Column(name="comments", type="text", length=65535, nullable=true)
     */
    private $comments;

    /**
     * @var string|null
     *
     * @ORM\Column(name="isConfirmed", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $isconfirmed;

    /**
     * @var string|null
     *
     * @ORM\Column(name="isDone", type="string", length=1, nullable=true, options={"fixed"=true})
     */
    private $isdone;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="startDate", type="datetime", nullable=true)
     */
    private $startdate;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="endDate", type="datetime", nullable=true)
     */
    private $enddate;

    /**
     * @var \Cliente
     *
     * @ORM\ManyToOne(targetEntity="Cliente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="cliente_idcliente", referencedColumnName="idcliente")
     * })
     */
    private $clienteIdcliente;

    /**
     * @var \Eventtype
     *
     * @ORM\ManyToOne(targetEntity="Eventtype")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="eventType_ideventType", referencedColumnName="ideventType")
     * })
     */
    private $eventtypeIdeventtype;

    /**
     * @var \Ordenlaboratorio
     *
     * @ORM\ManyToOne(targetEntity="Ordenlaboratorio")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="ordenLaboratorio_idordenLaboratorio", referencedColumnName="idordenLaboratorio")
     * })
     */
    private $ordenlaboratorioIdordenlaboratorio;

    /**
     * @var \Shipment
     *
     * @ORM\ManyToOne(targetEntity="Shipment")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="shipment_idshipment", referencedColumnName="idshipment")
     * })
     */
    private $shipmentIdshipment;

    /**
     * @var \Sucursal
     *
     * @ORM\ManyToOne(targetEntity="Sucursal")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="sucursal_idsucursal", referencedColumnName="idsucursal")
     * })
     */
    private $sucursalIdsucursal;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    /**
     * @var \Venta
     *
     * @ORM\ManyToOne(targetEntity="Venta")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="venta_idventa", referencedColumnName="idventa")
     * })
     */
    private $ventaIdventa;

    public function getIdevent(): ?int
    {
        return $this->idevent;
    }

    public function getEventdate(): ?\DateTimeInterface
    {
        return $this->eventdate;
    }

    public function setEventdate(?\DateTimeInterface $eventdate): self
    {
        $this->eventdate = $eventdate;

        return $this;
    }

    public function getCreationdate(): ?\DateTimeInterface
    {
        return $this->creationdate;
    }

    public function setCreationdate(\DateTimeInterface $creationdate): self
    {
        $this->creationdate = $creationdate;

        return $this;
    }

    public function getUpdatedate(): ?\DateTimeInterface
    {
        return $this->updatedate;
    }

    public function setUpdatedate(\DateTimeInterface $updatedate): self
    {
        $this->updatedate = $updatedate;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getUrgency(): ?string
    {
        return $this->urgency;
    }

    public function setUrgency(string $urgency): self
    {
        $this->urgency = $urgency;

        return $this;
    }

    public function getComments(): ?string
    {
        return $this->comments;
    }

    public function setComments(?string $comments): self
    {
        $this->comments = $comments;

        return $this;
    }

    public function getIsconfirmed(): ?string
    {
        return $this->isconfirmed;
    }

    public function setIsconfirmed(?string $isconfirmed): self
    {
        $this->isconfirmed = $isconfirmed;

        return $this;
    }

    public function getIsdone(): ?string
    {
        return $this->isdone;
    }

    public function setIsdone(?string $isdone): self
    {
        $this->isdone = $isdone;

        return $this;
    }

    public function getStartdate(): ?\DateTimeInterface
    {
        return $this->startdate;
    }

    public function setStartdate(?\DateTimeInterface $startdate): self
    {
        $this->startdate = $startdate;

        return $this;
    }

    public function getEnddate(): ?\DateTimeInterface
    {
        return $this->enddate;
    }

    public function setEnddate(?\DateTimeInterface $enddate): self
    {
        $this->enddate = $enddate;

        return $this;
    }

    public function getClienteIdcliente(): ?Cliente
    {
        return $this->clienteIdcliente;
    }

    public function setClienteIdcliente(?Cliente $clienteIdcliente): self
    {
        $this->clienteIdcliente = $clienteIdcliente;

        return $this;
    }

    public function getEventtypeIdeventtype(): ?Eventtype
    {
        return $this->eventtypeIdeventtype;
    }

    public function setEventtypeIdeventtype(?Eventtype $eventtypeIdeventtype): self
    {
        $this->eventtypeIdeventtype = $eventtypeIdeventtype;

        return $this;
    }

    public function getOrdenlaboratorioIdordenlaboratorio(): ?Ordenlaboratorio
    {
        return $this->ordenlaboratorioIdordenlaboratorio;
    }

    public function setOrdenlaboratorioIdordenlaboratorio(?Ordenlaboratorio $ordenlaboratorioIdordenlaboratorio): self
    {
        $this->ordenlaboratorioIdordenlaboratorio = $ordenlaboratorioIdordenlaboratorio;

        return $this;
    }

    public function getShipmentIdshipment(): ?Shipment
    {
        return $this->shipmentIdshipment;
    }

    public function setShipmentIdshipment(?Shipment $shipmentIdshipment): self
    {
        $this->shipmentIdshipment = $shipmentIdshipment;

        return $this;
    }

    public function getSucursalIdsucursal(): ?Sucursal
    {
        return $this->sucursalIdsucursal;
    }

    public function setSucursalIdsucursal(?Sucursal $sucursalIdsucursal): self
    {
        $this->sucursalIdsucursal = $sucursalIdsucursal;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }

    public function getVentaIdventa(): ?Venta
    {
        return $this->ventaIdventa;
    }

    public function setVentaIdventa(?Venta $ventaIdventa): self
    {
        $this->ventaIdventa = $ventaIdventa;

        return $this;
    }


}
