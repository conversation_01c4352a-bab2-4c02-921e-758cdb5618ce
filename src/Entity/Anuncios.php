<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Anuncios
 *
 * @ORM\Table(name="anuncios", indexes={@ORM\Index(name="fk_anuncios_categioriaAnuncio1_idx", columns={"categoriaAnuncio_idcategoriaAnuncio"}), @ORM\Index(name="fk_anuncios_empresa1_idx", columns={"empresa_idempresa"})})
 * @ORM\Entity
 */
class Anuncios
{
    /**
     * @var int
     *
     * @ORM\Column(name="idanuncios", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idanuncios;

    /**
     * @var string
     *
     * @ORM\Column(name="titulo", type="string", length=200, nullable=false)
     */
    private $titulo;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="fechaCreacion", type="datetime", nullable=false)
     */
    private $fechacreacion;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="fechaActualizacion", type="datetime", nullable=false)
     */
    private $fechaactualizacion;

    /**
     * @var string
     *
     * @ORM\Column(name="texto", type="text", length=65535, nullable=false)
     */
    private $texto;

    /**
     * @var string
     *
     * @ORM\Column(name="envioCorreo", type="string", length=1, nullable=false, options={"fixed"=true})
     */
    private $enviocorreo = '0';

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \Empresa
     *
     * @ORM\ManyToOne(targetEntity="Empresa")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="empresa_idempresa", referencedColumnName="idempresa")
     * })
     */
    private $empresaIdempresa;

    /**
     * @var \Categoriaanuncio
     *
     * @ORM\ManyToOne(targetEntity="Categoriaanuncio")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="categoriaAnuncio_idcategoriaAnuncio", referencedColumnName="idcategoriaAnuncio")
     * })
     */
    private $categoriaanuncioIdcategoriaanuncio;

    public function getIdanuncios(): ?int
    {
        return $this->idanuncios;
    }

    public function getTitulo(): ?string
    {
        return $this->titulo;
    }

    public function setTitulo(string $titulo): self
    {
        $this->titulo = $titulo;

        return $this;
    }

    public function getFechacreacion(): ?\DateTimeInterface
    {
        return $this->fechacreacion;
    }

    public function setFechacreacion(\DateTimeInterface $fechacreacion): self
    {
        $this->fechacreacion = $fechacreacion;

        return $this;
    }

    public function getFechaactualizacion(): ?\DateTimeInterface
    {
        return $this->fechaactualizacion;
    }

    public function setFechaactualizacion(\DateTimeInterface $fechaactualizacion): self
    {
        $this->fechaactualizacion = $fechaactualizacion;

        return $this;
    }

    public function getTexto(): ?string
    {
        return $this->texto;
    }

    public function setTexto(string $texto): self
    {
        $this->texto = $texto;

        return $this;
    }

    public function getEnviocorreo(): ?string
    {
        return $this->enviocorreo;
    }

    public function setEnviocorreo(string $enviocorreo): self
    {
        $this->enviocorreo = $enviocorreo;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getEmpresaIdempresa(): ?Empresa
    {
        return $this->empresaIdempresa;
    }

    public function setEmpresaIdempresa(?Empresa $empresaIdempresa): self
    {
        $this->empresaIdempresa = $empresaIdempresa;

        return $this;
    }

    public function getCategoriaanuncioIdcategoriaanuncio(): ?Categoriaanuncio
    {
        return $this->categoriaanuncioIdcategoriaanuncio;
    }

    public function setCategoriaanuncioIdcategoriaanuncio(?Categoriaanuncio $categoriaanuncioIdcategoriaanuncio): self
    {
        $this->categoriaanuncioIdcategoriaanuncio = $categoriaanuncioIdcategoriaanuncio;

        return $this;
    }


}
