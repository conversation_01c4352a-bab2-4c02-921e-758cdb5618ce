<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Laboratorio
 *
 * @ORM\Table(name="laboratorio", indexes={@ORM\Index(name="fk_laboratorio_usuario1_idx", columns={"usuarioEnvio"}), @ORM\Index(name="fk_laboratorio_usuario3_idx", columns={"usuarioEntregoCliente"}), @ORM\Index(name="fk_laboratorio_usuario2_idx", columns={"usuarioRecibio"})})
 * @ORM\Entity
 */
class Laboratorio
{
    /**
     * @var int
     *
     * @ORM\Column(name="idlaboratorio", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idlaboratorio;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaEnvio", type="datetime", nullable=true)
     */
    private $fechaenvio;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaRegreso", type="datetime", nullable=true)
     */
    private $fecharegreso;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaEntregaCliente", type="datetime", nullable=true)
     */
    private $fechaentregacliente;

    /**
     * @var string|null
     *
     * @ORM\Column(name="estado", type="string", length=45, nullable=true)
     */
    private $estado;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuarioEntregoCliente", referencedColumnName="idusuario")
     * })
     */
    private $usuarioentregocliente;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuarioEnvio", referencedColumnName="idusuario")
     * })
     */
    private $usuarioenvio;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuarioRecibio", referencedColumnName="idusuario")
     * })
     */
    private $usuariorecibio;

    public function getIdlaboratorio(): ?int
    {
        return $this->idlaboratorio;
    }

    public function getFechaenvio(): ?\DateTimeInterface
    {
        return $this->fechaenvio;
    }

    public function setFechaenvio(?\DateTimeInterface $fechaenvio): self
    {
        $this->fechaenvio = $fechaenvio;

        return $this;
    }

    public function getFecharegreso(): ?\DateTimeInterface
    {
        return $this->fecharegreso;
    }

    public function setFecharegreso(?\DateTimeInterface $fecharegreso): self
    {
        $this->fecharegreso = $fecharegreso;

        return $this;
    }

    public function getFechaentregacliente(): ?\DateTimeInterface
    {
        return $this->fechaentregacliente;
    }

    public function setFechaentregacliente(?\DateTimeInterface $fechaentregacliente): self
    {
        $this->fechaentregacliente = $fechaentregacliente;

        return $this;
    }

    public function getEstado(): ?string
    {
        return $this->estado;
    }

    public function setEstado(?string $estado): self
    {
        $this->estado = $estado;

        return $this;
    }

    public function getUsuarioentregocliente(): ?Usuario
    {
        return $this->usuarioentregocliente;
    }

    public function setUsuarioentregocliente(?Usuario $usuarioentregocliente): self
    {
        $this->usuarioentregocliente = $usuarioentregocliente;

        return $this;
    }

    public function getUsuarioenvio(): ?Usuario
    {
        return $this->usuarioenvio;
    }

    public function setUsuarioenvio(?Usuario $usuarioenvio): self
    {
        $this->usuarioenvio = $usuarioenvio;

        return $this;
    }

    public function getUsuariorecibio(): ?Usuario
    {
        return $this->usuariorecibio;
    }

    public function setUsuariorecibio(?Usuario $usuariorecibio): self
    {
        $this->usuariorecibio = $usuariorecibio;

        return $this;
    }


}
