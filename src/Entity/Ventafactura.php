<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * Ventafactura
 *
 * @ORM\Table(name="ventaFactura", indexes={@ORM\Index(name="fk_ventaFactura_clienteFacturaDatos1_idx", columns={"clienteFacturaDatos_idclienteFacturaDatos"}), @ORM\Index(name="fk_ventaFactura_venta1_idx", columns={"venta_idventa"})})
 * @ORM\Entity
 */
class Ventafactura
{
    /**
     * @var int
     *
     * @ORM\Column(name="idventaFactura", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idventafactura;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=false)
     */
    private $creacion;

    /**
     * @var string|null
     *
     * @ORM\Column(name="estado", type="string", length=250, nullable=true)
     */
    private $estado;

    /**
     * @var string|null
     *
     * @ORM\Column(name="montoVenta", type="decimal", precision=9, scale=2, nullable=true)
     */
    private $montoventa;

    /**
     * @var string|null
     *
     * @ORM\Column(name="rfcEmisor", type="string", length=45, nullable=true)
     */
    private $rfcemisor;

    /**
     * @var string|null
     *
     * @ORM\Column(name="rfcReceptor", type="string", length=45, nullable=true)
     */
    private $rfcreceptor;

    /**
     * @var string|null
     *
     * @ORM\Column(name="regimenFiscal", type="string", length=250, nullable=true)
     */
    private $regimenfiscal;

    /**
     * @var string|null
     *
     * @ORM\Column(name="usuCfdi", type="string", length=250, nullable=true)
     */
    private $usucfdi;

    /**
     * @var string|null
     *
     * @ORM\Column(name="nombreEmisor", type="string", length=500, nullable=true)
     */
    private $nombreemisor;

    /**
     * @var string|null
     *
     * @ORM\Column(name="nombreReceptor", type="string", length=500, nullable=true)
     */
    private $nombrereceptor;

    /**
     * @var string|null
     *
     * @ORM\Column(name="metodoPago", type="string", length=250, nullable=true)
     */
    private $metodopago;

    /**
     * @var string|null
     *
     * @ORM\Column(name="formaPago", type="string", length=250, nullable=true)
     */
    private $formapago;

    /**
     * @var string|null
     *
     * @ORM\Column(name="condicionesPago", type="string", length=250, nullable=true)
     */
    private $condicionespago;

    /**
     * @var string|null
     *
     * @ORM\Column(name="lugarExpedicion", type="string", length=45, nullable=true)
     */
    private $lugarexpedicion;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaComprobante", type="datetime", nullable=true)
     */
    private $fechacomprobante;

    /**
     * @var \DateTime|null
     *
     * @ORM\Column(name="fechaTimbrado", type="datetime", nullable=true)
     */
    private $fechatimbrado;

    /**
     * @var string|null
     *
     * @ORM\Column(name="nombreArchivoXml", type="string", length=250, nullable=true)
     */
    private $nombrearchivoxml;

    /**
     * @var string|null
     *
     * @ORM\Column(name="nombreArchivoPdf", type="string", length=250, nullable=true)
     */
    private $nombrearchivopdf;

    /**
     * @var string|null
     *
     * @ORM\Column(name="nombreZip", type="string", length=45, nullable=true)
     */
    private $nombrezip;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \Venta
     *
     * @ORM\ManyToOne(targetEntity="Venta")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="venta_idventa", referencedColumnName="idventa")
     * })
     */
    private $ventaIdventa;

    /**
     * @var \Clientefacturadatos
     *
     * @ORM\ManyToOne(targetEntity="Clientefacturadatos")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="clienteFacturaDatos_idclienteFacturaDatos", referencedColumnName="idclienteFacturaDatos")
     * })
     */
    private $clientefacturadatosIdclientefacturadatos;

    public function getIdventafactura(): ?int
    {
        return $this->idventafactura;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getEstado(): ?string
    {
        return $this->estado;
    }

    public function setEstado(?string $estado): self
    {
        $this->estado = $estado;

        return $this;
    }

    public function getMontoventa(): ?string
    {
        return $this->montoventa;
    }

    public function setMontoventa(?string $montoventa): self
    {
        $this->montoventa = $montoventa;

        return $this;
    }

    public function getRfcemisor(): ?string
    {
        return $this->rfcemisor;
    }

    public function setRfcemisor(?string $rfcemisor): self
    {
        $this->rfcemisor = $rfcemisor;

        return $this;
    }

    public function getRfcreceptor(): ?string
    {
        return $this->rfcreceptor;
    }

    public function setRfcreceptor(?string $rfcreceptor): self
    {
        $this->rfcreceptor = $rfcreceptor;

        return $this;
    }

    public function getRegimenfiscal(): ?string
    {
        return $this->regimenfiscal;
    }

    public function setRegimenfiscal(?string $regimenfiscal): self
    {
        $this->regimenfiscal = $regimenfiscal;

        return $this;
    }

    public function getUsucfdi(): ?string
    {
        return $this->usucfdi;
    }

    public function setUsucfdi(?string $usucfdi): self
    {
        $this->usucfdi = $usucfdi;

        return $this;
    }

    public function getNombreemisor(): ?string
    {
        return $this->nombreemisor;
    }

    public function setNombreemisor(?string $nombreemisor): self
    {
        $this->nombreemisor = $nombreemisor;

        return $this;
    }

    public function getNombrereceptor(): ?string
    {
        return $this->nombrereceptor;
    }

    public function setNombrereceptor(?string $nombrereceptor): self
    {
        $this->nombrereceptor = $nombrereceptor;

        return $this;
    }

    public function getMetodopago(): ?string
    {
        return $this->metodopago;
    }

    public function setMetodopago(?string $metodopago): self
    {
        $this->metodopago = $metodopago;

        return $this;
    }

    public function getFormapago(): ?string
    {
        return $this->formapago;
    }

    public function setFormapago(?string $formapago): self
    {
        $this->formapago = $formapago;

        return $this;
    }

    public function getCondicionespago(): ?string
    {
        return $this->condicionespago;
    }

    public function setCondicionespago(?string $condicionespago): self
    {
        $this->condicionespago = $condicionespago;

        return $this;
    }

    public function getLugarexpedicion(): ?string
    {
        return $this->lugarexpedicion;
    }

    public function setLugarexpedicion(?string $lugarexpedicion): self
    {
        $this->lugarexpedicion = $lugarexpedicion;

        return $this;
    }

    public function getFechacomprobante(): ?\DateTimeInterface
    {
        return $this->fechacomprobante;
    }

    public function setFechacomprobante(?\DateTimeInterface $fechacomprobante): self
    {
        $this->fechacomprobante = $fechacomprobante;

        return $this;
    }

    public function getFechatimbrado(): ?\DateTimeInterface
    {
        return $this->fechatimbrado;
    }

    public function setFechatimbrado(?\DateTimeInterface $fechatimbrado): self
    {
        $this->fechatimbrado = $fechatimbrado;

        return $this;
    }

    public function getNombrearchivoxml(): ?string
    {
        return $this->nombrearchivoxml;
    }

    public function setNombrearchivoxml(?string $nombrearchivoxml): self
    {
        $this->nombrearchivoxml = $nombrearchivoxml;

        return $this;
    }

    public function getNombrearchivopdf(): ?string
    {
        return $this->nombrearchivopdf;
    }

    public function setNombrearchivopdf(?string $nombrearchivopdf): self
    {
        $this->nombrearchivopdf = $nombrearchivopdf;

        return $this;
    }

    public function getNombrezip(): ?string
    {
        return $this->nombrezip;
    }

    public function setNombrezip(?string $nombrezip): self
    {
        $this->nombrezip = $nombrezip;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getVentaIdventa(): ?Venta
    {
        return $this->ventaIdventa;
    }

    public function setVentaIdventa(?Venta $ventaIdventa): self
    {
        $this->ventaIdventa = $ventaIdventa;

        return $this;
    }

    public function getClientefacturadatosIdclientefacturadatos(): ?Clientefacturadatos
    {
        return $this->clientefacturadatosIdclientefacturadatos;
    }

    public function setClientefacturadatosIdclientefacturadatos(?Clientefacturadatos $clientefacturadatosIdclientefacturadatos): self
    {
        $this->clientefacturadatosIdclientefacturadatos = $clientefacturadatosIdclientefacturadatos;

        return $this;
    }
    private ?UploadedFile $file = null;

    const SERVER_PATH_TO_IMAGE_FOLDER = "/uploads/zipFacturas/";

    public function setFile(?UploadedFile $file = null): void
    {
        $this->file = $file;
    }

    public function getFile(): ?UploadedFile
    {
        return $this->file;
    }

    public function upload(): void
    {
        // the file property can be empty if the field is not required
        if (null === $this->getFile()) {
            return;
        }

        $file = $this->getFile();

        $originalFilename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $safeFilename = transliterator_transliterate('Any-Latin; Latin-ASCII; [^A-Za-z0-9_] remove; Lower()', $originalFilename);
        $fileName = $safeFilename.'-'.uniqid().'.'.$file->guessExtension();

        // we use the original file name here but you should
        // sanitize it at least to avoid any security issues

        // move takes the target directory and target filename as params
        $carpeta = "uploads/zipFacturas/".$fileName;

        $this->getFile()->move($carpeta, $fileName);

        $this->setNombrezip($fileName);


        // set the path property to the filename where you've saved the file
        $this->filename = $this->getFile()->getClientOriginalName();

        // clean up the file property as you won't need it anymore
        $this->setFile(null);
    }

    public function __toString() {
        return $this->estado;
    }


}
