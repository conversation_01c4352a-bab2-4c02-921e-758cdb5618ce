<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Purchaseorder
 *
 * @ORM\Table(name="purchaseOrder", indexes={@ORM\Index(name="fk_PurchaseOrder_proveedor1_idx", columns={"proveedor_idproveedor"}), @ORM\Index(name="fk_PurchaseOrder_empresa1_idx", columns={"empresa_idempresa"}), @ORM\Index(name="fk_PurchaseOrder_usuario1_idx", columns={"usuario_idusuario"})})
 * @ORM\Entity
 */
class Purchaseorder
{
    /**
     * @var int
     *
     * @ORM\Column(name="idPurchaseOrder", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idpurchaseorder;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="creation", type="datetime", nullable=false)
     */
    private $creation;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="update", type="datetime", nullable=false)
     */
    private $update;

    /**
     * @var string
     *
     * @ORM\Column(name="stage", type="string", length=300, nullable=false)
     */
    private $stage;

    /**
     * @var string
     *
     * @ORM\Column(name="invoiceDocument", type="string", length=300, nullable=false)
     */
    private $invoicedocument;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    /**
     * @var \Empresa
     *
     * @ORM\ManyToOne(targetEntity="Empresa")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="empresa_idempresa", referencedColumnName="idempresa")
     * })
     */
    private $empresaIdempresa;

    /**
     * @var \Proveedor
     *
     * @ORM\ManyToOne(targetEntity="Proveedor")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="proveedor_idproveedor", referencedColumnName="idproveedor")
     * })
     */
    private $proveedorIdproveedor;

    public function getIdpurchaseorder(): ?int
    {
        return $this->idpurchaseorder;
    }

    public function getCreation(): ?\DateTimeInterface
    {
        return $this->creation;
    }

    public function setCreation(\DateTimeInterface $creation): self
    {
        $this->creation = $creation;

        return $this;
    }

    public function getUpdate(): ?\DateTimeInterface
    {
        return $this->update;
    }

    public function setUpdate(\DateTimeInterface $update): self
    {
        $this->update = $update;

        return $this;
    }

    public function getStage(): ?string
    {
        return $this->stage;
    }

    public function setStage(string $stage): self
    {
        $this->stage = $stage;

        return $this;
    }

    public function getInvoicedocument(): ?string
    {
        return $this->invoicedocument;
    }

    public function setInvoicedocument(string $invoicedocument): self
    {
        $this->invoicedocument = $invoicedocument;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }

    public function getEmpresaIdempresa(): ?Empresa
    {
        return $this->empresaIdempresa;
    }

    public function setEmpresaIdempresa(?Empresa $empresaIdempresa): self
    {
        $this->empresaIdempresa = $empresaIdempresa;

        return $this;
    }

    public function getProveedorIdproveedor(): ?Proveedor
    {
        return $this->proveedorIdproveedor;
    }

    public function setProveedorIdproveedor(?Proveedor $proveedorIdproveedor): self
    {
        $this->proveedorIdproveedor = $proveedorIdproveedor;

        return $this;
    }


}
