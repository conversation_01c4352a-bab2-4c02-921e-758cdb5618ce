<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Framematerial
 *
 * @ORM\Table(name="frameMaterial")
 * @ORM\Entity
 */
class Framematerial
{
    /**
     * @var int
     *
     * @ORM\Column(name="idframeMaterial", type="integer", nullable=false)
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idframematerial;

    /**
     * @var string
     *
     * @ORM\Column(name="material", type="string", length=45, nullable=false)
     */
    private $material;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    public function getIdframematerial(): ?int
    {
        return $this->idframematerial;
    }

    public function getMaterial(): ?string
    {
        return $this->material;
    }

    public function setMaterial(string $material): self
    {
        $this->material = $material;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }


}
