<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Productotag
 *
 * @ORM\Table(name="productoTag", indexes={@ORM\Index(name="fk_productoTag_producto1_idx", columns={"producto_idproducto"}), @ORM\Index(name="fk_productoTag_tag1_idx", columns={"tag_idtag"})})
 * @ORM\Entity
 */
class Productotag
{
    /**
     * @var int
     *
     * @ORM\Column(name="idproductoTag", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idproductotag;

    /**
     * @var \Producto
     *
     * @ORM\ManyToOne(targetEntity="Producto")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="producto_idproducto", referencedColumnName="idproducto")
     * })
     */
    private $productoIdproducto;

    /**
     * @var \Tag
     *
     * @ORM\ManyToOne(targetEntity="Tag")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="tag_idtag", referencedColumnName="idtag")
     * })
     */
    private $tagIdtag;

    public function getIdproductotag(): ?int
    {
        return $this->idproductotag;
    }

    public function getProductoIdproducto(): ?Producto
    {
        return $this->productoIdproducto;
    }

    public function setProductoIdproducto(?Producto $productoIdproducto): self
    {
        $this->productoIdproducto = $productoIdproducto;

        return $this;
    }

    public function getTagIdtag(): ?Tag
    {
        return $this->tagIdtag;
    }

    public function setTagIdtag(?Tag $tagIdtag): self
    {
        $this->tagIdtag = $tagIdtag;

        return $this;
    }


}
