<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Medida
 *
 * @ORM\Table(name="medida", indexes={@ORM\Index(name="fk_medida_unidadMedida1_idx", columns={"unidadMedida_idunidadMedida"})})
 * @ORM\Entity
 */
class Medida
{
    /**
     * @var int
     *
     * @ORM\Column(name="idmedida", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idmedida;

    /**
     * @var string
     *
     * @ORM\Column(name="nombre", type="string", length=100, nullable=false)
     */
    private $nombre;

    /**
     * @var string
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var \Unidadmedida
     *
     * @ORM\ManyToOne(targetEntity="Unidadmedida")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="unidadMedida_idunidadMedida", referencedColumnName="idunidadMedida")
     * })
     */
    private $unidadmedidaIdunidadmedida;

    public function getIdmedida(): ?int
    {
        return $this->idmedida;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(string $nombre): self
    {
        $this->nombre = $nombre;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getUnidadmedidaIdunidadmedida(): ?Unidadmedida
    {
        return $this->unidadmedidaIdunidadmedida;
    }

    public function setUnidadmedidaIdunidadmedida(?Unidadmedida $unidadmedidaIdunidadmedida): self
    {
        $this->unidadmedidaIdunidadmedida = $unidadmedidaIdunidadmedida;

        return $this;
    }
    
    public function __toString() {
        return $this->nombre;
    }



}
