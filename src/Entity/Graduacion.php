<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Graduacion
 *
 * @ORM\Table(name="graduacion", indexes={@ORM\Index(name="fk_graduacion_beneficiario1_idx", columns={"beneficiario_idbeneficiario"}), @ORM\Index(name="fk_graduacion_cliente1_idx", columns={"cliente_idcliente"}), @ORM\Index(name="fk_graduacion_disenoLente1_idx", columns={"disenoLente_iddisenoLente"}), @ORM\Index(name="fk_graduacion_material1_idx", columns={"material_idmaterial"}), @ORM\Index(name="fk_graduacion_tipoBisel1_idx", columns={"tipoBisel_idtipoBisel"}), @ORM\Index(name="fk_graduacion_tratamiento1_idx", columns={"tratamiento_idtratamiento"}), @ORM\Index(name="fk_graduacion_usuario1_idx", columns={"usuario_idusuario"})})
 * @ORM\Entity
 */
class Graduacion
{
    /**
     * @var int
     *
     * @ORM\Column(name="idgraduacion", type="integer", nullable=false, options={"unsigned"=true})
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="IDENTITY")
     */
    private $idgraduacion;

    /**
     * @var string|null
     *
     * @ORM\Column(name="GPEsferaOD", type="string", length=45, nullable=true)
     */
    private $gpesferaod;

    /**
     * @var string|null
     *
     * @ORM\Column(name="GPEsferaOI", type="string", length=45, nullable=true)
     */
    private $gpesferaoi;

    /**
     * @var string|null
     *
     * @ORM\Column(name="GPCilindroOD", type="string", length=45, nullable=true)
     */
    private $gpcilindrood;

    /**
     * @var string|null
     *
     * @ORM\Column(name="GPCilindroOI", type="string", length=45, nullable=true)
     */
    private $gpcilindrooi;

    /**
     * @var string|null
     *
     * @ORM\Column(name="GPEjeOD", type="string", length=45, nullable=true)
     */
    private $gpejeod;

    /**
     * @var string|null
     *
     * @ORM\Column(name="GPEjeOI", type="string", length=45, nullable=true)
     */
    private $gpejeoi;

    /**
     * @var string|null
     *
     * @ORM\Column(name="GPAddOD", type="string", length=45, nullable=true)
     */
    private $gpaddod;

    /**
     * @var string|null
     *
     * @ORM\Column(name="GPAddOI", type="string", length=45, nullable=true)
     */
    private $gpaddoi;

    /**
     * @var string|null
     *
     * @ORM\Column(name="GPAVlejosOD", type="string", length=45, nullable=true)
     */
    private $gpavlejosod;

    /**
     * @var string|null
     *
     * @ORM\Column(name="GPAVlejosOI", type="string", length=45, nullable=true)
     */
    private $gpavlejosoi;

    /**
     * @var string|null
     *
     * @ORM\Column(name="GPAVcercaOD", type="string", length=45, nullable=true)
     */
    private $gpavcercaod;

    /**
     * @var string|null
     *
     * @ORM\Column(name="GPAVcercaOI", type="string", length=45, nullable=true)
     */
    private $gpavcercaoi;

    /**
     * @var string|null
     *
     * @ORM\Column(name="avSRxLejosOD", type="string", length=45, nullable=true)
     */
    private $avsrxlejosod;

    /**
     * @var string|null
     *
     * @ORM\Column(name="avSRxLejosOI", type="string", length=45, nullable=true)
     */
    private $avsrxlejosoi;

    /**
     * @var string|null
     *
     * @ORM\Column(name="avSRxCercaOD", type="string", length=45, nullable=true)
     */
    private $avsrxcercaod;

    /**
     * @var string|null
     *
     * @ORM\Column(name="avSRxCercaOI", type="string", length=45, nullable=true)
     */
    private $avsrxcercaoi;

    /**
     * @var string|null
     *
     * @ORM\Column(name="avSRxCVOD", type="string", length=45, nullable=true)
     */
    private $avsrxcvod;

    /**
     * @var string|null
     *
     * @ORM\Column(name="avSRxCVOI", type="string", length=45, nullable=true)
     */
    private $avsrxcvoi;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="creacion", type="datetime", nullable=false)
     */
    private $creacion;

    /**
     * @var \DateTime
     *
     * @ORM\Column(name="actualizacion", type="datetime", nullable=false)
     */
    private $actualizacion;

    /**
     * @var string|null
     *
     * @ORM\Column(name="tipoBisel", type="text", length=16777215, nullable=true)
     */
    private $tipobisel;

    /**
     * @var string|null
     *
     * @ORM\Column(name="status", type="string", length=1, nullable=true, options={"default"="1","fixed"=true})
     */
    private $status = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="APObservaciones", type="text", length=16777215, nullable=true)
     */
    private $apobservaciones;

    /**
     * @var string|null
     *
     * @ORM\Column(name="anamnesis", type="text", length=16777215, nullable=true)
     */
    private $anamnesis;

    /**
     * @var string|null
     *
     * @ORM\Column(name="sugerencias", type="text", length=16777215, nullable=true)
     */
    private $sugerencias;

    /**
     * @var string|null
     *
     * @ORM\Column(name="cb", type="string", length=45, nullable=true)
     */
    private $cb;

    /**
     * @var string|null
     *
     * @ORM\Column(name="diam", type="string", length=45, nullable=true)
     */
    private $diam;

    /**
     * @var string|null
     *
     * @ORM\Column(name="tipoLenteContacto", type="string", length=2, nullable=true, options={"fixed"=true})
     */
    private $tipolentecontacto;

    /**
     * @var string|null
     *
     * @ORM\Column(name="tipoLente", type="string", length=2, nullable=true, options={"fixed"=true})
     */
    private $tipolente;

    /**
     * @var string|null
     *
     * @ORM\Column(name="anamnesisJson", type="text", length=65535, nullable=true)
     */
    private $anamnesisjson;

    /**
     * @var string
     *
     * @ORM\Column(name="isGlassesUser", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $isglassesuser = '1';

    /**
     * @var string
     *
     * @ORM\Column(name="hasGlassesNow", type="string", length=1, nullable=false, options={"default"="1","fixed"=true})
     */
    private $hasglassesnow = '1';

    /**
     * @var string|null
     *
     * @ORM\Column(name="esfODSF", type="string", length=45, nullable=true)
     */
    private $esfodsf;

    /**
     * @var string|null
     *
     * @ORM\Column(name="esfOISF", type="string", length=45, nullable=true)
     */
    private $esfoisf;

    /**
     * @var string|null
     *
     * @ORM\Column(name="cilODSF", type="string", length=45, nullable=true)
     */
    private $cilodsf;

    /**
     * @var string|null
     *
     * @ORM\Column(name="cilOISF", type="string", length=45, nullable=true)
     */
    private $ciloisf;

    /**
     * @var string|null
     *
     * @ORM\Column(name="ejeODSF", type="string", length=45, nullable=true)
     */
    private $ejeodsf;

    /**
     * @var string|null
     *
     * @ORM\Column(name="ejeOISF", type="string", length=45, nullable=true)
     */
    private $ejeoisf;

    /**
     * @var string|null
     *
     * @ORM\Column(name="avLODSF", type="string", length=45, nullable=true)
     */
    private $avlodsf;

    /**
     * @var string|null
     *
     * @ORM\Column(name="avLOISF", type="string", length=45, nullable=true)
     */
    private $avloisf;

    /**
     * @var string|null
     *
     * @ORM\Column(name="avCSAODSF", type="string", length=45, nullable=true)
     */
    private $avcsaodsf;

    /**
     * @var string|null
     *
     * @ORM\Column(name="avCSAOISF", type="string", length=45, nullable=true)
     */
    private $avcsaoisf;

    /**
     * @var string|null
     *
     * @ORM\Column(name="avCCAODSF", type="string", length=45, nullable=true)
     */
    private $avccaodsf;

    /**
     * @var string|null
     *
     * @ORM\Column(name="avCCAOISF", type="string", length=45, nullable=true)
     */
    private $avccaoisf;

    /**
     * @var string|null
     *
     * @ORM\Column(name="addSF", type="string", length=45, nullable=true)
     */
    private $addsf;

    /**
     * @var \Disenolente
     *
     * @ORM\ManyToOne(targetEntity="Disenolente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="disenoLente_iddisenoLente", referencedColumnName="iddisenoLente")
     * })
     */
    private $disenolenteIddisenolente;

    /**
     * @var \Tratamiento
     *
     * @ORM\ManyToOne(targetEntity="Tratamiento")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="tratamiento_idtratamiento", referencedColumnName="idtratamiento")
     * })
     */
    private $tratamientoIdtratamiento;

    /**
     * @var \Cliente
     *
     * @ORM\ManyToOne(targetEntity="Cliente")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="cliente_idcliente", referencedColumnName="idcliente")
     * })
     */
    private $clienteIdcliente;

    /**
     * @var \Usuario
     *
     * @ORM\ManyToOne(targetEntity="Usuario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="usuario_idusuario", referencedColumnName="idusuario")
     * })
     */
    private $usuarioIdusuario;

    /**
     * @var \Material
     *
     * @ORM\ManyToOne(targetEntity="Material")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="material_idmaterial", referencedColumnName="idmaterial")
     * })
     */
    private $materialIdmaterial;

    /**
     * @var \Tipobisel
     *
     * @ORM\ManyToOne(targetEntity="Tipobisel")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="tipoBisel_idtipoBisel", referencedColumnName="idtipoBisel")
     * })
     */
    private $tipobiselIdtipobisel;

    /**
     * @var \Beneficiario
     *
     * @ORM\ManyToOne(targetEntity="Beneficiario")
     * @ORM\JoinColumns({
     *   @ORM\JoinColumn(name="beneficiario_idbeneficiario", referencedColumnName="idbeneficiario")
     * })
     */
    private $beneficiarioIdbeneficiario;

    public function getIdgraduacion(): ?int
    {
        return $this->idgraduacion;
    }

    public function getGpesferaod(): ?string
    {
        return $this->gpesferaod;
    }

    public function setGpesferaod(?string $gpesferaod): self
    {
        $this->gpesferaod = $gpesferaod;

        return $this;
    }

    public function getGpesferaoi(): ?string
    {
        return $this->gpesferaoi;
    }

    public function setGpesferaoi(?string $gpesferaoi): self
    {
        $this->gpesferaoi = $gpesferaoi;

        return $this;
    }

    public function getGpcilindrood(): ?string
    {
        return $this->gpcilindrood;
    }

    public function setGpcilindrood(?string $gpcilindrood): self
    {
        $this->gpcilindrood = $gpcilindrood;

        return $this;
    }

    public function getGpcilindrooi(): ?string
    {
        return $this->gpcilindrooi;
    }

    public function setGpcilindrooi(?string $gpcilindrooi): self
    {
        $this->gpcilindrooi = $gpcilindrooi;

        return $this;
    }

    public function getGpejeod(): ?string
    {
        return $this->gpejeod;
    }

    public function setGpejeod(?string $gpejeod): self
    {
        $this->gpejeod = $gpejeod;

        return $this;
    }

    public function getGpejeoi(): ?string
    {
        return $this->gpejeoi;
    }

    public function setGpejeoi(?string $gpejeoi): self
    {
        $this->gpejeoi = $gpejeoi;

        return $this;
    }

    public function getGpaddod(): ?string
    {
        return $this->gpaddod;
    }

    public function setGpaddod(?string $gpaddod): self
    {
        $this->gpaddod = $gpaddod;

        return $this;
    }

    public function getGpaddoi(): ?string
    {
        return $this->gpaddoi;
    }

    public function setGpaddoi(?string $gpaddoi): self
    {
        $this->gpaddoi = $gpaddoi;

        return $this;
    }

    public function getGpavlejosod(): ?string
    {
        return $this->gpavlejosod;
    }

    public function setGpavlejosod(?string $gpavlejosod): self
    {
        $this->gpavlejosod = $gpavlejosod;

        return $this;
    }

    public function getGpavlejosoi(): ?string
    {
        return $this->gpavlejosoi;
    }

    public function setGpavlejosoi(?string $gpavlejosoi): self
    {
        $this->gpavlejosoi = $gpavlejosoi;

        return $this;
    }

    public function getGpavcercaod(): ?string
    {
        return $this->gpavcercaod;
    }

    public function setGpavcercaod(?string $gpavcercaod): self
    {
        $this->gpavcercaod = $gpavcercaod;

        return $this;
    }

    public function getGpavcercaoi(): ?string
    {
        return $this->gpavcercaoi;
    }

    public function setGpavcercaoi(?string $gpavcercaoi): self
    {
        $this->gpavcercaoi = $gpavcercaoi;

        return $this;
    }

    public function getAvsrxlejosod(): ?string
    {
        return $this->avsrxlejosod;
    }

    public function setAvsrxlejosod(?string $avsrxlejosod): self
    {
        $this->avsrxlejosod = $avsrxlejosod;

        return $this;
    }

    public function getAvsrxlejosoi(): ?string
    {
        return $this->avsrxlejosoi;
    }

    public function setAvsrxlejosoi(?string $avsrxlejosoi): self
    {
        $this->avsrxlejosoi = $avsrxlejosoi;

        return $this;
    }

    public function getAvsrxcercaod(): ?string
    {
        return $this->avsrxcercaod;
    }

    public function setAvsrxcercaod(?string $avsrxcercaod): self
    {
        $this->avsrxcercaod = $avsrxcercaod;

        return $this;
    }

    public function getAvsrxcercaoi(): ?string
    {
        return $this->avsrxcercaoi;
    }

    public function setAvsrxcercaoi(?string $avsrxcercaoi): self
    {
        $this->avsrxcercaoi = $avsrxcercaoi;

        return $this;
    }

    public function getAvsrxcvod(): ?string
    {
        return $this->avsrxcvod;
    }

    public function setAvsrxcvod(?string $avsrxcvod): self
    {
        $this->avsrxcvod = $avsrxcvod;

        return $this;
    }

    public function getAvsrxcvoi(): ?string
    {
        return $this->avsrxcvoi;
    }

    public function setAvsrxcvoi(?string $avsrxcvoi): self
    {
        $this->avsrxcvoi = $avsrxcvoi;

        return $this;
    }

    public function getCreacion(): ?\DateTimeInterface
    {
        return $this->creacion;
    }

    public function setCreacion(\DateTimeInterface $creacion): self
    {
        $this->creacion = $creacion;

        return $this;
    }

    public function getActualizacion(): ?\DateTimeInterface
    {
        return $this->actualizacion;
    }

    public function setActualizacion(\DateTimeInterface $actualizacion): self
    {
        $this->actualizacion = $actualizacion;

        return $this;
    }

    public function getTipobisel(): ?string
    {
        return $this->tipobisel;
    }

    public function setTipobisel(?string $tipobisel): self
    {
        $this->tipobisel = $tipobisel;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getApobservaciones(): ?string
    {
        return $this->apobservaciones;
    }

    public function setApobservaciones(?string $apobservaciones): self
    {
        $this->apobservaciones = $apobservaciones;

        return $this;
    }

    public function getAnamnesis(): ?string
    {
        return $this->anamnesis;
    }

    public function setAnamnesis(?string $anamnesis): self
    {
        $this->anamnesis = $anamnesis;

        return $this;
    }

    public function getSugerencias(): ?string
    {
        return $this->sugerencias;
    }

    public function setSugerencias(?string $sugerencias): self
    {
        $this->sugerencias = $sugerencias;

        return $this;
    }

    public function getCb(): ?string
    {
        return $this->cb;
    }

    public function setCb(?string $cb): self
    {
        $this->cb = $cb;

        return $this;
    }

    public function getDiam(): ?string
    {
        return $this->diam;
    }

    public function setDiam(?string $diam): self
    {
        $this->diam = $diam;

        return $this;
    }

    public function getTipolentecontacto(): ?string
    {
        return $this->tipolentecontacto;
    }

    public function setTipolentecontacto(?string $tipolentecontacto): self
    {
        $this->tipolentecontacto = $tipolentecontacto;

        return $this;
    }

    public function getTipolente(): ?string
    {
        return $this->tipolente;
    }

    public function setTipolente(?string $tipolente): self
    {
        $this->tipolente = $tipolente;

        return $this;
    }

    public function getAnamnesisjson(): ?string
    {
        return $this->anamnesisjson;
    }

    public function setAnamnesisjson(?string $anamnesisjson): self
    {
        $this->anamnesisjson = $anamnesisjson;

        return $this;
    }

    public function getIsglassesuser(): ?string
    {
        return $this->isglassesuser;
    }

    public function setIsglassesuser(string $isglassesuser): self
    {
        $this->isglassesuser = $isglassesuser;

        return $this;
    }

    public function getHasglassesnow(): ?string
    {
        return $this->hasglassesnow;
    }

    public function setHasglassesnow(string $hasglassesnow): self
    {
        $this->hasglassesnow = $hasglassesnow;

        return $this;
    }

    public function getEsfodsf(): ?string
    {
        return $this->esfodsf;
    }

    public function setEsfodsf(?string $esfodsf): self
    {
        $this->esfodsf = $esfodsf;

        return $this;
    }

    public function getEsfoisf(): ?string
    {
        return $this->esfoisf;
    }

    public function setEsfoisf(?string $esfoisf): self
    {
        $this->esfoisf = $esfoisf;

        return $this;
    }

    public function getCilodsf(): ?string
    {
        return $this->cilodsf;
    }

    public function setCilodsf(?string $cilodsf): self
    {
        $this->cilodsf = $cilodsf;

        return $this;
    }

    public function getCiloisf(): ?string
    {
        return $this->ciloisf;
    }

    public function setCiloisf(?string $ciloisf): self
    {
        $this->ciloisf = $ciloisf;

        return $this;
    }

    public function getEjeodsf(): ?string
    {
        return $this->ejeodsf;
    }

    public function setEjeodsf(?string $ejeodsf): self
    {
        $this->ejeodsf = $ejeodsf;

        return $this;
    }

    public function getEjeoisf(): ?string
    {
        return $this->ejeoisf;
    }

    public function setEjeoisf(?string $ejeoisf): self
    {
        $this->ejeoisf = $ejeoisf;

        return $this;
    }

    public function getAvlodsf(): ?string
    {
        return $this->avlodsf;
    }

    public function setAvlodsf(?string $avlodsf): self
    {
        $this->avlodsf = $avlodsf;

        return $this;
    }

    public function getAvloisf(): ?string
    {
        return $this->avloisf;
    }

    public function setAvloisf(?string $avloisf): self
    {
        $this->avloisf = $avloisf;

        return $this;
    }

    public function getAvcsaodsf(): ?string
    {
        return $this->avcsaodsf;
    }

    public function setAvcsaodsf(?string $avcsaodsf): self
    {
        $this->avcsaodsf = $avcsaodsf;

        return $this;
    }

    public function getAvcsaoisf(): ?string
    {
        return $this->avcsaoisf;
    }

    public function setAvcsaoisf(?string $avcsaoisf): self
    {
        $this->avcsaoisf = $avcsaoisf;

        return $this;
    }

    public function getAvccaodsf(): ?string
    {
        return $this->avccaodsf;
    }

    public function setAvccaodsf(?string $avccaodsf): self
    {
        $this->avccaodsf = $avccaodsf;

        return $this;
    }

    public function getAvccaoisf(): ?string
    {
        return $this->avccaoisf;
    }

    public function setAvccaoisf(?string $avccaoisf): self
    {
        $this->avccaoisf = $avccaoisf;

        return $this;
    }

    public function getAddsf(): ?string
    {
        return $this->addsf;
    }

    public function setAddsf(?string $addsf): self
    {
        $this->addsf = $addsf;

        return $this;
    }

    public function getDisenolenteIddisenolente(): ?Disenolente
    {
        return $this->disenolenteIddisenolente;
    }

    public function setDisenolenteIddisenolente(?Disenolente $disenolenteIddisenolente): self
    {
        $this->disenolenteIddisenolente = $disenolenteIddisenolente;

        return $this;
    }

    public function getTratamientoIdtratamiento(): ?Tratamiento
    {
        return $this->tratamientoIdtratamiento;
    }

    public function setTratamientoIdtratamiento(?Tratamiento $tratamientoIdtratamiento): self
    {
        $this->tratamientoIdtratamiento = $tratamientoIdtratamiento;

        return $this;
    }

    public function getClienteIdcliente(): ?Cliente
    {
        return $this->clienteIdcliente;
    }

    public function setClienteIdcliente(?Cliente $clienteIdcliente): self
    {
        $this->clienteIdcliente = $clienteIdcliente;

        return $this;
    }

    public function getUsuarioIdusuario(): ?Usuario
    {
        return $this->usuarioIdusuario;
    }

    public function setUsuarioIdusuario(?Usuario $usuarioIdusuario): self
    {
        $this->usuarioIdusuario = $usuarioIdusuario;

        return $this;
    }

    public function getMaterialIdmaterial(): ?Material
    {
        return $this->materialIdmaterial;
    }

    public function setMaterialIdmaterial(?Material $materialIdmaterial): self
    {
        $this->materialIdmaterial = $materialIdmaterial;

        return $this;
    }

    public function getTipobiselIdtipobisel(): ?Tipobisel
    {
        return $this->tipobiselIdtipobisel;
    }

    public function setTipobiselIdtipobisel(?Tipobisel $tipobiselIdtipobisel): self
    {
        $this->tipobiselIdtipobisel = $tipobiselIdtipobisel;

        return $this;
    }

    public function getBeneficiarioIdbeneficiario(): ?Beneficiario
    {
        return $this->beneficiarioIdbeneficiario;
    }

    public function setBeneficiarioIdbeneficiario(?Beneficiario $beneficiarioIdbeneficiario): self
    {
        $this->beneficiarioIdbeneficiario = $beneficiarioIdbeneficiario;

        return $this;
    }

    /**
     * Retorna el nombre completo del cliente asociado.
     *
     * @return string
     */
    public function getNombreCompleto(): string
    {
        return $this->clienteIdcliente ? $this->clienteIdcliente->getFullName() : '';
    }


}
