<?php

namespace App\EventListener;

use App\Entity\Productostranspasoalmacen;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\Event\LifecycleEventArgs;

class ProductoTranspasoAlmacenListener
{
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function postLoad(LifecycleEventArgs $args)
    {
        $entity = $args->getEntity();
        
        if (!$entity instanceof Productostranspasoalmacen) {
            return;
        }
    
        $transpasoAlmacen = $entity->getTranspasoalmacenIdtranspasoalmacen();
        if ($transpasoAlmacen === null) {
            return;
        }
    
        $idtranspasoalmacen = $transpasoAlmacen->getIdtranspasoalmacen();
    
        $aceptada = $this->entityManager->getRepository(Productostranspasoalmacen::class)
                                         ->obtenerAceptadaPorProducto($idtranspasoalmacen);
    
        $entity->setAceptada($aceptada);
    }
    
    
    
}
