<?php
// src/EventListener/MenuBuilderListener.php

namespace App\EventListener;

use Sonata\AdminBundle\Event\ConfigureMenuEvent;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
final class MenuBuilderListener
{
    var $token=null;
   /* public function __construct(TokenInterface $token) {
        $this->token=$token;
    }*/
    public function addMenuItems(ConfigureMenuEvent $event): void
    {
        $menu = $event->getMenu();
       /* $user = $this->token->gett();
        if($user->hasRole("ROLE_VENDEDOR")){
            $menu->removeChild('admin.producto');
        }*/
    }
}