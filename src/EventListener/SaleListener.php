<?php

namespace App\EventListener;
use App\Entity\Venta;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\Event\LifecycleEventArgs;
use App\Service\SalesService;

class SaleListener
{

    private $saleService;

    public function __construct(SalesService $saleService)
    {
        $this->saleService = $saleService;
    }
    public function postLoad(Venta $sale, LifecycleEventArgs $event)
    {

        $sale->setStorableProducts($this->saleService->getSoldProductos($sale->getIdventa(),1));
        $sale->setServiceProducts($this->saleService->getSoldProductos($sale->getIdventa(),2));
        $sale->setLiquidatedDate($this->saleService->getLastPaymentDate($sale->getIdventa()));

    }
}