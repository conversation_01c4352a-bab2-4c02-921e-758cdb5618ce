<?php

namespace App\EventListener;

use Lexik\Bundle\JWTAuthenticationBundle\Event\AuthenticationSuccessEvent;
use App\Entity\Cliente;

class AuthenticationSuccessHandler
{
    public function onAuthenticationSuccess(AuthenticationSuccessEvent $event): void
    {
        $data = $event->getData();
        $user = $event->getUser();

        if (!$user instanceof Cliente) {
            return;
        }

        $data['cliente'] = [
            'id' => $user->getIdcliente(),
            'nombre' => $user->getNombre(),
            'apellidos' => $user->getApellidopaterno().' '.$user->getApellidomaterno(),
            'telefono' => $user->getTelefono(),
            'email' => $user->getEmail(),
            'num_empleado' => $user->getNumeroempleado()
        ];

        $event->setData($data);
    }
}
