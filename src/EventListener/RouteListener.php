<?php

// src/EventListener/RouteListener.php

namespace App\EventListener;

use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;


class RouteListener
{
    /** @var  TokenStorageInterface */
    protected $tokenStorage;

    protected $user;

    private $urlGenerator;

    public function __construct(UrlGeneratorInterface $urlGenerator, TokenStorageInterface $tokenStorage)
    {
        $this->tokenStorage = $tokenStorage;
        $this->urlGenerator = $urlGenerator;
    }

    public function onKernelRequest(RequestEvent $event)
    {
        $request = $event->getRequest();

        // Check if the requested route matches the criteria
        $routeName = $request->attributes->get('_route');

        

        if ($token = $this->tokenStorage->getToken()) {
            // Check if the token exists and is not null
            $this->user = $token->getUser();

            if ($this->user instanceof AdminUserInterface) {
                // Print the user to check what we are getting


                $entryPoints = $this->user->getEntryPoints();

                // If entry points are defined and the requested route is not in the entry points, redirect
                if ($entryPoints !== null && in_array($routeName, $entryPoints)) {
                    // Redirect only if the user is logged in and the requested route is not in their entry points
                    $event->setResponse(new RedirectResponse($this->urlGenerator->generate('app_dashboard_flujo_expediente')));
                    return;
                }
            }
        }

        // If conditions are not met or the user is anonymous, let the user continue
    }
}
