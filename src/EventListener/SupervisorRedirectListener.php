<?php

namespace App\EventListener;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Routing\RouterInterface;

class SupervisorRedirectListener
{
    private $security;
    private $router;

    public function __construct(Security $security, RouterInterface $router)
    {
        $this->security = $security;
        $this->router = $router;
    }

    public function onKernelRequest(RequestEvent $event)
    {
        // Solo procesar la petición principal
        if (!$event->isMasterRequest()) {
            return;
        }

        $request = $event->getRequest();
        $user = $this->security->getUser();

        // Verificar si el usuario está autenticado y tiene rol SUPERVISOR
        if (!$user || !$this->security->isGranted('ROLE_SUPERVISOR')) {
            return;
        }

        // Obtener la ruta actual
        $currentRoute = $request->attributes->get('_route');
        $currentPath = $request->getPathInfo();

        // Rutas permitidas para SUPERVISOR
        $allowedRoutes = [
            'app_oficial',                        // La ruta del dashboard
            'admin_logout',                       // Permitir logout
            'almacen-obtener-clase',             // Para AJAX del dashboard
            'almacen-obtener-sucursal-supervisor', // Nueva ruta para sucursales de supervisores
            'get-ingreso-comparison',            // Nueva ruta para comparación de tendencias
        ];

        // Rutas permitidas por path (para AJAX y assets)
        $allowedPaths = [
            '/reporte/dashboard',
            '/admin/logout',
            '/ajax/clases',
            '/cliente-api/',                    // Para las llamadas AJAX del dashboard
            '/filter/obtener-sucursal-supervisor', // Nueva ruta para filtros de supervisores
        ];

        // Si ya está en una ruta permitida, no hacer nada
        if (in_array($currentRoute, $allowedRoutes)) {
            return;
        }

        // Verificar paths permitidos
        foreach ($allowedPaths as $allowedPath) {
            if (strpos($currentPath, $allowedPath) === 0) {
                return;
            }
        }

        // Si no está en una ruta permitida, redirigir al dashboard
        $dashboardUrl = $this->router->generate('app_oficial');
        $response = new RedirectResponse($dashboardUrl);
        $event->setResponse($response);
    }
}
