<?php

namespace App\Helper;

use App\Enum\ResponseCode;
use App\Enum\TraspasoErrorCode;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Helper para crear respuestas estandarizadas de la API
 */
class ApiResponseHelper
{
    /**
     * Crea una respuesta de éxito
     */
    public static function success(
        $data = null, 
        string $message = null, 
        string $code = ResponseCode::SUCCESS
    ): JsonResponse {
        $response = [
            'success' => true,
            'code' => $code,
            'message' => $message ?? ResponseCode::getDefaultMessage($code),
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        return new JsonResponse($response, ResponseCode::getHttpCode($code));
    }

    /**
     * Crea una respuesta de error
     */
    public static function error(
        string $errorCode, 
        string $message = null, 
        $details = null,
        string $responseCode = ResponseCode::BAD_REQUEST
    ): JsonResponse {
        $response = [
            'success' => false,
            'code' => $responseCode,
            'error' => [
                'code' => $errorCode,
                'message' => $message ?? TraspasoErrorCode::getMessage($errorCode),
            ]
        ];

        if ($details !== null) {
            $response['error']['details'] = $details;
        }

        return new JsonResponse($response, ResponseCode::getHttpCode($responseCode));
    }

    /**
     * Crea una respuesta de error de validación
     */
    public static function validationError(array $errors): JsonResponse
    {
        return new JsonResponse([
            'success' => false,
            'code' => ResponseCode::VALIDATION_ERROR,
            'message' => ResponseCode::getDefaultMessage(ResponseCode::VALIDATION_ERROR),
            'errors' => $errors
        ], ResponseCode::getHttpCode(ResponseCode::VALIDATION_ERROR));
    }

    /**
     * Crea una respuesta para errores de traspaso específicos
     */
    public static function traspasoError(
        string $errorCode, 
        string $customMessage = null, 
        array $details = null
    ): JsonResponse {
        $response = [
            'success' => false,
            'code' => ResponseCode::BAD_REQUEST,
            'error' => [
                'code' => $errorCode,
                'message' => $customMessage ?? TraspasoErrorCode::getMessage($errorCode),
            ]
        ];

        if ($details !== null) {
            $response['error']['details'] = $details;
        }

        return new JsonResponse($response, 400);
    }

    /**
     * Crea una respuesta para múltiples errores de productos
     */
    public static function multipleProductErrors(
        array $errors, 
        array $successfulCodes = [], 
        string $message = 'Algunos productos presentaron errores'
    ): JsonResponse {
        $response = [
            'success' => count($errors) === 0,
            'code' => count($errors) > 0 ? ResponseCode::VALIDATION_ERROR : ResponseCode::SUCCESS,
            'message' => $message,
            'data' => [
                'errors' => $errors,
                'successful_codes' => $successfulCodes,
                'total_errors' => count($errors),
                'total_successful' => count($successfulCodes)
            ]
        ];

        $httpCode = count($errors) > 0 ? 422 : 200;
        return new JsonResponse($response, $httpCode);
    }

    /**
     * Crea una respuesta para operaciones de traspaso
     */
    public static function traspasoResponse(
        bool $success,
        string $message,
        array $productosNoEncontrados = [],
        array $productosApartados = [],
        $additionalData = null
    ): JsonResponse {
        $response = [
            'success' => $success,
            'message' => $message,
            'data' => [
                'productos_no_encontrados' => $productosNoEncontrados,
                'productos_apartados' => $productosApartados,
                'total_errores' => count($productosNoEncontrados) + count($productosApartados)
            ]
        ];

        if ($additionalData !== null) {
            $response['data'] = array_merge($response['data'], $additionalData);
        }

        $httpCode = $success ? 200 : 400;
        return new JsonResponse($response, $httpCode);
    }

    /**
     * Crea una respuesta para errores de Google Sheets
     */
    public static function googleSheetsError(
        string $errorCode = TraspasoErrorCode::GOOGLE_SHEETS_API_ERROR,
        string $customMessage = null,
        array $details = null
    ): JsonResponse {
        return self::error(
            $errorCode,
            $customMessage,
            $details,
            ResponseCode::EXTERNAL_API_ERROR
        );
    }
}
