<?php

namespace App\DTO\PhoneVerification;

/**
 * DTO for phone verification request
 */
class PhoneVerificationRequestDTO
{
    /**
     * @var string
     */
    private $phoneNumber;

    /**
     * @return string
     */
    public function getPhoneNumber(): string
    {
        return $this->phoneNumber;
    }

    /**
     * @param string $phoneNumber
     * @return self
     */
    public function setPhoneNumber(string $phoneNumber): self
    {
        $this->phoneNumber = $phoneNumber;
        return $this;
    }

    /**
     * Create from request data
     *
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        $dto = new self();
        $dto->setPhoneNumber($data['phoneNumber'] ?? '');
        return $dto;
    }
}