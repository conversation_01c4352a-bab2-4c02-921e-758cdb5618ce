<?php

namespace App\DTO\PhoneVerification;

/**
 * DTO for phone verification response
 */
class PhoneVerificationResponseDTO implements \JsonSerializable
{
    /**
     * @var bool
     */
    private $success;

    /**
     * @var string
     */
    private $message;

    /**
     * @var string|null
     */
    private $status;

    /**
     * @var string|null
     */
    private $expiresAt;

    /**
     * @return bool
     */
    public function isSuccess(): bool
    {
        return $this->success;
    }

    /**
     * @param bool $success
     * @return self
     */
    public function setSuccess(bool $success): self
    {
        $this->success = $success;
        return $this;
    }

    /**
     * @return string
     */
    public function getMessage(): string
    {
        return $this->message;
    }

    /**
     * @param string $message
     * @return self
     */
    public function setMessage(string $message): self
    {
        $this->message = $message;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getStatus(): ?string
    {
        return $this->status;
    }

    /**
     * @param string|null $status
     * @return self
     */
    public function setStatus(?string $status): self
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getExpiresAt(): ?string
    {
        return $this->expiresAt;
    }

    /**
     * @param string|null $expiresAt
     * @return self
     */
    public function setExpiresAt(?string $expiresAt): self
    {
        $this->expiresAt = $expiresAt;
        return $this;
    }

    /**
     * Create a success response
     *
     * @param string $message
     * @param string|null $status
     * @param string|null $expiresAt
     * @return self
     */
    public static function createSuccess(string $message, ?string $status = null, ?string $expiresAt = null): self
    {
        $dto = new self();
        $dto->setSuccess(true);
        $dto->setMessage($message);
        $dto->setStatus($status);
        $dto->setExpiresAt($expiresAt);
        return $dto;
    }

    /**
     * Create an error response
     *
     * @param string $message
     * @param string|null $status
     * @return self
     */
    public static function createError(string $message, ?string $status = null): self
    {
        $dto = new self();
        $dto->setSuccess(false);
        $dto->setMessage($message);
        $dto->setStatus($status);
        return $dto;
    }

    /**
     * @return array
     */
    public function jsonSerialize(): array
    {
        $data = [
            'success' => $this->success,
            'message' => $this->message,
        ];

        if ($this->status !== null) {
            $data['status'] = $this->status;
        }

        if ($this->expiresAt !== null) {
            $data['expiresAt'] = $this->expiresAt;
        }

        return $data;
    }
}