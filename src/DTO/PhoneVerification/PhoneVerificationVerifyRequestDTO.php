<?php

namespace App\DTO\PhoneVerification;

/**
 * DTO for phone verification verify request
 */
class PhoneVerificationVerifyRequestDTO
{
    /**
     * @var string
     */
    private $phoneNumber;

    /**
     * @var string
     */
    private $code;

    /**
     * @return string
     */
    public function getPhoneNumber(): string
    {
        return $this->phoneNumber;
    }

    /**
     * @param string $phoneNumber
     * @return self
     */
    public function setPhoneNumber(string $phoneNumber): self
    {
        $this->phoneNumber = $phoneNumber;
        return $this;
    }

    /**
     * @return string
     */
    public function getCode(): string
    {
        return $this->code;
    }

    /**
     * @param string $code
     * @return self
     */
    public function setCode(string $code): self
    {
        $this->code = $code;
        return $this;
    }

    /**
     * Create from request data
     *
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        $dto = new self();
        $dto->setPhoneNumber($data['phoneNumber'] ?? '');
        $dto->setCode($data['code'] ?? '');
        return $dto;
    }
}