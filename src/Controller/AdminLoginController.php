<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use App\Form\AdminLoginForm;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Encoder\UserPasswordEncoderInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Scheb\TwoFactorBundle\Security\TwoFactor\QrCode\QrCodeGenerator;

use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;

use Symfony\Component\Mime\Email;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\Mime\Address;

final class AdminLoginController extends Controller
{
    private $mailer;

    /**
     * @var AuthenticationUtils
     */
    private $authenticationUtils;

    public function __construct(AuthenticationUtils $authenticationUtils, MailerInterface $mailer)
    {
        $this->authenticationUtils = $authenticationUtils;
        $this->mailer = $mailer;
    }

    /**
     * @Route("/admin/login", name="admin_login")
     */
    public function loginAction(): Response
    {
        $em = $this->getDoctrine()->getManager();

        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery('SELECT usr.nombre, usr.apellidopaterno, usr.apellidomaterno, usr.email  
         FROM App\Entity\Usuario usr
         where usr.status=:status')
            ->setParameters(['status' => '1']);

        $results = $query->getResult();

        $results = [
            [
                'nombre' => 'Luis Fernando',
                'apellidoPaterno' => 'Fernandez',
                'apellidoMaterno' => 'Ordaz',
                'email' => '<EMAIL>',
            ],
            [
                'nombre' => 'María Elena',
                'apellidoPaterno' => 'Cruz',
                'apellidoMaterno' => 'Elías',
                'email' => '<EMAIL>',
            ],
            [
                'nombre' => 'Karla',
                'apellidoPaterno' => 'García',
                'apellidoMaterno' => 'Hernandez',
                'email' => '<EMAIL>',
            ]
        ];


        $form = $this->createForm(AdminLoginForm::class, [
            'email' => $this->authenticationUtils->getLastUsername()
        ]);

        return $this->render('security/login.html.twig', [
            'last_username' => $this->authenticationUtils->getLastUsername(),
            'form' => $form->createView(),
            'error' => $this->authenticationUtils->getLastAuthenticationError(),
            'birthdays' => $results,
        ]);
    }

    /**
     * @Route("/admin/logout", name="admin_logout")
     */
    public function logoutAction(): void
    {
        // Left empty intentionally because this will be handled by Symfony.
    }
    /**
     * @Route("/admin/cambiar-contrasena/{idusuario}", name="admin-cambiar-contrasena")
     */
    public function cambiarContrasenaAction($idusuario = "", UserPasswordEncoderInterface $encoder, Request $request, ValidatorInterface $validator)
    {

        $em = $this->getDoctrine()->getManager();
        $exito = false;
        $msj = "";
        $errors = [];

        try {

            $Usuario = $em->getRepository('App\Entity\Usuario')->findOneBy(array('idusuario' => $idusuario));

            if ($Usuario) {
                $pass = $request->get("pass");

                $constraints = new Assert\Collection([
                    'pass' => [
                        new Assert\NotBlank(['message' => 'La contraseña no puede estar vacía.']),
                        new Assert\Length([
                            'min' => 8,
                            'minMessage' => 'La contraseña debe tener al menos {{ limit }} caracteres.'
                        ]),
                        new Assert\Regex([
                            'pattern' => '/[A-Z]/',
                            'message' => 'La contraseña debe contener al menos una letra mayúscula.'
                        ]),
                        new Assert\Regex([
                            'pattern' => '/[a-z]/',
                            'message' => 'La contraseña debe contener al menos una letra minúscula.'
                        ]),
                        new Assert\Regex([
                            'pattern' => '/\d/',
                            'message' => 'La contraseña debe contener al menos un número.'
                        ]),
                        new Assert\Regex([
                            'pattern' => '/[^a-zA-Z\d]/',
                            'message' => 'La contraseña debe contener al menos un carácter especial.'
                        ]),
                    ],
                ]);

                $violations = $validator->validate(['pass' => $pass], $constraints);

                if (count($violations) > 0) {

                    foreach ($violations as $violation) {
                        $errors[] = $violation->getMessage();
                    }

                    $msj = "La contraseña no es válida";

                } else {
                    $encoded = $encoder->encodePassword($Usuario, $pass);
                    $Usuario->setContrasena($encoded);
                    $em->persist($Usuario);
                    $em->flush();
                    $exito = true;

                    $this->sendEmailPasswordChange($Usuario, $pass);
                }

            }

        } catch (\Exception $e) {
            $msj = $e->getMessage();
            ;

        }



        return new JsonResponse(['exito' => $exito, 'msj' => $msj, 'errors' => $errors]);





    }

    private function sendEmailPasswordChange($object, $password)
    {

        $name = $object->getNombreVendedor();

        $websiteUrl = "sistema.grupooptimo.com.mx";

        $Enterprise = $object->getSucursalIdsucursal()->getEmpresaIdempresa();
        $logo = $Enterprise->getLogoimagen();
        $User = $this->getUser();
        $fullName = $User->getNombreVendedor();

        $email = (new TemplatedEmail())
            ->from('<EMAIL>')
            ->to(new Address($object->getEmail()))
            ->subject("Cambio de contraseña exitoso")
            ->htmlTemplate('emails/emailLoginInformation.html.twig')
            ->context([
                'emailLoginInformation' => $object->getEmail(),
                'password' => $password,
                'websiteUrl' => $websiteUrl,
                'logo' => $logo,
                'name' => $name,
                'fullName' => $fullName,
                'passwordReset' => "1",
            ]);

        $this->mailer->send($email);
    }



}
