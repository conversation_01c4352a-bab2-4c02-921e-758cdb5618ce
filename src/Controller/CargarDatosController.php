<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Annotation\Route;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use App\Entity\Cliente;
use App\Entity\Beneficiario;


class CargarDatosController extends AbstractController
{

    /**
     * @Route("/subir-excel", name="subir-excel")
     */
    public function subirExcel()
    {
      $carpeta_front=$this->getParameter('uploads_front');
      return $this->render('cargar_datos/subir-excel.html.twig', ['uploads_front' => $carpeta_front]);
    }
    /**
     * @Route("/subir-documento", name="subir-documento")
     */
    public function subirDocumento()
    {

      //  $exito=true;
    $response="";
    $valid = false;
    $msj="";
    $documentName="";
    $log="";
  //  $request->query->get('page');
 //$request->request->get('page');
  //echo "hola ".$request->request->get('typeDocument');
    //$typeDocument= $request->request->get('typeDocument');
    //echo "tipo ".$typeDocument;

    //para ver en que carpeta se guardarán
    $documentName="";
    $targetPath ="";
    $carpeta="";
    $carpeta=$this->getParameter('uploads');
    //este id es el que viene de la vista y sirve para mostar el mensaje de eerror en el documento que sea necesario
    $idVista="";
    $mes;
    $anio;
    $tipoDocumento="";

    $clientesNuevos=0;
    $beneficiariosNuevos=0;
    $beneficiariosActualizados=0;
    $beneficiariosOmitidos=0;
    $beneficiariosenBlanco=0;
        $responseProcesarDocumento=null;

    try{
      //var_dump($_FILES);
      //var_dump($_POST);

      if(isset($_FILES['file1']['name'])){
        $idVista=$_POST['id'];
        $nombreDocumento="";


        $name=$_FILES['file1']['name'];

        $nombreDocumento=explode(".",$_FILES['file1']['name']);

        $mes=$nombreDocumento[1];
        $anio=$nombreDocumento[0];
        //var_dump($nombreDocumento);
        array_pop($nombreDocumento);
      //  var_dump($nombreDocumento);
        $nombreDocumento=implode(".",$nombreDocumento);
        //sacamos el año



        //$nombreDocumento=$nombreDocumento[0];
        $tmpName  = $_FILES['file1']['tmp_name'];
        $error    = $_FILES['file1']['error'];
        $size     = $_FILES['file1']['size'];
        $ext      = strtolower(pathinfo($name, PATHINFO_EXTENSION));


        if($ext=="xls" || $ext=="xlsx"){
          $documentName=md5(uniqid()).'.'.$ext;

            $targetPath =  $carpeta.DIRECTORY_SEPARATOR.$documentName;
             switch ($error) {
                 case UPLOAD_ERR_OK:

                         //echo "rl ".$targetPath;9
                         if(move_uploaded_file($tmpName,$targetPath)){
                          //   $responseProcesarDocumento=$this->procesarDocumento($documentName);
                           $result=$this->verificarTipoDocumento($documentName);

                           $responseVerificarDocumento=json_decode($result->getContent());

                      //     var_dump($responseVerificarDocumento);


                           if($responseVerificarDocumento->exito==true){

                              $tipoDocumento=$responseVerificarDocumento->tipoDocumento;
                              $responseProcesarDocumento=$this->procesarDocumento($documentName,$tipoDocumento);
                              $responseProcesarDocumento=json_decode($responseProcesarDocumento->getContent());

                              if($responseProcesarDocumento->exito==true){
                                $valid=true;
                                $clientesNuevos=$responseProcesarDocumento->clientesNuevos;
                                $beneficiariosNuevos=$responseProcesarDocumento->beneficiariosNuevos;
                                $beneficiariosActualizados=$responseProcesarDocumento->beneficiariosActualizados;
                                $beneficiariosOmitidos=$responseProcesarDocumento->beneficiariosOmitidos;
                                $beneficiariosenBlanco=$responseProcesarDocumento->beneficiariosenBlanco;
                                  $msj=$responseProcesarDocumento->msj;
                              }else{
                                $msj=$responseProcesarDocumento->msj;
                              }

                           }else{
                          //   $msj=$responseVerificarDocumento->msj;
                        }

                         }


                     break;
                 case UPLOAD_ERR_INI_SIZE:
                     $msj = 'The uploaded file exceeds the upload_max_filesize directive in php.ini.';
                     break;
                 case UPLOAD_ERR_FORM_SIZE:
                     $msj = 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form.';
                     break;
                 case UPLOAD_ERR_PARTIAL:
                     $msj = 'The uploaded file was only partially uploaded.';
                     break;
                 case UPLOAD_ERR_NO_FILE:
                     $msj = 'No file was uploaded.';
                     break;
                 case UPLOAD_ERR_NO_TMP_DIR:
                     $msj = 'Missing a temporary folder. Introduced in PHP 4.3.10 and PHP 5.0.3.';
                     break;
                 case UPLOAD_ERR_CANT_WRITE:
                     $msj = 'Failed to write file to disk. Introduced in PHP 5.1.0.';
                     break;
                 case UPLOAD_ERR_EXTENSION:
                     $msj = 'File upload stopped by extension. Introduced in PHP 5.2.0.';
                     break;
                 default:
                     $msj = 'Unknown error';
                 break;
             }
        }else{
            $msj="Tipo de Formato Inválido";
        }


      }else{
        $msj="Debe seleccionar un documento";
      }


   }catch (\Exception $e) {
     $msj=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();

   }
          // echo $response;
          $clientesNuevos=$responseProcesarDocumento->clientesNuevos;
          $beneficiariosNuevos=$responseProcesarDocumento->beneficiariosNuevos;
          $beneficiariosActualizados=$responseProcesarDocumento->beneficiariosActualizados;
          $beneficiariosOmitidos=$responseProcesarDocumento->beneficiariosOmitidos;
    return $this->json(array('msj'=>$msj,'exito'=>$valid,
    'idVista'=>$idVista,'nombreDocumento'=>$documentName,'tipoDocumento'=>$tipoDocumento,
    'clientesNuevos'=>$clientesNuevos,'beneficiariosNuevos'=>$beneficiariosNuevos,
    'beneficiariosActualizados'=>$beneficiariosActualizados,'beneficiariosOmitidos'=>$beneficiariosOmitidos,
    'beneficiariosenBlanco'=>$beneficiariosenBlanco
  ));

  }

  function verificarTipoDocumento($nombreDocumento){
    $carpeta=$this->getParameter('uploads');
    $exito=false;
    $msj="";
    $tipoDocumento="";

    $tiposDocumentos['baseTarjetaHabientes']=array('Lugar de Entrega',NULL,'IUT','Número Tarjeta',NULL,NULL,'Nombre',NULL,'No. Empleado/Ref.',NULL,'Origen',NULL,'Tipo Tarjeta',NULL,'Fecha Creación');
    $tiposDocumentos['baseTitularesBeneficiarios']=array('Num Emp','Nombre del Titular','Nombre del Beneficiario','F Nacimiento','Tipo','Sexo','Unidad');

    try{
      $documento1=$nombreDocumento;

      $directory1=$carpeta.DIRECTORY_SEPARATOR.$documento1;

      $spreadsheet1 = IOFactory::load($directory1);

      //$sheetData1 = $spreadsheet1->getActiveSheet()->toArray();

      $worksheet = $spreadsheet1->getActiveSheet();
      $encabezados = [];
      $numeroRows=6;
      $rows = [];
      foreach ($worksheet->getRowIterator() AS $row) {
          $cellIterator = $row->getCellIterator();
          $cellIterator->setIterateOnlyExistingCells(FALSE); // This loops through all cells,
          $cells = [];
          foreach ($cellIterator as $cell) {
            $valor="";
            if(gettype($cell->getValue()) =="object"){
              $valor=$cell->getValue()->__toString();
            }else{
              $valor=$cell->getValue();
            }
              $cells[] = $valor;
          }
          $rows[] = $cells;
          if($numeroRows>0){
            $numeroRows--;
          }else{
            break;
          }
          /*echo "<pre>";
          var_dump($rows);
          echo "</pre>";
          exit("salimos");*/
      }
      $clave="";

    //  var_dump($sheetData1);/*
  /*  echo "<pre>";
    var_dump($rows);
    echo "</pre>";
*/
      foreach ($tiposDocumentos as $key => $td) {

        $result=array_diff($td,$rows[4]);
        $result2=array_diff($td,$rows[0]);

        if(count($result)==0){
          $tipoDocumento=$key;
        }elseif(count($result2)==0){
          $tipoDocumento=$key;
        }
      }
      /*echo "<pre>";
      var_dump($tipoDocumento);
      echo "</pre>";
        exit("hol");*/

      if($tipoDocumento !=""){
          $exito=true;
      }else{
        $msj="Tipo de Documento desconocido";
      }



    }catch (\Exception $e) {
     $msj=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();

    }

    return $this->json(array('msj'=>$msj,'exito'=>$exito,'tipoDocumento'=>$tipoDocumento));

  }

//con esta funcion se va a guardar la información en la base de datos
    function procesarDocumento($nombreDocumento,$tipoDocumento){

      $carpeta=$this->getParameter('uploads');
      $exito=false;
      $msj="";
      $em=$this->getDoctrine()->getManager();

      $clientesNuevos=0;
      $beneficiariosNuevos=0;
      $beneficiariosActualizados=0;
      $beneficiariosOmitidos=0;
      $beneficiariosenBlanco=0;
      try{
        $documento=$nombreDocumento;

        $directory=$carpeta.DIRECTORY_SEPARATOR.$documento;

        $spreadsheet= IOFactory::load($directory);

        $worksheet = $spreadsheet->getActiveSheet();
        //subir los tarjetahabientes
        if($tipoDocumento=="baseTarjetaHabientes"){
          // Get the highest row and column numbers referenced in the worksheet

          $highestRow = $worksheet->getHighestRow(); // e.g. 10
          $highestColumn = $worksheet->getHighestColumn(); // e.g 'F'
          $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn); // e.g. 5
          /********************>RESETEAMOS LOS CLIENTES POR LOS QUEYA NO TIENEN MEMBRESIAS-----*/

          $parameters3['status']="1";
          $query = $em->createQuery(
            'SELECT c
            FROM App\Entity\Cliente c
            where  c.status=:status
            '
            )->setParameters($parameters3);
            $Clientes= $query->execute();
            foreach ($Clientes as $key => $b) {
              // code...
              $b->setStatus("0");
              $em->persist($b);
            }
            //$em->flush();
          /***********************************************************************************/

          for ($row = 1; $row <= $highestRow; ++$row) {

              /*for ($col = 1; $col <= $highestColumnIndex; ++$col) {
                  $value = $worksheet->getCellByColumnAndRow($col, $row)->getValue();
                  echo '<td>' . $value . '</td>' . PHP_EOL;
              }*/



              $nombre = $worksheet->getCellByColumnAndRow(8, $row)->getValue();
              if(gettype($nombre) =="object"){
                $nombre=$nombre->__toString();
              }



              $iut = $worksheet->getCellByColumnAndRow(3, $row)->getValue();
              if(gettype($iut) =="object"){
                $iut=$iut->__toString();
              }




              $numeroTarjeta = $worksheet->getCellByColumnAndRow(5, $row)->getValue();
              if(gettype($numeroTarjeta) =="object"){
                $numeroTarjeta=$numeroTarjeta->__toString();
              }


              $tipoTarjeta = $worksheet->getCellByColumnAndRow(13, $row)->getValue();
              if(gettype($tipoTarjeta) =="object"){
                $tipoTarjeta=$tipoTarjeta->__toString();
              }


              $numeroEmpleado = $worksheet->getCellByColumnAndRow(10, $row)->getValue();
              if(gettype($numeroEmpleado) =="object"){
                $numeroEmpleado=(int)$numeroEmpleado->__toString();
              }else{
                $numeroEmpleado=(int)$numeroEmpleado;
              }


          //}

      //    foreach ($sheetData as $key => $fila) {
            //identificador es el numero de tarj
            //echo "<br>".(int)$fila[9];

              $Cliente=$this->getDoctrine()->getRepository(Cliente::class)->findOneBy(['numeroempleado'=>$numeroEmpleado,'status'=>"1"]);
            /*  echo "<br>nombre ".$fila[7];
              echo "<br>Iut ".$fila[2];
              echo "<br>tpo tarjeta ".$fila[12];
              echo "<br>Numerotarjeta ".$fila[4];
              echo "<br>numero empleado  ".$fila[9];*/

              if($nombre !=NULL && $numeroEmpleado != NULL){
                if($Cliente!=NULL){
                   //actualizamos

                   $Cliente->setNombre($nombre);
                   $Cliente->setIut($iut);
                   $Cliente->setNumerotarjeta($numeroTarjeta);
                   $Cliente->setTipotarjeta($tipoTarjeta);
                   $Cliente->setNumeroempleado($numeroEmpleado);
                   $Cliente->setStatus("1");
                 }else{
                   $Cliente= new Cliente();
                   $Cliente->setNombre($nombre);
                   $Cliente->setIut($iut);
                   $Cliente->setNumerotarjeta($numeroTarjeta);
                   $Cliente->setTipotarjeta($tipoTarjeta);
                   $Cliente->setNumeroempleado($numeroEmpleado);
                   $Cliente->setStatus("1");
                 }
                 $em->persist($Cliente);
              }
            /* */
          }
        //  exit("flsih");
          $em->flush();
          $exito=true;
        }elseif($tipoDocumento=="baseTitularesBeneficiarios"){
          //subimos los beneficiarios
          $highestRow = $worksheet->getHighestRow(); // e.g. 10
          $highestColumn = $worksheet->getHighestColumn(); // e.g 'F'
          $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn); // e.g. 5


          for ($row = 1; $row <= $highestRow; ++$row) {

          //foreach ($sheetData as $key => $fila) {
            //identificador es el numero de tarj
            //echo "<br>".(int)$fila[9];

            $numeroEmpleado=$worksheet->getCellByColumnAndRow(1, $row)->getValue();

            if(gettype($numeroEmpleado) =="object"){
              $numeroEmpleado=(int)$numeroEmpleado->__toString();
            }else{
              $numeroEmpleado=(int)$numeroEmpleado;
            }

            $nombre=$worksheet->getCellByColumnAndRow(2, $row)->getValue();
            if(gettype($nombre) =="object"){
              $nombre=$nombre->__toString();
            }
            $nombreBeneficario=$worksheet->getCellByColumnAndRow(3, $row)->getValue();

            if(gettype($nombreBeneficario) =="object"){
              $nombreBeneficario=$nombreBeneficario->__toString();
            }
            $fechaNacimiento=$worksheet->getCellByColumnAndRow(4, $row)->getValue();

            if(gettype($fechaNacimiento) =="object"){

              $fechaNacimiento=$fechaNacimiento->__toString();
            }

            $tipo=$worksheet->getCellByColumnAndRow(5, $row)->getValue();
            if(gettype($tipo) =="object"){
              $tipo=$tipo->__toString();
            }

            $sexo=$worksheet->getCellByColumnAndRow(6, $row)->getValue();
            if(gettype($sexo) =="object"){
              $sexo=$sexo->__toString();
            }
            $unidad=$worksheet->getCellByColumnAndRow(7, $row)->getValue();
            if(gettype($unidad) =="object"){
              $unidad=$unidad->__toString();
            }



            if($numeroEmpleado>0){
              $Cliente=$this->getDoctrine()->getRepository(Cliente::class)->findOneBy(['numeroempleado'=>$numeroEmpleado,'status'=>"1"]);
              /******crear Cliente****/
              if(!$Cliente){
                $Cliente= new Cliente();
                $Cliente->setNombre($nombre);
                $Cliente->setStatus("1");

                $Cliente->setNumeroempleado($numeroEmpleado);
                $em->persist($Cliente);
                $em->flush();
                $clientesNuevos++;

              }
              /*********************/
              if($Cliente){
                //reseteamos a todos los beneficiarios que tenga
                $parameters['numeroEmpleado']=$numeroEmpleado;
                $parameters['status']="1";
                $query = $em->createQuery(
                  'SELECT b
                  FROM App\Entity\Beneficiario b
                  inner join b.clientecliente c

                  where  b.status=:status and c.numeroempleado=:numeroEmpleado
                  '
                  )->setParameters($parameters);
                  $Beneficiarios= $query->execute();
                  foreach ($Beneficiarios as $key => $b) {
                    // code...
                    $b->setStatus("0");
                    $em->persist($b);
                  }
                  $em->flush();
                /*************fin de reset beneeficiarios****************/

                $parameters2['nombreBeneficario']=$nombreBeneficario;
                $parameters2['numeroempleado']=$numeroEmpleado;
                $query = $em->createQuery(
                  'SELECT b
                  FROM App\Entity\Beneficiario b
                  inner join b.clientecliente c

                  where  b.nombre=:nombreBeneficario and c.numeroempleado=:numeroempleado
                  '
                  )->setParameters($parameters2);
                  $Beneficiario= $query->getOneOrNullResult();
              //  $Beneficiario=$this->getDoctrine()->getRepository(Beneficiario::class)->findOneBy(['nombre'=>$fila[2]]);
                /****************************/

                /****VERIFICAMOS QUE EL  NOMBRE DE BENEFICIARIO SEA DIFRENTE DE VACIO.*****/
              /*  if($Beneficiario!=null){
                  if(count($Beneficiario)>1){
                    echo "<pre>";
                    var_dump($Beneficiario);
                    echo "</pre>";
                  }
                    $Beneficiario=$Beneficiario[0];
                }else{
                    $Beneficiario=NULL;
                }*/

                if(trim($nombreBeneficario) !=""){
                  if($Beneficiario){
                    $Beneficiario->setNombre($nombreBeneficario);
                    if(trim($fechaNacimiento) !=""){

                      $fechaNacimiento=\DateTime::createFromFormat('d/m/Y',$fechaNacimiento);
                      //se puso esto porque hay fechas que son m+ás bajas del tiempo unix y no se pudo conevrit

                      if($fechaNacimiento instanceof DateTimeInterface ){
                      $Beneficiario->setFechanacimiento($fechaNacimiento);
                      }

                    }

                    $Beneficiario->setTipo($tipo);
                    $Beneficiario->setSexo($sexo);
                    $Beneficiario->setUnidad($unidad);
                    $Beneficiario->setClientecliente($Cliente);
                     $em->persist($Beneficiario);
                     $beneficiariosActualizados++;
                  }else{
                    $Beneficiario =new Beneficiario();

                    $Beneficiario->setNombre($nombreBeneficario);
                    if(trim($fechaNacimiento) !=""){

                      $fechaNacimiento=\DateTime::createFromFormat('d/m/Y',$fechaNacimiento);
                      if($fechaNacimiento instanceof DateTimeInterface ){
                          $Beneficiario->setFechanacimiento($fechaNacimiento);
                      }
                    }

                    $Beneficiario->setTipo($tipo);
                    $Beneficiario->setSexo($sexo);
                    $Beneficiario->setUnidad($unidad);
                    $Beneficiario->setClientecliente($Cliente);
                    $Beneficiario->setStatus("1");
                     $em->persist($Beneficiario);
                     $beneficiariosNuevos++;
                  }

                }else{
                //  $beneficiariosenBlanco++;
                }

              }else{
                $msj.=" Cliente no encontrado ";
              }//fin de if cliente
            }else{
              $msj.=$numeroEmpleado;
              $beneficiariosOmitidos++;
            }



          }//fin del for
            $em->flush();
            /* */
          }else{
            $msj="Tipo  de documento desconocido, no se pudo procesar";
          }
        //  exit("flsih");

          $exito=true;



      }catch (\Exception $e) {
       $msj=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();

      }
      /*
      $clientesNuevos=0;
      $beneficiariosNuevos=0;
      $beneficiariosActualizados=0;
      $beneficiariosOmitidos=0;
      */
        return $this->json(array('msj'=>$msj,'exito'=>$exito,'clientesNuevos'=>$clientesNuevos,
        'beneficiariosNuevos'=>$beneficiariosNuevos,
        'beneficiariosActualizados'=>$beneficiariosActualizados,
        'beneficiariosenBlanco'=>$beneficiariosenBlanco,
        'beneficiariosOmitidos'=>$beneficiariosOmitidos));
    }
}
