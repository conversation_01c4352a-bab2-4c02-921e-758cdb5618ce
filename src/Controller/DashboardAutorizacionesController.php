<?php

namespace App\Controller;

use App\Entity\Authstage;
use App\Entity\Salelog;
use App\Entity\Venta;
use App\Service\MailService;
use DateTime;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class DashboardAutorizacionesController extends AbstractController
{
    /**
     * @Route("/autorizaciones/dashboard", name="app_dashboard_autorizaciones_dash")
     */
    public function index(): Response
    {
        $em = $this->getDoctrine()->getManager();

        $stages = [
        "Sin asociar", "Mandar a corporativo", "Recibir autorizaciones", "Mandar a facturar", "Facturar convenio"
        ];


        try {
            $query = $em->createQuery(
                'SELECT tv.nombre, tv.idtipoventa
                 FROM App\Entity\Tipoventa tv
                 WHERE tv.status = :status'
            )->setParameter("status", "1");
            $filtrosAutorizaciones = $query->getResult();

            $Usuario = $this->getUser();
            $query = $em->createQuery(
                'SELECT e.idempresa, e.nombre
                FROM App\Entity\Usuarioempresapermiso uem
                INNER JOIN uem.empresaIdempresa e
                INNER JOIN uem.usuarioIdusuario u
                WHERE e.status =:status and u.idusuario=:idusuario order by e.nombre asc 
                '
            )->setParameters(['status' => "1", 'idusuario' => $Usuario->getIdUsuario()]);
            $enterprises = $query->getResult();
            $query = $em->createQuery(
                'SELECT aus.idauthstage, aus.name
                FROM App\Entity\Authstage aus
                WHERE aus.status =:status order by aus.stageorder asc
                '
            )->setParameters(['status' => "1"]);
            $authStages = $query->getResult();

            $exito = true;

        } catch (\Exception $e) {
            $msj = 'Ocurrió un error al obtener los tipos de venta: ' . $e->getMessage();
        }

        return $this->render('dashboard_autorizaciones/index.html.twig', [
            'filtrosAutorizaciones' => $filtrosAutorizaciones,
            'enterprises' => $enterprises,
            'exito' => $exito,
            'authStages' => $authStages
        ]);
    }

    /**
     * @Route("/tabla-dashboard", name="tabla_dashboard_autorizaciones")
     */
    public function tableAutorizaciones(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $exito = false;
        $msj = "";
        $autorizaciones = [];

        $stages = [
            "Sin asociar", "Mandar a corporativo", "Recibir autorizaciones", "Mandar a facturar", "Pendiente Facturar", "Facturado"
        ];

        // Obtener los filtros de la solicitud
        $autorizacionState = $request->query->get('autorizacionState');

        $tipoVenta = $request->query->get('tipoVenta');
        $folioVenta = $request->query->get('folioVenta');
        $scanvalue = $request->query->get('scanvalue');

        $numeroAutorizacion = $request->query->get('numeroAutorizacion', '');

        $fechaInicioObj = DateTime::createFromFormat('d/m/Y', $request->get('fechaInicio'));
        $fechaFinObj = DateTime::createFromFormat('d/m/Y', $request->get('fechaFin'));

        if ($fechaInicioObj) {
            $fechaInicioObj = $fechaInicioObj->format('Y-m-d') . " 00:00:00";
        } else {
            // Manejar el error si la fecha no es válida
            $fechaInicioObj = null;
        }

        if ($fechaFinObj) {
            $fechaFinObj = $fechaFinObj->format('Y-m-d') . " 23:59:59";
        } else {
            // Manejar el error si la fecha no es válida
            $fechaFinObj = null;
        }


        $locations = $request->get("locations");
        $idempresa = $request->get("idempresa");



        $whereLocation = "";
        if (isset($locations[0])) {
            $whereLocation = " and (";
            for ($i = 0; $i < count($locations); $i++) {
                $whereLocation .= " s.idsucursal=" . $locations[$i];
                if ($i != count($locations) - 1) $whereLocation .= " or ";
            }
            $whereLocation .= ") ";
        }

        $whereDate = "";

        $params = [
            "status" => "1"
        ];

        if ($fechaInicioObj && $fechaFinObj) {
            $whereDate = " AND v.fechaventa BETWEEN :fechaInicio AND :fechaFin";
            $params["fechaInicio"] = $fechaInicioObj;
            $params["fechaFin"] = $fechaFinObj;
        } else if ($fechaInicioObj) {
            $whereDate = " AND v.fechaventa >= :fechaInicio";
            $params["fechaInicio"] = $fechaInicioObj;
        } else if ($fechaFinObj) {
            $whereDate = " AND v.fechaventa <= :fechaFin";
            $params["fechaFin"] = $fechaFinObj;
        }



        // Construir la consulta DQL
        $dql = '
        SELECT v.idventa, v.folio,v.total, v.pagadototal AS pagado, v.authorizationnumber, auths.stageorder as autorizacionstate, auths.name, v.fechaventa, v.liquidada ,tv.nombre AS tipoVenta, s.nombre as location,
        CONCAT(cl2.apellidopaterno, \' \', cl2.apellidomaterno, \' \', cl2.nombre) as beneficiario,
        CONCAT(c.apellidopaterno, \' \', c.apellidomaterno, \' \', c.nombre) as clientName
        FROM App\Entity\Venta v
        INNER JOIN v.clienteIdcliente c
        LEFT JOIN App\Entity\Beneficiarioventa bv With bv.ventaIdventa = v.idventa
        LEFT JOIN bv.clienteIdcliente cl2
        INNER JOIN v.sucursalIdsucursal s
        INNER JOIN s.empresaIdempresa e
        INNER JOIN v.tipoventaIdtipoventa tv
        LEFT JOIN v.authstageIdauthstage auths
        WHERE v.status = :status AND v.cotizacion = 0' . $whereDate . $whereLocation;

        // Agregar condiciones según los filtros
        $conditions = [];

        if ($tipoVenta != "-2" && $tipoVenta != "-1") {
            $conditions[] = 'tv.idtipoventa = :tipoVenta';
            $params["tipoVenta"] = $tipoVenta;
        }

        if ($autorizacionState != "-2" && $autorizacionState != "-1") {
            if ($autorizacionState == 0) $conditions[] = 'v.authstageIdauthstage IS NULL';
            else {
                $conditions[] = 'auths.idauthstage = :autorizacionState';
                $params["autorizacionState"] = $autorizacionState;
            }
        }

        if ($numeroAutorizacion !== '') {
            $conditions[] = 'v.authorizationnumber = :numeroAutorizacion';
            $params["numeroAutorizacion"] = $numeroAutorizacion;
        }

        if ($folioVenta !== '') {
            $conditions[] = 'v.folio = :folio';
            $params["folio"] = $folioVenta;
        }

        if ($scanvalue != null) {
            $conditions[] = 'v.authscan LIKE :scanvalue ';
            $params["scanvalue"] = $scanvalue;
        }

        if ($idempresa && $idempresa != '-1') {
            $conditions[] = 'e.idempresa = :idempresa';
            $params["idempresa"] = $idempresa;
        }

        if (count($conditions) > 0) {
            $dql .= ' AND ' . implode(' AND ', $conditions);
        }

        $dql .= ' ORDER BY v.fechaventa ASC';
        $query = $em->createQuery($dql)->setParameters($params);
        $autorizaciones = $query->getResult();

        $estadoCounts = [];
        $estadoTotales = [];

        foreach ($autorizaciones as $autorizacion) {
            $estado = $autorizacion['name'] ?? 'Sin asociar';
            $importe = $autorizacion['pagado'] ?? 0;


            if (!isset($estadoCounts[$estado])) {
                $estadoCounts[$estado] = 0;
                $estadoTotales[$estado] = 0;
            }

            $estadoCounts[$estado]++;
            $estadoTotales[$estado] += $importe;
        }



        $exito = true;


        return $this->render('dashboard_autorizaciones/tableAutorizaciones.html.twig', [
            'autorizaciones' => $autorizaciones,
            'estadoCounts' => $estadoCounts,  // Pasar la variable a la vista
            'estadoTotales'=>$estadoTotales,
            'exito' => $exito,
            'mensaje' => $msj
        ]);
    }

    /**
     * @Route("/dashboard-get-documents", name="dashboard-get-documents")
     */
    public function getDocuments(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $saleid = $request->get("saleId");
        $Sale = $em->getRepository(Venta::class)->findOneBy(array('idventa' => $saleid));
        $success = false;
        $msg = "";
        $documents = [];

        try {

            if ($Sale) {
                $enterpriseRFC = $Sale->getSucursalIdsucursal()->getEmpresaIdempresa()->getRfc();
                $pathSaleDocuments = $this->getParameter('carpetaDocumentosVenta') . '/';
                $pathTickets = $this->getParameter('carpetaTickets') . '/' . $enterpriseRFC . '/' . $Sale->getFolio() . '/';

                $ticket = $Sale->getTicketpdf();
                $specialTicket = $Sale->getTickerpdfespecial();

                if ($ticket) array_push($documents, ["filename" => $ticket, "path" => $pathTickets, "exists" => file_exists($pathTickets . $ticket)]);
                if ($specialTicket) array_push($documents, ["filename" => $specialTicket, "path" => $pathTickets, "exists" => file_exists($pathTickets . $specialTicket)]);

                $query = $em->createQuery(
                    'SELECT dv.nombredocumento, u.idusuario
                       FROM App\Entity\Documentoventa dv
                       INNER JOIN dv.ventaIdventa v
                       INNER JOIN v.gerente u
                       where v.idventa =:idventa AND dv.status =:status
                       '
                )->setParameters(['idventa' => $saleid, 'status' => '1']);
                $saledocuments = $query->getResult();

                foreach ($saledocuments as $saledocument) {
                    $path = $pathSaleDocuments . $saledocument["idusuario"] . '/' . $saleid . '/';
                    array_push($documents, ["filename" => $saledocument["nombredocumento"], "path" => $path, "exists" => file_exists($path . $saledocument["nombredocumento"])]);
                }

                $success = true;
            } else $msg = "No se encontróp la venta";

        } catch (\Exception $e) {
            $msg = $e->getMessage();
        }

        return $this->render('dashboard_autorizaciones/documents-visor.html.twig', [
            'documents' => $documents,
            'success' => $success,
            'msg' => $msg
        ]);
    }

    /**
     * @Route("/dashboard-get-history", name="dashboard-get-history")
     */
    public function getHistory(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $saleid = $request->get("saleId");

        $query = $em->createQuery(
            'SELECT sl.name, sl.date, sl.comment, aus.name as autorizacionstate,
            CONCAT(u.nombre, \' \', u.apellidomaterno, \' \', u.apellidopaterno) as user
                FROM App\Entity\Salelog sl
                INNER JOIN sl.ventaIdventa v
                INNER JOIN sl.usuarioIdusuario u
                LEFT JOIN sl.authstageIdauthstage aus
                WHERE v.idventa =:idventa
                '
        )->setParameters(['idventa' => $saleid]);
        $saleLogs = $query->getResult();

        return $this->render('dashboard_autorizaciones/get-history.html.twig', [
            'saleLogs' => $saleLogs
        ]);
    }

    /**
     * @Route("/dashboard-get-change-state-form", name="dashboard-get-change-state-form")
     */
    public function getChangeStateForm(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $saleid = $request->get("saleId");
        $Sale = $em->getRepository(Venta::class)->findOneBy(array('idventa' => $saleid));

        $AuthStage = $Sale->getAuthstageIdauthstage();
        $curStageOrder = ($AuthStage) ? $AuthStage->getStageorder() : 0;
        $query = $em->createQuery(
            'SELECT aus.name, aus.stageorder
                FROM App\Entity\Authstage aus
                WHERE aus.stageorder <=:stageorder
                ORDER BY aus.stageorder DESC
                '
        )->setParameters(['stageorder' => $curStageOrder]);
        $authStages = $query->getResult();

        return $this->render('dashboard_autorizaciones/get-change-state-form.html.twig', [
            'authStages' => $authStages,
            'saleid' => $saleid
        ]);
    }

    /**
     * @Route("/dashboard-change-auth-stage", name="dashboard-change-auth-stage")
     */
    public function changeAuthStage(Request $request, MailService $mailer): Response
    {
        $em = $this->getDoctrine()->getManager();
        $saleid = $request->get("saleId");
        $comment = $request->get("comment");
        $stage = $request->get("stage");
        $success = false;
        $msg = '';

        try {
            $Sale = $em->getRepository(Venta::class)->findOneBy(array('idventa' => $saleid));
            $AuthStage = $em->getRepository(Authstage::class)->findOneBy(array('stageorder' => $stage));
            $curUser = $this->getUser();
            $curUserName = $curUser->getNombre() . ' ' . $curUser->getApellidopaterno() . ' ' . $curUser->getApellidomaterno();
            $SaleLog = new Salelog();

            $SaleLog->setName($AuthStage->getName());
            $SaleLog->setDate(new \DateTime("now"));
            $SaleLog->setAuthstageIdauthstage($AuthStage);
            $SaleLog->setVentaIdventa($Sale);
            $SaleLog->setUsuarioIdusuario($curUser);
            $SaleLog->setComment($comment);
            $Sale->setAuthstageIdauthstage($AuthStage);

            if ($stage < 5) {
                $Sale->setVentagroupIdventagroup(null);
            }

            $em->persist($Sale);
            $em->persist($SaleLog);
            $em->flush();

            $subject = "Cambio de estado de venta con folio " . $Sale->getFolio() . " de la empresa " . $Sale->getSucursalIdsucursal()->getEmpresaIdempresa()->getNombre();
            $body = "Se les hace llegar este correo porque el usuario " . $curUserName . " cambio el estado de la venta con folio " . $Sale->getFolio() . " y número de autorización " . $Sale->getAuthorizationnumber() . " a " . $AuthStage->getName() . " con la siguiente razón: " . $comment;

            $emailData = [
                "subject" => $subject,
                "body" => $body
            ];

            $query = $em->createQuery(
                'SELECT DISTINCT u.email
                    FROM App\Entity\Salelog sl
                    INNER JOIN sl.usuarioIdusuario u
                    WHERE u.status =:status
                    ORDER BY u.email ASC
                    '
            )->setParameters(['status' => 1]);
            $subscribers = $query->getResult();

            $resultmail = $mailer->sendEmailUpdateAuthStage($emailData, array_column($subscribers, 'email'));

            $success = true;

        } catch (\Exception $e) {
            $msg = $e->getMessage();
        }

        return $this->json(["success" => $success, "msg" => $msg]);
    }
}
