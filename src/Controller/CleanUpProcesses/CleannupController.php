<?php

namespace App\Controller\CleanUpProcesses;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

use App\Entity\Venta;
use App\Entity\Beneficiarioventa;
use App\Entity\Cliente;

class CleannupController extends AbstractController
{

    /**
     * @Route("/beneficiarios-en-ventas", name="beneficiariosEnVentas")
     */
    public function beneficiariosEnVentas(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $updated=[];

        $query = $em->createQuery('SELECT  v
          
         FROM App\Entity\Venta v
         INNER JOIN App\Entity\Beneficiarioventa bv WITH bv.ventaIdventa = v
         where v.status=:status')
            ->setParameters(['status' => '1']);

        $ventas = $query->getResult();

        foreach ($ventas as $venta) {

            $query2 = $em->createQuery('SELECT  COALESCE(GROUP_CONCAT(CONCAT(\' \' , benef.nombre, \' \' , benef.apellidopaterno, \' \' , benef.apellidomaterno) ), \', \' )  as beneficiarios
            FROM App\Entity\Beneficiarioventa bv
            INNER JOIN bv.clienteIdcliente benef
            where bv.ventaIdventa =:venta
            GROUP BY bv.ventaIdventa'
            
            )->setParameters(['venta' => $venta]);

            $namearray = $query2->getOneOrNullResult();

            $updated[]= $namearray;

            $venta->setBeneficiario($namearray['beneficiarios']);

            $em->persist($venta);

        }

        $em->flush();

        return $this->json(["msj" => $updated]);
    }

    /**
     * @Route("/carga-masiva-cats-class-limp/{rfc}", name="cleanClases")
     */
    public function cleanClases(Request $request, string $rfc = "GOM180228DD4"): Response
    {
        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery('SELECT prod.idproducto, cat.idcategoria, clase.idclase, clase.nombre        
         FROM App\Entity\Producto prod
         INNER JOIN prod.categoriaIdcategoria cat
         INNER JOIN cat.claseIdclase clase
         INNER JOIN clase.empresaIdempresa emp
         where prod.status=:status clase.status =:status AND cat.nombre != TRIM(cat.nombre) AND emp.rfc =:emprfc')
            ->setParameters(['status' => '1', 'emprfc' => $rfc]);

        $results = $query->getResult();

        $dict = [];
        foreach ($results as $result) {
            $claseNombre = $result['nombre'];

            if (!array_key_exists($claseNombre, $dict)) {
                $dict[$claseNombre] = [];
            }

            $idCategoria = $result['idcategoria'];

            if (!array_key_exists($idCategoria, $dict[$claseNombre])) {
                $dict[$claseNombre][$idCategoria] = [];
            }

            $dict[$claseNombre][$idCategoria][] = [
                'idproducto' => $result['idproducto'],
            ];
        }

        $updated = [];
        foreach ($dict as $claseNombre => $values) {
            $oldClase = $em->getRepository(Clase::class)->findOneBy(['nombre' => $claseNombre, 'status' => "1"]);
            $correctclasename = trim($claseNombre);
            $clase = $em->getRepository(Clase::class)->findOneBy(['nombre' => $correctclasename, 'status' => "1"]);

            if (!$clase && !$oldClase) {
                continue;
            } else if (!$clase && $oldClase) {
                $clase = $oldClase;
                $oldClase = null;
            }

            if ($oldClase != $clase) {
                $oldClase->setStatus(0);
                $em->persist($oldClase);
            }

            $clase->setNombre($correctclasename);
            $em->persist($clase);
            $em->flush();

            foreach ($values as $idcategoria => $productos) {
                $oldCat = $em->getRepository(Categoria::class)->findOneBy(['idcategoria' => $idcategoria]);
                $oldCatname = $oldCat->getNombre();
                if ($oldCat) {
                    $correctname = trim($oldCatname);
                } else {
                    continue;
                }

                $cat = $em->getRepository(Categoria::class)->findOneBy(['nombre' => $correctname, 'status' => "1"]);

                if (!$cat && !$oldCat) {
                    continue;
                } else if (!$cat && $oldCat) {
                    $cat = $oldCat;
                    $oldCat = null;
                }

                if ($oldCat != $cat) {
                    $oldCat->setStatus(0);
                    $em->persist($oldCat);
                }

                $cat->setNombre($correctname);
                $cat->setClaseIdclase($clase);
                $em->persist($cat);
                $em->flush();

                foreach ($productos as $productoData) {
                    $producto = $em->getRepository(Producto::class)->findOneBy(['idproducto' => $productoData['idproducto']]);

                    if ($producto) {
                        $producto->setCategoriaIdcategoria($cat);
                        $em->persist($producto);
                        $em->flush();

                        $updated[$correctclasename][$correctname][] = [
                            'idproducto' => $productoData['idproducto'],
                            'old clase' => $claseNombre,
                            'old cat' => $oldCatname
                        ];
                    }
                }
            }
        }
        return $this->json(["prev" => $dict, "msj" => $updated]);
    }
}
