<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
/**
 * @Route("/filter")
 */
class FilterController extends AbstractController
{
    /**
     * @Route("/obtener-sucursal", name="almacen-obtener-sucursal")
     */
    public function obtenerSucursal(Request $request): Response
    {
        $em=$this->getDoctrine()->getManager();
        $idempresa=$request->get("idempresa");
        $tipo = 'sucursal';

        //var_dump($idempresa);
        $query = $em->createQuery(
            'SELECT s.idsucursal, s.nombre
               FROM App\Entity\Sucursal s
               inner join s.empresaIdempresa emp
               where s.status =:status and emp.idempresa=:idempresa and s.tipo =:tipo order by s.nombre asc 
               '
        )->setParameters(['status'=>"1", 'idempresa'=>$idempresa, 'tipo'=>$tipo]);
        $sucursales= $query->getResult();

        $tipo = 'bodega';

        //var_dump($idempresa);
        $query = $em->createQuery(
            'SELECT s.idsucursal, s.nombre
               FROM App\Entity\Sucursal s
               inner join s.empresaIdempresa emp
               where s.status =:status and emp.idempresa=:idempresa and s.tipo =:tipo order by s.nombre asc 
               '
        )->setParameters(['status'=>"1", 'idempresa'=>$idempresa, 'tipo'=>$tipo]);
        $bodegas= $query->getResult();


        $tipo = 'campaña';

        //var_dump($idempresa);
        $query = $em->createQuery(
            'SELECT s.idsucursal, s.nombre
               FROM App\Entity\Sucursal s
               inner join s.empresaIdempresa emp
               where s.status =:status and emp.idempresa=:idempresa and s.tipo =:tipo order by s.nombre asc 
               '
        )->setParameters(['status'=>"1", 'idempresa'=>$idempresa, 'tipo'=>$tipo]);
        $campañas= $query->getResult();


        $query_clase = $em->createQuery(
            'SELECT c.idclase, c.nombre
               FROM App\Entity\Clase c
               inner join c.empresaIdempresa emp
               where c.status =:status and emp.idempresa=:idempresa order by c.nombre asc 
               '
        )->setParameters(['status'=>"1", 'idempresa'=>$idempresa]);
        $clases= $query_clase->getResult();


        return $this->render('almacen/sucursales.html.twig', [
            'sucursales'=>$sucursales,
            'bodegas'=>$bodegas,
            'campañas'=>$campañas,
            'clases'=>$clases
        ]);
    }

    /**
     * @Route("/obtener-clase", name="almacen-obtener-clase") 
     */
    public function obtenerClases(Request $request): Response
    {
        $em=$this->getDoctrine()->getManager();
        $idempresa=$request->get("idempresa");
        


        $query_clase = $em->createQuery(
            'SELECT c.idclase, c.nombre
               FROM App\Entity\Clase c
               inner join c.empresaIdempresa emp
               where c.status =:status and emp.idempresa=:idempresa order by c.nombre asc 
               '
        )->setParameters(['status'=>"1", 'idempresa'=>$idempresa]);
        $clases= $query_clase->getResult();


        return $this->render('almacen/clases.html.twig', [

            'clases'=>$clases
        ]);
    }

    /**
     * @Route("/get-tags", name="almacen-get-tags") 
     */
    public function getTags(Request $request): Response
    {
        $em=$this->getDoctrine()->getManager();
        $enterpriseId=$request->get("enterpriseId");

        $query = $em->createQuery(
            'SELECT t.idtag, t.name
               FROM App\Entity\Tag t
               INNER JOIN t.empresaIdempresa emp
               WHERE t.status =:status AND emp.idempresa=:enterpriseId ORDER BY t.name ASC 
               '
        )->setParameters(['status'=>"1", 'enterpriseId'=>$enterpriseId]);
        $tags= $query->getResult();


        return $this->render('almacen/tags.html.twig', [
            'tags'=>$tags
        ]);
    }

    /**
     * @Route("/obtener-subcategoria", name="almacen-obtener-subcategoria")
     */
    public function obtenerSubcategoria(Request $request): Response
    {
        $em=$this->getDoctrine()->getManager();
        $idempresa=$request->get("idempresa");
        $clases=$request->get("clases");

        $whereClase="";
        $parametros['status']=1;

        if(isset($clases[0])){

            $whereClase=" and (";

            for($i = 0; $i < count($clases); $i++){

                $whereClase .=" c.idclase=".$clases[$i];

                if($i != count($clases)-1) $whereClase .=" or ";
            }
            $whereClase .=") ";
            
        }
        

        $query = $em->createQuery(
            'SELECT sc.idcategoria, sc.nombre
               FROM App\Entity\Categoria sc
               INNER JOIN  sc.claseIdclase c
               where sc.status =:status'.$whereClase.'  order by sc.nombre asc
               '
        )->setParameters($parametros);
        $categorias= $query->getResult();



        return $this->render('almacen/subcategorias.html.twig', [
            'categorias'=>$categorias
        ]);
    }

    /**
     * @Route("/obtener-marcas", name="almacen-obtener-marcas")
     */
    public function obtenerMarca(Request $request): Response
    {
        $em=$this->getDoctrine()->getManager();
        
        $categorias=$request->get("categorias");

        $parametros['status']=1;
        //Categorias
        $whereCategoria="";

        if(isset($categorias[0])){

            $whereCategoria=" and (";

            for($i = 0; $i < count($categorias); $i++){

                $whereCategoria .=" sc.idcategoria=".$categorias[$i];

                if($i != count($categorias)-1) $whereCategoria .=" or ";
            }
            $whereCategoria .=") ";
            
        }

        //obtenemos el stoc por marca
        $query = $em->createQuery(
            'SELECT m.nombre, m.idmarca
               FROM App\Entity\Stock s
               INNER JOIN  s.productoIdproducto p
               INNER JOIN  p.marcaIdmarca m
               INNER JOIN  p.categoriaIdcategoria sc
               where s.status =:status and s.cantidad > 0 '.$whereCategoria.' group by m.nombre,m.idmarca order by m.nombre asc
               '
        )->setParameters($parametros);
        $marcas= $query->getResult();



        return $this->render('almacen/marcas.html.twig', [
            'marcas'=>$marcas
        ]);
    }

    /**
     * @Route("/obtener-tipoventa", name="reporte-ventas-tipoventa")
     */
    public function obtenerTipoVenta(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();

        $idempresa = $request->get("idempresa");

        $query = $em->createQuery(
            'SELECT tv
               FROM App\Entity\Tipoventa tv
               INNER JOIN tv.empresaIdempresa e
               where tv.status =:status AND e.idempresa =:idempresa order by tv.nombre
               '
        )->setParameters(['status' => "1", 'idempresa' => $idempresa]);
        $tiposVenta = $query->getResult();


        return $this->render('reporte_ventas/tipoventas.html.twig', [
            'tiposVenta' => $tiposVenta,
        ]);
    }

    /**
     * @Route("/get-sale-quotations-filter", name="reporte-ventas-sale-quotations-filter")
     */
    public function getSaleQuotationsFilter(Request $request): Response
    {
        return $this->render('reporte_ventas/reporte-ventas-sale-quotations-filter.html.twig', []);
    }

    /**
     * @Route("/obtener-sucursal-supervisor", name="almacen-obtener-sucursal-supervisor")
     */
    public function obtenerSucursalSupervisor(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $idempresa = $request->get("idempresa");

        // Query específico para sucursales que han tenido ventas del tipo BIMBO (1799)
        $query = $em->createQuery(
            'SELECT DISTINCT s.idsucursal, s.nombre
             FROM App\Entity\Venta v
             INNER JOIN v.sucursalIdsucursal s
             INNER JOIN v.tipoventaIdtipoventa tv
             INNER JOIN s.empresaIdempresa emp
             WHERE s.status = :status
             AND emp.idempresa = :idempresa
             AND tv.idtipoventa = 1799
             AND v.status = :status
             ORDER BY s.nombre ASC'
        )->setParameters(['status' => "1", 'idempresa' => $idempresa]);

        $sucursales = $query->getResult();

        return $this->render('almacen/sucursales_supervisor.html.twig', [
            'sucursales' => $sucursales
        ]);
    }

}
