<?php

namespace App\Controller;

use App\Entity\Stock;
use App\Entity\Sucursal;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
/**
 * @Route("/reportes")
 */
class InformesController extends AbstractController
{
    /**
     * @Route("/ventas", name="informe-ventas")
     */
    public function informeVentas(): Response
    {
        $em=$this->getDoctrine()->getManager();

        $Usuario=$this->getUser();
        $query = $em->createQuery(
            'SELECT e.idempresa, e.nombre
               FROM App\Entity\Usuarioempresapermiso uem
               inner join uem.empresaIdempresa e
               inner join uem.usuarioIdusuario u
               where e.status =:status and u.idusuario=:idusuario order by e.nombre asc 
               '
        )->setParameters(['status'=>"1",'idusuario'=>$Usuario->getIdUsuario()]);
        $empresas= $query->getResult();

        return $this->render('informes/ventas.html.twig', [
            'empresas' => $empresas,
        ]);
    }


    /**
     * @Route("/obtener-resultados", name="informe-ventas-obtener-resultados")
     */
    public function obtenerResultados(Request $request): Response
    {
        //$sucursales = $this->getDoctrine()->getRepository(Sucursal::class)->findBy(array('status'=>"1",'tipo'=>'sucursal'),array('nombre'=>'ASC'));

        $em=$this->getDoctrine()->getManager();
        //$sucursales = $this->getDoctrine()->getRepository(Sucursal::class)->findBy(array('status'=>"1",'tipo'=>'sucursal'),array('nombre'=>'ASC'));
        //$sucursales = $em->getRepository(Sucursal::class)->findBy(array('status'=>"1",'tipo'=>'sucursal'),array('nombre'=>'ASC'));

        $empresas=$request->get("empresas");
        $whereEmpresa="";

        //Empresas
        if(isset($empresas[0])){

            $whereEmpresa=" and (";

            for($i = 0; $i < count($empresas); $i++){

                $whereEmpresa .=" emp.idempresa=".$empresas[$i];

                if($i != count($empresas)-1) $whereEmpresa .=" or ";
            }
            $whereEmpresa .=") ";
            
            
        }
        else $whereEmpresa=" and emp.idempresa= 0 ";


        $query = $em->createQuery(
            'SELECT s
               FROM App\Entity\Sucursal s
               inner join s.empresaIdempresa emp
               where s.status =:status and s.tipo =:tipo'.$whereEmpresa.'order by s.nombre asc 
               '
        )->setParameters(['status'=>"1",'tipo'=>'sucursal']);
        $sucursales= $query->getResult();

        $totalVentasCantidad="";
        $totalVentasMonto="";

        $User=$this->getUser();


        $sucursalesData=[];
        $parametros=[];
        $conveniosDataGlobal=[];
        $tipopagoDataGlobal=[];
        $rangoCampoFechaDia = $request->get("rangoCampoFechaDia");
        $opcionesVentas = $request->get("opcionesVentas");

        $fechaInicioRangoDia = \DateTime::createFromFormat('d/m/Y', $request->get("fechaInicioRangoDia"));
        $fechaFinRangoDia = \DateTime::createFromFormat('d/m/Y', $request->get("fechaFinRangoDia"));

        $whereUsuario="";
        $whereSucursal="";

        if($this->isGranted('ROLE_ADMIN') ){
            if( $opcionesVentas==""){
                $whereUsuario=" and u.idusuario =:idusuario ";
                $parametros['idusuario']=$User->getIdusuario();

            }else if($opcionesVentas=="resultadoscomogerente"){
                $whereUsuario=" and gerente.idusuario =:gerente ";
                $parametros['gerente']=$User->getIdusuario();

            }else if($opcionesVentas=="resultadoscomoadministrador" ){

            }

        }else  if($this->isGranted('ROLE_VENDEDOR') || $opcionesVentas==""){
            $whereUsuario=" and u.idusuario =:idusuario ";
            $parametros['idusuario']=$User->getIdusuario();

        }


        $whereDateRangoDia = " and v.fechaventa >= :fechaInicioRangoDia  and v.fechaventa<= :fechaFinRangoDia ";
        if (!$fechaInicioRangoDia || !$fechaFinRangoDia) {
            $whereDateRangoDia = "";

        } else {
            //   $fechaInicioRangoDia = $fechaInicioRangoDia->modify('first day of this month 00:00:00');
            //  $currentMonthDateTime = new \DateTime();
            //  $fechaFinRangoDia = $fechaFinRangoDia->modify('last day of this month 23:59:00');
            $parametros['fechaInicioRangoDia'] = $fechaInicioRangoDia->format('Y-m-d')." 00:00:00" ;
            $parametros['fechaFinRangoDia'] =  $fechaFinRangoDia->format('Y-m-d')." 23:59:00";
        }
        $parametros['status']=1;



        //$em=$this->getDoctrine()->getManager();
        foreach ($sucursales as $keySucursal =>$sucursal){
            $parametros['idsucursal']=$sucursal->getIdsucursal();
            //obtenemos las ventas
            $query = $em->createQuery(
                'SELECT  v.idventa, v.fechaventa, v.total, v.iva, v.folio, v.tipopago,v.pidiofactura,tv.nombre AS convenio,v.pagado
               FROM App\Entity\Venta v
               inner join v.sucursalIdsucursal s
               inner join v.gerente gerente
               inner join v.usuarioIdusuario u
               inner join v.tipoventaIdtipoventa tv
               where v.status=:status and s.idsucursal=:idsucursal and v.cotizacion !=1 '.$whereDateRangoDia.' '.$whereUsuario.'
               '
            )->setParameters($parametros);
            $Ventas= $query->getResult();
            
            $sucursalesData[$sucursal->getNombre()]=['nombre'=>$sucursal->getNombre(),'ventas'=>$Ventas];
            

        }

        

        /* echo "<pre>";
         var_dump($sucursalesData);
         echo "</pre>";*/

        //scamos los resultados por tipo de venta

        foreach ($sucursalesData as $keySucursal =>$sucursal){
            $conveniosData=[];
            $tipopagoData=[];

            foreach ($sucursal['ventas'] as $keyVenta =>$venta){



                if(!isset($sucursalesData[$keySucursal]['conveniosData'][$venta['convenio']]['nombre'])){
                    $sucursalesData[$keySucursal]['conveniosData'][$venta['convenio']]['nombre']=$venta['convenio'];
                }
                if(!isset($sucursalesData[$keySucursal]['conveniosData'][$venta['convenio']]['noVentas'])){
                    $sucursalesData[$keySucursal]['conveniosData'][$venta['convenio']]['noVentas']=0;
                }
                $sucursalesData[$keySucursal]['conveniosData'][$venta['convenio']]['noVentas']+=1;


                /* Datos Global */
                if(!isset($conveniosDataGlobal[$venta['convenio']]['nombre'])){
                    $conveniosDataGlobal[$venta['convenio']]['nombre']=$venta['convenio'];
                }

                if(!isset($conveniosDataGlobal[$venta['convenio']]['noVentas'])){
                    $conveniosDataGlobal[$venta['convenio']]['noVentas']=0;
                }
                $conveniosDataGlobal[$venta['convenio']]['noVentas']+=1;
                /* fin de adtos global*/




                //sacamos los pagos de la venta


                $Pagos=[];

                if($venta['convenio']=="UAM"){
                    //   echo "entra <br>";
                    unset($Pagos);
                    $Pagos[]=array('idpago'=>"",'monto'=>$venta['pagado'],'fecha'=>$venta['fechaventa'],'total'=>$venta['total'],'tipopago'=>"convenio");
                }else{
                    //  echo "entra 2<br>";
                    $query = $em->createQuery(
                        'SELECT  p.idpago, p.monto, p.fecha, p.total, p.tipopago
               FROM App\Entity\Pago p
               inner join p.ventaIdventa v
               where p.status=:status and v.idventa=:idventa 
               '
                    )->setParameters(['status'=>'1','idventa'=>$venta['idventa']]);
                    $Pagos =  $query->getResult();
                }

                if(!isset( $sucursalesData[$keySucursal]['conveniosData'][$venta['convenio']]['pagos'])){
                    $sucursalesData[$keySucursal]['conveniosData'][$venta['convenio']]['pagos']=[];
                }
                if(!isset($sucursalesData[$keySucursal]['conveniosData'][$venta['convenio']]['totalPagosMonto'])){
                    $sucursalesData[$keySucursal]['conveniosData'][$venta['convenio']]['totalPagosMonto']=0;
                }
                /* variables globales*/
                if(!isset($conveniosDataGlobal[$venta['convenio']]['pagos'])){
                    $conveniosDataGlobal[$venta['convenio']]['pagos']=[];
                }
                if(!isset($conveniosDataGlobal[$venta['convenio']]['totalPagosMonto'])){
                    $conveniosDataGlobal[$venta['convenio']]['totalPagosMonto']=0;
                }

                /* fin de variables globales*/
                $venta['ṕagos']=[];

                if($Pagos){

                    $venta['ṕagos']=$Pagos;

                    foreach ($Pagos as $keyPago =>$Pago){
                        //   echo "entra 3<br>";

                        if(!isset($tipopagoData[$Pago['tipopago']]['noVentas'])){
                            $tipopagoData[$Pago['tipopago']]['noVentas']=[];
                        }
                        if(!isset($tipopagoDataGlobal[$Pago['tipopago']]['noVentas'])){
                            $tipopagoDataGlobal[$Pago['tipopago']]['noVentas']=[];
                        }
                        $tipopagoData[$Pago['tipopago']]['noVentas'][$venta['idventa']]=1;
                        $tipopagoDataGlobal[$Pago['tipopago']]['noVentas'][$venta['idventa']]=1;


                        /*********************************/

                        if(!isset($tipopagoData[$Pago['tipopago']]['nombre'])){
                            $tipopagoData[$Pago['tipopago']]['nombre']=$Pago['tipopago'];
                        }
                        if(!isset($tipopagoDataGlobal[$Pago['tipopago']]['nombre'])){
                            $tipopagoDataGlobal[$Pago['tipopago']]['nombre']=$Pago['tipopago'];
                        }

                        if(!isset($tipopagoData[$Pago['tipopago']]['totalPagosMonto'])){
                            $tipopagoData[$Pago['tipopago']]['totalPagosMonto']=0;
                        }
                        if(!isset($tipopagoDataGlobal[$Pago['tipopago']]['totalPagosMonto'])){
                            $tipopagoDataGlobal[$Pago['tipopago']]['totalPagosMonto']=0;
                        }
                        $tipopagoData[$Pago['tipopago']]['totalPagosMonto']+=$Pago['monto'];
                        $tipopagoDataGlobal[$Pago['tipopago']]['totalPagosMonto']+=$Pago['monto'];


                        if(!isset($tipopagoData[$Pago['tipopago']]['noPagos'])){
                            $tipopagoData[$Pago['tipopago']]['noPagos']=0;
                        }
                        if(!isset($tipopagoDataGlobal[$Pago['tipopago']]['noPagos'])){
                            $tipopagoDataGlobal[$Pago['tipopago']]['noPagos']=0;
                        }
                        $tipopagoData[$Pago['tipopago']]['noPagos']+=1;
                        $tipopagoDataGlobal[$Pago['tipopago']]['noPagos']+=1;

                        if(!isset($tipopagoData[$Pago['tipopago']]['pagos'])){
                            $tipopagoData[$Pago['tipopago']]['pagos']=[];
                        }
                        if(!isset($tipopagoDataGlobal[$Pago['tipopago']]['pagos'])){
                            $tipopagoDataGlobal[$Pago['tipopago']]['pagos']=[];
                        }
                        $tipopagoData[$Pago['tipopago']]['pagos'][]=$Pago;
                        $tipopagoDataGlobal[$Pago['tipopago']]['pagos'][]=$Pago;

                        /*******************************/
                        //  $sucursalesData[$keySucursal]['conveniosData'][$venta['convenio']]['pagos'][]=$Pago;
                        $venta['pagos'][]=$Pago;

                        // $conveniosData[$venta['convenio']]['totalPagosMonto']+=$Pago['monto'];
                        $sucursalesData[$keySucursal]['conveniosData'][$venta['convenio']]['totalPagosMonto']+=$Pago['monto'];
                        /* variebla global*/
                        $conveniosDataGlobal[$venta['convenio']]['totalPagosMonto']+=$Pago['monto'];
                        /* foin de variable global*/
                    }
                }
                /*    echo "<pre>";
                    var_dump($venta);
                    echo "</pre>";*/
                //   $sucursalesData[$keySucursal]
                //$conveniosData[$venta['convenio']]['ventas'][]=$venta;

                //     echo "se agrego esta venta a ".$sucursalesData[$keySucursal]['nombre']." ".$venta["idventa"]."<br>";
                $sucursalesData[$keySucursal]['conveniosData'][$venta['convenio']]['ventas'][]=$venta;
                $conveniosDataGlobal[$venta['convenio']]['ventas'][]=$venta;
            }
            // $sucursalesData[$keySucursal]['conveniosData']=$conveniosData;
            $sucursalesData[$keySucursal]['tipopagoData']=$tipopagoData;
            unset($conveniosData);
            unset($tipopagoData);

        }

        /*echo "<pre>";
        var_dump($sucursalesData);
        echo "</pre>";*/


        return $this->render('informes/obtener-resultados.html.twig', [
            'sucursalesData'=>$sucursalesData,
            'conveniosDataGlobal'=>$conveniosDataGlobal,
            'tipopagoDataGlobal'=>$tipopagoDataGlobal
        ]);
    }


    /**
     * @Route("/armazones-vendidos-con-stock", name="armazones-vendidos-con-stock")
     */
    public function armazonesVendidosConStock(): Response
    {
        $em=$this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT s.idstock,s.cantidad, p.modelo, s.codigobarras
               FROM App\Entity\Stock s
               inner join s.productoIdproducto p
               where s.status =:status and s.cantidad >0 and p.tipoproducto=:tipoproducto and p.masivounico=:masivounico
               '
        )->setParameters(['status'=>"1",'tipoproducto'=>"1","masivounico"=>"1"]);
        $stocks= $query->getResult();

        foreach ($stocks as $keyStock=> $stock){

            $query = $em->createQuery(
                'SELECT v.folio, 
               v.fechacreacion,sucursal.nombre as nombreSucursal,
               u.apellidopaterno, u.apellidomaterno, u.nombre as nombreVendedor
               FROM App\Entity\Stockventa sv
               inner join sv.ventaIdventa v
               inner join sv.stockIdstock s
               inner join v.sucursalIdsucursal sucursal
               inner join v.usuarioIdusuario u
               where sv.status =:status and v.status=:status and s.idstock=:idstock
               and v.cotizacion!=1  
               order by v.idventa DESC
               '
            )->setParameters(['status'=>"1",'idstock'=>$stock['idstock']])->setMaxResults(1);
            $Stockventa= $query->getOneOrNullResult();
            if(!$Stockventa){
                unset($stocks[$keyStock]);
            }else{
                $stocks[$keyStock]['folio']=$Stockventa['folio'];
                $stocks[$keyStock]['fechacreacion']=$Stockventa['fechacreacion'];
                $stocks[$keyStock]['sucursal']=$Stockventa['nombreSucursal'];
                $stocks[$keyStock]['vendedor']=$Stockventa['nombreVendedor']
                    ." ".$Stockventa['apellidopaterno']." "
                    ." ".$Stockventa['apellidomaterno'];
            }
        }
        return $this->render('informes/armazones-vendidos-con-stock.html.twig', [
            'stocks' => $stocks,
        ]);
    }
    /**
     * @Route("/descontar-armazon-sistema/", name="descontar-armazon-sistema")
     */
    public function descontarArmazonSistema(Request $request): Response
    {
        $msj="";
        $exito=false;
        $em=$this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT s.idstock,s.cantidad, p.modelo, s.codigobarras
               FROM App\Entity\Stock s
               inner join s.productoIdproducto p
               where s.status =:status and s.cantidad >0 and p.tipoproducto=:tipoproducto
               '
        )->setParameters(['status'=>"1",'tipoproducto'=>"1"]);
        $stocks= $query->getResult();

        foreach ($stocks as $keyStock=> $stock){

            $query = $em->createQuery(
                'SELECT v.folio, 
v.fechacreacion,sucursal.nombre as nombreSucursal,
u.apellidopaterno, u.apellidomaterno, u.nombre as nombreVendedor
               FROM App\Entity\Stockventa sv
               inner join sv.ventaIdventa v
               inner join sv.stockIdstock s
               inner join v.sucursalIdsucursal sucursal
               inner join v.usuarioIdusuario u
               where sv.status =:status and v.status=:status and s.idstock=:idstock
               and v.cotizacion!=1  
               order by v.idventa DESC
               '
            )->setParameters(['status'=>"1",'idstock'=>$stock['idstock']])->setMaxResults(1);
            $Stockventa= $query->getOneOrNullResult();
            if(!$Stockventa){
                unset($stocks[$keyStock]);
            }else{
                $stocks[$keyStock]['folio']=$Stockventa['folio'];
                $stocks[$keyStock]['fechacreacion']=$Stockventa['fechacreacion'];
                $stocks[$keyStock]['sucursal']=$Stockventa['nombreSucursal'];
                $stocks[$keyStock]['vendedor']=$Stockventa['nombreVendedor']
                    ." ".$Stockventa['apellidopaterno']." "
                    ." ".$Stockventa['apellidomaterno'];


                $Stock = $this->getDoctrine()->getRepository(Stock::class)->findOneBy(array('status'=>"1",'idstock'=>$stock['idstock']));
                $cantidadNueva=0;
                $cantidadActual=$Stock->getCantidad();
                if($cantidadActual > 0){
                    $cantidadNueva=$cantidadActual-1;
                }
                $Stock->setCantidad($cantidadNueva);
                $em->persist($Stock);
                $em->flush();
            }
        }


        //ponermos todos los stock en 1
        return $this->render('informes/resultado-descontar-armazon-sistema.html.twig', [
            'stocks' => $stocks,
        ]);

    }

}
