<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

class ReporteComisionesController extends AbstractController
{
    /**
     * @Route("/r/c", name="app_reporte_comisiones")
     */
    public function index(): Response
    {
        $em = $this->getDoctrine()->getManager();
        $Usuario = $this->getUser();

        $query = $em->createQuery(
            'SELECT e.idempresa AS idempresa, e.nombre AS Empresa
         FROM App\Entity\Usuarioempresapermiso uep
         INNER JOIN uep.empresaIdempresa e
         INNER JOIN uep.usuarioIdusuario u
         where e.status=:status AND u.idusuario=:idusuario
         ORDER BY e.nombre ASC
        '
        )->setParameters(['status' => "1", 'idusuario' => $Usuario->getIdUsuario()]);
        $empresaPermiso = $query->getResult();

        $query = $em->createQuery(
            'SELECT v.folio, tv.nombre AS tipoventa, e.nombre AS Empresa, v.cotizacion AS Cotizacion,
             v.pagado AS montodeventa, pg.tipopago AS tipodepago, 
            pg.mesesintereses AS mesessinintereses, s.nombre AS Sucursal, c.nombre AS Cliente, v.idventa AS id
            FROM App\Entity\Pago pg
            INNER JOIN pg.ventaIdventa v
            INNER JOIN v.clienteIdcliente c
            INNER JOIN v.tipoventaIdtipoventa tv
            INNER JOIN v.sucursalIdsucursal s
            INNER JOIN s.empresaIdempresa e
            WHERE v.status = :status AND v.cotizacion = :cotizacion
            order by v.fechaactualizacion asc'
        )->setParameters(['status' => "1", 'cotizacion' => "0"]);

        $query->setMaxResults(30);

        $pagoventa = $query->getResult();


        return $this->render('reporte_comisiones/index.html.twig', [
            'empresaPermiso' => $empresaPermiso,
            'pagoventa' => $pagoventa,
        ]);
    }

    /**
     * @Route("/reporte/comisiones/date", name="reporte-comisiones-date", methods={"POST"})
     */

    public function pagoVenta(Request $request): JsonResponse
    {

        $em = $this->getDoctrine()->getManager();
        $data = json_decode($request->getContent(), true);

        $fechaInicio = \DateTime::createFromFormat('d/m/Y', $data['fechaInicio']);
        $fechaFin = \DateTime::createFromFormat('d/m/Y', $data['fechaFin']);

        $whereDateRangoDia = " and v.fechaventa >= :fechaInicio  and v.fechaventa <= :fechaFin ";

        $consultaDQL = "SELECT v FROM App\Entity\Venta v WHERE v.status = :status
    AND v.cotizacion = '0' 
    AND (v.fechaventa BETWEEN :fechaInicio AND :fechaFin OR v.fechaventa IS NULL) $whereDateRangoDia";

        $ventas = $em->createQuery($consultaDQL)
            ->setParameter('status', 1)
            ->setParameter('fechaInicio', $fechaInicio)
            ->setParameter('fechaFin', $fechaFin)
            ->setMaxResults(40)
            ->getResult();


        $ventasConPagos = [];

        foreach ($ventas as $venta) {
            $fechaVenta = $venta->getFechaventa();

            if ($fechaVenta === null) {
                $fechaVenta = $venta->getFechacreacion();

                if ($fechaVenta === null) {
                    $fechaVenta = $venta->getFecha();
                }

                if ($fechaVenta !== null) {
                    $fechaVentaFormateada = $fechaVenta->format('d/m/Y H:i:s');
                } else {
                    $fechaVentaFormateada = 'Fecha no disponible';
                }
            } else {
                $fechaVentaFormateada = $fechaVenta->format('d/m/Y H:i:s');
            }

            $ventaArray = [
                'folio' => $venta->getFolio(),
                'Empresa' => $venta->getSucursalIdsucursal()->getEmpresaIdempresa()->getNombre(),
                'Sucursal' => $venta->getSucursalIdsucursal()->getNombre(),
                'Cliente' => $venta->getClienteIdcliente()->getNombre(),
                'Fechaventa' => $venta->getFechaventa(),
                'Fecha' => $fechaVentaFormateada,
                'montodeventa' => $venta->getPagado(),
                'Pagos' => []
            ];

            $pagos = $em->getRepository('App\Entity\Pago')->findBy([
                'ventaIdventa' => $venta->getIdventa(),
            ], ['fecha' => 'DESC'], 40);

            $ultimoPago = end($pagos);
            if ($ultimoPago !== false) {
                $ventaArray['UltimoPago'] = $ultimoPago->getFecha()->format('Y-m-d H:i:s');
            } else {
                $ventaArray['UltimoPago'] = 'Pago no disponible';
            }


            foreach ($pagos as $pago) {
                $tipoPago = $pago->getTipoPago();
                $fechaPago = $pago->getFecha();

                if ($fechaPago !== null) {
                    $fechaPagoFormateada = $fechaPago->format('Y-m-d H:i:s');
                } else {
                    $fechaPagoFormateada = 'Fecha no disponible';
                }

                if (!isset($ventaArray['Pagos'][$tipoPago])) {
                    $ventaArray['Pagos'][$tipoPago] = [];
                }

                $ventaArray['Pagos'][$tipoPago][] = [
                    'idpago' => $pago->getIdpago(),
                    'tipopago' => $pago->getTipopago(),
                    'monto' => $pago->getMonto(),
                    'fecha' => $fechaPagoFormateada
                ];
            }
            $ventasConPagos[] = $ventaArray;
        }
        return new JsonResponse($ventasConPagos);
    }
}
