<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Dompdf\Dompdf;
use Dompdf\Options;
use Dompdf\Option;
use Dompdf\Exception as DomException;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mime\Attachment;
use App\Entity\Stockventaordenlaboratorio;


class OrdenCompraController extends AbstractController
{

    /**
     * @Route("/orden", name="app_orden_compra")
     */
    public function index(): Response
    {
        return $this->render('orden_compra/indexOrden_compra.html.twig', [
            'controller_name' => 'OrdenCompraController',
        ]);
    }


    /**
     * @Route("/orden/filtrosOrdenesCompra", name="filtros_Ordenes_Compra")
     */

    public function filtrosOrdenesCompra()
    {
        // Permite interactuar con la base de datos usando objetos en lugar de SQL directamente.
        $em = $this->getDoctrine()->getManager();
        // Definiendo variables que serán usadas para enviar al frontend.
        $success = false;
        $message = "";

        $query = $em->createQuery(
            'SELECT e.nombre AS Empresa, e.idempresa
             FROM App\Entity\Empresa e
             WHERE e.status = :status'
        )->setParameters(['status' => '1']);
        $filtrosEmpresas = $query->getResult();


        $query = $em->createQuery(
            'SELECT s.nombre AS Sucursal, s.idsucursal
                 FROM App\Entity\Sucursal s
                 WHERE s.status = :status'
        )->setParameters(['status' => '1']);
        $filtroSucursales = $query->getResult();


        return $this->render('orden_compra/filtros_Ordenes_Compra.html.twig', [
            'filtrosEmpresas' => $filtrosEmpresas,
            'filtroSucursales' => $filtroSucursales,
        ]);
    }

    /**
     * MODAL CON PROVEEDOR, TIPO DE PAGO Y PLAZO DE PAGO
     * @Route("/orden/modal-orden-compra", name="modal-orden-compra")
     */


    public function modalOrdenCompra(Request $request)
    {
        // Permite interactuar con la base de datos usando objetos en lugar de SQL directamente.
        $em = $this->getDoctrine()->getManager();

        // Definiendo variables que serán usadas para enviar al frontend.
        $success = false;
        $message = "";
        $idproveedor = $request->get('idproveedor');
        $idsucursal = $request->get('idsucursal');


        $query = $em->createQuery(
            'SELECT p.idproveedor, p.nombre AS NombreProveedor, p.calle AS Calle, p.ciudad AS Ciudad, p.estado AS Estado, p.codigopostal AS CodigoPostal,
                    p.pais AS Pais, p.telefono AS Telefono, p.correoelectronico AS Correo, p.numeroexterior AS Exterior, p.numerointerior AS Interior,
                    p.colonia AS Colonia, p.municipio AS Municipio, p.rfc AS RFC, p.razonsocial AS RazonSocial
            FROM App\Entity\Proveedor p
            WHERE p.status = :status'
        )->setParameters(['status' => '1']);
        $proveedores = $query->getResult();

        $query = $em->createQuery(
            'SELECT 
                pc.idproveedorcontacto, 
                pc.nombre AS NombreContacto, 
                pe.email AS NombreEmail,
                pt.telefono AS NombreTelefono
             FROM App\Entity\Proveedorcontacto pc
             LEFT JOIN App\Entity\Proveedoremail pe WITH pc.idproveedorcontacto = pe.proveedorcontactoIdproveedorcontacto
             LEFT JOIN App\Entity\Proveedortelefono pt WITH pc.idproveedorcontacto = pt.proveedorcontactoIdproveedorcontacto
             WHERE pc.status = :status AND pc.proveedorIdproveedor= :idproveedor'
        )->setParameters(['status' => '1', 'idproveedor' => $idproveedor]);
        $contactosConEmail = $query->getResult();

        return $this->render('orden_compra/modal-orden-compra.html.twig', [
            'proveedores' => $proveedores,
            'idsucursal' => $idsucursal,
            'contactosConEmail' => $contactosConEmail,
        ]);
    }

    /**
     * @Route("/orden/contactos-proveedores", name="contactos-proveedores")
     */

    public function contactosProveedores(Request $request)
    {
        // Permite interactuar con la base de datos usando objetos en lugar de SQL directamente.
        $em = $this->getDoctrine()->getManager();
        // Definiendo variables que serán usadas para enviar al frontend.
        $success = false;
        $message = "";
        $idproveedor = $request->get('idproveedor');

        $query = $em->createQuery(
            'SELECT p.idproveedor, p.nombre AS NombreProveedor, p.calle AS Calle, p.ciudad AS Ciudad, p.estado AS Estado, p.codigopostal AS CodigoPostal,
                    p.pais AS Pais, p.telefono AS Telefono, p.correoelectronico AS Correo, p.numeroexterior AS Exterior, p.numerointerior AS Interior,
                    p.colonia AS Colonia, p.municipio AS Municipio, p.rfc AS RFC, p.razonsocial AS RazonSocial
            FROM App\Entity\Proveedor p
            WHERE p.status = :status'
        )->setParameters(['status' => '1']);
        $proveedores = $query->getResult();

        $query = $em->createQuery(
            'SELECT 
                pc.idproveedorcontacto, 
                pc.nombre AS NombreContacto, 
                pe.email AS NombreEmail,
                pt.telefono AS NombreTelefono
             FROM App\Entity\Proveedorcontacto pc
             LEFT JOIN App\Entity\Proveedoremail pe WITH pc.idproveedorcontacto = pe.proveedorcontactoIdproveedorcontacto
             LEFT JOIN App\Entity\Proveedortelefono pt WITH pc.idproveedorcontacto = pt.proveedorcontactoIdproveedorcontacto
             WHERE pc.status = :status AND pc.proveedorIdproveedor= :idproveedor'
        )->setParameters(['status' => '1', 'idproveedor' => $idproveedor]);

        $contactosConEmail = $query->getResult();

        return $this->render('orden_compra/contactos-proveedores.html.twig', [
            'contactosConEmail' => $contactosConEmail,
        ]);
    }


    /**
     * @Route("/orden/obtener-ordenes-compra", name="obtener-ordenes-compra")
     */

    public function obtenerOrdenesCompra(Request $request): Response
    {
        $exito = true;
        $ordenesdeCompra = [];
        $mensajeError = '';
        $hayResultados = false;
        try {

            $em = $this->getDoctrine()->getManager();
            $parametros = [];
            $whereSucursal = "";
            $idsucursal = $request->get("idsucursal");
            $idEmpresa = $request->query->get('idempresa');
            $idsucursal = $request->get("idsucursal");

            $fechaInicio = \DateTime::createFromFormat('d/m/Y', $request->get("fechaInicio"));
            $fechaFin = \DateTime::createFromFormat('d/m/Y', $request->get("fechaFin"));

            if (!$fechaInicio || !$fechaFin) {
                $fechaInicio = new \DateTime("now");
                $fechaFin = new \DateTime("now");
            }

            $parametros['fechaInicio'] = $fechaInicio->format('Y-m-d') . " 00:00:00";
            $parametros['fechaFin'] = $fechaFin->format('Y-m-d') . " 23:59:00";

            $whereDateRangoDia = "and sv.creacion >= :fechaInicio  and sv.creacion<= :fechaFin ";

            // Si se ha seleccionado "TODAS LAS SUCURSALES", ajusta la consulta y los parámetros
            if ($idsucursal !== "todasSucursales") {
                $whereSucursal = 'AND s.idsucursal = :idsucursal';
                $parametros['idsucursal'] = $idsucursal;
            } else {
                $whereSucursal = '';
            }

            $parametros['status'] = 1;

            $query = $em->createQuery(
                'SELECT 
            svol.idstockventaordenlaboratorio AS IdStockventaOrdenLaboratorio,
            svol.creacion AS Fecha, v.folio AS Folio, s.nombre AS Sucursal, tv.nombre AS TipodeVenta, 
            c.nombre AS PrimerNombre, c.apellidopaterno AS ApellidoPaterno, c.apellidomaterno AS ApellidoMaterno, v.beneficiario AS Beneficiario, 
            c.numeroempleado AS NumerodeEmpleado, u.nombre AS Unidad, p.nombre AS Producto, m.nombre AS Marca, p.modelo AS Modelo,
            dl.nombre AS DisenoLente, mt.nombre AS Material, tr.nombre AS Tratamiento, ol.addordenlaboratorio AS AddOrden,
            ol.esferaod AS EsferaD, ol.cilindrood AS CilD, ol.ejeod AS EjeD, ol.esferaoi AS EsfI, ol.cilindrooi AS CilI, ol.ejeoi AS EjeI,
            ol.observaciones AS Observaciones, us.nombre AS Nombre, us.apellidopaterno AS ApellidoPaternoUsuario, us.apellidomaterno AS ApellidoMaternoUsuario,
            fe.etapa AS Etapa, ol.folioautorizacion AS FolioAutorizacion, ol.statusautorizacion AS StatusAutorizacion, ol.recepcion AS Recepcion, ol.fechalaboratorio AS FechaEntradaLaboratorio,
            ol.statuslaboratorio AS StatusLaboratorio, ol.fechasalidalaboratorio AS FechaSalidaLaboratorio, sv.garantia AS Garantia, s.idsucursal, ol.base AS Base
    
            FROM App\Entity\Stockventaordenlaboratorio svol
            INNER JOIN svol.stockventaIdstockventa sv
            INNER JOIN svol.ordenlaboratorioIdordenlaboratorio ol
            LEFT JOIN ol.disenolenteIddisenolente dl
            LEFT JOIN ol.materialIdmaterial mt
            LEFT JOIN ol.tratamientoIdtratamiento tr
            LEFT JOIN ol.flujoexpedienteIdflujoexpediente fe
            LEFT JOIN sv.ventaIdventa v
            LEFT JOIN v.usuarioIdusuario us
            LEFT JOIN sv.stockIdstock st
            LEFT JOIN v.tipoventaIdtipoventa tv
            LEFT JOIN v.clienteIdcliente c
            LEFT JOIN v.unidadIdunidad u
            LEFT JOIN v.sucursalIdsucursal s
            LEFT JOIN st.productoIdproducto p
            LEFT JOIN p.marcaIdmarca m
            WHERE ol.status = :status AND sv.status = 1 AND svol.alreadyset = 1
            ' . $whereSucursal . '
            AND svol.creacion >= :fechaInicio AND svol.creacion <= :fechaFin
            ORDER BY svol.creacion DESC'
            )->setParameters($parametros);

            $ordenesdeCompra = $query->getResult();

            if ($ordenesdeCompra) {
                $exito = true;
            }
            $hayResultados = !empty($ordenesdeCompra);
        } catch (\Exception $e) {
            $mensajeError = $e->getMessage();
        }

        return $this->render('orden_compra/tableOrdenCompras.html.twig', [
            'ordenesdeCompra' => $ordenesdeCompra ?? [],
            'exito' => $exito,
            'hayResultados' => !empty($ordenesdeCompra),
            'mensajeError' => $mensajeError,
        ]);
    }

    /**
     * @Route("/obtener-compras-pdf", name="obtener-compras-pdf")
     */

    public function obtenerOrdenesCompraPdf(Request $request, MailerInterface $mailer)
    {

        $ordenesdeCompra = [];
        $proveedor = $request->get('idproveedor');
        $tipoPago = $request->get('idtipopago');
        $plazoPago = $request->get('plazopago');
        $checkboxesTotal = $request->get('checkboxesTotal');
        $seleccionados = $request->get('seleccionados');
        $idempresa = $request->get('idempresa');
        $idsucursal = $request->get('idsucursal');
        $contador = 0;
        $result = "";


        if (!$proveedor || $proveedor === "-1") {
            return new JsonResponse(['error' => 'Falta seleccionar un proveedor.'], 400);
        }

        if (!$tipoPago || $tipoPago === "-1") {
            return new JsonResponse(['error' => 'Falta seleccionar un tipo de pago.'], 400);
        }

        if (!$plazoPago || $plazoPago === "-1") {
            return new JsonResponse(['error' => 'Falta seleccionar un plazo de pago.'], 400);
        }

        $whereSeleccionados = "";

        if (count($seleccionados) > 0) {
            $whereSeleccionados = " and (";

            for ($i = 0; $i < count($seleccionados); $i++) {
                $whereSeleccionados .= " pc.idproveedorcontacto=" . $seleccionados[$i];

                if ($i != count($seleccionados) - 1) {
                    $whereSeleccionados .= " or ";
                }
            }

            $whereSeleccionados .= ") ";
        }

        $whereConditions = 'e.status = :status AND e.idempresa = :idempresa';


        if ($idsucursal !== "todasSucursales") {
            $whereConditions .= ' AND s.idsucursal = :idsucursal';
            $parameters = [
                'status' => '1',
                'idempresa' => $idempresa,
                'idsucursal' => $idsucursal,
            ];
        } else {
            $parameters = [
                'status' => '1',
                'idempresa' => $idempresa,
            ];
        }

        $em = $this->getDoctrine()->getManager();
        $query = $em->createQuery(
            'SELECT
            svol.idstockventaordenlaboratorio AS IdStockventaOrdenLaboratorio, svol AS returnObject,
            svol.creacion, v.folio AS Folio,s.nombre AS Sucursal,tv.nombre AS TipodeVenta, st.codigobarras AS SKU,
            c.nombre AS PrimerNombre,c.apellidopaterno AS ApellidoPaterno,c.apellidomaterno AS ApellidoMaterno,v.beneficiario AS Beneficiario, 
            c.numeroempleado AS NumerodeEmpleado,u.nombre AS Unidad,p.nombre AS Producto, m.nombre AS Marca, p.modelo AS Modelo,
            dl.nombre AS DisenoLente, mt.nombre AS Material, tr.nombre AS Tratamiento, ol.addordenlaboratorio AS AddOrden, ol.base AS Base,
            ol.esferaod AS EsferaD,ol.cilindrood AS CilD, ol.ejeod AS EjeD, ol.esferaoi AS EsfI, ol.cilindrooi AS CilI, ol.ejeoi AS EjeI,
            ol.observaciones AS Observaciones , us.nombre AS Nombre, us.apellidopaterno AS ApellidoPaternoUsuario, us.apellidomaterno AS ApellidoMaternoUsuario,
            fe.etapa AS Etapa, ol.folioautorizacion AS FolioAutorizacion, ol.statusautorizacion AS StatusAutorizacion, ol.recepcion AS Recepcion, ol.fechalaboratorio AS FechaEntradaLaboratorio,
            ol.statuslaboratorio AS StatusLaboratorio, ol.fechasalidalaboratorio AS FechaSalidaLaboratorio, sv.garantia AS Garantia, s.idsucursal
    
        FROM App\Entity\Stockventaordenlaboratorio svol
        INNER JOIN svol.stockventaIdstockventa sv
        INNER JOIN svol.ordenlaboratorioIdordenlaboratorio ol
        LEFT JOIN ol.disenolenteIddisenolente dl
        LEFT JOIN ol.materialIdmaterial mt
        LEFT JOIN ol.tratamientoIdtratamiento tr
        LEFT JOIN ol.flujoexpedienteIdflujoexpediente fe
        LEFT JOIN sv.ventaIdventa v 
        LEFT JOIN v.usuarioIdusuario us
        LEFT JOIN sv.stockIdstock st
        LEFT JOIN v.tipoventaIdtipoventa tv
        LEFT JOIN v.clienteIdcliente c
        LEFT JOIN v.unidadIdunidad u
        LEFT JOIN v.sucursalIdsucursal s
        LEFT JOIN st.productoIdproducto p
        LEFT JOIN p.marcaIdmarca m
        WHERE ol.status =:status AND v.folio IN (:folio)
        GROUP BY st.codigobarras
        '
        )->setParameters(['status' => '1', 'folio' => $checkboxesTotal]);
        $ordenesdeCompraPdf = $query->getResult();


        /*foreach ($ordenesdeCompraPdf as $orden) {
            $changeObject = $orden['returnObject'];
            $changeObject->setAlreadyset(0);
            $em->persist($changeObject);
        }
        $em->flush();*/


        $query = $em->createQuery(
            'SELECT
                    p.idproveedor, p.nombre AS NombreProveedor, p.calle AS Calle, p.ciudad AS Ciudad, p.estado AS Estado, p.codigopostal AS CodigoPostal,
                    p.pais AS Pais, p.telefono AS Telefono, p.correoelectronico AS Correo, p.numeroexterior AS Exterior, p.numerointerior AS Interior,
                    p.colonia AS Colonia, p.municipio AS Municipio, p.rfc AS RFC, p.razonsocial AS RazonSocial, pc.idproveedorcontacto, pc.nombre, pc.apellidopaterno, pc.apelliidomaterno
             FROM App\Entity\Proveedorcontacto pc
             LEFT JOIN pc.proveedorIdproveedor p
             WHERE pc.status =:status AND p.idproveedor =:idproveedor' . $whereSeleccionados
        )->setParameters(['status' => '1', 'idproveedor' => $proveedor]);
        $contactosProveedores = $query->getResult();

        $query = $em->createQuery(
            'SELECT s.nombre AS NombreSucursal, s.idsucursal, e.idempresa, s.direccion AS DireccionSEmpresa, e.nombre AS EmpresaNombre, e.razonsocial AS RazonSocial, e.rfc AS RFC
             FROM App\Entity\Sucursal s
             LEFT JOIN s.empresaIdempresa e
             WHERE ' . $whereConditions
        )->setParameters($parameters);

        // Ejecutar la consulta y obtener resultados
        $empresaSucursales = $query->getResult();


        // Renderizo una vista en una variable
        $html = $this->renderView('orden_compra/ordenesCompra.html.twig', [
            'ordenesdeCompraPdf' => $ordenesdeCompraPdf,
            'tipoPago' => $tipoPago,
            'plazoPago' => $plazoPago,
            'contactosProveedores' => $contactosProveedores,
            'empresaSucursales' => $empresaSucursales,
        ]);

        // Configuración DOMpdf
        $options = new Options();
        $options->set('defaultFont', 'Arial');
        $options->set('isHtml5ParserEnabled', true);
        $dompdf = new Dompdf($options);
        $dompdf->loadHtml($html);

        // Renderiza el PDF
        $dompdf->render();


        // Envía la respuesta en formato PDF
        return new Response($dompdf->output(), 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="ordenesCompra.pdf"',
        ]);
    }


    /**
     * @Route("/send-email-pdf", name="send-email-pdf")
     */

    public function sendEmailPdf(Request $request, MailerInterface $mailer)
    {

        $ordenesdeCompra = [];
        $proveedor = $request->get('idproveedor');
        $tipoPago = $request->get('idtipopago');
        $plazoPago = $request->get('plazopago');
        $checkboxesTotal = $request->get('checkboxesTotal');
        $seleccionados = $request->get('seleccionados');
        $idempresa = $request->get('idempresa');
        $idsucursal = $request->get('idsucursal');

        $whereSeleccionados = "";

        if (count($seleccionados) > 0) {
            $whereSeleccionados = " and (";

            for ($i = 0; $i < count($seleccionados); $i++) {
                $whereSeleccionados .= " pc.idproveedorcontacto=" . $seleccionados[$i];

                if ($i != count($seleccionados) - 1) {
                    $whereSeleccionados .= " or ";
                }
            }

            $whereSeleccionados .= ") ";
        }


        $em = $this->getDoctrine()->getManager();
        $query = $em->createQuery(
            'SELECT 
            svol.idstockventaordenlaboratorio AS IdStockventaOrdenLaboratorio, 
            svol.creacion, v.folio,s.nombre AS Sucursal,tv.nombre AS TipodeVenta, 
            c.nombre AS PrimerNombre,c.apellidopaterno AS ApellidoPaterno,c.apellidomaterno AS ApellidoMaterno,v.beneficiario AS Beneficiario, 
            c.numeroempleado AS NumerodeEmpleado,u.nombre AS Unidad,p.nombre AS Producto, m.nombre AS Marca, p.modelo AS Modelo,
            dl.nombre AS DisenoLente, mt.nombre AS Material, tr.nombre AS Tratamiento, ol.addordenlaboratorio AS AddOrden,
            ol.esferaod AS EsferaD,ol.cilindrood AS CilD, ol.ejeod AS EjeD, ol.esferaoi AS EsfI, ol.cilindrooi AS CilI, ol.ejeoi AS EjeI,
            ol.observaciones AS Observaciones , us.nombre AS Nombre, us.apellidopaterno AS ApellidoPaternoUsuario, us.apellidomaterno AS ApellidoMaternoUsuario,
            fe.etapa AS Etapa, ol.folioautorizacion AS FolioAutorizacion, ol.statusautorizacion AS StatusAutorizacion, ol.recepcion AS Recepcion, ol.fechalaboratorio AS FechaEntradaLaboratorio,
            ol.statuslaboratorio AS StatusLaboratorio, ol.fechasalidalaboratorio AS FechaSalidaLaboratorio, sv.garantia AS Garantia, s.idsucursal
    
        FROM App\Entity\Stockventaordenlaboratorio svol
        INNER JOIN svol.stockventaIdstockventa sv
        INNER JOIN svol.ordenlaboratorioIdordenlaboratorio ol
        LEFT JOIN ol.disenolenteIddisenolente dl
        LEFT JOIN ol.materialIdmaterial mt 
        LEFT JOIN ol.tratamientoIdtratamiento tr
        LEFT JOIN ol.flujoexpedienteIdflujoexpediente fe
        LEFT JOIN sv.ventaIdventa v 
        LEFT JOIN v.usuarioIdusuario us
        LEFT JOIN sv.stockIdstock st
        LEFT JOIN v.tipoventaIdtipoventa tv
        LEFT JOIN v.clienteIdcliente c
        LEFT JOIN v.unidadIdunidad u
        LEFT JOIN v.sucursalIdsucursal s
        LEFT JOIN st.productoIdproducto p
        LEFT JOIN p.marcaIdmarca m
        WHERE ol.status =:status AND v.folio IN (:folio)
        '
        )->setParameters(['status' => '1', 'folio' => $checkboxesTotal]);
        $ordenesdeCompraPdf = $query->getResult();

        $query = $em->createQuery(
            'SELECT
                    p.idproveedor, p.nombre AS NombreProveedor, p.calle AS Calle, p.ciudad AS Ciudad, p.estado AS Estado, p.codigopostal AS CodigoPostal,
                    p.pais AS Pais, p.telefono AS Telefono, p.correoelectronico AS Correo, p.numeroexterior AS Exterior, p.numerointerior AS Interior,
                    p.colonia AS Colonia, p.municipio AS Municipio, p.rfc AS RFC, p.razonsocial AS RazonSocial, pc.idproveedorcontacto, pc.nombre, pc.apellidopaterno, pc.apelliidomaterno
             FROM App\Entity\Proveedorcontacto pc
             LEFT JOIN pc.proveedorIdproveedor p
             WHERE pc.status =:status AND p.idproveedor =:idproveedor' . $whereSeleccionados
        )->setParameters(['status' => '1', 'idproveedor' => $proveedor]);
        $contactosProveedores = $query->getResult();

        $query = $em->createQuery(
            'SELECT s.nombre AS NombreSucursal, s.idsucursal, e.idempresa, s.direccion AS DireccionSEmpresa, e.nombre AS EmpresaNombre, e.razonsocial AS RazonSocial, e.rfc AS RFC
             FROM App\Entity\Sucursal s
             LEFT JOIN s.empresaIdempresa e
             WHERE e.status =:status AND e.idempresa =:idempresa AND s.idsucursal =:idsucursal'
        )->setParameters(['status' => '1', 'idempresa' => $idempresa, 'idsucursal' => $idsucursal]);
        $empresaSucursales = $query->getResult();


        // Renderizo una vista en una variable
        $html = $this->renderView('orden_compra/ordenesCompra.html.twig', [
            'ordenesdeCompraPdf' => $ordenesdeCompraPdf,
            'tipoPago' => $tipoPago,
            'plazoPago' => $plazoPago,
            'contactosProveedores' => $contactosProveedores,
            'empresaSucursales' => $empresaSucursales,
        ]);

        // Configuración DOMpdf
        $options = new Options();
        $options->set('defaultFont', 'Arial');
        $options->set('isHtml5ParserEnabled', true);
        $dompdf = new Dompdf($options);
        $dompdf->loadHtml($html);

        // Renderiza el PDF
        $dompdf->render();

        // Obtienes el contenido del PDF
        $pdfOutput = $dompdf->output();

        // Creas el correo electrónico
        $email = (new Email())
            ->from('<EMAIL>')
            ->to('<EMAIL>')
            ->subject('Orden de Compra')
            ->html('Contenido del correo')
            ->attach($pdfOutput, 'orden_compra.pdf', 'application/pdf');

        // Envías el correo
        $mailer->send($email);

        // Envía la respuesta en formato PDF
        return new Response($dompdf->output(), 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="ordenesCompra.pdf"',
            'status' => 'success',
            'message' => 'Correo enviado con éxito.'
        ]);
    }

    /**
     * This function is a controller that creates a ProveedorContacto form.
     *
     * @Route("/add/contact-email-phone/{idproveedor}", name="add-contact-email-phone")
     *
     * @param Request $request The object representing the HTTP request.
     * @param $idproveedor The ID of the provider to which the contact is to be added.
     *
     * @return Response An HTTP response.
     */


    public function addContactEmailPhone(Request $request, $idproveedor): Response
    {
        //Variables iniciales.

        $success = false; //Variable que dice si la operación ha tenido éxito.
        $message = ""; // Mensaje para mostrar al usuario.
        $em = $this->getDoctrine()->getManager(); //Gestor de entidades de Doctrine.


        $Proveedor = $request->request->get('idproveedor');

        $idproveedor = $request->get('idproveedor');


        // Crea un formulario basado de ProveedorContactoType.
        $form = $this->createForm(ProveedorContactoType::class);
        $form->handleRequest($request); // Maneja la solicitud y rellena el formulario con datos de la solicitud.

        $proveedorContacto = "";


        try {
            if ($form->isSubmitted() && $form->isValid()) {

                // Si el formulario es enviado y es válido, sigue a guardar los datos.
                $proveedorContacto = $form->getData();

                // Ponemos la fecha de creación y actualización.
                $now = new \DateTime();
                $proveedorContacto->setCreacion($now);
                $proveedorContacto->setActualizacion($now);

                // Asocia el contacto al proveedor usando el ID del proveedor.
                $Proveedor = $em->getRepository(Proveedor::class)->findOneBy(array('idproveedor' => $idproveedor));

                if ($Proveedor)
                    $proveedorContacto->setProveedorIdproveedor($Proveedor);

                $em->persist($proveedorContacto); // Guarda el contacto en la base de datos.
                $em->flush(); // Confirma los cambios.
                $success = true;
            }
        } catch (\Exception $e) {
            $message = $e->getMessage(); // Si ocurre un error, guarda el mensaje del error.
        }

        // Retorna la vista, pasando las variables necesarias para mostrar la página.
        return $this->render('orden_compra/modal-orden-compra.html.twig', [
            'success' => $success,
            'idproveedor' => $idproveedor,
            'message' => $message,
            'form' => $form->createView()
        ]);
    }
}
