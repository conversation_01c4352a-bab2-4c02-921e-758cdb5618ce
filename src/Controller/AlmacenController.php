<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/almacen")
 */
class AlmacenController extends AbstractController
{
    /**
     * @Route("/stock", name="almacen-stock")
     */

    public function index(): Response
    {
        $em = $this->getDoctrine()->getManager();

        $Usuario = $this->getUser();
        $query = $em->createQuery(
            'SELECT e.idempresa, e.nombre
               FROM App\Entity\Usuarioempresapermiso uem
               inner join uem.empresaIdempresa e
               inner join uem.usuarioIdusuario u
               where e.status =:status and u.idusuario=:idusuario order by e.nombre asc 
               '
        )->setParameters(['status' => "1", 'idusuario' => $Usuario->getIdUsuario()]);
        $empresas = $query->getResult();


        return $this->render('almacen/stock.html.twig', [
            'empresas' => $empresas
        ]);
    }


    //LENOOOON

    /**
     * @Route("/almacen-obtener-informacion-stock", name="almacen-obtener-informacion-stock")
     */
    public function obtenerInformacionStock(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();

        $clases = $request->get("clases");

        $sucursales = $request->get("sucursales");

        $categorias = $request->get("categorias");

        $marcas = $request->get("marcas");

        $tags = $request->get("tags");

        $modelo = $request->get("modelo");

        $idempresa = $request->get("idempresa");

        $parametros['status'] = 1;
        $parametros['tipoproducto'] = 1;

        $whereSucursal = "";

        //Sucursales
        if (isset($sucursales[0])) {

            $whereSucursal = " and (";

            for ($i = 0; $i < count($sucursales); $i++) {

                $whereSucursal .= " suc.idsucursal=" . $sucursales[$i];

                if ($i != count($sucursales) - 1) $whereSucursal .= " or ";
            }
            $whereSucursal .= ") ";
        }


        //Clases
        $whereClase = "";

        if (isset($clases[0])) {

            $whereClase = " and (";

            for ($i = 0; $i < count($clases); $i++) {

                $whereClase .= " c.idclase=" . $clases[$i];

                if ($i != count($clases) - 1) $whereClase .= " or ";
            }
            $whereClase .= ") ";
        }

        //Categorias
        $whereCategoria = "";

        if (isset($categorias[0])) {

            $whereCategoria = " and (";

            for ($i = 0; $i < count($categorias); $i++) {

                $whereCategoria .= " sc.idcategoria=" . $categorias[$i];

                if ($i != count($categorias) - 1) $whereCategoria .= " or ";
            }
            $whereCategoria .= ") ";
        }


        //Marcas
        $whereMarca = "";

        if (isset($marcas[0])) {

            $whereMarca = " and (";

            for ($i = 0; $i < count($marcas); $i++) {

                $whereMarca .= " m.idmarca=" . $marcas[$i];

                if ($i != count($marcas) - 1) $whereMarca .= " or ";
            }
            $whereMarca .= ") ";
        }


        $whereModelo = "";

        //Modelo
        if ($modelo != "") {
            $whereModelo = " and p.modelo ='" . $modelo . "' ";
        }

        //TAGS
        $tmpTags = ($tags) ? $tags : [];

        // Inicializa $whereTag con una lista vacía que incluirá -1 si no hay tags válidos
        $whereTag = "(-1)";  // Asegura que siempre hay al menos un ID, incluso si es inválido, para prevenir errores SQL.

        // Si hay tags en $tmpTags, reconstruye $whereTag con esos tags
        if (!empty($tmpTags)) {
            $whereTag = "(" . implode(',', $tmpTags) . ")";
        }


        $query = $em->createQuery(
            'SELECT p.idproducto
                FROM App\Entity\Productotag pt
                INNER JOIN  pt.productoIdproducto p
                INNER JOIN  pt.tagIdtag t
                WHERE p.tipoproducto = :tipoproducto AND t.idtag IN ' . $whereTag . '
                GROUP BY p.idproducto, pt.tagIdtag, pt.productoIdproducto
                HAVING COUNT(DISTINCT t.idtag) = :tagCount
            '
        )->setParameters(['tagCount' => count($tmpTags), 'tipoproducto' => "1"]);
        $productsWTags = $query->getResult();

        //PRODUCTS
        $whereProduct = "";
        if (isset($productsWTags[0])) {

            $whereProduct = " and (";

            for ($i = 0; $i < count($productsWTags); $i++) {

                $whereProduct .= " p.idproducto=" . $productsWTags[$i]['idproducto'];

                if ($i != count($productsWTags) - 1) $whereProduct .= " or ";
            }
            $whereProduct .= ") ";
        } else if (count($tmpTags) > 0) $whereProduct = " and ( p.idproducto=-1) ";

        //obtenemos el stock por marca
        $query = $em->createQuery(
            'SELECT SUM(s.cantidad) as cantidad, m.nombre as marca
               FROM App\Entity\Stock s
               inner join s.sucursalIdsucursal suc
               INNER JOIN  s.productoIdproducto p
               INNER JOIN  p.marcaIdmarca m
               INNER JOIN  p.categoriaIdcategoria sc
               INNER JOIN  sc.claseIdclase c
               where  s.status =:status and p.tipoproducto=:tipoproducto and s.cantidad > 0 ' . $whereModelo . $whereSucursal . $whereClase . $whereCategoria . $whereMarca . $whereProduct . ' group by m.nombre order by cantidad DESC
               '
        )->setParameters($parametros);
        $stock = $query->getResult();

        //de linea por categoria

        $query = $em->createQuery(
            'SELECT SUM(s.cantidad) as cantidad, c.nombre as categoria, c.idclase
               FROM App\Entity\Stock s
                inner join s.sucursalIdsucursal suc
               INNER JOIN  s.productoIdproducto p
               INNER JOIN  p.marcaIdmarca m 
               INNER JOIN  p.categoriaIdcategoria sc
               INNER JOIN  sc.claseIdclase c
               where s.status =:status and p.tipoproducto=:tipoproducto  and s.cantidad > 0 ' . $whereModelo . $whereSucursal . $whereClase . $whereCategoria . $whereMarca . $whereProduct . ' group by c.nombre, c.idclase order by cantidad DESC
               '
        )->setParameters($parametros);
        $stockCategoria = $query->getResult();

        $query = $em->createQuery(
            'SELECT SUM(s.cantidad) as cantidad, sc.nombre as subcategoria, c.idclase, sc.idcategoria
               FROM App\Entity\Stock s
                inner join s.sucursalIdsucursal suc
               INNER JOIN  s.productoIdproducto p
               INNER JOIN  p.marcaIdmarca m 
               INNER JOIN  p.categoriaIdcategoria sc
               INNER JOIN  sc.claseIdclase c

               where s.status =:status and p.tipoproducto=:tipoproducto  and s.cantidad > 0 ' . $whereModelo . $whereSucursal . $whereCategoria . $whereMarca . $whereProduct . ' group by sc.nombre, sc.idcategoria order by cantidad DESC
               '
        )->setParameters($parametros);
        $stockSubcategoria = $query->getResult();

        $query = $em->createQuery(
            'SELECT SUM(s.cantidad) as cantidad, m.nombre as nombre, suc.nombre as sucursal
               FROM App\Entity\Stock s
                inner join s.sucursalIdsucursal suc
               INNER JOIN  s.productoIdproducto p
               INNER JOIN  p.marcaIdmarca m 
               INNER JOIN  p.categoriaIdcategoria sc

               where s.status =:status and p.tipoproducto=:tipoproducto  and s.cantidad > 0 ' . $whereModelo . $whereSucursal . $whereCategoria . $whereMarca . $whereProduct . ' group by suc.idsucursal, m.nombre order by cantidad DESC
               '
        )->setParameters($parametros);
        $stockSucursalMarca = $query->getResult();

        $query = $em->createQuery(
            'SELECT SUM(s.cantidad) as cantidad, c.nombre as nombre, suc.nombre as sucursal
               FROM App\Entity\Stock s
                inner join s.sucursalIdsucursal suc
               INNER JOIN  s.productoIdproducto p
               INNER JOIN  p.marcaIdmarca m 
               INNER JOIN  p.categoriaIdcategoria sc
               INNER JOIN  sc.claseIdclase c

               where s.status =:status and p.tipoproducto=:tipoproducto  and s.cantidad > 0 ' . $whereModelo . $whereSucursal . $whereCategoria . $whereMarca . $whereClase . $whereProduct . ' group by suc.idsucursal, c.nombre order by cantidad DESC
               '
        )->setParameters($parametros);
        $stockSucursalCategoria = $query->getResult();

        $query = $em->createQuery(
            'SELECT SUM(s.cantidad) as cantidad, sc.nombre as nombre, suc.nombre as sucursal
               FROM App\Entity\Stock s
                inner join s.sucursalIdsucursal suc
               INNER JOIN  s.productoIdproducto p
               INNER JOIN  p.marcaIdmarca m 
               INNER JOIN  p.categoriaIdcategoria sc

               where s.status =:status and p.tipoproducto=:tipoproducto  and s.cantidad > 0 ' . $whereModelo . $whereSucursal . $whereCategoria . $whereMarca . $whereProduct . ' group by suc.idsucursal, sc.nombre order by cantidad DESC
               '
        )->setParameters($parametros);
        $stockSucursalSubcategoria = $query->getResult();

        $MarcasSucursalMapped = [];

        foreach ($stockSucursalMarca as $item) {
            $sucursal = $item['sucursal'];
            if (!isset($MarcasSucursalMapped[$sucursal])) {
                $MarcasSucursalMapped[$sucursal] = [];
            }
            $MarcasSucursalMapped[$sucursal][] = $item;
        }

        $CategoriasSucursalMapped = [];

        foreach ($stockSucursalCategoria as $item) {
            $sucursal = $item['sucursal'];
            if (!isset($CategoriasSucursalMapped[$sucursal])) {
                $CategoriasSucursalMapped[$sucursal] = [];
            }
            $CategoriasSucursalMapped[$sucursal][] = $item;
        }

        $SubcategoriasSucursalMapped = [];

        foreach ($stockSucursalSubcategoria as $item) {
            $sucursal = $item['sucursal'];
            if (!isset($SubcategoriasSucursalMapped[$sucursal])) {
                $SubcategoriasSucursalMapped[$sucursal] = [];
            }
            $SubcategoriasSucursalMapped[$sucursal][] = $item;
        }


        $query = $em->createQuery(
            'SELECT SUM(s.cantidad) as cantidad, p.modelo as modelo, suc.nombre as sucursal, s.codigobarras as codigo, m.nombre as marca, c.nombre as categoria, sc.nombre as subcategoria
               FROM App\Entity\Stock s
                inner join s.sucursalIdsucursal suc
               INNER JOIN  s.productoIdproducto p
               INNER JOIN  p.marcaIdmarca m 
               INNER JOIN  p.categoriaIdcategoria sc
               INNER JOIN  sc.claseIdclase c

               where s.status =:status and p.tipoproducto=:tipoproducto  and s.cantidad > 0 ' . $whereModelo . $whereSucursal . $whereCategoria . $whereMarca . $whereProduct . ' group by suc.idsucursal, p.idproducto order by modelo DESC
               '
        )->setParameters($parametros);
        $modeloSucursal = $query->getResult();

        $modeloSucursalMapped = [];

        foreach ($modeloSucursal as $item) {
            $marca = $item['marca'];
            if (!isset($modeloSucursalMapped[$marca])) {
                $modeloSucursalMapped[$marca] = [];
            }
            $modeloSucursalMapped[$marca][] = $item;
        }

        $categoriaModeloSucursalMapped = [];

        foreach ($modeloSucursal as $item) {
            $categoria = $item['categoria'];
            if (!isset($categoriaModeloSucursalMapped[$categoria])) {
                $categoriaModeloSucursalMapped[$categoria] = [];
            }
            $categoriaModeloSucursalMapped[$categoria][] = $item;
        }

        $subcategoriaModeloSucursalMapped = [];

        foreach ($modeloSucursal as $item) {
            $subcategoria = $item['subcategoria'];
            if (!isset($subcategoriaModeloSucursalMapped[$subcategoria])) {
                $subcategoriaModeloSucursalMapped[$subcategoria] = [];
            }
            $subcategoriaModeloSucursalMapped[$subcategoria][] = $item;
        }


        return $this->render('almacen/obtener-informacion-stock.html.twig', [
            'stock' => $stock,
            'stockCategoria' => $stockCategoria,
            'stockSubcategoria' => $stockSubcategoria,
            'MarcasSucursalMapped' => $MarcasSucursalMapped,
            'CategoriasSucursalMapped' => $CategoriasSucursalMapped,
            'SubcategoriasSucursalMapped' => $SubcategoriasSucursalMapped,
            'modeloSucursalMapped' => $modeloSucursalMapped,
            'categoriaModeloSucursalMapped' => $categoriaModeloSucursalMapped,
            'subcategoriaModeloSucursalMapped' => $subcategoriaModeloSucursalMapped,
        ]);
    }

    /**
     * @Route("/almacen-autocompletar-modelo", name="autocompletar-modelo")
     */
    public function autocompleteModelo(Request $request)
    {
        $em = $this->getDoctrine()->getManager();
        $parameters = array('modelo' => "%" . $request->get('query') . "%");
        $query = $em->createQuery(
            'SELECT  p.idproducto as data,p.modelo as value, p.descripcion
               FROM App\Entity\Producto p
               where p.modelo like :modelo order by p.modelo desc
               '
        )->setParameters($parameters);
        $query->setMaxResults(20);
        $Descripciones = $query->getResult();

        return $this->json(['suggestions' => $Descripciones]);
    }

    /**
     * @Route("/stock-report-table", name="almacen-stock-report-table")
     */

    public function stockReportTable(Request $request): Response
    {

        $em = $this->getDoctrine()->getManager();

        $clases = $request->get("clases");
        $sucursales = $request->get("sucursales");
        $categorias = $request->get("categorias");
        $marcas = $request->get("marcas");
        $tags = $request->get("tags");
        $modelo = $request->get("modelo");
        $idempresa = $request->get("idempresa");


        $parametros['status'] = 1;
        $parametros['tipoproducto'] = 1;
        $whereSucursal = "";

        //Sucursales
        if (isset($sucursales[0])) {

            $whereSucursal = " and (";

            for ($i = 0; $i < count($sucursales); $i++) {

                $whereSucursal .= " suc.idsucursal=" . $sucursales[$i];

                if ($i != count($sucursales) - 1) $whereSucursal .= " or ";
            }
            $whereSucursal .= ") ";
        }


        //Clases
        $whereClase = "";

        if (isset($clases[0])) {

            $whereClase = " and (";

            for ($i = 0; $i < count($clases); $i++) {

                $whereClase .= " c.idclase=" . $clases[$i];

                if ($i != count($clases) - 1) $whereClase .= " or ";
            }
            $whereClase .= ") ";
        }

        //Categorias
        $whereCategoria = "";

        if (isset($categorias[0])) {

            $whereCategoria = " and (";

            for ($i = 0; $i < count($categorias); $i++) {

                $whereCategoria .= " sc.idcategoria=" . $categorias[$i];

                if ($i != count($categorias) - 1) $whereCategoria .= " or ";
            }
            $whereCategoria .= ") ";
        }


        //Marcas
        $whereMarca = "";

        if (isset($marcas[0])) {

            $whereMarca = " and (";

            for ($i = 0; $i < count($marcas); $i++) {

                $whereMarca .= " m.idmarca=" . $marcas[$i];

                if ($i != count($marcas) - 1) $whereMarca .= " or ";
            }
            $whereMarca .= ") ";
        }


        $whereModelo = "";

        //Modelo
        if ($modelo != "") {
            $whereModelo = " and p.modelo ='" . $modelo . "' ";
        }

        //TAGS
        $tmpTags = ($tags) ? $tags : [];

        $whereTag = " (";
        for ($i = 0; $i < count($tmpTags); $i++) $whereTag .= $tmpTags[$i] . ',';
        $whereTag .= "-1) ";

        $query = $em->createQuery(
            'SELECT p.idproducto, t.name
                FROM App\Entity\Productotag pt
                INNER JOIN  pt.productoIdproducto p
                INNER JOIN  pt.tagIdtag t
                WHERE p.tipoproducto = :tipoproducto AND t.idtag IN ' . $whereTag . '
                GROUP BY p.idproducto, pt.productoIdproducto, pt.tagIdtag
                HAVING COUNT(DISTINCT t.idtag) = :tagCount
            '
        )->setParameters(['tagCount' => count($tmpTags), 'tipoproducto' => "1"]);
        $productsWTags = $query->getResult();

        //PRODUCTS
        $whereProduct = "";
        if (isset($productsWTags[0])) {

            $whereProduct = " and (";

            for ($i = 0; $i < count($productsWTags); $i++) {

                $whereProduct .= " p.idproducto=" . $productsWTags[$i]['idproducto'];

                if ($i != count($productsWTags) - 1) $whereProduct .= " or ";
            }
            $whereProduct .= ") ";
        } else if (count($tmpTags) > 0) $whereProduct = " and ( p.idproducto=-1) ";

        $query = $em->createQuery(
            'SELECT p.idproducto, t.name
                FROM App\Entity\Productotag pt
                INNER JOIN  pt.productoIdproducto p
                INNER JOIN  pt.tagIdtag t
                WHERE p.tipoproducto = :tipoproducto ' . $whereProduct . '
                ORDER BY p.idproducto ASC, t.name ASC
            '
        )->setParameters(['tipoproducto' => "1"]);
        $productsWAllTags = $query->getResult();

        $tagsPerProducts = [];

        foreach ($productsWAllTags as $productTags) {
            if (isset($tagsPerProducts[$productTags['idproducto']])) $tagsPerProducts[$productTags['idproducto']] .= ", " . $productTags['name'];
            else $tagsPerProducts[$productTags['idproducto']] = $productTags['name'];
        }


        $query = $em->createQuery(
            'SELECT suc.nombre as branch, c.nombre as category, sc.nombre as subcategory, m.nombre as brand,
            p.modelo as model, s.cantidad as quantity, s.codigobarras as sku, p.codigobarrasuniversal as barcode, p.idproducto,
            p.color AS Color
            FROM App\Entity\Stock s
            INNER JOIN s.sucursalIdsucursal suc
            INNER JOIN s.productoIdproducto p
            INNER JOIN p.marcaIdmarca m
            INNER JOIN p.categoriaIdcategoria sc
            INNER JOIN sc.claseIdclase c
            WHERE s.status = :status 
            AND p.tipoproducto = :tipoproducto ' . $whereModelo . $whereSucursal . $whereClase . $whereCategoria . $whereMarca . $whereProduct . 'AND s.cantidad > 0 ORDER BY s.cantidad DESC'
        )->setParameters(['status' => "1", 'tipoproducto' => "1"]);

        $stockData = $query->getResult();

        return $this->render('almacen/almacen-stock-report-table.html.twig', [
            'stockData' => $stockData,
            'tagsPerProducts' => $tagsPerProducts

        ]);
    }
}
