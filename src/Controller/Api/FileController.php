<?php

namespace App\Controller\Api;

use App\Entity\Tipoventa;
use App\Entity\Cliente;
use App\Entity\Usuario;
use App\Entity\Unidad;
use App\Entity\Empresacliente;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;

class FileController extends AbstractController
{
    /**
    * @Route("/api/upload-prescription", name="file-upload-prescription", methods={"POST"})
    */
    public function uploadPrescription(Request $request): Response
    {
        $uploadedFile = $request->files->get('file');
        $orderId = $request->request->get('orderId');
        $flowId = $request->request->get('flowId');

        if (!$uploadedFile) {
            return new JsonResponse(['message' => 'File not found.'], 400);
        }

        $path=$this->getParameter('prescriptionsFolder');

        $path = $path."/". $flowId.'/'.$orderId.'/';
        if (!file_exists($path)) mkdir($path, 0777, true);

        $ext = $uploadedFile->getClientOriginalExtension();

        $fileName = $uploadedFile->getClientOriginalName().'.'.$ext;

        $uploadedFile->move(
            $path,
            $fileName
        );


        return new JsonResponse(['message' => 'File uploaded successfully.']);
    }



}


