<?php

namespace App\Controller\Api\OptiHub;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;



class FiltersOptihubController extends AbstractController
{

    /**
     * @Route("/api/mega", name="api_mega", methods={"GET"})
     */
    public function getMegaFilters(EntityManagerInterface $entityManager): JsonResponse
    {
        try {

            $em = $this->getDoctrine()->getManager();

            $query = $entityManager->createQuery(
                'SELECT DISTINCT m.nombre, m.idmarca, p.showonlinestore, p.gender, p.design
     FROM App\Entity\Producto p
     INNER JOIN p.marcaIdmarca m
     INNER JOIN p.categoriaIdcategoria ca
     WHERE p.status = :status AND p.showonlinestore = :showonlinestore AND ca.idcategoria in (:idcategoria)
     GROUP BY m.idmarca, p.idproducto
     ORDER BY m.nombre ASC 
     '
            )->setParameters(["status" => '1', "showonlinestore" => '1', "idcategoria" => ['16','242']]);

            $filtersOft = $query->getResult();
            $filtermega = [];

            $genders = [
                'M' => 'Mujer',
                'H' => 'Hombre',
                'U' => 'Unisex'
            ];

            foreach ($filtersOft as $filter) {
                $filtermega['marcas'][$filter['idmarca']] = $filter['nombre'];

                if($filter['design'] || $filter['design'] != ""){ $filtermega['designs'][$filter['design']] = $filter['design'];}
                
                $filtermega['genders'][$filter['gender']] = $genders[$filter['gender']];


            }

            return new JsonResponse([
                'filtermega' => $filtermega,
            ]);
        } catch (\Exception $e) {

            return new JsonResponse(['error' => 'Error al obtener los datos: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @Route("/api/products", name="api_products", methods={"GET"})
     */
    public function getProductos(Request $request): JsonResponse
    {
        try {
            $queryParam = $request->query->get('query', '');

            $em = $this->getDoctrine()->getManager();

            // Cambia la consulta para que busque coincidencias parciales en modelo y marca
            $query = $em->createQuery(
                'SELECT p.modelo, m.nombre
            FROM  App\Entity\Stock s
            INNER JOIN s.productoIdproducto p
            INNER JOIN s.sucursalIdsucursal su
            INNER JOIN su.empresaIdempresa e
            INNER JOIN p.marcaIdmarca m
            WHERE (p.modelo LIKE :query OR m.nombre LIKE :query)
            AND e.idempresa = :idempresa
            AND p.masivounico = :masivounico
            AND p.tipoproducto = :tipoproducto
            AND p.showonlinestore = :showonlinestore
            AND p.status=:status
            AND su.codigo in (:codes)
            GROUP BY p.idproducto'
            )->setParameters([
                'query' => '%' . $queryParam . '%',  // Búsqueda parcial
                'idempresa' => '1',
                'masivounico' => '1',
                'tipoproducto' => '1',
                'showonlinestore' => '1',
                'status' => '1',
                'codes' => ['ALMACEN2', 'E-COMMERCE'],
            ]);

            $products = $query->getResult();

            return new JsonResponse([
                'products' => $products,
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Error al obtener los productos: ' . $e->getMessage()], 500);
        }
    }

}
