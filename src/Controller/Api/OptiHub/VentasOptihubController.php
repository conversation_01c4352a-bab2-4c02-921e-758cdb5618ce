<?php

namespace App\Controller\Api\OptiHub;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Twig\Environment;
use App\Entity\Cliente;
use Swift_SmtpTransport;
use Swift_Mailer;
use Swift_Message;
use Swift_Attachment;
use SwiftmailerBundle;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class VentasOptihubController extends AbstractController
{

    private $params;

    public function __construct(ParameterBagInterface $params)
    {
        $this->params = $params;
    }

    /**
     * @Route("/send-verification-code", name="sendVerificationCode", methods={"POST"})
     */
    public function sendVerificationCode(Request $request, EntityManagerInterface $em, \Swift_Mailer $mailer, LoggerInterface $logger): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $email = $data['email'] ?? null;
            $numeroEmpleado = $data['numeroEmpleado'] ?? null;
            $numeroTelefono = $data['numeroTelefono'] ?? null;

            if (!$email || !$numeroTelefono) {
                $logger->error('Email y número de teléfono son requeridos');
                return new JsonResponse(['message' => 'Email y número de empleado son requeridos'], 400);
            }
            if ($numeroEmpleado && $numeroTelefono) {
                $cliente = $em->getRepository(Cliente::class)->findOneBy(['numeroempleado' => $numeroEmpleado, 'telefono' => $numeroTelefono], ['idcliente' => 'DESC']);
            } else {
                $logger->error('Cliente no encontrado: ' . $email . 'Número Celular' . $numeroTelefono);
                return new JsonResponse(['message' => 'Cliente no encontrado'], 404);
            }

            /* if ($email) {
                 $cliente = $em->getRepository(Cliente::class)->findOneBy(['email' => $email]);
             } elseif ($numeroEmpleado) {
                 $cliente = $em->getRepository(Cliente::class)->findOneBy(['numeroEmpleado' => $numeroEmpleado]);
             } else {
                 $cliente = $em->getRepository(Cliente::class)->findOneBy(['telefono' => $numeroTelefono]);
             }*/


            // Generar un token único
            $verificationToken = bin2hex(random_bytes(32));
            $cliente->setGoogleauthtoken($verificationToken);
            $em->flush();

            $PV360Base = $_ENV['PV360'];

            $verificationLink = $PV360Base . "verificar?token=" . $verificationToken;

            // Crear el cuerpo del correo con estilo
            $messageBody = "
        <html>
        <head>
 <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            color: #333;
            text-align: center;
            padding: 20px;
        }
        .container {
            background-color: #fff;
            border-radius: 10px;
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .logo-container {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }
        .logo {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #1616b8;
        }
        h1 {
            color: #124DDE;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color:rgb(0, 0, 0);
            color: white;
            border-radius: 5px;
            margin-top: 20px;
            text-decoration: none;
            font-size: 16px;
            font-weight: bold;
        }
        .btn:hover {
            background-color:rgb(255, 255, 255);
        }
        .footer {
            margin-top: 20px;
            font-size: 12px;
            color: #777;
        }
    </style>
        </head>
        <div class='container'>
   
        <div class='logo-container'>
      <img src='https://scontent.fmex5-1.fna.fbcdn.net/v/t39.30808-6/448435206_122154033896209452_6165876503085467031_n.jpg?_nc_cat=104&ccb=1-7&_nc_sid=6ee11a&_nc_ohc=b9mq94MysTIQ7kNvgGUpYnK&_nc_oc=Adjiooi3pc5kIlbV9alJja1WjRaiIWF6PwfQ9VKd5jO-YLrYUUdlupndcHOKpyHcryM&_nc_zt=23&_nc_ht=scontent.fmex5-1.fna&_nc_gid=M8V0ASFJF0qResyj5m9YGg&oh=00_AYH4YM9Dkg4N_YTG2fKVTIvF3t1qYumDGJfkSC9F45QREQ&oe=67DA9062'
     alt='logo Optihub'
     class='img-fluid'
     style='max-width: 50px; border-radius: 50%;'>

        </div>
        
        <h1>¡Hola desde Óptimo Ópticas!</h1>
        <h4>Hemos recibido una solicitud para verificar tu cuenta. Por favor, haz clic en el siguiente enlace para completar el proceso:</h4>
        
        <a href='$verificationLink' class='btn'>Verificar mi cuenta</a>
        
        <p>Si no solicitaste este enlace, puedes ignorar este mensaje.</p>
        
        <div class='footer'>
            <p>Este correo es automático, por favor no respondas.</p>
        </div>
    </div>
        </html>";

            $smtpUser = '<EMAIL>';
            $smtpPassword = 'jbnefpwybhunaffk';

            $transport = (new Swift_SmtpTransport('smtp.gmail.com', 465, 'ssl'))
                ->setUsername($smtpUser)
                ->setPassword($smtpPassword);

            $mailer = new Swift_Mailer($transport);

            $message = (new Swift_Message('🔑 Verifica tu cuenta - Óptimo Ópticas 👓'))
                ->setFrom($smtpUser)
                ->setTo($email)
                ->setBody($messageBody, 'text/html');

            $logger->info('Enviando correo a: ' . $email);
            $mailer->send($message);

            return new JsonResponse(['message' => 'Enlace de verificación enviado correctamente'], 200);
        } catch (\Exception $e) {
            $logger->error('Error al enviar el correo: ' . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return new JsonResponse([
                'message' => 'Error en el servidor',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString() // Solo para desarrollo, no usar en producción
            ], 500);
        }
    }

    /**
     * @Route("/verificar", name="verificar_cuenta", methods={"GET"})
     */
    public function verificarCuenta(Request $request, EntityManagerInterface $em): Response
    {
        $optihubUrl = $this->params->get('OPTIHUBV3') . 'profile';

        try {
            $token = $request->query->get('token');

            // Si no hay token, mostrar mensaje de error
            if (!$token) {
                return $this->render('verificacion.html.twig', [
                    'mensaje' => 'Token no proporcionado',
                    'tipoAlerta' => 'danger'
                ]);
            }

            // Buscar cliente por el token
            $cliente = $em->getRepository(Cliente::class)->findOneBy(['googleauthtoken' => $token]);

            // Si el token es inválido o no se encuentra en la base de datos
            if (!$cliente) {
                return $this->render('verificacion.html.twig', [
                    'mensaje' => 'Token utilizado',
                    'tipoAlerta' => 'primary',
                    'optihubUrl' => $optihubUrl,
                ]);
            }

            // Marcar al cliente como verificado
            $cliente->setIsVerified(true); // Cambiar el valor de isVerified a true

            // Persistir el cambio en la base de datos
            $em->persist($cliente);
            $em->flush();

            // Retornar vista con mensaje de éxito
            return $this->render('verificacion.html.twig', [
                'mensaje' => 'Cuenta correctamente verificada',
                'tipoAlerta' => 'success',
                'optihubUrl' => $optihubUrl,
                'isVerified' => $cliente->getIsVerified(),
            ]);

        } catch (\Exception $e) {
            return $this->render('verificacion.html.twig', [
                'mensaje' => 'Error en el servidor',
                'tipoAlerta' => 'danger',
            ]);
        }
    }


    /**
     * @Route("/get-ventas", name="get-ventas", methods={"POST"})
     */
    public function getVentas(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $telefono = $data['telefono'] ?? null;
            /*$numeroEmpleado = $data['numeroEmpleado'] ?? null;
            $phone = $data['phone'] ?? null;*/

            if (!$telefono) {
                return new JsonResponse(['error' => 'Número de teléfono es requerido'], 400);
            }

            $cliente = $em->getRepository(Cliente::class)->findOneBy(['telefono' => $telefono, 'isverified' => 1, 'status' => 1]);

            if (!$cliente) {
                return new JsonResponse(['message' => 'Cliente no encontrado' . $telefono], 404);
            }

            $query = $em->createQuery(
                'SELECT v.idventa AS idventa, v.fecha AS fecha, v.total AS total, c.nombre AS cliente,
                v.ticketpdf AS Ticket, v.archivoautorizacion, v.tickerpdfespecial, c.email, c.isverified
                 FROM App\Entity\Venta v
                 INNER JOIN v.clienteIdcliente c
                 WHERE v.status = :status 
                   AND c.email = :email
                 ORDER BY v.fecha DESC'
            )->setParameters([
                'status' => 1,
                'email' => $cliente->getEmail()
            ]);

            $ventas = $query->getResult();

            return new JsonResponse([
                'message' => 'Ventas obtenidas correctamente',
                'ventas' => $ventas
            ], 200);


        } catch (\Exception $e) {
            return new JsonResponse([
                'message' => 'Error en el servidor',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }
}