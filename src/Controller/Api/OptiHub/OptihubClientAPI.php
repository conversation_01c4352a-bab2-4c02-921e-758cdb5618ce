<?php

namespace App\Controller\Api\OptiHub;

use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Twig\Environment;

use App\Entity\Cliente;

class OptihubClientAPI extends AbstractController
{
    private $twig;


    public function __construct(Environment $twig)
    {
        $this->twig = $twig;
    }

    /**
     * @Route("/api/cliente/{id}", name="api_client", methods={"GET"})
     */
    public function getCliente(EntityManagerInterface $entityManager, Request $request, SerializerInterface $serializer, int $id = null): JsonResponse
    {

        /*
         *
          'n'=> $existingUser
          'ln'=> $existingUser
         * */
        $name = $request->query->get('n');
        $lastnames = $request->query->get('ln');

        $query = $entityManager->createQuery(
            'SELECT c
            FROM App\Entity\Cliente c 
            WHERE c.status =:status AND c.idcliente = :idcliente'
        )->setParameters(['status' => 1, 'idcliente' => $id]);


        $result = $query->getResult()[0] ?? new Cliente();

        if ($name) {
            $result->setNombre($name);
        }
        if ($lastnames) {

            $parts = preg_split('/\s+/', trim($lastnames));
            $result->setApellidopaterno($parts[0]);


            if (isset($parts[1])) {
                $result->setApellidomaterno($parts[1]);
            }
        }

        $entityManager->persist($result);
        $entityManager->flush();

        // Return the serialized JSON response
        return new JsonResponse(['idpv360' => $result->getIdcliente(), 'nombre' => $result->getNombre(), 'n' => $name, 'data' => ['isverified' => $result->getIsverified()]]);
    }

    /**
     * @Route("/api/get-ventas-rifa", name="get-ventas", methods={"POST"})
     */
    public function getVentasRifa(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $numeroVentas = 0; // Initialize this variable
        $numeroBoletos = 0;
        $message = "";
        $success = false;
        try {
            $data = json_decode($request->getContent(), true);
            $numeroEmpleado = $data['numeroEmpleado'] ?? null;
            $telefono = $data['telefono'] ?? null;

            if (!$numeroEmpleado) {
                return new JsonResponse(['error' => 'Número de empleado es requerido'], 400);
            }
            if (!$telefono) {
                return new JsonResponse(['error' => 'Teléfono de empleado es requerido'], 400);
            }

            // Ensure query is correct
            $query = $em->createQuery(
                'SELECT v
            FROM App\Entity\Venta v
            INNER JOIN v.clienteIdcliente c
            WHERE v.status = :status
            AND c.numeroempleado = :numeroempleado
            AND c.telefono = :telefono'
            )->setParameters([
                'status' => 1,
                'numeroempleado' => $numeroEmpleado,
                'telefono' => $telefono
            ]);

            $ventas = $query->getResult();

            if ($ventas) {
                $numeroVentas = count($ventas);

                foreach ($ventas as $venta) {
                    $tipoVentaId = $venta->getTipoventaIdtipoventa();

                    if ($tipoVentaId == "1748" || $tipoVentaId == "1779") {
                        $numeroBoletos += 1;
                    } elseif ($tipoVentaId == "1756") {
                        $numeroBoletos += 5;
                    }
                }


            }

            $success = true;
        } catch (\Exception $e) {
            $message = $e->getMessage();

        }
        return new JsonResponse([

            'numeroVentas' => $numeroVentas,
            'numeroBoletos' => $numeroBoletos,
            'success' => $success,
            'message' => $message,
        ], 200);
    }

    /**
     * @Route("/api-get-rifa", name="get-rifa", methods={"POST"})
     */
    public function apiRifa(Request $request, EntityManagerInterface $em, LoggerInterface $logger): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $numeroEmpleado = $data['numeroEmpleado'] ?? null;
        $numeroTelefono = $data['numeroTelefono'] ?? null;


        if (empty($numeroEmpleado) && empty($numeroTelefono)) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Se requiere al menos un número de empleado o teléfono'
            ], 400);
        }

        try {
            $clienteRepository = $em->getRepository(Cliente::class);
            $cliente = null;


            if (!empty($numeroEmpleado) && !empty($numeroTelefono)) {
                $cliente = $clienteRepository->findOneBy(['numeroempleado' => $numeroEmpleado, 'telefono' => $numeroTelefono]);

                if (!$cliente) {
                    $cliente = $clienteRepository->findOneBy(['telefono' => $numeroTelefono]);
                    if (!$cliente) {
                        $cliente = $clienteRepository->findOneBy(['numeroempleado' => $numeroEmpleado]);
                    }
                }
            } elseif (!empty($numeroTelefono)) {
                $cliente = $clienteRepository->findOneBy(['telefono' => $numeroTelefono]);
            } else {
                $cliente = $clienteRepository->findOneBy(['numeroempleado' => $numeroEmpleado]);
            }

            if (!$cliente) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'No se encontró ningún cliente con los datos proporcionados',
                    'numeroVentas' => 0,
                    'numeroBoletos' => 0,
                    'ventas' => []
                ], 404);
            }

            $queryBuilder = $em->createQueryBuilder()
                ->select('v.idventa', 'v.folio', 'c.telefono', 'c.numeroempleado', 'tv.idtipoventa')
                ->from('App\Entity\Venta', 'v')
                ->innerJoin('v.clienteIdcliente', 'c')
                ->innerJoin('v.tipoventaIdtipoventa', 'tv')
                ->where('v.status = :status')
                ->andWhere('c.idcliente = :clienteId')
                ->setParameter('status', 1)
                ->setParameter('clienteId', $cliente->getIdcliente());

            $ventas = $queryBuilder->getQuery()->getResult();

            $numeroVentas = count($ventas);
            $numeroBoletos = 0;

            foreach ($ventas as $venta) {
                switch ($venta["idtipoventa"]) {
                    case "1748":
                    case "1779":
                        $numeroBoletos += 1;
                        break;
                    case "1756":
                        $numeroBoletos += 5;
                        break;
                }
            }

            $cliente->setIsverified(true);
            $em->persist($cliente);
            $em->flush();

            $logger->info('Cliente verificado', [
                'clienteId' => $cliente->getIdcliente(),
                'telefono' => $numeroTelefono,
                'numeroEmpleado' => $numeroEmpleado
            ]);

            return new JsonResponse([
                'numeroVentas' => $numeroVentas,
                'numeroBoletos' => $numeroBoletos,
                'success' => true,
                'message' => $numeroVentas > 0 ? 'Ventas encontradas y cliente verificado' : 'No se encontraron ventas (cliente verificado)',
                'ventas' => $ventas,
                'cliente' => [
                    'id' => $cliente->getIdcliente(),
                    'nombre' => $cliente->getNombre(),
                    'isVerified' => $cliente->getIsverified()
                ]
            ], 200);

        } catch (\Exception $e) {
            $logger->error('Error en apiRifa', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return new JsonResponse([
                'success' => false,
                'message' => 'Error en el servidor',
                'error' => $e->getMessage(),
                'numeroVentas' => 0,
                'numeroBoletos' => 0,
                'ventas' => []
            ], 500);
        }
    }

    /**
     * @Route("/ventas-optihub", name="ventas-optihub", methods={"POST"})
     */
    public function ventasOptihub(Request $request, EntityManagerInterface $em, LoggerInterface $logger): JsonResponse
    {

        $logger->info('Petición recibida en /ventas-optihub', [
            'method' => $request->getMethod(),
            'content_type' => $request->headers->get('Content-Type'),
            'client_ip' => $request->getClientIp()
        ]);

        // Registra el contenido recibido (sin datos sensibles)
        $requestData = json_decode($request->getContent(), true) ?? [];
        $logger->debug('Datos recibidos:', ['email' => $requestData['email'] ?? 'No proporcionado']);

        // Verificar contenido JSON
        if ($request->getContentType() !== 'json' || !$request->getContent()) {
            $logger->error('Solicitud sin cuerpo JSON');
            return new JsonResponse([
                'success' => false,
                'message' => 'Se requiere aplicación/json',
                'isVerified' => false
            ], 400);
        }

        try {
            $data = json_decode($request->getContent(), true);
            $logger->debug('Datos recibidos:', $data ?? []);

            $email = $data['email'] ?? null;

            if (!$email) {
                $logger->warning('Solicitud sin email');
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Correo electrónico es requerido',
                    'isVerified' => false
                ], 400);
            }

            $logger->info('Buscando ventas para: '.$email);

            $query = $em->createQuery(
                'SELECT v.idventa, v.fecha, v.total, v.folio, tv.idtipoventa, c.isverified, c.email
         FROM App\Entity\Venta v
         INNER JOIN v.clienteIdcliente c
         INNER JOIN v.tipoventaIdtipoventa tv
         WHERE v.status = :status 
           AND c.email = :email
         ORDER BY v.fecha DESC'
            )->setParameters([
                'status' => 1,
                'email' => $email
            ]);

            $resultados = $query->getResult();
            $logger->debug('Resultados encontrados:', $resultados);

            if (empty($resultados)) {
                $logger->info('No hay ventas para: '.$email);
                return new JsonResponse([
                    'success' => false,
                    'message' => 'No se encontraron ventas para este email',
                    'isVerified' => false
                ], 404);
            }

            $isVerified = (bool)$resultados[0]['isverified'];

            if (!$isVerified) {
                $logger->warning('Cliente no verificado: '.$email);
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Cliente no verificado',
                    'isVerified' => false
                ], 403);
            }

            $numeroBoletos = 0;
            foreach ($resultados as $venta) {
                if (in_array($venta['idtipoventa'], ["1748", "1779"])) {
                    $numeroBoletos += 1;
                } elseif ($venta['idtipoventa'] == "1756") {
                    $numeroBoletos += 5;
                }
            }

            $logger->info('Ventas encontradas: '.count($resultados).' - Boletos: '.$numeroBoletos);

            return new JsonResponse([
                'success' => true,
                'message' => 'Ventas obtenidas correctamente',
                'ventas' => $resultados,
                'numeroVentas' => count($resultados),
                'numeroBoletos' => $numeroBoletos,
                'isVerified' => true
            ], 200);

        } catch (\Exception $e) {
            $logger->error('Error en ventasOptihub: '.$e->getMessage());
            return new JsonResponse([
                'success' => false,
                'message' => 'Error en el servidor',
                'error' => $e->getMessage(),
                'isVerified' => false
            ], 500);
        }
    }
}