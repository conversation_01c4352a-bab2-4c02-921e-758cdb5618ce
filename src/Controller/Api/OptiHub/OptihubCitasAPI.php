<?php

namespace App\Controller\Api\OptiHub;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Serializer\SerializerInterface;
use \DateTime;
use App\Service\MailService;

use App\Entity\Cliente;
use App\Entity\Event;
use App\Entity\Eventtype;
use App\Entity\Sucursal;
class OptihubCitasAPI extends AbstractController
{

    public $empRFC = "GOM180228DD4";
    public $tipoev = "cita-optometria";

    /**
     * @Route("/api/get-sucursales", name="api-get-sucursales")
     */
    public function apiGetSucursales(EntityManagerInterface $entityManager, SerializerInterface $serializer): JsonResponse
    {
        $queryBuilder = $entityManager->createQueryBuilder();
        $queryBuilder
            ->select('s.idsucursal, s.nombre')
            ->from('App\Entity\Sucursal', 's')
            ->join('s.empresaIdempresa', 'emp')
            ->where('s.status = 1')
            ->andWhere('emp.rfc = :empresa')
            ->andWhere('s.tipo = :type')
            ->andWhere('s.idsucursal IN (2,7,8,10,60,64)')
            ->setParameter('empresa', $this->empRFC)
            ->setParameter('type', "sucursal")
            ->orderBy('s.nombre', 'DESC');


        $sucursales = $queryBuilder->getQuery()->getResult();;

        $jsonData = $serializer->serialize($sucursales, 'json');

        return new JsonResponse($jsonData, 200, [], true);
    }

    /**
     * @Route("/api/getExistingDates/{id}/{datestring}", name="api-existing-dates")
     */
    public function apiGetExistingDates(EntityManagerInterface $entityManager, SerializerInterface $serializer, string $id = null, string $datestring = null): JsonResponse
    {

        if (!$id) return new JsonResponse("Invalid ID", 500, [], true);

        $date = $datestring ? new DateTime($datestring) : new DateTime();

        $queryBuilder = $entityManager->createQueryBuilder();
        $queryBuilder
            ->select('ev.eventdate')
            ->from('App\Entity\Event', 'ev')
            ->join('ev.sucursalIdsucursal', 's')
            ->join('ev.eventtypeIdeventtype', 'et')
            ->where('ev.status = 1')
            ->andWhere('s.tipo = :type')
            ->andWhere('s.idsucursal = :sucid')
            ->andWhere($queryBuilder->expr()->between('ev.eventdate', ':startDate', ':endDate'))
            ->setParameter('type', "sucursal")
            ->setParameter('sucid', $id)
            ->setParameter('startDate', $date->format('Y-m-d 00:00:00'))
            ->setParameter('endDate', $date->format('Y-m-d 23:59:59'))
            ->orderBy('ev.eventdate', 'DESC');

        $events = $queryBuilder->getQuery()->getResult();

        $jsonData = $serializer->serialize($events, 'json');

        return new JsonResponse($jsonData, 200, [], true);
    }

    /**
     * @Route("/api/setdate", name="api-set-date")
     */
    public function apiSetdate(EntityManagerInterface $entityManager, SerializerInterface $serializer, Request $request, MailService $mailer): JsonResponse
    {
        $msg = "";
            
        try{
            $eventType = $request->get("eventType");

            $now = new DateTime();
            $cliente = $entityManager->getRepository(Cliente::class)->findOneBy(['email' => $request->get("email")]) ?? new Cliente($request->get("email"), $request->get("phone"), $request->get("name"), $request->get("lastname"));
            $sucursal = $entityManager->getRepository(Sucursal::class)->findOneBy(['idsucursal' => $request->get('selectedSucursal')]);
            $tipoevento = $entityManager->getRepository(Eventtype::class)->findOneBy(['ideventtype' => $eventType]);

            if (!$tipoevento) {
                $etName = ($eventType == "cita-optometrista") ? $eventType : "cita-audiologia" ;
                $etDesc = ($eventType == "cita-optometrista") ? "Citas en sucursales de Optimo Ópticas para exámen de la vista" : "Citas en sucursales de Optimo Ópticas para exámen auditivo" ;
                $tipoevento = new Eventtype();
                $tipoevento->setName($etName);
                $tipoevento->setDescription($etDesc);
                $entityManager->persist($tipoevento);
            }

            $entityManager->persist($cliente);

            $timezone = $now->getTimezone();
            $appointmentDate = new DateTime($request->get('appointmentDate'));
            $appointmentDate->setTimezone($timezone);

            $date = new Event();
            $date->setClienteIdcliente($cliente);
            $date->setSucursalIdsucursal($sucursal);
            $date->setEventtypeIdeventtype($tipoevento);
            $date->setEventdate($appointmentDate);
            $date->setCreationdate($now);
            $date->setUpdatedate($now);

            $entityManager->persist($date);
            $entityManager->flush();

            $appointmentDate->modify('+1 hour'); 
            $locale = 'es_ES';
            $dateFormatter = new \IntlDateFormatter(
                $locale,
                \IntlDateFormatter::FULL,
                \IntlDateFormatter::SHORT
            );
            $formattedDate = $dateFormatter->format($appointmentDate);
            $sucName = $sucursal->getNombre();
            $sucAddress = $sucursal->getDireccion();
            $sucPhone = $sucursal->getTelefono();
            $cliName = $cliente->getNombreCompleto();
            $cliEmail = $cliente->getEmail();
            $cliPhone = $cliente->getTelefono();

            $curEventTypeDescription = $tipoevento->getDescription();
            
            $emailContent = [
                "emailS" => $sucursal->getEmail(),
                "emailC" => $cliente->getEmail(),
                "subject" => "Cita agendada en $sucName el dia $formattedDate",
                "bodyS" => "El cliente $cliName con email $cliEmail y teléfono $cliPhone ha agendado una cita el día $formattedDate con el siguiente motivo: $curEventTypeDescription",
                "bodyC" => "Estimado $cliName, has agendado una cita el día $formattedDate en nuestra $sucName. El teléfono de la sucursal es $sucPhone y se encuentra en $sucAddress ¡Esperamos verte pronto!"
            ];
            $mailer->sendEmailCitaSucursal($emailContent);

        } catch (\Exception $e) {
            $msg = $e->getMessage() . " linea " . $e->getLine();
        }
        

        return $this->apiGetExistingDates($entityManager, $serializer, $sucursal->getIdsucursal());
        //return $this->json(["test" => $msg]);
        
    }


}
