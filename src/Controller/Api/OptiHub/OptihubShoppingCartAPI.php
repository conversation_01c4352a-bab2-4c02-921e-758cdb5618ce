<?php

namespace App\Controller\Api\OptiHub;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;
use DateTime;
use App\Entity\Cart;
use App\Entity\Cliente;



class OptihubShoppingCartAPI extends AbstractController
{
    /**
     * @Route("/api/cart/{id}", name="api_shopping_cart_controller_get_cart", methods={"GET"})
     */
    public function getcart(EntityManagerInterface $entityManager, SerializerInterface $serializer, string $id = null): JsonResponse
    {

        $cart = null;

        if ($id) {

            $cart = $entityManager->getRepository(Cart::class)->findOneBy(['idcart' => $id, 'clienteIdcliente' => NULL]);
        }

        if (!$cart) {

            $queryBuilder = $entityManager->createQueryBuilder();
            $queryBuilder
                ->select('DISTINCT c')
                ->from('App\Entity\Cart', 'c')
                ->Where('c.status = 0')
                ->setMaxResults(1);

            $arraycart = $queryBuilder->getQuery()->getResult();

            if ($arraycart && count($arraycart) > 0) {
                $cart = $arraycart[0];
                $cart->setData('[]');
            } else {
                $cart = new Cart();
            }
        }

        $cart = $cart ?? new Cart();

        $now = new DateTime();

        $cart
            ->setCreationdate($cart->getCreationdate() ?? $now)
            ->setUpdatedate($now);

        $entityManager->persist($cart);
        $entityManager->flush();


        return new JsonResponse($serializer->serialize($cart, 'json'), 200, [], true);
    }

    /**
     * @Route("/api/cart-update/{id}", name="api_shopping_cart_controller_update_cart")
     */
    public function updatecart(Request $request, EntityManagerInterface $entityManager, SerializerInterface $serializer, string $id = null): JsonResponse
    {
        $cart = $entityManager->getRepository(Cart::class)->findOneBy(['idcart' => $id]) ?? new Cart();
        $now = new DateTime();

        $receivedData = json_decode($request->getContent(), true);

        // Assuming you have received the response as an associative array called $response
        $existingItems = $cart->getData() ? json_decode($cart->getData(), true) : [];

        $productIds = $receivedData['productIds'];

        $index = -1; // Initialize with a default value if the item is not found

        // Iterate through the array to find the index
        foreach ($existingItems as $key => $item) {
            if ($item['id'] === $receivedData['id'] && $item['productIds'] === $productIds) {
                $index = $key; // Set the index if the item is found
                break; // Exit the loop once the item is found
            }
        }

        $productConfigurationObject = $receivedData['onlyframe'] ? null :  $receivedData['productConfiguration'];

        if ($index !== -1) {
            $existingItems[$index]['amount'] += $receivedData['amount'];
        } else {
            $newItem = [
                'id' => $receivedData['id'],
                'fatherPrice' => $receivedData['fatherPrice'],
                'amount' => $receivedData['amount'],
                'maxamount' => $receivedData['maxamount'],
                'nombre' => $receivedData['nombre'],
                'url' => $receivedData['url'],
                'price' => $receivedData['price'],
                'discount' => $receivedData['discount'],
                'brandId' => $receivedData['brandId'],
                'productConfigurationObject' => $productConfigurationObject,
                'productIds' => $productIds
            ];
            array_push($existingItems, $newItem);
        }

        $now = new DateTime();

        $cart
            ->setCreationdate($cart->getCreationdate() ?? $now)
            ->setUpdatedate($now)
            ->setData($serializer->serialize($existingItems, 'json'));


        $entityManager->persist($cart);
        $entityManager->flush();


        return new JsonResponse($serializer->serialize($cart, 'json'), 200, [], true);
    }

    #[Route('/api/cart-override/{id}', name: 'shopping_cart_controller_override_cart')]
    public function overridecart(Request $request, EntityManagerInterface $entityManager, SerializerInterface $serializer, string $id = null): JsonResponse
    {
        $cart = $entityManager->getRepository(Cart::class)->findOneBy(['idcart' => $id]) ?? new Cart();
        $receivedData = json_decode($request->getContent(), true);


        // Assuming you have received the response as an associative array called $response
        $existingItems = $receivedData ? $receivedData['cartItems'] : [];


        $now = new DateTime();

        $cart
            ->setCreationdate($cart->getCreationdate() ?? $now)
            ->setUpdatedate($now)
            ->setData($serializer->serialize($existingItems, 'json'));


        $entityManager->persist($cart);
        $entityManager->flush();


        return new JsonResponse($serializer->serialize($cart, 'json'), 200, [], true);
    }

    #[Route('/api/cart-unuse/{id}', name: 'shopping_cart_controller_unuse_cart')]
    public function unusecart(Request $request, EntityManagerInterface $entityManager, SerializerInterface $serializer, string $id = null): JsonResponse
    {
        $cart = $entityManager->getRepository(Cart::class)->findOneBy(['idcart' => $id]);
        $receivedData = json_decode($request->getContent(), true);


        // Assuming you have received the response as an associative array called $response
        $existingItems = $receivedData ? $receivedData['cartItems'] : [];


        $now = new DateTime();

        $cart
            ->setUpdatedate($now)
            ->setData($serializer->serialize($existingItems, 'json'));


        $entityManager->persist($cart);
        $entityManager->flush();


        return new JsonResponse($serializer->serialize($cart, 'json'), 200, [], true);
    }
}

