<?php

namespace App\Controller\Api\OptiHub;

use App\Entity\Stockstate;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Twig\Environment;
use App\Service\MailService;
use DateTime;

use App\Entity\Cliente;
use App\Entity\Usuario;
use App\Entity\Venta;
use App\Entity\Stockventa;
use App\Entity\Stock;
use App\Entity\Sucursal;
use App\Entity\Pago;
use App\Entity\Producto;
use App\Entity\Paymenttype;
use App\Entity\Shipment;
use App\Entity\Cupon;
use App\Entity\Ventacupon;

class OptihubVentaAPI extends AbstractController
{
    public $empRFC = "GOM180228DD4";
    public $gerenteId = "1";
    public $sucursalId = "1";
    public $iva = 0.16;

    private $twig;

    private $micas =
        [
            'monofocal' => 5326,
            'bifocal' => 5326,
            'progresivo' => 5326,
            'mica_sin_graduacion' => 5326
        ];

    private $filtros =
        [
            'blue' => 1439,
            'fotocromatico' => 1437,
            'foto_y_blue' => 1440,
            'ninguno' => null
        ];

        private $tratamientosvalues = [

            "ULTRA_HI_INDEX_AR" => 1500,
            "ULTRA_HI_INDEX_AR_FOTO" => 2400,
            "ULTRA_HI_INDEX_BLUE_RAY" => 2500,
            "ULTRA_HI_INDEX_BLUE_RAY_FOTO" => 3300,
    
            "POLY_AR" => 1200,
            "POLY_AR_PHOTO" => 1400,
            "POLY_BLUE_RAY" => 1700,
            "POLY_BLUE_RAY_FOTO" => 2000,
    
            "HI_INDEX_AR" => 700,
            "HI_INDEX_PHOTO_AR" => 1000,
            "BLUE_RAY" => 1000,
            "BLUE_RAY_PHOTO" => 1800,
    
            "FLAT_TOP_W" => 1100,
            "FLAT_TOP_AR" => 1200,
            "FLAT_TOP_W_PHOTO" => 1500,
            "FLAT_TOP_AR_PHOTO" => 1600,
            "FLAT_TOP_BLUE_RAY_PHOTO" => 2600,
    
            "BLENDED_W" => 1300,
            "BLENDED_AR" => 1400,
            "BLENDED_W_PHOTO" => 1600,
            "BLENDED_AR_PHOTO" => 1700,
            "BLENDED_BLUE_RAY_PHOTO" => 2700,
    
            "PROGRESIVO_AR" => 1500,
            "PROGRESIVO_W_PHOTO" => 1700,
            "PROGRESIVO_AR_PHOTO" => 1800,
    
            "PANORAMIC_BLUE" => 2500,
            "PANORAMIC_BLUE_PHOTO" => 2800
        ];


    public function __construct(Environment $twig)
    {
        $this->twig = $twig;
    }

    /**
     * @Route("/api/venta/{idcliente}/{idventa}", name="api_venta", methods={"POST", "GET"})
     */
    public function apiVenta(Request $request, EntityManagerInterface $entityManager, string $idcliente = null, string $idventa = null): JsonResponse
    {
        
        try {
            // Get user and gerente
            $user = $entityManager->getRepository(Cliente::class)->findOneBy(['idcliente' => $idcliente]);
            $gerente = $entityManager->getRepository(Usuario::class)->findOneBy(['idusuario' => $this->gerenteId]);

            // Error handling for missing user or gerente
            if (!$user || !$gerente) {
                return new JsonResponse(['error' => 'User or Gerente not encontrado']);
            }
           
            // Build query for Tipoventa
            $queryBuilder = $entityManager->createQueryBuilder();
            $queryBuilder
                ->select('DISTINCT tv')
                ->from('App\Entity\Tipoventa', 'tv')
                ->join('tv.empresaIdempresa', 'emp')
                ->where('tv.status = 1')
                ->andWhere('tv.onlinestore = 1')
                ->andWhere('emp.rfc = :empresa')
                ->setParameter('empresa', $this->empRFC)
                ->setMaxResults(1);
            $tipoventa = $queryBuilder->getQuery()->getOneOrNullResult();
           
            // Ensure Tipoventa is found
            if (!$tipoventa) {
                return new JsonResponse(['error' => 'Tipoventa not found']);
            }
           
            // Decode the request content
            $receivedData = json_decode($request->getContent(), true);
            if (!$receivedData) {
                return new JsonResponse(['error' => 'post incorrecto']);
            }
            
            //return new JsonResponse($receivedData);
           
            // Get the current date and time
            $now = new \DateTime();

            // Find or create a new Venta
            $venta = $entityManager->getRepository(Venta::class)->findOneBy(['idventa' => $idventa]) ?? new Venta();

            if ($venta->getFolio() != null) {

                //Si la venta tiene un folio se crea el array con el responsedata

                $responseData = [
                    'status' => 'Success',
                    'venta' => [
                            'id' => $venta->getIdventa(),
                            'folio' => $venta->getFolio(),
                            'total' => $venta->getTotal(),
                            'items' => $receivedData['items'],
                            'address' => $receivedData['address'],
                        ]
                ];
    
            }
           
            $iva = ($receivedData['total'] / (1 + $this->iva)) * $this->iva;
            // Set Venta properties
            $venta->setFecha($now ?? $venta->getFecha());
            $venta->setFechacreacion($now ?? $venta->getFechacreacion());
            $venta->setFechaactualizacion($now ?? $venta->getFechacreacion());
            $venta->setTotal($receivedData['total'] - $iva);
            $venta->setPorcentajeiva($this->iva);
            $venta->setIva($iva);
            $venta->setCotizacion('1');
            $venta->setNotas($receivedData['notas']);
            $venta->setGerente($gerente);

            $venta->setUsuarioIdusuario($gerente);
            $venta->setClienteIdcliente($user);
            $venta->setTipoventaIdtipoventa($tipoventa);
            $venta->setSucursalIdsucursal($gerente->getSucursalIdsucursal());
            $venta->setPagado($receivedData['total']);
            $venta->setLiquidada("0");
            $venta->setCredito(0);
            $venta->setSedescontodeinventario("0");
            $venta->setDiascredito(0);
            $venta->setFolio($this->setfolio($entityManager));
            // Persist and flush Venta
            $entityManager->persist($venta);


            //Se crean arreglos
            $stockspersists = [];
            $stockVentaspersists = [];
            $Cupon = null;

            if ($receivedData['cupon']) {
                $Cupon = $entityManager->getRepository(Cupon::class)->findOneBy(['idcupon' => $receivedData['cupon']]);
                if ($Cupon != null) {
                    $curUsedCupons = $Cupon->getCuponesusados();
                    $Cupon->setCuponesusados($curUsedCupons + 1);
                    $entityManager->persist($Cupon);
                    $Ventacupon = new Ventacupon();
                    $Ventacupon->setCuponIdcupon($Cupon);
                    $Ventacupon->setVentaIdventa($venta);
                    $Ventacupon->setFechacreacion($now);
                    $Ventacupon->setFechaactualizacion($now);
                    $Ventacupon->setPorcentajedescuento($Cupon->getPorcentajedescuento());
                    $entityManager->persist($Ventacupon);
                }
            }

            foreach ($receivedData['items'] as $key => $item) {

                $producto = $entityManager->getRepository(Producto::class)->findOneBy(['idproducto' => $item['id']]);

                if ($producto) {
                    $queryBuilder = $entityManager->createQueryBuilder();
                    $queryBuilder
                        ->select('s')
                        ->from('App\Entity\Stock', 's')
                        ->join('s.productoIdproducto', 'p')
                        ->innerJoin('s.sucursalIdsucursal', 'suc')
                        ->where('s.status = 1')
                        ->andWhere('s.cantidad > 0')
                        ->andWhere('p.idproducto = :producto')
                        ->andWhere('suc.codigo IN (:succode)')
                        ->orderBy('s.modificacion', 'ASC')
                        ->setParameter('producto', $item['id'])
                        ->setParameter('succode', ['ALMACEN2', 'E-COMMERCE']);

                    $stocks = $queryBuilder->getQuery()->getResult();



                    foreach ($stocks as $stock) {

                        if (array_key_exists($stock->getIdstock(), $stockspersists)) {
                            $existingstock = $stockspersists[$stock->getIdstock()];

                            $stock->setCantidad($existingstock->getCantidad());
                            if ($stock->getCantidad() <= 0) {
                                unset($stocks, $stock);
                            }
                        }
                    }

                    if (count($stocks) <= 0) {
                        return new JsonResponse(['error' => 'Sin stock']);
                    }

                    $tmpamount = (int) $item['amount'];

                    foreach ($stocks as $stock) {
                        if ($tmpamount <= 0) {
                            break;
                        } else if ($tmpamount > 0) {
                            $diff = $tmpamount - $stock->getCantidad();
                            $addamount = ($diff > 0) ? ($stock->getCantidad()) : ($stock->getCantidad() + $diff);

                            $price = (float) $item['precio'];
                            $tmpDiscount = 0;
                            if ($Cupon != null)
                                $tmpDiscount = $Cupon->getPorcentajedescuento();
                            $discount = $item['discount'] > $tmpDiscount ? $item['discount'] : $tmpDiscount;

                            $preciofinal = (1 - ($discount / 100)) * $price;
                            $tmpamount -= $addamount;

                            for ($i = 0; $i < $addamount; $i++) {
                                $fatherstockventa = new Stockventa();
                                $fatherstockventa->setCreacion($now)
                                    ->setModificacion($now)
                                    ->setCantidad(1)
                                    ->setPrecio($price)
                                    ->setCosto($stock->getProductoIdproducto()->getCosto())
                                    ->setPorcentajecomision(0.0)
                                    ->setComision($price * 0.0)
                                    ->setPorcentajedescuento($discount)
                                    ->setPreciofinal($preciofinal)
                                    ->setEstaapartado("1")
                                    ->setFechaapartado($now)
                                    ->setPreciobase($price)
                                    ->setStockIdstock($stock);

                                $stockVentaspersists[] = $fatherstockventa;
                            }

                            $stock->setCantidad($stock->getCantidad() - $addamount)
                                ->setModificacion($now);

                            $stockspersists[$stock->getIdstock()] = $stock;
                        }
                    } 

                    $sucursal= $entityManager->getRepository(Sucursal::class)->findOneBy(['codigo' => 'E-COMMERCE']);
                    $stockstate= $entityManager->getRepository(Stockstate::class)->findOneBy(['name' => 'DISPONIBLE']);

                    if (isset($item["extraInfo"]["tratamientos"]) && array_key_exists($item["extraInfo"]["tratamientos"]["name"], $this->tratamientos)) {
                        $micaID = $this->micas[$item["extraInfo"]["tratamientos"]["name"]];
                        $micaPrice = $item["extraInfo"]["tratamientos"]["price"];

                        $micaProducto = $entityManager->getRepository(Producto::class)->findOneBy(['idproducto' => $micaID]);

                        $queryBuilder = $entityManager->createQueryBuilder();
                        $queryBuilder
                            ->select('s')
                            ->from('App\Entity\Stock', 's')
                            ->join('s.productoIdproducto', 'p')
                            ->innerJoin('s.sucursalIdsucursal', 'suc')
                            ->where('s.status = 1')
                            ->andWhere('s.cantidad > 0')
                            ->andWhere('p.idproducto = :producto')
                            ->andWhere('suc.codigo IN (:succode)')
                            ->orderBy('s.modificacion', 'ASC')
                            ->setParameter('producto', $micaID)
                            ->setParameter('succode', ['E-COMMERCE']);

                        $micastock= $queryBuilder->getQuery()->getResult()[0] ?? new Stock();

                        $micastock->setCantidad(1)->setCreacion($micastock->getCreacion()?? $now)->setModificacion($now)->setStatus('1')->setTipo('1')->setPrecio($micaPrice)->setCosto($micaProducto->getCosto())->setSucursalIdsucursal($sucursal)->setProductoIdproducto($micaProducto)->setStockstateIdstockstate($micastock->getStockstateIdstockstate() ?? $stockstate);

                        $entityManager->persist($micastock);
                        $entityManager->flush();

                        $stockventa = new Stockventa();
                                $stockventa->setCreacion($now)
                                    ->setModificacion($now)
                                    ->setCantidad(1)
                                    ->setPrecio($micaPrice)
                                    ->setCosto($micastock->getProductoIdproducto()->getCosto())
                                    ->setPorcentajecomision(0.0)
                                    ->setComision($micaPrice * 0.0)
                                    ->setPorcentajedescuento($discount)
                                    ->setPreciofinal($preciofinal)
                                    ->setEstaapartado("1")
                                    ->setFechaapartado($now)
                                    ->setPreciobase($micaPrice)
                                    ->setStockIdstock($micastock);

                                $stockVentaspersists[] = $fatherstockventa;
                    }
 
                } else {
                    return new JsonResponse(['error' => 'Producto no encontrado']);

                }
            }

            $venta->setCotizacion('0')->setLiquidada("1")->setSedescontodeinventario("1");
            $venta->setFechaventa($now ?? $venta->getFechaventa());

            $entityManager->persist($venta);
            $entityManager->flush();

            foreach ($stockspersists as $stock) {
                $entityManager->persist($stock);
                $entityManager->flush();
            }
            foreach ($stockVentaspersists as $stockventas) {
                $stockventas->setVentaIdventa($venta);
                $entityManager->persist($stockventas);
                $entityManager->flush();
            }

            $pago = new Pago();
            $PaymentType = $entityManager->getRepository(Paymenttype::class)->findOneBy(['name' => "Pago online"]);
            $pago->setMonto($venta->getPagado())->setFecha($now)->setRestan(0)->setTipopago("Pago online")->setMesesintereses(0)->setUsuarioCobro($gerente)->setVentaIdventa($venta)->setValidado('1')->setPaymenttypeIdpaymenttype($PaymentType);

            $entityManager->persist($pago);
            $entityManager->flush();

            $shipment = new Shipment();
            $shipment->setCreationdate($now)->setUpdatedate($now)->setShipmentstage("0")->setTrackingnumber("none")->setVentaIdventa($venta)->setShippingaddress(json_encode($receivedData['address']));

            $entityManager->persist($shipment);
            $entityManager->flush();


            // Prepare the response data
            $responseData = [
                'status' => 'Success',
                'venta' => [
                        'id' => $venta->getIdventa(),
                        'folio' => $venta->getFolio(),
                        'total' => $venta->getTotal(),
                        'items' => $receivedData['items'],
                        'address' => $receivedData['address'],
                        'tratamientos' => $receivedData['tratamientos'],
                    ]
            ];

            // Return the data as a JSON response
            return new JsonResponse($responseData);

        } catch (\Exception $e) {
            // Catch any errors and return a 500 Internal Server Error response
            return new JsonResponse([
                'error' => 'An error occurred ' . $e->getLine(),
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * @Route("/api/ventaInfo/{idventa}", name="api_ventaInfo", methods={"POST", "GET"})
     */
    public function apiVentaTicket(Request $request, EntityManagerInterface $entityManager, string $idventa = null): JsonResponse
    {
        $em = $this->getDoctrine()->getManager();
       
        try {

            $query = $em->createQuery(
                'SELECT v
                 FROM App\Entity\Venta v
                 WHERE v.status = :status AND v.idventa = :idventa'
            )->setParameters(['status' => '1', 'idventa' => $idventa]);
            
            $Venta = $query->getOneOrNullResult();
            
            if (!$Venta) {
                return new JsonResponse(['error' => 'Venta no encontrada']);
            }
            
            $dql = 'SELECT sv.cantidad, p.modelo, sv.porcentajedescuento, sv.preciofinal
                    FROM App\Entity\Stockventa sv
                    INNER JOIN sv.stockIdstock s
                    INNER JOIN sv.ventaIdventa v
                    INNER JOIN s.productoIdproducto p
                    WHERE sv.status = 1 AND v.idventa = :idventa';
            
            $query = $em->createQuery($dql)->setParameter('idventa', $Venta->getIdventa());
            $stockVentas = $query->getResult();
            
            $responseData = [
                'venta' => [
                    'folio' => $Venta->getFolio(),
                    'total' => $Venta->getTotal(),
                    'iva' => $Venta->getIva(),
                    'pagado' => $Venta->getPagado(),
                    'fechaventa' => $Venta->getFechaventa(),
                ],
                'stockVentas' => array_map(function ($sv) {
                    return [
                        'cantidad' => $sv['cantidad'],
                        'modelo' => $sv['modelo'],
                        'porcentajeDescuento' => $sv['porcentajedescuento'],
                        'precioFinal' => $sv['preciofinal'],
                    ];
                }, $stockVentas),
            ];
            
            return new JsonResponse($responseData);

        } catch (\Exception $e) {
            return new JsonResponse([
                'error' => 'An error occurred ' . $e->getLine(),
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * @Route("/api/sendgraduation", name="api_venta-send-graduation", methods={"POST", "GET"})
     */
    public function handleGraduationData(Request $request, MailService $mailer): JsonResponse
    {
        // Decode JSON data
        $appointments = json_decode($request->request->get('appointments'), true);
        $customer = $request->request->get('customer');
        $email = $request->request->get('email');
        $folio = $request->request->get('folio');

        // Retrieve files array
        $files = $request->files->get('files'); // Will be an array of UploadedFile objects
        $path = $this->getParameter('prescriptionsFolder');
        $success = false;
        $msj = "";
        $filesReceived = [];

        try {
            if ($files) {
                foreach ($files as $key => $file) {
                    if ($file) {
                        // Process each file (e.g., save to a directory)
                        $file->move($path . '/', $file->getClientOriginalName());
                    }
                    $filesReceived[$key] = $file->getClientOriginalName();
                }
            }

            $emailContext = [
                "subject" => "Graduación para venta " . $folio,
                "appointments" => $appointments,
                "customerName" => $customer,
                "path" => $path . '/',
                "filesReceived" => $filesReceived,
                "customerEmail" => $email,
                "folio" => $folio
            ];

            $mailer->sendEmailGraduation($emailContext);

            $success = true;
        } catch (\Exception $e) {
            $msj = $e->getMessage();
        }


        return new JsonResponse(['success' => $success, 'msj' => $msj]);
    }

    public function setfolio(EntityManagerInterface $entityManager): int
    {

        $queryBuilder = $entityManager->createQueryBuilder();
        $result = $queryBuilder
            ->select('v.folio')
            ->from('App\Entity\Venta', 'v')
            ->innerJoin('v.sucursalIdsucursal', 's')
            ->innerJoin('s.empresaIdempresa', 'e')
            ->where('e.rfc = :empresa')
            ->orderBy('v.idventa', 'DESC')
            ->setParameter('empresa', $this->empRFC)
            ->setMaxResults(1)
            ->getQuery()
            ->getSingleScalarResult();

        $ultimoFolio = (int) $result + 1;

        return $ultimoFolio;
    }
}