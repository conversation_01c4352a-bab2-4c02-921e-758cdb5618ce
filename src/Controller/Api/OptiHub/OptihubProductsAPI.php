<?php

namespace App\Controller\Api\OptiHub;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Doctrine\ORM\EntityManagerInterface;
use Twig\Environment;

class OptihubProductsAPI extends AbstractController
{
    private $numOfReturns = 16;
    private $empRFC = "GOM180228DD4";
    public $prices = ['precio', 'precioEspecial', 'precioDistribuidor', 'precioSubdistribuidor'];
    private $twig;
    private $tipoventa;

    public function __construct(Environment $twig)
    {
        $this->twig = $twig;
    }

    /**
     * @Route("/api/filters/{index}/{amount}", name="api_filters", methods={"GET"})
     */
    public function getFilters(EntityManagerInterface $entityManager, Request $request, int $index = 0, int $amount = 16): JsonResponse
    {
        $queryBuilder = $entityManager->createQueryBuilder();
        $queryBuilder
            ->select('DISTINCT tv')
            ->from('App\Entity\Tipoventa', 'tv')
            ->join('tv.empresaIdempresa', 'emp')
            ->where('tv.status = 1')
            ->andWhere('tv.onlinestore = 1')
            ->andWhere('emp.rfc = :empresa')
            ->setParameter('empresa', $this->empRFC)
            ->setMaxResults(1);

        $this->tipoventa = $queryBuilder->getQuery()->getOneOrNullResult();
        $precioBase = $this->tipoventa->getPreciobase();

        $precioBaseInt = intval($precioBase);

        $query = $entityManager->createQuery(
            'SELECT DISTINCT
            m.idmarca as m_id, m.nombre as m_n, prod.design, prod.precio,
            cat.idcategoria as sub_id, cat.nombre as sub_n, 
            mat.idframematerial as mat_id, mat.material as mat_n,
            color.idframecolor as color_id, color.color as color_n, 
            prod.' . $this->prices[$precioBaseInt] . ' as price, prod.showonlinestore
            FROM App\Entity\Stock st 
            INNER JOIN st.productoIdproducto prod
            INNER JOIN st.sucursalIdsucursal suc
            INNER JOIN prod.marcaIdmarca m
            INNER JOIN App\Entity\Cuponmarca cm WITH m = cm.marcaIdmarca
            INNER JOIN prod.categoriaIdcategoria cat
            INNER JOIN App\Entity\Productimages prodimg WITH prod = prodimg.productoIdproducto
            LEFT JOIN prod.framematerialIdframematerial mat
            LEFT JOIN prod.framecolorIdframecolor color
            WHERE prod.showonlinestore = 1 AND st.status = 1 AND st.cantidad > 0 AND suc.codigo in (:codes)  AND cat.idcategoria != 163
            GROUP BY prod.idproducto    
            ORDER BY m.nombre, color.color, mat.material, cat.nombre, prod.precio ASC'
        )->setParameters(['codes' => ['ALMACEN2', 'E-COMMERCE']]);

        $result = $query->getResult();


        $returnarray = [];
        $range = 100;

        foreach ($result as $item) {
            if ((int)$item['price'] > $range)
                $range = (int)$item['price'];

            if ($item['design'] && $item['design'] != "")
                $returnarray['diseño'][$item['design']] = $item['design'];
            if ($item['m_n'] && $item['m_n'] != "NA")
                $returnarray['marcas'][$item['m_id']] = $item['m_n'];
            if ($item['sub_n'] && $item['sub_n'] != "")
                $returnarray['categorias'][$item['sub_id']] = $item['sub_n'];
            if ($item['mat_n'] && $item['mat_n'] != "")
                $returnarray['materiales'][$item['mat_id']] = $item['mat_n'];
            if ($item['color_n'] && $item['color_n'] != "")
                $returnarray['colores'][$item['color_id']] = $item['color_n'];
        }

        $range += 100;

        // Extracting filters from the request
        $filters = [];
        $parameters = [];

        $sort = $request->query->get('sort', 'ASC');
        $name = $request->query->get('name');
        $tag = $request->query->get('tag');
        $max = $request->query->get('max', $range);
        $min = $request->query->get('min', 0);
        $modelo = $request->query->get('modelo');


        $estilos = $request->query->get('estilo');
        $marcas = $request->query->get('marcas');
        $categorias = $request->query->get('categorias');
        $materiales = $request->query->get('materiales');
        $colores = $request->query->get('colores');

        if ($modelo) {
            $filters[] = 'prod.modelo LIKE :modelovalue';
            $parameters['modelovalue'] = '%' . $modelo . '%';
        }

        // Building the filters
        if ($name) {
            $filters[] = 'prod.modelo LIKE :namevalue';
            $filters[] = 'm.nombre LIKE :namevalue';
            $parameters['namevalue'] = '%' . $name . '%';
        }

        if ($tag) {
            $filters[] = 'tag.idtag LIKE :tagvalue';
            $parameters['tagvalue'] = '%' . $tag . '%';
        }
        $whereBrandCupon = "";
        $parametersCupon = [];
        if ($marcas) {
            $filters[] = 'm.idmarca IN (:marcasvalue)';
            $parameters['marcasvalue'] = $marcas;
            $whereBrandCupon = "WHERE m.idmarca IN (:marcasvalue)";
            $parametersCupon['marcasvalue'] = $marcas;
        }
        if ($categorias) {
            $filters[] = 'cat.idcategoria IN (:categoriasvalue)';
            $parameters['categoriasvalue'] = $categorias;
        }
        if ($materiales) {
            $filters[] = 'mat.idframematerial IN (:materialvalue)';
            $parameters['materialvalue'] = $materiales;
        }
        if ($colores) {
            $filters[] = 'color.idframecolor IN (:coloresvalue)';
            $parameters['coloresvalue'] = $colores;
        }
        if ($estilos) {
            $filters[] = 'prod.design IN (:designvalue)';
            $parameters['designvalue'] = $estilos;
        }

        $filters[] = '(prod.' . $this->prices[$precioBaseInt] . ' BETWEEN :min AND :max)';
        $parameters['min'] = $min;
        $parameters['max'] = $max;

        $filters[] = 'suc.codigo IN (:codes)';
        $parameters['codes'] = ['ALMACEN2', 'E-COMMERCE'];

        $hasCupons = $request->query->get('hasCupons', false);

        if ($hasCupons) {
            $filters[] = 'EXISTS (
        SELECT 1
        FROM App\Entity\Cuponmarca cm
        WHERE cm.marcaIdmarca = m.idmarca)';
        }


        // Building the query dynamically
        $dql = 'SELECT prod.idproducto as id, prod.modelo, prodimg.filename, prod.' . $this->prices[$precioBaseInt] . ' as price, m.nombre as brand, m.idmarca
            FROM App\Entity\Stock st 
            INNER JOIN st.productoIdproducto prod
            INNER JOIN st.sucursalIdsucursal suc
            INNER JOIN prod.categoriaIdcategoria cat
            INNER JOIN prod.marcaIdmarca m
            LEFT JOIN App\Entity\Productimages prodimg WITH prod = prodimg.productoIdproducto
            LEFT JOIN App\Entity\Productotag prodtag WITH prod = prodtag.productoIdproducto
            LEFT JOIN prodtag.tagIdtag tag
            LEFT JOIN prod.framecolorIdframecolor color
            LEFT JOIN prod.framematerialIdframematerial mat
            WHERE prodimg.mainimage = 1 AND prod.showonlinestore = 1 AND st.status = 1
            AND st.cantidad > 0 AND prod.creacion IS NOT NULL
            ';

        if (!empty($filters)) {
            $dql .= ' AND ' . implode(' AND ', $filters);
        }



        $dql .= ' GROUP BY prod.idproducto, prodimg.idproductimages';

        if ($sort) {
            $dql .= " ORDER BY prod.precio " . $sort;
        }

        $query = $entityManager->createQuery($dql)->setMaxResults($amount)->setFirstResult($index * $amount)->setParameters($parameters);
        $products = $query->getResult();

        $query = $entityManager->createQuery(
            'SELECT m.idmarca as brand, c.porcentajedescuento
    FROM App\Entity\Cuponmarca cm
    INNER JOIN cm.marcaIdmarca m
    INNER JOIN cm.cuponIdcupon c
    WHERE m.idmarca IN (:marcasvalue)
    ORDER BY cm.idcuponmarca ASC'
        )->setParameters(['marcasvalue' => array_column($products, 'idmarca')]);

        $cupons = $query->getResult();

        $query = $entityManager->createQuery(
            'SELECT m.idmarca as brand, c.porcentajedescuento
        FROM App\Entity\Cuponmarca cm
        INNER JOIN cm.marcaIdmarca m
        INNER JOIN cm.cuponIdcupon c
        ' . $whereBrandCupon . ' ORDER BY cm.idcuponmarca ASC'
        )->setParameters($parametersCupon);
        $cupons = $query->getResult();

        $formattedCupons = [];
        foreach ($cupons as $Cupon) $formattedCupons[$Cupon["brand"]] = $Cupon;

        $allprods = $entityManager->createQuery($dql)->setParameters($parameters)->getResult();
        $count = floor(count($allprods) / $amount) - 2;

        $filters = [
            'values' => $returnarray,
            'range' => $range,
            'products' => $products,
            'index' => $index,
            'count' => $count,
            'dql' => $query->getDQL(),
            'cupons' => $formattedCupons
        ];

        return new JsonResponse($filters);
    }


    /**
     * @Route("/api/item/{productid}", name="api_item", methods={"GET"})
     */
    public function item(string $productid = null, EntityManagerInterface $entityManager): JsonResponse
    {
        $parameters = ['idproducto' => $productid, 'status' => '1'];

        // Query to get the main product details
        $query = $entityManager->createQuery(
            'SELECT p.idproducto, fc.colorcode, fc.color, fm.material, p.modelo, p.precio, p.graduable,
        m.nombre AS marca, m.idmarca, p.descriptiononlinestore, SUM(s.cantidad) as totalCantidad, p.moreinfo,
        p.gender as genero, med.nombre as medida, unimed.nombre as unidad ,cat.nombre as armazonCat, p.design
        FROM App\Entity\Producto p
        INNER JOIN p.marcaIdmarca m
        LEFT JOIN p.framecolorIdframecolor fc
        LEFT JOIN p.framematerialIdframematerial fm
        LEFT JOIN p.categoriaIdcategoria cat
        LEFT JOIN p.medidaIdmedida med
        LEFT JOIN med.unidadmedidaIdunidadmedida unimed
        LEFT JOIN App\Entity\Stock s With s.productoIdproducto = p.idproducto
        LEFT JOIN s.sucursalIdsucursal suc
        WHERE p.status =:status and p.idproducto =:idproducto
        ORDER BY p.precio ASC
        '
        )->setParameters($parameters);
        $productResult = $query->getOneOrNullResult();

        // Query to get product variants
        $query = $entityManager->createQuery(
            'SELECT pi.filename, p.idproducto as id, p.modelo, p.precio
        FROM App\Entity\Producto p
        LEFT JOIN App\Entity\Productimages pi WITH p.idproducto = pi.productoIdproducto
        WHERE p.status =:status AND p.modelo = :originalmodel AND pi.order =:order AND p.idproducto !=:idproducto
        ORDER BY p.precio ASC
        '
        )->setParameters([
            'originalmodel' => $productResult['modelo'],
            'order' => '1',
            'status' => '1',
            'idproducto' => $productid,
        ]);
        $productVariants = $query->getResult();

        // Query to get product images
        $query = $entityManager->createQuery(
            'SELECT pi.order, pi.filename
        FROM App\Entity\Productimages pi
        INNER JOIN pi.productoIdproducto p
        WHERE pi.status =:status AND p.idproducto =:idproducto'
        )->setParameters($parameters);
        $images = $query->getResult();

        // Query to get services
        $query = $entityManager->createQuery(
            'SELECT p.idproducto, p.modelo, p.precio, p.descriptiononlinestore
        FROM App\Entity\Producto p
        INNER JOIN p.categoriaIdcategoria c
        WHERE p.status =:status AND p.tipoproducto =:tipoproducto AND p.showonlinestore =:showonlinestore AND c.codigo =:categorycode
        ORDER BY p.precio ASC
        '
        )->setParameters([
            'status' => '1',
            'tipoproducto' => 2,
            'showonlinestore' => '1',
            'categorycode' => 'TRATAMIENTO',
        ]);
        $services = $query->getResult();

        // Query to get materials
        $query = $entityManager->createQuery(
            'SELECT p.idproducto, p.modelo, p.precio, p.descriptiononlinestore
        FROM App\Entity\Producto p
        INNER JOIN p.categoriaIdcategoria c
        WHERE p.status =:status AND p.tipoproducto =:tipoproducto AND p.showonlinestore =:showonlinestore AND c.codigo =:categorycode
        ORDER BY p.precio ASC
        '
        )->setParameters([
            'status' => '1',
            'tipoproducto' => 2,
            'showonlinestore' => '1',
            'categorycode' => 'MATERIAL',
        ]);
        $materials = $query->getResult();

        // Query to get vision areas
        $query = $entityManager->createQuery(
            'SELECT p.idproducto, p.modelo, p.precio, p.descriptiononlinestore
        FROM App\Entity\Producto p
        INNER JOIN p.categoriaIdcategoria c
        WHERE p.status =:status AND p.tipoproducto =:tipoproducto AND p.showonlinestore =:showonlinestore AND c.codigo =:categorycode
        ORDER BY p.precio ASC
        '
        )->setParameters([
            'status' => '1',
            'tipoproducto' => 2,
            'showonlinestore' => '1',
            'categorycode' => 'AREAVISION',
        ]);
        $visionAreas = $query->getResult();

        // Query to get dye colors
        $query = $entityManager->createQuery(
            'SELECT p.idproducto, p.modelo, p.precio, p.descriptiononlinestore, fc.color, fc.colorcode
        FROM App\Entity\Producto p
        INNER JOIN p.categoriaIdcategoria c
        INNER JOIN p.framecolorIdframecolor fc
        WHERE p.status =:status AND p.tipoproducto =:tipoproducto AND p.showonlinestore =:showonlinestore AND c.codigo =:categorycode
        ORDER BY p.precio ASC
        '
        )->setParameters([
            'status' => '1',
            'tipoproducto' => 2,
            'showonlinestore' => '1',
            'categorycode' => 'TINTE',
        ]);
        $dyeColors = $query->getResult();

        $brandId = isset($productResult["idmarca"]) ? $productResult["idmarca"] : -1;
        $query = $entityManager->createQuery(
            'SELECT m.idmarca as brand, c.porcentajedescuento
        FROM App\Entity\Cuponmarca cm
        INNER JOIN cm.marcaIdmarca m
        INNER JOIN cm.cuponIdcupon c
        WHERE m.idmarca =:brandId
        ORDER BY cm.idcuponmarca DESC'
        )->setParameters(["brandId" => $brandId])->setMaxResults(1);
        $cupon = $query->getOneOrNullResult();

        if (isset($productResult['genero'])) {
            switch ($productResult['genero']) {
                case 'U':
                    $productResult['genero'] = 'Unisex';
                    break;
                case 'H':
                    $productResult['genero'] = 'Hombre';
                    break;
                case 'M':
                    $productResult['genero'] = 'Mujer';
                    break;
                default:
                    $productResult['genero'] = 'No especificado';
                    break;
            }
        }

        // Return all the gathered data in a JSON response
        return new JsonResponse([
            'product' => $productResult,
            'productvariants' => $productVariants,
            'images' => $images,
            'services' => $services,
            'materials' => $materials,
            'visionAreas' => $visionAreas,
            'dyeColors' => $dyeColors,
            'cupon' => $cupon
        ]);
    }

    /**
     * @Route("/api/cupon/{cuponcode}", name="api_cupon", methods={"GET"})
     */
    public function cupon(string $cuponcode = null, EntityManagerInterface $entityManager): JsonResponse
    {
        $parameters = ['cupon' => $cuponcode, 'status' => '1'];

        $query = $entityManager->createQuery(
            'SELECT m.idmarca as brand, c.porcentajedescuento, c.idcupon
        FROM App\Entity\Cuponmarca cm
        LEFT JOIN cm.marcaIdmarca m
        INNER JOIN cm.cuponIdcupon c
        WHERE c.codigo =:cupon AND c.status=:status AND c.numerocupones > c.cuponesusados
        ORDER BY cm.idcuponmarca DESC'
        )->setParameters($parameters)->setMaxResults(1);
        $cupon = $query->getOneOrNullResult();

        // Return all the gathered data in a JSON response
        return new JsonResponse([
            'cupon' => $cupon
        ]);
    }

}
