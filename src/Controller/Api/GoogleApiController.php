<?php

namespace App\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Doctrine\ORM\EntityManagerInterface;
use App\Entity\Usuario;
use App\Entity\Eventtype;

class GoogleApiController extends AbstractController
{
    /**
     * @Route("/getGoogleToken", name="app_get_GoogleToken")
     */
    public function getGoogleToken(EntityManagerInterface $em): Response
    {
        $token = null;
        try {
            $user = $this->getUser();

            $token = $user->getGoogleauthtoken() ?? null;
        } catch (\Exception $e) {
            return new JsonResponse(['msg' => $e->getMessage()], Response::HTTP_NOT_FOUND);
        }

        // Render the Twig template and pass the events as JSON
        return new JsonResponse(['token' => $token], Response::HTTP_OK);
    }

    /**
     * @Route("/setGoogleToken", name="app_set_GoogleToken")
     */
    public function setGoogleToken(Request $request): Response
    {
        try {
            $token = $request->get('token');

            $em = $this->getDoctrine()->getManager();
            $User = $this->getUser();

            $User->setGoogleauthtoken($token);

            $em->persist($User);
            $em->flush();
        } catch (\Exception $e) {
            return new JsonResponse(['msg' => $e->getMessage()], Response::HTTP_NOT_FOUND);
        }

        return new JsonResponse(['msg' => 'saved token'], Response::HTTP_OK);
    }
}
