<?php

namespace App\Controller\Api\AppOptimo;

use App\Controller\InVoiceController;
use App\Entity\Clientefacturadatos;
use App\Entity\Pago;
use App\Entity\Paymenttype;
use App\Entity\Sucursal;
use App\Entity\Ventafactura;
use App\Form\InvoiceClientefacturadatosType;
use App\Service\FileUploader;
use App\Service\FacturamaService;
use PHP_CodeSniffer\Reports\Json;
use Psalm\Node\Expr\VirtualAssignRef;
use Psr\Log\LoggerInterface;

use App\Entity\Cliente;
use App\Entity\Empresa;
use App\Entity\Anuncios;
use App\Entity\Empresacliente;
use App\Entity\Graduacion;
use App\Entity\Venta;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;
use Symfony\Component\Security\Core\Encoder\UserPasswordEncoderInterface;
use Google_Client;
use Google_Service_Oauth2;
use Symfony\Component\HttpFoundation\Response;
use Doctrine\ORM\Query;
use Swift_SmtpTransport;
use Swift_Mailer;
use Swift_Message;

/**
 * @Route("/cliente-api", name="api_")
 */
class SecurityController extends AbstractController
{
    private $logger;
    private $facturamaService;

    public function __construct(LoggerInterface $logger, FacturamaService $facturamaService)
    {
        $this->logger = $logger;
        $this->facturamaService = $facturamaService;
    }

    /**
     * @Route("/register", name="api_users_create", methods={"POST"})
     */
    public function registerCustomer(Request $request, EntityManagerInterface $em, UserPasswordEncoderInterface $passwordEncoder, JWTTokenManagerInterface $JWTManager): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        // Extraer y validar datos
        $company = $data['company'] ?? 1;
        $companyNumber = $data['employeeNumber'] ?? null;
        $name = $data['name'] ?? null;
        $email = $data['email'] ?? null;
        $number = $data['number'] ?? null;
        $plainPassword = $data['password'] ?? null;


        if (!$number || !$plainPassword || !$name || !$email) {
            return new JsonResponse(['message' => 'Faltan datos: nombre, email, teléfono y contraseña son obligatorios'], 400);
        }

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return new JsonResponse(['message' => 'El formato del email no es válido'], 400);
        }

        try {
            $em->beginTransaction();

            $existingUser = $em->getRepository(Cliente::class)->findOneBy(['telefono' => $number, 'status' => 1]);

            if ($existingUser) {
                return $this->handleExistingUser($em, $existingUser, $plainPassword, $passwordEncoder, $number);
            } else {
                return $this->createNewUser($em, $name, $email, $number, $companyNumber, $company, $plainPassword, $passwordEncoder, $JWTManager);
            }

            $em->commit();

        } catch (\Exception $e) {
            $em->rollback();
            return new JsonResponse(['message' => 'Error al procesar la solicitud: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Maneja el caso de un usuario existente
     */
    private function handleExistingUser(EntityManagerInterface $em, Cliente $existingUser, string $plainPassword, UserPasswordEncoderInterface $passwordEncoder, string $number): JsonResponse
    {
        $encoded = $passwordEncoder->encodePassword($existingUser, $plainPassword);
        $existingUser->setPassword($encoded);

        // Buscar y fusionar duplicados
        $this->mergeDuplicateCustomers($em, $number);

        $em->persist($existingUser);
        $em->flush();

        return new JsonResponse([
            'message' => 'Usuario existente',
            'userExists' => true,
            'user' => [
                'id' => $existingUser->getIdcliente(),
                'nombre' => $existingUser->getNombre(),
                'email' => $existingUser->getEmail(),
            ]
        ], 200);
    }

    /**
     * Crea un nuevo usuario
     */
    private function createNewUser(EntityManagerInterface $em, ?string $name, ?string $email, string $number, ?string $companyNumber, $company, string $plainPassword, UserPasswordEncoderInterface $passwordEncoder, JWTTokenManagerInterface $JWTManager): JsonResponse
    {
        $user = new Cliente();
        $user->setNombre($name);
        $user->setEmail($email);
        $user->setTelefono($number);
        $user->setNumeroempleado($companyNumber);

        if ($company) {
            $clientEnterprise = $em->getRepository(Empresacliente::class)->findOneBy(['idempresacliente' => $company]);
            $user->setEmpresaclienteIdempresacliente($clientEnterprise);
        }

        $encoded = $passwordEncoder->encodePassword($user, $plainPassword);
        $user->setPassword($encoded);

        $em->persist($user);
        $em->flush();

        $token = $JWTManager->create($user);

        return new JsonResponse([
            'message' => 'Usuario creado con éxito',
            'token' => $token,
            'user' => [
                'id' => $user->getIdcliente(),
                'nombre' => $user->getNombre(),
                'email' => $user->getEmail(),
            ]
        ], 201);
    }

    /**
     * Fusiona clientes duplicados, eligiendo el cliente con más historial como principal
     * y transfiriendo solo las ventas
     */
    private function mergeDuplicateCustomers(EntityManagerInterface $em, string $number): void
    {
        try {
            // Iniciar transacción
            $em->beginTransaction();

            // Buscar todos los clientes activos con el mismo número de teléfono
            $query = $em->createQuery(
                'SELECT c
            FROM App\Entity\Cliente c
            WHERE c.telefono = :telefono AND c.status = 1'
            )->setParameters(['telefono' => $number]);

            $clientes = $query->getResult();

            if (count($clientes) <= 1) {
                return;
            }

            // Determinar el cliente con más historial (más ventas)
            $clientePrincipal = null;
            $maxVentas = -1;

            foreach ($clientes as $cliente) {
                $ventasCount = $em->createQuery(
                    'SELECT COUNT(v.idventa)
                FROM App\Entity\Venta v
                WHERE v.clienteIdcliente = :cliente'
                )->setParameter('cliente', $cliente)
                    ->getSingleScalarResult();

                if ($ventasCount > $maxVentas) {
                    $maxVentas = $ventasCount;
                    $clientePrincipal = $cliente;
                }
            }

            // Si no se encontró un cliente principal (ninguno tiene ventas), usar el primero
            if (!$clientePrincipal && count($clientes) > 0) {
                $clientePrincipal = $clientes[0];
            }

            // Transferir solo las ventas de los clientes duplicados al cliente principal
            foreach ($clientes as $cliente) {
                if ($cliente->getIdcliente() === $clientePrincipal->getIdcliente()) {
                    continue; // Saltar el cliente principal
                }

                // Transferir ventas
                $ventas = $em->createQuery(
                    'SELECT v
                FROM App\Entity\Venta v
                WHERE v.clienteIdcliente = :cliente'
                )->setParameter('cliente', $cliente)
                    ->getResult();

                foreach ($ventas as $venta) {
                    $venta->setClienteIdcliente($clientePrincipal);
                    $em->persist($venta);
                }

                // Desactivar el cliente duplicado
                $cliente->setStatus('0');
                $em->persist($cliente);
            }

            // Asegurar que el cliente principal esté activo
            $clientePrincipal->setStatus('1');
            $em->persist($clientePrincipal);

            // Guardar todos los cambios
            $em->flush();
            $em->commit();

        } catch (\Exception $e) {
            // En caso de error, revertir la transacción
            if ($em->getConnection()->isTransactionActive()) {
                $em->rollback();
            }

            // Registrar el error pero no interrumpir el flujo
            error_log('Error al fusionar clientes: ' . $e->getMessage());
        }
    }

    /**
     * @Route("/update-email", name="updateEmail", methods={"POST"})
     */
    public function updateEmail(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $phone = $data['phone'] ?? null;
        $email = $data['email'] ?? null;

        if (!$phone || !$email) {
            return new JsonResponse(['message' => 'Número de teléfono y correo son requeridos'], 400);
        }

        $cliente = $em->getRepository(Cliente::class)->findOneBy(['telefono' => $phone]);

        if (!$cliente) {
            return new JsonResponse(['message' => 'Cliente no encontrado'], 404);
        }

        $cliente->setEmail($email);

        try {
            $em->persist($cliente);
            $em->flush();
            return new JsonResponse(['message' => 'Correo actualizado correctamente'], 200);
        } catch (\Exception $e) {
            return new JsonResponse(['message' => 'Error al actualizar correo', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * @Route("/send-verification-code", name="sendVerificationCode", methods={"POST"})
     */
    public function sendVerificationCode(Request $request, EntityManagerInterface $em, \Swift_Mailer $mailer): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $email = $data['email'] ?? null;
        $cliente = null;

        if ($email) {
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                return new JsonResponse(['message' => 'Correo inválido'], 400);
            }
            $cliente = $em->getRepository(Cliente::class)->findOneBy(['email' => $email, 'status' => 1]);
        }

        if (!$cliente) {
            return new JsonResponse(['message' => 'Cliente no encontrado'], 400);
        }

        // Generar y guardar código de verificación
        $verificationCode = random_int(100000, 999999);
        $cliente->setHosteddomain($verificationCode);
        $em->persist($cliente);
        $em->flush();

        // Configurar correo
        $smtpUser = '<EMAIL>';
        $smtpPassword = 'jbnefpwybhunaffk';

        $transport = (new Swift_SmtpTransport('smtp.gmail.com', 465, 'ssl'))
            ->setUsername($smtpUser)
            ->setPassword($smtpPassword);

        $mailer = new Swift_Mailer($transport);

        $messageBody = "
    <html>
    <body style='font-family: Arial, sans-serif; color: #333; text-align: center;'>
        <h2 style='color: #124DDE;'>¡Hola desde Óptimo Ópticas!</h2>
        <p>Hemos encontrado tu usuario, ¡Y ahora es momento de actualizar tu contraseña!</p>
        <p><strong>Tu código de verificación es:</strong></p>
        <h1 style='color: #124DDE;'>$verificationCode</h1>
        <p>Introduce este código en la aplicación para continuar con el proceso.</p>
        <hr>
        <p style='font-size: 12px; color: #777;'>Este correo es automático, por favor no respondas.</p>
        <p style='font-size: 12px; color: #777;'>Si no solicitaste este código, ignora este mensaje.</p>
    </body>
    </html>";

        if ($email) {
            $message = (new Swift_Message('🔑 Código de Verificación - Óptimo Ópticas'))
                ->setFrom($smtpUser)
                ->setTo($email)
                ->setBody($messageBody, 'text/html');
            $mailer->send($message);
        }

        return new JsonResponse(['message' => 'Código enviado correctamente'], 200);
    }


    /**
     * @Route("/verify-code-update-email", name="verifyCodeUpdateEmail", methods={"POST"})
     */
    public function verifyCodeAndUpdateEmail(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $phone = $data['phone'] ?? null;
            $email = $data['email'] ?? null;
            $code = $data['code'] ?? null;
            $forgetPassword = $data['forgetPassword'] ?? null;

            if ($forgetPassword == 1) {
                $cliente = $em->getRepository(Cliente::class)->findOneBy(['email' => $email, 'status' => 1]);

                if (!$cliente && $cliente->getHosteddomain() !== $code) {
                    return new JsonResponse(['message' => 'Cliente no encontrado con email o codigo incorrecto'], 404);
                }

                $cliente->setEmail($email);
                $cliente->setHosteddomain(null);
                $em->persist($cliente);
                $em->flush();

                return new JsonResponse(['message' => 'Correo actualizado correctamente'], 200);
            }

            if (!$email || !$code) {
                return new JsonResponse(['message' => 'Datos incompletos'], 400);
            }

            $cliente = $em->getRepository(Cliente::class)->findOneBy(['telefono' => $phone]);

            if (!$cliente) {
                return new JsonResponse(['message' => 'Cliente no encontrado'], 404);
            }

            if ($cliente->getHosteddomain() !== $code) {
                return new JsonResponse(['message' => 'Código incorrecto'], 400);
            }

            $cliente->setEmail($email);
            $cliente->setHosteddomain(null);
            $em->persist($cliente);
            $em->flush();

            return new JsonResponse(['message' => 'Correo actualizado correctamente'], 200);

        } catch (\Exception $e) {
            return new JsonResponse(['message' => 'Error en el servidor', 'error' => $e->getMessage()], 500);
        }
    }


    /**
     * @Route("/update-profile-app", name="updateProfile", methods={"POST"})
     */
    public function updateProfile(Request $request): JsonResponse
    {
        $user = $this->getUser();
        $em = $this->getDoctrine()->getManager();

        if (!$user) {
            return new JsonResponse([
                'status' => 'error',
                'code' => 401,
                'message' => 'No se encontró usuario o sesión expirada'
            ], 401);
        }

        $idcliente = $user->getIdcliente();

        $cliente = $em->getRepository(Cliente::class)->find($idcliente);

        if (!$cliente) {
            return new JsonResponse([
                'status' => 'error',
                'code' => 404,
                'message' => 'Cliente no encontrado'
            ], 404);
        }

        // Validar el formato de los datos recibidos
        $data = json_decode($request->getContent(), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return new JsonResponse([
                'status' => 'error',
                'code' => 400,
                'message' => 'Formato JSON inválido'
            ], 400);
        }

        // Guardar el teléfono original para detectar cambios
        $originalPhone = $cliente->getTelefono();
        $fieldsUpdated = [];

        // Actualizar los campos del cliente si están presentes en la solicitud
        if (isset($data['name'])) {
            $cliente->setNombre($data['name']);
            $fieldsUpdated[] = 'nombre';
        }
        if (isset($data['lastname'])) {
            $cliente->setApellidoPaterno($data['lastname']);
            $fieldsUpdated[] = 'apellido paterno';
        }
        if (isset($data['surname'])) {
            $cliente->setApellidoMaterno($data['surname']);
            $fieldsUpdated[] = 'apellido materno';
        }
        if (isset($data['phone'])) {
            // Validar formato de teléfono
            if (!preg_match('/^\d{10}$/', $data['phone'])) {
                return new JsonResponse([
                    'status' => 'error',
                    'code' => 400,
                    'message' => 'Formato de teléfono inválido. Debe contener 10 dígitos.'
                ], 400);
            }
            $cliente->setTelefono($data['phone']);
            $fieldsUpdated[] = 'teléfono';
        }
        if (isset($data['email'])) {
            // Validar formato de email
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                return new JsonResponse([
                    'status' => 'error',
                    'code' => 400,
                    'message' => 'Formato de correo electrónico inválido'
                ], 400);
            }
            $cliente->setEmail($data['email']);
            $fieldsUpdated[] = 'email';
        }
        if (isset($data['convenio'])) {
            $cliente->setTipocliente($data['convenio']);
            $fieldsUpdated[] = 'convenio';
        }
        if (isset($data['numeroEmpleado'])) {
            $cliente->setNumeroempleado($data['numeroEmpleado']);
            $fieldsUpdated[] = 'número de empleado';
        }

        // Marcar el cliente como verificado
        $cliente->setIsverified(true);

        try {
            $em->beginTransaction();
            $em->persist($cliente);

            // Detectar y fusionar clientes duplicados si se actualizó el teléfono
            $duplicatesMerged = false;
            if (isset($data['phone']) && $data['phone'] !== $originalPhone) {
                $this->mergeDuplicateCustomers($em, $data['phone']);
                $duplicatesMerged = true;
            }

            $em->flush();
            $em->commit();

            $response = [
                'status' => 'success',
                'code' => 200,
                'message' => 'Perfil actualizado correctamente'
            ];

            if (!empty($fieldsUpdated)) {
                $response['updated_fields'] = $fieldsUpdated;
            }

            if ($duplicatesMerged) {
                $response['duplicate_merged'] = true;
                $response['message'] .= '. Se han fusionado perfiles duplicados';
            }

            return new JsonResponse($response, 200);
        } catch (\PDOException $e) {
            // Revertir la transacción en caso de error
            if ($em->getConnection()->isTransactionActive()) {
                $em->rollback();
            }

            // Manejar errores específicos de la base de datos
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                return new JsonResponse([
                    'status' => 'error',
                    'code' => 409,
                    'message' => 'Ya existe un cliente con esos datos'
                ], 409);
            }

            return new JsonResponse([
                'status' => 'error',
                'code' => 500,
                'message' => 'Error en la base de datos',
                'details' => $e->getMessage()
            ], 500);
        } catch (\Exception $e) {
            // Revertir la transacción en caso de error
            if ($em->getConnection()->isTransactionActive()) {
                $em->rollback();
            }

            return new JsonResponse([
                'status' => 'error',
                'code' => 500,
                'message' => 'Error al actualizar perfil',
                'details' => $e->getMessage()
            ], 500);
        }
    }


    /**
     * @Route("/get-profile-app", name="getProfile", methods={"GET"})
     */

    public function getProfileApp(Request $request): JsonResponse
    {
        $em = $this->getDoctrine()->getManager();
        $customer = $em->getRepository(Cliente::class)->findOneBy([
            'idcliente' => $this->getUser()->getIdcliente(),
            'status' => 1,
        ]);

        if (!$customer) {
            return new JsonResponse(['message' => 'No se encontró cliente'], 401);
        }

        return new JsonResponse([
            'id' => $customer->getIdcliente(),
            'nombre' => $customer->getNombre(),
            'apellidoPaterno' => $customer->getApellidoPaterno(),
            'apellidoMaterno' => $customer->getApellidoMaterno(),
            'telefono' => $customer->getTelefono(),
            'email' => $customer->getEmail(),
            'numeroempleado' => $customer->getNumeroempleado(),
            'isVerified' => $customer->getIsverified(),
            'convenio' => $customer->getTipocliente(),
        ]);
    }


    /**
     * @Route("/update-password", name="updatePassword", methods={"POST"})
     */
    public function updatePassword(Request $request, EntityManagerInterface $em, UserPasswordEncoderInterface $passwordEncoder): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $phone = $data['phone'] ?? null;
            $newPassword = $data['newPassword'] ?? null;

            if (!$phone || !$newPassword) {
                return new JsonResponse(['message' => 'Número de teléfono y nueva contraseña son requeridos'], 400);
            }

            $cliente = $em->getRepository(Cliente::class)->findOneBy(['telefono' => $phone]);

            if (!$cliente) {
                return new JsonResponse(['message' => 'Cliente no encontrado'], 404);
            }

            $encodedPassword = $passwordEncoder->encodePassword($cliente, $newPassword);
            $cliente->setPassword($encodedPassword);

            $cliente->setHosteddomain(null);

            $em->persist($cliente);
            $em->flush();

            return new JsonResponse(['message' => 'Contraseña actualizada correctamente'], 200);
        } catch (\Exception $e) {
            return new JsonResponse(['message' => 'Error en el servidor', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * @Route("/forget-password", name="forgetPassword", methods={"POST"})
     */

    public function forgetPassword(Request $request, EntityManagerInterface $em, UserPasswordEncoderInterface $passwordEncoder): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $email = $data['email'] ?? null;
            $newPassword = $data['newPassword'] ?? null;

            if (!$email) {
                return new JsonResponse(['message' => 'Olvidaste tu Email'], 404);
            }

            $cliente = $em->getRepository(Cliente::class)->findOneBy(['email' => $email, 'status' => 1]);

            if (!$cliente) {
                return new JsonResponse(['message' => 'Cliente no encontrado en la base de datos'], 404);
            }

            $encodedPassword = $passwordEncoder->encodePassword($cliente, $newPassword);
            $cliente->setPassword($encodedPassword);
            $cliente->setHosteddomain(null);
            $em->persist($cliente);
            $em->flush();

            return new JsonResponse(['message' => 'Contraseña Cambiada Correctamente'], 200);
        } catch (\Exception $e) {
            return new JsonResponse(['message' => 'Error no se cambio', $cliente, 'password' => $newPassword], 404);
        }
    }

    /**
     * @Route("/get-procesos-app"), name="getExaminations", methods="POST"
     */
    public function getProcesos(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return new JsonResponse(['message' => 'No autenticado'], 401);
            }

            $idcliente = (int)$user->getIdcliente();
            $cliente = $em->getRepository(Cliente::class)->findOneBy(['idcliente' => $idcliente]);
            if (!$cliente) {
                return new JsonResponse(['message' => 'Cliente no encontrado'], 404);
            }

            $query = $em->createQuery(
                'SELECT ol.idordenlaboratorio, ol.stage
                FROM App\Entity\Ordenlaboratorio ol
                JOIN ol.clienteIdcliente c
                WHERE ol.clienteIdcliente = :idcliente'
            )->setParameter('idcliente', $idcliente);

            $stages = $query->getResult();

            if (empty($stages)) {
                return new JsonResponse([]);
            }

            return new JsonResponse($stages);

        } catch (\Exception $e) {
            return new JsonResponse(['message' => 'Error en el servidor', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * @Route("/examination-data/{idgraduacion}", name="examination-data", methods={"GET"})
     */
    public function getExaminationData($idgraduacion, EntityManagerInterface $em): JsonResponse
    {
        try {
            $graduacion = $em->getRepository(Graduacion::class)->find($idgraduacion);

            if (!$graduacion) {
                return new JsonResponse(['message' => 'Examen no encontrado'], 404);
            };

            $query = $em->createQuery(
                'SELECT m.nombre AS material, d.nombre AS disenio, t.nombre AS tratamiento
                FROM App\Entity\Graduacion g
                JOIN g.materialIdmaterial m
                JOIN g.disenolenteIddisenolente d
                JOIN g.tratamientoIdtratamiento t
                WHERE g.idgraduacion = :idgraduacion'
            )->setParameter('idgraduacion', $idgraduacion);

            $dmp = $query->getResult();

            return new JsonResponse([
                'anamnesis' => $graduacion->getAnamnesis(),
                'isGlassesUser' => $graduacion->getIsGlassesUser(),
                'hasGlassesNow' => $graduacion->getHasGlassesNow(),
                'GP' => [
                    'OD' => [
                        'esfera' => $graduacion->getGpesferaod(),
                        'cilindro' => $graduacion->getGpcilindrood(),
                        'eje' => $graduacion->getGpejeod(),
                        'add' => $graduacion->getGpaddod(),
                        'avlejos' => $graduacion->getGpavlejosod(),
                        'avcerca' => $graduacion->getGpavcercaod(),
                    ],
                    'OI' => [
                        'esfera' => $graduacion->getGpesferaoi(),
                        'cilindro' => $graduacion->getGpcilindrooi(),
                        'eje' => $graduacion->getGpejeoi(),
                        'add' => $graduacion->getGpaddoi(),
                        'avlejos' => $graduacion->getGpavlejosoi(),
                        'avcerca' => $graduacion->getGpavcercaoi(),
                    ],
                ],
                'AP' => [
                    'lente' => $graduacion->getTipolente() ? "Lente de contacto" : ($graduacion->getTipolentecontacto() ? "Armazon" : "No aplica"),
                    'observaciones' => $graduacion->getApobservaciones(),
                ],
                'DMP' => [
                    'material' => $dmp[0]['material'],
                    'diseño' => $dmp[0]['disenio'],
                    'tratamiento' => $dmp[0]['tratamiento'],
                ],
                'AVSRX' => [
                    'OD' => [
                        'lejos' => $graduacion->getAvsrxlejosod(),
                        'cerca' => $graduacion->getAvsrxcercaod(),
                        'cv' => $graduacion->getAvsrxcvod(),
                    ],
                    'OI' => [
                        'lejos' => $graduacion->getAvsrxlejosoi(),
                        'cerca' => $graduacion->getAvsrxcercaoi(),
                        'cv' => $graduacion->getAvsrxcvoi(),
                    ]
                ],
                'SF' => [
                    'OD' => [
                        'esfera' => $graduacion->getEsfodsf(),
                        'cilindro' => $graduacion->getCilodsf(),
                        'eje' => $graduacion->getEjeodsf(),
                        'avlejos' => $graduacion->getAvlodsf(),
                        'avcsa' => $graduacion->getAvcsaodsf(),
                        'avcca' => $graduacion->getAvccaodsf(),

                    ],
                    'OI' => [
                        'esfera' => $graduacion->getEsfoisf(),
                        'cilindro' => $graduacion->getCiloisf(),
                        'eje' => $graduacion->getEjeoisf(),
                        'avlejos' => $graduacion->getAvloisf(),
                        'avcsa' => $graduacion->getAvcsaoisf(),
                        'avcca' => $graduacion->getAvccaoisf(),
                    ],
                    'add' => $graduacion->getAddsf(),
                ],
                'sugerencias' => $graduacion->getSugerencias(),
            ]);


        } catch (\Exception $e) {
            return new JsonResponse(['message' => 'Error en el servidor', 'error' => $e->getMessage()], 500);
        }

    }


    /**
     * @Route("/get-ventas-app", name="getVentas", methods={"GET"})
     */
    public function getVentas(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return new JsonResponse(['message' => 'No autenticado'], 401);
            }

            $idcliente = $request->query->get('idcliente');
            if (!$idcliente) {
                return new JsonResponse(['message' => 'ID de cliente no proporcionado'], 400);
            }


            $cliente = $em->getRepository(Cliente::class)->findOneBy(['idcliente' => $idcliente]);
            if (!$cliente) {
                return new JsonResponse(['message' => 'Cliente no encontrado'], 404);
            }

            $query = $em->createQuery(
                'SELECT v.idventa AS idventa, v.fecha AS fecha,v.pagado, v.iva, v.total AS total, c.nombre AS cliente, su.nombre AS Sucursal, su.idsucursal,
                v.ticketpdf AS Ticket, v.archivoautorizacion as archivoAutorizacion, v.tickerpdfespecial AS tickerpdfespecial,
                v.liquidada, c.apellidopaterno, c.idcliente, tv.nombre AS tipoVenta, v.deuda
                 FROM App\Entity\Venta v
                 INNER JOIN v.sucursalIdsucursal su
                 INNER JOIN v.clienteIdcliente c
                 INNER JOIN v.tipoventaIdtipoventa tv
                 WHERE v.status = :status 
                   AND c.idcliente = :idcliente 
                   AND v.fecha BETWEEN :startDate AND :endDate
                 ORDER BY v.fecha DESC'
            )->setParameters([
                'status' => 1,
                'idcliente' => $idcliente,
                'startDate' => new \DateTime('-1 year'),
                'endDate' => new \DateTime(),
            ]);


            $ventas = $query->getResult();

            $query2 = $em->createQuery(
                'SELECT v.idventa AS idventa, v.fecha AS fecha, v.pagado, v.iva, v.total AS total, c.nombre AS cliente, su.nombre AS Sucursal, su.idsucursal,
                v.ticketpdf AS Ticket, v.archivoautorizacion as archivoAutorizacion, v.tickerpdfespecial AS tickerpdfespecial,
                vf.status AS facturado, v.liquidada, c.apellidopaterno, c.idcliente, v.deuda, tv.nombre AS tipoVenta
                 FROM App\Entity\Ventafactura vf
                 INNER JOIN vf.ventaIdventa v
                 INNER JOIN v.sucursalIdsucursal su
                 INNER JOIN v.clienteIdcliente c
                 INNER JOIN v.tipoventaIdtipoventa tv
                 WHERE v.status = :status 
                   AND c.idcliente = :idcliente
                   AND v.fecha BETWEEN :startDate AND :endDate
                 ORDER BY v.fecha DESC'
            )->setParameters([
                'status' => 1,
                'idcliente' => $idcliente,
                'startDate' => new \DateTime('-1 year'),
                'endDate' => new \DateTime(),
            ]);

            $ventasFacturadas = $query2->getResult();

            if (!empty($ventasFacturadas)) {
                $ventasPorId = [];

                foreach (array_merge($ventas, $ventasFacturadas) as $venta) {
                    $ventasPorId[$venta['idventa']] = $venta;
                }

                $ventas = array_values($ventasPorId);
            }

            if (empty($ventas)) {
                return new JsonResponse([]);
            }

            $idVentas = [];
            foreach ($ventas as $venta) {
                $idVentas[] = $venta['idventa'];
            }

            $this->fetchClipVentasStatus($em, $idVentas);

            return new JsonResponse($ventas);
        } catch (\Exception $e) {
            return new JsonResponse(['message' => 'Error en el servidor', 'error' => $e->getMessage()], 500);
        }
    }

    private function fetchClipVentasStatus(EntityManagerInterface $em, array $idVentas)
    {
        $pagos = [];

        foreach ($idVentas as $idVenta) {
            //cambiar justificacion a clip
            $query2 = $em->createQuery(
                'SELECT p.idpago, p.clip
                 FROM App\Entity\Pago p
                 WHERE p.ventaIdventa = :idVenta 
                   AND p.status != :status
                   AND p.paymenttypeIdpaymenttype = :paymenttypeIdpaymenttype'
            )->setParameters([
                'status' => 1,
                'idVenta' => $idVenta,
                'paymenttypeIdpaymenttype' => 23,
            ]);

            $result = $query2->getResult();

            foreach ($result as $pago) {
                $pagos[] = $pago;
            }
        }

        foreach ($pagos as $pago) {
            $clipInfo = json_decode($pago['clip'], true);
            $paymentRequestId = $clipInfo['payment_request_id'];

            $url = 'https://api.payclip.com/v2/checkout/' . $paymentRequestId;

            $client = new \GuzzleHttp\Client();

            $response = $client->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Basic NjlhMTFjM2YtMzAxZS00ZjY1LWFiYTQtOGQxMzRlYTg0MGQxOjI4ZDgwM2VjLWE5MTYtNGEwMC1iNThjLTI2Yjg5NTQwZDk4Mw==',
                    'accept' => 'application/json',
                ],
            ]);

            $clipInfo = json_decode($response->getBody()->getContents(), true);
            $paymentStatus = $clipInfo['status'];

            $pagoObject = $em->getRepository(Pago::class)->findOneBy(['idpago' => $pago['idpago']]);

            if ($paymentStatus == 'CHECKOUT_COMPLETED') {
                $pagoObject->setStatus(1);
            } elseif ($paymentStatus == 'CHECKOUT_PENDING' || $paymentStatus == 'CHECKOUT_CREATED') {
                //checar los estados
                $pagoObject->setStatus(3);
            } else {
                $pagoObject->setStatus(0);
            }

            $em->persist($pagoObject);
            $em->flush();
        }


    }

    private function validateLink(string $paymentRequestId)
    {
        $url = 'https://api.payclip.com/v2/checkout/' . $paymentRequestId;

        $client = new \GuzzleHttp\Client();

        $response = $client->request('GET', $url, [
            'headers' => [
                'Authorization' => 'Basic NjlhMTFjM2YtMzAxZS00ZjY1LWFiYTQtOGQxMzRlYTg0MGQxOjI4ZDgwM2VjLWE5MTYtNGEwMC1iNThjLTI2Yjg5NTQwZDk4Mw==',
                'accept' => 'application/json',
            ],
        ]);

        $clipInfo = json_decode($response->getBody()->getContents(), true);
        $paymentStatus = $clipInfo['status'];
        return $paymentStatus;
    }

    /**
     * @Route("/ticket-data/{idventa}", name="ticket-data", methods={"GET"})
     */
    public function getTicketData($idventa, EntityManagerInterface $em): JsonResponse
    {
        try {
            $venta = $em->getRepository(Venta::class)->find($idventa);

            if (!$venta) {
                return new JsonResponse(['message' => 'Venta no encontrada'], 404);
            }

            // Obtener productos de la venta
            $query = $em->createQuery(
                'SELECT sv.preciofinal AS precio, sv.cantidad, p.descripcion AS producto, 
                    p.tipoproducto, p.masivounico, c.nombre AS categoria, sv.precio AS subtotal,
                    sv.isomittable, p.idproducto, sv.porcentajedescuento AS descuento
             FROM App\Entity\Stockventa sv
             INNER JOIN sv.stockIdstock s
             INNER JOIN sv.ventaIdventa v
             INNER JOIN s.productoIdproducto p
             INNER JOIN p.categoriaIdcategoria sc
             INNER JOIN sc.claseIdclase c
             WHERE sv.status = 1 AND v.idventa = :idventa'
            )->setParameter('idventa', $idventa);

            $productos = $query->getResult();

            $query2 = $em->createQuery(
                'SELECT p.idpago, p.clip
                 FROM App\Entity\Pago p
                 WHERE p.ventaIdventa = :idVenta 
                   AND p.status = :status
                   AND p.paymenttypeIdpaymenttype = :paymenttypeIdpaymenttype'
            )->setParameters([
                'status' => 3,
                'idVenta' => $venta->getIdventa(),
                'paymenttypeIdpaymenttype' => 23,
            ]);

            $response = $query2->getResult();


            if ($response) {

                $pago = $response[0];
                $idpago = $pago['idpago'];
                // Decodificar el string JSON contenido en "justification"
                $clipInfo = json_decode($pago['clip'], true);


                // Verificar que la decodificación haya sido correcta y que exista la clave requerida
                $paymentRequestUrl = isset($clipInfo['payment_request_url']) ? $clipInfo['payment_request_url'] : null;
                $paymentRequestId = isset($clipInfo['payment_request_id']) ? $clipInfo['payment_request_id'] : null;

                $url = 'https://api.payclip.com/v2/checkout/' . $paymentRequestId;

                $client = new \GuzzleHttp\Client();

                $response = $client->request('GET', $url, [
                    'headers' => [
                        'Authorization' => 'Basic NjlhMTFjM2YtMzAxZS00ZjY1LWFiYTQtOGQxMzRlYTg0MGQxOjI4ZDgwM2VjLWE5MTYtNGEwMC1iNThjLTI2Yjg5NTQwZDk4Mw==',
                        'accept' => 'application/json',
                    ],
                ]);

                $clipInfo = json_decode($response->getBody()->getContents(), true);
                $paymentStatus = $clipInfo['status'];

                $pagoObject = $em->getRepository(Pago::class)->findOneBy(['idpago' => $idpago]);
                if ($paymentStatus == 'CHECKOUT_COMPLETED') {
                    $pagoObject->setStatus(1);
                } elseif ($paymentStatus == 'CHECKOUT_PENDING' || $paymentStatus == 'CHECKOUT_CREATED') {
                    //checar los estados
                    $pagoObject->setStatus(3);
                } else {
                    $pagoObject->setStatus(0);
                }

                $em->persist($pagoObject);
                $em->flush();

                return new JsonResponse([
                    'idventa' => $venta->getIdventa(),
                    'fecha' => $venta->getFecha()->format('Y-m-d H:i:s'),
                    'total' => number_format($venta->getTotal(), 2),
                    'iva' => $venta->getIva(),
                    'pagado' => $venta->getPagado(),
                    'deuda' => $venta->getDeuda(),
                    'idCliente' => $venta->getClienteIdcliente()->getIdcliente(),
                    'cliente' => $venta->getClienteIdcliente()->getNombre(),
                    'sucursal' => $venta->getSucursalIdsucursal()->getNombre(),
                    'vendedor' => $venta->getUsuarioIdusuario()->getNombre(),
                    'folio' => $venta->getFolio(),
                    'tipoVenta' => $venta->getTipoventaIdtipoventa()->getNombre(),
                    'telefono' => $venta->getCLienteIdcliente()->getTelefono(),
                    'correo' => $venta->getClienteIdcliente()->getEmail(),
                    'productos' => $productos,
                    'linkPago' => $paymentRequestUrl,
                    'liquidada' => $venta->getLiquidada(),
                ]);
            }


            return new JsonResponse([
                'idventa' => $venta->getIdventa(),
                'fecha' => $venta->getFecha()->format('Y-m-d H:i:s'),
                'total' => number_format($venta->getTotal(), 2),
                'iva' => $venta->getIva(),
                'pagado' => $venta->getPagado(),
                'deuda' => $venta->getDeuda(),
                'idCliente' => $venta->getClienteIdcliente()->getIdcliente(),
                'cliente' => $venta->getClienteIdcliente()->getNombre(),
                'sucursal' => $venta->getSucursalIdsucursal()->getNombre(),
                'vendedor' => $venta->getUsuarioIdusuario()->getNombre(),
                'folio' => $venta->getFolio(),
                'tipoVenta' => $venta->getTipoventaIdtipoventa()->getNombre(),
                'telefono' => $venta->getCLienteIdcliente()->getTelefono(),
                'correo' => $venta->getClienteIdcliente()->getEmail(),
                'productos' => $productos,
                'linkPago' => "",
                'liquidada' => $venta->getLiquidada(),
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['message' => 'Error en el servidor', 'error' => $e->getMessage()], 500);
        }
    }


    /**
     * Función para obtener los anuncios activos (status = "1") y generar la URL completa de las imágenes.
     *
     * @Route("/anuncios/activos", name="api_anuncios_activos", methods={"GET"})
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getActiveAnuncios(Request $request): JsonResponse
    {
        $em = $this->getDoctrine()->getManager();
        try {
            $query = $em->createQuery(
                'SELECT a FROM App\Entity\Anuncios a WHERE a.status = :status'
            )->setParameter('status', '1');

            $anuncios = $query->getResult(Query::HYDRATE_OBJECT);


            $baseUrl = $request->getSchemeAndHttpHost();

            // Obtenemos la ruta relativa de la carpeta, definida en tu configuración, por ejemplo: "/uploads/anuncios"
            $folder = $this->getParameter('carpetaAnuncios');
            $folder = rtrim($folder, '/');

            $data = [];

            foreach ($anuncios as $anuncio) {
                if ($anuncio->getImagenPrincipal()) {
                    //http://localhost:8000uploads/anuncios/67f3fc05d0488.png
                    $imageUrl = $baseUrl . '/' . $folder . '/' . $anuncio->getImagenPrincipal();
                } else {
                    $imageUrl = null;
                }
                // Se prepara el array de datos para cada anuncio
                $data[] = [
                    'id' => $anuncio->getIdanuncios(),
                    'titulo' => $anuncio->getTitulo(),
                    'imagenUrl' => $imageUrl,
                ];
            }

            return new JsonResponse([
                'status' => 'success',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return new JsonResponse([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Endpoint in charge of showcase and retrive an especific Empresa form for Facturación.
     *
     * @Route("/facturacion/formulario-empresa/{e}", name="app_invoice_formulario_empresa")
     *
     * @param Request $request The HTTP request object with a Form containing:
     * folio : Identifier for Venta Entity.
     * brochure : .
     * notas : Additional info.
     * @return JsonResponse A JSON response containing information about the success or failure of the operation,
     *
     */


    /**
     * @Route("/facturacion/reenviar-factura", name="app_send_facutra", methods={"POST"})
     */
    public function resendInvoiceEmail(EntityManagerInterface $em, InVoiceController $service, Request $request): JsonResponse
    {
        // ---------------------------------------------------------------------------------------
        // Envío de correo al cliente
        // ---------------------------------------------------------------------------------------

        //Preparar datos
        $data = json_decode($request->getContent(), true);
        $idcliente = $data['idcliente'];
        $idventa = $data['idventa'];
        $idsucursal = $data['idsucursal'];

        //echo($idsucursal . $idcliente . $idventa);

        //Buscar datos
        $Venta = $em->getRepository(Venta::class)->findOneBy(['idventa' => $idventa]);
        $Sucursal = $em->getRepository(Sucursal::class)->findOneBy(['idsucursal' => $idsucursal]);
        $folio = $Venta->getFolio();
        $Clientefacturadatos = $em->getRepository(Clientefacturadatos::class)->findOneBy(['clienteIdcliente' => $idcliente]);
        $Ventafactura = $em->getRepository(Ventafactura::class)->findOneBy(['ventaIdventa' => $idventa]);


        //Buscar archivo
        try {
            $facturasDir = $this->getParameter('uploads') . DIRECTORY_SEPARATOR . "zipFacturas";
            $filename = $Ventafactura->getNombrezip();
            $filenameZip = $facturasDir . DIRECTORY_SEPARATOR . $filename . ".zip";
        } catch (\Exception $exception) {
            return new JsonResponse(["mensaje" => "archivo no encontrado"]);
        }


        $Empresa = $Sucursal->getEmpresaIdempresa();
        $correoFacturacion = $Empresa->getEmailfacturacion();

        try {
            $clienteEmail = $Clientefacturadatos->getEmail();

            //echo($folio . ' ' . $Clientefacturadatos->getClienteIdcliente() . ' '. $Sucursal->getIdsucursal(). ' '. $filenameZip. ' ' . $clienteEmail);
            $response = $service->sendInvoiceEmail($folio, $Clientefacturadatos, $Sucursal, $filenameZip, $clienteEmail);

            if ($response["exito"] == false) {
                return new JsonResponse(["mensaje" => $response["msj"]]);
            }
            return new JsonResponse(["mensaje" => $response["msj"], "exito" => $response["exito"]]);
        } catch (\Exception $exEnvioCliente) {
            $Ventafactura->setEstado("0");
            $em->persist($Ventafactura);
            $em->flush();
            // Enviamos notificación a correo de facturación
            $service->sendInvoiceEmail($folio, $Clientefacturadatos, $Sucursal, $filenameZip, $correoFacturacion);

            return new JsonResponse(["exito" => false, "msj" => $exEnvioCliente->getMessage()]);
        }

    }

    /**
     * @Route("/checkout/request-paymentLink", name="app_request_payment_link", methods={"POST"})
     */
    public function requestPaymentLink(EntityManagerInterface $em, Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $totalAmount = $data['totalAmount'];
        $idCliente = $data['idCliente'];
        $promotion = $data['promotion'];
        $ventaIdventa = $data['ventaIdventa'];
        $msiEnabled = ($promotion == "promotion1") ? '"installments_msi":[3,6,12],' : '"installments_msi":[3],';


        // Client information for payment
        $customer = $em->getRepository(Cliente::class)->findOneBy(['idcliente' => $idCliente]);
        $fullname = $customer->getNombre() . ' ' . $customer->getApellidopaterno() . ' ' . $customer->getApellidomaterno();

        //API client

        $client = new \GuzzleHttp\Client();

        $defaultLink = "https:/optimoopticas.mx/app/optimo/clip/payment/success";
        $errorLink = "https:/optimoopticas.mx/app/optimo/clip/payment/fail";
        $successLink = "https:/optimoopticas.mx//app/optimo/clip/payment/success";

        $utcTimezone = new \DateTimeZone('UTC');

// Crear un objeto DateTime en UTC
        $fechaUTC = new \DateTime('now', $utcTimezone);

// Sumar 1 minuto
        $fechaUTC->modify('+1 minute');

// Formatear la fecha en formato ISO 8601 con 'Z' al final
        $expiresAt = $fechaUTC->format('Y-m-d\TH:i:s\Z');


        try {
            $response = $client->request('POST', 'https://api.payclip.com/v2/checkout', [
                'body' => '{
                "amount":' . floatval($totalAmount) . ',
                "expires_at":' . '"' . $expiresAt . '"' . ',
                "currency":"MXN",
                "purchase_description":"Venta de Lentes",
                "redirection_url":{
                    "success":' . '"' . $successLink . '"' . ',
                    "error":' . '"' . $errorLink . '"' . ',
                    "default":' . '"' . $defaultLink . '"' . '
                },
                "override_settings":{
                    "locale":"es-MX",
                    "tip_enabled":false
                },
                "merchant_info":{
                    "show_contact_info":true
                },
                "metadata": {
                    "customer_info": {
                        "name": ' . '"' . $fullname . '"' . ',
                        "email": ' . '"' . $customer->getEmail() . '"' . ',
                        "phone": ' . '"' . $customer->getTelefono() . '"' . '
                    },
                    "shipping_address":{
                        "zip_code":' . '"' . '"' . ',
                        "street":' . '"' . '"' . ',
                        "outdoor_number":"",
                        "interior_number":"",
                        "locality":"",
                        "city":' . '"' . '"' . ',
                        "state":"",
                        "country":"",
                        "between_streets":' . '"' . '"' . ',
                        "floor":"s"
                    },
                    "billing_address":{
                        "zip_code":' . '"' . '"' . ',
                        "street":"",
                        "outdoor_number":"",
                        "interior_number":"",
                        "locality":"",
                        "city":' . '"' . '"' . ',
                        "state":"",
                        "country":"",
                        "between_streets":' . '"' . '"' . ',
                        "floor":""
                    }
                },
                "custom_payment_options":{
                    "payment_method_types":["debit","credit"],
                    ' . $msiEnabled . '
                    "international_enabled":true
                }
            }',
                'headers' => [
                    'Authorization' => 'Basic NjlhMTFjM2YtMzAxZS00ZjY1LWFiYTQtOGQxMzRlYTg0MGQxOjI4ZDgwM2VjLWE5MTYtNGEwMC1iNThjLTI2Yjg5NTQwZDk4Mw==',
                    'accept' => 'application/json',
                    'content-type' => 'application/json',
                ],
            ]);

            $responseContent = json_decode($response->getBody()->getContents(), true);

            $this->saveClipPayment($em, $ventaIdventa, $responseContent, $totalAmount);

            return new JsonResponse(["url" => $responseContent["payment_request_url"]]);
        } catch (\Exception $exception) {
            return new JsonResponse(["mensaje" => $exception->getMessage()]);
        }
    }

    private function saveClipPayment(EntityManagerInterface $em, string $ventaIdventa, array $responseContent, string $totalAmount)
    {
        $responseContent = json_encode($responseContent);
        $venta = $em->getRepository(Venta::class)->findOneBy(['idventa' => $ventaIdventa]);
        $idpaymenttype = '23';
        $paymentType = $em->getRepository(PaymentType::class)->findOneBy(['idpaymenttype' => $idpaymenttype]);


        $pago = new Pago();
        $pago->setUsuarioCobro($venta->getUsuarioIdusuario());
        $pago->setVentaIdventa($venta);
        $pago->setMonto($totalAmount);
        $pago->setFecha(new \DateTime());
        $pago->setStatus('3');
        $pago->setTipopago('pago con link de cobro CLIP');
        $pago->setPaymenttypeIdpaymenttype($paymentType);
        //provicional
        $pago->setClip($responseContent);

        $em->persist($pago);
        $em->flush();
    }

    /**
     * @Route("/noVentas", name="app_get_noVentas", methods={"GET"})
     */
    public function getNoventas(EntityManagerInterface $em, Request $reques)
    {

        try {
            $user = $this->getUser();
            if (!$user) {
                return new JsonResponse(['message' => 'No autenticado'], 401);
            }
            $idcliente = (int)$user->getIdcliente();

            $cliente = $em->getRepository(Cliente::class)->findOneBy(['idcliente' => $idcliente]);
            if (!$cliente) {
                return new JsonResponse(['message' => 'Cliente no encontrado'], 404);
            }

            $query = $em->createQuery(
                'SELECT COUNT(v.idventa) AS noVentas
                 FROM App\Entity\Venta v
                 INNER JOIN v.sucursalIdsucursal su
                 INNER JOIN v.clienteIdcliente c
                 INNER JOIN v.tipoventaIdtipoventa tv
                 WHERE v.status = :status 
                   AND c.idcliente = :idcliente
                   AND v.fecha BETWEEN :startDate AND :endDate
                 ORDER BY v.fecha DESC'
            )->setParameters([
                'status' => 1,
                'idcliente' => $idcliente,
                'startDate' => new \DateTime('2025-02-01 00:00:00'),
                'endDate' => new \DateTime('2025-04-30 23:59:59'),
            ]);


            $ventas = $query->getResult();

            return new JsonResponse(['code' => 200, 'noVentas' => $ventas[0]['noVentas']]);

        } catch (\Exception $exception) {
            return new JsonResponse(['error' => $exception->getMessage()]);
        }


    }
}
