<?php

namespace App\Controller\Api\AppOptimo\SuperAdmin;

use App\DTO\EventsGetRequest;
use App\Enum\ErrorCodes\AppOptimo\EventsErrorCodes;
use App\Enum\Status;
use App\Service\ErrorResponseService;
use App\Service\ImagePathService;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Doctrine\ORM\Query;

/**
 * @Route("/cliente-api", name="api_")
 */
class ModalController extends AbstractController
{
    private EntityManagerInterface $em;
    private ErrorResponseService $errorResponseService;

    public function __construct(
        EntityManagerInterface $em,
        ErrorResponseService $errorResponseService
    ) {
        $this->errorResponseService = $errorResponseService;
        $this->em = $em;
    }

    /**
     *
     * @Route("/get-modal-data", name="api-get-revenues", methods={"GET"})
     *
     * @param Request $request La petición HTTP, debe incluir una lista de identificadores de las campañas en la query.
     * @return JsonResponse Respuesta JSON con el listado y estadísticas.
     *
     * @throws \Exception Si la lista no es proporcionada o no tiene el formato correcto o no existe.
     */
    public function getModalData(Request $request): JsonResponse
    {

        $campaignsId = $request->query->get('ids');
        $startDate = $request->query->get('startDate');

        if (! $startDate) {
            return $this->errorResponseService
                ->createErrorResponse(EventsErrorCodes::EVENTS_INVALID_DATE);
        }

        try {
            $campaignsIdArray = explode(',', $campaignsId);
        } catch (\Exception $e) {
            return new JsonResponse(['code' => 'ids no generado'], 400);
        }

        $revenues = $this->getRevenues($campaignsIdArray, $startDate);

        return new JsonResponse([
            'revenues' => $revenues,
        ]);
    }

    private function getCampaigns($idsArray, $startDate)
    {
        if (!empty($idsArray)) {
            echo 'no id';
            $query =
                'SELECT e.idevent AS id
            FROM App\Entity\Event e
            JOIN e.sucursalIdsucursal su
            WHERE e.startdate >= :startdate
            ';
        }else {
            echo 'id';
            $query =
                'SELECT e.idevent AS id
            FROM App\Entity\Event e
            JOIN e.sucursalIdsucursal su
            WHERE su.idsucursal IN (:idsucursal)
            AND e.startdate >= :startdate
            ';
        }

        $parameters = [
            'startdate' => $startDate,
        ];

        if (empty($idsArray)) {
            $parameters['idsArray'] = $idsArray;
        }

        $qb = $this->em->createQuery($query)
            ->setParameters($parameters);


        $campaigns = $qb->getResult();

        return $campaigns;

    }

    private function generateTableStockData($stock, $vendidos)
    {
        return([]);
    }

    private function getVendidos($tipo, $startDate)
    {
        $conn = $this->em->getConnection();

        $sql =

            '
            SELECT
              m.nombre AS marca,
              COUNT(*) AS vendidos,
              su.nombre
            FROM stockVenta AS sv 
            INNER JOIN venta AS v ON sv.venta_idventa = v.idventa
            INNER JOIN stock AS s ON s.idstock = sv.stock_idstock
            INNER JOIN sucursal AS su ON v.sucursal_idsucursal = su.idsucursal
            INNER JOIN producto AS p ON s.producto_idproducto = p.idproducto
            INNER JOIN marca AS m ON p.marca_idmarca = m.idmarca
            WHERE su.idsucursal = 110
            AND su.tipo = :tipo
            AND v.fechaventa >= :startDate
            GROUP BY m.nombre
            ORDER BY marca ASC';


        $stmt = $conn->executeQuery($sql, [
            'idsucursal' => 110,
            'aceptada' => 1,
            'tipo' => $tipo,
            'startDate' => new \DateTime($startDate),
        ]);

        return ($stmt->fetchAllAssociative());
    }

    private function getStock($campaignsIdArray, $startDate)
    {
        $conn = $this->em->getConnection();

        $sql =
            '
            SELECT 
            ';


        $stmt = $conn->executeQuery($sql, [
            'idsucursal' => 110,
            'aceptada' => 1,
            'tipo' => $tipo,
            'startDate' => $startDate,
        ]);

        return ($stmt->fetchAllAssociative());

    }

}