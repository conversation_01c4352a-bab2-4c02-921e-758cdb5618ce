<?php

namespace App\Controller\Api\AppOptimo\SuperAdmin;
use App\Entity\Venta;
use App\Entity\Stockventa;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class GraficasController extends AbstractController
{
    /**
     * @Route("/cliente-api/get-ventas-donut", name="get_ventas_donut", methods={"GET"})
     */
    public function salesByBranch(EntityManagerInterface $em): JsonResponse
    {

        $query = $em->createQuery(
            'SELECT su.nombre AS Sucursal, su.idsucursal, tv.nombre AS tipoVenta,
            COUNT(v.idventa) AS totalVentas
                 FROM App\Entity\Venta v
                 INNER JOIN v.sucursalIdsucursal su
                 INNER JOIN v.clienteIdcliente c
                 INNER JOIN v.tipoventaIdtipoventa tv
                 WHERE v.status = :status 
                 AND v.fecha BETWEEN :startDate AND :endDate
                 GROUP BY su.idsucursal
                 ORDER BY v.idventa DESC'
        )->setParameters([
            'status' => 1,
            'startDate' => new \DateTime('2025-02-01 00:00:00'),
            'endDate' => new \DateTime('2025-03-30 23:59:59'),
        ]);

        $rows = $query->getResult();

        return new JsonResponse([
            'rows' => $rows,
        ]);
    }

}
