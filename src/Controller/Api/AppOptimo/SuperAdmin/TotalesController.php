<?php

namespace App\Controller\Api\AppOptimo\SuperAdmin;

use App\Enum\ErrorCodes\AppOptimo\EventsErrorCodes;
use App\Service\ErrorResponseService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use App\DTO\EventsGetRequest;



/**
 * @Route("/cliente-api", name="api_")
 */
class TotalesController extends AbstractController
{

    private EntityManagerInterface $em;
        private ErrorResponseService $errorResponseService;

    public function __construct(
        EntityManagerInterface $em,
        ErrorResponseService $errorResponseService
    ) {
        $this->errorResponseService = $errorResponseService;
        $this->em = $em;
    }

    /**
     *
     * @Route("/get-cifras-totales", name="get-cifras-totales", methods={"GET"})
     *
     * @param Request $request La petición HTTP, debe incluir una lista de identificadores de las campañas en la query.
     * @return JsonResponse Respuesta JSON con el listado y estadísticas.
     *
     * @throws \Exception Si la lista no es proporcionada o no tiene el formato correcto o no existe.
     */
    public function getRevenues(Request $request): JsonResponse
    {

        $startDate = $request->query->get('startDate');
        $endDate = $request->query->get('endDate');

        if (! $startDate) {
            return $this->errorResponseService
                ->createErrorResponse(EventsErrorCodes::EVENTS_INVALID_DATE);
        }

        $query =
            '
            SELECT 
                s.idsucursal AS key,
                s.nombre AS sucursal,
                SUM(v.total) AS totalVentas,
                SUM(v.totalPagado) AS cobrado,
                SUM(v.deuda) AS deuda
            FROM 
                App\Entity\Venta v
            JOIN 
                v.sucursalIdsucursal s
            WHERE
                v.fechaVenta BETWEEN :startDate AND :endDate
            GROUP BY
                s.idsucursal
            ORDER BY
                totalVentas DESC
            ';

        $parameters = [
            'startDate' => new \DateTime($startDate),
            'endDate' => new \DateTime($endDate),
        ];

        $qb = $this->em->createQuery($query)
            ->setParameters($parameters);

        $revenues = $qb->getResult();

        $tableRevenueData = [];
        foreach ($revenues as $revenue) {
            $tableRow = [];
            $tableRow[] = $revenue['nombre'];
            $tableRow[] = $revenue['total'];
            $tableRow[] = $revenue['cobrado'];
            $tableRow[] = $revenue['porCobrar'];

            $tableRevenueData[] = $tableRow;
        }

        return new JsonResponse($tableRevenueData);
    }
}