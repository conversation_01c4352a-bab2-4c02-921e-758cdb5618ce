<?php

namespace App\Controller\Api\AppOptimo\SuperAdmin;

use App\DTO\EventsGetRequest;
use App\Entity\Sucursal;
use App\Enum\ErrorCodes\AppOptimo\EventsErrorCodes;
use App\Enum\Status;
use App\Service\ErrorResponseService;
use App\Service\ImagePathService;
use App\Service\RequestValidatorService;
use DateTime;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use PhpMyAdmin\Database\Events;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Doctrine\ORM\Query;

/**
 * @Route("/cliente-api", name="api_")
 */
class CalendarController extends AbstractController
{
    private EntityManagerInterface $em;
    private ErrorResponseService $errorResponseService;

    public function __construct(
        EntityManagerInterface $em,
        ErrorResponseService $errorResponseService
    ) {
        $this->errorResponseService = $errorResponseService;
        $this->em = $em;
    }

    /**
     *
     * @Route("/get-sucursales", name="api-get-sucursales", methods={"GET"})
     *
     * @param Request $request La petición HTTP, debe incluir `nombre` en la query.
     * @return JsonResponse Respuesta JSON con el listado y estadísticas.
     *
     * @throws \Exception Si el nombre proporcionado no tiene el formato correcto o no existe.
     */
    public function getSucursales(Request $request)
    {
        $nombreLike = $request->query->get('nombre', '');
        $qb = $this->em->createQueryBuilder();

        $qb->select('s.idsucursal as idSucursal, s.nombre')
            ->from(Sucursal::class, 's')
            ->where('s.status = 1')
            ->andWhere('s.nombre LIKE :nombreLike')
            ->andWhere('s.tipo = :tipo')
            ->orderBy('s.idsucursal', 'DESC')
            ->setParameter('nombreLike', '%' . $nombreLike . '%')
            ->setParameter('tipo', "campaña")
            ->setMaxResults(5);

        $sucursales = $qb->getQuery()->getResult();

        if (!$sucursales) {
            return $this->errorResponseService->createErrorResponse(EventsErrorCodes::EVENTS_NO_EVENTS_FOUND);
        }

        return new JsonResponse([
            'sucursales' => $sucursales,
            'code' => "200"
        ]);
    }

    /**
     * Obtiene la lista de campañas a partir de una fecha de inicio.
     *
     *  Lee el parámetro `startDate` de la query string (formato `Y-m-d H:i:s`),
     *  filtra las sucursales cuyo `startDate` sea mayor o igual, y devuelve:
     *   - events: array de campañas serializadas
     *   - upcoming: número de campañas por empezar
     *   - inProgress: número de campañas en curso
     *   - closingSoonEvents: número de campañas por cerrar en 2 días
     *   - ended: número de campañas ya finalizadas
     *
     * El array events está compuesto de la siguiente manera:
     * - id
     * - nombre
     * - start_date
     * - end_date
     * - progress (0 aún no empieza, 1 en progreso, 2 términado)
     *
     * @Route("/get-campaigns", name="api-get-campaigns", methods={"GET"})
     *
     * @param Request $request La petición HTTP, debe incluir `startDate` en la query.
     * @return JsonResponse Respuesta JSON con el listado y estadísticas.
     *
     * @throws \Exception Si la fecha proporcionada no tiene el formato correcto o no existe.
     */
    public function getCampaigns(Request $request): JsonResponse
    {
        $startDateString = $request->query->get('startDate');
        $idsucursals = $request->query->get('sucursales');

        if (! $startDateString) {
            return $this->errorResponseService
                ->createErrorResponse(EventsErrorCodes::EVENTS_INVALID_DATE);
        }
        try {
            $startDate = new \DateTime($startDateString);
        } catch (\Exception $e) {
            return new JsonResponse(['code' => 'fecha no creada'], 400);
        }

        $qb = $this->em->createQueryBuilder();

        if (strlen($idsucursals) > 0) {

            $idsucursalsArray = explode(',', $idsucursals);

            $qb
                ->select('e, s')
                ->from('App\Entity\Event', 'e')
                ->join('e.sucursalIdsucursal', 's')
                ->where('e.startdate >= :startdate')
                ->andWhere('s.tipo = :tipo')
                ->andWhere('s.idsucursal in (:idsucursals)')
                ->orderBy('e.startdate', 'ASC')
                ->setParameters([
                    'startdate' => $startDate,
                    'tipo' => 'campaña',
                    'idsucursals' => $idsucursalsArray,
                ]);
        }else {
            $qb
                ->select('e, s')
                ->from('App\Entity\Event', 'e')
                ->join('e.sucursalIdsucursal', 's')
                ->where('e.startdate >= :startdate')
                ->andWhere('s.tipo = :tipo')
                ->orderBy('e.startdate', 'ASC')
                ->setParameters([
                    'startdate' => $startDate,
                    'tipo' => 'campaña',
                ]);
        }

        $campaigns = $qb->getQuery()->getResult();


        if (sizeof($campaigns) == 0) {
            return $this->errorResponseService->createErrorResponse(EventsErrorCodes::EVENTS_NO_EVENTS_FOUND,
            [
                'startdate' => $startDate,
            ]);
        }

        $response = array_map(fn($campaign) => $this->mapCampaignsToArray($campaign), $campaigns);
        $inProgress = $this->inProgressCampaings($response);
        $upcoming = $this->upcomingCampaings($response);
        $closingSoonEvents = $this->closingSoonCampaings($response);
        $ended = $this->endedCampaings($response);
        $idCampaigns = [];

        foreach ($response as $campaign) {
            $idCampaigns[] = $campaign['idEvento'];
        }

        return new JsonResponse([
            'idCampaigns' => $idCampaigns,
            'events' => $response,
            'upcoming' => $upcoming,
            'inProgress' => $inProgress,
            'closingSoonEvents' => $closingSoonEvents,
            'ended' => $ended,
            'code' => "200",
        ], 200);
    }


    private function mapCampaignsToArray( $campaign): array
    {
        return [
            'idEvento' => $campaign->getIdevent(),
            'nombre' => $campaign->getSucursalIdsucursal()->getNombre(),
            'start_date' => $campaign->getStartdate()->format('Y-m-d'),
            'end_date' => $campaign->getEnddate()->format('Y-m-d'),
            'progress' => $this->getSucursalProgres($campaign->getStartdate(), $campaign->getEnddate()),
        ];
    }

    private function getSucursalProgres( DateTime $startDate, DateTime $endDate): int
    {
        $today     = new \DateTime();
        // Ya terminou
        if ($endDate < $today) {
            return 2;
        }
        // Aún no comienza
        if ($startDate > $today) {
            return 0;
        }
        // En progreso
        return 1;
    }

    private function inProgressCampaings($sucursales)
    {
        $today = new DateTime();
        $counter = 0;
        foreach ($sucursales as $sucursal) {
            $startDate = new \DateTime($sucursal['start_date']);
            $endDate   = new \DateTime($sucursal['end_date']);
            $includeEvent = true;

            if ( $startDate > $today || $today > $endDate ) {

                $includeEvent = false;
            }
            if ( $includeEvent ) {
                $counter += 1;
            }
        }

        return $counter;
    }

    private function upcomingCampaings( $events)
    {
        $today = new DateTime();

        $counter = 0;
        foreach ($events as $event) {
            $startDate = new \DateTime($event['start_date']);
            if ( $startDate > $today ) {
                $counter += 1;
            }

        }

        return $counter;
    }

    private function closingSoonCampaings($events)
    {
        $today = new DateTime();
        $limit = (clone $today)->add(new \DateInterval('P2D'));

        $counter = 0;
        foreach ($events as $event) {

            $endDate   = new \DateTime($event['end_date']);
            if ($today <= $endDate && $endDate <= $limit) {
                $counter++;
            }
        }

        return $counter;
    }

    private function endedCampaings($events)
    {
        $today = new DateTime();
        $counter = 0;

        foreach ($events as $event) {
            $endDate   = new \DateTime($event['end_date']);
            if ( $endDate < $today ) {
                $counter += 1;
            }
        }

        return $counter;
    }

/*    /**
     * Obtiene la lista de campañas a partir de una fecha de inicio
     *
     * @Route("/get-campaigns-stock", name="api-get-campaigns", methods={"GET"})
     *
     * @param Request $request La petición HTTP, debe incluir los id de las campañas en la query.
     * @return JsonResponse Respuesta JSON con el listado y estadísticas.
     *
     * @throws \Exception Si la fecha proporcionada no tiene el formato correcto o no existe.
     */
    /*public function getCampaignStock(Request $request): JsonResponse
    {
        $campaigns = $request->query->get('campaigns');

        if (! $campaigns) {
            return $this->errorResponseService
                ->createErrorResponse(EventsErrorCodes::CAMPAIGNS_NOT_RECEIVED);
        }

        $tocks = [];
        foreach ($campaigns as $campaign) {
            $tocks[] = getStockByCampaign($campaign->getIdstock());
        }




        return new JsonResponse([]);
    }

    private function getStockByCampaign($idsucursal)
    {
        $qb = $this->em->createQuery(
            'SELECT
                m.nombre AS marca,
                SUM( pta.cantidad ) AS llevados
            FROM App\Entity\Productostranspasoalmacen AS pta
            JOIN pta.stockIdstock s
            JOIN s.productoIdproducto p
            JOIN p.marcaIdmarca m
            JOIN pta.transpasoalmacenIdtranspasoalmacen t
            JOIN t.sucursalIdsucursal su
            JOIN App\Entity\Ordensalida AS os ON t.idtranspasoAlmacen = os.transpasoAlmacen_idtranspasoAlmacen
            WHERE su.idsucursal = :idsucursal
                AND os.aceptada = 1
            GROUP BY m.nombre'
        )->setParameter('idsucursal', $idsucursal);

        return $qb->getQuery()->getResult();
    }*/
}