<?php

namespace App\Controller\Api\AppOptimo\Auth;

use App\DTO\Auth\GoogleLoginRequest;
use App\Entity\Cliente;
use App\Exception\InvalidGoogleTokenException;
use App\Service\GoogleAuthService;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use App\DTO\Auth\GoogleLoginResponseDTO;


/**
 * @Route("/cliente-api", name="api_")
 */
class GoogleLoginController extends AbstractController
{
    private $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    /**
     * @Route("/google_login", name="api_google_login", methods={"POST"})
     */
    public function googleLogin(
        Request $request,
        GoogleAuthService $service,
        JWTTokenManagerInterface $JWTManager
    ): JsonResponse {
        $data = json_decode($request->getContent(), true);

        $token = $data['token'] ?? null;

        if (!$token) {
            return new JsonResponse(['message' => 'Falta token'], 400);
        }

        try {
            $cliente = $service->login($token);
            $jwt = $JWTManager->create($cliente);

            $responseDTO = new GoogleLoginResponseDTO($jwt, [
                'id'         => $cliente->getIdcliente(),
                'name'       => $cliente->getNombre(),
                'lastname'   => $cliente->getApellidoPaterno() ?? '',
                'surname'    => $cliente->getApellidoMaterno() ?? '',
                'email'      => $cliente->getEmail(),
                'phone'      => $cliente->getTelefono(),
                'isVerified' => $cliente->getIsverified(),
            ]);

            return new JsonResponse($responseDTO->toArray(), 200);
        } catch (InvalidGoogleTokenException $e) {
            $errorResponse = [
                'message' => 'Token de Google inválido'
            ];

            $errorDetails = $e->getErrorDetails();
            // Only include non-sensitive error details
            if (isset($errorDetails['payload_empty'])) {
                $errorResponse['details'] = [
                    'payload_empty' => $errorDetails['payload_empty'],
                    'email_missing' => $errorDetails['email_missing'] ?? false
                ];
            }
            $this->logger->error('Google login error', [
                'error' => $e->getMessage(),
                'details' => $errorDetails
            ]);

            return new JsonResponse($errorResponse, 401);
        } catch (\Throwable $e) {
            $errorData = [
                'error' => $e->getMessage()
            ];
            $this->logger->error('Error en login con Google', $errorData);

            $response = ['message' => 'Error interno del servidor'];

            return new JsonResponse($response, 500);
        }
    }


}
