<?php

namespace App\Controller\Api\AppOptimo;

use App\Entity\Cliente;
use App\Entity\Graduacion;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Doctrine\ORM\Query;


/**
 * @Route("/cliente-api", name="api_")
 */
class ExaminationController extends AbstractController
{
    /**
     * @Route("/get-examinations-app", name="getExaminations", methods={"GET"})
     */
    public function getExaminations(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            $user = $this->getUser();
            if (!$user) {
                return new JsonResponse(['message' => 'No autenticado'], 401);
            }

            // Get idcliente from request params if provided, otherwise use authenticated user's ID
            $idcliente = $request->query->get('idcliente') ? (int)$request->query->get('idcliente') : (int)$user->getIdcliente();

            $cliente = $em->getRepository(Cliente::class)->findOneBy(['idcliente' => $idcliente]);
            if (!$cliente) {
                return new JsonResponse(['message' => 'Cliente no encontrado'], 404);
            }

            // Calculate date range for the last 2 years
            $endDate = new \DateTime();
            $startDate = clone $endDate;
            $startDate->modify('-2 years');

            $query = $em->createQuery(
                'SELECT g.idgraduacion, g.creacion, su.nombre AS sucursal, c.nombre AS cliente, 
                g.anamnesis, g.isglassesuser AS isGlassesUser, g.hasglassesnow AS hasGlassesNow,
                -- GP (Gafas Previas) fields
                CONCAT(\'{\', 
                    \'"esferaod": "\', g.gpesferaod, \'", \',
                    \'"esferaoi": "\', g.gpesferaoi, \'", \',
                    \'"cilindrood": "\', g.gpcilindrood, \'", \',
                    \'"cilindrooi": "\', g.gpcilindrooi, \'", \',
                    \'"ejeod": "\', g.gpejeod, \'", \',
                    \'"ejeoi": "\', g.gpejeoi, \'", \',
                    \'"addod": "\', g.gpaddod, \'", \',
                    \'"addoi": "\', g.gpaddoi, \'", \',
                    \'"avlejosod": "\', g.gpavlejosod, \'", \',
                    \'"avlejosoi": "\', g.gpavlejosoi, \'", \',
                    \'"avcercaod": "\', g.gpavcercaod, \'", \',
                    \'"avcercaoi": "\', g.gpavcercaoi, \'"\'
                , \'}\') AS GP,

                -- AP (Anamnesis Personal) fields
                g.apobservaciones AS AP,

                -- DMP (Datos Médicos Personales) - Using anamnesisjson field
                g.anamnesisjson AS DMP,

                -- AVSRX (Agudeza Visual Sin RX) fields
                CONCAT(\'{\', 
                    \'"lejosod": "\', g.avsrxlejosod, \'", \',
                    \'"lejosoi": "\', g.avsrxlejosoi, \'", \',
                    \'"cercaod": "\', g.avsrxcercaod, \'", \',
                    \'"cercaoi": "\', g.avsrxcercaoi, \'", \',
                    \'"cvod": "\', g.avsrxcvod, \'", \',
                    \'"cvoi": "\', g.avsrxcvoi, \'"\'
                , \'}\') AS AVSRX,

                -- SF (Subjetivo Final) fields
                CONCAT(\'{\', 
                    \'"esfodsf": "\', g.esfodsf, \'", \',
                    \'"esfoisf": "\', g.esfoisf, \'", \',
                    \'"cilodsf": "\', g.cilodsf, \'", \',
                    \'"ciloisf": "\', g.ciloisf, \'", \',
                    \'"ejeodsf": "\', g.ejeodsf, \'", \',
                    \'"ejeoisf": "\', g.ejeoisf, \'", \',
                    \'"avlodsf": "\', g.avlodsf, \'", \',
                    \'"avloisf": "\', g.avloisf, \'", \',
                    \'"avcsaodsf": "\', g.avcsaodsf, \'", \',
                    \'"avcsaoisf": "\', g.avcsaoisf, \'", \',
                    \'"avccaodsf": "\', g.avccaodsf, \'", \',
                    \'"avccaoisf": "\', g.avccaoisf, \'", \',
                    \'"addsf": "\', g.addsf, \'"\'
                , \'}\') AS SF

                FROM App\Entity\Graduacion g
                JOIN g.usuarioIdusuario u
                JOIN u.sucursalIdsucursal su
                JOIN g.clienteIdcliente c
                WHERE g.status = :status
                AND g.clienteIdcliente = :idcliente
                AND g.creacion BETWEEN :startDate AND :endDate
                ORDER BY g.creacion DESC'

            )->setParameters([
                'status' => 1,
                'idcliente' => $idcliente,
                'startDate' => $startDate,
                'endDate' => $endDate,
            ]);

            $examinations = $query->getResult();

            if (empty($examinations)) {
                return new JsonResponse([]);
            }

            // Process the results to ensure proper JSON formatting
            foreach ($examinations as &$exam) {
                // Convert string JSON to actual JSON objects
                if (isset($exam['GP'])) {
                    $exam['GP'] = json_decode($exam['GP'], true);
                }
                if (isset($exam['AVSRX'])) {
                    $exam['AVSRX'] = json_decode($exam['AVSRX'], true);
                }
                if (isset($exam['SF'])) {
                    $exam['SF'] = json_decode($exam['SF'], true);
                }
                if (isset($exam['DMP']) && is_string($exam['DMP'])) {
                    $exam['DMP'] = json_decode($exam['DMP'], true);
                }

                // Format the creation date
                if (isset($exam['creacion']) && $exam['creacion'] instanceof \DateTime) {
                    $exam['creacion'] = [
                        'date' => $exam['creacion']->format('Y-m-d H:i:s')
                    ];
                }
            }

            return new JsonResponse($examinations);

        } catch (\Exception $e) {
            return new JsonResponse(['message' => 'Error en el servidor', 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * @Route("/examination-data/{idgraduacion}", name="examination-data", methods={"GET"})
     */
    public function getExaminationData($idgraduacion, EntityManagerInterface $em, Request $request): JsonResponse
    {
        try {
            // Check authentication
            $user = $this->getUser();
            if (!$user) {
                return new JsonResponse(['message' => 'No autenticado'], 401);
            }

            $graduacion = $em->getRepository(Graduacion::class)->find($idgraduacion);

            if (!$graduacion) {
                return new JsonResponse(['message' => 'Examen no encontrado'], 404);
            };

            $query = $em->createQuery(
                'SELECT m.nombre AS material, d.nombre AS disenio, t.nombre AS tratamiento
                FROM App\Entity\Graduacion g
                LEFT JOIN g.materialIdmaterial m
                LEFT JOIN g.disenolenteIddisenolente d
                LEFT JOIN g.tratamientoIdtratamiento t
                WHERE g.idgraduacion = :idgraduacion'
            )->setParameter('idgraduacion', $idgraduacion);

            $dmp = $query->getResult();

            $dmpData = [
                'material' => 'No especificado',
                'diseño' => 'No especificado',
                'tratamiento' => 'No especificado'
            ];

            if (!empty($dmp) && isset($dmp[0])) {
                $dmpData['material'] = $dmp[0]['material'] ?? 'No especificado';
                $dmpData['diseño'] = $dmp[0]['disenio'] ?? 'No especificado';
                $dmpData['tratamiento'] = $dmp[0]['tratamiento'] ?? 'No especificado';
            }

            return new JsonResponse([
                'anamnesis' => $graduacion->getAnamnesis(),
                'isGlassesUser' => $graduacion->getIsGlassesUser(),
                'hasGlassesNow' => $graduacion->getHasGlassesNow(),
                'GP' => [
                    'OD' => [
                        'esfera' => $graduacion->getGpesferaod(),
                        'cilindro' => $graduacion->getGpcilindrood(),
                        'eje' => $graduacion->getGpejeod(),
                        'add' => $graduacion->getGpaddod(),
                        'avlejos' => $graduacion->getGpavlejosod(),
                        'avcerca' => $graduacion->getGpavcercaod(),
                    ],
                    'OI' => [
                        'esfera' => $graduacion->getGpesferaoi(),
                        'cilindro' => $graduacion->getGpcilindrooi(),
                        'eje' => $graduacion->getGpejeoi(),
                        'add' => $graduacion->getGpaddoi(),
                        'avlejos' => $graduacion->getGpavlejosoi(),
                        'avcerca' => $graduacion->getGpavcercaoi(),
                    ],
                ],
                'AP' => [
                    'lente' => $graduacion->getTipolente() ? "Lente de contacto" : ($graduacion->getTipolentecontacto() ? "Armazon" : "No aplica"),
                    'observaciones' => $graduacion->getApobservaciones(),
                ],
                'DMP' => $dmpData,
                'AVSRX' => [
                    'OD' => [
                        'lejos' => $graduacion->getAvsrxlejosod(),
                        'cerca' => $graduacion->getAvsrxcercaod(),
                        'cv' => $graduacion->getAvsrxcvod(),
                    ],
                    'OI' => [
                        'lejos' => $graduacion->getAvsrxlejosoi(),
                        'cerca' => $graduacion->getAvsrxcercaoi(),
                        'cv' => $graduacion->getAvsrxcvoi(),
                    ]
                ],
                'SF' => [
                    'OD' => [
                        'esfera' => $graduacion->getEsfodsf(),
                        'cilindro' => $graduacion->getCilodsf(),
                        'eje' => $graduacion->getEjeodsf(),
                        'avlejos' => $graduacion->getAvlodsf(),
                        'avcsa' => $graduacion->getAvcsaodsf(),
                        'avcca' => $graduacion->getAvccaodsf(),

                    ],
                    'OI' => [
                        'esfera' => $graduacion->getEsfoisf(),
                        'cilindro' => $graduacion->getCiloisf(),
                        'eje' => $graduacion->getEjeoisf(),
                        'avlejos' => $graduacion->getAvloisf(),
                        'avcsa' => $graduacion->getAvcsaoisf(),
                        'avcca' => $graduacion->getAvccaoisf(),
                    ],
                    'add' => $graduacion->getAddsf(),
                ],
                'sugerencias' => $graduacion->getSugerencias(),
            ]);


        } catch (\Exception $e) {
            error_log('Error in getExaminationData: ' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            return new JsonResponse(['message' => 'Error en el servidor', 'error' => $e->getMessage()], 500);
        }

    }
}
