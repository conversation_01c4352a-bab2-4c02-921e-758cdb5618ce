<?php

namespace App\Controller\Api\AppOptimo;

use App\DTO\PhoneVerification\PhoneVerificationRequestDTO;
use App\DTO\PhoneVerification\PhoneVerificationResponseDTO;
use App\DTO\PhoneVerification\PhoneVerificationVerifyRequestDTO;
use App\Exception\PhoneVerificationException;
use App\Service\TwilioVerifyService;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Controller for phone verification
 * 
 * @Route("/phone-verification")
 */
class PhoneVerificationController extends AbstractController
{
    /**
     * @var TwilioVerifyService
     */
    private $twilioVerifyService;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * PhoneVerificationController constructor.
     *
     * @param TwilioVerifyService $twilioVerifyService
     * @param LoggerInterface $logger
     */
    public function __construct(TwilioVerifyService $twilioVerifyService, LoggerInterface $logger)
    {
        $this->twilioVerifyService = $twilioVerifyService;
        $this->logger = $logger;
    }

    /**
     * Send verification code
     *
     * @Route("/send", name="phone_verification_send", methods={"POST"})
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function send(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $dto = PhoneVerificationRequestDTO::fromArray($data);
            
            if (empty($dto->getPhoneNumber())) {
                return $this->json(
                    PhoneVerificationResponseDTO::createError('El número de teléfono es requerido'),
                    400
                );
            }
            
            $result = $this->twilioVerifyService->sendVerificationCode($dto->getPhoneNumber());
            
            return $this->json(
                PhoneVerificationResponseDTO::createSuccess(
                    'Código de verificación enviado',
                    $result['status'],
                    $result['expiresAt']
                )
            );
        } catch (PhoneVerificationException $e) {
            $this->logger->error('Error sending verification code', [
                'error' => $e->getMessage(),
                'status' => $e->getStatus()
            ]);
            
            return $this->json(
                PhoneVerificationResponseDTO::createError($e->getMessage(), $e->getStatus()),
                400
            );
        } catch (\Exception $e) {
            $this->logger->error('Unexpected error sending verification code', [
                'error' => $e->getMessage()
            ]);
            
            return $this->json(
                PhoneVerificationResponseDTO::createError('Error inesperado al enviar el código de verificación'),
                500
            );
        }
    }

    /**
     * Verify code
     *
     * @Route("/verify", name="phone_verification_verify", methods={"POST"})
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function verify(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $dto = PhoneVerificationVerifyRequestDTO::fromArray($data);
            
            if (empty($dto->getPhoneNumber())) {
                return $this->json(
                    PhoneVerificationResponseDTO::createError('El número de teléfono es requerido'),
                    400
                );
            }
            
            if (empty($dto->getCode())) {
                return $this->json(
                    PhoneVerificationResponseDTO::createError('El código de verificación es requerido'),
                    400
                );
            }
            
            $status = $this->twilioVerifyService->checkVerificationCode($dto->getPhoneNumber(), $dto->getCode());
            
            $message = $status === 'approved' 
                ? 'Verificación exitosa' 
                : 'Verificación fallida';
            
            return $this->json(
                PhoneVerificationResponseDTO::createSuccess($message, $status)
            );
        } catch (PhoneVerificationException $e) {
            $this->logger->error('Error verifying code', [
                'error' => $e->getMessage(),
                'status' => $e->getStatus()
            ]);
            
            return $this->json(
                PhoneVerificationResponseDTO::createError($e->getMessage(), $e->getStatus()),
                400
            );
        } catch (\Exception $e) {
            $this->logger->error('Unexpected error verifying code', [
                'error' => $e->getMessage()
            ]);
            
            return $this->json(
                PhoneVerificationResponseDTO::createError('Error inesperado al verificar el código'),
                500
            );
        }
    }

    /**
     * Get verification status
     *
     * @Route("/status", name="phone_verification_status", methods={"GET"})
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function status(Request $request): JsonResponse
    {
        try {
            $phoneNumber = $request->query->get('phoneNumber');
            
            if (empty($phoneNumber)) {
                return $this->json(
                    PhoneVerificationResponseDTO::createError('El número de teléfono es requerido'),
                    400
                );
            }
            
            $result = $this->twilioVerifyService->getVerificationStatus($phoneNumber);
            
            return $this->json(
                PhoneVerificationResponseDTO::createSuccess(
                    'Estado de verificación obtenido',
                    $result['status'],
                    $result['expiresAt']
                )
            );
        } catch (PhoneVerificationException $e) {
            $this->logger->error('Error getting verification status', [
                'error' => $e->getMessage(),
                'status' => $e->getStatus()
            ]);
            
            return $this->json(
                PhoneVerificationResponseDTO::createError($e->getMessage(), $e->getStatus()),
                400
            );
        } catch (\Exception $e) {
            $this->logger->error('Unexpected error getting verification status', [
                'error' => $e->getMessage()
            ]);
            
            return $this->json(
                PhoneVerificationResponseDTO::createError('Error inesperado al obtener el estado de verificación'),
                500
            );
        }
    }
}