<?php

namespace App\Controller;

use App\Entity\Beneficiario;
use App\Entity\Stockstate;
use App\Entity\Stockventa;
use App\Entity\Unidad;
use App\Entity\Producto;
use App\Entity\Usuario;
use App\Entity\Tipoventa;
use App\Entity\Stock;
use App\Entity\Venta;
use App\Entity\Cliente;
use App\Entity\Beneficiarioventa;
use App\Form\PreAuthUAMType;
use Doctrine\ORM\EntityManagerInterface;
use phpDocumentor\Reflection\DocBlock\Tags\Throws;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\JsonResponse;

class PreAuthUAMController extends AbstractController
{
    public $empRFC = "GOM180228DD4";
    public $gerenteId = "1";
    public $iva = 0.16;
    public $tiposDeVenta = [
        'auditivos' => 1756,
        'lentes_oftalmicos' => 1748,
        'lentes_contacto_blandos' => 1778,
        'lentes_contacto_rigidos' => 1779
    ];
    public $UnidadSucursal = [
        'auditivos' => 1756,
        'lentes_oftalmicos' => 1772,
        'lentes_contacto_blandos' => 1778,
        'lentes_contacto_rigidos' => 1779
    ];

    /**
     * @Route("/precotizacion/{vendedor}", name="pre_auth_uam", methods={"GET", "POST"})
     */
    public function create(Request $request, EntityManagerInterface $entityManager, string $vendedor = '')
    {
        $folio="";
        if ($request->isMethod('POST') && $vendedor !="") {
            $esNuevaventa = true;
            $tipoVenta = "";
            $totalSinIva = 0;
            $total = 0;
            $numeroAuditivos = 1;
            $beneficiariovalue = null;
            $gerente = $entityManager->getRepository(Usuario::class)->findOneBy(['idusuario' => $vendedor]);
            $now = new \DateTime();
            $beneficiario = $request->request->get('beneficiario');
            $nombreBeneficiario = $request->request->get('nombreBeneficiario');
            $apellidoPaternoBeneficiario = $request->request->get('apellidoPaternoBeneficiario');
            $apellidoMaternoBeneficiario = $request->request->get('apellidoMaternoBeneficiario');
            $Sucursal = $gerente->getSucursalIdsucursal();
            $numeroEmpleado = $request->request->get('numeroEmpleado');
            if ($numeroEmpleado != '') {

                $apellidoPaterno = $request->request->get('apellidoPaterno');
                $apellidoMaterno = $request->request->get('apellidoMaterno');
                $nombre = $request->request->get('nombre');
                $telefono = $request->request->get('telefono');
                $email = $request->request->get('email');


                $cliente = $entityManager->getRepository(Cliente::class)
                    ->findOneBy(['numeroempleado' => trim($numeroEmpleado)]) ?? new Cliente();

                $cliente->setNumeroempleado($numeroEmpleado);
                $cliente->setNombre($nombre);
                $cliente->setApellidopaterno($apellidoPaterno);
                $cliente->setApellidomaterno($apellidoMaterno);
                $cliente->setTelefono($telefono);
                $cliente->setEmail($email);

                $unidad = $request->request->get('unidad');
                if ($unidad || $unidad != '') {
                    $Unidadvalue = $entityManager->getRepository(Unidad::class)
                        ->findOneBy(['idunidad' => trim($unidad)]) ?? new Unidad();
                    $cliente->setUnidadIdunidad($Unidadvalue);
                }
                $entityManager->persist($cliente);
                $entityManager->flush();




                if ($beneficiario != 'ADD' && $beneficiario != '') {
                    $beneficiariovalue = $entityManager->getRepository(Cliente::class)
                        ->findOneBy(['idcliente' => trim($beneficiario)]);
                } else { // If beneficiario is empty, create a new one

                    if($beneficiario =="ADD") {

                        $telefonoBeneficiario = $request->request->get('telefonoBeneficiario');
                        $emailBeneficiario = $request->request->get('emailBeneficiario');
                        $sexoBeneficiario = $request->request->get('sexoBeneficiario');
                        $tipoBeneficiario = $request->request->get('tipoBeneficiario');
                        $fechaNacimientoBeneficiario = $request->request->get('fechaNacimientoBeneficiario');

                        // Create new Beneficiario entity
                        $beneficiariovalue = new Cliente();
                        $beneficiariovalue->setNombre($nombreBeneficiario);

                        $beneficiariovalue->setApellidopaterno($apellidoPaternoBeneficiario);
                        $beneficiariovalue->setApellidomaterno($apellidoMaternoBeneficiario);
                        $beneficiariovalue->setGenero($sexoBeneficiario);
                        $beneficiariovalue->setBeneficiarytype($tipoBeneficiario);
                        $beneficiariovalue->setUnidadIdunidad($Unidadvalue);
                        $beneficiariovalue->setHolder($cliente);

                        // Convert date string to DateTime
                        if ($fechaNacimientoBeneficiario) {
                            $beneficiariovalue->setFechanacimiento(new \DateTime($fechaNacimientoBeneficiario));
                        }



                        $entityManager->persist($beneficiariovalue);
                        $entityManager->flush();
                    }

                }

                $tipoPrestacion = $request->request->get('tipoPrestacion');
                $tipoDeVenta = $entityManager->getRepository(Tipoventa::class)->findOneBy(['idtipoventa' => $this->tiposDeVenta[$tipoPrestacion]]);


                $venta = new Venta();

                $venta->setFecha($now ?? $venta->getFecha());
                $venta->setFechacreacion($now ?? $venta->getFechacreacion());
                $venta->setFechaactualizacion($now ?? $venta->getFechacreacion());
                $venta->setTotal(0);
                $venta->setCotizacion('1');
                $venta->setNotas("");



                $venta->setUsuarioIdusuario($gerente);
                $venta->setGerente($gerente);
                $venta->setClienteIdcliente($cliente);
                $venta->setTipoventaIdtipoventa($tipoDeVenta);
                $venta->setSucursalIdsucursal($gerente->getSucursalIdsucursal());
                $venta->setPagado("0");
                $venta->setLiquidada("0");
                $venta->setCredito(0);
                $venta->setSedescontodeinventario("0");
                $venta->setDiascredito(0);
                $venta->setPorcentajeiva("16");
                $venta->setIva("16");
                $venta->setFolio($this->setfolio($entityManager));


                $entityManager->persist($venta);
                $entityManager->flush();
                $folio=$venta->getFolio();

                if ($beneficiariovalue) {
                    $venta->setBeneficiario($beneficiariovalue->getNombre()." ".$beneficiariovalue->getApellidomaterno()." ".$beneficiariovalue->getApellidomaterno());
                    $beneficiarioVenta = new Beneficiarioventa();
                    $beneficiarioVenta->setVentaIdventa($venta);
                    $beneficiarioVenta->setClienteIdcliente($beneficiariovalue);
                    $entityManager->persist($beneficiarioVenta);
                    $entityManager->flush();
                }


                $opcion = null;
                switch ($tipoPrestacion) {
                    case 'auditivos':
                        $numeroAuditivos = (int)$request->request->get('opcionesAuditivosNum', 1);
                        /*$loop = max(1, min($loop, 2));
                        $options = [];
                        for ($i = 0; $i < $loop; $i++) {
                            $options[] = '1984';
                        }*/
                        $opcionesLentesOftalmicos = $request->request->get('opcionesAuditivos', []);
                        break;
                    case 'lentes_oftalmicos':
                        $opcion = $request->request->get('opcionesLentesOftalmicos');
                        $opcionesLentesOftalmicos = $request->request->get('opcionesLentesOftalmicos');
                        break;
                    case 'lentes_contacto_blandos':
                        $opcion = $request->request->get('opcionesLentesContactoBlandos');
                        $opcionesLentesOftalmicos = $request->request->get('opcionesLentesContactoBlandos');
                        break;
                    case 'lentes_contacto_rigidos':
                        $opcion = $request->request->get('opcionesLentesContactoRigidos');
                        $opcionesLentesOftalmicos = $request->request->get('opcionesLentesContactoRigidos');
                        break;
                }

                if (!is_array($opcion)) {
                    $opcion = $opcion !== null ? [$opcion] : [];
                }

                //  foreach ($opcion as $value) {
                /*********************************************/


                foreach ($opcionesLentesOftalmicos as $key => $productoElegido) {

                    $Stock = null;
                    $idproducto = $productoElegido;
                    $idstockventa = "";
                    $esCotizacion = 1;
                    $apartararmazon = 0;

                    $iva = 16;
                    $Producto = null;
                    $Stock = null;
                    //Query donde sacamos los productos
                    $query = $entityManager->createQuery(
                        'SELECT p.idproducto, s.codigobarras as value, p.nombre ,p.precio, p.tipoproducto,
                p.idproducto, p.descripcion, m.nombre as marca, 
                c.nombre as categoria, cc.nombre as clase,  s.cantidad, p.modelo, s.idstock,p.costo, s.costo AS CostoStock, 
                p.codigobarrasuniversal,p.masivounico, p.precioespecial, p.preciosubdistribuidor, p.preciodistribuidor, s.precio as stockPrice 
                FROM App\Entity\Stock s
                inner join s.productoIdproducto p
			    inner join p.marcaIdmarca m
			    inner join p.categoriaIdcategoria c
			    inner join c.claseIdclase cc
			    inner join s.sucursalIdsucursal sucursal
                where s.status=:status and p.idproducto=:idproducto  and sucursal.idsucursal=:idsucursal order by s.idstock desc
            '
                    )->setParameters(['status' => "1", 'idproducto' => $idproducto, 'idsucursal' => $Sucursal->getIdsucursal()]);
                    $producto = $query->getResult();


                    if (!$producto) {
                        $Producto = $entityManager->getRepository(Producto::class)->find($idproducto);
                        $Stock = new Stock();
                        $Stock->setProductoIdproducto($Producto);
                        $Stock->setSucursalIdsucursal($Sucursal);
                        $Stock->setCantidad("100");
                        $Stock->setCreacion(new \DateTime("now"));
                        $Stock->setModificacion(new \DateTime("now"));

                        $tmpStockState = $entityManager->getRepository(Stockstate::class)->findOneBy(array('name' => "DISPONIBLE"));
                        $Stock->setStockstateIdstockstate($tmpStockState);
                        $entityManager->persist($Stock);
                        $entityManager->flush();
                        $producto = $query->getResult();
                        $producto = $producto[0];
                    }else{
                        $producto = $producto[0];
                        $Stock = $entityManager->getRepository(Stock::class)->find($producto['idstock']);
                    }

                    if ($producto) {


                        //$tipoproducto = $producto['tipoproducto'];
                        //hay produyctos

                        $precioSeleccionado = 0;
                        if ($tipoDeVenta->getPreciobase() == "0") {
                            $precioSeleccionado = $producto['precio'];
                        } else if ($tipoDeVenta->getPreciobase() == "1") {
                            $precioSeleccionado = $producto['precioespecial'];
                        } else if ($tipoDeVenta->getPreciobase() == "2") {
                            $precioSeleccionado = $producto['preciodistribuidor'];
                        } else if ($tipoDeVenta->getPreciobase() == "3") {
                            $precioSeleccionado = $producto['preciosubdistribuidor'];
                        }

                        //  foreach ($producto as $productos) {


                        // Revisamos que el producto tenga codigo si no no se muestra



                        $producto['precioConIva'] = $precioSeleccionado + ($precioSeleccionado * (.01 * $iva));
                        $producto['productoPrecioFinal'] = $precioSeleccionado + ($precioSeleccionado * (.01 * $iva));
                        $producto['precioespecial'] = $producto['precioespecial'] ? number_format((float)$producto['precioespecial'] + ($producto['precioespecial'] * (.01 * $iva)), 2, '.', '') : null;
                        $producto['preciosubdistribuidor'] = $producto['preciosubdistribuidor'] ? number_format((float)$producto['preciosubdistribuidor'] + ($producto['preciosubdistribuidor'] * (.01 * $iva)), 2, '.', '') : null;
                        $producto['preciodistribuidor'] = $producto['preciodistribuidor'] ? number_format((float)$producto['preciodistribuidor'] + ($producto['preciodistribuidor'] * (.01 * $iva)), 2, '.', '') : null;
                        //$producto['value'] = $producto['value'] . "-" . $producto['codigobarrasuniversal'];
                        // echo "<br>precio ".$producto['precio'];
                        //$total+=(float)$producto['total'];


                        $total += $producto['precioConIva'];
                        $totalSinIva += $precioSeleccionado;

                        //   }


                        if ($producto['marca'] == "Sin marca") {
                            $producto['marca'] = "";
                        }
                        $producto['nombre'] = $producto['marca'] . " / " . $producto['modelo'] . " / " . $producto['descripcion'] . " / " . $producto['value'] . " / " . $producto['codigobarrasuniversal'];
                        // $producto['value']=$producto['marca']." / ".$producto['modelo']." / ".$producto['descripcion']." / ".$producto['nombre'];

                        $exito = true;

                    } else {
                        $msj = "No hay productos";
                    }

                    $Stockventa = new Stockventa();
                    $Stockventa->setCreacion(new \DateTime("now"));
                    $Stockventa->setModificacion(new \DateTime("now"));
                    //   }

                    $Stockventa->setCantidad($numeroAuditivos);
                    $Stockventa->setFixproduct("1");
                    //solo se descuenta si es venta o es cotizacion y apartado


                    $tmpCost = ($producto['CostoStock'] > 0) ? $producto['CostoStock'] : $producto['costo'];

                    $Stockventa->setCosto($tmpCost);

                    // if($tipoproducto=="2"){
                    //aqui va lo nuevo de la base de adtos
                    // $Stockventa->setProductoIdproducto($Producto);
                    // }else{
                    $Stockventa->setStockIdstock($Stock);
                    // }

                    $Stockventa->setPreciobase($producto['precioConIva']);
                    $Stockventa->setPreciofinal($producto['precioConIva']);
                    $Stockventa->setPrecio($producto['productoPrecioFinal']);


                    $Stockventa->setStatus("1");


                    $Stockventa->setVentaIdventa($venta);
                    // $Stockventa->setPorcentajedescuento($productoElegido['productoDescuento']);
                    //$Stockventa->setPreciofinal($Producto->getPrecio());
                    $Stockventa->setStatus("1");

                    //if ($updateToSale && $apartararmazon == "1") $Stock->setApartados($Stock->getApartados() - $productoCantidad);


                    $Stockventa->setIsomittable(0);


                    $entityManager->persist($Stockventa);
                }


                $iva = ($total / (1 + $this->iva)) * $this->iva;
                $venta->setPorcentajeiva($this->iva);
                $venta->setIva($iva);

                $venta->setTotal($totalSinIva);
                $venta->setPagado($total);
                $entityManager->persist($venta);
                $entityManager->persist($cliente);
                $entityManager->flush();

                $this->addFlash(
                    'success',
                    'Cotización guardada correctamente Folio: '.$folio
                );
            }
        }

        $qb = $entityManager->createQueryBuilder();

        $qb->select('u.idunidad, u.nombre')
            ->from(Unidad::class, 'u')
            ->where('u.status = :status')
            ->setParameter('status', 1);

        $unidades = $qb->getQuery()->getResult();

        return $this->render('pre_auth_uam/index.html.twig', [
            'unidades' => $unidades, 'vendedor' => $vendedor
        ]);
    }

    /**
     * @Route("/employee-autofill", name="employee_autofill", methods={"GET"})
     */
    public function employeeAutofill(
        Request                $request,
        EntityManagerInterface $entityManager
    )
    {
        $numero = $request->query->get('numeroEmpleado');

        if (!$numero || $numero == '') {
            return new JsonResponse(['success' => false]);
        }

        $cliente = $entityManager
            ->getRepository(Cliente::class)
            ->findOneBy(['numeroempleado' => trim($numero)]);


        if ($cliente) {
            $qb = $entityManager->createQueryBuilder();

            $qb->select('b.idcliente, b.nombre, b.tipocliente as tipo')
                ->from(Cliente::class, 'b')
                ->where('b.holder = :cliente')
                ->andWhere('b.status = :status')
                ->setParameter('cliente', $cliente)
                ->setParameter('status', 1);

            $beneficiarios = $qb->getQuery()->getResult();

            $data = [
                'id' => $cliente->getIdcliente(),
                'nombre' => $cliente->getNombre(),
                'apellidoPaterno' => $cliente->getApellidopaterno(),
                'apellidoMaterno' => $cliente->getApellidomaterno(),
                'beneficiarios' => $beneficiarios,
                'unidad' => $cliente->getUnidadIdunidad()->getIdunidad(),
                'telefono' => $cliente->getTelefono(),
                'email' => $cliente->getEmail(),
            ];

            return new JsonResponse(['success' => true, 'data' => $data]);
        }

        return new JsonResponse(['success' => false]);
    }

    public function setfolio(EntityManagerInterface $entityManager): int
    {

        $queryBuilder = $entityManager->createQueryBuilder();
        $result = $queryBuilder
            ->select('v.folio')
            ->from('App\Entity\Venta', 'v')
            ->innerJoin('v.sucursalIdsucursal', 's')
            ->innerJoin('s.empresaIdempresa', 'e')
            ->where('e.rfc = :empresa')
            ->orderBy('v.idventa', 'DESC')
            ->setParameter('empresa', $this->empRFC)
            ->setMaxResults(1)
            ->getQuery()
            ->getSingleScalarResult();

        $ultimoFolio = (int)$result + 1;

        return $ultimoFolio;
    }

    function eliminar_simbolos($string)
    {

        $string = trim($string);

        $string = str_replace(
            array('á', 'à', 'ä', 'â', 'ª', 'Á', 'À', 'Â', 'Ä'),
            array('a', 'a', 'a', 'a', 'a', 'A', 'A', 'A', 'A'),
            $string
        );

        $string = str_replace(
            array('é', 'è', 'ë', 'ê', 'É', 'È', 'Ê', 'Ë'),
            array('e', 'e', 'e', 'e', 'E', 'E', 'E', 'E'),
            $string
        );

        $string = str_replace(
            array('í', 'ì', 'ï', 'î', 'Í', 'Ì', 'Ï', 'Î'),
            array('i', 'i', 'i', 'i', 'I', 'I', 'I', 'I'),
            $string
        );

        $string = str_replace(
            array('ó', 'ò', 'ö', 'ô', 'Ó', 'Ò', 'Ö', 'Ô'),
            array('o', 'o', 'o', 'o', 'O', 'O', 'O', 'O'),
            $string
        );

        $string = str_replace(
            array('ú', 'ù', 'ü', 'û', 'Ú', 'Ù', 'Û', 'Ü'),
            array('u', 'u', 'u', 'u', 'U', 'U', 'U', 'U'),
            $string
        );

        $string = str_replace(
            array('ñ', 'Ñ', 'ç', 'Ç'),
            array('n', 'N', 'c', 'C',),
            $string
        );

        $string = str_replace(
            array(
                "\\",
                "¨",
                "º",
                "-",
                "~",
                "#",
                "@",
                "|",
                "!",
                "\"",
                "·",
                "$",
                "%",
                "&",
                "/",
                "(",
                ")",
                "?",
                "'",
                "¡",
                "¿",
                "[",
                "^",
                "<code>",
                "]",
                "+",
                "}",
                "{",
                "¨",
                "´",
                ">",
                "< ",
                ";",
                ",",
                ":",
                ".",
                " "
            ),
            ' ',
            $string
        );
        return $string;
    }
}