<?php

namespace App\Controller;

use App\Entity\Venta;
use App\Entity\Ventagroup;
use App\Entity\Ventafactura;
use App\Form\VentafacturaType;
use App\Entity\Authstage;
use App\Entity\Paymenttype;
use App\Entity\Salelog;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Doctrine\ORM\EntityManagerInterface;
use Twig\Environment;

use Symfony\Component\HttpFoundation\File\UploadedFile;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Component\HttpFoundation\StreamedResponse;

class FacturaAutorizacionController extends AbstractController
{
    private $twig;
    private $stages = [
        "Sin asociar", "Mandar a corporativo", "Recibir autorizaciones", "Mandar a facturar", "Facturar convenio"
    ];

    private $stagesnames = [
        "Sin asociar","En Sucursal", "Por recibir en Corporativo.", "En Corporativo", "En Proceso a Facturar", "Pendiente de Pago"
    ];

    public function __construct(Environment $twig)
    {
        $this->twig = $twig;
    }

    /**
     * @Route("/admin/factura/autorizacion/{stage}", name="app_factura_autorizacion")
     */
    public function index(EntityManagerInterface $entityManager, int $stage = 1): Response
    {
        if ($stage < 1) $stage = 1;

        return $this->render('factura_autorizacion/index.html.twig', [
            'stage' => $stage,
            'titulo' => $this->stages[$stage],
        ]);
    }

    /**
     * @Route("/get-auth-table", name="facturaautorizacion-get-auth-table")
     */
    public function getAuthTable(EntityManagerInterface $entityManager, int $stage = 1): Response
    {

        $paytype = $entityManager->getRepository(Paymenttype::class)->findOneBy(array('name' => "Convenio")) ?? new Paymenttype();
            $paytype->setName("Convenio");
            $entityManager->persist($paytype);
            $entityManager->flush();


        $query = $entityManager->createQuery(
            'SELECT COALESCE(un.nombre, \'sin unidad\')  as u_n, v.idventa, v.folio, v.authscan, SUM(pay.monto)as pagado,
            cl.numeroempleado as cl_num, CONCAT(cl.apellidopaterno, \' \' , cl.apellidomaterno, cl.nombre, \' \') as cl_n, cl2.beneficiarytype, cl.numeroempleado,
            cl2.numeroempleado as cl2_num, CONCAT(cl2.apellidopaterno, \' \' , cl2.apellidomaterno, cl2.nombre, \' \') as cl2_n, cl2.numeroempleado
            FROM App\Entity\Venta v 
            INNER JOIN v.clienteIdcliente cl
            LEFT JOIN App\Entity\Beneficiarioventa bv With bv.ventaIdventa = v.idventa
            LEFT JOIN bv.clienteIdcliente cl2
            LEFT JOIN cl.unidadIdunidad un
            INNER JOIN v.authstageIdauthstage aus
            INNER JOIN App\Entity\Pago pay With pay.ventaIdventa = v.idventa
            WHERE v.status = 1 AND aus.stageorder = 4 AND pay.paymenttypeIdpaymenttype = :paytype
            AND pay.ventaIdventa = v.idventa AND pay.status = 1
            GROUP BY v.idventa ORDER BY v.pagado 
            '
        )->setParameter('paytype', $paytype);;

        $values = $query->getResult();

        $autorizaciones['sin unidad'] = [];
        foreach ($values as $value) {
            $unidad = $value['u_n'];

            $autorizaciones[$unidad][] = $value;
        }

        return $this->render('factura_autorizacion/auth-table.html.twig', [
            'values' => $autorizaciones
        ]);
    }

    /**
     * @Route("/facturas-en-grupo/{stage}", name="app_factura_grupo")
     */
    public function groupFacturaVenta(EntityManagerInterface $entityManager): Response
    {
        $query = $entityManager->createQuery(
            'SELECT DISTINCT vg.idventagroup, v.idventa, v.folio, v.authscan, v.pagado,
            cl.numeroempleado, CONCAT(cl.apellidopaterno, \' \' , cl.apellidomaterno, cl.nombre, \' \') as cl_n, cl.beneficiarytype, cl.numeroempleado,
            cl2.numeroempleado, CONCAT(cl2.apellidopaterno, \' \' , cl2.apellidomaterno, cl2.nombre, \' \') as cl2_n, cl2.numeroempleado
            FROM App\Entity\Venta v 
            INNER JOIN v.ventagroupIdventagroup vg
            INNER JOIN vg.ventafacturaIdventafactura vf
            LEFT JOIN v.clienteIdcliente cl
            LEFT JOIN App\Entity\Beneficiarioventa bv With bv.ventaIdventa = v.idventa
            LEFT JOIN bv.clienteIdcliente cl2
            LEFT JOIN cl.unidadIdunidad un
            WHERE v.status = 1'
        );

        $values = $query->getResult();


        $autorizaciones = [];
        foreach ($values as $value) {
            $unidad = $value['idventagroup'];

            $autorizaciones[$unidad][] = $value;
        }

        return $this->render('factura_autorizacion/facturas_en_grupo.html.twig', [
            'values' => $autorizaciones
        ]);
    }

    /**
     * @Route("/factura/autorizacion/get-auth/{scan}/{index}/{base}", name="app_factura_autorizacion_auth")
     */
    public function getauth(EntityManagerInterface $entityManager, string $scan = null, int $index = 0, int $base = 1): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => 'Invalid scan value.',
            'data' => null
        ];

        if ($scan) {
            $paytype = $entityManager->getRepository(Paymenttype::class)->findOneBy(array('name' => "Convenio")) ?? new Paymenttype();
            $paytype->setName("Convenio");
            $entityManager->persist($paytype);
            $entityManager->flush();

            $query = $entityManager->createQuery(
                'SELECT COALESCE(un.nombre, \'sin unidad\')  as u_n, v.idventa, v.folio, v.authscan, SUM(pay.monto)as pagado,
                CONCAT(cl.apellidopaterno, \' \' , cl.apellidomaterno, \' \' , cl.nombre) as cl_n, cl2.beneficiarytype, cl.numeroempleado as cl_num, 
                CONCAT(cl2.apellidopaterno, \' \' , cl2.apellidomaterno, \' \' , cl2.nombre) as cl2_n, 
                cl2.numeroempleado as cl2_num, aus.stageorder, aus.name as sname
                FROM App\Entity\Venta v 
                INNER JOIN v.clienteIdcliente cl
                LEFT JOIN App\Entity\Beneficiarioventa bv With bv.ventaIdventa = v.idventa
                LEFT JOIN bv.clienteIdcliente cl2
                LEFT JOIN cl.unidadIdunidad un
                LEFT JOIN v.authstageIdauthstage aus
                INNER JOIN App\Entity\Pago pay With pay.ventaIdventa = v.idventa
                WHERE v.authscan = :scan  AND v.status = 1 AND pay.paymenttypeIdpaymenttype = :paytype AND pay.ventaIdventa = v.idventa AND pay.status = 1
                GROUP BY v.idventa'
            )->setParameter('scan', $scan)->setParameter('paytype', $paytype);//->setParameter('base', $base);

            //aus.stageorder <= :base OR 
            $value = $query->getOneOrNullResult();

            if ($value) {
                if (isset($value['stageorder']) && (intval($value['stageorder']) > $base || intval($value['stageorder']) < $base)) {
                    $response['message'] = "Autotización se encuentra en {$value['sname']}.";
                } else {
                    $htmlContent = $this->twig->render('factura_autorizacion/auth_scan.html.twig', [
                        'venta' => $value,
                        'index' => $index,
                        'checked' => 'true'
                    ]);
                    $response['success'] = true;
                    $response['message'] = 'Scan value found.';
                    $response['data'] = $htmlContent;
                }
            } else {
                $response['message'] = 'Valor no encontrado.';
            }
        }

        return new JsonResponse($response);
    }

    /**
     * @Route("/admin/factura/submit-compiled/{state}", name="app_factura_compiled_submit", methods={"POST"})
     */
    public function submitCompiled(EntityManagerInterface $entityManager, Request $request, int $state = 1)
    {
        $success = true;
        $message = 'No se proceso';
        $scannedValues = $request->request->all('rows');
        $ids = $scannedValues['scans'];

        if ($state < 1)
            $state = 1;

        try {
            $ventagroup = null;

            if (count($ids) <= 0) {
                throw new \Exception('No se han seleccionado elementos.');
            }

            $query = $entityManager->createQuery(
                'SELECT v
             FROM App\Entity\Venta v 
             WHERE v.idventa IN (:ids) AND v.status = 1'
            )->setParameter('ids', $ids);

            $values = $query->getResult();

            if ($state == 5) {
                $filteredValues = array_filter($values, function ($entity) {
                    return $entity->getAuthstageIdauthstage()->getStageorder() == '5';
                });

                if (count($filteredValues) > 0) {
                    throw new \Exception("Algunas opciones seleccionadas ya fueron previamente facturadas.");
                }

                /** @var UploadedFile $file */
                $file = $request->files->get('fileInput');

                if ($file) {
                    //Verifica si es un archivo zip
                    if ($file->getClientMimeType() !== 'application/zip' && $file->getClientOriginalExtension() !== 'zip') {
                        throw new \Exception('The file is not a ZIP file');
                    } else {
                        //Si si es un zip crea la clase ZipArchive
                        $zip = new \ZipArchive();
                        //el metodo getPathname te da toda la ruta del archivo
                        if ($zip->open($file->getPathname()) === TRUE) {
                            $hasXml = false;
                            $hasPdf = false;
                            $xmlContent = null;

                            // Recorre los archivos dentro del ZIP
                            for ($i = 0; $i < $zip->numFiles; $i++) {
                                $filename = $zip->getNameIndex($i);
                                $extension = pathinfo($filename, PATHINFO_EXTENSION);

                                // Verifica si el archivo es XML o PDF
                                if ($extension === 'xml') {
                                    $hasXml = true;
                                    $xmlContent = $zip->getFromName($filename); // Obtiene el contenido del XML
                                } elseif ($extension === 'pdf') {
                                    $hasPdf = true;
                                }
                            }

                            // Verifica si ambos archivos están presentes
                            if (!$hasXml || !$hasPdf) {
                                throw new \Exception('El archivo ZIP debe contener al menos un XML y un PDF.');
                            }

                            // Procesa el XML
                            $xml = simplexml_load_string($xmlContent);
                            $namespaces = $xml->getNamespaces(true);
                            $xml->registerXPathNamespace('cfdi', $namespaces['cfdi']);

                            $emisor = $xml->xpath('//cfdi:Emisor')[0];
                            $receptor = $xml->xpath('//cfdi:Receptor')[0];
                            $comprobante = $xml->xpath('//cfdi:Comprobante')[0];

                            $emisorRfc = (string)$emisor['Rfc'];
                            $emisorNombre = (string)$emisor['Nombre'];
                            $receptorRfc = (string)$receptor['Rfc'];
                            $receptorNombre = (string)$receptor['Nombre'];
                            $montoVenta = (string)$comprobante['Total'];
                            $regimenFiscal = (string)$emisor['RegimenFiscal'];
                            $usoCFDI = (string)$receptor['UsoCFDI'];
                            $metodoPago = (string)$comprobante['MetodoPago'];
                            $formaPago = (string)$comprobante['FormaPago'];
                            $condicionesPago = isset($comprobante['CondicionesDePago']) ? (string)$comprobante['CondicionesDePago'] : null;
                            $lugarExpedicion = (string)$comprobante['LugarExpedicion'];
                            $fechaComprobante = new \DateTime((string)$comprobante['Fecha']);
                            $now = new \DateTime("now");

                            // Busca la venta asociada
                            $ventaEncontrada = $entityManager->getRepository(Venta::class)->findOneBy(['idventa' => $values]);

                            if (!$ventaEncontrada instanceof Venta) {
                                throw new \Exception("La variable ventaEncontrada no es una instancia de Venta.");
                            }

                            // Crea la entidad Ventafactura
                            $ventafactura = new Ventafactura();
                            $ventafactura->setVentaIdventa($ventaEncontrada);
                            $ventafactura->setRfcemisor($emisorRfc);
                            $ventafactura->setRfcreceptor($receptorRfc);
                            $ventafactura->setNombreemisor($emisorNombre);
                            $ventafactura->setNombrereceptor($receptorNombre);
                            $ventafactura->setMontoventa($montoVenta);
                            $ventafactura->setRegimenfiscal($regimenFiscal);
                            $ventafactura->setUsucfdi($usoCFDI);
                            $ventafactura->setMetodopago($metodoPago);
                            $ventafactura->setFormapago($formaPago);
                            $ventafactura->setCondicionespago($condicionesPago);
                            $ventafactura->setLugarexpedicion($lugarExpedicion);
                            $ventafactura->setFechacomprobante($fechaComprobante);
                            $ventafactura->setCreacion($now);
                            $ventafactura->setNombrearchivoxml($filename);

                            $entityManager->persist($ventafactura);
                            $entityManager->flush();

                            // Crea la entidad Ventagroup
                            $ventagroup = new Ventagroup();
                            $ventagroup->setCreationdate($now)
                                ->setVentafacturaIdventafactura($ventafactura);

                            $entityManager->persist($ventagroup);
                            $entityManager->flush();

                            // Guarda el archivo ZIP
                            $zipName = $ventagroup->getIdventagroup() . '-' . $ventafactura->getIdventafactura() . '-' . time() . ".zip";
                            $ventafactura->setNombrezip($zipName);
                            $entityManager->persist($ventafactura);
                            $entityManager->flush();

                            $path = $this->getParameter('authZip') . "/" . $ventagroup->getIdventagroup() . '/';
                            if (!file_exists($path)) {
                                mkdir($path, 0777, true);
                            }
                            $file->move($path, $zipName);

                            $zip->close();
                        } else {
                            throw new \Exception('Failed to open ZIP file');
                        }
                    }
                } else {
                    throw new \Exception('No file uploaded');
                }
            }

            if ($success) {
                $AuthStage = $entityManager->getRepository(Authstage::class)->findOneBy(['stageorder' => $state]) ?? new AuthStage();
                $AuthStage->setStageorder($state)->setName($this->stagesnames[$state]);
                $entityManager->persist($AuthStage);
                $entityManager->flush();

                foreach ($values as $value) {
                    $tempSaleLog = new Salelog();
                    $tempSaleLog->setName($AuthStage->getName());
                    $tempSaleLog->setDate(new \DateTime("now"));
                    $tempSaleLog->setAuthstageIdauthstage($AuthStage);
                    $tempSaleLog->setVentaIdventa($value);
                    $tempSaleLog->setUsuarioIdusuario($this->getUser());
                    $value->setAuthstageIdauthstage($AuthStage);
                    if ($ventagroup) {
                        $value->setVentagroupIdventagroup($ventagroup);
                    }
                    $entityManager->persist($tempSaleLog);
                    $entityManager->persist($value);
                }
                $entityManager->flush();

                $message = 'Updated values: ';
            }
        } catch (\Exception $e) {
            $success = false;
            $message = $e->getMessage();
        }

        return new JsonResponse([
            'success' => $success,
            'message' => $message,
        ]);
    }



    private function generateExcelFile(array $scannedValues): string
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Scans');

        // Set headers
        $sheet->setCellValue('A1', 'TITULAR');
        $sheet->setCellValue('B1', 'BENEFICIARIO');
        $sheet->setCellValue('C1', 'N° DE EMPLEADO');
        $sheet->setCellValue('D1', 'N° DE FACTURA');
        $sheet->setCellValue('E1', 'SUBTOTAL');
        $sheet->setCellValue('F1', 'TOTAL');

        $sheet->getColumnDimension('A')->setWidth(30);
        $sheet->getColumnDimension('B')->setWidth(30);
        $sheet->getColumnDimension('C')->setWidth(30);
        $sheet->getColumnDimension('D')->setWidth(30);
        $sheet->getColumnDimension('E')->setWidth(30);
        $sheet->getColumnDimension('F')->setWidth(30);
        // Add more headers as needed

        // Add data

        $row = 2;
        $sheet->setCellValue('F' . $row, '=SUM(E:E)');
        foreach ($scannedValues as $index => $value) {
            $sheet->setCellValue('A' . $row, $value['client']);
            $sheet->setCellValue('B' . $row, $value['altClient']);
            $sheet->setCellValue('C' . $row, $value['employeeNumber']);
            $sheet->setCellValue('D' . $row, $value['folio']);
            $sheet->setCellValue('E' . $row, $value['total']);
            // Add more cells as needed
            $row++;
        }

        $writer = new Xlsx($spreadsheet);

        ob_start();
        $writer->save('php://output');
        $fileContent = ob_get_contents();
        ob_end_clean();

        return $fileContent;
    }

}
