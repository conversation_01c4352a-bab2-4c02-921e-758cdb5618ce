<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

use DoctrineExtensions\Query\Mysql;


class ReporteVentasController extends AbstractController
{
    /**
     * @Route("/reporte/ventas", name="app_reporte_ventas")
     */
    public function index(): Response
    {
        $em = $this->getDoctrine()->getManager();

        $Usuario = $this->getUser();
        $query = $em->createQuery(
            'SELECT e.idempresa, e.nombre
               FROM App\Entity\Usuarioempresapermiso uem
               inner join uem.empresaIdempresa e
               inner join uem.usuarioIdusuario u
               where e.status =:status and u.idusuario=:idusuario order by e.nombre asc 
               '
        )->setParameters(['status' => "1", 'idusuario' => $Usuario->getIdUsuario()]);
        $empresas = $query->getResult();



        return $this->render('reporte_ventas/index.html.twig', [
            'empresas' => $empresas,

        ]);
    }

    /**
     * @Route("/obtener-ventas", name="obtener-ventas")
     */
    public function obtenerVentas(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $tipoventas = $request->get("tipoventas");
        $sucursales = $request->get("sucursales");
        $enterpriseId = $request->get("enterpriseId");
        $saleQuotations = $request->get("saleQuotations");
        $fechaInicio = \DateTime::createFromFormat('d/m/Y', $request->get("fechaInicio"));
        $fechaFin = \DateTime::createFromFormat('d/m/Y', $request->get("fechaFin"));
        $tipoFecha = $request->get("fecha_ventaCotizacion");


        if (!$fechaInicio || !$fechaFin) {

            $fechoInicioHoy = new \DateTime("now");
            $fechoFinHoy = new \DateTime("now");

            $fechaInicio =  $fechoInicioHoy->format('Y-m-d') . " 00:00:00";
            $fechaFin =  $fechoFinHoy->format('Y-m-d') . " 23:59:00";
        } else {
            $fechaInicio = $fechaInicio->format('Y-m-d') . " 00:00:00";
            $fechaFin =  $fechaFin->format('Y-m-d') . " 23:59:00";
        }

        $whereFecha = "";
        if ($tipoFecha == "1") { // Si se seleccionó "Fecha de Creación"
            $whereFecha = "v.fechacreacion BETWEEN :fechaInicio AND :fechaFin";
        } else { // Si se seleccionó "Fecha de Venta" o cualquier otro valor por defecto
            $whereFecha = "v.fechaventa BETWEEN :fechaInicio AND :fechaFin";
        }


        $whereTipoVenta = "";

        //Tipos de venta
        if (isset($tipoventas[0])) {

            $whereTipoVenta = " and (";

            for ($i = 0; $i < count($tipoventas); $i++) {

                $whereTipoVenta .= " tv.idtipoventa=" . $tipoventas[$i][0];

                if ($i != count($tipoventas) - 1) $whereTipoVenta .= " or ";
            }
            $whereTipoVenta .= ") ";
        } else $whereTipoVenta = " and tv.idtipoventa = -1 ";


        $whereSucursal = "";

        //Sucursales
        if (isset($sucursales[0])) {

            $whereSucursal = " and (";

            for ($i = 0; $i < count($sucursales); $i++) {

                $whereSucursal .= " suc.idsucursal=" . $sucursales[$i];

                if ($i != count($sucursales) - 1) $whereSucursal .= " or ";
            }
            $whereSucursal .= ") ";
        } else $whereSucursal = " and suc.idsucursal= 0 ";

        $whereSaleQuotations = "";

        //Sale and quotation filter
        if (isset($saleQuotations[0])) {

            $whereSaleQuotations = " and (";

            for ($i = 0; $i < count($saleQuotations); $i++) {

                $whereSaleQuotations .= " v.cotizacion=" . $saleQuotations[$i];

                if ($i != count($saleQuotations) - 1) $whereSaleQuotations .= " or ";
            }
            $whereSaleQuotations .= ") ";
        } else $whereSaleQuotations = " and v.cotizacion = -1 ";


        $query = $em->createQuery(
            'SELECT SUM(v.pagado) as cantidad, suc.nombre as sucursal, tv.nombre as tipoventa, tv.idtipoventa as idtipoventa, COUNT(v.idventa) as salesAmount
               FROM App\Entity\Venta v
               INNER JOIN  v.sucursalIdsucursal suc
               INNER JOIN  suc.empresaIdempresa e
               INNER JOIN  v.tipoventaIdtipoventa tv
               WHERE ' . $whereFecha . ' and v.status =:status and e.idempresa =:enterpriseId and v.total >= 0 ' . $whereSaleQuotations . $whereTipoVenta . $whereSucursal . ' group by suc.nombre, tv.idtipoventa order by suc.nombre asc
               '
        )->setParameters(['status' => "1", 'enterpriseId' => $enterpriseId, 'fechaInicio' => $fechaInicio, 'fechaFin' => $fechaFin]);
        $totales = $query->getResult();

        $totalesSucursalMapped = [];
        $sucursales1 = [];

        foreach ($totales as $item) {
            $sucursal = $item['sucursal'];
            if (!isset($totalesSucursalMapped[$sucursal])) {
                $totalesSucursalMapped[$sucursal] = [];
                array_push($sucursales1, $item['sucursal']);
            }
            $totalesSucursalMapped[$sucursal][] = $item;
        }



        $query = $em->createQuery(
            'SELECT SUM(v.pagado) as cantidad, tv.nombre as tipoventa, tv.idtipoventa as idtipoventa, COUNT(v.idventa) as salesAmount
               FROM App\Entity\Venta v
               INNER JOIN  v.sucursalIdsucursal suc
               INNER JOIN  suc.empresaIdempresa e
               INNER JOIN  v.tipoventaIdtipoventa tv
               where ' . $whereFecha . ' and v.status =:status and e.idempresa =:enterpriseId and v.total >= 0 ' . $whereSaleQuotations . $whereTipoVenta . $whereSucursal . ' group by tv.idtipoventa order by cantidad asc
               '
        )->setParameters(['status' => "1", 'enterpriseId' => $enterpriseId, 'fechaInicio' => $fechaInicio, 'fechaFin' => $fechaFin,]);
        $totales2 = $query->getResult();

        $query = $em->createQuery(
            'SELECT SUM(sv.preciofinal * sv.cantidad) as total, cl.nombre
               FROM App\Entity\Stockventa sv
               INNER JOIN  sv.stockIdstock s
               INNER JOIN  s.productoIdproducto p
               INNER JOIN  p.categoriaIdcategoria sb
               INNER JOIN  sb.claseIdclase cl
               INNER JOIN  sv.ventaIdventa v
               INNER JOIN  v.tipoventaIdtipoventa tv
               INNER JOIN  v.sucursalIdsucursal suc
               INNER JOIN  suc.empresaIdempresa e
               where v.status=:status and sv.status=:status and e.idempresa =:enterpriseId and ' . $whereFecha . $whereTipoVenta . $whereSaleQuotations . $whereSucursal . '
                group by cl.idclase order by total asc
               '
        )->setParameters(['status' => "1", 'enterpriseId' => $enterpriseId, 'fechaInicio' => $fechaInicio, 'fechaFin' => $fechaFin]);
        $categoryProfit = $query->getResult();
        
        $query = $em->createQuery(
            'SELECT SUM(sv.preciofinal * sv.cantidad) as total, sb.nombre
               FROM App\Entity\Stockventa sv
               INNER JOIN  sv.stockIdstock s
               INNER JOIN  s.productoIdproducto p
               INNER JOIN  p.categoriaIdcategoria sb
               INNER JOIN  sb.claseIdclase cl
               INNER JOIN  sv.ventaIdventa v
               INNER JOIN  v.tipoventaIdtipoventa tv
               INNER JOIN  v.sucursalIdsucursal suc
               INNER JOIN  suc.empresaIdempresa e

               where v.status=:status and sv.status=:status AND sv.status =:status and e =:enterpriseId and ' . $whereFecha .  $whereTipoVenta . $whereSaleQuotations . $whereSucursal . '
                group by sb.idcategoria order by total asc
               '
        )->setParameters(['status' => "1", "enterpriseId" => $enterpriseId, 'fechaInicio' => $fechaInicio, 'fechaFin' => $fechaFin]);
        $subcategoryProfit = $query->getResult();

        $totalTipoVenta = [];
        $totalSalesType = [];

        foreach ($totales as $item) {
            $sucursal = $item['tipoventa'];
            if (!isset($totalTipoVenta[$sucursal])) {
                $totalTipoVenta[$sucursal] = [];
            }
            $totalTipoVenta[$sucursal][] = $item;
        }

        $saleTypeAvailable = [];

        foreach ($totales2 as $item) {
            $totalSalesType[$item['cantidad']] = $item['salesAmount'];

            $saleTypeTemp = ($item['tipoventa'] == "UAM") ? "Prestación UAM" : $item['tipoventa'];
            array_push($saleTypeAvailable, [$item['idtipoventa'], $saleTypeTemp]);
        }

        $resultArray = [];

        foreach ($totalTipoVenta as $tipoventa => $objects) {
            // Create an array for the current tipoventa
            $currentArray = [];

            // Iterate over each sucursal
            for ($i = 0; $i < count($sucursales1); $i++) {
                // Check if any object for the current sucursal exists in the original array
                $exists = false;
                foreach ($objects as $obj) {
                    if ($obj['sucursal'] == $sucursales1[$i]) {
                        $exists = true;
                        break;
                    }
                }

                // Assign the value to the current sucursal in the current tipoventa array
                $currentArray[$i] = $exists ? ['sucursal' => $sucursales1[$i], 'cantidad' => $obj['cantidad']] : ['sucursal' => $sucursales1[$i], 'cantidad' => 0];
            }

            // Assign the current tipoventa array to the resulting array
            $resultArray[$tipoventa] = $currentArray;
        }

        return $this->render('reporte_ventas/ventasestadisticas.html.twig', [
            'totalesSucursal' => $totalesSucursalMapped,
            'totales' => $totales2,
            'totalesTipoVentaSucursal' => $resultArray,
            'tipoventas' => $saleTypeAvailable,
            'totalSalesType' => $totalSalesType,
            'categoryProfit' => $categoryProfit,
            'subcategoryProfit' => $subcategoryProfit,
        ]);
    }
}
