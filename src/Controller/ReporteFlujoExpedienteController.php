<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use App\Entity\Stockventa;
use App\Entity\Venta;
use Symfony\Component\HttpFoundation\JsonResponse;

class ReporteFlujoExpedienteController extends AbstractController
{
    /**
     * @Route("/reporte/flujo/expediente", name="app_reporte_flujo_expediente")
     */
    public function index(): Response
    {
        return $this->render('reporte_flujo_expediente/index.html.twig', [
            'controller_name' => 'ReporteFlujoExpedienteController',
        ]);
    }

    /**
     * @Route("/obtener-datos", name="obtener_datos_reporte")
     */
    public function obtenerDatos(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();

        // Obtener parámetros de filtro
        $fechaVentaDesde = $request->query->get('fechaVentaDesde');
        $fechaVentaHasta = $request->query->get('fechaVentaHasta');
        $fechaCreacionDesde = $request->query->get('fechaCreacionDesde');
        $fechaCreacionHasta = $request->query->get('fechaCreacionHasta');
        $cotizacion = $request->query->get('cotizacion');
        $folio = $request->query->get('folio');
        $sucursal = $request->query->get('sucursal');
        $nombreCliente = $request->query->get('nombreCliente');
        $emailCliente = $request->query->get('emailCliente');
        $telefonoCliente = $request->query->get('telefonoCliente');
        $liquidada = $request->query->get('liquidada');

        // Obtener parámetros de paginación
        $page = max(1, (int)$request->query->get('page', 1));
        $limit = (int)$request->query->get('limit', 1500);
        $offset = ($page - 1) * $limit;

        // Construir la consulta base - Optimizada para rendimiento
        $qb = $em->createQueryBuilder();
        $qb->select([
            'v.idventa',
            'v.folio               AS folioVenta',
            'v.authorizationnumber AS authorizationnumber',
            'c.idcliente           AS idCliente',
            'c.nombre              AS nombreCliente',
            'c.apellidopaterno     AS apellidoPaternoCliente',
            'c.apellidomaterno     AS apellidoMaternoCliente',
            'c.email               AS emailCliente',
            'c.telefono            AS telefonoCliente',
            'tv.nombre             AS tipoVenta',
            'v.credito             AS credito',
            'e.nombre              AS empresa',
            'suc.nombre            AS sucursal',
            'c.numeroempleado      AS numeroEmpleado',
            'ec.nombre             AS nombreEmpresaCliente',
            'u.nombre              AS nombreUnidad',
            'sr.name               AS nombreReferencia',
            'sv.precio as precioVenta',
            'sv.preciofinal as precioFinal',
            'sv.porcentajedescuento as descuento',
            'v.products            AS ProductosVenta',
            'v.cotizacion          AS cotizacion',
            'v.liquidada           AS liquidada',
            'v.pagado               AS total',
            '(v.pagado - v.iva)    AS subtotal',
            'v.iva                 AS iva',
            'v.porcentajeiva       AS porcentajeIva',
            's.codigobarras        AS codigobarras',
            'v.deuda               AS deuda',
            'v.fechaventa          AS fechaVenta',
            'v.fecha               AS fechaCreacion',
            'sv.cantidad as cantidadVendida',
            'ol.esferaod as esferaDerecha',
            'ol.cilindrood as cilindroDerecho',
            'ol.ejeod as ejeDerecho',
            'ol.esferaoi as esferaIzquierda',
            'ol.cilindrooi as cilindroIzquierdo',
            'ol.ejeoi as ejeIzquierdo',
            'ol.dip as distanciaPupilar',
            'ol.ao as alturaOjos',
            'ol.status as statusOrden',
            'ol.diagnosis As Diagnostico',
            'ol.notes AS Notas',
            'ol.aco AS ACO',
            'ol.avcercacaddoi AS AVCercaAddOI',
            'ol.avcercacaddod AS AVCercaAddOD',
        ])
       // ->from('App\Entity\Venta', 'v')
        ->from('App\Entity\Stockventa','sv')
        ->innerJoin('sv.ventaIdventa', 'v')
        ->innerJoin('v.tipoventaIdtipoventa', 'tv')
        ->innerJoin('v.sucursalIdsucursal', 'suc')
        ->innerJoin('suc.empresaIdempresa', 'e')
        ->innerJoin('v.clienteIdcliente','c')
        ->innerJoin('c.empresaclienteIdempresacliente', 'ec')
        ->leftJoin('c.unidadIdunidad', 'u')
        ->leftjoin('c.sellreferenceIdsellreference', 'sr')
           // ->leftJoin('App\Entity\Stockventa', 'sv',  'WITH', 'sv.ventaIdventa = v')
            ->leftJoin('sv.stockIdstock','s')
            ->innerJoin('s.productoIdproducto', 'p')
            ->innerjoin('App\Entity\Stockventaordenlaboratorio','svol','WITH','svol.stockventaIdstockventa = sv')
            ->leftJoin('svol.ordenlaboratorioIdordenlaboratorio','ol')
        ->where('v.status = :status')
        ->setParameter('status', '1')
            ->orderBy('v.fechaventa', 'DESC')
            ->setFirstResult($offset)
            ->setMaxResults($limit);

        // Aplicar filtros si están presentes
        if ($fechaVentaDesde) {
            $qb->andWhere('v.fechaventa >= :fechaVentaDesde')
               ->setParameter('fechaVentaDesde', new \DateTime($fechaVentaDesde));
        }

        if ($fechaVentaHasta) {
            $qb->andWhere('v.fechaventa <= :fechaVentaHasta')
               ->setParameter('fechaVentaHasta', new \DateTime($fechaVentaHasta . ' 23:59:59'));
        }

        if ($fechaCreacionDesde) {
            $qb->andWhere('v.fecha >= :fechaCreacionDesde')
               ->setParameter('fechaCreacionDesde', new \DateTime($fechaCreacionDesde));
        }

        if ($fechaCreacionHasta) {
            $qb->andWhere('v.fecha <= :fechaCreacionHasta')
               ->setParameter('fechaCreacionHasta', new \DateTime($fechaCreacionHasta . ' 23:59:59'));
        }

        if ($cotizacion !== null && $cotizacion !== '') {
            $qb->andWhere('v.cotizacion = :cotizacion')
               ->setParameter('cotizacion', $cotizacion);
        }

        if ($folio) {
            $qb->andWhere('v.folio LIKE :folio')
               ->setParameter('folio', '%' . $folio . '%');
        }

        if ($sucursal) {
            $qb->andWhere('suc.idsucursal = :sucursal AND suc.status = 1')
               ->setParameter('sucursal', $sucursal);
        }

        if ($nombreCliente) {
            $qb->andWhere('(c.nombre LIKE :nombreCliente OR c.apellidopaterno LIKE :nombreCliente OR c.apellidomaterno LIKE :nombreCliente)')
               ->setParameter('nombreCliente', '%' . $nombreCliente . '%');
        }

        if ($emailCliente) {
            $qb->andWhere('c.email LIKE :emailCliente')
               ->setParameter('emailCliente', '%' . $emailCliente . '%');
        }

        if ($telefonoCliente) {
            $qb->andWhere('c.telefono LIKE :telefonoCliente')
               ->setParameter('telefonoCliente', '%' . $telefonoCliente . '%');
        }

        if ($liquidada !== null && $liquidada !== '') {
            $qb->andWhere('v.liquidada = :liquidada')
               ->setParameter('liquidada', $liquidada);
        }

        // Ejecutar la consulta principal para obtener ventas
        $ventas = $qb->getQuery()->getResult();

        // Si necesitamos información detallada de productos y órdenes de laboratorio, 
        // hacemos una consulta separada para cada venta
        if (!empty($ventas)) {
            $ventaIds = array_map(function($venta) {
                return $venta['idventa'];
            }, $ventas);

            // Consulta para obtener información de productos y órdenes de laboratorio
            $qbDetails = $em->createQueryBuilder();
            $qbDetails->select('
                v.idventa,
                s.codigobarras AS codigobarras,
                p.modelo as modeloProducto,
                p.descripcion as descripcionProducto,
                sv.cantidad as cantidadVendida,
                sv.precio as precioVenta,
                sv.preciofinal as precioFinal,
                sv.porcentajedescuento as descuento,
                ol.esferaod as esferaDerecha,
                ol.cilindrood as cilindroDerecho,
                ol.ejeod as ejeDerecho,
                ol.esferaoi as esferaIzquierda,
                ol.cilindrooi as cilindroIzquierdo,
                ol.ejeoi as ejeIzquierdo,
                ol.dip as distanciaPupilar,
                ol.ao as alturaOjos,
                ol.status as statusOrden,
                ol.diagnosis As Diagnostico,
                ol.notes AS Notas,
                ol.fechalaboratorio as fechaLaboratorio
            ')
                ->from('App\Entity\Stockventa','sv')
                ->innerJoin('sv.ventaIdventa', 'v')
                ->innerJoin('v.tipoventaIdtipoventa', 'tv')
                ->innerJoin('v.sucursalIdsucursal', 'suc')
                ->innerJoin('suc.empresaIdempresa', 'e')
                ->innerJoin('v.clienteIdcliente','c')
                ->innerJoin('c.empresaclienteIdempresacliente', 'ec')
                ->leftJoin('c.unidadIdunidad', 'u')
                ->leftjoin('c.sellreferenceIdsellreference', 'sr')
                // ->leftJoin('App\Entity\Stockventa', 'sv',  'WITH', 'sv.ventaIdventa = v')
                ->leftJoin('sv.stockIdstock',       's')
                ->innerJoin('s.productoIdproducto', 'p')
                ->innerjoin('App\Entity\Stockventaordenlaboratorio','svol','WITH','svol.stockventaIdstockventa = sv')
                ->leftJoin('svol.ordenlaboratorioIdordenlaboratorio','ol')
            ->where('v.idventa IN (:ventaIds)')
            ->setParameter('ventaIds', $ventaIds);

            $detalles = $qbDetails->getQuery()->getResult();

            // Agrupar detalles por venta
            $detallesPorVenta = [];
            foreach ($detalles as $detalle) {
                $ventaId = $detalle['idventa'];
                if (!isset($detallesPorVenta[$ventaId])) {
                    $detallesPorVenta[$ventaId] = [];
                }
                $detallesPorVenta[$ventaId][] = $detalle;
            }

            // Añadir detalles a cada venta
            foreach ($ventas as &$venta) {
                $ventaId = $venta['idventa'];
                $venta['detalles'] = $detallesPorVenta[$ventaId] ?? [];
            }
        }

        // Obtener el total de registros para la paginación
        $qbCount = $em->createQueryBuilder();
        $qbCount->select('COUNT(v.idventa)')
                ->from('App\Entity\Venta', 'v')
                ->where('v.status = :status')
                ->setParameter('status', '1');

        // Aplicar los mismos filtros a la consulta de conteo
        if ($fechaVentaDesde) {
            $qbCount->andWhere('v.fechaventa >= :fechaVentaDesde')
                   ->setParameter('fechaVentaDesde', new \DateTime($fechaVentaDesde));
        }

        if ($fechaVentaHasta) {
            $qbCount->andWhere('v.fechaventa <= :fechaVentaHasta')
                   ->setParameter('fechaVentaHasta', new \DateTime($fechaVentaHasta . ' 23:59:59'));
        }

        if ($fechaCreacionDesde) {
            $qbCount->andWhere('v.fecha >= :fechaCreacionDesde')
                   ->setParameter('fechaCreacionDesde', new \DateTime($fechaCreacionDesde));
        }

        if ($fechaCreacionHasta) {
            $qbCount->andWhere('v.fecha <= :fechaCreacionHasta')
                   ->setParameter('fechaCreacionHasta', new \DateTime($fechaCreacionHasta . ' 23:59:59'));
        }

        if ($cotizacion !== null && $cotizacion !== '') {
            $qbCount->andWhere('v.cotizacion = :cotizacion')
                   ->setParameter('cotizacion', $cotizacion);
        }

        if ($folio) {
            $qbCount->andWhere('v.folio LIKE :folio')
                   ->setParameter('folio', '%' . $folio . '%');
        }

        if ($sucursal) {
            $qbCount->innerJoin('v.sucursalIdsucursal', 'sucCount')
                   ->andWhere('sucCount.idsucursal = :sucursal AND sucCount.status = 1')
                   ->setParameter('sucursal', $sucursal);
        }

        if ($nombreCliente) {
            $qbCount->innerJoin('v.clienteIdcliente', 'cCount')
                   ->andWhere('(cCount.nombre LIKE :nombreCliente OR cCount.apellidopaterno LIKE :nombreCliente OR cCount.apellidomaterno LIKE :nombreCliente)')
                   ->setParameter('nombreCliente', '%' . $nombreCliente . '%');
        }

        if ($emailCliente) {
            if (!$nombreCliente) {
                $qbCount->innerJoin('v.clienteIdcliente', 'cCount');
            }
            $qbCount->andWhere('cCount.email LIKE :emailCliente')
                   ->setParameter('emailCliente', '%' . $emailCliente . '%');
        }

        if ($telefonoCliente) {
            if (!$nombreCliente && !$emailCliente) {
                $qbCount->innerJoin('v.clienteIdcliente', 'cCount');
            }
            $qbCount->andWhere('cCount.telefono LIKE :telefonoCliente')
                   ->setParameter('telefonoCliente', '%' . $telefonoCliente . '%');
        }

        if ($liquidada !== null && $liquidada !== '') {
            $qbCount->andWhere('v.liquidada = :liquidada')
                   ->setParameter('liquidada', $liquidada);
        }

        $totalRegistros = $qbCount->getQuery()->getSingleScalarResult();
        $totalPaginas = ceil($totalRegistros / $limit);

        return $this->json([
            'data' => $ventas,
            'pagination' => [
                'total' => $totalRegistros,
                'page' => $page,
                'limit' => $limit,
                'pages' => $totalPaginas
            ]
        ]);
    }

    /**
     * @Route("/admin/reporte/detalle-venta/{id}", name="reporte_detalle_venta")
     */
    public function detalleVenta(int $id): Response
    {
        $em = $this->getDoctrine()->getManager();
        $empresaid = 0;

        $stockventa = $em->getRepository(Stockventa::class)->find($id);

        if (!$stockventa) {
            throw $this->createNotFoundException("Stockventa con id $id no encontrada");
        }

        $venta = $stockventa->getVentaIdventa();

        $idventa = $venta->getIdventa();

        if (!$venta) {
            throw new NotFoundHttpException("Venta con id $idventa no encontrada");
        }

        $empresaid = null;
        if ($venta->getSucursalIdsucursal() && $venta->getSucursalIdsucursal()->getEmpresaIdempresa()) {
            $empresaid = $venta->getSucursalIdsucursal()->getEmpresaIdempresa()->getIdempresa();
        }

        $em = $this->getDoctrine()->getManager();

        $parameters = array('idventa' => $idventa);

        $query = $em->createQuery(
            'SELECT  sv.cantidad,sv.creacion,sv.modificacion,sv.porcentajedescuento,sv.status,
            sv.precio, sv.costo, p.modelo, p.descripcion, s.codigobarras, v.convenio, v.folio,
            uni.idunidad, uni.nombre as unidad, v.seapartoarmazon, sv.cantidad, v.porcentajeiva,
            sv.preciofinal, v.fechacancelacion, v.fechacreacion as fechaTicket
            FROM App\Entity\Stockventa sv
            inner join sv.stockIdstock s
            inner join s.productoIdproducto p
            inner join sv.ventaIdventa v
            left join v.unidadIdunidad uni 
            where v.idventa =:idventa 
            '
        )->setParameters($parameters);
        $Productos = $query->getResult();

        $query = $em->createQuery(
            'SELECT  p.fecha, p.monto, p.mesesintereses, pt.name as tipopago
            FROM App\Entity\Pago p
            INNER JOIN p.ventaIdventa v
            INNER JOIN p.paymenttypeIdpaymenttype pt
            WHERE v.idventa =:idventa and p.status=1'
        )->setParameters($parameters);
        $Pagos = $query->getResult();

        $query = $em->createQuery(
            'SELECT  m
                FROM App\Entity\Merma m
                INNER JOIN m.stockventaIdstockventa sv
                INNER JOIN sv.ventaIdventa v
               WHERE m.status=:status AND v.idventa =:saleId ORDER BY m.idmerma ASC'
        )->setParameters(['status' => '1', 'saleId' => $idventa]);

        $mermas = $query->getResult();

        return $this->render(
            'admin/detalle-venta.html.twig', [
                'venta' => $venta,
                'id' => $idventa,
                'productos' => $Productos,
                'pagos' => $Pagos,
                'listUrl' => $this->generateUrl('app_reporte_flujo_expediente'),
                'empresaid' => $empresaid, 'mermas' => $mermas]
        );
    }

    /**
     * @Route("/reporte/cancelar-venta/{id}", name="reporte_cancelar_venta")
     */
    public function cancelarVentaAction(int $id): Response
    {
        $em = $this->getDoctrine()->getManager();

        $venta = $em->getRepository(Venta::class)->find($id);

        if (!$venta) {
            throw $this->createNotFoundException("Venta con id $id no encontrada");
        }

        return $this->render(
            'admin/cancelar-venta.html.twig',
            [
                'idventa' => $id,
                'listUrl' => $this->generateUrl('app_reporte_flujo_expediente')
            ]
        );
    }

    /**
     * @Route("/obtener-sucursales-activas", name="obtener_sucursales_activas")
     */

    public function obtenerSucursalesActivas(Request $request): JsonResponse
    {
        $em = $this->getDoctrine()->getManager();
        $searchTerm = $request->query->get('q', '');


        $qb = $em->createQueryBuilder();
        $qb->select('s.idsucursal', 's.nombre')
           ->from('App:Sucursal', 's')
           ->where('s.status = :status')
           ->setParameter('status', 1);

        if ($searchTerm) {
            $qb->andWhere('s.nombre LIKE :searchTerm')
               ->setParameter('searchTerm', '%' . $searchTerm . '%');
        }

        $qb->orderBy('s.nombre', 'ASC');

        $sucursales = $qb->getQuery()->getResult();

        return new JsonResponse([
            'items' => $sucursales,
            'total_count' => count($sucursales)
        ]);
    }
}
