<?php

namespace App\Controller\AccessModule;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\RouterInterface;

use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Role\RoleHierarchyInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;

use App\Entity\Usuario;

/**
 * @Route("/admin/app/prod")
 */

class UsrPermissionsController extends AbstractController
{

    /**
     * @Route("/config-entry-points", name="config_entry_points")
     */
    public function indexEntryPoints(): Response
    {
        $em = $this->getDoctrine()->getManager();

        $queryBuilder = $em->createQueryBuilder();
        $queryBuilder
            ->select('usr.idusuario, CONCAT (usr.nombre, \' \', usr.apellidopaterno, \' \', usr.apellidomaterno) as name')
            ->from('App\Entity\Usuario', 'usr')
            ->Where('usr.status = 1');

        // Execute the query
        $result = $queryBuilder->getQuery()->getResult();

        return $this->render('usr_permissions/index.html.twig', [
            'usrsresult' => $result,
        ]);
    }

    /**
     * @Route("/display-entry-points", name="display_entry_points")
     */
    public function displayEntryPoints(UrlGeneratorInterface $routerInterface, Request $request): Response
    {
        $User = $this->getUser();
        $usrID = $request->get("usrID");
        $em = $this->getDoctrine()->getManager();

        if ($usrID) {
            $User = $em->getRepository(Usuario::class)->findOneBy(array('idusuario' => $usrID));
        }

        $usrID = $User->getIdusuario();

        if(!$usrID)
        {
            return new RedirectResponse($routerInterface->generate('app_dashboard_flujo_expediente'));
        }

        $results = [];

        $exsistinginusr = $User->getEntrypoints() ?? [];
        $entries = $routerInterface->getRouteCollection()->all();

        foreach ($entries as $routeName => $route) {

            $parts = explode("_", $routeName);

            if($parts[0] != 'r' || count($parts) < 1) continue;

            $results[$parts[1]][] = [$routeName, !in_array($routeName, $exsistinginusr)];

        }


        return $this->render('usr_permissions/entrypoints.html.twig', [
            'roles' => $results,
            'usrID' => $usrID,
        ]);
    }

    /**
     * @Route("/update-entry-points", name="update_entry_points")
     */
    public function updateEntryPoints(Request $request): Response
    {
        $entrypoints = $request->get("checkedValues");
        $usrID = $request->get("usrID");

        $em = $this->getDoctrine()->getManager();
        $User = $em->getRepository(Usuario::class)->findOneBy(array('idusuario' => $usrID));

        $User->setEntrypoints($entrypoints);

        $em->persist($User);
        $em->flush();

        return $this->json(["msj" => "actualizado", "exito" => true]);
    }
}
