<?php
// src/Controller/LuckyController.php
namespace App\Controller;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;

class PaginaController  extends AbstractController {
    #[Route('/', name: 'index')]
    public function index(): Response
    {
        $index = random_int(0, 100);

        return $this->render('Pagina/index.html.twig', [
            
        ]);
    }

    #[Route('/optimo' , name: 'optimo')]
    public function optimo(): Response
    {
        $optimo = random_int(0, 100);

        return $this->render('Pagina/optimo.html.twig', [
            
        ]);
    }

    #[Route('/abathon', name: 'abathon')]
    public function abathon(): Response
    {
        $abathon = random_int(0, 100);

        return $this->render('Pagina/abathon.html.twig', [
            
        ]);
    }

    #[Route('/orthopedie', name: 'orthopedie')]
    public function orthopedie(): Response
    {
        $orthopedie = random_int(0, 100);

        return $this->render('Pagina/orthopedie.html.twig', [
            
        ]);
    }

    #[Route('/burgergarden', name: 'burgergarden')]
    public function burgergarden(): Response
    {
        $burgergarden = random_int(0, 100);

        return $this->render('Pagina/burgergarden.html.twig', [
            
        ]);
    }

    #[Route('/contacto', name: 'contacto')]
    public function contacto(): Response
    {
        $contacto = random_int(0, 100);

        return $this->render('Pagina/contacto.html.twig', [
            
        ]);
    }

    #[Route('/política', name: 'política')]
    public function política(): Response
    {
        $política = random_int(0, 100);

        return $this->render('Pagina/política.html.twig', [
            
        ]);
    }

    #[Route('/mail', name: 'mail')]
    public function enviarmail(MailerInterface $mailer): Response
    {

        $exito = false;
        $msj = "";


        try {
            $email = (new Email())
                ->from('<EMAIL>')
                ->to('<EMAIL>')
                //->cc('<EMAIL>')
                //->bcc('<EMAIL>')
                //->replyTo('<EMAIL>')
                //->priority(Email::PRIORITY_HIGH)
                ->subject('Prueba')
                ->text('Esto es una prueba')
                ->html('<p>See Twig integration for better HTML integration!</p>');
            $respuesta = $mailer->send($email);
            //    var_dump($respuesta);
            // echo "Enviado exitosamente";

          //  return $this->redirectToRoute('index');

            // echo "llga aqui";

            $exito=true;
        } catch (TransportExceptionInterface $e) {
            //  var_dump($e);
            $msj .= $e->getMessage() . " linea " . $e->getLine() . " archivo " . $e->getFile();
        }

        return new JsonResponse(['msj' => $msj, 'exito' => $exito]);
    }

}
