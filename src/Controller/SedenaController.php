<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

use App\Entity\Cliente;
use App\Entity\Empresacliente;
use App\Form\SedenaType;


use DateTime;

class SedenaController extends AbstractController
{
    /**
     * @Route("/registro-sedena", name="sedena-registro-sedena")
     */
    public function index(Request $request): Response
    {


        return $this->render('sedena_form/registro-sedena.html.twig', []);
    }

    /**
     * @Route("/get-sedena-form", name="sedena-get-sedena-form")
     */
    public function getSedenaForm(Request $request): Response
    {
        $success=false;
        $msg = '';
        $idNumber = $request->get("idNumber");
        $beneficiaryType = $request->get("beneficiaryType");

        $em = $this->getDoctrine()->getManager();

        $Client = new Cliente();

        $clientId = '';

        if ($idNumber){

            $query = $em->createQuery(
                'SELECT c
                   FROM App\Entity\Cliente c
                   WHERE c.status =:status AND c.numeroempleado =:idNumber AND c.beneficiarytype is NULL
                   '
            )->setParameters(['status'=>1,'idNumber'=>$idNumber]);
            $checkHOLDER= $query->setMaxResults(1)->getResult();
            if (isset($checkHOLDER[0]) && $beneficiaryType == "HOLDER") $Client = $checkHOLDER[0];
        }


        $form = $this->createForm(SedenaType::class,$Client);

        $form->handleRequest($request);

        try {
            if($form->isSubmitted() && $form->isvalid() ){

                $Client = $form->getData();

                if ($Client->getBeneficiarytype() == "HOLDER") $Client->setBeneficiarytype(NULL);
                else {
                    if (isset($checkHOLDER[0])) {
                        $Client->setHolder($checkHOLDER[0]);
                        $Client->setNumeroempleado(NULL);
                    }
                }
                
                $ClientEnterprise = $em->getRepository(Empresacliente::class)->findOneBy(array('nombre' => "SEDENA"));

                if ($ClientEnterprise) $Client->setEmpresaclienteIdempresacliente($ClientEnterprise);

                $em->persist($Client);

                $em->flush();

                $clientId = $Client->getIdcliente();

                $success = true;

            }

        }catch (\Exception $e) {
            
            $msg.=$e->getMessage();
        }

        return $this->render('sedena_form/get-sedena-form.html.twig', [
            'form' => $form->createView(),
            'success'=>$success,
            'msg'=>$msg,
            'Cliente' => $Client,
            "clientId" => $clientId
        ]);
    }

}
