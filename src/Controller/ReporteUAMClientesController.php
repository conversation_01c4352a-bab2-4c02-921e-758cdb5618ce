<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class ReporteUAMClientesController extends AbstractController
{
    /**
     * @Route("/reporte/tableClientesUam", name="reporte-uam-table")
     */
    public function index(): Response
    {
        $em = $this->getDoctrine()->getManager();
        $clientesUam = [];
        $clientes = "";
        $tventa = 1772;
        $conteoBeneficiariosPorCliente = [];

        $query = $em->createQuery(
            'SELECT c.nombre AS NombreC, c.apellidopaterno, c.apellidomaterno, u.nombre AS Unidad, c.telefono AS Telefono, c.email AS Email,
                    COUNT(v.idventa) AS NumeroCompras, tv.idtipoventa, tv.nombre, c.idcliente AS IdCliente
            FROM App\Entity\Venta v
            INNER JOIN v.tipoventaIdtipoventa tv
            INNER JOIN v.clienteIdcliente c
            INNER JOIN c.unidadIdunidad u
            WHERE v.status = :status
            AND tv.nombre LIKE :tipoVentaNombre
            GROUP BY c.idcliente'
        )->setParameters([
            'status' => '1',
            'tipoVentaNombre' => '%UAM%'
        ]);
        $clientes = $query->getResult();
        

        foreach ($clientes as $cliente) {
            $nombreCompleto = $cliente['NombreC'] . ' ' . $cliente['apellidopaterno'] . ' ' . $cliente['apellidomaterno'];
            
            $clientesUam[] = [
                'IdCliente' => $cliente['IdCliente'],
                'Titular' => $nombreCompleto,
                'Unidad' => $cliente['Unidad'],
                'Telefono' => $cliente['Telefono'],
                'Email' => $cliente['Email'],
                'NumeroCompras' => $cliente['NumeroCompras'],
                'IdTipoVenta' => $cliente['idtipoventa'],
                'NombreTipoVenta' => $cliente['nombre'],
                'NumeroBeneficiarios' => 0,
                'Ventas2023' => 0,
                'Ventas2024' => 0,
                'FechaUltimaCompra2023' => null,
                'FechaUltimaCompra2024' => null,
            ];
        }
        

        $idsBenefactores = array_map(function ($cliente) {
            return $cliente['IdCliente'];
        }, $clientesUam);

        $idsBenefactoresString = implode(',', $idsBenefactores);

        $query = $em->createQuery(
            'SELECT c
            FROM App\Entity\Cliente c
            WHERE c.holder IN (:idsBenefactores)'
        )->setParameter('idsBenefactores', $idsBenefactores);

        $beneficiarios = $query->getResult();



        $conteoBeneficiariosPorCliente = [];
        foreach ($beneficiarios as $beneficiario) {
            $holder = $beneficiario->getHolder();
            if ($holder !== null) {
                $idHolder = $holder->getIdcliente();

                if (!isset($conteoBeneficiariosPorCliente[$idHolder])) {
                    $conteoBeneficiariosPorCliente[$idHolder] = 0;
                }
                $conteoBeneficiariosPorCliente[$idHolder]++;
            }
        }


        foreach ($clientesUam as &$clienteUam) {
            $clienteUam['NumeroBeneficiarios'] = $conteoBeneficiariosPorCliente[$clienteUam['IdCliente']] ?? 0;
        }

        $query = $em->createQuery(
            'SELECT c.idcliente AS IdCliente, 
                    SUM(CASE WHEN YEAR(v.fechaventa) = 2023 THEN 1 ELSE 0 END) AS Ventas2023, 
                    SUM(CASE WHEN YEAR(v.fechaventa) = 2024 THEN 1 ELSE 0 END) AS Ventas2024
            FROM App\Entity\Venta v
            JOIN v.clienteIdcliente c
            WHERE c.idcliente IN (:idsClientes)
            AND YEAR(v.fechaventa) IN (2023, 2024)
            GROUP BY c.idcliente'
        )->setParameter('idsClientes', $idsBenefactores);

        $ventasPorClienteYAnio = $query->getResult();

        
        foreach ($clientesUam as &$cliente) {
            foreach ($ventasPorClienteYAnio as $venta) {
                if ($venta['IdCliente'] == $cliente['IdCliente']) {
                    $cliente['Ventas2023'] = $venta['Ventas2023'];
                    $cliente['Ventas2024'] = $venta['Ventas2024'];
                    break; 
                }
            }
        }

        $query = $em->createQuery(
            'SELECT c.idcliente AS IdCliente, MAX(v.fechaventa) AS UltimaCompra
             FROM App\Entity\Venta v
             JOIN v.clienteIdcliente c
             WHERE c.idcliente IN (:idsClientes)
             GROUP BY c.idcliente'
        )->setParameter('idsClientes', $idsBenefactores);

        $ultimasCompras = $query->getResult();

        
        foreach ($clientesUam as &$clienteUam) {
            foreach ($ultimasCompras as $compra) {
                if ($compra['IdCliente'] == $clienteUam['IdCliente']) {
                    $clienteUam['UltimaCompra'] = $compra['UltimaCompra'];
                    break;
                }
            }
        }

        foreach ($clientesUam as &$clienteUam) {
            $fechaUltimaCompra = new \DateTime($clienteUam['UltimaCompra']);
            $anio = $fechaUltimaCompra->format('Y');
            if ($anio == '2023') {
                $clienteUam['FechaUltimaCompra2023'] = $clienteUam['UltimaCompra'];
            } elseif ($anio == '2024') {
                $clienteUam['FechaUltimaCompra2024'] = $clienteUam['UltimaCompra'];
            }
            
            unset($clienteUam['UltimaCompra']);
        }


        return $this->render('reporte_uam_clientes/index.html.twig', [
            'clientes' => $clientesUam, 
            'beneficiarios' => $beneficiarios,
        ]);
    }
}
