<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Psr\Log\LoggerInterface;


class ComisionesController extends AbstractController
{

    private $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    /**
     * @Route("/reporte/comisiones", name="app_comisiones")
     */
    public function index(): Response
    {
        $em = $this->getDoctrine()->getManager();
        $Usuario = $this->getUser();

        // Primera consulta para obtener empresas del usuario
        $queryEmpresas = $em->createQuery(
            'SELECT e.idempresa, e.nombre
        FROM App\Entity\Usuarioempresapermiso uem
        INNER JOIN uem.empresaIdempresa e
        INNER JOIN uem.usuarioIdusuario u
        WHERE e.status = :status AND u.idusuario = :idusuario
        ORDER BY e.nombre ASC'
        )->setParameters(['status' => "1", 'idusuario' => $Usuario->getIdUsuario()]);

        $empresas = $queryEmpresas->getResult();

        $query = $em->createQuery(
            'SELECT pt.name,pt.idpaymenttype
         FROM App\Entity\Paymenttype pt
         WHERE pt.status = :status'
        )->setParameters([
            'status' => "1",
        ]);
        $tipopago = $query->getResult();

        $query = $em -> createQuery(
            'SELECT tv.nombre AS tipoVenta, tv.idtipoventa
            FROM App\Entity\Tipoventa tv
            WHERE tv.status = :status
            ')->setParameters(["status" => "1"]);

        $tipoventa = $query->getResult();

        return $this->render('comisiones/index.html.twig', [
            'tipoVenta' =>$tipoventa,
            'empresas' => $empresas,
            'tipopago' => $tipopago
        ]);
    }

    /**
     * @Route("/get-sucursales/{empresaId}", name="get_sucursales", requirements={"empresaId"="\d+"})
     */
    public function getSucursales(int $empresaId): Response
    {
        $em = $this->getDoctrine()->getManager();

        $querySucursales = $em->createQuery(
            'SELECT su.idsucursal, su.nombre
         FROM App\Entity\Sucursal su
         WHERE su.empresaIdempresa = :empresaId AND su.status = :status'
        )->setParameters([
            'empresaId' => $empresaId,
            'status' => '1'
        ]);

        $sucursales = $querySucursales->getResult();

        return $this->json($sucursales);
    }

    /**
     * @Route("/get-vendedores/{sucursalId}", name="get_vendedores", requirements={"sucursalId"="\d+"})
     */
    public function getVendedores(int $sucursalId): Response
    {
        $em = $this->getDoctrine()->getManager();

        $queryVendedores = $em->createQuery(
            'SELECT u.idusuario, u.nombre, u.apellidopaterno, u.apellidomaterno
         FROM App\Entity\Usuario u
         INNER JOIN u.sucursalIdsucursal su
         WHERE su.idsucursal = :sucursalId AND u.status = :status'
        )->setParameters([
            'sucursalId' => $sucursalId,
            'status' => '1'
        ]);

        $vendedores = $queryVendedores->getResult();

        return $this->json($vendedores);
    }

    /**
     * @Route("/tabla", name="tablaComisiones", methods={"POST"})
     */
    public function tablaComisiones(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $stages = [
            0 => "En Sucursal",
            1 => "Por recibir en Corporativo.",
            2 => "En Corporativo",
            3 => "En Proceso a Facturar",
            4 => "Pendiente de Pago"
        ];

        $sucursal = $request->request->get('sucursal');
        $vendedor = $request->request->get('vendedor');
        $ventaLiquidada = $request->request->get('ventaLiquidada');
        $tipoPago = $request->request->get('tipoPago');
        $fechaInicioObj = \DateTime::createFromFormat('d/m/Y', $request->get("fechaInicio"));
        $fechaFinObj = \DateTime::createFromFormat('d/m/Y', $request->get("fechaFin"));
        $tipoVenta = $request->request->get('tipoVenta');

        if (!$fechaInicioObj || !$fechaFinObj) {
            $fechaInicioObj = new \DateTime("now");
            $fechaFinObj = new \DateTime("now");
        }

        // Construir la consulta base
        $dql = 'SELECT v.folio, su.nombre AS sucursal, u.nombre AS vendedor, u.apellidopaterno, u.apellidomaterno, p.mesesintereses, tv.nombre AS tipoVenta,
        tv.idtipoventa, v.liquidada, v.fechaventa, v.fechaactualizacion AS fechaLiquidacion, COUNT(p.idpago) AS numeroPagos,
        SUM(p.monto) AS monto, pt.name AS TipodePago, v.authorizationnumber, v.autorizacionstate, MAX(p.fecha) as fechaUltimoPago
        FROM App\Entity\Pago p
        INNER JOIN p.ventaIdventa v
        INNER JOIN v.tipoventaIdtipoventa tv
        INNER JOIN v.sucursalIdsucursal su
        INNER JOIN v.usuarioIdusuario u
        INNER JOIN p.paymenttypeIdpaymenttype pt
        WHERE p.status = :status';

        $parameters = ['status' => '1'];

        if ($sucursal != '-1' && $sucursal != '') {
            $dql .= ' AND su.idsucursal = :sucursal';
            $parameters['sucursal'] = $sucursal;
        }

        if ($vendedor != '-1' && $vendedor != '') {
            $dql .= ' AND u.idusuario = :vendedor';
            $parameters['vendedor'] = $vendedor;
        }

        if ($ventaLiquidada != '-1') {
            $dql .= ' AND v.liquidada = :ventaLiquidada';
            $parameters['ventaLiquidada'] = $ventaLiquidada;
        }

        /*if ($tipoPago != '-1') {
            $dql .= ' AND pt.idpaymenttype = :tipoPago';
            $parameters['tipoPago'] = $tipoPago;
        }*/

        if ($tipoVenta != '-1') {
            $dql .= ' AND tv.idtipoventa = :tipoVenta';
            $parameters['tipoVenta'] = $tipoVenta;
        }

        if ($fechaInicioObj && $fechaFinObj) {
            $dql .= ' AND v.fechaventa BETWEEN :fechaInicio AND :fechaFin ';
            $parameters['fechaInicio'] = $fechaInicioObj->format('Y-m-d');
            $parameters['fechaFin'] = $fechaFinObj->format('Y-m-d');
        }

        $dql .= ' GROUP BY v.idventa';

        $query = $em->createQuery($dql)->setParameters($parameters);

        $pagoResults = $query->getResult();
        $ventas = [];
        $tiposDePago = [];

        foreach ($pagoResults as $pago) {
            $folio = $pago['folio'];
            if (!isset($ventas[$folio])) {
                $ventas[$folio] = [
                    'folio' => $pago['folio'],
                    'sucursal' => $pago['sucursal'],
                    'vendedor' => $pago['vendedor'],
                    'apellidopaterno' => $pago['apellidopaterno'],
                    'apellidomaterno' => $pago['apellidomaterno'],
                    'liquidada' => $pago['liquidada'],
                    'tipoVenta' => $pago['tipoVenta'],
                    'fechaLiquidacion' => new \DateTime($pago['fechaUltimoPago']),
                    'authorizationNumber' => $pago['authorizationnumber'] ?? 'N/A', // Asignar "N/A" si no hay valor
                    'autorizacionState' => $pago['autorizacionstate'] ?? 'N/A',    // Asignar "N/A" si no hay valor
                    'pagos' => [],
                    'montoTotal' => 0,
                    'numeroPagos' => 0,
                    'adeudo' => 0
                ];
            }
            $tipoPago = $pago['TipodePago'];
            if (!isset($ventas[$folio]['pagos'][$tipoPago])) {
                $ventas[$folio]['pagos'][$tipoPago] = [
                    'montoTotal' => 0.0,
                    'numeroVentas' => 0,
                    'mesesintereses' => $pago['mesesintereses'],
                ];

            }
            $ventas[$folio]['pagos'][$tipoPago]['montoTotal'] += $pago['monto'];
            $ventas[$folio]['pagos'][$tipoPago]['numeroVentas'] += 1;
            $ventas[$folio]['montoTotal'] += $pago['monto'];
            $ventas[$folio]['numeroPagos'] += $pago['numeroPagos'];
            $ventas[$folio]['adeudo'] = $ventas[$folio]['montoTotal'] - $pago['monto'];
            if (!in_array($tipoPago, $tiposDePago)) {
                $tiposDePago[] = $tipoPago;
            }
        }

        // Calcular comisiones y preparar datos para el gráfico
        $comisionesPorMes = [];
        $totalComisiones = 0;
        foreach ($ventas as &$venta) {
            $venta['comision'] = $venta['montoTotal'] * 0.042; // Comisión del 4.2% sobre el monto incluyendo IVA
            $totalComisiones += $venta['comision'];

            if ($venta['fechaLiquidacion'] !== null) {
                $mes = $venta['fechaLiquidacion']->format('m-Y'); // Agrupar por mes y año
                if (!isset($comisionesPorMes[$mes])) {
                    $comisionesPorMes[$mes] = 0;
                }
                $comisionesPorMes[$mes] += $venta['comision'];
            }
        }

        // Convertir datos de comisiones por mes en formato adecuado para el gráfico
        ksort($comisionesPorMes); // Ordenar por mes
        $labels = array_keys($comisionesPorMes);
        $data = array_values($comisionesPorMes);

        return $this->render('comisiones/tablaComisiones.html.twig', [
            'ventas' => $ventas,
            'labels' => json_encode($labels),
            'data' => json_encode($data),
            'totalComisiones' => $totalComisiones,
            'tiposDePago' => $tiposDePago
        ]);
    }
}