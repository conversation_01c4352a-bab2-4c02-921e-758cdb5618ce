<?php

namespace App\Controller;

use App\Service\FileUploader;
use App\Form\ClientefacturadatosType;
use App\Entity\Clientefacturadatos;
use App\Entity\Cliente;
use App\Entity\Venta;
use App\Entity\Ventafactura;
use App\Entity\Empresa;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\Mime\Address;
use App\Service\MailService;


class FacturacionController extends AbstractController
{
    private $mailer;
    private $mailService;

    public function __construct(MailerInterface $mailer, MailService $mailService)
    {
        $this->mailer = $mailer;
        $this->mailService = $mailService;
    }



    public function index(Request $request, FileUploader $fileUploader): Response
    {
        return $this->render('facturacion/index.html.twig', [
        ]);

    }


    public function facturacionEmpresa(Request $request, FileUploader $fileUploader, $e): Response
    {
        $em = $this->getDoctrine()->getManager();
        $msj = "";
        $exito = false;
        $logo64 = "";
        $nombre = "";

        try {

            if (!$e) throw new \Exception('Ruta inválida');
            else {
                $Empresa = $em->getRepository(Empresa::class)->findOneBy(array('prefijotickets' => $e));
                if ($Empresa) {
                    $logo64 = $Empresa->getLogo64();
                    $nombre = $Empresa->getNombre();

                } else throw new \Exception('Ruta inválida');
            }

        } catch (\Exception $e) {
            $msj .= $e->getMessage();
        }

        return $this->render('facturacion/index2.html.twig', [
            'msj' => $msj,
            'exito' => $exito,
            'logo64' => $logo64,
            'nombre' => $nombre,
            'prefijo' => $e,

        ]);

    }



    public function formularioEmpresa(Request $request, FileUploader $fileUploader, $e = ""): Response
    {
        $exito = false;
        $msj = "";
        $em = $this->getDoctrine()->getManager();
        $Clientefacturadatos = new Clientefacturadatos();
        $Cliente = new Cliente();
        $Venta = new Venta();
        $formularioGuardado = false;

        //   $e = $request->get('e');

        $form = $this->createForm(ClientefacturadatosType::class, $Clientefacturadatos);
        $form->handleRequest($request);


        try {
            if ($form->isSubmitted() && $form->isvalid()) {


                $folio = $form->get('folio')->getData();
                //var_dump($folio);

                $query = $em->createQuery(
                    'SELECT v
                FROM App\Entity\Venta v
                
                inner join  v.sucursalIdsucursal s
                inner join  s.empresaIdempresa e
            
                where v.status=:status and v.folio=:folio and e.prefijotickets =:e
                '
                )->setParameters(['status' => '1', 'folio' => $folio, 'e' => $e]);


                $Venta = $query->getOneOrNullResult();

                //$Venta = $em->getRepository(Venta::class)->findOneBy(array('folio' => $folio));
                //var_dump($Venta->getIdventa());
                if ($Venta) {

                    $CheckVenta = $em->getRepository(Ventafactura::class)->findOneBy(array('ventaIdventa' => $Venta->getIdventa()));

                    if ($CheckVenta) throw new \Exception('Ya se solicitó la factura para este folio');

                    $totalPagado = 0;

                    $query = $em->createQuery(
                        'SELECT p
                    FROM App\Entity\Pago p
                    
                    inner join  p.ventaIdventa v
                
                    where p.status=:status and v.idventa=:idventa
                    '
                    )->setParameters(['status' => '1', 'idventa' => $Venta->getIdventa()]);


                    $pagos = $query->getResult();

                    foreach ($pagos as $key => $Pago) {
                        $totalPagado += $Pago->getMonto();
                    }

                    if ($totalPagado >= $Venta->getPagado()) {

                        $Cliente = $Venta->getClienteIdcliente();

                        $Clientefacturadatos = $form->getData();

                        $Clientefacturadatos->setClienteIdcliente($Cliente);

                        $Clientefacturadatos->setCreacion(new \DateTime("now"));

                        $Clientefacturadatos->setModificacion(new \DateTime("now"));

                        $brochureFile = $form->get('brochure')->getData();

                        if ($brochureFile) {
                            $brochureFileName = $fileUploader->upload($brochureFile, "constancia");

                            $Clientefacturadatos->setConstanciasituacionfiscal($brochureFileName);

                        } else throw new \Exception('Debes seleccionar su constancia de situación fiscal');

                        $em->persist($Clientefacturadatos);


                        $em->flush();

                        $notas = $form->get('notas')->getData();

                        $Sucursal = $Venta->getSucursalIdsucursal();

                        $enviarCorreoData = [
                            'Clientefacturadatos' => $Clientefacturadatos,
                            'folio' => $folio,
                            'notas' => $notas,
                            'Sucursal' => $Sucursal,
                        ];

                        foreach ($enviarCorreoData as $key => $value) {
                            if ($value === null && $key !== 'notas') {
                                echo "Error: El valor para '{$key}' es nulo.";
                                die();
                            }
                        }


                        $Ventafactura = new Ventafactura();

                        $Ventafactura->setEstado("0");
                        $Ventafactura->setRfcreceptor($Clientefacturadatos->getRfc());
                        $Ventafactura->setRegimenfiscal($Clientefacturadatos->getRegimenfiscal());
                        $Ventafactura->setUsucfdi($Clientefacturadatos->getUsocfdi());
                        $Ventafactura->setVentaIdventa($Venta);
                        $Ventafactura->setClientefacturadatosIdclientefacturadatos($Clientefacturadatos);
                        $Ventafactura->setCreacion(new \DateTime("now"));
                        $em->persist($Ventafactura);
                        $em->flush();
                        if ($Ventafactura->getIdventafactura()) {

                            try {
                                $response = $this->enviarCorreo($Clientefacturadatos, $folio, $notas, $Sucursal);

                                $exito = true;
                                $formularioGuardado = true;
                            } catch (\Exception $e) {
                                echo "Error al enviar correo: " . $e->getMessage();
                                die();
                            }
                            throw new \Exception('No se pudo generar la petición');
                        }


                    } else $msj = "La venta no está liquidada";
                } else {
                    $msj = "El folio de venta no existe";
                }
            }

        } catch (\Exception $e) {
            $msj .= $e->getMessage();
        }

        //facturacion/formularioFacturacion.html.twig
        //facturacion/formulario.html.twig

        return $this->render('facturacion/formularioEmpresa.html.twig', [
            'form' => $form->createView(),
            'cliente' => $Cliente,
            'exito' => $exito,
            'msj' => $msj,
            'formularioGuardado' => $formularioGuardado,
            'prefijo' => $e

        ]);
    }


    public function PlantillaSelect(Request $request, $tipoPersona = "")
    {
        // $tipoPersona=$request->get("tipoPersona");


        if ($tipoPersona == "1" || $tipoPersona == "0") {

            $opcionesUsoCfdi[0] = ['Selecciona una opción' => '-----Selecciona una opción-----', 'Adquisición de mercancias' => "Adquisición de mercancias", "Gastos en general" => "Gastos en general",
                "Honorarios médicos, dentales y gastos" => "Honorarios médicos, dentales y gastos hospitalarios",
                "Gastos médicos por incapacidad o discapacidad" => "Gastos médicos por incapacidad o discapacidad"];

            $opcionesUsoCfdi[1] = ['Selecciona una opción' => '-----Selecciona una opción-----', 'Adquisición de mercancias' => "Adquisición de mercancias", "Devoluciones, descuentos o bonificaciones" => "Devoluciones, descuentos o bonificaciones",
                "Gastos en general" => "Gastos en general", "Construcciones" => "Construcciones", "Mobiliario y equipo de oficina por inversiones" => "Mobiliario y equipo de oficina por inversiones",
                "Equipo de transporte" => "Equipo de transporte", "Equipo de computo y accesorios" => "Equipo de computo y accesorios", "Dados,troqueles,moldes,matrices y herramientas" => "Dados,troqueles,moldes,matrices y herramientas",
                "Comunicaciones telefónicas" => "Comunicaciones telefónicas", "Comunicaciones satelitales" => "Comunicaciones satelitales", "Otra maquinaria y equipo" => "Otra maquinaria y equipo",
                "Sin efectos fiscales" => "Sin efectos fiscales", "Pagos" => "Pagos"];


            $regimenFiscal[0] = ['Selecciona una opción' => '-----Selecciona una opción-----', 'Sueldos y salarios e ingresos asimilados a salarios' => "Sueldos y salarios e ingresos asimilados a salarios", "Arrendamiento" => "Arrendamiento", "Régimen de enajenación o adquisición" => "Régimen de enajenación o adquisición",
                "Demás ingresos" => "Demás ingresos", "Residentes en el extranjero sin establecimiento permanente en méxico" => "Residentes en el extranjero sin establecimiento permanente en méxico", "Ingresos por Dividendos (socios y acciones)" => "Ingresos por Dividendos (socios y acciones)",
                "Personas físicas con actividades empresariales y profesionales" => "Personas físicas con actividades empresariales y profesionales", "Ingresos por intereses" => "Ingresos por intereses",
                "Régimen de los ingresos por obtención" => "Régimen de los ingresos por obtención", "Sin obligaciones fiscales" => "Sin obligaciones fiscales", "Incorporación Fiscal" => "Incorporación Fiscal",
                "Régimen de las Actividades Empresariales con ingresos a través de Plataformas Tecnológicas" => "Régimen de las Actividades Empresariales con ingresos a través de Plataformas Tecnológicas",
                "Régimen Simplicado de Confianza" => "Régimen Simplicado de Confianza"];

            $regimenFiscal[1] = ['Selecciona una opción' => '-----Selecciona una opción-----', 'General de Ley Personas Morales' => "General de Ley Personas Morales", "Personas morales con fines no lucrativos" => "Personas morales con fines no lucrativos",
                "Residentes en el extranjero sin establecimiento permanente en méxico" => "Residentes en el extranjero sin establecimiento permanente en México", "Sociedades Cooperativas de producción" => "Sociedades Cooperativas de producción",
                "Actividades agrícolas ganaderas silvícolas y pesqueras" => "Actividades agrícolas ganaderas silvícolas y pesqueras", "Opcional para Grupos de Sociedades" => "Opcional para Grupos de Sociedades",
                "Coordinados" => "Coordinados", "Régimen simplificado de Confianza" => "Régimen simplificado de Confianza"];

        } else {

            $tipoPersona = 2;

            $opcionesUsoCfdi[2] = [];

            $regimenFiscal[2] = [];


        }

        //$html = $this->render('PlantillaSelect.html.twig')->getContent();

        // return new Response($html);
        return $this->render('facturacion/PlantillaSelect.html.twig', [
            'opcionesUsoCfdi' => $opcionesUsoCfdi[$tipoPersona],
            'regimenFiscal' => $regimenFiscal[$tipoPersona]
        ]);
    }

    private function enviarCorreo($clienteFacturaDatos, $folio, $notas, $Sucursal)
    {
        $clienteRfc = $clienteFacturaDatos->getRfc();
        $archivo = $clienteFacturaDatos->getConstanciasituacionfiscal();
        $clienteEmail = $clienteFacturaDatos->getEmail();
        $Empresa = $Sucursal->getEmpresaIdempresa();
        $enterpriseEmail = $this->getParameter($Empresa->getPrefijotickets());
        $pattern = '/\/\/(.*?):/';
        $msj = '';
        $path = $this->getParameter('carpetaConstancias');


        $correoFacturacion = $Empresa->getEmailfacturacion();

        $asunto = 'La factura del cliente con el - rfc: ' . $clienteRfc . ' con el -correo: ' . $clienteEmail;

        $template = $this->renderView(
            'emails/peticionFactura.html.twig',
            [
                'logo' => $Empresa->getLogoimagen(),
                'folio' => $folio,
                'sucursal' => $Sucursal->getNombre(),
                'clienteFD' => $clienteFacturaDatos,
                'notas' => $notas
            ]
        );

        $emailData = [
            'recipient' => $correoFacturacion,
            'subject' => $asunto,
            'body' => $template,
            'attachment' => $archivo,
            'isHtml' => true,
            'isFactura' => true
        ];

        foreach ($emailData as $key => $value) {
            if ($value === null && $key !== 'notas') {
                echo "Error: El valor para '{$key}' es nulo.";
                die();
            }
        }

        try {

            $attachmentPath = $path . '/' . $archivo;
            $this->mailService->sendEmail($emailData, $attachmentPath);

        } catch (\Exception $e) {

            $msj = 'Error al enviar factura por correo: ' . $e->getMessage();
            var_dump($msj);
            die();
        }
    }

}
