<?php

namespace App\Controller\InventoryModule;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Doctrine\ORM\EntityManagerInterface;


use App\Entity\Cargainventariolog;
use App\Entity\Unidadmedida;
use App\Entity\Categoria;
use App\Entity\Proveedor;
use App\Entity\Producto;
use App\Entity\Sucursal;
use App\Entity\Empresa;
use App\Entity\Medida;
use App\Entity\Clase;
use App\Entity\Stock;
use App\Entity\Marca;
use App\Entity\Stockstate;

/**
 * @Route("/admin/dashboard")
 */
class CargaMasivaController extends AbstractController
{
    private $dictionary;
    private $allowedExtensions;
    private $uploadErrorMessages;

    public function __construct()
    {
        $this->dictionary = [
            'Carga-Inv' => [
                'folder_name' => 'Carga-Inv', 'template_route' => 'value3', 'entity' => Stock::class, 'entity_UK' => 'UPC',
                'cols' => [
                    'SKU'               => ['required' => false],
                    'CLAVE'             => ['required' => false],
                    'MODELO'            => ['required' => true, 'dbname' => 'modelo', 'type' => 'param'],
                    'UPC'               => ['required' => false],
                    'CANTIDAD'          => ['required' => true, 'dbname' => 'almacen', 'type' => 'num'],
                    'ALMACEN'           => ['required' => true, 'dbname' => 'almacen', 'type' => 'param'],
                    'SERIE'             => ['required' => false],
                    'NUMERO FACTURA'    => ['required' => false],
                    'COSTO'             => ['required' => false],
                    'PRECIO'            => ['required' => false]
                ],
            ],
            'Carga-Producto' => [
                'folder_name' => 'carga-prods', 'template_route' => 'value3', 'entity' => Producto::class, 'entity_UK' => 'codigobarrasuniversal',
                'cols' => [
                    'CLAVE'             => ['required' => false],
                    'MARCA'             => ['required' => false],
                    'MODELO'            => ['required' => true, 'dbname' => 'modelo', 'type' => 'param'],
                    'CODIGO DE COLOR'   => ['required' => false],
                    'COLOR'             => ['required' => false],
                    'UPC'               => ['required' => false],
                    'TIPO MATERIAL'     => ['required' => false],
                    'DESCRIPCION'       => ['required' => false],
                    'SOBREPUESTO'       => ['required' => false],
                    'MASIVO UNICO'      => ['required' => false],
                    'CATEGORIA'         => ['required' => true, 'dbname' => 'modelo', 'type' => 'param'],
                    'SUBCATEGORIA'      => ['required' => true, 'dbname' => 'modelo', 'type' => 'param'],
                    'UNIDAD DE MEDIDA'  => ['required' => false],
                    'MEDIDA'            => ['required' => true, 'dbname' => 'modelo',   'type' => 'param'],
                    'ESFERA'            => ['required' => false,],
                    'CILINDRO'          => ['required' => false,],
                    'EJE'               => ['required' => false,],
                    'ADICION'           => ['required' => false,],
                    'BASE'              => ['required' => false,],
                    'DISENO'            => ['required' => false,]
                ],

            ],
            'Carga-Costo' => [
                'folder_name' => 'Carga-Costo', 'template_route' => 'value3', 'entity' => Producto::class, 'entity_UK' => 'codigobarrasuniversal',
                'cols' => [
                    'CLAVE'                     => ['dbname' => 'clave', 'type' => 'param'],
                    'MARCA'                     => ['dbname' => 'modelo', 'type' => 'param'],
                    'MODELO'                    => ['dbname' => 'modelo', 'type' => 'param'],
                    'CODIGO DE COLOR'           => ['dbname' => 'modelo', 'type' => 'param'],
                    'COLOR'                     => ['dbname' => 'modelo', 'type' => 'param'],
                    'UPC'                       => ['dbname' => 'modelo', 'type' => 'param'],
                    'COSTO'                     => ['dbname' => 'modelo', 'type' => 'param'],
                    'PRECIO'                    => ['dbname' => 'modelo', 'type' => 'param'],
                    'PRECIO DISTRIBUIDOR'       => ['dbname' => 'modelo', 'type' => 'param'],
                    'PRECIO SUBDISTRIBUIDOR'    => ['dbname' => 'modelo', 'type' => 'param'],
                    'PRECIO ESPECIAL'           => ['dbname' => 'modelo', 'type' => 'param']
                ],

            ]
        ];

        $this->uploadErrorMessages = [
            UPLOAD_ERR_INI_SIZE => 'The uploaded file exceeds the upload_max_filesize directive in php.ini.',
            UPLOAD_ERR_FORM_SIZE => 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form.',
            UPLOAD_ERR_PARTIAL => 'The uploaded file was only partially uploaded.',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded.',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing a temporary folder. Introduced in PHP 4.3.10 and PHP 5.0.3.',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk. Introduced in PHP 5.1.0.',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension. Introduced in PHP 5.2.0.',
        ];

        $this->allowedExtensions = ['xls', 'xlsx'];
    }

    /**
     * @Route("/carga-masiva", name="r_Inventario_cargaMasivaIndex")
     */
    public function cargaMasivaIndex(): Response
    {
        $carpeta_front = $this->getParameter('uploads_carga_masiva_inventario');
        return $this->render('carga_inventario_bodega/carga_masiva.twig', [
            'uploads_front' => $carpeta_front
        ]);
    }

    /**
     * Controller endpoint to handle bulk addition of products.
     *
     * This method processes a request containing formatted product data, validates it against a predefined dictionary,
     * and adds the valid products to the system. It returns a JSON response containing information about the success
     * or failure of the operation, any errors encountered, and HTML content for the added products.
     *
     * @Route("/add-products", name="carga_masiva_traspaso_add_products")
     * @param Request $request The HTTP request object containing the formatted product data.
     * @return JsonResponse A JSON response containing information about the success or failure of the operation,
     * any errors encountered, HTML content for the added products, and other relevant data.
     */
    public function addProducts(Request $request): Response
    {

        $id = rand(5, 15) . uniqid();
        $em = $this->getDoctrine()->getManager();
        $exito = true;
        $typoDoc = "invalido";

        $msj = "";
        $html = "";
        $extrahtml = "";
        $Stock = null;
        $errors = [];
        $codesAdded = [];
        $dictKey = "not found";

        $content = $request->get("formatedCodes");
        $columns = array_shift($content);

        try {
            // Trim and convert to uppercase
            $columns = array_map(function ($value) {
                return (string)strtoupper(trim($value));
            }, $columns);

            // Remove null or empty values
            $columns = array_filter($columns, function ($value) {
                return $value !== null && $value !== '';
            });

            // Ensure that each row has the same number of elements as $columns
            $content = array_filter($content, function ($row) use ($columns) {
                return count($row) == count($columns);
            });

            // Trim each value in the rows
            $content = array_map(function ($row) use ($columns) {
                return array_map('trim', array_slice($row, 0, count($columns)));
            }, $content);


            if (count($content) <= 0) throw new \Exception("El archivo no tiene contenido.");

            $content = array_map(function ($row) use ($columns) {
                return array_combine($columns, $row);
            }, $content);

            foreach ($this->dictionary as $key => $dictionary) { // Compare the columns values with the dictionary

                if (array_keys($dictionary['cols']) === $columns) { // Check if the 'cols' keys in the inner dictionary match the column structure
                    $dictKey = (string)$key;
                    $typoDoc = (string)$key;
                    break;
                }
            }

            if ($dictKey != "not found") {

                if ($dictKey == 'Carga-Inv') {
                    $queryBuilder = $em->createQueryBuilder();
                    $queryBuilder
                        ->select('prov.rfc, prov.nombre')
                        ->from('App\Entity\Proveedor', 'prov')
                        ->Where('prov.status = 1');
                    $results = $queryBuilder->getQuery()->getResult();

                    $rfcprovedores = [];
                    foreach ($results as $result) {
                        $rfcprovedores[$result['rfc']] = $result['rfc'] . ' - ' . $result['nombre'];
                    }

                    $extrahtml = $this->renderView(
                        'carga_inventario_bodega/inv-extras.html.twig',
                        [
                            'selects' => [
                                [
                                    'options' => [
                                        0 => 'Ingreso por compra de productos nuevos.',
                                        1 => 'Ajuste de Inventario.',
                                        2 => 'Garantía de proveedor.'
                                    ],
                                    'selectName' => "tipo"
                                ],
                                [
                                    'options' => $rfcprovedores,
                                    'selectName' => "proveedor"
                                ]
                            ]
                        ]
                    );
                } else if ($dictKey == 'Carga-Producto') {
                    $user = $this->getUser();
                    $queryBuilder = $em->createQueryBuilder();
                    $queryBuilder
                        ->select('emp.rfc, emp.nombre')
                        ->from('App\Entity\Usuarioempresapermiso', 'usremp')
                        ->join('usremp.empresaIdempresa', 'emp')
                        ->join('usremp.usuarioIdusuario', 'usr')
                        ->Where('emp.status = 1')
                        ->andWhere('usr.idusuario =:usrID')
                        ->groupBy('emp.rfc');

                    $queryBuilder->setParameter('usrID', $user->getIdusuario());
                    $results = $queryBuilder->getQuery()->getResult();

                    $options = [];
                    foreach ($results as $result) {
                        $options[$result['rfc']] = $result['nombre'];
                    }

                    $extrahtml = $this->renderView(
                        'carga_inventario_bodega/inv-extras.html.twig',
                        [
                            'selects' => [
                                [
                                    'options' => $options,
                                    'selectName' => "empresa"
                                ]
                            ]
                        ]
                    );
                }

                $requiredarray = array_filter($this->dictionary[$dictKey]['cols'], function ($value) {
                    return isset($value['required']) && $value['required'] === true;
                });

                foreach ($content as $index => $row) {

                    $filteredCheck = [];
                    $errorDesc = 'Valores Erroneos: ';
                    foreach ($requiredarray as $key => $value) {
                        if (!array_key_exists($key, $row) || $row[$key] === null || $row[$key] === '') {
                            $filteredCheck[$key] = $row[$key];
                            $errorDesc .= $key . ', ';
                        } else if ($row[$key] !== null && $value['type'] == 'num') {
                            if (!is_numeric($row[$key])) {
                                $filteredCheck[$key] = $row[$key];
                                $errorDesc .= $key . '[NAN], ';
                            }
                        } else if ($row[$key] !== null && ($value['type'] !== 'param')) {
                            $entity = $em->getRepository($value['type'])->findOneBy([$value['pk']  => $row[$key], 'status' => "1"]);
                            if (!$entity) {
                                $filteredCheck[$key] = $row[$key];
                                $errorDesc .= $key . ', ';
                            }
                        }
                    }

                    $errorDesc = rtrim($errorDesc, ', ');

                    $idprod = $row['CLAVE'] . ' ' . $row['MODELO'];

                    if (count($filteredCheck) <= 0) {

                        $exsistingObj = null;

                        if ($row['UPC'] != null && $row['UPC'] != '') {
                            $exsistingObj = $em->getRepository(Producto::class)->findOneBy(['codigobarrasuniversal' => $row['UPC'], 'status' => "1"]);
                        }

                        if (!$exsistingObj && $row['CLAVE'] != null && $row['CLAVE'] != '') {
                            $exsistingObj = $em->getRepository(Producto::class)->findOneBy(['clave' => $row['CLAVE'], 'status' => "1"]);
                        }


                        if (!$exsistingObj) {
                            $exsistingObj = $em->getRepository(Producto::class)->findOneBy([
                                'modelo' => $row['MODELO'],
                                'clave' => $row['CLAVE'] == "" ? NULL : $row['CLAVE'],
                                'status' => "1"
                            ]);
                        }

                        $oldAmount = -1;

                        $ObjStock = null;

                        if ($exsistingObj && $dictKey == 'Carga-Inv') {
                            $ObjSucursal = $em->getRepository(Sucursal::class)->findOneBy(['codigo' => $row['ALMACEN'], 'status' => "1"]);

                            if ($row['SKU'] != null && $row['SKU'] != '') {
                                $ObjStock = $em->getRepository(Stock::class)->findOneBy(['codigobarras' => $row['SKU'], 'status' => "1"]);

                                if (!$ObjStock) $errorDesc = "Actualizando SKU Inexistente";
                            }

                            if ($ObjSucursal) {
                                $queryBuilder = $em->createQueryBuilder();
                                $queryBuilder
                                    ->select('COALESCE(SUM(s.cantidad), 0) as cantidad')
                                    ->from('App\Entity\Stock', 's')
                                    ->join('s.productoIdproducto', 'p')
                                    ->join('s.sucursalIdsucursal', 'suc')
                                    ->Where('s.status = 1')
                                    ->andWhere('p.idproducto =:productoID')
                                    ->andWhere('suc.idsucursal =:sucursalID')
                                    ->andWhere('s.cantidad > 0')
                                    ->groupBy('suc.idsucursal', 'p.idproducto');

                                $queryBuilder->setParameter('productoID', $exsistingObj->getIdproducto());
                                $queryBuilder->setParameter('sucursalID', $ObjSucursal->getIdsucursal());

                                $oldAmount = $queryBuilder->getQuery()->getResult()[0]['cantidad'] ?? 0;
                            }
                        }

                        $html .= $this->renderView(
                            'carga_inventario_bodega/agregar-producto.html.twig',
                            ['value' => $row, 'id' => $id . '-' . $index, 'dictionary' => $dictKey, 'oldObj' => $exsistingObj, 'oldAmount' => $oldAmount, 'ObjStock' => $ObjStock]
                        );

                        $jsonData = json_encode($row);

                        if ($dictKey == 'Carga-Producto' || ($dictKey == 'Carga-Inv' && $oldAmount == -1 && (($row['SKU'] == null || $row['SKU'] == '') || $ObjStock)) || $exsistingObj)
                            array_push($codesAdded, $jsonData);
                    } else {
                        array_push($errors, ["index" => $index + 1, "code" => $idprod, "msg" => $errorDesc]);
                    }
                }
            } else {
                throw new \Exception("El archivo no tiene un formato valido.");
            }
        } catch (\Exception $e) {
            $msj = $e->getMessage() . " " . $e->getLine();
            $exito = false;
        }


        return $this->json(["msj" => $msj, "exito" => $exito, "tipoDoc" => $typoDoc, 'html' => $html, 'extrahtml' => $extrahtml, 'errors' => $errors, 'codesAdded' => $codesAdded, 'cols' => $columns, 'dictKey' => $dictKey]);
    }

    /**
     * @Route("/upload-drive", name="carga_masiva_upload_drive")
     */
    public function uploadDrive(Request $request)
    {
        $exito = true;
        $documentName = '';
        $tipoDocumento = '';
        $html = '';

        $content = $request->get("checkProducts");
        $columns = $request->get("cols");
        $dictKey = $request->get("dictKey");
        $extrainfo = $request->get("extrainfo");

        try {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            $column = 'A';
            foreach ($columns as $header) {
                $sheet->setCellValue($column . '1', $header);
                $column++;
            }

            $row = 2; // Start from second row
            foreach ($content as $rowData) {
                $column = 'A';
                foreach ($rowData as $cellData) {
                    $sheet->setCellValue($column . $row, $cellData);
                    $column++;
                }
                $row++;
            }

            $curDictionary = $this->dictionary[$dictKey];
            $carpeta = $this->getParameter('uploads_carga_masiva_inventario');
            $documentName = $dictKey . '-' . '-' . md5(uniqid()) . '.xlsx';

            // $directoryPath =  '%kernel.project_dir%/public/uploads/carga-masiva/inventario   / ($curDictionary['folder_name']) '
            $directoryPath = $carpeta . DIRECTORY_SEPARATOR . $curDictionary['folder_name'];
            $targetPath = $directoryPath . DIRECTORY_SEPARATOR . $documentName;

            // Create directory if not exists
            if (!is_dir($directoryPath)) {
                if (!mkdir($directoryPath, 0755, true)) {
                    throw new \Exception("Hubo un error interno creando el directorio: $directoryPath");
                }
            }

            // Save the Excel file
            $writer = new Xlsx($spreadsheet);
            $writer->save($targetPath);
            if (file_exists($targetPath)) {
                // Process document based on $dictKey
                switch ($dictKey) {
                    case 'Carga-Inv':
                        list($msj, $exito, $tipoDocumento, $html) = $this->procesarDocumentoINV($columns, $content, $documentName, $extrainfo);
                        break;
                    case 'Carga-Producto':
                        list($msj, $exito, $tipoDocumento) = $this->procesarDocumentoProd($columns, $content, $extrainfo);
                        break;
                    case 'Carga-Costo':
                        list($msj, $exito, $tipoDocumento) = $this->procesarDocumentoCost($columns, $content);
                        break;
                    default:
                        throw new \Exception("Aùn no existe metodo para cargar: $dictKey");
                }
            } else {
                throw new \Exception("Failed to save the file: $targetPath");
            }
        } catch (\Exception $e) {
            $msj = $e->getMessage() . " linea " . $e->getLine() . " CargaMasivaController.php";
            $exito = false;
        }

        return $this->json(array(
            'msj' => $msj, 'exito' => $exito, 'nombreDocumento' => $documentName, 'tipoDocumento' => $tipoDocumento, 'html' => $html
        ));
    }



    /**
     * @Route("/upload-doc", name="carga-masiva-upload-doc")
     */
    public function uploadDoc()
    {
        ini_set('upload_max_filesize', '30M');
        $valid = false;
        $msj = "Error Desconocido archivo no procesado";
        $name = $_FILES['file1']['name'] ?? "none";
        $documentName = $_FILES['file1']['name'] ? explode(".", $_FILES['file1']['name']) : "none";

        if ($documentName != "none") {
            array_pop($documentName);
            $documentName = implode(".", $documentName);
        } else {
            $documentName = "unknown";
        }
        $idVista = $_POST['id'] ?? "none";
        $tipoDocumento = "invalido";
        $dictKey = "not found";

        $tmpName = $_FILES['file1']['tmp_name'];
        $ext = strtolower(pathinfo($name, PATHINFO_EXTENSION)) ?? "none";
        $html = null;

        try {

            // Detect Document Exist
            if (isset($_FILES['file1']) && in_array($ext, $this->allowedExtensions)) {

                $error = $_FILES['file1']['error']; // Getting status of the Document

                switch ($error) { // Switchcase Based on error UPLOAD_ERR_OK = success
                    case UPLOAD_ERR_OK:

                        $uploadedFile = $_FILES['file1']['tmp_name']; // Load the uploaded file
                        $reader = IOFactory::createReaderForFile($uploadedFile); // Create a PhpSpreadsheet reader
                        $spreadsheet = $reader->load($uploadedFile);
                        $worksheet = $spreadsheet->getActiveSheet(); // Get the first worksheet (assuming there's only one)

                        $content = $worksheet->toArray(); // Initialize an empty array to store the result
                        $columns = array_shift($content); // Get the first row as an array (column names)

                        $columns = array_map(function ($value) { // Trim and convert to uppercase
                            return (string)strtoupper(trim($value));
                        }, $columns);

                        $columns = array_filter($columns, function ($value) { // Remove null or empty values
                            return $value !== null && $value !== '';
                        });

                        $content = array_map(function ($row) use ($columns) {
                            return array_slice($row, 0, count($columns));
                        }, $content);

                        $content = array_map(function ($row) use ($columns) {
                            return array_map('trim', array_slice($row, 0, count($columns)));
                        }, $content);

                        if (count($content) <= 0) throw new \Exception("Sin Contenido.");

                        $content = array_map(function ($row) use ($columns) {
                            return array_combine($columns, $row);
                        }, $content);

                        foreach ($this->dictionary as $key => $dictionary) { // Compare the columns values with the dictionary

                            if (array_keys($dictionary['cols']) === $columns) { // Check if the 'cols' keys in the inner dictionary match the column structure
                                $dictKey = (string)$key;
                                break;
                            }
                        }

                        if ($dictKey != "not found") {

                            $curDictionary = $this->dictionary[$dictKey];
                            $carpeta =  $this->getParameter('uploads_carga_masiva_inventario');
                            $documentName = md5(uniqid()) . '.' . $ext;
                            $directoryPath = $carpeta . DIRECTORY_SEPARATOR . $curDictionary['folder_name'];
                            $targetPath =  $directoryPath . DIRECTORY_SEPARATOR . $documentName;

                            if (!is_dir($directoryPath)) {
                                // The directory doesn't exist, so create it with the necessary permissions (e.g., 0755)
                                if (!mkdir($directoryPath, 0755, true)) {
                                    // Failed to create the directory, handle the error as needed
                                    $msj = "Failed to create the directory: $directoryPath";
                                    break;
                                }
                            }

                            if (move_uploaded_file($tmpName, $targetPath)) {

                                switch ($dictKey) {
                                    case 'Carga-Inv':
                                        list(
                                            $msj, $valid, $tipoDocumento, $html,
                                        ) = $this->procesarDocumentoINV($columns, $content, $documentName);
                                        break;

                                    case 'Carga-Producto':
                                        list(
                                            $msj, $valid, $tipoDocumento
                                        ) = $this->procesarDocumentoProd($columns, $content);
                                        break;

                                    case 'Carga-Costo':
                                        list(
                                            $msj, $valid, $tipoDocumento,
                                        ) = $this->procesarDocumentoCost($columns, $content);
                                        break;
                                }
                            } else {
                                $msj = "Formato valido, problema subiendo archivo. " . $dictKey;
                            }
                        } else {
                            $msj = "Debe seleccionar un documento con formato valido";
                        }

                        break;

                    default:
                        $msj = $this->uploadErrorMessages[$error] ?? 'An unknown error occurred during file upload.';
                        break;
                }
            } else {
                $msj = "Debe seleccionar un documento valido de extension " . $this->allowedExtensions . " y de formato valido";
            }
        } catch (\Exception $e) {
            $msj = $e->getMessage() . " linea " . $e->getLine() . " CargaMasivaController.php llamar a Soporte";
            $valid = false;
        }

        return $this->json(array(
            'msj' => $msj, 'exito' => $valid,
            'idVista' => $idVista, 'nombreDocumento' => $documentName, 'tipoDocumento' => $tipoDocumento,
            'html' => $html,

        ));
    }

    function procesarDocumentoINV($columns, $content, $documento, $extrainfo = null)
    {
        ini_set('max_execution_time', '6000');

        $exito = false;
        $msj = "";
        $em = $this->getDoctrine()->getManager();
        $nombreExcelCodigos = "";
        $productosActualizados = 0;
        $productosNoEncontrados = null;
        $productosEncontrados = 0;
        $productosNoEncontradosData = [];
        $stockDadosAlta = [];
        $Usuarioconectado = $this->getUser();
        $columnValues = [];

        $stockstate = $em->getRepository(Stockstate::class)->findOneBy(['name' => 'DISPONIBLE', 'status' => "1"]);

        if (!$stockstate) throw new \Exception('Problema con el stockstate DISPONIBLE');

        try {

            if (empty($extrainfo['proveedor'])) {
                throw new \Exception('Problema con el proveedor');
            }

            $ObjProveedor = $em->getRepository(Proveedor::class)->findOneBy(['rfc' => $extrainfo['proveedor'], 'status' => "1"]);

            if (!$ObjProveedor) {
                throw new \Exception('No se encontró el proveedor ' . $extrainfo['proveedor'] . ', regístrelo o habilitelo primero');
            }

            foreach ($content as $row) {

                $busquedaporCodigoModelo = "";

                if ($row['MODELO'] != "" || $row['UPC'] != "" || $row['CLAVE'] != "") {
                    if ((is_int((int)$row['CANTIDAD']) || is_float((float)$row['CANTIDAD'])) && $row['ALMACEN'] != "") {
                        if ($row['MODELO'] != "") {
                            $busquedaporCodigoModelo = "modelo";
                        } else if ($row['UPC'] != "") {
                            $busquedaporCodigoModelo = "codigoBarras";
                        } else if ($row['CLAVE'] == "") {
                            throw new \Exception('Un producto no tiene modelo ni código de barras ni Clave' . $row['MODELO'] . " almacen " . $row['ALMACEN'] . " cantidad " . $row['CANTIDAD']);
                        }

                        $stockDadosAlta[$row['ALMACEN']][] = [
                            'almacen' => trim($row['ALMACEN']),
                            'cantidad' => trim($row['CANTIDAD']),
                            'codigoBarras' => trim($row['UPC']),
                            'modelo' => trim($row['MODELO']),
                            'busquedaporCodigoModelo' => trim($busquedaporCodigoModelo),
                            'serie' => trim($row['SERIE']),
                            'numeroFactura' => trim($row['NUMERO FACTURA']),
                            'clave' => trim($row['CLAVE']),
                            'precio' => trim($row['PRECIO']) ?? 0,
                            'costo' => trim($row['COSTO']) ?? 0,
                            'sku' => trim($row['SKU'])
                        ];
                    } else {
                        throw new \Exception('No se encontró el almacen o hay un valor de cantidad incorrecto modelo ' . $row['MODELO'] . " almacen " . $row['ALMACEN'] . " cantidad " . $row['CANTIDAD']);
                    }
                }
            }

            if (count($stockDadosAlta) <= 0) {
                throw new \Exception('Ninguno de los elementos fue encontrado');
            }

            $stockDadosAltaConcentrado = [];

            foreach ($stockDadosAlta as $keyProd => $productos) {
                $almacen = $keyProd;

                foreach ($productos as $keyProducto => $producto) {
                    $modelo = $producto['modelo'];
                    $clave = $producto['clave'] ?? '';
                    $codigoBarras = $producto['codigoBarras'];
                    $cantidad = $producto['cantidad'];
                    $busquedaporCodigoModelo = $producto['busquedaporCodigoModelo'];
                    $serie = trim($producto['serie']);
                    $numeroFactura = trim($producto['numeroFactura']);

                    $ObjSucursal = $em->getRepository(Sucursal::class)->findOneBy(['codigo' => $almacen, 'status' => "1"]);

                    if (!$ObjSucursal) {
                        throw new \Exception('No se encontró el almacén ' . $almacen);
                    }

                    if ($ObjSucursal->getTipo() != "bodega") {
                        throw new \Exception('El almacén no puede ser una sucursal ' . $almacen);
                    }

                    $ObjProducto = null;

                    if ($producto['clave'] != '' && $producto['clave'] != null) {
                        $ObjProducto = $this->getDoctrine()->getRepository(Producto::class)->findOneBy([
                            'clave' => $producto['clave'],
                            'status' => "1"
                        ]);
                    }

                    if (!$ObjProducto && $codigoBarras != null && $codigoBarras != '') {
                        $ObjProducto = $em->getRepository(Producto::class)->findOneBy(['codigobarrasuniversal' => $codigoBarras, 'status' => "1"]);
                    }

                    if (!$ObjProducto && $producto['modelo'] != null && $producto['modelo'] != '') {

                        $ObjProducto = $this->getDoctrine()->getRepository(Producto::class)->findOneBy([
                            'modelo' => $producto['modelo'],
                            'status' => "1"
                        ]);
                    }

                    if (!$ObjProducto) {
                        $productosNoEncontrados++;
                        $productosNoEncontradosData[] = ("modelo: " . $modelo . " codigo " . $codigoBarras . " clave " . $clave);
                    } else {

                        $productosEncontrados++;
                        $masivoUnico = $ObjProducto->getMasivounico();
                        $tipoProducto = $ObjProducto->getTipoproducto();
                        $codigoBarras = $ObjProducto->getCodigobarrasuniversal();

                        if ($masivoUnico != "1" && $serie != "") {
                            throw new \Exception('El producto ' . $modelo . ' no puede tener serie porque es masivo ');
                        }

                        if ($tipoProducto != "1") {
                            throw new \Exception('El producto ' . $modelo . ' no puede tener stock porque es un servicio ');
                        }

                        $sku = $producto['sku'];

                        if ($masivoUnico == "1") {
                            for ($i = 0; $i < $cantidad; $i++) {

                                if ($producto['sku'] == null || $producto['sku'] == '') {
                                    $sku = "151" . mt_rand(100000000, 999999999);

                                    $ObjStockAuxliiar = $em->getRepository(Stock::class)->findOneBy(['codigobarras' => $sku, 'status' => "1"]);

                                    if ($ObjStockAuxliiar) {

                                        for ($i = 0; $i < 20; $i++) {
                                            $ObjStockAuxliiar = $em->getRepository(Stock::class)->findOneBy(['codigobarras' => $sku, 'status' => "1"]);
                                            if (!$ObjStockAuxliiar) {
                                                break;
                                            }
                                        }
                                    }
                                } else {
                                    $sku = $producto['sku'];
                                }

                                $stockDadosAltaConcentrado[] = [
                                    'almacen' => $almacen,
                                    'sku' => $sku,
                                    'cantidad' => 1,
                                    'modelo' => $modelo,
                                    'idproducto' => $ObjProducto->getIdproducto(),
                                    'marca' => $ObjProducto->getMarcaIdmarca()->getNombre(),
                                    'descripcion' => $ObjProducto->getDescripcion(),
                                    'precio' => $producto['precio'],
                                    'costo' => $producto['costo'],
                                    'idsucursal' => $ObjSucursal->getIdsucursal(),
                                    'sucursal' => $ObjSucursal->getNombre(),
                                    'Producto' => $ObjProducto,
                                    'Sucursal' => $ObjSucursal,
                                    'masivoUnico' => $masivoUnico,
                                    'serie' => $serie,
                                    'codigoBarras' => $codigoBarras,
                                    'numeroFactura' => $numeroFactura,
                                    'iva' => $ObjSucursal->getPorcentajeiva(),
                                    'precioConIva' => $ObjProducto->getPrecio() * (1 + (.01 * $ObjSucursal->getPorcentajeiva())),
                                ];
                            }
                        } else {
                            $stockDadosAltaConcentrado[] = [
                                'almacen' => $almacen,
                                'sku' => $sku,
                                'cantidad' => $cantidad,
                                'modelo' => $modelo,
                                'idproducto' => $ObjProducto->getIdproducto(),
                                'marca' => $ObjProducto->getMarcaIdmarca()->getNombre(),
                                'descripcion' => $ObjProducto->getDescripcion(),
                                'precio' => $producto['precio'],
                                'costo' => $producto['costo'],
                                'idsucursal' => $ObjSucursal->getIdsucursal(),
                                'sucursal' => $ObjSucursal->getNombre(),
                                'Producto' => $ObjProducto,
                                'Sucursal' => $ObjSucursal,
                                'masivoUnico' => $masivoUnico,
                                'serie' => $serie,
                                'codigoBarras' => $codigoBarras,
                                'numeroFactura' => $numeroFactura,
                                'iva' => $ObjSucursal->getPorcentajeiva(),
                                'precioConIva' => $ObjProducto->getPrecio() * (1 + (.01 * $ObjSucursal->getPorcentajeiva())),
                            ];
                        }
                    }
                }
            }

            if (!$productosNoEncontrados) {
                foreach ($stockDadosAltaConcentrado as $producto) {
                    $productosActuales = 0;
                    $Stock = null;
                    $isOverride = true;
                    if ($producto['masivoUnico'] == "2") {
                        $query = $em->createQuery(
                            'SELECT s
                            FROM App\Entity\Stock s
                            INNER JOIN s.productoIdproducto p 
                            INNER JOIN s.sucursalIdsucursal sucursal
                            WHERE sucursal.idsucursal = :idsucursal AND p.idproducto = :idproducto
                            AND s.status >= 0'
                        )->setParameters([
                            'idproducto' => $producto['idproducto'],
                            'idsucursal' => $producto['idsucursal']
                        ]);

                        $Stock = $query->getResult();

                        $amount = 0;

                        foreach ($Stock as $check) {
                            $check->setStatus('0');
                            $em->persist($check);
                            $amount +=  $check->getCantidad() ?? 0;
                            $check->setCantidad(0);
                        }

                        $tmpStock = $Stock[0] ?? null;
                        if ($tmpStock) {
                            $tmpStock->setStatus('1');
                            $tmpStock->setCantidad($amount);
                            $em->persist($tmpStock);
                        }
                        $Stock = $tmpStock;
                    } else if ($Stock && $producto['masivoUnico'] == "1") {
                        $Stock = null;
                        if ($producto['sku'] != "" && $producto['sku'] != null) {
                            $Stock = $em->getRepository(Stock::class)->findOneBy(['codigobarras' => $producto['sku'], 'status' => "1"]);
                        }

                        $isOverride = $Stock == null;
                    }

                    if (!$Stock) {

                        $Stock = new Stock();
                        $Stock->setCreacion(new \DateTime("now"));
                        $Stock->setProductoIdproducto($producto['Producto']);
                        $Stock->setSucursalIdsucursal($producto['Sucursal']);
                        $Stock->setJustificacion($extrainfo['justificacion'] ?? NULL);
                        $Stock->setTipo($extrainfo['tipo'] ?? '0');
                        $Stock->setSerie($producto['serie']);
                    }

                    if ($producto['precio'] != '' &&  $producto['precio'] != null) $Stock->setPrecio($producto['precio']);
                    if ($producto['costo'] != '' &&  $producto['costo'] != null) $Stock->setCosto($producto['costo']);

                    $Stock->setModificacion(new \DateTime("now"));

                    $stockstate = $em->getRepository(Stockstate::class)->findOneBy(['name' => 'DISPONIBLE', 'status' => "1"]);
                    $Stock->setStockstateIdstockstate($stockstate);

                    if ($isOverride) {
                        $productosActuales = $Stock->getCantidad() ?? 0;
                        $Stock->setCantidad($productosActuales + $producto['cantidad']);
                        $Stock->setCodigobarras($producto['sku']);
                    }

                    $columnValues[] = [$producto['sku'], $producto['serie'], $producto['codigoBarras'], $producto['marca'], $producto['modelo'], $producto['descripcion'], $producto['cantidad'], "$" . round($producto['precio'], 2), (floatval($producto['iva'])) . "%", "$" . round($producto['precioConIva'], 2)];

                    $Stock->setStatus("1");
                    $em->persist($Stock);

                    $Cargainventariolog = new Cargainventariolog();
                    $Cargainventariolog->setCantidad($producto['cantidad']);
                    $Cargainventariolog->setFecha(new \DateTime("now"));
                    $Cargainventariolog->setConexcel("1");
                    $Cargainventariolog->setArchivoexcel($documento);
                    $Cargainventariolog->setStockIdstock($Stock);
                    $Cargainventariolog->setUsuarioIdusuario($Usuarioconectado);
                    $Cargainventariolog->setProveedorIdproveedor($ObjProveedor);
                    $Cargainventariolog->setNumerofactura($numeroFactura);
                    $Cargainventariolog->setJustificacion($extrainfo['justificacion'] ?? NULL);
                    $Cargainventariolog->setTipo($extrainfo['tipo'] ?? '0');
                    $em->persist($Cargainventariolog);

                    $productosActualizados++;
                }
            } else {
                $msj = "Revise que el formato esté llenado correctamente";
            }

            $em->flush();

            // Generate an Excel file for printing labels
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            $columnNames = [
                'SKU',
                'SERIE',
                'Código de Barras',
                'Marca',
                'Modelo',
                'Descripción',
                'Cantidad',
                'Precio ',
                'Porcentaje IVA ',
                'Precio con IVA'
            ];
            $columnLetter = 'A';

            foreach ($columnNames as $columnName) {
                $sheet->setCellValue($columnLetter . '1', $columnName);
                $columnLetter++;
            }

            $i = 2;

            foreach ($columnValues as $columnValue) {
                $columnLetter = 'A';

                foreach ($columnValue as $value) {
                    $sheet->getColumnDimension($columnLetter)->setAutoSize(true);

                    if ($columnLetter == "H" || $columnLetter == "J") {
                        $sheet->setCellValue($columnLetter . $i, "$" . round($value, 2));
                        $sheet->getStyle($columnLetter . $i)->setQuotePrefix(true);
                    } else if ($columnLetter == "I") {
                        $sheet->setCellValue($columnLetter . $i, (floatval($value)) . "%");
                        $sheet->getStyle($columnLetter . $i)->setQuotePrefix(true);
                    } else {
                        $sheet->setCellValue($columnLetter . $i, $value);
                    }

                    $columnLetter++;
                }
                $i++;
            }

            //guardar excel de resultados en carpeta public, antes codigos

            $nombreExcelCodigos = 'codigos-' . date('m-d-Y_hia') . '-' . rand() . rand() . '.xlsx';
            $path_guardar = $this->getParameter('uploads_carga_masiva_resultados');
            $excelFilepath = $path_guardar .'/'. $nombreExcelCodigos;
            $writer = new Xlsx($spreadsheet);

            try {
                if (!is_dir($path_guardar)) {
                    mkdir($path_guardar, 0777, true);
                }
                $writer->save($excelFilepath);
            } catch (FileException $e) {
                throw new \Exception($e->getMessage());
            }


            

            $exito = true;
        } catch (\Exception $e) {
            $msj = $e->getMessage() . " linea " . " CargaMasivaController.php" . $e->getLine();
            $exito = false;
        }

        $html = $this->renderView('carga_inventario_bodega/respuesta-carga-inventario-bodega.html.twig', [
            'msj' => $msj, 'exito' => $exito,
            'productosNoEncontradosData' => $productosNoEncontradosData,
            'productosNoEncontrados' => $productosNoEncontrados,
            'productosActualizados' => $productosActualizados,
            'nombreExcelCodigos' => $nombreExcelCodigos,
        ]);

        return [$msj, $exito, "Carga Inventario", $html];
    }

    function procesarDocumentoProd($columns, $content, $extrainfo = null)
    {
        ini_set('max_execution_time', '600000');
        $em = $this->getDoctrine()->getManager();
        $exito = false;
        $msj = "Productos cargados: ";
        $productosOmitidos = 0;
        $productosNuevos = 0;
        $productosActualizados = 0;
        $codigoBarras = "";

        try {

            $ObjEmpresa = $em->getRepository(Empresa::class)->findOneBy(['rfc' => $extrainfo['empresa'], 'status' => "1"]);
                if (!$ObjEmpresa) {
                    throw new \Exception('Debe poner una empresa valida ');
                }

            foreach ($content as $row) {

                if ($row['SOBREPUESTO'] !== "1") $row['SOBREPUESTO'] = 0;
                if (!in_array(strtoupper($row['MASIVO UNICO']), ["UNICO", "ÚNICO"])) $row['MASIVO UNICO'] = "MASIVO";

                $requiredColumns = [
                    'MARCA', 'MODELO', 'MASIVO UNICO', 'CATEGORIA',
                    'SUBCATEGORIA', 'UNIDAD DE MEDIDA', 'MEDIDA'
                ];

                /*if (!array_reduce($requiredColumns, function ($carry, $col) use ($row) {
                    return $carry && ($row[$col] !== "" && $row[$col] !== null);
                }, true)) {
                    $productosOmitidos++;
                    $productosOmitidosData[] = ['modelo' => $row['MODELO'], 'barcode' => $row['UPC'], 'descripcion' => $row['DESCRIPCION']];
                    continue;
                }*/

                $ObjMarca = $em->getRepository(Marca::class)->findOneBy(['nombre' => $row['MARCA'], 'status' => "1"]);
                if (!$ObjMarca) {
                    $ObjMarca = new Marca();
                    $ObjMarca->setNombre($row['MARCA']);
                    $em->persist($ObjMarca);
                    $em->flush();
                }

                $ObjCategoria = $this->getOrCreateEntity(Clase::class, 'nombre', trim($row['CATEGORIA']), 'setEmpresaIdempresa', $ObjEmpresa, $em);

                if ($ObjCategoria->getEmpresaIdempresa() != $ObjEmpresa) throw new \Exception('Producto ' . trim($row['MODELO']) . ' dado de alta en otra empresa: ' . $ObjEmpresa->getNombre());

                $ObjSubCategoria = $this->getOrCreateEntity(Categoria::class, 'nombre', trim($row['SUBCATEGORIA']), 'setClaseIdclase', $ObjCategoria, $em);
                $ObjUnidadMedida = $this->getOrCreateEntity(Unidadmedida::class, 'nombre', trim($row['UNIDAD DE MEDIDA']), null, null, $em);
                $ObjMedida = $this->getOrCreateEntity(Medida::class, 'nombre', trim($row['MEDIDA']), 'setUnidadmedidaIdunidadmedida', $ObjUnidadMedida, $em);

                $ObjProducto = null;
                if (trim($row['UPC']) ?? '' != '') {
                    $ObjProducto = $em->getRepository(Producto::class)->findOneBy(['codigobarrasuniversal' => $row['UPC'], 'status' => "1"]);
                }

                if (!$ObjProducto) {
                    $ObjProducto = $em->getRepository(Producto::class)->findOneBy([
                        'modelo' => $row['MODELO'],
                        'clave' => $row['CLAVE'] == "" ? NULL : $row['CLAVE'],
                        'status' => "1"
                    ]);
                }

                if (!$ObjProducto) {
                    $ObjProducto = new Producto();
                    $ObjProducto->setCosto(0)->setPrecio(0)->setCantidad(0)->setCreacion(new \DateTime("now"))
                        ->setModificacion(new \DateTime("now"))->setTipoproducto("1");
                    $productosNuevos++;
                } else {
                    $productosActualizados++;
                }

                /*var_dump(trim($ObjProducto->getCodigo()));
                var_dump(trim($row['UPC']));
                var_dump(trim($row['UPC']) ?? '');*/
                if (trim($ObjProducto->getCodigobarrasuniversal()) == "" && (trim($row['UPC']) ?? '') == '') {
                    $codigoBarras = "111" . mt_rand(100000000, 999999999);
                    $ObjProductoAuxliiar = $em->getRepository(Producto::class)->findOneBy(['codigobarrasuniversal' => $codigoBarras, 'status' => "1"]);
                    if ($ObjProductoAuxliiar) {
                        $codigoBarras = "111" . mt_rand(100000000, 999999999);
                        unset($ObjProductoAuxliiar);
                    }

                    if (trim($row['MASIVO UNICO']) == "MASIVO" && $ObjProducto->getCodigobarrasuniversal() == "") {
                        $ObjProducto->setCodigobarrasuniversal($codigoBarras);
                    }
                    $ObjProducto->setCodigo($codigoBarras);
                } else if ((trim($row['UPC']) ?? '') != '') {
                    $ObjProducto->setCodigobarrasuniversal(trim($row['UPC']));
                }

                $ObjProducto->setNombre("SN")->setModelo(trim($row['MODELO']))->setTipo(1)->setCodigocolor($row['CODIGO DE COLOR'])
                    ->setColor($row['COLOR'])->setModificacion(new \DateTime("now"))->setCategoriaIdcategoria($ObjSubCategoria)
                    ->setMarcaIdmarca($ObjMarca)->setTipomaterial($row['TIPO MATERIAL'])->setDescripcion($row['DESCRIPCION'])
                    ->setSobrepuesto($row['SOBREPUESTO'])->setMedidaIdmedida($ObjMedida)->setOrdenlaboratorio('1')->setTipoproducto('1')
                    ->setEsfera($row['ESFERA'])->setCilindro($row['CILINDRO'])->setEje($row['EJE'])->setAdicion($row['ADICION'])
                    ->setBase($row['BASE'])->setDesign($row['DISENO'])->setClave($row['CLAVE'] == "" ? NULL : $row['CLAVE'])
                    ->setMasivounico(in_array(strtoupper($row['MASIVO UNICO']), ["UNICO", "ÚNICO"]) ? "1" : "2");

                $em->persist($ObjProducto);
                $em->flush();
                $msj .= $row['MARCA'] . $row['MODELO'] . $row['COLOR'] . ", ";
            }


            $exito = true;
        } catch (\Exception $e) {
            $msj .= $e->getMessage() . " linea " . $e->getLine() . " CargaMasivaController.php  llamar a Soporte";
            $exito = false;
        }

        return [$msj, $exito, "carga Producto"];
    }

    private function getOrCreateEntity($entityClass, $fieldName, $fieldValue, $setMethod = null, $setValue = null, $entityManager)
    {
        $entity = $entityManager->getRepository($entityClass)->findOneBy([trim($fieldName) => trim($fieldValue), 'status' => "1"]);
        if (!$entity) {
            $entity = new $entityClass();
            $entity->setNombre(trim($fieldValue));
            if ($setMethod && $setValue) {
                $entity->$setMethod($setValue);
            }
            $entityManager->persist($entity);
            $entityManager->flush();
        }
        return $entity;
    }

    function procesarDocumentoCost($columns, $content)
    {
        $exito = false;
        $todoEsValido = true;
        $msj = "";
        $em = $this->getDoctrine()->getManager();

        $productosActualizados = 0;
        $productosNoEncontrados = 0;
        $productosNoEncontradosData = [];


        try {
            foreach ($content as $row) {

                $costo = $row['COSTO'] ?? 0;
                $costo = (float)preg_replace('([^0-9 .])', '', $costo);

                $precio = $row['PRECIO'] ?? 0;
                $precio = (float)preg_replace('([^0-9 .])', '', $precio);

                $precioDistribuidor = $row['PRECIO DISTRIBUIDOR'] ?? 0;
                $precioDistribuidor = (float)preg_replace('([^0-9 .])', '', $precioDistribuidor);

                $precioSubdistribuidor = $row['PRECIO SUBDISTRIBUIDOR'] ?? 0;
                $precioSubdistribuidor = (float)preg_replace('([^0-9 .])', '', $precioSubdistribuidor);

                $precioEspecial = $row['PRECIO ESPECIAL'] ?? 0;
                $precioEspecial = (float)preg_replace('([^0-9 .])', '', $precioEspecial);

                if ($row['MODELO']) {

                    $ObjProducto = NULL;

                    if (trim($row['UPC']) ?? '' != '') {
                        $ObjProducto = $em->getRepository(Producto::class)->findOneBy(['codigobarrasuniversal' => $row['UPC'], 'status' => "1"]);
                    }

                    if (!$ObjProducto) {
                        $ObjProducto = $em->getRepository(Producto::class)->findOneBy([
                            'modelo' => $row['MODELO'],
                            'clave' => $row['CLAVE'] == "" ? NULL : $row['CLAVE'],
                            'status' => "1"
                        ]);
                    }

                    //si no fue encontrado entonces lo creamos
                    if (!$ObjProducto) {
                        $productosNoEncontrados++;
                        $productosNoEncontradosData[] = $row['MODELO'];
                        $todoEsValido = false;
                    } else {

                        $ObjProducto->setCosto($costo);
                        $ObjProducto->setPrecio($precio);
                        $ObjProducto->setPreciodistribuidor($precioDistribuidor);
                        $ObjProducto->setPreciosubdistribuidor($precioSubdistribuidor);
                        $ObjProducto->setPrecioespecial($precioEspecial);
                        $ObjProducto->setModificacion(new \DateTime("now"));
                        $productosActualizados++;

                        $em->persist($ObjProducto);
                        $em->flush();
                    }
                }
            }
            if ($todoEsValido == true) {
                $em->flush();
            } else {
                $productosActualizados = 0;
            }

            $exito = true;
        } catch (\Exception $e) {
            $msj = $e->getMessage() . " linea " . $e->getLine() . " CargaMasivaController.php  llamar a Soporte";
            $exito = false;
        }

        return [$msj, $exito, "Carga de Costos"];
    }
}
