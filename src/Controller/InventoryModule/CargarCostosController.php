<?php

namespace App\Controller\InventoryModule;

use App\Entity\Categoria;
use App\Entity\Producto;
use App\Entity\Proveedor;
use App\Entity\Stock;
use App\Entity\Sucursal;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
/**
 * @Route("inventario")
 */
class CargarCostosController extends AbstractController
{
    /**
     * @Route("/cargar-costos/", name="cargar_costos")
     */
    public function cargarCostos(): Response
    {
        $carpeta_front=$this->getParameter('uploads_front');
        return $this->render('cargar_costos/cargar_costos.html.twig', [
            'uploads_front' => $carpeta_front
        ]);
    }

    /**
     * @Route("/cargar-costos/subir-documento", name="cargar-costos-subir-documento")
     */
    public function subirDocumento()
    {
        ini_set('memory_limit', '2024M'); // or you could use 1G
        $responseProcesarDocumento=null;

        $valid = false;
        $msj="";

        //para ver en que carpeta se guardarán
        $documentName="";

        $carpeta=$this->getParameter('uploads');
        //este id es el que viene de la vista y sirve para mostar el mensaje de eerror en el documento que sea necesario
        $idVista="";

        $tipoDocumento="";
        $beneficiariosenBlanco=0;

        try{

            if(isset($_FILES['file1']['name'])){
                $idVista=$_POST['id'];
                $nombreDocumento="";


                $name=$_FILES['file1']['name'];

                $nombreDocumento=explode(".",$_FILES['file1']['name']);

                $mes=$nombreDocumento[1];
                $anio=$nombreDocumento[0];
                //var_dump($nombreDocumento);
                array_pop($nombreDocumento);
                //  var_dump($nombreDocumento);
                $nombreDocumento=implode(".",$nombreDocumento);
                //sacamos el año


                $productosNuevos=0;
                $productosOmitidos=0;
                $productosOmitidosData=[];
                $productosActualizados=0;
                $productosNoEncontradosData=[];
                $productosNoEncontrados=0;

                //$nombreDocumento=$nombreDocumento[0];
                $tmpName  = $_FILES['file1']['tmp_name'];
                $error    = $_FILES['file1']['error'];
                $size     = $_FILES['file1']['size'];
                $ext      = strtolower(pathinfo($name, PATHINFO_EXTENSION));


                if($ext=="xls" || $ext=="xlsx"){
                    $documentName=md5(uniqid()).'.'.$ext;

                    $targetPath =  $carpeta.DIRECTORY_SEPARATOR.$documentName;
                    switch ($error) {
                        case UPLOAD_ERR_OK:

                            //echo "rl ".$targetPath;9
                            if(move_uploaded_file($tmpName,$targetPath)){
                                //   $responseProcesarDocumento=$this->procesarDocumento($documentName);
                                $result=$this->verificarTipoDocumento($documentName);

                                $responseVerificarDocumento=json_decode($result->getContent());



                                $msj=$responseVerificarDocumento->msj;
                                $valid=$responseVerificarDocumento->exito;
                                if($responseVerificarDocumento->exito==true){

                                    $tipoDocumento=$responseVerificarDocumento->tipoDocumento;

                                    $productosActualizados=$responseVerificarDocumento->productosActualizados;
                                    $productosNoEncontradosData=$responseVerificarDocumento->productosNoEncontradosData;
                                    $productosNoEncontrados=$responseVerificarDocumento->productosNoEncontrados;
                                }

                            }


                            break;
                        case UPLOAD_ERR_INI_SIZE:
                            $msj = 'The uploaded file exceeds the upload_max_filesize directive in php.ini.';
                            break;
                        case UPLOAD_ERR_FORM_SIZE:
                            $msj = 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form.';
                            break;
                        case UPLOAD_ERR_PARTIAL:
                            $msj = 'The uploaded file was only partially uploaded.';
                            break;
                        case UPLOAD_ERR_NO_FILE:
                            $msj = 'No file was uploaded.';
                            break;
                        case UPLOAD_ERR_NO_TMP_DIR:
                            $msj = 'Missing a temporary folder. Introduced in PHP 4.3.10 and PHP 5.0.3.';
                            break;
                        case UPLOAD_ERR_CANT_WRITE:
                            $msj = 'Failed to write file to disk. Introduced in PHP 5.1.0.';
                            break;
                        case UPLOAD_ERR_EXTENSION:
                            $msj = 'File upload stopped by extension. Introduced in PHP 5.2.0.';
                            break;
                        default:
                            $msj = 'Unknown error';
                            break;
                    }
                }else{
                    $msj="Tipo de Formato Inválido";
                }


            }else{
                $msj="Debe seleccionar un documento";
            }


        }catch (\Exception $e) {
            $msj=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();

        }

        //obtenemos una vista de lo que se procesó
        $html= $this->renderView('cargar_costos/respuesta-carga-costos.html.twig', [
            'productosNoEncontradosData'=>$productosNoEncontradosData,
            'productosNoEncontrados'=>$productosNoEncontrados,
            'productosActualizados'=>$productosActualizados
        ]);

        return $this->json(array('msj'=>$msj,'exito'=>$valid,
            'idVista'=>$idVista,'nombreDocumento'=>$documentName,'tipoDocumento'=>$tipoDocumento,
            'html'=>$html,


        ));

    }

    function verificarTipoDocumento($nombreDocumento){

        $carpeta=$this->getParameter('uploads');
        $exito=true;
        $msj="";
        $tipoDocumento="";

        $tiposDocumentos['inventarioBodega']=array('MODELO','CODIGO','COSTO','PRECIO','PRECIO DISTRIBUIDOR','PRECIO SUBDISTRIBUIDOR','PRECIO ESPECIAL');

        $productosActualizados=0;
        $productosNoEncontrados=0;
        $productosNoEncontradosData=[];


        /*$client = new \Redis();

        $client->connect('127.0.0.1', 6379);
        $pool = new \Cache\Adapter\Redis\RedisCachePool($client);
        $simpleCache = new \Cache\Bridge\SimpleCache\SimpleCacheBridge($pool);

        \PhpOffice\PhpSpreadsheet\Settings::setCache($simpleCache);*/
        try{
            $documento1=$nombreDocumento;

            $directory1=$carpeta.DIRECTORY_SEPARATOR.$documento1;

            /**  Identify the type of $inputFileName  **/
            $inputFileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($directory1);

            /**  Create a new Reader of the type that has been identified  **/
            $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($inputFileType);

            /**  Load $inputFileName to a Spreadsheet Object  **/
            $spreadsheet1 = $reader->load($directory1);

            /**  Convert Spreadsheet Object to an Array for ease of use  **/
            // $schdeules = $spreadsheet->getActiveSheet()->toArray();



            /* $reader = \PHPExcel_IOFactory::createReaderForFile($directory1);
             $reader->setReadDataOnly(true);*/

            //  $spreadsheet1 = $reader->load($directory1);
            // $spreadsheet1 =  IOFactory::load($directory1);

            $sheets = $spreadsheet1->getAllSheets();



            $encabezados = [];
            $numeroRows=6;
            $rows = [];
            $iteraciones=count($sheets);

            for ($i = 0; $i < $iteraciones; $i++) {

                $worksheet = $spreadsheet1->getSheet($i);
                unset($rows);
                $rows=[];
                ///tomamos la primera celda de la hoja
                foreach ($worksheet->getRowIterator() AS $row) {
                    $cellIterator = $row->getCellIterator();
                    $cellIterator->setIterateOnlyExistingCells(FALSE); // This loops through all cells,
                    $cells = [];
                    foreach ($cellIterator as $cell) {
                        $valor="";
                        if(gettype($cell->getValue()) =="object"){
                            $valor=$cell->getValue()->__toString();
                        }else{
                            $valor=$cell->getValue();
                        }
                        //   echo "<br>".$valor;
                        $cells[] = strtoupper($valor);
                    }
                    $rows[] = $cells;
                    break;
                }
                //fin de tomar las celdfas
                //verificamo que el documento tenga la estructura correcta
                foreach ($tiposDocumentos as $key => $td) {

                    $result=array_diff($td,$rows[0]);
                    if(count($result)==0){
                        $tipoDocumento=$key;
                    }
                }
                if($tipoDocumento !=""){


                    //aqui procesamos


                    $res=$this->procesarDocumento($nombreDocumento,$tipoDocumento,$i);



                    if(!$res['exito']){
                        $msj.="<br> Algo salió mal al procesar la hoja ".$i." : ".$res['msj'];
                        $exito=false;
                    }else{
                        //si hay por lo menos algo bien
                        $msj.="<br> la hoja ".$i." : se ha procesado correctamente";
                        $productosActualizados+=$res['productosActualizados'];
                        $productosNoEncontrados+=$res['productosNoEncontrados'];
                        if(count($res['productosNoEncontradosData']) > 0){
                            foreach ($res['productosNoEncontradosData'] as $keyProducto =>$producto){
                                $productosNoEncontradosData[]=$producto;
                            }
                        }

                        // $productosNoEncontradosData[]=$res['productosNoEncontradosData'];
                    }
                }else{
                    $msj.= "hoja ".$i." es un Tipo de Documento desconocido, Revise el formato de las columnas";
                    $exito=false;
                }



            }//fin de for hojas




            /*    echo "<pre>";
                    var_dump($productosNoEncontradosData);
                echo "<pre>";*/



        }catch (\Exception $e) {
            $msj=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();

        }
        //  echo $msj;

        return $this->json(array('msj'=>$msj,'exito'=>$exito,
            'productosActualizados'=>$productosActualizados,
            'productosNoEncontrados'=>$productosNoEncontrados,
            'productosNoEncontradosData'=>$productosNoEncontradosData,


            'tipoDocumento'=>$tipoDocumento));

    }

    function procesarDocumento($nombreDocumento,$tipoDocumento,$numHoja=0){

        $carpeta=$this->getParameter('uploads');
        $exito=false;
        $todoEsValido=true;
        $msj="";
        $em=$this->getDoctrine()->getManager();

        $productosActualizados=0;
        $productosNoEncontrados=0;
        $productosNoEncontradosData=[];
        try{
            $documento=$nombreDocumento;


            $directory=$carpeta.DIRECTORY_SEPARATOR.$documento;

            /**  Identify the type of $inputFileName  **/
            $inputFileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($directory);

            /**  Create a new Reader of the type that has been identified  **/
            $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($inputFileType);

            /**  Load $inputFileName to a Spreadsheet Object  **/
            $spreadsheet = $reader->load($directory);


            // $reader = \PHPExcel_IOFactory::createReaderForFile($directory);
            // $reader->setReadDataOnly(true);
            //  $spreadsheet = $reader->load($directory);
            //var_dump($spreadsheet);
            //  $spreadsheet= IOFactory::load($directory);

            $worksheet = $spreadsheet->getSheet($numHoja);

            //   var_dump($worksheet);

            $highestRow = $worksheet->getHighestRow(); // e.g. 10

            $worksheet = $spreadsheet->getActiveSheet();
            $highestColumn = $worksheet->getHighestColumn(); // e.g 'F'
            // $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn); // e.g. 5

            $ObjCategoria=$this->getDoctrine()->getRepository(Categoria::class)->findOneBy(['idcategoria'=>10,'status'=>"1"]);
            $ObjProveeder=$this->getDoctrine()->getRepository(Proveedor::class)->findOneBy(['idproveedor'=>1,'status'=>"1"]);
            /* for ($row = 2; $row <= $highestRow; ++$row) {
                 echo '<tr>' . PHP_EOL;
                 for ($col = 'A'; $col != $highestColumn; ++$col) {
                     echo '<td>' .
                         $worksheet->getCell($col . $row)
                             ->getValue() .
                         '</td>' . PHP_EOL;
                 }
                 echo '</tr>' . PHP_EOL;
             }
 */
            for ($row = 2; $row <= $highestRow; ++$row) {

                $precio=0;
                $costo=0;


                $modelo = $worksheet->getCell('A'.$row)->getValue();

                if (gettype($modelo) == "object") {
                    $modelo = trim($modelo->__toString());
                }
                $codigoBarras = $worksheet->getCell('B'.$row)->getValue();

                if (gettype($codigoBarras) == "object") {
                    $codigoBarras = $codigoBarras->__toString();
                }

                $costo=0;
                $costo = $worksheet->getCell("C".$row)->getValue();
                //   var_dump($costo);
                if (gettype($costo) == "object") {
                    $costo = $costo->__toString();
                }
                if($costo== null){
                    $costo=0;
                }
                //quitamos siugno de pesos si tiene
                $costo = preg_replace('([^0-9 .])', '', $costo);

                $precio=0;
                $precio = $worksheet->getCell("D".$row)->getValue();

                if (gettype($precio) == "object") {
                    $precio = $precio->__toString();
                }
                if($precio== null){
                    $precio=0;
                }
                $precio = preg_replace('([^0-9 .])', '', $precio);
                //buscamos el almacen

                //--------------------------------------------------------------
                $precioDistribuidor=0;
                $precioDistribuidor = $worksheet->getCell("E".$row)->getValue();

                if (gettype($precioDistribuidor) == "object") {
                    $precioDistribuidor = $precioDistribuidor->__toString();
                }
                if($precioDistribuidor== null){
                    $precioDistribuidor=0;
                }
                $precioDistribuidor = preg_replace('([^0-9 .])', '', $precioDistribuidor);

                //--------------------------------------------------------------
                $precioSubdistribuidor=0;
                $precioSubdistribuidor = $worksheet->getCell("F".$row)->getValue();

                if (gettype($precioSubdistribuidor) == "object") {
                    $precioSubdistribuidor = $precioSubdistribuidor->__toString();
                }
                if($precioSubdistribuidor== null){
                    $precioSubdistribuidor=0;
                }
                $precioSubdistribuidor = preg_replace('([^0-9 .])', '', $precioSubdistribuidor);

                //--------------------------------------------------------------
                $precioEspecial=0;
                $precioEspecial = $worksheet->getCell("G".$row)->getValue();

                if (gettype($precioEspecial) == "object") {
                    $precioEspecial = $precioEspecial->__toString();
                }
                if($precioEspecial== null){
                    $precioEspecial=0;
                }
                $precioEspecial = preg_replace('([^0-9 .])', '', $precioEspecial);


                if($modelo){


                    $ObjProducto=$this->getDoctrine()->getRepository(Producto::class)->findOneBy(['modelo'=>$modelo,'status'=>"1"]);
                    //vemos si existe por codigo
                    if(!$ObjProducto){
                        $ObjProducto=$this->getDoctrine()->getRepository(Producto::class)->findOneBy(['codigobarrasuniversal'=>$codigoBarras,'status'=>"1"]);


                    }
                    //si no fue encontrado entonces lo creamos
                    if(!$ObjProducto) {
                        $productosNoEncontrados++;
                        $productosNoEncontradosData[]=$modelo;
                        $todoEsValido=false;
                    }else{

                        $ObjProducto->setCosto($costo);
                        $ObjProducto->setPrecio($precio);
                        $ObjProducto->setPreciodistribuidor($precioDistribuidor);
                        $ObjProducto->setPreciosubdistribuidor($precioSubdistribuidor);
                        $ObjProducto->setPrecioespecial($precioEspecial);
                        $ObjProducto->setModificacion(new \DateTime("now"));
                        $productosActualizados++;

                        //buscamos la bodega primer

                        $em->persist($ObjProducto);



                    }


                    //agregamos el producto
                    //vemos si existe por modelo




                }

                // exit();


            }
            if($todoEsValido == true){
                $em->flush();
            }else{
                $productosActualizados=0;
            }





            $exito=true;

        }catch (\Exception $e) {
            $msj=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();

        }

        return array('msj'=>$msj,'exito'=>$exito,

            'productosActualizados'=>$productosActualizados,
            'productosNoEncontrados'=>$productosNoEncontrados,
            'productosNoEncontradosData'=>$productosNoEncontradosData,


        );
    }
}
