<?php

namespace App\Controller\InventoryModule;

use App\Entity\Cargainventariolog;
use App\Entity\Categoria;
use App\Entity\Marca;
use App\Entity\Proveedor;
use App\Entity\Stock;
use App\Entity\Sucursal;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\IReadFilter;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Reader;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Component\HttpFoundation\File\UploadedFile;


use App\Entity\Producto;

/**
 * @Route("/inventario")
 */
class CargaInventarioBodegaController extends AbstractController
{

    public $stock;

    /**
     * @Route("/cargar-inventario-bodega/", name="r_Inventario_carga_inventario_bodega")
     */
    public function cargaInvenatarioBodega(): Response
    {
        $carpeta_front = $this->getParameter('uploads_carga_masiva_inventario');
        return $this->render('carga_inventario_bodega/carga_inventario_bodega.html.twig', [
            'uploads_front' => $carpeta_front
        ]);
    }

    /**
     * @Route("/cargar-inventario-bodega/subir-documento", name="cargar-inventario-bodega-subir-documento")
     */
    public function subirDocumento()
    {
        ini_set('memory_limit', '2024M'); // or you could use 1G
        $valid = false;
        $msj = "";
        $documentName = "";
        $carpeta = $this->getParameter('uploads_carga_masiva_inventario');
        $idVista = "";
        $tipoDocumento = "";
        $nombreExcelCodigos = "";

        if (isset($_FILES['file1']['name'])) {
            $idVista = $_POST['id'];
            $nombreDocumento = "";
            $name = $_FILES['file1']['name'];
            $nombreDocumento = explode(".", $_FILES['file1']['name']);
            array_pop($nombreDocumento);
            $nombreDocumento = implode(".", $nombreDocumento);
            $productosActualizados = 0;
            $productosNoEncontradosData = [];
            $productosNoEncontrados = 0;
            $tmpName  = $_FILES['file1']['tmp_name'];
            $error    = $_FILES['file1']['error'];
            $ext      = strtolower(pathinfo($name, PATHINFO_EXTENSION));

            if ($ext == "xls" || $ext == "xlsx") {
                $documentName = md5(uniqid()) . '.' . $ext;
                $targetPath =  $carpeta . DIRECTORY_SEPARATOR . $documentName;
                switch ($error) {
                    case UPLOAD_ERR_OK:

                        if (move_uploaded_file($tmpName, $targetPath)) {
                            $result = $this->verificarTipoDocumento($documentName);

                            $responseVerificarDocumento = json_decode($result->getContent());
                            $msj = $responseVerificarDocumento->msj;
                            $valid = $responseVerificarDocumento->exito;
                            if ($responseVerificarDocumento->exito == true) {

                                $tipoDocumento = $responseVerificarDocumento->tipoDocumento;
                                $productosActualizados = $responseVerificarDocumento->productosActualizados;
                                $productosNoEncontradosData = $responseVerificarDocumento->productosNoEncontradosData;
                                $productosNoEncontrados = $responseVerificarDocumento->productosNoEncontrados;
                                $nombreExcelCodigos = $responseVerificarDocumento->nombreExcelCodigos;
                            }
                        }

                        break;
                    case UPLOAD_ERR_INI_SIZE:
                        $msj = 'The uploaded file exceeds the upload_max_filesize directive in php.ini.';
                        break;
                    case UPLOAD_ERR_FORM_SIZE:
                        $msj = 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form.';
                        break;
                    case UPLOAD_ERR_PARTIAL:
                        $msj = 'The uploaded file was only partially uploaded.';
                        break;
                    case UPLOAD_ERR_NO_FILE:
                        $msj = 'No file was uploaded.';
                        break;
                    case UPLOAD_ERR_NO_TMP_DIR:
                        $msj = 'Missing a temporary folder. Introduced in PHP 4.3.10 and PHP 5.0.3.';
                        break;
                    case UPLOAD_ERR_CANT_WRITE:
                        $msj = 'Failed to write file to disk. Introduced in PHP 5.1.0.';
                        break;
                    case UPLOAD_ERR_EXTENSION:
                        $msj = 'File upload stopped by extension. Introduced in PHP 5.2.0.';
                        break;
                    default:
                        $msj = 'Unknown error';
                        break;
                }
            } else {
                $msj = "Tipo de Formato Inválido";
            }
        } else {
            $msj = "Debe seleccionar un documento";
        }

        $html = $this->renderView('carga_inventario_bodega/respuesta-carga-inventario-bodega.html.twig', [
            'msj' => $msj, 'exito' => $valid,
            'productosNoEncontradosData' => $productosNoEncontradosData,
            'productosNoEncontrados' => $productosNoEncontrados,
            'productosActualizados' => $productosActualizados,
            'nombreExcelCodigos' => $nombreExcelCodigos
        ]);

        return $this->json(array(
            'msj' => $msj, 'exito' => $valid,
            'idVista' => $idVista, 'nombreDocumento' => $documentName, 'tipoDocumento' => $tipoDocumento,
            'html' => $html,

        ));
    }

    function verificarTipoDocumento($nombreDocumento)
    {
        $carpeta = $this->getParameter('uploads_carga_masiva_inventario');
        $exito = true;
        $msj = "";
        $tipoDocumento = "";
        $tiposDocumentos['inventarioBodega'] = array('MODELO', 'CODIGO BARRAS', 'CANTIDAD', 'ALMACEN', 'SERIE', 'RFC PROVEEDOR', 'NÚMERO FACTURA');
        $productosActualizados = 0;
        $productosNoEncontrados = 0;
        $productosNoEncontradosData = [];
        $nombreExcelCodigos = "";
        $documento1 = $nombreDocumento;
        $directory1 = $carpeta . DIRECTORY_SEPARATOR . $documento1;
        $reader = IOFactory::createReaderForFile($directory1);
        $reader->setReadDataOnly(true);
        $spreadsheet1 = $reader->load($directory1);
        $sheets = $spreadsheet1->getAllSheets();
        $rows = [];
        $iteraciones = count($sheets);

        for ($i = 0; $i < $iteraciones; $i++) {

            $worksheet = $spreadsheet1->getSheet($i);
            unset($rows);
            $rows = [];
            ///tomamos la primera celda de la hoja
            foreach ($worksheet->getRowIterator() as $row) {
                $cellIterator = $row->getCellIterator();
                $cellIterator->setIterateOnlyExistingCells(FALSE); // This loops through all cells,
                $cells = [];
                foreach ($cellIterator as $cell) {
                    $valor = "";
                    if (gettype($cell->getValue()) == "object") {
                        $valor = $cell->getValue()->__toString();
                    } else {
                        $valor = $cell->getValue();
                    }
                    $cells[] = strtoupper($valor);
                }
                $rows[] = $cells;
                break;
            }
            //fin de tomar las celdfas
            //verificamo que el documento tenga la estructura correcta
            foreach ($tiposDocumentos as $key => $td) {

                $result = array_diff($td, $rows[0]);
                if (count($result) == 0) {
                    $tipoDocumento = $key;
                }
            }

            if ($tipoDocumento != "") {

                $res = $this->procesarDocumento($nombreDocumento, $tipoDocumento, $i);


                if (!$res['exito']) {
                    $msj .= "<br> Algo salió mal al procesar la hoja " . $i . " : " . $res['msj'];
                    $exito = false;
                } else {
                    //si hay por lo menos algo bien
                    $msj .= "<br> la hoja " . $i . " : se ha procesado correctamente";
                    $nombreExcelCodigos = $res['nombreExcelCodigos'];
                    $productosActualizados += $res['productosActualizados'];
                    $productosNoEncontrados += $res['productosNoEncontrados'];
                    if (count($res['productosNoEncontradosData']) > 0) {
                        foreach ($res['productosNoEncontradosData'] as $keyProducto => $producto) {
                            $productosNoEncontradosData[] = $producto;
                        }
                    }
                }
            } else {
                $msj .= "hoja " . $i . " es un Tipo de Documento desconocido, Revise el formato de las columnas";
                $exito = false;
            }
        } //fin de for hojas

        return $this->json(array(
            'msj' => $msj, 'exito' => $exito,
            'productosActualizados' => $productosActualizados,
            'productosNoEncontrados' => $productosNoEncontrados,
            'productosNoEncontradosData' => $productosNoEncontradosData,
            'nombreExcelCodigos' => $nombreExcelCodigos,
            'tipoDocumento' => $tipoDocumento
        ));
    }


    /// <summary>
    /// This Void gets the selected index using a comparison by the selected ID.
    /// </summary>
    /// <returns>Returns the selected index relative to the unlocked index. </returns>
    /// <param name="nombreDocumento"> nombre del documento a procesar. </param>
    function procesarDocumento($nombreDocumento, $tipoDocumento, $numHoja = 0)
    {
        ini_set('max_execution_time', '6000');
        $carpeta = $this->getParameter('uploads_carga_masiva_inventario');
        $exito = false;
        $todoEsValido = true;
        $msj = "";
        $em = $this->getDoctrine()->getManager();
        $nombreExcelCodigos = "";
        $productosActualizados = 0;
        $productosNoEncontrados = 0;
        $productosNoEncontradosData = [];
        $stockDadosAlta = null;
        $documento = "";
        $Usuarioconectado = $this->getUser();
        $columnValues = [];
        $documento = $nombreDocumento;
        $directory = $carpeta . DIRECTORY_SEPARATOR . $documento;


        /**  Identify the type of $inputFileName  **/
        $inputFileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($directory);

        /**  Create a new Reader of the type that has been identified  **/
        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($inputFileType);

        /**  Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = $reader->load($directory);
        $worksheet = $spreadsheet->getSheet($numHoja);
        $highestRow = $worksheet->getHighestRow(); // e.g. 10
        $highestColumn = $worksheet->getHighestColumn(); // e.g 'F'

        for ($row = 2; $row <= $highestRow; ++$row) {

            $modelo = $worksheet->getCell("A" . $row)->getValue();

            if (gettype($modelo) == "object") {
                $modelo = trim($modelo->__toString());
            }
            $codigoBarras = $worksheet->getCell("B" . $row)->getValue();

            if (gettype($codigoBarras) == "object") {
                $codigoBarras = trim($codigoBarras->__toString());
            }

            $cantidad = $worksheet->getCell("C" . $row)->getValue();

            if (gettype($cantidad) == "object") {
                $cantidad = trim($cantidad->__toString());
            }
            $almacen = $worksheet->getCell("D" . $row)->getValue();

            if (gettype($almacen) == "object") {
                $almacen = trim($almacen->__toString());
            }

            $serie = $worksheet->getCell("E" . $row)->getValue();
            if (gettype($serie) == "object") {
                $serie = trim($serie->__toString());
            }
            $rfcProveedor = $worksheet->getCell("F" . $row)->getValue();
            if (gettype($serie) == "object") {
                $rfcProveedor = trim($rfcProveedor->__toString());
            }
            $numeroFactura = $worksheet->getCell("G" . $row)->getValue();
            if (gettype($numeroFactura) == "object") {
                $numeroFactura = trim($numeroFactura->__toString());
            }
            $trimmedString = trim($rfcProveedor);

            if (empty($rfcProveedor)) {
                continue;
            }

            $busquedaporCodigoModelo = "";

            $ObjProveedor = $em->getRepository(Proveedor::class)->findOneBy(['rfc' => $trimmedString, 'status' => "1"]);

            if ($ObjProveedor) {
                if ($modelo != "" || $codigoBarras != "") {
                    if ((is_int($cantidad) || is_float($cantidad)) && $almacen != "") {
                        if ($modelo != "") {
                            $busquedaporCodigoModelo = "modelo";
                        } else if ($codigoBarras != "") {
                            $busquedaporCodigoModelo = "codigoBarras";
                        } else {
                            throw new \Exception('Un producto no tiene modelo ni código de barras ' . $modelo . " almacen " . $almacen . " cantidad " . $cantidad);
                        }
                        $stockDadosAlta[$almacen][] = [
                            'almacen' => trim($almacen),
                            'cantidad' => trim($cantidad),
                            'codigoBarras' => trim($codigoBarras),
                            'modelo' => trim($modelo),
                            'busquedaporCodigoModelo' => trim($busquedaporCodigoModelo),
                            'serie' => trim($serie),
                            'numeroFactura' => trim($numeroFactura)
                        ];
                    } else {
                        throw new \Exception('No se encontró el almacen o hay un valor de cantidad incorrecto modelo ' . $modelo . " almacen " . $almacen . " cantidad " . $cantidad);
                    }
                }
            } else {
                throw new \Exception('No se encontró el proveedor, registrelo primero');
            }
        }

        $stockDadosAltaConcentrado = [];

        foreach ($stockDadosAlta as $keyProd => $productos) {

            $almacen = $keyProd;

            //hacemos por sucursalesñ
            foreach ($productos as $keyProducto => $producto) {
                $modelo = $producto['modelo'];
                $codigoBarras = $producto['codigoBarras'];
                $cantidad = $producto['cantidad'];
                $busquedaporCodigoModelo = $producto['busquedaporCodigoModelo'];
                $serie = trim($producto['serie']);
                $numeroFactura = trim($producto['numeroFactura']);

                //ver si la marca existe

                $ObjSucursal = $em->getRepository(Sucursal::class)->findOneBy(['codigo' => $almacen, 'status' => "1"]);
                $almacenError = $producto;
                if ($ObjSucursal) {
                    if ($ObjSucursal->getTipo() == "bodega") {
                        if ($busquedaporCodigoModelo == "modelo") {
                            $ObjProducto = $em->getRepository(Producto::class)->findOneBy(['modelo' => $modelo, 'status' => "1", 'tipoproducto' => "1"]);
                        } else {
                            $ObjProducto = $em->getRepository(Producto::class)->findOneBy(['codigobarrasuniversal' => $codigoBarras, 'status' => "1"]);
                        }

                        $masivoUnico = $ObjProducto->getMasivounico();
                        $tipoProducto = $ObjProducto->getTipoproducto();
                        $codigoBarras = $ObjProducto->getCodigobarrasuniversal(); //parano pnerl en el escel

                        if ($masivoUnico != "1" && $serie != "") {
                            throw new \Exception('El producto ' . $modelo . ' no puede tener serie porque es masivo ');
                        }
                        if ($tipoProducto != "1") {
                            throw new \Exception('El producto ' . $modelo . ' no puede tener stock porque es un servicio ');
                        }
                        if (!$ObjProducto) {
                            $productosNoEncontrados++;
                            $productosNoEncontradosData[] = "modelo: " . $modelo . " codigo " . $codigoBarras;
                            $todoEsValido = false;
                        } else {

                            //unico es 1
                            //masivo 2

                            $sku = "";

                            if ($masivoUnico == "1") { //es unico
                                for ($i = 0; $i < $cantidad; $i++) {
                                    //creamos el codigo de cbarra por stock
                                    $sku = "";
                                    $sku = "151" . mt_rand(100000000, 999999999);
                                    //vemos si ya existe el codgio de barra si no lo cambiamos

                                    $ObjStockAuxliiar = $em->getRepository(Stock::class)->findOneBy(['codigobarras' => $sku, 'status' => "1"]);
                                    if ($ObjStockAuxliiar) {
                                        //si existe hayq ue cambiarlo
                                        $sku = "151" . mt_rand(100000000, 999999999);
                                        unset($ObjStockAuxliiar);
                                    }

                                    $stockDadosAltaConcentrado[] = [
                                        'almacen' => $almacen,
                                        'sku' => $sku,
                                        'cantidad' => 1,
                                        'modelo' => $modelo,
                                        'idproducto' => $ObjProducto->getIdproducto(),
                                        'marca' => $ObjProducto->getMarcaIdmarca()->getNombre(),
                                        'descripcion' => $ObjProducto->getDescripcion(),
                                        'precio' => $ObjProducto->getPrecio(),
                                        'idsucursal' => $ObjSucursal->getIdsucursal(),
                                        'sucursal' => $ObjSucursal->getNombre(),
                                        'Producto' => $ObjProducto,
                                        'Sucursal' => $ObjSucursal,
                                        'masivoUnico' => $masivoUnico,
                                        'serie' => $serie,
                                        'codigoBarras' => $codigoBarras,
                                        'numeroFactura' => $numeroFactura,
                                        'iva' => $ObjSucursal->getPorcentajeiva(),
                                        'precioConIva' => $ObjProducto->getPrecio() * (1 + (.01 * $ObjSucursal->getPorcentajeiva())),

                                    ];
                                }
                            } else {
                                $stockDadosAltaConcentrado[] = [
                                    'almacen' => $almacen,
                                    'sku' => $sku,
                                    'cantidad' => $cantidad,
                                    'modelo' => $modelo,
                                    'idproducto' => $ObjProducto->getIdproducto(),
                                    'marca' => $ObjProducto->getMarcaIdmarca()->getNombre(),
                                    'descripcion' => $ObjProducto->getDescripcion(),
                                    'precio' => $ObjProducto->getPrecio(),
                                    'idsucursal' => $ObjSucursal->getIdsucursal(),
                                    'sucursal' => $ObjSucursal->getNombre(),
                                    'Producto' => $ObjProducto,
                                    'Sucursal' => $ObjSucursal,
                                    'masivoUnico' => $masivoUnico,
                                    'serie' => $serie,
                                    'codigoBarras' => $codigoBarras,
                                    'numeroFactura' => $numeroFactura,
                                    'iva' => $ObjSucursal->getPorcentajeiva(),
                                    'precioConIva' => $ObjProducto->getPrecio() * (1 + (.01 * $ObjSucursal->getPorcentajeiva())),

                                ];
                            }
                        }
                    } else {
                        throw new \Exception('El almacén no puede ser una sucursal ' . $almacen);
                    }
                } else {
                    throw new \Exception('No se encontró el almacén ' . $almacen);
                }

                //agregamos el producto
                //vemos si existe por modelo

            }
            $ObjProducto = null;
            $ObjSucursal = null;
            if ($todoEsValido) {
                //ya con el concetrado ya relizamos el query
                foreach ($stockDadosAltaConcentrado as $producto) {
                    $productosActuales = 0;
                    $Producto = null;
                    $Sucursal = null;
                    $Stock = null;
                    if ($producto['masivoUnico'] != "1") {
                        $query = $em->createQuery(
                            'SELECT  s
                                   FROM App\Entity\Stock s
                                   inner join s.productoIdproducto p 
                                   inner join s.sucursalIdsucursal sucursal
                                   where sucursal.idsucursal=:idsucursal and p.idproducto=:idproducto AND p.masivounico!=:masivounico
                                   '
                        )->setParameters([
                            'idproducto' => $producto['idproducto'], 'masivounico' => "1",
                            'idsucursal' => $producto['idsucursal']
                        ]);

                        $Stock = $query->getResult();
                    }


                    if ($Stock) {
                        if (count($Stock) > 1) {

                            throw new \Exception("Este producto tiene más de un registro en  " . $producto['sucursal'] . " modelo " . $producto['modelo'] . " codigo barras " . $producto['codigoBarras']);
                        } else {
                            $Stock = $Stock[0];
                        }
                    } else {
                        $Stock = new Stock();
                        $Stock->setCreacion(new \DateTime("now"));
                        $Stock->setProductoIdproducto($producto['Producto']);
                        $Stock->setSucursalIdsucursal($producto['Sucursal']);
                        $Stock->setSerie($producto['serie']);
                    }

                    $productosActuales = $Stock->getCantidad();
                    $Stock->setModificacion(new \DateTime("now"));
                    $Stock->setCantidad($productosActuales + $producto['cantidad']);
                    $Stock->setCodigobarras($producto['sku']);

                    $columnValues[] = [$producto['sku'], $producto['serie'], $producto['codigoBarras'], $producto['marca'], $producto['modelo'], $producto['descripcion'], $producto['cantidad'], $producto['precio'], $producto['iva'], $producto['precioConIva']];

                    $Stock->setStatus("1");
                    $em->persist($Stock);
                    //carga inventario log

                    $Cargainventariolog = new Cargainventariolog();
                    $Cargainventariolog->setCantidad($producto['cantidad']);
                    $Cargainventariolog->setFecha(new \DateTime("now"));
                    $Cargainventariolog->setConexcel("1");
                    $Cargainventariolog->setArchivoexcel($documento);
                    $Cargainventariolog->setStockIdstock($Stock);
                    $Cargainventariolog->setUsuarioIdusuario($Usuarioconectado);
                    $Cargainventariolog->setProveedorIdproveedor($ObjProveedor);
                    $Cargainventariolog->setNumerofactura($numeroFactura);
                    $em->persist($Cargainventariolog);
                    $tipoProducto = "";
                    $productosActualizados++;
                }
            } else {
                $msj = "Revise que el formato este llenado correctamente";
            }
        }

        $em->flush();
        $exito = true;

        //si todo esta bien generamos un excel para imrpimir la etiwuetas
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        //$sheet->setCellValue('A1', 'Browser characteristics')->mergeCells('A1:D1');

        // Set column names
        $columnNames = [
            'SKU',
            'SERIE',
            'Código de Barras',
            'Marca',
            'Modelo',
            'Descripción',
            'Cantidad',
            'Precio ',
            'Porcentaje IVA ',
            'Precio con IVA'
        ];
        $columnLetter = 'A';
        foreach ($columnNames as $columnName) {
            // Allow to access AA column if needed and more

            $sheet->setCellValue($columnLetter . '1', $columnName);
            $columnLetter++;
        }

        $i = 2;

        foreach ($columnValues as $columnValue) {
            $columnLetter = 'A';
            foreach ($columnValue as $value) {
                $sheet->getColumnDimension($columnLetter)->setAutoSize(true);
                if ($columnLetter == "H" || $columnLetter == "J") { //precio y prwecio con iva

                    $sheet->setCellValue($columnLetter . $i, "$" . round($value, 2));
                    $sheet->getStyle($columnLetter . $i)->setQuotePrefix(true);
                } else if ($columnLetter == "I") { //iva ocentaje
                    $sheet->setCellValue($columnLetter . $i, (floatval($value)) . "%");
                    $sheet->getStyle($columnLetter . $i)->setQuotePrefix(true);
                } else {
                    $sheet->setCellValue($columnLetter . $i, $value);
                }


                $columnLetter++;
            }
            $i++;
        }

        // In this case, we want to write the file in the public directory

        $nombreExcelCodigos = 'codigos-' . date('m-d-Y_hia') . '-' . rand() . rand() . '.xlsx';
        $excelFilepath = 'codigos/' . $nombreExcelCodigos;
        $writer = new Xlsx($spreadsheet);
        $writer->save($excelFilepath);
        return array(
            'msj' => $msj, 'exito' => $exito,
            'productosActualizados' => $productosActualizados,
            'productosNoEncontrados' => $productosNoEncontrados,
            'productosNoEncontradosData' => $productosNoEncontradosData,
            'nombreExcelCodigos' => $nombreExcelCodigos,

        );
    }
}
