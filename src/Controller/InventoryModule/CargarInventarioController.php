<?php

namespace App\Controller\InventoryModule;

use App\Entity\Categoria;
use App\Entity\Clase;
use App\Entity\Empresa;
use App\Entity\Marca;
use App\Entity\Medida;
use App\Entity\Proveedor;
use App\Entity\Unidadmedida;
use Safe\DateTime;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\IReadFilter;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Reader;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use App\Entity\Producto;
/**
 * @Route("/inventario")
 */
class CargarInventarioController extends AbstractController
{
    /**
     * @Route("/cargar-inventario", name="cargar_inventario")
     */
    public function index(): Response
    {
        $carpeta_front=$this->getParameter('uploads_front');
        return $this->render('cargar_inventario/index.html.twig', [
            'uploads_front' => $carpeta_front
        ]);
    }


    /**
     * @Route("/cargar-inventario/subir-documento", name="cargar-inventario-subir-documento")
     */
    public function subirDocumento()
    {
        ini_set('memory_limit', '2024M'); // or you could use 1G
        $valid = false;
        $msj="";
        //para ver en que carpeta se guardarán
        $documentName="";
        $carpeta=$this->getParameter('uploads');
        //este id es el que viene de la vista y sirve para mostar el mensaje de eerror en el documento que sea necesario
        $idVista="";
        $tipoDocumento="";
        $beneficiariosenBlanco=0;
        $productosNuevos=0;
        $productosOmitidos=0;
        $productosOmitidosData=[];
        $productosActualizados=0;
        try{

            if(isset($_FILES['file1']['name'])){
                $idVista=$_POST['id'];
                $nombreDocumento="";
                $name=$_FILES['file1']['name'];
                $nombreDocumento=explode(".",$_FILES['file1']['name']);
                $mes=$nombreDocumento[1];
                $anio=$nombreDocumento[0];
                array_pop($nombreDocumento);
                $nombreDocumento=implode(".",$nombreDocumento);
                //sacamos el año
                $tmpName  = $_FILES['file1']['tmp_name'];
                $error    = $_FILES['file1']['error'];
                $size     = $_FILES['file1']['size'];
                $ext      = strtolower(pathinfo($name, PATHINFO_EXTENSION));


                if($ext=="xls" || $ext=="xlsx"){
                    $documentName=md5(uniqid()).'.'.$ext;

                    $targetPath =  $carpeta.DIRECTORY_SEPARATOR.$documentName;
                    switch ($error) {
                        case UPLOAD_ERR_OK:

                            //echo "rl ".$targetPath;9
                            if(move_uploaded_file($tmpName,$targetPath)){
                                $result=$this->verificarTipoDocumento($documentName);

                                $responseVerificarDocumento=json_decode($result->getContent());

                      //               var_dump($responseVerificarDocumento);


                                if($responseVerificarDocumento->exito==true){


                                    if($responseVerificarDocumento->exito==true){
                                        $valid=true;
                                        $productosNuevos=$responseVerificarDocumento->productosNuevos;
                                        $productosOmitidos=$responseVerificarDocumento->productosOmitidos;
                                        $productosOmitidosData=$responseVerificarDocumento->productosOmitidosData;
                                        $productosActualizados=$responseVerificarDocumento->productosActualizados;

                                        $msj=$responseVerificarDocumento->msj;
                                    }else{
                                        $msj=$responseVerificarDocumento->msj;
                                    }

                                }else{
                                       $msj=$responseVerificarDocumento->msj;
                                }

                            }


                            break;
                        case UPLOAD_ERR_INI_SIZE:
                            $msj = 'The uploaded file exceeds the upload_max_filesize directive in php.ini.';
                            break;
                        case UPLOAD_ERR_FORM_SIZE:
                            $msj = 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form.';
                            break;
                        case UPLOAD_ERR_PARTIAL:
                            $msj = 'The uploaded file was only partially uploaded.';
                            break;
                        case UPLOAD_ERR_NO_FILE:
                            $msj = 'No file was uploaded.';
                            break;
                        case UPLOAD_ERR_NO_TMP_DIR:
                            $msj = 'Missing a temporary folder. Introduced in PHP 4.3.10 and PHP 5.0.3.';
                            break;
                        case UPLOAD_ERR_CANT_WRITE:
                            $msj = 'Failed to write file to disk. Introduced in PHP 5.1.0.';
                            break;
                        case UPLOAD_ERR_EXTENSION:
                            $msj = 'File upload stopped by extension. Introduced in PHP 5.2.0.';
                            break;
                        default:
                            $msj = 'Unknown error';
                            break;
                    }
                }else{
                    $msj="Tipo de Formato Inválido";
                }


            }else{
                $msj="Debe seleccionar un documento";
            }


        }catch (\Exception $e) {
            $msj=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();

        }
        //  echo $msj;

        //obtenemos una vista de lo que se procesó
        $html= $this->renderView('cargar_inventario/respuesta-carga-inventario.html.twig', [
            'productosNuevos'=>$productosNuevos,
            'productosOmitidos'=>$productosOmitidos,
            'productosOmitidosData'=>$productosOmitidosData,
            'productosActualizados'=>$productosActualizados,
            'msj'=>$msj
        ]);

        return $this->json(array(
            'msj'=>$msj,
            'exito'=>$valid,
            'idVista'=>$idVista,
            'nombreDocumento'=>$documentName,
            'tipoDocumento'=>$tipoDocumento,
            'html'=>$html,
        ));

    }

    function verificarTipoDocumento($nombreDocumento){
        $carpeta=$this->getParameter('uploads');
        $exito=true;
        $msj="";
        $tipoDocumento="";

        $tiposDocumentos['inventario']=array('MARCA','MODELO','CODIGO DE COLOR','COLOR','CODIGO BARRAS UNIVERSAL','TIPO MATERIAL','DESCRIPCION','SOBREPUESTO','MASIVO UNICO','CATEGORIA','SUBCATEGORIA','UNIDAD DE MEDIDA','MEDIDA','EMPRESA');
      //  echo "entra 11";


           $productosNuevos=0;
           $productosActualizados=0;
        $productosOmitidosData=[];
        $productosOmitidos=0;
        /*$client = new \Redis();

        $client->connect('127.0.0.1', 6379);
        $pool = new \Cache\Adapter\Redis\RedisCachePool($client);
        $simpleCache = new \Cache\Bridge\SimpleCache\SimpleCacheBridge($pool);

        \PhpOffice\PhpSpreadsheet\Settings::setCache($simpleCache);*/
        try{
            $documento1=$nombreDocumento;

            $directory1=$carpeta.DIRECTORY_SEPARATOR.$documento1;

            /**  Identify the type of $inputFileName  **/
            $inputFileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($directory1);

            /**  Create a new Reader of the type that has been identified  **/
            $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($inputFileType);

            /**  Load $inputFileName to a Spreadsheet Object  **/
            $spreadsheet1 = $reader->load($directory1);

            /**  Convert Spreadsheet Object to an Array for ease of use  **/
            // $schdeules = $spreadsheet->getActiveSheet()->toArray();



            //$reader = \PHPExcel_IOFactory::createReaderForFile($directory1);
            // $reader->setReadDataOnly(true);
            $spreadsheet1 = $reader->load($directory1);
            // $spreadsheet1 =  IOFactory::load($directory1);

            $sheets = $spreadsheet1->getAllSheets();



            $encabezados = [];
            $numeroRows=6;
            $rows = [];
            $iteraciones=count($sheets);


            for ($i = 0; $i < $iteraciones; $i++) {

                $worksheet = $spreadsheet1->getSheet($i);
                unset($rows);
                $rows=[];
                ///tomamos la primera celda de la hoja
                foreach ($worksheet->getRowIterator() AS $row) {
                    $cellIterator = $row->getCellIterator();
                    $cellIterator->setIterateOnlyExistingCells(FALSE); // This loops through all cells,
                    $cells = [];
                    foreach ($cellIterator as $cell) {
                        $valor="";
                        if(gettype($cell->getValue()) =="object"){
                            $valor=$cell->getValue()->__toString();
                        }else{
                            $valor=$cell->getValue();
                        }
                        //   echo "<br>".$valor;
                        $cells[] = strtoupper($valor);
                    }
                    $rows[] = $cells;
                    break;
                }
                //fin de tomar las celdfas
                //verificamo que el documento tenga la estructura correcta
                foreach ($tiposDocumentos as $key => $td) {

                    $result=array_diff($td,$rows[0]);
                    if(count($result)==0){
                        $tipoDocumento=$key;
                    }
                }
                if($tipoDocumento !=""){


                    //aqui procesamos

                 //   echo "entra 1";
                    $res=$this->procesarDocumento($nombreDocumento,$tipoDocumento,$i);


                    if($res['exito']){

                        if(count($res['productosOmitidosData'])> 0){
                            foreach($res['productosOmitidosData'] as $keyProducto => $prod){
                                $productosOmitidosData[]=$prod;
                            }
                        }


                        $productosNuevos+=$res['productosNuevos'];
                        $productosOmitidos+=$res['productosOmitidos'];

                        $productosActualizados+=$res['productosActualizados'];


                    }else{
                        $msj.="Algo salió mal al procesar la hoja ".$i." : ".$res['msj'];
                        $exito=false;
                    }
                }else{
                    $msj.= "hoja ".$i." es un Tipo de Documento desconocido";
                    $exito=false;
                }



            }//fin de for hojas








        }catch (\Exception $e) {
            $msj=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();

        }
        //  echo $msj;

        return $this->json(array('msj'=>$msj,'exito'=>$exito,
            'tipoDocumento'=>$tipoDocumento,
            'productosNuevos'=>$productosNuevos,
            'productosActualizados'=>$productosActualizados,
            'productosOmitidos'=>$productosOmitidos,
            'productosOmitidosData'=>$productosOmitidosData


            ));

    }

    function procesarDocumento($nombreDocumento,$tipoDocumento,$numHoja=0){

        $carpeta=$this->getParameter('uploads');
        $exito=false;
        $msj="";
        $em=$this->getDoctrine()->getManager();

        $clientesNuevos=0;
        $beneficiariosNuevos=0;
        $beneficiariosActualizados=0;
        $beneficiariosOmitidos=0;
        $beneficiariosenBlanco=0;

        $productosNoAgregados=[];
        $productosNuevosData=0;
        $productosOmitidos=0;
        $productosOmitidosData=[];
        $productosProcesados=0;
        $productosNuevos=0;
        $productosActualizados=0;
        $codigoBarras="";
        try{
            $documento=$nombreDocumento;

            $directory=$carpeta.DIRECTORY_SEPARATOR.$documento;
            /**  Identify the type of $inputFileName  **/
            $inputFileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($directory);

            /**  Create a new Reader of the type that has been identified  **/
            $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($inputFileType);

            /**  Load $inputFileName to a Spreadsheet Object  **/


            // $spreadsheet = $reader->load($directory);
            //$reader = \PHPExcel_IOFactory::createReaderForFile($directory);
            // $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($directory);
            //var_dump($spreadsheet);
            //  $spreadsheet= IOFactory::load($directory);

            $worksheet = $spreadsheet->getSheet($numHoja);

            //   var_dump($worksheet);

            $highestRow = $worksheet->getHighestRow(); // e.g. 10


            $highestColumn = $worksheet->getHighestColumn(); // e.g 'F'
            // $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn); // e.g. 5

           // $ObjCategoria=$this->getDoctrine()->getRepository(Categoria::class)->findOneBy(['idcategoria'=>10,'status'=>"1"]);
           // $ObjProveeder=$this->getDoctrine()->getRepository(Proveedor::class)->findOneBy(['idproveedor'=>1,'status'=>"1"]);



            for ($row = 2; $row <= $highestRow; ++$row) {

                $codigoBarrasDuplicado=false;




                $marca = $worksheet->getCell("A".$row)->getValue();

                if (gettype($marca) == "object") {
                    $marca = $marca->__toString();
                }
                $modelo =  $worksheet->getCell("B".$row)->getValue();
                if (gettype($modelo) == "object") {
                    $modelo = $modelo->__toString();
                }

                $codigoColor =  $worksheet->getCell("C".$row)->getValue();
                if (gettype($codigoColor) == "object") {
                    $codigoColor = $codigoColor->__toString();
                }
                $color =  $worksheet->getCell("D".$row)->getValue();
                if (gettype($color) == "object") {
                    $color = $color->__toString();
                }
                $codigoBarrasUniversal =  $worksheet->getCell("E".$row)->getValue();
                if (gettype($codigoBarrasUniversal) == "object") {
                    $codigoBarrasUniversal = $codigoBarrasUniversal->__toString();
                }

                $tipoMaterial =  $worksheet->getCell("F".$row)->getValue();
                if (gettype($tipoMaterial) == "object") {
                    $tipoMaterial =$tipoMaterial->__toString();
                }
                $descripcion =  $worksheet->getCell("G".$row)->getValue();
                if (gettype($descripcion) == "object") {
                    $descripcion = $descripcion->__toString();
                }

                $sobrepuesto = $worksheet->getCell("H".$row)->getValue();
                if (gettype($sobrepuesto) == "object") {
                    $sobrepuesto = $sobrepuesto->__toString();

                }

                $masivoUnico =  $worksheet->getCell("I".$row)->getValue();
                if (gettype($masivoUnico) == "object") {
                    $masivoUnico = strtoupper($masivoUnico->__toString());

                }else{
                    $masivoUnico=strtoupper($masivoUnico);
                }
                $categoria =  $worksheet->getCell("J".$row)->getValue();
                if (gettype($categoria) == "object") {
                    $categoria = $categoria->__toString();

                }
                $subCategoria =  $worksheet->getCell("K".$row)->getValue();
                if (gettype($subCategoria) == "object") {
                    $subCategoria = $subCategoria->__toString();

                }
                $unidadMedida = $worksheet->getCell("L".$row)->getValue();
                if (gettype($unidadMedida) == "object") {
                    $unidadMedida = $unidadMedida->__toString();

                }
                $medida =  $worksheet->getCell("M".$row)->getValue();
                if (gettype($medida) == "object") {
                    $medida = $medida->__toString();

                }

                $empresa = $worksheet->getCell("N".$row)->getValue();
                if (gettype($empresa) == "object") {
                    $empresa = $empresa->__toString();

                }

                $rfc = $worksheet->getCell("O".$row)->getValue();
                if (gettype($rfc) == "object") {
                    $rfc = $rfc->__toString();

                }

                if($sobrepuesto !="1"){
                    $sobrepuesto=0;
                }


                //vemos si solo es actualizar los codigos de barra o nuevos productos con caracteristicas
            //    echo "<br>Entra 1000";
                if($empresa !="" && $empresa != null && $marca !="" && $marca != null  && $modelo !="" && $modelo !=null  && $categoria !="" && $categoria !=null && $subCategoria !="" && $subCategoria !=null && $masivoUnico !="" && $masivoUnico !=null){


                        //ver si la marca existe
                        $ObjMarca=$this->getDoctrine()->getRepository(Marca::class)->findOneBy(['nombre'=>$marca,'status'=>"1"]);

                        if(!$ObjMarca){
                            //vemos si el nombre de la marca no es null


                                $ObjMarca= new Marca();
                                $ObjMarca->setNombre($marca);
                                $em->persist($ObjMarca);
                                $em->flush();



                        }
                        $ObjEmpresa=$this->getDoctrine()->getRepository(Empresa::class)->findOneBy(['rfc'=>$rfc,'status'=>"1"]);

                        if(!$ObjEmpresa){
                            throw new \Exception('Debe poner la empresa a la que pertenece el producto');
                        }


                    //la categoria es la subcategoria y la clase la categoría
                        $ObjCategoria=$this->getDoctrine()->getRepository(Clase::class)->findOneBy(['nombre'=>$categoria,'status'=>"1"]);
                        if(!$ObjCategoria){
                            //vemos si el nombre de la marca no es null

                                // echo "es nuevo ".$marca."<br>";
                                $ObjCategoria= new Clase();
                                $ObjCategoria->setNombre($categoria);
                                $ObjCategoria->setEmpresaIdempresa($ObjEmpresa);
                                $em->persist($ObjCategoria);
                                $em->flush();
                        }

                    $ObjSubCategoria=$this->getDoctrine()->getRepository(Categoria::class)->findOneBy(['nombre'=>$subCategoria,'status'=>"1"]);
                    if(!$ObjSubCategoria){
                        //vemos si el nombre de la marca no es null

                        // echo "es nuevo ".$marca."<br>";
                        $ObjSubCategoria= new Categoria();
                        $ObjSubCategoria->setNombre($subCategoria);
                        $ObjSubCategoria->setClaseIdclase($ObjCategoria);
                        $em->persist($ObjSubCategoria);
                        $em->flush();
                    }

                    $ObjUnidadMedida=$this->getDoctrine()->getRepository(Unidadmedida::class)->findOneBy(['nombre'=>$unidadMedida,'status'=>"1"]);
                    if(!$ObjUnidadMedida){
                        //vemos si el nombre de la marca no es null

                        // echo "es nuevo ".$marca."<br>";
                        $ObjUnidadMedida= new Unidadmedida();
                        $ObjUnidadMedida->setNombre($unidadMedida);
                        $em->persist($ObjUnidadMedida);
                        $em->flush();
                    }

                    $ObjMedida=$this->getDoctrine()->getRepository(Medida::class)->findOneBy(['nombre'=>$medida,'status'=>"1"]);
                    if(!$ObjMedida){
                        //vemos si el nombre de la marca no es null

                        // echo "es nuevo ".$marca."<br>";
                        $ObjMedida= new Medida();
                        $ObjMedida->setNombre($medida);
                        $ObjMedida->setUnidadmedidaIdunidadmedida($ObjUnidadMedida);
                        $em->persist($ObjMedida);
                        $em->flush();
                    }


                        $ObjProducto=$this->getDoctrine()->getRepository(Producto::class)->findOneBy(['modelo'=>$modelo,'status'=>"1"]);
                    //si no fue encontrado entonces lo creamos
                    if(!$ObjProducto) {
                        $ObjProducto = new Producto();
                        $ObjProducto->setCosto(0);
                        $ObjProducto->setPrecio(0);
                        $ObjProducto->setCantidad(0);
                        $ObjProducto->setCreacion(new \DateTime("now"));
                        $ObjProducto->setModificacion(new \DateTime("now"));
                        //1= almacenable 2=servicio
                        $ObjProducto->setTipoproducto("1");
                        $productosNuevos++;
                    }else{
                        $productosActualizados++;
                    }






                            //si no tiene codigo de barras de lo asignamos sólo a los masivos
                            if(trim($ObjProducto->getCodigo())==""){
                                $codigoBarras="111".mt_rand(100000000,999999999);
                                //vemos si ya existe el codgio de barra si no lo cambiamos

                                $ObjProductoAuxliiar=$this->getDoctrine()->getRepository(Producto::class)->findOneBy(['codigobarrasuniversal'=>$codigoBarras,'status'=>"1"]);
                                if($ObjProductoAuxliiar){
                                    //si existe hayq ue cambiarlo
                                    $codigoBarras="111".mt_rand(100000000,999999999);
                                    unset($ObjProductoAuxliiar);
                                }

                                if( $masivoUnico =="MASIVO" && $ObjProducto->getCodigo()==""){
                                    $ObjProducto->setCodigobarrasuniversal($codigoBarras);
                                }
                              //esta como oblogatorio en base de datos lo quitamos en la version que vienme
                                $ObjProducto->setCodigo($codigoBarras);
                            }
                            $ObjProducto->setNombre("SN");
                            $ObjProducto->setModelo($modelo);
                            $ObjProducto->setTipo(1);
                            $ObjProducto->setCodigocolor($codigoColor);
                            $ObjProducto->setColor($color);
                            $ObjProducto->setModificacion(new \DateTime("now"));

                            $ObjProducto->setCategoriaIdcategoria($ObjSubCategoria);

                            $ObjProducto->setMarcaIdmarca($ObjMarca);
                            $ObjProducto->setTipomaterial($tipoMaterial);
                            $ObjProducto->setDescripcion($descripcion);
                            $ObjProducto->setSobrepuesto($sobrepuesto);
                            $ObjProducto->setMedidaIdmedida($ObjMedida);

                            $ObjProducto->setOrdenlaboratorio('1');
                       //     var_dump($masivoUnico);
                             if( trim($masivoUnico) =="UNICO" || trim($masivoUnico) =="ÚNICO"){
                                 $ObjProducto->setMasivounico("1");
                            }else{
                                 $ObjProducto->setMasivounico("2");
                             }




                            $em->persist($ObjProducto);
                            $em->flush();








                }else{

                        $productosOmitidos++;
                        $productosOmitidosData[]=['modelo'=>$modelo,'barcode'=>$codigoBarrasUniversal,'descripcion'=>$descripcion];


                }





                // exit();


            }
            $em->flush();




            $exito=true;

        }catch (\Exception $e) {
            $msj.=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();

        }
        echo $msj;
        /*
        $clientesNuevos=0;
        $beneficiariosNuevos=0;
        $beneficiariosActualizados=0;
        $beneficiariosOmitidos=0;
        */
        return array('msj'=>$msj,'exito'=>$exito,
            'productosNuevos'=>$productosNuevos,
            'productosOmitidos'=>$productosOmitidos,
            'productosOmitidosData'=>$productosOmitidosData,
            'productosActualizados'=>$productosActualizados

        );
    }
}
