<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;


class ReporteRezagadosController extends AbstractController
{
    /**
     * @Route("/reporte/rezagados", name="app_reporte_rezagados")
     */

    public function index(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();

        $Usuario = $this->getUser();

        $query = $em->createQuery(
            'SELECT e.idempresa, e.nombre
                FROM App\Entity\Usuarioempresapermiso uem
                inner join uem.empresaIdempresa e
                inner join uem.usuarioIdusuario u
                where e.status =:status and u.idusuario=:idusuario order by e.nombre asc 
                '
        )->setParameters(['status' => "1", 'idusuario' => $Usuario->getIdUsuario()]);
        $empresas = $query->getResult();

        $query = $em->createQuery(
            'SELECT s.cantidad, suc.nombre, s.codigobarras AS SKU, p.modelo, s.creacion, 
            p.codigobarrasuniversal AS CodigoBarras, suc.nombre AS NombreSucursal, emp.nombre AS NombreEmpresa
            FROM App\Entity\Stock s
            INNER JOIN s.productoIdproducto p
            INNER JOIN s.sucursalIdsucursal suc
            INNER JOIN suc.empresaIdempresa emp
            WHERE s.status = :status AND s.cantidad >= :minCantidad order by p.creacion asc
        '
        )->setParameters(['status' => "1", 'minCantidad' => 1])->setMaxResults(30);  // Limitar a 10 resultados

        $fecha = $query->getResult();


        return $this->render('reporte_rezagados/index.html.twig', [
            'empresas' => $empresas,
            'fecha' => $fecha
        ]);
    }


    /**
     * @Route("/reporte/rezagados/data", name="reporte-rezagados", methods={"POST"})
     */
    public function getRezagadosData(Request $request): Response
    {
        try {

            $em = $this->getDoctrine()->getManager();
            $fechaId = $request->get("fechaId");
            $empresaId = $request->get('idempresa');
            $masivoUnico = $request->get('masivoUnico');
            $sucursalesSeleccionadas = $request->request->get('sucursal', []);
            $bodegasSeleccionadas = $request->request->get('bodegas', []);
            $campanasSeleccionadas = $request->request->get('campanas', []);
            $condiciones = [];

            
            if ($masivoUnico === '1') {
                $masivoUnico='1';
            }else{
                $masivoUnico='2';
            }

            $fechaLimiteMax = new \DateTime(null, new \DateTimeZone('America/Mexico_City'));
            $fechaLimiteMax -> setTime(0,0,0);

            // Calcular fecha límite mínima según el valor de fechaId
            switch ($fechaId) {
                case 1:
                    $fechaLimiteMax->modify('-1 month');

                    break;
                case 2:
                    $fechaLimiteMax->modify('-6 month');
                    break;
                case 3:
                    /*$fechaLimiteMin->modify('-365 days');*/
                    break;
                case 4:
                    $fechaLimiteMax->modify('-1 year');
                    break;
                default:
                    throw new \Exception("Error: ningún elemento seleccionado");
            }


            $fechaLimiteMaxStr = $fechaLimiteMax->format('Y-m-d H:i:s');


            $whereCategoria = " AND (";

            // Sucursales
            if (!empty($sucursalesSeleccionadas)) {
                foreach ($sucursalesSeleccionadas as $index => $idSucursal) {
                    $whereCategoria .= ($index ? " OR " : "") . "(suc.tipo = 'sucursal' AND suc.idsucursal = $idSucursal)";
                }
            }
            
            // Bodegas
            if (!empty($bodegasSeleccionadas)) {
                if (!empty($whereCategoria)) $whereCategoria .= " OR ";
                foreach ($bodegasSeleccionadas as $index => $idBodega) {
                    $whereCategoria .= ($index ? " OR " : "") . "(suc.tipo = 'bodega' AND suc.idsucursal = $idBodega)";
                }
            }
            
            // Campañas
            if (!empty($campanasSeleccionadas)) {
                if (!empty($whereCategoria)) $whereCategoria .= " OR ";
                foreach ($campanasSeleccionadas as $index => $idCampana) {
                    $whereCategoria .= ($index ? " OR " : "") . "(suc.tipo = 'campaña' AND suc.idsucursal = $idCampana)";
                }
            }
            
            $whereCategoria .= ")";

            $queryBuilder = $em->createQueryBuilder();

            $queryBuilder
                ->select('s.cantidad, suc.nombre, s.codigobarras AS SKU, ca.nombre AS Categoria, p.masivounico, p.modelo, sv.creacion, p.tipoproducto,
                         m.nombre AS Marcanombre, p.codigobarrasuniversal AS CodigoBarras, suc.nombre AS NombreSucursal, emp.nombre AS NombreEmpresa')
                ->from('App\Entity\Stockventa', 'sv')
                ->innerJoin('sv.stockIdstock', 's')
                ->innerJoin('s.productoIdproducto', 'p')
                ->innerJoin('p.categoriaIdcategoria', 'ca')
                ->innerJoin('p.marcaIdmarca', 'm')
                ->innerJoin('s.sucursalIdsucursal', 'suc')
                ->innerJoin('suc.empresaIdempresa', 'emp')
                ->where('sv.status = :status')
                ->andWhere('emp.idempresa = :empresaId')
                ->andWhere('s.cantidad >= :cantidad')
                ->andWhere('p.tipoproducto = :tipoProducto')
                ->andWhere('sv.creacion <= :fechaLimiteMax') 
                ->andWhere('p.masivounico = :masivounico')
                ->setParameter('tipoProducto', '1')
                ->setParameter('masivounico', $masivoUnico)
                ->setParameter('status', "1")
                ->setParameter('cantidad', "1")
                ->setParameter('empresaId', $empresaId)
                ->setParameter('fechaLimiteMax', $fechaLimiteMaxStr)
                ->orderBy('sv.creacion', 'ASC');

            
            $orX = $queryBuilder->expr()->orX();
            
            // Sucursales
            foreach ($sucursalesSeleccionadas as $idSucursal) {
                $orX->add($queryBuilder->expr()->andX(
                    $queryBuilder->expr()->eq('suc.tipo', ':sucursalTipo'),
                    $queryBuilder->expr()->eq('suc.idsucursal', $idSucursal)
                ));
                $queryBuilder->setParameter('sucursalTipo', 'sucursal');
            }
            
            // Bodegas
            foreach ($bodegasSeleccionadas as $idBodega) {
                $orX->add($queryBuilder->expr()->andX(
                    $queryBuilder->expr()->eq('suc.tipo', ':bodegaTipo'),
                    $queryBuilder->expr()->eq('suc.idsucursal', $idBodega)
                ));
                $queryBuilder->setParameter('bodegaTipo', 'bodega');
            }
            
            // Campañas
            foreach ($campanasSeleccionadas as $idCampana) {
                $orX->add($queryBuilder->expr()->andX(
                    $queryBuilder->expr()->eq('suc.tipo', ':campanaTipo'),
                    $queryBuilder->expr()->eq('suc.idsucursal', $idCampana)
                ));
                $queryBuilder->setParameter('campanaTipo', 'campaña');
            }
            
            if ($orX->count() > 0) {
                $queryBuilder->andWhere($orX);
            }
            
            $query = $queryBuilder->getQuery();
            
            $fecha = $query->getResult();

            // Renderizar la vista y pasar los datos
            return $this->render('reporte_rezagados/tableRezagados.html.twig', [
                'fecha' => $fecha
            ]);
        } catch (\Exception $e) {
            // Manejar la excepción aquí
            return new Response($e->getMessage());
        }
    }
}

