<?php

namespace App\Controller;

use App\Entity\Flujoexpediente;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

use Symfony\Component\HttpFoundation\JsonResponse;
use App\Form\ClienteExpedienteType;
//use App\Form\GraduacionType;
use App\Form\GraduacionExpedienteType;
use App\Form\OrdenlaboratorioType;

use App\Entity\Cliente;
use App\Entity\Graduacion;
use App\Entity\Ordenlaboratorio;
use App\Entity\Venta;
use App\Entity\Stockventa;
use App\Entity\Stockventaordenlaboratorio;
use App\Entity\Documentosexamenvisual;
use App\Entity\Flujoexpedienteventa;
use App\Entity\Flowsigneddocuments;

use Dompdf\Exception;
use Dompdf\Dompdf;
use Dompdf\Options;

use Symfony\Component\HttpFoundation\File\Exception\FileException;

use DateTime;

class ExpedientesController extends AbstractController
{
    /**
     * @Route("/admin/expediente-clinico/{idflujoexpediente}", name="expedientes-expediente-clinico", requirements={"idflujoexpediente"="\d+"}, defaults={"idflujoexpediente"=null})
     */
    public function expedienteClinico($idflujoexpediente): Response
    {
        $etapa = 1;

        $em = $this->getDoctrine()->getManager();

        $Usuario = $this->getUser();

        $query = $em->createQuery(
            'SELECT e.nombre as nombre, e.idempresa as id
               FROM App\Entity\Usuarioempresapermiso up
               inner join up.empresaIdempresa e
               where up.status =:status and up.usuarioIdusuario =:idusuario
               '
        )->setParameters(['status'=>"1",'idusuario'=>$Usuario->getIdusuario()]);
        $permisos= $query->getResult();

        $Flujoexpediente = $em->getRepository(Flujoexpediente::class)->findOneBy(array('idflujoexpediente' => $idflujoexpediente));

        if ($Flujoexpediente){

            $etapa =$Flujoexpediente->getEtapa();

        }

        $query = $em->createQuery(
            'SELECT g.idgraduacion, g.creacion
               FROM App\Entity\Graduacion g
               INNER JOIN g.clienteIdcliente c
               WHERE g.status =:status AND c.idcliente =:clientId
               '
        )->setParameters(['status'=>"1",'clientId'=>$Flujoexpediente->getClienteIdcliente()->getIdcliente()]);
        $prevGraduations= $query->getResult();

        $query = $em->createQuery(
            'SELECT d.idflowsigneddocuments
               FROM App\Entity\Flowsigneddocuments d
               INNER JOIN d.flujoexpedienteIdflujoexpediente fe
               WHERE d.status =:status AND fe.idflujoexpediente =:flowId
               '
        )->setParameters(['status'=>"1",'flowId'=>$Flujoexpediente->getIdflujoexpediente()]);
        $flowDocuments = $query->getResult();

        

        
        return $this->render('expedientes/flujoexpediente.html.twig', [
            'etapa' => $etapa,
            'idflujoexpediente' => $idflujoexpediente,
            'Flujoexpediente' => $Flujoexpediente,
            'permisos'=>$permisos,
            'prevGraduations'=>$prevGraduations,
            'flowDocuments' => $flowDocuments
        ]);
    }


    /**
     * @Route("/admin/expediente-clinico/agregar-cliente", name="expedientes-agregar-cliente")
     */
    public function agregarCliente(Request $request): Response
    {
        $exito=false;
        $msj="";
        $em = $this->getDoctrine()->getManager();
        $Cliente = new Cliente();

        $alcaldia = "";

        $idflujoexpediente = $request->get("idflujoexpediente");

        $Flujoexpediente = $em->getRepository(Flujoexpediente::class)->findOneBy(array('idflujoexpediente' => $idflujoexpediente));

        if ($Flujoexpediente){

            if($Flujoexpediente->getClienteIdcliente()){
                $Cliente = $Flujoexpediente->getClienteIdcliente();
                $alcaldia = $Cliente->getAlcaldia();
            }
        }

        $form = $this->createForm(ClienteExpedienteType::class,$Cliente);

        if ($idflujoexpediente) $form->get('idflujo')->setData($idflujoexpediente);

        $form->handleRequest($request);

        try {
            if($form->isSubmitted() && $form->isvalid() ){

                
                $idflujo =  $form->get('idflujo')->getData();

                
                $Flujoexpediente = $em->getRepository(Flujoexpediente::class)->findOneBy(array('idflujoexpediente' => $idflujo));

                $Cliente = $form->getData();

                $dateString = $form->get('fechanacimiento')->getData();

                $currentDate = new DateTime();

                $interval = $currentDate->diff($dateString);

                $Cliente->setEdad($interval->y);

                $alcaldia = $Cliente->getAlcaldia();

                $em->persist($Cliente);

                $em->flush();

                $Flujoexpediente->setClienteIdcliente($Cliente);

                $em->persist($Flujoexpediente);

                $em->flush();

                $exito = true;

            }

            

        }catch (\Exception $e) {
            //$msj.=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();
            $msj.=$e->getMessage();
        }

        return $this->render('expedientes/formularioagregarcliente.html.twig', [
            'form' => $form->createView(),
            'exito'=>$exito,
            'msj'=>$msj,
            'alcaldia'=>$alcaldia,
        ]);
    }

    /**
     * @Route("/admin/expediente-clinico/agregar-graduacion", name="expedientes-agregar-graduacion")
     */
    public function agregarGraduacion(Request $request): Response
    {
        $exito = false;
        $msj   = "";
        $em    = $this->getDoctrine()->getManager();
        $Graduacion = new Graduacion();

        $idflujoexpediente = $request->get("idflujoexpediente");
        $graduationId      = $request->get("graduationId");

        // Buscamos el flujo
        $Flujoexpediente = $em->getRepository(Flujoexpediente::class)
            ->findOneBy(['idflujoexpediente' => $idflujoexpediente]);

        // Si el flujo ya tiene graduación y NO piden crear una nueva
        if ($Flujoexpediente && $Flujoexpediente->getGraduacionIdgraduacion() && $graduationId != '-1') {
            $Graduacion = $Flujoexpediente->getGraduacionIdgraduacion();
        } else {
            // Crear una graduación nueva
            $Graduacion->setCreacion(new \DateTime());
            $Graduacion->setActualizacion(new \DateTime());
            if ($Flujoexpediente) {
                $Graduacion->setClienteIdcliente($Flujoexpediente->getClienteIdcliente());
            }
            $Graduacion->setUsuarioIdusuario($this->getUser());
        }

        // Si viene un graduationId real, cargamos esa Graduación
        $tempGrad = $em->getRepository(Graduacion::class)
            ->findOneBy(['idgraduacion' => $graduationId]);
        if ($tempGrad) {
            $Graduacion = $tempGrad;
        }

        // Construimos el formulario
        $form = $this->createForm(GraduacionExpedienteType::class, $Graduacion);
        if ($idflujoexpediente) {
            $form->get('idflujo')->setData($idflujoexpediente);
        }

        $form->handleRequest($request);

        try {
            if ($form->isSubmitted() && $form->isValid()) {
                $idflujo = $form->get('idflujo')->getData();
                $Flujoexpediente = $em->getRepository(Flujoexpediente::class)
                    ->findOneBy(['idflujoexpediente' => $idflujo]);

                // Guardamos la graduación
                $Graduacion = $form->getData();
                $em->persist($Graduacion);

                // Relacionamos la graduación al flujo
                if ($Flujoexpediente) {
                    $Flujoexpediente->setGraduacionIdgraduacion($Graduacion);
                    $em->persist($Flujoexpediente);
                }

                $em->flush();

                $OrdenLaboratorio = $em->getRepository(Ordenlaboratorio::class)
                    ->findOneBy([
                        'flujoexpedienteIdflujoexpediente' => $Flujoexpediente->getIdflujoexpediente()
                    ]);
                if (!$OrdenLaboratorio) {
                    $OrdenLaboratorio = new Ordenlaboratorio();
                    $OrdenLaboratorio->setFlujoexpedienteIdflujoexpediente($Flujoexpediente);
                    $OrdenLaboratorio->setCreacion(new \DateTime());
                    $OrdenLaboratorio->setActualizacion(new \DateTime());
                    $em->persist($OrdenLaboratorio);
                } else {
                    $OrdenLaboratorio->setActualizacion(new \DateTime());
                }

                $OrdenLaboratorio->setEsferaod($Graduacion->getEsfodsf());
                $OrdenLaboratorio->setEsferaoi($Graduacion->getEsfoisf());

                $OrdenLaboratorio->setCilindrood($Graduacion->getCilodsf());
                $OrdenLaboratorio->setCilindrooi($Graduacion->getCiloisf());

                $OrdenLaboratorio->setEjeod($Graduacion->getEjeodsf());
                $OrdenLaboratorio->setEjeoi($Graduacion->getEjeoisf());

                $OrdenLaboratorio->setAddordenlaboratorio($Graduacion->getAddsf());



                $em->persist($OrdenLaboratorio);
                $em->flush();

                $exito = true;
            }
        } catch (\Exception $e) {
            $msj .= $e->getMessage();
        }

        $anamnesisJsonExist = $Graduacion->getAnamnesisjson() ? true : false;

        return $this->render('expedientes/formularioagregargraduacion.html.twig', [
            'form' => $form->createView(),
            'idgraduacion' => $Graduacion->getIdgraduacion(),
            'exito' => $exito,
            'msj' => $msj,
            'anamnesisJsonExist' => $anamnesisJsonExist,
        ]);
    }


    /**
     * @Route("get-anamnesis", name="expedientes-get-anamnesis")
     */
    public function getAnamnesis(Request $request): Response
    {
        $success=false;
        $msg="";
        $em = $this->getDoctrine()->getManager();

        $graduationId = $request->get("graduationId");

        $Graduation = $em->getRepository(Graduacion::class)->findOneBy(array('idgraduacion' => $graduationId));

        $anamnesis = null;

        try {
            if ($Graduation){

                $anamnesisJson = str_replace('_', ' ', $Graduation->getAnamnesisjson());
                $anamnesis = json_decode($anamnesisJson, true);
                
                $success = true;

            }

        }catch (\Exception $e) {
            //$msj.=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();
            $msg.=$e->getMessage();
        }

        return $this->render('expedientes/expedientes-get-anamnesis.html.twig', [
            'anamnesis' => $anamnesis,
            'success'=>$success,
            'msg'=>$msg,
        ]);
    }

    /**
     * @Route("update-anamnesis", name="expedientes-update-anamnesis")
     */
    public function updateAnamnesis(Request $request): Response
    {
        $success=false;
        $msg="";
        $em = $this->getDoctrine()->getManager();

        $graduationId = $request->get("graduationId");

        $Graduation = $em->getRepository(Graduacion::class)->findOneBy(array('idgraduacion' => $graduationId));

        $formData = $request->request->all();

        $anamnesisJson = json_encode($formData,true);

        try {
            if ($Graduation){

                $Graduation->setAnamnesisjson($anamnesisJson);
                
                $em->persist($Graduation);
                $em->flush();
                
                $success = true;

            }

        }catch (\Exception $e) {
            $msg.=$e->getMessage();
        }

        return $this->json(['success'=>$success,'msg'=>$msg]);
    }

    /**
     * @Route("/admin/expediente-clinico/agregar-ordenlaboratorio", name="expedientes-agregar-ordenlaboratorio")
     */
    public function agregarOrdenLaboratorio(Request $request): Response
    {
    
        $exito=false;
        $msj="";
        $em = $this->getDoctrine()->getManager();
        
        $productos = array();

        $servicios = array();
        $serviciosdisponibles = null;
        $productosdisponibles = null;

        $idflujoexpediente = $request->get("idflujoexpediente");
        $idordenlaboratorio = $request->get("idordenlaboratorio");
        $idstockventa = $request->get("idstockventa");
        

        $Flujoexpediente = $em->getRepository(Flujoexpediente::class)->findOneBy(array('idflujoexpediente' => $idflujoexpediente));
        $Ordenlaboratorio = $em->getRepository(Ordenlaboratorio::class)->findOneBy(array('idordenlaboratorio' => $idordenlaboratorio));

        $laboratoryOrdersService = [];

        if(!$Ordenlaboratorio) $Ordenlaboratorio = new Ordenlaboratorio();

        if($Flujoexpediente){

            $query = $em->createQuery(
                'SELECT v.idventa
                FROM App\Entity\Flujoexpedienteventa fv
                INNER JOIN fv.flujoexpedienteIdflujoexpediente fe
                INNER JOIN fv.ventaIdventa v
                where v.status =:status and fe.idflujoexpediente =:idFlow
                '
            )->setParameters(['status'=>"1", 'idFlow'=>$Flujoexpediente->getIdflujoexpediente()]);
            $sales = $query->getResult(); 

            if (count($sales) > 0){

                $whereSale=" ";

                $whereSale=" AND v.idventa IN (";
            
                for($i = 0; $i < count($sales); $i++){

                    $whereSale .= $sales[$i]["idventa"];
                            
                    if ($i != count($sales)-1 ) $whereSale .=", ";

                }
                $whereSale .=") ";

                $query = $em->createQuery(
                    'SELECT p.modelo as modelo, p.descripcion as descripcion, s.codigobarras as sku, p.codigo as codigo, m.nombre as marca,
                     p.tipoproducto as tipoproducto, sv.idstockventa, sv.cantidad

                    FROM App\Entity\Stockventa sv
                    inner join sv.ventaIdventa v
                    inner join sv.stockIdstock s
                    inner join s.productoIdproducto p 
                    inner join p.marcaIdmarca m
                    where p.ordenlaboratorio =:ordenlaboratorio and sv.status =:status'.$whereSale
                )->setParameters(['status'=>"1",'ordenlaboratorio'=>"1"]);
                $productos= $query->getResult();

                $query = $em->createQuery(
                    'SELECT p.modelo as modelo, p.descripcion as descripcion, s.codigobarras as sku, p.codigo as codigo, m.nombre as marca,
                     sv.idstockventa as idstockventa, sv.cantidad
                    
                    FROM App\Entity\Stockventa sv
                    inner join sv.ventaIdventa v
                    inner join sv.stockIdstock s
                    inner join s.productoIdproducto p
                    inner join p.marcaIdmarca m
                    where p.ordenlaboratorio =:ordenlaboratorio and sv.status =:status'.$whereSale
                )->setParameters(['status'=>"1",'ordenlaboratorio'=>"0"]);
                $servicios= $query->getResult();

                $serviciosdisponibles = 0;

                for($i = 0; $i < count($servicios); $i++){

                    $query = $em->createQuery(
                        'SELECT ol.idordenlaboratorio
                        FROM App\Entity\Stockventaordenlaboratorio svol
                        inner join svol.stockventaIdstockventa sv
                        inner join svol.ordenlaboratorioIdordenlaboratorio ol

                        where sv.idstockventa =:idstockventa
                        '
                    )->setParameters(['idstockventa'=>$servicios[$i]['idstockventa']]);
                    $laboratoryOrders= $query->getResult();

                    $laboratoryOrdersMapped = array();

                    foreach ($laboratoryOrders as $value){

                        array_push($laboratoryOrdersMapped,$value['idordenlaboratorio']);
                        array_push($laboratoryOrdersService,$value['idordenlaboratorio']);
                    }

                    $servicios[$i]['quantity'] = $servicios[$i]['cantidad'] - count($laboratoryOrders);

                    $serviciosdisponibles += $servicios[$i]['quantity'];

                    $Stockventaol = $em->getRepository(Stockventaordenlaboratorio::class)->findOneBy(array('stockventaIdstockventa' => $servicios[$i]['idstockventa']));

                    if ($Stockventaol) {
                        $servicios[$i]['Ordenlaboratorio'] = $Stockventaol->getOrdenlaboratorioIdordenlaboratorio()->getIdordenlaboratorio();
                        $servicios[$i]['Ordenlaboratorios'] = $laboratoryOrdersMapped;
                        //if ($Stockventaol->getOrdenlaboratorioIdordenlaboratorio()->getIdordenlaboratorio() != $idordenlaboratorio ){} $serviciosdisponibles--;
                    }
                    else{
                        $servicios[$i]['Ordenlaboratorio'] = -1;
                        $servicios[$i]['Ordenlaboratorios'] = -1;
                    } 
                }

                $availableStorableProducts = 0;
                $availableNonStorableProducts = 0; 

                for($i = 0; $i < count($productos); $i++){
                    $Stockventaol = $em->getRepository(Stockventaordenlaboratorio::class)->findOneBy(array('stockventaIdstockventa' => $productos[$i]['idstockventa']));
                    
                    $query = $em->createQuery(
                        'SELECT ol.idordenlaboratorio
                        FROM App\Entity\Stockventaordenlaboratorio svol
                        inner join svol.stockventaIdstockventa sv
                        inner join svol.ordenlaboratorioIdordenlaboratorio ol

                        where sv.idstockventa =:idstockventa
                        '
                    )->setParameters(['idstockventa'=>$productos[$i]['idstockventa']]);
                    $laboratoryOrders= $query->getResult();

                    $laboratoryOrdersMapped = array();

                    foreach ($laboratoryOrders as $value){

                        array_push($laboratoryOrdersMapped,$value['idordenlaboratorio']);

                    }

                    $productos[$i]['quantity'] = $productos[$i]['cantidad'] - count($laboratoryOrders);

                    //($productos[$i]['tipoproducto'] == '1') ? $availableStorableProducts += $productos[$i]['quantity'] : $availableNonStorableProducts += $productos[$i]['quantity'];

                    if (($productos[$i]['tipoproducto'] == '1')) $availableStorableProducts += $productos[$i]['quantity'];
                    else $availableNonStorableProducts += $productos[$i]['quantity'];

                    if ($Stockventaol) {
                        $productos[$i]['Ordenlaboratorio'] = $Stockventaol->getOrdenlaboratorioIdordenlaboratorio()->getIdordenlaboratorio();
                        $productos[$i]['Ordenlaboratorios'] = $laboratoryOrdersMapped;

                        if ($Stockventaol->getOrdenlaboratorioIdordenlaboratorio()->getIdordenlaboratorio() != $idordenlaboratorio ) {

                            /*if($Stockventaol->getStockventaIdstockventa()->getStockIdstock()->getProductoIdproducto()->getTipoproducto()== '1') $productosAlmacenables--;
                            else $productosNoAlmacenables--;*/

                        }
                    }else{
                        $productos[$i]['Ordenlaboratorio'] = -1;
                        $productos[$i]['Ordenlaboratorios'] = -1;
                    } 
                }

                $productosdisponibles = ['1'=>$availableStorableProducts, '2' => $availableNonStorableProducts];

            }
                       
               
            
        }

        $form = $this->createForm(OrdenlaboratorioType::class,$Ordenlaboratorio);

        if ($idflujoexpediente) $form->get('idflujo')->setData($idflujoexpediente);
        if ($idstockventa) $form->get('idstockventa')->setData($idstockventa);
        

        $form->handleRequest($request);

        $isRefused = $Ordenlaboratorio->getRefusedclient();
        $refuseReason = $Ordenlaboratorio->getRefusereason();

        $curProductsCount = 0;

        if ($Ordenlaboratorio->getEtapa() > 0) {
            $query = $em->createQuery(
                'SELECT svol.idstockventaordenlaboratorio   
                FROM App\Entity\Stockventaordenlaboratorio svol
                inner join svol.ordenlaboratorioIdordenlaboratorio ol
                where ol.idordenlaboratorio =:idordenlaboratorio'
                
            )->setParameters(['idordenlaboratorio'=>$Ordenlaboratorio->getIdordenlaboratorio()]);
            $curSelectedProducts = $query->getResult();
            
            $curProductsCount = count($curSelectedProducts);
        }

        $orderDisabled = $idordenlaboratorio && $Flujoexpediente->getEtapa() > 5 && $curProductsCount > 0;

        try {
            

            if($form->isSubmitted() && $form->isvalid() ){

                if ($isRefused == '1') throw new \Exception('No se puede enviar una orden rechazada');

                $idflujo =  $form->get('idflujo')->getData();

                $Flujoexpediente = $em->getRepository(Flujoexpediente::class)->findOneBy(array('idflujoexpediente' => $idflujo));

                $Ordenlaboratorio = $form->getData();

                $Ordenlaboratorio->setFlujoexpedienteIdflujoexpediente($Flujoexpediente);

                $Ordenlaboratorio->setCreacion(new DateTime());
                $Ordenlaboratorio->setActualizacion(new DateTime());
                $Ordenlaboratorio->setClienteIdcliente($Flujoexpediente->getClienteIdcliente());
                $curStage = $Ordenlaboratorio->getEtapa();
                $stage = ($curStage > 2) ? $curStage : 2;
                $Ordenlaboratorio->setEtapa($stage);

                $em->persist($Ordenlaboratorio);

                $em->flush();

                if($idstockventa){
                    $Stockventa = $em->getRepository(Stockventa::class)->findOneBy(array('idstockventa' => $idstockventa));
                    if($Stockventa){

                        $query = $em->createQuery(
                            'SELECT svol   
                            FROM App\Entity\Stockventaordenlaboratorio svol
                            inner join svol.stockventaIdstockventa sv
                            inner join svol.ordenlaboratorioIdordenlaboratorio ol
                            inner join sv.stockIdstock s
                            inner join s.productoIdproducto p
                            
                            
                            where ol.status =:status and ol.idordenlaboratorio =:idordenlaboratorio and p.ordenlaboratorio =:ordenlaboratorio'
                            
                        )->setParameters(['status'=>"1",'idordenlaboratorio'=>$Ordenlaboratorio->getIdordenlaboratorio(), 'ordenlaboratorio'=>'1']);
                        $productoPrevio = $query->getResult();
    
                        
                        for ($i = 0; $i < count($productoPrevio); $i++){

                            $em->remove($productoPrevio[$i]);
                            $em->flush();

                        }

                        $stockventaordenlaboratorio = new Stockventaordenlaboratorio();

                        $stockventaordenlaboratorio->setOrdenlaboratorioIdordenlaboratorio($Ordenlaboratorio);
                        $stockventaordenlaboratorio->setStockventaIdstockventa($Stockventa);
                        $stockventaordenlaboratorio->setCreacion(new DateTime());

                        $em->persist($stockventaordenlaboratorio);
                        $em->flush();
                    } 
                    //else throw new \Exception('Producto inválido');
                } /*else if($Ordenlaboratorio->getStockventaIdstockventa()){

                }else throw new \Exception('No hay un producto seleccionado');*/

                $exito = true;

            }

        }catch (\Exception $e) {
            //$msj.=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();
            $msj.=$e->getMessage();
        }

        return $this->render('expedientes/formularioagregarordenlaboratorio.html.twig', [
            'form' => $form->createView(),
            'ordenlaboratorio' => $Ordenlaboratorio,
            'productos'=>$productos,
            'servicios'=>$servicios,
            'exito'=>$exito,
            'msj'=>$msj,
            'serviciosdisponibles'=>$serviciosdisponibles,
            'productosdisponibles'=>$productosdisponibles,
            'laboratoryOrdersService'=>$laboratoryOrdersService,
            "isRefused" => $isRefused,
            "refuseReason" => $refuseReason,
            "orderDisabled" => $orderDisabled
        ]);
    }

    /**
     * @Route("/admin/expediente-clinico/obtener-ordenlaboratorio", name="expedientes-obtener-ordenlaboratorio")
     */
    public function obtenerOrdenLaboratorio(Request $request): Response
    {
    
        $exito=false;
        $msj="";
        $hasVenta = false;
        $hasStockVenta = false;
        $em = $this->getDoctrine()->getManager();

        $ordeneslaboratorio = array();
        $productos = array();

        $productosOrdenes = [];

        $idflujo = $request->get("idflujoexpediente");

        $Flujoexpediente = $em->getRepository(Flujoexpediente::class)->findOneBy(array('idflujoexpediente' => $idflujo));

        if($Flujoexpediente){

            if ($Flujoexpediente){

                $query = $em->createQuery(
                    'SELECT ol               
                    FROM App\Entity\Ordenlaboratorio ol
                    inner join ol.flujoexpedienteIdflujoexpediente fe
                    where ( ol.status =:status OR ol.refusedclient =:status ) and fe.idflujoexpediente =:idflujoexpediente
                    '
                )->setParameters(['status'=>"1",'idflujoexpediente'=>$idflujo]);
                $ordeneslaboratorio= $query->getResult();
                
                $query = $em->createQuery(
                    'SELECT v.idventa               
                    FROM App\Entity\Flujoexpedienteventa fv
                    INNER JOIN fv.flujoexpedienteIdflujoexpediente fe
                    INNER JOIN fv.ventaIdventa v
                    where v.status =:status and fe.idflujoexpediente =:idFlow
                    '
                )->setParameters(['status'=>"1", 'idFlow'=>$Flujoexpediente->getIdflujoexpediente()]);
                $sales = $query->getResult();

                if (count($sales)){

                    $query = $em->createQuery(
                        'SELECT sv.idstockventa, ol.idordenlaboratorio, p.codigobarrasuniversal, p.modelo, s.codigobarras      
                        FROM App\Entity\Stockventaordenlaboratorio svol
                        inner join svol.stockventaIdstockventa sv
                        inner join svol.ordenlaboratorioIdordenlaboratorio ol
                        inner join sv.stockIdstock s
                        inner join s.productoIdproducto p
                        inner join ol.flujoexpedienteIdflujoexpediente fe
                        
                        where ol.status =:status and fe.idflujoexpediente =:idflujoexpediente and p.ordenlaboratorio =:ordenlaboratorio'
                        
                    )->setParameters(['status'=>"1",'idflujoexpediente'=>$idflujo, 'ordenlaboratorio'=>'1']);
                    $productos= $query->getResult();

                    if (count($productos) > 0) $hasStockVenta = true;

                    foreach ($ordeneslaboratorio as $ordenlaboratorio){
                        if ($ordenlaboratorio->getArmazoncliente() == '1') $hasStockVenta = true;
                    }

                    
                    for ($i = 0; $i < count($productos); $i++){

                        $productosOrdenes[$productos[$i]["idordenlaboratorio"]] = $productos[$i];

                    }

                    $hasVenta = true;
                }
            }
        }

        return $this->render('expedientes/tablaordeneslaboratorio.html.twig', [
            'ordeneslaboratorio'=>$ordeneslaboratorio,
            'hasVenta'=>$hasVenta,
            'hasStockVenta'=>$hasStockVenta,
            'productosOrdenes'=>$productosOrdenes
        ]);
    }

    /**
     * @Route("/admin/expediente-clinico/eliminar-ordenlaboratorio", name="expedientes-eliminar-ordenlaboratorio")
     */
    public function eliminarOrdenLaboratorio(Request $request): Response
    {
    
        $exito=false;
        $msj="";
        $em = $this->getDoctrine()->getManager();
        
        $idordenlaboratorio = $request->get("idordenlaboratorio");
        $refuse = $request->get("refuse");
        $refuseReason = $request->get("refuseReason");

        try{

            $Ordenlaboratorio = $em->getRepository(Ordenlaboratorio::class)->findOneBy(array('idordenlaboratorio' => $idordenlaboratorio));

            if($Ordenlaboratorio){

                $query = $em->createQuery(
                    'SELECT svol
                    FROM App\Entity\Stockventaordenlaboratorio svol
                    inner join svol.ordenlaboratorioIdordenlaboratorio ol
                    inner join svol.stockventaIdstockventa sv
                    inner join sv.stockIdstock s
                    inner join s.productoIdproducto p
                    where ol.idordenlaboratorio =:idordenlaboratorio
                    '
                )->setParameters(['idordenlaboratorio'=>$Ordenlaboratorio->getIdordenlaboratorio()]);

                $stockventaordenlaboratorios = $query->getResult();

                for ($i = 0; $i < count($stockventaordenlaboratorios); $i++){

                    $em->remove($stockventaordenlaboratorios[$i]);
                    $em->flush();
                    
                }
                
                $Ordenlaboratorio->setStatus(0);

                if ($refuse == '1'){
                    $now = new DateTime();
                    $Ordenlaboratorio->setRefusedclient('1');
                    $Ordenlaboratorio->setRefusedate($now);
                    $Ordenlaboratorio->setRefusereason($refuseReason);
                }
                
                $em->persist($Ordenlaboratorio);

                $em->flush();

                $exito = true;
            }else throw new \Exception('No se encontró la orden');
            
        }catch (\Exception $e) {
            //$msj.=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();
            $msj.=$e->getMessage();
        }

        return $this->json(['exito'=>$exito,'msj'=>$msj]);
    }

    /**
     * @Route("/admin/expediente-clinico/agregar-venta", name="expedientes-agregar-venta")
     */
    public function agregarVenta(Request $request): Response
    {
    
        $exito=false;
        $msj="";
        $em = $this->getDoctrine()->getManager();
        
        $idflujoexpediente = $request->get("idflujoexpediente");
        $idventa = $request->get("idventa");

        $Flujoexpediente = $em->getRepository(Flujoexpediente::class)->findOneBy(array('idflujoexpediente' => $idflujoexpediente));
        $Venta = $em->getRepository(Venta::class)->findOneBy(array('idventa' => $idventa));

        try{
            if($Venta && $Flujoexpediente){

                $query = $em->createQuery(
                    'SELECT fev.idflujoexpedienteventa               
                    FROM App\Entity\Flujoexpedienteventa fev
                    INNER JOIN fev.flujoexpedienteIdflujoexpediente fe
                    INNER JOIN fev.ventaIdventa v
                    WHERE fe.idflujoexpediente =:flowId AND v.idventa =:saleId
                    '
                )->setParameters(['flowId'=>$Flujoexpediente->getIdflujoexpediente(), 'saleId'=>$Venta->getIdventa()]);
                $prevFlowSale = $query->getResult();

                if (count($prevFlowSale)) throw new \Exception('Este flujo ya tiene esta venta');

                $FlowSale = new Flujoexpedienteventa();

                $FlowSale->setFlujoexpedienteIdflujoexpediente($Flujoexpediente);
                $FlowSale->setVentaIdventa($Venta);

                $em->persist($FlowSale);

                $em->flush();

                $exito = true;
            }else{
                $missingElement = $Venta ? "el flujo" : "la venta";
                throw new \Exception('No se encocntró '.$missingElement);
            }
            
        }catch (\Exception $e) {
            $msj.=$e->getMessage();
        }
        return $this->json(['exito'=>$exito,'msj'=>$msj]);
    }

    /**
     * @Route("/delete-flow-sale", name="expedientes-delete-flow-sale")
     */
    public function deleteFlowSale(Request $request): Response
    {
    
        $success=false;
        $msg="";
        $em = $this->getDoctrine()->getManager();
        
        $flowSaleId = $request->get("flowSaleId");

        $FlowSale = $em->getRepository(Flujoexpedienteventa::class)->findOneBy(array('idflujoexpedienteventa' => $flowSaleId));

        try{

            if($FlowSale){

                $saleId = $FlowSale->getVentaIdventa();

                $query = $em->createQuery(
                    'SELECT svol               
                    FROM App\Entity\Stockventaordenlaboratorio svol
                    INNER JOIN svol.stockventaIdstockventa sv
                    INNER JOIN sv.ventaIdventa v
                    where v.idventa =:saleId
                    '
                )->setParameters(['saleId'=>$saleId]);
                $svols = $query->getResult();

                foreach($svols as $Svol) $em->remove($Svol);

                $em->remove($FlowSale);
                $em->flush();

                $success = true;

            }else throw new \Exception('No se encocntró la venta');
            
        }catch (\Exception $e) {
            $msg.=$e->getMessage();
        }

        return $this->json(['success'=>$success,'msg'=>$msg]);
    }

    /**
     * @Route("/get-flow-sales-table", name="expedientes-get-flow-sales-table")
     */
    public function getFlowSalesTable(Request $request): Response
    {
    
        $em = $this->getDoctrine()->getManager();
        
        $idflujoexpediente = $request->get("idflujoexpediente");

        $Flujoexpediente = $em->getRepository(Flujoexpediente::class)->findOneBy(array('idflujoexpediente' => $idflujoexpediente));

        $msg = "";
        $sales = null;

        try{

            if($Flujoexpediente){

                $query = $em->createQuery(
                    'SELECT v.idventa, v.folio, fv.idflujoexpedienteventa
                    FROM App\Entity\Flujoexpedienteventa fv
                    INNER JOIN fv.flujoexpedienteIdflujoexpediente fe
                    INNER JOIN fv.ventaIdventa v
                    where v.status =:status and fe.idflujoexpediente =:idFlow
                    '
                )->setParameters(['status'=>"1", 'idFlow'=>$Flujoexpediente->getIdflujoexpediente()]);
                $sales = $query->getResult();

                $whereSale=" ";
    
                if (count($sales) > 0) {
                    $whereSale=" AND v.idventa IN (";
                    for($i = 0; $i < count($sales); $i++){
                        $whereSale .= $sales[$i]["idventa"];        
                        if ($i != count($sales)-1 ) $whereSale .=", ";
                    }
                    $whereSale .=") ";
                }

                $query = $em->createQuery(
                    'SELECT p.modelo, sv.cantidad, v.idventa, sv.idstockventa, m.nombre as brand,
                    s.codigobarras as sku, p.codigobarrasuniversal
                    FROM App\Entity\Stockventa sv
                    INNER JOIN sv.ventaIdventa v
                    INNER JOIN sv.stockIdstock s
                    INNER JOIN s.productoIdproducto p
                    INNER JOIN p.marcaIdmarca m
                    WHERE sv.status =:status'.$whereSale
                )->setParameters(['status'=>"1"]);
                $products = $query->getResult();
                
                $mappedSales = [];

                foreach ($products as $Product) {
                    if (!isset($mappedSales[$Product['idventa']])) $mappedSales[$Product['idventa']] = [$Product];
                    else array_push($mappedSales[$Product['idventa']], $Product);
                }

                $query = $em->createQuery(
                    'SELECT sv.idstockventa, COUNT(svol) as cantidad
                    FROM App\Entity\Stockventaordenlaboratorio svol
                    INNER JOIN svol.stockventaIdstockventa sv
                    INNER JOIN sv.ventaIdventa v
                    INNER JOIN sv.stockIdstock s
                    INNER JOIN s.productoIdproducto p
                    WHERE sv.status =:status'.$whereSale.'
                    GROUP BY sv.idstockventa'
                )->setParameters(['status'=>"1"]);
                $checkQuantitiesLeft = $query->getResult();
                
                $mappedQuantities = [];
                foreach ($checkQuantitiesLeft as $check) $mappedQuantities[$check["idstockventa"]] = $check["cantidad"];

            }else throw new \Exception('No se encocntró el flujo');
            
        }catch (\Exception $e) {
            $msg.=$e->getMessage();
        }

        return $this->render('expedientes/flow-sales-table.html.twig', [
            'msg' => $msg,
            'sales' => $sales,
            'mappedSales' => $mappedSales,
            'mappedQuantities' => $mappedQuantities
        ]);
    }

    

    /**
     * @Route("/admin/expediente-clinico/cambiar-etapa", name="expedientes-cambiar-etapa")
     */
    public function cambiarEtapa(Request $request): Response
    {

        $etapa = $request->get('etapa');
        $idflujoexpediente = $request->get('idflujoexpediente');


        $em = $this->getDoctrine()->getManager();

        $Flujoexpediente = $em->getRepository(Flujoexpediente::class)->findOneBy(array('idflujoexpediente' => $idflujoexpediente));

        if ($Flujoexpediente){

            if($etapa >  $Flujoexpediente->getEtapa()){
                $Flujoexpediente->setEtapa($etapa);
                $Flujoexpediente->setActualizacion(new DateTime());
                $em->persist($Flujoexpediente);
                $em->flush();
            }

        }

        $query = $em->createQuery(
            'SELECT g.idgraduacion, g.creacion
               FROM App\Entity\Graduacion g
               INNER JOIN g.clienteIdcliente c
               WHERE g.status =:status AND c.idcliente =:clientId
               '
        )->setParameters(['status'=>"1",'clientId'=>$Flujoexpediente->getClienteIdcliente()->getIdcliente()]);
        $prevGraduations= $query->getResult();

        $query = $em->createQuery(
            'SELECT d.idflowsigneddocuments
               FROM App\Entity\Flowsigneddocuments d
               INNER JOIN d.flujoexpedienteIdflujoexpediente fe
               WHERE d.status =:status AND fe.idflujoexpediente =:flowId
               '
        )->setParameters(['status'=>"1",'flowId'=>$Flujoexpediente->getIdflujoexpediente()]);
        $flowDocuments = $query->getResult();


        return $this->render('expedientes/flujoexpediente.html.twig', [
            'etapa' => $etapa,
            'idflujoexpediente' => $idflujoexpediente,
            'Flujoexpediente' => $Flujoexpediente,
            'prevGraduations' => $prevGraduations,
            'flowDocuments' => $flowDocuments
        ]);

    }

    /**
     * @Route("/admin/expediente-clinico/documento-expediente", name="expedientes-documento-expediente")
     */
    public function documentoExpediente(Request $request): Response
    {

        $etapa = $request->get('etapa');
        $idflujoexpediente = $request->get('idflujoexpediente');

        $tratamientos = array();
        $em = $this->getDoctrine()->getManager();

        $Flujoexpediente = $em->getRepository(Flujoexpediente::class)->findOneBy(array('idflujoexpediente' => $idflujoexpediente));
        //$Flujoexpediente = $em->getRepository(Flujoexpediente::class)->findOneBy(array('idflujoexpediente' => 1));
        $logo = "";

        $servicios = [];
        $productos = [];

        if ($Flujoexpediente){

            $query = $em->createQuery(
                'SELECT ol               
                FROM App\Entity\Ordenlaboratorio ol
                inner join ol.flujoexpedienteIdflujoexpediente fe
                where ol.status =:status and fe.idflujoexpediente =:idflujoexpediente
                '
            )->setParameters(['status'=>"1",'idflujoexpediente'=>$idflujoexpediente]);
            $ordeneslaboratorio= $query->getResult();

            $query = $em->createQuery(
                'SELECT v.folio, v.fechacreacion         
                FROM App\Entity\Flujoexpedienteventa fv
                INNER JOIN fv.flujoexpedienteIdflujoexpediente fe
                INNER JOIN fv.ventaIdventa v
                WHERE fe.idflujoexpediente =:idflujoexpediente
                '
            )->setParameters(['idflujoexpediente'=>$idflujoexpediente]);
            $sales= $query->getResult();

            $query = $em->createQuery(
                'SELECT ol.idordenlaboratorio as idordenlaboratorio, p.modelo as modelo  
                FROM App\Entity\Stockventaordenlaboratorio svol
                inner join svol.stockventaIdstockventa sv
                inner join svol.ordenlaboratorioIdordenlaboratorio ol
                inner join sv.stockIdstock s
                inner join s.productoIdproducto p
                inner join ol.flujoexpedienteIdflujoexpediente fe
                
                where ol.status =:status and fe.idflujoexpediente =:idflujoexpediente and p.ordenlaboratorio =:ordenlaboratorio'
                
            )->setParameters(['status'=>"1",'idflujoexpediente'=>$Flujoexpediente->getIdflujoexpediente(), 'ordenlaboratorio'=>'0']);
            $svolServicios= $query->getResult();

            $query = $em->createQuery(
                'SELECT ol.idordenlaboratorio, p.modelo, s.codigobarras, p.codigobarrasuniversal, m.nombre as marca
                FROM App\Entity\Stockventaordenlaboratorio svol
                inner join svol.stockventaIdstockventa sv
                inner join svol.ordenlaboratorioIdordenlaboratorio ol
                inner join sv.stockIdstock s
                inner join s.productoIdproducto p
                inner join ol.flujoexpedienteIdflujoexpediente fe
                inner join p.marcaIdmarca m
                
                where ol.status =:status and fe.idflujoexpediente =:idflujoexpediente and p.ordenlaboratorio =:ordenlaboratorio'
                
            )->setParameters(['status'=>"1",'idflujoexpediente'=>$Flujoexpediente->getIdflujoexpediente(), 'ordenlaboratorio'=>'1']);
            $svolSAlmacenables= $query->getResult();

            foreach ($svolServicios as $item) {
                $id = $item['idordenlaboratorio'];
                $modelo = $item['modelo'];

                // Check if the key already exists
                if (!isset($servicios[$id])) {
                    $servicios[$id] = [];  // Initialize it if not
                }

                // Append the modelo value to the corresponding array
                $servicios[$id][] = $modelo;
            }

            foreach ($svolSAlmacenables as $item) {
                $id = $item['idordenlaboratorio'];

                // Check if the key already exists
                if (!isset($servicios[$id])) {
                    $productos[$id] = [];  // Initialize it if not
                }

                // Append the modelo value to the corresponding array
                $productos[$id][] = $item;
            }



            
            $logo = $Flujoexpediente->getUsuarioIdusuario()->getSucursalIdsucursal()->getEmpresaIdempresa()->getLogo64();

        }

        $dompdf = new Dompdf();
        $dompdf->setPaper('A4', 'portrait');

        $html = $this->renderView(
            'expedientes/documentoexpediente.html.twig', [
                'ordeneslaboratorio' => $ordeneslaboratorio,
                'flujoexpediente' => $Flujoexpediente,
                'logo' => $logo,
                'servicios' => $servicios,
                'productos' => $productos,
                'sales' => $sales
            ]
        );

        $dompdf->loadHtml($html);

        $dompdf->render();

        $output = $dompdf->output();

        $nombreTicket=$this->sanitize($idflujoexpediente."-".time()).".pdf";
        $carpeta=$this->getParameter('carpetaDocumentosFirma');

        $Flujoexpediente->setDocumentoexpedienteclinico($nombreTicket);
        $em->persist($Flujoexpediente);
        $em->flush();

        if (!is_dir($carpeta."/". $idflujoexpediente)) {
            mkdir($carpeta."/". $idflujoexpediente);
        }

        file_put_contents($carpeta."/". $idflujoexpediente.'/'.$nombreTicket, $dompdf->output());

        $filename = $carpeta."/". $idflujoexpediente.'/'.$nombreTicket; 
        header("Content-type: application/pdf");
        header("Content-Disposition: inline; filename=\"" . $nombreTicket . "\"");
        header("Content-Length: " . filesize($filename));

        readfile($filename);


        
        $msj = "";
        $exito = false;


        return $this->render('expedientes/documentoexpediente.html.twig', [
            'ordeneslaboratorio' => $ordeneslaboratorio,
            'flujoexpediente' => $Flujoexpediente,
            'logo' => $logo,
            'servicios' => $servicios,
            'productos' => $productos,
            'sales' => $sales
        ]);

    }

    /**
     * @Route("/admin/expediente-clinico/subir-archivo", name="upload_file")
     */
    public function uploadFile(Request $request)
    {
        $exito = false;
        $msj = "";

        $file = $request->files->get('file');
        //$idgraduacion = $request->get('idgraduacion');
        $idflujoexpediente = $request->get('idflujoexpediente');

        $tipoarchivo = $request->get('tipoarchivo');
        //

        $em = $this->getDoctrine()->getManager();

        $Flujoexpediente = $em->getRepository(Flujoexpediente::class)->findOneBy(array('idflujoexpediente' => $idflujoexpediente));
        //$Graduacion = $em->getRepository(Graduacion::class)->findOneBy(array('idgraduacion' => $idgraduacion));

        try {
            if (!$file) {
                throw new \Exception('Error al cargar el archivo');
            } else {
                if ($Flujoexpediente){
                    if ($tipoarchivo == "expediente"){
                        $carpeta=$this->getParameter('carpetaDocumentosFirma');
                        $carpeta = $carpeta."/". $idflujoexpediente.'/';

                        $fileName = $this->sanitize($idflujoexpediente."-FIRMADO-".time()).".pdf";
                            
                        //$Flujoexpediente->setDocumentoexpedienteclinicofirmado1($fileName);
                        $now = new DateTime();

                        $firstUpdate = $Flujoexpediente->getEtapa() < 6;

                        $Flujoexpediente->setEtapa(6);
                        $Flujoexpediente->setFechaterminoflujo($now);

                        $Flowsigneddocument = new Flowsigneddocuments();

                        $Flowsigneddocument->setFlujoexpedienteIdflujoexpediente($Flujoexpediente);
                        $Flowsigneddocument->setCreation($now);
                        $Flowsigneddocument->setFilename($fileName);
                        
                        $em->persist($Flowsigneddocument);
                        $em->persist($Flujoexpediente);
                        $em->flush();

                        $file->move(
                            $carpeta,
                            $fileName
                        );

                        $exito = true;

                        if ($firstUpdate) {
                            $query = $em->createQuery(
                                'SELECT ol               
                                FROM App\Entity\Ordenlaboratorio ol
                                inner join ol.flujoexpedienteIdflujoexpediente fe
                                where ol.status =:status and fe.idflujoexpediente =:idflujoexpediente
                                '
                            )->setParameters(['status'=>"1",'idflujoexpediente'=>$Flujoexpediente->getIdflujoexpediente()]);
                            $laboratoryOrders= $query->getResult();

                            foreach ($laboratoryOrders as $LaboratoryOrder){
                                $LaboratoryOrder->setEtapa(3);
                                $em->persist($LaboratoryOrder);
                            }
                            $em->flush();

                        }
                    }
                    else if ($tipoarchivo == "examen"){

                        $categoriaDocumentos = $request->get('categoriaDocumentos');

                        $carpeta=$this->getParameter('carpetaDocumentosExpediente');
                        $carpeta = $carpeta."/". $idflujoexpediente.'/';
                        $ext = $file->getClientOriginalExtension();
                        $fileName = $this->sanitize($idflujoexpediente.time()).'.'.$ext;

                        $file->move(
                            $carpeta,
                            $fileName
                        );

                        $Documentosexamenvisual = new Documentosexamenvisual();

                        $Documentosexamenvisual->setFlujoexpedienteIdflujoexpediente($Flujoexpediente);
                        $Documentosexamenvisual->setCreacion(new DateTime());
                        $Documentosexamenvisual->setNombredocumento($fileName);
                        $Documentosexamenvisual->setTipodocumento($categoriaDocumentos);

                        $em->persist($Documentosexamenvisual);
                        $em->flush();

                        $exito = true;

                    }else throw new \Exception('Consulta inválida');
                }else throw new \Exception('No se encontró el flujo');
            }
        } catch (\Exception $e) {
            $msj = $e->getMessage();
        }

        return new JsonResponse(["msj" => $msj, "exito" => $exito, "request"=>$request]);
    }


    /**
    * Function: sanitize
    * Returns a sanitized string, typically for URLs.
    *
    * Parameters:
    *     $string - The string to sanitize.
    *     $force_lowercase - Force the string to lowercase?
    *     $anal - If set to *true*, will remove all non-alphanumeric characters.
    */
    function sanitize($string, $force_lowercase = true, $anal = false) {
        $strip = array("~", "`", "!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "_", "=", "+", "[", "{", "]",
            "}", "\\", "|", ";", ":", "\"", "'", "&#8216;", "&#8217;", "&#8220;", "&#8221;", "&#8211;", "&#8212;",
            "â€”", "â€“", ",", "<", ".", ">", "/", "?");
        $clean = trim(str_replace($strip, "", strip_tags($string)));
        $clean = preg_replace('/\s+/', "-", $clean);
        $clean = ($anal) ? preg_replace("/[^a-zA-Z0-9]/", "", $clean) : $clean ;
        return ($force_lowercase) ?
            (function_exists('mb_strtolower')) ?
                mb_strtolower($clean, 'UTF-8') :
                strtolower($clean) :
            $clean;
    }

    /**
     * @Route("/admin/expediente-clinico/guardar-servicios", name="guardar-servicios")
     */
    public function guardarServicios(Request $request): Response
    {
    
        $exito=false;
        $msj="";
        $em = $this->getDoctrine()->getManager();
        
        $servicios = $request->get("servicios");
        $idordenlaboratorio = $request->get("idordenlaboratorio");

        try{

            $Ordenlaboratorio = $em->getRepository(Ordenlaboratorio::class)->findOneBy(array('idordenlaboratorio' => $idordenlaboratorio));

            if($Ordenlaboratorio){

                $query = $em->createQuery(
                    'SELECT svol
                    FROM App\Entity\Stockventaordenlaboratorio svol
                    inner join svol.ordenlaboratorioIdordenlaboratorio ol
                    inner join svol.stockventaIdstockventa sv
                    inner join sv.stockIdstock s
                    inner join s.productoIdproducto p
                    where ol.status =:status and ol.idordenlaboratorio =:idordenlaboratorio and p.ordenlaboratorio =:ordenlaboratorio
                    '
                )->setParameters(['status'=>"1",'idordenlaboratorio'=>$idordenlaboratorio, 'ordenlaboratorio'=>'0']);

                $stockventaordenlaboratorios = $query->getResult();

                for ($i = 0; $i < count($stockventaordenlaboratorios); $i++){


                    $em->remove($stockventaordenlaboratorios[$i]);
                    $em->flush();
                    

                }

                
                for($i = 0; $i < count($servicios); $i++){
                    $Stockventa = $em->getRepository(Stockventa::class)->findOneBy(array('idstockventa' => $servicios[$i]));

                    $stockventaordenlaboratorio = new Stockventaordenlaboratorio();

                    $stockventaordenlaboratorio->setOrdenlaboratorioIdordenlaboratorio($Ordenlaboratorio);
                    $stockventaordenlaboratorio->setStockventaIdstockventa($Stockventa);
                    $stockventaordenlaboratorio->setCreacion(new DateTime());

                    $em->persist($stockventaordenlaboratorio);

                    $em->flush();
                }
                

                $exito = true;

            }else throw new \Exception('No se encontró la orden');
           
            
        }catch (\Exception $e) {
            //$msj.=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();
            $msj.=$e->getMessage();
        }

        return $this->json(['exito'=>$exito,'msj'=>$msj,'servicios'=>$servicios]);
    }

    /**
     * @Route("/admin/expediente-clinico/tabla-documentos", name="tabla-documentos")
     */
    public function tablaDocumentos(Request $request): Response
    {

        $em = $this->getDoctrine()->getManager();

        $idflujoexpediente = $request->get('idflujoexpediente');

        $query = $em->createQuery(
            'SELECT d
               FROM App\Entity\Documentosexamenvisual d
               where d.status =:status and d.flujoexpedienteIdflujoexpediente =:idflujoexpediente
               '
        )->setParameters(['status'=>"1",'idflujoexpediente'=>$idflujoexpediente]);
        $docuementosExpediente= $query->getResult();

        $Flujoexpediente = $em->getRepository(Flujoexpediente::class)->findOneBy(array('idflujoexpediente' => $idflujoexpediente));
        
        $documentsGraduation = [];

        if ($Flujoexpediente){
            $Graduation = $Flujoexpediente->getGraduacionIdgraduacion();
            $graduationId = ($Graduation) ? $Graduation->getIdgraduacion() : -1;

            $query = $em->createQuery(
                'SELECT dg
                   FROM App\Entity\Documentosgraduacion dg
                   where dg.status =:status and dg.graduacionIdgraduacion =:graduationId
                   '
            )->setParameters(['status'=>"1",'graduationId'=>$graduationId]);
            $documentsGraduation= $query->getResult();
        }
        
        return $this->render('expedientes/tablaDocumentos.html.twig', [
            'docuementosExpediente'=>$docuementosExpediente,
            'documentsGraduation'=> $documentsGraduation
            
        ]);

    }

    /**
     * @Route("/admin/expediente-clinico/formulario-documentos", name="formulario-documentos")
     */
    public function formularioDocumentos(Request $request): Response
    {
        $idgraduacion = $request->get('idgraduacion');
        $nombredocumento = $request->get('nombredocumento');

        

        $carpeta = "";

        return $this->render('expedientes/formularioDocumentoExpediente.html.twig', [
            'carpeta'=>$carpeta,
            'nombredocumento'=>$nombredocumento,
        ]);

    }

    /**
     * @Route("/admin/expediente-clinico/abrir-documento-examen", name="abrir-documento-examen")
     */
    public function abrirVisor(Request $request): Response
    {

        $idflujoexpediente = $request->get('idflujoexpediente');
        $firma = $request->get('firma');
        $fileExist = false;

        $em = $this->getDoctrine()->getManager();

        //$Flujoexpediente = $em->getRepository(Flujoexpediente::class)->findOneBy(array('idflujoexpediente' => $idflujoexpediente));

        $carpeta = ($firma == '1') ? $this->getParameter('carpetaDocumentosFirma') : $this->getParameter('carpetaDocumentosExpediente');
        $nombredocumento = $request->get('nombredocumento');

        $carpeta = $carpeta."/".$idflujoexpediente."/";

        if (file_exists($carpeta.$nombredocumento) and $nombredocumento != null and $nombredocumento != "") $fileExist = true;

        return $this->render('expedientes/visordocumentosexamen.html.twig', [
            'nombredocumento'=>$nombredocumento,
            'carpeta'=>$carpeta,
            'fileExist' => $fileExist,
        ]);

    }

    /**
     * @Route("/get-flow-signed-document-table", name="expedientes-get-flow-signed-document-table")
     */
    public function getFlowSignedDocumentTable(Request $request): Response
    {

        $flowId = $request->get('flowId');

        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT fsd.creation, fsd.filename, fsd.idflowsigneddocuments
               FROM App\Entity\Flowsigneddocuments fsd
               INNER JOIN fsd.flujoexpedienteIdflujoexpediente f
               where fsd.status =:status and f.idflujoexpediente =:flowId
            '
        )->setParameters(['status'=>"1",'flowId'=>$flowId]);
        $flowSignedDocuments= $query->getResult();

        return $this->render('expedientes/get-flow-signed-document-table.html.twig', [
            'flowSignedDocuments'=>$flowSignedDocuments,
        ]);

    }

    /**
     * @Route("/eliminar-documento-examen", name="eliminar-documento-examen")
     */
    public function eliminarDocumentoExamen(Request $request): Response
    {
    
        $success=false;
        $msg="";
        $em = $this->getDoctrine()->getManager();
    
        $documentId = $request->get("documentId");
        $signDocument = $request->get("signDocument");

        try{
            $Document = null;
            if ($signDocument == '1') $Document = $em->getRepository(Flowsigneddocuments::class)->findOneBy(array('idflowsigneddocuments' => $documentId));
            else $Document = $em->getRepository(Documentosexamenvisual::class)->findOneBy(array('iddocumentosexamenvisual' => $documentId));

            if($Document){
                if ($Document->getStatus() == 1){
                    $Document->setStatus(0);

                    $em->persist($Document);

                    $em->flush();

                    if ($signDocument == '1'){
                        $Flow = $Document->getFlujoexpedienteIdflujoexpediente();
                        $query = $em->createQuery(
                            'SELECT d.idflowsigneddocuments
                               FROM App\Entity\Flowsigneddocuments d
                               INNER JOIN d.flujoexpedienteIdflujoexpediente fe
                               WHERE d.status =:status AND fe.idflujoexpediente =:flowId
                               '
                        )->setParameters(['status'=>"1",'flowId'=>$Flow->getIdflujoexpediente()]);
                        $flowDocuments= $query->getResult();

                        if (count($flowDocuments) <= 0){
                            $Flow->setEtapa(5);
                            $em->persist($Flow);
                            $em->flush();
                        }
                    }

                    $success = true;

                }else throw new \Exception('El documento ya se había eliminado');

            }else throw new \Exception('No se encontró el documento');
            
        }catch (\Exception $e) {
            $msg.=$e->getMessage();
        }

        return $this->json(['success'=>$success,'msg'=>$msg]);
    }

    /**
     * @Route("/set-deliver-status", name="expedientes-set-deliver-status")
     */
    public function setDeliverStatus(Request $request): Response
    {
    
        $exito=false;
        $msj="";
        $em = $this->getDoctrine()->getManager();
        $deliverDate = null;
        
        $clinicalRecordId = $request->get("clinicalRecordId");
        $observations = $request->get("observations");

        try{

            $ClinicalRecord = $em->getRepository(Flujoexpediente::class)->findOneBy(array('idflujoexpediente' => $clinicalRecordId));

            if($ClinicalRecord){

                $deliverDate = new DateTime();
                
                $ClinicalRecord->setObservaciones($observations);

                $ClinicalRecord->setFechaentrega($deliverDate);

                $ClinicalRecord->setEtapa(7);

                $em->persist($ClinicalRecord);

                $em->flush();

                $deliverDate = $deliverDate->format('d/m/Y');

                $exito = true;
                
            }else throw new \Exception('No se encontró el flujo de expediente');
            
        }catch (\Exception $e) {
            $msj.=$e->getMessage();
        }

        return $this->json(['exito'=>$exito,'msj'=>$msj, 'deliverDate' => $deliverDate]);
    }

    /**
     * @Route("/get-refuse-form", name="expedientes-get-refuse-form")
     */
    public function getRefuseForm(Request $request): Response
    {
         
        $orderId = $request->get("orderId");

        $orderId = ($orderId) ? $orderId : 0;

        return $this->render('expedientes/get-refuse-form.html.twig', [
            'orderId'=>$orderId,
        ]);
    }

    /**
     * @Route("/admin/expediente-clinico/get-final-subjetive-preview", name="expedientes-get-final-subjetive-preview")
     */
    public function getFinalSubjetivePreview(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();

        $idflujo = $request->get("idflujoexpediente");

        $Flujoexpediente = $em->getRepository(Flujoexpediente::class)->findOneBy(array('idflujoexpediente' => $idflujo));

        if($Flujoexpediente) $Graduation = $Flujoexpediente->getGraduacionIdgraduacion();

        return $this->render('expedientes/getFinalSubjetivePreview.html.twig', [
            'Graduation'=>$Graduation,
        ]);
    }


}
