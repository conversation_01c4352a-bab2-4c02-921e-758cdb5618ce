<?php

namespace App\Controller;

use Couchbase\QueryResult;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use App\Service\TwilioService;
use App\Entity\Cliente;
use Symfony\Component\HttpFoundation\JsonResponse;
use DateTime;
use Transliterator;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;


class MessagesController extends AbstractController
{
    private $twilioService;

    public function __construct(TwilioService $twilioService)
    {
        $this->twilioService = $twilioService;
    }

    /**
     * @Route("/messages", name="app_messages")
     */
    public function index(): Response
    {
        return $this->render('messages/index.html.twig', [
            'controller_name' => 'MessagesController',
        ]);
    }

    /**
     * @Route("/dashboardMessages", name="dashboard_messages")
     */

    public function dashboardMessages()
    {

        return $this->render('messages/dashboard.html.twig', [
            'lenon' => 'lenonMessage',
        ]);
    }

    /**
     * @Route("/filters-messages", name="filters-messages")
     */
    public function filterMessages(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT ec.idempresacliente, ec.nombre AS nombreEmpresaCliente
         FROM App\Entity\Empresacliente ec
         WHERE ec.status = :status
         ORDER BY ec.nombre ASC'
        )->setParameters(['status' => '1']);

        $filters = $query->getResult();

        $query = $em->createQuery(
            'SELECT s.idsucursal, s.nombre
         FROM App\Entity\Sucursal s
         WHERE s.status = :status
         ORDER BY s.nombre ASC'
        )->setParameters(['status' => '1']);

        $locations = $query->getResult();

        return $this->render('messages/filters.html.twig', [
            'filters' => $filters,
            'locations' => $locations,
        ]);
    }

    /**
     * @Route("/tableCustomer", name="table-Customer")
     */
    public function modalCustomer(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $parameters = ['status' => '1'];

        // Definir la base de la consulta
        $qb = $em->createQueryBuilder();
        $qb->select('DISTINCT c.idcliente,c.nombre, c.apellidopaterno, c.apellidomaterno, c.edad AS Edad, c.email,
                      c.telefono AS Telefono, c.numeroempleado AS NumEmpleado, ec.nombre AS TipoCliente')
            ->from('App\Entity\Cliente', 'c')
            ->innerJoin('c.empresaclienteIdempresacliente', 'ec')
            ->leftJoin('App\Entity\Venta', 'v', 'WITH', 'c.idcliente = v.clienteIdcliente')
            ->innerJoin('v.sucursalIdsucursal', 's')
            ->where('c.status = :status')
            ->andWhere('LENGTH(c.telefono) = 10')
            ->orderBy('c.apellidopaterno', 'ASC');


        if ($request->get('nombre') != '') {
            $qb->andWhere('c.nombre LIKE :nombre');
            $parameters['nombre'] = '%' . $request->get('nombre') . '%';
        }
        if ($request->get('apellidoPaterno') != '') {
            $qb->andWhere('c.apellidopaterno LIKE :apellidoPaterno');
            $parameters['apellidoPaterno'] = '%' . $request->get('apellidoPaterno') . '%';
        }
        if ($request->get('apellidoMaterno') != '') {
            $qb->andWhere('c.apellidomaterno LIKE :apellidoMaterno');
            $parameters['apellidoMaterno'] = '%' . $request->get('apellidoMaterno') . '%';
        }
        if ($request->get('correoElectronico') != '') {
            $qb->andWhere('c.email LIKE :correoElectronico');
            $parameters['correoElectronico'] = '%' . $request->get('correoElectronico') . '%';
        }
        if ($request->get('telefono') != '') {
            $qb->andWhere('c.telefono LIKE :telefono');
            $parameters['telefono'] = '%' . $request->get('telefono') . '%';
        }
        if ($request->get('numeroEmpleado') != '') {
            $qb->andWhere('c.numeroempleado = :numeroEmpleado');
            $parameters['numeroEmpleado'] = $request->get('numeroEmpleado');
        }
        if ($request->get('dateStart') != '') {
            $formattedDate = DateTime::createFromFormat('d/m/Y', $request->get('dateStart'))->format('Y-m-d') . " 00:00:00";
            $qb->andWhere('v.fechacreacion > :dateStart');
            $parameters['dateStart'] = $formattedDate;
        }
        if ($request->get('dateEnd') != '') {
            $formattedDate = DateTime::createFromFormat('d/m/Y', $request->get('dateEnd'))->format('Y-m-d') . " 23:59:59";
            $qb->andWhere('v.fechacreacion < :dateEnd');
            $parameters['dateEnd'] = $formattedDate;
        }

        $typeCustomer = $request->get('tipoCustomer');

        if (!empty($typeCustomer) && $typeCustomer != '-1') {
            $qb->andWhere('ec.idempresacliente = :TipoCliente');
            $parameters['TipoCliente'] = $typeCustomer;
        }

        $location = $request->get('location');

        if (!empty($location) && $location != '-1') {
            $qb->andWhere('s.idsucursal = :location');
            $parameters['location'] = $location;
        }

        $ageRange = $request->get('rangoEdad');

        if ($ageRange !== "18,85") {

            list($ageMin, $ageMax) = explode(',', $ageRange);

            $qb->andWhere('c.edad >= :ageMin AND c.edad <= :ageMax');
            $parameters['ageMin'] = $ageMin;
            $parameters['ageMax'] = $ageMax;
        }

        $qb->setParameters($parameters);
        $clientes = $qb->getQuery()->getResult();

        return $this->render('messages/tableCustomer.html.twig', [
            'clientes' => $clientes,
        ]);
    }

    /**
     * @Route("/send-messages", name="send_messages", methods={"POST"})
     */
    public function sendMessages(Request $request, TwilioService $twilioService, MailerInterface $mailer): Response
    {
        try {
            $messageSMS = $request->request->get('sms_area');
            $messageEMAIL = $request->request->get('email_area');
            $messageLerma = "Hola {{1}}, te recordamos que la entrega de tus lentes de la campaña UAM LERMA será el próximo jueves 2 de mayo. ¡Nos vemos allí!";
            $clientIds = $request->request->get('clientIds', []);

            $transliterator = Transliterator::createFromRules(':: Any-Latin; :: Latin-ASCII; :: NFD; :: [:Nonspacing Mark:] Remove; :: NFC;', Transliterator::FORWARD);

            foreach ($clientIds as $clientId) {
                $clientDetails = $this->getDoctrine()->getRepository(Cliente::class)->find($clientId);
                if (!$clientDetails) {
                    continue;
                }

                // Preparar el mensaje personalizado
                $personalizedMessage = str_replace("{{1}}", $clientDetails->getNombreCompleto(), $messageLerma);


                if ($messageSMS) {
                    $twilioService->sendMsg($clientDetails->getTelefono(), $personalizedMessage);
                } elseif ($messageEMAIL) {
                    $email = (new Email())
                        ->from('<EMAIL>')
                        ->to($clientDetails->getEmail())
                        ->subject('Important Message')
                        ->text($personalizedMessage);
                    $mailer->send($email);
                } else {
                    $twilioService->sendMsgLerma($clientDetails->getTelefono(), $clientDetails->getNombreCompleto());
                }
            }

            return new JsonResponse(['message' => 'Mensajes enviados correctamente.'], Response::HTTP_OK);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Error: ' . $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * @Route("/send-messages-whats", name="send_messages", methods={"GET"})
     */
    public function sendMessagesWhats(TwilioService $twilioService): Response
    {
        $phones = [
            '5549138324',
        ];

        $lastsent = "";
        try {
            foreach ($phones as $phone) {

                $twilioService->mensajeTmp($phone);
                $lastsent = $phone;
            }
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Error: ' . $e->getMessage(), 'last' => $lastsent], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return new JsonResponse(['message' => 'Mensajes enviados correctamente.'], Response::HTTP_OK);
    }
}
