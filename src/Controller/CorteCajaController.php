<?php

namespace App\Controller;

use App\Entity\Sucursal;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use App\Entity\Pago;
/**
 * @Route("/corte-caja")
 */
class CorteCajaController extends AbstractController
{
    /**
     * @Route("/", name="corte_caja")
     */
    public function corteCaja(): Response
    {
        
        $em=$this->getDoctrine()->getManager();

        $Usuario=$this->getUser();
        $query = $em->createQuery(
            'SELECT e.idempresa, e.nombre
               FROM App\Entity\Usuarioempresapermiso uem
               inner join uem.empresaIdempresa e
               inner join uem.usuarioIdusuario u
               where e.status =:status and u.idusuario=:idusuario order by e.nombre asc 
               '
        )->setParameters(['status'=>"1",'idusuario'=>$Usuario->getIdUsuario()]);
        $empresas= $query->getResult();



        return $this->render('corte_caja/corte-caja.html.twig', [
            'empresas' => $empresas,
        ]);
    }


    /**
     * @Route("/obtener-resultados", name="corte_caja_obtener_resultados")
     */
    public function corteCajaObtenerResultados(Request $request): Response
    {
        $em=$this->getDoctrine()->getManager();

        $empresas=$request->get("empresas");
        $whereEmpresa="";

        //Empresas
        if(isset($empresas[0])){

            $whereEmpresa=" and (";

            for($i = 0; $i < count($empresas); $i++){

                $whereEmpresa .=" emp.idempresa=".$empresas[$i];

                if($i != count($empresas)-1) $whereEmpresa .=" or ";
            }
            $whereEmpresa .=") ";
            
            
        }
        else $whereEmpresa=" and emp.idempresa= 0 ";


        $query = $em->createQuery(
            'SELECT s
               FROM App\Entity\Sucursal s
               inner join s.empresaIdempresa emp
               where s.status =:status '.$whereEmpresa.'order by s.nombre asc 
               '
        )->setParameters(['status'=>"1"]);
        $sucursales= $query->getResult();

        $totalVentasCantidad="";
        $totalVentasMonto="";

        $User=$this->getUser();

        $pagosConFactura=[];
        $pagosSinFactura=[];

        $globalNumeroAutorizacionesUAM=0;



        $sucursalesData=[];
        $parametros=[];
        $conveniosDataGlobal=[];
        $tipopagoDataGlobal=[];
        $rangoCampoFechaDia = $request->get("rangoCampoFechaDia");
        $opcionesVentas = $request->get("opcionesVentas");

        $fechaInicioRangoDia = \DateTime::createFromFormat('d/m/Y', $request->get("fechaInicioRangoDia"));
        $fechaFinRangoDia = \DateTime::createFromFormat('d/m/Y', $request->get("fechaFinRangoDia"));

        $whereUsuario="";
        $whereSucursal="";

        $globalMontoVentaSinFactura=0;
        $globalMontoVentaConFactura=0;
        $tiposVentaDataGlobal=[];
        $tiposVentaDataGlobalCotizacion=[];
         if($this->isGranted('ROLE_ADMIN') ){
            if( $opcionesVentas==""){
                $whereUsuario=" and u.idusuario =:idusuario ";
                $parametrosPagos['idusuario']=$User->getIdusuario();

            }else if($opcionesVentas=="resultadoscomogerente"){
                $whereUsuario=" and gerente.idusuario =:gerente ";
                $parametrosPagos['gerente']=$User->getIdusuario();

            }else if($opcionesVentas=="resultadoscomoadministrador" ){

            }

        }else  if($this->isGranted('ROLE_VENDEDOR') || $opcionesVentas==""){
            $whereUsuario=" and u.idusuario =:idusuario ";
             $parametrosPagos['idusuario']=$User->getIdusuario();

        }


        $whereDateRangoDia = " and p.fecha >= :fechaInicioRangoDia  and p.fecha<= :fechaFinRangoDia ";
        $whereDateRangoDiaFechaVenta = " and v.fechaventa >= :fechaInicioRangoDia  and v.fechaventa<= :fechaFinRangoDia ";
        $whereDateRangoDiaFechaCotizacion = " and v.fecha >= :fechaInicioRangoDia  and v.fecha<= :fechaFinRangoDia ";
        $parametros['status']=1;

        

        foreach ($sucursales as $keySucursal =>$sucursal){
            $parametrosPagos['idsucursal']=$sucursal->getIdsucursal();
            //obtenemos las ventas


            $Pagos=[];

            $parametrosPagos['status']=1;
           if (!$fechaInicioRangoDia || !$fechaFinRangoDia) {

               $fechoInicioHoy=new \DateTime("now");
               $fechoFinHoy=new \DateTime("now");

               $parametrosPagos['fechaInicioRangoDia']=  $fechoInicioHoy->format('Y-m-d')." 00:00:00";
               $parametrosPagos['fechaFinRangoDia']=  $fechoFinHoy->format('Y-m-d')." 23:59:00";

            } else {
                //   $fechaInicioRangoDia = $fechaInicioRangoDia->modify('first day of this month 00:00:00');
                //  $currentMonthDateTime = new \DateTime();
                //  $fechaFinRangoDia = $fechaFinRangoDia->modify('last day of this month 23:59:00');
                $parametrosPagos['fechaInicioRangoDia'] = $fechaInicioRangoDia->format('Y-m-d')." 00:00:00" ;
                $parametrosPagos['fechaFinRangoDia'] =  $fechaFinRangoDia->format('Y-m-d')." 23:59:00";
            }

            $parametrosPagos['cotizacion'] ="1";



                //  echo "entra 2<br>";
                $query = $em->createQuery(
                    'SELECT  p.idpago, p.monto, p.fecha as fechaPago, p.total, pt.name as tipopago, v.idventa, v.fechaventa as fechaVenta, 
                     v.folio,v.pidiofactura,v.convenio,v.pagado,v.iva,v.total as montoVenta, v.cotizacion, tv.nombre as tipoVenta
               FROM App\Entity\Pago p
               INNER JOIN p.paymenttypeIdpaymenttype pt
               inner join p.ventaIdventa v
               inner join v.tipoventaIdtipoventa tv
                inner join v.sucursalIdsucursal s
                  inner join v.gerente gerente
               inner join v.usuarioIdusuario u
               where p.status=:status  and s.idsucursal=:idsucursal  and v.cotizacion <> :cotizacion     '.$whereDateRangoDia.' '.$whereUsuario.'
               '
                )->setParameters($parametrosPagos);
            $Ventas =  $query->getResult();

            /*******************************/
            //sacamos las autorizaciones
            //$parametrosPagos['cotizacion'] ="2";
            unset($parametrosPagos['cotizacion']);
            unset($parametrosPagos['status']);

            //  echo "entra 2<br>";
          $query = $em->createQuery(
                'SELECT  tv.nombre as tipoVenta, COUNT(tv.idtipoventa) as numeroVentas
                     
               FROM App\Entity\Venta v
               inner join v.tipoventaIdtipoventa tv
               inner join v.sucursalIdsucursal s
               inner join v.gerente gerente
               inner join v.usuarioIdusuario u
               where v.status=1  and s.idsucursal=:idsucursal  and v.cotizacion <> 1      '.$whereDateRangoDiaFechaVenta.' '.$whereUsuario.'

               group by tv.idtipoventa ,tv.nombre
               '
            )->setParameters($parametrosPagos);
            $tiposVenta =  $query->getResult();

            $query = $em->createQuery(
                'SELECT  tv.nombre as tipoVenta, COUNT(tv.idtipoventa) as numeroVentas
                     
               FROM App\Entity\Venta v
               inner join v.tipoventaIdtipoventa tv
               inner join v.sucursalIdsucursal s
               inner join v.gerente gerente
               inner join v.usuarioIdusuario u
               where v.status=1  and s.idsucursal=:idsucursal and v.cotizacion = 1 '.$whereDateRangoDiaFechaCotizacion.' '.$whereUsuario.'
               group by tv.idtipoventa
               '
            )->setParameters($parametrosPagos);
            $cotizaciones =  $query->getResult();

            $sucursalesData[$sucursal->getNombre()]=['nombre'=>$sucursal->getNombre(),'ventas'=>$Ventas,'cotizaciones' => $cotizaciones, 'tiposVenta'=>$tiposVenta];

        }

      //scamos los resultados por tipo de venta

        foreach ($sucursalesData as $keySucursal =>$sucursal){
            $conveniosData=[];
            $tipopagoData=[];


            if(!isset($sucursalesData[$keySucursal]['confactura'])){
                $sucursalesData[$keySucursal]['confactura']=[];
            }
            if(!isset($sucursalesData[$keySucursal]['montoVentaConFactura'])){
                $sucursalesData[$keySucursal]['montoVentaConFactura']=0;
            }
            if(!isset($sucursalesData[$keySucursal]['sinfactura'])){
                $sucursalesData[$keySucursal]['sinfactura']=[];
            }
            if(!isset($sucursalesData[$keySucursal]['montoVentaSinFactura'])){
                $sucursalesData[$keySucursal]['montoVentaSinFactura']=0;
            }
            if(!isset($sucursalesData[$keySucursal]['autorizacionesUAM'])){
                $sucursalesData[$keySucursal]['autorizacionesUAM']=0;
            }

            foreach ($sucursal['tiposVenta'] as $keyTipoVenta =>$tipoVenta){
                if(!isset($tiposVentaDataGlobal['tiposVenta'][$tipoVenta['tipoVenta']])){
                    $tiposVentaDataGlobal['tiposVenta'][$tipoVenta['tipoVenta']]="";

                }
                $tiposVentaDataGlobal['tiposVenta'][$tipoVenta['tipoVenta']]=$tipoVenta['numeroVentas'];
            }

            foreach ($sucursal['cotizaciones'] as $keyTipoVenta =>$tipoVenta){
                if(!isset($tiposVentaDataGlobalCotizacion['cotizaciones'][$tipoVenta['tipoVenta']])){
                    $tiposVentaDataGlobalCotizacion['cotizaciones'][$tipoVenta['tipoVenta']]="";

                }
                $tiposVentaDataGlobalCotizacion['cotizaciones'][$tipoVenta['tipoVenta']]=$tipoVenta['numeroVentas'];
            }



            foreach ($sucursal['ventas'] as $keyVenta =>$venta){
                $sucursalesData[$keySucursal]['conveniosData']=[];

              /*  if(!isset($tiposVentaDataGlobal['tiposVenta'])){
                    $tiposVentaDataGlobal['tiposVenta']=[];

                }*/
                
                if($venta['convenio'] != "UAM"){
                    if($venta['pidiofactura']=="1"){

                        $pagosConFactura[]=$venta;

                    }else{
                        $pagosSinFactura[]=$venta;
                    }
                    if(!isset($sucursalesData[$keySucursal]['tipopagoData'][$venta['tipopago']]['nombre'])){
                        $sucursalesData[$keySucursal]['tipopagoData'][$venta['tipopago']]['nombre']=$venta['convenio'];
                    }

                    if(!isset($sucursalesData[$keySucursal]['tipopagoData'][$venta['tipopago']]['tipopago'])){
                        $sucursalesData[$keySucursal]['tipopagoData'][$venta['tipopago']]['tipopago']=$venta['tipopago'];
                    }
                    if(!isset($sucursalesData[$keySucursal]['tipopagoData'][$venta['tipopago']]['fechaPago'])){
                        $sucursalesData[$keySucursal]['tipopagoData'][$venta['tipopago']]['fechaPago']=$venta['fechaPago'];
                    }

                    if(!isset($sucursalesData[$keySucursal]['tipopagoData'][$venta['tipopago']]['fechaVenta'])){
                        $sucursalesData[$keySucursal]['tipopagoData'][$venta['tipopago']]['fechaVenta']=$venta['fechaVenta'];
                    }
                    if(!isset($sucursalesData[$keySucursal]['tipopagoData'][$venta['tipopago']]['folio'])){
                        $sucursalesData[$keySucursal]['tipopagoData'][$venta['tipopago']]['folio']=$venta['folio'];
                    }
                    if(!isset($sucursalesData[$keySucursal]['tipopagoData'][$venta['tipopago']]['folio'])){
                        $sucursalesData[$keySucursal]['tipopagoData'][$venta['tipopago']]['folio']=$venta['folio'];
                    }

                    if(!isset($sucursalesData[$keySucursal]['tipopagoData'][$venta['tipopago']]['pidiofactura'])){
                        $sucursalesData[$keySucursal]['tipopagoData'][$venta['tipopago']]['folio']=$venta['pidiofactura'];
                    }

                    if($venta['pidiofactura']=="1"){
                        $sucursalesData[$keySucursal]['confactura'][]=$venta;
                        $sucursalesData[$keySucursal]['montoVentaConFactura']+=$venta['monto'];
                        $globalMontoVentaConFactura+=$venta['monto'];
                    }else{
                        $sucursalesData[$keySucursal]['sinfactura'][]=$venta;
                        $sucursalesData[$keySucursal]['montoVentaSinFactura']+=$venta['monto'];
                        $globalMontoVentaSinFactura+=$venta['monto'];
                    }


                    /* Datos Global */



                    if(!isset($conveniosDataGlobal[$venta['tipopago']]['nombre'])){
                        $conveniosDataGlobal[$venta['tipopago']]['nombre']="";
                        $conveniosDataGlobal[$venta['tipopago']]['nombre']=$venta['tipopago'];
                    }

                    if(!isset($conveniosDataGlobal[$venta['tipopago']]['totalPagos'])){
                        $conveniosDataGlobal[$venta['tipopago']]['totalPagos']=0;
                    }
                    $conveniosDataGlobal[$venta['tipopago']]['totalPagos']+=$venta['monto'];


                    if(!isset( $sucursalesData[$keySucursal]['tipopagoData'][$venta['tipopago']]['pagos'])){
                        $sucursalesData[$keySucursal]['tipopagoData'][$venta['tipopago']]['pagos']=[];
                    }
                    if(!isset($sucursalesData[$keySucursal]['tipopagoData'][$venta['tipopago']]['totalPagosMonto'])){
                        $sucursalesData[$keySucursal]['tipopagoData'][$venta['tipopago']]['totalPagosMonto']=0;
                    }
                    /* variables globales*/


                    if(!isset($conveniosDataGlobal[$venta['tipopago']]['pagos'])){
                        $conveniosDataGlobal[$venta['tipopago']]['pagos']=[];
                    }
                    if(!isset($conveniosDataGlobal[$venta['tipopago']]['totalPagosMonto'])){
                        $conveniosDataGlobal[$venta['tipopago']]['totalPagosMonto']=0;
                    }

                    if(!isset($conveniosDataGlobal[$venta['tipopago']]['totalPagosMonto'])){
                        $conveniosDataGlobal[$venta['tipopago']]['totalPagosMonto']=0;
                    }
                    /*************************************************/



                    if(!isset($tipopagoData[$venta['tipopago']]['nombre'])){
                        $tipopagoData[$venta['tipopago']]['nombre']=$venta['tipopago'];
                    }
                    if(!isset($tipopagoDataGlobal[$venta['tipopago']]['nombre'])){
                        $tipopagoDataGlobal[$venta['tipopago']]['nombre']=$venta['tipopago'];
                    }

                    if(!isset($tipopagoData[$venta['tipopago']]['totalPagosMonto'])){
                        $tipopagoData[$venta['tipopago']]['totalPagosMonto']=0;
                    }
                    if(!isset($tipopagoDataGlobal[$venta['tipopago']]['totalPagosMonto'])){
                        $tipopagoDataGlobal[$venta['tipopago']]['totalPagosMonto']=0;
                    }
                    $tipopagoData[$venta['tipopago']]['totalPagosMonto']+=$venta['monto'];
                    $tipopagoDataGlobal[$venta['tipopago']]['totalPagosMonto']+=$venta['monto'];


                    if(!isset($tipopagoData[$venta['tipopago']]['noPagos'])){
                        $tipopagoData[$venta['tipopago']]['noPagos']=0;
                    }
                    if(!isset($tipopagoDataGlobal[$venta['tipopago']]['noPagos'])){
                        $tipopagoDataGlobal[$venta['tipopago']]['noPagos']=0;
                    }
                    $tipopagoData[$venta['tipopago']]['noPagos']+=1;
                    $tipopagoDataGlobal[$venta['tipopago']]['noPagos']+=1;

                    if(!isset($tipopagoData[$venta['tipopago']]['pagos'])){
                        $tipopagoData[$venta['tipopago']]['pagos']=[];
                    }
                    if(!isset($tipopagoDataGlobal[$venta['tipopago']]['pagos'])){
                        $tipopagoDataGlobal[$venta['tipopago']]['pagos']=[];
                    }
                    $tipopagoData[$venta['tipopago']]['pagos'][]=$venta;
                    $tipopagoDataGlobal[$venta['tipopago']]['pagos'][]=$venta;

                    /*******************************/

                    $venta['pagos'][]=$venta;


                  //  $sucursalesData[$keySucursal]['tipopago'][$venta['tipopago']]['totalPagosMonto']+=$venta['monto'];
                    /* variebla global*/
                    $conveniosDataGlobal[$venta['tipopago']]['totalPagosMonto']+=$venta['monto'];


                    $sucursalesData[$keySucursal]['tipopago'][$venta['tipopago']]['ventas'][]=$venta;
                    $conveniosDataGlobal[$venta['tipopago']]['ventas'][]=$venta;
                }else{
                    //se sumas a las autorizaciones
                    $sucursalesData[$keySucursal]['autorizacionesUAM']+=1;
                    $globalNumeroAutorizacionesUAM+=1;

                }


            }
           // $sucursalesData[$keySucursal]['conveniosData']=$conveniosData;
            $sucursalesData[$keySucursal]['tipopagoData']=$tipopagoData;
            unset($conveniosData);
            unset($tipopagoData);

        }
        
        
        


        return $this->render('corte_caja/corte-caja-obtener-resultados.html.twig', [
          'sucursalesData'=>$sucursalesData,
            'conveniosDataGlobal'=>$conveniosDataGlobal,
            'tipopagoDataGlobal'=>$tipopagoDataGlobal,
            'pagosSinFactura'=>$pagosSinFactura,
            'pagosConFactura'=>$pagosConFactura,
            'globalMontoVentaConFactura'=>$globalMontoVentaConFactura,
            'globalMontoVentaSinFactura'=>$globalMontoVentaSinFactura,
            'fechaInicioRangoDia'=>$fechaInicioRangoDia,
            'fechaFinRangoDia'=>$fechaFinRangoDia,
            'globalNumeroAutorizacionesUAM'=>$globalNumeroAutorizacionesUAM,
            'tiposVentaDataGlobal'=>$tiposVentaDataGlobal,
            'tiposVentaDataGlobalCotizacion' => $tiposVentaDataGlobalCotizacion,

        ]);
    }






}
