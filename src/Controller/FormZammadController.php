<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use App\Form\SupportFormType;
use GuzzleHttp\Client;

class FormZammadController extends AbstractController
{
    /**
     * @Route("/formulario-masoftcode", name="support_masoftcode")
     */
    public function support(Request $request): Response
    {
        $client = new \GuzzleHttp\Client();

        $client = new Client([
            'url'           => 'https://myzammad.com',
            'username'      => '<EMAIL>',
            'password'      => 'mypassword',
        ]);

        $response = $client->post('http://104.198.241.255:8080/api/v1/tickets', [
            'headers' => [
                'Authorization' => 'Bearer lX73L3A_WrZ0FGXA1bmFZEGL3k67OJeQWgN3KwjN4Pw',
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'title' => 'Ticket desde formulario personalizado',
                'group' => 'Support', // Asegúrate de que "Support" es un grupo válido
                'customer' => '<EMAIL>', // Asegúrate de que es un correo válido
                'article' => [
                    'subject' => 'Problema reportado por el cliente',
                    'body' => 'Detalles del problema...',
                    'type' => 'email', // Asegúrate de que "email" es un tipo válido
                ],
                'priority' => 3, // Asegúrate de que la prioridad está dentro de los valores permitidos
            ],
        ]);

        if ($response->getStatusCode() === 201) {
            echo "Ticket creado con éxito!";
        } else {
            echo "Hubo un error al crear el ticket.";
        }


        return $this->render('form_zammad/index.html.twig', [
        ]);
    }
}
