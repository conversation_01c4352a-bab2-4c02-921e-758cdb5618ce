<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Symfony\Bundle\MonologBundle\SwiftMailer;
use Symfony\Bundle\SwiftmailerBundle;
use Swift_SmtpTransport;
use Swift_Mailer;
use Swift_Message;

class VisorAnuncioController extends AbstractController
{

    private $mailer;

    public function __construct(MailerInterface $mailer)
    {
        $this->mailer = $mailer;
    }


     /**
     * @Route("/abrir-visor-anuncios", name="app_abrir_visor_anuncio")
     */
    public function VisorAnuncio(Request $request): Response {
        $em = $this->getDoctrine()->getManager();
        $idanuncios = $request->get("idanuncios");


        $query = $em->createQuery('SELECT 
        a.texto
        FROM App\Entity\Anuncios a
       
        WHERE a.status=:status and a.idanuncios=:idanuncios
        '
        )->setParameters(['status'=>"1",'idanuncios'=>$idanuncios]);

        $texto=$query->getOneOrNullResult();


        $nombretexto = $texto['texto'];
        $rutaCarpeta = $this->getParameter('carpetaAnuncios');
        
        
        return $this->render('visor_anuncio/visorAnuncio.html.twig', [
            'texto'=>$nombretexto,
            'carpetaAnuncios'=>$rutaCarpeta  
        ]);
    }

    /**
     * @Route("/enviar-email", name="app_enviar-email")
     */
    public function sendEmail()
    {
        // Crear un nuevo objeto Email
        $email = (new Email())
            ->from('<EMAIL>')
            ->to('<EMAIL>')
            ->subject('Asunto del correo')
            ->text('Texto del correo')
            ->html('<p>HTML del correo</p>');

        // Enviar el correo
        $this->mailer->send($email);
        

        return $this->render('', [
            
        ]);
    }

    /**
     * @Route("/test-email", name="app_enviar-email")
     */
    public function testSwifmailer()
    {
        $transport = (new Swift_SmtpTransport('smtp.gmail.com',  465, 'ssl'))
            ->setUsername('<EMAIL>')
            ->setPassword('xkazzdgozdgbzvpq')
        ;

// Create the Mailer using your created Transport
        $mailer = new Swift_Mailer($transport);

// Create a message
        $message = (new Swift_Message('Wonderful Subject'))
            ->setFrom(['<EMAIL>' => 'John Doe'])
            ->setTo(['<EMAIL>'])
            ->setBody('Here is the message itself')
        ;

// Send the message
        $result = $mailer->send($message);

        return $this->render('', [

        ]);
    }

}



