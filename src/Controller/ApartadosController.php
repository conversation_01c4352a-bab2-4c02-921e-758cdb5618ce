<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Psr\Log\LoggerInterface;
class ApartadosController extends AbstractController
{
    /**
     * @Route("/reporte/apartados", name="app_apartados")
     */

    public function index(): Response
    {
        $em = $this->getDoctrine()->getManager();
        $Usuario = $this->getUser();

        // Primera consulta para obtener empresas del usuario
        $queryEmpresas = $em->createQuery(
            'SELECT e.idempresa, e.nombre
        FROM App\Entity\Usuarioempresapermiso uem
        INNER JOIN uem.empresaIdempresa e
        INNER JOIN uem.usuarioIdusuario u
        WHERE e.status = :status AND u.idusuario = :idusuario
        ORDER BY e.nombre ASC'
        )->setParameters(['status' => "1", 'idusuario' => $Usuario->getIdUsuario()]);

        $empresas = $queryEmpresas->getResult();

        // Consulta para obtener modelos de productos
        $queryProductos = $em->createQuery(
            'SELECT DISTINCT p.modelo
        FROM App\Entity\Stockventa sv
        INNER JOIN sv.stockIdstock s
        INNER JOIN s.productoIdproducto p
        INNER JOIN sv.ventaIdventa v
        WHERE sv.status = :status AND v.status = :status'
        )->setParameters(['status' => "1"]);

        $productos = $queryProductos->getResult();

        return $this->render('apartados/index.html.twig', [
            'empresas' => $empresas,
            'productos' => array_column($productos, 'modelo'), // Pasamos solo los modelos
        ]);
    }


    /**
     * @Route("/get-sucursales/{empresaId}", name="get-sucursales" , requirements={"empresaId"="\d+"})
     */

    public function getSucursales($empresaId): Response
    {
        $em = $this->getDoctrine()->getManager();

        $querySucursales = $em->createQuery(
            'SELECT su.idsucursal, su.nombre
         FROM App\Entity\Sucursal su
         WHERE su.empresaIdempresa = :empresaId AND su.status = :status'
        )->setParameters([
            'empresaId' => $empresaId,
            'status' => '1'
        ]);

        $sucursales = $querySucursales->getResult();

        return $this->json($sucursales);
    }

    /**
     * @Route("/get-vendedores/{sucursalId}", name="get_vendedores", requirements={"sucursalId"="\d+"})
     */
    public function getVendedores(int $sucursalId): Response
    {
        $em = $this->getDoctrine()->getManager();

        $queryVendedores = $em->createQuery(
            'SELECT u.idusuario, u.nombre, u.apellidopaterno, u.apellidomaterno
         FROM App\Entity\Usuario u
         INNER JOIN u.sucursalIdsucursal su
         WHERE su.idsucursal = :sucursalId AND u.status = :status'
        )->setParameters([
            'sucursalId' => $sucursalId,
            'status' => '1'
        ]);

        $vendedores = $queryVendedores->getResult();

        return $this->json($vendedores);
    }

    /**
     * @Route("/tabla-apartados", name="app-tabla-apartados")
     */
    public function tablaApartados(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $sucursal = $request->request->get('sucursal');
        $modelo = $request->request->get('modelo');

        $fechaInicioObj = \DateTime::createFromFormat('d/m/Y', $request->get("fechaInicio"));
        $fechaFinObj = \DateTime::createFromFormat('d/m/Y', $request->get("fechaFin"));

        if (!$fechaInicioObj || !$fechaFinObj) {
            $this->addFlash('error', 'Las fechas proporcionadas no son válidas.');
            return $this->redirectToRoute('app_tabla_apartados');
        }

        // Construir la consulta DQL
        $dql = 'SELECT p.modelo, e.nombre AS Empresa, su.nombre AS Sucursal,
                s.codigobarras AS Sku, v.fechacreacion AS FechaCreacion
            FROM App\Entity\Stockventa sv
            INNER JOIN sv.stockIdstock s
            INNER JOIN s.productoIdproducto p
            INNER JOIN sv.ventaIdventa v
            INNER JOIN v.sucursalIdsucursal su
            INNER JOIN su.empresaIdempresa e
            WHERE sv.status = :status
            AND v.status = :status';

        $parameters = [
            'status' => '1'
        ];

        if ($modelo) {
            $dql .= ' AND p.modelo LIKE :modelo';
            $parameters['modelo'] = '%' . $modelo . '%';
        }

        if ($sucursal && $sucursal != '-1') {
            $dql .= ' AND su.idsucursal = :sucursal';
            $parameters['sucursal'] = $sucursal;
        }

        if ($fechaInicioObj && $fechaFinObj) {
            $dql .= ' AND v.fechacreacion BETWEEN :fechaInicio AND :fechaFin';
            $parameters['fechaInicio'] = $fechaInicioObj->format('Y-m-d');
            $parameters['fechaFin'] = $fechaFinObj->format('Y-m-d');
        }


        $query = $em->createQuery($dql)->setParameters($parameters);
        $productosApartados = $query->getResult();


        return $this->render('apartados/tablaApartados.html.twig', [
            'productosApartados' => $productosApartados,
        ]);
    }

}