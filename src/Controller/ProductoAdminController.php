<?php
// src/Controller/CarAdminController.php

namespace App\Controller;

use Sonata\AdminBundle\Controller\CRUDController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Symfony\Component\HttpFoundation\Request;
class ProductoAdminController extends CRUDController
{
  /**
   * @param $id
   */
  public function eliminarAction($id)
  {
      $object = $this->admin->getSubject();

      if (!$object) {
          throw new NotFoundHttpException(sprintf('unable to find the object with id: %s', $id));
      }

      // Be careful, you may need to overload the __clone method of your object
      // to set its id to null !
    //  $clonedObject = clone $object;

      $object->setStatus("0");

      $this->admin->update($object);

      $this->addFlash('sonata_flash_success', 'Cloned successfully');

      return new RedirectResponse($this->admin->generateUrl('list'));
  }
  public function batchActionEliminar(ProxyQueryInterface $selectedModelQuery, Request $request = null)
  {

    $this->admin->checkAccess('edit');
        $this->admin->checkAccess('delete');

        $modelManager = $this->admin->getModelManager();



        $selectedModels = $selectedModelQuery->execute();

        // do the merge work here

        try {
            foreach ($selectedModels as $selectedModel) {
              $selectedModel->setStatus("0");


              $this->admin->update($selectedModel);
              $modelManager->update($selectedModel);
              //  $modelManager->delete($selectedModel);
            }

          //  $modelManager->update($selectedModel);
        } catch (\Exception $e) {
            $this->addFlash('sonata_flash_e', 'Error al eliminar el producto');

            return new RedirectResponse(
                $this->admin->generateUrl('list', [
                    'filter' => $this->admin->getFilterParameters()
                ])
            );
        }

        $this->addFlash('sonata_flash_success', 'Eliminado(s) Exitosamente');

        return new RedirectResponse(
            $this->admin->generateUrl('list', [
                'filter' => $this->admin->getFilterParameters()
            ])
        );
  }
}
