<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;

class ReferenciasController extends AbstractController
{
    /**
     * @Route("/referencias", name="app_referencias")
     */
    public function index(): Response
    {
        return $this->render('referencias/index.html.twig', [
            'controller_name' => 'ReferenciasController',
        ]);
    }

    /**
     * @Route("/filters", name="filters")
     */
    public function filters(): Response
    {
        $em = $this->getDoctrine()->getManager();
        $filters = [];
        $success = false;
        $message = "";

        $query = $em->createQuery(
            'SELECT  s
            FROM App\Entity\Sucursal s
            WHERE s.status = :status'
        )->setParameters(['status' => '1']);

        $sucursales = $query->getResult();

        $query = $em->createQuery(
            'SELECT e
            FROM App\Entity\Empresa e
            WHERE e.status=:status'
        )->setParameters(['status' => '1']);

        $empresas = $query->getResult();

        $success = true;

        return $this->render('referencias/filters.html.twig', [
            'sucursales' => $sucursales,
            'empresas' => $empresas
        ]);
    }

    /**
     * @Route("/tableReference", name="table-reference")
     */

    public function tableReference(Request $request): Response
    {
        try {

            $em = $this->getDoctrine()->getManager();
            $reference = [];
            $success = false;
            $message = "";

            $tipoventas = $request->get("tipoventas");
            $sucursales = $request->get("sucursales");
            $idempresa = $request->get("idempresa");
            $saleQuotations = $request->get("saleQuotations");


            $fechaInicioObj = \DateTime::createFromFormat('d/m/Y', $request->get("fechaInicio"));
            $fechaFinObj = \DateTime::createFromFormat('d/m/Y', $request->get("fechaFin"));


            if (!$fechaInicioObj || !$fechaFinObj) {
                $fechaInicioObj = new \DateTime("now");
                $fechaFinObj = new \DateTime("now");
            }

            $usarFechasDeVenta = in_array('0', $saleQuotations);
            $usarFechasDeCotizacion = in_array('1', $saleQuotations);


            $whereFecha = "";
            if ($usarFechasDeCotizacion == "1") { // Si se seleccionó "Fecha de Creación"
                $whereFecha = " AND v.fechacreacion BETWEEN :fechaInicio AND :fechaFin";
            } else { // Si se seleccionó "Fecha de Venta" o cualquier otro valor por defecto
                $whereFecha = " AND v.fechaventa BETWEEN :fechaInicio AND :fechaFin";
            }

            $fechaInicio = $fechaInicioObj->format('Y-m-d') . " 00:00:00";
            $fechaFin = $fechaFinObj->format('Y-m-d') . " 23:59:59";

            $parametros = [
                'status' => 1,
                'liquidada' => 1,
                'idempresa' => $idempresa,
                'fechaInicio' => $fechaInicio,
                'fechaFin' => $fechaFin
            ];

            $whereConditions = [];


            //Tipos de venta
            if (!empty($tipoventas)) {
                $tipoVentaConditions = [];
                foreach ($tipoventas as $index => $tipoventa) {
                    $tipoVentaConditions[] = "tv.idtipoventa = :tipoventa{$index}";
                    $parametros["tipoventa{$index}"] = $tipoventa[0];
                }
                $whereConditions[] = '(' . implode(' OR ', $tipoVentaConditions) . ')';
            }

            //Sucursales
            if (!empty($sucursales)) {
                $sucursalConditions = [];
                foreach ($sucursales as $index => $sucursal) {
                    $sucursalConditions[] = "s.idsucursal = :sucursal{$index}";
                    $parametros["sucursal{$index}"] = $sucursal;
                }
                $whereConditions[] = '(' . implode(' OR ', $sucursalConditions) . ')';
            }

            if (!empty($saleQuotations)) {
                $saleQuotationConditions = [];
                foreach ($saleQuotations as $index => $saleQuotation) {
                    $saleQuotationConditions[] = "v.cotizacion = :saleQuotation{$index}";
                    $parametros["saleQuotation{$index}"] = $saleQuotation;
                }
                $whereConditions[] = '(' . implode(' OR ', $saleQuotationConditions) . ')';
            }

            // Construir la consulta dinámica
            $whereClause = !empty($whereConditions) ? ' AND ' . implode(' AND ', $whereConditions) : '';


            $query = $em->createQuery(
                'SELECT v.folio, v.fechacreacion, v.fechaventa, c.nombre AS NombreCliente, c.apellidopaterno AS Paterno, c.apellidomaterno AS Materno,
                sr.name, tv.nombre AS TipoVenta, e.idempresa, e.nombre AS Empresa, s.nombre AS Sucursal
                FROM App\Entity\Venta v
                INNER JOIN v.clienteIdcliente c
                INNER JOIN c.sellreferenceIdsellreference sr
                INNER JOIN v.tipoventaIdtipoventa tv
                INNER JOIN v.sucursalIdsucursal s
                INNER JOIN s.empresaIdempresa e
                WHERE v.status = :status AND v.liquidada = :liquidada AND e.idempresa = :idempresa' . $whereClause . $whereFecha
            )->setParameters(array_merge($parametros, ['fechaInicio' => $fechaInicio, 'fechaFin' => $fechaFin]));

            $reference = $query->getResult();
            $success = true;
        } catch (\Exception $e) {
            // Aquí capturas cualquier excepción y puedes decidir cómo manejarla
            $success = false;
            echo ($e->getMessage());
        }

        return $this->render('referencias/tableReference.html.twig', [
            'reference' => $reference,
            'succes' => $success,
            'message' => $message,
        ]);
    }
}
