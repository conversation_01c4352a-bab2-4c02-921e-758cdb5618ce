<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;


class ReporteController extends AbstractController
{
    /**
     * @Route("/reporte", name="reporte-producto")
     */
    public function index(): Response
    {
        $em=$this->getDoctrine()->getManager();

        $Usuario=$this->getUser();
        $query = $em->createQuery(
            'SELECT e.idempresa, e.nombre
               FROM App\Entity\Usuarioempresapermiso uem
               inner join uem.empresaIdempresa e
               inner join uem.usuarioIdusuario u
               where e.status =:status and u.idusuario=:idusuario order by e.nombre asc 
               '
        )->setParameters(['status'=>"1",'idusuario'=>$Usuario->getIdUsuario()]);
        $empresas= $query->getResult();

        return $this->render('reporte/producto.html.twig', [
            'empresas'=>$empresas
        ]);
    }


    /**
     * @Route("/reporte-obtener-sucursal", name="reporte-obtener-sucursal")
    */
    public function obtenerSucursal(Request $request): Response
    {
        $em=$this->getDoctrine()->getManager();
        $idempresa=$request->get("idempresa");
        
        $tipo = 'sucursal';

        //var_dump($idempresa);
        $query = $em->createQuery(
            'SELECT s.idsucursal, s.nombre
               FROM App\Entity\Sucursal s
               inner join s.empresaIdempresa emp
               where s.status =:status and emp.idempresa=:idempresa and s.tipo =:tipo order by s.nombre asc 
               '
        )->setParameters(['status'=>"1", 'idempresa'=>$idempresa, 'tipo'=>$tipo]);
        $sucursales= $query->getResult();

        $tipo ='tipoventa';

        
        $query = $em->createQuery(
            'SELECT tv.idtipoventa, tv.nombre
             FROM App\Entity\Tipoventa tv
             WHERE tv.status = :status
             ORDER BY tv.nombre ASC'
        )->setParameter('status', "1");
        
        $tiposVenta = $query->getResult();

        $tipo = 'bodega';

        //var_dump($idempresa);
        $query = $em->createQuery(
            'SELECT s.idsucursal, s.nombre
               FROM App\Entity\Sucursal s
               inner join s.empresaIdempresa emp
               where s.status =:status and emp.idempresa=:idempresa and s.tipo =:tipo order by s.nombre asc 
               '
        )->setParameters(['status'=>"1", 'idempresa'=>$idempresa, 'tipo'=>$tipo]);
        $bodegas= $query->getResult();


        $tipo = 'campaña';

        //var_dump($idempresa);
        $query = $em->createQuery(
            'SELECT s.idsucursal, s.nombre
               FROM App\Entity\Sucursal s
               inner join s.empresaIdempresa emp
               where s.status =:status and emp.idempresa=:idempresa and s.tipo =:tipo order by s.nombre asc 
               '
        )->setParameters(['status'=>"1", 'idempresa'=>$idempresa, 'tipo'=>$tipo]);
        $campañas= $query->getResult();


        $query_clase = $em->createQuery(
            'SELECT c.idclase, c.nombre
               FROM App\Entity\Clase c
               inner join c.empresaIdempresa emp
               where c.status =:status and emp.idempresa=:idempresa order by c.nombre asc 
               '
        )->setParameters(['status'=>"1", 'idempresa'=>$idempresa]);
        $clases= $query_clase->getResult();


        return $this->render('reporte/sucursales1.html.twig', [
            'sucursales'=>$sucursales,
            'tiposVenta'=>$tiposVenta,
            'bodegas'=>$bodegas,
            'campañas'=>$campañas,
            'clases'=>$clases
        ]);
    }
     /**
     * @Route("/almacen-obtener-informacion-producto", name="almacen-obtener-informacion-producto")
     */
    public function obtenerInformacionProducto(Request $request): Response
    {
        $em=$this->getDoctrine()->getManager();
        $clases=$request->get("clases");
        $sucursales=$request->get("sucursales");
        $categorias=$request->get("categorias");
        $marcas=$request->get("marcas"); 
        $tipoventas=$request->get("tipoventas");
        $productType=$request->get("productType");
        
        $parametros1=[];
        
        $fechaInicio = \DateTime::createFromFormat('d/m/Y', $request->get("fechaInicio"));
        $fechaFin = \DateTime::createFromFormat('d/m/Y', $request->get("fechaFin"));
            
        if (!$fechaInicio || !$fechaFin) {
            $fechaInicio = new \DateTime("now");
            $fechaFin = new \DateTime("now");
        } 

        $parametros1['fechaInicio'] = $fechaInicio->format('Y-m-d')." 00:00:00" ;
        $parametros1['fechaFin'] =  $fechaFin->format('Y-m-d')." 23:59:00";
        $parametros1['status']=1;

        //Sucursales
        $whereSucursal="";
        if(isset($sucursales[0])){

            $whereSucursal=" and (";

            for($i = 0; $i < count($sucursales); $i++){

                $whereSucursal .=" suc.idsucursal=".$sucursales[$i];

                if($i != count($sucursales)-1) $whereSucursal .=" or ";

            }
            $whereSucursal .=") ";
            
        }

        //Tipo de ventas
        $whereSaleType="";
        if(isset($tipoventas[0])){

            $whereSaleType=" and (";

            for($i = 0; $i < count($tipoventas); $i++){

                $whereSaleType .=" sl.idtipoventa=".$tipoventas[$i];

                if($i != count($tipoventas)-1) $whereSaleType .=" or ";

            }
            $whereSaleType .=") ";
            
        }
        //else $whereSaleType=" and sl.idtipoventa= 0 ";

        //Clases
        $whereClase="";

        if(isset($clases[0])){

            $whereClase=" and (";

            for($i = 0; $i < count($clases); $i++){

                $whereClase .=" c.idclase=".$clases[$i];

                if($i != count($clases)-1) $whereClase .=" or ";
            }
            $whereClase .=") ";
            
            
        }
        //else $whereClase=" and c.idclase= 0 ";

        //Categorias
        $whereCategoria="";

        if(isset($categorias[0])){

            $whereCategoria=" and (";

            for($i = 0; $i < count($categorias); $i++){

                $whereCategoria .=" sc.idcategoria=".$categorias[$i];

                if($i != count($categorias)-1) $whereCategoria .=" or ";
            }
            $whereCategoria .=") ";
            
        }
        //else $whereCategoria=" and sc.idcategoria= 0 ";

        //Marcas
        $whereMarca="";

        if(isset($marcas[0])){

            $whereMarca=" and (";

            for($i = 0; $i < count($marcas); $i++){

                $whereMarca .=" m.idmarca=".$marcas[$i];

                if($i != count($marcas)-1) $whereMarca .=" or ";
            }
            $whereMarca .=") ";
            
            
        }

        $whereProductType = ($productType == "-1") ? '' : $productType;

        if ($whereProductType) $whereProductType = " AND p.tipoproducto = ".$whereProductType.' ';

        //obtenemos el stock por marca
        try {
            $query = $em->createQuery(
                'SELECT SUM(sv.cantidad) as cantidad, m.nombre as marca
                FROM App\Entity\Stockventa sv
                inner join sv.stockIdstock s
                inner join s.sucursalIdsucursal suc
                INNER JOIN  s.productoIdproducto p
                left JOIN  p.marcaIdmarca m
                left JOIN  p.categoriaIdcategoria sc
                left JOIN  sc.claseIdclase c
                INNER JOIN sv.ventaIdventa v
                INNER JOIN v.tipoventaIdtipoventa sl
                where sv.status =:status AND sv.status = 1
                AND v.fechaventa >= :fechaInicio AND v.fechaventa <= :fechaFin '.$whereSucursal.$whereClase.$whereCategoria.$whereMarca.$whereSaleType.$whereProductType.' 
                GROUP BY m.nombre
                order by m.nombre ASC'
            )->setParameters(['fechaInicio' => $parametros1['fechaInicio'], 'fechaFin' => $parametros1['fechaFin'], 'status' => 1]);
            
            $stock = $query->getResult();
            
        } catch (\Exception $e) {
            echo($e->getMessage());
        }
        
        try {
            
            $query = $em->createQuery(
                'SELECT SUM(sv.cantidad) as cantidad, suc.nombre as sucursal
                FROM App\Entity\Stockventa sv
                INNER JOIN sv.stockIdstock s
                INNER JOIN s.sucursalIdsucursal suc
                INNER JOIN  s.productoIdproducto p
                LEFT JOIN  p.marcaIdmarca m
                LEFT JOIN  p.categoriaIdcategoria sc
                LEFT JOIN  sc.claseIdclase c
                INNER JOIN sv.ventaIdventa v
                INNER JOIN v.tipoventaIdtipoventa sl 
                WHERE sv.status =:status AND sv.status = 1 AND v.fechaventa >= :fechaInicio AND v.fechaventa <= :fechaFin '.$whereSucursal.$whereClase.$whereCategoria.$whereMarca.$whereSaleType.$whereProductType.' 
                GROUP BY suc.nombre
                ORDER BY suc.nombre ASC'
            )->setParameters(['fechaInicio' => $parametros1['fechaInicio'], 'fechaFin' => $parametros1['fechaFin'], 'status' => 1]);
            
            $stock1 = $query->getResult();

        } catch (\Exception $e) {
            echo($e->getMessage());
        }

        try {
            
            $query1 = $em->createQuery(
                'SELECT SUM(sv.cantidad) as cantidad, c.nombre as categoria
                FROM App\Entity\Stockventa sv
                inner join sv.stockIdstock s
                INNER JOIN  s.sucursalIdsucursal suc
                INNER JOIN  s.productoIdproducto p
                INNER JOIN  p.marcaIdmarca m 
                INNER JOIN  p.categoriaIdcategoria sc
                inner join sc.claseIdclase c
                INNER JOIN sv.ventaIdventa v
                INNER JOIN v.tipoventaIdtipoventa sl
                where sv.status =:status AND sv.status = 1 AND v.fechaventa >= :fechaInicio AND v.fechaventa <= :fechaFin '.$whereSucursal.$whereCategoria.$whereMarca.$whereSaleType.$whereProductType.' 
                group by c.idclase
                order by c.nombre ASC'
                )->setParameters(['fechaInicio' => $parametros1['fechaInicio'], 'fechaFin' => $parametros1['fechaFin'], 'status' => 1]);
            
            $stockCategoria= $query1->getResult();

        } catch (\Exception $e) {
            echo($e->getMessage());
        }

        $query = $em->createQuery(
            'SELECT SUM(sv.cantidad) as cantidad, sc.nombre as subcategoria
             FROM App\Entity\Stockventa sv
             inner join sv.stockIdstock s
             INNER JOIN  s.sucursalIdsucursal suc
             INNER JOIN  s.productoIdproducto p
             INNER JOIN  p.marcaIdmarca m 
             INNER JOIN  p.categoriaIdcategoria sc
             INNER JOIN sv.ventaIdventa v
             INNER JOIN v.tipoventaIdtipoventa sl
             where sv.status =:status AND sv.status = 1 AND v.fechaventa >= :fechaInicio AND v.fechaventa <= :fechaFin '.$whereSucursal.$whereCategoria.$whereMarca.$whereSaleType.$whereProductType.' 
             group by sc.idcategoria
             order by sc.nombre ASC'
             )->setParameters(['fechaInicio' => $parametros1['fechaInicio'], 'fechaFin' => $parametros1['fechaFin'], 'status' => 1]);
        
        $stockSubcategoria= $query->getResult();
        
        //var_dump($stockSubcategoria);

        $query = $em->createQuery(
            'SELECT SUM(sv.cantidad) as cantidad, m.nombre as nombre, suc.nombre as sucursal, v.fechaventa as fecha
             FROM App\Entity\Stockventa sv
             inner join sv.stockIdstock s
             inner join s.sucursalIdsucursal suc
             INNER JOIN  s.productoIdproducto p
             INNER JOIN  p.marcaIdmarca m 
             INNER JOIN  p.categoriaIdcategoria sc
             INNER JOIN sv.ventaIdventa v
             INNER JOIN v.tipoventaIdtipoventa sl
             where sv.status =:status AND sv.status = 1 AND v.fechaventa >= :fechaInicio AND v.fechaventa <= :fechaFin '.$whereSucursal.$whereCategoria.$whereMarca.$whereSaleType.$whereProductType.' 
             group by suc.idsucursal, m.idmarca , v.fechaventa
             order by v.fechaventa ASC, cantidad DESC'
             )->setParameters(['fechaInicio' => $parametros1['fechaInicio'], 'fechaFin' => $parametros1['fechaFin'], 'status' => 1]);
        
        $stockSucursalMarca= $query->getResult();
        
        $query = $em->createQuery(
            'SELECT SUM(sv.cantidad) as cantidad, c.nombre as nombre, suc.nombre as sucursal, v.fechaventa as fecha
             FROM App\Entity\Stockventa sv
             inner join sv.stockIdstock s
             inner join s.sucursalIdsucursal suc
             INNER JOIN  s.productoIdproducto p
             INNER JOIN  p.marcaIdmarca m 
             INNER JOIN  p.categoriaIdcategoria sc
             INNER JOIN  sc.claseIdclase c
             INNER JOIN sv.ventaIdventa v
             INNER JOIN v.tipoventaIdtipoventa sl
             where sv.status =:status AND sv.status = 1 AND v.fechaventa >= :fechaInicio AND v.fechaventa <= :fechaFin '.$whereSucursal.$whereCategoria.$whereMarca.$whereClase.$whereSaleType.$whereProductType.' 
             group by suc.idsucursal, c.idclase , v.fechaventa
             order by v.fechaventa ASC, cantidad DESC'
             )->setParameters(['fechaInicio' => $parametros1['fechaInicio'], 'fechaFin' => $parametros1['fechaFin'], 'status' => 1]);
        
        $stockSucursalCategoria= $query->getResult();

        $query = $em->createQuery(
            'SELECT SUM(sv.cantidad) as cantidad, sc.nombre as nombre, suc.nombre as sucursal, v.fechaventa as fecha
             FROM App\Entity\Stockventa sv
             inner join sv.stockIdstock s
             INNER JOIN  s.sucursalIdsucursal suc
             INNER JOIN  s.productoIdproducto p
             INNER JOIN  p.marcaIdmarca m 
             INNER JOIN  p.categoriaIdcategoria sc
             INNER JOIN sv.ventaIdventa v
             INNER JOIN v.tipoventaIdtipoventa sl
             where sv.status =:status AND sv.status = 1 AND v.fechaventa >= :fechaInicio AND v.fechaventa <= :fechaFin '.$whereSucursal.$whereCategoria.$whereMarca.$whereSaleType.$whereProductType.' 
             group by suc.idsucursal, sc.idcategoria , v.fechaventa
             order by v.fechaventa ASC, cantidad DESC'
             )->setParameters(['fechaInicio' => $parametros1['fechaInicio'], 'fechaFin' => $parametros1['fechaFin'], 'status' => 1]);
        
        $stockSucursalSubcategoria= $query->getResult();
        

        try {
            $query2 = $em->createQuery(
                'SELECT SUM(sv.cantidad) as cantidad, sl.nombre as categoria
                 FROM App\Entity\Stockventa sv
                 inner join sv.stockIdstock s
                 INNER JOIN  s.sucursalIdsucursal suc
                 INNER JOIN  s.productoIdproducto p
                 INNER JOIN  p.marcaIdmarca m 
                 INNER JOIN  p.categoriaIdcategoria sc
                 inner join sc.claseIdclase c
                 INNER JOIN sv.ventaIdventa v
                 INNER JOIN v.tipoventaIdtipoventa sl
                 where sv.status =:status AND sv.status = 1 AND v.fechaventa >= :fechaInicio AND v.fechaventa <= :fechaFin '.$whereSucursal.$whereCategoria.$whereMarca.$whereSaleType.$whereProductType.' 
                 group by sl.idtipoventa
                 order by sl.nombre ASC'
                 )->setParameters(['fechaInicio' => $parametros1['fechaInicio'], 'fechaFin' => $parametros1['fechaFin'], 'status' => 1]);
            
            $stockTipoVenta= $query2->getResult();

            $query2 = $em->createQuery(
                'SELECT SUM(sv.cantidad) as cantidad, sl.nombre as saletype, s.codigobarras as codigo, p.modelo, suc.nombre as sucursal, m.nombre as marca, p.codigobarrasuniversal
                 FROM App\Entity\Stockventa sv
                 inner join sv.stockIdstock s
                 INNER JOIN  s.sucursalIdsucursal suc
                 INNER JOIN  s.productoIdproducto p
                 INNER JOIN  p.marcaIdmarca m 
                 INNER JOIN  p.categoriaIdcategoria sc
                 inner join sc.claseIdclase c
                 INNER JOIN sv.ventaIdventa v
                 INNER JOIN v.tipoventaIdtipoventa sl
                 where sv.status =:status AND sv.status = 1 AND v.fechaventa >= :fechaInicio AND v.fechaventa <= :fechaFin '.$whereSucursal.$whereCategoria.$whereMarca.$whereSaleType.$whereProductType.' 
                 group by sl.idtipoventa, p.modelo
                 order by p.modelo'
                 )->setParameters(['fechaInicio' => $parametros1['fechaInicio'], 'fechaFin' => $parametros1['fechaFin'], 'status' => 1]);
            
            $stockTipoVentaDetail = $query2->getResult();
            //var_dump($stockTipoVenta);
        } catch (\Exception $e) {
            echo($e->getMessage());
        }

        $MarcasSucursalMapped = [];

        foreach ($stockSucursalMarca as $item) {
            $sucursal = $item['sucursal'];
            if (!isset($MarcasSucursalMapped[$sucursal])) {
                $MarcasSucursalMapped[$sucursal] = [];
            }
            $MarcasSucursalMapped[$sucursal][] = $item;
        }

        $CategoriasSucursalMapped = [];

        foreach ($stockSucursalCategoria as $item) {
            $sucursal = $item['sucursal'];
            if (!isset($CategoriasSucursalMapped[$sucursal])) {
                $CategoriasSucursalMapped[$sucursal] = [];
            }
            $CategoriasSucursalMapped[$sucursal][] = $item;
        }

        $SubcategoriasSucursalMapped = [];

        foreach ($stockSucursalSubcategoria as $item) {
            $sucursal = $item['sucursal'];
            if (!isset($SubcategoriasSucursalMapped[$sucursal])) {
                $SubcategoriasSucursalMapped[$sucursal] = [];
            }
            $SubcategoriasSucursalMapped[$sucursal][] = $item;
        }

        $query = $em->createQuery(
            'SELECT SUM(sv.cantidad) as cantidad, p.modelo as modelo,
            suc.nombre as sucursal, s.codigobarras as codigo, m.nombre as marca,
            c.nombre as categoria, sc.nombre as subcategoria, p.codigobarrasuniversal
               FROM App\Entity\Stockventa sv
               inner join sv.stockIdstock s
                inner join s.sucursalIdsucursal suc
               INNER JOIN  s.productoIdproducto p
               INNER JOIN  p.marcaIdmarca m 
               INNER JOIN  p.categoriaIdcategoria sc
               INNER JOIN  sc.claseIdclase c
               INNER JOIN sv.ventaIdventa v
               INNER JOIN v.tipoventaIdtipoventa sl
               where sv.status =:status AND sv.status = 1 AND v.fechaventa >= :fechaInicio AND v.fechaventa <= :fechaFin '.$whereSucursal.$whereCategoria.$whereMarca.$whereSaleType.$whereProductType.' 
               GROUP BY p.idproducto, suc.idsucursal
               order by modelo DESC
               '
               )->setParameters(['fechaInicio' => $parametros1['fechaInicio'], 'fechaFin' => $parametros1['fechaFin'], 'status' => 1]);
        $modeloSucursal= $query->getResult();

        $modeloSucursalMapped = [];

        foreach ($modeloSucursal as $item) {
            $marca = $item['marca'];
            if (!isset($modeloSucursalMapped[$marca])) {
                $modeloSucursalMapped[$marca] = [];
            }
            $modeloSucursalMapped[$marca][] = $item;
        }

        $categoriaModeloSucursalMapped = [];

        foreach ($modeloSucursal as $item) {
            $categoria = $item['categoria'];
            if (!isset($categoriaModeloSucursalMapped[$categoria])) {
                $categoriaModeloSucursalMapped[$categoria] = [];
            }
            $categoriaModeloSucursalMapped[$categoria][] = $item;
        }

        $subcategoriaModeloSucursalMapped = [];

        foreach ($modeloSucursal as $item) {
            $subcategoria = $item['subcategoria'];
            if (!isset($subcategoriaModeloSucursalMapped[$subcategoria])) {
                $subcategoriaModeloSucursalMapped[$subcategoria] = [];
            }
            $subcategoriaModeloSucursalMapped[$subcategoria][] = $item;
        }

        $saleTypeSucursalMapped = [];

        foreach ($stockTipoVentaDetail as $item) {
            $subcategoria = $item['saletype'];
            if (!isset($saleTypeSucursalMapped[$subcategoria])) {
                $saleTypeSucursalMapped[$subcategoria] = [];
            }
            $saleTypeSucursalMapped[$subcategoria][] = $item;
        }

        return $this->render('reporte/obtener-informacion-producto.html.twig', [
            'stock'=>$stock,
            'stock1'=>$stock1,
            'stockCategoria'=>$stockCategoria,
            'stockSubcategoria'=>$stockSubcategoria,
            'stockTipoVenta'=>$stockTipoVenta,
            'modeloSucursalMapped'=>$modeloSucursalMapped,
            'categoriaModeloSucursalMapped'=>$categoriaModeloSucursalMapped,
            'subcategoriaModeloSucursalMapped'=>$subcategoriaModeloSucursalMapped,
            'saleTypeSucursalMapped' => $saleTypeSucursalMapped            
        ]);
    }

}
