<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use App\Entity\Ordenlaboratorio;
use Symfony\Component\HttpFoundation\JsonResponse;

class AdministradorOrdenCompraController extends AbstractController
{
    /**
     * @Route("/administrador/orden/compra", name="app_administrador_orden_compra")
     */
    public function index(): Response
    {
        return $this->render('administrador_orden_compra/indexAdministradorOrden.html.twig', [
            'controller_name' => 'AdministradorOrdenCompraController',
        ]);
    }

    /**
     * @Route("/orden/filtrosOrdenesCompraAdministrador", name="filtros_Ordenes_Compra_Administrador")
     */

    public function filtrosOrdenesCompraAdministrador()
    {
        $em = $this->getDoctrine()->getManager();

        $success = false;
        $message = "";

        $query = $em->createQuery(
            'SELECT e.nombre AS Empresa, e.idempresa
             FROM App\Entity\Empresa e
             WHERE e.status = :status'
        )->setParameters(['status' => '1']);
        $filtrosEmpresas = $query->getResult();


        $query = $em->createQuery(
            'SELECT s.nombre AS Sucursal, s.idsucursal
                 FROM App\Entity\Sucursal s
                 WHERE s.status = :status'
        )->setParameters(['status' => '1']);
        $filtroSucursales = $query->getResult();


        return $this->render('administrador_orden_compra/filtros_Ordenes_Compra_Administrador.html.twig', [
            'filtrosEmpresas' => $filtrosEmpresas,
            'filtroSucursales' => $filtroSucursales,
        ]);
    }


    /**
     * @Route("/orden/table-ordenes-admin", name="table-ordenes-admin")
     */

    public function tableOrdenesAdmin(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();

        $parametros = [];
        $whereSucursal = "";
        $fechaInicio = \DateTime::createFromFormat('d/m/Y', $request->get("fechaInicio"));
        $fechaFin = \DateTime::createFromFormat('d/m/Y', $request->get("fechaFin"));
        $ordenesdeCompra = [];
        $whereDateRangoDia = "and sv.creacion >= :fechaInicio  and sv.creacion<= :fechaFin ";
        $idsucursal = $request->get("idsucursal");

        if ($idsucursal !== "todasSucursales") {
            $whereSucursal = 'AND s.idsucursal = :idsucursal';
            $parametros['idsucursal'] = $idsucursal;
        } else {
            $whereSucursal = '';
        }

        if (!$fechaInicio || !$fechaFin) {
            $fechaInicio = new \DateTime("now");
            $fechaFin = new \DateTime("now");

            $fechaInicio = $fechaInicio->format('Y-m-d') . " 00:00:00";
            $fechaFin = $fechaFin->format('Y-m-d') . " 23:59:00";
        } else {
            $parametros['fechaInicio'] = $fechaInicio->format('Y-m-d') . " 00:00:00";
            $parametros['fechaFin'] = $fechaFin->format('Y-m-d') . " 23:59:00";
        }
        $parametros['status'] = 1;

        try {


            $query = $em->createQuery(
                'SELECT 
        svol.idstockventaordenlaboratorio AS IdStockventaOrdenLaboratorio, 
        svol.creacion, v.folio AS Folio,s.nombre AS Sucursal,tv.nombre AS TipodeVenta, 
        c.nombre AS PrimerNombre,c.apellidopaterno AS ApellidoPaterno,c.apellidomaterno AS ApellidoMaterno,v.beneficiario AS Beneficiario, 
        c.numeroempleado AS NumerodeEmpleado,u.nombre AS Unidad, p.modelo AS Modelo,
        m.nombre as Marca, ec.nombre AS EmpresaCliente, cla.nombre AS Categoria,
        dl.nombre AS DisenoLente, mt.nombre AS Material, tr.nombre AS Tratamiento, ol.addordenlaboratorio AS AddOrden, cat.nombre AS Subcategoria,
        ol.esferaod AS EsferaD,ol.cilindrood AS CilD, ol.ejeod AS EjeD, ol.esferaoi AS EsfI, ol.cilindrooi AS CilI, ol.ejeoi AS EjeI,
        ol.observaciones AS Observaciones , us.nombre AS Nombre, us.apellidopaterno AS ApellidoPaternoUsuario, us.apellidomaterno AS ApellidoMaternoUsuario,
        fe.etapa AS Etapa, ol.folioautorizacion AS FolioAutorizacion, ol.statusautorizacion AS StatusAutorizacion, ol.recepcion AS Recepcion, ol.fechalaboratorio AS FechaEntradaLaboratorio,
        ol.statuslaboratorio AS StatusLaboratorio, ol.fechasalidalaboratorio AS FechaSalidaLaboratorio, ol.idordenlaboratorio, sv.garantia AS Garantia, s.idsucursal, ol.base AS Base

    FROM App\Entity\Stockventaordenlaboratorio svol
    INNER JOIN svol.stockventaIdstockventa sv
    INNER JOIN sv.ventaIdventa v
    INNER JOIN svol.ordenlaboratorioIdordenlaboratorio ol
    LEFT JOIN sv.stockIdstock st
    LEFT JOIN ol.flujoexpedienteIdflujoexpediente fe
    LEFT JOIN st.productoIdproducto p
    LEFT JOIN ol.disenolenteIddisenolente dl
    LEFT JOIN ol.materialIdmaterial mt
    LEFT JOIN ol.tratamientoIdtratamiento tr
    LEFT JOIN v.clienteIdcliente c
    LEFT JOIN c.empresaclienteIdempresacliente ec
    LEFT JOIN v.usuarioIdusuario us
    LEFT JOIN v.tipoventaIdtipoventa tv
    LEFT JOIN v.unidadIdunidad u
    LEFT JOIN v.sucursalIdsucursal s
    LEFT JOIN p.marcaIdmarca m
    LEFT JOIN p.categoriaIdcategoria cat
    LEFT JOIN cat.claseIdclase cla
    WHERE ol.status = :status AND sv.status = 1 AND svol.alreadyset = 1
    ' . $whereSucursal . '
    AND svol.creacion >= :fechaInicio AND svol.creacion <= :fechaFin
    
    ORDER BY svol.creacion DESC'
            )->setParameters($parametros);

            $ordenesdeCompra = $query->getResult();
        } catch (\Exception $e) {
        }

        return $this->render('administrador_orden_compra/tableOrdenesAdmin.html.twig', [
            'ordenesdeCompra' => $ordenesdeCompra,
            'fechaInicio' => $fechaInicio,
            'fechaFin' => $fechaFin,
        ]);
    }


    /**
     * @Route("/orden/edit-ordenes-admin", name="edit-ordenes-admin", methods={"POST"})
     */
    public function editOrdenesAdmin(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $fieldName = $request->request->get('statusName');
        $fieldValue = $request->request->get('fieldValue');
        $orderId = $request->request->get('orderId');

        $ordenLaboratorio = $em->getRepository(Ordenlaboratorio::class)->find($orderId);

        if (!$ordenLaboratorio) {
            return new JsonResponse(['error' => 'Orden no encontrada'], Response::HTTP_NOT_FOUND);
        }

        switch ($fieldName) {
            case 'statusAutorizacion':
                $ordenLaboratorio->setStatusAutorizacion($fieldValue);
                break;
            case 'statusLaboratorio':
                $ordenLaboratorio->setStatusLaboratorio($fieldValue);
                break;
            case 'statusBase':
                $ordenLaboratorio->setBase($fieldValue);
                break;
            case 'fechaSalidaLaboratorio':
                $fecha = \DateTime::createFromFormat('d-m-Y', $fieldValue);
                if ($fecha === false) {
                    return new JsonResponse(['error' => 'Formato de fecha inválido'], Response::HTTP_BAD_REQUEST);
                }
                $ordenLaboratorio->setFechasalidalaboratorio($fecha);
                break;
            case 'fechaEntradaLaboratorio':
                $fecha = \DateTime::createFromFormat('d-m-Y', $fieldValue);
                if ($fecha === false) {
                    return new JsonResponse(['error' => 'Formato de fecha inválido'], Response::HTTP_BAD_REQUEST);
                }
                $ordenLaboratorio->setFechalaboratorio($fecha);
                break;
            case 'fechaEntradaLaboratorio':
                $fecha = \DateTime::createFromFormat('d-m-Y', $fieldValue);
                if ($fecha === false) {
                    return new JsonResponse(['error' => 'Formato de fecha inválido'], Response::HTTP_BAD_REQUEST);
                }
                $ordenLaboratorio->setFechalaboratorio($fecha);
                break;
            default:
                return new JsonResponse(['error' => 'Campo no reconocido'], Response::HTTP_BAD_REQUEST);
        }

        $em->flush();

        return new JsonResponse(['success' => 'Actualización realizada correctamente'], Response::HTTP_OK);
    }
}