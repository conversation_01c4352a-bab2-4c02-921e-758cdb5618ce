<?php

namespace App\Controller;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Serializer\SerializerInterface;

use App\Entity\Ordenlaboratorio;
use App\Entity\Stock;
use App\Entity\Event;
use App\Entity\Eventtype;
use App\Entity\Venta;
use App\Entity\Stockventa;
use App\Entity\Stockventaordenlaboratorio;
use DateTime;
use Php<PERSON>arser\Node\Expr\Exit_;
use App\Entity\Shipmentordenlaboratorio;
use App\Entity\Proveedor;
use App\Entity\Shipment;
use App\Entity\Sucursal;


/**
 *
 * This method processes a request containing formatted product data, validates it against a predefined dictionary,
 * and adds the valid products to the system. It returns a JSON response containing information about the success
 * or failure of the operation, any errors encountered, and HTML content for the added products.
 *
 * @Route("/admin/dashboard")
 */
class LaboratoryOrderController extends AbstractController
{
    /**
     * @Route("/laboratory-order", name="app_laboratory_order")
     */
    public function index(): Response
    {

        $orderStages = [
            'Sin micas',
            'Micas asignadas',
            'Laboratorio Esperando Material',
            'Pendiente',
            'Procesando',
            'Calidad',
            'Terminado',
        ];

        return $this->render('laboratory_order/index.html.twig', [
            'orderStages' => $orderStages
        ]);
    }

    /**
     * @Route("/laboratory-order/{id}", name="laboratory_order_get_single", methods={"GET"})
     */
    public function getSingleLaboratoryOrder(int $id): Response
    {
        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT ol.idordenlaboratorio, ol.esferaod, ol.esferaoi, ol.cilindrood, ol.cilindrooi, ol.etapa as stage,
            ol.ejeod, ol.ejeoi, ol.addordenlaboratorio, ol.base, suc.nombre as locationName,
            CONCAT(c.nombre, \' \', c.apellidopaterno, \' \', c.apellidomaterno) as fullName, fe.idflujoexpediente, v.folio
            FROM App\Entity\Ordenlaboratorio ol
            INNER JOIN ol.flujoexpedienteIdflujoexpediente fe
            LEFT JOIN fe.ventaIdventa v
            INNER JOIN fe.sucursalIdsucursal suc
            INNER JOIN fe.clienteIdcliente c
            WHERE ol.idordenlaboratorio = :id'
        )->setParameter('id', $id);

        $laboratoryOrder = $query->getOneOrNullResult();

        if (!$laboratoryOrder) {
            throw $this->createNotFoundException('No laboratory order found for id ' . $id);
        }

        $query = $em->createQuery(
            'SELECT v.folio, fe.idflujoexpediente
            FROM App\Entity\Flujoexpedienteventa fv
            INNER JOIN fv.flujoexpedienteIdflujoexpediente fe
            INNER JOIN fv.ventaIdventa v
            WHERE fe.idflujoexpediente = :idflujoexpediente'
        )->setParameter('idflujoexpediente', $laboratoryOrder['idflujoexpediente']);

        $sales = $query->getResult();

        $mappedSales = [];

        foreach ($sales as $Sale) {

            array_push($mappedSales, $Sale['folio']);
        }

        $query = $em->createQuery(
            'SELECT p.modelo, ol.idordenlaboratorio, svol.mainproduct as errase, svol.idstockventaordenlaboratorio as id, 
            p.tipoproducto, sv.idstockventa, father.idstockventaordenlaboratorio as parent
            FROM App\Entity\Stockventaordenlaboratorio svol
            INNER JOIN svol.stockventaIdstockventa sv
            INNER JOIN sv.stockIdstock s
            INNER JOIN s.productoIdproducto p
            INNER JOIN svol.ordenlaboratorioIdordenlaboratorio ol
            LEFT JOIN svol.stockventaordenlaboratorioIdstockventaordenlaboratorio father
            WHERE ol.idordenlaboratorio =:id AND p.status = 1'
        )->setParameter('id', $id);

        $productsOrder = $query->getResult();
        $mappedProducts = [];

        foreach ($productsOrder as $productOrder) {
            if ($productOrder['parent']) {
                $mappedProducts[$productOrder['parent']]['children'][] = $productOrder;
            } else {
                $mappedProducts[$productOrder['id']]['main'] = $productOrder;
            }
        }

        $orderStages = [
            'Orden creada',
            'Completar flujo',
            'Sin micas',
            'Micas asignadas',
            'Laboratorio Esperando Material',
            'Pendiente',
            'Procesando',
            'Calidad',
            'Terminado',
        ];

        return $this->render('laboratory_order/laboratory_order-config-table-single.html.twig', [
            'laboratoryOrder' => $laboratoryOrder,
            'mappedProducts' => $mappedProducts,
            'orderStages' => $orderStages,
            'mappedSales' => $mappedSales
        ]);
    }

    /**
     * @Route("/laboratory-order-table", name="laboratory-order-laboratory-order-config-table")
     */
    public function laboratoryOrderConfigTable(Request $request): Response
    {

        $em = $this->getDoctrine()->getManager();

        $etapanum = $request->get('etapanum');

        $query = $em->createQuery(
            'SELECT ol.idordenlaboratorio, ol.esferaod, ol.esferaoi, ol.cilindrood, ol.cilindrooi, ol.etapa as stage,
            ol.ejeod, ol.ejeoi,  ol.addordenlaboratorio, ol.base, suc.nombre as locationName,
            CONCAT(c.nombre, \' \', c.apellidopaterno, \' \', c.apellidomaterno) as fullName, fe.idflujoexpediente, v.folio
            FROM App\Entity\Ordenlaboratorio ol
            INNER JOIN ol.flujoexpedienteIdflujoexpediente fe
            LEFT JOIN fe.ventaIdventa v
            INNER JOIN fe.sucursalIdsucursal suc
            INNER JOIN fe.clienteIdcliente c
            WHERE ol.status =:status AND ol.etapa =:etapanum AND fe.status =:status AND fe.etapa >= :flowStage
            '
        )->setParameters(["status" => '1', "flowStage" => 6, 'etapanum' => $etapanum]);
        $laboratoryOrders = $query->getResult();

        $whereLaboratoryOrder = "";

        if (isset($laboratoryOrders[0])) {
            $whereLaboratoryOrder .= "AND (";
            for ($i = 0; $i < count($laboratoryOrders); $i++) {
                if ($i == count($laboratoryOrders) - 1) $whereLaboratoryOrder .= "ol.idordenlaboratorio = " . $laboratoryOrders[$i]["idordenlaboratorio"];
                else $whereLaboratoryOrder .= "ol.idordenlaboratorio = " . $laboratoryOrders[$i]["idordenlaboratorio"] . " OR ";
            }
            $whereLaboratoryOrder .= ") ";
        }

        $whereFlow = "";

        if (isset($laboratoryOrders[0])) {
            $whereFlow .= "AND (";
            for ($i = 0; $i < count($laboratoryOrders); $i++) {
                if ($i == count($laboratoryOrders) - 1) $whereFlow .= "fe.idflujoexpediente = " . $laboratoryOrders[$i]["idflujoexpediente"];
                else $whereFlow .= "fe.idflujoexpediente = " . $laboratoryOrders[$i]["idflujoexpediente"] . " OR ";
            }
            $whereFlow .= ") ";
        }

        $query = $em->createQuery(
            'SELECT v.folio, fe.idflujoexpediente
            FROM App\Entity\Flujoexpedienteventa fv
            INNER JOIN fv.flujoexpedienteIdflujoexpediente fe
            INNER JOIN fv.ventaIdventa v
            where v.status =:status ' . $whereFlow
        )->setParameters(['status' => "1"]);
        $sales = $query->getResult();

        $mappedSales = [];

        foreach ($sales as $Sale) {
            if (isset($mappedSales[$Sale['idflujoexpediente']])) array_push($mappedSales[$Sale['idflujoexpediente']], $Sale['folio']);
            else $mappedSales[$Sale['idflujoexpediente']] = [$Sale['folio']];
        }

        $query = $em->createQuery(
            'SELECT p.modelo, ol.idordenlaboratorio, svol.mainproduct as errase, svol.idstockventaordenlaboratorio as id, 
            p.tipoproducto, sv.idstockventa, father.idstockventaordenlaboratorio as parent
            FROM App\Entity\Stockventaordenlaboratorio svol
            INNER JOIN svol.stockventaIdstockventa sv
            INNER JOIN sv.stockIdstock s
            INNER JOIN s.productoIdproducto p
            INNER JOIN svol.ordenlaboratorioIdordenlaboratorio ol
            LEFT JOIN svol.stockventaordenlaboratorioIdstockventaordenlaboratorio father
            WHERE p.status =:status ' . $whereLaboratoryOrder
        )->setParameters(["status" => '1']);
        $productsOrder = $query->getResult();

        $mappedProducts = [];

        foreach ($productsOrder as $productOrder) {
            if ($productOrder['parent']) {
                $mappedProducts[$productOrder['idordenlaboratorio']][$productOrder['parent']]['children'][] = $productOrder;
            } else {
                $mappedProducts[$productOrder['idordenlaboratorio']][$productOrder['id']]['main'] = $productOrder;
            }
        }

        $orderStages = [
            'Orden creada',
            'Completar flujo',
            'Sin micas',
            'Micas asignadas',
            'Laboratorio Esperando Material',
            'Pendiente',
            'Procesando',
            'Calidad',
            'Terminado',
        ];


        return $this->render('laboratory_order/laboratory_order-config-table.html.twig', [
            'laboratoryOrders' => $laboratoryOrders,
            'mappedProducts' =>  $mappedProducts,
            'orderStages' => $orderStages,
            'mappedSales' => $mappedSales
        ]);
    }

    /**
     * Controller endpoint to update the stage of a laboratory order to 5 if aditional products where added and return a response.
     * 
     * @Route("/deletestockOrder", name="laboratory-order-delete-stock-Order")
     * 
     * @param Request $request The HTTP request object containing the update ID.
     * 
     * @return Response HTTP response redirect to the method \ref laboratoryOrderConfigTable()
     */
    public function deletestockOrder(Request $request): Response
    {

        $user = $this->getUser();

        $em = $this->getDoctrine()->getManager();
        $deleteid = $request->get('deleteid');
        $updateid = $request->get('updateid');

        $stockLaboratoryOrder = $em->getRepository(Stockventaordenlaboratorio::class)->findOneBy(array('idstockventaordenlaboratorio' => $deleteid));
        $stockventa = $stockLaboratoryOrder->getStockventaIdstockventa();

        $now = new DateTime();

        $stock = $stockventa->getStockIdstock();
        $stockventa->setStatus("0");
        $stockventa->setUsuarioresponsablecancelacion($user);
        $stockventa->setModificacion($now);

        $newamount = $stock->getCantidad() + $stockventa->getCantidad();

        $stock->setModificacion($now);
        $stock->setCantidad($newamount);
        $stock->setStatus("1");

        $em->remove($stockLaboratoryOrder);
        $em->persist($stockventa);
        $em->persist($stock);
        $em->flush();

        return $this->getSingleLaboratoryOrder($updateid);
    }

    /**
     * @Route("/set-base", name="laboratory-order-set-base")
     */
    public function setBase(Request $request): Response
    {

        $em = $this->getDoctrine()->getManager();
        $idlaboratoryOrder = $request->get('idlaboratoryOrder');
        $newBase = $request->get('newBase');

        $msg = "";
        $success = false;

        $LaboratoryOrder = $em->getRepository(Ordenlaboratorio::class)->findOneBy(array('idordenlaboratorio' => $idlaboratoryOrder));

        try {
            if ($LaboratoryOrder) {
                if (isset($newBase)) {
                    $LaboratoryOrder->setBase($newBase);
                    $em->persist($LaboratoryOrder);
                    $em->flush();
                    $success = true;
                } else throw new \Exception('No se pudo completar la petición');
            } else throw new \Exception('No se encontró la orden de laboratorio');
        } catch (\Exception $e) {
            $msg .= $e->getMessage();
        }

        return $this->json(['success' => $success, 'msg' => $msg]);
    }

    /**
     * Controller endpoint to update the stage of a laboratory order to 5 if aditional products where added and return a response.
     *
     * @Route("/sendlab", name="laboratory-order-sendlab")
     * 
     * @param Request $request The HTTP request object containing the update ID.
     * 
     * Example:
     * @code
     * <div> example </div>
     * 
     * 
     * function sendlab(updateid) {
     *	$.ajax({
     *		url: "{{ path('laboratory-order-sendlab') }}",
     *		type: 'POST',
     *		data: {
     *			updateid: updateid
     *		},
     *		beforeSend: loadingGif("laboratory-order-table-container"),
     *		dataType: "html"
     *	}).done(function (html) {
     *		$("#laboratory-order-table-container").html(html);
     *		$("#lens-config-result").html();
     *	}).fail(function () {
     *		alert("error");
     *	});
     *}
     * @endcode
     * 
     * @return Response HTTP response redirect to the method \ref laboratoryOrderConfigTable.
     */
    public function sendlab(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $idlaboratoryOrder = $request->get('updateid');

        $msg = "";
        $success = false;

        $LaboratoryOrder = $em->getRepository(Ordenlaboratorio::class)->findOneBy(array('idordenlaboratorio' => $idlaboratoryOrder));

        try {
            if ($LaboratoryOrder) {

                $User = $this->getUser();
                $labevent = new Event();

                $etype = $em->getRepository(Eventtype::class)->findOneBy(array('name' => 'send-Lab')) ?? new Eventtype();

                $etype
                    ->setName('send-Lab')
                    ->setDescription('Se envio al Laboratorío');

                $em->persist($etype);
                $em->flush();

                $now = new DateTime();

                $labevent
                    ->setUsuarioIdusuario($User)
                    ->setCreationdate($now)
                    ->setEventtypeIdeventtype($etype)
                    ->setClienteIdcliente($LaboratoryOrder->getClienteIdcliente())
                    ->setUpdatedate($now)
                    ->setOrdenlaboratorioIdordenlaboratorio($LaboratoryOrder);

                $em->persist($labevent);

                $LaboratoryOrder->setEtapa(5);
                $em->persist($LaboratoryOrder);
                $em->flush();
                $success = true;
            } else throw new \Exception('No se encontró la orden de laboratorio');
        } catch (\Exception $e) {
            $msg .= $e->getMessage();
        }

        // Return a response by calling another controller method.
        return $this->getSingleLaboratoryOrder($idlaboratoryOrder);
    }

    /**
     * @Route("/get-micas-filters", name="getMicasFilters")
     */
    public function getMicasFilters(Request $request, EntityManagerInterface $entityManager): Response
    {
        $filters = null;
        $exito = true;
        $msj = 'undif';
        $data = json_decode($request->getContent(), true);

        try {

            $marcas = $entityManager->createQueryBuilder()->select('DISTINCT m.nombre as name, m.idmarca as id')
                ->from('App\Entity\Producto', 'p')
                ->join('p.marcaIdmarca', 'm')
                ->where('m.status = 1')
                ->andWhere('p.tipoproducto= 1')
                ->getQuery()->getResult();

            $clases = $entityManager->createQueryBuilder()->select('DISTINCT class.nombre as name, class.idclase as id')
                ->from('App\Entity\Producto', 'p')
                ->join('p.categoriaIdcategoria', 'cat')
                ->join('cat.claseIdclase', 'class')
                ->where('class.status = 1')
                ->andWhere('p.tipoproducto= 1')
                ->getQuery()->getResult();

            $subcategorias = $entityManager->createQueryBuilder()->select('DISTINCT cat.nombre as name, cat.idcategoria as id')
                ->from('App\Entity\Producto', 'p')
                ->join('p.categoriaIdcategoria', 'cat')
                ->where('cat.status = 1')
                ->andWhere('p.tipoproducto= 1')
                ->getQuery()->getResult();

            $materials = $entityManager->createQueryBuilder()->select('DISTINCT mat.material as name, mat.idframematerial as id')
                ->from('App\Entity\Producto', 'p')
                ->join('p.framematerialIdframematerial', 'mat')
                ->where('p.status = 1')
                ->andWhere('p.framematerialIdframematerial IS NOT NULL')
                ->andWhere('p.tipoproducto = 1')
                ->getQuery()->getResult();

            $colors = $entityManager->createQueryBuilder()->select('DISTINCT color.color as name, color.idframecolor as id')
                ->from('App\Entity\Producto', 'p')
                ->join('p.framecolorIdframecolor', 'color')
                ->where('p.status = 1')
                ->andWhere('p.framecolorIdframecolor IS NOT NULL')
                ->getQuery()->getResult();

            $esferas = $entityManager->createQueryBuilder()->select('DISTINCT p.esfera as name, p.idproducto as id')
                ->from('App\Entity\Producto', 'p')
                ->where('p.status = 1')
                ->andWhere('p.esfera IS NOT NULL')
                ->andWhere('p.esfera !=:value')
                ->groupBy('p.esfera, p.idproducto')
                ->setParameter('value', '')
                ->getQuery()->getResult();


            $cilindros = $entityManager->createQueryBuilder()->select('DISTINCT p.cilindro as name, p.idproducto as id')
                ->from('App\Entity\Producto', 'p')
                ->where('p.status = 1')
                ->andWhere('p.cilindro IS NOT NULL')
                ->andWhere('p.cilindro !=:value')
                ->groupBy('p.cilindro , p.idproducto')
                ->setParameter('value', '')
                ->getQuery()->getResult();


            $ejes = $entityManager->createQueryBuilder()->select('DISTINCT p.eje as name, p.idproducto as id')
                ->from('App\Entity\Producto', 'p')
                ->where('p.status = 1')
                ->andWhere('p.eje IS NOT NULL')
                ->andWhere('p.eje !=:value')
                ->groupBy('p.eje, p.idproducto')
                ->setParameter('value', '')
                ->getQuery()->getResult();

            $adiciones = $entityManager->createQueryBuilder()->select('DISTINCT p.adicion as name, p.idproducto as id')
                ->from('App\Entity\Producto', 'p')
                ->where('p.status = 1')
                ->andWhere('p.adicion IS NOT NULL')
                ->andWhere('p.adicion !=:value')
                ->groupBy('p.adicion, p.idproducto')
                ->setParameter('value', '')
                ->getQuery()->getResult();

            $bases = $entityManager->createQueryBuilder()->select('DISTINCT p.base as name, p.idproducto as id')
                ->from('App\Entity\Producto', 'p')
                ->where('p.status = 1')
                ->andWhere('p.base IS NOT NULL')
                ->andWhere('p.base !=:value')
                ->groupBy('p.base, p.idproducto')
                ->setParameter('value', '')
                ->getQuery()->getResult();

            $disenos = $entityManager->createQueryBuilder()->select('DISTINCT p.design as name, p.idproducto as id')
                ->from('App\Entity\Producto', 'p')
                ->where('p.status = 1')
                ->andWhere('p.design IS NOT NULL')
                ->andWhere('p.design !=:value')
                ->groupBy('p.design, p.idproducto')
                ->setParameter('value', '')
                ->getQuery()->getResult();

            $filters = [
                'checkBoxes' => [
                    'categorias' => ['db_name' => "subcategorias", 'values' => $subcategorias],
                    'materials' => ['db_name' => "materials",   'values' => $materials],
                    'colores'   => ['db_name' => "colors",      'values' => $colors],
                    'clases'    => ['db_name' => "clases",      'values' => $clases],
                    'marca'     => ['db_name' => "marca",       'values' => $marcas]
                ],
                'selects' => [
                    'diseño'    => ['db_name' => "design",      'values' => $disenos],
                    'base'      => ['db_name' => "base",        'values' => $bases],
                    'esfera'    => ['db_name' => "esfera",      'values' => $esferas],
                    'cilindro'  => ['db_name' => "cilindro",    'values' => $cilindros],
                    'eje'       => ['db_name' => "eje",         'values' => $ejes],
                    'adicion'   => ['db_name' => "adicion",     'values' => $adiciones]
                ]
            ];

            $exito = true;
        } catch (\Exception $e) {
            $msj = $e->getMessage() . " " . $e->getLine();
            $exito = false;
        }

        return $this->render('laboratory_order/laboratory_order-addproducts-filters.html.twig', [
            'filters' => $filters,
            'exito' => $exito,
            'msj' => $msj,
            'ordenlab' => isset($data['ordenlab']) ? $data['ordenlab'] : null,
            'idstockventaordenlaboratorio' => isset($data['idstockventaordenlaboratorio']) ? $data['idstockventaordenlaboratorio'] : null
        ]);
    }

    /**
     * @Route("/get-micas", name="getMicas", methods={"GET", "POST"})
     */
    public function getMicas(Request $request, EntityManagerInterface $entityManager): Response
    {
        $exito = false;
        $msj = "";
        $stocks = null;

        try {
            $maindata = json_decode($request->getContent(), true);
            $data = $maindata['filters'];

            $queryBuilder = $entityManager->createQueryBuilder();
            $queryBuilder
                ->select('DISTINCT suc.nombre as num, p.idproducto as id, p.modelo, s.idstock, s.codigobarras as sku, s.serie,
                    p.nombre, p.esfera, p.cilindro, p.eje, p.design, p.adicion, SUM(s.cantidad) as cantidad')
                ->from('App\Entity\Stock', 's')
                ->join('s.productoIdproducto', 'p')
                ->join('s.sucursalIdsucursal', 'suc')
                ->join('p.marcaIdmarca', 'm')
                ->join('p.categoriaIdcategoria', 'cat')
                ->join('cat.claseIdclase', 'clas')
                ->leftjoin('p.framematerialIdframematerial', 'mat')
                ->Where('s.status = 1')
                ->andWhere('p.tipoproducto = 1')
                ->andWhere('cat.status = 1')
                ->andWhere('s.cantidad > 0')
                ->groupBy('suc.idsucursal', 's.codigobarras')
                ->orderBy('s.idstock', 'DESC')
                ->setMaxResults(30);

            if (isset($maindata['sku']) && $maindata['sku'] != "" && $maindata['sku'] != null) {

                $queryBuilder
                    ->andWhere("s.codigobarras = :codigobarrasvalue")
                    ->setParameter("codigobarrasvalue", $maindata['sku']);
            } else {

                if (isset($maindata['serie']) && $maindata['serie'] != "" && $maindata['serie'] != null) {

                    $queryBuilder
                        ->andWhere("s.serie = :serievalue")
                        ->setParameter("serievalue", $maindata['serie']);
                }

                foreach ($data['selects'] as $key => $value) {

                    if (!is_array($value) && $value != '') {
                        $queryBuilder
                            ->andWhere("p.$key = :{$key}value")
                            ->setParameter("{$key}value", $value);
                    }
                }

                $checkBoxes = $data['checkBoxes'];

                if (isset($checkBoxes['colors']) && sizeof($checkBoxes['colors']) > 0) {
                    $conditions = array_map(function ($param) {
                        return "p.framematerialIdframematerial.idframecolor = $param";
                    }, $checkBoxes['colors']);

                    $conditionString = implode(' OR ', $conditions);
                    $conditionString = "($conditionString)";
                    $queryBuilder->andWhere($conditionString);
                }

                if (isset($checkBoxes['materials']) && sizeof($checkBoxes['materials']) > 0) {
                    $conditions = array_map(function ($param) {
                        return "mat.idframematerial = $param";
                    }, $checkBoxes['materials']);

                    $conditionString = implode(' OR ', $conditions);
                    $conditionString = "($conditionString)";
                    $queryBuilder->andWhere($conditionString);
                }
                if (isset($checkBoxes['marca']) && sizeof($checkBoxes['marca']) > 0) {
                    $conditions = array_map(function ($param) {
                        return "m.idmarca = $param";
                    }, $checkBoxes['marca']);

                    $conditionString = implode(' OR ', $conditions);
                    $conditionString = "($conditionString)";
                    $queryBuilder->andWhere($conditionString);
                }
                if (isset($checkBoxes['subcategorias']) && sizeof($checkBoxes['subcategorias']) > 0) {
                    $conditions = array_map(function ($param) {
                        return "cat.idcategoria = $param";
                    }, $checkBoxes['subcategorias']);

                    $conditionString = implode(' OR ', $conditions);
                    $conditionString = "($conditionString)";
                    $queryBuilder->andWhere($conditionString);
                }
                if (isset($checkBoxes['clases']) && sizeof($checkBoxes['clases']) > 0) {
                    $conditions = array_map(function ($param) {
                        return "cat.claseIdclase = $param";
                    }, $checkBoxes['clases']);

                    $conditionString = implode(' OR ', $conditions);
                    $conditionString = "($conditionString)";
                    $queryBuilder->andWhere($conditionString);
                }
            }


            $stocks = $queryBuilder->getQuery()->getResult();
            $exito = true;
        } catch (\Exception $e) {
            $msj = $e->getMessage();
        }

        return $this->render('laboratory_order/laboratory_order-addproducts-table.html.twig', [
            'addproducts' => $stocks,
            'exito' => $exito,
            'msj' => $msj,
            'ordenlab' => isset($maindata['ordenlab']) ? $maindata['ordenlab'] : null,
            'idstockventaordenlaboratorio' => isset($maindata['idstockventaordenlaboratorio']) ? $maindata['idstockventaordenlaboratorio'] : null
        ]);
    }

    /**
     * @Route("/new-prod-to-laborder", name="addProductToLabOrder")
     */
    public function addProductToLabOrder(Request $request, EntityManagerInterface $entityManager): Response
    {
        $user = $this->getUser();

        $receivedData = json_decode($request->getContent(), true);

        $orden = $entityManager->getRepository(Ordenlaboratorio::class)->findOneBy(['idordenlaboratorio' => $receivedData['ordenlab'] ?? -1]);
        $flujo = $orden->getFlujoexpedienteIdflujoexpediente();
        $stock = $entityManager->getRepository(Stock::class)->findOneBy(['idstock' => $receivedData['idstock'] ?? -1]);

        $query = $entityManager->createQuery(
            'SELECT v.idventa               
            FROM App\Entity\Flujoexpedienteventa fv
            INNER JOIN fv.flujoexpedienteIdflujoexpediente fe
            INNER JOIN fv.ventaIdventa v
            where v.status =:status and fe.idflujoexpediente =:idFlow
            '
        )->setParameters(['status' => "1", 'idFlow' => $flujo->getIdflujoexpediente()]);
        $sales = $query->getResult();

        if (!$orden || !$stock || count($sales) <= 0) throw new \Exception('Información invalida');

        $now = new DateTime();

        $sale = $entityManager->getRepository(Venta::class)->findOneBy(['idventa' => $sales[0]["idventa"]]);

        $stockVenta = new Stockventa();
        $stockVenta
            ->setCantidad(1)
            ->setPrecio(0)
            ->setCosto($stock->getProductoIdproducto()->getCosto())
            ->setCreacion($now)
            ->setModificacion($now)
            ->setPreciofinal(0)
            ->setPreciobase($stock->getProductoIdproducto()->getPrecio())
            ->setProductoIdproducto($stock->getProductoIdproducto())
            ->setStockIdstock($stock)
            ->setVentaIdventa($sale);

        $entityManager->persist($stockVenta);
        $entityManager->flush();

        $orderStock = new Stockventaordenlaboratorio();
        $orderStock
            ->setCreacion($now)
            ->setMainproduct("0")
            ->setOrdenlaboratorioIdordenlaboratorio($orden)
            ->setStockventaIdstockventa($stockVenta);

        $entityManager->persist($orderStock);
        $entityManager->flush();

        $newAmount = $stock->getCantidad() - 1;
        $stock->setCantidad($newAmount);
        $stock->setModificacion($now);

        $entityManager->persist($stock);
        $entityManager->flush();

        return $this->getMicas($request, $entityManager);
    }

    /**
     * @Route("/switch-prod-in-laborder", name="switchProductToLabOrder")
     */
    public function switchProductToLabOrder(Request $request, EntityManagerInterface $entityManager): Response
    {
        $receivedData = json_decode($request->getContent(), true);
        $stockVentaOrdenLab = $entityManager->getRepository(Stockventaordenlaboratorio::class)->findOneBy(['idstockventaordenlaboratorio' => $receivedData['idstockventaordenlaboratorio'] ?? -1]);

        $orden = $stockVentaOrdenLab->getOrdenlaboratorioIdordenlaboratorio();
        $stockVenta = $stockVentaOrdenLab->getStockventaIdstockventa();
        $flujo = $orden->getFlujoexpedienteIdflujoexpediente();

        $stock = $entityManager->getRepository(Stock::class)->findOneBy(['idstock' => $receivedData['idstock'] ?? -1]);

        $query = $entityManager->createQuery(
            'SELECT v.idventa               
            FROM App\Entity\Flujoexpedienteventa fv
            INNER JOIN fv.flujoexpedienteIdflujoexpediente fe
            INNER JOIN fv.ventaIdventa v
            where v.status =:status and fe.idflujoexpediente =:idFlow
            '
        )->setParameters(['status' => "1", 'idFlow' => $flujo->getIdflujoexpediente()]);
        $sales = $query->getResult();

        if (!$orden || (!$stock && !$stockVentaOrdenLab) || count($sales) <= 0) throw new \Exception('Información invalida');

        $now = new DateTime();
        $sale = $entityManager->getRepository(Venta::class)->findOneBy(['idventa' => $sales[0]["idventa"]]);


        $newstockVenta = new Stockventa();
        $newstockVenta
            ->setCantidad(1)
            ->setPrecio($stock->getProductoIdproducto()->getPrecio())
            ->setCosto($stock->getProductoIdproducto()->getCosto())
            ->setCreacion($now)
            ->setModificacion($now)
            ->setPreciofinal($stockVenta->getPreciofinal())
            ->setPreciobase($stockVenta->getPreciobase())
            ->setProductoIdproducto($stock->getProductoIdproducto())
            ->setStockIdstock($stock)
            ->setVentaIdventa($sale);

        $entityManager->persist($newstockVenta);
        $entityManager->flush();

        $orderStock = new Stockventaordenlaboratorio();
        $orderStock
            ->setCreacion($now)
            ->setMainproduct("0")
            ->setOrdenlaboratorioIdordenlaboratorio($orden)
            ->setStockventaordenlaboratorioIdstockventaordenlaboratorio($stockVentaOrdenLab)
            ->setStockventaIdstockventa($newstockVenta);

        $entityManager->persist($orderStock);
        $entityManager->flush();

        $newAmount = $stock->getCantidad() - 1;
        $stock->setCantidad($newAmount);
        $stock->setModificacion($now);

        $entityManager->persist($stock);
        $entityManager->flush();

        $orden->setEtapa(4);
        $entityManager->persist($orden);

        // EVENT SAVE
        $User = $this->getUser();
        $labevent = new Event();
        $etype = $entityManager->getRepository(Eventtype::class)->findOneBy(array('name' => 'asignar-micas')) ?? new Eventtype();

        $etype
            ->setName('asignar-micas')
            ->setDescription('Se asignaron las micas');

        $entityManager->persist($etype);
        $entityManager->flush();

        $now = new DateTime();

        $labevent
            ->setUsuarioIdusuario($User)
            ->setCreationdate($now)
            ->setEventtypeIdeventtype($etype)
            ->setClienteIdcliente($orden->getClienteIdcliente())
            ->setUpdatedate($now)
            ->setOrdenlaboratorioIdordenlaboratorio($orden);

        $entityManager->persist($labevent);



        return $this->getMicas($request, $entityManager);
    }


    /**
     * @Route("/show-order-history", name="laboratory-order-show-order-history")
     */
    public function showOrderHistory(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $laboratoryOrderId = $request->get("laboratoryOrderId");

        $query = $em->createQuery(
            'SELECT e.creationdate, et.description, e.comments, sh.trackingnumber, usr.nombre, usr.apellidopaterno, usr.apellidomaterno
        FROM App\Entity\Event e
        INNER JOIN e.eventtypeIdeventtype et
        INNER JOIN e.ordenlaboratorioIdordenlaboratorio ol
        LEFT JOIN e.shipmentIdshipment sh
        LEFT JOIN e.usuarioIdusuario usr
        WHERE e.status =:status AND ol.idordenlaboratorio =:laboratoryOrderId
        ORDER BY e.creationdate ASC'
        )->setParameters(["status" => '1', "laboratoryOrderId" => $laboratoryOrderId]);
        $events = $query->getResult();

        return $this->render('laboratory_order/show-order-history.html.twig', [
            'events' => $events,
            'laboratoryOrderId' => $laboratoryOrderId,
        ]);
    }


    /**
     * @Route("/show-order-history-download", name="laboratory-order-show-order-history-export")
     */
    public function exportOrderHistory(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $laboratoryOrderId = $request->get("laboratoryOrderId");

        $query = $em->createQuery(
            'SELECT e.creationdate, et.description, e.comments, sh.trackingnumber, usr.nombre, usr.apellidopaterno, usr.apellidomaterno
             FROM App\Entity\Event e
             INNER JOIN e.eventtypeIdeventtype et
             INNER JOIN e.ordenlaboratorioIdordenlaboratorio ol
             INNER JOIN e.usuarioIdusuario usr
             LEFT JOIN e.shipmentIdshipment sh
             WHERE e.status =:status AND ol.idordenlaboratorio =:laboratoryOrderId
             ORDER BY e.creationdate ASC'
        )->setParameters(["status" => '1', "laboratoryOrderId" => $laboratoryOrderId]);
        $events = $query->getResult();

        // Create new Spreadsheet object
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set document properties
        $spreadsheet->getProperties()->setCreator('Your Name')
            ->setLastModifiedBy('Your Name')
            ->setTitle('Order History')
            ->setSubject('Order History')
            ->setDescription('Order History document generated using PHPSpreadsheet.');

        // Add header row
        $sheet->setCellValue('A1', 'Fecha');
        $sheet->setCellValue('B1', 'Descripción');
        $sheet->setCellValue('C1', 'Comentarios');
        $sheet->setCellValue('D1', 'Número de envío');

        $sheet->getColumnDimension('A')->setWidth(20);
        $sheet->getColumnDimension('B')->setWidth(30);
        $sheet->getColumnDimension('C')->setWidth(40);
        $sheet->getColumnDimension('D')->setWidth(20);

        // Add data rows
        $row = 2;
        foreach ($events as $event) {
            $sheet->setCellValue('A' . $row, $event['creationdate']->format('Y-m-d H:i:s'));
            $sheet->setCellValue('B' . $row, $event['description']);
            $sheet->setCellValue('C' . $row, $event['comments']);
            $sheet->setCellValue('D' . $row, $event['trackingnumber']);
            $row++;
        }

        // Create a StreamedResponse to send the file
        $response = new StreamedResponse(function () use ($spreadsheet) {
            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
        });

        // Set headers for file download
        $dispositionHeader = $response->headers->makeDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            'order_history.xlsx'
        );

        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Cache-Control', 'max-age=0');
        $response->headers->set('Content-Disposition', $dispositionHeader);

        return $response;
    }

    /**
     * @Route("/get-suppliers", name="laboratory-get-suppliers")
     */
    public function getSuppliers(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $laboratoryOrderId = $request->get("laboratoryOrderId");
        $LaboratoryOrder = $em->getRepository(Ordenlaboratorio::class)->findOneBy(array('idordenlaboratorio' => $laboratoryOrderId));
        $targetLocation = null;
        $enterpriseId = null;
        $flujoExpediente = $LaboratoryOrder->getFlujoexpedienteIdflujoexpediente();
        if ($flujoExpediente && $flujoExpediente->getSucursalIdsucursal()) {
            $targetLocation = $flujoExpediente->getSucursalIdsucursal()->getIdsucursal();
            if ($flujoExpediente->getSucursalIdsucursal()->getEmpresaIdempresa()) {
                $enterpriseId = $flujoExpediente->getSucursalIdsucursal()->getEmpresaIdempresa()->getIdempresa();
            }
        }

        $query = $em->createQuery(
            'SELECT p.idproveedor, p.nombre, p.sendglasses 
            FROM App\Entity\Proveedor p
            WHERE p.status =:status
            ORDER BY p.nombre ASC'
        )->setParameters(["status" => '1']);

        $suppliers = $query->getResult();

        $query = $em->createQuery(
            'SELECT s.idsucursal, s.nombre
            FROM App\Entity\Sucursal s
            INNER JOIN s.empresaIdempresa e
            WHERE s.status =:status AND s.tipo =:type AND e.idempresa =:enterpriseId
            ORDER BY s.nombre ASC'
        )->setParameters(["type" => 'sucursal', "status" => '1', "enterpriseId" => $enterpriseId]);

        $locations = $query->getResult();

        return $this->render('laboratory_order/get-suppliers.html.twig', [
            'suppliers' => $suppliers,
            'locations' => $locations,
            'laboratoryOrderId' => $laboratoryOrderId,
            'targetLocation' => $targetLocation
        ]);
    }
    /**
     * @Route("/send-to-supplier", name="laboratory-send-to-supplier")
     */
    public function sendToSupplier(Request $request): Response
    {
        $msg = "";
        $success = false;
        try {
            $em = $this->getDoctrine()->getManager();
            $laboratoryOrderId = $request->get("laboratoryOrderId");
            $supplierId = $request->get("supplierId");
            $sendglasses = $request->get("sendglasses");
            $locationId = $request->get("locationId");

            if ($supplierId == "-1") throw new \Exception('No se seleccionó el proveedor');

            $LaboratoryOrder = $em->getRepository(Ordenlaboratorio::class)->findOneBy(array('idordenlaboratorio' => $laboratoryOrderId));
            $Supplier = $em->getRepository(Proveedor::class)->findOneBy(array('idproveedor' => $supplierId));

            $shipord = new Shipmentordenlaboratorio();
            $flujo = $LaboratoryOrder->getFlujoexpedienteIdflujoexpediente();
            $dir = $flujo->getSucursalIdsucursal();
            $shipord
                ->setOrdenlaboratorioIdordenlaboratorio($LaboratoryOrder)
                ->setDestination($dir);
            if ($sendglasses == '1') {

                $LaboratoryOrder->setEtapa(12);
                $Shipment = new Shipment();
                $now = new DateTime();
                $Shipment->setCreationdate($now);
                $Shipment->setUpdatedate($now);
                $em->persist($Shipment);

                $Location = $em->getRepository(Sucursal::class)->findOneBy(array('idsucursal' => $locationId));
                if (!$Location) throw new \Exception('No se seleccionó la sucursal');

                $shipord->setDestination($Location);
                $shipord->setDelivered('1');
                $shipord->setDeliverytime($now);
                $shipord->setShipmentIdshipment($Shipment);
                $LaboratoryOrder->setSendglassessupplier('1');
            } else $LaboratoryOrder->setEtapa(10);
            $LaboratoryOrder->setProveedorIdproveedor($Supplier);
            $em->persist($shipord);
            $em->persist($LaboratoryOrder);

            $em->flush();

            if ($sendglasses == '1') {
                $trackingNumber = str_replace(' ', '', $Supplier->getName()).'-'.$Shipment->getIdshipment();
                $Shipment->setTrackingnumber($trackingNumber);
                $em->persist($Shipment);
                $em->flush();
            }
            $success = true;
        } catch (\Exception $e) {
            $msg .= $e->getMessage();
        }
        return $this->json([
            'msg' => $msg,
            'success' => $success
        ]);
    }

    /**
     * @Route("/update-stage", name="update_stage", methods={"POST"})
     */
    public function updateStage(Request $request, EntityManagerInterface $entityManager): Response
    {
        $data = json_decode($request->getContent(), true);

        if (!isset($data['idordenlaboratorio']) || !isset($data['stage'])) {
            return $this->json(['success' => false, 'message' => 'Datos incompletos'], Response::HTTP_BAD_REQUEST);
        }

        $order = $entityManager->getRepository(Ordenlaboratorio::class)->find($data['idordenlaboratorio']);

        if (!$order) {
            return $this->json(['success' => false, 'message' => 'Orden no encontrada'], Response::HTTP_NOT_FOUND);
        }

        $order->setStage((string)$data['stage']);

        // Si el stage es 11 (Entregado), establecer fechasalidalaboratorio
        if ($data['stage'] == '11') {
            $order->setFechasalidalaboratorio(new \DateTime());
        }

        $entityManager->persist($order);
        $entityManager->flush();

        return $this->json(['success' => true, 'message' => 'Etapa actualizada correctamente']);
    }
}
