<?php

namespace App\Controller;

use App\Form\AgregarClienteType;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Serializer\SerializerInterface;
use Twig\Environment;
use App\Entity\Cliente;
use App\Entity\Usuario;
use App\Service\FileUploader;
use App\Form\ClientefacturadatosType;
use App\Form\AgregarBeneficiarioType;
use App\Entity\Clientefacturadatos;
use App\Entity\Empresacliente;
use App\Entity\OrdenSalida;
use App\Entity\Flujoexpediente;
use App\Entity\Ordenlaboratorio;
use Doctrine\ORM\EntityManagerInterface;
use App\Entity\Venta;
use App\Entity\Stock;
use App\Entity\Stockventa;
use App\Entity\Stockventaordenlaboratorio;
use DateTime;


/**
 * @Route("/admin/dashboard")
 */
class DashboardFlujoExpedienteController extends AbstractController
{
    /**
     * @Route("/", name="dashboard_flujo_expediente_index")
     */
    public function traspasoDashboard()
    {
        $em = $this->getDoctrine()->getManager();
        $Usuario = $this->getUser();

        // Verificar que el usuario existe
        if (!$Usuario) {
            throw $this->createAccessDeniedException('Usuario no autenticado');
        }

        $puesto = $Usuario->getPuesto();
        $rol = $Usuario->getRol();

        // Verificar que el rol no sea null y sea ROLE_SUPERVISOR
        if($rol !== null && $rol === "ROLE_SUPERVISOR"){
            return $this->redirectToRoute('app_oficial');
        }

        // Verificar que el usuario tiene sucursal asignada
        $sucursalUsuario = $Usuario->getSucursalIdsucursal();
        if (!$sucursalUsuario) {
            throw $this->createNotFoundException('Usuario no tiene una sucursal asociada.');
        }

        $locationId = $sucursalUsuario->getIdsucursal();
        $today = new \DateTime('today');
        $inventario = "";
        $stages = ['Registro datos Personales', 'Examen Visual', 'Orden de laboratorio', 'Venta', 'Firma', 'Entrega de lentes pendiente', 'Finalizado'];
        $orderStages = [
            'Orden creada',
            'Completar flujo',
            'Sin micas',
            'Micas asignadas',
            'Esperando Material',
            'Pendiente',
            'Procesando',
            'Pausado',
            'Calidad',
            'Terminado',
        ];
        //$today = new \DateTime('2024-05-15');

        // Query para obtener traspasos (mantenemos los queries existentes)
        $query1 = $em->createQuery(
            'SELECT os.fechasalida, so.nombre
         FROM App\Entity\Ordensalida os
         INNER JOIN os.transpasoalmacenIdtranspasoalmacen ta
         INNER JOIN ta.sucursalIdsucursalorigen so
         INNER JOIN ta.sucursalIdsucursaldestino sd
         WHERE os.status = :status AND sd.idsucursal = :sucursalDestino AND os.aceptada = :estado'
        )->setParameters([
            'status' => "1",
            'sucursalDestino' => $sucursalUsuario->getIdsucursal(),
            'estado' => "2"
        ]);

        $traspasoDashboard = $query1->getResult();


        $query = $em->createQuery(
            'SELECT DISTINCT ship.trackingnumber, ship.idshipment
            FROM App\Entity\Shipmentordenlaboratorio sol
            INNER JOIN sol.destination dest
            INNER JOIN sol.shipmentIdshipment ship
            WHERE dest.idsucursal =:locationId AND sol.status =:status
            AND sol.accepted !=:status
            '
        )->setParameters(["status" => '1', "locationId" => $locationId]);
        $shipments = $query->getResult();

        $query2 = $em->createQuery(
            'SELECT os.fechasalida, so.nombre AS sucursal_origen, sd.nombre AS sucursal_destino, 
                os.aceptada, rs.nombre AS responsable, os.idordensalida
         FROM App\Entity\Ordensalida os
         INNER JOIN os.transpasoalmacenIdtranspasoalmacen ta
         INNER JOIN ta.sucursalIdsucursalorigen so
         INNER JOIN ta.sucursalIdsucursaldestino sd
         INNER JOIN os.responsablesalida rs
         WHERE os.status = :status AND sd.idsucursal = :sucursalDestino AND os.aceptada = :estado'
        )->setParameters([
            'status' => "1",
            'sucursalDestino' => $sucursalUsuario->getIdsucursal(),
            'estado' => "2"
        ]);

        $traspasoModal = $query2->getResult();

        // Query para obtener las ventas del día
        $queryVentas = $em->createQuery(
            'SELECT v.folio AS folio, 
                tv.nombre AS tipoVenta, 
                c.nombre AS nombreCliente, 
                c.apellidopaterno AS apellidoPaternoCliente, 
                c.apellidomaterno AS apellidoMaternoCliente, 
                suc.nombre AS nombreSucursal, 
                v.fechaventa AS fechaVenta,
                v.total AS totalVenta,
                SUM(CASE WHEN p.tipoproducto = 1 THEN sv.cantidad ELSE 0 END) as almacenesVendidos,
                SUM(CASE WHEN p.tipoproducto = 2 THEN sv.cantidad ELSE 0 END) as serviciosVendidos
         FROM App\Entity\Stockventa sv
         INNER JOIN sv.ventaIdventa v
         INNER JOIN v.tipoventaIdtipoventa tv
         INNER JOIN sv.stockIdstock s
         INNER JOIN s.productoIdproducto p
         INNER JOIN v.sucursalIdsucursal suc
         INNER JOIN v.clienteIdcliente c
         WHERE sv.status = :status 
           AND v.status = :statusVenta 
           AND v.fechaventa BETWEEN :startOfDay AND :endOfDay
         GROUP BY v.idventa, tv.idtipoventa, c.idcliente, suc.idsucursal, v.folio, v.fechaventa, v.total'
        )->setParameters([
            'status' => "1",
            'statusVenta' => "1",
            'startOfDay' => $today->format('Y-m-d 00:00:00'),
            'endOfDay' => $today->format('Y-m-d 23:59:59')
        ]);

        $ventas = $queryVentas->getResult();

        // Inicializar valores a 0 si no hay ventas
        if (empty($ventas)) {
            $ventas = [
                [
                    'totalVentas' => 0,
                    'totalVendido' => 0,
                    'almacenesVendidos' => 0,
                    'serviciosVendidos' => 0,
                    'folio' => null,
                    'tipoVenta' => null,
                    'nombreCliente' => null,
                    'apellidoPaternoCliente' => null,
                    'apellidoMaternoCliente' => null,
                    'nombreSucursal' => null,
                    'fechaVenta' => null,
                    'totalVenta' => 0
                ]
            ];
        } else {
            $totalVentas = count($ventas);
            $almacenesVendidos = array_sum(array_column($ventas, 'almacenesVendidos'));
            $serviciosVendidos = array_sum(array_column($ventas, 'serviciosVendidos'));
            $totalVendido = array_sum(array_column($ventas, 'totalVenta'));

            // Añadir estas variables a la matriz de resultados
            foreach ($ventas as &$venta) {
                $venta['totalVentas'] = $totalVentas;
                $venta['almacenesVendidos'] = $almacenesVendidos;
                $venta['serviciosVendidos'] = $serviciosVendidos;
                $venta['totalVendido'] = $totalVendido;
            }
        }

        $queryProductosMasVendidos = $em->createQuery(
            'SELECT p.descripcion, SUM(sv.cantidad) as ventas
             FROM App\Entity\Stockventa sv
             INNER JOIN sv.ventaIdventa v
             INNER JOIN sv.stockIdstock s
             INNER JOIN s.productoIdproducto p
             WHERE s.sucursalIdsucursal = :sucursalId AND p.tipoproducto=:tipoProducto AND p.masivounico=:masivoUnico
             GROUP BY p.idproducto
             ORDER BY ventas DESC'
        )->setParameters([
            'sucursalId' => $sucursalUsuario->getIdsucursal(),
            'tipoProducto' => "1",
            'masivoUnico' => "1"
        ])
            ->setMaxResults(5);
        $productosMasVendidos = $queryProductosMasVendidos->getResult();

        $queryTotalUnidades = $em->createQuery(
            'SELECT SUM(s.cantidad) as totalUnidades
             FROM App\Entity\Stock s
             INNER JOIN s.productoIdproducto p
             WHERE s.sucursalIdsucursal = :sucursalId AND p.tipoproducto=:tipoProducto AND p.masivounico=:masivoUnico'
        )->setParameters([
            'sucursalId' => $sucursalUsuario->getIdsucursal(),
            'tipoProducto' => "1",
            'masivoUnico' => "1"
        ]);
        $totalUnidades = $queryTotalUnidades->getSingleScalarResult();

        $queryProductosMasExistencia = $em->createQuery(
            'SELECT p.descripcion, s.cantidad
             FROM App\Entity\Stock s
             INNER JOIN s.productoIdproducto p
             WHERE s.sucursalIdsucursal = :sucursalId AND p.tipoproducto=:tipoProducto AND p.masivounico=:masivoUnico
             ORDER BY s.cantidad DESC'
        )->setParameters([
            'sucursalId' => $sucursalUsuario->getIdsucursal(),
            'tipoProducto' => "1",
            'masivoUnico' => "1"
        ])
            ->setMaxResults(5);
        $productosMasExistencia = $queryProductosMasExistencia->getResult();


        $queryPendientes = $em->createQuery(
            'SELECT COUNT(f.idflujoexpediente) as pendientes
         FROM App\Entity\Flujoexpediente f
         JOIN f.usuarioIdusuario u
         WHERE f.status = :status AND f.etapa != :finalizado AND f.creacion BETWEEN :startOfDay AND :endOfDay
         '
        )->setParameters([
            'status' => "1",
            'finalizado' => 7,
            'startOfDay' => $today->format('Y-m-d 00:00:00'),
            'endOfDay' => $today->format('Y-m-d 23:59:59')
        ]);
        $pendientes = $queryPendientes->getSingleScalarResult();

        // Obtener todos los flujos finalizados (etapa = 7)
        $queryFinalizados = $em->createQuery(
            'SELECT COUNT(f.idflujoexpediente) as finalizados
         FROM App\Entity\Flujoexpediente f
         JOIN f.usuarioIdusuario u
         WHERE f.status = :status AND f.etapa = :finalizado AND f.creacion BETWEEN :startOfDay AND :endOfDay
         '
        )->setParameters([
            'status' => "1",
            'finalizado' => 7,
            'startOfDay' => $today->format('Y-m-d 00:00:00'),
            'endOfDay' => $today->format('Y-m-d 23:59:59')
        ]);
        $finalizados = $queryFinalizados->getSingleScalarResult();


        $tomorrow = new \DateTime('tomorrow');

        $queryFlujos = $em->createQuery(
            'SELECT c.nombre, c.apellidopaterno, c.apellidomaterno, f.idflujoexpediente, f.status, f.etapa, 
            u.nombre as nombreUsuario, u.apellidopaterno as apellidoUsuario, uni.nombre as unidad, f.actualizacion
     FROM App\Entity\Flujoexpediente f
     JOIN f.clienteIdcliente c
     JOIN f.usuarioIdusuario u
     LEFT JOIN c.unidadIdunidad uni
     WHERE f.status = :status AND f.creacion >= :today AND f.creacion < :tomorrow
     ORDER BY f.idflujoexpediente DESC'
        )->setParameters([
            'today' => $today,
            'tomorrow' => $tomorrow,
            'status' => "1"
        ]);

        $flujoExpediente = $queryFlujos->getResult();


        $queryLaboratorio = $em->createQuery(
            'SELECT COUNT(ol.idordenlaboratorio) AS Laboratorio
            FROM App\Entity\Ordenlaboratorio ol
            INNER JOIN ol.flujoexpedienteIdflujoexpediente fe
            LEFT JOIN fe.ventaIdventa v
            INNER JOIN fe.sucursalIdsucursal suc
            INNER JOIN fe.clienteIdcliente c
            WHERE ol.status = :status AND fe.status = :status AND ol.creacion BETWEEN :startOfDay AND :endOfDay
            '
        )->setParameters([
            "status" => '1',
            'startOfDay' => $today->format('Y-m-d 00:00:00'),
            'endOfDay' => $today->format('Y-m-d 23:59:59')
        ]);

        $laboratoryOrders = $queryLaboratorio->getSingleScalarResult();


        $queryCalidad = $em->createQuery(
            'SELECT COUNT(ol.idordenlaboratorio) AS Calidad
            FROM App\Entity\Ordenlaboratorio ol
            INNER JOIN ol.flujoexpedienteIdflujoexpediente fe
            WHERE ol.status = :status AND ol.etapa = :etapaCalidad AND ol.creacion BETWEEN :startOfDay AND :endOfDay
            AND fe.status = :status'
        )->setParameters([
            "status" => '1',
            "etapaCalidad" => "9",
            'startOfDay' => $today->format('Y-m-d 00:00:00'),
            'endOfDay' => $today->format('Y-m-d 23:59:59')
        ]);
        $calidadOrders = $queryCalidad->getSingleScalarResult();


        $queryTratamientos = $em->createQuery(
            'SELECT COUNT(p.tipoproducto) AS Tratamientos
             FROM App\Entity\Stockventa sv
             INNER JOIN sv.ventaIdventa v
             INNER JOIN sv.stockIdstock s
             INNER JOIN s.productoIdproducto p
             WHERE sv.status = :status AND s.status= :status AND v.fechaventa BETWEEN :startOfDay AND :endOfDay
             AND p.tipoproducto=:tipoProducto
            '
        )->setparameters([
            'status' => "1",
            'tipoProducto' => "2",
            'startOfDay' => $today->format('Y-m-d 00:00:00'),
            'endOfDay' => $today->format('Y-m-d 23:59:59')
        ]);

        $tratamientos = $queryTratamientos->getSingleScalarResult();

        $queryPiezas = $em->createQuery(
            'SELECT COUNT(p.tipoproducto) AS Tratamientos
             FROM App\Entity\Stockventa sv
             INNER JOIN sv.ventaIdventa v
             INNER JOIN sv.stockIdstock s
             INNER JOIN s.productoIdproducto p
             WHERE sv.status = :status AND s.status= :status AND v.fechaventa BETWEEN :startOfDay AND :endOfDay
             AND p.tipoproducto=:tipoProducto AND p.masivounico=:masivoUnico
            '
        )->setparameters([
            'status' => "1",
            'tipoProducto' => "1",
            'masivoUnico' => "1",
            'startOfDay' => $today->format('Y-m-d 00:00:00'),
            'endOfDay' => $today->format('Y-m-d 23:59:59')
        ]);

        $piezasOrden = $queryPiezas->getSingleScalarResult();


        // Query para obtener detalles de órdenes de laboratorio
        $queryLaboratorioDetalles = $em->createQuery(
            'SELECT ol.idordenlaboratorio, ol.stage, v.folio, 
            CONCAT(c.nombre, \' \', c.apellidopaterno, \' \', c.apellidomaterno) AS cliente, 
            ol.actualizacion, p.descripcion AS producto
     FROM App\Entity\Stockventaordenlaboratorio svol
     INNER JOIN svol.ordenlaboratorioIdordenlaboratorio ol
     INNER JOIN ol.flujoexpedienteIdflujoexpediente fe
     INNER JOIN fe.clienteIdcliente c
     INNER JOIN svol.stockventaIdstockventa sv
     INNER JOIN sv.ventaIdventa v
     INNER JOIN sv.stockIdstock st
     INNER JOIN st.productoIdproducto p
     WHERE ol.status = :status AND fe.status = :status AND ol.creacion BETWEEN :startOfDay AND :endOfDay'
        )->setParameters([
            "status" => '1',
            'startOfDay' => $today->format('Y-m-d 00:00:00'),
            'endOfDay' => $today->format('Y-m-d 23:59:59')
        ]);

        $laboratoryOrderDetailsRaw = $queryLaboratorioDetalles->getResult();

        // Procesar los datos para agrupar productos por orden
        $laboratoryOrderDetails = [];
        foreach ($laboratoryOrderDetailsRaw as $detail) {
            $orderId = $detail['idordenlaboratorio'];
            if (!isset($laboratoryOrderDetails[$orderId])) {
                $laboratoryOrderDetails[$orderId] = [
                    'idordenlaboratorio' => $detail['idordenlaboratorio'],
                    'stage' => $detail['stage'],
                    'folio' => $detail['folio'],
                    'cliente' => $detail['cliente'],
                    'actualizacion' => $detail['actualizacion'],
                    'productos' => []
                ];
            }
            $laboratoryOrderDetails[$orderId]['productos'][] = $detail['producto'];
        }

        // Convertir productos a una cadena separada por comas
        foreach ($laboratoryOrderDetails as &$order) {
            $order['productos'] = implode(', ', $order['productos']);
        }

        $whereSucursal = ($rol !== null && $rol == "ROLE_ADMIN") ? "" : " AND suc.idsucursal = " . $sucursalUsuario->getIdsucursal();

        $query = $em->createQuery('
            SELECT u.nombre, u.apellidopaterno, u.idusuario
            FROM App\Entity\Usuario u
            INNER JOIN u.sucursalIdsucursal suc
            WHERE u.status = :status' . $whereSucursal)
            ->setParameters([
                'status' => 1
            ]);
        $activeUsers = $query->getResult();

        $query = $em->createQuery(
            'SELECT d.archivo, su.nombre, ds.iddocumentossucursal, d.iddocumentos
     FROM App\Entity\Documentosucursal ds
     INNER JOIN ds.documentosIddocumentos d
     INNER JOIN d.usuarioIdusuario u
     INNER JOIN ds.sucursalIdsucursal su
     WHERE su.status = :status AND ds.status = :status AND ds.sucursalIdsucursal = :sucursalId'
        )->setParameters([
            'status' => "1",
            'sucursalId' => $sucursalUsuario->getIdsucursal(),
        ]);

        $documentos = $query->getResult();

        $query = $em->createQuery(
            'SELECT a.texto AS Contenido, COUNT(a.idanuncios) AS Notificaciones, a.idanuncios, a.titulo, a.fechacreacion AS Fecha
     FROM App\Entity\Anuncios a
     WHERE a.status = :status AND a.fechacreacion BETWEEN :startOfDay AND :endOfDay
     GROUP BY a.texto'
        )->setParameters([
            "status" => '1',
            'startOfDay' => $today->format('Y-m-d 00:00:00'),
            'endOfDay' => $today->format('Y-m-d 23:59:59')
        ]);

        $notificaciones = $query->getResult();


// Validar que los documentos no sean null
        if (empty($documentos)) {
            // Manejar el caso donde no se encuentran documentos
            return $this->render('dashboard_flujo_expediente/vendedor-optometrista.html.twig', [
                'error' => 'No se encontraron documentos para la sucursal',
                // Otras variables necesarias para la vista
                'traspasoDashboard' => $traspasoDashboard,
                'shipments' => $shipments,
                'traspasoModal' => $traspasoModal,
                'ventas' => $ventas,
                'today' => $today,
                'productosMasVendidos' => $productosMasVendidos,
                'totalUnidades' => $totalUnidades,
                'productosMasExistencia' => $productosMasExistencia,
                'sucursalUsuario' => $sucursalUsuario,
                'flujoExpediente' => $flujoExpediente,
                'pendientes' => $pendientes,
                'finalizados' => $finalizados,
                'stages' => $stages,
                'laboratoryOrders' => $laboratoryOrders,
                'calidadOrders' => $calidadOrders,
                'tratamientos' => $tratamientos,
                'piezasOrden' => $piezasOrden,
                'orderStages' => $orderStages,
                'laboratoryOrderDetails' => $laboratoryOrderDetails,
                'activeUsers' => $activeUsers,
                'notificaciones' => $notificaciones,

            ]);
        }

        return $this->render('dashboard_flujo_expediente/vendedor-optometrista.html.twig', [
            'documentos' => $documentos,
            'shipments' => $shipments,
            'traspasoDashboard' => $traspasoDashboard,
            'traspasoModal' => $traspasoModal,
            'ventas' => $ventas,
            'today' => $today,
            'productosMasVendidos' => $productosMasVendidos,
            'totalUnidades' => $totalUnidades,
            'productosMasExistencia' => $productosMasExistencia,
            'sucursalUsuario' => $sucursalUsuario,
            'flujoExpediente' => $flujoExpediente,
            'pendientes' => $pendientes,
            'finalizados' => $finalizados,
            'stages' => $stages,
            'laboratoryOrders' => $laboratoryOrders,
            'calidadOrders' => $calidadOrders,
            'tratamientos' => $tratamientos,
            'piezasOrden' => $piezasOrden,
            'orderStages' => $orderStages,
            'laboratoryOrderDetails' => $laboratoryOrderDetails,
            'activeUsers' => $activeUsers,
            'notificaciones' => $notificaciones,
        ]);
    }


    /**
     * LABORATORIO
     */


    /**
     * @Route("/dashboardAntiguo", name="app_dashboard_flujo_expediente")
     */
    public function dashboard(Request $request, FileUploader $fileUploader, EntityManagerInterface $em): Response
    {
        $exito = false;
        $msj = "";
        $formularioGuardado = false;
        $flujoExpediente = null;
        $form = $this->createForm(AgregarClienteType::class);
        $form->handleRequest($request);

        $orden1 = "";

        $rol = $this->getUser()->getRol();
        $whereSucursal = ($rol !== null && $rol == "ROLE_SUPER_ADMIN") ? "" : " AND suc.idsucursal = " . $this->getUser()->getSucursalIdsucursal()->getIdsucursal();

        $query = $em->createQuery('
            SELECT u.nombre, u.apellidopaterno, u.idusuario
            FROM App\Entity\Usuario u
            INNER JOIN u.sucursalIdsucursal suc
            WHERE u.status = :status' . $whereSucursal)
            ->setParameters([
                'status' => 1
            ]);
        $activeUsers = $query->getResult();

        $query = $em->createQuery('
            SELECT DISTINCT u.idusuario, u.nombre, u.apellidopaterno
            FROM App\Entity\Flujoexpediente fe
            INNER JOIN fe.usuarioIdusuario u
            INNER JOIN u.sucursalIdsucursal suc
            WHERE u.status = :status' . $whereSucursal)
            ->setParameters([
                'status' => 1
            ]);
        $flowUsers = $query->getResult();

        $query = $em->createQuery(
            'SELECT c.nombre, c.apellidopaterno, c.apellidomaterno, f.idflujoexpediente, f.status, f.etapa, 
                u.nombre as nombreUsuario, u.apellidopaterno as apellidoUsuario
                FROM App\Entity\Flujoexpediente f
                JOIN f.clienteIdcliente c
                JOIN f.usuarioIdusuario u
                WHERE f.status = :status
                ORDER BY f.idflujoexpediente DESC
                '
        )->setParameter('status', '1');

        $results = $query->getResult();

        $orden = null;

        try {

            $user = $this->getUser();
            $sucursalAsignada = $user->getSucursalIdsucursal();


            $query = $em->createQuery(
                'SELECT o.creacion as creacion, sd.nombre as sucursalDestino, rs.nombre as responsableSalida, ta.notas, o.idordensalida AS IdOrdensalida,
                o.aceptada AS Aceptada
                FROM App\Entity\Ordensalida o
                JOIN o.transpasoalmacenIdtranspasoalmacen ta
                JOIN o.responsablesalida rs
                JOIN ta.sucursalIdsucursaldestino sd
                WHERE ta.status = :status AND sd = :sucursalAsignada AND o.aceptada = 2 order by o.creacion desc
                '
            )->setParameters([
                'status' => '1',
                'sucursalAsignada' => $sucursalAsignada
            ]);

            $orden = $query->getResult();

            $user = $this->getUser();
            $sucursalUsuario = $user->getSucursalIdsucursal();

            $query = $em->createQuery(
                'SELECT o, ta, rs
             FROM App\Entity\Ordensalida o
             JOIN o.transpasoalmacenIdtranspasoalmacen ta
             JOIN o.responsablesalida rs
             JOIN ta.sucursalIdsucursaldestino sd
             WHERE ta.status = :status AND sd = :sucursalUsuario AND o.aceptada = 2' // Estado pendiente con código 2
            )->setParameters([
                'status' => '1',
                'sucursalUsuario' => $sucursalUsuario
            ]);

            $orden1 = $query->getResult();

            $query = $em->createQuery(
                'SELECT f
            FROM App\Entity\Flujoexpediente f
            WHERE f.status = :status'
            )->setParameters([
                'status' => '1'
            ])->setMaxResults(1);

            $flujoExpediente = $query->getResult();

            if ($form->isSubmitted() && $form->isValid()) {
                $cliente = $form->getData();

                $telefono = $cliente->getTelefono();

                $clienteExistente = $em->getRepository(Cliente::class)->findOneBy(['telefono' => $telefono]);

                if ($clienteExistente) {
                    throw new \Exception('Ya existe un cliente con este número de teléfono');
                }

                $em->persist($cliente);
                $em->flush();

                $datosCliente = new \stdClass();
                $datosCliente->apellidopaterno = $cliente->getApellidopaterno();
                $datosCliente->apellidomaterno = $cliente->getApellidomaterno();
                $datosCliente->nombre = $cliente->getNombre();
                $datosCliente->email = $cliente->getEmail();
                $datosCliente->telefono = $cliente->getTelefono();
            }
        } catch (\Exception $e) {
            $msj = $e->getMessage();
        }
        echo $msj;
        return $this->render('dashboard_flujo_expediente/dashboard.html.twig', [
            'form' => $form->createView(),
            'cliente' => $cliente ?? new Cliente(),
            'exito' => $exito,
            'msj' => $msj,
            'orden1' => $orden1,
            'datosCliente' => $datosCliente ?? new \stdClass(),
            'flujoExpediente' => $flujoExpediente,
            'orden' => $orden,
            'formularioGuardado' => $formularioGuardado,
            'results' => $results,
            'activeUsers' => $activeUsers,
            'flowUsers' => $flowUsers,
            'role' => $rol
        ]);
    }


    public function someMethod($id) // $id es pasado al método
    {
        // Obtiene el objeto de una base de datos
        $object = $this->getDoctrine()->getRepository(Ordensalida::class)->find($id);

        // Ahora $object está definido y puedes llamar a getId()
        $detalleUrl = $this->generateUrl('detalleOrdenSalida', ['id' => $object->getId()]);

        // ...

    }

    /**
     * @Route("/get-arriving-shipments-table", name="dashboard-get-arriving-shipments-table")
     */
    public function getArrivingShipmentsTable(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $locationId = $this->getUser()->getSucursalIdsucursal()->getIdsucursal();

        $query = $em->createQuery(
            'SELECT DISTINCT ship.trackingnumber, ship.idshipment,ship.creationdate, user.nombre
            FROM App\Entity\Shipmentordenlaboratorio sol
            INNER JOIN sol.destination dest
            INNER JOIN sol.shipmentIdshipment ship
            INNER JOIN sol.usuarioIdusuario user
            WHERE dest.idsucursal =:locationId AND sol.status =:status
            AND sol.accepted !=:status
            '
        )->setParameters(["status" => '1', "locationId" => $locationId]);
        $shipments = $query->getResult();


        return $this->render('dashboard_flujo_expediente/arriving-shipments-table.html.twig', [
            'shipments' => $shipments
        ]);
    }

    /**
     * @Route("/show-shipment-detail", name="dashboard-show-shipment-detail")
     */
    public function showShipmentDetail(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $locationId = $this->getUser()->getSucursalIdsucursal()->getIdsucursal();

        $shipmentId = $request->get("shipmentId");

        $query = $em->createQuery(
            'SELECT suc.nombre as locationName, 
            sol.idshipmentordenlaboratorio, u.idusuario, ol.idordenlaboratorio, p.modelo,
            s.codigobarras, p.codigobarrasuniversal, m.nombre as brand
            FROM App\Entity\Shipmentordenlaboratorio sol
            LEFT JOIN sol.usuarioIdusuario u
            INNER JOIN sol.destination suc
            INNER JOIN sol.ordenlaboratorioIdordenlaboratorio ol
            INNER JOIN sol.shipmentIdshipment ship
            LEFT JOIN App\Entity\Stockventaordenlaboratorio svol WITH ol.idordenlaboratorio = svol.ordenlaboratorioIdordenlaboratorio
            INNER JOIN svol.stockventaIdstockventa sv
            INNER JOIN sv.stockIdstock s
            INNER JOIN s.productoIdproducto p
            INNER JOIN p.marcaIdmarca m
            WHERE ol.status =:status AND p.ordenlaboratorio =:status AND svol.mainproduct =:status
            AND ship.idshipment =:shipmentId AND suc.idsucursal =:locationId AND sol.status =:status
            AND sol.accepted !=:status
            '
        )->setParameters(["status" => '1', 'shipmentId' => $shipmentId, 'locationId' => $locationId]);
        $laboratoryOrders = $query->getResult();


        return $this->render('dashboard_flujo_expediente/shipment-detail.twig', [
            'laboratoryOrders' => $laboratoryOrders,
            'shipmentId' => $shipmentId
        ]);
    }


    /**
     * @Route("/create-flujo-expediente", name="create_flujo_expediente", methods={"POST"})
     */
    public function createFlujoExpediente(Request $request): Response
    {
        $clienteId = $request->request->get("clienteId");
        $userId = $request->request->get("userId");

        // Llama a la lógica de creación del flujo
        $result = $this->createFlujoExpedienteForClient($clienteId, $userId);

        // Codificar el array como JSON
        return new Response(json_encode($result), Response::HTTP_OK);
    }


    /**
     * @Route("/create-flujo-expediente-sedena", name="dashboard-create-flow-sedena", methods={"POST"})
     */
    public function createFlowSedena(Request $request): Response
    {
        $clienteId = $request->request->get("clienteId");
        $userId = $request->request->get("userId");

        // Llamada a la nueva función que maneja la lógica de negocio
        $response = $this->createFlujoExpedienteForClient($clienteId, $userId, 1);

        // Manejo de la respuesta dependiendo del resultado de la función
        if ($response["success"]) {
            return $this->json($response);
        } else {
            return $this->json($response);
        }
    }


    /**
     * @Route("/admin/cambiar-status/{id}", name="cambiar_status", methods={"POST"})
     */
    public function cambiarStatus($id)
    {
        $em = $this->getDoctrine()->getManager();
        $flujoExpediente = $em->getRepository(FlujoExpediente::class)->find($id);

        if (!$flujoExpediente) {
            return new JsonResponse(['error' => 'FlujoExpediente no encontrado'], 404);
        }

        $query = $em->createQuery(
            'SELECT svol
                FROM App\Entity\Stockventaordenlaboratorio svol
                JOIN svol.ordenlaboratorioIdordenlaboratorio ol
                JOIN ol.flujoexpedienteIdflujoexpediente fe
                WHERE fe.idflujoexpediente = :flowId
                '
        )->setParameter('flowId', $flujoExpediente->getIdflujoexpediente());
        $svols = $query->getResult();

        foreach ($svols as $Svol) $em->remove($Svol);

        $flujoExpediente->setStatus(0);
        $em->flush();

        return new JsonResponse(['success' => 'Estado cambiado correctamente'], 200);
    }


    public function createFlujoExpedienteForClient($clienteId, $userId, $sedena = 0)
    {
        $cliente = $this->getDoctrine()->getRepository(Cliente::class)->find($clienteId);
        $User = $this->getDoctrine()->getRepository(Usuario::class)->find($userId);

        if (!$cliente) {
            // Manejo de error si no se encuentra el cliente con el ID dado
            return "Cliente no encontrado";
        }

        if (!$User) {
            // Manejo de error si no se encuentra el cliente con el ID dado
            return "Usuario no encontrado";
        }

        // Crear un nuevo registro en FlujoExpediente con el cliente
        $flujoExpediente = new FlujoExpediente();
        $flujoExpediente->setEtapa(1);
        $flujoExpediente->setActualizacion(new \DateTime());
        $flujoExpediente->setCreacion(new \DateTime());
        $flujoExpediente->setStatus(1);
        $flujoExpediente->setClienteIdcliente($cliente);
        $flujoExpediente->setUsuarioIdusuario($User);
        $flujoExpediente->setSucursalIdsucursal($User->getSucursalIdsucursal());

        try {
            $em = $this->getDoctrine()->getManager();
            $em->persist($flujoExpediente);
            $em->flush();

            // Devolver una respuesta de éxito
            if ($sedena == 1)
                return ['success' => true, 'idflow' => $flujoExpediente->getIdflujoexpediente()];
            else
                return ['success' => true, 'idflow' => $flujoExpediente->getIdflujoexpediente()];
        } catch (\Exception $e) {
            // Devolver una respuesta de error en caso de excepción
            if ($sedena == 1) return ['success' => false, 'idflow' => -1, "msg" => $e->getMessage()];
            else return $e->getMessage();
        }
    }

    /**
     * @Route("/ObtenerFormularioCliente", name="obtener-formulario-cliente")
     */
    public function ObtenerFormularioCliente(Request $request, FileUploader $fileUploader, SerializerInterface $serializer): JsonResponse
    {
        $exito = false; // Inicializa la variable de éxito como false.
        $msj = ""; // Inicializa el mensaje vacío.
        $em = $this->getDoctrine()->getManager(); // Obtiene el entity manager de Doctrine.

        $formularioGuardado = false; // Inicializa la variable de formulario guardado como false.

        $form = $this->createForm(AgregarClienteType::class); // Crea un nuevo formulario de tipo AgregarClienteType.
        $form->handleRequest($request); // Maneja la solicitud y asigna los datos del formulario a partir de ella.

        $cliente = ""; // Inicializa la variable cliente como una cadena vacía.
        $idcliente = ""; // Inicializa la variable idcliente como una cadena vacía.

        try {
            if ($form->isSubmitted() && $form->isValid()) { // Si el formulario se ha enviado y es válido.
                $cliente = $form->getData(); // Obtiene los datos del formulario.

                $telefono = $cliente->getTelefono(); // Obtiene el teléfono del cliente.

                // Busca en la base de datos un cliente existente con el mismo teléfono.
                $clienteExistente = $em->getRepository(Cliente::class)->findOneBy(['telefono' => $telefono]);

                if ($clienteExistente) { // Si existe un cliente con el mismo teléfono.
                    throw new \Exception('Ya existe un cliente con este número de teléfono'); // Lanza una excepción.
                }

                $em->persist($cliente); // Persiste el cliente en la base de datos.
                $em->flush(); // Hace un flush para que los cambios en el cliente se guarden en la base de datos.

                $idcliente = $cliente->getIdcliente(); // Obtiene el ID del cliente.
                $exito = true; // Establece la variable de éxito como verdadera.
            }
        } catch (\Exception $e) { // Si ocurre una excepción.
            $msj = $e->getMessage(); // Asigna el mensaje de la excepción a la variable de mensaje.
        }

        // Serializa el objeto cliente en una cadena JSON, convierte el objeto en un array
        $clienteSerializado = $serializer->serialize($cliente, 'json');

        // Devuelve una respuesta JSON con los datos del cliente, si fue exitoso, el mensaje, el cliente serializado y si el formulario fue guardado.
        return new JsonResponse([
            'idcliente' => $idcliente,
            'exito' => $exito,
            'msj' => $msj,
            'cliente' => $clienteSerializado,
            'formularioGuardado' => $formularioGuardado
        ]);
    }

    //Paso ng
    function eliminar_simbolos($string)
    {

        $string = trim($string);

        $string = str_replace(
            array('ú', 'ù', 'ü', 'û', 'Ú', 'Ù', 'Û', 'Ü'),
            array('u', 'u', 'u', 'u', 'U', 'U', 'U', 'U'),
            $string
        );

        $string = str_replace(
            array('ñ', 'Ñ', 'ç', 'Ç'),
            array('n', 'N', 'c', 'C',),
            $string
        );

        $string = str_replace(
            array(
                "\\", "¨", "º", "-", "~",
                "#", "@", "|", "!", "\"",
                "·", "$", "%", "&", "/",
                "(", ")", "?", "'", "¡",
                "¿", "[", "^", "<code>", "]",
                "+", "}", "{", "¨", "´",
                ">", "< ", ";", ",", ":",
                ".", " "
            ),
            ' ',
            $string
        );
        return $string;
    }


    /**
     * @Route("/creacion-usuario/formulario", name="formularioCliente")
     */
    public function formularioCliente(Request $request): Response
    {
        $exito = false;
        $msj = "";
        $em = $this->getDoctrine()->getManager();
        $formularioGuardado = false;
        $user = $this->getUser();
        $Cliente = new Cliente();
        $clienteRecienAgregado = null;
        $clienteId = null;
        $form = $this->createForm(AgregarBeneficiarioType::class, $Cliente);

        $form->handleRequest($request);

        try {
            if ($form->isSubmitted() && $form->isValid()) {
                $Cliente = $form->getData();
                $telefono = $Cliente->getTelefono();

                $ClientEnterprise = $em->getRepository(Empresacliente::class)->findOneBy(array('idempresacliente' => 1));
                $Cliente->setEmpresaclienteIdempresacliente($ClientEnterprise);

                $em->persist($Cliente);
                $em->flush();
                $clienteId = $Cliente->getIdcliente();
                // Obtener los datos del cliente recién agregado
                $clienteRecienAgregado = $em->getRepository(Cliente::class)->find($clienteId);
                // Verificar si se encontró el cliente
                if (!$clienteRecienAgregado) {
                    throw new \Exception('No se encontró el cliente recién agregado en la base de datos');
                }

                // Obtener los datos del cliente
                $apellidopaterno = $clienteRecienAgregado->getApellidopaterno();
                $apellidomaterno = $clienteRecienAgregado->getApellidomaterno();
                $nombre = $clienteRecienAgregado->getNombre();
                $email = $clienteRecienAgregado->getEmail();
                $telefono = $clienteRecienAgregado->getTelefono();
                $numeroEmpleado = $clienteRecienAgregado->getNumeroempleado();

                $datosCliente = [
                    'apellidopaterno' => $apellidopaterno,
                    'apellidomaterno' => $apellidomaterno,
                    'nombre' => $nombre,
                    'email' => $email,
                    'telefono' => $telefono,
                    'numeroEmpleado' => $numeroEmpleado
                ];

                // Pasar los datos del cliente al modal

            }
        } catch (\Exception $e) {
            $msj = $e->getMessage();
        }

        return $this->render('dashboard_flujo_expediente/formulario1.html.twig', [
            'form' => $form->createView(),
            'cliente' => $clienteRecienAgregado,
            'exito' => $exito,
            'msj' => $msj,
            'formularioGuardado' => $formularioGuardado,
            'idcliente' => $clienteId,
            //'datosCliente' => $datosCliente
        ]);
    }

    /**
     * @Route("/flow-table", name="dashboardFlujoExpediente-flow-table")
     */
    public function flowTable(Request $request): Response
    {

        $em = $this->getDoctrine()->getManager();

        $userId = $request->get("userId");

        $stages = ['Registro datos Personales', 'Examen Visual', 'Orden de laboratorio', 'Venta', 'Firma', 'Lentes en producción', 'Entrega de lentes pendiente', 'Finalizado'];

        $User = $em->getRepository(Usuario::class)->findOneBy(['idusuario' => $userId]);

        if (!$User) {
            $User = $this->getUser();
        }


        $query = $em->createQuery(
            'SELECT c.nombre, c.apellidopaterno, c.apellidomaterno, f.idflujoexpediente, f.status, f.etapa, 
            u.nombre as nombreUsuario, u.apellidopaterno as apellidoUsuario, uni.nombre as unidad, f.actualizacion
            FROM App\Entity\Flujoexpediente f
            JOIN f.clienteIdcliente c
            JOIN f.usuarioIdusuario u
            LEFT JOIN c.unidadIdunidad uni
            WHERE f.status = :status AND u.idusuario = :idusuario
            ORDER BY f.idflujoexpediente DESC'
        )->setParameters(['status' => "1", 'idusuario' => $User->getIdusuario()]);


        $activeFlows = $query->getResult();


        return $this->render('dashboard_flujo_expediente/dashboardFlujoExpediente-table.html.twig', [
            'activeFlows' => $activeFlows,
            'stages' => $stages,
        ]);
    }

    /**
     * @Route("/erase-flow", name="dashboardFlujoExpediente-erase-flow")
     */
    public function eraseFlow(Request $request): Response
    {

        $em = $this->getDoctrine()->getManager();

        $msg = "";
        $success = false;

        $idclinicalrecordflow = $request->get("idclinicalrecordflow");

        try {

            $Clinicalrecordflow = $em->getRepository(Flujoexpediente::class)->findOneBy(['idflujoexpediente' => $idclinicalrecordflow]);

            if (!$Clinicalrecordflow) throw new \Exception('No se encontró un flujo');

            $Clinicalrecordflow->setStatus('0');

            if ($Clinicalrecordflow->getGraduacionIdgraduacion()) {

                $Graduation = $Clinicalrecordflow->getGraduacionIdgraduacion();
                $Graduation->setStatus('0');
                $em->persist($Graduation);
            }

            $em->persist($Clinicalrecordflow);

            $query = $em->createQuery(
                'SELECT ol
                FROM App\Entity\Ordenlaboratorio ol
                JOIN ol.flujoexpedienteIdflujoexpediente f
                WHERE ol.status = :status AND f.idflujoexpediente =:idflujoexpediente
                '
            )->setParameters(['status' => "1", 'idflujoexpediente' => $Clinicalrecordflow->getIdflujoexpediente()]);

            $laboratoryOrders = $query->getResult();


            foreach ($laboratoryOrders as $LaboratoryOrder) {

                $LaboratoryOrder->setStatus('0');
                $em->persist($LaboratoryOrder);

                $query = $em->createQuery(
                    'SELECT svol   
                    FROM App\Entity\Stockventaordenlaboratorio svol
                    inner join svol.stockventaIdstockventa sv
                    inner join svol.ordenlaboratorioIdordenlaboratorio ol
                    inner join sv.stockIdstock s
                    inner join s.productoIdproducto p
                    
                    where ol.idordenlaboratorio =:idordenlaboratorio'

                )->setParameters(['idordenlaboratorio' => $LaboratoryOrder->getIdordenlaboratorio()]);
                $stockVentaOrdenLaboratorio = $query->getResult();


                for ($i = 0; $i < count($stockVentaOrdenLaboratorio); $i++) {

                    $em->remove($stockVentaOrdenLaboratorio[$i]);
                }
            }

            $query = $em->createQuery(
                'SELECT dev
                FROM App\Entity\Documentosexamenvisual dev
                JOIN dev.flujoexpedienteIdflujoexpediente f
                WHERE dev.status = :status AND f.idflujoexpediente =:idflujoexpediente
                '
            )->setParameters(['status' => "1", 'idflujoexpediente' => $Clinicalrecordflow->getIdflujoexpediente()]);

            $documents = $query->getResult();

            foreach ($documents as $Document) {
                $Document->setStatus('0');
                $em->persist($Document);
            }

            $em->flush();
            $success = true;
        } catch (\Exception $e) {
            $msg = $e->getMessage();
        }

        return new JsonResponse([
            'success' => $success,
            'msg' => $msg,
        ]);
    }

    /**
     * @Route("/get-micas/{index}", name="getMicas")
     */
    public function getMicas(Request $request, EntityManagerInterface $entityManager, SerializerInterface $serializer, string $index = null): JsonResponse
    {
        $exito = false;
        $msj = "";
        $em = $this->getDoctrine()->getManager();
        $formularioGuardado = false;
        $user = $this->getUser();
        $stocks = null;

        try {

            $data = json_decode($request->getContent(), true);

            $queryBuilder = $entityManager->createQueryBuilder();
            $queryBuilder
                ->select('DISTINCT s.idstock as id, p.modelo, p.nombre')
                ->from('App\Entity\Stock', 's')
                ->join('s.productoIdproducto', 'p')
                ->join('p.marcaIdmarca', 'm')
                ->join('p.categoriaIdcategoria', 'cat')
                ->join('cat.claseIdclase', 'clas')
                ->leftjoin('p.framematerialIdframematerial', 'mat')
                ->Where('s.status = 1')
                ->andWhere('p.tipoproducto= 1')
                ->andWhere('cat.status = 1')
                ->andWhere('s.cantidad > 0')
                ->setMaxResults(10)
                ->setFirstResult($index ? 10 * $index : 0)
                ->orderBy('s.idstock', 'ASC');

            if (isset($data['selectedMaterials']) && sizeof($data['selectedMaterials']) > 0) {
                $conditions = array_map(function ($param) {
                    return "mat.idframematerial = $param";
                }, $data['selectedMaterials']);

                $conditionString = implode(' OR ', $conditions);
                $conditionString = "($conditionString)";
                $queryBuilder->andWhere($conditionString);
            }
            if (isset($data['selectedBrands']) && sizeof($data['selectedBrands']) > 0) {
                $conditions = array_map(function ($param) {
                    return "m.idmarca = $param";
                }, $data['selectedBrands']);

                $conditionString = implode(' OR ', $conditions);
                $conditionString = "($conditionString)";
                $queryBuilder->andWhere($conditionString);
            }
            if (isset($data['selectedSubCat']) && sizeof($data['selectedSubCat']) > 0) {
                $conditions = array_map(function ($param) {
                    return "cat.idcategoria = $param";
                }, $data['selectedSubCat']);

                $conditionString = implode(' OR ', $conditions);
                $conditionString = "($conditionString)";
                $queryBuilder->andWhere($conditionString);
            }
            if (isset($data['isDESC']) && $data['isDESC'] !== "Empty") {
                $queryBuilder->orderBy('p.precio', $data['isDESC']);
            }

            $stocks = $queryBuilder->getQuery()->getResult();
        } catch (\Exception $e) {
            $msj = $e->getMessage();
        }

        return new JsonResponse([
            'exito' => $exito,
            'msj' => $msj,
            'stocks' => $stocks
        ], 200, []);
    }

    /**
     * @Route("/new-prod-to-laborder", name="addProductToLabOrder")
     */
    public function addProductToLabOrder(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $exito = false;
        $msj = "";
        $user = $this->getUser();

        try {
            $receivedData = json_decode($request->getContent(), true);

            $orden = $entityManager->getRepository(Ordenlaboratorio::class)->findOneBy(['idordenlaboratorio' => $receivedData['idordenlaboratorio'] ?? -1]);
            $stock = $entityManager->getRepository(Stock::class)->findOneBy(['idstock' => $receivedData['idstock'] ?? -1]);
            $venta = $entityManager->getRepository(Venta::class)->findOneBy(['idventa' => $receivedData['idventa'] ?? -1]);

            if (!$orden || !$stock || !$venta) throw new \Exception('Información invalida');

            $now = new DateTime();

            $stockVenta = new Stockventa();
            $stockVenta
                ->setCantidad(1)
                ->setPrecio($stock->getProductoIdproducto()->getPrecio())
                ->setCosto($stock->getProductoIdproducto()->getCosto())
                ->setCreacion($now)
                ->setModificacion($now)
                ->setPreciofinal($stock->getProductoIdproducto()->getPrecio())
                ->setPreciobase($stock->getProductoIdproducto()->getPrecio())
                ->setProductoIdproducto($stock->getProductoIdproducto())
                ->setStockIdstock($stock)
                ->setUsuarioresponsablecancelacion($user)
                ->setVentaIdventa($venta);

            $entityManager->persist($stockVenta);
            $entityManager->flush();

            $orderStock = new Stockventaordenlaboratorio();
            $orderStock
                ->setCreacion($now)
                ->setAlreadyset("0")
                ->setOrdenlaboratorioIdordenlaboratorio($orden)
                ->setStockventaIdstockventa($stockVenta);

            $entityManager->persist($orderStock);
            $entityManager->flush();
        } catch (\Exception $e) {
            $msj = $e->getMessage();
        }

        return new JsonResponse([
            'exito' => $exito,
            'msj' => $msj,
        ], 200, [], true);
    }

    /**
     * @Route("/detalle-flujo-expediente", name="detalleFlujoExp", methods={"POST"})
     */
    public function detalleFlujo(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $idordenlaboratorio = $request->request->get('idordenlaboratorio');

        // Consulta para obtener detalles específicos por orden
        $query = $em->createQuery(
            'SELECT ol.idordenlaboratorio, ol.actualizacion, ol.creacion, ol.esferaod, ol.esferaoi, ol.cilindrood,
         ol.cilindrooi, ol.ejeod, ol.ejeoi, ol.addordenlaboratorio, ol.base, v.folio,
         CONCAT(c.nombre, \' \', c.apellidopaterno, \' \', c.apellidomaterno) as fullName
         FROM App\Entity\Ordenlaboratorio ol
         INNER JOIN ol.flujoexpedienteIdflujoexpediente fe
         LEFT JOIN fe.ventaIdventa v
         INNER JOIN fe.clienteIdcliente c
         WHERE ol.idordenlaboratorio = :idordenlaboratorio'
        )->setParameter('idordenlaboratorio', $idordenlaboratorio);

        $laboratoryOrder = $query->getOneOrNullResult();

        if (!$laboratoryOrder) {
            return new Response('Orden no encontrada', 404);
        }

        return $this->render('dashboard_flujo_expediente/modalDetalleFlujo.html.twig', [
            'order' => $laboratoryOrder,
        ]);
    }


}
