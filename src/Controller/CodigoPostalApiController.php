<?php

namespace App\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Annotation\Route;

class CodigoPostalApiController extends AbstractController
{
    /**
     * @Route("/subir-codigos-postales", name="web_subir_cp", methods={"GET", "POST"})
     */
    public function subirCodigosPostales(Request $request): Response
    {
        $registros = [];

        if ($request->isMethod('POST')) {
            $archivo = $request->files->get('archivo');

            if ($archivo && $archivo->getClientOriginalExtension() === 'txt') {
                // Leemos y convertimos todo a UTF-8 UNA SOLA VEZ
                $contenido = file_get_contents($archivo->getPathname());
                $encoding = mb_detect_encoding($contenido, ['UTF-8', 'ISO-8859-1', 'Windows-1252'], true);
                if ($encoding && $encoding !== 'UTF-8') {
                    $contenido = mb_convert_encoding($contenido, 'UTF-8', $encoding);
                }
                

                $lineas = explode("\n", $contenido);

                foreach (array_slice($lineas, 1) as $linea) {
                    $partes = explode('|', trim($linea));

                    if (count($partes) < 6) continue;

                    $cp = trim($partes[0]);
                    $asentamiento = trim($partes[1]);
                    $tipoAsentamiento = trim($partes[2]);
                    $municipio = trim($partes[3]);
                    $estado = trim($partes[4]);
                    $ciudad = trim($partes[5]) ?: $municipio;

                    if (!isset($registros[$cp])) {
                        $registros[$cp] = [
                            'estado' => $estado,
                            'municipio' => $municipio,
                            'ciudad' => $ciudad,
                            'colonias' => [],
                        ];
                    }

                    $registros[$cp]['colonias'][] = [
                        'nombre' => $asentamiento,
                        'tipo' => $tipoAsentamiento,
                    ];
                }

                // Exportamos a archivo PHP
                $export = "<?php\n\nreturn [\n";

                foreach ($registros as $cp => $info) {
                    $export .= "    '$cp' => [\n";
                    $export .= "        'estado' => '" . addslashes($info['estado']) . "',\n";
                    $export .= "        'municipio' => '" . addslashes($info['municipio']) . "',\n";
                    $export .= "        'ciudad' => '" . addslashes($info['ciudad']) . "',\n";
                    $export .= "        'colonias' => [\n";
                    foreach ($info['colonias'] as $colonia) {
                        $export .= "            ['nombre' => '" . addslashes($colonia['nombre']) . "', 'tipo' => '" . addslashes($colonia['tipo']) . "'],\n";
                    }
                    $export .= "        ],\n";
                    $export .= "    ],\n";
                }

                $export .= "];\n";

                $archivoPhp = $this->getParameter('kernel.project_dir') . '/config/data/codigos_postales_array.php';
                file_put_contents($archivoPhp, $export);
            }
        }

        return $this->render('codigo_postal/upload.html.twig', [
            'registros' => $registros,
        ]);
    }

    /**
     * @Route("/descargar-codigos-postales", name="descargar_cp_php", methods={"GET"})
     */
    public function descargarPhp(): Response
    {
        $archivo = $this->getParameter('kernel.project_dir') . '/config/data/codigos_postales_array.php';

        if (!file_exists($archivo)) {
            return new Response("Archivo no encontrado", 404);
        }

        return $this->file($archivo, 'codigos_postales_array.php');
    }

    /**
     * @Route("/api/codigos-postales", name="api_codigos_postales", methods={"GET"})
     */
    public function buscarPorCodigoPostal(Request $request): Response
    {
        $cp = $request->query->get('cp');

        if (!$cp) {
            return new Response(json_encode(['error' => 'Código postal requerido']), 400, [
                'Content-Type' => 'application/json'
            ]);
        }

        $archivo = $this->getParameter('kernel.project_dir') . '/config/data/codigos_postales_array.php';

        if (!file_exists($archivo)) {
            return new Response(json_encode(['error' => 'Base de datos no cargada']), 500, [
                'Content-Type' => 'application/json'
            ]);
        }

        $data = include $archivo;

        if (!isset($data[$cp])) {
            return new Response(json_encode(['message' => 'No se encontraron resultados']), 404, [
                'Content-Type' => 'application/json'
            ]);
        }

        return new Response(json_encode($data[$cp]), 200, [
            'Content-Type' => 'application/json'
        ]);
    }
}
