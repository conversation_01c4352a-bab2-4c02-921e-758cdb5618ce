<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use App\Entity\Pago;
use App\Entity\Venta;
use App\Entity\Paymenttype;
use App\Service\PagoEraser;

use Symfony\Component\Mime\Email;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\Mime\Address;

use DateTime;


class CobranzaController extends AbstractController
{
    private $mailer;

    public function __construct(MailerInterface $mailer)
    {
        $this->mailer = $mailer;
    }

    /**
     * @Route("/cobranza", name="app_cobranza")
     */
    public function index(): Response
    {
        return $this->render('cobranza/cobranzaIndex.html.twig', [
            'controller_name' => 'MermaController',
        ]);
    }

    /**
     * @Route("/tabla-cobranza", name="tabla-cobranza")
     */
    public function obtenerTablaCobranza(Request $request): Response
    {
    
        $exito=false;
        $msj="";
        $globalSeDebe=0;

        $em = $this->getDoctrine()->getManager();

        $ventasNoLiquidadas = [];

        $query = $em->createQuery(
            'SELECT  v.idventa, c.nombre, c.apellidopaterno, c.apellidomaterno, c.email, c.telefono, c.idcliente,v.fechaventa,v.total,v.pagado
            FROM App\Entity\Venta v
            INNER JOIN v.clienteIdcliente c
            WHERE v.status = :status AND v.cotizacion = 0 AND v.credito = 1 AND v.liquidada = 0
            GROUP BY c.idcliente,v.idventa
            ORDER BY v.fechaventa ASC'
        )->setParameters(['status' => '1']);

        $ventasNoLiquidadasAux= $query->getResult();

        //sacamos los demás datos de cada uausarios
        //Número de ventas
        //monto que debe
        //monto fecha de ultimo pago

        $arrayPagado = [];

        foreach ($ventasNoLiquidadasAux as $keyVentas=>$venta){
            $globalSeDebe+=$venta['pagado'];

            $query = $em->createQuery(

                'SELECT p
                FROM App\Entity\Pago p
                
                inner join  p.ventaIdventa v
                
                where p.status=:status and v.idventa=:idventa
                order by p.idpago asc
                '

            )->setParameters(['status'=>'1','idventa'=>$venta['idventa']]);

            $pagos = $query->getResult();

            $totalPagado=0;
            
            $venta['fechaUltimoPago']=null;

            foreach ($pagos as $key =>$Pago){
                $totalPagado+=$Pago->getMonto();
                $venta['fechaUltimoPago']=$Pago->getFecha();
            }
            

            if (!isset($arrayPagado[$venta['idcliente']])) {
                $arrayPagado[$venta['idcliente']] = floatval($venta['pagado']);

            } else $arrayPagado[$venta['idcliente']] = floatval($arrayPagado[$venta['idcliente']] + $venta['pagado']);

            if (isset($arrayPagado[$ventasNoLiquidadasAux[$keyVentas]['idcliente']])){
                $arrayPagado[$ventasNoLiquidadasAux[$keyVentas]['idcliente']] = $arrayPagado[$ventasNoLiquidadasAux[$keyVentas]['idcliente']] - $totalPagado;
            }

            $globalSeDebe -= $totalPagado;

            $ventasNoLiquidadasAux[$keyVentas]['totalRestante']=$venta['total']-$totalPagado;

            if (isset($ventasNoLiquidadas[$venta['idcliente']])){

                if ($ventasNoLiquidadas[$venta['idcliente']]['fechaUltimoPago'] > $venta['fechaUltimoPago'] and $venta['fechaUltimoPago'] != null){
                    $fechapago = $ventasNoLiquidadas[$venta['idcliente']]['fechaUltimoPago'];
                    $ventasNoLiquidadas[$venta['idcliente']] = $venta;
                    $ventasNoLiquidadas[$venta['idcliente']]['fechaUltimoPago'] = $fechapago;

                }else $ventasNoLiquidadas[$venta['idcliente']]=$venta;
                
            }else $ventasNoLiquidadas[$venta['idcliente']]=$venta;

            //$ventasNoLiquidadas[$venta['idcliente']]=$venta;

        }


        return $this->render('cobranza/cobranzaTable.html.twig', [
            'exito'=>$exito,
            'msj'=>$msj,
            'ventasNoLiquidadas'=>$ventasNoLiquidadas,
            'globalSeDebe'=>$globalSeDebe,
            'arrayPagado'=>$arrayPagado,
        ]);
    }

    /**
     * @Route("/cobranza-tabla-ventas", name="cobranza-tabla-ventas")
     */
    public function obtenerTablaVentas(Request $request): Response
    {
    
        $exito=false;
        $msj="";

        $em = $this->getDoctrine()->getManager();

        $idcliente = $request->get("idcliente");

        $query = $em->createQuery(
            'SELECT  v
            FROM App\Entity\Venta v
            INNER JOIN v.clienteIdcliente c
            WHERE v.status = :status AND v.cotizacion = 0 AND v.credito = 1 AND c.idcliente =:idcliente AND v.liquidada = 0
            ORDER BY v.idventa ASC'
        )->setParameters(['status' => '1', 'idcliente'=>$idcliente]);

        $ventasClienteNoLiquidadas= $query->getResult();

        $deudas = array();
        $datosextra = [];
        $beneficiaries = [];
        $currentDate = new DateTime();

        foreach ($ventasClienteNoLiquidadas as $key =>$Venta){

            $interval = $currentDate->diff($Venta->getFechaventa());

            $datosextra['diasActiva'][$Venta->getIdventa()] = $interval->days;

            $query = $em->createQuery(
    
                'SELECT p
                FROM App\Entity\Pago p
                inner join  p.ventaIdventa v
                where p.status=:status and v.idventa=:idventa
                order by p.idpago asc'
                    
            )->setParameters(['status'=>'1','idventa'=>$Venta->getIdventa()]);
    
            $pagos = $query->getResult();
    
            $totalPagado=0;
            $calculoDiasCredito=0;
            
            $datosextra['ultimaFechaPago'][$Venta->getIdventa()] = null;

            
            foreach ($pagos as $key =>$Pago){
                $totalPagado+=$Pago->getMonto();
                $datosextra['ultimaFechaPago'][$Venta->getIdventa()] = $Pago->getFecha();   
            }

            $deudas[$Venta->getIdventa()] = $totalPagado;

            $query = $em->createQuery(
    
                'SELECT CONCAT(c.nombre, \' \', c.apellidopaterno, \' \', c.apellidomaterno) as fullName
                FROM App\Entity\Beneficiarioventa bv
                inner join  bv.ventaIdventa v
                inner join  bv.clienteIdcliente c
                where v.idventa=:idventa
                order by c.nombre asc'
                    
            )->setParameters(['idventa'=>$Venta->getIdventa()]);
    
            $beneficiariesPerSale = $query->getResult();

            $beneficiaries[$Venta->getIdventa()] = $beneficiariesPerSale;
    
        }

        return $this->render('cobranza/cobranzaVentaTable.html.twig', [
            'exito'=>$exito,
            'msj'=>$msj,
            'ventasClienteNoLiquidadas'=>$ventasClienteNoLiquidadas,
            'deudas'=>$deudas,
            'idcliente'=>$idcliente,
            'datosextra'=>$datosextra,
            'beneficiaries'=>$beneficiaries,
        ]);
    }

    /**
     * @Route("/cobranza-tabla-total-ventas", name="cobranza-tabla-total-ventas")
     */
    public function obtenerTablaTotalVentas(Request $request): Response
    {
    
        $exito=false;
        $msj="";

        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT  v
            FROM App\Entity\Venta v
            INNER JOIN v.clienteIdcliente c
            WHERE v.status = :status AND v.cotizacion = 0 AND v.credito = 1 AND v.liquidada = 0
            ORDER BY v.idventa ASC'
        )->setParameters(['status' => '1']);

        $ventasClienteNoLiquidadas= $query->getResult();
 
        $deudas = array();

        $datosextra = [];

        $currentDate = new DateTime();

        $beneficiaries = [];

        foreach ($ventasClienteNoLiquidadas as $key =>$Venta){


            if ($Venta->getFechaventa()) $interval = $currentDate->diff($Venta->getFechaventa());

            else $interval = $currentDate->diff($currentDate);

            

            $datosextra['diasActiva'][$Venta->getIdventa()] = $interval->days;

            $query = $em->createQuery(
    
                'SELECT p
                FROM App\Entity\Pago p
                inner join  p.ventaIdventa v
                where p.status=:status and v.idventa=:idventa
                order by p.idpago asc'
            )->setParameters(['status'=>'1','idventa'=>$Venta->getIdventa()]);
    
            $pagos = $query->getResult();
    
            $totalPagado=0;
            
            $datosextra['ultimaFechaPago'][$Venta->getIdventa()] = null;
            
            foreach ($pagos as $key =>$Pago){
                $totalPagado+=$Pago->getMonto();
                $datosextra['ultimaFechaPago'][$Venta->getIdventa()] = $Pago->getFecha();
            }

            $deudas[$Venta->getIdventa()] = $totalPagado;

            $query = $em->createQuery(
    
                'SELECT CONCAT(c.nombre, \' \', c.apellidopaterno, \' \', c.apellidomaterno) as fullName
                FROM App\Entity\Beneficiarioventa bv
                
                inner join  bv.ventaIdventa v
                inner join  bv.clienteIdcliente c
                
                where v.idventa=:idventa

                order by c.nombre asc
                '
                    
            )->setParameters(['idventa'=>$Venta->getIdventa()]);
    
            $beneficiariesPerSale = $query->getResult();

            $beneficiaries[$Venta->getIdventa()] = $beneficiariesPerSale;
    
        }

       

        // 
        return $this->render('cobranza/cobranzaVentaTotalTable.html.twig', [
            'exito'=>$exito,
            'msj'=>$msj,
            'ventasClienteNoLiquidadas'=>$ventasClienteNoLiquidadas,
            'deudas'=>$deudas,
            'datosextra'=>$datosextra,
            'beneficiaries'=>$beneficiaries,
        ]);
    }

    /**
     * @Route("/cobranza-pago", name="cobranza-pago")
     */
    public function cobranzaPago(Request $request): Response
    {
        $deuda = $request->get("deuda");
        $idventa = $request->get("idventa");
        $idcliente = $request->get("idcliente");

        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT pt.idpaymenttype, pt.name
               FROM App\Entity\Paymenttype pt
               where pt.status >=:status
               ORDER BY pt.name ASC
               '
        )->setParameters(['status' => "1"]);
        $paymentTypes = $query->getResult();

        return $this->render('cobranza/pantallaPago.html.twig', [
            'deuda' => $deuda,
            'idventa' => $idventa,
            'idcliente' => $idcliente,
            'paymentTypes' => $paymentTypes
        ]);
    }

    /**
     * @Route("/cobranza-guardar-pago", name="cobranza-guardar-pago")
     */
    public function guardarPago(Request $request): Response
    {
        $monto = $request->get("monto");
        $idventa = $request->get("idventa");
        $tipoPago = $request->get("tipoPago");
        $meses = $request->get("meses");
        $deuda = $request->get("deuda");

        //date_default_timezone_set("America/Tijuana");

        $msj = "";
        $exito = false;

        $em = $this->getDoctrine()->getManager();

        $Pago = new Pago();

        try{
            $Venta = $em->getRepository(Venta::class)->findOneBy(array('idventa' => $idventa));

            if ($Venta){

                $Paymenttype = $em->getRepository(Paymenttype::class)->findOneBy(array('idpaymenttype' => $tipoPago));

                if ($Paymenttype) $Pago->setPaymenttypeIdpaymenttype($Paymenttype);
                else throw new \Exception('Seleccionar un tipo de pago');

                if (is_numeric($monto) and floatval($monto) > 0 and floatval($monto) <= (floatval($deuda) + 0.5) ){

                    $Pago->setMonto($monto);
                }
                else throw new \Exception('Ingresa un monto válido');


                $Pago->setVentaIdventa($Venta);
                $Pago->setFecha(new \DateTime("now"));
                $Pago->setUsuarioCobro($this->getUser());

                if ($meses) $Pago->setMesesintereses($meses);

                $em->persist($Pago);
                $em->flush();

                $Client = $Venta->getClienteIdcliente();
                if ($Venta->getCredito() == '1' && $Venta->getCotizacion() != '1') {
                    $Client->setDebt($Client->getDebt() - $Pago->getMonto());
                }
                
                $em->persist($Client);
                $em->flush();

                $query = $em->createQuery(
    
                    'SELECT p
                    FROM App\Entity\Pago p
                    
                    inner join  p.ventaIdventa v
                    
                    where p.status=:status and v.idventa=:idventa
                    '
                        
                )->setParameters(['status'=>'1','idventa'=>$Venta->getIdventa()]);
        
                $pagos = $query->getResult();
        
                $totalPagado=0;
                
                foreach ($pagos as $key =>$Pago){
                    $totalPagado+=$Pago->getMonto();
                }

                if ($totalPagado >= $Venta->getPagado()){
                    $Venta->setLiquidada('1');
                    $em->persist($Venta);
                    $em->flush();

                }

                $this->sendPaymentMail($Pago,$Venta,$totalPagado);

                $exito = true;

            }else throw new \Exception('No se encontró la venta');
            

        }catch (\Exception $e) {
            //$msj.=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();
            $msj.=$e->getMessage();
        }

        return $this->json(['exito' => $exito,'msj' => $msj,]);

    }

    /**
     * @Route("/cobranza-tabla-pagos", name="cobranza-tabla-pagos")
     */
    public function tablaPagos(Request $request): Response
    {
        $idventa = $request->get("idventa");

        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
    
            'SELECT p
            FROM App\Entity\Pago p
            
            inner join  p.ventaIdventa v
            
            where p.status=:status and v.idventa=:idventa
            '
                
        )->setParameters(['status'=>'1','idventa'=>$idventa]);

        $pagos = $query->getResult();

        return $this->render('cobranza/tablaPagos.html.twig', [
            'pagos' => $pagos,
            'idventa' => $idventa,
        ]);
    }

    /**
     * @Route("/borrar-pago", name="borrar-pago")
     */
    public function borrarPago(Request $request, PagoEraser $pagoEraser): Response
    {
        $idpago = $request->get("idpago");

        $em = $this->getDoctrine()->getManager();

        $msj = "";
        $exito = false;

        try{

            $Pago = $em->getRepository(Pago::class)->findOneBy(array('idpago' => $idpago));

            if ($Pago){

                $pagoEraser->erasePago($Pago);

            }else throw new \Exception("No se encontró el pago");
            
            $exito = true;

        }catch(\Exception $e){
            $msj.=$e->getMessage();
        }

        return $this->json(['exito' => $exito,'msj' => $msj,]);
    }

    private function sendPaymentMail($Payment, $Sale, $totalPayed)
    {

        $Enterprise = $Sale->getSucursalIdsucursal()->getEmpresaIdempresa();

        $folio = $Sale->getFolio();

        $clientEmail = $Sale->getClienteIdcliente()->getEmail();

        
        if($clientEmail){

            $email = (new TemplatedEmail())
                ->from('<EMAIL>')
                ->to(new Address($clientEmail))
                ->subject("Registro de pago para venta con folio ".$folio)
                ->htmlTemplate('emails/cobranza-sendPaymentEmail.html.twig')
                ->context([
                    'amount' => $Payment->getMonto() ?? 0 ,
                    'paymentType' => $Payment->getTipopago() ?? null ,
                    'date' => $Payment->getFecha() ?? null,
                    'clientName' => $Sale->getClienteIdcliente()->getNombreCompleto() ?? null,
                    'logo' => $Enterprise->getLogoimagen(),
                    'totalPayed' => $totalPayed ?? 0,
                    'total' => $Sale->getPagado() ?? 0,
                ])
            ;

            $this->mailer->send($email);
            
        }

    }

}
