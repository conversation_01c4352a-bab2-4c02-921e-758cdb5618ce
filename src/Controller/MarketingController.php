<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class MarketingController extends AbstractController
{
    /**
     * @Route("/marketing", name="app_marketing")
     */
    public function index(): Response
    {
        return $this->render('marketing/index.html.twig', [
            'controller_name' => 'MarketingController',
        ]);
    }

    /**
     * @Route("/app_marketing_campaign", name="app_marketing_campaign")
     */
    public function campaign(): Response
    {
        return $this->render('marketing/campaign.html.twig', [
            'controller_name' => 'MarketingController',
        ]);
    }

    /**
     * @Route("/app_marketing_userList", name="app_marketing_userList")
     */
    public function userList(): Response
    {
        return $this->render('marketing/userList.html.twig', [
            'controller_name' => 'MarketingController',
        ]);
    }

    /**
     * @Route("/app_marketing_templates", name="app_marketing_templates")
     */
    public function templates(): Response
    {
        return $this->render('marketing/templates.html.twig', [
            'controller_name' => 'MarketingController',
        ]);
    }
}