<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;

class ReporteFacturacionController extends AbstractController
{
    /**
     * @Route("/reporte/facturacion", name="app_reporte_facturacion")
     */
    public function index(): Response
    {
        return $this->render('reporte_facturacion/index.html.twig', [
            'controller_name' => 'ReporteFacturacionController',
        ]);
    }

    /**
     * @Route("/reporte/filtrosFacturacion", name="filtros-facturacion")
     */
    public function filtrosFacturacion(): Response
    {
        $em = $this->getDoctrine()->getManager();
        $success = false;
        $message = "";

        $query = $em->createQuery(
            'SELECT e.nombre AS Empresa, e.idempresa
             FROM App\Entity\Empresa e
             WHERE e.status = :status'
             )->setParameters(['status' => '1']);
             $filtrosEmpresas = $query->getResult();
    
             $query = $em->createQuery(
                'SELECT s.nombre AS Sucursal, s.idsucursal
                 FROM App\Entity\Sucursal s
                 WHERE s.status = :status'
                 )->setParameters(['status' => '1']);
                 $filtroSucursales = $query->getResult();

        return $this->render('reporte_facturacion/filtros_Facturas.html.twig', [
            'filtrosEmpresas' => $filtrosEmpresas,
            'filtroSucursales' => $filtroSucursales,
        ]);
    }

    /**
    * @Route("/reporte/ventasFacturacion", name="ventas-facturacion")
    */

    public function ventasFacturacion(Request $request):Response
    {
        $em = $this->getDoctrine()->getManager();
        $succes="";
        $message="";
        $whereSucursal="";
        $fechaInicio= \DateTime::createFromFormat('d/m/Y',$request->get("fechaInicio"));
        $fechaFin= \DateTime::createFromFormat('d/m/Y',$request->get("fechaFin"));
        $idSucursal=$request->get("idsucursal");
        $idEmpresa=$request->get("idempresa");
        $ventasfacturadasS=$request->get("ventasfacturadasS");
        $parameters=[];
        $ventasFacturadas="";

        try {
            if ($idSucursal !== "todasSucursales") {
                $whereSucursal = 'AND s.idsucursal = :idsucursal';
                $parameters['idsucursal'] = $idSucursal;
            } else {
                $whereSucursal = '';
            }
    
            if(!$fechaInicio || !$fechaFin)
            {
               $fechaInicio = new \DateTime("now");
               $fechaFin = new \DateTime("now");

                $parameters['fechaInicio'] = $fechaInicio->format('Y-m-d')." 00:00:00";
                $parameters['fechaFin'] = $fechaFin->format('Y-m-d')." 23:59:00";
       
            }
            else
            {
                $parameters['fechaInicio'] = $fechaInicio->format('Y-m-d')." 00:00:00";
                $parameters['fechaFin'] = $fechaFin->format('Y-m-d')." 23:59:00";
            }
               $parameters['status']=1;
               $parameters['cotizacion']=0;
               $parameters['liquidada']=1;

               $query = $em->createQuery(
                'SELECT v.idventa, v.fecha, v.total, v.iva, v.descuento, v.pagado, v.status, v.beneficiario, v.folio, v.ticketpdf,
                        v.cotizacion, v.tipopago, v.sedescontodeinventario, v.pidiofactura, v.fechacreacion, v.fechaventa, v.fechaactualizacion,
                        v.fechacancelacion, v.porquesecancelo, v.archivoautorizacion, v.notas, v.liquidada, v.credito, v.diascredito, tv.nombre AS TipoVenta, tv.status AS StatusTipoVenta,
                        u.nombre AS NombreUsuario, c.nombre AS NombreCliente, e.nombre AS NombreEmpresa, s.nombre AS SucursalNombre, v.fechaventa AS FechaVenta, v.fechaactualizacion AS FechaActualizacion,
                        tv.customerbilling AS CustomerBilling
                FROM App\Entity\Venta v
                INNER JOIN v.usuarioIdusuario u
                INNER JOIN v.clienteIdcliente c
                INNER JOIN v.sucursalIdsucursal s
                INNER JOIN s.empresaIdempresa e
                INNER JOIN v.tipoventaIdtipoventa tv
                WHERE v.fecha >= :fechaInicio AND v.status =:status AND v.cotizacion =:cotizacion AND v.liquidada =:liquidada AND
                v.fecha <= :fechaFin ' . $whereSucursal
            )->setParameters($parameters);
            
            $ventasFacturadas = $query->getResult();

        } catch (\Exception $e) {
            $message = $e->getMessage();
            echo $message;
        }

        return $this->render('reporte_facturacion/tableVentaFactura.html.twig', [
            'ventasFacturadas' => $ventasFacturadas,
            'message' => $message,
        ]);

    }

}