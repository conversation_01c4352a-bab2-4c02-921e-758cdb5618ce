<?php

namespace App\Controller;

use App\Entity\Flujoexpediente;
use App\Entity\Ordenlaboratorio;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Dompdf\Dompdf;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\HttpFoundation\JsonResponse;


class PdfGeneratorController extends AbstractController
{

    /**
     * @Route("/pdf/generat/etiqueta", name="app_pdf_generator_etiqueta")
     */
    public function etiqueta(Request $request, RouterInterface $router): Response
    {
        $qrValue = $request->query->get('qr_value');
        $absoluteRoute = $router->generate('app_entry_point', ['value' => $qrValue ?? 0], RouterInterface::ABSOLUTE_URL);
        $qr = QrCode::create($absoluteRoute);
        $writer = new PngWriter();
        $result = $writer->write($qr);

        if (!is_dir("uploads/QRs")) {
            // The directory doesn't exist, so create it with the necessary permissions (e.g., 0755)
            if (!mkdir("uploads/QRs", 0777, true)) {
                // Failed to create the directory, handle the error as needed
                echo "Failed to create the directory: QRs";
            }
        }
        $result->saveToFile("uploads/QRs/qr.png");

        $em = $this->getDoctrine()->getManager();
        $LaboratoryOrder = $em->getRepository(Ordenlaboratorio::class)->findOneBy(array('idordenlaboratorio' => $qrValue));
        $flujo = $LaboratoryOrder->getFlujoexpedienteIdflujoexpediente();

        $query = $em->createQuery(
            'SELECT p.modelo, m.nombre as marca, p.color, p.tipomaterial as material
        FROM App\Entity\Stockventaordenlaboratorio svol
        INNER JOIN svol.ordenlaboratorioIdordenlaboratorio ol
        INNER JOIN svol.stockventaIdstockventa sv
        INNER JOIN sv.stockIdstock s
        INNER JOIN s.productoIdproducto p
        INNER JOIN p.marcaIdmarca m 
        WHERE p.status = :status 
        AND p.ordenlaboratorio = :ordlab 
        AND ol.idordenlaboratorio = :ordlab
        AND (svol.mainproduct = 1 OR svol.mainproduct = 3)'
        )->setParameters([
            "status" => '1',
            "ordlab" => $LaboratoryOrder->getIdordenlaboratorio()
        ]);

        $product = $query->getResult();

        $query = $em->createQuery(
            'SELECT v.folio
        FROM App\Entity\Flujoexpedienteventa fv
        INNER JOIN fv.flujoexpedienteIdflujoexpediente fe
        INNER JOIN fv.ventaIdventa v
        where v.status =:status and fe.idflujoexpediente =:idFlow
        '
        )->setParameters(['status' => "1", 'idFlow' => $flujo->getIdflujoexpediente()]);
        $folios = $query->getResult();

        $result->saveToFile("uploads/QRs/qr.png");

        $data = [
            'logo'   => $this->imageToBase64($this->getParameter('kernel.project_dir') . '/public/img/OptimoLogoAzul.png'),
            'qrCodeSrc'   => $this->imageToBase64($this->getParameter('kernel.project_dir') . '/public/QRs/qr.png'),
            'ol' => $LaboratoryOrder,
            'folios' => $folios,
            'p1' => $product[0] ?? null,
            'p2' => $product[1] ?? null,
        ];
        $html =  $this->renderView('pdf_generator/index.html.twig', $data);
        $dompdf = new Dompdf();
        $dompdf->loadHtml($html);
        $dompdf->render();

        $pdfOutput = $dompdf->output();

        return new Response(
            $pdfOutput,
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'attachment; filename="etiqueta.pdf"'
            ]
        );
    }

    private function imageToBase64($path)
    {
        $type = pathinfo($path, PATHINFO_EXTENSION);
        $data = file_get_contents($path);
        $base64 = 'data:image/' . $type . ';base64,' . base64_encode($data);
        return $base64;
    }

    /**
     * @Route("/entry-point/{value}", name="app_entry_point")
     */
    public function entryPoint(string $value = null): Response
    {

        $em = $this->getDoctrine()->getManager();
        $LaboratoryOrder = $em->getRepository(Ordenlaboratorio::class)->findOneBy(array('idordenlaboratorio' => $value));
        $flujo = $LaboratoryOrder->getFlujoexpedienteIdflujoexpediente();

        $query = $em->createQuery(
            'SELECT p.modelo, m.nombre as marca, p.codigocolor, p.tipomaterial as material, p.tipoproducto 
             FROM App\Entity\Stockventaordenlaboratorio svol
             INNER JOIN svol.ordenlaboratorioIdordenlaboratorio ol
             INNER JOIN svol.stockventaIdstockventa sv
             INNER JOIN sv.stockIdstock s
             INNER JOIN s.productoIdproducto p
             LEFT JOIN App\Entity\Marca m WITH p.marcaIdmarca = m.idmarca
             WHERE p.status = :status
             AND ol.idordenlaboratorio = :ordlab '
        )->setParameters([
            "status" => '1',
            "ordlab" => $LaboratoryOrder->getIdordenlaboratorio()
        ]);

        $product = $query->getResult();

        $query = $em->createQuery(
            'SELECT v.folio
            FROM App\Entity\Flujoexpedienteventa fv
            INNER JOIN fv.flujoexpedienteIdflujoexpediente fe
            INNER JOIN fv.ventaIdventa v
            where v.status =:status and fe.idflujoexpediente =:idFlow
            '
        )->setParameters(['status' => "1", 'idFlow' => $flujo->getIdflujoexpediente()]);
        $folios = $query->getResult();

        // Render a Twig template
        return $this->render('pdf_generator/index.html.twig', [
            'logo' => $this->imageToBase64($this->getParameter('kernel.project_dir') . '/public/img/OptimoLogoAzul.png'),
            'qrCodeSrc' => $this->imageToBase64($this->getParameter('kernel.project_dir') . '/public/QRs/qr.png'),
            'ol' => $LaboratoryOrder,
            'folios' => $folios,
            'p1' => $product[0]??null,
            'p2' => $product[1]??null,
        ]);
    }
}
