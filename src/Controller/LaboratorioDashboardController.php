<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use App\Entity\Shipment;
use App\Entity\Shipmentordenlaboratorio;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Entity\Stockventaordenlaboratorio;
use App\Entity\Ordenlaboratorio;
use App\Entity\Event;
use App\Entity\Eventtype;
use DateTime;

/**
 * @Route("/laboratorio")
 */
class LaboratorioDashboardController extends AbstractController
{

    /**
     * @Route("/dashboard", name="app_lab_dashboard")
     */
    public function index(): Response
    {
        return $this->render('laboratorio_dashboard/index.html.twig', [
            'controller_name' => 'LabDashBoardController',
        ]);
    }

    /**
     * @Route("/dashboard/design", name="app_laboratorio_dashboardD")
     */
    public function dashboard(): Response
    {

        $em = $this->getDoctrine()->getManager();

        $laboratoryOrders = null;

        $limits = ['max' => 10, 'min' => 10];

        $user = $this->getUser()->getRol();

        if ($this->isGranted('ROLE_LAB') && $this->isGranted('ROLE_CALIDAD')) {
            $limits = ['max' => 10, 'min' => 5];
        } else if ($this->isGranted('ROLE_LAB')) {
            $limits = ['max' => 8, 'min' => 5];
        } else if ($this->isGranted('ROLE_CALIDAD')) {
            $limits = ['max' => 9, 'min' => 9];
        }

        $query = $em->createQuery(
            'SELECT ol.idordenlaboratorio, ol.actualizacion, ol.creacion, ol.esferaod, ol.esferaoi, ol.cilindrood,
            ol.cilindrooi, ol.etapa as stage, ol.ejeod, ol.ejeoi, ol.addordenlaboratorio, ol.base, v.folio,
            CONCAT(c.nombre, \' \', c.apellidopaterno, \' \', c.apellidomaterno) as fullName, fe.idflujoexpediente
            FROM App\Entity\Ordenlaboratorio ol
            INNER JOIN ol.flujoexpedienteIdflujoexpediente fe
            LEFT JOIN fe.ventaIdventa v
            INNER JOIN fe.sucursalIdsucursal suc
            INNER JOIN fe.clienteIdcliente c
            WHERE ol.status =:status AND ol.etapa BETWEEN :limitsMin AND :limitsMax AND fe.status =:status AND fe.etapa >= :flowStage
            '
        )->setParameters(["status" => '1', "flowStage" => 6, 'limitsMin' => $limits['min'], 'limitsMax' => $limits['max']]);
        $laboratoryOrders = $query->getResult();

        $whereLaboratoryOrder = "";

        if (isset($laboratoryOrders[0])) {
            $whereLaboratoryOrder .= "AND (";
            for ($i = 0; $i < count($laboratoryOrders); $i++) {
                if ($i == count($laboratoryOrders) - 1) $whereLaboratoryOrder .= "ol.idordenlaboratorio = " . $laboratoryOrders[$i]["idordenlaboratorio"];
                else $whereLaboratoryOrder .= "ol.idordenlaboratorio = " . $laboratoryOrders[$i]["idordenlaboratorio"] . " OR ";
            }
            $whereLaboratoryOrder .= ") ";
        }

        $whereFlow = "";

        if (isset($laboratoryOrders[0])) {
            $whereFlow .= "AND (";
            for ($i = 0; $i < count($laboratoryOrders); $i++) {
                if ($i == count($laboratoryOrders) - 1) $whereFlow .= "fe.idflujoexpediente = " . $laboratoryOrders[$i]["idflujoexpediente"];
                else $whereFlow .= "fe.idflujoexpediente = " . $laboratoryOrders[$i]["idflujoexpediente"] . " OR ";
            }
            $whereFlow .= ") ";
        }

        $query = $em->createQuery(
            'SELECT v.folio, fe.idflujoexpediente
            FROM App\Entity\Flujoexpedienteventa fv
            INNER JOIN fv.flujoexpedienteIdflujoexpediente fe
            INNER JOIN fv.ventaIdventa v
            where v.status =:status ' . $whereFlow
        )->setParameters(['status' => "1"]);
        $sales = $query->getResult();

        $mappedSales = [];

        foreach ($sales as $Sale) {
            if (isset($mappedSales[$Sale['idflujoexpediente']])) array_push($mappedSales[$Sale['idflujoexpediente']], $Sale['folio']);
            else $mappedSales[$Sale['idflujoexpediente']] = [$Sale['folio']];
        }

        $query = $em->createQuery(
            'SELECT p.modelo, ol.idordenlaboratorio, m.nombre as marca, p.descripcion, svol.mainproduct as errase, svol.idstockventaordenlaboratorio as id, svol.revision as revision
                FROM App\Entity\Stockventaordenlaboratorio svol
                INNER JOIN svol.stockventaIdstockventa sv
                INNER JOIN sv.stockIdstock s
                INNER JOIN s.productoIdproducto p
                INNER JOIN p.marcaIdmarca m 
                INNER JOIN svol.ordenlaboratorioIdordenlaboratorio ol
                WHERE p.status =:status ' . $whereLaboratoryOrder
        )->setParameters(["status" => '1']);
        $productsOrder = $query->getResult();

        $mappedProducts = [];

        foreach ($productsOrder as $productOrder) {
            if (!isset($mappedProducts[$productOrder['idordenlaboratorio']])) $mappedProducts[$productOrder['idordenlaboratorio']] = [$productOrder];
            else array_push($mappedProducts[$productOrder['idordenlaboratorio']], $productOrder);
        }


        $ordenLaboratorioIds = array_map(function ($ordenLaboratorio) {
            return $ordenLaboratorio['idordenlaboratorio'];
        }, $laboratoryOrders);

        $eventData = [];
        $now = new DateTime();
        foreach ($ordenLaboratorioIds as $orderId) {

            $query = $em->createQuery(
                "SELECT 
                    (SUM(CASE WHEN et.name IN ('start-Lab') THEN ev.creationdate ELSE 0 END) - 
                    SUM(CASE WHEN et.name IN ('pause-Lab') THEN ev.creationdate ELSE 0 END)) AS Total,
                    MAX(ev.creationdate) AS lastdate
                FROM App\Entity\Event ev
                JOIN ev.eventtypeIdeventtype et
                JOIN ev.ordenlaboratorioIdordenlaboratorio ol
                WHERE ol.idordenlaboratorio =:ordlab
                AND et.name IN ('start-Lab', 'pause-Lab') "
            )->setParameters(["ordlab" => $orderId]);
            $eventQuery = $query->getSingleResult();

            $query2 = $em->createQuery(
                "SELECT 
                    (SUM(CASE WHEN et.name IN ('start-Lab') THEN ev.creationdate ELSE 0 END) - 
                    SUM(CASE WHEN et.name IN ('pause-Lab') THEN ev.creationdate ELSE 0 END)) AS AlterTotal
                FROM App\Entity\Event ev
                JOIN ev.eventtypeIdeventtype et
                JOIN ev.ordenlaboratorioIdordenlaboratorio ol
                WHERE ol.idordenlaboratorio =:ordlab
                AND et.name IN ('start-Lab', 'pause-Lab') 
                AND ev.creationdate < :maxdate "
            )->setParameters(["ordlab" => $orderId, "maxdate" => $eventQuery['lastdate']]);
            $eventQuery2 = $query2->getSingleResult();

            $lastDate = new DateTime($eventQuery['lastdate']);

            $difference = (int)abs($now->getTimestamp() - $lastDate->getTimestamp());

            $total = $eventQuery["Total"] ?? 0;

            $eventData[$orderId] = (int)($total > 0 ? abs($eventQuery2["AlterTotal"]) : abs($eventQuery["Total"])) + (int)($total > 0 ? $difference : 0);
        }


        $orderStages = [
            'Orden creada',
            'Completar flujo',
            'Sin micas',
            'Micas asignadas',
            'Esperando Material',
            'Pendiente',
            'Procesando',
            'Pausado',
            'Calidad',
            'Terminado',
        ];


        return $this->render('laboratorio_dashboard/dashboard.html.twig', [
            'laboratoryOrders' => $laboratoryOrders,
            'mappedProducts' => $mappedProducts,
            'orderStages' => $orderStages,
            'eventQuery' => $eventData,
        ]);
    }

    /**
     * @Route("/labdashboard/order-products", name="app_lab_dashboard_order_productos")
     */
    public function laboratoryOrderproducts(EntityManagerInterface $em, Request $request): Response
    {

        $idlaboratoryOrder = $request->get('updateorderid');

        $LaboratoryOrder = $em->getRepository(Ordenlaboratorio::class)->findOneBy(array('idordenlaboratorio' => $idlaboratoryOrder));
        $qb = $em->createQueryBuilder();

        // First query
        $products = $qb->select('p.modelo, ol.idordenlaboratorio, m.nombre as marca, svol.mainproduct as errase, 
            svol.idstockventaordenlaboratorio as id, svol.revision as revision')
            ->from('App\Entity\Stockventaordenlaboratorio', 'svol')
            ->innerJoin('svol.stockventaIdstockventa', 'sv')
            ->innerJoin('sv.stockIdstock', 's')
            ->innerJoin('s.productoIdproducto', 'p')
            ->innerJoin('p.marcaIdmarca', 'm')
            ->innerJoin('svol.ordenlaboratorioIdordenlaboratorio', 'ol')
            ->where('p.status = 1 ')
            ->andWhere('ol.idordenlaboratorio = :idorlab')
            ->setParameters(["idorlab" => $idlaboratoryOrder])
            ->getQuery()->getResult();

        return $this->render('lab_dashboard/laboratory-dashboard-returntable.html.twig', [
            'returnproducts' => $products,
            'idordenlaboratorio' => $idlaboratoryOrder,
        ]);
    }

    /**
     * @Route("/changestage", name="laboratory-order-change-dashboard")
     */
    public function changestage(Request $request): Response
    {

        $eventtype = ['7' => 'start-Lab', '8' => 'pause-Lab', '9' => 'pause-Lab'];
        $eventDesc = ['7' => 'Contador de produccion iniciado', '8' => 'Contador de produccion detenido', '9' => 'Contador de produccion detenido'];


        $em = $this->getDoctrine()->getManager();
        $idlaboratoryOrder = $request->get('updateid');
        $num = $request->get('num');

        $msg = "";

        $LaboratoryOrder = $em->getRepository(Ordenlaboratorio::class)->findOneBy(array('idordenlaboratorio' => $idlaboratoryOrder));


        try {
            if ($LaboratoryOrder) {

                $LaboratoryOrder->setEtapa((int)$num);
                $user = $this->getUser();
                $now = new DateTime();

                if (isset($eventtype[$num])) {

                    $labevent = new Event();

                    $etype = $em->getRepository(Eventtype::class)->findOneBy(array('name' => $eventtype[$num])) ?? new Eventtype();

                    $etype
                        ->setName($eventtype[$num])
                        ->setDescription($eventDesc[$num]);

                    $em->persist($etype);
                    $em->flush();

                    $labevent
                        ->setUsuarioIdusuario($user)
                        ->setCreationdate($now)
                        ->setEventtypeIdeventtype($etype)
                        ->setClienteIdcliente($LaboratoryOrder->getClienteIdcliente())
                        ->setUpdatedate($now)
                        ->setOrdenlaboratorioIdordenlaboratorio($LaboratoryOrder);

                    $em->persist($labevent);
                }

                if ($num == 10) {
                    $shipord = new Shipmentordenlaboratorio();

                    $flujo = $LaboratoryOrder->getFlujoexpedienteIdflujoexpediente();
                    $dir = $flujo->getSucursalIdsucursal();

                    $shipord
                        ->setOrdenlaboratorioIdordenlaboratorio($LaboratoryOrder)
                        ->setDestination($dir);

                    $em->persist($shipord);
                }

                $em->persist($LaboratoryOrder);
                $em->flush();
            } else throw new \Exception('No se encontró la orden de laboratorio');
        } catch (\Exception $e) {
            $msg .= $e->getMessage();

            var_dump($msg);
        }

        return $this->dashboard($em);
    }

    /**
     * @Route("/updatestocklabord", name="laboratory-dashboard-updatestock")
     */
    public function updatestocklabord(Request $request): Response
    {

        $em = $this->getDoctrine()->getManager();
        $idlaboratoryOrder = $request->get('updateid');
        $value = $request->get('value');
        $revision = $request->get('revision');


        $msg = "";

        $stockLabOrder = $em->getRepository(Stockventaordenlaboratorio::class)->findOneBy(
            array('idstockventaordenlaboratorio' => $idlaboratoryOrder)
        );


        try {
            if ($stockLabOrder) {

                $stockLabOrder->setMainproduct($value);
                $stockLabOrder->setRevision($revision);

                $em->persist($stockLabOrder);
                $em->flush();
            } else throw new \Exception('No se encontró la orden de laboratorio');
        } catch (\Exception $e) {
            $msg = $e->getMessage();
        }

        return $this->laboratoryOrderproducts($em, $request);
    }


}
