<?php
// src/Controller/UploadController.php
namespace App\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use App\Service\FileUploader;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;

class UploadController extends AbstractController
{
    public function uploadFile(Request $request, FileUploader $fileUploader)
    {
        $file = $request->files->get('fileUpload');

        if ($file instanceof UploadedFile) {
            $fileName = $fileUploader->upload($file, 'documento'); // Cambia 'documento' por el tipo de documento que quieres

            // ... haz algo con el nombre del archivo, como guardarlo en la base de datos
        }

        // Redirige a la página correspondiente después de subir el archivo
        //return $this->redirectToRoute('ruta_despues_de_subir');
    }
}
