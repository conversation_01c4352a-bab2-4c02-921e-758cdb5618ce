<?php

namespace App\Controller;

use App\Entity\Proveedor;
use App\Entity\Proveedorcontacto;
use App\Entity\Proveedoremail;
use App\Entity\Proveedortelefono;
use App\Form\EmailType;
use App\Form\ProveedorContactoType;
use App\Form\AgregarClienteType;
use App\Form\ProviderType;
use App\Form\TelefonoType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use App\Service\FileUploader;
use Symfony\Component\Serializer\SerializerInterface;


class ProveedorController extends AbstractController
{

/**
 * These form types are used for building and managing forms within the framework.
 * The purpose of `configureOptions` is to set up the options the form accepts.
 *
 * @param OptionsResolver $resolver An instance of the OptionsResolver that allows configuring the options for this form type.
 */

public function configureOptions(OptionsResolver $resolver)
{
    // Esto significa que cuando instancias este tipo de formulario, puedes pasar un valor para 'idproveedorcontacto'.
    // Si no pasas un valor, 'idproveedorcontacto' será null por defecto.
    $resolver->setDefaults([
        'idproveedorcontacto' => null,
    ]);
}


/**
 * The `buildForm` function is responsible for constructing the form.
 * This includes adding fields and configuring how they are displayed.
 *
 * @param FormBuilderInterface $builder An instance of the form builder. It allows adding fields to the form.
 * @param array $options An array containing the options passed to the form upon instantiation, such as 'idproveedorcontacto'.
 */


public function buildForm(FormBuilderInterface $builder, array $options)
{
    // Aquí estamos tomando el valor de 'idproveedorcontacto' desde las opciones del formulario.
    // Esto puede ser útil si deseas utilizar este valor para configurar ciertos campos del formulario.
    $idproveedorcontacto = $options['idproveedorcontacto'];
}

    
    /**
     * @Route("/proveedor", name="app_proveedor")
     */
    public function index(): Response
    {
        // Renderiza la plantilla indexProveedor.html.twig
        return $this->render('proveedor/indexProveedor.html.twig', [
            'controller_name' => 'ProveedorController',
        ]);
    }


/**
 * The 'tableProveedor' function handles constructing the provider table.
 * This includes: Idproveedor, Provider's Trade Name, RFC, Phone, Email.
 * @Route("proveedor/tabla-proveedores", name="tabla-proveedores")
 * @param Request $request An instance of Symfony's Request class, representing the current HTTP request.
 * @return Response Returns the page the user should see.
 */

 

public function tableProveedor(Request $request): Response
{
    // Definiendo variables que serán usadas para enviar al frontend.
    $success = false;
    $message = "";

    // Permite interactuar con la base de datos usando objetos en lugar de SQL directamente.
    $em = $this->getDoctrine()->getManager();

    // Creando una consulta para obtener Idproveedor,NombreComercial de proveedor, RFC, Télefono, Correo Electronico de la entidad Proveedor donde el status es igual a '1'.
    $query = $em->createQuery(
        'SELECT pr.idproveedor, pr.nombre AS NombreComercial, pr.rfc AS RFC, pr.telefono, pr.correoelectronico
        FROM App\Entity\Proveedor pr
        WHERE pr.status = :status'
    )->setParameters(['status' => '1']);

    // Ejecutando la consulta y obteniendo los resultados.
    $proveedores = $query->getResult();
    $success = true; // Si la consulta se ejecuta correctamente, cambiamos el éxito a verdadero.
    $message = "Proveedores obtenidos correctamente"; // Mensaje de éxito.

    // Renderizando una plantilla twig y pasando variables a ella. Esta plantilla muestra la tabla de proveedores.
    return $this->render('proveedor/tablaProveedores.html.twig', [
        'success' => $success,
        'message' => $message,
        'proveedores' => $proveedores,
    ]);
}



/**
 * The 'cambiarStatus' function is responsible for changing the Status field to 0.
 * 
 * @Route("/proveedor/cambiar-status-proveedor/{id}", name="cambiar_status_proveedor", methods={"POST"})
 * 
 * @param int $id It's the identifier of the provider whose status we want to change.
 * 
 * @return JsonResponse Returns a response in JSON format to see whether the change was successful or not.
 */

 
public function cambiarStatus($id)
{
    // Aquí estamos preparando todo para manejar la base de datos.
    $em = $this->getDoctrine()->getManager();

    // Buscamos al proveedor con el ID dado en la base de datos.
    $proveedor = $em->getRepository(Proveedor::class)->find($id);

    // Si no encontramos al proveedor, devolvemos un error.
    //El error HTTP 404 es un estándar que indica que el recurso solicitado no se encontró
    if (!$proveedor) {
        return new JsonResponse(['error' => 'Proveedor no encontrado'], 404);
    }

    // Cambiamos el estado del proveedor a inactivo (0) y guardamos los cambios.
    $proveedor->setStatus(0);
    $em->flush();

    // Devolvemos un mensaje de éxito.
    return new JsonResponse(['success' => 'Estado cambiado correctamente'], 200);
}




/**
 * This function is a controller that creates a ProveedorContacto form.
 *
 * @Route("/proveedor/agregar-contacto/{idproveedor}", name="agregar_contacto")
 *
 * @param Request $request The object representing the HTTP request.
 * @param $idproveedor The ID of the provider to which the contact is to be added.
 *
 * @return Response An HTTP response.
 */


public function ProveedorContact(Request $request, $idproveedor): Response
{
    //Variables iniciales.

    $success = false; //Variable que dice si la operación ha tenido éxito.
    $message = ""; // Mensaje para mostrar al usuario.
    $em = $this->getDoctrine()->getManager(); //Gestor de entidades de Doctrine.
    $Proveedor=null;

    // Crea un formulario basado de ProveedorContactoType.
    $form = $this->createForm(ProveedorContactoType::class); 
    $form->handleRequest($request); // Maneja la solicitud y rellena el formulario con datos de la solicitud.

    $proveedorContacto = "";

    try {
        if ($form->isSubmitted() && $form->isValid()) {

            // Si el formulario es enviado y es válido, sigue a guardar los datos.
            $proveedorContacto = $form->getData();

            // Ponemos la fecha de creación y actualización.
            $now = new \DateTime();
            $proveedorContacto->setCreacion($now);
            $proveedorContacto->setActualizacion($now);

            // Asocia el contacto al proveedor usando el ID del proveedor.
            $Proveedor = $em->getRepository(Proveedor::class)->findOneBy(array('idproveedor' => $idproveedor));

            if ($Proveedor) 
                $proveedorContacto->setProveedorIdproveedor($Proveedor);
            
            $em->persist($proveedorContacto); // Guarda el contacto en la base de datos.
            $em->flush(); // Confirma los cambios.
            $success = true;
        }
    } catch (\Exception $e) {
        $message = $e->getMessage(); // Si ocurre un error, guarda el mensaje del error.
    }

    // Retorna la vista, pasando las variables necesarias para mostrar la página.
    return $this->render('proveedor/agregarContacto.html.twig', [
        'success' => $success,
        'idproveedor' => $idproveedor,
        'message' => $message,
        'form' => $form->createView()
    ]);
}




/**
 * 
 * This function creates a form to add a provider 
 * and, upon success, persists the provider in the database.
 *
 * @Route("/proveedor/add-provider", name="add-provider")
 * 
 * @param Request $request An instance of the Request object representing the HTTP request.
 * 
 * @return Response Returns a response that renders a specific view.
 */

 
 public function addprovider(Request $request): Response
 {
     $em = $this->getDoctrine()->getManager();
     $success = false;
     $message = "";
 
     $idproveedor = $request->request->get('idproveedor');  // Obtener idproveedor desde el formulario
 
     if ($idproveedor) {
         $Provider = $em->getRepository(Proveedor::class)->find($idproveedor);
 
         if (!$Provider) {
             return new Response('Proveedor no encontrado', 404);
         }
     } else {
         $Provider = new Proveedor();
     }
 
     $form = $this->createForm(ProviderType::class, $Provider);
     $form->handleRequest($request);
 
     try {
         if ($form->isSubmitted() && $form->isValid()) {
             $rfc = $Provider->getRfc();
             
             $existingProvider = $em->getRepository(Proveedor::class)->findOneBy(['rfc' => $rfc]);
 
             $em->persist($Provider);
             $em->flush();
 
             $success = true;
         }
     } catch (\Exception $e) {
         $message = $e->getMessage();
     }
 
     return $this->render('proveedor/addProvider.html.twig', [
         'success' => $success,
         'message' => $message,
         'form' => $form->createView()
     ]);
 }
/**
 * @Route("proveedor/table-contacts", name="table-contacts")
 *
 * Controller that manages the view of the table of contacts associated with a specific provider.
 * associates with a provider and then renders a view to display these contacts.
 *
 * @param Request $request Represents the HTTP request including client and server information.
 *
 * @return \Symfony\Component\HttpFoundation\Response An instance of Response representing the HTTP response, including rendered content.
 */


public function tablecontacts(Request $request)
{
    // Inicializa la variable de éxito a false.
    $success = false;

    // Inicializa la variable de mensaje a una cadena vacía.
    $message = "";

    // Obtiene el administrador de entidades.
    $em = $this->getDoctrine()->getManager();

    // Obtiene el idproveedor desde la petición.
    $idproveedor = $request->query->get('idproveedor');

    // Crea una consulta que selecciona los contactos asociados a un proveedor específico.
    $query = $em->createQuery(
        'SELECT pc.idproveedorcontacto, pc.nombre, pc.apellidopaterno, pc.apelliidomaterno AS materno, 
                pc.puesto, pc.nota, pr.idproveedor, pe.email, pe.idproveedoremail
         FROM App\Entity\Proveedorcontacto pc
         INNER JOIN pc.proveedorIdproveedor pr
         LEFT JOIN App\Entity\Proveedoremail pe WITH pe.proveedorcontactoIdproveedorcontacto = pc
         WHERE pr.status = :status AND pr.idproveedor = :idproveedor'
    )->setParameters([
        'status' => '1',
        'idproveedor' => $idproveedor,
    ]);
    
    $proveedoresContactos = $query->getResult();

    // Renderiza la vista 'proveedor/tablecontact.html.twig', pasando las variables necesarias.
    return $this->render('proveedor/tablecontact.html.twig', [
        'success' => $success,
        'message' => $message,
        'proveedoresContactos' => $proveedoresContactos,
    ]);
}



/**
 * @Route("proveedor/form-email", name="form-email")
 *
 * Creates a form for creating an email for a provider.
 * It allows for querying the database to fetch emails 
 * associated with a specific provider contact and then renders a view to display these emails.
 *
 * @param Request $request Represents the HTTP request including client and server information.
 *
 * @return Response | JsonResponse 
 * Returns an instance of Response representing the HTTP response, including rendered content. 
 * If an exception is caught, a JSON response with the error message is returned.
 */

public function formemail(Request $request): Response
{
    try {
        // Obtiene el administrador de entidades.
        $em = $this->getDoctrine()->getManager();
        $exito = true;
        $idproveedorcontacto=null;

        // Recupera él, id del contacto del proveedor desde la petición.
        $idproveedorcontacto = $request->query->get('idproveedorcontacto');

        // Crea una consulta que selecciona los correos electrónicos asociados a un contacto de proveedor específico.
        $query = $em->createQuery(
            'SELECT pe.email, pc.nombre, pe.idproveedoremail
     FROM App\Entity\Proveedoremail pe
     INNER JOIN pe.proveedorcontactoIdproveedorcontacto pc
     WHERE pc.status = :pcStatus AND pe.status = :peStatus'
        )->setParameters([
            'pcStatus' => '1',
            'peStatus' => '1',
        ]);

        $results = $query->getResult();


        // Crea una nueva instancia de Proveedoremail.
        $Proveedoremail= new Proveedoremail();

        // Si el idproveedorcontacto no es válido.
        if (!$idproveedorcontacto || !is_numeric($idproveedorcontacto)){
            // Busca el contacto del proveedor en la base de datos.
            $proveedorcontacto = $em->getRepository(Proveedorcontacto::class)->findOneBy(array('idproveedorcontacto' => $idproveedorcontacto));

            // Asigna el contacto del proveedor a la entidad Proveedoremail.
            $Proveedoremail->setProveedorcontactoIdproveedorcontacto($proveedorcontacto);
        }

        // Crea el formulario basado en EmailType y lo vincula con la entidad Proveedoremail.
        $form = $this->createForm(EmailType::class, $Proveedoremail);

        // Maneja la petición y establece los datos del formulario.
        $form->handleRequest($request);

        // Si el formulario se ha enviado y es válido.
        if ($form->isSubmitted() && $form->isValid()) {
            // Obtiene los datos del formulario.
            $Email = $form->getData();

            // Establece las fechas de creación y actualización.
            $Email->setCreacion(new \DateTime("now"));
            $Email->setActualizacion(new \DateTime("now"));

            // Persiste el nuevo correo electrónico en la base de datos.
            $em->persist($Email);

            // Guarda los cambios en la base de datos.
            $em->flush();

            // Establece la variable de éxito.
            
        }

        // Renderiza la vista, pasando las variables necesarias.
        return $this->render('proveedor/addEmail.html.twig', [
            'success' => false,
            'idproveedorcontacto' => $idproveedorcontacto,
            'results'=>$results,
            'message' => '',
            'form' => $form->createView()
        ]);
    } catch (\Exception $e) { // Si ocurre una excepción.
        // Devuelve una respuesta JSON con el mensaje de error.
        return new JsonResponse(['error' => $e->getMessage()], 500);
    }
}


/**
 * @Route("proveedor/form-phone", name="form-phone")
 *
 * Controller that creates the form for adding a phone number to a provider.
 * This function creates and handles a form for adding a phone to a specific provider contact.
 *
 * @param Request $request Represents the HTTP request including client and server information.
 *
 * @return \Symfony\Component\HttpFoundation\Response|\Symfony\Component\HttpFoundation\JsonResponse 
 * Returns an instance of Response representing the HTTP response, including rendered content. 
 * If an exception is caught, a JSON response with the error message is returned.
 */

 public function formphone(Request $request): Response
 {
     try {
    // Obtiene el administrador de entidades.
    $em = $this->getDoctrine()->getManager();
    $idproveedorcontacto=null;
    // Recupera el id del contacto del proveedor desde la petición.
    $idproveedorcontacto = $request->query->get('idproveedorcontacto');

    // Crea una nueva instancia de Proveedortelefono.
    $Proveedortelefono= new Proveedortelefono();

    // Si se ha proporcionado un idproveedorcontacto.
    if ($idproveedorcontacto!= null) {
        // Busca el contacto del proveedor en la base de datos.
        $proveedorcontacto = $em->getRepository(Proveedorcontacto::class)->findOneBy(array('idproveedorcontacto' => $idproveedorcontacto));

        // Asigna el contacto del proveedor a la entidad Proveedortelefono.
        $Proveedortelefono->setProveedorcontactoIdproveedorcontacto($proveedorcontacto);
    }

    // Crea el formulario basado en TelefonoType y lo vincula con la entidad Proveedortelefono.
    $form = $this->createForm(TelefonoType::class, $Proveedortelefono);

    // Maneja la petición y establece los datos del formulario.
    $form->handleRequest($request);

    // Si el formulario se ha enviado y es válido.
    if ($form->isSubmitted() && $form->isValid()) {
        // Obtiene los datos del formulario.
        $Phone = $form->getData();

        // Establece las fechas de creación y actualización.
        $Phone->setCreacion(new \DateTime("now"));
        $Phone->setActualizacion(new \DateTime("now"));

        // Persiste el nuevo número de teléfono en la base de datos.
        $em->persist($Phone);

        // Guarda los cambios en la base de datos.
        $em->flush();

    }

    // Renderiza la vista inicial del formulario.
    return $this->render('proveedor/addPhone.html.twig', [
        'success' => false,
        'idproveedorcontacto' => $idproveedorcontacto,
        'message' => '',
        'form' => $form->createView()
    ]);
    } catch (\Exception $e) { // En caso de cualquier excepción.
    // Devuelve una respuesta JSON con el mensaje de error.
    return new JsonResponse(['error' => $e->getMessage()], 500);
    }
}


/**
 * @Route("proveedor/edit-provider/{idproveedor}", name="edit-provider")
 *
 * Este controlador lo que hace es agarrar el id que obtengo desde el botón
 * Y editar el proveedor
 *
 */

 public function editProvider(Request $request, $idproveedor): Response
 {
     $em = $this->getDoctrine()->getManager();
     $success = false;
     $message = "";
     $proveedor = $em->getRepository(Proveedor::class)->findOneBy(array('idproveedor' => $idproveedor));
 
 
     $form = $this->createForm(ProviderType::class, $proveedor);
     $form->handleRequest($request);
 
     if ($form->isSubmitted() && $form->isValid()) {
         try {
             $em->persist($proveedor);
             $em->flush();
             $success = true;
             $message = "Proveedor editado";
         } catch (\Exception $e) {
             $message = "Ocurrió un error al guardar: " . $e->getMessage();
         }
     }
  
     return $this->render('proveedor/editProveedor.html.twig', [
         'form' => $form->createView(),
         'idproveedor'=>$idproveedor,
         'success' => $success,
         'message' => $message
     ]);
 }

}
