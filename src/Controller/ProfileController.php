<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\Security\Core\Security;
use Scheb\TwoFactorBundle\Security\TwoFactor\Provider\Google\GoogleAuthenticatorInterface;
use Scheb\TwoFactorBundle\Security\TwoFactor\QrCode\QrCodeGenerator;
use Doctrine\ORM\EntityManagerInterface;
use App\Entity\Usuario;
use App\Entity\Sucursal;
use DateTime;

class ProfileController extends AbstractController
{
    /**
     * @Route("/profile", name="app-profile")
     */
    public function index(EntityManagerInterface $em): Response
    {
        $User = $this->getUser();
        $profilePicExists = false;

        if ($User){
            $path = $this->getParameter('profilePicsFolder').'/'.$User->getIdusuario().'/'.$User->getProfilepicture();
            $profilePicExists = ($User->getProfilepicture()) ? file_exists($path) : false;
        }

        $sucursales = $em->getRepository(Sucursal::class)->findBy(['status' => '1']);

        if (!$User->getSucursalIdsucursal() || $User->getSucursalIdsucursal()->getStatus() !== '1') {
            if (count($sucursales) > 0) {
                $User->setSucursalIdsucursal($sucursales[0]);
                $em->persist($User);
                $em->flush();
            }
        }   

        return $this->render('profile/index.html.twig', ["profilePicExists" => $profilePicExists, "sucursales" => $sucursales]);
    }

    /**
     * @Route("/qr-code", name="google-qr-code")
     */
    public function displayGoogleAuthenticatorQrCode(QrCodeGenerator $qrCodeGenerator, GoogleAuthenticatorInterface $googleAuthenticatorInterface)
    {
        $User = $this->getUser();
        $em = $this->getDoctrine()->getManager();

        $secret = $googleAuthenticatorInterface->generateSecret();
        $User->setGoogleAuthenticatorSecret($secret);
        $curVersion = $User->getTrustedTokenVersion();
        $User->setTrustedTokenVersion($curVersion+1);

        $em->persist($User);
        $em->flush();

        $qrCode = $qrCodeGenerator->getGoogleAuthenticatorQrCode($User);

        return new Response($qrCode->writeString(), 200, ['Content-Type' => 'image/png']);
    }

    /**
     * @Route("/profile/change-sucursal", name="profile_change_sucursal", methods={"POST"})
     */
    public function changeSucursal(Request $request, EntityManagerInterface $em, Security $security): JsonResponse
    {
        $usuario = $security->getUser();

        if (!$usuario instanceof Usuario) {
            return new JsonResponse(['success' => false, 'message' => 'Usuario no autenticado'], 401);
        }

        $sucursalId = $request->request->get('sucursal_id');

        if (!$sucursalId) {
            return new JsonResponse(['success' => false, 'message' => 'Sucursal no especificada'], 400);
        }

        $sucursal = $em->getRepository(Sucursal::class)->findOneBy(['idsucursal' => $sucursalId]);

        if (!$sucursal) {
            return new JsonResponse(['success' => false, 'message' => 'Sucursal no encontrada'], 404);
        }

        $usuario->setSucursalIdsucursal($sucursal);
        $em->persist($usuario);
        $em->flush();

        return new JsonResponse(['success' => true, 'message' => 'Sucursal actualizada con éxito']);
    }

}