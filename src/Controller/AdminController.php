<?php
// src/Controller/CarAdminController.php

namespace App\Controller;

use App\Entity\Cliente;
use App\Entity\Documentosucursal;
use App\Entity\Flujoexpediente;
use App\Entity\Sucursal;
use PharIo\Manifest\Email;
use Sonata\AdminBundle\Controller\CRUDController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Symfony\Component\HttpFoundation\Request;
use Sonata\AdminBundle\Route\RouteCollection;
use Symfony\Component\HttpFoundation\Response;
use App\Entity\Producto;
use App\Entity\Ventagroup;
use App\Entity\Stock;
use App\Entity\Stockventa;
use App\Entity\Ventacupon;
use App\Entity\Venta;
use App\Entity\Productostranspasoalmacen;
use App\Entity\Productoventa;
use App\Entity\Ordensalida;
use App\Entity\Ventafactura;
use App\Entity\Paymenttype;
use Doctrine\ORM\EntityManagerInterface;
use Sonata\AdminBundle\Templating\TemplateRegistryInterface;
use Sonata\AdminBundle\Datagrid\DatagridInterface;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use App\Entity\Pago;
use App\Service\PagoEraser;
use App\Entity\Documentos;
use App\Service\ExamenVisualService;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;

class AdminController extends CRUDController
{
    /**
     * Delete action.
     *
     * @param int|string|null $id
     *
     * @return Response|RedirectResponse
     * @throws AccessDeniedException If access is not granted
     *
     * @throws NotFoundHttpException If the object does not exist
     */
    private TemplateRegistryInterface $templateRegistry;


    public function deleteAction(Request $request): Response
    {

        $object = $this->assertObjectExists($request, true);
        \assert(null !== $object);

        $this->checkParentChildAssociation($request, $object);

        $this->admin->checkAccess('delete', $object);

        $preResponse = $this->preDelete($request, $object);
        if (null !== $preResponse) {
            return $preResponse;
        }

        if (\in_array($request->getMethod(), [Request::METHOD_POST, Request::METHOD_DELETE], true)) {
            // check the csrf token
            $this->validateCsrfToken($request, 'sonata.delete');

            $objectName = $this->admin->toString($object);

            try {

                $em = $this->getDoctrine()->getManager();

                if ($object instanceof Pago) {

                    $em = $this->getDoctrine()->getManager();
                    $pagoEraser = new PagoEraser($em);
                    $pagoEraser->erasePago($object);
                } else if ($object instanceof Ventagroup) {
                    $object->setStatus("0");

                    $em = $this->getDoctrine()->getManager();

                    $query = $em->createQuery(
                        'SELECT v
                        FROM App\Entity\Venta v 
                        WHERE v.ventagroupIdventagroup =:groupfactura'
                    );

                    $ventas = $query->setParameter('groupfactura', $object)->getResult();

                    foreach ($ventas as $venta) {
                        $venta->setVentagroupIdventagroup(null);
                        $venta->setAutorizacionstate('4');
                        $em->persist($venta);
                    }
                    var_dump("correct");


                } else {
                    var_dump("enter");

                    $object->setStatus("0");
                }


                $em->persist($object);
                $em->flush();
                //$this->admin->delete($object);

                if ($this->isXmlHttpRequest($request)) {
                    return $this->renderJson(['result' => 'ok']);
                }

                $this->addFlash(
                    'sonata_flash_success',
                    $this->trans(
                        'flash_delete_success',
                        ['%name%' => $this->escapeHtml($objectName)],
                        'SonataAdminBundle'
                    )
                );
            } catch (ModelManagerException $e) {
                // NEXT_MAJOR: Remove this catch.
                $this->handleModelManagerException($e);

                if ($this->isXmlHttpRequest($request)) {
                    return $this->renderJson(['result' => 'error']);
                }

                $this->addFlash(
                    'sonata_flash_error',
                    $this->trans(
                        'flash_delete_error',
                        ['%name%' => $this->escapeHtml($objectName)],
                        'SonataAdminBundle'
                    )
                );
            } catch (ModelManagerThrowable $e) {
                $errorMessage = $this->handleModelManagerThrowable($e);

                if ($this->isXmlHttpRequest($request)) {
                    return $this->renderJson(['result' => 'error'], Response::HTTP_OK, []);
                }

                $this->addFlash(
                    'sonata_flash_error',
                    $errorMessage ?? $this->trans(
                    'flash_delete_error',
                    ['%name%' => $this->escapeHtml($objectName)],
                    'SonataAdminBundle'
                )
                );
            }

            return $this->redirectTo($request, $object);
        }
        $this->templateRegistry = $this->admin->getTemplateRegistry();
        $template = $this->templateRegistry->getTemplate('delete');

        return $this->renderWithExtraParams($template, [
            'object' => $object,
            'action' => 'delete',
            'csrf_token' => $this->getCsrfToken('sonata.delete'),
        ]);
    }

    /**
     * Execute a batch delete.
     *
     * @return RedirectResponse
     * @throws AccessDeniedException If access is not granted
     *
     */

    public function batchActionDelete(ProxyQueryInterface $query): Response
    {
        exit();
        $selectedCategories = $query->execute();

        try {
            /** @var Category $category */
            foreach ($selectedCategories as $category) {
                $category->setStatus('0');
                $this->admin->update($category);
            }
            $this->trans('flash_batch_delete_success', [], 'SonataAdminBundle');
        } catch (ModelManagerException $e) {
            $this->handleModelManagerException($e);
            $this->addFlash(
                'sonata_flash_error',
                $this->trans('flash_batch_delete_error', [], 'SonataAdminBundle')
            );
        }
        return $this->redirectToList();
    }

    /**
     * @param $id
     */

    public function detalleVentaAction($id)
    {
        $object = $this->admin->getSubject();

        if (!$object) {
            throw new NotFoundHttpException(sprintf('unable to find the object with id: %s', $id));
        }

        $empresaid = null;
        if ($object->getSucursalIdsucursal() && $object->getSucursalIdsucursal()->getEmpresaIdempresa()) {
            $empresaid = $object->getSucursalIdsucursal()->getEmpresaIdempresa()->getIdempresa();
        }

        $em = $this->getDoctrine()->getManager();

        $parameters = array('idventa' => $id);

        $query = $em->createQuery(
            'SELECT  sv.cantidad,sv.creacion,sv.modificacion,sv.porcentajedescuento,sv.status,
            sv.precio, sv.costo, p.modelo, p.descripcion, s.codigobarras, v.convenio, v.folio,
            uni.idunidad, uni.nombre as unidad, v.seapartoarmazon, sv.cantidad, v.porcentajeiva,
            sv.preciofinal, v.fechacancelacion, v.fechacreacion as fechaTicket, sv.idstockventa
            FROM App\Entity\Stockventa sv
            inner join sv.stockIdstock s
            inner join s.productoIdproducto p
            inner join sv.ventaIdventa v
            left join v.unidadIdunidad uni
            where v.idventa =:idventa
            '
        )->setParameters($parameters);
        $Productos = $query->getResult();

        $query = $em->createQuery(
            'SELECT  p.fecha, p.monto, p.mesesintereses, pt.name as tipopago
            FROM App\Entity\Pago p
            INNER JOIN p.ventaIdventa v
            INNER JOIN p.paymenttypeIdpaymenttype pt
            WHERE v.idventa =:idventa and p.status=1'
        )->setParameters($parameters);
        $Pagos = $query->getResult();

        $query = $em->createQuery(
            'SELECT  m
                FROM App\Entity\Merma m
                INNER JOIN m.stockventaIdstockventa sv
                INNER JOIN sv.ventaIdventa v
               WHERE m.status=:status AND v.idventa =:saleId ORDER BY m.idmerma ASC'
        )->setParameters(['status' => '1', 'saleId' => $id]);

        $mermas = $query->getResult();

        return $this->render(
            'admin/detalle-venta.html.twig', [
                'venta' => $object,
                'id' => $id,
                'productos' => $Productos,
                'pagos' => $Pagos,
                'listUrl' => $this->admin->generateUrl('list'),
                'empresaid' => $empresaid, 'mermas' => $mermas]
        );
    }

    public function profile_settingesAction()
    {
        $User = $this->getUser();
        $Sucursal = $User->getSucursalIdsucursal();

        return $this->render(
            'admin/user_block.html.twig ',
            ['User' => $User, 'Sucursal' => $Sucursal]
        );
    }


    public function detalleVentaAction1($id)
    {
        $object = $this->admin->getSubject();

        if (!$object) {
            throw new NotFoundHttpException(sprintf('unable to find the object with id: %s', $id));
        }

        $empresaid = null;
        if ($object->getSucursalIdsucursal() && $object->getSucursalIdsucursal()->getEmpresaIdempresa()) {
            $empresaid = $object->getSucursalIdsucursal()->getEmpresaIdempresa()->getIdempresa();
        }

        $em = $this->getDoctrine()->getManager();

        $parameters = array('idventa' => $id);

        $query = $em->createQuery(
            'SELECT  sv.cantidad,sv.creacion,sv.modificacion,sv.porcentajedescuento,sv.status, sv.precio, sv.costo, p.modelo, p.descripcion, s.codigobarras, v.convenio, v.folio, uni.idunidad, uni.nombre as unidad, v.seapartoarmazon, sv.cantidad, v.porcentajeiva, sv.preciofinal, v.fechacancelacion, v.fechacreacion as fechaTicket
            FROM App\Entity\Stockventa sv
            inner join sv.stockIdstock s
            inner join s.productoIdproducto p
            inner join sv.ventaIdventa v
            left join v.unidadIdunidad uni 
            where v.idventa =:idventa 
            '
        )->setParameters($parameters);
        $Productos = $query->getResult();

        $query = $em->createQuery(
            'SELECT  p.monto, p.fecha, p.tipopago
            FROM App\Entity\Pago p
            inner join p.ventaIdventa v
            where v.idventa =:idventa and p.status=1
            '
        )->setParameters($parameters);
        $Pagos = $query->getResult();

        return $this->render(
            'admin/list-action-detalleventa.html.twig',
            ['venta' => $object, 'id' => $id, 'productos' => $Productos, 'pagos' => $Pagos, 'listUrl' => $this->admin->generateUrl('list'), 'empresaid' => $empresaid,]
        );
    }


    public function detalleTraspasoAlmacenAction($id)
    {
        $object = $this->admin->getSubject();

        if (!$object) {
            throw new NotFoundHttpException(sprintf('unable to find the object with id: %s', $id));
        }
        /**
         * Aquí obtengo de stock -> codigobarras, de producto->modelo, de marca->nombre,
         * traspasoalmacen->cantidad,producto->codigobarrasuniversal
         *
         */
        $em = $this->getDoctrine()->getManager();
        $parameters = array('idtranspasoalmacen' => $id);

        $query = $em->createQuery(
            'SELECT s.codigobarras, p.modelo, m.nombre as marca, pta.cantidad as cantidad, p.codigobarrasuniversal as codigobarrasuniversal
            FROM App\Entity\Productostranspasoalmacen pta
            inner join pta.transpasoalmacenIdtranspasoalmacen ta
            inner join pta.stockIdstock s
            inner join s.productoIdproducto p
            inner join p.marcaIdmarca m

            where ta.idtranspasoalmacen =:idtranspasoalmacen and pta.status=1
            '
        )->setParameters($parameters);
        $productos = $query->getResult();
        /**
         * Aquí se obtiene transpasoalmacen-> creación, transpasoalmacen->notas
         * usuario->apellidopaterno, usuari->apellidomaterno, sucursalorigen->nombre, sucursaldestino->nombre
         */
        $query = $em->createQuery(
            'SELECT ta.creacion,ta.notas, 
            u.nombre as nombreUsuarioEnvia,
            u.apellidopaterno as apellidopaternoUsuarioEnvia,
            u.apellidomaterno as apellidomaternoUsuarioEnvia,
            sucursalo.nombre as sucursalSalida,
            sucursald.nombre as sucursalDestino
                FROM App\Entity\Transpasoalmacen ta
                inner join ta.sucursalIdsucursalorigen as sucursalo
                inner join ta.sucursalIdsucursaldestino as sucursald
                inner join ta.usuarioIdusuario as u

                where ta.idtranspasoalmacen =:idtranspasoalmacen 
                '
        )->setParameters($parameters);
        $traspasoalmacen = $query->getOneOrNullResult();

        return $this->render(
            'admin/detalle-traspaso-almacen.html.twig',
            ['productos' => $productos, 'listUrl' => $this->admin->generateUrl('list'), 'traspasoalmacen' => $traspasoalmacen, "transferId" => $id]
        );
    }


    public function detalleOrdenSalidaAction($id)
    {

        $object = $this->admin->getSubject();


        if (!$object) {
            throw new NotFoundHttpException(sprintf('unable to find the object with id: %s', $id));
        }

        $em = $this->getDoctrine()->getManager();

        $ordenSalidaObj = $em->getRepository(Ordensalida::class)->find($id);


        $parameters = array('idordensalida' => $id);

        /**
         * SKU -> codigoBarras
         * Código de barras -> codigoBarrasUniversal
         */

        $Ordensalida = $em->getRepository(Ordensalida::class)->findOneBy(['idordensalida' => $id]);
        $Idtranspaso = $Ordensalida->getTranspasoalmacenIdtranspasoalmacen()->getIdtranspasoalmacen();


        $query = $em->createQuery('SELECT  p.modelo, s.codigobarras AS SKU, 
         p.codigobarrasuniversal AS codigobarras, m.nombre AS marca, pta.cantidad AS cantidad

         FROM App\Entity\Productostranspasoalmacen pta
         inner join pta.stockIdstock s
         INNER JOIN s.productoIdproducto p
         INNER JOIN p.marcaIdmarca m
         inner join pta.transpasoalmacenIdtranspasoalmacen ta
         where s.status=:status AND ta.idtranspasoalmacen = :idTranspasoalmacen')
            ->setParameters(['status' => '1', 'idTranspasoalmacen' => $Idtranspaso]);

        $productos = $query->getResult();

        $query = $em->createQuery(
            'SELECT ta.creacion, ta.notas, os.nota, os.aceptada,
            u.nombre as nombreUsuarioEnvia,
            u.apellidopaterno as apellidopaternoUsuarioEnvia,
            u.apellidomaterno as apellidomaternoUsuarioEnvia,
            sucursalo.nombre as sucursalSalida,
            sucursald.nombre as sucursalDestino
            FROM App\Entity\Ordensalida os
            INNER JOIN os.transpasoalmacenIdtranspasoalmacen ta
            INNER JOIN ta.sucursalIdsucursalorigen sucursalo
            INNER JOIN ta.sucursalIdsucursaldestino sucursald
            INNER JOIN ta.usuarioIdusuario u
            WHERE os.idordensalida = :idordensalida'
        )->setParameters($parameters);
        $traspasoalmacen = $query->getOneOrNullResult();

        return $this->render(
            'admin/detalle-orden-salida.html.twig',
            [
                'productos' => $productos,
                'listUrl' => $this->admin->generateUrl('list'),
                'ordenSalida' => $id,
                'estadoAceptada' => $ordenSalidaObj ? $ordenSalidaObj->getAceptada() : null,
                'traspasoalmacen' => $traspasoalmacen
            ]
        );
    }


    public function detalleStockAction($id)
    {
        $object = $this->admin->getSubject();
        $idventa = null;
        $codigocupon = ' ';

        if (!$object) {
            throw new NotFoundHttpException(sprintf('unable to find the object with id: %s', $id));
        }

        $em = $this->getDoctrine()->getManager();

        $parameters = array('idstock' => $id);


        $query = $em->createQuery(
            '   SELECT  sv.precio, v.folio, u.nombre, u.apellidopaterno, suc.nombre as sucursal, v.fecha, c.nombre as cliente,
                        s.idstock,v.idventa, v.total, sv.porcentajedescuento, sv.costo, v.cotizacion,  v.status, v.fechaventa,
                        v.fecha as fechas, v.idventa , cp.codigo, sv.estaapartado, sv.cantidad, tv.nombre AS TipoventaNombre
                FROM App\Entity\Stockventa sv
                inner join sv.stockIdstock s
                inner join sv.ventaIdventa v
                inner join v.tipoventaIdtipoventa tv
                inner join v.usuarioIdusuario u
                inner join s.sucursalIdsucursal suc
                inner join v.clienteIdcliente c
                left join App\Entity\Ventacupon vc with vc.ventaIdventa = v
                left join vc.cuponIdcupon cp
                WHERE s.idstock = :idstock and sv.status=1
                ORDER BY sv.idstockventa DESC
            '
        )->setParameters(["idstock" => $id]);
        $stockventas = $query->getResult();

        if ($stockventas) {
            $idventa = $stockventas[0]['idventa'];
        }


        $query = $em->createQuery(
            '   SELECT  sucursalo.nombre as sucursalSalida, sucursald.nombre as sucursalDestino, u.nombre as nombreUsuarioEnvia, u.apellidopaterno as apellidopaternoUsuarioEnvia, ta.creacion
                FROM App\Entity\Productostranspasoalmacen pta
                inner join pta.transpasoalmacenIdtranspasoalmacen ta
                inner join pta.stockIdstock s
                inner join ta.sucursalIdsucursalorigen as sucursalo
                inner join ta.sucursalIdsucursaldestino as sucursald
                inner join ta.usuarioIdusuario as u
                WHERE s.idstock = :idstock
            '
        )->setParameters(["idstock" => $id]);
        $productos = $query->getResult();


        $query = $em->createQuery(
            '   SELECT  s.codigobarras, s.creacion, s.cantidad, p.modelo, p.tipoproducto
                FROM App\Entity\Stock s 
                inner join s.productoIdproducto p
                WHERE s.idstock = :idstock
            '
        )->setParameters(["idstock" => $id]);
        $stock = $query->getOneOrNullResult();


        $query = $em->createQuery(
            '   SELECT  cil.fecha, cil.cantidad, cil.archivoexcel
                FROM App\Entity\Cargainventariolog cil 
                inner join cil.stockIdstock s
                WHERE s.idstock = :idstock
            '
        )->setParameters(["idstock" => $id]);
        $cargainventariologs = $query->getResult();

        $query = $em->createQuery(
            '   SELECT sh.date, ss.name as state, CONCAT(u.nombre, \' \', u.apellidopaterno, \' \', u.apellidomaterno) as fullName
                FROM App\Entity\Stockhistory sh 
                INNER JOIN sh.stockIdstock s
                INNER JOIN sh.stockstateIdstockstate ss
                INNER JOIN sh.usuarioIdusuario u
                WHERE s.idstock = :idstock
                ORDER BY sh.idstockhistory ASC
            '
        )->setParameters(["idstock" => $id]);
        $stockHistories = $query->getResult();

        $query = $em->createQuery(
            'SELECT  m
                FROM App\Entity\Merma m
                INNER JOIN m.stockIdstock s
               where m.status=:status AND s.idstock =:idstock'
        )->setParameters(['status' => '1', "idstock" => $id]);

        $merma = $query->getOneOrNullResult();

        return $this->render(
            'admin/detalle-stock.html.twig',
            [
                "stock" => $stock,
                "stockventas" => $stockventas,
                "productos" => $productos,
                'cargainventariologs' => $cargainventariologs,
                'stockHistories' => $stockHistories,
                'merma' => $merma
            ]
        );
    }

    public function detalleApartadoAction($id)
    {
        $object = $this->admin->getSubject();

        if (!$object) {
            throw new NotFoundHttpException(sprintf('unable to find the object with id: %s', $id));
        }

        $em = $this->getDoctrine()->getManager();

        $exito = false;
        $msj = "";
        $stockventas = [];
        try {
            $query = $em->createQuery(
                '   SELECT  sv.precio, v.folio, u.nombre, u.apellidopaterno, suc.nombre as sucursal, v.fecha, c.nombre as cliente,
                        v.total, sv.porcentajedescuento, sv.costo, v.cotizacion,  
                        v.status, v.fechaventa, v.fecha as fechas,  sv.fechaapartado,
                        v.idventa , cp.codigo, sv.preciofinal,
                        sv.estaapartado, sv.cantidad as catidadApartada,tv.nombre as tipoVenta               
                FROM App\Entity\Stockventa sv
                inner join sv.stockIdstock s
                inner join sv.ventaIdventa v
                inner join v.usuarioIdusuario u
                inner join v.sucursalIdsucursal suc
                inner join v.clienteIdcliente c
                inner join v.tipoventaIdtipoventa tv
                left join App\Entity\Ventacupon vc with vc.ventaIdventa = v
                left join vc.cuponIdcupon cp
                WHERE sv.idstockventa = :idstockventa and sv.estaapartado=:estaapartado
            '
            )->setParameters(["idstockventa" => $object->getIdstockventa(), 'estaapartado' => "1"]);
            $stockventas = $query->getResult();
            $exito = true;
        } catch (\Exception $e) {
            $msj = $e->getMessage() . " linea " . $e->getLine() . " archivo " . $e->getFile();
        }

        return $this->render(
            'admin/detalle-stock-apartados.html.twig',
            ['exito' => $exito, 'msj' => $msj, 'stockventas' => $stockventas]
        );
    }


    public function cancelarVentaAction($id)
    {
        $object = $this->admin->getSubject();

        if (!$object) {
            throw new NotFoundHttpException(sprintf('unable to find the object with id: %s', $id));
        }

        $em = $this->getDoctrine()->getManager();
        $parameters = array('idventa' => $id);

        return $this->render(
            'admin/cancelar-venta.html.twig',
            ['idventa' => $id, 'listUrl' => $this->admin->generateUrl('list')]

        );
    }

    public function cambiarContrasenaAction($id)
    {
        $object = $this->admin->getSubject();

        if (!$object) {
            throw new NotFoundHttpException(sprintf('unable to find the object with id: %s', $id));
        }

        return $this->render(
            'admin/cambiar-contrasena.html.twig',
            ['idusuario' => $id,]

        );
    }

    /**
     * Muestra el detalle de una factura (Ventafactura) y permite descargar su ZIP.
     *
     * @param int $id idventafactura
     */
    public function detalleFacturaAction(int $id): Response
    {
        /* ───── 1. Objeto Sonata Admin ───── */
        $venta = $this->admin->getSubject();          // ← se mostrará en la vista
        if (!$venta) {
            throw $this->createNotFoundException(
                sprintf('Unable to find the object with id: %d', $id)
            );
        }

        $em = $this->getDoctrine()->getManager();

        /* ───── 2. Datos de la factura y Venta asociada ───── */
        $factura = $em->createQuery(
            'SELECT  df.razonsocial, df.rfc, df.email,
                 df.codigopostal, df.regimenfiscal, df.usocfdi,
                 df.constanciasituacionfiscal, v.idventa
         FROM   App\Entity\Ventafactura         vf
         JOIN   vf.clientefacturadatosIdclientefacturadatos df
         JOIN   vf.ventaIdventa                 v
         WHERE  vf.idventafactura = :id'
        )
            ->setParameter('id', $id)
            ->getOneOrNullResult();

        if (!$factura) {
            throw $this->createNotFoundException('La factura solicitada no existe.');
        }
        $idVenta = $factura['idventa'];

        /* ───── 3. Productos de la Venta ───── */
        $productos = $em->createQuery(
            'SELECT sv.cantidad, sv.creacion, sv.modificacion, sv.porcentajedescuento,
                sv.status, sv.precio, sv.costo, p.modelo, p.descripcion,
                s.codigobarras, v.convenio, v.folio,
                uni.idunidad, uni.nombre AS unidad,
                v.seapartoarmazon, v.porcentajeiva,
                sv.preciofinal, v.pagado, v.iva, v.total
         FROM   App\Entity\Stockventa sv
         JOIN   sv.stockIdstock         s
         JOIN   s.productoIdproducto    p
         JOIN   sv.ventaIdventa         v
         LEFT  JOIN v.unidadIdunidad    uni
         WHERE  v.idventa = :idVenta'
        )
            ->setParameter('idVenta', $idVenta)
            ->getResult();

        /* ───── 4. Pagos de la Venta ───── */
        $pagos = $em->createQuery(
            'SELECT p.monto, p.fecha, p.tipopago
         FROM   App\Entity\Pago p
         JOIN   p.ventaIdventa v
         WHERE  v.idventa = :idVenta'
        )
            ->setParameter('idVenta', $idVenta)
            ->getResult();

        /* ───── 5. Historal de Ventafacturas relacionadas (opcional) ───── */
        $ventaFacturas = $em->createQuery(
            'SELECT vf.status, vf.creacion, vf.fechacomprobante, vf.nombrezip
         FROM   App\Entity\Ventafactura vf
         JOIN   vf.ventaIdventa v
         WHERE  v.idventa = :idVenta       -- ➊ solo las de esta venta
         AND    vf.status   = 1'
        )
            ->setParameter('idVenta', $idVenta)
            ->getResult();

        /* ───── 6. Localizar el ZIP ───── */
        $ventaFacturaObj = $em->getRepository(Ventafactura::class)->find($id);

        $zipFileName = null;
        $zipFilePath = null;
        $existZip = false;

        if ($ventaFacturaObj && $ventaFacturaObj->getNombrezip()) {
            $zipFileName = $ventaFacturaObj->getNombrezip();          // ej. idcliente/factura_xxx.zip

            $facturasDir = $this->getParameter('uploads') . DIRECTORY_SEPARATOR . 'zipFActuras';       // ➜ public/uploads/zipFActuras

            // Debug information
            error_log("ZIP File Name: " . $zipFileName);
            error_log("Facturas Dir: " . $facturasDir);

            // Try multiple possible paths for the ZIP file
            $possiblePaths = [
                // Original path
                $facturasDir . DIRECTORY_SEPARATOR . $zipFileName,

                // If zipFileName contains a client ID (e.g., "clientId/filename.zip")
                // Try to extract parts and construct path
                $facturasDir . DIRECTORY_SEPARATOR . str_replace('/', DIRECTORY_SEPARATOR, $zipFileName),

                // Try directly in zipFacturas directory (for files like "factura114885-667da3c69127b.zip")
                $facturasDir . DIRECTORY_SEPARATOR . basename($zipFileName),

                // Try with authZip parameter
                $this->getParameter('authZip') . DIRECTORY_SEPARATOR . $id . DIRECTORY_SEPARATOR . basename($zipFileName),

                // Try with the URL format from the issue description
                $facturasDir . DIRECTORY_SEPARATOR . "factura" . $id . "-" . basename($zipFileName)
            ];

            // Debug each path
            foreach ($possiblePaths as $index => $path) {
                error_log("Path " . $index . ": " . $path . " - Exists: " . (is_file($path) ? "Yes" : "No"));
            }

            // Check each possible path
            foreach ($possiblePaths as $path) {
                if (is_file($path)) {
                    $zipFilePath = $path;
                    $existZip = true;
                    error_log("Found ZIP file at: " . $path);
                    break;
                }
            }

            // If still not found, check if the file exists in the URL format
            if (!$existZip) {
                // Try with the URL format from the issue description
                $baseFileName = basename($zipFileName);

                // Ensure the filename ends with .zip
                if (!str_ends_with(strtolower($baseFileName), '.zip')) {
                    $baseFileName .= '.zip';
                }

                $urlPath = "https://sistema.optimoopticas.mx/uploads/zipFacturas/" . $baseFileName;

                // For URLs, we can't use is_file(), so we'll set existZip to true if the filename is not empty
                if (!empty($zipFileName)) {
                    error_log("Using URL path as fallback: " . $urlPath);
                    $zipFilePath = $urlPath;
                    $existZip = true;
                }
            }
        }

        /* ───── 7. Render ───── */
        return $this->render('admin/detalle-factura.html.twig', [
            'venta' => $venta,
            'facturas' => $factura,        // datos encabezado factura
            'productos' => $productos,
            'pagos' => $pagos,
            'ventaFacturas' => $ventaFacturas,
            'zipFileName' => $zipFileName,
            'zipFilePath' => $zipFilePath,
            'existZip' => $existZip,       // ← ahora disponible en Twig
            'admin' => $this->admin     // Pasar el objeto admin para generar URLs
        ]);
    }

    /**
     * Action to download a ZIP file
     *
     * @param string $clienteId The client ID directory
     * @param string $filename The name of the ZIP file to download
     * @return BinaryFileResponse
     */
    public function downloadZipAction(string $clienteId, string $filename): BinaryFileResponse
    {
        $facturasDir = $this->getParameter('uploads') . DIRECTORY_SEPARATOR . 'zipFActuras';

        // First try the original path structure with client ID subdirectory
        $filePath = $facturasDir . DIRECTORY_SEPARATOR . $clienteId . DIRECTORY_SEPARATOR . $filename;

        // If file doesn't exist in the original path, try alternative paths
        if (!is_file($filePath)) {
            // Try directly in zipFacturas directory (without client ID subdirectory)
            $directPath = $facturasDir . DIRECTORY_SEPARATOR . $filename;
            if (is_file($directPath)) {
                $filePath = $directPath;
            } else {
                // Try with the format from the URL: "factura[id]-[hash].zip"
                $alternativePath = $facturasDir . DIRECTORY_SEPARATOR . "factura" . $clienteId . "-" . $filename;
                if (is_file($alternativePath)) {
                    $filePath = $alternativePath;
                } else {
                    // If none of the paths work, check if the full path was provided in filename
                    if (strpos($filename, 'factura') === 0 && is_file($facturasDir . DIRECTORY_SEPARATOR . $filename)) {
                        $filePath = $facturasDir . DIRECTORY_SEPARATOR . $filename;
                    } else {
                        // If still not found, try the authZip parameter path
                        $authZipPath = $this->getParameter('authZip') . DIRECTORY_SEPARATOR . $clienteId . DIRECTORY_SEPARATOR . $filename;
                        if (is_file($authZipPath)) {
                            $filePath = $authZipPath;
                        } else {
                            throw $this->createNotFoundException(
                                sprintf('El archivo "%s" no existe en ninguna de las rutas verificadas', $filename)
                            );
                        }
                    }
                }
            }
        }

        return (new BinaryFileResponse($filePath))
            ->setContentDisposition(
                ResponseHeaderBag::DISPOSITION_ATTACHMENT,
                $filename
            );
    }

    /**
     * @param $id
     */
    public function detalleFacturagrupoAction($id)
    {
        //entra un idventafactura
        $object = $this->admin->getSubject();
        $zipFilePath = null;

        $idventa = null;

        if (!$object) {
            throw new NotFoundHttpException(sprintf('unable to find the object with id: %s', $id));
        }

        $em = $this->getDoctrine()->getManager();

        $paytype = $em->getRepository(Paymenttype::class)->findOneBy(array('name' => "Convenio")) ?? new Paymenttype();
        $paytype->setName("Convenio");
        $em->persist($paytype);
        $em->flush();


        $query = $em->createQuery(
            'SELECT COALESCE(un.nombre, \'sin unidad\')  as u_n, v.idventa, v.folio, v.authscan, SUM(pay.monto)as pagado,
            cl.numeroempleado as cl_num, CONCAT(cl.nombre, \' \' , cl.apellidopaterno, \' \' , cl.apellidomaterno) as cl_n, cl2.beneficiarytype, cl.numeroempleado,
            cl2.numeroempleado as cl2_num, CONCAT(cl2.nombre, \' \' , cl2.apellidopaterno, \' \' , cl2.apellidomaterno) as cl2_n, cl2.numeroempleado
            FROM App\Entity\Venta v 
            INNER JOIN v.clienteIdcliente cl
            LEFT JOIN App\Entity\Beneficiarioventa bv With bv.ventaIdventa = v.idventa
            LEFT JOIN bv.clienteIdcliente cl2
            LEFT JOIN cl.unidadIdunidad un
            INNER JOIN v.authstageIdauthstage aus
            INNER JOIN App\Entity\Pago pay With pay.ventaIdventa = v.idventa
            WHERE v.status = 1 AND aus.stageorder >= 5 AND v.ventagroupIdventagroup =:groupfactura 
            AND pay.paymenttypeIdpaymenttype = :paytype AND pay.ventaIdventa = v.idventa AND pay.status = 1
            GROUP BY v.idventa ORDER BY v.pagado 
            '
        )->setParameter('paytype', $paytype)->setParameter('groupfactura', $id);

        $values = $query->getResult();

        $autorizaciones = [];
        foreach ($values as $value) {
            $unidad = $value['u_n'];

            $autorizaciones[$unidad][] = $value;

        }


        $Ventagroup = $em->getRepository(Ventagroup::class)->find($id);
        $ventaFacturaObj = $Ventagroup->getVentafacturaIdventafactura();
        $zipFileName = null;
        $zipFilePath = null;
        $existZip = false;

        // Ahora, si $ventaFacturaObj es un objeto, puedes acceder a sus métodos.
        if ($ventaFacturaObj && $ventaFacturaObj->getNombrezip()) {
            $zipFileName = $ventaFacturaObj->getNombrezip(); // Accedes al nombre del archivo ZIP.

            // Debug information
            error_log("detalleFacturagrupoAction - ZIP File Name: " . $zipFileName);

            // Get directory paths
            $facturasDir = $this->getParameter('uploads') . DIRECTORY_SEPARATOR . 'zipFActuras';
            $authZipDir = $this->getParameter('authZip');

            error_log("detalleFacturagrupoAction - Facturas Dir: " . $facturasDir);
            error_log("detalleFacturagrupoAction - Auth ZIP Dir: " . $authZipDir);

            // Try multiple possible paths for the ZIP file
            $possiblePaths = [
                // Original path using authZip parameter
                $authZipDir . DIRECTORY_SEPARATOR . $Ventagroup->getIdventagroup() . DIRECTORY_SEPARATOR . $zipFileName,

                // Try in uploads/zipFacturas directory
                $facturasDir . DIRECTORY_SEPARATOR . $zipFileName,

                // If zipFileName contains a client ID (e.g., "clientId/filename.zip")
                $facturasDir . DIRECTORY_SEPARATOR . str_replace('/', DIRECTORY_SEPARATOR, $zipFileName),

                // Try directly in zipFacturas directory (for files like "factura114885-667da3c69127b.zip")
                $facturasDir . DIRECTORY_SEPARATOR . basename($zipFileName),

                // Try with the URL format from the issue description
                $facturasDir . DIRECTORY_SEPARATOR . "factura" . $id . "-" . basename($zipFileName)
            ];

            // Debug each path
            foreach ($possiblePaths as $index => $path) {
                error_log("detalleFacturagrupoAction - Path " . $index . ": " . $path . " - Exists: " . (file_exists($path) ? "Yes" : "No"));
            }

            // Check each possible path
            foreach ($possiblePaths as $path) {
                if (file_exists($path)) {
                    $zipFilePath = $path;
                    $existZip = true;
                    error_log("detalleFacturagrupoAction - Found ZIP file at: " . $path);
                    break;
                }
            }

            // If still not found, check if the file exists in the URL format
            if (!$existZip) {
                // 1. Aislamos el nombre base del archivo sin rutas previas
                $zipName = basename($zipFileName);

                // 2. Eliminamos parámetros de consulta y fragmentos (si existen)
                $zipName = strtok($zipName, '?'); // Elimina query strings
                $zipName = strtok($zipName, '#'); // Elimina fragmentos URL

                // 3. Extraemos el nombre base sin extensión
                $baseName = pathinfo($zipName, PATHINFO_FILENAME);

                // 4. Forzamos la extensión .zip
                $zipName = $baseName . '.zip';

                // 5. Construimos la URL definitiva
                $urlPath = sprintf(
                    'https://sistema.optimoopticas.mx/uploads/zipFacturas/%s',
                    $zipName
                );

                $existZip = true;
                $zipFilePath = $urlPath;
                error_log("detalleFacturagrupoAction - Using URL path as fallback: " . $urlPath);
            }

        }


        return $this->render(
            'admin/paymentfile.html.twig',
            [
                "object" => $object,
                "ventas" => $values,
                "zipFilePath" => $zipFilePath,
                "existZip" => $existZip
            ]
        );
    }

    /**
     * @Route("/descargar-constancia/{id}", name="descargar_constancia")
     */
    public function descargarConstancia($id)
    {
        $em = $this->getDoctrine()->getManager();

        // Obtener la entidad Clientefacturadatos por su ID
        $clienteFactura = $em->getRepository('App\Entity\Clientefacturadatos')->find($id);

        if (!$clienteFactura) {
            throw $this->createNotFoundException('No se encontró el archivo.');
        }

        $pathToFile = $this->getParameter('carpetaConstancias') . '/' . $clienteFactura->getConstanciasituacionfiscal();

        $content = file_get_contents($pathToFile);

        $response = new Response($content);
        $response->headers->set('Content-Type', 'application/pdf');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . basename($pathToFile) . '"');

        return $response;
    }

    /**
     * @Route("/descargar/documento/venta/{idVenta}", name="descargar_documento_venta")
     */
    public function descargarDocumentoVenta($idVenta)
    {
        $documento = $this->obtenerUltimoDocumentoVentaAutorizacionUAM($idVenta);

        // Asumiendo que $documento->getRuta() te da la ruta completa al archivo en el servidor
        return $this->file($documento->getRuta());
    }


    /**
     * @Route("/obtener/ultimo/documento/venta/uam/{id}", name="obtener_documento_uam")
     */
    public function obtenerUltimoDocumentoVentaAutorizacionUAM($id)
    {
        $query = $this->entityManager->createQuery(
            'SELECT dv
        FROM App\Entity\Documentoventa dv
        WHERE dv.ventaIdventa = :idVenta AND dv.tipoDocumento = :tipoDocumento AND dv.status = :status
        ORDER BY dv.creacion DESC'
        )
            ->setParameters([
                'idVenta' => $id,
                'tipoDocumento' => 'Autorización UAM',
                'status' => '1'
            ])
            ->setMaxResults(1);

        $documentoVenta = $query->getOneOrNullResult();

        return $this->render(
            'dashboard_flujo_expediente/list-action-descargardocumento.html.twig ',
            ['documentoVenta' => $documentoVenta]
        );
    }

    public function cambiarPagoAction($id)
    {
        $object = $this->admin->getSubject();
        $em = $this->getDoctrine()->getManager();

        // Obtenemos los tipos de pago disponibles
        $query = $em->createQuery(
            'SELECT pt.name, pt.idpaymenttype
         FROM App\Entity\PaymentType pt
         WHERE pt.status = :status'
        )->setParameters(["status" => 1]);

        $tiposPago = $query->getResult();

        // Verificamos que el objeto existe y obtenemos el monto
        if (!$object) {
            throw new NotFoundHttpException(sprintf('Unable to find the object with id: %s', $id));
        }

        $cantidad = $object->getMonto();

        return $this->render('admin/cambiar-pago.html.twig', [
            'idpago' => $id,
            'tiposPago' => $tiposPago,
            'cantidad' => $cantidad, // Pasamos el monto actual a la vista
            'listUrl' => $this->admin->generateUrl('list')
        ]);
    }

    public function ligarSucursalesAction($id): Response
    {
        $em = $this->getDoctrine()->getManager();

        $documento = $em->getRepository(Documentos::class)->find($id);
        if (!$documento) {
            throw $this->createNotFoundException('No se encontró el documento con ID: ' . $id);
        }

        $query = $em->createQuery(
            'SELECT s
        FROM App\Entity\Sucursal s
        WHERE s.status = :status
        ORDER BY s.nombre ASC
        '
        )->setParameter('status', 1);

        $sucursalesDisponibles = $query->getResult();

        // Obtener las sucursales ya asociadas al documento
        $sucursalesAsociadas = $em->getRepository(Documentosucursal::class)->findBy(['documentosIddocumentos' => $documento]);

        // Crear un array de IDs de las sucursales asociadas
        $sucursalesAsociadasIds = array_map(function ($documentoSucursal) {
            return $documentoSucursal->getSucursalIdsucursal()->getIdsucursal();
        }, $sucursalesAsociadas);

        return $this->render('admin/asignar-sucursal.html.twig', [
            'iddocumentos' => $id,
            'sucursalesDisponibles' => $sucursalesDisponibles,
            'sucursalesAsociadasIds' => $sucursalesAsociadasIds,
        ]);
    }





    protected function configureRoutes(RouteCollection $collection)
    {
        $collection
            ->add('detalleVenta', $this->getRouterIdParameter() . '/detalleVenta');

        $collection
            ->add('detalleOrdenSalida', $this->getRouterIdParameter() . '/detalleOrdenSalida');

        $collection
            ->add('detalleTraspasoAlmacen', $this->getRouterIdParameter() . '/detalleTraspasoAlmacen');

        $collection
            ->add('detalleStock', $this->getRouterIdParameter() . '/detalleStock');

        $collection
            ->add('cancelarVenta', $this->getRouterIdParameter() . '/cancelarVenta');

        $collection
            ->add('cambiarContrasena', $this->getRouterIdParameter() . '/cambiarContrasena');
        $collection
            ->add('detalleApartado', $this->getRouterIdParameter() . '/detalleApartado');

        $collection
            ->add('detalleFactura', $this->getRouterIdParameter() . '/detalleFactura');

        $collection
            ->add('detalleFacturagrupo', $this->getRouterIdParameter() . '/detalleFacturagrupo');

        $collection
            ->add('cambiarPago', $this->getRouterIdParameter() . '/cambiarPago');

        $collection
            ->add('ligarSucursales', $this->getRouterIdParameter() . '/ligarSucursales');

        $collection
            ->add('sendEmail', $this->getRouterIdParameter() . '/sendEmail');
    }
}
