<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use App\Entity\Usuario;
use App\Entity\Event;
use App\Entity\Eventtype;
use DateTimeZone;
use DateTime;

/**
 * @Route("/admin/dashboard")
 */
class DatesCalendarController extends AbstractController
{
    /**
     * @Route("/dates/calendar", name="app_dates_calendar")
     */
    public function index(): Response
    {
        $em = $this->getDoctrine()->getManager();

        $user = $this->getUser();
        $eventsJson = [];


        $idsucursal = $user->getSucursalIdsucursal()->getIdsucursal();

        $etype = $em->getRepository(Eventtype::class)->findOneBy(array('name' => 'Cita-Cliente-Optimo')) ?? new Eventtype();

        if ($etype == new Eventtype()) {
            $etype
                ->setName('Cita-Cliente-Optimo')
                ->setDescription("Citas en sucursales de Optimo Ópticas");

            $em->persist($etype);
            $em->flush();
        }

        $query = $em->createQuery(
            'SELECT CONCAT(evtype.name, cli.email) as title, ev.idevent,
                DATE_FORMAT(ev.eventdate, \'%Y-%m-%dT%H:%i:%s\') as start,
                DATE_FORMAT(DATE_ADD(ev.eventdate, 30, \'MINUTE\'), \'%Y-%m-%dT%H:%i:%s\') as end,
                CONCAT(evtype.name, \' - cliente: \', cli.nombre, \' \', cli.apellidopaterno, \' \', cli.apellidomaterno,
                \' email: \', COALESCE(cli.email, \'sin email\'), \' telefono: \', COALESCE(cli.telefono, \'sin telefono\')) as description,
                cli.email, cli.telefono
            FROM App\Entity\Event ev
            LEFT JOIN ev.usuarioIdusuario us 
            INNER JOIN ev.sucursalIdsucursal sucursal
            INNER JOIN ev.eventtypeIdeventtype evtype
            INNER JOIN ev.clienteIdcliente cli
            WHERE (sucursal.idsucursal = :idsucursal OR us.idusuario = :idusuario) 
            AND ev.status = :stat AND evtype.name = :evtypename'
        )->setParameters([
            'idusuario' => $user->getIdusuario(),
            'stat' => "1",
            'idsucursal' => $idsucursal,
            'evtypename' => 'Cita-Cliente-Optimo '
        ]);

        $events = $query->getResult();

        // Get the system timezone for Mexico City
        $mexicoCityTimeZone = new DateTimeZone('America/Mexico_City');
        // Get the system timezone for UTC
        $utcTimeZone = new DateTimeZone('UTC');

        // Convert dates to Mexico City timezone
        foreach ($events as &$event) {
            $start = new DateTime($event['start'], $utcTimeZone);
            $start->setTimezone($mexicoCityTimeZone);
            $event['start'] = $start->format('Y-m-d\TH:i:s');

            $end = new DateTime($event['end'], $utcTimeZone);
            $end->setTimezone($mexicoCityTimeZone);
            $event['end'] = $end->format('Y-m-d\TH:i:s');
        }

        // Convert events array to JSON
        $eventsJson = json_encode($events);


        // Render the Twig template and pass the events as JSON
        return $this->render('dates_calendar/index.html.twig', [
            'controller_name' => 'DatesCalendarController',
            'eventsJson' => $eventsJson,
        ]);
    }

    /**
     * @Route("/done/date", name="app_done_date")
     */
    public function setDoneDate(Request $request): JsonResponse
    {

        $em = $this->getDoctrine()->getManager();
        $eventid = $request->get('eventid');

        $event = $em->getRepository(Event::class)->findOneBy(array('idevent' => $eventid));

        $event->setIsdone('1');

        $em->persist($event);
        $em->flush();

        // Render a Twig template
        return new JsonResponse(['msj' => "", "exito" => true]);
    }
}
