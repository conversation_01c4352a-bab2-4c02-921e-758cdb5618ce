<?php

namespace App\Controller;

use App\Entity\Documentos;
use App\Entity\Documentosucursal;
use App\Entity\Flujoexpediente;
use App\Entity\Framecolor;
use App\Entity\Producto;
use App\Entity\Shipmentordenlaboratorio;
use App\Entity\Stockventa;
use App\Entity\Sucursal;
use App\Entity\Tipoventa;
use App\Entity\Cliente;
use App\Entity\Usuario;
use App\Entity\Unidad;
use App\Entity\Stock;
use App\Entity\Sellreference;
use App\Entity\Empresacliente;
use App\Entity\Productotag;
use App\Entity\Flujoexpedienteventa;
use App\Entity\Flowsigneddocuments;
use App\Entity\Paymenttype;
use App\Entity\Pago;
use App\Entity\Stockstate;
use App\Entity\Stockhistory;
use App\Entity\Authstage;
use App\Entity\Framematerial;
use App\Entity\Medida;
use App\Entity\Venta;
use PHPUnit\Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Doctrine\ORM\EntityManagerInterface;
use DateTime;
use App\Service\PagoEraser;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use Symfony\Component\HttpFoundation\JsonResponse;
use App\Service\ExamenVisualService;
use Dompdf\Dompdf;
use Dompdf\Options;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use App\Entity\Stockmovimiento;

/**
 * @Route("/actualizacion")
 */
class ActualizacionController extends AbstractController
{


    private $entityManager;
    private $examenVisualService;

    public function __construct(EntityManagerInterface $entityManager, ExamenVisualService $examenVisualService)
    {
        $this->entityManager = $entityManager;
        $this->examenVisualService = $examenVisualService;
    }

    /**
     * @Route("/tipo-venta", name="app_actualizacion_tipo-venta")
     */
    public function tipoVenta(): Response
    {
        //Poner a las ventas anteriores el tipo de venta porque ya se creo una tabla para configurar"

        //Traer todos los registros de las ventas
        $tiposVentaNuevos = 0;
        $registrosCambiados = 0;
        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
            '   SELECT  v
                FROM App\Entity\Venta v
                WHERE v.status=:status
            '
        )->setParameters(["status" => "1"]);
        $Ventas = $query->getResult();

        foreach ($Ventas as $keyVenta => $Venta) {

            $query = $em->createQuery(
                '   SELECT  tv
                FROM App\Entity\Tipoventa tv
                WHERE tv.nombre=:nombre
            '
            )->setParameters(["nombre" => trim($Venta->getConvenio())]);
            $Tipoventa = $query->getOneOrNullResult();


            if (!$Tipoventa) {
                $tiposVentaNuevos++;
                $Tipoventa = new Tipoventa();
                $Tipoventa->setNombre($Venta->getConvenio());
                $em->persist($Tipoventa);
                $em->flush();
            }
            $Venta->setTipoventaIdtipoventa($Tipoventa);
            $em->persist($Venta);
            $registrosCambiados++;
        }
        $em->flush();

        return $this->render('actualizacion/index.html.twig', [
            'registrosCambiados' => $registrosCambiados,
            'tiposVentaNuevos' => $tiposVentaNuevos,
        ]);
    }


    /**
     * @Route("/tipo-venta", name="app_actualizacion_tipo-venta")
     */
    public function calculosubtotal(): Response
    {
        //Poner a las ventas anteriores el tipo de venta porque ya se creo una tabla para configurar"
        $registrosCambiados = "";
        $tiposVentaNuevos = "";
        //Traer todos los registros de las ventas
        $em = $this->getDoctrine()->getManager();

        //  select * form Venta as v where v.status=1;

        $query = $em->createQuery(
            '   SELECT v
                FROM App\Entity\Venta v
                WHERE v.status=:status
            '
        )->setParameters(["status" => "1"]);
        $Ventas = $query->getResult();

        //por cada venta obtener el campo pagado

        if ($Ventas) {

            foreach ($Ventas as $Venta) {
                $pagado = $Venta->getPagado();
                $total = $Venta->getTotal();
                $iva = $Venta->getIva();
                /*  $pagado=$Venta['pagado'];  
                $total=$Venta['total'];   
                $iva=$Venta['iva'];*/
                echo "<br>folio " . $Venta->getFolio();
                echo "<br>pagado " . $pagado;
                echo "<br>total " . $total;
                echo "<br>iva " . $iva;

                $subtotal = $pagado / 1.16;
                $ivaFinal = $subtotal * 0.16;
                echo "<br>subtotal " . $subtotal;
                echo "<br>ivaFinal " . $ivaFinal;


                $Venta->setTotal($subtotal);
                $Venta->setIva($ivaFinal);
                $em->persist($Venta);
                $registrosCambiados++;
            }
            $em->flush();
        }

        return $this->render('actualizacion/calculo-subtotal.html.twig', [
            'registrosCambiados' => $registrosCambiados

        ]);
    }

    /**
     * @Route("/update-duplicated-clients", name="actualizacion-update-duplicated-clients")
     */
    public function updateSuplicatedClients(): Response
    {


        return $this->render('actualizacion/actualizacion-update-duplicated-clients.html.twig', []);
    }

    /**
     * @Route("/contar-duplicados", name="app_contar_duplicados")
     */
    public function contarDuplicados(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();

        $phone = $request->get("phone");

        $totalDuplicados = 0;

        $query1 = $em->createQuery(
            'SELECT c
            FROM App\Entity\Cliente c
            WHERE c.telefono = :telefono AND c.status = 1 order by c.idcliente DESC'
        )->setParameters(['telefono' => $phone]);
        $Clientes = $query1->getResult();

        if ($Clientes) {
            $ClienteUltimoRegistro = $Clientes[0];
            unset($Clientes[0]);

            foreach ($Clientes as $Cliente) {
                $query2 = $em->createQuery(
                    'SELECT v
                    FROM App\Entity\Venta v
                    inner join v.clienteIdcliente c
                    WHERE c.idcliente = :idcliente'
                )->setParameters(['idcliente' => $Cliente->getIdCliente()]);
                $Ventas = $query2->getResult();

                if ($Ventas) {
                    foreach ($Ventas as $Venta) {


                        $Venta->setClienteIdcliente($ClienteUltimoRegistro);


                        $em->persist($Venta);

                        $totalDuplicados++;
                    }
                }

                $Cliente->setStatus('0');
                $em->persist($Cliente);
            }
            $ClienteUltimoRegistro->setStatus("1");
            $em->persist($ClienteUltimoRegistro);
        } else {
            $msj = "Cliente con teléfono " . $phone . " no tiene resultados";
        }

        $em->flush();


        // Calcular el número total de registros duplicados

        // Renderizar una plantilla con el contador de registros duplicados y los números repetidos
        /*return $this->render('actualizacion/index3.html.twig', [
            'totalDuplicados' => $totalDuplicados,
            'numerosRepetidos' => $numerosRepetidos,
        ]);*/

        return new Response('Se actualizó: ' . $totalDuplicados . " ventas");
    }

    /**
     * @Route("/actualizar-cliente", name="app_actualizacion_cliente")
     */
    public function Cliente(): Response
    {
        // Inicializar el contador de registros cambiados en 0 
        $registrosCambiados = 0;
        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT c FROM App\Entity\Cliente c'
        );
        $clientes = $query->getResult();


        // Recorrer cada cliente y actualizar los campos de apellido y nombre

        foreach ($clientes as $cliente) {
            // Dividir el campo nombre en apellido paterno, apellido materno y nombre
            $nombreCompleto = explode(" ", $cliente->getNombre());

            // Verificar que el nombre tenga al menos 3 partes
            /**
             * La función COUNT cuenta las filas definidas por la expresión.
             * La función COUNT tiene tres variaciones. COUNT ( * ) cuenta todas las filas en la tabla destino, incluya o
             * no valores nulos. COUNT ( expression [expresión] ) calcula el número de filas con valores no NULL de una
             * determinada columna o expresión. COUNT ( DISTINCT expression [expresión] ) calcula el número de valores
             * no NULL diferentes de una columna o expresión.
             * La función COUNT admite todos los tipos de datos de argumentos.
             */

            if (count($nombreCompleto) >= 3) {
                $apellidoPaterno = $nombreCompleto[0];
                $apellidoMaterno = $nombreCompleto[1];
                $nombre = implode(" ", array_slice($nombreCompleto, 2));

                // Actualizar los campos del cliente
                $cliente->setApellidoPaterno($apellidoPaterno);
                $cliente->setApellidoMaterno($apellidoMaterno);
                $cliente->setNombre($nombre);

                // Persistir el cliente para actualizarlo
                $em->persist($cliente);
                $registrosCambiados++;
            }
        }
        // Guardar los cambios en la base de datos
        $em->flush();

        // Renderizar una plantilla con el contador de registros cambiados
        return $this->render('actualizacion/index2.html.twig', [
            'registrosCambiados' => $registrosCambiados,
        ]);
    }

    /**
     * @Route("/mostrar_stock_sucursal_subcategoria", name="mostrar_stock_sucursal_subcategoria")
     */
    public function mostrarStockSucursalSubcategoria(EntityManagerInterface $em): Response
    {
        $parametros = [];  // Aquí deberías colocar tus parámetros
        $parametros1 = []; // Aquí deberías colocar tus parámetros1

        $query = $em->createQuery(
            'SELECT SUM(sv.cantidad) as cantidad, sc.nombre as nombre, suc.nombre as sucursal, v.fechaventa as fecha
         FROM App\Entity\Stockventa sv
         inner join sv.stockIdstock s
         INNER JOIN  s.sucursalIdsucursal suc
         INNER JOIN  s.productoIdproducto p
         INNER JOIN  p.marcaIdmarca m 
         INNER JOIN  p.categoriaIdcategoria sc
         INNER JOIN sv.ventaIdventa v
         where sv.status =:status and p.tipoproducto=:tipoproducto AND sv.status = 1 AND v.fechaventa >= :fechaInicio AND v.fechaventa <= :fechaFin
         group by suc.idsucursal, sc.idcategoria 
         order by v.fechaventa ASC, cantidad DESC'
        )->setParameters(array_merge($parametros, ['fechaInicio' => $parametros1['fechaInicio'], 'fechaFin' => $parametros1['fechaFin']]));

        $stockSucursalSubcategoria = $query->getResult();

        return $this->render('actualizac/stockSucursalSubcategoria.html.twig', [
            'stock' => $stockSucursalSubcategoria
        ]);
    }


    /**
     * @Route("/actualizar_venta", name="actualizar_venta")
     */
    public function actualizarVenta(EntityManagerInterface $em): Response
    {
        // Inicializa los contadores
        $cantidadActualizada = 0;
        $fechaVentaActualizada = 0;

        $query = $em->createQuery(
            'SELECT v
        FROM App\Entity\Venta v
        WHERE v.cotizacion = 0 AND v.fechaventa IS NULL 
        ORDER BY v.idventa DESC'
        );
        $ventas = $query->getResult();

        foreach ($ventas as $venta) {
            // Asigna el valor de fechaActualizacion a fechaVenta
            if ($venta->getFechaActualizacion()) $venta->setFechaVenta($venta->getFechaActualizacion());
            else $venta->setFechaVenta($venta->getFecha());

            $fechaVentaActualizada++;  // Incrementa el contador

            // Marca la entidad para persistencia
            $em->persist($venta);

            // Obtener StockVentas relacionados con la venta

            $queryStock = $em->createQuery(
                'SELECT sv
            FROM App\Entity\Stockventa sv
            WHERE sv.ventaIdventa = :venta AND (sv.cantidad = 0 OR sv.cantidad IS NULL)'
            )->setParameter('venta', $venta);

            $stockVentas = $queryStock->getResult();

            // Para cada StockVenta, establecer la cantidad en '1'
            foreach ($stockVentas as $stockVenta) {
                $stockVenta->setCantidad(1);
                $cantidadActualizada++;  // Incrementa el contador

                // Marcar la entidad StockVenta para persistencia
                $em->persist($stockVenta);
            }
        }

        // Escribe los cambios en la base de datos
        $em->flush();

        return $this->render('actualizacion/actualizarVenta.html.twig', [
            'ventas' => $ventas,
            'fechaVentaActualizada' => $fechaVentaActualizada,
            'cantidadActualizada' => $cantidadActualizada
        ]);
    }


    /**
     * @Route("/actualizar_masivo", name="actualizar_masivo")
     */
    public function CambioMasivo(EntityManagerInterface $em): Response
    {
        $query = $em->createQuery(
            'SELECT p
        FROM App\Entity\Producto p
        WHERE p.masivounico = 0 OR p.masivounico IS NULL'
        );

        $masivosUnicos = $query->getResult();

        foreach ($masivosUnicos as $masivoUnico) {
            $masivoUnico->setMasivounico(1);
            $em->persist($masivoUnico);
        }

        $em->flush();


        // Añade una respuesta o redirecciona según sea necesario
        return new Response('Productos Masivo Unico = 1 todos');
    }

    /**
     * @Route("/actualizar_fecha_pagos", name="actualizar_fecha_pagos")
     */
    public function ActualizarFechaPagos(EntityManagerInterface $em): Response
    {

        $query = $em->createQuery(
            'SELECT p
        FROM App\Entity\Pago p
        INNER JOIN p.ventaIdventa v
        INNER JOIN v.tipoventaIdtipoventa tv
        WHERE v.cotizacion = 0
        ORDER BY v.idventa DESC
        '
        );
        $pagos = $query->getResult();

        foreach ($pagos as $pago) {
            $pago->setFecha($pago->getVentaIdventa()->getFecha());
            $em->persist($pago);
        }

        $cambiosPagos = count($pagos);

        $em->flush();

        // Añade una respuesta o redirecciona según sea necesario
        return new Response('Se actualizó: ' . $cambiosPagos . " productos");
    }

    /**
     * @Route("/actualizar_revisar_sku", name="actualizar_revisar_sku")
     */
    public function RevisarSKU(EntityManagerInterface $em): Response
    {

        $query = $em->createQuery(
            'SELECT s.codigobarras as codigobarras, s.idstock as idstock, COUNT(s.codigobarras) as veces
        FROM App\Entity\Stock s
        WHERE s.status = 1
        GROUP BY s.codigobarras, s.idstock 
        HAVING COUNT(s.codigobarras) >= 2
        ORDER BY s.idstock ASC'
        );
        $codigobarrasRepetidos = $query->getResult();

        $cuenta = count($codigobarrasRepetidos);


        return $this->render('actualizacion/stockrepetidos.html.twig', [
            'codigobarrasRepetidos' => $codigobarrasRepetidos,
            'cuenta' => $cuenta,
        ]);
    }

    /**
     * @Route("/actualizar_responsable_pagos", name="actualizar_responsable_pagos")
     */
    public function ActualizarResponsablePagos(EntityManagerInterface $em): Response
    {

        $query = $em->createQuery(
            'SELECT p
        FROM App\Entity\Pago p
        INNER JOIN p.ventaIdventa v
        
        ORDER BY p.idpago DESC'
        );
        $pagos = $query->getResult();

        $usuario = $em->getRepository(Usuario::class)->findOneBy(array('idusuario' => 1));

        foreach ($pagos as $pago) {

            //if ($pago->getVentaIdventa()->getIdventa() ) 
            if ($pago->getVentaIdventa()->getIdventa()) $pago->setUsuarioCobro($pago->getVentaIdventa()->getGerente());
            $em->persist($pago);
        }
        $em->flush();
        $cambiosPagos = count($pagos);


        return new Response('Se actualizó: ' . $cambiosPagos . " pagos");
    }

    /**
     * @Route("/actualizar_ventas_liquidadas", name="actualizar_ventas_liquidadas")
     */
    public function ActualizarVentasLiquidadas(EntityManagerInterface $em): Response
    {

        $query = $em->createQuery(
            'SELECT  v
            FROM App\Entity\Venta v
            where v.status=:status and v.cotizacion = 0 order by v.idventa ASC
        '
        )->setParameters(['status' => '1']);

        $ventas = $query->getResult();

        $cambios = 0;
        $cambioscredito = 0;

        foreach ($ventas as $key => $Venta) {


            $query = $em->createQuery(

                'SELECT p
            FROM App\Entity\Pago p
            inner join  p.ventaIdventa v
            where p.status=:status and v.idventa=:idventa
            '

            )->setParameters(['status' => '1', 'idventa' => $Venta->getIdventa()]);

            $pagos = $query->getResult();

            $totalPagado = 0;

            $Venta->setCredito('0');
            $Venta->setLiquidada('0');

            if ($Venta->getTipoventaIdtipoventa()->getIdtipoventa() == 1753) $Venta->setCredito('1');

            foreach ($pagos as $key => $Pago) {

                if ($Pago->getTipopago() == "venta a crédito") {
                    $Pago->setStatus('0');
                    $Venta->setCredito('1');
                    $em->persist($Pago);
                    $cambioscredito++;
                } else $totalPagado += $Pago->getMonto();
            }

            // COMENTADO: Los triggers de BD ya manejan automáticamente el estado de liquidación
            // if ($totalPagado >= $Venta->getPagado()) {
            //     $Venta->setLiquidada('1');
            //     $cambios++;
            // }

            // COMENTADO: Los triggers actualizan automáticamente
            // $em->persist($Venta);
        }

        $em->flush();


        return new Response('Se actualizó: ' . $cambios . " ventas y " . $cambioscredito . " ventas a crédito.");
    }

    /**
     * @Route("/actualizar_orden_productos", name="actualizar_orden_productos")
     */
    public function ActualizarOrdenProductos(EntityManagerInterface $em): Response
    {

        $query = $em->createQuery(
            'SELECT p
        FROM App\Entity\Producto p     
        ORDER BY p.idproducto DESC'
        );
        $productos = $query->getResult();


        $cambiosProductos = 0;

        foreach ($productos as $key => $Producto) {

            if ($Producto->getTipoproducto() == '1') {
                $Producto->setOrdenlaboratorio('1');
                $cambiosProductos++;
            } else if ($Producto->getTipoproducto() == '2') {
                $Producto->setOrdenlaboratorio('0');
                $cambiosProductos++;
            }

            $em->persist($Producto);
        }

        $em->flush();


        return new Response('Se actualizó: ' . $cambiosProductos . " productos");
    }

    /**
     * @Route("/update-beneficiaries", name="actualizacion-update-beneficiaries")
     */
    public function updateBeneficiaries(EntityManagerInterface $em): Response
    {

        $msg = "";

        try {

            $query = $em->createQuery(
                'SELECT b
            FROM App\Entity\Beneficiario b
            WHERE b.status =:status
            '
            )->setParameters(['status' => '1']);
            $beneficiaries = $query->getResult();

            $beneficiaryChanges = 0;

            foreach ($beneficiaries as $key => $Beneficiary) {

                $Client = $Beneficiary->getClienteIdcliente();

                $ClienteTemp = new Cliente();

                $name = $Beneficiary->getNombre();

                $nameParts = $this->separeClientName($name);

                if (count($nameParts) == 3) {

                    $ClienteTemp->setNombre($nameParts[2]);
                    $ClienteTemp->setApellidopaterno($nameParts[1]);
                    $ClienteTemp->setApellidomaterno($nameParts[0]);
                } elseif (count($nameParts) == 2) {

                    $ClienteTemp->setNombre($nameParts[1]);
                    $ClienteTemp->setApellidopaterno($nameParts[0]);
                } else {
                    $ClienteTemp->setNombre($nameParts[0]);
                }

                if ($Beneficiary->getFechanacimiento()) $ClienteTemp->setFechanacimiento($Beneficiary->getFechanacimiento());

                if ($Beneficiary->getSexo()) {
                    if ($Beneficiary->getSexo() == 'F') $ClienteTemp->setGenero("Femenino");
                    else if ($Beneficiary->getSexo() == 'M') $ClienteTemp->setGenero("Maculino");
                }

                $ClienteTemp->setBeneficiarytype($Beneficiary->getTipo());

                if ($Beneficiary->getUnidad()) {
                    $Unity = $em->getRepository(Unidad::class)->findOneBy(array('nombre' => $Beneficiary->getUnidad()));

                    if ($Unity) $ClienteTemp->setUnidadIdunidad($Unity);
                    else {

                        $unityNames = explode(" ", $Beneficiary->getUnidad());

                        if (count($unityNames) >= 2) {

                            $query = $em->createQuery(
                                'SELECT u
                            FROM App\Entity\Unidad u
                            WHERE u.nombre LIKE :unityName
                            '
                            )->setParameters(['unityName' => "'%" . $unityNames[1] . "%'"]);
                            $UnityCheck = $query->getOneOrNullResult();
                            $ClienteTemp->setUnidadIdunidad($UnityCheck);
                        }
                    }
                }

                if ($Beneficiary->getClienteIdcliente()) {
                    $ClienteTemp->setHolder($Client);
                    $ClientEnterprise = $em->getRepository(Empresacliente::class)->findOneBy(array('idempresacliente' => 1));
                    $ClienteTemp->setEmpresaclienteIdempresacliente($ClientEnterprise);
                    $ClienteTemp->setTelefono($Client->getTelefono());
                    $ClienteTemp->setEmail($Client->getEmail());
                    $em->persist($ClienteTemp);
                    $beneficiaryChanges++;
                }
            }

            $em->flush();
        } catch (\Exception $e) {
            $beneficiaryChanges++;
            $msg . $e->getMessage();
        }

        return new Response('Se actualizó: ' . $beneficiaryChanges . " beneficiarios " . $msg);
    }

    /**
     * @Route("/update-sell-references", name="actualizacion-update-sell-references")
     */
    public function updateSellReferences(EntityManagerInterface $em): Response
    {

        $query = $em->createQuery(
            'SELECT c 
        FROM App\Entity\Cliente c
        WHERE c.status =:status
        '
        )->setParameters(["status" => "1"]);
        $clients = $query->getResult();

        $clientChanges = 0;

        foreach ($clients as $key => $ClientTemp) {

            if ($ClientTemp->getComonosconocio()) {

                $query = $em->createQuery(
                    'SELECT sr
                FROM App\Entity\Sellreference sr
                WHERE sr.name LIKE :srname
                '
                )->setParameters(['srname' => $ClientTemp->getComonosconocio()]);

                $SellReferenceTemp = $query->getOneOrNullResult();

                if ($SellReferenceTemp) {
                    $ClientTemp->setSellreferenceIdsellreference($SellReferenceTemp);
                    $clientChanges++;
                    $em->persist($ClientTemp);
                }
            }
        }

        $em->flush();


        return new Response('Se actualizó: ' . $clientChanges . " clientes ");
    }

    public function separeClientName(string $name)
    {

        $nameParts = explode(" ", $name);

        $links = ['de', 'del', 'la', 'los', 'las', 'DE', 'DEL', 'LA', 'LOS', 'LAS'];

        $resultName = array();

        $tempName = "";

        for ($i = 0; $i < count($nameParts); $i++) {


            if ($i == count($nameParts) - 1) {
                $tempName .= $nameParts[$i];
                array_push($resultName, $tempName);
            } elseif (in_array($nameParts[$i], $links)) {
                $tempName .= $nameParts[$i] . ' ';
            } elseif (count($resultName) < 2) {
                $tempName .= $nameParts[$i];
                array_push($resultName, $tempName);
                $tempName = "";
            } else {
                $tempName .= $nameParts[$i] . ' ';
            }
        }


        return $resultName;
    }

    /**
     * @Route("/update-separated-products", name="actualizacion-update-separated-products")
     */
    public function updateSeparatedProducts(): Response
    {
        // Inicializar el contador de registros cambiados en 0 
        $stockVentaChanges = 0;
        $test = '';

        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT sv FROM App\Entity\Stockventa sv
             INNER JOIN sv.ventaIdventa v
             INNER JOIN sv.stockIdstock s
             INNER JOIN s.productoIdproducto p
             WHERE sv.status =:status AND v.cotizacion=:quotation AND sv.cantidad > 0
             AND p.tipoproducto =:productType AND sv.estaapartado =:separated AND v.status=:status
            '
        )->setParameters(["status" => '1', "quotation" => '1', 'productType' => '1', 'separated' => '1']);
        $stockVentas = $query->getResult();

        foreach ($stockVentas as $Stockventa) {
            $TempStock = $Stockventa->getStockIdstock();

            if ($TempStock->getProductoIdproducto()->getMasivounico() == '1') {
                if ($TempStock->getCantidad() == 0) {
                    $TempStock->setApartados(1);
                    $stockVentaChanges++;
                    $test .= '/' . '1-' . $TempStock->getIdstock();
                }
            } else if ($TempStock->getProductoIdproducto()->getMasivounico() == '2') {
                $TempStock->setApartados($TempStock->getApartados() + $Stockventa->getCantidad());
                $stockVentaChanges++;
                $test .= '/' . '2-' . $TempStock->getIdstock();
            }

            $em->persist($TempStock);
        }

        $em->flush();

        // Renderizar una plantilla con el contador de registros cambiados
        return new Response('Se actualizó: ' . $stockVentaChanges . " stockVentas ");
        //return new Response($test);
    }

    /**
     * @Route("/update-mermas", name="actualizacion-update-mermas")
     */
    public function updateMermas(): Response
    {
        // Inicializar el contador de registros cambiados en 0 
        $mermaChanges = 0;

        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT m FROM App\Entity\Merma m'
        );
        $mermas = $query->getResult();

        $query = $em->createQuery(
            'SELECT dc
            FROM App\Entity\Departurecause dc
            WHERE dc.name LIKE :dcname
            '
        )->setParameters(['dcname' => "Robo"]);
        $DeparturecauseRobo = $query->getOneOrNullResult();

        $query = $em->createQuery(
            'SELECT dc
            FROM App\Entity\Departurecause dc
            WHERE dc.name LIKE :dcname
            '
        )->setParameters(['dcname' => "Errores"]);
        $DeparturecauseErrores = $query->getOneOrNullResult();

        $query = $em->createQuery(
            'SELECT dc
            FROM App\Entity\Departurecause dc
            WHERE dc.name LIKE :dcname
            '
        )->setParameters(['dcname' => "Extravío"]);
        $DeparturecauseExtravio = $query->getOneOrNullResult();

        $types = ['0' => $DeparturecauseRobo, '1' => $DeparturecauseErrores, '2' => $DeparturecauseExtravio];

        foreach ($mermas as $merma) {

            $tempDeparturecause = $types[$merma->getTipo()];

            $merma->setDeparturecauseIddeparturecause($tempDeparturecause);
            $merma->setFolio($tempDeparturecause->getDeparturetypeIddeparturetype()->getPrefix() . '-' . $merma->getIdmerma());
            $em->persist($merma);
            $mermaChanges++;
        }

        $em->flush();

        return new Response('Se actualizó: ' . $mermaChanges . " mermas ");
    }

    /**
     * @Route("/update-linea-products", name="actualizacion-update-linea-products")
     */
    public function updateLineaProducts(): Response
    {
        $productChanges = 0;

        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT p FROM App\Entity\Producto p
             INNER JOIN p.marcaIdmarca m
             WHERE p.status=:status AND m.nombre LIKE :brand
            '
        )->setParameters(["status" => '1', 'brand' => '%LINEA%']);
        $products = $query->getResult();

        $query = $em->createQuery(
            'SELECT t FROM App\Entity\Tag t
             WHERE t.status=:status AND t.name LIKE :linea
            '
        )->setParameters(["status" => '1', 'linea' => '%LINEA%']);
        $TagLinea = $query->getOneOrNullResult();

        foreach ($products as $Product) {

            $existProductoTag = $em->getRepository(Productotag::class)->findOneBy(array('tagIdtag' => $TagLinea, 'productoIdproducto' => $Product));
            if (!$existProductoTag) {
                $Productotag = new Productotag();
                $Productotag->setProductoIdproducto($Product);
                $Productotag->setTagIdtag($TagLinea);

                $em->persist($Productotag);

                $productChanges++;
            }
        }

        $em->flush();

        return new Response('Se actualizó: ' . $productChanges . " productos ");
    }

    /**
     * @Route("/update-flujo-ventas", name="actualizacion-update-flujo-ventas")
     */
    public function updateFlujoVentas(EntityManagerInterface $em): Response
    {
        $query = $em->createQuery(
            'SELECT fe
            FROM App\Entity\Flujoexpediente fe
            WHERE fe.ventaIdventa IS NOT NULL
            '
        );
        $flows = $query->getResult();

        $countFlows = 0;

        foreach ($flows as $Flow) {
            $tempFlowSale = new Flujoexpedienteventa();
            $tempFlowSale->setFlujoexpedienteIdflujoexpediente($Flow);
            $tempFlowSale->setVentaIdventa($Flow->getVentaIdventa());
            $em->persist($tempFlowSale);
            $countFlows++;
        }

        $em->flush();


        // Añade una respuesta o redirecciona según sea necesario
        return new Response('Se actualizó: ' . $countFlows . " flujos de expediente");
    }

    /**
     * @Route("/update-flow-locations", name="actualizacion-update-flow-locations")
     */
    public function updateFlowLocations(EntityManagerInterface $em): Response
    {

        $query = $em->createQuery(
            'SELECT fev FROM App\Entity\Flujoexpedienteventa fev
             INNER JOIN fev.flujoexpedienteIdflujoexpediente fe
             INNER JOIN fe.ventaIdventa v
             GROUP BY fe.idflujoexpediente
            '
        );
        $flowSales = $query->getResult();

        $countFlows = 0;

        foreach ($flowSales as $FlowSale) {

            $tempFlow = $FlowSale->getFlujoexpedienteIdflujoexpediente();
            $tempLocationId = $tempFlow->getSucursalIdsucursal()->getIdsucursal();;
            if (!$tempLocationId) {
                $tempFlow->setSucursalIdsucursal($FlowSale->getVentaIdventa()->getSucursalIdsucursal());
                $em->persist($tempFlow);
                $countFlows++;
            }
        }

        $em->flush();

        $query = $em->createQuery(
            'SELECT fe
            FROM App\Entity\Flujoexpediente fe
            INNER JOIN fe.ventaIdventa v
            '
        );

        $flows = $query->getResult();

        foreach ($flows as $Flow) {
            $tempLocationId = $Flow->getSucursalIdsucursal()->getIdsucursal();
            if (!$tempLocationId) {
                $Flow->setSucursalIdsucursal($Flow->getUsuarioIdusuario()->getSucursalIdsucursal());
                $em->persist($Flow);
                $countFlows++;
            }
        }

        $em->flush();

        return new Response('Se actualizó: ' . $countFlows . " flujos de expediente");
    }

    /**
     * @Route("/update-flow-signed-document", name="actualizacion-update-flow-signed-document")
     */
    public function updateFlowSignedDocument(EntityManagerInterface $em): Response
    {
        $query = $em->createQuery(
            'SELECT fe
            FROM App\Entity\Flujoexpediente fe
            WHERE fe.documentoexpedienteclinicofirmado1 IS NOT NULL
            '
        );
        $flows = $query->getResult();

        $countDocs = 0;

        foreach ($flows as $Flow) {
            $tempFlowDoc = new Flowsigneddocuments();
            $tempFlowDoc->setFlujoexpedienteIdflujoexpediente($Flow);
            $date = ($Flow->getFechaterminoflujo()) ? $Flow->getFechaterminoflujo() : new DateTime();
            $tempFlowDoc->setCreation($date);
            $tempFlowDoc->setFilename($Flow->getDocumentoexpedienteclinicofirmado1());
            $em->persist($tempFlowDoc);
            $countDocs++;
        }

        $em->flush();

        return new Response('Se actualizó: ' . $countDocs . " documentos");
    }

    /**
     * @Route("/change-defective", name="actualizacion-change-defective")
     */
    public function changeDefective(Request $request)
    {
        $em = $this->getDoctrine()->getManager();
        $defective = $request->get('defective');
        $stockId = $request->get('stockId');

        $msg = "";
        $success = false;

        $Stock = $em->getRepository(Stock::class)->findOneBy(array('idstock' => $stockId));
        $Stockstate = $em->getRepository(Stockstate::class)->findOneBy(array('idstockstate' => $defective));
        $User = $this->getUser();

        try {
            $Stockhistory = new Stockhistory();
            $Stockhistory->setStockIdstock($Stock);
            $Stockhistory->setStockstateIdstockstate($Stockstate);
            $Stockhistory->setUsuarioIdusuario($User);
            $Stockhistory->setDate(new DateTime());

            $Stock->setStockstateIdstockstate($Stockstate);
            $em->persist($Stock);
            $em->persist($Stockhistory);
            $em->flush();
        } catch (\Exception $e) {
            $msg .= $e->getMessage();
        }

        return $this->json(['success' => $success, 'msg' => $msg]);
    }

    /**
     * @Route("/change-stockOnline", name="update-change-stockOnline")
     */
    public function changestockOnline(Request $request)
    {
        $em = $this->getDoctrine()->getManager();
        $productosOnline = null;
        $msg = "";

        try {
            $query = $em->createQuery(
                'SELECT p.modelo
                FROM App\Entity\Stock s
                INNER JOIN s.stockstateIdstockstate sts
                INNER JOIN s.productoIdproducto p
                WHERE sts.idstockstate = s.idstockstate
                AND p.showonlinestore=:showonlinestore
                ')->setParameters(
                [
                    "showonlinestore" => '1'
                ]);

            $productosOnline = $query->getResult();

        } catch (\Exception $e) {
            $msg .= $e->getMessage();
        }

        return $this->render('actualizacion/productosShowOnlineStore.html.twig', [
            'productosOnline' => $productosOnline
        ]);
    }


    /**
     * @Route("/update-stock-state-history", name="actualizar-update-stock-state-history")
     */
    public function updateStockStateHistory(EntityManagerInterface $em): Response
    {

        $query = $em->createQuery(
            'SELECT s
            FROM App\Entity\Stock s
            '
        );
        $stocks = $query->getResult();

        $stocksChanges = 0;

        $User = $em->getRepository(Usuario::class)->findOneBy(array('idusuario' => 76));

        foreach ($stocks as $Stock) {

            $curStockState = $Stock->getStockstateIdstockstate();

            $Stockhistory = new Stockhistory();
            $Stockhistory->setStockIdstock($Stock);
            $Stockhistory->setStockstateIdstockstate($curStockState);
            $Stockhistory->setUsuarioIdusuario($User);
            $Stockhistory->setDate(new DateTime());

            $em->persist($Stockhistory);
            $stocksChanges++;
        }
        $em->flush();

        return new Response('Se actualizó: ' . $stocksChanges . " estados de stocks");
    }

    /**
     * @Route("/update-negative-separated-stocks", name="actualizacion-update-negative-separated-stocks")
     */
    public function updateNegativeSeparatedStocks(): Response
    {
        // Inicializar el contador de registros cambiados en 0 
        $stockVentaChanges = 0;
        $stockChanges = 0;
        $changesStocks = [];
        $test = '';

        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT s FROM App\Entity\Stock s
             WHERE s.apartados < 0
            '
        );
        $stoks = $query->getResult();

        $whereStock = "";

        if (isset($stoks[0])) {
            $whereStock = " (";

            foreach ($stoks as $Stock) {
                $whereStock .= $Stock->getIdstock() . ", ";
                $Stock->setApartados(0);
                $em->persist($Stock);
                $stockChanges++;
            }
            $em->flush();
            $whereStock .= "-1) ";

            $query = $em->createQuery(
                'SELECT sv FROM App\Entity\Stockventa sv
                 INNER JOIN sv.ventaIdventa v
                 INNER JOIN sv.stockIdstock s
                 INNER JOIN s.productoIdproducto p
                 WHERE sv.status =:status AND v.cotizacion=:quotation AND sv.cantidad > 0
                 AND p.tipoproducto =:productType AND sv.estaapartado =:separated AND v.status=:status
                 AND s.idstock IN ' . $whereStock
            )->setParameters(["status" => '1', "quotation" => '1', 'productType' => '1', 'separated' => '1']);
            $stockVentas = $query->getResult();

            foreach ($stockVentas as $Stockventa) {

                $TempStock = $Stockventa->getStockIdstock();

                if ($TempStock->getProductoIdproducto()->getMasivounico() == '1') {
                    if ($TempStock->getCantidad() == 0) {
                        $TempStock->setApartados(1);
                        $stockVentaChanges++;
                        $test .= '/' . '1-' . $TempStock->getIdstock();
                    }
                } else if ($TempStock->getProductoIdproducto()->getMasivounico() == '2') {
                    array_push($changesStocks, $TempStock->getIdstock());
                    $TempStock->setApartados($TempStock->getApartados() + $Stockventa->getCantidad());
                    $stockVentaChanges++;
                    $test .= '/' . '2-' . $TempStock->getIdstock();
                }

                $em->persist($TempStock);
            }

            $em->flush();
        }

        $changesStocksString = "";
        foreach ($changesStocks as $changesStock) $changesStocksString . $changesStock . ', ';

        return new Response('Se actualizó: ' . $stockChanges . " stocks con apartados < 0 y se actualizaron los apartados de: " . $stockVentaChanges . " stocks, los masivos fueron: " . $changesStocksString);
    }

    /**
     * @Route("/update-sale-amount-uam", name="actualizacion-update-sale-amount-uam")
     */
    public function updateSaleAmountUam(Request $request)
    {
        $em = $this->getDoctrine()->getManager();

        $startDate = "2024-01-01 00:00:00";
        $endDate = "2024-02-28 23:59:00";

        $query = $em->createQuery(
            'SELECT v
            FROM App\Entity\Venta v
            INNER JOIN v.tipoventaIdtipoventa tv
            WHERE v.fechacreacion BETWEEN :startDate AND :endDate AND tv.idtipoventa =:saleTypeId
            '
        )->setParameters(['startDate' => $startDate, 'endDate' => $endDate, 'saleTypeId' => 1748]);
        $sales = $query->getResult();

        $countSales = 0;

        $pagado = 3265;
        $subtotal = 3265 / 1.16;
        $iva = $pagado - $subtotal;

        foreach ($sales as $Sale) {
            $Sale->setPagado($pagado);
            $Sale->setIva($iva);
            $Sale->setTotal($subtotal);
            $em->persist($Sale);
            $countSales++;
        }

        $em->flush();

        return new Response('Se actualizó: ' . $countSales . " ventas");
    }

    /**
     * @Route("/register-clients-sheets", name="actualizacion-register-clients-sheets")
     */
    public function registerClientesSheets(): Response
    {
        return $this->render('actualizacion/register-clients-sheets.html.twig', []);
    }

    /**
     * @Route("/add-clients-sheets", name="actualizacion-add-clients-sheets")
     */
    public function addClientsSheets(Request $request)
    {
        $em = $this->getDoctrine()->getManager();
        $clients = $request->get("curClients");
        $curClients = explode("*", $clients);

        $clientEnterprise = $request->get("clientEnterprise");
        $unity = $request->get("unity");

        $success = false;
        $msg = "";

        $updatedClients = 0;

        try {

            $Unidad = $em->getRepository(Unidad::class)->findOneBy(array('nombre' => $unity));
            $Sellreference = $em->getRepository(Sellreference::class)->findOneBy(array('idsellreference' => 1));
            $Empresacliente = $em->getRepository(Empresacliente::class)->findOneBy(array('nombre' => $clientEnterprise));

            if (!$Empresacliente && $clientEnterprise) {
                $Empresacliente = new Empresacliente();
                $Empresacliente->setNombre($clientEnterprise);
                $em->persist($Empresacliente);
            }
            if (!$Unidad && $unity) {
                $Unidad = new Unidad();
                $Unidad->setNombre($unity);
                $Unidad->setEmpresaclienteIdempresacliente($Empresacliente);
                $em->persist($Unidad);
            }


            if (count($curClients) > 0) {

                foreach ($curClients as $clientName) {

                    $ClienteTemp = new Cliente();

                    $nameParts = $this->separeClientName($clientName);

                    if (count($nameParts) == 3) {

                        $ClienteTemp->setNombre($nameParts[2]);
                        $ClienteTemp->setApellidopaterno($nameParts[0]);
                        $ClienteTemp->setApellidomaterno($nameParts[1]);
                    } elseif (count($nameParts) == 2) {

                        $ClienteTemp->setNombre($nameParts[1]);
                        $ClienteTemp->setApellidopaterno($nameParts[0]);
                    } else {
                        $ClienteTemp->setNombre($nameParts[0]);
                    }

                    $ClienteTemp->setEmpresaclienteIdempresacliente($Empresacliente);
                    $ClienteTemp->setUnidadIdunidad($Unidad);
                    $ClienteTemp->setSellreferenceIdsellreference($Sellreference);

                    $em->persist($ClienteTemp);
                    $updatedClients++;
                }
                $em->flush();


                $success = true;
            } else throw new \Exception('Selecciona una lista válida');
        } catch (\Exception $e) {
            $msg .= $e->getMessage() . ' ' . $e->getLine();
        }

        return $this->json(["success" => $success, "msg" => $msg, "updatedClients" => $updatedClients]);
    }


    /**
     * @Route("/clientes-trim", name="clientes-trim")
     */
    public function clientesTrim(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT c
         FROM App\Entity\Cliente c
         WHERE c.status = :status AND c.tipocliente = :tipocliente'
        )->setParameters([
            "status" => 1,
            "tipocliente" => "uam"
        ]);

        $clientes = $query->getResult();

        foreach ($clientes as $cliente) {
            $cliente->setNombre(trim($cliente->getNombre()));
            $em->persist($cliente);
        }

        $em->flush();

        return $this->render('actualizacion/clientes-trim.html.twig', [
            'clientes' => $clientes
        ]);
    }

    /**
     * @Route("/update-flow-stages", name="actualizar-update-flow-stages")
     */
    public function updateFlowStages(EntityManagerInterface $em): Response
    {

        $query = $em->createQuery(
            'SELECT fe
        FROM App\Entity\Flujoexpediente fe
        WHERE fe.etapa >= 6
        ORDER BY fe.etapa DESC
        '
        );
        $flows = $query->getResult();

        $flowChanges = 0;

        if (count($flows) > 0) {

            $lastStage = intval($flows[0]->getEtapa());

            if ($lastStage < 8) {

                foreach ($flows as $Flow) {

                    $curStage = intval($Flow->getEtapa());
                    $Flow->setEtapa($curStage + 1);
                    $em->persist($Flow);
                    $flowChanges++;
                }
            }
        }
        $em->flush();

        return new Response('Se actualizó: ' . $flowChanges . " flujos de expediente");
    }

    /**
     * @Route("/update-payment-types", name="actualizar-update-payment-types")
     */
    public function updatePaymentTypes(EntityManagerInterface $em): Response
    {

        $query = $em->createQuery(
            'SELECT p
            FROM App\Entity\Pago p
            '
        );
        $payments = $query->getResult();

        $paymentChanges = 0;

        foreach ($payments as $Payment) {

            $curType = $Payment->getTipopago();
            $tempPaymenttype = $em->getRepository(Paymenttype::class)->findOneBy(array('name' => $curType));
            $Payment->setPaymenttypeIdpaymenttype($tempPaymenttype);
            $em->persist($Payment);
            $paymentChanges++;
        }
        $em->flush();

        return new Response('Se actualizó: ' . $paymentChanges . " pagos");
    }

    /**
     * @Route("/update-uam-payments", name="actualizar-update-uam-payments")
     */
    public function updateUAMPayments(EntityManagerInterface $em): Response
    {

        $query = $em->createQuery(
            'SELECT v
            FROM App\Entity\Venta v
            INNER JOIN v.tipoventaIdtipoventa tv
            WHERE v.status = :status AND v.cotizacion !=:status AND tv.nombre = :saleType AND v.pagado > 0'
        )->setParameters([
            "status" => 1,
            "saleType" => "UAM"
        ]);

        $sales = $query->getResult();

        $saleChanges = 0;

        $whereSale = "";

        for ($i = 0; $i < count($sales); $i++) {

            if ($i == 0) $whereSale = " and (";

            $whereSale .= " v.idventa=" . $sales[$i]->getIdventa();

            if ($i != count($sales) - 1) $whereSale .= " or ";
            else $whereSale .= ") ";

        }

        $query = $em->createQuery(
            'SELECT p.monto, v.idventa
            FROM App\Entity\Pago p
            INNER JOIN p.ventaIdventa v
            WHERE p.status = :status AND v.status =:status ' . $whereSale
        )->setParameters([
            "status" => 1
        ]);
        $payments = $query->getResult();

        $paymentMapped = [];

        foreach ($payments as $Payment) {
            $tempSale = $Payment["idventa"];
            if (isset($paymentMapped[$tempSale])) array_push($paymentMapped[$tempSale], $Payment["monto"]);
            else $paymentMapped[$tempSale] = [$Payment["monto"]];
        }

        var_dump($paymentMapped);

        $convenioPT = $em->getRepository(Paymenttype::class)->findOneBy(array('name' => "Convenio"));

        foreach ($sales as $Sale) {

            $tmpTotal = floatval($Sale->getPagado());

            $curPayed = 0;

            if (isset($paymentMapped[$Sale->getIdventa()])) {
                foreach ($paymentMapped[$Sale->getIdventa()] as $payment) $curPayed += floatval($payment);
            }

            $diff = $tmpTotal - $curPayed;

            if ($diff > 0) {

                $curDate = ($Sale->getFechaventa()) ? $Sale->getFechaventa() : $Sale->getFecha();

                $Payment = new Pago();
                $Payment->setMonto($diff);
                $Payment->setFecha($curDate);
                $Payment->setPaymenttypeIdpaymenttype($convenioPT);
                $Payment->setVentaIdventa($Sale);
                $Payment->setUsuarioCobro($Sale->getUsuarioIdusuario());
                $em->persist($Payment);

                $Sale->setLiquidada('1');
                $em->persist($Sale);
                $saleChanges++;

            } else if ($diff < 0) var_dump($Sale->getIdventa() . '/');

        }
        $em->flush();
        var_dump($saleChanges);

        return new Response(count($sales) . '-' . count($paymentMapped));
    }

    /**
     * @Route("/update-sales-liquidated-v2", name="actualizar-update-sales-liquidated-v2")
     */
    public function updateSalesLiquidatedV2(EntityManagerInterface $em): Response
    {

        $query = $em->createQuery(
            'SELECT v
            FROM App\Entity\Venta v
            WHERE v.status = :status AND v.cotizacion !=:status'
        )->setParameters([
            "status" => 1
        ]);

        $sales = $query->getResult();

        $liquidatedChanges = 0;
        $noLiquidatedChanges = 0;

        $liquidatedSales = [];
        $noLiquidatedSales = [];

        $whereSale = "";

        for ($i = 0; $i < count($sales); $i++) {

            if ($i == 0) $whereSale = " and (";

            $whereSale .= " v.idventa=" . $sales[$i]->getIdventa();

            if ($i != count($sales) - 1) $whereSale .= " or ";
            else $whereSale .= ") ";

        }

        $query = $em->createQuery(
            'SELECT p.monto, v.idventa
            FROM App\Entity\Pago p
            INNER JOIN p.ventaIdventa v
            WHERE p.status = :status AND v.status =:status ' . $whereSale . ' ORDER BY p.idpago ASC'
        )->setParameters(["status" => 1]);
        $payments = $query->getResult();

        $paymentMapped = [];

        foreach ($payments as $Payment) {
            $tempSale = $Payment["idventa"];
            if (isset($paymentMapped[$tempSale])) array_push($paymentMapped[$tempSale], $Payment["monto"]);
            else $paymentMapped[$tempSale] = [$Payment["monto"]];
        }

        foreach ($sales as $Sale) {

            $tmpTotal = floatval($Sale->getPagado());

            $curPayed = 0;

            if (isset($paymentMapped[$Sale->getIdventa()])) {
                foreach ($paymentMapped[$Sale->getIdventa()] as $payment) $curPayed += $payment;
            }

            $diff = $tmpTotal - $curPayed;

            $curState = ($diff <= 0.01) ? '1' : '0';
            $prevState = $Sale->getLiquidada();

            if ($prevState != $curState) {
                $Sale->setLiquidada($curState);
                if ($curState == '1') {
                    $liquidatedChanges++;
                    array_push($liquidatedSales, $Sale->getFolio());
                } else {
                    $noLiquidatedChanges++;
                    array_push($noLiquidatedSales, $Sale->getFolio());
                }
                $em->persist($Sale);
            }

        }
        $em->flush();

        var_dump($liquidatedSales);
        var_dump("----------------------------------------");
        var_dump($noLiquidatedSales);

        return new Response("Se cambió: " . $liquidatedChanges . " ventas a liquidadas y " . $noLiquidatedChanges . " a no liquidadas");
    }

    /**
     * @Route("/update-uam-products", name="actualizar-update-uam-products")
     */
    public function updateUAMProducts(EntityManagerInterface $em): Response
    {

        $query = $em->createQuery(
            'SELECT v
            FROM App\Entity\Venta v
            INNER JOIN v.tipoventaIdtipoventa tv
            WHERE v.status = :status AND v.cotizacion !=:status AND tv.nombre = :saleType AND v.fechacreacion BETWEEN :minDate AND :maxDate'
        )->setParameters([
            "status" => 1,
            "saleType" => "UAM",
            "minDate" => "2024-03-15 00:00:00",
            "maxDate" => "2024-05-28 00:00:00"
        ]);
        $sales = $query->getResult();

        $targetPagado = 4245.00;
        $targetIva = 585.52;
        $tagetTotal = 3659.48;

        $correctSales = [];
        $incorrectSales = [];

        $test = [];


        foreach ($sales as $Sale) {
            $query = $em->createQuery(
                'SELECT sv
                FROM App\Entity\Stockventa sv
                INNER JOIN sv.ventaIdventa v
                WHERE sv.status = :status AND v.idventa =:saleId '
            )->setParameters([
                "status" => 1,
                "saleId" => $Sale->getIdventa()
            ]);
            $curStockventas = $query->getResult();

            //if (count($curStockventas) == 0) array_push($test,$Sale->getIdventa());

            $curStorables = 0;
            $curServices = 0;
            $curSum = 0;
            $curSumStorables = 0;

            foreach ($curStockventas as $Stockventa) {

                $tempProductType = $Stockventa->getStockIdstock()->getProductoIdproducto()->getTipoproducto();

                if ($tempProductType == '2') $curServices += $Stockventa->getCantidad();
                else {
                    $curStorables += $Stockventa->getCantidad();
                    $curSumStorables += $Stockventa->getPreciofinal();
                }

                $curSum = $curSum + $Stockventa->getPreciofinal();

            }

            $tmpDiff = $targetPagado - $curSum;

            if ($tmpDiff != 0) {
                array_push($incorrectSales, $Sale->getFolio());

                if ($curStorables == 0) {
                    $tempService = $targetPagado / $curServices;
                    foreach ($curStockventas as $Stockventa) {
                        $Stockventa->setPreciofinal($tempService);
                        $Stockventa->setPrecio($tempService);
                    }
                } else if ($curServices == 0) {

                    $tempStorable = $targetPagado / $curStorables;
                    foreach ($curStockventas as $Stockventa) {
                        $Stockventa->setPreciofinal($tempStorable);
                        $Stockventa->setPrecio($tempStorable);
                    }

                } else {
                    $tmpRemaining = $targetPagado - $curSumStorables;
                    $tempService = $tmpRemaining / $curServices;
                    foreach ($curStockventas as $Stockventa) {
                        $tempProductType = $Stockventa->getStockIdstock()->getProductoIdproducto()->getTipoproducto();
                        if ($tempProductType == '2') {
                            $Stockventa->setPreciofinal($tempService);
                            $Stockventa->setPrecio($tempService);
                        }
                    }
                }
                $em->persist($Stockventa);
                $Sale->setPagado($targetPagado);
                $Sale->setIva($targetIva);
                $Sale->setTotal($tagetTotal);
                $em->persist($Sale);

            } else array_push($correctSales, $Sale->getFolio());


        }

        $em->flush();

        echo "<h1>Ventas correctas</h1>";
        $correctMessages = "<p>";
        foreach ($correctSales as $correctSale) $correctMessages .= $correctSale . ", ";
        $correctMessages .= "</p>";

        echo $correctMessages;

        echo "<h1>Ventas corregidas</h1>";
        $incorrectMessages = "<p>";
        foreach ($incorrectSales as $incorrectSale) $incorrectMessages .= $incorrectSale . ", ";
        $incorrectMessages .= "</p>";

        echo $incorrectMessages;

        //var_dump($test);

        return new Response("Ventas correctas: " . count($correctSales) . " / Ventas corregidas: " . count($incorrectSales));
    }

    /**
     * @Route("/update-stock-states", name="actualizar-update-stock-states")
     */
    public function updateStockStates(EntityManagerInterface $em): Response
    {

        $query = $em->createQuery(
            'SELECT s
            FROM App\Entity\Stock s
            '
        );
        $stocks = $query->getResult();

        $stocksChanges = 0;

        $stockStates = [];

        foreach ($stocks as $Stock) {

            $curType = $Stock->getDefective();
            if (!isset($stockStates[$curType])) {
                $tempStockState = $em->getRepository(Stockstate::class)->findOneBy(array('name' => $curType));
                $stockStates[$curType] = $tempStockState;
            }

            $Stock->setStockstateIdstockstate($stockStates[$curType]);
            $em->persist($Stock);
            $stocksChanges++;
        }
        $em->flush();

        return new Response('Se actualizó: ' . $stocksChanges . " stocks");
    }

    /**
     * @Route("/update-stolen-stock", name="actualizar-update-stolen-stock")
     */
    public function updateStolenStock(EntityManagerInterface $em): Response
    {
        $skus = ["151498660794", "151714616782", "151557002204", "151370362446", "151107778850", "151625443862", "151177678716", "151954493428", "151701407659", "151866912901", "151953247951", "151191049440", "151701870487", "151549294884", "151172891520", "151414608864", "151305167050", "151293917702", "151927309087", "151777917694", "151640344756", "151950089007", "151155165770", "151522694552", "151795520455", "151226633569", "151179532569", "151518604668", "151932946046", "151185194726", "151945184720", "151676444455", "151752587832", "151610140436", "151786812410", "151599281009", "151635644868", "151675818906", "151176076173", "151933882692", "151170280192", "151447917340", "151691278831", "151827323557", "151951585092", "151190207924", "151332583223", "151589976289", "151765476047", "151838460814", "151878642745", "151586713660", "151735128446", "151749744853", "151210346813", "151614068438", "151786276091", "151908171683", "151734002695", "151405084454", "151242029316", "151551048814", "151718886962", "151215263389", "151232471613", "151393599267", "151590761638", "151230082922", "151441100141", "151462058800", "151289056496", "151805693021", "151790806289", "151303937015", "151430415346", "151602481658", "151788813204", "151947716116", "151232766475", "151106440243", "151101865284", "151350363919", "151257750092", "151355524610", "151403682188", "151339373159", "151802163844", "151964357836", "151158069299", "151896463227", "151197146300", "151606971275", "151151386738", "151985423524", "151467351940", "151264938469", "151669329887", "151615540407", "151757724345", "151334852136", "151517231994", "151419928773", "151874353605", "151662838512", "151637707078", "151624573810", "151336620442", "151294112136", "151294892873", "151833237700", "151171808101", "151608038569", "151510688818", "151107017365", "151373623393", "151193165038", "151829426223", "151367803131", "151374561981", "151425260798", "151303601921", "151960339797", "151419772853", "151837476200", "151741921262", "151470861188", "151806516909", "151709080907", "151299372905", "151949234378", "151997795057", "151479850312", "151496806174", "151832552826", "151984086526", "151414401158", "151419185776", "151947818503", "151697304168", "151210401116", "151380115655", "151977863916", "151186605859", "151419775471", "151309234544", "151552028960", "151754825283", "151520372742", "151718154049", "151460389906", "151122395484", "151687791829", "151444880104", "151779201358", "151945580909", "151234447027", "151904261950", "151221566303", "151275283710", "151994135280", "151359507734", "151460224833", "151405787068", "151593465479", "151621628680", "151523544799", "151830290556", "151393582022", "151634071737", "151837968606", "151424009883", "151975743560", "151685924000", "151581888700", "151656696747", "151457393057", "151241325800", "151441892566", "151972643225", "151993538302", "151227287004", "151202656631", "151652275669", "151449934495", "151849111251", "151141580119", "151645189768", "151403033026", "151226156670", "151202906329", "151969306739", "151237382300", "151924923564", "151958899809", "151919022740", "151896101876", "151897546237", "151925298973", "151945024854", "151400270062", "151771424818", "151976193837", "151756305176", "151527322373", "151636343564", "151579580550", "151844607009", "151186107441", "151359131120", "151827955877", "151167000466", "151537433904", "151395249454", "151574225084", "151115349182", "151922835420", "151701929891", "151294589434", "151173151680", "151486963731", "151983335933", "151779450765", "151224742325", "151844194603", "151667520892", "151297444406", "151179919229", "151229393641", "151734432682", "151596474524", "151335040292", "151615881126", "151527001686", "151480952332", "151858960749", "151387096192", "151886933742", "151640633202", "151163410309", "151183566868", "151179862131", "151237730084", "151474806048", "151997676803", "151287473209", "151262850602", "151909878991", "151688733827", "151641804378", "151807711849", "151637614523", "151370868492", "151981415837", "151752279214", "151469516349", "151200615943", "151373898924", "151963256834", "151696972773", "151920803140", "151494484546", "151300346515", "151919756925", "151355153856", "151195079161", "151908952480", "151942971585", "151594551530", "151478342668", "151349520851", "151697424442", "151370751298", "151592014242", "151533894015"];

        $whereSKU = " WHERE (";
        for ($i = 0; $i < count($skus); $i++) {
            $whereSKU .= "s.codigobarras=" . $skus[$i];
            if ($i < count($skus) - 1) $whereSKU .= " or ";
        }
        $whereSKU .= ") ";


        $query = $em->createQuery(
            'SELECT s
            FROM App\Entity\Stock s
            ' . $whereSKU
        );
        $stocks = $query->getResult();

        $stocksChanges = 0;

        $stolenStockState = $em->getRepository(Stockstate::class)->findOneBy(array('name' => "ROBO"));

        foreach ($stocks as $Stock) {

            $Stock->setStockstateIdstockstate($stolenStockState);
            $em->persist($Stock);
            $stocksChanges++;
        }
        $em->flush();

        return new Response('Se actualizó: ' . $stocksChanges . " stocks");
    }

    /**
     * @Route("/cancelar-ventas-skus", name="cancelar-ventas-skus")
     */
    public function cancelarVentasPorSkus(Request $request)
    {
        $skus = [
            "151176457617", "151438353408", "151367254630", "151434607887", "151313862659",
            "151777619753", "151899122540", "151256022002", "151283815647", "151854677053",
            "151557289578", "151678965318", "151775715316", "151870149949", "151843008765",
            "151800779886", "151488703680", "151462310234", "151134729585", "151474687055",
            "151443891572", "151920064025", "151690242840", "151265576002", "151986707710",
            "151231275807", "151127213072", "151237581619", "151543515990", "151250248991",
            "151384059599", "151685878534", "151795309408", "151556894602", "151482486587",
            "151181525201", "151912264024", "151758124877", "151837116114", "151791341161",
            "151392192051", "151606090547", "151487254624", "151575526825", "151728667599",
            "151901093181", "151855364410", "151120270459", "151857167220", "151724578295",
            "151709031476", "151365270675", "151894967437", "151652878604", "151115525156",
            "151196208142", "151423525387", "151613382505", "151838251311", "151421065103",
            "151119049791", "151176516128", "151522585149", "151525584788", "151848506247",
            "151732337130", "151348031151", "151996560139", "151664375232", "151773255948",
            "151998357094", "151343104294", "151864044853", "151346795016", "151568470528",
            "151477561339", "151402456333", "151311569077", "151524663426", "151572619339",
            "151288079871", "151896594556", "151426168859", "151617088915", "151605916124",
            "151202513836", "151224222582", "151868931345", "151856719285", "151631755372",
            "151807736369", "151301433776", "151107900950", "151354414429", "151441233081",
            "151302031086", "151617865876", "151656273356", "151621135946", "151607379637",
            "151172989567", "151573833785", "151113503462", "151473008471", "151570172784",
            "151612215248", "151907944988", "151992825530", "151501335564", "151526932046",
            "151836208084", "151774472920", "151251567959", "151499684955", "151961240415",
            "151459299878", "151514696251", "151569543378", "151214510183", "151458064698",
            "151698858472"
        ];

        $exito = false;
        $msj = "";
        $em = $this->getDoctrine()->getManager();

        try {
            // Obtener todas las Stockventa asociadas a los SKU y sus respectivas ventas
            $query = $em->createQuery(
                'SELECT sv, v
            FROM App\Entity\Stockventa sv
            INNER JOIN sv.ventaIdventa v
            INNER JOIN sv.stockIdstock s
            WHERE s.codigobarras IN (:skus) AND v.status = :status'
            )->setParameters(['skus' => $skus, 'status' => "1"]);
            $stockVentas = $query->getResult();

            foreach ($stockVentas as $stockVenta) {
                $Venta = $stockVenta->getVentaIdventa();
                if ($Venta) {
                    if ($Venta->getCotizacion() == '0') {
                        $currenUserRol = $this->getUser()->getRol();
                        if ($currenUserRol != "ROLE_SUPER_ADMIN") throw new \Exception("No tienes permisos para cancelar esta venta");
                    }

                    $query = $em->createQuery(
                        'SELECT sv.idstockventa
                    FROM App\Entity\Merma m
                    INNER JOIN m.stockventaIdstockventa sv
                    INNER JOIN sv.ventaIdventa v
                    WHERE v.idventa = :idventa'
                    )->setParameters(["idventa" => $Venta->getIdventa()]);
                    $checkMerma = $query->getResult();

                    if (count($checkMerma) > 0) throw new \Exception("Hay una merma que está asociada con esta venta");

                    $Venta->setStatus('0');
                    $Venta->setFechacancelacion(new \DateTime('now'));
                    $Venta->setPorquesecancelo("por ajuste de inventario lo pidió Lesly");
                    $Venta->setUsuarioResponsablecancelacion($this->getUser());
                    $em->persist($Venta);

                    $cotizacion = $Venta->getCotizacion();
                    $seapartoarmazon = $Venta->getSeapartoarmazon();
                    $sedescontodeinventario = $Venta->getSedescontodeinventario();

                    $query = $em->createQuery(
                        'SELECT sv
                    FROM App\Entity\Stockventa sv
                    WHERE sv.ventaIdventa = :idventa'
                    )->setParameters(["idventa" => $Venta->getIdventa()]);
                    $StockVentas = $query->getResult();

                    foreach ($StockVentas as $Stockventa) {
                        $cantidadproductos = $Stockventa->getCantidad();
                        if ($Stockventa->getStockIdstock()) {
                            $Stock = $Stockventa->getStockIdstock();
                            $Producto = $Stock->getProductoIdproducto();
                            $tipoproducto = $Producto->getTipoproducto();

                            if ($tipoproducto != "2" && ($sedescontodeinventario == "1" || ($cotizacion == "1" && $seapartoarmazon == "1"))) {
                                $cantidadstock = $Stock->getCantidad();
                                $Stock->setCantidad($cantidadstock + $cantidadproductos);
                                if ($cotizacion == "1" && $seapartoarmazon == "1") {
                                    $Stock->setApartados($Stock->getApartados() - $cantidadproductos);
                                }
                            }
                            // Cambiar estado del stock a 0
                            $Stock->setStatus('0');
                            $em->persist($Stock);
                        }
                        $em->persist($Stockventa);
                    }

                    $query = $em->createQuery(
                        'SELECT p
                    FROM App\Entity\Pago p
                    INNER JOIN p.ventaIdventa v
                    WHERE v.idventa = :idventa AND p.status = :status'
                    )->setParameters(["idventa" => $Venta->getIdventa(), "status" => '1']);
                    $Pagos = $query->getResult();

                    $pagoEraser = new PagoEraser($em);

                    if ($Pagos) {
                        foreach ($Pagos as $Pago) {
                            $pagoEraser->erasePago($Pago);
                            $em->persist($Pago);
                        }
                    }

                    if ($Venta->getCredito() == '1') {
                        $Client = $Venta->getClienteIdcliente();
                        $debtTemp = $Venta->getPagado();
                        if ($Client->getDebt() > 0) $Client->setDebt($Client->getDebt() - $debtTemp);
                        $em->persist($Client);
                    }

                    $query = $em->createQuery(
                        'SELECT vc
                    FROM App\Entity\Ventacupon vc
                    INNER JOIN vc.cuponIdcupon c
                    INNER JOIN vc.ventaIdventa v
                    WHERE v.idventa = :idventa and vc.status = :status'
                    )->setParameters(["idventa" => $Venta->getIdventa(), "status" => '1']);
                    $VentaCupones = $query->getResult();

                    if ($VentaCupones) {
                        foreach ($VentaCupones as $VentaCupon) {
                            $VentaCupon->setStatus("0");
                            $Cupon = $VentaCupon->getCuponIdcupon();

                            $usedCupons = ($Cupon->getCuponesusados()) ? $Cupon->getCuponesusados() : 0;

                            if ($usedCupons > 0) {
                                $Cupon->setCuponesusados($usedCupons - 1);
                                $em->persist($Cupon);
                            }

                            $em->persist($VentaCupon);
                        }
                    }
                }
            }

            $em->flush();
            $exito = true;
        } catch (\Exception $e) {
            $msj = $e->getMessage();
        }

        return $this->json(['exito' => $exito, 'msj' => $msj]);
    }

    /**
     * @Route("/actualizacion-pagos", name="app_actualizacion_pagos")
     */
    public function actualizarPagos(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $exito = false;
        $msj = "";
        $ventasNoLiquidadasConPagos = [];

        $confirmar = $request->request->get('confirmar');

        try {
            $query = $em->createQuery(
                'SELECT v.idventa, v.liquidada, v.total, tv.nombre AS Tipoventa, v.folio, v.status,
                    COUNT(p.idpago) AS NumeroPagos
             FROM App\Entity\Venta v
             LEFT JOIN App\Entity\Pago p WITH p.ventaIdventa = v.idventa
             INNER JOIN v.tipoventaIdtipoventa tv
             INNER JOIN v.sucursalIdsucursal s
             INNER JOIN s.empresaIdempresa e
             WHERE v.liquidada = :liquidada AND v.status = :status
             AND v.fechacreacion <= :fecha
             AND e.idempresa = :empresaId
             GROUP BY v.idventa'
            )->setParameters([
                "liquidada" => "0",
                "fecha" => new \DateTime('2024-06-30'),
                "status" => "1",
                "empresaId" => 1  // Filtrar OptimoOpticas
            ]);

            $ventasNoLiquidadasConPagos = $query->getResult();

            // Calcular el total con IVA para cada venta y añadirlo al array
            foreach ($ventasNoLiquidadasConPagos as &$ventaData) {
                $ventaData['totalConIVA'] = round($ventaData['total'] * 1.16, 2);
            }

            // Si se confirma el proceso, crear un pago para cada venta con deuda
            if ($confirmar) {
                $totalCambios = 0;

                foreach ($ventasNoLiquidadasConPagos as $ventaData) {
                    $venta = $em->getRepository('App\Entity\Venta')->find($ventaData['idventa']);
                    $paymentType = $em->getRepository('App\Entity\PaymentType')->find(27);

                    if (!$paymentType) {
                        throw new \Exception("PaymentType con ID 27 no encontrado.");
                    }

                    if ($venta) {
                        // Crear el nuevo pago con el total con IVA
                        $pago = new Pago();
                        $pago->setMonto($ventaData['totalConIVA']);
                        $pago->setFecha(new \DateTime());
                        $pago->setVentaIdventa($venta);
                        $pago->setPaymenttypeIdpaymenttype($paymentType);  // Asignar la instancia de PaymentType

                        // Persistir el pago
                        $em->persist($pago);

                        // Actualizar el estado de liquidación si la deuda restante es cero después del pago
                        $venta->setLiquidada(1);

                        // Incrementar el contador de cambios
                        $totalCambios++;
                    }
                }

                // Guardar los cambios en la base de datos
                $em->flush();
                $exito = true;
                $msj = "Pagos actualizados correctamente.";
            }

        } catch (\Exception $e) {
            $exito = false;
            $msj = $e->getMessage(); // Captura el mensaje de error
        }

        // Si la confirmación es verdadera, retornar un JSON
        if ($confirmar) {
            return $this->json([
                'exito' => $exito,
                'mensaje' => $msj,
                'totalCambios' => $totalCambios ?? 0,
            ]);
        }

        // Si no se confirmó, retornar la vista
        return $this->render('actualizacion/ActualizarPagos.html.twig', [
            'ventasNoLiquidadasConPagos' => $ventasNoLiquidadasConPagos,
            'mensaje' => $msj,
            'exito' => $exito,
        ]);
    }

    /**
     * @Route("/update-auth-stage", name="actualizar-update-auth-stage")
     */
    public function updateAuthStage(EntityManagerInterface $em): Response
    {

        $query = $em->createQuery(
            'SELECT v
            FROM App\Entity\Venta v
            WHERE v.autorizacionstate != 0
            '
        );
        $sales = $query->getResult();

        $salesChanges = 0;
        $authStages = [];

        foreach ($sales as $Sale) {

            $curType = $Sale->getAutorizacionstate();
            if (!isset($authStages[$curType])) {
                $tempAuthStage = $em->getRepository(Authstage::class)->findOneBy(array('stageorder' => $curType));
                $authStages[$curType] = $tempAuthStage;
            }

            $Sale->setAuthstageIdauthstage($authStages[$curType]);
            $em->persist($Sale);
            $salesChanges++;
        }
        $em->flush();

        return new Response('Se actualizó: ' . $salesChanges . " ventas");
    }

    /**
     * @Route("/asignar-sucursal/{iddocumentos}", name="asignar-sucursal")
     */
    public function asignarSucursal($iddocumentos, Request $request)
    {
        // Declaración de variables
        $exito = false;
        $msj = "";
        $em = $this->getDoctrine()->getManager();
        $fechaActual = new \DateTime();
        // Obtengo el array de sucursales seleccionadas
        $sucursalesSeleccionadas = $request->get('sucursal', []);
        // Buscar el documento
        $documento = $em->getRepository(Documentos::class)->find($iddocumentos);
        if (!$documento) {
            throw $this->createNotFoundException('No se encontró el documento con ID: ' . $iddocumentos);
        }
        // Obtener las relaciones actuales (sucursales asociadas al documento)
        $relacionesActuales = $em->getRepository(Documentosucursal::class)
            ->findBy(['documentosIddocumentos' => $documento]);
        // Crear un array con los IDs de las sucursales asociadas actualmente
        $relacionesActualesIds = array_map(function ($relacion) {
            return $relacion->getSucursalIdsucursal()->getIdsucursal();
        }, $relacionesActuales);
        // Paso 1: Añadir las nuevas relaciones
        foreach ($sucursalesSeleccionadas as $sucursalId) {
            // Si la sucursal seleccionada no está ya relacionada, la agregamos
            if (!in_array($sucursalId, $relacionesActualesIds)) {
                $sucursal = $em->getRepository(Sucursal::class)->find($sucursalId);
                if ($sucursal) {
                    try {
                        $documentoSucursal = new Documentosucursal();
                        $documentoSucursal->setSucursalIdsucursal($sucursal);
                        $documentoSucursal->setDocumentosIddocumentos($documento);
                        $documentoSucursal->setCreacion($fechaActual);
                        // Persistir la nueva relación
                        $em->persist($documentoSucursal);
                        $exito = true;
                        $msj = "Sucursal asignada correctamente.";
                    } catch (\Exception $e) {
                        $msj = "Error: " . $e->getMessage();
                    }
                } else {
                    $msj = "No se encontró la sucursal con ID: " . $sucursalId;
                }
            }
        }
        foreach ($relacionesActuales as $relacionActual) {
            if (!in_array($relacionActual->getSucursalIdsucursal()->getIdsucursal(), $sucursalesSeleccionadas)) {
                $em->remove($relacionActual);
                $exito = true;
                $msj = "Sucursal eliminada correctamente.";
            }
        }
        // Realizar flush después de procesar todas las sucursales
        $em->flush();
        return $this->json(['exito' => $exito, 'msj' => $msj]);
    }


    /**
     * @Route("/base_de_datos_UAM_2401", name="base_de_datos")
     */
    public function baseDatosUam(EntityManagerInterface $em): Response
    {
        $query = $em->createQuery(
            'SELECT c.nombre, c.apellidopaterno, c.apellidomaterno, c.numeroempleado, u.nombre AS Unidad, v.fechacreacion, tv.nombre AS Tipoventa, c.telefono AS numeroTelefono
     FROM App\Entity\Venta v
     INNER JOIN v.clienteIdcliente c
     INNER JOIN c.unidadIdunidad u
     INNER JOIN v.tipoventaIdtipoventa tv
     WHERE tv.nombre LIKE :prestacionUAM
     AND v.fechacreacion BETWEEN :startDate AND :endDate
     AND v.status = :status
     AND MONTH(v.fechacreacion) IN (10, 11, 12)
     ORDER BY c.numeroempleado ASC, v.fechacreacion DESC'
        );

        $query->setParameters([
            'prestacionUAM' => '%UAM%',
            'startDate' => new \DateTime('2021-10-01'),
            'endDate' => new \DateTime('2024-12-31'),
            'status' => 1
        ]);

        $clientes = $query->getResult();

        return $this->render('actualizacion/basesdeDatosUam.html.twig', [
            'clientes' => $clientes,
        ]);
    }

    /**
     * @Route("/cambiar-pago/{idpago}", name="cambiar-pago1")
     */
    public function cambiarPago1($idpago, Request $request)
    {
        $exito = false;
        $msj = "";
        $em = $this->getDoctrine()->getManager();
        $tipoPagoId = $request->get('tipoPago'); // Cambiado para reflejar que es el ID del tipo de pago
        $cantidad = $request->get('cantidad');
        $user = $this->getUser();

        try {
            // Obtener el objeto Pago desde la base de datos
            $Pago = $em->getRepository(Pago::class)->find($idpago);
            if (!$Pago) {
                throw new \Exception('Pago no encontrado.');
            }

            // Obtener el objeto Paymenttype desde la base de datos usando el ID
            $tipoPago = $em->getRepository(Paymenttype::class)->find($tipoPagoId);
            if (!$tipoPago) {
                throw new \Exception('Tipo de pago no encontrado.');
            }

            // Guardar los datos actuales antes de cambiarlos
            $justificacionAnterior = $Pago->getJustification();
            $cantidadAnterior = $Pago->getMonto();
            $tipoPagoAnterior = $Pago->getTipopago(); // Si tienes un getter para el tipo de pago actual

            // Actualizar los campos del pago
            $Pago->setMonto($cantidad);
            $Pago->setPaymenttypeIdpaymenttype($tipoPago); // Establece el nuevo tipo de pago

            $fechaActual = new \DateTime();
            $fechaFormateada = $fechaActual->format('d-m-Y H:i:s');

            // Concatenar la justificación anterior con la nueva, añadiendo la fecha
            $nuevaJustificacion = $justificacionAnterior . "\n\r[" . $fechaFormateada . "] " .
                $user->getNombre() . " ha cambiado el pago de " .
                $cantidadAnterior . " (tipo de pago anterior: " . $tipoPagoAnterior . ") a " .
                $cantidad . " (nuevo tipo de pago: " . $tipoPago->getName() . ")"; // Aquí usarás el nombre del tipo de pago actual

            $Pago->setJustification($nuevaJustificacion);

            // Persistir los cambios
            $em->persist($Pago);
            $em->flush();

            $exito = true;
            $msj = "Pago actualizado correctamente.";

        } catch (\Exception $e) {
            $msj = "Error: " . $e->getMessage();
        }

        return $this->json(['exito' => $exito, 'msj' => $msj]);
    }

    /**
     * @Route("/cambiar-pago/{idpago}", name="cambiar-pago")
     */
    public function cambiarPago($idpago, Request $request)
    {
        $exito = false;
        $msj = "";
        $em = $this->getDoctrine()->getManager();
        $tipoPagoId = $request->get('tipoPago'); // Cambiado para reflejar que es el ID del tipo de pago
        $cantidad = $request->get('cantidad');
        $user = $this->getUser();

        try {
            // Obtener el objeto Pago desde la base de datos
            $Pago = $em->getRepository(Pago::class)->find($idpago);
            if (!$Pago) {
                throw new \Exception('Pago no encontrado.');
            }

            // Obtener el objeto Paymenttype desde la base de datos usando el ID
            $tipoPago = $em->getRepository(Paymenttype::class)->find($tipoPagoId);
            if (!$tipoPago) {
                throw new \Exception('Tipo de pago no encontrado.');
            }

            // Guardar los datos actuales antes de cambiarlos
            $justificacionAnterior = $Pago->getJustification();
            $cantidadAnterior = $Pago->getMonto();
            $tipoPagoAnterior = $Pago->getTipopago(); // Si tienes un getter para el tipo de pago actual

            // Actualizar los campos del pago
            $Pago->setMonto($cantidad);
            $Pago->setPaymenttypeIdpaymenttype($tipoPago); // Establece el nuevo tipo de pago

            $fechaActual = new \DateTime();
            $fechaFormateada = $fechaActual->format('d-m-Y H:i:s');

            // Concatenar la justificación anterior con la nueva, añadiendo la fecha
            $nuevaJustificacion = $justificacionAnterior . "\n\r[" . $fechaFormateada . "] " .
                $user->getNombre() . " ha cambiado el pago de " .
                $cantidadAnterior . " (tipo de pago anterior: " . $tipoPagoAnterior . ") a " .
                $cantidad . " (nuevo tipo de pago: " . $tipoPago->getName() . ")"; // Aquí usarás el nombre del tipo de pago actual

            $Pago->setJustification($nuevaJustificacion);

            // Persistir los cambios
            $em->persist($Pago);
            $em->flush();

            $exito = true;
            $msj = "Pago actualizado correctamente.";

        } catch (\Exception $e) {
            $msj = "Error: " . $e->getMessage();
        }

        return $this->json(['exito' => $exito, 'msj' => $msj]);
    }


    /**
     * @Route("/productosTiendaOnline", name="tienda_online")
     */
    public function productosTiendaOnline(EntityManagerInterface $em): Response
    {
        $query = $em->createQuery(
            'SELECT p.modelo, su.nombre
     FROM App\Entity\Stock s
     INNER JOIN s.productoIdproducto p
     INNER JOIN s.sucursalIdsucursal su
     WHERE p.showonlinestore = 1 
     AND (p.precio IS NULL OR p.precio = \'\')
     AND p.status=:status AND p.cantidad=:cantidad
     '
        )->setParameters(["status" => '1', "cantidad" => '1']);

        $productosOnline = $query->getResult();

        return $this->render('actualizacion/queryparaquitarproductossincaracteristicas.html.twig', [
            'productosOnline' => $productosOnline,
        ]);
    }

    /**
     * @Route("/actualizar-productos-tienda-online", name="actualizar-productos-tienda-online")
     */
    public function actualizarProductos(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();

        // Arreglo de modelos específicos
        $modelosPermitidos = [
            'BS500602655', 'BW500400260', 'BW500507158', 'BW500805551', 'BW500903252', 'BW501709256',
            'BW501800856', 'BW501800956', 'CARRERA1118GOIT145', 'CARRERA1123807135', 'DIORBOBBYR1U10A0356',
            'DIORSIGNATUREB1U20B0355', 'DIORSOLARS1U95A0359', 'FE50005I09554', 'FE50012U03356', 'GU0002632C61',
            'GU764110B60', 'GU771902C143', 'GU771983W143', 'GU772132W60', 'HD084306856', 'HD086600256',
            'HD086603256', 'HD086805256', 'HD087505256', 'MM500301654', 'MM500600154', 'MM501506654',
            'MM501706653', 'MO503000155', 'MO503200153', 'MO503300155', 'MO503307155', 'MO503503254',
            'MO503702855', 'MO504000154', 'MO504009054', 'MO504100154', 'MO504200153', 'OR003602U60',
            'OR005620Q5221', 'OR005702A5320', 'OR005792X5320', 'OR006316A59', 'OR006432L6214', 'OR501209056',
            'OR501305456', 'SK539403255', 'SK539500154', 'SK539703254', 'SP500700260', 'SP500705660',
            'ULTRADIORMUB0B0200', 'VCO.90222BLU52', 'VCO.90223BLK54', 'VCO.A2910OC1.54', 'VCO.A2933.0C2.51',
            'VCO.SPAR4.LGRY.53', 'VDY.A0526.BLK.53', 'VDY.A0598.BLK.55', 'VDY.A0608.PDMI.55', 'VDY.A0608.RGLD.55',
            'VDY.B2389.DMI.54', 'VFL.9294K52', 'VFL.9956K59', 'VFL.I129K.09GU.54', 'VFL.I132K.0579.52',
            'VFL.I133K.01HR.54', 'VFL.I134K.06A7.52', 'VFL.I134K.09FH.52', 'VFL.I136K.06A7.53', 'VFL.I136K.0722.53',
            'VFL.I138K.0700.54', 'VFL.I140K.0579.55', 'VFL.I141K0700.54', 'VFL.I142K.09FH.49', 'VFL.I144K.09GU.54',
            'VFL.I147K.0304.54', 'VFL.I147K.0H59.54', 'VLE.19G22.BLK.54', 'VLE.21208.BLK.54', 'VLE.23097.AMA.52',
            'VLE.25368.BLK.54', 'VNV.01909DMI54', 'VNV.01914BLU54', 'VNV.01961BLU56', 'VNV.01968BLU',
            'VNV.23855.SBRG.50', 'VNV.90193.SBRN.54', 'VNV.90688.SDMI.53', 'VNV.N9019.SBRN.51',
            'VTO381COL.0301135', 'VTO412COL.0301135', 'VTO412COL.0323135', 'VTOA75NCOL.0700135',
            'VTOA97COL.09D6135', 'VTOA98COL.0752135'
        ];

        // Consulta para obtener los productos y sus relaciones
        $query = $em->createQuery(
            'SELECT s,p,fc, fm, m, me
         FROM App\Entity\Stock s
         INNER JOIN s.productoIdproducto p
         LEFT JOIN p.framecolorIdframecolor fc
         LEFT JOIN p.framematerialIdframematerial fm
         LEFT JOIN p.marcaIdmarca m
         LEFT JOIN p.medidaIdmedida me
         WHERE p.modelo IN (:modelos)
         AND p.showonlinestore = :showonlinestore
         AND p.status = :status'
        )->setParameters([
            'modelos' => $modelosPermitidos,
            'showonlinestore' => '1',
            'status' => '1'
        ]);

        $productos = $query->getResult();

        return $this->render('actualizacion/actualizarProductosOnline.html.twig', [
            'productos' => $productos,
        ]);
    }

    /**
     * @Route("/actualizar-productos-json", name="actualizar_productos_json")
     */
    public function actualizarProductosJson(Request $request): JsonResponse
    {
        $em = $this->getDoctrine()->getManager();

        // Construir la ruta completa al archivo JSON
        $projectDir = $this->getParameter('kernel.project_dir');
        $jsonPath = $projectDir . '/public/img/Tabla_buena_complete.json';

        if (!file_exists($jsonPath)) {
            return new JsonResponse(['success' => false, 'message' => 'Archivo JSON no encontrado en: ' . $jsonPath]);
        }

        $jsonData = file_get_contents($jsonPath);
        $productosData = json_decode($jsonData, true);

        if (!$productosData) {
            return new JsonResponse(['success' => false, 'message' => 'Error al decodificar el JSON']);
        }

        $mapaGeneros = [
            'Masculino' => 'H', // Asumiendo que 'H' es para 'Hombre'
            'Femenino' => 'M',  // Asumiendo que 'M' es para 'Mujer'
            'Unisex' => 'U'
        ];

        $modelosActualizados = [];

        foreach ($productosData as $data) {
            $modelo = $data['Modelo'] ?? null;

            if (!$modelo) {
                continue;
            }

            // Buscar los productos en la base de datos
            $query = $em->createQuery(
                'SELECT p
            FROM App\Entity\Producto p
            WHERE p.modelo = :modelo'
            )->setParameter('modelo', $modelo);

            $productos = $query->getResult(); // Obtener un array de productos

            if ($productos) {
                foreach ($productos as $producto) {
                    // Actualizar solo los campos vacíos

                    // Marca
                    if ($producto->getMarcaIdmarca() !== null) {
                        if (empty($producto->getMarcaIdmarca()->getNombre()) && isset($data['Marca'])) {
                            $producto->getMarcaIdmarca()->setNombre($data['Marca']);
                        }
                    } else {
                        if (isset($data['Marca'])) {
                            $nuevaMarca = new Marca();
                            $nuevaMarca->setNombre($data['Marca']);
                            $producto->setMarcaIdmarca($nuevaMarca);
                            $em->persist($nuevaMarca);
                        }
                    }

                    if (empty($producto->getGender()) && isset($data['Género'])) {
                        $generoOriginal = $data['Género'];
                        $generoMapeado = $mapaGeneros[$generoOriginal] ?? 'U'; // Valor por defecto 'U' si no coincide
                        $producto->setGender($generoMapeado);
                    }

                    // Diseño
                    if (empty($producto->getMoreinfo()) && isset($data['Diseño'])) {
                        $producto->setMoreinfo($data['Diseño']);
                    }

                    // Estilo
                    if (empty($producto->getDesign()) && isset($data['Estilo'])) {
                        $producto->setDesign($data['Estilo']);
                    }

                    // Material
                    if ($producto->getFramematerialIdframematerial() !== null) {
                        if (empty($producto->getFramematerialIdframematerial()->getMaterial()) && isset($data['Material'])) {
                            $producto->getFramematerialIdframematerial()->setMaterial($data['Material']);
                        }
                    } else {
                        // Crear un nuevo Framematerial si es necesario
                        if (isset($data['Material'])) {
                            $nuevoMaterial = new Framematerial();
                            $nuevoMaterial->setMaterial($data['Material']);
                            $producto->setFramematerialIdframematerial($nuevoMaterial);
                            $em->persist($nuevoMaterial);
                        }
                    }

                    // Color
                    if ($producto->getFramecolorIdframecolor() !== null) {
                        if (empty($producto->getFramecolorIdframecolor()->getColor()) && isset($data['Color'])) {
                            $producto->getFramecolorIdframecolor()->setColor($data['Color']);
                        }
                    } else {
                        // Crear un nuevo Framecolor si es necesario
                        if (isset($data['Color'])) {
                            $nuevoColor = new Framecolor();
                            $nuevoColor->setColor($data['Color']);
                            $producto->setFramecolorIdframecolor($nuevoColor);
                            $em->persist($nuevoColor);
                        }
                    }

                    // Medidas
                    if ($producto->getMedidaIdmedida() !== null) {
                        if (empty($producto->getMedidaIdmedida()->getNombre()) && isset($data['Medidas'])) {
                            $producto->getMedidaIdmedida()->setNombre($data['Medidas']);
                        }
                    } else {
                        // Crear una nueva Medida si es necesario
                        if (isset($data['Medidas'])) {
                            $nuevaMedida = new Medida();
                            $nuevaMedida->setNombre($data['Medidas']);
                            $producto->setMedidaIdmedida($nuevaMedida);
                            $em->persist($nuevaMedida);
                        }
                    }

                    // Descripción
                    if (empty($producto->getDescripcion()) && isset($data['Descripción'])) {
                        $producto->setDescripcion($data['Descripción']);
                    }

                    // Precio
                    if (empty($producto->getPrecio()) && isset($data['¿Tiene precio?'])) {
                        $producto->setPrecio($data['¿Tiene precio?'] === 'Sí');
                    }

                    $modelosActualizados[] = $modelo;
                }
            }
        }

        // Guardar los cambios
        $em->flush();

        return new JsonResponse([
            'success' => true,
            'modelos' => $modelosActualizados,
        ]);
    }


    /**
     * @Route("/actualizar-colores", name="actualizar-productos-tienda-online")
     */
    public function actualizarColoresProductos(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();

        // Arreglo de modelos específico

        /*$idsColores = array_unique([
            20, 25, 37, 44, 46, 48, 51, 52, 53, 54, 55, 56, 57, 58, 59, 62, 63, 64,
            65, 66, 67, 68, 69, 70, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83,
            84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101,
            102, 103, 104, 105, 106, 107, 108, 109, 111, 112, 113, 115, 116, 117, 120,
            121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 134, 135, 136,
            137, 139, 140, 141, 142, 143, 144, 145
        ]);*/

        $idsMateriales = array_unique([
            2, 3, 5, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25,
            26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44,
            45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63,
            64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82,
            83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101,
            102, 103, 104, 105, 106, 107
        ]);


        $query = $em->createQuery(
            'SELECT s, p, fc, fm
     FROM App\Entity\Stock s
     INNER JOIN s.productoIdproducto p
     LEFT JOIN p.framecolorIdframecolor fc
     LEFT JOIN p.framematerialIdframematerial fm
     WHERE fm.idframematerial IN (:idsmateriales)
     ORDER BY fc.color ASC
     '
        )->setParameters(['idsmateriales' => $idsMateriales]);

        $productos = $query->getResult();


        $queryColores = $em->createQuery(
            'SELECT fc.idframecolor, fc.color
     FROM App\Entity\Stock s
     INNER JOIN s.productoIdproducto p
     LEFT JOIN p.framecolorIdframecolor fc
     WHERE fc.idframecolor IS NOT NULL
     GROUP BY fc.idframecolor, fc.color
     ORDER BY fc.color ASC'
        );
        $colores = $queryColores->getResult();


        $queryMateriales = $em->createQuery(
            'SELECT fm.idframematerial, fm.material
     FROM App\Entity\Stock s
     INNER JOIN s.productoIdproducto p
     LEFT JOIN p.framematerialIdframematerial fm
     WHERE fm.idframematerial IS NOT NULL
     GROUP BY fm.idframematerial, fm.material
     ORDER BY fm.material ASC'
        );
        $materiales = $queryMateriales->getResult();


        return $this->render('actualizacion/actualizarColores.html.twig', [
            'productos' => $productos,
            'colores' => $colores,
            'materiales' => $materiales
        ]);
    }


    /**
     * @Route("/cambio-producto", name="cambioProducto")
     */
    public function cambioProductoMC(Request $request): JsonResponse
    {
        $em = $this->getDoctrine()->getManager();
        $idProducto = $request->get('productoId');
        $idColor = $request->get('productoSelect');
        $idMaterial = $request->get('idframeMaterialNew');

        if (!$idProducto) {
            return $this->json(['success' => false, 'msj' => 'El ID del producto es obligatorio']);
        }

        $producto = $em->getRepository(Producto::class)->find($idProducto);
        if (!$producto) {
            return $this->json(['success' => false, 'msj' => 'Producto no encontrado']);
        }

        try {
            if ($idColor) {
                $color = $em->getRepository(Framecolor::class)->find($idColor);
                if (!$color) {
                    throw new \Exception('Color no encontrado');
                }
                $producto->setFramecolorIdframecolor($color);
            }

            if ($idMaterial) {
                $material = $em->getRepository(Framematerial::class)->find($idMaterial);
                if (!$material) {
                    throw new \Exception('Material no encontrado');
                }
                $producto->setFramematerialIdframematerial($material);
            }

            $em->persist($producto);
            $em->flush();

            return $this->json(['success' => true, 'msj' => 'Producto actualizado correctamente']);
        } catch (\Exception $e) {
            return $this->json(['success' => false, 'msj' => $e->getMessage()]);
        }
    }

    /**
     * @Route("/actualizar-sucursales-flujoexpediente", name="actualizar-sucursales-flujoexpediente")
     */
    public function actualizarFlujos(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();

        // Consulta para obtener los flujos con sucursalIdsucursal = 0 y usuarios con sucursales asignadas
        $query = $em->createQuery(
            'SELECT fe
             FROM App\Entity\Flujoexpediente fe
             INNER JOIN fe.usuarioIdusuario u
             INNER JOIN u.sucursalIdsucursal su
             WHERE fe.sucursalIdsucursal = 0'
        );

        $flujos = $query->getResult();

        if (empty($flujos)){
            return $this->json(['success' => false]);
        }

        // Iterar sobre los flujos y asignar la sucursal del usuario
        foreach ($flujos as $flujo) {
            //se crea la variable usuario y obtenemos el usuario
            $usuario = $flujo->getUsuarioIdusuario();
            //ahora obtenemos la sucursal del usuario
            $sucursal = $usuario->getSucursalIdsucursal();
            //seteamos la sucursal delflujo
            $flujo->setSucursalIdsucursal($sucursal);
            $em->persist($flujo);
        }
        $em->flush();
        return $this->json(['success' => true]);
    }

    /**
     * @Route("/subir-clientes-excel", name="subir-clientes-excel")
     */

    public function subirClientes(Request $request):Response
    {
        $em = $this->getDoctrine()->getManager();

        return $this->render('actualizacion/subirClientes.html.twig',
            []
        );
    }

    /**
     * @Route("/clientes/guardar", name="guardar_clientes", methods={"POST"})
     */
    public function guardarClientes(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $clientes = json_decode($request->getContent(), true)['clientes']; // Obtener datos JSON

        foreach ($clientes as $cliente) {
            $nuevoCliente = new Cliente();
            $nuevoCliente->setNombre($cliente['nombre']);
            $nuevoCliente->setApellidoPaterno($cliente['apellidoPaterno'] ?? null);
            $nuevoCliente->setApellidoMaterno($cliente['apellidoMaterno'] ?? null);
            $nuevoCliente->setOcupacion($cliente['puesto'] ?? null);
            $nuevoCliente->setEdad($cliente['edad'] ?? null);
            $nuevoCliente->setEmail($cliente['correoElectronico'] ?? null);
            $nuevoCliente->setTelefono($cliente['celular'] ?? null);


            $empresaId = $cliente['empresaId'] ?? null;
            $empresa = $em->getRepository(Empresacliente::class)->find($empresaId);

            
            $nuevoCliente->setEmpresaclienteIdempresacliente($empresa);

            $em->persist($nuevoCliente);
        }

        $em->flush();

        return new JsonResponse(['status' => 'success']);
    }

    /**
     * @Route("/send-email", name="send-email", methods={"POST"})
     */
    public function sendEmailAction(Request $request, EntityManagerInterface $entityManager, ExamenVisualService $examenVisualService): JsonResponse
    {
        $em = $this->getDoctrine()->getManager();
        $id = $request->request->get('idcliente');

        if (!$id) {
            return new JsonResponse(['error' => 'ID del cliente no recibido'], 400);
        }


        try {
            $examenVisualService->sendEmailExamen(['objectId' => $id]);

            return new JsonResponse(['message' => 'Correo enviado exitosamente a cliente ID: ' . $id]);
        } catch (\Exception $e) {
            return new JsonResponse(['Error en el controlador' => $e->getMessage()], 500);
        }

        // 1) Capturar el idcliente de la petición (GET o POST)
        $id = $request->query->get('idcliente') ?: $request->request->get('idcliente');
        if (!$id) {
            return new Response('ID del cliente no recibido.', 400);
        }

        // 2) Buscar la graduación asociada (ajusta el criterio de búsqueda)
        $graduacion = $entityManager->getRepository(Graduacion::class)->findOneBy([
            'cliente_idcliente' => $id
        ]);

        if (!$graduacion) {
            return new Response('No se encontró la graduación para ese cliente.', 404);
        }

        // 3) Renderizar la vista Twig como HTML
        $html = $this->renderView('pdf/graduacion.html.twig', [
            'graduacion' => $graduacion
        ]);

        // 4) Configurar Dompdf
        $pdfOptions = new Options();
        $pdfOptions->set('defaultFont', 'Arial');

        $dompdf = new Dompdf($pdfOptions);
        $dompdf->loadHtml($html);

        // (Opcional) Ajusta el tamaño de papel y orientación:
        $dompdf->setPaper('A4', 'portrait');

        // 5) Generar el PDF
        $dompdf->render();

        // 6) Retornar el PDF como objeto Response
        //    - inline: muestra el PDF en el navegador
        //    - attachment: fuerza la descarga
        $output = $dompdf->output();
        $filename = sprintf('Reporte_Graduacion_cliente_%d.pdf', $id);

        return new Response($output, 200, [
            'Content-Type' => 'application/pdf',
            // 'Content-Disposition' => 'attachment; filename="'. $filename .'"'
            'Content-Disposition' => 'inline; filename="'. $filename .'"'
        ]);
    }

    /**
     * @Route("/ventas-pagos-repetidos-uam", name="ventas-pagos-repetidos-uam")
     */
    public function ventasPagosRepetidosUam(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $buttonClick = $request->request->get('buttonClick');

        if ($buttonClick) {

            $query = $em->createQuery("
            SELECT v.idventa AS ventaId, COUNT(p.idpago) AS totalPagos, SUM(p.automatic) AS totalAutomatic
            FROM App\Entity\Pago p
            INNER JOIN p.ventaIdventa v
            INNER JOIN v.tipoventaIdtipoventa tv
            WHERE tv.idtipoventa = :tipoVenta
              AND p.status = :status
            GROUP BY v.idventa
            HAVING totalPagos > 1
               AND totalAutomatic > 0
        ")->setParameters(['tipoVenta' => 1748, 'status' => 1]);

            $resultado = $query->getResult();

            foreach ($resultado as $resultados) {
                $ventaId = $resultados['ventaId'];
                //Se buscan los pagos con status 1 y automatic 0 para quitarlas
                $pagosDuplicados = $em->getRepository(\App\Entity\Pago::class)->findBy(['ventaIdventa' => $ventaId, 'status'=> 1, 'automatic'=> 0]);
                foreach ($pagosDuplicados as $pago) {
                    $pago->setStatus(0);
                }
            }

            $em->flush();

            return new JsonResponse([
                'exito' => true,
                'msj'   => 'Pagos repetidos inactivados correctamente.'
            ]);
        }

        else {

            $query = $em->createQuery("
            SELECT 
                v.idventa AS venta, 
                COUNT(p.idpago) AS totalPagos, 
                SUM(p.automatic) AS totalAutomatic
            FROM App\Entity\Pago p
            INNER JOIN p.ventaIdventa v
            INNER JOIN v.tipoventaIdtipoventa tv
            WHERE tv.idtipoventa = :tipoVenta
              AND p.status = :status
            GROUP BY v.idventa
            HAVING totalPagos > 1
               AND totalAutomatic > 0
            ORDER BY v.idventa ASC
        ")->setParameters(['tipoVenta' => 1748, 'status' => 1]);

            $resultado = $query->getResult();

            return $this->render('actualizacion/quitarPagosDuplicados.html.twig', [
                'resultado' => $resultado
            ]);
        }
    }

    /**
     * @Route("/update-venta-colection", name="update_venta_colection")
     */
    public function updateVentas(EntityManagerInterface $em): JsonResponse
    {
        try {
            $ventas = $em->getRepository(Venta::class)->findAll();
            $updatedCount = 0;

            foreach ($ventas as $venta) {
                // Calcular suma de productos
                $suma = 0;
                $stockVentas = $em->getRepository(Stockventa::class)->findBy(['ventaIdventa' => $venta]);

                foreach ($stockVentas as $sv) {
                    $precio = $sv->getPreciofinal() ?? $sv->getPrecio();
                    $cantidad = $sv->getCantidad() ?? 1;
                    $suma += (float)$precio * (float)$cantidad;
                }

                $convenio = $venta->getConvenio();
                $esUAM = ($venta->getCotizacion() === '1' && $convenio === 'UAM');

                if ($esUAM) {
                    $subtotal = $suma / 1.16;
                } else {
                    $iva = (float)$venta->getIva();
                    $subtotal = $suma - $iva;
                }

                $venta->setTotal(number_format($subtotal, 2, '.', ''));
                $venta->setPagado(number_format($suma, 2, '.', ''));
                $updatedCount++;
            }

            $em->flush();

            return new JsonResponse([
                'status' => 'success',
                'message' => "¡Reparación completada! Ventas actualizadas: $updatedCount",
                'data' => [
                    'ventas_afectadas' => $updatedCount,
                    'nota' => 'Base de datos actualizada'
                ]
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'status' => 'error',
                'message' => '¡Maldición! Algo salió mal: ' . $e->getMessage(),
                'sarcasmo' => 'Este error es más grave que los chistes de Peter'
            ], 500);
        }
    }

    /**
     * @Route("/traspaso-masivo-excel", name="traspaso_masivo_excel")
     */
    public function traspasoMasivoExcel(): Response
    {
        // Obtener la sucursal destino (198)
        $em = $this->getDoctrine()->getManager();
        $sucursalDestino = $em->getRepository(Sucursal::class)->find(198);
        
        if (!$sucursalDestino) {
            $this->addFlash('danger', 'No se encontró la sucursal destino (ID: 198)');
            return $this->redirectToRoute('homepage');
        }
        
        return $this->render('actualizacion/traspaso-masivo-excel.html.twig', [
            'sucursalDestino' => $sucursalDestino
        ]);
    }
    
    /**
     * @Route("/procesar-excel-traspaso", name="procesar_excel_traspaso")
     */
    public function procesarExcelTraspaso(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $skus = [];
        $productosEncontrados = [];
        $mensaje = '';
        $exito = false;
        
        try {
            // Verificar si se ha subido un archivo
            $archivo = $request->files->get('archivo_excel');
            
            if (!$archivo) {
                throw new \Exception('No se ha subido ningún archivo');
            }
            
            // Verificar que sea un archivo Excel
            $extensionesPermitidas = ['xlsx', 'xls'];
            $extension = $archivo->getClientOriginalExtension();
            
            if (!in_array($extension, $extensionesPermitidas)) {
                throw new \Exception('El archivo debe ser un Excel (.xlsx o .xls)');
            }
            
            // Cargar el archivo Excel
            $spreadsheet = IOFactory::load($archivo->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();
            $highestRow = $worksheet->getHighestRow();
            
            // Extraer SKUs del archivo Excel (columnas B-E)
            for ($row = 2; $row <= $highestRow; $row++) {
                for ($col = 'B'; $col <= 'E'; $col++) {
                    $sku = trim($worksheet->getCell($col . $row)->getValue());
                    if (!empty($sku)) {
                        $skus[] = $sku;
                    }
                }
            }
            
            // Verificar si se encontraron SKUs
            if (empty($skus)) {
                throw new \Exception('No se encontraron SKUs en el archivo Excel');
            }
            
            // Buscar productos con cantidad 1 en sucursales distintas a 198
            $query = $em->createQuery(
                'SELECT p.idproducto, p.codigo, p.nombre, s.idsucursal, s.nombre as sucursal_nombre, st.idstock, st.cantidad
                FROM App\Entity\Producto p
                JOIN p.stocks st
                JOIN st.sucursalIdsucursal s
                WHERE p.codigo IN (:skus)
                AND st.cantidad = 1
                AND st.status = :status
                AND s.idsucursal != 198
                ORDER BY p.codigo, s.nombre'
            )->setParameters([
                'skus' => $skus,
                'status' => '1'
            ]);
            
            $productosEncontrados = $query->getResult();
            
            if (empty($productosEncontrados)) {
                throw new \Exception('No se encontraron productos con cantidad 1 en sucursales distintas a 198');
            }
            
            $exito = true;
            $mensaje = 'Se encontraron ' . count($productosEncontrados) . ' productos con cantidad 1 en sucursales distintas a 198';
            
        } catch (\Exception $e) {
            $mensaje = 'Error: ' . $e->getMessage();
        }
        
        // Obtener la sucursal destino (198)
        $sucursalDestino = $em->getRepository(Sucursal::class)->find(198);
        
        if (!$sucursalDestino) {
            $this->addFlash('danger', 'No se encontró la sucursal destino (ID: 198)');
            return $this->redirectToRoute('homepage');
        }
        
        return $this->render('actualizacion/seleccionar-productos-traspaso.html.twig', [
            'exito' => $exito,
            'mensaje' => $mensaje,
            'productos' => $productosEncontrados,
            'sucursalDestino' => $sucursalDestino
        ]);
    }
    
    /**
     * @Route("/ejecutar-traspaso-masivo", name="ejecutar_traspaso_masivo")
     */
    public function ejecutarTraspasoMasivo(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $productosSeleccionados = $request->request->get('productos', []);
        $mensaje = '';
        $exito = false;
        $productosTransferidos = [];
        $productosNoTransferidos = [];
        
        if (empty($productosSeleccionados)) {
            $this->addFlash('danger', 'No se seleccionaron productos para transferir');
            return $this->redirectToRoute('traspaso_masivo_excel');
        }
        
        // Obtener la sucursal destino (198)
        $sucursalDestino = $em->getRepository(Sucursal::class)->find(198);
        
        if (!$sucursalDestino) {
            $this->addFlash('danger', 'No se encontró la sucursal destino (ID: 198)');
            return $this->redirectToRoute('homepage');
        }
        
        // Iniciar transacción
        $em->beginTransaction();
        
        try {
            $fechaActual = new \DateTime();
            $usuario = $this->getUser();
            
            foreach ($productosSeleccionados as $idstock) {
                // Obtener el stock original
                $stockOriginal = $em->getRepository(Stock::class)->find($idstock);
                
                if (!$stockOriginal || $stockOriginal->getCantidad() != 1 || $stockOriginal->getStatus() != '1') {
                    $productosNoTransferidos[] = [
                        'idstock' => $idstock,
                        'motivo' => 'Stock no encontrado o cantidad diferente de 1'
                    ];
                    continue;
                }
                
                $producto = $stockOriginal->getProductoIdproducto();
                $sucursalOrigen = $stockOriginal->getSucursalIdsucursal();
                
                // Verificar si el producto ya tiene stock en la sucursal destino
                $stockExistente = $em->getRepository(Stock::class)->findOneBy([
                    'productoIdproducto' => $producto,
                    'sucursalIdsucursal' => $sucursalDestino,
                    'status' => '1'
                ]);
                
                // Actualizar el stock original (cantidad = 0)
                $stockOriginal->setCantidad(0);
                $stockOriginal->setModificacion($fechaActual);
                $em->persist($stockOriginal);
                
                // Registrar movimiento de stock (salida)
                $movimientoSalida = new Stockmovimiento();
                $movimientoSalida->setStockIdstock($stockOriginal);
                $movimientoSalida->setCantidad(1);
                $movimientoSalida->setTipo('salida');
                $movimientoSalida->setCreacion($fechaActual);
                $movimientoSalida->setUsuarioIdusuario($usuario);
                $movimientoSalida->setNotas('Traspaso masivo a sucursal 198');
                $em->persist($movimientoSalida);
                
                if ($stockExistente) {
                    // Actualizar stock existente en sucursal destino
                    $stockExistente->setCantidad($stockExistente->getCantidad() + 1);
                    $stockExistente->setModificacion($fechaActual);
                    $em->persist($stockExistente);
                    
                    // Registrar movimiento de stock (entrada)
                    $movimientoEntrada = new Stockmovimiento();
                    $movimientoEntrada->setStockIdstock($stockExistente);
                    $movimientoEntrada->setCantidad(1);
                    $movimientoEntrada->setTipo('entrada');
                    $movimientoEntrada->setCreacion($fechaActual);
                    $movimientoEntrada->setUsuarioIdusuario($usuario);
                    $movimientoEntrada->setNotas('Traspaso masivo desde sucursal ' . $sucursalOrigen->getIdsucursal());
                    $em->persist($movimientoEntrada);
                } else {
                    // Crear nuevo stock en sucursal destino
                    $nuevoStock = new Stock();
                    $nuevoStock->setProductoIdproducto($producto);
                    $nuevoStock->setSucursalIdsucursal($sucursalDestino);
                    $nuevoStock->setCantidad(1);
                    $nuevoStock->setStatus('1');
                    $nuevoStock->setCreacion($fechaActual);
                    $nuevoStock->setModificacion($fechaActual);
                    $nuevoStock->setTipo('n');
                    $em->persist($nuevoStock);
                    
                    // Registrar movimiento de stock (entrada)
                    $movimientoEntrada = new Stockmovimiento();
                    $movimientoEntrada->setStockIdstock($nuevoStock);
                    $movimientoEntrada->setCantidad(1);
                    $movimientoEntrada->setTipo('entrada');
                    $movimientoEntrada->setCreacion($fechaActual);
                    $movimientoEntrada->setUsuarioIdusuario($usuario);
                    $movimientoEntrada->setNotas('Traspaso masivo desde sucursal ' . $sucursalOrigen->getIdsucursal());
                    $em->persist($movimientoEntrada);
                }
                
                $productosTransferidos[] = [
                    'idstock' => $idstock,
                    'codigo' => $producto->getCodigo(),
                    'nombre' => $producto->getNombre(),
                    'sucursal_origen' => $sucursalOrigen->getNombre()
                ];
            }
            
            // Confirmar transacción
            $em->flush();
            $em->commit();
            
            $exito = true;
            $mensaje = 'Se transfirieron ' . count($productosTransferidos) . ' productos a la sucursal ' . $sucursalDestino->getNombre();
            $this->addFlash('success', $mensaje);
            
        } catch (\Exception $e) {
            // Revertir transacción en caso de error
            $em->rollback();
            $mensaje = 'Error al realizar el traspaso: ' . $e->getMessage();
            $this->addFlash('danger', $mensaje);
        }
        
        return $this->render('actualizacion/resultado-traspaso.html.twig', [
            'exito' => $exito,
            'mensaje' => $mensaje,
            'productosTransferidos' => $productosTransferidos,
            'productosNoTransferidos' => $productosNoTransferidos,
            'sucursalDestino' => $sucursalDestino
        ]);
    }
}