<?php

namespace App\Controller;

use App\Entity\Productimages;
use App\Entity\Producto;
use App\Entity\Stock;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use App\Entity\Anuncios;
use Doctrine\ORM\EntityManagerInterface;
use App\Entity\Categoriaanuncio;

class AbrirVisorController extends AbstractController
{

    /**
     * @Route("/abrir-visor-documentos", name="app_abrir_visor")
     */
    public function abrirVisorDoc(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $iddocumento = $request->get("iddocumentos");

        // Consulta
        $query = $em->createQuery('
        SELECT d.archivo
        FROM App\Entity\Documentos d
        WHERE d.status = :status AND d.iddocumentos = :iddocumentos'
        )->setParameters(['status' => "1", 'iddocumentos' => $iddocumento]);

        // Obtener el resultado o null
        $documento = $query->getOneOrNullResult();

        // Verificar si el documento fue encontrado
        if ($documento !== null) {
            $nombredocumento = $documento['archivo'];
            $rutaCarpeta = $this->getParameter('carpetaDocumentos');
        } else {
            // Manejo del caso donde no se encuentra el documento
            return new Response("Documento no encontrado o no disponible.", 404);
        }

        // Renderizar la vista
        return $this->render('abrir_visor/visor.html.twig', [
            'nombreDocumento' => $nombredocumento,
            'carpetaDocumentos' => $rutaCarpeta
        ]);
    }

    /**
     * @Route("/abrir-visor-anuncios", name="app_abrir_visor_anuncios")
     */
    public function abrirVisorAnuncio(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $idanuncio = $request->get("idanuncios");

        // Consulta
        $query = $em->createQuery('
        SELECT a.imagenPrincipal
        FROM App\Entity\Anuncios a
        WHERE a.status = :status AND a.idanuncios = :idanuncios
        ')->setParameters(['status' => "1", 'idanuncios' => $idanuncio]);

        // Obtener el resultado o null
        $anuncio = $query->getOneOrNullResult();

        // Verificar si el anuncio fue encontrado
        if ($anuncio !== null) {
            $nombreAnuncio = $anuncio['imagenPrincipal'];

            $rutaCarpetaRel = $this->getParameter('carpetaAnuncios');
        } else {
            return new Response("Anuncio no encontrado o no disponible.", 404);
        }

        return $this->render('abrir_visor/visor-anuncio.html.twig', [
            'nombreAnuncio'   => $nombreAnuncio,
            'carpetaAnuncios' => $rutaCarpetaRel,
        ]);
    }


    /**
     * @Route("/abrir-enterprise-logos", name="app-enterprise-visor")
     */
    public function openEnterpriseVisor(Request $request): Response
    {

        $filename = $request->get("logoname");
        $filetype = $request->get("filetype");
        $enterpriseName = $request->get("enterpriseName");

        if ($filetype == "logo") $fileDirectory = $this->getParameter('carpetaLogos');
        else {
            $fileDirectory = $this->getParameter('carpetaPublicidades');
            $fileDirectory .= "/" . $enterpriseName;
        }
        $fileExist = file_exists($fileDirectory . "/" . $filename);


        return $this->render('abrir_visor/logo-visor.html.twig', [
            'filename' => $filename,
            'fileDirectory' => $fileDirectory,
            'fileExist' => $fileExist,
        ]);
    }

    /**
     * @Route("/open-product-images-visor", name="visor-open-product-images-visor")
     */
    public function openProductImagesVisor(Request $request): Response
    {

        $productId = $request->get("productId");


        return $this->render('product_images/product-images-index.html.twig', [
            'productId' => $productId,
        ]);
    }

    /**
     * @Route("/get-product-images-table", name="visor-get-product-images-table")
     */
    public function getProductImagesTable(Request $request): Response
    {

        $em = $this->getDoctrine()->getManager();
        $productId = $request->get("productId");

        $query = $em->createQuery(
            'SELECT p.modelo, pi.mainimage, pi.idproductimages, pi.status, pi.filename
        FROM App\Entity\Productimages pi
        INNER JOIN pi.productoIdproducto p
        WHERE pi.status=:status and pi.productoIdproducto=:productId
        '
        )->setParameters(['status' => "1", 'productId' => $productId]);

        $productImages = $query->getResult();

        return $this->render('product_images/product-images-table.html.twig', [
            'productImages' => $productImages,
        ]);


    }

    /**
     * @Route("/upload-product-image", name="visor-upload-product-image")
     */
    public function uploadProductImage(Request $request)
    {
        $success = false;
        $msg = "";

        $file = $request->files->get('file');
        $productId = $request->get('productId');

        $em = $this->getDoctrine()->getManager();

        $Product = $em->getRepository(Producto::class)->findOneBy(array('idproducto' => $productId));

        try {
            if (!$file) {
                throw new \Exception('Error al cargar el archivo');
            } else {
                if ($Product) {

                    $path = $this->getParameter('productImagesFolder');
                    $path = $path . "/" . $productId . '/';
                    if (!file_exists($path)) mkdir($path, 0777, true);

                    $ext = $file->getClientOriginalExtension();

                    $fileName = $this->sanitize($productId . '-' . time()) . '.' . $ext;

                    $file->move(
                        $path,
                        $fileName
                    );

                    $query = $em->createQuery(
                        'SELECT MAX(pi.order) as maxOrder
                        FROM App\Entity\Productimages pi
                        WHERE pi.status=:status and pi.productoIdproducto=:productId
                        '
                    )->setParameters(['status' => "1", 'productId' => $productId]);

                    $curMaxOrder = $query->getOneOrNullResult();

                    $ProductImage = new Productimages();

                    if ($curMaxOrder['maxOrder']) $ProductImage->setOrder(intval($curMaxOrder['maxOrder']) + 1);
                    else {
                        $ProductImage
                            ->setOrder(1)
                            ->setMainimage('1');
                    }

                    $ProductImage
                        ->setProductoIdproducto($Product)
                        ->setFilename($fileName);

                    $em->persist($ProductImage);
                    $em->flush();

                    $success = true;


                } else throw new \Exception('No se encontró el producto');
            }
        } catch (\Exception $e) {
            $msg .= $e->getMessage();
        }


        return new JsonResponse(["msg" => $msg, "success" => $success]);
    }

    /**
     * @Route("/delete-product-image", name="visor-delete-product-image")
     */
    public function deleteProductImage(Request $request): Response
    {

        $em = $this->getDoctrine()->getManager();

        $success = false;
        $msg = "";

        $productId = $request->get("productId");
        $productImageId = $request->get("productImageId");

        try {

            $ProductImage = $em->getRepository(Productimages::class)->findOneBy(array('idproductimages' => $productImageId));

            if ($ProductImage) {

                if ($ProductImage->getStatus() == '1') {

                    $query = $em->createQuery(
                        'SELECT pi
                        FROM App\Entity\Productimages pi
                        WHERE pi.status=:status and pi.productoIdproducto=:productId
                        ORDER BY pi.idproductimages
                        '
                    )->setParameters(['status' => "1", 'productId' => $productId]);
                    $productImages = $query->getResult();

                    $mainImage = ($ProductImage->getMainimage() == '1') ? true : false;
                    $setMainImage = false;

                    foreach ($productImages as $tempProductImage) {

                        if ($mainImage) {
                            if ($tempProductImage->getIdproductimages() != $ProductImage->getIdproductimages() && !$setMainImage) {
                                $tempProductImage->setMainimage('1');
                                $setMainImage = true;
                            }
                        }

                        if ($tempProductImage->getIdproductimages() == $ProductImage->getIdproductimages()) {
                            $tempProductImage->setStatus('0');
                            $tempProductImage->setMainimage('0');
                        } else if ($tempProductImage->getOrder() > $ProductImage->getOrder()) $tempProductImage->setOrder($tempProductImage->getOrder() - 1);

                        $em->persist($tempProductImage);
                    }

                    $em->flush();
                    $success = true;

                } else throw new \Exception('La imagen ya está eliminada');

            } else throw new \Exception('No se encontró el producto');

        } catch (\Exception $e) {
            $msg .= $e->getMessage();
        }

        return new JsonResponse(["msg" => $msg, "success" => $success]);

    }

    /**
     * @Route("/open-product-image-visor", name="visor-open-product-image-visor")
     */
    public function openProductImageVisor(Request $request): Response
    {

        $filename = $request->get("filename");
        $productId = $request->get("productId");

        $fileDirectory = $this->getParameter('productImagesFolder');

        $fileDirectory .= '/' . $productId;

        $fileExist = file_exists($fileDirectory . "/" . $filename);


        return $this->render('abrir_visor/logo-visor.html.twig', [
            'filename' => $filename,
            'fileDirectory' => $fileDirectory,
            'fileExist' => $fileExist,
        ]);
    }

    /**
     * Function: sanitize
     * Returns a sanitized string, typically for URLs.
     *
     * Parameters:
     *     $string - The string to sanitize.
     *     $force_lowercase - Force the string to lowercase?
     *     $anal - If set to *true*, will remove all non-alphanumeric characters.
     */
    function sanitize($string, $force_lowercase = true, $anal = false)
    {
        $strip = array("~", "`", "!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "_", "=", "+", "[", "{", "]",
            "}", "\\", "|", ";", ":", "\"", "'", "&#8216;", "&#8217;", "&#8220;", "&#8221;", "&#8211;", "&#8212;",
            "â€”", "â€“", ",", "<", ".", ">", "/", "?");
        $clean = trim(str_replace($strip, "", strip_tags($string)));
        $clean = preg_replace('/\s+/', "-", $clean);
        $clean = ($anal) ? preg_replace("/[^a-zA-Z0-9]/", "", $clean) : $clean;
        return ($force_lowercase) ?
            (function_exists('mb_strtolower')) ?
                mb_strtolower($clean, 'UTF-8') :
                strtolower($clean) :
            $clean;
    }


    /**
     * @Route("/abrir-visor-ventas", name="app_abrir_visor_ventas")
     */
    public function abrirVisorVenta(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $idventa = $request->get("idventa");

        $query = $em->createQuery(
            'SELECT v.idventa,
        v.archivoautorizacion
        FROM App\Entity\Venta v
        WHERE v.status=:status and v.idventa=:idventa
        '
        )->setParameters(['status' => "1", 'idventa' => $idventa]);

        $documentov = $query->getOneOrNullResult();

        $nombredocumento = $documentov['archivoautorizacion'];
        $rutaCarpeta = $this->getParameter('carpetaDocumentosVenta');

        return $this->render('CRUD/visor1.html.twig', [
            'nombreDocumento' => $nombredocumento,
            'carpetaDocumentosVenta' => $rutaCarpeta
        ]);


    }

    /**
     * @Route("/admin/stockalmacen/change-precio/{id}", name="admin.stockalmacen.changePrecio", methods={"POST"})
     */
    public function changePrice(Request $request, $id): Response
    {
        $em = $this->getDoctrine()->getManager();

        $nuevoPrecio = $request->request->get('priceStock');

        // Buscar el stock por ID
        $stock = $em->getRepository(Stock::class)->find($id);

        if (!$stock) {
            return new JsonResponse(["msg" => "Stock no encontrado", "success" => false]);
        }

        $stock->setPrecio($nuevoPrecio); // Asegúrate de que esta línea esté presente

        $em->persist($stock);
        $em->flush();

        return new JsonResponse(["msg" => "Precio actualizado con éxito", "success" => true]);
    }


    /**
     * @Route("/visor-sucursales", name="visor-sucursales")
     */
    public function visorSucursales(Request $request): Response
    {

        $em = $this->getDoctrine()->getManager();
        $iddocumento = $request->get("iddocumento");


        $query = $em->createQuery('SELECT 
        d.archivo
        FROM App\Entity\Documentos d
       
        WHERE d.status=:status and d.iddocumentos=:iddocumentos
        '
        )->setParameters(['status' => "1", 'iddocumentos' => $iddocumento]);

        $documento = $query->getOneOrNullResult();


        $nombredocumento = $documento['archivo'];
        $rutaCarpeta = $this->getParameter('carpetaDocumentos');


        return $this->render('abrir_visor/visor.html.twig', [
            'nombreDocumento' => $nombredocumento,
            'carpetaDocumentos' => $rutaCarpeta
        ]);
    }

    /**
     * @Route("/admin/anuncios", name="admin_anuncios_index")
     */
    public function index(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $anuncios = $em->getRepository(Anuncios::class)->findBy(['status' => '1']);

        return $this->render('admin/appOptimo/index.html.twig', [
            'anuncios' => $anuncios,
        ]);
    }

    /**
     * @Route("/admin/anuncios/new", name="admin_anuncios_new", methods={"GET","POST"})
     */
    public function new(Request $request, EntityManagerInterface $em): Response
    {
        if ($request->isMethod('POST')) {
            try {
                $titulo = $request->request->get('titulo');
                $position = $request->request->get('position');
                $caption = $request->request->get('caption');

                $file = $request->files->get('imagenprincipal');
                $filename = null;

                if ($file) {
                    $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/svg+xml'];
                    if (!in_array($file->getMimeType(), $allowedMimeTypes)) {
                        $this->addFlash('error', 'Formato de imagen no permitido.');
                        return $this->redirectToRoute('admin_anuncios_new');
                    }

                    $filename = uniqid() . '.' . $file->guessExtension();
                    $uploadsDirectory = $this->getParameter('carpetaAnuncios');
                    $file->move($uploadsDirectory, $filename);
                }

                $categoriaRepo = $em->getRepository(Categoriaanuncio::class);
                $categoria = $categoriaRepo->find(4);

                if (!$categoria) {
                    throw new \Exception('Categoría no encontrada.');
                }


                $anuncio = new Anuncios();
                $anuncio->setTitulo($titulo);
                $anuncio->setFechacreacion(new \DateTime());
                $anuncio->setFechaactualizacion(new \DateTime());
                $anuncio->setTexto('');
                $anuncio->setEnviocorreo('0');
                $anuncio->setStatus('1');
                $anuncio->setCategoriaanuncioIdcategoriaanuncio($categoria);
                $anuncio->setImagenprincipal($filename);

                // Persistir y guardar
                $em->persist($anuncio);
                $em->flush();

                $this->addFlash('success', 'Anuncio creado correctamente.');
                return $this->redirectToRoute('admin_anuncios_index');
            } catch (\Exception $e) {
                // Manejo de la excepción: log, flash message y redirección
                $this->addFlash('error', 'Error al crear el anuncio: ' . $e->getMessage());
                return $this->redirectToRoute('admin_anuncios_new');
            }
        }

        return $this->render('admin/anuncios/new_manual.html.twig');
    }


    /**
     * @Route("/admin/anuncios/{id}/edit", name="admin_anuncios_edit", methods={"GET","POST"})
     */
    public function edit(Request $request, Anuncios $anuncio, EntityManagerInterface $em): Response
    {
        if ($request->isMethod('POST')) {

            $titulo = $request->request->get('titulo');
            $position = $request->request->get('position');
            $caption = $request->request->get('caption');

            // Actualizar datos en la entidad
            $anuncio->setTitulo($titulo);
            // Puedes actualizar otros campos, por ejemplo:
            // $anuncio->setTexto($request->request->get('texto'));
            // $anuncio->setPosition($position);  // Si tu entidad tuviera este campo
            // $anuncio->setCaption($caption);

            // Procesar subida de una nueva imagen, si se ha enviado un archivo
            $file = $request->files->get('imagenprincipal');
            if ($file) {
                $allowedMimeTypes = ['image/jpeg', 'image/png'];
                if (!in_array($file->getMimeType(), $allowedMimeTypes)) {
                    $this->addFlash('error', 'Formato de imagen no permitido.');
                    return $this->redirectToRoute('admin_anuncios_edit', ['id' => $anuncio->getIdanuncios()]);
                }
                $filename = uniqid() . '.' . $file->guessExtension();
                $uploadsDirectory = $this->getParameter('uploads_directory');
                $file->move($uploadsDirectory, $filename);
                $anuncio->setImagenprincipal($filename);
            }

            // Actualizar fecha de actualización
            $anuncio->setFechaactualizacion(new \DateTime());

            $em->flush();
            $this->addFlash('success', 'Anuncio actualizado correctamente.');
            return $this->redirectToRoute('admin_anuncios_index');
        }

        return $this->render('admin/anuncios/edit_manual.html.twig', [
            'anuncio' => $anuncio,
        ]);
    }

}