<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use App\Entity\Departurecause;
use App\Entity\Merma;
use App\Entity\Salidaproductodoc;
use App\Entity\Stock;
use App\Entity\Stockventa;
use App\Form\MermaType;

use DateTime;

class MermaController extends AbstractController
{
    /**
     * @Route("/merma", name="app_merma")
     */
    public function index(): Response
    {
        return $this->render('merma/index.html.twig', [
            'controller_name' => 'MermaController',
        ]);
    }

    /**
     * @Route("/select-output-type", name="merma-select-output-type")
     */
    public function selectOutputType(): Response
    {

        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT dt.iddeparturetype, dt.name
            FROM App\Entity\Departuretype dt
            WHERE dt.status =:status 
            '
        )->setParameters(['status' => '1']);
        $departureTypes = $query->getResult();

        return $this->render('merma/merma-select-output-type.html.twig', [
            'departureTypes' => $departureTypes
        ]);
    }

    /**
     * @Route("/select-departure-cause", name="merma-select-departure-cause")
     */
    public function selectDepartureCause(Request $request): Response
    {

        $em = $this->getDoctrine()->getManager();
        $departureTypeId = $request->get("departureTypeId");

        $query = $em->createQuery(
            'SELECT dc.iddeparturecause, dc.name
            FROM App\Entity\Departurecause dc
            WHERE dc.status =:status AND dc.departuretypeIddeparturetype =:departureTypeId
            '
        )->setParameters(['status' => '1', 'departureTypeId' => $departureTypeId]);
        $departureCauses = $query->getResult();

        return $this->render('merma/merma-select-departure-cause.html.twig', [
            'departureCauses' => $departureCauses
        ]);
    }

    /**
     * Function: sanitize
     * Returns a sanitized string, typically for URLs.
     *
     * Parameters:
     *     $string - The string to sanitize.
     *     $force_lowercase - Force the string to lowercase?
     *     $anal - If set to *true*, will remove all non-alphanumeric characters.
     */
    function sanitize($string, $force_lowercase = true, $anal = false)
    {
        $strip = array(
            "~",
            "`",
            "!",
            "@",
            "#",
            "$",
            "%",
            "^",
            "&",
            "*",
            "(",
            ")",
            "_",
            "=",
            "+",
            "[",
            "{",
            "]",
            "}",
            "\\",
            "|",
            ";",
            ":",
            "\"",
            "'",
            "&#8216;",
            "&#8217;",
            "&#8220;",
            "&#8221;",
            "&#8211;",
            "&#8212;",
            "â€”",
            "â€“",
            ",",
            "<",
            ".",
            ">",
            "/",
            "?"
        );
        $clean = trim(str_replace($strip, "", strip_tags($string)));
        $clean = preg_replace('/\s+/', "-", $clean);
        $clean = ($anal) ? preg_replace("/[^a-zA-Z0-9]/", "", $clean) : $clean;
        return ($force_lowercase) ?
            (function_exists('mb_strtolower')) ?
            mb_strtolower($clean, 'UTF-8') :
            strtolower($clean) :
            $clean;
    }

    /**
     * @Route("/agregar-merma", name="agregar-merma")
     */
    public function agregarMerma(Request $request): Response
    {

        $exito = false;
        $msj = "";
        $em = $this->getDoctrine()->getManager();
        $idstock = $request->get("idstock");
        $cantidad = $request->get("cantidad");
        $departureCauseId = $request->get("departureCauseId");
        $idmerma = $request->get("idmerma");
        $idstockventa = $request->get("idstockventa");

        $query = $em->createQuery(
            'SELECT  e.idempresa, e.nombre
                FROM App\Entity\Empresa e
               where e.status=:status
               '
        )->setParameters(['status' => 1]);
        $enterprises = $query->getResult();

        $Merma = $em->getRepository(Merma::class)->findOneBy(['idmerma' => $idmerma]) ?? new Merma();
        $DepartureCause = $em->getRepository(Departurecause::class)->findOneBy(array('iddeparturecause' => $departureCauseId));

        $form = $this->createForm(MermaType::class, $Merma);
        $form->handleRequest($request);


        try {
            if ($form->isSubmitted() && $form->isvalid()) {

                $User = $this->getUser();
                $file = $request->files->get('file');
                $path = $this->getParameter('carpetaSalidas');

                $Merma = $form->getData();

                $Merma->setUsuarioIdusuario($User)
                    ->setDeparturecauseIddeparturecause($DepartureCause);

                if ($DepartureCause->getName() == "Garantía de venta"){
                    $Stockventa = $em->getRepository(Stockventa::class)->findOneBy(array('idstockventa' => $idstockventa));
                    if ($Stockventa) $Merma->setStockventaIdstockventa($Stockventa);
                    else throw new \Exception('No se encontró el producto de la garantía');
                    
                }

                $Stock = $em->getRepository(Stock::class)->findOneBy(array('idstock' => $idstock));
                $StockPrevio = $Merma->getStockIdstock();


                if ($DepartureCause) {

                    $prefix = $DepartureCause->getDeparturetypeIddeparturetype()->getPrefix();

                    if ($Stock) {

                        if ($StockPrevio and $StockPrevio->getIdstock() != $idstock) {
                            $StockPrevio->setCantidad($StockPrevio->getCantidad() + $Merma->getCantidad());
                        }

                        $Stock->setCantidad($Stock->getCantidad() - intval($cantidad));

                        $em->persist($Stock);
                        $em->flush();

                        $Merma->setStockIdstock($Stock);
                        if (!$Merma->getIdmerma()) $Merma->setFechacreacion(new DateTime());
                        $Merma->setCantidad(intval($cantidad));
                        $em->persist($Merma);
                        $em->flush();
                        $Merma->setFolio($prefix . '-' . $Merma->getIdmerma());
                        $em->persist($Merma);
                        $em->flush();


                        if ($file) {
                            $path .= "/" . $User->getIdusuario() . '/';
                            if (!file_exists($path)) {
                                mkdir($path, 0777, true);
                            }

                            $originalFilename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
                            $newFilename = $this->sanitize($originalFilename . '-' . uniqid()) . '.' . $file->guessExtension();

                            try {
                                $file->move($path, $newFilename);

                                $SalidaproductoDoc = new Salidaproductodoc();
                                $SalidaproductoDoc->setFilemerma($newFilename); // Se debe guardar el nombre del archivo, no el path
                                $SalidaproductoDoc->setStatus(1);
                                $SalidaproductoDoc->setCreacion(new \DateTime());
                                $SalidaproductoDoc->setTipo("tipo"); // Asegúrate que este valor es correcto
                                $SalidaproductoDoc->setNombre($newFilename); // Nombre del archivo
                                $SalidaproductoDoc->setMermaIdmerma($Merma);
                                $SalidaproductoDoc->setUsuarioIdusuario($User);

                                $em->persist($SalidaproductoDoc);
                                $em->flush();


                                $exito = true;
                                $msj = "Archivo subido y datos guardados correctamente.";
                            } catch (\Exception $e) {
                                $msj = "Error al subir el archivo: " . $e->getMessage();
                            }
                        } else {
                            $msj = "No se ha subido ningún archivo.";
                        }

                        $exito = true;
                    } else throw new \Exception('No se encontró el producto');
                } else throw new \Exception('No se encontró el tipo de salida');
            }
        } catch (\Exception $e) {
            $msj .= $e->getMessage();
        }
        echo $msj;

        return $this->render('merma/formularioagregarmerma.html.twig', [
            'form' => $form->createView(),
            'exito' => $exito,
            'msj' => $msj,
            'idmerma' => $idmerma,
            'enterprises' => $enterprises
        ]);
    }

    /**
     * @Route("/tabla-merma", name="tabla-merma")
     */
    public function obtenerTablaMerma(Request $request): Response
    {

        $exito = false;
        $msj = "";

        $sucursales = $request->get("sucursales");
        $fechaInicio = \DateTime::createFromFormat('d/m/Y', $request->get("fechaInicio"));
        $fechaFin = \DateTime::createFromFormat('d/m/Y', $request->get("fechaFin"));
        $estadoMerma = $request->get("estadoMerma");
        $departures = $request->get("departures");

        $em = $this->getDoctrine()->getManager();

        $User = $this->getUser();

        $whereSucursal = "";

        if (isset($sucursales[0])) {

            $whereSucursal .= " and (";

            for ($i = 0; $i < count($sucursales); $i++) {

                $whereSucursal .= " suc.idsucursal = " . $sucursales[$i];

                if ($i != count($sucursales) - 1) $whereSucursal .= " or ";
            }

            $whereSucursal .= ") ";
        }

        $whereDeparture = "";

        if (isset($departures[0])) {

            $whereDeparture .= " and (";

            for ($i = 0; $i < count($departures); $i++) {

                $whereDeparture .= " dt.iddeparturetype = " . $departures[$i];

                if ($i != count($departures) - 1) $whereDeparture .= " or ";
            }

            $whereDeparture .= ") ";
        }

        $whereFecha = "";

        if ($fechaInicio && $fechaFin) {
            $fechaInicio = $fechaInicio->format('Y-m-d') . " 00:00:00";
            $fechaFin = $fechaFin->format('Y-m-d') . " 23:59:00";
            $whereFecha .= " and m.fechaincidencia BETWEEN '" . $fechaInicio . "' AND '" . $fechaFin . "' ";
        }

        $query = $em->createQuery(
            'SELECT  m
                FROM App\Entity\Merma m
                INNER JOIN m.stockIdstock s
                INNER JOIN s.sucursalIdsucursal suc
                INNER JOIN m.departurecauseIddeparturecause dc
                INNER JOIN dc.departuretypeIddeparturetype dt

               where m.status=:status ' . $whereFecha . $whereSucursal . $whereDeparture . ' order by m.fechaincidencia DESC
               '
        )->setParameters(['status' => $estadoMerma]);

        $mermas = $query->getResult();

        return $this->render('merma/tablamerma.html.twig', [
            'exito' => $exito,
            'msj' => $msj,
            'mermas' => $mermas,
            'rol' => $User->getRol(),
        ]);
    }

    /**
     * @Route("/merma-producto/{codigo}", name="merma-agregar-producto")
     */
    public function agregarProducto(Request $request, $codigo = ""): Response
    {
        //$id=rand(5, 15).uniqid();


        $em = $this->getDoctrine()->getManager();
        $exito = false;
        $msj = "";
        $producto = null;
        $sucursales = array();
        $sucursal = $request->get("sucursalIndex");


        //verificamos que exista en el stock, que este disponoble y no este aparatado
        $query = $em->createQuery(
            'SELECT  s.idstock,p.masivounico,s.apartado,p.modelo, suc.nombre as sucursal, s.cantidad
               FROM App\Entity\Stock s
               inner join s.sucursalIdsucursal suc
               inner join s.productoIdproducto p
               where s.status=:status and (s.codigobarras=:codigobarras or p.codigobarrasuniversal=:codigobarras ) and s.cantidad > 0
               '
        )->setParameters(['status' => '1', 'codigobarras' => $codigo]);

        $productos = $query->getResult();

        if (count($productos) > 0) {

            if (count($productos) == 1) {
                $exito = true;
                $msj = "Producto agregado correctamente";
                $producto = $productos[0];
            } else {
                if ($productos[0]['masivounico'] == '1') {

                    $msj = "Error en inventario";
                } else {

                    for ($i = 0; $i < count($productos); $i++) {
                        array_push($sucursales, $productos[$i]["sucursal"]);
                    }

                    if ($sucursal and $sucursal != -1) {
                        $exito = true;
                        $msj = "Producto agregado correctamente";
                        $producto = $productos[intval($sucursal)];
                    } else {

                        $producto = $productos[0];
                        $msj = "Producto agregado correctamente";
                        $exito = true;
                    }
                }
            }
        } else {
            $msj = "Producto no disponible";
        }

        return $this->render('merma/productodescripcion.html.twig', [
            'exito' => $exito,
            'msj' => $msj,
            'producto' => $producto,
            'productos' => $productos,
            'codigo' => $codigo,
            'sucursales' => $sucursales,
            'sucursal' => $sucursal,
        ]);
    }

    /**
     * @Route("/eliminar-merma", name="eliminar-merma")
     */
    public function eliminarMerma(Request $request): Response
    {

        $exito = false;
        $msj = "";
        $em = $this->getDoctrine()->getManager();

        $idmerma = $request->get("idmerma");
        $detalleborrado = $request->get("detalleborrado");

        $Usuario = $this->getUser();

        try {

            $Merma = $em->getRepository(Merma::class)->findOneBy(array('idmerma' => $idmerma));

            if ($Merma) {
                if ($Merma->getStatus() == 1) {
                    if ($Usuario->getRol() == "ROLE_SUPER_ADMIN") {

                        $Merma->setStatus(0);

                        $Stock = $Merma->getStockIdstock();

                        $Stock->setCantidad($Stock->getCantidad() + $Merma->getCantidad());

                        $Merma->setFechaborrado(new DateTime());
                        $Merma->setDetalleborrado($detalleborrado);
                        $Merma->setUsuarioresponsableborrar($Usuario);

                        $em->persist($Stock);

                        $em->persist($Merma);

                        $em->flush();

                        $exito = true;
                    } else throw new \Exception('El usuario no es súper administrador');
                } else throw new \Exception('La merma ya se había eliminado');
            } else throw new \Exception('No se encontró la merma');
        } catch (\Exception $e) {
            //$msj.=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();
            $msj .= $e->getMessage();
        }

        return $this->json(['exito' => $exito, 'msj' => $msj, 'idmerma' => $idmerma]);
    }

    /**
     * @Route("/merma-estadisticas", name="merma-estadisticas")
     */
    public function mermaEstadisticas(): Response
    {
        $em = $this->getDoctrine()->getManager();

        $Usuario = $this->getUser();
        $query = $em->createQuery(
            'SELECT e.idempresa, e.nombre
               FROM App\Entity\Usuarioempresapermiso uem
               inner join uem.empresaIdempresa e
               inner join uem.usuarioIdusuario u
               where e.status =:status and u.idusuario=:idusuario order by e.nombre asc 
               '
        )->setParameters(['status' => "1", 'idusuario' => $Usuario->getIdUsuario()]);
        $empresas = $query->getResult();

        $query = $em->createQuery(
            'SELECT dt.iddeparturetype, dt.name
            FROM App\Entity\Departuretype dt
            WHERE dt.status =:status 
            '
        )->setParameters(['status' => '1']);
        $departureTypes = $query->getResult();

        return $this->render('merma/estadisticas.html.twig', [
            'empresas' => $empresas,
            'departureTypes' => $departureTypes
        ]);
    }

    /**
     * @Route("/merma-obtener-categorias", name="merma-obtener-categorias")
     */
    public function mermaObtenerCategorias(Request $request): Response
    {

        $exito = false;
        $msj = "";
        $em = $this->getDoctrine()->getManager();
        $datos = null;

        $sucursales = $request->get("sucursales");
        $fechaInicio = \DateTime::createFromFormat('d/m/Y', $request->get("fechaInicio"));
        $fechaFin = \DateTime::createFromFormat('d/m/Y', $request->get("fechaFin"));
        $estadoMerma = $request->get("estadoMerma");
        $departures = $request->get("departures");

        try {

            $whereSucursal = "";

            if (isset($sucursales[0])) {

                $whereSucursal .= " and (";

                for ($i = 0; $i < count($sucursales); $i++) {

                    $whereSucursal .= " suc.idsucursal = " . $sucursales[$i];

                    if ($i != count($sucursales) - 1) $whereSucursal .= " or ";
                }

                $whereSucursal .= ") ";
            }

            $whereDeparture = "";

            if (isset($departures[0])) {

                $whereDeparture .= " and (";

                for ($i = 0; $i < count($departures); $i++) {

                    $whereDeparture .= " dt.iddeparturetype = " . $departures[$i];

                    if ($i != count($departures) - 1) $whereDeparture .= " or ";
                }

                $whereDeparture .= ") ";
            }

            $whereFecha = "";

            if ($fechaInicio && $fechaFin) {
                $fechaInicio = $fechaInicio->format('Y-m-d') . " 00:00:00";
                $fechaFin = $fechaFin->format('Y-m-d') . " 23:59:00";
                $whereFecha .= " and m.fechaincidencia BETWEEN '" . $fechaInicio . "' AND '" . $fechaFin . "' ";
            }

            $msj .= $whereSucursal;

            $query = $em->createQuery(
                'SELECT SUM(m.cantidad) as cantidad, cla.nombre as categoria
                FROM App\Entity\Merma m
                INNER JOIN m.stockIdstock s
                INNER JOIN m.departurecauseIddeparturecause dc
                INNER JOIN dc.departuretypeIddeparturetype dt
                INNER JOIN s.sucursalIdsucursal suc
                INNER JOIN s.productoIdproducto p
                INNER JOIN p.categoriaIdcategoria cat
                INNER JOIN cat.claseIdclase cla
                WHERE m.status =:status' . $whereFecha . $whereSucursal . $whereDeparture . ' group by cla.nombre order by s.cantidad asc 
                '
            )->setParameters(['status' => $estadoMerma]);
            $datos = $query->getResult();
        } catch (\Exception $e) {
            //$msj.=$e->getMessage()." linea ".$e->getLine()." archivo ".$e->getFile();
            $msj .= $e->getMessage();
        }

        return $this->render('merma/grafica.html.twig', [
            'exito' => $exito,
            'msj' => $msj,
            'datos' => $datos,
            'sucursales' => $sucursales,
            'fechaInicio' => $fechaInicio,
            'fechaFin' => $fechaFin,
        ]);
    }

    /**
     * @Route("/detalle-merma", name="detalle-merma")
     */
    public function detalleMerma(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $idmerma = $request->get("idmerma");
        $Merma = $em->getRepository(Merma::class)->findOneBy(array('idmerma' => $idmerma));
        $StockVenta = $Merma->getStockventaIdstockventa();

        if ($StockVenta){
            $barcode = $StockVenta->getStockIdstock()->getCodigobarras();

            if (!$barcode){
                $barcode = $StockVenta->getStockIdstock()->getProductoIdproducto()->getCodigobarrasuniversal();
            }
        }
        

        $query = $em->createQuery(
            'SELECT spd.idsalidaproductodoc, spd.filemerma, spd.creacion, spd.tipo, spd.nombre, u.idusuario, m.idmerma
            FROM App\Entity\Salidaproductodoc spd
            INNER JOIN spd.mermaIdmerma m
            INNER JOIN spd.usuarioIdusuario u
            WHERE spd.status = :status AND m.idmerma =:idmerma'
        )->setParameters(['status' => 1, 'idmerma'=>$idmerma ]);
        $Documentosmerma = $query->getResult();

        return $this->render('merma/detalleMerma.html.twig', [
            'merma' => $Merma,
            'StockVenta' => $StockVenta,
            'Documentosmerma'=>$Documentosmerma,
            'barcode'=>$barcode ?? 0,
        ]);
    }

    /**
     * @Route("/subir-documento-merma", name="subir_documento_merma", methods={"POST"})
     */
    public function subirDocumentoMerma(Request $request): JsonResponse
    {
        $mermaId = $request->request->get('mermaId');
        $em = $this->getDoctrine()->getManager();
        $file = $request->files->get('file'); 
        $User = $this->getUser(); 

        if (!$file) {
            return new JsonResponse(['success' => false, 'msg' => 'No se ha recibido ningún archivo.']);
        }

        $Merma = $em->getRepository(Merma::class)->find($mermaId);
        if (!$Merma) {
            return new JsonResponse(['success' => false, 'msg' => 'Merma no encontrada.']);
        }

        $basePath = $this->getParameter('carpetaSalidas');

        // Crea una ruta específica para el usuario y la merma
        $specificPath = $basePath . "/" . $User->getIdusuario() . "/" . $mermaId . "/";

        if (!file_exists($specificPath)) {
            mkdir($specificPath, 0777, true);
        }

        $originalFilename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $newFilename = $this->sanitize($originalFilename . '-' . uniqid()) . '.' . $file->guessExtension();

        try {
            $file->move($specificPath, $newFilename);

            $SalidaproductoDoc = new Salidaproductodoc();
            $SalidaproductoDoc->setFilemerma($newFilename);
            $SalidaproductoDoc->setStatus(1);
            $SalidaproductoDoc->setCreacion(new \DateTime());
            $SalidaproductoDoc->setTipo("tipo");
            $SalidaproductoDoc->setNombre($newFilename);
            $SalidaproductoDoc->setMermaIdmerma($Merma);
            $SalidaproductoDoc->setUsuarioIdusuario($User);

            $em->persist($SalidaproductoDoc);
            $em->flush();

            return new JsonResponse(['success' => true, 'msg' => 'Archivo subido con éxito.', 'fileName' => $newFilename]);
        } catch (FileException $e) {
            return new JsonResponse(['success' => false, 'msg' => 'Error al subir el archivo.']);
        }
    }



    /**
     * @Route("/eliminar-merma-formulario", name="eliminar-merma-formulario")
     */
    public function eliminarMermaFormulario(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();

        $idmerma = $request->get("idmerma");

        //$Merma = $em->getRepository(Merma::class)->findOneBy(array('idmerma' => $idmerma));

        return $this->render('merma/eliminarMermaFormulario.html.twig', [
            'idmerma' => $idmerma,
        ]);
    }

    /**
     * @Route("/select-sale-products", name="merma-select-sale-products")
     */
    public function searchSaleProducts(Request $request): Response
    {

        $em = $this->getDoctrine()->getManager();
        $foil = $request->get("foil");
        $enterpriseId = $request->get("enterpriseId");

        $query = $em->createQuery(
            'SELECT sv.idstockventa
            FROM App\Entity\Merma m
            INNER JOIN m.stockventaIdstockventa sv
            INNER JOIN sv.ventaIdventa v
            INNER JOIN sv.stockIdstock s
            INNER JOIN s.productoIdproducto p
            INNER JOIN v.sucursalIdsucursal suc
            INNER JOIN suc.empresaIdempresa e
            WHERE v.status =:status AND m.status =:status AND v.folio =:foil AND p.tipoproducto = 1 
            AND p.masivounico = 1 AND e.idempresa =:enterpriseId
            '
        )->setParameters(['status' => '1', 'foil' => $foil, 'enterpriseId' => $enterpriseId]);
        $checkSV = $query->getResult();
        
        $whereIdSV = " (";
        foreach ($checkSV as $stockVenta){
            $tmpId = $stockVenta["idstockventa"];
            $whereIdSV.=$tmpId.',';
        }
        $whereIdSV.= "-1) ";

        $query = $em->createQuery(
            'SELECT sv.idstockventa, m.nombre as brand, p.modelo, s.codigobarras, p.codigobarrasuniversal
            FROM App\Entity\Stockventa sv
            INNER JOIN sv.ventaIdventa v
            INNER JOIN sv.stockIdstock s
            INNER JOIN s.productoIdproducto p
            INNER JOIN p.marcaIdmarca m
            INNER JOIN v.sucursalIdsucursal suc
            INNER JOIN suc.empresaIdempresa e
            WHERE v.status =:status AND v.folio =:foil AND p.tipoproducto = 1 AND p.masivounico = 1
            AND e.idempresa =:enterpriseId AND sv.status=:status AND sv.idstockventa NOT IN '.$whereIdSV
        )->setParameters(['status' => '1', 'foil' => $foil, 'enterpriseId' => $enterpriseId]);
        $products = $query->getResult();


        return $this->render('merma/merma-select-sale-products.html.twig', [
            'products' => $products
        ]);
    }

    /**
     * @Route("/post-batch-departures", name="merma-post-batch-departures")
     */
    public function postBatchDepartures(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $bachDepartures = $request->get("bachDepartures");
        $errors = [];
        $MermasToSetFoil = [];

        foreach ($bachDepartures as $departure){

            $code = $departure[0] ? $departure[0] : $departure[1];
            $parameters = [
                "code" => $code,
                "location" => $departure[3],
                "status" => 1
            ];

            $departureCause = $departure[4];
            $quantity = intval($departure[2]);

            $query = $em->createQuery(
                'SELECT  s.idstock,p.masivounico,p.modelo,s.codigobarras,p.codigobarrasuniversal,s.cantidad
                FROM App\Entity\Stock s
                INNER JOIN s.sucursalIdsucursal sucursal
                INNER JOIN s.productoIdproducto p
                WHERE s.status=:status AND (sucursal.codigo LIKE :location OR sucursal.nombre LIKE :location) AND (s.codigobarras=:code OR p.codigobarrasuniversal=:code ) AND s.cantidad>0
                '
            )->setParameters($parameters);
            $StockAux = $query->getResult();
            if ($StockAux) {
                if (count($StockAux) > 1) { //no debe hanber más de un resultado cuando es unitario
                    if ($StockAux[0]['masivounico'] == "1") {
                        array_push($errors, ["code" => $code, "msg" => "Este producto es unitario debe escanear el SKU"]);
                    } else { //cuando es masivco no debe haber más de un rwultado
                        array_push($errors, ["code" => $code, "msg" => "No debe haber más de un producto en stock con este código, verificar inventario"]);
                    }
                } else { //solo hay un resultado asignar el estock cero
                    $query = $em->createQuery(
                        'SELECT  dc
                        FROM App\Entity\Departurecause dc
                        WHERE dc.status=:status AND dc.name LIKE :nameDC
                        '
                    )->setParameters(["status" => 1, "nameDC" => $departureCause]);
                    $DCAux = $query->getResult();
                    
                    if (count($DCAux) > 0) {
                        $tmpDepartureC = $DCAux[0];
                        $diffQuantity = intval($StockAux[0]["cantidad"]) - $quantity;

                        if ($diffQuantity >= 0){
                            $tmpStock = $em->getRepository(Stock::class)->findOneBy(['idstock' => $StockAux[0]["idstock"]]);
                            $tmpStock->setCantidad($diffQuantity);
                            $em->persist($tmpStock);

                            $tmpMerma = new Merma();
                            $tmpMerma->setUsuarioIdusuario($this->getUser());
                            $tmpMerma->setStockIdstock($tmpStock);
                            $tmpMerma->setDetalleincidencia("Carga desde Google Sheets");
                            $tmpMerma->setCantidad($quantity);
                            $tmpMerma->setFechaincidencia(new DateTime());
                            $tmpMerma->setFechacreacion(new DateTime());
                            $tmpMerma->setDeparturecauseIddeparturecause($tmpDepartureC);
                            $em->persist($tmpMerma);
                            array_push($MermasToSetFoil, $tmpMerma);

                        } else array_push($errors, ["code" => $code, "msg" => "No hay suficiente cantidad de este producto"]);

                    } else array_push($errors, ["code" => $code, "msg" => "No se encontró el tipo de salida"]);
                    
                    
                }
            } else {
                array_push($errors, ["code" => $code, "msg" => "El producto no existe en la sucursal seleccionada"]);
            }
        }

        $em->flush();

        foreach ($MermasToSetFoil as $MermaWOFoil){
            $id = $MermaWOFoil->getIdmerma();
            $prefix = $MermaWOFoil->getDeparturecauseIddeparturecause()->getDeparturetypeIddeparturetype()->getPrefix();
            $MermaWOFoil->setFolio($prefix.'-'.$id);
            $em->persist($MermaWOFoil);
        }
        $em->flush();
        

        return $this->json([
            'errors' => $errors,
            'successfulDepartures' => count($MermasToSetFoil)
        ]);
    }
}
