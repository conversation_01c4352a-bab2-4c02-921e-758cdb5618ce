<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Serializer\SerializerInterface;
use App\Entity\Sucursal;
use App\Entity\Usuario;
use App\Entity\Shipmentordenlaboratorio;
use App\Entity\Shipment;
use App\Entity\Event;
use App\Entity\Eventtype;

use App\Service\TwilioService;

use DateTime;


/**
 * @Route("/envios")
 */
class ShipmentController extends AbstractController
{
    private $twilioService;
    

    public function __construct(TwilioService $twilioService)
    {
        $this->twilioService = $twilioService;

    }

    /**
     * @Route("/shipment", name="app_shipment")
     */
    public function index(): Response
    {
        return $this->render('shipment/index.html.twig', []);
    }

    /**
     * @Route("/get-pending-shipment-table", name="laboratory-get-pending-shipment-table")
     */
    public function getShipmentTable(): Response
    {

        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT ol.idordenlaboratorio, ol.esferaod, ol.esferaoi, ol.cilindrood, ol.cilindrooi, ol.etapa as stage,
            ol.ejeod, ol.ejeoi,  ol.addordenlaboratorio, ol.base, v.folio, suc.nombre as locationName,
            CONCAT(c.nombre, \' \', c.apellidopaterno, \' \', c.apellidomaterno) as fullName, sol.idshipmentordenlaboratorio,
            u.idusuario, dest.idsucursal, fe.idflujoexpediente
            FROM App\Entity\Shipmentordenlaboratorio sol
            LEFT JOIN sol.usuarioIdusuario u
            INNER JOIN sol.destination dest
            INNER JOIN sol.ordenlaboratorioIdordenlaboratorio ol
            INNER JOIN ol.flujoexpedienteIdflujoexpediente fe
            LEFT JOIN fe.ventaIdventa v
            INNER JOIN fe.sucursalIdsucursal suc
            INNER JOIN fe.clienteIdcliente c
            WHERE ol.status =:status AND sol.status =:status
            AND ol.etapa BETWEEN :minStage AND :maxStage
            '
        )->setParameters(["status" => '1', "minStage" => 10, "maxStage" => 11]);
        $laboratoryOrders = $query->getResult();

        $whereLaboratoryOrder = "";

        if (isset($laboratoryOrders[0])) {
            $whereLaboratoryOrder .= "AND (";
            for ($i = 0; $i < count($laboratoryOrders); $i++) {
                if ($i == count($laboratoryOrders) - 1) $whereLaboratoryOrder .= "ol.idordenlaboratorio = " . $laboratoryOrders[$i]["idordenlaboratorio"];
                else $whereLaboratoryOrder .= "ol.idordenlaboratorio = " . $laboratoryOrders[$i]["idordenlaboratorio"] . " OR ";
            }
            $whereLaboratoryOrder .= ") ";
        }

        $query = $em->createQuery(
            'SELECT p.modelo, ol.idordenlaboratorio, svol.mainproduct as errase, svol.idstockventaordenlaboratorio as id
            FROM App\Entity\Stockventaordenlaboratorio svol
            INNER JOIN svol.stockventaIdstockventa sv
            INNER JOIN sv.stockIdstock s
            INNER JOIN s.productoIdproducto p
            INNER JOIN svol.ordenlaboratorioIdordenlaboratorio ol
            WHERE p.status =:status ' . $whereLaboratoryOrder
        )->setParameters(["status" => '1']);
        $productsOrder = $query->getResult();

        $mappedProducts = [];

        foreach ($productsOrder as $productOrder) {
            if (!isset($mappedProducts[$productOrder['idordenlaboratorio']])) $mappedProducts[$productOrder['idordenlaboratorio']] = [$productOrder];
            else array_push($mappedProducts[$productOrder['idordenlaboratorio']], $productOrder);
        }

        $whereFlow = "";

        if (isset($laboratoryOrders[0])) {
            $whereFlow .= "AND (";
            for ($i = 0; $i < count($laboratoryOrders); $i++) {
                if ($i == count($laboratoryOrders) - 1) $whereFlow .= "fe.idflujoexpediente = " . $laboratoryOrders[$i]["idflujoexpediente"];
                else $whereFlow .= "fe.idflujoexpediente = " . $laboratoryOrders[$i]["idflujoexpediente"] . " OR ";
            }
            $whereFlow .= ") ";
        }

        $query = $em->createQuery(
            'SELECT v.folio, fe.idflujoexpediente
            FROM App\Entity\Flujoexpedienteventa fv
            INNER JOIN fv.flujoexpedienteIdflujoexpediente fe
            INNER JOIN fv.ventaIdventa v
            where v.status =:status ' . $whereFlow
        )->setParameters(['status' => "1"]);
        $sales = $query->getResult();

        $mappedSales = [];

        foreach ($sales as $Sale) {
            if (isset($mappedSales[$Sale['idflujoexpediente']])) array_push($mappedSales[$Sale['idflujoexpediente']], $Sale['folio']);
            else $mappedSales[$Sale['idflujoexpediente']] = [$Sale['folio']];
        }

        $query = $em->createQuery(
            'SELECT u.idusuario, CONCAT(u.nombre, \' \', u.apellidopaterno, \' \', u.apellidomaterno) as fullName
            FROM App\Entity\Usuario u
            WHERE u.rol =:rol AND u.status =:status'
        )->setParameters(["rol" => 'ROLE_MENSAJERO', "status" => '1']);
        $deliveryMen = $query->getResult();

        $query = $em->createQuery(
            'SELECT s.idsucursal, s.nombre
            FROM App\Entity\Sucursal s
            WHERE s.status =:status AND s.tipo =:type'
        )->setParameters(["type" => 'sucursal', "status" => '1']);
        $locations = $query->getResult();

        $orderStages = [
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            'Pendiente de envío',
            'Pendiente de aceptar por el repartidor',
            'En transito',
            'Pendiente marcar como entregado',
            'Pendiente de aceptación por sucursal',
            'Entregado',
        ];

        return $this->render('shipment/pending-shipment-table.html.twig', [
            'laboratoryOrders' => $laboratoryOrders,
            'mappedProducts' =>  $mappedProducts,
            'orderStages' => $orderStages,
            'deliveryMen' => $deliveryMen,
            'locations' => $locations,
            'mappedSales' => $mappedSales
        ]);
    }

    /**
     * @Route("/get-transit-shipment-table", name="laboratory-get-transit-shipment-table")
     */
    public function getTransitShipmentTable(): Response
    {

        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT ol.idordenlaboratorio, ol.esferaod, ol.esferaoi, ol.cilindrood, ol.cilindrooi, ol.etapa as stage,
            ol.ejeod, ol.ejeoi,  ol.addordenlaboratorio, ol.base, v.folio, suc.nombre as locationName,
            CONCAT(c.nombre, \' \', c.apellidopaterno, \' \', c.apellidomaterno) as fullName, sol.idshipmentordenlaboratorio,
            dest.nombre as destination, CONCAT(u.nombre, \' \', u.apellidopaterno, \' \', u.apellidomaterno) as deliveryMan, fe.idflujoexpediente
            FROM App\Entity\Shipmentordenlaboratorio sol
            LEFT JOIN sol.usuarioIdusuario u
            INNER JOIN sol.destination dest
            INNER JOIN sol.ordenlaboratorioIdordenlaboratorio ol
            INNER JOIN ol.flujoexpedienteIdflujoexpediente fe
            LEFT JOIN fe.ventaIdventa v
            INNER JOIN fe.sucursalIdsucursal suc
            INNER JOIN fe.clienteIdcliente c
            WHERE ol.status =:status AND sol.status =:status
            AND ol.etapa > :maxStage
            '
        )->setParameters(["status" => '1', "maxStage" => 11]);
        $laboratoryOrders = $query->getResult();

        $whereLaboratoryOrder = "";

        if (isset($laboratoryOrders[0])) {
            $whereLaboratoryOrder .= "AND (";
            for ($i = 0; $i < count($laboratoryOrders); $i++) {
                if ($i == count($laboratoryOrders) - 1) $whereLaboratoryOrder .= "ol.idordenlaboratorio = " . $laboratoryOrders[$i]["idordenlaboratorio"];
                else $whereLaboratoryOrder .= "ol.idordenlaboratorio = " . $laboratoryOrders[$i]["idordenlaboratorio"] . " OR ";
            }
            $whereLaboratoryOrder .= ") ";
        }

        $whereFlow = "";

        if (isset($laboratoryOrders[0])) {
            $whereFlow .= "AND (";
            for ($i = 0; $i < count($laboratoryOrders); $i++) {
                if ($i == count($laboratoryOrders) - 1) $whereFlow .= "fe.idflujoexpediente = " . $laboratoryOrders[$i]["idflujoexpediente"];
                else $whereFlow .= "fe.idflujoexpediente = " . $laboratoryOrders[$i]["idflujoexpediente"] . " OR ";
            }
            $whereFlow .= ") ";
        }

        $query = $em->createQuery(
            'SELECT v.folio, fe.idflujoexpediente
            FROM App\Entity\Flujoexpedienteventa fv
            INNER JOIN fv.flujoexpedienteIdflujoexpediente fe
            INNER JOIN fv.ventaIdventa v
            where v.status =:status ' . $whereFlow
        )->setParameters(['status' => "1"]);
        $sales = $query->getResult();

        $mappedSales = [];

        foreach ($sales as $Sale) {
            if (isset($mappedSales[$Sale['idflujoexpediente']])) array_push($mappedSales[$Sale['idflujoexpediente']], $Sale['folio']);
            else $mappedSales[$Sale['idflujoexpediente']] = [$Sale['folio']];
        }

        $query = $em->createQuery(
            'SELECT p.modelo, ol.idordenlaboratorio, svol.mainproduct as errase, svol.idstockventaordenlaboratorio as id
            FROM App\Entity\Stockventaordenlaboratorio svol
            INNER JOIN svol.stockventaIdstockventa sv
            INNER JOIN sv.stockIdstock s
            INNER JOIN s.productoIdproducto p
            INNER JOIN svol.ordenlaboratorioIdordenlaboratorio ol
            WHERE p.status =:status ' . $whereLaboratoryOrder
        )->setParameters(["status" => '1']);
        $productsOrder = $query->getResult();

        $mappedProducts = [];

        foreach ($productsOrder as $productOrder) {
            if (!isset($mappedProducts[$productOrder['idordenlaboratorio']])) $mappedProducts[$productOrder['idordenlaboratorio']] = [$productOrder];
            else array_push($mappedProducts[$productOrder['idordenlaboratorio']], $productOrder);
        }

        $query = $em->createQuery(
            'SELECT u.idusuario, CONCAT(u.nombre, \' \', u.apellidopaterno, \' \', u.apellidomaterno) as fullName
            FROM App\Entity\Usuario u
            WHERE u.rol =:rol AND u.status =:status'
        )->setParameters(["rol" => 'ROLE_MENSAJERO', "status" => '1']);
        $deliveryMen = $query->getResult();

        $query = $em->createQuery(
            'SELECT s.idsucursal, s.nombre
            FROM App\Entity\Sucursal s
            WHERE s.status =:status AND s.tipo =:type'
        )->setParameters(["type" => 'sucursal', "status" => '1']);
        $locations = $query->getResult();

        $orderStages = [
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            'Pendiente de envío',
            'Pendiente de aceptar por el repartidor',
            'En transito',
            'Pendiente marcar como entregado',
            'Pendiente de aceptación por sucursal',
            'Entregado',
        ];

        return $this->render('shipment/transit-shipment-table.html.twig', [
            'laboratoryOrders' => $laboratoryOrders,
            'mappedProducts' =>  $mappedProducts,
            'orderStages' => $orderStages,
            'deliveryMen' => $deliveryMen,
            'locations' => $locations,
            'mappedSales' => $mappedSales
        ]);
    }

    /**
     * @Route("/set-delivery-man", name="shipment-set-delivery-man")
     */
    public function setDeliveryMan(Request $request): Response
    {

        $em = $this->getDoctrine()->getManager();
        $idshipmentordenlaboratorio = $request->get('idshipmentordenlaboratorio');
        $newDeliveryMan = $request->get('newDeliveryMan');

        $msg = "";
        $success = false;

        $DeliveryMan = $em->getRepository(Usuario::class)->findOneBy(array('idusuario' => $newDeliveryMan));
        $Shipmentordenlaboratorio = $em->getRepository(Shipmentordenlaboratorio::class)->findOneBy(array('idshipmentordenlaboratorio' => $idshipmentordenlaboratorio));

        try {
            
            if ($Shipmentordenlaboratorio) {
                $Shipmentordenlaboratorio->setUsuarioIdusuario($DeliveryMan);
                $LaboratoryOrder = $Shipmentordenlaboratorio->getOrdenlaboratorioIdordenlaboratorio();
                if ($DeliveryMan) $LaboratoryOrder->setEtapa(11);
                else $LaboratoryOrder->setEtapa(10);
                $em->persist($LaboratoryOrder);
                $em->persist($Shipmentordenlaboratorio);
                $em->flush();
                $success = true;
            } else throw new \Exception('No se encontró la orden');
            
        } catch (\Exception $e) {
            $msg .= $e->getMessage();
        }

        return $this->json(['success' => $success, 'msg' => $msg]);
    }

    /**
     * @Route("/set-new-location", name="shipment-set-new-location")
     */
    public function setNewLocation(Request $request): Response
    {

        $em = $this->getDoctrine()->getManager();
        $idshipmentordenlaboratorio = $request->get('idshipmentordenlaboratorio');
        $newLocation = $request->get('newLocation');

        $msg = "";
        $success = false;

        $Location = $em->getRepository(Sucursal::class)->findOneBy(array('idsucursal' => $newLocation));
        $Shipmentordenlaboratorio = $em->getRepository(Shipmentordenlaboratorio::class)->findOneBy(array('idshipmentordenlaboratorio' => $idshipmentordenlaboratorio));

        try {
            
            if ($Shipmentordenlaboratorio) {
                $Shipmentordenlaboratorio->setDestination($Location);
                $em->persist($Shipmentordenlaboratorio);
                $em->flush();
                $success = true;
            } else throw new \Exception('No se encontró la orden');
            
        } catch (\Exception $e) {
            $msg .= $e->getMessage();
        }

        return $this->json(['success' => $success, 'msg' => $msg]);
    }

    /**
     * @Route("/get-delivery-dashboard", name="shipment-get-delivery-dashboard")
     */
    public function getDeliveryDashboard(Request $request): Response
    {

        return $this->render('shipment/delivery-dashboard.html.twig', [

        ]);
    }

    /**
     * @Route("/get-delivery-order-table", name="shipment-get-delivery-order-table") 
     */
    public function getDeliveryOrderTable(Request $request): Response
    {

        $em = $this->getDoctrine()->getManager();
        $userId = $this->getUser()->getIdusuario();
        
        $query = $em->createQuery(
            'SELECT suc.nombre as locationName, 
            sol.idshipmentordenlaboratorio, u.idusuario, ol.idordenlaboratorio, p.modelo,
            s.codigobarras, p.codigobarrasuniversal, m.nombre as brand
            FROM App\Entity\Shipmentordenlaboratorio sol
            INNER JOIN sol.usuarioIdusuario u
            INNER JOIN sol.destination suc
            INNER JOIN sol.ordenlaboratorioIdordenlaboratorio ol
            LEFT JOIN App\Entity\Stockventaordenlaboratorio svol WITH ol.idordenlaboratorio = svol.ordenlaboratorioIdordenlaboratorio
            INNER JOIN svol.stockventaIdstockventa sv
            INNER JOIN sv.stockIdstock s
            INNER JOIN s.productoIdproducto p
            INNER JOIN p.marcaIdmarca m
            WHERE ol.status =:status AND p.ordenlaboratorio =:status AND ( svol.mainproduct =:status OR svol.mainproduct = 3)
            AND u.idusuario=:userId AND sol.shipmentIdshipment IS NULL
            '
        )->setParameters(["status" => '1', 'userId' => $userId]);
        $laboratoryOrders = $query->getResult();
        
        return $this->render('shipment/delivery-order-table.html.twig', [
            'laboratoryOrders' => $laboratoryOrders
        ]);
    }

    /**
     * @Route("/get-delivery-shipments-table", name="shipment-get-delivery-shipments-table")
     */
    public function getDeliveryShipmentTable(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $userId = $this->getUser()->getIdusuario();
        //$userId = 20;
        $query = $em->createQuery(
            'SELECT suc.nombre as locationName, 
            sol.idshipmentordenlaboratorio, u.idusuario, ol.idordenlaboratorio, p.modelo,
            s.codigobarras, p.codigobarrasuniversal, m.nombre as brand, sh.trackingnumber
            FROM App\Entity\Shipmentordenlaboratorio sol
            INNER JOIN sol.usuarioIdusuario u
            INNER JOIN sol.destination suc
            INNER JOIN sol.ordenlaboratorioIdordenlaboratorio ol
            LEFT JOIN App\Entity\Stockventaordenlaboratorio svol WITH ol.idordenlaboratorio = svol.ordenlaboratorioIdordenlaboratorio
            INNER JOIN svol.stockventaIdstockventa sv
            INNER JOIN sv.stockIdstock s
            INNER JOIN s.productoIdproducto p
            INNER JOIN p.marcaIdmarca m
            INNER JOIN sol.shipmentIdshipment sh
            WHERE ol.status =:status AND p.ordenlaboratorio =:status AND ( svol.mainproduct =:status OR svol.mainproduct = 3)
            AND u.idusuario=:userId AND sol.shipmentIdshipment IS NOT NULL AND sol.delivered !=:status
            ORDER BY sh.trackingnumber ASC
            '
        )->setParameters(["status" => '1', 'userId' => $userId]);
        $laboratoryOrders = $query->getResult();
        
        $mappedOrders = [];
        foreach ($laboratoryOrders as $LaboratoryOrder){
            $temp = $LaboratoryOrder["trackingnumber"];
            if (isset($mappedOrders[$temp])) array_push($mappedOrders[$temp], $LaboratoryOrder);
            else $mappedOrders[$temp] = [$LaboratoryOrder];
        }

        return $this->render('shipment/delivery-shipments-table.html.twig', [
            'mappedOrders' => $mappedOrders
        ]);
    }

    /**
     * @Route("/set-new-shipment", name="shipment-set-new-shipment")
     */
    public function setNewShipment(Request $request): Response
    {
        $em = $this->getDoctrine()->getManager();
        $msg = "";
        $success = false;

        $orders = $request->get("orders");
        $whereOrder = "";
        $trackingNumber = "";

        if(isset($orders[0])){
            $whereOrder = " (";
            foreach ($orders as $id) $whereOrder.=$id.", ";
            $whereOrder.="-1) ";

            try{
                $query = $em->createQuery(
                    'SELECT sol
                    FROM App\Entity\Shipmentordenlaboratorio sol
                    WHERE sol.status =:status AND sol.idshipmentordenlaboratorio IN'.$whereOrder
                )->setParameters(["status" => '1']);
                $sols = $query->getResult();

                if (count($sols) > 0){
                    $prefix = $sols[0]->getDestination()->getEmpresaIdempresa()->getPrefijotickets();
                    $Shipment = new Shipment();
                    $now = new DateTime();
                    $Shipment->setCreationdate($now);
                    $Shipment->setUpdatedate($now);

                    foreach ($sols as $Sol){
                        $Sol->setShipmentIdshipment($Shipment);
                        $tempOL = $Sol->getOrdenlaboratorioIdordenlaboratorio();
                        $tempOL->setEtapa(12);
                        $em->persist($Sol);
                        $em->persist($tempOL);
                    }

                    $em->persist($Shipment);
                    $em->flush();
                    $trackingNumber = $prefix.'-'.$Shipment->getIdshipment();
                    $Shipment->setTrackingnumber($trackingNumber);
                    $em->persist($Shipment);
                    $em->flush();
                    $success = true;
                } else throw new \Exception("No se encontraron envíos");
            }catch (\Exception $e) {
                $msg = $e->getMessage();
            }
        } else $msg = "No se seleccionaron envíos";

        return $this->json(['msg' => $msg, "success" => $success, "trackingNumber" => $trackingNumber]);
    }

    /**
     * @Route("/process-shipment", name="shipment-process-shipment")
     */
    public function processShipment(Request $request): Response
    {

        $em = $this->getDoctrine()->getManager();
        
        $msg="";
        $success = false;

        $orders = $request->get("orders");
        $accept = $request->get("accept");
        $commentaries = $request->get("commentaries");
        $fromLocation = $request->get("fromLocation");

        $commentaries = $commentaries ? $commentaries : null; 

        $whereOrder = "";
        
        if(isset($orders[0])){
            $whereOrder = " (";
            foreach ($orders as $id) $whereOrder.=$id.", ";
            $whereOrder.="-1) ";

            try{
                $query = $em->createQuery(
                    'SELECT sol
                    FROM App\Entity\Shipmentordenlaboratorio sol
                    WHERE sol.status =:status AND sol.idshipmentordenlaboratorio IN'.$whereOrder
                )->setParameters(["status" => '1']);
                $sols = $query->getResult();

                $query = $em->createQuery(
                    'SELECT fe.idflujoexpediente
                    FROM App\Entity\Shipmentordenlaboratorio sol
                    INNER JOIN sol.ordenlaboratorioIdordenlaboratorio ol
                    INNER JOIN ol.flujoexpedienteIdflujoexpediente fe
                    WHERE sol.status =:status AND sol.idshipmentordenlaboratorio IN'.$whereOrder
                
                )->setParameters(["status" => '1']);
                $flows = $query->getResult();

                $whereFlow = "";
                if(isset($flows[0])){
                    $whereFlow = " (";
                    foreach ($flows as $flowid) $whereFlow.=$flowid["idflujoexpediente"].", ";
                    $whereFlow.="-1) ";
                }

                $query = $em->createQuery(
                    'SELECT DISTINCT fe.idflujoexpediente, COUNT(ol.idordenlaboratorio) as orderCount
                    FROM App\Entity\Shipmentordenlaboratorio sol
                    INNER JOIN sol.ordenlaboratorioIdordenlaboratorio ol
                    INNER JOIN ol.flujoexpedienteIdflujoexpediente fe
                    WHERE ol.status =:status AND sol.status =:status AND sol.accepted !=:status AND fe.idflujoexpediente IN'.$whereFlow.'
                    GROUP BY fe.idflujoexpediente'
                )->setParameters(["status" => '1']);
                $flowCount = $query->getResult();

                $checkFlows = [];

                foreach ($flowCount as $flow) $checkFlows[$flow["idflujoexpediente"]] = $flow["orderCount"];

                $refusedName = ($fromLocation == 1) ? "Refuse-Shipment" : "Refuse-Delivery";
                $acceptedName = ($fromLocation == 1) ? "Accept-Shipment" : "Accept-Delivery";

                $Refused = $em->getRepository(Eventtype::class)->findOneBy(array('name' => $refusedName));
                $Accepted = $em->getRepository(Eventtype::class)->findOneBy(array('name' => $acceptedName));
                $User = $this->getUser();

                if (!$Refused){
                    $refusedDesc= ($fromLocation == 1) ? "La orden fue rechazada porque llego mal en el envío" : "El envío no se entregó";
                    $Refused = new Eventtype();
                    $Refused->setName($refusedName);
                    $Refused->setDescription($refusedDesc);
                    $em->persist($Refused);
                }
                if (!$Accepted){
                    $acceptedDesc= ($fromLocation == 1) ? "La orden fue aceptada porque llego bien en el envío" : "El envío se entregó";
                    $Accepted = new Eventtype();
                    $Accepted->setName($acceptedName);
                    $Accepted->setDescription($acceptedDesc);
                    $em->persist($Accepted);
                }

                $now = new DateTime();

                if (count($sols) > 0){
                    foreach ($sols as $Sol){
                        $tempOrder = $Sol->getOrdenlaboratorioIdordenlaboratorio();
                        $tempEvent = new Event();
                        $tempEvent->setClienteIdcliente($tempOrder->getClienteIdcliente());
                        $tempEvent->setCreationdate($now);
                        $tempEvent->setUpdatedate($now);
                        $tempEvent->setEventdate($now);
                        $tempEvent->setOrdenlaboratorioIdordenlaboratorio($tempOrder);
                        $tempEvent->setShipmentIdshipment($Sol->getShipmentIdshipment());
                        $tempEvent->setUsuarioIdusuario($User);
                        $tempEvent->setComments($commentaries);

                        if ($accept == 1){
                            if ($fromLocation == 1) {
                                $Sol->setAccepted($accept);
                                $Sol->setAcceptationdate($now);
                                if ($Sol->getDelivered() == '1') $tempOrder->setEtapa(15);
                                else $tempOrder->setEtapa(13);

                                $tempFlowId = $tempOrder->getFlujoexpedienteIdflujoexpediente()->getIdflujoexpediente();
                                if (isset($checkFlows[$tempFlowId])){
                                    $checkFlows[$tempFlowId]--;

                                    $phone = $tempOrder->getFlujoexpedienteIdflujoexpediente()->getClienteIdcliente()->getTelefono();
                                    $fullName =  $tempOrder->getFlujoexpedienteIdflujoexpediente()->getClienteIdcliente()->getNombreCompleto();
                                    $location = $Sol->getDestination()->getNombre();
                                    $address = $Sol->getDestination()->getDireccion();

                                    if ($checkFlows[$tempFlowId] == 0) {
                                        $tempFlow = $tempOrder->getFlujoexpedienteIdflujoexpediente();
                                        $tempFlow->setEtapa(7);
                                        $em->persist($tempFlow);
                                        //$this->twilioService->sendOrderReady($phone, $location, $fullName, $address);
                                    }
                                }
                            } else {
                                $Sol->setDelivered($accept);
                                $Sol->setDeliverytime($now);
                                if ($Sol->getAccepted() == '1') $tempOrder->setEtapa(15);
                                else $tempOrder->setEtapa(14);
                            }
                            $tempEvent->setEventtypeIdeventtype($Accepted);
                        } else {
                            if ($fromLocation == 1) {
                                $query = $em->createQuery(
                                    'SELECT svol
                                    FROM App\Entity\Stockventaordenlaboratorio svol
                                    WHERE svol.ordenlaboratorioIdordenlaboratorio =:laboratoryOrderId'
                                )->setParameters(["laboratoryOrderId" => $tempOrder->getIdordenlaboratorio()]);
                                $svols = $query->getResult();
                                foreach($svols as $Svol){
                                    $mainProduct = ($Svol->getMainproduct() == 1) ? 3 : 2;
                                    $Svol->setMainproduct($mainProduct);
                                    $em->persist($Svol);
                                }
                                $tempOrder->setEtapa(4);
                                $Sol->setAcceptationdate($now);
                                $Sol->setStatus($accept);
                            } else {
                                $tempOrder->setEtapa(10);
                                $Sol->setUsuarioIdusuario(null);
                                $Sol->setShipmentIdshipment(null);
                            }
                            
                            $tempEvent->setEventtypeIdeventtype($Refused);
                        }
                        
                        $em->persist($tempOrder);
                        $em->persist($Sol);
                        $em->persist($tempEvent);
                    }

                    $em->flush();
                    $success = true;
                } else throw new \Exception("No se encontraron envíos");
            }catch (\Exception $e) {
                $msg = $e->getMessage();
            }
        } else $msg = "No se seleccionaron envíos";

        return $this->json(['msg' => $msg, "success" => $success]);
    }


}