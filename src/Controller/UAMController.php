<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

use App\Entity\Cliente;
use App\Entity\Graduacion;
use App\Entity\Empresacliente;
use App\Entity\Sellreference;
use App\Form\UAMType;

use DateTime;
class UAMController extends AbstractController
{
    private $schools = [
        "uam" => [
            "logo" => "https://upload.wikimedia.org/wikipedia/commons/thumb/d/d5/Logo_de_la_UAM.svg/1200px-Logo_de_la_UAM.svg.png",
            "name" => "UAM",
            "title" => "Universidad Autónoma Metropolitana"
        ],
        "ipn" => [
            "logo" => "https://cdn.freelogovectors.net/wp-content/uploads/2020/02/ipn_logo_national_polytechnic_institute.png",
            "name" => "POLITECNICO",
            "title" => "Instituto Politécnico Nacional"
        ],
        "itesm" => [
            "logo" => "https://upload.wikimedia.org/wikipedia/commons/4/47/Logo_del_ITESM.svg",
            "name" => "TEC DE MONTERREY",
            "title" => "Tec de Monterrey"
        ],
    ];

    /**
     * @Route("/registro/{schoolName}", name="uam-registro-uam")
     */
    public function index($schoolName = ""): Response
    {
        $existSchool = isset($this->schools[$schoolName]);

        return $this->render('uam_form/registro-uam.html.twig', [
            "school" => $schoolName,
            "existSchool" => $existSchool
        ]);
    }

    /**
     * @Route("/get-form/{schoolName}", name="uam-get-uam-form")
     */
    public function getUAMForm(Request $request, $schoolName = ""): Response
    {
        $success=false;
        $msg = '';

        $existSchool = isset($this->schools[$schoolName]);


        $em = $this->getDoctrine()->getManager();

        $schoolId = $request->get("schoolId");

        $query = $em->createQuery(
            'SELECT c
               FROM App\Entity\Cliente c
               INNER JOIN c.sellreferenceIdsellreference sr
               WHERE c.status =:status AND c.numeroempleado =:schoolId AND sr.name =:sellReferenceName
               ORDER BY c.idcliente DESC
               '
        )->setParameters(['status'=>1,'schoolId'=>$schoolId, 'sellReferenceName' => "Formulario ".$this->schools[$schoolName]["title"]]);
        $checkClient= $query->setMaxResults(1)->getResult();
        
        if (isset($checkClient[0])) $Client = $checkClient[0];
        else $Client = new Cliente();


        $form = $this->createForm(UAMType::class, $Client,[
            'school' => $this->schools[$schoolName]["name"],
        ]);

        $form->handleRequest($request);

        $anamnesis = null;

        $questions = ["¿Cuál es su motivo principal de consulta?", "¿Realiza algún pasatiempo o deporte? ¿cuál?", "¿Algún golpe que haya recibido cerca/en el área ocular? ¿Hace cuánto tiempo?", "¿Recibió atención médica?", "¿Padece alguna enfermedad o algún síndrome sistémico? (especificar cuales, hacer énfasis en Hipertensión Arterial y Diabetes)", "¿Desde hace cuánto tiempo fue diagnosticado?", "¿Está actualmente controlado?", "¿Qué medicamentos toma? ¿Desde cuándo?", "¿Cada cuánto?", "¿Cuáles fueron sus últimos valores?", "¿Lo ha revisado un oftalmólogo? ¿le dió algún diagnóstico sobre su salud ocular?", "¿Alguna cirugía general u ocular que presente? (en caso de respuesta afirmativa preguntar ¿Cuál/Cuáles han sido? ¿Hace cuánto tiempo fue?)", "¿Ya había utilizado lentes anteriormente? ¿Hace cuánto se realizó el examen visual?", "¿Cuánto tiempo lleva utilizando su última graduación?", "¿Cómo considera que ve con ellos?", "¿Le mencionaron algo acerca de su salud visual en su última revisión?", "¿Lentes de contacto que haya utilizado o esté utilizando? (en caso de respuesta afirmativa) ¿Cuánto tiempo los estaba utilizando?", "¿Cuántas horas al día los utiliza?", "¿Sin lentes como considera su vista tanto de lejos como de cerca?", "¿Ha presentado alguna enfermedad o infección ocular?", "¿Se aplica algunas gotas oftálmicas? ¿Cuáles?", "¿Cada cuanto se las aplica?", "¿Cuánto tiempo al día utiliza dispositivos electrónicos? (en horas)", "después de utilizar dispositivos electrónicos ¿tiene algún síntoma? ¿Cuáles?", "¿Le llega a molestar la luz del sol o artificiales?"];

        try {
            if($form->isSubmitted() && $form->isvalid() ){

                $parameters = $request->request->all();
                if (array_key_exists("uam", $parameters )) unset($parameters["uam"]);
                
                $anamnesis = json_encode($parameters);
                
                $Client = $form->getData();

                $ClientEnterprise = $Client->getUnidadIdunidad()->getEmpresaclienteIdempresacliente();

                $Client->setEmpresaclienteIdempresacliente($ClientEnterprise);

                $Sellreference = $em->getRepository(Sellreference::class)->findOneBy(array('name' => "Formulario ".$this->schools[$schoolName]["title"]));
                if (!$Sellreference){
                    $Sellreference = new Sellreference();
                    $Sellreference->setName("Formulario ".$this->schools[$schoolName]["title"]);
                    $Sellreference->setCommissionstorablepercentage(0);
                    $Sellreference->setCommissionservicepercentage(0);
                    $em->persist($Sellreference);
                }

                $Client->setSellreferenceIdsellreference($Sellreference);
                
                $em->persist($Client);

                $Graduation = new Graduacion();

                $currentDate = new DateTime();

                $Graduation->setAnamnesisjson($anamnesis);

                $Graduation->setCreacion($currentDate);

                $Graduation->setActualizacion($currentDate);

                $Graduation->setClienteIdcliente($Client);

                $em->persist($Graduation);

                $em->flush();

                $success=true;

            }

        }catch (\Exception $e) {
            
            $msg.=$e->getMessage();
        }

        return $this->render('uam_form/get-uam-form.html.twig', [
            'form' => $form->createView(),
            'questions' => $questions,
            'schoolLogo' => $this->schools[$schoolName]["logo"] ?? null,
            'schoolTitle' => $this->schools[$schoolName]["title"] ?? null,
            'success' => $success,
            'existSchool' => $existSchool
        ]);
    }

}
