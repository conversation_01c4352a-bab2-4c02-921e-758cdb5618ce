<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Doctrine\ORM\EntityManagerInterface;

use App\Entity\Tag;
use App\Entity\Producto;
use App\Entity\Productotag;
use App\Entity\Empresa;
use App\Entity\Tagsgroup;
use DateTime;

class TagController extends AbstractController
{
    /**
     * @Route("/tag", name="app_tag")
     */
    public function index(Request $request, EntityManagerInterface $em): Response
    {
        $productId = $request->get("productId");
        $Product = $em->getRepository(Producto::class)->findOneBy(array('idproducto' => $productId));
        $Enterprise = null;
        $enterpriseId = null;
        $enterpriseName = '';

        if ($Product && $Product->getCategoriaIdcategoria() && $Product->getCategoriaIdcategoria()->getClaseIdclase()) {
            $Enterprise = $Product->getCategoriaIdcategoria()->getClaseIdclase()->getEmpresaIdempresa();
            if ($Enterprise) {
                $enterpriseId = $Enterprise->getIdempresa();
                $enterpriseName = $Enterprise->getNombre();
            }
        }

        $query = $em->createQuery(
            'SELECT DISTINCT taggroup.name, taggroup.idtagsgroup as id
            FROM App\Entity\Tagsgroup taggroup'
        );
        $categories = $query->getResult();

        return $this->render('tags/product-tag-index.html.twig', [
            'productId' => $productId,
            'enterpriseId' => $enterpriseId,
            'enterpriseName' => $enterpriseName,
            'categories' => $categories,
        ]);
    }

    /**
     * @Route("/available-tags-table", name="tag-available-tags-table")
     */
    public function availableTagsTable(Request $request, EntityManagerInterface $em): Response
    {
        $productId = $request->get("productId");
        $enterpriseId = $request->get("enterpriseId");

        $query = $em->createQuery(
            'SELECT t.name, t.idtag
            FROM App\Entity\Productotag pt
            INNER JOIN pt.productoIdproducto p
            INNER JOIN pt.tagIdtag t
            WHERE p.idproducto = :productId'
        )->setParameters(['productId' => $productId]);

        $tags = $query->getResult();

        $whereTagArray = [];
        foreach ($tags as $tag) {
            $whereTagArray[] = $tag['idtag'];
        }

        $whereTagArray[] = -1;

        $query = $em->createQuery(
            'SELECT tag.idtag, tag.name, COALESCE(taggroup.name, \'Sin Categoría\') as category
            FROM App\Entity\Tag tag
            LEFT JOIN tag.tagsgroupIdtagsgroup taggroup
            INNER JOIN tag.empresaIdempresa e
            WHERE tag.status = :status AND e.idempresa = :enterpriseId 
            AND tag.idtag NOT IN (:whereTag)
            ORDER BY tag.name ASC'
        )->setParameters([
            "status" => 1,
            'enterpriseId' => $enterpriseId,
            'whereTag' => $whereTagArray
        ]);

        $availableTags = $query->getResult();

        $groupedTags = array_reduce($availableTags, function ($carry, $item) {
            $category = $item['category'] ?? "Sin Categoría";
            $tagData = ['idtag' => $item['idtag'], 'name' => $item['name']];

            // Check if the category exists in the result array
            if (!isset($carry[$category])) {
                $carry[$category] = ['category' => $category, 'tags' => []];
            }

            // Add the tag to the corresponding category
            $carry[$category]['tags'][] = $tagData;

            return $carry;
        }, []);

        $result = array_values($groupedTags);

        /*echo "<pre>";
        var_dump($result);
        echo "</pre>";*/

        return $this->render('tags/product-tag-available-table.html.twig', [
            'availableTags' => $result,
        ]);
    }

    /**
     * @Route("/selected-tags-table", name="tag-selected-tags-table")
     */
    public function selectedTagsTable(Request $request): Response
    {

        $productId = $request->get("productId");
        $enterpriseId = $request->get("enterpriseId");
        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT t.name, pt.idproductotag
            FROM App\Entity\Productotag pt
            INNER JOIN pt.productoIdproducto p
            INNER JOIN pt.tagIdtag t
            INNER JOIN t.empresaIdempresa e
            WHERE p.idproducto = :productId AND e.idempresa=:enterpriseId'
        )->setParameters(['productId' => $productId, 'enterpriseId' => $enterpriseId]);

        $tags = $query->getResult();

        return $this->render('tags/product-tag-selected-table.html.twig', [
            'tags' => $tags,
        ]);
    }

    /**
     * @Route("/add-product-tag", name="tag-add-product-tag")
     */
    public function addProductTag(Request $request): Response
    {

        $productId = $request->get("productId");
        $tagId = $request->get("tagId");
        $em = $this->getDoctrine()->getManager();

        $success = false;
        $msg = "";

        try {
            $Product = $em->getRepository(Producto::class)->findOneBy(array('idproducto' => $productId));
            $Tag = $em->getRepository(Tag::class)->findOneBy(array('idtag' => $tagId));

            if ($Tag && $Product) {

                $ProductTag = new Productotag();
                $ProductTag->setProductoIdproducto($Product);
                $ProductTag->setTagIdtag($Tag);
                $em->persist($ProductTag);
                $em->flush();
                $success = true;
            } else throw new \Exception('Error con la solicitud');
        } catch (\Exception $e) {
            $msg .= $e->getMessage();
        }

        return $this->json(['success' => $success, 'msg' => $msg]);
    }

    /**
     * @Route("/delete-product-tag", name="tag-delete-product-tag")
     */
    public function deleteProductTag(Request $request): Response
    {

        $productTagId = $request->get("productTagId");
        $em = $this->getDoctrine()->getManager();

        $success = false;
        $msg = "";

        try {
            $ProductTag = $em->getRepository(Productotag::class)->findOneBy(array('idproductotag' => $productTagId));

            if ($ProductTag) {

                $em->remove($ProductTag);
                $em->flush();
                $success = true;
            } else throw new \Exception('Error con la solicitud');
        } catch (\Exception $e) {
            $msg .= $e->getMessage();
        }

        return $this->json(['success' => $success, 'msg' => $msg]);
    }

    /**
     * @Route("/add-tag", name="tag-add-tag")
     */
    public function addTag(Request $request): Response
    {

        $tagName = $request->get("tagName");
        $tagCategory = $request->get("tagCategory");

        $tagId = $request->get("tagId");
        $enterpriseId = $request->get("enterpriseId");
        $em = $this->getDoctrine()->getManager();
        $formattedName = $this->replaceAccentsAndUpperCase($tagName);
        $success = false;
        $msg = "";

        try {

            if ($tagName) {

                $query = $em->createQuery(
                    'SELECT t.idtag
                    FROM App\Entity\Tag t
                    INNER JOIN t.empresaIdempresa e
                    WHERE t.name = :name AND t.status =:status AND e.idempresa =:enterpriseId'
                )->setParameters(['name' => $formattedName, 'status' => 1, 'enterpriseId' => $enterpriseId]);
                $checkTag = $query->getResult();
                $Enterprise = $em->getRepository(Empresa::class)->findOneBy(array('idempresa' => $enterpriseId, 'status' => 1));
                $tempTag = $em->getRepository(Tag::class)->findOneBy(array('idtag' => $tagId, 'status' => 1));

                $existingTag = false;

                if ($tempTag) {
                    for ($i = 0; $i < count($checkTag); $i++) {
                        if ($checkTag[$i]['idtag'] == $tempTag->getIdtag()) {
                            $existingTag = true;
                            break;
                        }
                    }
                }

                if (count($checkTag) <= 0 || $existingTag) {
                    if ($Enterprise) {
                        $Tag = ($tempTag) ? $tempTag : new Tag();

                        if ($tagCategory) {
                            $formattedCategory = $this->replaceAccentsAndUpperCase($tagCategory);
                            $category = $em->getRepository(Tagsgroup::class)->findOneBy(array('name' => $formattedCategory)) ?? new Tagsgroup();
                            $category->setName($formattedCategory);
                            $em->persist($category);
                            $em->flush();
                            if ($category) {
                                $Tag->setTagsgroupIdtagsgroup($category);
                            }
                        }
                        $Tag->setName($formattedName);
                        $Tag->setEmpresaIdempresa($Enterprise);
                        $em->persist($Tag);
                        $em->flush();
                        $success = true;
                    } else throw new \Exception('No se encontró la empresa');
                } else throw new \Exception('Ya existe este tag');
            } else throw new \Exception('Ingresa un valor válido');
        } catch (\Exception $e) {
            $msg .= $e->getMessage();
        }

        return $this->json(['success' => $success, 'msg' => $msg]);
    }

    /**
     * @Route("/delete-tag", name="tag-delete-tag")
     */
    public function deleteTag(Request $request): Response
    {

        $tagId = $request->get("tagId");
        $em = $this->getDoctrine()->getManager();

        $success = false;
        $msg = "";

        try {
            $Tag = $em->getRepository(Tag::class)->findOneBy(array('idtag' => $tagId));

            if ($Tag) {

                $query = $em->createQuery(
                    'SELECT pt
                    FROM App\Entity\Productotag pt
                    INNER JOIN pt.tagIdtag t
                    WHERE t.idtag = :tagId'
                )->setParameters(['tagId' => $tagId]);

                $productTags = $query->getResult();

                foreach ($productTags as $ProductTag) $em->remove($ProductTag);
                $em->remove($Tag);

                $em->flush();
                $success = true;
            } else throw new \Exception('Error con la solicitud');
        } catch (\Exception $e) {
            $msg .= $e->getMessage();
        }

        return $this->json(['success' => $success, 'msg' => $msg]);
    }

    function replaceAccentsAndUpperCase($text)
    {
        // Define a character map for accented to non-accented character replacements
        $characterMap = array(
            'á' => 'a', 'é' => 'e', 'í' => 'i', 'ó' => 'o', 'ú' => 'u',
            'Á' => 'A', 'É' => 'E', 'Í' => 'I', 'Ó' => 'O', 'Ú' => 'U',
            'â' => 'a', 'ê' => 'e', 'î' => 'i', 'ô' => 'o', 'û' => 'u',
            'Â' => 'A', 'Ê' => 'E', 'Î' => 'I', 'Ô' => 'O', 'Û' => 'U',
            'à' => 'a', 'è' => 'e', 'ì' => 'i', 'ò' => 'o', 'ù' => 'u',
            'À' => 'A', 'È' => 'E', 'Ì' => 'I', 'Ò' => 'O', 'Ù' => 'U',
            '^' => '', // Remove circumflex
            '`' => '', // Remove grave accent
            // Add more characters as needed
        );

        // Replace accented characters with their non-accented equivalents
        $text = strtr($text, $characterMap);

        // Remove any remaining non-alphanumeric characters
        $text = preg_replace('/[^a-zA-Z0-9]/', '', $text);

        // Convert the text to uppercase
        $text = strtoupper($text);

        return $text;
    }
}
