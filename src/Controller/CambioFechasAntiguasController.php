<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class CambioFechasAntiguasController extends AbstractController
{
    /**
     * @Route("/cambiofechasantiguas", name="app_cambio_fechas_antiguas")
     */
    public function index(): Response
    {
        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT v
             FROM App\Entity\Venta v
             WHERE v.status = :status AND v.fecha IS NOT NULL AND v.fechacreacion IS NULL 
             AND v.fechaactualizacion IS NULL AND v.fechaventa IS NULL'
        )->setParameters(["status" => '1']);

        $ventasFechasAntiguas = $query->getResult();

        return $this->render('cambio_fechas_antiguas/index.html.twig', [
            'ventasFechasAntiguas' => $ventasFechasAntiguas,
        ]);
    }

    /**
     * @Route("/correrProceso", name="correr-proceso")
     */
    public function correrProceso(): JsonResponse
    {
        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
            'SELECT v
             FROM App\Entity\Venta v
             WHERE v.status = :status AND v.fecha IS NOT NULL AND v.fechacreacion IS NULL 
             AND v.fechaactualizacion IS NULL AND v.fechaventa IS NULL'
        )->setParameters(["status" => '1']);

        $ventasFechasAntiguas = $query->getResult();

        foreach ($ventasFechasAntiguas as $venta) {
            $fecha = $venta->getFecha();
            $venta->setFechacreacion($fecha);
            $venta->setFechaventa($fecha);
            $venta->setFechaactualizacion($fecha);
        }

        $em->flush();

        return new JsonResponse(['status' => 'success', 'message' => 'Proceso completado correctamente.']);
    }
}
