<?php

namespace App\Twig;

use App\Service\TraspasoAlertService;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

/**
 * Extensión de Twig para hacer disponible el TraspasoAlertService en las plantillas
 */
class TraspasoAlertExtension extends AbstractExtension
{
    private TraspasoAlertService $alertService;

    public function __construct(TraspasoAlertService $alertService)
    {
        $this->alertService = $alertService;
    }

    public function getFunctions(): array
    {
        return [
            // Funciones básicas de alerta
            new TwigFunction('traspaso_alert_success', [$this, 'successAlert'], ['is_safe' => ['html']]),
            new TwigFunction('traspaso_alert_error', [$this, 'errorAlert'], ['is_safe' => ['html']]),
            new TwigFunction('traspaso_alert_script', [$this, 'alertScript'], ['is_safe' => ['html']]),
            
            // Funciones específicas de traspaso
            new TwigFunction('traspaso_alert_product_added', [$this, 'productAddedAlert'], ['is_safe' => ['html']]),
            new TwigFunction('traspaso_alert_multiple_products', [$this, 'multipleProductsAlert'], ['is_safe' => ['html']]),
            new TwigFunction('traspaso_alert_confirm_transfer', [$this, 'confirmTransferAlert'], ['is_safe' => ['html']]),
            new TwigFunction('traspaso_alert_transfer_completed', [$this, 'transferCompletedAlert'], ['is_safe' => ['html']]),
            new TwigFunction('traspaso_alert_exit_order_accepted', [$this, 'exitOrderAcceptedAlert'], ['is_safe' => ['html']]),
            new TwigFunction('traspaso_alert_exit_order_rejected', [$this, 'exitOrderRejectedAlert'], ['is_safe' => ['html']]),
            new TwigFunction('traspaso_alert_google_sheets_error', [$this, 'googleSheetsErrorAlert'], ['is_safe' => ['html']]),
            new TwigFunction('traspaso_alert_insufficient_stock', [$this, 'insufficientStockAlert'], ['is_safe' => ['html']]),
            new TwigFunction('traspaso_alert_confirm_remove_product', [$this, 'confirmRemoveProductAlert'], ['is_safe' => ['html']]),
            
            // Funciones para generar JSON (para AJAX)
            new TwigFunction('traspaso_alert_json', [$this, 'alertJson']),
        ];
    }

    /**
     * Genera alerta de éxito con script
     */
    public function successAlert(string $title, string $text = null): string
    {
        $config = $this->alertService->successAlert($title, $text);
        return $this->alertService->generateAlertScript($config);
    }

    /**
     * Genera alerta de error con script
     */
    public function errorAlert(string $errorCode, string $customMessage = null): string
    {
        $config = $this->alertService->errorAlert($errorCode, $customMessage);
        return $this->alertService->generateAlertScript($config);
    }

    /**
     * Genera script de alerta desde configuración personalizada
     */
    public function alertScript(array $config): string
    {
        return $this->alertService->generateAlertScript($config);
    }

    /**
     * Alerta para producto agregado
     */
    public function productAddedAlert(string $codigo, string $modelo = null): string
    {
        $config = $this->alertService->productAddedAlert($codigo, $modelo);
        return $this->alertService->generateAlertScript($config);
    }

    /**
     * Alerta para múltiples productos
     */
    public function multipleProductsAlert(array $successful, array $errors): string
    {
        $config = $this->alertService->multipleProductsAlert($successful, $errors);
        return $this->alertService->generateAlertScript($config);
    }

    /**
     * Alerta de confirmación para traspaso
     */
    public function confirmTransferAlert(string $sucursalOrigen, string $sucursalDestino, int $totalProductos, string $callback = ''): string
    {
        $config = $this->alertService->confirmTransferAlert($sucursalOrigen, $sucursalDestino, $totalProductos);
        
        if ($callback) {
            return $this->alertService->generateAlertWithCallback($config, $callback);
        }
        
        return $this->alertService->generateAlertScript($config);
    }

    /**
     * Alerta para traspaso completado
     */
    public function transferCompletedAlert(int $idTraspaso): string
    {
        $config = $this->alertService->transferCompletedAlert($idTraspaso);
        return $this->alertService->generateAlertScript($config);
    }

    /**
     * Alerta para orden de salida aceptada
     */
    public function exitOrderAcceptedAlert(): string
    {
        $config = $this->alertService->exitOrderAcceptedAlert();
        return $this->alertService->generateAlertScript($config);
    }

    /**
     * Alerta para orden de salida rechazada
     */
    public function exitOrderRejectedAlert(): string
    {
        $config = $this->alertService->exitOrderRejectedAlert();
        return $this->alertService->generateAlertScript($config);
    }

    /**
     * Alerta para errores de Google Sheets
     */
    public function googleSheetsErrorAlert(string $errorCode, string $details = null): string
    {
        $config = $this->alertService->googleSheetsErrorAlert($errorCode, $details);
        return $this->alertService->generateAlertScript($config);
    }

    /**
     * Alerta para stock insuficiente
     */
    public function insufficientStockAlert(string $codigo, int $disponible, int $solicitado): string
    {
        $config = $this->alertService->insufficientStockAlert($codigo, $disponible, $solicitado);
        return $this->alertService->generateAlertScript($config);
    }

    /**
     * Alerta de confirmación para eliminar producto
     */
    public function confirmRemoveProductAlert(string $codigo, string $callback = ''): string
    {
        $config = $this->alertService->confirmRemoveProductAlert($codigo);
        
        if ($callback) {
            return $this->alertService->generateAlertWithCallback($config, $callback);
        }
        
        return $this->alertService->generateAlertScript($config);
    }

    /**
     * Convierte configuración de alerta a JSON (para uso en AJAX)
     */
    public function alertJson(string $type, ...$args): string
    {
        switch ($type) {
            case 'success':
                $config = $this->alertService->successAlert($args[0], $args[1] ?? null);
                break;
            case 'error':
                $config = $this->alertService->errorAlert($args[0], $args[1] ?? null);
                break;
            case 'product_added':
                $config = $this->alertService->productAddedAlert($args[0], $args[1] ?? null);
                break;
            case 'multiple_products':
                $config = $this->alertService->multipleProductsAlert($args[0], $args[1]);
                break;
            default:
                $config = ['title' => 'Error', 'text' => 'Tipo de alerta no válido'];
        }
        
        return $this->alertService->toJson($config);
    }
}
