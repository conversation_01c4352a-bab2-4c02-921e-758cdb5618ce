<?php

namespace App\Twig;

use App\Service\AlertService;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

/**
 * Extensión de Twig para hacer disponible el AlertService en las plantillas
 * 
 * Proporciona funciones de Twig para generar alertas de SweetAlert2 de manera
 * fácil y consistente en todas las plantillas.
 */
class AlertExtension extends AbstractExtension
{
    private AlertService $alertService;

    public function __construct(AlertService $alertService)
    {
        $this->alertService = $alertService;
    }

    public function getFunctions(): array
    {
        return [
            // Funciones básicas de alerta
            new TwigFunction('alert_success', [$this, 'success'], ['is_safe' => ['html']]),
            new TwigFunction('alert_error', [$this, 'error'], ['is_safe' => ['html']]),
            new TwigFunction('alert_warning', [$this, 'warning'], ['is_safe' => ['html']]),
            new TwigFunction('alert_info', [$this, 'info'], ['is_safe' => ['html']]),
            new TwigFunction('alert_confirm', [$this, 'confirm'], ['is_safe' => ['html']]),
            
            // Funciones de alerta con script
            new TwigFunction('alert_script', [$this, 'alertScript'], ['is_safe' => ['html']]),
            new TwigFunction('alert_script_callback', [$this, 'alertScriptWithCallback'], ['is_safe' => ['html']]),
            
            // Funciones específicas comunes
            new TwigFunction('alert_confirm_delete', [$this, 'confirmDelete'], ['is_safe' => ['html']]),
            new TwigFunction('alert_operation_success', [$this, 'operationSuccess'], ['is_safe' => ['html']]),
            new TwigFunction('alert_operation_error', [$this, 'operationError'], ['is_safe' => ['html']]),
            new TwigFunction('alert_validation_error', [$this, 'validationError'], ['is_safe' => ['html']]),
            new TwigFunction('alert_access_denied', [$this, 'accessDenied'], ['is_safe' => ['html']]),
            new TwigFunction('alert_session_expired', [$this, 'sessionExpired'], ['is_safe' => ['html']]),
            new TwigFunction('alert_loading', [$this, 'loading'], ['is_safe' => ['html']]),
            new TwigFunction('alert_toast', [$this, 'toast'], ['is_safe' => ['html']]),
            
            // Funciones para generar JSON (para AJAX)
            new TwigFunction('alert_json', [$this, 'alertJson']),
            new TwigFunction('alert_config_json', [$this, 'configToJson']),
        ];
    }

    /**
     * Genera alerta de éxito con script
     */
    public function success(string $title, string $text = null, array $options = []): string
    {
        $config = $this->alertService->success($title, $text, $options);
        return $this->alertService->generateScript($config);
    }

    /**
     * Genera alerta de error con script
     */
    public function error(string $title, string $text = null, array $options = []): string
    {
        $config = $this->alertService->error($title, $text, $options);
        return $this->alertService->generateScript($config);
    }

    /**
     * Genera alerta de advertencia con script
     */
    public function warning(string $title, string $text = null, array $options = []): string
    {
        $config = $this->alertService->warning($title, $text, $options);
        return $this->alertService->generateScript($config);
    }

    /**
     * Genera alerta informativa con script
     */
    public function info(string $title, string $text = null, array $options = []): string
    {
        $config = $this->alertService->info($title, $text, $options);
        return $this->alertService->generateScript($config);
    }

    /**
     * Genera alerta de confirmación con script
     */
    public function confirm(string $title, string $text = null, array $options = [], string $callback = ''): string
    {
        $config = $this->alertService->confirm($title, $text, $options);
        
        if ($callback) {
            return $this->alertService->generateScriptWithCallback($config, $callback);
        }
        
        return $this->alertService->generateScript($config);
    }

    /**
     * Genera script de alerta desde configuración personalizada
     */
    public function alertScript(array $config): string
    {
        return $this->alertService->generateScript($config);
    }

    /**
     * Genera script de alerta con callback
     */
    public function alertScriptWithCallback(array $config, string $callback): string
    {
        return $this->alertService->generateScriptWithCallback($config, $callback);
    }

    /**
     * Alerta de confirmación para eliminar
     */
    public function confirmDelete(string $itemName = 'elemento', array $options = [], string $callback = ''): string
    {
        $config = $this->alertService->confirmDelete($itemName, $options);
        
        if ($callback) {
            return $this->alertService->generateScriptWithCallback($config, $callback);
        }
        
        return $this->alertService->generateScript($config);
    }

    /**
     * Alerta para operación exitosa
     */
    public function operationSuccess(string $operation = 'Operación', array $options = []): string
    {
        $config = $this->alertService->operationSuccess($operation, $options);
        return $this->alertService->generateScript($config);
    }

    /**
     * Alerta para operación fallida
     */
    public function operationError(string $operation = 'Operación', string $details = null, array $options = []): string
    {
        $config = $this->alertService->operationError($operation, $details, $options);
        return $this->alertService->generateScript($config);
    }

    /**
     * Alerta para errores de validación
     */
    public function validationError(array $errors = [], array $options = []): string
    {
        $config = $this->alertService->validationError($errors, $options);
        return $this->alertService->generateScript($config);
    }

    /**
     * Alerta para acceso denegado
     */
    public function accessDenied(string $resource = 'recurso', array $options = []): string
    {
        $config = $this->alertService->accessDenied($resource, $options);
        return $this->alertService->generateScript($config);
    }

    /**
     * Alerta para sesión expirada
     */
    public function sessionExpired(array $options = []): string
    {
        $config = $this->alertService->sessionExpired($options);
        return $this->alertService->generateScript($config);
    }

    /**
     * Alerta de carga
     */
    public function loading(string $message = 'Procesando...', array $options = []): string
    {
        $config = $this->alertService->loading($message, $options);
        return $this->alertService->generateScript($config);
    }

    /**
     * Alerta tipo toast
     */
    public function toast(string $message, string $type = 'success', array $options = []): string
    {
        $config = $this->alertService->toast($message, $type, $options);
        return $this->alertService->generateScript($config);
    }

    /**
     * Convierte configuración de alerta a JSON (para uso en AJAX)
     */
    public function alertJson(string $type, ...$args): string
    {
        switch ($type) {
            case 'success':
                $config = $this->alertService->success($args[0], $args[1] ?? null, $args[2] ?? []);
                break;
            case 'error':
                $config = $this->alertService->error($args[0], $args[1] ?? null, $args[2] ?? []);
                break;
            case 'warning':
                $config = $this->alertService->warning($args[0], $args[1] ?? null, $args[2] ?? []);
                break;
            case 'info':
                $config = $this->alertService->info($args[0], $args[1] ?? null, $args[2] ?? []);
                break;
            case 'confirm':
                $config = $this->alertService->confirm($args[0], $args[1] ?? null, $args[2] ?? []);
                break;
            case 'confirm_delete':
                $config = $this->alertService->confirmDelete($args[0] ?? 'elemento', $args[1] ?? []);
                break;
            case 'operation_success':
                $config = $this->alertService->operationSuccess($args[0] ?? 'Operación', $args[1] ?? []);
                break;
            case 'operation_error':
                $config = $this->alertService->operationError($args[0] ?? 'Operación', $args[1] ?? null, $args[2] ?? []);
                break;
            case 'validation_error':
                $config = $this->alertService->validationError($args[0] ?? [], $args[1] ?? []);
                break;
            case 'access_denied':
                $config = $this->alertService->accessDenied($args[0] ?? 'recurso', $args[1] ?? []);
                break;
            case 'session_expired':
                $config = $this->alertService->sessionExpired($args[0] ?? []);
                break;
            case 'loading':
                $config = $this->alertService->loading($args[0] ?? 'Procesando...', $args[1] ?? []);
                break;
            case 'toast':
                $config = $this->alertService->toast($args[0], $args[1] ?? 'success', $args[2] ?? []);
                break;
            default:
                $config = $this->alertService->error('Error', 'Tipo de alerta no válido');
        }
        
        return $this->alertService->toJson($config);
    }

    /**
     * Convierte configuración personalizada a JSON
     */
    public function configToJson(array $config): string
    {
        return $this->alertService->toJson($config);
    }
}
