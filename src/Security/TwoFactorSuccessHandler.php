<?php
namespace App\Security;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Http\Authentication\AuthenticationSuccessHandlerInterface;
use App\Service\RedirectService;

class TwoFactorSuccessHandler implements AuthenticationSuccessHandlerInterface
{
    private $router;
    private $authorizationChecker;
    private $redirectService;

    public function __construct(RouterInterface $router, AuthorizationCheckerInterface $authorizationChecker, RedirectService $redirectService)
    {
        $this->router = $router;
        $this->authorizationChecker = $authorizationChecker;
        $this->redirectService = $redirectService;
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token): RedirectResponse
    {
        return $this->redirectService->getRedirect();
    }
}
