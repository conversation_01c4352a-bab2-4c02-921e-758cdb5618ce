<?php
namespace App\Security;

use App\Entity\Usuario;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Core\User\UserProviderInterface;
use Symfony\Component\Security\Core\Exception\UsernameNotFoundException;

final class UserProvider implements UserProviderInterface
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function loadUserByUsername($email): Usuario
    {
        $user = $this->findOneUserBy(['email' => $email, 'status'=>'1']);


        if (!$user) {
            throw new UsernameNotFoundException(
                sprintf(
                    'User with "%s" email does not exist.',
                    $email
                )
            );
        }

        return $user;
    }

    private function findOneUserBy(array $options): ?Usuario
    {
        return $this->entityManager
            ->getRepository(Usuario::class)
            ->findOneBy($options);
    }

    public function refreshUser(UserInterface $user): Usuario
    {
        assert($user instanceof $user);

        if (null === $reloadedUser = $this->findOneUserBy(['idusuario' => $user->getIdUsuario()])) {
            throw new UsernameNotFoundException(sprintf(
                'User with ID "%s" could not be reloaded.',
                $user->getIdUsuario()
            ));
        }

        return $reloadedUser;
    }

    public function supportsClass($class): bool
    {
        return $class === Usuario::class;
    }
}
