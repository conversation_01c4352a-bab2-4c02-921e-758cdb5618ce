<?php
namespace App\Security;

use App\Entity\Cliente;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Core\User\UserProviderInterface;
use Symfony\Component\Security\Core\Exception\UsernameNotFoundException;

final class ClienteProvider implements UserProviderInterface
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function loadUserByUsername($email): Cliente
    {
        $user = $this->findOneUserBy(['email' => $email, 'status'=>'1']);


        if (!$user) {
            throw new UsernameNotFoundException(
                sprintf(
                    'User with "%s" email does not exist.',
                    $email
                )
            );
        }

        return $user;
    }

    public function loadUserByIdentifier(string $identifier): Cliente
    {
        return $this->loadUserByUsername($identifier);
    }


    private function findOneUserBy(array $options): ?Cliente
    {
        return $this->entityManager
            ->getRepository(Cliente::class)
            ->findOneBy($options);
    }

    public function refreshUser(UserInterface $user): Cliente
    {
        assert($user instanceof Cliente);


        if (null === $reloadedUser = $this->findOneUserBy(['idcliente' => $user->getIdcliente()])) {
            throw new UsernameNotFoundException(sprintf(
                'User with ID "%s" could not be reloaded.',
                $user->getIdcliente()
            ));
        }

        return $reloadedUser;
    }

    public function supportsClass($class): bool
    {
        return $class === Cliente::class;
    }
}
