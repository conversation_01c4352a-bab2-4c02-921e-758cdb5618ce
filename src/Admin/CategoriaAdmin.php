<?php
// src/Admin/CategoryAdmin.php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use App\Entity\Categoria;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Sonata\DoctrineORMAdminBundle\Filter\ChoiceFilter;
use Sonata\AdminBundle\Form\Type\ModelAutocompleteType;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Sonata\AdminBundle\Form\Type\ModelAutocompleteType as AdminModelAutocompleteType;
use Sonata\DoctrineORMAdminBundle\Filter\ModelAutocompleteType as FilterModelAutocompleteType;


final class CategoriaAdmin extends AbstractAdmin 
{
    private $tokenStorage;
    private $em;
    private $empresasg;

    public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage, EntityManagerInterface $em)
    {
        parent::__construct($code, $class, $baseControllerName);
        $this->tokenStorage = $tokenStorage;

        $this->em = $em;
    }

    public function buscarEmpresas(){

        $user=$this->tokenStorage->getToken()->getUser();

        $query = $this-> em->createQuery(
            'SELECT e.idempresa
            FROM App\Entity\Usuarioempresapermiso up
            INNER JOIN up.empresaIdempresa e
            where up.status =:status and up.usuarioIdusuario =:idusuario
            '
        )->setParameters(['status'=>"1",'idusuario'=>$user->getIdusuario()]);
        $empresas= $query->getResult();

        $this->empresasg=$empresas;
    }

    protected function configureDatagridFilters(DatagridMapper $datagridMapper): void 
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $datagridMapper
            ->add('nombre', null, ['show_filter' => true, 'label' => "Nombre"])
            ->add('codigo', null, ['show_filter' => true, 'label' => "Código"])
            ->add('sat', null, ['show_filter' => true, 'label' => "SAT"])
            ->add('claseIdclase', ModelFilter::class, [
                'label' => 'Categoría',
                'field_options' => [
                    'class' => 'App\Entity\Clase',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('c')
                            ->andWhere('c.status = :status')
                            ->setParameter('status', '1');
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
            ->add('sucursalIdsucursal.empresaIdempresa', ModelFilter::class, [
                'label' => 'Empresas',
                'field_options' => [
                    'class' => 'App\Entity\Empresa',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('e')
                            ->andWhere('e.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('empresa', $this->empresasg);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
        ;
    }

    protected function configureListFields(ListMapper $listMapper): void 
    {
        $listMapper
            ->add('nombre', null, ['label' => "Nombre"]) 
            ->add('codigo', null, ['label' => "Código"])
            ->add('sat', null, ['label' => "SAT"])
            ->add('claseIdclase.nombre', null, [
                'label' => 'Categoría',
                'associated_property' => 'nombre',
            ])     
            ->add(ListMapper::NAME_ACTIONS, null, [ 'label' => "Opciones",
                'actions' => [
                    'edit' => [],
                    'delete' => []
                ]
            ])
        ;
    }

    protected function configureFormFields(FormMapper $formMapper): void 
    {
        $formMapper
            ->with("-", ['class' => 'col-md-12'])
                ->add('nombre', TextType::class,[])
                ->add('codigo', TextType::class,[])
                ->add('sat', null, ['label' => "SAT"])
                ->add('claseIdclase', ModelListType::class,['label' => 'Categoría del producto'], [ 'placeholder' => 'Seleccione una opción'])
            ->end() 
        ;
    }

    protected function configureExportFields(): array 
    {
        return array(
            'Nombre'=> 'nombre',
            'Código'=> 'codigo',
        );
    }

    public function getExportFormats(): array 
    {
        return ['xlsx'];
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {

        $Usuario=$this->tokenStorage->getToken()->getUser();
        $idusuario=$Usuario->getIdusuario();
        $mm = $this->getModelManager();
        $query = parent::configureQuery($query);
        $queryBuilder = $mm->getEntityManager($this->getClass())->createQueryBuilder();

        // Realiza una consulta para obtener la empresa del usuario autenticado.
        $queryBuilder->select('empresa')
            ->from('App\Entity\Usuarioempresapermiso', 'eup')
            ->innerJoin('eup.empresaIdempresa', 'empresa')
            ->innerJoin('eup.usuarioIdusuario', 'u')
            ->where('u.idusuario='.$idusuario);
    
        $rootAlias = current($query->getRootAliases());
    
        // Se une a la empresa a través de la relación de categoría, clase y empresa.
        $query->innerJoin($rootAlias.'.claseIdclase', 'cl')
              ->innerJoin('cl.empresaIdempresa', 'e');
    
        // Añade una restricción para que solo se muestren los productos relacionados con la empresa del usuario autenticado.
        $query->where($queryBuilder->expr()->in("e", $queryBuilder->getDQL()));
        
        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );
        $query->setParameter('status', '1'); 

        $query = parent::configureQuery($query);
    
        $rootAlias = current($query->getRootAliases());
    
        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );

        $query->setParameter('status', '1');

        return $query;
    }

    protected function configureBatchActions($actions): array 
    {
        if (
            $this->hasRoute('edit') && $this->hasAccess('edit') &&
            $this->hasRoute('delete') && $this->hasAccess('delete')
        ) {
            $actions['delete'] = [
                'ask_confirmation' => true
            ];
        }

        return $actions;
    }

    public function toString($object): string 
    {
        return $object instanceof Categoria
        ? $object->getNombre()
        : 'Categoria'; //shown in the breadcrumb on the create view
    }
}
