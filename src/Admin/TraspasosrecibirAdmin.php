<?php

declare(strict_types=1);

namespace App\Admin;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Sonata\Form\Type\DateRangePickerType;
use Sonata\DoctrineORMAdminBundle\Filter\ChoiceFilter;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;

final class TraspasosrecibirAdmin extends AbstractAdmin
{

    private $tokenStorage;
    private $em;
    private $empresasg;

    public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage, EntityManagerInterface $em)
    {
      parent::__construct($code, $class, $baseControllerName);
      $this->tokenStorage = $tokenStorage;
        $this->em = $em;
    }

    public function buscarEmpresas(){

        $user=$this->tokenStorage->getToken()->getUser();

        $query = $this-> em->createQuery(
            'SELECT e.idempresa
           FROM App\Entity\Usuarioempresapermiso up
           INNER JOIN up.empresaIdempresa e
           where up.status =:status and up.usuarioIdusuario =:idusuario
           '
        )->setParameters(['status'=>"1",'idusuario'=>$user->getIdusuario()]);
        $empresas= $query->getResult();


        $this->empresasg=$empresas;

    }

    protected function configureFormFields(FormMapper $formMapper): void
    {
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $filter
            ->add('transpasoalmacenIdtranspasoalmacen.creacion',DateRangeFilter::class, array(
                'show_filter' => true,
                'label'=>"Fecha de traspaso",
                'field_type' => DateRangePickerType::class,[
                    'dp_side_by_side'       => true,
                    'dp_use_current'        => true,
                    'dp_use_seconds'        => false,
                    'dp_collapse'           => true,
                    'dp_calendar_weeks'     => false,
                    'dp_view_mode'          => 'days',
                    'dp_min_view_mode'      => 'days',
                ]
            ))
            ->add('aceptada', ChoiceFilter::class, [
                'show_filter' => true,
                'label' => 'Solicitud',
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        'Aceptada' => '1', 'Pendiente' => '2','Negado' => '0'
                    ],
                ],
            ])
            ->add('transpasoalmacenIdtranspasoalmacen.sucursalIdsucursalorigen', ModelFilter::class, [
                'label'=>'Sucursal de origen',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('so')
                            ->andWhere('so.status = :status')
                            ->andWhere('IDENTITY(so.empresaIdempresa) IN (:empresa)')
                            ->setParameter('status', 1)
                            ->setParameter('empresa', $this->empresasg)
                            ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('transpasoalmacenIdtranspasoalmacen.sucursalIdsucursaldestino', ModelFilter::class, [
                'label'=>'Sucursal de destino',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('sd')
                            ->andWhere('sd.status = :status')
                            ->andWhere('IDENTITY(sd.empresaIdempresa) IN (:empresa)')
                            ->setParameter('status', 1)
                            ->setParameter('empresa', $this->empresasg)
                            ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
        ;
    }

    protected function configureListFields(ListMapper $listMapper): void { 
        $listMapper
            ->add(ListMapper::NAME_ACTIONS, null, [
                'label' => "Orden de salida",
                'actions' => [
                    'detalleOrdenSalida' => ['template' => 'CRUD/list_action_detalleOrdenSalida.html.twig'],
                ]
            ])
            ->add('AceptadaText', 'string', ['label' => 'Solicitud','template' => 'admin/aceptada_column.html.twig'])
            ->add('transpasoalmacenIdtranspasoalmacen.creacion','date', [
                'label'=>'Fecha de traspaso', 'format' => 'd-m-Y  H:i', 'timezone' => 'America/Mexico_City'
            ])
            ->add('transpasoalmacenIdtranspasoalmacen.notas', null, ['label'=>'Nota de salida'])
            ->add('nota', null, ['label'=>'Nota de rechazo'])
            ->add('transpasoalmacenIdtranspasoalmacen.sucursalIdsucursaldestino.nombre', null, ['label'=>'Sucursal de destino'])
            ->add('responsableentrada.nombre', null, ['label'=>'Responsable de entrada'])
            ->add('responsablesalida.nombre', null, ['label'=>'Responsable de salida']);
    }

    public function toString($object): string {
        return $object instanceof Membresia
           ? $object->getNombre()
           : 'Traspaso Almacen';
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface{

        $Usuario = $this->tokenStorage->getToken()->getUser();
        $idusuario = $Usuario->getIdusuario();
        $mm = $this->getModelManager();
        $query = parent::configureQuery($query);
        $queryBuilder = $mm->getEntityManager($this->getClass())->createQueryBuilder();
    
        // Subconsulta para obtener la empresa del usuario autenticado.
        $queryBuilder->select('empresa')
            ->from('App\Entity\Usuarioempresapermiso', 'eup')
            ->innerJoin('eup.empresaIdempresa', 'empresa')
            ->innerJoin('eup.usuarioIdusuario', 'u')
            ->where('u.idusuario = :idusuario')
            ->setParameter('idusuario', $idusuario);
    
            
        $rootAlias = current($query->getRootAliases());
    
        // Se une a la empresa a través de la relación de Ordensalida, transpasoalmacen, sucursal y empresa.
        $query->innerJoin($rootAlias . '.transpasoalmacenIdtranspasoalmacen', 'ta')
              ->innerJoin('ta.sucursalIdsucursalorigen', 's')
              ->innerJoin('s.empresaIdempresa', 'e');
    
        // Añade una restricción para que solo se muestren las ordensalidas relacionadas con la empresa del usuario autenticado.
        $query->andWhere($query->expr()->in('e', $queryBuilder->getDQL()));
        $query->setParameter('idusuario', $idusuario); // Agregar este parámetro a la consulta principal
    
        $query->andWhere($query->expr()->eq($rootAlias . '.status', ':status'));
        $query->setParameter('status', '1');


        $query = parent::configureQuery($query);
    
        $rootAlias = current($query->getRootAliases());
        $query->addOrderBy($rootAlias.'.creacion', 'DESC');
        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );
        $query->setParameter('status', '1');

        $query->addOrderBy($rootAlias . '.creacion', 'DESC');
    
        return $query;
    }

    public function getExportFormats(): array 
    {
        return ['xlsx'];
    }    
    protected function configureRoutes(RouteCollectionInterface $collection): void {
        $collection->remove('create');
        $collection->remove('delete');
        $collection->add('detalleOrdenSalida', $this->getRouterIdParameter().'/detalleOrdenSalida');
        ;
    }
    

}
