<?php

declare(strict_types=1);

namespace App\Admin;

use Doctrine\ORM\EntityManagerInterface;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Sonata\DoctrineORMAdminBundle\Filter\ChoiceFilter;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Sonata\Form\Type\DateRangePickerType;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Sonata\DoctrineORMAdminBundle\Filter\DateTimeFilter;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Doctrine\ORM\EntityRepository;


final class CargainventariologAdmin extends AbstractAdmin
{
    private $tokenStorage;

    private $empresasg;
    private $em;

    public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage, EntityManagerInterface $em)
    {
      parent::__construct($code, $class, $baseControllerName);
      $this->tokenStorage = $tokenStorage;
        $this->em = $em;
    }

    public function buscarEmpresas()
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $query = $this->em->createQuery(
            'SELECT e.idempresa
               FROM App\Entity\Usuarioempresapermiso up
               INNER JOIN up.empresaIdempresa e
               where up.status =:status and up.usuarioIdusuario =:idusuario
               '
        )->setParameters(['status' => "1", 'idusuario' => $user->getIdusuario()]);
        $empresas = $query->getResult();

        $this->empresasg = $empresas;
    }

    public function downloadAction($filename)
    {
        // Ruta completa del archivo
        $filePath = '/var/www/html/public/uploads/carga-masiva/inventario/Carga-Inv/' . $filename;

        // Verificar si el archivo existe
        if (!file_exists($filePath)) {
            throw $this->createNotFoundException('El archivo no existe.');
        }

        // Servir el archivo como respuesta binaria
        $response = new BinaryFileResponse($filePath);
        $response->setContentDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            $filename // Nombre del archivo para la descarga
        );

        return $response;
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $user = $this->tokenStorage->getToken()->getUser();
        
        $filter
            ->add('cantidad', null, ['label' => "Cantidad"])
            ->add('fecha', DateRangeFilter::class, array(
                'label' => "Fecha de alta",
                'show_filter' => true,
                'field_type' => DateRangePickerType::class,[
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('conexcel',  ChoiceFilter::class, [
                'label' =>  'Disponibilidad de archivo',
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        'No' => '0', 'Si' => '1'
                    ],
                ],
            ])
            ->add('archivoexcel', null, ['label' => "Archivo de Excel"])
            ->add('usuarioIdusuario', ModelFilter::class, [
                'label'=>'Responsable',
                'field_options' => [
                    'class' => 'App\Entity\Usuario',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('u')
                            ->join('u.sucursalIdsucursal', 's')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('u.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', 1)
                            ->setParameter('empresa', $this->empresasg);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('stockIdstock.codigobarras', null, ['label' => "SKU",'show_filter' => true])
            ->add('stockIdstock.productoIdproducto.codigobarrasuniversal', null, ['label' => "UPC", 'show_filter' => true])
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        
        $list
            ->add('cantidad', null, ['label' => "Cantidad"])
            ->add('fecha', 'date', [
                'label' => 'Fecha de alta',
                'format' => 'd-m-Y  H:i a',
                'timezone' => 'America/Mexico_City',
            ])
            ->add('conexcel', 'choice', [
                'label' => 'Disponibilidad de archivo',
                'choices' => [
                    '' => 'No',
                    '0' => 'No',
                    '1' => 'Si',
                ],
            ])
            ->add('usuarioIdusuario.nombre', null, ['label' => "Responsable"])
            ->add('stockIdstock.codigobarras', null, ['label' => "SKU"])
            ->add('stockIdstock.productoIdproducto.codigobarrasuniversal', null, ['label' => "UPC "])
            ->add('archivoexcel', 'string', [
                'label' => "Archivo de Excel",
                'template' => 'admin/list_archivo_excel.html.twig', // Usa un template Twig personalizado
            ])
        ;
    }

    protected function configureExportFields(): array 
    {
        return array (
            'Cantidad'=> 'cantidad',
            'Fecha de alta'=>'fecha',
            'Disponibilidad de archivo'=>'conexcel',
            'Archivo de Excel'=>'archivoexcel',
            'Responsable'=>'usuarioIdusuario.nombre',
            'SKU'=>'stockIdstock.codigobarras',
            'UPC'=>'stockIdstock.productoIdproducto.codigobarrasuniversal',
        );
    }
    
    public function getExportFormats(): array 
    {
        return ['xlsx'];
    }

    protected function configureRoutes(RouteCollectionInterface $collection): void 
    {
        $collection->add('download', $this->getRouterIdParameter() . '/download');
        $collection->remove('create');
        $collection->remove('delete');
    }

    protected function configureQuery(ProxyQueryInterface $query):ProxyQueryInterface
    {
        $Usuario=$this->tokenStorage->getToken()->getUser();
        $idusuario=$Usuario->getIdusuario();
        $mm = $this->getModelManager();
        $query = parent::configureQuery($query);
        $queryBuilder = $mm->getEntityManager($this->getClass())->createQueryBuilder();

        $queryBuilder->select('empresa')
        ->from('App\Entity\Usuarioempresapermiso', 'eup')
        ->innerJoin('eup.empresaIdempresa', 'empresa')
        ->innerJoin('eup.usuarioIdusuario', 'u')
        ->where('u.idusuario='.$idusuario);

    $rootAlias = current($query->getRootAliases());

    // Se une a la empresa a través de la relación de categoría, clase y empresa.
    $query->innerJoin($rootAlias.'.stockIdstock', 'st')
          ->innerJoin('st.sucursalIdsucursal', 's')
          ->innerJoin('s.empresaIdempresa', 'e');

    // Añade una restricción para que solo se muestren los productos relacionados con la empresa del usuario autenticado.
    $query->where($queryBuilder->expr()->in("e", $queryBuilder->getDQL()));
    
    $query->andWhere(
        $query->expr()->eq($rootAlias . '.status', ':status')
    );
    $query->setParameter('status', '1');


        $query = parent::configureQuery($query);
    
        $rootAlias = current($query->getRootAliases());

        $query->addOrderBy($rootAlias . '.fecha', 'DESC');

        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );
      
        $query->setParameter('status', '1');
    
        return $query;
    }
}