<?php

declare(strict_types=1);

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;

final class ProveedorcontactoAdmin extends AbstractAdmin
{
    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('proveedorIdproveedor' , null, ['label' => 'Proveedor', 'show_filter' => true,])
            ->add('apellidopaterno', null, ['label' => 'Apellido paterno',])
            ->add('apelliidomaterno', null, ['label' => 'Apellido materno',])
            ->add('nombre', null, ['label' => 'Nombre',])
            ->add('puesto', null, ['label' => 'Puesto',])
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('proveedorIdproveedor' , null, ['label' => 'Proveedor',])
            ->add('apellidopaterno' , null, ['label' => 'Apellido paterno',])
            ->add('apelliidomaterno' , null, ['label' => 'Apellido materno',])
            ->add('nombre', null, ['label' => 'Nombre',])
            ->add('puesto', null, ['label' => 'Puesto',])
            ->add('nota', null, ['label' => 'Nota',])
            ->add('creacion', 'date', array('label'=>'Fecha de creación', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('actualizacion', 'date', array('label'=>'Fecha de actualización', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add(ListMapper::NAME_ACTIONS, null, ['label' => "Opciones",
                'actions' => [
                    'edit' => [],
                    'delete' => [],
                ],
            ])
        ;
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form 
            ->with("-",['class' => 'col-md-6 '])
                ->add('proveedorIdproveedor' , ModelListType::class, ['btn_list' => true, 'label'=>"Proveedor",], ['delete'])
                ->add('apellidopaterno', null, ['label' => 'Apellido paterno',])
                ->add('apelliidomaterno', null, ['label' => 'Apellido materno',])
            ->end()
            ->with(" ",['class' => 'col-md-6 '])
                ->add('nombre', null, ['label' => 'Nombre',])
                ->add('puesto', null, ['label' => 'Puesto',])
            ->add('nota', null, ['label' => 'Nota',])
            ->end()
        ;
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show
            ->add('idproveedorcontacto')
            ->add('nombre')
            ->add('apellidopaterno')
            ->add('apelliidomaterno')
            ->add('puesto')
            ->add('nota')
            ->add('status')
            ->add('creacion')
            ->add('actualizacion')
        ;
    }

    public function prePersist($object): void
    {
        $object->setCreacion(new \DateTime("now"));
        $object->setActualizacion(new \DateTime("now"));
    }

    public function preUpdate($object): void 
    {
        $object->setActualizacion(new \DateTime("now"));
    }
}
