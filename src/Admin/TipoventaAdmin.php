<?php

declare(strict_types=1);

namespace App\Admin;

use App\Entity\Tipoventa;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Validator\Constraints\Regex;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

final class TipoventaAdmin extends AbstractAdmin
{

    private $tokenStorage;
    private $em;
    private $empresasg;

    public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage, EntityManagerInterface $em)
    {
        parent::__construct($code, $class, $baseControllerName);
        $this->tokenStorage = $tokenStorage;
        $this->em = $em;
    }

    public function buscarEmpresas()
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $query = $this->em->createQuery(
            'SELECT e.idempresa
            FROM App\Entity\Usuarioempresapermiso up
            INNER JOIN up.empresaIdempresa e
            where up.status =:status and up.usuarioIdusuario =:idusuario
            '
        )->setParameters(['status' => "1", 'idusuario' => $user->getIdusuario()]);
        $empresas = $query->getResult();

        $this->empresasg = $empresas;

    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
        $query = parent::configureQuery($query);

        $rootAlias = current($query->getRootAliases());

        $query->addOrderBy($rootAlias . '.nombre', 'DESC')
            ->where($rootAlias . '.status=1');;
        return $query;
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $filter
            ->add('nombre', null, ['show_filter' => true, 'label' => "Nombre"])
            ->add('empresaIdempresa', ModelFilter::class, [
                'label' => 'Empresas',
                'show_filter' => true,
                'field_options' => [
                    'class' => 'App\Entity\Empresa',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('e')
                            ->andWhere('e.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('empresa', $this->empresasg);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ]);
    }

    protected function configureListFields(ListMapper $list): void
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $list
            ->add('nombre', null, ['label' => "Nombre"])
            ->add('empresaIdempresa', null, array('label' => 'Empresa'))
            ->add('apartararmazon', 'choice', array(
                'label' => 'Apartado',
                'choices' => [
                    '0' => 'No',
                    '1' => 'Sí',
                    '' => 'No'
                ]
            ))
            ->add('nota', null, ['label' => "Notas"])
            ->add('totalventaconiva', null, ['label' => "Total Venta con Iva"])
            ->add('descuentoproductosalmacenables', 'choice', array(
                'label' => 'Descuento de armazón',
                'choices' => [
                    '0' => 'No',
                    '1' => 'Si',
                    '' => 'No'
                ]
            ))
            ->add('descuentoservicios', 'choice', array(
                'label' => 'Descuento de servicios',
                'choices' => [
                    '0' => 'No',
                    '1' => 'Si',
                    '' => 'No'
                ]
            ))
            ->add('preciobase', 'choice', array(
                'label' => 'Precio base',
                'choices' => [
                    '' => '',
                    '0' => 'Precio publico',
                    '1' => 'Precio especial',
                    '2' => 'Precio de distribuidor',
                    '3' => 'Precio de subdistribuidor'
                ]
            ))
            ->add('customerbilling', 'choice', array(
                'label' => 'Facturado',
                'choices' => [
                    '' => '',
                    '0' => 'No Facturado',
                    '1' => 'Facturado'
                ]
            ))
            ->add('onlinestore', 'choice', array(
                'label' => 'Para tienda en línea',
                'choices' => [
                    '0' => 'No',
                    '1' => 'Sí',
                ]
            ))
            ->add(ListMapper::NAME_ACTIONS, null, ['label' => "Opciones",
                'actions' => [
                    'show' => [],
                    'edit' => [],
                    'delete' => [],
                ],
            ]);
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->with("-", ['class' => 'col-md-6'])
            ->add('nombre', null, ['label' => 'Nombre', 'required' => true])
            ->add('apartararmazon', ChoiceType::class, [
                'label' => 'Apartado',
                'choices' => [
                    'Si' => "1",
                    'No' => "0",
                ]
            ])
            ->add('nota', TextareaType::class, ['label' => 'Notas', 'required' => false])
            ->add('notagarantia', TextareaType::class, ['label' => 'Texto de Garantía que saldrá en el ticket', 'required' => false])
            ->add('descuentoproductosalmacenables', NumberType::class, [
                'required' => false,
                'label' => 'Descuento de almacenables ',
                'help' => 'Descuento que se pondrá automaticamente en productos de almacen como armazones',
                'attr' => array(
                    'onkeydown' => "validarNumero(this);",
                    'onkeyup' => "validarNumero(this);"
                )
            ])
            ->add('descuentoservicios', NumberType::class, [
                'required' => false,
                'label' => 'Descuento de servicios ',
                'help' => 'Decuento que se pondrá automáticamente al poner un servicio - micas, tratamientos  ',
                'attr' => array(
                    'onkeydown' => "validarNumero(this);",
                    'onkeyup' => "validarNumero(this);"
                )
            ])
            ->add('preciofijoproductosalmacenables', TextType::class, [
                'required' => false,
                'label' => 'Precio fijo en Almacenables ',
                'help' => 'Precio fijo en los productos de almacén, se pondrá automáticamente;  si es cero no se tomará en cuenta',
                'attr' => array(
                    'onkeydown' => "validarNumero(this);",
                    'onkeyup' => "validarNumero(this);"
                )
            ])
            ->add('preciofijoservicios', TextType::class, [
                'required' => false,
                'label' => 'Precio fijo en Servicios ',
                'help' => 'Precio fijo en servicios - micas, tratamientos; si es cero no se tomará en cuenta ',
                'attr' => array(
                    'onkeydown' => "validarNumero(this);",
                    'onkeyup' => "validarNumero(this);"
                )
            ])
            ->add('totalventaconiva', TextType::class, [
                'label' => 'Precio fijo con IVA',
                'required' => false,
                'help' => 'El precio se pondrá al seleccionar el tipo de venta',
                'data' => $this->getSubject() && $this->getSubject()->getTotalVentaConIva() !== null
                    ? $this->getSubject()->getTotalVentaConIva()
                    : '0.00', // Asigna 0.00 solo si el valor es nulo o vacío
                'attr' => array(
                    'onkeydown' => "validarNumero(this);",
                    'onkeyup' => "validarNumero(this);"
                ),
            ])
            ->add('mostrarcampogarantia', ChoiceType::class, [
                'label' => 'Mostrar garantia',
                'choices' => [
                    'Si' => "1",
                    'No' => "0",
                ]
            ])
            ->add('preciobase', ChoiceType::class, [
                'label' => 'Precio base',
                'choices' => [
                    'Precio público' => "0",
                    'Precio especial' => "1",
                    'Precio de distribuidor' => "2",
                    'Precio de subdistribuidor' => "3",
                ]
            ])
            ->add('fixproducts', TextType::class, [
                'label' => '¿Deseas agregar tratamientos fijos?',
                'required' => false,
                'help' => 'Separe los códigos de barras universales por comas',
            ])
            ->add('documentoobligatoriocerrarventa', CheckboxType::class, [
                'label' => 'Documento Obligatorio Cerrar Venta',
                'required' => false,
            ])
            ->end()
            ->with(" ", ['class' => 'col-md-6'])
            ->add('mostrartopesautorizados', ChoiceType::class, [
                'label' => 'Mostrar topes autorizados en el ticket ',
                'help' => 'se pondrá el texto que se ponga en el campo de abajo',
                'choices' => [
                    'Si' => "1",
                    'No' => "0",
                ]
            ])
            ->add('textotopesautorizados', TextareaType::class, ['label' => 'Texto en el Ticket', 'required' => false])
            ->add('mostrardetalleprecio', ChoiceType::class, [
                'label' => 'Mostrar detalle de precio',
                'choices' => [
                    'Si' => "1",
                    'No' => "0",
                ]
            ])
            ->add('mostrarnumeroempleado', ChoiceType::class, [
                'label' => 'Mostrar número de empleado',
                'help' => ' Se podrá seleccionar de un usuario registrado',
                'choices' => [
                    'Si' => "1",
                    'No' => "0",
                ]
            ])
            ->add('mostrarunidadprocedencia', ChoiceType::class, [
                'label' => 'Mostrar unidad de procedencia',
                'choices' => [
                    'Si' => "1",
                    'No' => "0",
                ]
            ])
            ->add('mostrarbeneficiario', ChoiceType::class, [
                'label' => 'Mostrar lista beneficiarios',
                'choices' => [
                    'Si' => "1",
                    'No' => "0",
                ]
            ])
            ->add('diascredito', NumberType::class, [
                'label' => 'Dias de crédito',
                'required' => true,
                'data' =>'0.00', // Usar 0.00 solo si el valor es nulo o vacío
            ])
            ->add('onlinestore', ChoiceType::class, [
                'label' => '¿El tipo de venta es para la tienda en línea?',
                'choices' => [
                    'No' => "0",
                    'Si' => "1"
                ]
            ])
            ->add('empresaIdempresa', ModelListType::class, ['required' => true, 'btn_list' => true, 'label' => "Empresa",], ['delete'])
            ->add('paymenttypeIdpaymenttype', ModelListType::class, ['required' => false, 'btn_list' => true, 'label' => "¿Deseas que se agregue un pago automáticamente con el precio total de la venta? Si no, deja vacio el campo",], ['delete'])
            ->add('pagoalfinal', ChoiceType::class, [
                'label' => 'Agregar pago convenio al cerrar venta',
                'choices' => [
                    'Si' => "1",
                    'No' => "0",
                ]
            ])
            ->add('enviarWhatsapp', CheckboxType::class, [
                'label' => 'Enviar WhatsApp al comprar',
                'required' => false,
                'mapped' => false, // Esto sigue siendo un campo virtual
                'data' => $this->getVentaMsgFieldValue('enviarWhatsapp'), // Inicializamos el valor con la función
            ])
            ->add('enviarEmail', CheckboxType::class, [
                'label' => 'Enviar Email al comprar',
                'required' => false,
                'mapped' => false,
                'data' => $this->getVentaMsgFieldValue('enviarEmail'), // Inicializamos el valor con la función
            ])
            ->add('enviarSms', CheckboxType::class, [
                'label' => 'Enviar SMS al comprar',
                'required' => false,
                'mapped' => false,
                'data' => $this->getVentaMsgFieldValue('enviarSms'), // Inicializamos el valor con la función
            ])
            ->end()
            ->get('documentoobligatoriocerrarventa')->addModelTransformer(new CallbackTransformer(
                function ($value) {
                    return $value === '1';
                },
                function ($value) {
                    return $value ? '1' : '0';
                }
            ));
    }

    public function preValidate($datos): void
    {
        $datos->setPreciofijoproductosalmacenables(str_replace(",", "", $datos->getPreciofijoproductosalmacenables()));
        $datos->setPreciofijoservicios(str_replace(",", "", $datos->getPreciofijoservicios()));
        $datos->setTotalventaconiva(str_replace(",", "", $datos->getTotalventaconiva()));

        if ($datos->getOnlinestore() == '1') {
            $this->checkOnlineStore($datos);
            $datos->setOnlinestore('1');
        }

    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show
            ->add('idtipoventa')
            ->add('nombre')
            ->add('apartararmazon')
            ->add('nota')
            ->add('status')
            ->add('descuentoproductosalmacenables')
            ->add('descuentoservicios');
    }

    public function getExportFormats(): array
    {
        return ['xlsx'];
    }

    protected function configureExportFields(): array
    {
        return array(
            'Nombre' => 'nombre',
            'Apartado' => 'apartararmazon',
            'Notas' => 'nota',
            'Descuento de armazón' => 'descuentoproductosalmacenables',
            'Descuento de servicios' => 'descuentoservicios',
        );
    }

    public function toString($object): string
    {
        return "Tipo de venta";
    }

    public function checkOnlineStore($object): void
    {
        if ($object->getOnlinestore() == '1') {
            $query = $this->em->createQuery(
                'SELECT tv
                FROM App\Entity\Tipoventa tv
                INNER JOIN  tv.empresaIdempresa e
                where tv.status=:status and e.idempresa=:idempresa
                '
            )->setParameters(['status' => '1', 'idempresa' => $object->getEmpresaIdempresa()]);

            $saleTypes = $query->getResult();

            foreach ($saleTypes as $saleType) {
                $saleType->setOnlinestore('0');
                $this->em->persist($saleType);
            }
            $this->em->flush();
        }
    }

    public function prePersist($object): void
    {
        $this->handleCustomFields($object);
    }

    public function preUpdate($object): void
    {
        $this->handleCustomFields($object);
    }

    private function handleCustomFields($object): void
    {
        // Obtener los valores de los checkboxes
        $enviarWhatsapp = $this->getForm()->get('enviarWhatsapp')->getData() ? 'si' : 'no';
        $enviarEmail = $this->getForm()->get('enviarEmail')->getData() ? 'si' : 'no';
        $enviarSms = $this->getForm()->get('enviarSms')->getData() ? 'si' : 'no';

        // Crear el array que guardaremos como JSON
        $ventaMsgArray = [
            'enviarWhatsapp' => $enviarWhatsapp,
            'enviarEmail' => $enviarEmail,
            'enviarSms' => $enviarSms
        ];

        $object->setVentamsgarray($ventaMsgArray);
    }

    private function getVentaMsgFieldValue(string $field): bool
    {
        // Obtener el valor del campo 'ventaMsgArray' de la entidad actual
        $ventaMsgArray = $this->getSubject()->getVentamsgarray();

        // Verificar si el campo existe en el array y si está marcado como 'si'
        return isset($ventaMsgArray[$field]) && $ventaMsgArray[$field] === 'si';
    }

}