<?php

declare(strict_types=1);

namespace App\Admin;

use App\Service\FileUploader;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Sonata\DoctrineORMAdminBundle\Filter\ModelAutocompleteType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Sonata\DoctrineORMAdminBundle\Filter\ChoiceFilter;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\Mime\Address;
use Sonata\DoctrineORMAdminBundle\Filter\StringFilter;
use Sonata\DoctrineORMAdminBundle\Filter\StringListFilter;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Twig\Environment;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use App\Service\MailService;
use App\Entity\Ventafactura;

final class VentafacturaAdmin extends AbstractAdmin
{
    private $mailer;
    private $tokenStorage;
    private $em;
    private $empresasg;
    private $mailService;
    private $twig;

    public function __construct(
        ?string $code = null,
        ?string $class = null,
        ?string $baseControllerName = null,
        MailerInterface $mailer,
        EntityManagerInterface $em,
        TokenStorageInterface $tokenStorage,
        Environment $twig,
        MailService $mailService
    ) {
        parent::__construct($code, $class, $baseControllerName);
        $this->mailer = $mailer;
        $this->em = $em;
        $this->tokenStorage = $tokenStorage;
        $this->mailService = $mailService;
        $this->twig = $twig;
    }

    public function buscarEmpresas()
    {

        $user = $this->tokenStorage->getToken()->getUser();

        $query = $this->em->createQuery(
            'SELECT e.idempresa
               FROM App\Entity\Usuarioempresapermiso up
               INNER JOIN up.empresaIdempresa e
               where up.status =:status and up.usuarioIdusuario =:idusuario
               '
        )->setParameters(['status' => "1", 'idusuario' => $user->getIdusuario()]);
        $empresas = $query->getResult();


        $this->empresasg = $empresas;
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $filter
            ->add('ventaIdventa.folio', null, ['label' => "Folio", 'show_filter' => true,])
            ->add('ventaIdventa.clienteIdcliente', null, ['label' => "Cliente"])
            ->add('rfcreceptor', null, ['label' => "RFC", 'show_filter' => true,])
            ->add('regimenfiscal', null, ['label' => "Regimen fiscal"])
            ->add('usucfdi', null, ['label' => "CFDI"])
            ->add('nombrereceptor', null, ['label' => "Nombre"])
            ->add('montoventa', null, ['label' => "Monto"])
            ->add('metodopago', null, ['label' => "Metodo de pago"])
            ->add('formapago', null, ['label' => "Forma de pago"])
            ->add('ventaIdventa.sucursalIdsucursal.empresaIdempresa', ModelFilter::class, [
                'label' => 'Empresas',
                'field_options' => [
                    'class' => 'App\Entity\Empresa',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('e')
                            ->andWhere('e.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('empresa', $this->empresasg);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])

        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('ventaIdventa.folio', null, ['label' => "Folio del ticket"])
            ->add('ventaIdventa.clienteIdcliente.nombreCompleto', null, ['label' => "Cliente"])
            ->add('creacion', 'date', array('label' => 'Fecha de alta', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('fechacomprobante', 'date', array('label' => 'Fecha de envio factura', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('clientefacturadatosIdclientefacturadatos.rfc', null, ['label' => "RFC"])
            ->add('clientefacturadatosIdclientefacturadatos.razonsocial', null, ['label' => "Razón social"])
            ->add('ventaIdventa.sucursalIdsucursal.empresaIdempresa.nombre', null, ['label' => "Empresa"])
            ->add('estado', 'choice', array(
                'label' => 'Estado',
                'choices' => [
                    '0' =>  'Pendiente',
                    '1' => 'Enviado',
                    '' =>  'Pendiente'
                ]
            ))
            ->add(ListMapper::NAME_ACTIONS, null, [
                'label' => "Opciones",
                'actions' => [
                    'edit' => [],
                    'detalleFactura' => ['template' => 'CRUD/list__action_detalleFactura.html.twig'],
                ],
            ]);
    }
    protected function configureRoutes(RouteCollectionInterface $collection): void
    {
        // Removing the export route will disable exporting entities.
        $collection->remove('delete');
        $collection->remove('create');
        $collection
            ->add('detalleFactura', $this->getRouterIdParameter() . '/detalleFactura')
            ->add('download_zip', 'download-zip/{clienteId}/{filename}/{groupId}', [
                'groupId' => null // Parámetro opcional
            ]);
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->with(" ", ['class' => 'col-md-6'])
            ->add('clientefacturadatosIdclientefacturadatos', ModelListType::class, [
                'label' => 'Datos de facturación del cliente',
                'class' => 'App\Entity\ClienteFacturaDatos',
            ])
            ->end()

            ->with("-", ['class' => 'col-md-6'])
            ->add('ventaIdventa', ModelListType::class, [
                'label' => 'Folio del ticket',
                'class' => 'App\Entity\Venta', // Asegúrate de que esta es la entidad correcta
                'required' => false,
            ])

            ->add('file', FileType::class, [
                'label' => 'Zip de facturación (XML y PDF)',
                'required' => false,
                'constraints' => [
                    new File([
                        'maxSize' => '5120k',
                        'mimeTypes' => [
                            'application/zip',
                        ],
                        'mimeTypesMessage' => 'Sube un archivo válido',
                    ])
                ],
            ])
            ->end();
    }



    public function getExportFormats(): array
    {
        return ['xlsx', 'csv'];
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show
            ->add('idventafactura', null, ['label' => 'Venta Factura'])
            ->add('creacion')
            ->add('estado')
            ->add('montoventa')
            ->add('rfcemisor')
            ->add('rfcreceptor')
            ->add('regimenfiscal')
            ->add('usucfdi')
            ->add('nombreemisor')
            ->add('nombrereceptor')
            ->add('metodopago')
            ->add('formapago')
            ->add('condicionespago')
            ->add('lugarexpedicion')
            ->add('fechacomprobante')
            ->add('fechatimbrado')
            ->add('nombrearchivoxml')
            ->add('nombrearchivopdf')
            ->add('nombrezip')
            ->add('status');
    }

    public function prePersist(object $ventaFactura): void
    {
        $ventaFactura->setCreacion(new \DateTime("now"));
        $ventaFactura->setEstado("1");
        $this->manageFileUpload($ventaFactura);
        $this->enviarCorreo($ventaFactura);
    }

    public function preUpdate(object $ventaFactura): void
    {
        $ventaFactura->setEstado("1");
        $this->manageFileUpload($ventaFactura);
        $this->enviarCorreo($ventaFactura);
    }

    private function manageFileUpload(object $ventaFactura): void
    {
        if ($ventaFactura->getFile()) {
            $ventaFactura->upload();
        }
    }

    private function enviarCorreo($object)
    {
        $Cliente = $object->getClientefacturadatosIdclientefacturadatos()->getClienteIdcliente();
        $Empresa = $object->getVentaIdventa()->getSucursalIdsucursal()->getEmpresaIdempresa();
        $msj = "";
        $logo = $Empresa->getLogoimagen();
        $publicidad = $Empresa->getImagenemailfacturacion();
        $nombre = $Empresa->getNombre();
        $ClienteFacturaDatos = $object->getClientefacturadatosIdclientefacturadatos();
        $emailDelDestinatario = $ClienteFacturaDatos->getEmail();
        $archivo = $object->getNombrezip();
        $folio = $object->getVentaIdventa()->getFolio();

        if (!filter_var($emailDelDestinatario, FILTER_VALIDATE_EMAIL)) {
            throw new \Exception("La dirección de correo electrónico es inválida: " . $emailDelDestinatario);
        }


        $template = $this->twig->render(
            'emails/envioZIP.html.twig',
            [
                'cliente' => $Cliente,
                'logo' => $logo,
                'publicidad' => $publicidad,
                'nombre' => $nombre,
            ]
        );

        $archivoEmail = "uploads/zipFacturas/" . $archivo . "/" . $archivo;
        $attachmentPath = "uploads/zipFacturas/" . $archivo . "/" . $archivo;


        $emailData = [
            'recipient' => $emailDelDestinatario,
            'subject' => "Documentos de facturación para la venta con folio " . $folio,
            'body' => $template,
            'attachment' => $attachmentPath,
            'isHtml' => true,
        ];

        foreach ($emailData as $key => $value) {
            if ($value === null) {
                echo "Error: El valor para '{$key}' es nulo.";
                die();
            }
        }

        try {
            $this->mailService->sendEmail($emailData, $attachmentPath);
        } catch (\Exception $e) {
            $msj = 'Error al enviar factura por correo: ' . $e->getMessage();
            echo $msj;
            die;
        }
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
        $query = parent::configureQuery($query);

        $rootAlias = current($query->getRootAliases());

        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );

        $query->setParameter('status', '1');
        $query->addOrderBy($rootAlias . '.creacion', 'DESC');


        return $query;
    }

    protected function configureExportFields(): array
    {

        return array(
            'Cliente' => 'clienteIdcliente.nombreCompleto',
        );
    }
}
