<?php

declare(strict_types=1);

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Sonata\Form\Type\DateRangePickerType;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;

final class GraduacionAdmin extends AbstractAdmin
{

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('creacion', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de creación",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('actualizacion', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de actualización",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('clienteIdcliente.nombre', null, array('label' => 'Nombre'))
            ->add('clienteIdcliente.apellidopaterno', null, array('label' => 'Apellido paterno'))
            ->add('clienteIdcliente.apellidomaterno', null, array('label' => 'Apellido materno'))
            ->add('clienteIdcliente.empresaclienteIdempresacliente.nombre', null, array('label'=>'Empresa del cliente','show_filter' => true))
            ->add('clienteIdcliente.unidadIdunidad', ModelFilter::class, ['label' => "Unidad", 'field_options' => ['expanded' => false, 'multiple' => true]])
            ->add('clienteIdcliente.numeroempleado', null, array('label'=>'Número de empleado'))
            ->add('clienteIdcliente.telefono', null, array('label'=>'Teléfono'))
            ->add('clienteIdcliente.email', null, array('label'=>'Correo electrónico'))
            ->add('usuarioIdusuario.nombre', null, array('label'=>'Optometrista','show_filter' => true))
            ->add('usuarioIdusuario.sucursalIdsucursal', ModelFilter::class, ['label' => "Sucursal", 'field_options' => ['expanded' => false, 'multiple' => true]])
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add(ListMapper::NAME_ACTIONS, null, [
                'label' => "Opciones",
                'actions' => [
                    'delete' => [],
                ],
            ])
            ->add('nombrecompleto', 'text', [
                'label' => 'Nombre Completo',
                'sortable' => false,
            ])
            ->add('clienteIdcliente.empresaclienteIdempresacliente.nombre', null, array('label'=>'Empresa del cliente'))
            ->add('clienteIdcliente.unidadIdunidad.nombre', null, array('label'=>'Unidad'))
            ->add('clienteIdcliente.numeroempleado', null, array('label'=>'Número de empleado'))
            ->add('clienteIdcliente.telefono', null, array('label'=>'Teléfono'))
            ->add('clienteIdcliente.email', null, array('label'=>'Correo electrónico'))
            ->add('anamnesis', null, array('label'=>'Anamnesis'))
            ->add('isglassesuser' , 'choice', array('label'=>'¿Usa lentes actualmente?', 'choices' => ['' => 'No', '0' => 'No', '1' => 'Si']))
            ->add('hasglassesnow' , 'choice', array('label'=>'¿Los trae consigo?', 'choices' => ['' => 'No', '0' => 'No', '1' => 'Si']))
            ->add('gpesferaod', null, array('label'=>'Rx ant Esfera OD'))
            ->add('gpesferaoi', null, array('label'=>'Rx ant Esfera OI'))
            ->add('gpcilindrood', null, array('label'=>'Rx ant Cilindro OD'))
            ->add('gpcilindrooi', null, array('label'=>'Rx ant Cilindro OI'))
            ->add('gpejeod', null, array('label'=>'Rx ant Eje OD'))
            ->add('gpejeoi', null, array('label'=>'Rx ant Eje OI'))
            ->add('gpaddod', null, array('label'=>'Rx ant Add'))
            ->add('gpavlejosod', null, array('label'=>'Rx ant AV lejos OD'))
            ->add('gpavlejosoi', null, array('label'=>'Rx ant AV lejos OI'))
            ->add('gpavcercaod', null, array('label'=>'Rx ant AV cerca OD'))
            ->add('gpavcercaoi', null, array('label'=>'Rx ant AV cerca OI'))
            ->add('tipolente', 'choice', array('label'=>'Tipo de Lente', 'choices' => ['' => '', '0' => '', '1' => 'Armazón', '2' => 'Lente de contacto']))
            ->add('apobservaciones', null, array('label'=>'Observaciones del armazón previo'))
            ->add('tipolentecontacto', 'choice', array('label'=>'Tipo de lente de contacto', 'choices' => ['' => '', '0' => '', '1' => 'Blando', '2' => 'Rígido']))
            ->add('cb', null, array('label'=>'Curva base'))
            ->add('diam', null, array('label'=>'Diámetro'))
            ->add('materialIdmaterial.nombre', null, array('label'=>'Material'))
            ->add('disenolenteIddisenolente.nombre', null, array('label'=>'Diseño'))
            ->add('tratamientoIdtratamiento.nombre', null, array('label'=>'Tratamiento'))
            ->add('avsrxlejosod', null, array('label'=>'AV s/Rx Lejos OD'))
            ->add('avsrxlejosoi', null, array('label'=>'AV s/Rx Lejos OI'))
            ->add('avsrxcercaod', null, array('label'=>'AV s/Rx Cerca OD'))
            ->add('avsrxcercaoi', null, array('label'=>'AV s/Rx Cerca OI'))
            ->add('avsrxcvod', null, array('label'=>'AV s/Rx CV OD'))
            ->add('avsrxcvoi', null, array('label'=>'AV s/Rx CV OI'))
            ->add('esfodsf', null, array('label'=>'Subjetiva final Esfera OD'))
            ->add('esfoisf', null, array('label'=>'Subjetiva final Esfera OI'))
            ->add('cilodsf', null, array('label'=>'Subjetiva final Cilindro OD'))
            ->add('ciloisf', null, array('label'=>'Subjetiva final Cilindro OI'))
            ->add('ejeodsf', null, array('label'=>'Subjetiva final Eje OD'))
            ->add('ejeoisf', null, array('label'=>'Subjetiva final Eje OI'))
            ->add('avlodsf', null, array('label'=>'Subjetiva final AV lejos OD'))
            ->add('avloisf', null, array('label'=>'Subjetiva final AV lejos OI'))
            ->add('avcsaodsf', null, array('label'=>'Subjetiva final AV cerca s/Add OD'))
            ->add('avcsaoisf', null, array('label'=>'Subjetiva final AV cerca s/Add OI'))
            ->add('avccaodsf', null, array('label'=>'Subjetiva final AV cerca c/Add OD'))
            ->add('avccaoisf', null, array('label'=>'Subjetiva final AV cerca c/Add OI'))
            ->add('addsf' , null, array('label'=>'Subjetiva final Add'))
            ->add('sugerencias', null, array('label'=>'Sugerencias'))
            ->add('creacion', 'date', array('label' => 'Fecha de creación', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('actualizacion', 'date', array('label' => 'Fecha de actualización', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('usuarioIdusuario.nombre', null, array('label'=>'Optometrista'))
            ->add('usuarioIdusuario.sucursalIdsucursal.nombre', null, array('label'=>'Sucursal'))
        ;
    }

    protected function configureExportFields(): array
    {
        $exportFields = array(
            'Nombre' => 'clienteIdcliente.nombre',
            'Apellido paterno' => 'clienteIdcliente.apellidopaterno',
            'Apellido materno' => 'clienteIdcliente.apellidomaterno',
            'Empresa del cliente' => 'clienteIdcliente.empresaclienteIdempresacliente.nombre',
            'Unidad' => 'clienteIdcliente.unidadIdunidad.nombre',
            'Número de empleado' => 'clienteIdcliente.numeroempleado',
            'Teléfono' => 'clienteIdcliente.telefono',
            'Correo electrónico' => 'clienteIdcliente.email',
            'Anamnesis' => 'anamnesis',
            '¿Usa lentes actualmente? (Vacio => No, 0 => No, 1 => Si)' => 'isglassesuser',
            '¿Los trae consigo? (Vacio => No, 0 => No, 1 => Si)' => 'hasglassesnow',
            'Rx ant Esfera OD' => 'gpesferaod',
            'Rx ant Esfera OI' => 'gpesferaoi',
            'Rx ant Cilindro OD' => 'gpcilindrood',
            'Rx ant Cilindro OI' => 'gpcilindrooi',
            'Rx ant Eje OD' => 'gpejeod',
            'Rx ant Eje OI' => 'gpejeoi',
            'Rx ant Add' => 'gpaddod',
            'Rx ant AV lejos OD' => 'gpavlejosod',
            'Rx ant AV lejos OI' => 'gpavlejosoi',
            'Rx ant AV cerca OD' => 'gpavcercaod',
            'Rx ant AV cerca OI' => 'gpavcercaoi',
            'Tipo de Lente (Vacio => N/A, 0 => N/A, 1 => Armazón, 2 => Lente de contacto)' => 'tipolente',
            'Observaciones del armazón previo' => 'apobservaciones',
            'Tipo de lente de contacto (Vacio => N/A, 0 => N/A, 1 => Blando, 2 => Rígido)' => 'tipolentecontacto',
            'Curva base' => 'cb',
            'Diámetro' => 'diam',
            'Material' => 'materialIdmaterial.nombre',
            'Diseño' => 'disenolenteIddisenolente.nombre',
            'Tratamiento' => 'tratamientoIdtratamiento.nombre',
            'AV s/Rx Lejos OD' => 'avsrxlejosod',
            'AV s/Rx Lejos OI' => 'avsrxlejosoi',
            'AV s/Rx Cerca OD' => 'avsrxcercaod',
            'AV s/Rx Cerca OI' => 'avsrxcercaoi',
            'AV s/Rx CV OD' => 'avsrxcvod',
            'AV s/Rx CV OI' => 'avsrxcvoi',
            'Subjetiva final Esfera OD' => 'esfodsf',
            'Subjetiva final Esfera OI' => 'esfoisf',
            'Subjetiva final Cilindro OD' => 'cilodsf',
            'Subjetiva final Cilindro OI' => 'ciloisf',
            'Subjetiva final Eje OD' => 'ejeodsf',
            'Subjetiva final Eje OI' => 'ejeoisf',
            'Subjetiva final AV lejos OD' => 'avlodsf',
            'Subjetiva final AV lejos OI' => 'avloisf',
            'Subjetiva final AV cerca s/Add OD' => 'avcsaodsf',
            'Subjetiva final AV cerca s/Add OI' => 'avcsaoisf',
            'Subjetiva final AV cerca c/Add OD' => 'avccaodsf',
            'Subjetiva final AV cerca c/Add OI' => 'avccaoisf',
            'Subjetiva final Add' => 'addsf',
            'Sugerencias' => 'sugerencias',
            'Fecha de creación' => 'creacion',
            'Fecha de actualización' => 'actualizacion',
            'Optometrista' => 'usuarioIdusuario.nombre',
            'Sucursal' => 'usuarioIdusuario.sucursalIdsucursal.nombre',
            
        );

        return $exportFields;
    }


    public function getExportFormats(): array
    {
        return ['xlsx'];
    }
}
