<?php
// src/Admin/CategoryAdmin.php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\MoneyType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use App\Entity\Cliente;
use App\Entity\Empresacliente;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Sonata\AdminBundle\Form\Type\ModelListType; 
use Doctrine\ORM\EntityManagerInterface;

final class ClienteAdmin extends AbstractAdmin 
{

  private $em;
  public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, EntityManagerInterface $em)
  {
    parent::__construct($code, $class, $baseControllerName);
   
    $this->em = $em;
  }
  protected function configureFormFields(FormMapper $formMapper): void 
  {
    $formMapper 
      ->with("Cliente",['class' => 'col-md-6'])
        ->add('apellidopaterno', TextType::class, ['label' => 'Apellido paterno', 'required' => true,])
        ->add('apellidomaterno', TextType::class, ['label' => 'Apellido materno', 'required' => true,])
        ->add('nombre', TextType::class, ['label' => 'Nombre', 'required' => true,])
        ->add('numeroempleado', TextType::class, ['label' => 'Número de empleado', 'required' => false,])
        ->add('edad', IntegerType::class, ['label' => 'Edad', 'required' => false,])
        ->add('fechanacimiento', DateType::class, ['label' => 'Fecha de nacimiento', 'widget' => 'choice', 'required' => false, 'years' => range(1923, date('Y'))])
        ->add('email', TextType::class, ['label' => 'Correo Electrónico', 'required' => false,])
        ->add('telefono', TextType::class, ['label' => 'Teléfono', 'required' => true,])
        ->add('ocupacion', TextType::class, ['label' => 'Ocupación', 'required' => false,])
        ->add('ocupacion', TextType::class, ['label' => 'Ocupación', 'required' => true,])
        ->add('diascredito', ChoiceType::class, [
          'label' => 'Dias de crédito',
          'choices' => [
            '15' => 15,
            '30' => 30,
            '60' => 60,
            '90' => 90,
          ],
          'required' => false
        ])
      ->add('authorizedcredit', MoneyType::class, ['label' => 'Crédito autorizado', 'required' => true, 'currency' => 'MXN'])
      ->end()  
      ->with(" ",['class' => 'col-md-6'])
      ->add('genero', ChoiceType::class, [
        'label' => 'Genero',
        'required' => false,
        'choices' => [
            'Masculino' => 'masculino',
            'Femenino' => 'femenino',
            'Otro' => 'otro',
        ],
        'placeholder' => 'Seleccione un género'
    ])
    ->add('alcaldia', ChoiceType::class, [
      'label' => 'Alcaldía',
      'required' => true,
      'choices' => [
          'NA' => 'NA',
          'Azcápotzalco' => 'azcapotzalco',
          'Benito Juárez' => 'benito_juarez',
          'Coyoacán' => 'coyoacan',
          'Cuajimalpa de Morelos' => 'cuajimalpa',
          'Cuauhtémoc' => 'cuauhtemoc',
          'Gustavo A. Madero' => 'gustavo_a_madero',
          'Iztacalco' => 'iztacalco',
          'Iztapalapa' => 'iztapalapa',
          'Magdalena Contreras' => 'magdalena_contreras',
          'Miguel Hidalgo' => 'miguel_hidalgo',
          'Milpa Alta' => 'milpa_alta',
          'Tláhuac' => 'tlahuac',
          'Tlalpan' => 'tlalpan',
          'Venustiano Carranza' => 'venustiano_carranza',
          'Xochimilco' => 'xochimilco',
          'Álvaro Obregón' => 'alvaro_obregon'
      ],
      'placeholder' => 'Seleccione una alcaldía'
  ])
          ->add('tipocalle', TextType::class, ['label' => 'Tipo de calle', 'required' => false,])
        ->add('calle', TextType::class, ['label' => 'Calle', 'required' => false,])
        ->add('numero', TextType::class, ['label' => 'Número', 'required' => false,])
        ->add('codigopostal', TextType::class, ['label' => 'Código postal', 'required' => true,])
        ->add('colonia', TextType::class, ['label' => 'Colonia', 'required' => false,])
        ->add('entidadfederativa', TextType::class, ['label' => 'Entidad federativa', 'required' => true,])
        ->add('localidad', TextType::class, ['label' => 'Localidad', 'required' => true,])
        ->add('empresaclienteIdempresacliente', ModelListType::class,['required' => false, 'btn_list' => true, 'label'=>"Empresa",],['delete'])
      ->end();
  }

  protected function configureDatagridFilters(DatagridMapper $datagridMapper): void 
  {
    $datagridMapper
      ->add('apellidopaterno', null, ['label' => 'Apellido paterno','show_filter' => true,])
      ->add('apellidomaterno', null, ['label' => 'Apellido materno','show_filter' => true,])
      ->add('nombre', null, ['label' => 'Nombre','show_filter' => true,])
      ->add('numeroempleado', null, ['label' => 'Número de empleado',])
      ->add('email', null, ['label' => 'Correo electrónico',])
      ->add('telefono', null, ['label' => 'Telefono',])
    ;
  }

  protected function configureListFields(ListMapper $listMapper): void 
  {
    $listMapper
      ->addIdentifier('apellidopaterno', null, ['label' => 'Apellido paterno',])
      ->add('apellidomaterno', null, ['label' => 'Apellido materno',])
      ->add('nombre', null, ['label' => 'Nombre',])
      ->add('numeroempleado', null, ['label' => 'Número de empleado',])
      ->add('edad', null, ['label' => 'Edad',])
      ->add('fechanacimiento', null, ['label' => 'Fecha de nacimiento', 'format' => 'd-m-Y', 'timezone' => 'America/Mexico_City',])
      ->add('email', null, ['label' => 'Correo electrónico',])
      ->add('telefono', null, ['label' => 'Teléfono',])
      ->add('ocupacion', null, ['label' => 'Ocupación',])
      ->add('genero', null, ['label' => 'Genero',])
      ->add('alcaldia', null, ['label' => 'Alcaldia',])
      ->add('tipocalle', null, ['label' => 'Tipo de calle',])
      ->add('calle', null, ['label' => 'Calle',])
      ->add('numero', null, ['label' => 'Número',])
      ->add('codigopostal', null, ['label' => 'Código postal',])
      ->add('colonia', null, ['label' => 'Colonia',])
      ->add('entidadfederativa', null, ['label' => 'Entidad federativa',])
      ->add('localidad', null, ['label' => 'Localidad',])
      ->add('diascredito', null, ['label' => 'Dias de crédito',])
    
      ->add(ListMapper::NAME_ACTIONS, null, [ 'label' => "Opciones",
        'actions' => [
          'edit' => [],
        ]
      ])
    ;
  }

  protected function configureExportFields(): array 
  {
    return array(
      'Apellido paterno' => 'apellidopaterno',
      'Apellido materno' => 'apellidomaterno',
      'Nombre' => 'nombre',
      'Número de empleado' => 'numeroempleado',
      'Edad' => 'edad',
      'Fecha de nacimiento' => 'fechanacimiento',
      'Correo electrónico' => 'email',
      'Teléfono' => 'telefono',
      'Ocupación' => 'ocupacion',
      'Genero' => 'genero',
      'Alcaldia' => 'alcaldia',
      'Tipo de calle' => 'tipocalle',
      'Calle' => 'calle',
      'Número' => 'numero',
      'Código postal' => 'codigopostal',
      'Colonia' => 'colonia',
      'Entidad federativa' => 'entidadfederativa',
      'Localidad' => 'localidad',
    
    );
  }
  
  public function getExportFormats(): array 
  {
    return ['xlsx'];
  }

  protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
  {
    $query = parent::configureQuery($query);

    $rootAlias = current($query->getRootAliases());

    $query->andWhere(
      $query->expr()->eq($rootAlias . '.status', ':status')
    );

    $query->setParameter('status', '1');

    return $query;
  }

  protected function configureBatchActions($actions): array 
  {
    if (
      $this->hasRoute('edit') && $this->hasAccess('edit') &&
      $this->hasRoute('delete') && $this->hasAccess('delete')
    ) {
      $actions['delete'] = [
        'ask_confirmation' => true
      ];
    }

    return $actions;
  }

  public function toString($object): string 
  {
    return $object instanceof Cliente
    ? $object->getNombre()
    : 'Cliente'; // shown in the breadcrumb on the create view
  }

  public function prePersist($object): void { // $object is an instance of App\Entity\Cliente as specified in services.yaml

    $ClientEnterprise = $object->getEmpresaclienteIdempresacliente();
    if (!$ClientEnterprise) {
      $ClientEnterprise = $this->em->getRepository(Empresacliente::class)->findOneBy(array('idempresacliente' => 1));
      $object->setEmpresaclienteIdempresacliente($ClientEnterprise);
    }
    
  }

  public function preUpdate($object): void { // $object is an instance of App\Entity\Cliente as specified in services.yaml
    $ClientEnterprise = $object->getEmpresaclienteIdempresacliente();
    if (!$ClientEnterprise) {
      $ClientEnterprise = $this->em->getRepository(Empresacliente::class)->findOneBy(array('idempresacliente' => 1));
      $object->setEmpresaclienteIdempresacliente($ClientEnterprise);
    }
  }

}