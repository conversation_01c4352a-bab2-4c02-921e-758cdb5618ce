<?php
// src/Admin/CategoryAdmin.php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use App\Entity\Marca;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;

final class LaboratorioAdmin extends AbstractAdmin 
{
    protected function configureDatagridFilters(DatagridMapper $datagridMapper): void 
    {
        //$datagridMapper->add('codigo');
        //$datagridMapper->add('nombre');
    }

    protected function configureListFields(ListMapper $listMapper): void 
    {
        $listMapper
            ->addIdentifier('fechaenvio')
            ->add('fecharegreso')
            ->add('fechaentregacliente')
            ->add('estado')
        ;
    }

    protected function configureFormFields(FormMapper $formMapper): void 
    {
        $formMapper
            ->with("Laboratorio",['class' => 'col-md-6'])
            ->end()
        ;
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
        $query = parent::configureQuery($query);
    
        $rootAlias = current($query->getRootAliases());
    
        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );

        $query->setParameter('status', '1');
    
        return $query;
    }

    protected function configureBatchActions($actions): array 
    {
        if (
            $this->hasRoute('edit') && $this->hasAccess('edit') &&
            $this->hasRoute('delete') && $this->hasAccess('delete')
        ) {
            $actions['delete'] = [
                'ask_confirmation' => true
            ];
        }
        return $actions;
    }

    
    public function toString($object): string 
    {
        return $object instanceof Marca
        ? "laboratorio de venta con folio".$object->ventaIdventa()->getFolio()
        : 'Laboratorio'; // shown in the breadcrumb on the create view
    }
}