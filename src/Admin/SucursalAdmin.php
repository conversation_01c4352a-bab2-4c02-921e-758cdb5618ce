<?php
// src/Admin/CategoryAdmin.php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Sonata\AdminBundle\Form\Type\ModelType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use App\Entity\Sucursal;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Sonata\DoctrineORMAdminBundle\Filter\ModelAutocompleteFilter;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Symfony\Component\Validator\Constraints\NotBlank;

final class SucursalAdmin extends AbstractAdmin 
{
    private $tokenStorage;
    private $em;
    private $empresasg;
    public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage, EntityManagerInterface $em)
{
    parent::__construct($code, $class, $baseControllerName);
    $this->tokenStorage = $tokenStorage;

    $this->em = $em;

}

public function buscarEmpresas(){

    $user=$this->tokenStorage->getToken()->getUser();

    $query = $this-> em->createQuery(
        'SELECT e.idempresa
           FROM App\Entity\Usuarioempresapermiso up
           INNER JOIN up.empresaIdempresa e
           where up.status =:status and up.usuarioIdusuario =:idusuario
           '
    )->setParameters(['status'=>"1",'idusuario'=>$user->getIdusuario()]);
    $empresas= $query->getResult();


    $this->empresasg=$empresas;

}
    protected function configureFormFields(FormMapper $formMapper): void 
    {
        $formMapper
            ->with("-",['class' => 'col-md-6'])
            ->add('codigo', TextType::class,[ 'help' => 'En mayúsculas sin acentos ni carácteres especiales'])
            ->add('empresaIdempresa', ModelListType::class, [
                'required' => true,
                'btn_list' => true,
                'label' => "Empresa a la que pertenece",
                'constraints' => [
                    new NotBlank(['message' => 'Este campo es requerido.'])
                ],
            ], ['delete'])
            ->add('nombre', TextType::class,[])
            ->add('telefono', TextType::class,['label'=>"Teléfono"])
            ->add('email', TextType::class,['label'=>"Correo electrónico"])
            ->end()
            ->with(" ",['class' => 'col-md-6'])
            ->add('direccion', TextType::class,['label'=>"Dirección"])

            ->add('codigo_postal', TextType::class, [
                'label' => 'Código Postal',
                'attr' => ['id' => 'codigo_postal']
            ])
            ->add('estado', TextType::class, [
                'attr' => ['id' => 'estado'],
            ])
            ->add('porcentajeiva', TextType::class, [
                'attr' => ['id' => 'porcentajeiva'],
            ])
            ->add('ciudad', TextType::class, [
                'attr' => ['id' => 'ciudad'],
            ])
            
            ->add('municipio_delegacion', TextType::class, [
                'label' => 'Municipio / Delegación',
                'attr' => ['id' => 'municipio_delegacion'],
            ])
            
          
            ->add('tipo',ChoiceType::class, [
                'choices'  => [
                    'Sucursal' => "sucursal",
                    'Bodega' => "bodega",
                    'Campaña' => "campaña"
                ]
            ])
           // ->add('porcentajeiva',NumberType::class, ['label'=>"Porcentaje de Iva", 'attr' => array('onkeydown'=>"validarNumero(this);",'onkeyup'=>"validarNumero(this);")])
            ->end()
        ;
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void 
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $filter
            ->add('empresaIdempresa', ModelFilter::class, [
                'label' => 'Empresas',
                'show_filter' => true,
                'field_options' => [
                    'class' => 'App\Entity\Empresa',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('e')
                            ->andWhere('e.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('empresa', $this->empresasg);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
        ;
    }

    protected function configureListFields(ListMapper $listMapper): void 
    {
        $listMapper
            ->addIdentifier('codigo')
            ->add('nombre')
            ->add('empresaIdempresa.nombre', null, ['label' => 'Empresa'])
            ->add('telefono', null,['label' =>"Teléfono"])
            ->add('email', null ,['label' =>"Correo electrónico"])
            ->add('direccion', null,['label' =>"Dirección"])
            ->add('ciudad')
            ->add('tipo')
            ->add(ListMapper::NAME_ACTIONS, null, ['label' => "Opciones",'actions' => ['edit' => [],'delete' => [],]])
        ;
    }

    public function toString($object): string 
    {
       return $object instanceof Sucursal
        ? $object->getNombre()
        : 'Sucursal'; // shown in the breadcrumb on the create view
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
        $Usuario=$this->tokenStorage->getToken()->getUser();
        $idusuario=$Usuario->getIdusuario();
        $mm = $this->getModelManager();
        $query = parent::configureQuery($query);
        $queryBuilder = $mm->getEntityManager($this->getClass())->createQueryBuilder();

        $queryBuilder->select('empresa')
            ->from('App\Entity\Usuarioempresapermiso', 'eup')
            ->innerJoin('eup.empresaIdempresa', 'empresa')
            ->innerJoin('eup.usuarioIdusuario', 'u')
            ->where(' u.idusuario='.$idusuario);


        $rootAlias = current($query->getRootAliases());
        $query->innerJoin($rootAlias.'.empresaIdempresa','e');
        
            $query->where($queryBuilder->expr()->in("e",$queryBuilder->getDQL()));



        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );
        $query->setParameter('status', '1');
    
        return $query;
    }

    protected function configureBatchActions($actions): array 
    {
      if (
            $this->hasRoute('edit') && $this->hasAccess('edit') &&
            $this->hasRoute('delete') && $this->hasAccess('delete')
        ) {
            $actions['delete'] = [
                'ask_confirmation' => true
            ];
        }
      return $actions;
    }

    protected function configureExportFields(): array 
    {
        return array(
            'Código'=>'codigo',
            'Nombre'=>'nombre',
            'Telefono'=>'telefono',
            'Correo electrónico'=>'email',
            'Dirección'=>'direccion',
            'Ciudad'=>'ciudad'
        );
    }
    
    public function getExportFormats(): array 
    {
        return ['xlsx'];
    }
}