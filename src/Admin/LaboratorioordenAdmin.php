<?php

declare(strict_types=1);

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Sonata\DoctrineORMAdminBundle\Filter\StringFilter;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Sonata\Form\Type\DateRangePickerType;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;

final class LaboratorioordenAdmin extends AbstractAdmin
{
    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('stockventaIdstockventa.ventaIdventa', ModelFilter::class, [ 'label' => "Folio de venta"])
            ->add('creacion', DateRangeFilter::class, array(
                'label'=>"Fecha de alta",
                'field_type' => DateRangePickerType::class,[
                    'dp_side_by_side'       => true,
                    'dp_use_current'        => true,
                    'dp_use_seconds'        => false,
                    'dp_collapse'           => true,
                    'dp_calendar_weeks'     => false,
                    'dp_view_mode'          => 'days',
                    'dp_min_view_mode'      => 'days',
                ]
            ))
            ->add('actualizacion', DateRangeFilter::class, array(
                'label'=>"Fecha de actualización",
                'field_type' => DateRangePickerType::class,[
                    'dp_side_by_side'       => true,
                    'dp_use_current'        => true,
                    'dp_use_seconds'        => false,
                    'dp_collapse'           => true,
                    'dp_calendar_weeks'     => false,
                    'dp_view_mode'          => 'days',
                    'dp_min_view_mode'      => 'days',
                ]
            ))
            ->add('fechaenviolaboratorio', DateRangeFilter::class, array(
                'label'=>"Fecha de envio a laboratorio",
                'field_type' => DateRangePickerType::class,[
                    'dp_side_by_side'       => true,
                    'dp_use_current'        => true,
                    'dp_use_seconds'        => false,
                    'dp_collapse'           => true,
                    'dp_calendar_weeks'     => false,
                    'dp_view_mode'          => 'days',
                    'dp_min_view_mode'      => 'days',
                ]
            ))
            ->add('fecharecibidolaboratorio', DateRangeFilter::class, array(
                'label'=>"Fecha de recibido en laboratorio",
                'field_type' => DateRangePickerType::class,[
                    'dp_side_by_side'       => true,
                    'dp_use_current'        => true,
                    'dp_use_seconds'        => false,
                    'dp_collapse'           => true,
                    'dp_calendar_weeks'     => false,
                    'dp_view_mode'          => 'days',
                    'dp_min_view_mode'      => 'days',
                ]
            ))
            ->add('trabajocorrecto', StringFilter::class, [
                'label' => 'Trabajo correcto',
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        'Si' => "Si", 
                        'No' => "No",
                    ],
                    'multiple' => false,
                ],
            ])
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('stockventaIdstockventa.ventaIdventa', null, [ 'label' => "Folio de venta"])
            ->add('creacion', null, ['label' => "Fecha de alta"])
            ->add('actualizacion', null, ['label' => "Fecha de actualización"])
            ->add('fechaenviolaboratorio', null, ['label' => "Fecha de envio"])
            ->add('fecharecibidolaboratorio', null, ['label' => "Fecha de recibido"])
            ->add('trabajocorrecto', null, ['label' => "Trabajo correcto"])
            ->add('observacionestrabajo', null, ['label' => "Observaciones"])
            ->add(ListMapper::NAME_ACTIONS, null, [
                'actions' => [
                    'edit' => [],
                ],
            ])
        ;
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->add('trabajocorrecto', ChoiceType::class, [
                'label' => 'Trabajo correcto', 
                'required'=>true, 
                'choices'  => [
                    'Si' => "Si", 
                    'No' => "No",
                ],
            ])
            ->add('observacionestrabajo', null, ['label'=>"Observaciones", 'required'=>true,])
        ;
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show
            ->add('idlaboratorioorden')
            ->add('creacion')
            ->add('actualizacion')
            ->add('status')
            ->add('fechaenviolaboratorio')
            ->add('fecharecibidolaboratorio')
            ->add('trabajocorrecto')
            ->add('observacionestrabajo')
        ;
    }

    function prePersist($object): void 
    { 
        $object
            ->setFecha(new \Datetime())
        ;
    }

    public function toString($object): string  
    {
        return $object instanceof Stock
        ? $object->getIdlaboratorioorden()
        : 'Orden de laboratorio'; // shown in the breadcrumb on the create view
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
      $query = parent::configureQuery($query);
  
      $rootAlias = current($query->getRootAliases());
  
      $query->andWhere(
        $query->expr()->eq($rootAlias . '.status', ':status')
      );
  
      $query->setParameter('status', '1');
  
      return $query;
    }
}