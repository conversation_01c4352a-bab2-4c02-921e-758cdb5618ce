<?php

declare(strict_types=1);

namespace App\Admin;

use App\Entity\Usuario;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Sonata\Form\Type\DateRangePickerType;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

final class UsuarioempresapermisoAdmin extends AbstractAdmin
{

    private $tokenStorage;
    private $em;
    private $empresasg;

    public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage, EntityManagerInterface $em)
{
    parent::__construct($code, $class, $baseControllerName);
    $this->tokenStorage = $tokenStorage;

    $this->em = $em;

}

public function buscarEmpresas(){

    $user=$this->tokenStorage->getToken()->getUser();

    $query = $this-> em->createQuery(
        'SELECT e.idempresa
           FROM App\Entity\Usuarioempresapermiso up
           INNER JOIN up.empresaIdempresa e
           where up.status =:status and up.usuarioIdusuario =:idusuario
           '
    )->setParameters(['status'=>"1",'idusuario'=>$user->getIdusuario()]);
    $empresas= $query->getResult();


    $this->empresasg=$empresas;

}
    protected function configureDatagridFilters(DatagridMapper $filter): void
    {

        $user = $this->tokenStorage->getToken()->getUser();

        $filter
            ->add('fechacreacion', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de creación",
                'field_type' => DateRangePickerType::class,[
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('usuarioIdusuario', null, ['label' => "Usuario"])
            ->add('usuarioIdusuario.email', null, ['label' => 'Correo electrónico','show_filter' => true])
            ->add('empresaIdempresa', ModelFilter::class, [
                'label' => 'Empresas',
                'show_filter' => true,
                'field_options' => [
                    'class' => 'App\Entity\Empresa',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('e')
                            ->andWhere('e.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('empresa', $this->empresasg);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('usuarioIdusuario', null, array('label' => 'Usuario'))
            ->add('usuarioIdusuario.email', null, ['label' => 'Correo electrónico'])
            ->add('empresaIdempresa', null, array('label' => 'Empresa'))
            ->add('fechacreacion', 'date', array('label' => 'Fecha de creación', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add(ListMapper::NAME_ACTIONS, null, [
                'label' => "Opciones",
                'actions' => [
                    'edit' => [],
                    'delete' => [],
                ],
            ])
        ;
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->with("", ['class' => 'col-md-6'])
                ->add('usuarioIdusuario', ModelListType::class, ['required' => true, 'btn_list' => true, 'label'=>"Usuario",], ['delete'])
            ->end()
            ->with(" ", ['class' => 'col-md-6'])
                ->add('empresaIdempresa', ModelListType::class, ['required' => true, 'btn_list' => true, 'label'=>"Empresa",], ['delete'])
            ->end()
        ;
    }

    public function toString($object): string 
    {
        return $object instanceof Usuarioempresapermiso? $object->getUsuarioIdusuario()->getNombre(): $object->getUsuarioIdusuario()->getNombre();
    }

    protected function configureExportFields(): array 
    {
        return array (
            'Usuario' => 'usuarioIdusuario',
            'Empresa' => 'empresaIdempresa',
            'Fecha de creación' => 'fechacreacion',
        );
    }

    public function getExportFormats(): array 
    {
        return ['xlsx'];
    }

    public function prePersist($object): void 
    {
        $object->setFechacreacion(new \DateTime("now"));
    }

    public function preUpdate($object): void 
    {
        $object->setFechacreacion(new \DateTime("now"));
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
      $query = parent::configureQuery($query);
  
      $rootAlias = current($query->getRootAliases());
  
      $query->andWhere(
        $query->expr()->eq($rootAlias . '.status', ':status')
      );
  
      $query->setParameter('status', '1');
  
      return $query;
    }
}
