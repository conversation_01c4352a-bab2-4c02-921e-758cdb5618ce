<?php

declare(strict_types=1);

namespace App\Admin;

use App\Service\SalesService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

final class ReglaabastecimientoAdmin extends AbstractAdmin
{
    private $tokenStorage;
    private $empresasg;
    private $em;

    public function __construct(TokenStorageInterface $tokenStorage, EntityManagerInterface $em)
    {
        $this->tokenStorage = $tokenStorage;
        $this->em = $em;
    }

    public function buscarEmpresas()
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $query = $this->em->createQuery(
            'SELECT e.idempresa
               FROM App\Entity\Usuarioempresapermiso up
               INNER JOIN up.empresaIdempresa e
               where up.status =:status and up.usuarioIdusuario =:idusuario
               '
        )->setParameters(['status' => "1", 'idusuario' => $user->getIdusuario()]);
        $empresas = $query->getResult();

        $this->empresasg = $empresas;
    }

    protected function configureDatagridFilters(DatagridMapper $datagridMapper): void
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $datagridMapper
            ->add('productoIdproducto.modelo', null, ['label' => 'Producto','show_filter' => true,])
            ->add('sucursalIdsucursal', ModelFilter::class, [
                'label' => 'Sucursal',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('s')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('s.status = :status')
                            ->andWhere('s.tipo = :tipo')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('tipo', 'sucursal')
                            ->setParameter('empresa', $this->empresasg)
                            ->orderBy('s.nombre', 'ASC')
                            ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('productoIdproducto.modelo', null, ['label' => 'Producto',])
            ->add('sucursalIdsucursal.nombre', null, ['label' => 'Sucursal',])
            ->add('cantidadminima', null, ['label' => 'Cantidad minima',])
            ->add('cantidadmaxima', null, ['label' => 'Cantidad maxima',])
            ->add(ListMapper::NAME_ACTIONS, null, [ 
                'label' => 'Opciones',
                'actions' => [
                    'edit' => [],
                    'delete' => [],
                ],
            ]);
    }
    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->add('cantidadminima', null, ['label' => 'Cantidad minima'])
            ->add('cantidadmaxima', null, ['label' => 'Cantidad maxima'])
            ->add('productoIdproducto', ModelListType::class, ['required' => true, 'btn_list' => true, 'label' => "Producto"], ['delete'])
            ->add('sucursalIdsucursal', ModelListType::class, ['required' => true, 'btn_list' => true, 'label' => "Sucursal"], ['delete'])
        ;
    }
}