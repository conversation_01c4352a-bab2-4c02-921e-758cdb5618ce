<?php
// src/Admin/CategoryAdmin.php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use App\Entity\Unidad;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Doctrine\ORM\EntityManagerInterface;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Doctrine\ORM\EntityRepository;

final class UnidadAdmin extends AbstractAdmin {

  private $tokenStorage;
  private $em;
  private $empresasg;

  public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage, EntityManagerInterface $em)
  {
      parent::__construct($code, $class, $baseControllerName);
      $this->tokenStorage = $tokenStorage;
  
      $this->em = $em;
  
  }
  
  public function buscarEmpresas(){
  
      $user=$this->tokenStorage->getToken()->getUser();
  
      $query = $this-> em->createQuery(
          'SELECT e.idempresa
             FROM App\Entity\Usuarioempresapermiso up
             INNER JOIN up.empresaIdempresa e
             where up.status =:status and up.usuarioIdusuario =:idusuario
             '
      )->setParameters(['status'=>"1",'idusuario'=>$user->getIdusuario()]);
      $empresas= $query->getResult();
  
  
      $this->empresasg=$empresas;
  
  }

  protected function configureFormFields(FormMapper $formMapper): void {
    $formMapper
      ->with("Unidad",['class' => 'col-md-6'])
      ->add('nombre', TextType::class,[])
      ->end()
      ->with(" ",['class' => 'col-md-6'])
      ->add('direccion', TextType::class,['label'=>"Dirección"])
      ->end()
      ->add('empresaclienteIdempresacliente', ModelListType::class,['required' => true, 'btn_list' => true, 'label'=>"Empresa",],['delete'])
    ;
  }

  protected function configureDatagridFilters(DatagridMapper $datagridMapper): void {
      $user=$this->tokenStorage->getToken()->getUser();

      $datagridMapper
          ->add('nombre',null, ['label' => 'Nombre', 'show_filter' => true,])
      ;
  }

  protected function configureListFields(ListMapper $listMapper): void {
      $listMapper
        ->addIdentifier('nombre')
        ->add('direccion',null,['label'=>"Dirección"])
        ->add(ListMapper::NAME_ACTIONS, null, [ 'label' => "Opciones",
                'actions' => [
                    'edit' => [],
                    'delete' => []
                ]
            ])
      ;
  }
  
  public function toString($object): string {
    return $object instanceof Unidad
      ? $object->getNombre()
      : 'Unidad'; // shown in the breadcrumb on the create view
  }

  protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface{
    $query = parent::configureQuery($query);

    $rootAlias = current($query->getRootAliases());

    $query->andWhere(
        $query->expr()->eq($rootAlias . '.status', ':status')
    );
    $query->setParameter('status', '1');

    return $query;
}

  protected function configureBatchActions($actions): array {
    if (
      $this->hasRoute('edit') && $this->hasAccess('edit') &&
      $this->hasRoute('delete') && $this->hasAccess('delete')
    ) {
      $actions['delete'] = [
        'ask_confirmation' => true
      ];
    }
    return $actions;
  }

  public function getExportFormats(): array {
    return ['xlsx'];
  } 

  protected function configureExportFields(): array {
    return array(
        'Nombre'=> 'nombre',
        'Dirección'=> 'direccion',
    );
}
}