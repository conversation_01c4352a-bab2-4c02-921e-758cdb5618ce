<?php

declare(strict_types=1);

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Sonata\Form\Type\DateRangePickerType;
use app\Entity\Clientefacturadatos;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Sonata\AdminBundle\Route\RouteCollectionInterface;

final class ClientefacturadatosAdmin extends AbstractAdmin
{
    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('razonsocial', null, ['label'=>"Razón social"])
            ->add('rfc', null, ['label'=>"RFC", 'show_filter' => true,])
            ->add('email', null, ['label'=>"Correo electronico", 'show_filter' => true,])
            ->add('regimenfiscal', null, ['label'=>"Regimen fiscal"])
            ->add('usocfdi', null, ['label'=>"CFDI"])
            ->add('clienteIdcliente.telefono', null, ['label'=>"Telefono", 'show_filter' => true,])
            ->add('clienteIdcliente.email', null, ['label'=>"Correo electrónico del cliente"])
            ->add('clienteIdcliente.nombre', null, ['label'=>"Nombre"])
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('razonsocial', null, ['label'=>"Razón social"])
            ->add('rfc', null, ['label'=>"RFC"])
            ->add('email', null, ['label'=>"Correo electrónico"])
            ->add('codigopostal', null, ['label'=>"Código Postal"])
            ->add('regimenfiscal', null, ['label'=>"Regimen fiscal"])
            ->add('usocfdi', null, ['label'=>"CFDI"])
            ->add('constanciasituacionfiscal', null, ['label'=>"Constancia de situación fiscal"])
            
            ->add(ListMapper::NAME_ACTIONS, null, [
                'actions' => [
                    'edit' => [],
                    'delete' => [],
                ],
            ])
        ;
    }

    protected function configureFormFields(FormMapper $formMapper): void
    {
        $formMapper
            ->with(" ",['class' => 'col-md-6'])
                ->add('clienteIdcliente', ModelListType::class,['label'=>"Seleccionar un cliente"], [ 'placeholder' => 'Seleccione una opción'])
                ->add('razonsocial', TextType::class, ['label'=>"Razón social"])
                ->add('rfc', TextType::class, ['label'=>"RFC"])
                ->add('email', TextType::class, ['label'=>"Correo electrónico"])
            ->end()

            ->with("-",['class' => 'col-md-6'])
                ->add('codigopostal', NumberType::class, ['label'=>"Codigo postal"])
                ->add('regimenfiscal', ChoiceType::class, ['label'=>"Regimen fiscal", 'choices'  => [
                    'General de Ley Personas Morales' => "601",
                    'Personas Morales con Fines no Lucrativos' => "603",
                    'Sueldos y salarios e ingresos asimilados a salarios.' => "605",
                    'Régimen de Actividades Empresariales y Profesionales.' => "612",
                    'Régimen de Incorporación Fiscal.' => "621",
                    'Régimen Simplificado de Confianza.' => "626",
                    
                ]])
                ->add('usocfdi', ChoiceType::class, ['label'=>"CFDI", 'choices'  => [
                    'Adquisición de mercancías' => "G01",
                    'Devoluciones, descuentos o bonificaciones' => "G02",
                    'Gastos en general' => "G03",
                    'Construcciones' => "I01",
                    'Mobiliario y equipo de oficina por inversiones' => "I02",
                    'Equipo de transporte' => "I03",
                    'Equipo de cómputo y accesorios' => "I04",
                    'Dados, troqueles, moldes, matrices y herramental' => "I05",
                    'ConvenioComunicaciones telefónicas' => "I06",
                    'Comunicaciones satelitales' => "I07",
                    'Otra maquinaria y equipo' => "I08",
                    'Honorarios médicos, dentales y gastos hospitalarios' => "D01",
                    'Gastos médicos por incapacidad o discapacidad' => "D02",
                    'Gastos funerales' => "D03",
                    'Donativos' => "D04",
                    'Intereses reales efectivamente pagados por créditos hipotecarios (casa habitación)' => "D05",
                    'Aportaciones voluntarias al SAR' => "D06",
                    'Primas por seguros de gastos médicos' => "D07",
                    'Gastos de transportación escolar' => "D08",
                    'Depósitos en cuentas para el ahorro, primas que tengan como base planes de pensiones' => "D09",
                    'Pagos por servicios educativos' => "D10",
                    'Sin efectos fiscales' => "S01",
                    'Pagos' => "CP01",
                    'Nómina' => "CN01",
                ]])
                ->add('constanciasituacionfiscal', FileType::class, array('data_class' => null, 'label'=>"Constancia de Situación Fiscal", 'required' => false))        
                ->add('tipopersona', ChoiceType::class, ['label'=>"Tipo de persona", 'choices'  => [
                    'Fisica' => "1",
                    'Moral' => "2",
                ]])
            ->end()
        ;
    }

    public function prePersist($object): void
    {
        $object->setCreacion(new \DateTime("now"));
        $object->setModificacion(new \DateTime("now"));
    }

    public function preUpdate($object): void 
    {
        //$object->setCreacion(new \DateTime("now"));

        $object->setModificacion(new \DateTime("now"));
    }

    
    public function toString($object): string 
    {
        return $object instanceof Clientefacturadatos
        ? $object->getRfc()
        : 'Cliente Facturación de datos'; // shown in the breadcrumb on the create view
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show
            ->add('idclientefacturadatos')
            ->add('razonsocial')
            ->add('rfc')
            ->add('email')
            ->add('codigopostal')
            ->add('regimenfiscal')
            ->add('usocfdi')
            ->add('constanciasituacionfiscal')
        ;
    }

    public function getExportFormats(): array {
        return ['xlsx'];
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('tags', TextType::class);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Task::class,
        ]);
    }

    /*
    public function prePersist($object): void
    {
        $object->setCreacion(new \DateTime("now"));
    }

    public function preUpdate($object): void
    {
        //$object->setCreacion(new \DateTime("now"));
    } 
    */

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
      $query = parent::configureQuery($query);
  
      $rootAlias = current($query->getRootAliases());
  
      $query->andWhere(
        $query->expr()->eq($rootAlias . '.status', ':status')
      );
  
      $query->setParameter('status', '1');
  
      return $query;
    }

    protected function configureRoutes(RouteCollectionInterface $collection): void
    {
        // Removing the export route will disable exporting entities.
        $collection->remove('create');
    }

}