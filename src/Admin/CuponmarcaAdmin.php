<?php

declare(strict_types=1);

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;

final class CuponmarcaAdmin extends AbstractAdmin 
{
    protected function configureDatagridFilters(DatagridMapper $datagridMapper): void 
    {
        $datagridMapper
            ->add('cuponIdcupon.codigo',null, array('label'=>'Cupón','show_filter' => true))
        ;
    }

    protected function configureListFields(ListMapper $listMapper): void 
    {
        $listMapper
            ->add('cuponIdcupon',   ModelListType::class,[
                'required' => true,
                'btn_list' => true,
                'label'=>"Cupón",

            ])
            ->add('marcaIdmarca',   ModelListType::class,[
                'required' => true,
                'btn_list' => true,
                'label'=>"Marca",

            ])
            ->add(ListMapper::NAME_ACTIONS, null, [ 'label' => "Opciones",
            'actions' => [
                'show' => [],
                'edit' => [],
                'delete' => []
            ]
        ])
        ;
    }

    protected function configureFormFields(FormMapper $formMapper): void 
    {
        $formMapper
            ->add('cuponIdcupon',   ModelListType::class,[
                'required' => true,
                'btn_list' => true,
                'label'=>"Cupón",

            ],['delete'])
            ->add('marcaIdmarca',   ModelListType::class,[
                'required' => true,
                'btn_list' => true,
                'label'=>"Marca",

            ],['delete'])
        ;
    }

    protected function configureShowFields(ShowMapper $showMapper): void 
    {

    }

    protected function configureExportFields(): array 
    {
        return array(
            'Cupón'=>'cuponIdcupon',
            'Marca'=> 'marcaIdmarca',
            
        );
    }

    public function getExportFormats(): array 
    {
        return ['xlsx'];
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
        $query = parent::configureQuery($query);
    
        $rootAlias = current($query->getRootAliases());
    
        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );
        $query->setParameter('status', '1');
    
        return $query;
    }

    public function toString($object): string 
    {
        return $object instanceof Cuponmarca
        ? $object->getCuponIdcupon()->getCodigo()." ". $object->getMarcaIdmarca()->getNombre()
        : 'Cuponmarca'; // shown in the breadcrumb on the create view
    }
}
