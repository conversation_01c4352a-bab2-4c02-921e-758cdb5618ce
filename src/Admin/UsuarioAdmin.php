<?php
// src/Admin/CategoryAdmin.php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\MoneyType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Validator\Constraints\File;
use Sonata\AdminBundle\Form\Type\ModelType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\RepeatedType;
use App\Entity\Usuario;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Sonata\CoreBundle\Validator\ErrorElement;
use Sonata\Form\Type\DateRangePickerType;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Sonata\CoreBundle\Form\Type\DateTimePickerType;
use Sonata\CoreBundle\Form\Type\DatePickerType;
use Sonata\Form\Type\DateRangeType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Encoder\MessageDigestPasswordEncoder;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;

use Symfony\Component\Mime\Email;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\Mime\Address;

use Symfony\Component\DependencyInjection\ContainerInterface;

final class UsuarioAdmin extends AbstractAdmin {

    private $tokenStorage;
    private $em;
    private $empresasg;

    private $mailer;

    private $container;

    public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage,EntityManagerInterface $em,MailerInterface $mailer, ContainerInterface $container)
    {
        parent::__construct($code, $class, $baseControllerName);
        $this->tokenStorage = $tokenStorage;

        $this->mailer=$mailer;

        $this->container=$container;

        $this->em = $em;
    }

    public function buscarEmpresas(){

        $user=$this->tokenStorage->getToken()->getUser();

        $query = $this-> em->createQuery(
            'SELECT e.idempresa
           FROM App\Entity\Usuarioempresapermiso up
           INNER JOIN up.empresaIdempresa e
           where up.status =:status and up.usuarioIdusuario =:idusuario
           '
        )->setParameters(['status'=>"1",'idusuario'=>$user->getIdusuario()]);
        $empresas= $query->getResult();


        $this->empresasg=$empresas;

    }

    protected function configureFormFields(FormMapper $formMapper): void {
        $subject = $this->getSubject();
        $idusuario=$subject->getIdusuario();
        /*
        var_dump($subject->getIdusuario());
        exit();
        */

        //var_dump($subject->getIdusuario());

        $formMapper
            ->with("Datos Usuario", ['class' => 'col-md-6 ', 'label' => 'Datos del usuario'])
            ->add('nombre', TextType::class, ['label' => "Nombre"])
            ->add('apellidopaterno', TextType::class, ['label' => "Apellido paterno"])
            ->add('apellidomaterno', TextType::class, ['label' => "Apellido materno"])
            ->add('puesto', TextType::class, ['label' => "Puesto"])
            ->add('sucursalIdsucursal', null, [
                'label' => 'Sucursales',
                'expanded' => false,
                'multiple' => false,
                'query_builder' => function (\Doctrine\ORM\EntityRepository $repository) {
                    return $repository->createQueryBuilder('t')
                        ->where('t.status = :status')
                        ->setParameter('status', 1)
                        ->orderBy('t.nombre', 'ASC');
                }
                ,   'required' => true
            ])
            ->end();

        $formMapper
            ->with(" ", ['class' => 'col-md-6 '])
            ->add('rol', ChoiceType::class, [
                'choices' => [
                    'Vendedor' => "ROLE_VENDEDOR",
                    'Laboratorio' => 'ROLE_LAB',
                    'Calidad' => 'ROLE_CALIDAD',
                    'Mensajero' => "ROLE_MENSAJERO",
                    'Administrador' => "ROLE_ADMIN",
                    'Super Administrador' => "ROLE_SUPER_ADMIN",
                ]
            ])
            ->add('email', EmailType::class, [
                'label' => "Correo electrónico",
                'attr' => ['autocomplete'=>'off'],
            ])
            ->add('file', FileType::class, [
                'label' => 'Foto de perfil',
                'required' => false,
                'constraints' => [
                    new File([
                        'maxSize' => '1000000',
                        'mimeTypes' => [
                            'image/jpeg',
                            'image/png',

                        ],
                        'mimeTypesMessage' => 'Sube un archivo válido',
                    ])
                ],
            ])
            ->end();

        if (!$idusuario){

            $formMapper
                ->with(" ", ['class' => 'col-md-6 '])
                ->add('contrasena', RepeatedType::class, [
                    'type' => PasswordType::class,
                    'invalid_message' => 'Los campos de contraseña deben ser iguales',
                    'options' => ['attr' => ['class' => 'password-field', 'autocomplete'=>'off']],
                    'required' => true,
                    'first_options'  => ['label' => 'Contraseña'],
                    'second_options' => ['label' => 'Repita la contraseña'],
                ])
                ->end();
        }

    }
    protected function configureDatagridFilters(DatagridMapper $datagridMapper): void {
        $user = $this->tokenStorage->getToken()->getUser();

        $datagridMapper
            ->add('apellidopaterno', null, ['label' => 'Apellido paterno'])
            ->add('apellidomaterno', null, ['label' => 'Apellido materno',])
            ->add('nombre', null, ['label' => 'Nombre',])
            ->add('puesto', null, ['label' => 'Puesto',])
            ->add('email', null, ['label' => 'Correo electrónico','show_filter' => true])
            ->add('sucursalIdsucursal', ModelFilter::class, [
                'label' => 'Sucursales',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        return $repository->createQueryBuilder('s')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('s.status = :status')
                            ->andWhere('s.tipo = :tipo')
                            ->andWhere('e = :empresa')
                            ->setParameter('status', '1')
                            ->setParameter('tipo', 'sucursal')
                            ->setParameter('empresa', $user->getSucursalIdsucursal()->getEmpresaIdempresa())
                            ->orderBy('s.nombre', 'ASC');
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
            ->add('campana', ModelFilter::class, [
                'label' => 'Campañas',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        return $repository->createQueryBuilder('s')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('s.status = :status')
                            ->andWhere('s.tipo = :tipo')
                            ->andWhere('e = :empresa')
                            ->setParameter('status', '1')
                            ->setParameter('tipo', 'campaña')
                            ->setParameter('empresa', $user->getSucursalIdsucursal()->getEmpresaIdempresa())
                            ->orderBy('s.nombre', 'ASC');
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
            ->add('bodega', ModelFilter::class, [
                'label' => 'Bodegas',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        return $repository->createQueryBuilder('s')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('s.status = :status')
                            ->andWhere('s.tipo = :tipo')
                            ->andWhere('e = :empresa')
                            ->setParameter('status', '1')
                            ->setParameter('tipo', 'bodega')
                            ->setParameter('empresa', $user->getSucursalIdsucursal()->getEmpresaIdempresa())
                            ->orderBy('s.nombre', 'ASC');
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
            ->add('empresaIdempresa', ModelFilter::class, [
                'label' => 'Empresas',
                'field_options' => [
                    'class' => 'App\Entity\Empresa',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('e')
                            ->andWhere('e.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('empresa', $this->empresasg);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
        ;
    }

    protected function configureListFields(ListMapper $listMapper): void {
        $listMapper
            ->addIdentifier('nombre',null,array('label'=>'Nombre'))
        ;
        $listMapper
            ->add('apellidopaterno',null,array('label'=>'Apellido paterno'))
        ;
        $listMapper
            ->add('email',null,array('label'=>'Correo electrónico'))
        ;
        $listMapper
            ->add('puesto', null, array('label' => 'Puesto'))
            ->add('sucursalIdsucursal.nombre', null, array('label' => 'Sucursal'));
        $listMapper
            ->add('rol', 'choice', array(
                    'label'=>'Rol',
                    'choices' => [
                        'ROLE_VENDEDOR' => "Vendedor",
                        'ROLE_LAB' => 'Laboratorio',
                        'ROLE_CALIDAD' => 'Calidad',
                        'ROLE_MENSAJERO' => "Mensajero",
                        'ROLE_ADMIN' => "Administrador",
                        'ROLE_SUPER_ADMIN' => "Súper Administrador",
                    ]
                )
            )
        ;
        //  $listMapper->add('costo',null,array('label'=>'Costo'));
        $listMapper
            ->add(ListMapper::NAME_ACTIONS, null, [ 'label' => "Opciones",
                'actions' => [
                    'edit' => [],
                    'delete' => [],
                    'cambiarContrasena' => ['template' => 'CRUD/list__action_cambiarContrasena.html.twig',],
                ]
            ])
        ;
    }

    public function toString($object): string {
        return $object instanceof Usuario
            ? $object->getNombre()." ".$object->getApellidopaterno()." ".$object->getApellidomaterno()
            : 'Usuario'; // shown in the breadcrumb on the create view
    }

    protected function configureBatchActions($actions): array {
        if (
            $this->hasRoute('edit') && $this->hasAccess('edit') &&
            $this->hasRoute('delete') && $this->hasAccess('delete')
        ) {
            $actions['delete'] = [
                'ask_confirmation' => true
            ];
        }
        return $actions;
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface{

        $Usuario=$this->tokenStorage->getToken()->getUser();
        $idusuario=$Usuario->getIdusuario();
        $mm = $this->getModelManager();
        $query = parent::configureQuery($query);
        $queryBuilder = $mm->getEntityManager($this->getClass())->createQueryBuilder();

        // Realiza una consulta para obtener la empresa del usuario autenticado.
        $queryBuilder->select('empresa')
            ->from('App\Entity\Usuarioempresapermiso', 'eup')
            ->innerJoin('eup.empresaIdempresa', 'empresa')
            ->innerJoin('eup.usuarioIdusuario', 'u')
            ->where('u.idusuario='.$idusuario);

        $rootAlias = current($query->getRootAliases());

        // Se une a la empresa a través de la relación de categoría, clase y empresa.
        $query->innerJoin($rootAlias.'.sucursalIdsucursal', 'su')
            ->innerJoin('su.empresaIdempresa', 'e');

        // Añade una restricción para que solo se muestren los productos relacionados con la empresa del usuario autenticado.
        $query->where($queryBuilder->expr()->in("e", $queryBuilder->getDQL()));

        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );
        $query->setParameter('status', '1');


        $query = parent::configureQuery($query);

        $rootAlias = current($query->getRootAliases());

        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );
        $query->setParameter('status', '1');



        return $query;
    }

    protected function configureRoutes(RouteCollectionInterface $collection): void {
        $collection
            ->add('cambiarContrasena', $this->getRouterIdParameter().'/cambiarContrasena')
        ;
    }

    public function prePersist($object): void { // $object is an instance of App\Entity\User as specified in services.yaml
        //$contrasenaNueva= $this->getForm()->get('contrasena2')->getData();

        //$container = $this->getConfigurationPool()->getContainer();
        $idusuario=$object->getIdusuario();

        if (!$idusuario){
            //$this->sendEmail($object);

            $encoder = $this->container->get('security.password_encoder');
            $encoded = $encoder->encodePassword($object, $object->getContrasena());
            $object->setContrasena($encoded);

        }
        $this->manageFileUpload($object);
        //$object->setContrasena("");
    }

    public function preUpdate($object): void { // $object is an instance of App\Entity\User as specified in services.yaml
        /* $contrasenaNueva= $this->getForm()->get('contrasena2')->getData();
         if($contrasenaNueva != null){
           $container = $this->getConfigurationPool()->getContainer();
           $encoder = $container->get('security.password_encoder');
           $encoded = $encoder->encodePassword($object, $contrasenaNueva);
           $object->setContrasena($encoded);
         }*/
        $idusuario=$object->getIdusuario();

        if (!$idusuario){
            //$this->sendEmail($object);

            $encoder = $this->container->get('security.password_encoder');
            $encoded = $encoder->encodePassword($object, $object->getContrasena());
            $object->setContrasena($encoded);
        }
        $this->manageFileUpload($object);
    }

    private function manageFileUpload(object $user): void
    {
        if ($user->getFile()) $user->upload();
    }

    public function deleteAction(Request $request): Response {
        $object = $this->assertObjectExists($request, true);
        \assert(null !== $object);

        $this->checkParentChildAssociation($request, $object);

        $this->admin->checkAccess('delete', $object);

        $preResponse = $this->preDelete($request, $object);
        if (null !== $preResponse) {
            return $preResponse;
        }

        if (\in_array($request->getMethod(), [Request::METHOD_POST, Request::METHOD_DELETE], true)) {
            // check the csrf token
            $this->validateCsrfToken($request, 'sonata.delete');

            $objectName = $this->admin->toString($object);

            try {
                $object->setStatus("0");
                $em=$this->getDoctrine()->getManager();
                $em->persist($object);
                $em->flush();
                //$this->admin->delete($object);

                if ($this->isXmlHttpRequest($request)) {
                    return $this->renderJson(['result' => 'ok']);
                }

                $this->addFlash(
                    'sonata_flash_success',
                    $this->trans(
                        'flash_delete_success',
                        ['%name%' => $this->escapeHtml($objectName)],
                        'SonataAdminBundle'
                    )
                );
            } catch (ModelManagerException $e) {
                // NEXT_MAJOR: Remove this catch.
                $this->handleModelManagerException($e);

                if ($this->isXmlHttpRequest($request)) {
                    return $this->renderJson(['result' => 'error']);
                }

                $this->addFlash(
                    'sonata_flash_error',
                    $this->trans(
                        'flash_delete_error',
                        ['%name%' => $this->escapeHtml($objectName)],
                        'SonataAdminBundle'
                    )
                );
            } catch (ModelManagerThrowable $e) {
                $errorMessage = $this->handleModelManagerThrowable($e);

                if ($this->isXmlHttpRequest($request)) {
                    return $this->renderJson(['result' => 'error'], Response::HTTP_OK, []);
                }

                $this->addFlash(
                    'sonata_flash_error',
                    $errorMessage ?? $this->trans(
                    'flash_delete_error',
                    ['%name%' => $this->escapeHtml($objectName)],
                    'SonataAdminBundle'
                )
                );
            }

            return $this->redirectTo($request, $object);
        }

        $template = $this->getTemplateRegistry()->getTemplate('delete');

        return $this->renderWithExtraParams($template, [
            'object' => $object,
            'action' => 'delete',
            'csrf_token' => $this->getCsrfToken('sonata.delete'),
        ]);
    }

    protected function configureExportFields(): array {
        return array(
            'Nombre'=> 'nombre',
            'Apellido Paterno'=>'apellidopaterno',
            'Email'=>'email',
            'Sucursal'=>'sucursalIdsucursal.nombre',
            'Rol'=>'rol'
        );
    }

    public function getExportFormats(): array {
        return ['xlsx'];
    }

    private function sendEmail($object)
    {
        //$email = $object->getEmail();
        $password = $object->getPassword();
        $name = $object->getNombreVendedor();
        $User = $this->tokenStorage->getToken()->getUser();
        $fullName = $User->getNombreVendedor();

        //$this->container

        $websiteUrl = "sistema.grupooptimo.com.mx";

        $Enterprise = $object->getSucursalIdsucursal()->getEmpresaIdempresa();
        $logo = $Enterprise->getLogoimagen();

        $email = (new TemplatedEmail())
            ->from('<EMAIL>')
            ->to(new Address($object->getEmail()))
            ->subject("Datos para accesar al sistema ")
            ->htmlTemplate('emails/emailLoginInformation.html.twig')
            ->context([
                'emailLoginInformation' => $object->getEmail(),
                'password' => $password,
                'websiteUrl' => $websiteUrl,
                'logo' => $logo,
                'name'=>$name,
                'fullName'=>$fullName,

            ]);

        $this->mailer->send($email);
    }

}