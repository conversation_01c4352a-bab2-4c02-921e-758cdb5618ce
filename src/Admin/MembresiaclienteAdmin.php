<?php
// src/Admin/CategoryAdmin.php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\MoneyType;
use Sonata\AdminBundle\Form\Type\ModelType;
use App\Entity\Sucursal;
use App\Entity\Cliente;
use App\Entity\Membresia;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;

final class MembresiaclienteAdmin extends AbstractAdmin 
{
  protected function configureDatagridFilters(DatagridMapper $datagridMapper): void 
  {
    /*  
    $datagridMapper->add('codigo')
      ->add('nombre')
      ->add('telefono')
      ->add('email')
      ->add('direccion')
      ->add('ciudad')
    ;
    */
  }

  protected function configureListFields(ListMapper $listMapper): void 
  {
    $listMapper
      ->addIdentifier('membresiamembresia')
      ->add('descuento')
      ->add('montodescuento')
      ->add('clientecliente')
    ;
  }

  protected function configureFormFields(FormMapper $formMapper) : void 
  {
    $queryBuilderClientes = $this->getModelManager()
      ->getEntityManager(Cliente::class)
      ->createQueryBuilder('c')
      ->select('c')
      ->from('App:Cliente', 'c')
      ->where('c.status =1')
      ->orderBy('c.nombre', 'ASC')
    ;

    $queryBuilderMembresia = $this->getModelManager()
      ->getEntityManager(Membresia::class)
      ->createQueryBuilder('m')
      ->select('m')
      ->from('App:Membresia', 'm')
      ->where('m.status =1')
      ->orderBy('m.nombre', 'ASC')
    ;

    $formMapper
      ->with("Promoción",['class' => 'col-md-6'])
        ->add('descuento', NumberType::class,[
          'grouping'=>true,
          'label' => '% Descuento',
          'attr' => array(
            'onkeydown'=>"validarNumero(this);",
            'onkeyup'=>"validarNumero(this);"
          )
        ])
        ->add('montodescuento', MoneyType::class,[
          'label' => 'Descuento (pesos)',
          'currency'=>"MXN",
          'grouping'=>true,

          'attr' => array(
            'class'=>'form-control',
              'onkeydown' => "validarNumero(this);",
              'onkeyup' => "validarNumero(this);"
          )
        ])
      ->end()

      ->with(" ",['class' => 'col-md-6'])
        ->add('clientecliente', ModelType::class,[
          'class' => Cliente::class,
          'label' => 'Cliente',
          'required'=>true,
          'invalid_message' => 'error!',
          'property' => 'nombre',
          'query' => $queryBuilderClientes
        ])
        ->add('membresiamembresia', ModelType::class,[
          'class' => Membresia::class,
          'required'=>true,
          'label' => 'Membresia',
          'invalid_message' => 'error!',
          'property' => 'nombre',
          'query' => $queryBuilderMembresia
        ])
      ->end()
    ;
  }

  protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
  {
    $query = parent::configureQuery($query);

    $rootAlias = current($query->getRootAliases());

    $query->andWhere(
        $query->expr()->eq($rootAlias . '.status', ':status')
    );

    $query->setParameter('status', '1');

    return $query;
  }

  protected function configureBatchActions($actions): array 
  {
    if (
      $this->hasRoute('edit') && $this->hasAccess('edit') &&
      $this->hasRoute('delete') && $this->hasAccess('delete')
    ) {
        $actions['delete'] = [
          'ask_confirmation' => true
        ];
      }
    return $actions;
  }

  public function toString($object): string 
  {
    return $object instanceof Sucursal
    ? $object->getNombre()
    : 'Membresia'; // shown in the breadcrumb on the create view
  }

}