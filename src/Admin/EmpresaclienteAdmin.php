<?php

declare(strict_types=1);

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;

final class EmpresaclienteAdmin extends AbstractAdmin
{

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('nombre',null, ['label' => 'Empresa', 'show_filter' => true,])
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('nombre')
            ->add(ListMapper::NAME_ACTIONS, null, ['label' => "Opciones",
                'actions' => [
                    'show' => [],
                    'edit' => [],
                    'delete' => [],
                ],
            ])
        ;
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->add('nombre')
        ;
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show
            ->add('idempresacliente')
            ->add('nombre')
            ->add('status')
        ;
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
      $query = parent::configureQuery($query);
  
      $rootAlias = current($query->getRootAliases());
  
      $query->andWhere(
        $query->expr()->eq($rootAlias . '.status', ':status')
      );
  
      $query->setParameter('status', '1');
  
      return $query;
    }
}
