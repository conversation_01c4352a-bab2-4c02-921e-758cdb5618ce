<?php
// src/Admin/CategoryAdmin.php
namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\BirthdayType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Sonata\AdminBundle\Form\Type\ModelType;
use Sonata\AdminBundle\Form\Type\ModelAutocompleteType;
use App\Entity\Benficiario;
use App\Entity\Cliente;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;

final class BeneficiarioAdmin extends AbstractAdmin 
{
  protected function configureDatagridFilters(DatagridMapper $datagridMapper): void 
  {
    $datagridMapper
      ->add('nombre', null, ['label'=>"Nombre del Beneficiario"])
      ->add('clienteIdcliente.nombre', null, ['label'=>"Nombre del titular"])
      ->add('clienteIdcliente.apellidopaterno', null, ['label'=>"Apellido paterno del titular"])
      ->add('clienteIdcliente.apellidomaterno', null, ['label'=>"Apellido materno del titular"])
      ->add('clienteIdcliente.telefono', null, ['label'=>"Teléfono del titular"])
      ->add('clienteIdcliente.numeroempleado', null, ['label'=>"Número de empleado"])
    ;
  }

  protected function configureListFields(ListMapper $listMapper): void 
  {
    $listMapper
      ->addIdentifier('nombre',null,['label'=>"Beneficiario"])
      ->add('fechanacimiento',null,['label'=>"Fecha de nacimiento"])
      ->add('tipo',null,['label'=>"Relación familiar"])
      ->add('sexo')
      ->add('clienteIdcliente.nombre',null,['label'=>"Cliente"])
      ->add('clienteIdcliente.numeroempleado',null,['label'=>"Número de empleado"])
      ->add(ListMapper::NAME_ACTIONS, null, [ 
        'label' => "Opciones",
        'actions' => [
          'edit' => [],
          'delete' => []
        ]
      ])
    ;
  }

  protected function configureFormFields(FormMapper $formMapper): void 
  {
    $queryBuilderTitular = $this->getModelManager()
      ->getEntityManager(Cliente::class)
      ->createQueryBuilder('c')
      ->select('c')
      ->from('App:Cliente', 'c')
      ->where('c.status =1')
      ->setMaxResults(15)
    ;

    $formMapper
      ->with("Beneficiario",['class' => 'col-md-6'])
        ->add('clienteIdcliente',ModelListType::class,[
          'required' => true,
          'btn_list' => true,
          'label'=>"Cliente",
        ])
        ->add('nombre', TextType::class,['label' => 'Nombre del beneficiario',])
        ->add('fechanacimiento', BirthdayType::class,[
          'label' => 'Fecha de nacimiento',
          'required'=>false,
          'widget' => 'single_text',
        ])
      ->end()

      ->with(" ",['class' => 'col-md-6'])
        ->add('tipo', ChoiceType::class,[
          'label' => 'Relación familiar',
          'required'=>false,
          'choices'  => [
            '' => null,
            'Conyuge' => "CONYUGE",
            'Padres' => "PADRES",
            'Hijo' => "HIJO",
            'Concubina' => "CONCUBINA"
          ],
        ])
        ->add('sexo', ChoiceType::class,[
          'label' => 'Sexo',
          'required'=>false,
          'choices'  => [
            '' => null,
            'Femenino' => "F",
            'Masculino' => "M",
          ],
        ])
      ->end()
    ;
  }

  protected function configureExportFields(): array 
  {
    return array(
      'Beneficiario'=> 'nombre',
      'Fecha de nacimiento'=> 'fechanacimiento',
      'Relación familiar'=> 'tipo',
      'Sexo'=> 'sexo',
      'Cliente'=> 'clienteIdcliente.nombre',
    );
  }

  public function getExportFormats(): array 
  {
    return ['xlsx'];
  }

  protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
  {
    $query = parent::configureQuery($query);

    $rootAlias = current($query->getRootAliases());

    $query->andWhere(
      $query->expr()->eq($rootAlias . '.status', ':status')
    );

    $query->setParameter('status', '1');

    return $query;
  }

  protected function configureBatchActions($actions): array { 
    if (
      $this->hasRoute('edit') && $this->hasAccess('edit') &&
      $this->hasRoute('delete') && $this->hasAccess('delete')
    ) {
        $actions['delete'] = [
          'ask_confirmation' => true
        ];
      }
    return $actions;
  }
  
  public function toString($object): string 
  {
    return $object instanceof Beneficiario
    ? $object->getNombre()
    : 'Beneficiario'; // shown in the breadcrumb on the create view
  }
}
