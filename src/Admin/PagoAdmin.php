<?php
// src/Admin/CategoryAdmin.php

namespace App\Admin;

use App\Entity\Pago;
use App\Entity\Venta;
use Doctrine\ORM\EntityManagerInterface;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Form\Type\ModelType;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Sonata\DoctrineORMAdminBundle\Filter\StringFilter;
use Sonata\Form\Type\DateRangePickerType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\MoneyType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use App\Entity\Categoria;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Doctrine\ORM\EntityRepository;
use App\Service\PagoEraser;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;


final class PagoAdmin extends AbstractAdmin {
    private $tokenStorage;
    private $pagoEraser;
    private $em;
    private $empresasg;

    public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage, PagoEraser $pagoEraser,EntityManagerInterface $em) { 
      parent::__construct($code, $class, $baseControllerName);
      $this->tokenStorage = $tokenStorage;
      $this->pagoEraser = $pagoEraser;
      $this->em = $em;
    }

    public function buscarEmpresas(){
        $user=$this->tokenStorage->getToken()->getUser();

        $query = $this-> em->createQuery(
            'SELECT e.idempresa
            FROM App\Entity\Usuarioempresapermiso up
            INNER JOIN up.empresaIdempresa e
            where up.status =:status and up.usuarioIdusuario =:idusuario
            '
        )->setParameters(['status'=>"1",'idusuario'=>$user->getIdusuario()]);
        $empresas= $query->getResult();

        $this->empresasg=$empresas;
    }

    protected function configureDatagridFilters(DatagridMapper $datagridMapper): void {
        $user = $this->tokenStorage->getToken()->getUser();

        $datagridMapper
            ->add('ventaIdventa.sucursalIdsucursal.empresaIdempresa', ModelFilter::class, [
                'label' => 'Empresas',
                'field_options' => [
                    'class' => 'App\Entity\Empresa',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('e')
                            ->andWhere('e.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('empresa', $this->empresasg)
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
            ->add('ventaIdventa.sucursalIdsucursal', ModelFilter::class, [
                'label' => 'Sucursales',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('s')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('s.status = :status')
                            ->andWhere('s.tipo = :tipo')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('tipo', 'sucursal')
                            ->setParameter('empresa', $this->empresasg)
                            ->orderBy('s.nombre', 'ASC')
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])           
            ->add('ventaIdventa.campana', ModelFilter::class, [
                'label' => 'Campañas',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('s')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('s.status = :status')
                            ->andWhere('s.tipo = :tipo')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('tipo', 'campaña')
                            ->setParameter('empresa', $this->empresasg)
                            ->orderBy('s.nombre', 'ASC')
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
            ->add('ventaIdventa.bodega', ModelFilter::class, [
                'label' => 'Bodegas',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('s')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('s.status = :status')
                            ->andWhere('s.tipo = :tipo')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('tipo', 'bodega')
                            ->setParameter('empresa', $this->empresasg)
                            ->orderBy('s.nombre', 'ASC')
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
            ->add('fecha',DateRangeFilter::class, array(
                'show_filter' => true,
                'label'=>"Fecha de pago",
                'field_type' => DateRangePickerType::class,[
                    'dp_side_by_side'       => true,
                    'dp_use_current'        => true,
                    'dp_use_seconds'        => false,
                    'dp_collapse'           => true,
                    'dp_calendar_weeks'     => false,
                    'dp_view_mode'          => 'days',
                    'dp_min_view_mode'      => 'days',
                ]
            ))
            ->add('ventaIdventa.fecha',DateRangeFilter::class, array(
                'label'=>"Fecha de venta",
                'field_type' => DateRangePickerType::class,[
                    'dp_side_by_side'       => true,
                    'dp_use_current'        => true,
                    'dp_use_seconds'        => false,
                    'dp_collapse'           => true,
                    'dp_calendar_weeks'     => false,
                    'dp_view_mode'          => 'days',
                    'dp_min_view_mode'      => 'days',
                ]
            ))
            ->add('paymenttypeIdpaymenttype', ModelFilter::class, [
                'label' => 'Tipo de pago',
                'field_options' => [
                    'class' => 'App\Entity\Paymenttype',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('pt')
                            ->andWhere('pt.status >= :status')
                            ->setParameter('status', '1')
                            ->orderBy('pt.name', 'ASC')
                        ;
                    },
                    'multiple' => true,
                    'expanded' => false,
                ],
            ])
            ->add('ventaIdventa.tipoventaIdtipoventa', ModelFilter::class, [
                'label' => "Tipo de venta",
                'field_options' =>[
                    'class'=>'App\Entity\Tipoventa',
                    'query_builder'=> function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('tv')
                            ->join('tv.empresaIdempresa','e')
                            ->andWhere('tv.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('empresa',$this->empresasg)
                            ->setParameter('status','1')
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
            ->add('ventaIdventa.folio', null, array('label'=>'Folio de ticket','show_filter' => true))
        ;
    }

    protected function configureListFields(ListMapper $listMapper): void {
        $listMapper
            ->add('ventaIdventa.sucursalIdsucursal.empresaIdempresa', null, ['label' => "Empresa"])
            ->add('ventaIdventa.folio', null, ['label' => "Folio"])
            ->add('fecha', null, [
                'label' => 'Fecha de pago',
                'format' => 'g:ia d/m/Y',
            ])
            ->add('monto', null, ['label' => "Monto"])
            ->add('justification', null, ['label' => "Justificación"])
            ->add('paymenttypeIdpaymenttype.name', null, ['label'=>"Tipo de pago"])
            ->add('mesesintereses', null, ['label'=>"Meses sin intereses"])
            ->add('ventaIdventa.tipoventaIdtipoventa.nombre', null, ['label'=>"Tipo de venta"])
            ->add('ventaIdventa.sucursalIdsucursal', null, ['label'=>"Sucursal"])
            ->add('ventaIdventa.sucursalIdsucursal.ciudad', null, ['label' => "Estado"])
            ->add(ListMapper::NAME_ACTIONS, null, [
                'label' => "Opciones",
                'actions' => [
                    'delete' => [],
                    'cambiarPago' => ['template' => 'CRUD/list-action-cambio-pago.html.twig'],
                ]
                ]);
            }




    protected function configureFormFields(FormMapper $formMapper): void {
        $queryBuilder = $this->getModelManager()
            ->getEntityManager(Venta::class)
            ->createQueryBuilder('c')
            ->select('c')
            ->from('App:Venta', 'c')
            ->where('c.status =1')
            ->orderBy('c.folio', 'ASC')
        ;
        $formMapper
            ->with(" ",['class' => 'col-md-6'])
                ->add('tipopago',ChoiceType::class, [
                    'label'=>'Tipo de pago',
                    'choices'  => [
                        'Efectivo' => "efectivo",
                        'Tarjeta' => "tarjeta",
                        'Transferencia' => "transferencia",
                        'Convenio' => "convenio",
                        'Vales' => "vales",
                    ]
                ])
                ->add('validado', ChoiceType::class,[
                    'label' => 'Estado',
                    'required'=>true,
                    'choices'  => [
                        'Pendiente' => "0",
                        'Validado' => "1",
                    ],
                ])
            ->end()
        ;
    }

    protected function configureExportFields(): array {
        return array(
            'Folio' => 'ventaIdventa.folio',
            'Fecha de pago' => 'fecha',
            'Monto' => 'monto',
            'Tipo de pago' => 'paymenttypeIdpaymenttype.name',
            'Tipo de venta' => 'ventaIdventa.tipoventaIdtipoventa.nombre',
            'Sucursal' => 'ventaIdventa.sucursalIdsucursal.nombre',
            'Estado' => 'ventaIdventa.sucursalIdsucursal.ciudad',
            'Estado 0 =  Pendiente / 1 = Validado' => 'validado',
            'Meses sin intereses' => 'mesesintereses'
        );
    }

    public function getExportFormats(): array {
        return ['xlsx'];
    }

    protected function configureRoutes(RouteCollectionInterface $collection): void {
        $collection
            ->add('cambiarPago', $this->getRouterIdParameter() . '/cambiarPago');

        $collection->remove('create');
    }
    
    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface {
        $Usuario=$this->tokenStorage->getToken()->getUser();
        $idusuario=$Usuario->getIdusuario();
        $mm = $this->getModelManager();
        $query = parent::configureQuery($query);
        $queryBuilder = $mm->getEntityManager($this->getClass())->createQueryBuilder();

        // Realiza una consulta para obtener la empresa del usuario autenticado.
        $queryBuilder->select('empresa')
            ->from('App\Entity\Usuarioempresapermiso', 'eup')
            ->innerJoin('eup.empresaIdempresa', 'empresa')
            ->innerJoin('eup.usuarioIdusuario', 'u')
            ->where('u.idusuario='.$idusuario);
    
        $rootAlias = current($query->getRootAliases());
    
        // Se une a la empresa a través de la relación de categoría, clase y empresa.
        $query->innerJoin($rootAlias.'.ventaIdventa', 've')
              ->innerJoin('ve.sucursalIdsucursal', 'su')
              ->innerJoin('su.empresaIdempresa', 'e');
    
        // Añade una restricción para que solo se muestren los productos relacionados con la empresa del usuario autenticado.
        $query->where($queryBuilder->expr()->in("e", $queryBuilder->getDQL()));
        
        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );

        $query->setParameter('status', '1'); 

        $query = parent::configureQuery($query);
    
        $rootAlias = current($query->getRootAliases());
    
        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );

        $query->setParameter('status', '1');

        $query->addOrderBy($rootAlias . '.fecha', 'DESC');
    
        return $query;
    }

    protected function configureBatchActions($actions): array {
        if (
            $this->hasRoute('edit') && $this->hasAccess('edit') &&
            $this->hasRoute('delete') && $this->hasAccess('delete')
        ) {
            $actions['delete'] = [
                'ask_confirmation' => true
            ];
        }
        return $actions;
    }

    function prePersist($object) : void { 
        //  exit("o");
        $object->setFecha($object->getFecha()??new \Datetime());
        $Pago = new Pago();
        $Pago=clone $object;
        /*return $object;*/
    }

    public function toString($object): string {
        return $object instanceof Pago
        ? $object->getIdpago()
        : 'Pago'; // shown in the breadcrumb on the create view
    }
}
