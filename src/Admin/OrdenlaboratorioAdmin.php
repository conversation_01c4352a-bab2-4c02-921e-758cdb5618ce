<?php

declare(strict_types=1);

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Sonata\Form\Type\DateRangePickerType;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Sonata\DoctrineORMAdminBundle\Filter\ChoiceFilter;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\Mime\Address;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\Regex;
use Vich\UploaderBundle\Form\Type\WysiwygType;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;

final class OrdenlaboratorioAdmin extends AbstractAdmin
{
    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('clienteIdcliente', null, ['label' => 'Cliente',])
            ->add('beneficiarioIdbeneficiario', null, ['label' => 'Beneficiario',])
            ->add('clienteIdcliente.empresaclienteIdempresacliente.nombre', null, ['label' => 'Empresa Cliente',])
            ->add('stockventaIdstockventa.ventaIdventa.folio', null, ['label' => 'Folio',])
            ->add('tipoorden', null, ['label' => 'Tipo de orden',])
            ->add('codigobarras', null, ['label' => 'Codigo de barras',])
            ->add('creacion', null, ['label' => 'Creación',])
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('clienteIdcliente.nombre', null, ['label' => 'Nombre del cliente',])
            ->add('beneficiarioIdbeneficiario', null, ['label' => 'Beneficiario',])
            ->add('clienteIdcliente.empresaclienteIdempresacliente.nombre', null, ['label' => 'Empresa Cliente',])
            ->add('tipoorden', null, ['label' => 'Tipo de orden',])
            ->add('codigobarras', null, ['label' => 'Codigo de barras',])
            ->add('stockventaIdstockventa.ventaIdventa.folio', null, ['label' => "Folio de Venta",])
            ->add('creacion')
          
            ->add(ListMapper::NAME_ACTIONS, null, [
                'actions' => [
                    'edit' => [],
                    'delete' => [],
                ],
            ])
        ;
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->with("Orden de laboratorio",['class' => 'col-md-6 '])
            ->add('clienteIdcliente', ModelListType::class,['btn_list' => true, 'label'=>"Cliente",],['delete'])
            ->add('beneficiarioIdbeneficiario', ModelListType::class,['btn_list' => true, 'label'=>"Beneficiario",],['delete'])
            //->add('empresaclienteIdempresaclientes',ModelListType::class,['btn_list' => true, 'label'=>"Empresa",],['delete'])
            ->add('tipoorden', ChoiceType::class, [
                'label' => "Tipo de orden",
                'choices'  => [
                  'Armazon' => "1",
                  'Lente de contacto' => "2",
                ]
            ])
            ->add('cilindrood', TextType::class, ['label' => "Cilindro OD",])
            ->add('cilindrooi', TextType::class, ['label' => "Cilindro OI",])
            ->add('ejeod', TextType::class, ['label' => "Eje OD",])
            ->add('ejeoi', TextType::class, ['label' => "Eje OI",])
            ->add('esferaod', TextType::class, ['label' => "Esfera OD",])
            ->add('esferaoi', TextType::class, ['label' => "Esfera OI",])
            ->add('addod', TextType::class, ['label' => "Add OD",])
            ->add('addoi', TextType::class, ['label' => "Add OI",])
            ->end()
            ->with("",['class' => 'col-md-6 '])
            ->add('armazoncliente', ChoiceType::class, [
                'label' => "Armazon del cliente",
                'choices'  => [
                  'Si' => "Si",
                  'No' => "No",
                ]
            ])
            ->add('dip', TextType::class, ['label' => "Dip",])
            ->add('ao', TextType::class, ['label' => "Ao",])
            ->add('aco', TextType::class, ['label' => "Aco",])
            ->add('disenolenteIddisenolente', ModelListType::class,['btn_list' => true, 'label'=>"Diseño",],['delete'])
            ->add('materialIdmaterial', ModelListType::class,['btn_list' => true, 'label'=>"Material",],['delete'])
            ->add('tratamientoIdtratamiento', ModelListType::class,['btn_list' => true, 'label'=>"Tratamiento",],['delete'])
            ->add('color', TextType::class, ['label' => "Color",])
            ->add('codigobarras', TextType::class, ['label' => 'Codigo de barras',])
            ->add('stockventaIdstockventa', ModelListType::class, ['label' => "Codigo de barras Armazon"])
            ->add('observaciones', TextareaType::class, ['label' => 'Observaciones',])
            ->end()
        ;
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
      $query = parent::configureQuery($query);
  
      $rootAlias = current($query->getRootAliases());
  
      $query->andWhere(
        $query->expr()->eq($rootAlias . '.status', ':status')
      );
  
      $query->setParameter('status', '1');
  
      return $query;
    }
}
