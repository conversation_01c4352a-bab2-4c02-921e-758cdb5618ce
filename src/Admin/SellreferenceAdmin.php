<?php

declare(strict_types=1);

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Sonata\AdminBundle\Form\Type\ModelType;

final class SellreferenceAdmin extends AbstractAdmin
{

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('name', null, array('label'=>'Nombre', 'show_filter' => true))
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('name', null, array('label'=>'Nombre', 'show_filter' => true))
            ->add('commissionstorablepercentage', null, array('label'=>'Porcentaje de comisión en almacenables', 'show_filter' => false))
            ->add('commissionservicepercentage', null, array('label'=>'Porcentaje de comisión en servicios', 'show_filter' => false))
            ->add(ListMapper::NAME_ACTIONS, null, [
                'label' => "Opciones",
                'actions' => [
                    'edit' => [],
                    'delete' => [],
                ],
            ])
        ;
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->with("-", ['class' => 'col-md-6 '])
                ->add('name', null, array('label'=>'Nombre', ))
            ->end()
            ->with(" ", ['class' => 'col-md-6 '])
                ->add('commissionstorablepercentage', NumberType::class, [
                    'required'=>false,
                    'label' => 'Porcentaje de comisión en almacenables',
                    'attr' => array(
                        'onkeydown'=>"validarNumero(this);",
                        'onkeyup'=>"validarNumero(this);"
                    )
                ])
                ->add('commissionservicepercentage', NumberType::class, [
                    'required'=>false,
                    'label' => 'Porcentaje de comisión en servicios',
                    'attr' => array(
                        'onkeydown'=>"validarNumero(this);",
                        'onkeyup'=>"validarNumero(this);"
                    )
                ])
            ->end()
        ;
    }

    protected function configureExportFields():array
    {
        return array(
            'Nombre'=>'name',
            'Porcentaje de comisión en almacenables'=>'commissionstorablepercentage',
            'Porcentaje de comisión en servicios'=>'commissionservicepercentage',
        );
    }

    public function getExportFormats():array
    {
        return ['xlsx'];
    }

    public function toString($object):string
    {
        return $object instanceof Sellreference
            ? $object->getname()
            : 'Referencia';
    }
}
