<?php

declare(strict_types=1);

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Doctrine\ORM\EntityManagerInterface;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Doctrine\ORM\EntityRepository;
use Sonata\AdminBundle\Form\Type\ModelType;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Sonata\AdminBundle\Form\Type\ModelListType;



final class CategoriadocumentosAdmin extends AbstractAdmin
{

    private $tokenStorage;
    private $em;
    private $empresasg;

    public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage, EntityManagerInterface $em)
{
    parent::__construct($code, $class, $baseControllerName);
    $this->tokenStorage = $tokenStorage;

    $this->em = $em;

}

public function buscarEmpresas(){

    $user=$this->tokenStorage->getToken()->getUser();

    $query = $this-> em->createQuery(
        'SELECT e.idempresa
           FROM App\Entity\Usuarioempresapermiso up
           INNER JOIN up.empresaIdempresa e
           where up.status =:status and up.usuarioIdusuario =:idusuario
           '
    )->setParameters(['status'=>"1",'idusuario'=>$user->getIdusuario()]);
    $empresas= $query->getResult();


    $this->empresasg=$empresas;

}

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $filter
            ->add('nombre',null, ['label' => 'Nombre', 'show_filter' => true,])
            ->add('empresaIdempresa.nombre', ModelFilter::class, [
                'label' => 'Empresas',
                'show_filter' => true,
                'field_options' => [
                    'class' => 'App\Entity\Empresa',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('e')
                            ->andWhere('e.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('empresa', $this->empresasg);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('nombre')
            ->add('empresaIdempresa.nombre', null, array('label'=>'Empresa'))
            ->add(ListMapper::NAME_ACTIONS, null, [ 'label' => "Opciones",
                'actions' => [
                    'edit' => [],
                    'delete' => [],
                ],
            ])
        ;
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->with("-", ['class' => 'col-md-12'])
                ->add('nombre')
                ->add('empresaIdempresa',   ModelListType::class,[
                    'required' => true,
                    'btn_list' => true,
                    'label'=>"Empresa",
                ],['delete'])
            ->end() 
            
        ;
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show
            ->add('nombre')
            
        ;
    }

    public function toString($object): string 
    {
        return $object instanceof Categoriadocumentos
        ? $object->getNombre()
        : 'Categoria'; //Shown in the breadcrumb on the create view
    }

    public function getExportFormats():array 
    {
        return ['xlsx'];
    }

    protected function configureExportFields(): array {
        return array (
            'Nombre'=> 'nombre',
        );
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
      $query = parent::configureQuery($query);
  
      $rootAlias = current($query->getRootAliases());
  
      $query->andWhere(
        $query->expr()->eq($rootAlias . '.status', ':status')
      );
  
      $query->setParameter('status', '1');
  
      return $query;
    }
}
