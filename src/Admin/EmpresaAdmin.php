<?php

declare(strict_types=1);

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Sonata\DoctrineORMAdminBundle\Filter\ChoiceFilter;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

final class EmpresaAdmin extends AbstractAdmin
{
 
    private $tokenStorage;
    private $em;
    private $empresasg;

    public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage, EntityManagerInterface $em)
{
    parent::__construct($code, $class, $baseControllerName);
    $this->tokenStorage = $tokenStorage;

    $this->em = $em;

}

public function buscarEmpresas(){

    $user=$this->tokenStorage->getToken()->getUser();

    $query = $this-> em->createQuery(
        'SELECT e.idempresa
           FROM App\Entity\Usuarioempresapermiso up
           INNER JOIN up.empresaIdempresa e
           where up.status =:status and up.usuarioIdusuario =:idusuario
           '
    )->setParameters(['status'=>"1",'idusuario'=>$user->getIdusuario()]);
    $empresas= $query->getResult();


    $this->empresasg=$empresas;

}

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('nombre', null, array('label'=>'Nombre', 'show_filter' => true,))
            ->add('razonsocial', null, array('label'=>'Razón social'))
            ->add('rfc', null, array('label'=>'RFC')) 
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('nombre')
            ->add('razonsocial', null, array('label'=>'Razón social'))
            ->add('rfc', null, array('label'=>'RFC'))
            ->add('emailfacturacion', null, array('label'=>'Correo de facturación'))
            ->add(ListMapper::NAME_ACTIONS, null, [ 'label' => "Opciones",
                'actions' => [
                    'edit' => [],
                    'delete' => [],
                    'logoVisor' => ['template' => 'abrir_visor/list-action-logo-visor.html.twig'],
                    'publicityVisor' => ['template' => 'abrir_visor/list-action-publicity-visor.html.twig'],
                ],
            ])
        ;
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->with(" ",['class' => 'col-md-6']) 
                ->add('nombre', null, array('label'=>'Nombre'))
                ->add('razonsocial', null, array('label'=>'Razón social','required' => true,))
                ->add('rfc', null, array('label'=>'RFC','required' => true,))
                ->add('prefijotickets',null, array('label'=>'Prefijo de Empresa', 'disabled' => true))
                ->add('emailfacturacion', null, array('label'=>'Email a donde llegarán las solicitudes de factura','required' => true,))
                ->add('configuracionservidorcorreo', null, array('label'=>'Servidor para enviar el correo','required' => true,))
            ->end()

            ->with("",['class' => 'col-md-6'])
                ->add('ivapercentage', null, array('label'=>'Porcentaje de Iva','required' => true,))
                ->add('file', FileType::class, [
                    'label' => 'Logo de la empresa',
                    // make it optional so you don't have to re-upload the PDF file
                    // every time you edit the Product details
                    'required' => false,
                    // unmapped fields can't define their validation using annotations
                    // in the associated entity, so you can use the PHP constraint classes
                    'constraints' => [
                        new File([
                            'maxSize' => '1000000',
                            'mimeTypes' => [
                                'image/jpeg',
                                'image/png',
                                
                            ],
                            'mimeTypesMessage' => 'Sube un archivo válido',
                        ])
                    ],
                ])
                ->add('publicidad', FileType::class, [
                    'label' => 'Publicidad de la empresa (Se sugiere un tamaño de 700 x 300 píxeles)',
                    // make it optional so you don't have to re-upload the PDF file
                    // every time you edit the Product details
                    'required' => false,
                    // unmapped fields can't define their validation using annotations
                    // in the associated entity, so you can use the PHP constraint classes
                    'constraints' => [
                        new File([
                            'maxSize' => '1000000',
                            'mimeTypes' => [
                                'image/jpeg',
                                'image/png',
                                
                            ],
                            'mimeTypesMessage' => 'Sube un archivo válido',
                        ])
                    ],
                ])
                ->add('pieticket',TextareaType::class,[ 'label' => 'Pie de ticket','required'=>true])
            ->end()
            
            
        ;
    }

    public function prePersist(object $empresa): void
    {
        $empresa->setFechacreacion(new \DateTime("now"));
        $empresa->setFechaactualizacion(new \DateTime("now"));
        $this->manageFileUpload($empresa);;
    }

    public function preUpdate(object $empresa): void
    {
        $this->manageFileUpload($empresa);
    }

    private function manageFileUpload(object $empresa): void
    {
        if ($empresa->getFile()) {
            $empresa->upload("logo");
        }

        if ($empresa->getPublicidad()) {
            $empresa->upload("publicidad");
        }
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
      $query = parent::configureQuery($query);
  
      $rootAlias = current($query->getRootAliases());
  
      $query->andWhere(
        $query->expr()->eq($rootAlias . '.status', ':status')
      );
  
      $query->setParameter('status', '1');
  
      return $query;
    }
}
