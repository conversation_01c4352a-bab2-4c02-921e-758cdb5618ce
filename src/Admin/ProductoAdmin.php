<?php
// src/Admin/CategoryAdmin.php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\MoneyType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Sonata\AdminBundle\Form\Type\ModelType;
use App\Entity\Categoria;
use Doctrine\ORM\EntityManagerInterface;
use App\Entity\Marca;
use App\Entity\Producto;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Sonata\AdminBundle\Route\RouteCollection;
use Sonata\CoreBundle\Validator\ErrorElement;
use Sonata\DoctrineORMAdminBundle\Filter\ModelAutocompleteFilter;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Sonata\DoctrineORMAdminBundle\Filter\ChoiceFilter;
use Doctrine\ORM\EntityRepository;

final class ProductoAdmin extends AbstractAdmin
{

    private $em;
    private $empresasg;
    private $tokenStorage;

    public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage, EntityManagerInterface $em)
    {
        parent::__construct($code, $class, $baseControllerName);
        $this->tokenStorage = $tokenStorage;

        $this->em = $em;

    }

    public function buscarEmpresas()
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $query = $this->em->createQuery(
            'SELECT e.idempresa
           FROM App\Entity\Usuarioempresapermiso up
           INNER JOIN up.empresaIdempresa e
           WHERE up.status =:status AND up.usuarioIdusuario =:idusuario'
        )->setParameters([
            'status' => "1",
            'idusuario' => $user->getIdusuario()
        ]);

        $empresas = $query->getResult();

        $this->empresasg = array_column($empresas, 'idempresa');
    }


    protected function configureDatagridFilters(DatagridMapper $datagridMapper): void
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $datagridMapper
            ->add('marcaIdmarca', ModelFilter::class, [
                'label' => "Marca",
                'field_options' => [
                    'query_builder' => function (EntityRepository $repository) {
                        return $repository->createQueryBuilder('m')
                            ->where('m.status = :status')
                            ->setParameter('status', 1)
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('categoriaIdcategoria.claseIdclase', ModelFilter::class, [
                'label' => "Categoría",
                'field_options' => [
                    'class' => 'App\Entity\Clase',
                    'query_builder' => function (EntityRepository $repository) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('c')
                            ->join('c.empresaIdempresa', 'e')
                            ->andWhere('c.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', 1)
                            ->setParameter('empresa', $this->empresasg)
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true
                ]
            ])
            ->add('categoriaIdcategoria.claseIdclase.empresaIdempresa', ModelFilter::class, [
                'label' => 'Empresas',
                'field_options' => [
                    'class' => 'App\Entity\Empresa',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('e')
                            ->andWhere('e.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('empresa', $this->empresasg)
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('modelo', null, array('label' => 'Modelo', 'show_filter' => true,))
            ->add('clave', null, array('label' => 'Clave', 'show_filter' => true,))
            ->add('codigobarrasuniversal', null, ['show_filter' => true, 'label' => "UPC"])
            ->add('masivounico', ChoiceFilter::class, [
                'label' => 'Masivo / Único',
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        'Único' => '1', 'Masivo' => '2'
                    ],
                ]
            ])
        ;
    }


    protected function configureListFields(ListMapper $listMapper): void
    {
        $listMapper
            ->addIdentifier('modelo', null, array('label' => 'Modelo'))
            ->add('clave', null, ['label' => "Clave"])
            ->add('descripcion', null, ['label' => "Descripción"])
            ->add('codigobarrasuniversal', null, ['label' => "UPC"])
            ->add('tipoproducto', null, array('label' => 'Tipo de producto', 'template' => 'admin/tipo_producto_list.html.twig'))
            ->add('categoriaIdcategoria.claseIdclase.nombre', null, array('label' => 'Categoría'))
            ->add('categoriaIdcategoria.nombre', null, ['label' => 'Subcategoría',])
            ->add('marcaIdmarca.nombre', null, array('label' => 'Marca'))
            ->add('costo')
            ->add('precio')
            ->add('color')
            ->add('precioespecial', null, array('label' => 'Precio especial'))
            ->add('preciodistribuidor', null, array('label' => 'Precio de distribuidor'))
            ->add('preciosubdistribuidor', null, array('label' => 'Precio de subdistribuidor'))
            ->add('medidaIdmedida', null, array('label' => 'Medida'))
            ->add('masivounico', 'choice', array(
                'label' => 'Masivo / Unico',
                'choices' => [
                    '1' => 'Único',
                    '2' => 'Masivo',
                ]
            ))
            ->add('showonlinestore', 'choice', array(
                'label' => '¿Se mostrará en la tienda en línea?',
                'choices' => [
                    '0' => "No",
                    '1' => "Sí"
                ]
            ))
            ->add(ListMapper::NAME_ACTIONS, null, ['label' => "Opciones",
                'actions' => [
                    'edit' => [],
                    'delete' => [],
                    'updateProductImages' => ['template' => 'product_images/list__action_updateProductImages.html.twig',],
                    'addTags' => ['template' => 'tags/list_action_tagSelector.html.twig'],
                ]
            ]);
    }

    protected function configureExportFields(): array
    {
        return array(
            'Modelo' => 'modelo',
            'Clave' => 'clave',
            'Descripción' => 'descripcion',
            'UPC' => 'codigobarrasuniversal',
            'Tipo de producto' => 'tipoproducto',
            'Categoria' => 'categoriaIdcategoria.claseIdclase.nombre',
            'Subcategoria' => 'categoriaIdcategoria.nombre',
            'Marca' => 'marcaIdmarca',
            'Costo' => 'costo',
            'Precio' => 'precio',
            'Color' => 'color',
            'Medida' => 'medidaIdmedida',
            'Precio especial' => 'precioespecial',
            'Masivo/Unico' => 'masivounico',
            'Precio de distribuidor' => 'preciodistribuidor',
            'Se muetra en la tienda en línea' => 'showonlinestore',
        );
    }

    protected function configureFormFields(FormMapper $formMapper): void
    {
        $queryBuilderCategoria = $this->getModelManager()
            ->getEntityManager(Categoria::class)
            ->createQueryBuilder('c')
            ->select('c')
            ->from('App:Categoria', 'c')
            ->where('c.status =1')
            ->orderBy('c.nombre', 'ASC');

        $queryBuilderMarca = $this->getModelManager()
            ->getEntityManager(Marca::class)
            ->createQueryBuilder('m')
            ->select('m')
            ->from('App:Marca', 'm')
            ->where('m.status =1')
            ->orderBy('m.nombre', 'ASC');

        $formMapper
            ->with("Datos del producto", ['class' => 'col-md-6 '])
            ->add('modelo', TextType::class, array('label' => 'Modelo'))
            ->add('categoriaIdcategoria', ModelType::class, [
                'label' => 'Subcategoria',
                'class' => Categoria::class,
                'required' => true,
                'invalid_message' => 'error!',
                'property' => 'nombre',
                'query' => $queryBuilderCategoria,
                'placeholder' => 'Selecciona una opción',
            ])
            ->add('clave', TextType::class, array('required' => false, 'label' => 'Clave'))
            ->add('marcaIdmarca', ModelType::class, [
                'label' => 'Marca',
                'class' => Marca::class,
                'required' => true,
                'invalid_message' => 'error!',
                'property' => 'nombre',
                'query' => $queryBuilderMarca
            ])
            /* ->add('proveedorIdproveedor', ModelListType::class,[
              'required' => true,
              'btn_list' => true,
              'label'=>"Proveedor",
            ],['delete'])*/
            ->add('tipoproducto', ChoiceType::class, [
                'label' => 'Tipo de producto',
                'choices' => [
                    'Almacenable' => "1",
                    'Servicio' => "2"
                ]
            ])
            ->add('medidaIdmedida', ModelListType::class, ['required' => true, 'btn_list' => true, 'label' => "Medida",], ['delete'])
            ->add('sobrepuesto', ChoiceType::class, [
                'required' => false,
                'label' => 'Sobre puesto',
                'choices' => [
                    'Si' => "1",
                    'No' => ""
                ]
            ])
            ->add('codigobarrasuniversal', TextType::class, array('label' => 'Codigo de barras universal', 'required' => false))
            ->add('masivounico', ChoiceType::class, [
                'label' => 'Masivo / Unico',
                'choices' => [
                    'Unico' => "1",
                    'Masivo' => "2"
                ]
            ])
            ->add('tipomaterial', TextType::class, array('label' => 'Tipo de material', 'required' => false))
            ->add('descripcion', TextareaType::class, array('label' => 'Descripción', 'required' => false))
            ->add('color', TextareaType::class, array('label' => 'Color', 'required' => false))
            ->add('costo', MoneyType::class, array(
                'label' => 'Costo',
                'currency' => "MXN",
                'grouping' => true,
                'attr' => array(
                    'class' => 'form-control',
                    'onkeydown' => "validarNumero(this);",
                    'onkeyup' => "validarNumero(this);"
                )
            ))
            ->add('precio', MoneyType::class, array(
                'label' => 'Precio',
                'currency' => "MXN",
                'grouping' => true,
                'attr' => array(
                    'class' => 'form-control',
                    'onkeydown' => "validarNumero(this);",
                    'onkeyup' => "validarNumero(this);"
                )
            ))
            ->add('precioespecial', MoneyType::class, array(
                'label' => 'Precio especial',
                'required' => false,
                'currency' => "MXN",
                'grouping' => true,
                'attr' => array(
                    'class' => 'form-control',
                    'onkeydown' => "validarNumero(this);",
                    'onkeyup' => "validarNumero(this);"
                )
            ))
            ->add('preciodistribuidor', MoneyType::class, array(
                'label' => 'Precio de distribuidor',
                'required' => false,
                'currency' => "MXN",
                'grouping' => true,
                'attr' => array(
                    'class' => 'form-control',
                    'onkeydown' => "validarNumero(this);",
                    'onkeyup' => "validarNumero(this);"
                )
            ))
            ->add('preciosubdistribuidor', MoneyType::class, array(
                'label' => 'Precio de subdistribuidor',
                'required' => false,
                'currency' => "MXN",
                'grouping' => true,
                'attr' => array(
                    'class' => 'form-control',
                    'onkeydown' => "validarNumero(this);",
                    'onkeyup' => "validarNumero(this);"
                )
            ))
            ->end()
            ->with("Datos para la tienda en línea", ['class' => 'col-md-6 '])
            ->add('showonlinestore', ChoiceType::class, [
                'label' => '¿Se mostrará en la tienda en línea?',
                'choices' => [
                    'No' => "0",
                    'Sí' => "1"
                ]
            ])
            ->add('gender', ChoiceType::class, [
                'label' => 'Género',
                'choices' => [
                    'Mujer' => "M",
                    'Hombre' => "H",
                    'Unisex' => "U"
                ]
            ])
            ->add('moreinfo', TextType::class, array('required' => false, 'label' => 'Estilo'))
            ->add('design', TextType::class, array('required' => false, 'label' => 'Diseño'))
            ->add('framecolorIdframecolor', ModelListType::class, ['required' => false, 'btn_list' => true, 'label' => "Color de tienda en línea",], ['delete'])
            ->add('framematerialIdframematerial', ModelListType::class, ['required' => false, 'btn_list' => true, 'label' => "Material de tienda en línea",], ['delete'])
            ->add('descriptiononlinestore', TextareaType::class, [
                'label' => 'Descripción para la tienda en línea',
                'required' => false,
                'attr' => [
                    'class' => 'tiny',
                    'tinymce' => true,
                    'data-theme' => 'advanced',
                    'novalidate' => 'novalidate',
                ],
                'mapped' => true,
            ])
            /*->add('moreinfo', TextareaType::class, [
              'label' => 'Campo de más información para la tienda en línea',
              'required' => false,
              'attr' => [
                  'class' => 'tiny',
                  'tinymce' => true,
                  'data-theme' => 'advanced',
                  'novalidate' => 'novalidate',
              ],
              'mapped' => true,
            ])*/

            ->end();
    }



    public function getExportFormats(): array
    {
        return ['xlsx'];
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {

        $Usuario = $this->tokenStorage->getToken()->getUser();
        $idusuario = $Usuario->getIdusuario();
        $mm = $this->getModelManager();
        $query = parent::configureQuery($query);
        $queryBuilder = $mm->getEntityManager($this->getClass())->createQueryBuilder();

        // Realiza una consulta para obtener la empresa del usuario autenticado.
        $queryBuilder->select('empresa')
            ->from('App\Entity\Usuarioempresapermiso', 'eup')
            ->innerJoin('eup.empresaIdempresa', 'empresa')
            ->innerJoin('eup.usuarioIdusuario', 'u')
            ->where('u.idusuario=' . $idusuario);

        $rootAlias = current($query->getRootAliases());

        // Se une a la empresa a través de la relación de categoría, clase y empresa.
        $query->innerJoin($rootAlias . '.categoriaIdcategoria', 'c')
            ->innerJoin('c.claseIdclase', 'cl')
            ->innerJoin('cl.empresaIdempresa', 'e');

        // Añade una restricción para que solo se muestren los productos relacionados con la empresa del usuario autenticado.
        $query->where($queryBuilder->expr()->in("e", $queryBuilder->getDQL()));

        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );
        $query->setParameter('status', '1');


        $query = parent::configureQuery($query);

        $rootAlias = current($query->getRootAliases());

        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );

        $query->setParameter('status', '1');

        return $query;
    }

    function prePersist($object): void
    {
        //  exit("o");
        if (trim($object->getCodigobarrasuniversal()) == "" || trim($object->getCodigobarrasuniversal()) == null) {
            $codigoBarras = "121" . mt_rand(100000000, 999999999);
            $object->setCodigobarrasuniversal($codigoBarras);
            /*
            return $object;
            */
        }
    }

    public function preValidate($object): void
    {
        //quitar signos a precio
        $object->setPrecio(str_replace("$", "", $object->getPrecio()));
        $object->setPrecio(str_replace(",", "", $object->getPrecio()));
        //quitar signos a costo
        $object->setCosto(str_replace("$", "", $object->getCosto()));
        $object->setCosto(str_replace(",", "", $object->getCosto()));
        //quitar signos a cantidad
        $object->setCantidad((int)trim(str_replace(",", "", $object->getCantidad())));
        //echo "\n"

        /*
        return $object;
        */
    }

    public function toString($object): string
    {
        return $object instanceof Producto
            ? $object->getModelo()
            : 'Producto'; // shown in the breadcrumb on the create view
    }
}