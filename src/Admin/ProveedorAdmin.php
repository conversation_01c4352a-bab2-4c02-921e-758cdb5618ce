<?php

declare(strict_types=1);

namespace App\Admin;

use App\Entity\Usuario;
use Doctrine\ORM\EntityRepository;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorage;

final class ProveedorAdmin extends AbstractAdmin 
{

    private $tokenStorage;

    public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage)
    {
      parent::__construct($code, $class, $baseControllerName);
      $this->tokenStorage = $tokenStorage;
    }
    protected function configureDatagridFilters(DatagridMapper $datagridMapper): void 
    {
        $datagridMapper
            ->add('nombre', null, ['label' => "Nombre comercial", 'show_filter' => true,])
            ->add('rfc', null, ['label' => "RFC"])
            ->add('razonsocial', null, ['label' => "Razón social"])
            ->add('telefono', null, ['label' => "Teléfono"])
            ->add('correoelectronico', null, ['label' => "Correo electrónico"])
        ;
    }

    protected function configureListFields(ListMapper $listMapper): void 
    {
        $listMapper
            ->add('nombre', null, ['label' => "Nombre comercial"])
            ->add('rfc', null, ['label' => "RFC"])
            ->add('razonsocial', null, ['label' => "Razón social"])
            ->add('ciudad')
            ->add('estado')
            ->add('codigopostal', null, ['label' => "Código postal"])
            ->add('telefono', null, ['label' => "Teléfono"])
            ->add('correoelectronico', null, ['label' => "Correo electrónico"])
            ->add(ListMapper::NAME_ACTIONS, null, [ 'label' => "Opciones",
                'actions' => [
                    'edit' => [],
                    'delete' => [],
                ]
            ])
        ;
    }

    protected function configureFormFields(FormMapper $formMapper): void 
    {
        $estados=[
            'Aguascalientes' => 'Aguascalientes',
            'Baja California' => 'Baja California',
            'Baja California Sur' => 'Baja California Sur',
            'Campeche' => 'Campeche',
            'Chiapas' => 'Chiapas',
            'Chihuahua' => 'Chihuahua',
            'Coahuila' => 'Coahuila',
            'Colima' => 'Colima',
            'Ciudad de México' => 'Ciudad de México',
            'Durango' => 'Durango',
            'Guanajuato' => 'Guanajuato',
            'Guerrero' => 'Guerrero',
            'Hidalgo' => 'Hidalgo',
            'Jalisco' => 'Jalisco',
            'Mexico' => 'Mexico',
            'Michoacan' => 'Michoacan',
            'Morelos' => 'Morelos',
            'Nayarit' => 'Nayarit',
            'Nuevo Leon' => 'Nuevo Leon',
            'Oaxaca' => 'Oaxaca',
            'Puebla' => 'Puebla',
            'Queretaro' => 'Queretaro',
            'Quintana Roo' => 'Quintana Roo',
            'San Luis Potosi' => 'San Luis Potosi',
            'Sinaloa' => 'Sinaloa',
            'Sonora' => 'Sonora',
            'Tabasco' => 'Tabasco',
            'Tamaulipas' => 'Tamaulipas',
            'Tlaxcala' => 'Tlaxcala',
            'Veracruz' => 'Veracruz',
            'Yucatan' => 'Yucatan',
            'Zacatecas' => 'Zacatecas',
        ];

        $formMapper
            ->with("Información",['class' => 'col-md-6 '])
                ->add('nombre', null, ['label' => 'Nombre comercial','required'=>true])
                ->add('RFC', null, ['label' => 'RFC','required'=>true])
                ->add('calle', null, ['label' => 'Calle'])
                ->add('calle2', null, ['label' => 'Calle 2'])
                ->add('numeroexterior', null, ['label' => 'Número exterior'])
                ->add('numerointerior', null, ['label' => 'Número interior'])
                ->add('colonia', null, ['label' => 'Colonia'])
                ->add('municipio', null, ['label' => 'Municipio'])
                ->add('ciudad', null, ['label' => 'Ciudad'])
            ->end()

            ->with("-",['class' => 'col-md-6 '])
                ->add('razonsocial', null, ['label' => "Razón social", 'required'=>true])
                ->add('estado', ChoiceType::class, ['label'=>"Estado", 'choices'  => $estados])
                ->add('codigopostal', null, ['label' => 'Código postal'])
                ->add('pais', null, ['label' => 'País'])
                ->add('telefono', null, ['label' => 'Teléfono'])
                ->add('correoelectronico', null, ['label' => 'Correo electrónico'])
                ->add('sitioweb', null, ['label' => 'Sitio web'])
                ->add('areaproveedorIdareaproveedor', null, ['label' => 'Área de proveedor','required'=>true])
                ->add('notas', TextareaType::class, ['label' => 'Notas','required'=>false])
            ->end()
        ;
    }

    protected function configureShowFields(ShowMapper $showMapper) : void 
    {
        $showMapper
            ->add('idproveedor')
            ->add('nombre')
            ->add('status')
            ->add('calle')
            ->add('calle2')
            ->add('ciudad')
            ->add('estado')
            ->add('codigopostal')
            ->add('pais')
            ->add('telefono')
            ->add('movil')
            ->add('correoelectronico')
            ->add('sitioweb')
            ->add('notas')
        ;
    } 

    protected function configureExportFields(): array 
    {
        return array(
            'Nombre' => 'nombre',
            'RFC' => 'rfc',
            'Ciudad' => 'ciudad',
            'Estado' => 'estado',
            'Código Postal' =>'codigopostal',
            'Teléfono' => 'telefono',
            'Correo electrónico' => 'correoelectronico'
        );
    }
    
    public function getExportFormats(): array 
    {
        return ['xlsx'];
    }

    public function toString($object): string 
    {
        return $object instanceof Proveedor
            ? $object->getNombre()
            : "Proveedor"
        ; // shown in the breadcrumb on the create view
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {


      $query = parent::configureQuery($query);
  
      $rootAlias = current($query->getRootAliases());
  
      $query->andWhere(
        $query->expr()->eq($rootAlias . '.status', ':status')
      );
  
      $query->setParameter('status', '1');
  
      return $query;
    }
}