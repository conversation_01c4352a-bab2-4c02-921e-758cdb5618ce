<?php

declare(strict_types=1);

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Sonata\CoreBundle\Form\Type\ColorType;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;

final class FramecolorAdmin extends AbstractAdmin
{

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('color')
            ->add('colorcode')
            ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('color')
            ->add('colorcode')
            ->add(ListMapper::NAME_ACTIONS, null, [
                'actions' => [
                    'show' => [],
                    'edit' => [],
                    'delete' => [],
                ],
            ]);
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->add('color')

            ->add('colorcode', null, array(
                'label' => 'Código de color',
                
              ))
            ;
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show
            ->add('color')
            ->add('colorcode')
            ;
    }
}
