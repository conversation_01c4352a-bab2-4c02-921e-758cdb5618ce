<?php

declare(strict_types=1);

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Doctrine\ORM\EntityManagerInterface;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use App\Entity\Salelog;
use App\Entity\Authstage;
use App\Service\SalesService;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Sonata\Form\Type\DateRangePickerType;

final class VentagroupAdmin extends AbstractAdmin
{
    private $entityManager;
    private $tokenStorage;
    private $em;
    private $empresasg;
    private $SalesService;
    private $report;
    protected $baseRouteName = "app";
    protected $baseRoutePattern = "venta";

    public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage, ?string $report = null, EntityManagerInterface $em, SalesService $SalesService)
    {
        parent::__construct($code, $class, $baseControllerName);
        $this->tokenStorage = $tokenStorage;
        $this->SalesService = $SalesService;
        $this->baseRouteName = $report;
        $this->baseRoutePattern = $report;
        $this->report = $report;
        $this->em = $em;
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('creationdate', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de creación",
                'field_type' => DateRangePickerType::class,
                [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('idventagroup', null, ['label' => 'Grupo', 'show_filter' => true,])
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('idventagroup', null, ['label' => 'Grupo'])
            ->add('creationdate', null, ['label' => 'Fecha de creación', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'])
            ->add('ventafacturaIdventafactura.montoventa', null, ['label' => 'Monto de factura'])
            ->add(ListMapper::NAME_ACTIONS, null, [
                'label' => 'Opciones',
                'actions' => [
                    'detalleFacturagrupo' => ['template' => 'CRUD/list__action_detalleFacturaGrupo.html.twig',],
                    'edit' => [],
                    'delete' => [],
                ],
            ])
        ;
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $subject = $this->getSubject();

        $form
            ->with("Añade el comprobante de pago", ['class' => 'col-md-6 '])
            ->add('paymentfile', FileType::class, [
                'label' => 'Subir nuevo archivo de pago',
                'required' => false,
                'mapped' => false,
            ])
            ->end()
        ;
    }

    protected function configureRoutes(RouteCollectionInterface $collection): void
    {

        $collection
            ->add('detalleFacturagrupo', $this->getRouterIdParameter() . '/detalleFacturagrupo')
        ;
    }

    public function prePersist($object): void
    {


        $this->manageFileUpload($object);
    }

    public function preUpdate($object): void
    {
        $this->manageFileUpload($object);
    }

    private function manageFileUpload($object): void
    {
        /** @var UploadedFile $file */
        $file = $this->getForm()->get('paymentfile')->getData();

        if ($file) {
            $directory = 'uploads/FacturasEnGrupo'; // Replace with your upload directory

            if (!is_dir($directory)) {
                mkdir($directory, 0755, true);
            }

            $filename = sprintf('%s_%s.%s', $object->getIdventagroup(), uniqid(), $file->guessExtension());

            $file->move($directory, $filename);

            $object->setPaymentfile($filename);

            $AuthStage = $this->em->getRepository(Authstage::class)->findOneBy(array('stageorder' => 6)) ?? new AuthStage();
            $AuthStage->setStageorder(6)->setName("Pagado");
            $this->em->persist($AuthStage);
            $this->em->flush();

            $query = $this->em->createQuery(
                'SELECT v
                    FROM App\Entity\Venta v
                    INNER JOIN v.ventagroupIdventagroup vg
                    WHERE v.status = :status AND vg.idventagroup =:ventaGroupId'
            )->setParameters(["status" => "1", "ventaGroupId" => $object->getIdventagroup()]);
            $sales = $query->getResult();

            foreach ($sales as $Sale) {
                $Sale->setAuthstageIdauthstage($AuthStage);
                $tempSaleLog = new Salelog();
                $tempSaleLog->setName($AuthStage->getName());
                $tempSaleLog->setDate(new \DateTime("now"));
                $tempSaleLog->setAuthstageIdauthstage($AuthStage);
                $tempSaleLog->setVentaIdventa($Sale);
                $tempSaleLog->setUsuarioIdusuario($this->tokenStorage->getToken()->getUser());
                $this->em->persist($Sale);
                $this->em->persist($tempSaleLog);
            }
            $this->em->flush();
        }
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
        $query = parent::configureQuery($query);

        $rootAlias = current($query->getRootAliases());

        // Add condition to filter by status = 1
        $query->andWhere($rootAlias . '.status = :status')
            ->setParameter('status', 1);

        $query->addOrderBy($rootAlias . '.creationdate', 'DESC');
        return $query;
    }
}
