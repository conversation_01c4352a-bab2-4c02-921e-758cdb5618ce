<?php
// src/Admin/CategoryAdmin.php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\Exporter\Source\IteratorSourceIterator;
use Sonata\Form\Type\DateRangePickerType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use App\Entity\Membresia;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;


final class TraspasoalmacenAdmin extends AbstractAdmin {

    private $tokenStorage;
    private $em;
    private $empresasg;

    public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage, EntityManagerInterface $em)
{
    parent::__construct($code, $class, $baseControllerName);
    $this->tokenStorage = $tokenStorage;

    $this->em = $em;

}

public function buscarEmpresas(){

    $user=$this->tokenStorage->getToken()->getUser();

    $query = $this-> em->createQuery(
        'SELECT e.idempresa
           FROM App\Entity\Usuarioempresapermiso up
           INNER JOIN up.empresaIdempresa e
           where up.status =:status and up.usuarioIdusuario =:idusuario
           '
    )->setParameters(['status'=>"1",'idusuario'=>$user->getIdusuario()]);
    $empresas= $query->getResult();


    $this->empresasg=$empresas;

}

    

    protected function configureFormFields(FormMapper $formMapper): void {
    }

    protected function configureDatagridFilters(DatagridMapper $datagridMapper): void {
        $user = $this->tokenStorage->getToken()->getUser();

        $datagridMapper
            ->add('creacion',DateRangeFilter::class, array(
                'show_filter' => true,
                'label'=>"Fecha de traspaso",
                'field_type' => DateRangePickerType::class,[
                    'dp_side_by_side'       => true,
                    'dp_use_current'        => true,
                    'dp_use_seconds'        => false,
                    'dp_collapse'           => true,
                    'dp_calendar_weeks'     => false,
                    'dp_view_mode'          => 'days',
                    'dp_min_view_mode'      => 'days',
                ]
            ))
            ->add('sucursalIdsucursalorigen.empresaIdempresa', ModelFilter::class, [
                'label' => 'Empresas',
                'field_options' => [
                    'class' => 'App\Entity\Empresa',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('e')
                            ->andWhere('e.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('empresa', $this->empresasg)
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('sucursalIdsucursalorigen', ModelFilter::class, [
                'label'=>'Sucursal de origen',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('so')
                            ->andWhere('so.status = :status')
                            ->andWhere('IDENTITY(so.empresaIdempresa) IN (:empresa)')
                            ->setParameter('status', 1)
                            ->setParameter('empresa', $this->empresasg)
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('sucursalIdsucursaldestino', ModelFilter::class, [
                'label'=>'Sucursal de destino',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('sd')
                            ->andWhere('sd.status = :status')
                            ->andWhere('IDENTITY(sd.empresaIdempresa) IN (:empresa)')
                            ->setParameter('status', 1)
                            ->setParameter('empresa', $this->empresasg)
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('usuarioIdusuario', ModelFilter::class, [
                'label'=>'Responsable',
                'field_options' => [
                    'class' => 'App\Entity\Usuario',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('u')
                            ->join('u.sucursalIdsucursal', 's')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('u.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', 1)
                            ->setParameter('empresa', $this->empresasg);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])

        ;
    }

    protected function configureListFields(ListMapper $listMapper): void {
        $listMapper
            ->add('creacion','date',array('label'=>'Fecha de traspaso', 'format' => 'd-m-Y  H:i', 'timezone' => 'America/Mexico_City'))
        ;

        $listMapper
            ->add('notas',null,array('label'=>'Nota'))
        ;

        $listMapper
            ->add('sucursalIdsucursalorigen.nombre',null,array('label'=>'Sucursal de origen'))
        ;

        $listMapper
            ->add('sucursalIdsucursaldestino.nombre',null,array('label'=>'Sucursal de destino'))
        ;

        $listMapper
        ->add('sucursalIdsucursalorigen', ModelFilter::class, [
            'label' => 'Empresas',
            'show_filter' => true,
            'field_options' => [
                'class' => 'App\Entity\Empresa',
                'query_builder' => function (EntityRepository $repository) {
                    $this->buscarEmpresas();
                    return $repository->createQueryBuilder('e')
                        ->andWhere('e.status = :status')
                        ->andWhere('e.idempresa IN (:empresa)')
                        ->setParameter('status', '1')
                        ->setParameter('empresa', $this->empresasg);
                },
                'expanded' => true,
                'multiple' => true,
            ],
        ])
        ;

        $listMapper
        ->add('usuarioIdusuario.nombre',null,array('label'=>'Responsable'))
    ;

        $listMapper ->add(ListMapper::NAME_ACTIONS, null, [ 'label' => "Opciones",
            'actions' => [
                'detalleTraspasoAlmacen' => ['template' => 'CRUD/list__action_detalleTraspasoAlmacen.html.twig'],
            ]
        ]);
    }

    public function toString($object): string {
        return $object instanceof Membresia
           ? $object->getNombre()
           : 'Orden Salida';
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface{

        $Usuario=$this->tokenStorage->getToken()->getUser();
        $idusuario=$Usuario->getIdusuario();
        $mm = $this->getModelManager();
        $query = parent::configureQuery($query);
        $queryBuilder = $mm->getEntityManager($this->getClass())->createQueryBuilder();
    
        // Realiza una consulta para obtener la empresa del usuario autenticado.
        $queryBuilder->select('empresa')
            ->from('App\Entity\Usuarioempresapermiso', 'eup')
            ->innerJoin('eup.empresaIdempresa', 'empresa')
            ->innerJoin('eup.usuarioIdusuario', 'u')
            ->where('u.idusuario='.$idusuario);
    
        $rootAlias = current($query->getRootAliases());
    
        // Se une a la empresa a través de la relación de categoría, clase y empresa.
        $query->innerJoin($rootAlias.'.sucursalIdsucursalorigen', 'su')
              ->innerJoin('su.empresaIdempresa', 'e');
    
        // Añade una restricción para que solo se muestren los productos relacionados con la empresa del usuario autenticado.
        $query->where($queryBuilder->expr()->in("e", $queryBuilder->getDQL()));
        
        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );
        $query->setParameter('status', '1');

        $query = parent::configureQuery($query);
    
        $rootAlias = current($query->getRootAliases());
        $query->addOrderBy($rootAlias.'.creacion', 'DESC');
        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );
        $query->setParameter('status', '1');

        $query->addOrderBy($rootAlias . '.creacion', 'DESC');
    
        return $query;
    }

    protected function configureBatchActions($actions): array {
        if (
            $this->hasRoute('edit') && $this->hasAccess('edit') &&
            $this->hasRoute('delete') && $this->hasAccess('delete')
        ) {
            $actions['delete'] = [
                'ask_confirmation' => true
            ];
        }

        return $actions;
    }

    protected function configureExportFields(): array {
        return array(
            'Fecha de traspaso' => 'creacion',
            'Nota' => 'notas',
            'Sucursal de origen' => 'sucursalIdsucursalorigen.nombre',
            'Sucursal de destino' => 'sucursalIdsucursaldestino.nombre',
            'Responsable' => 'usuarioIdusuario.nombre',
        );
    }
    
    public function getExportFormats(): array {
        return ['xlsx'];
    }

    protected function configureRoutes(RouteCollectionInterface $collection): void {
        $collection->remove('create');
        $collection->remove('delete');
        $collection 
            ->add('detalleTraspasoAlmacen', $this->getRouterIdParameter().'/detalleTraspasoAlmacen')
        ;
    }


}
