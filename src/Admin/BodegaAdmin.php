<?php

declare(strict_types=1);

namespace App\Admin;

use Doctrine\DBAL\Types\IntegerType;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Sonata\AdminBundle\Show\ShowMapper;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;

final class BodegaAdmin extends AbstractAdmin 
{
    protected function configureDatagridFilters(DatagridMapper $datagridMapper): void 
    {
        $datagridMapper
            ->add('nombre')
            ->add('identificador')
        ;
    }

    protected function configureListFields(ListMapper $listMapper): void 
    {
        $listMapper
            ->add('identificador',TextType::class,['label'=>"Identificador"])
            ->add('nombre',TextType::class,['label'=>"Nombre"])
            ->add('direccion',TextareaType::class,['label'=>"Dirección"])
            ->add('_action', null, [
                'actions' => [
                    'show' => [],
                    'edit' => [],
                    'delete' => [],
                ],
            ])
        ;
    }

    protected function configureFormFields(FormMapper $formMapper): void 
    {
        $formMapper
            ->with("Sucursal",['class' => 'col-md-6'])
                ->add('identificador',TextType::class,['label'=>"Identificador",  'help' => 'En mayúsculas sin acentos ni carácteres especiales'])
            ->end()

            ->with(" ",['class' => 'col-md-6'])
                ->add('nombre',TextType::class,['label'=>"Nombre"])
                ->add('direccion',TextareaType::class,['label'=>"Dirección"])
            ->end()
        ;
    }

    protected function configureShowFields(ShowMapper $showMapper): void 
    {
        $showMapper
            ->add('nombre')
            ->add('direccion')
            ->add('identificador')
        ;
    }

    public function prePersist($object): void 
    {
        //  $object->setCreacion(new \DateTime("now"));
        //  $object->setModificacion(new \DateTime("now"));
    }

    public function preUpdate($object): void 
    {
        //$object->setCreacion(new \DateTime("now"));
        //  $object->setModificacion(new \DateTime("now"));
    }

    
    public function toString($object): string 
    {
        return $object instanceof Bodega
        ? $object->getNombre()
        : 'Bodega'; // shown in the breadcrumb on the create view
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
        $query = parent::configureQuery($query);

        $rootAlias = current($query->getRootAliases());

        $query->andWhere(
        $query->expr()->eq($rootAlias . '.status', ':status')
        );

        $query->setParameter('status', '1');

        return $query;
    }
}