<?php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Sonata\AdminBundle\Route\RouteCollectionInterface;



final class StockventaordenlaboratorioAdmin extends AbstractAdmin
{

    protected function configureRoutes(RouteCollectionInterface $collection): void
    {
        $collection->remove('create');
    }


    protected function configureDatagridFilters(DatagridMapper $datagridMapper): void
    {
        $datagridMapper
            ->add('stockventaIdstockventa.ventaIdventa.folio', null, [
                'label' => 'Folio de Venta'
            ])
            ->add('stockventaIdstockventa.ventaIdventa.sucursalIdsucursal.nombre', null, [
                'label' => 'Sucursal'
            ])
            ->add('stockventaIdstockventa.ventaIdventa.clienteIdcliente.nombre', null, [
                'label' => 'Nombre del Cliente'
            ])
            ->add('stockventaIdstockventa.ventaIdventa.beneficiario', null, [
                'label' => 'Beneficiario'
            ])
            ->add('stockventaIdstockventa.ventaIdventa.unidad', null, [
                'label' => 'Unidad'
            ])
            ->add('stockventaIdstockventa.ventaIdventa.sucursalIdsucursal', null, [
                'label' => 'Sucursal'
            ]);
    }


    protected function configureListFields(ListMapper $list): void
    {
        $list
        
            ->add('stockventaIdstockventa.ventaIdventa.folio', null, ['label' => 'Folio']);/*
            ->add('nombreSucursal', null, ['label' => 'Sucursal/Campaña'])
            ->add('TipoVenta', null, ['label' => 'Tipo de Venta'])
            ->add('clienteNombreCompleto', TextType::class, [
                'label' => 'Nombre del Titular',
                'sortable' => false,
            ])
            ->add('NombreBeneficiario', TextType::class, [
                'label' => 'Nombre Beneficiario',
                'sortable' => false,
            ])
            ->add('Numeroempleado', TextType::class, [
                'label' => 'N° Trabajador',
                'sortable' => false,
            ])
            ->add('NombreUnidad', TextType::class, [
                'label' => 'Unidad Académica',
                'sortable' => false,
            ])
            ->add('Marca', TextType::class, [
                'label' => 'Marca',
                'sortable' => false,
            ])
            ->add('Modelo', TextType::class, [
                'label' => 'Modelo',
                'sortable' => false,
            ])
            ->add('disenoMaterialTratamiento', 'text', [
                'label' => 'Diseño/Material/Tratamiento',
                'sortable' => false,
            ])
            ->add('Base', 'text', [
                'label' => 'Base',
                'sortable' => false,
            ])
            ->add('EsferaD', 'text', [
                'label' => 'Esf D',
                'sortable' => false,
            ])
            ->add('CilD', 'text', [
                'label' => 'Cil D',
                'sortable' => false,
            ])
            ->add('EjeD', 'text', [
                'label' => 'Eje D',
                'sortable' => false,
            ])
            ->add('EsferaI', 'text', [
                'label' => 'Esf I',
                'sortable' => false,
            ])
            ->add('CilI', 'text', [
                'label' => 'Cil I',
                'sortable' => false,
            ])
            ->add('EjeI', 'text', [
                'label' => 'Eje I',
                'sortable' => false,
            ])
            ->add('Add', 'text', [
                'label' => 'Add',
                'sortable' => false,
            ])
            ->add('Observaciones', 'text', [
                'label' => 'Observaciones',
                'sortable' => false,
            ])
            ->add('NombreCompletoUsuario', null, ['label' => 'Opto'])
            ->add('Folioautorizacion', null, ['label' => 'Folio Autorización'])
            ->add('Statusautorizacion', null, ['label' => 'Status Autorizacion'])
            ->add('Recepcion', null, ['label' => 'Fecha Recepción'])
            ->add('Fechalaboratorio', null, ['label' => 'Fecha de Entrada a Laboratorio'])
            ->add('Statuslaboratorio', null, ['label' => 'Status de Laboratorio'])
            ->add('Fechasalidalaboratorio', null, ['label' => 'Fecha Salida de Laboratorio'])
            ->add('Garantia', null, ['label' => 'Garantia'])
            ->add(ListMapper::NAME_ACTIONS, null, [
                'label' => "Opciones",
                'actions' => [
                    'edit' => [],
                    'flujoExpediente' => ['template' => 'dashboard_flujo_expediente/list-action-flujoexpediente.html.twig'],
                    'detalleventa' => ['template' => 'dashboard_flujo_expediente/list-action-detalleventa.html.twig'],
                    'descargardocumento' => ['template' => 'dashboard_flujo_expediente/list-action-descargardocumento.html.twig'],
                ]

            ]);*/
    }

    protected function configureFormFields(FormMapper $formMapper): void
    {

        $formMapper
            ->with("Editar", ['class' => 'col-md-6'])
            ->add('EsferaD', TextType::class, [
                'label' => 'Esfera Ojo Derecho'
            ])
            ->add('EsferaI', TextType::class, [
                'label' => 'Esfera Ojo Izquierdo'
            ])
            ->add('CilD', TextType::class, [
                'label' => 'Esfera Ojo Derecho'
            ])
            ->add('CilI', TextType::class, [
                'label' => 'Esfera Ojo Izquierdo'
            ])
            ->add('EjeD', TextType::class, [
                'label' => 'Eje D'
            ])
            ->add('EjeI', TextType::class, [
                'label' => 'Eje I'
            ])
            ->add('Add', TextType::class, [
                'label' => 'ADD'
            ])
            ->add('Observaciones', TextType::class, [
                'label' => 'Observaciones'
            ])
            ->end()
            ->with("Flujo", ['class' => 'col-md-6 '])

            ->add('Folioautorizacion', TextType::Class, [
                'label' => 'Folio Autorizacion'
            ])
            ->add('Statusautorizacion', TextType::Class, [
                'label' => 'Estatus Autorización'
            ])
            ->add('Recepcion', DateType::Class, [
                'label' => 'Fecha de Recepción'
            ])
            ->add('Fechalaboratorio', DateType::Class, [
                'label' => 'Fecha de Laboratorio'
            ])
            ->add('Statuslaboratorio', TextType::Class, [
                'label' => 'Estatus de Laboratorio'
            ])
            ->add('Fechasalidalaboratorio', DateType::Class, [
                'label' => 'Fecha de Salida de Laboratorio'
            ])
            ->add('Garantia', TextType::Class, [
                'label' => 'Garantia'
            ])
            ->end();
    }

    public function getExportFormats(): array
    {
        return ['xlsx', 'csv'];
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show
            ->add('idstockventaordenlaboratorio');
    }

    protected function configureBatchActions($actions): array
    {
        if (
            $this->hasRoute('edit') && $this->hasAccess('edit') &&
            $this->hasRoute('delete') && $this->hasAccess('delete')
        ) {
            $actions['delete'] = [
                'ask_confirmation' => true
            ];
        }
        return $actions;
    }

    protected function configureExportFields(): array
    {
        return [
            'Folio',
            'nombreSucursal',
            'TipoVenta',
            'clienteNombreCompleto',
            'NombreBeneficiario',
            'Numeroempleado',
            'NombreUnidad',
            'Marca',
            'Modelo',
            'disenoMaterialTratamiento',
            'Base',
            'EsferaD',
            'CilD',
            'EjeD',
            'EsferaI',
            'CilI',
            'EjeI',
            'Add',
            'Observaciones',
            'NombreCompletoUsuario',
            'Etapa',
        ];
    }

    protected $datagridValues = [
        '_sort_order' => 'ASC',
        '_sort_by' => 'Folio',
    ];
}
