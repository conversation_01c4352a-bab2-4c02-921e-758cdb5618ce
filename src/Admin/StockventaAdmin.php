<?php

declare(strict_types=1);

namespace App\Admin;

use App\Entity\Venta;
use Doctrine\ORM\EntityManagerInterface;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use App\Entity\Sucursal;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Sonata\DoctrineORMAdminBundle\Filter\ChoiceFilter;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Sonata\Form\Type\DateRangePickerType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Doctrine\ORM\EntityRepository;
use function PHPStan\dumpType;

final class StockventaAdmin extends AbstractAdmin {
    private $tokenStorage;
    private $em;
    private $empresasg;

    public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage, EntityManagerInterface $em) {
        parent::__construct($code, $class, $baseControllerName);
        $this->tokenStorage = $tokenStorage;
        $this->em = $em;
    }

    public function buscarEmpresas() {
        $user = $this->tokenStorage->getToken()->getUser();
        $query = $this->em->createQuery(
            '   SELECT e.idempresa
                FROM App\Entity\Usuarioempresapermiso up
                INNER JOIN up.empresaIdempresa e
                where up.status =:status AND up.usuarioIdusuario =:idusuario
            '
        )->setParameters(['status' => "1", "idusuario" => $user->getIdusuario()]);
        $empresas = $query->getResult();
        $this->empresasg = $empresas;
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface {
        $Usuario = $this->tokenStorage->getToken()->getUser();
        $idusuario = $Usuario->getIdusuario();
        $mm = $this->getModelManager();
        $query = parent::configureQuery($query);
        $queryBuilder = $mm->getEntityManager($this->getClass())->createQueryBuilder();
        // Realiza una consulta para obtener la empresa del usuario autenticado.
        $queryBuilder->select('empresa')
            ->from('App\Entity\Usuarioempresapermiso', 'eup')
            ->innerJoin('eup.empresaIdempresa', 'empresa')
            ->innerJoin('eup.usuarioIdusuario', 'u')
            ->where('u.idusuario=' . $idusuario)
        ;
        $rootAlias = current($query->getRootAliases());
        // Se une a la empresa a través de la relación de categoría, clase y empresa.
        $query->innerJoin($rootAlias . '.ventaIdventa', 've')
            ->innerJoin('ve.sucursalIdsucursal', 'su')
            ->innerJoin('su.empresaIdempresa', 'e')
        ;
        // Añade una restricción para que solo se muestren los productos relacionados con la empresa del usuario autenticado.
        $query->where($queryBuilder->expr()->in("e", $queryBuilder->getDQL()));

        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );
        $query->setParameter('status', '1');

        $query = parent::configureQuery($query);

        $rootAlias = current($query->getRootAliases());

        $query->innerJoin($rootAlias . '.ventaIdventa', 'v');

        $query->andWhere($query->expr()->eq($rootAlias . '.status', ':status'));

        $query->setParameters(['status' => "1"]);


        $query->addSelect(' COALESCE(GROUP_CONCAT(CONCAT(us.nombre, \' \' , us.apellidopaterno, \' \' , us.apellidomaterno) ), \'\', \' /n\n  \')  as optometrista ');
        $query->leftJoin('App\Entity\Stockventaordenlaboratorio ', 'svol', 'WITH', 'svol.stockventaIdstockventa = ' . $rootAlias);
        $query->leftJoin('svol.stockventaIdstockventa', 'sv');
        $query->leftJoin('svol.ordenlaboratorioIdordenlaboratorio', 'ol');
        $query->leftJoin('ol.flujoexpedienteIdflujoexpediente ', 'f');
        $query->leftJoin('sv.ventaIdventa ', 'ven');
        $query->leftJoin('f.usuarioIdusuario ', 'us');
        $query->groupBy($rootAlias . '.idstockventa');

        $query->addSelect('COALESCE(GROUP_CONCAT(DISTINCT cup.nombre), \'\') AS Cupon');
        $query->leftJoin('App\Entity\Ventacupon', 'vcup', 'WITH', 'vcup.ventaIdventa = ' . $rootAlias . '.ventaIdventa');
        $query->leftJoin('vcup.cuponIdcupon', 'cup');
        $query->groupBy($rootAlias . '.idstockventa');

        return $query;
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void {
        $user = $this->tokenStorage->getToken()->getUser();

        $filter
            ->add('ventaIdventa.fechaventa', DateRangeFilter::class, array(
                'label' => "Fecha de venta",
                'show_filter' => true,
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('ventaIdventa.fechacreacion', DateRangeFilter::class, array(
                'label' => "Fecha de cotización",
                'show_filter' => true,
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('stockIdstock.sucursalIdsucursal.empresaIdempresa', ModelFilter::class, [
                'label' => 'Empresas',
                'field_options' => [
                    'class' => 'App\Entity\Empresa',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('e')
                            ->andWhere('e.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('empresa', $this->empresasg)
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
            ->add('stockIdstock.sucursalIdsucursal', ModelFilter::class, [
                'label' => 'Sucursales',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('s')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('s.status = :status')
                            ->andWhere('s.tipo = :tipo')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('tipo', 'sucursal')
                            ->setParameter('empresa', $this->empresasg)
                            ->orderBy('s.nombre', 'ASC')
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
            ->add('stockIdstock.campana', ModelFilter::class, [
                'label' => 'Campañas',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('s')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('s.status = :status')
                            ->andWhere('s.tipo = :tipo')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('tipo', 'campaña')
                            ->setParameter('empresa', $this->empresasg)
                            ->orderBy('s.nombre', 'ASC')
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
            ->add('stockIdstock.bodega', ModelFilter::class, [
                'label' => 'Bodegas',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('s')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('s.status = :status')
                            ->andWhere('s.tipo = :tipo')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('tipo', 'bodega')
                            ->setParameter('empresa', $this->empresasg)
                            ->orderBy('s.nombre', 'ASC')
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
            ->add('ventaIdventa.tipoventaIdtipoventa', ModelFilter::class, [
                'label' => "Tipo de venta",
                'field_options' =>[
                    'class'=>'App\Entity\Tipoventa',
                    'query_builder'=> function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('tv')
                            ->join('tv.empresaIdempresa','e')
                            ->andWhere('tv.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('empresa',$this->empresasg)
                            ->setParameter('status','1')
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
            ->add('ventaIdventa.folio', null, ['label' => "Folio de venta"])
            ->add('ventaIdventa.clienteIdcliente.nombre', null, ['label' => "Cliente"])
            ->add('ventaIdventa.beneficiario', null, ['label' => "Beneficiario"])
            ->add('ventaIdventa.unidadIdunidad', ModelFilter::class, [
                'label' => "Unidad",
                'field_options' => [
                    'query_builder' => function (EntityRepository $repository) {
                        return $repository->createQueryBuilder('u')
                            ->where('u.status = :status')
                            ->setParameter('status', 1);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true
                ]
            ])
            ->add('stockIdstock.productoIdproducto.modelo', null, ['label' => "Modelo"])
            ->add('stockIdstock.productoIdproducto.marcaIdmarca', ModelFilter::class, [
                'label' => "Marca",
                'field_options' => [
                    'query_builder' => function (EntityRepository $repository) {
                        return $repository->createQueryBuilder('m')
                            ->where('m.status = :status')
                            ->setParameter('status', 1);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true
                ]
            ])
            ->add('stockIdstock.productoIdproducto.categoriaIdcategoria', ModelFilter::class, [
                'label' => "Categoria",
                'field_options' => [
                    'query_builder' => function (EntityRepository $repository) {
                        return $repository->createQueryBuilder('c')
                            ->where('c.status = :status')
                            ->setParameter('status', 1);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true
                ]
            ])
            ->add('stockIdstock.productoIdproducto.tipomaterial', null, ['label' => "Tipo de material"])
            ->add('cantidad', null, ['label' => "Cantidad"])
            ->add('precio', null, ['label' => "Precio"])
            ->add('costo', null, ['label' => "Costo"])
            ->add('preciofinal', null, ['label' => "Precio final"])
            ->add('stockIdstock.codigobarras', null, ['label' => "SKU",'show_filter' => true])
            ->add('stockIdstock.productoIdproducto.codigobarrasuniversal', null, ['label' => "UPC",'show_filter' => true,])
            ->add('estaapartado', ChoiceFilter::class, [
                'label' => 'Apartado',
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        'No' => '0', 'Si' => '1'
                    ],
                ],
            ])
            ->add('ventaIdventa.status', ChoiceFilter::class, [
                'label' => 'Activa/Cancelada',
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        'Cancelada' => '0',
                        'Activa' => '1',
                    ],
                ],
            ])
            ->add('stockIdstock.productoIdproducto.tipoproducto', ChoiceFilter::class, [
                'label' => 'Tipo de producto',
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        'Almacenable' => '1',
                        'Servicio' => '2'
                    ],
                ],
            ])
            ->add('ventaIdventa.cotizacion', ChoiceFilter::class, array(
                'label' => 'Cotización/Venta',
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        'Cotización' => '1',
                        'Venta' => '0',

                    ],
                ],
            ))
        ;
    }

    protected function configureListFields(ListMapper $list): void {
        $list
            ->add('ventaIdventa.sucursalIdsucursal.empresaIdempresa', null, array('label' => 'Empresa'))
            ->add('stockIdstock.codigobarras', null, array('label' => 'SKU'))
            ->add('stockIdstock.productoIdproducto.codigobarrasuniversal', null, ['label' => "UPC"])
            ->add('ventaIdventa.folio', null, array('label' => 'Folio de venta'))
            ->add('ventaIdventa.liquidada', 'choice', array(
                'label' => 'Liquidada',
                'choices' => [
                    '0' => 'No',
                    '1' => 'Si',
                    '' => 'No',
                ]
            ))
            ->add('ventaIdventa.authorizationnumber', null, array('label' => 'Número de autorización'))
            ->add('ventaIdventa.status', 'choice', array(
                'label' => 'Activa/Cancelada',
                'choices' => [
                    '0' => 'Cancelada',
                    '1' => 'Activa',
                    '' => 'Cancelada',
                ]
            ))
            ->add('estaapartado', 'choice', array(
                'label' => '¿Esta apartado?',
                'choices' => [
                    '1' => 'Si',
                    '2' => 'No',
                    '0' => 'No',
                    '' => 'No'
                ]
            ))
            ->add('ventaIdventa.cotizacion', 'choice', array(
                'label' => 'Cotización/Venta',
                'choices' => [
                    '' => 'Venta',
                    '0' => 'Venta',
                    '1' => 'Cotización'
                ]
            ))
            ->add('ventaIdventa.clienteIdcliente.nombre', null, array('label' => 'Nombre de cliente'))
            ->add('ventaIdventa.beneficiario', null, array('label' => 'Beneficiario'))
            ->add('Cupon', null, array('label' => 'Cupon'))
            ->add('optometrista', null, array('label' => 'Optometrista'))
            ->add('ventaIdventa.clienteIdcliente.telefono', null, array('label' => 'Teléfono'))
            ->add('ventaIdventa.clienteIdcliente.email', null, array('label' => 'Correo electrónico'))
            ->add('ventaIdventa.clienteIdcliente.numeroempleado', null, array('label' => 'Número de empleado'))
            ->add('ventaIdventa.clienteIdcliente.comonosconocio', null, array('label' => '¿Cómo nos conoció?'))
            ->add('ventaIdventa.clienteIdcliente.fechanacimiento', null, array('label' => 'Fecha de nacimiento', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('ventaIdventa.clienteIdcliente.edad', null, array('label' => 'Edad'))
            ->add('ventaIdventa.fechacreacion', 'date', array('label' => 'Fecha de cotización', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('ventaIdventa.fechaventa', 'date', array('label' => 'Fecha de venta', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('ventaIdventa.fechacancelacion', 'date', array('label' => 'Fecha de cancelación', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('ventaIdventa.fechaactualizacion', 'date', array('label' => 'Fecha de Liquidación', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('ventaIdventa.porquesecancelo', null, array('label' => '¿Por qué canceló?'))
            ->add('getUsuarioResponsablecancelacion', null, array('label' => '¿Quién canceló?'))
            ->add('ventaIdventa.unidadIdunidad.nombre', TextType::class, array('label' => 'Unidad'))
            ->add('ventaIdventa.tipoventaIdtipoventa.nombre', TextType::class, array('label' => 'Tipo de venta'))
            ->add('ventaIdventa.sucursalIdsucursal.nombre', TextType::class, array('label' => 'Sucursal'))
            ->add('cantidad', null, array('label' => 'Número de unidades vendidas'))
            ->add('costo', null, array('label' => 'Costo en inventario'))
            ->add('precioNeto', null, array('label' => 'Precio neto '))
            ->add('porcentajedescuento', null, array('label' => 'Porcentaje de descuento '))
            ->add('getIva', null, array('label' => 'IVA'))
            ->add('preciofinal', null, array('label' => 'Precio Final Vendido Unitario'))
            ->add('montoTotal', null, array('label' => 'Monto total de productos vendidos'))
            ->add('getGerente', null, array('label' => 'Gerente'))
            ->add('getVendedorNombre', null, array('label' => 'Vendedor'))
            ->add('modificacion', 'date', array('label' => 'Fecha de última modificación', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('stockIdstock.productoIdproducto.modelo', null, array('label' => 'Modelo'))
            ->add('stockIdstock.productoIdproducto.categoriaIdcategoria.nombre', null, array('label' => 'Categoría'))
            ->add('stockIdstock.productoIdproducto.marcaIdmarca.nombre', null, array('label' => 'Marca'))
            ->add('stockIdstock.productoIdproducto.tipomaterial', null, array('label' => 'Tipo de material'))
            ->add('stockIdstock.productoIdproducto.tipoproducto', 'choice', array(
                'label' => 'Tipo de producto',
                'choices' => [
                    '1' => 'Almacenable',
                    '2' => 'Servicio'
                ]
            ))
            ->add('stockIdstock.productoIdproducto.color', null, array('label' => 'Color'))
            ->add('stockIdstock.productoIdproducto.codigocolor', null, array('label' => 'Código de color'))
            ->add('stockIdstock.productoIdproducto.descripcion', null, array('label' => 'Descripción'))
            ->add('productoIdproducto.medidaIdmedida.unidadmedidaIdunidadmedida.nombre', null, array('label' => 'Unidad de medida'))
            ->add('productoIdproducto.medidaIdmedida.nombre', null, array('label' => 'Medida'))
        ;
    }

    public function getOptometristas(Venta $venta): string
    {
        $query = $this->em->createQuery(
            'SELECT CONCAT(u.nombre, \' \' , u.apellidopaterno, \' \' , u.apellidomaterno) as optometrista
            FROM App\Entity\Stockventaordenlaboratorio svol
            LEFT JOIN svol.stockventaIdstockventa sv
            LEFT JOIN svol.ordenlaboratorioIdordenlaboratorio ol
            LEFT JOIN ol.flujoexpedienteIdflujoexpediente fe
            LEFT JOIN fe.usuarioIdusuario u
            WHERE sv.ventaIdventa = :venta'
        );

        $values = $query->setParameter('venta', $this->getIdventa())->getResult();

        $optometristaList = array_map(function ($item) {
            return $item['optometrista'];
        }, $values);

        return implode(', ', $optometristaList) ?: 'sin Optometrista';
    }

    protected function configureExportFields(): array {
        return array(
            'Empresa' => 'ventaIdventa.sucursalIdsucursal.empresaIdempresa.nombre',
            'SKU' => 'stockIdstock.codigobarras',
            'Código de barras universal' => 'stockIdstock.productoIdproducto.codigobarrasuniversal',
            'Folio de venta' => 'ventaIdventa.folio',
            'Liquidada' => 'ventaIdventa.getFormattedLiquidada',
            'Número de autorización' => 'ventaIdventa.authorizationnumber',
            'Activa/Cancelada' => 'ventaIdventa.getFormattedStatus',
            '¿Está apartado?' => 'getFormattedEstaApartado',
            'Cotización/Venta' => 'ventaIdventa.getFormattedQuotation',
            'Nombre de cliente' => 'ventaIdventa.clienteIdcliente.nombre',
            'Beneficiario' => 'ventaIdventa.beneficiario',
            'Teléfono' => 'ventaIdventa.clienteIdcliente.telefono',
            'Correo electrónico' => 'ventaIdventa.clienteIdcliente.email',
            'Número de empleado' => 'ventaIdventa.clienteIdcliente.numeroempleado',
            '¿Cómo nos conoció?' => 'ventaIdventa.clienteIdcliente.comonosconocio',
            'Edad' => 'ventaIdventa.clienteIdcliente.edad',
            'Fecha de cotización' => 'ventaIdventa.getFechaCreacionForExport',
            'Fecha de venta' => 'ventaIdventa.getFechaVentaForExport',
            '¿Por qué se canceló?' => 'ventaIdventa.porquesecancelo',
            '¿Quién canceló?' => 'getUsuarioResponsablecancelacion',
            'Fecha de cancelación' => 'ventaIdventa.getFechaCancelacionForExport',
            'Unidad' => 'ventaIdventa.unidadIdunidad.nombre',
            'Tipo de venta' => 'ventaIdventa.tipoventaIdtipoventa.nombre',
            'Sucursal' => 'ventaIdventa.sucursalIdsucursal.nombre',
            'Número de unidades vendidas' => 'cantidad',
            'Costo en inventario' => 'costo',
            'Precio al público' => 'stockIdstock.productoIdproducto.precio',
            'Precio neto' => 'precioNeto',
            'Porcentaje de descuento' => 'porcentajedescuento',
            'IVA' => 'getIva',
            'Precio Final Vendido Unitario' => 'preciofinal',
            'Gerente' => 'getGerente',
            'Vendedor' => 'getVendedorNombre',
            'Fecha de última modificación' => 'modificacion',
            'Modelo' => 'stockIdstock.productoIdproducto.modelo',
            'Categoría' => 'stockIdstock.productoIdproducto.categoriaIdcategoria.nombre',
            'Marca' => 'stockIdstock.productoIdproducto.marcaIdmarca.nombre',
            'Tipo de material' => 'stockIdstock.productoIdproducto.tipomaterial',
            'Color' => 'stockIdstock.productoIdproducto.color',
            'Código de color' => 'stockIdstock.productoIdproducto.codigocolor',
            'Descripción' => 'stockIdstock.productoIdproducto.descripcion',
        );
    }


    public function getExportFormats(): array {
        return ['xlsx','csv'];
    }

    protected function configureRoutes(RouteCollectionInterface $collection): void {
        $collection->remove('create');
        $collection
            ->add('detalleApartado', $this->getRouterIdParameter() . '/detalleApartado')
        ;
    }
}