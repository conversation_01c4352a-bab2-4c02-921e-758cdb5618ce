<?php
// src/Admin/CategoryAdmin.php

namespace App\Admin;

use App\Repository\VentaRepository;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Form\FormMapper;
use App\Entity\Venta;
use App\Entity\Cliente;
use App\Entity\Beneficiarioventa;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Sonata\DoctrineORMAdminBundle\Filter\StringFilter;
use Sonata\Form\Type\DateRangePickerType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Sonata\DoctrineORMAdminBundle\Filter\ChoiceFilter;
use Sonata\DoctrineORMAdminBundle\Filter\NullFilter;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Symfony\Component\HttpFoundation\InputBag;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\EntityManagerInterface;
use App\Service\SalesService;

final class VentaAdmin extends AbstractAdmin
{
    private $tokenStorage;
    private $em;
    private $empresasg;
    private $SalesService;
    private $report;
    protected $baseRouteName = "app";
    protected $baseRoutePattern = "venta";


    public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage, ?string $report = null, EntityManagerInterface $em, SalesService $SalesService)
    {
        parent::__construct($code, $class, $baseControllerName);
        $this->tokenStorage = $tokenStorage;
        $this->SalesService = $SalesService;
        $this->baseRouteName = $report;
        $this->baseRoutePattern = $report;
        $this->report = $report;
        $this->em = $em;
    }

    public function buscarEmpresas()
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $query = $this->em->createQuery(
            'SELECT e.idempresa
               FROM App\Entity\Usuarioempresapermiso up
               INNER JOIN up.empresaIdempresa e
               where up.status =:status and up.usuarioIdusuario =:idusuario
               '
        )->setParameters(['status' => "1", 'idusuario' => $user->getIdusuario()]);
        $empresas = $query->getResult();

        $this->empresasg = $empresas;
    }

    protected function configureFormFields(FormMapper $formMapper): void
    {
        //$formMapper->add('fecha', TextType::class);
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
        $query = parent::configureQuery($query);
        $Usuario = $this->tokenStorage->getToken()->getUser();
        $mm = $this->getModelManager();
        $idusuario = $Usuario->getIdusuario();

        $role = $Usuario->getRol();
        $sucursal = $Usuario->getSucursalIdsucursal()->getIdsucursal();

        $queryBuilder = $mm->getEntityManager($this->getClass())->createQueryBuilder();

        $queryBuilder->select('empresa')
            ->from('App\Entity\Usuarioempresapermiso', 'eup')
            ->innerJoin('eup.empresaIdempresa', 'empresa')
            ->innerJoin('eup.usuarioIdusuario', 'u')
            ->where(' u.idusuario=' . $idusuario)
            ->andWhere('eup.status = 1')
            ->setParameter('idusuario', $idusuario);

        if ($role == "ROLE_VENDEDOR") {

            $rootAlias = current($query->getRootAliases());
            //$query->addSelect($query);
            $query->leftJoin($rootAlias . '.usuarioIdusuario', 'usu');
            $query->leftJoin($rootAlias . '.sucursalIdsucursal', 's');
            $query->leftJoin('s.empresaIdempresa', 'e');

            $query->andWhere(' s.idsucursal=' . $sucursal)
                ->andWhere($queryBuilder->expr()->in('e', $queryBuilder->getDQL()))
                ->addOrderBy($rootAlias . '.folio', 'DESC');
        } else {


            $rootAlias = current($query->getRootAliases());
            $query->leftJoin($rootAlias . '.sucursalIdsucursal', 's');
            $query->leftJoin('s.empresaIdempresa', 'e');
            $query->addOrderBy($rootAlias . '.idventa', 'DESC')
                ->where($queryBuilder->expr()->in("e", $queryBuilder->getDQL()))
                ->addOrderBy($rootAlias . '.folio', 'DESC');
        }

        $query->addSelect(' COALESCE(GROUP_CONCAT(CONCAT(benef.nombre, \' \' , benef.apellidopaterno, \' \' , benef.apellidomaterno) ), \'\', \' /n\n  \')  as beneficiarios ');
        $query->leftJoin('App\Entity\Beneficiarioventa ', 'bv', 'WITH', 'bv.ventaIdventa = ' . $rootAlias);
        $query->leftJoin('bv.clienteIdcliente ', 'benef');
        $query->groupBy($rootAlias . '.idventa');

        $query->addSelect(' COALESCE(GROUP_CONCAT(CONCAT(us.nombre, \' \' , us.apellidopaterno, \' \' , us.apellidomaterno) ), \'\', \' /n\n  \')  as optometrista ');
        $query->leftJoin('App\Entity\Flujoexpedienteventa ', 'fev', 'WITH', 'fev.ventaIdventa = ' . $rootAlias);
        $query->leftJoin('fev.ventaIdventa ', 'v');
        $query->leftJoin('fev.flujoexpedienteIdflujoexpediente ', 'f');
        $query->leftJoin('f.usuarioIdusuario ', 'us');
        $query->groupBy($rootAlias . '.idventa');

        $query->addSelect('COALESCE(GROUP_CONCAT(cup.nombre), \'\') AS Cupon');
        $query->leftJoin('App\Entity\Ventacupon', 'vcup', 'WITH', 'vcup.ventaIdventa = ' . $rootAlias);
        $query->leftJoin('vcup.ventaIdventa', 'ven');
        $query->leftJoin('vcup.cuponIdcupon', 'cup');
        $query->groupBy($rootAlias . '.idventa');




        return $query;
    }

    protected function configureDatagridFilters(DatagridMapper $datagridMapper): void
    {
        $dateStart = new \DateTime();
        $dateEnd = new \DateTime();
        $Usuario = $this->tokenStorage->getToken()->getUser();
        $user = $this->tokenStorage->getToken()->getUser();

        $datagridMapper
            ->add('fechaventa', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de venta",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('fecha', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de creación",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('folio', null, array('label' => 'Folio de ticket', 'show_filter' => true))
            ->add('authorizationnumber', null, array('label' => 'Folio de autorización'))
            ->add('tipoventaIdtipoventa', ModelFilter::class, [
                'label' => "Tipo de venta",
                'field_options' => [
                    'class' => 'App\Entity\Tipoventa',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('tv')
                            ->join('tv.empresaIdempresa', 'e')
                            ->andWhere('tv.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('empresa', $this->empresasg)
                            ->setParameter('status', '1')
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('credito', StringFilter::class, [
                'label' => 'Credito',
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        'Si' => '1',
                        'No' => '0'
                    ],
                    'multiple' => false,
                ]
            ])
            ->add('sucursalIdsucursal.empresaIdempresa', ModelFilter::class, [
                'label' => 'Empresas',
                'field_options' => [
                    'class' => 'App\Entity\Empresa',
                    'query_builder' => function (EntityRepository $repository) use ($Usuario) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('e')
                            ->andWhere('e.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('empresa', $this->empresasg)
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('sucursalIdsucursal', ModelFilter::class, [
                'label' => 'Sucursales',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('s')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('s.status = :status')
                            ->andWhere('s.tipo = :tipo')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('tipo', 'sucursal')
                            ->setParameter('empresa', $this->empresasg)
                            ->orderBy('s.nombre', 'ASC')
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('campana', ModelFilter::class, [
                'label' => 'Campañas',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('s')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('s.status = :status')
                            ->andWhere('s.tipo = :tipo')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('tipo', 'campaña')
                            ->setParameter('empresa', $this->empresasg)
                            ->orderBy('s.nombre', 'ASC')
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('bodega', ModelFilter::class, [
                'label' => 'Bodegas',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('s')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('s.status = :status')
                            ->andWhere('s.tipo = :tipo')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('tipo', 'bodega')
                            ->setParameter('empresa', $this->empresasg)
                            ->orderBy('s.nombre', 'ASC')
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('clienteIdcliente.nombre', null, array('label' => 'Nombre del cliente',))
            ->add('clienteIdcliente.apellidopaterno', null, array('label' => 'Apellido paterno del cliente',))
            ->add('clienteIdcliente.apellidomaterno', null, array('label' => 'Apellido materno del cliente',))
            ->add('clienteIdcliente.email', null, array('label' => 'Correo del cliente',))
            ->add('clienteIdcliente.telefono', null, array('label' => 'Telefono del cliente',))
            ->add('clienteIdcliente.numeroempleado', null, array('label' => 'Numero de empleado del cliente',))
            ->add('unidadIdunidad', ModelFilter::class, [
                'label' => "Unidad",
                'field_options' => [
                    'query_builder' => function (EntityRepository $repository) {
                        return $repository->createQueryBuilder('u')
                            ->where('u.status = :status')
                            ->setParameter('status', 1);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true
                ]
            ])
            ->add('cotizacion', ChoiceFilter::class, [
                'label' => 'Estado',
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        'Venta' => '0',
                        'Cotización' => '1',
                    ],
                    'placeholder' => 'Seleccione una opción',
                ]
            ])
            ->add('status', ChoiceFilter::class, [
                'label' => 'Estado de la venta',
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        'Activa' => '1',
                        'Cancelada' => '0'
                    ],
                ]
            ])
            ->add('pidiofactura', StringFilter::class, [
                'label' => 'Facturada',
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        'Si' => '1',
                        'No' => '0'
                    ],
                    'multiple' => false,
                ]
            ])
            ->add('seapartoarmazon', StringFilter::class, [
                'label' => 'Armazón apartado',
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        'Si' => '1',
                        'No' => '0'
                    ],
                    'multiple' => false,
                ]
            ])
            ->add('archivoautorizacion', NullFilter::class, ['field_name' => 'archivoautorizacion', 'label' => "Archivo de autorización", 'inverse' => true])
            ->add('liquidada', ChoiceFilter::class, [
                'label' => 'Liquidación',
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        'No liquidada' => '0', 'Liquidada' => '1'
                    ],
                ]
            ])
        ;
    }

    protected function configureListFields(ListMapper $listMapper): void
    {
        $curReport = $this->report;
        $listMapper
            ->add(ListMapper::NAME_ACTIONS, null, [
                'label' => "Opciones",
                'actions' => [
                    'detalleVenta' => ['template' => 'CRUD/list__action_detalleVenta.html.twig',],
                    'cancelarVenta' => ['template' => 'CRUD/list__action_cancelarVenta.html.twig',],
                ]
            ])
            ->add('folio', null, array('label' => 'Folio de ticket','template' => 'CRUD/list_venta_color.html.twig'))
            ->add('authorizationnumber', null, array('label' => 'Folio de autorización'))
            ->add('tipoventaIdtipoventa.nombre', null, array('label' => 'Tipo de venta'))
            ->add('credito', 'choice', array(
                'label' => 'Credito',
                'choices' => [
                    '' => '',
                    '0' => 'No',
                    '1' => 'Sí'
                ]
            ))
            ->add('sucursalIdsucursal.empresaIdempresa.nombre', null, array('label' => 'Empresa',))
            ->add('sucursalIdsucursal.nombre', null, array('label' => 'Sucursal'))
            ->add('clienteIdcliente.isBeneficiario', null, array('label' => 'Cliente'))
            ->add('clienteIdcliente.empresaclienteIdempresacliente.nombre', null, array('label' => 'Empresa del Cliente'))
            ->add('clienteIdcliente.numeroempleado', null, array('label' => 'Número de empleado'))
            ->add('clienteIdcliente.unidadIdunidad.nombre', null, array('label' => 'Unidad'))
            ->add('clienteIdcliente.sellreferenceIdsellreference.name', null, array('label' => 'Cómo nos conoció'))
            ->add('total', null, array('label' => 'Subtotal'))
            ->add('iva', null, array('label' => 'IVA'))
            ->add('pagado', null, array('label' => 'Total'))
            ->add('deuda', null, [
                'label' => 'Estado del Pago',
                'template' => 'admin/deuda_total.html.twig',
            ])
            ->add('liquidada', 'choice', array(
                'label' => 'Liquidada',
                'choices' => [
                    '' => '',
                    '0' => 'No liquidada',
                    '1' => 'Liquidada'
                ]
            ))
            ->add('Cupon', null, array('label' => 'Cupon'))
            ->add('fecha', 'date', array('label' => 'Fecha de creación', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('fechaventa', 'date', array('label' => 'Fecha de venta', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('fechaactualizacion', 'date', array('label' => 'Fecha de actualización', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('liquidatedDate', null, array('label' => 'Fecha de liquidación'))
            ->add('storableProducts', null, array('label' => 'Almacenables'))
            ->add('serviceProducts', null, array('label' => 'Servicios'))
            ->add('beneficiarios', null, array('label' => 'Beneficiarios'))
            ->add('optometrista', null, array('label' => 'Optometrista'))
            ->add('unidadIdunidad.nombre', null, array('label' => 'Unidad de procedencia'))
            ->add('usuarioIdusuario.nombreVendedor', null, array('label' => 'Vendedor'))
            ->add('gerente', null, array('label' => 'Gerente'))
            ->add('getFormattedSaleQuotation', null, array('label' => 'Tipo'))
            ->add('seapartoarmazon', 'choice', array(
                'label' => 'Se apartó armazón',
                'choices' => [
                    '' => 'No',
                    '0' => 'No',
                    '1' => 'Si'
                ]
            ))
            ->add('sedescontodeinventario', 'choice', array(
                'label' => 'Se descontó de inventario',
                'choices' => [
                    '' => 'No',
                    '0' => 'No',
                    '1' => 'Si'
                ]
            ))
            ->add('status', 'choice', array(
                'label' => 'Estado',
                'choices' => [
                    '' => 'Cancelado',
                    '0' => 'Cancelado',
                    '1' => 'Activo'
                ]
            ))
            ->add('archivoautorizacion', null, array('label' => 'Archivo de Autorización'))
            ->add('notas', null, array('label' => 'Notas'));
    }


    public function toString($object): string
    {
        return $object instanceof Venta
            ? $object->getfolio()
            : 'Venta'; // shown in the breadcrumb on the create view
    }

    protected function configureBatchActions($actions): array
    {
        if (
            $this->hasRoute('edit') && $this->hasAccess('edit') &&
            $this->hasRoute('delete') && $this->hasAccess('delete')
        ) {
            $actions['delete'] = [
                'ask_confirmation' => true
            ];
        }
        return $actions;
    }

    protected function configureRoutes(RouteCollectionInterface $collection): void
    {
        $collection
            ->add('detalleVenta', $this->getRouterIdParameter() . '/detalleVenta');
        $collection
            ->add('cancelarVenta', $this->getRouterIdParameter() . '/cancelarVenta');
        $collection
            ->remove('create');
        $collection
            ->add('visualizar', $this->getRouterIdParameter() . '/visualizar');
    }

    public function getTest($idventa)
    {
        return (string)$this->SalesService->getSoldProductos(2);
    }

    public function getBeneficiarios(Venta $venta): string
    {
        $query = $this->em->createQuery(
            'SELECT CONCAT(cl.nombre, \' \' , cl.apellidopaterno, \' \' , cl.apellidomaterno) as beneficiario
            FROM App\Entity\Beneficiarioventa bv 
            LEFT JOIN bv.clienteIdcliente cl
            WHERE bv.ventaIdventa = :venta'
        );

        $values = $query->setParameter('venta', $this->getIdventa())->getResult();

        $beneficiariosList = array_map(function ($item) {
            return $item['beneficiario'];
        }, $values);

        return implode(', ', $beneficiariosList) ?: 'sin beneficiarios';
    }

    public function getOptometristas(Venta $venta): string
    {
        $query = $this->em->createQuery(
            'SELECT CONCAT(u.nombre, \' \' , u.apellidopaterno, \' \' , u.apellidomaterno) as optometrista
            FROM App\Entity\Flujoexpedienteventa fev
            LEFT JOIN fev.ventaIdventa v
            LEFT JOIN fev.flujoIdflujo f
            LEFT JOIN f.usuarioIdusuario u
            WHERE fev.ventaIdventa = :venta'
        );

        $values = $query->setParameter('venta', $this->getIdventa())->getResult();

        $optometristaList = array_map(function ($item) {
            return $item['optometrista'];
        }, $values);

        return implode(', ', $optometristaList) ?: 'sin Optometrista';
    }

    protected function configureExportFields(): array
    {
        $curReport = $this->report;

        $exportFields = [
            // Datos generales
            'Folio de ticket'           => 'folio',
            'Folio de autorización'     => 'authorizationnumber',
            'Tipo de venta'             => 'tipoventaIdtipoventa.nombre',
            'Sucursal'                  => 'sucursalIdsucursal.nombre',
            'Empresa'                   => 'sucursalIdsucursal.empresaIdempresa.nombre',

            // Cliente
            'Número de Empĺeado'        => 'clienteIdcliente.numeroempleado',
            'Cliente'                   => 'clienteIdcliente.nombreCompleto',
            'Cliente Teléfono'          => 'clienteIdcliente.telefono',
            'Cliente Email'             => 'clienteIdcliente.email',
            'Cómo nos conoció'          => 'clienteIdcliente.comonosconocio',
            'Beneficiarios'             => 'beneficiario',

            // Unidad y Vendedor
            'Unidad de procedencia'     => 'unidadIdunidad.nombre',
            'Vendedor'                  => 'usuarioIdusuario.nombreVendedor',
            'Gerente'                   => 'gerente',

            // Fechas importantes
            'Fecha de creación'         => 'fecha',
            'Fecha de venta'            => 'fechaventa',
            'Fecha de actualización'    => 'fechaactualizacion',

            // Productos y Servicios
            'Almacenables'              => 'storableProducts',
            'Servicios'                 => 'serviceProducts',

            // Información financiera
            'porcentaje'                => 'porcentajeIva',
            'Subtotal'                  => 'total',
            'IVA'                       => 'iva',
            'Total'                     => 'pagado',
            'Deuda Total'               => 'deuda',

            // Estado y otros
            'Venta/Cotizacion'          => 'getFormattedSaleQuotation',
            'Activo/Cancelado'          => 'canceladoActivo',
            'Notas'                     => 'notas',
        ];

        if ($curReport != "ventaAlmacen") {
            $exportFields["Fecha de liquidación"] = "liquidatedDate";
        }

        return $exportFields;
    }


    public function getExportFormats(): array
    {
        return ['xlsx','csv'];
    }
}
