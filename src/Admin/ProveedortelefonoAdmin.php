<?php

declare(strict_types=1);

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;

final class ProveedortelefonoAdmin extends AbstractAdmin
{
    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('telefono', null, ['label' => 'Número telefónico', 'show_filter' => true,])
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('proveedorcontactoIdproveedorcontacto', null, ['label' => 'Contacto',])
            ->add('telefono', null, ['label' => 'Número telefónico',])
            ->add('nombre', null, ['label' => 'Nombre',])
            ->add('creacion', 'date', array('label'=>'Fecha de creación', 'format' => 'd-m-Y', 'timezone' => 'America/Mexico_City'))
            ->add('actualizacion', 'date', array('label'=>'Fecha de actualización', 'format' => 'd-m-Y', 'timezone' => 'America/Mexico_City'))
            ->add(ListMapper::NAME_ACTIONS, null, ['label' => "Opciones",
                'actions' => [
                    'edit' => [],
                    'delete' => [],
                ],
            ])
        ;
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form 
            ->with("-", ['class' => 'col-md-6 '])
                ->add('telefono', null, ['label' => 'Número telefónico',])
                ->add('nombre', null, ['label' => 'Nombre',])

            ->end()
            ->with("", ['class' => 'col-md-6 '])
                ->add('proveedorcontactoIdproveedorcontacto', ModelListType::class, ['btn_list' => true, 'label'=>"Contacto",], ['delete'])
            ->end()
        ;
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show
            ->add('idproveedortelefono')
            ->add('telefono')
            ->add('creacion')
            ->add('actualizacion')
            ->add('status')
            ->add('nombre')
        ;
    }

    public function prePersist($object): void
    {
        $object->setCreacion(new \DateTime("now"));
        $object->setActualizacion(new \DateTime("now"));
    }

    public function preUpdate($object): void 
    {
        $object->setActualizacion(new \DateTime("now"));
    }

    public function getExportFormats(): array 
    {
        return ['xlsx'];
    }
}
