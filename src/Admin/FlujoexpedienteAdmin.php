<?php

declare(strict_types=1);

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Sonata\AdminBundle\Form\Type\ModelType;
use Sonata\AdminBundle\Form\Type\ModelAutocompleteType;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use App\Entity\Ordenlaboratorio;
use App\Admin\OrdenlaboratorioAdmin;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Sonata\DoctrineORMAdminBundle\Filter\ModelAutocompleteFilter;
use Sonata\DoctrineORMAdminBundle\Filter\ChoiceFilter;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Sonata\Form\Type\DateRangePickerType;
use Sonata\AdminBundle\Form\Type\Filter\NumberFilterType;
use Sonata\AdminBundle\Form\Type\Filter\ModelAutocompleteFilterType;
use Sonata\AdminBundle\Form\Type\Filter\ChoiceFilterType;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Sonata\AdminBundle\Form\Type\Filter\ChoiceType as SonataChoiceType;
use Sonata\AdminBundle\Form\Type\Filter\DateRangeType;
use Sonata\DoctrineORMAdminBundle\Filter\ClassFilter;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Doctrine\ORM\EntityRepository;

final class FlujoexpedienteAdmin extends AbstractAdmin
{
    private TokenStorageInterface $tokenStorage;

    public function __construct(TokenStorageInterface $tokenStorage)
    {
        $this->tokenStorage = $tokenStorage;
        parent::__construct();
    }


    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $Usuario = $this->tokenStorage->getToken() ? $this->tokenStorage->getToken()->getUser() : null;

        $filter
            ->add('graduacionIdgraduacion.creacion', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de creación",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('graduacionIdgraduacion.actualizacion', DateRangeFilter::class, array(
                'label' => "Fecha de actualización",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('clienteIdcliente.nombre',null, array('label' => "Nombre del cliente"))
            ->add('clienteIdcliente.apellidopaterno', null, array('label' => 'Apellido paterno del cliente'))
            ->add('clienteIdcliente.apellidomaterno', null, array('label' => 'Apellido materno del cliente'))
            ->add('clienteIdcliente.numeroempleado', null, array('label'=>'Número de empleado'))
            ->add('clienteIdcliente.telefono', null, array('label'=>'Teléfono del cliente'))
            ->add('clienteIdcliente.email', null, array('label'=>'Correo electrónico del cliente'))
            ->add('clienteIdcliente.empresaclienteIdempresacliente', ModelFilter::class, [
                'label' => "Empresa del cliente",
                'field_options' => [
                    'query_builder' => function (EntityRepository $repository) {
                        return $repository->createQueryBuilder('ec')
                            ->where('ec.status = :status')
                            ->setParameter('status', 1);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true
                ]
            ])
            ->add('clienteIdcliente.unidadIdunidad', ModelFilter::class, [
                'label' => "Unidad",
                'field_options' => [
                    'query_builder' => function (EntityRepository $repository) {
                        return $repository->createQueryBuilder('u')
                            ->where('u.status = :status')
                            ->setParameter('status', 1);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true
                ]
            ])
            ->add('usuarioIdusuario', ModelFilter::class, [
                'label' => 'Optometrista',
                'field_options' => [
                    'query_builder' => function (EntityRepository $repository) {
                        return $repository->createQueryBuilder('o')
                            ->where('o.status = :status')
                            ->setParameter('status', 1);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true
                ]
            ])
            ->add('sucursalIdsucursal', ModelFilter::class, [
                'label' => "Sucursal",
                'field_options' => ['expanded' => false,
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($Usuario) {
                        return $repository->createQueryBuilder('s')
                            ->where('s.status = :status')
                            ->setParameter('status','1');
                    },
                    'multiple' => true,
                    'expanded' => false,
                ]
            ])
        ;
    }


    //$stages = ['Registro datos Personales','Examen Visual','Orden de laboratorio','Venta','Firma','Entrega de lentes pendiente','Finalizado'];

    protected function configureListFields(ListMapper $list): void
    {
        $list
            // Acciones
            ->add(ListMapper::NAME_ACTIONS, null, [
                'label'    => 'Opciones',
                'actions'  => [
                    'flujo' => [
                        'template' => 'dashboard_flujo_expediente/list-action-flujo.html.twig',
                    ],
                    'sendEmail' => [
                        'template' => 'dashboard_flujo_expediente/list-action-sendEmail.html.twig',
                    ],
                ],
            ])
            // Información del Cliente
            ->add('nombrecompleto', 'text', [
                'label'    => 'Nombre Cliente',
                'sortable' => false,
            ])
            ->add('sucursalIdsucursal.nombre', null, [
                'label' => 'Sucursal Flujo',
            ])
            ->add('clienteIdcliente.empresaclienteIdempresacliente.nombre', null, [
                'label' => 'Empresa del cliente',
            ])
            ->add('clienteIdcliente.unidadIdunidad.nombre', null, [
                'label' => 'Unidad',
            ])
            ->add('clienteIdcliente.telefono', null, [
                'label' => 'Teléfono',
            ])
            ->add('clienteIdcliente.email', null, [
                'label' => 'Correo electrónico',
            ])

            // Información de la Graduación
            ->add('graduacionIdgraduacion.anamnesis', null, [
                'label' => 'Anamnesis',
            ])
            ->add('graduacionIdgraduacion.isglassesuser', 'choice', [
                'label'    => '¿Usa lentes actualmente?',
                'choices'  => [
                    ''  => 'No',
                    '0' => 'No',
                    '1' => 'Si',
                ],
            ])
            ->add('graduacionIdgraduacion.hasglassesnow', 'choice', [
                'label'    => '¿Los trae consigo?',
                'choices'  => [
                    ''  => 'No',
                    '0' => 'No',
                    '1' => 'Si',
                ],
            ])
            ->add('graduacionIdgraduacion.gpesferaod', null, [
                'label' => 'Rx ant Esfera OD',
            ])
            ->add('graduacionIdgraduacion.gpesferaoi', null, [
                'label' => 'Rx ant Esfera OI',
            ])
            ->add('graduacionIdgraduacion.gpcilindrood', null, [
                'label' => 'Rx ant Cilindro OD',
            ])
            ->add('graduacionIdgraduacion.gpcilindrooi', null, [
                'label' => 'Rx ant Cilindro OI',
            ])
            ->add('graduacionIdgraduacion.gpejeod', null, [
                'label' => 'Rx ant Eje OD',
            ])
            ->add('graduacionIdgraduacion.gpejeoi', null, [
                'label' => 'Rx ant Eje OI',
            ])
            ->add('graduacionIdgraduacion.gpaddod', null, [
                'label' => 'Rx ant Add',
            ])
            ->add('graduacionIdgraduacion.gpavlejosod', null, [
                'label' => 'Rx ant AV lejos OD',
            ])
            ->add('graduacionIdgraduacion.gpavlejosoi', null, [
                'label' => 'Rx ant AV lejos OI',
            ])
            ->add('graduacionIdgraduacion.gpavcercaod', null, [
                'label' => 'Rx ant AV cerca OD',
            ])
            ->add('graduacionIdgraduacion.gpavcercaoi', null, [
                'label' => 'Rx ant AV cerca OI',
            ])
            ->add('graduacionIdgraduacion.tipolente', 'choice', [
                'label'   => 'Tipo de Lente',
                'choices' => [
                    ''  => '',
                    '0' => '',
                    '1' => 'Armazón',
                    '2' => 'Lente de contacto',
                ],
            ])
            ->add('graduacionIdgraduacion.apobservaciones', null, [
                'label' => 'Observaciones del armazón previo',
            ])
            ->add('graduacionIdgraduacion.tipolentecontacto', 'choice', [
                'label'   => 'Tipo de lente de contacto',
                'choices' => [
                    ''  => '',
                    '0' => '',
                    '1' => 'Blando',
                    '2' => 'Rígido',
                ],
            ])
            ->add('graduacionIdgraduacion.cb', null, [
                'label' => 'Curva base',
            ])
            ->add('graduacionIdgraduacion.diam', null, [
                'label' => 'Diámetro',
            ])
            ->add('graduacionIdgraduacion.materialIdmaterial.nombre', null, [
                'label' => 'Material',
            ])
            ->add('graduacionIdgraduacion.disenolenteIddisenolente.nombre', null, [
                'label' => 'Diseño',
            ])
            ->add('graduacionIdgraduacion.tratamientoIdtratamiento.nombre', null, [
                'label' => 'Tratamiento',
            ])
            ->add('graduacionIdgraduacion.avsrxlejosod', null, [
                'label' => 'AV s/Rx Lejos OD',
            ])
            ->add('graduacionIdgraduacion.avsrxlejosoi', null, [
                'label' => 'AV s/Rx Lejos OI',
            ])
            ->add('graduacionIdgraduacion.avsrxcercaod', null, [
                'label' => 'AV s/Rx Cerca OD',
            ])
            ->add('graduacionIdgraduacion.avsrxcercaoi', null, [
                'label' => 'AV s/Rx Cerca OI',
            ])
            ->add('graduacionIdgraduacion.avsrxcvod', null, [
                'label' => 'AV s/Rx CV OD',
            ])
            ->add('graduacionIdgraduacion.avsrxcvoi', null, [
                'label' => 'AV s/Rx CV OI',
            ])
            ->add('graduacionIdgraduacion.esfodsf', null, [
                'label' => 'Subjetiva final Esfera OD',
            ])
            ->add('graduacionIdgraduacion.esfoisf', null, [
                'label' => 'Subjetiva final Esfera OI',
            ])
            ->add('graduacionIdgraduacion.cilodsf', null, [
                'label' => 'Subjetiva final Cilindro OD',
            ])
            ->add('graduacionIdgraduacion.ciloisf', null, [
                'label' => 'Subjetiva final Cilindro OI',
            ])
            ->add('graduacionIdgraduacion.ejeodsf', null, [
                'label' => 'Subjetiva final Eje OD',
            ])
            ->add('graduacionIdgraduacion.ejeoisf', null, [
                'label' => 'Subjetiva final Eje OI',
            ])
            ->add('graduacionIdgraduacion.avlodsf', null, [
                'label' => 'Subjetiva final AV lejos OD',
            ])
            ->add('graduacionIdgraduacion.avloisf', null, [
                'label' => 'Subjetiva final AV lejos OI',
            ])
            ->add('graduacionIdgraduacion.avcsaodsf', null, [
                'label' => 'Subjetiva final AV cerca s/Add OD',
            ])
            ->add('graduacionIdgraduacion.avcsaoisf', null, [
                'label' => 'Subjetiva final AV cerca s/Add OI',
            ])
            ->add('graduacionIdgraduacion.avccaodsf', null, [
                'label' => 'Subjetiva final AV cerca c/Add OD',
            ])
            ->add('graduacionIdgraduacion.avccaoisf', null, [
                'label' => 'Subjetiva final AV cerca c/Add OI',
            ])
            ->add('graduacionIdgraduacion.addsf', null, [
                'label' => 'Subjetiva final Add',
            ])
            ->add('graduacionIdgraduacion.sugerencias', null, [
                'label' => 'Sugerencias',
            ])

            // Fechas
            ->add('graduacionIdgraduacion.creacion', 'date', [
                'label'     => 'Fecha de creación',
                'format'    => 'd-m-Y  H:i a',
                'timezone'  => 'America/Mexico_City',
            ])
            ->add('graduacionIdgraduacion.actualizacion', 'date', [
                'label'     => 'Fecha de actualización',
                'format'    => 'd-m-Y  H:i a',
                'timezone'  => 'America/Mexico_City',
            ])
            ->add('fechaterminoflujo', 'date', [
                'label'     => 'Fecha de finalización',
                'format'    => 'd-m-Y  H:i a',
                'timezone'  => 'America/Mexico_City',
            ])

            // Información Adicional
            ->add('usuarioIdusuario.getNombreVendedor', null, [
                'label' => 'Optometrista',
            ])
            ->add('etapa', 'choice', [
                'label'   => 'Etapa',
                'choices' => [
                    1 => 'Registro datos Personales',
                    2 => 'Examen Visual',
                    3 => 'Orden de laboratorio',
                    4 => 'Venta',
                    5 => 'Firma',
                    6 => 'Entrega de lentes pendiente',
                    7 => 'Finalizado',
                ],
            ])

        ;
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->with("Datos del paciente",['class' => 'col-md-6 '])
            ->add('clienteIdcliente',   ModelListType::class,[
                'required' => true,
                'btn_list' => true,
                'label'=>"Cliente",
            ],['delete'])
            ->end()
            ->with("Examen visual",['class' => 'col-md-6 '])
            ->add('graduacionIdgraduacion',   ModelListType::class,[
                'required' => true,
                'btn_list' => true,
                'label'=>"Examen",
            ],['delete'])
            ->end()
            ->with("Orden de laboratorio",['class' => 'col-md-6 '])
            ->add('ordenLaboratorio',   EntityType::class, array('label' => 'Orden de laboratorio','required'=>false,
                'class'=>"App\Entity\Ordenlaboratorio",
                'mapped'=>false,
                'expanded' => true,

            ),
                array(

                    'query_builder' => function (\Doctrine\ORM\EntityRepository $repository) {
                        return $repository->createQueryBuilder('e')
                            ->where('e.status = ?1')
                            ->setParameter(1, 1)
                            //->add('orderBy', 'e.nombre ASC');

                            ;
                    }
                ))
            ->end()


        ;
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
        $query = parent::configureQuery($query);

        $rootAlias = current($query->getRootAliases());

        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );

        $query->setParameter('status', '1');
        $query->addOrderBy($rootAlias . '.creacion', 'DESC');

        return $query;
    }

    protected function configureRoutes(RouteCollectionInterface $collection): void
    {
        $collection->remove('create');
        $collection->add('sendEmail', $this->getRouterIdParameter() . '/sendEmail');
    }

    protected function configureBatchActions($actions): array
    {
        if (
            $this->hasRoute('edit') && $this->hasAccess('edit') &&
            $this->hasRoute('delete') && $this->hasAccess('delete')
        ) {
            $actions['delete'] = [
                'ask_confirmation' => true
            ];
        }
        return $actions;
    }

    protected function configureExportFields(): array
    {
        $exportFields = array(
            'Nombre' => 'nombrecompleto',
            'Empresa del cliente' => 'clienteIdcliente.empresaclienteIdempresacliente.nombre',
            'Unidad' => 'clienteIdcliente.unidadIdunidad.nombre',
            'Número de empleado' => 'clienteIdcliente.numeroempleado',
            'Teléfono' => 'clienteIdcliente.telefono',
            'Correo electrónico' => 'clienteIdcliente.email',
            'Anamnesis' => 'graduacionIdgraduacion.anamnesis',
            '¿Usa lentes actualmente? (Vacio => No, 0 => No, 1 => Si)' => 'graduacionIdgraduacion.isglassesuser',
            '¿Los trae consigo? (Vacio => No, 0 => No, 1 => Si)' => 'graduacionIdgraduacion.hasglassesnow',
            'Rx ant Esfera OD' => 'graduacionIdgraduacion.gpesferaod',
            'Rx ant Esfera OI' => 'graduacionIdgraduacion.gpesferaoi',
            'Rx ant Cilindro OD' => 'graduacionIdgraduacion.gpcilindrood',
            'Rx ant Cilindro OI' => 'graduacionIdgraduacion.gpcilindrooi',
            'Rx ant Eje OD' => 'graduacionIdgraduacion.gpejeod',
            'Rx ant Eje OI' => 'graduacionIdgraduacion.gpejeoi',
            'Rx ant Add' => 'graduacionIdgraduacion.gpaddod',
            'Rx ant AV lejos OD' => 'graduacionIdgraduacion.gpavlejosod',
            'Rx ant AV lejos OI' => 'graduacionIdgraduacion.gpavlejosoi',
            'Rx ant AV cerca OD' => 'graduacionIdgraduacion.gpavcercaod',
            'Rx ant AV cerca OI' => 'graduacionIdgraduacion.gpavcercaoi',
            'Tipo de Lente (Vacio => N/A, 0 => N/A, 1 => Armazón, 2 => Lente de contacto)' => 'graduacionIdgraduacion.tipolente',
            'Observaciones del armazón previo' => 'graduacionIdgraduacion.apobservaciones',
            'Tipo de lente de contacto (Vacio => N/A, 0 => N/A, 1 => Blando, 2 => Rígido)' => 'graduacionIdgraduacion.tipolentecontacto',
            'Curva base' => 'graduacionIdgraduacion.cb',
            'Diámetro' => 'graduacionIdgraduacion.diam',
            'Material' => 'graduacionIdgraduacion.materialIdmaterial.nombre',
            'Diseño' => 'graduacionIdgraduacion.disenolenteIddisenolente.nombre',
            'Tratamiento' => 'graduacionIdgraduacion.tratamientoIdtratamiento.nombre',
            'AV s/Rx Lejos OD' => 'graduacionIdgraduacion.avsrxlejosod',
            'AV s/Rx Lejos OI' => 'graduacionIdgraduacion.avsrxlejosoi',
            'AV s/Rx Cerca OD' => 'graduacionIdgraduacion.avsrxcercaod',
            'AV s/Rx Cerca OI' => 'graduacionIdgraduacion.avsrxcercaoi',
            'AV s/Rx CV OD' => 'graduacionIdgraduacion.avsrxcvod',
            'AV s/Rx CV OI' => 'graduacionIdgraduacion.avsrxcvoi',
            'Subjetiva final Esfera OD' => 'graduacionIdgraduacion.esfodsf',
            'Subjetiva final Esfera OI' => 'graduacionIdgraduacion.esfoisf',
            'Subjetiva final Cilindro OD' => 'graduacionIdgraduacion.cilodsf',
            'Subjetiva final Cilindro OI' => 'graduacionIdgraduacion.ciloisf',
            'Subjetiva final Eje OD' => 'graduacionIdgraduacion.ejeodsf',
            'Subjetiva final Eje OI' => 'graduacionIdgraduacion.ejeoisf',
            'Subjetiva final AV lejos OD' => 'graduacionIdgraduacion.avlodsf',
            'Subjetiva final AV lejos OI' => 'graduacionIdgraduacion.avloisf',
            'Subjetiva final AV cerca s/Add OD' => 'graduacionIdgraduacion.avcsaodsf',
            'Subjetiva final AV cerca s/Add OI' => 'graduacionIdgraduacion.avcsaoisf',
            'Subjetiva final AV cerca c/Add OD' => 'graduacionIdgraduacion.avccaodsf',
            'Subjetiva final AV cerca c/Add OI' => 'graduacionIdgraduacion.avccaoisf',
            'Subjetiva final Add' => 'graduacionIdgraduacion.addsf',
            'Sugerencias' => 'graduacionIdgraduacion.sugerencias',
            'Fecha de creación' => 'graduacionIdgraduacion.creacion',
            'Fecha de actualización' => 'graduacionIdgraduacion.actualizacion',
            'Optometrista' => 'usuarioIdusuario.nombre',
            'Sucursal' => 'usuarioIdusuario.sucursalIdsucursal.status',

        );

        return $exportFields;
    }

    public function getExportFormats(): array
    {
        return ['xlsx'];
    }

}