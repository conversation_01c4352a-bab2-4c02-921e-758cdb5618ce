<?php

declare(strict_types=1);

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;

final class CuponAdmin extends AbstractAdmin 
{
    protected function configureDatagridFilters(DatagridMapper $datagridMapper): void 
    {
        $datagridMapper
            ->add('codigo',null, array('label'=>'Código','show_filter' => true))
        ;
    }

    protected function configureListFields(ListMapper $listMapper): void
    {
        $listMapper
            ->add('codigo',null,array('label'=>'Código'))
            ->add('nombre',null,array('label'=>'Descripción'))
            ->add('porcentajedescuento',null,array('label'=>'Porcentaje de descuento'))
            ->add('numerocupones',null,array('label'=>'Número de cupones autorizados'))
            ->add('cuponesusados',null,array('label'=>'Número de cupones utilizados'))
            ->add('fechacreacion','date',array('label'=>'Fecha de creación', 'format' => 'd-m-Y  H:i a',
            'timezone' => 'America/Mexico_City'))
            ->add(ListMapper::NAME_ACTIONS, null, [ 'label' => "Opciones",
                'actions' => [
                    'show' => [],
                    'edit' => [],
                    'delete' => []
                ]
            ])
        ;
    }

    protected function configureFormFields(FormMapper $formMapper): void 
    {
        $formMapper
            ->with("-",['class' => 'col-md-6'])
                ->add('codigo', TextType::class,['label'=>"Código"])
                ->add('nombre', TextType::class,['label'=>"Descripción",  'required' => false])
            ->end()
    
            ->with("--",['class' => 'col-md-6'])
                ->add('porcentajedescuento',NumberType::class,['label'=>"Porcentaje de descuento"])
                ->add('numerocupones',IntegerType::class,['label'=>"Número de cupones autorizados"])
            ->end();
        ;
    }

    protected function configureShowFields(ShowMapper $showMapper): void {
        /*  
        $showMapper
            ->add('codigo',null,['label'=>"Código"])
            ->add('nombre',null,['label'=>"Descripción"])
            ->add('porcentajedescuento',null,['label'=>"Porcentaje de Descuento"])
            ->add('numerocupones',null,['label'=>"Número de Cupones Autorizados"])
            ->add('cuponesusados',null,['label'=>"Modelo"])
            ->add('fechacreacion',null,['label'=>"Modelo"])
        ;
        */
    }

    protected function configureExportFields(): array {
        return array(
            'Código'=>'codigo',
            'Descripción'=> 'nombre',
            'Porcentaje de descuento'=>'porcentajedescuento',
            'Número de cupones autorizados'=>'numerocupones',
            'Número de cupones usados'=>'cuponesusados',
            'Fecha de creación'=>'fechacreacion', 
        );
    }
    
    public function getExportFormats(): array {
        return ['xlsx'];
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
        $query = parent::configureQuery($query);
    
        $rootAlias = current($query->getRootAliases());
    
        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );
        $query->setParameter('status', '1');

        $query->addOrderBy($rootAlias . '.fechacreacion', 'DESC');
    
        return $query;
    }

    public function prePersist($object): void 
    {
        $object
            ->setFechacreacion(new \DateTime("now"))
            ->setFechaactualizacion(new \DateTime("now"))
        ;
    }
    public function preUpdate($object): void 
    {
        //$object->setCreacion(new \DateTime("now"));
        $object
            ->setFechaactualizacion(new \DateTime("now"))
        ;
    }

    public function toString($object): string {
        return $object instanceof Cupon
        ? $object->getCodigo()." ". $object->getNombre()
        : 'Codigo'; // shown in the breadcrumb on the create view
    }

}