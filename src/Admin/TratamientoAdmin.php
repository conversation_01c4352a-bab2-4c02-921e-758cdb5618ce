<?php
// src/Admin/CategoryAdmin.php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\MoneyType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Sonata\AdminBundle\Form\Type\ModelType;
use App\Entity\Tratamiento;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Sonata\AdminBundle\Route\RouteCollection;
use Sonata\CoreBundle\Validator\ErrorElement;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Sonata\AdminBundle\Route\RouteCollectionInterface;

final class TratamientoAdmin extends AbstractAdmin {
  
  public function toString($object): string {
    return $object instanceof Tratamiento
      ? $object->getNombre()
      : 'Tratamiento'; // shown in the breadcrumb on the create view
  }

  protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface{
    $query = parent::configureQuery($query);

    $rootAlias = current($query->getRootAliases());

    $query->andWhere(
        $query->expr()->eq($rootAlias . '.status', ':status')
    );
    $query->setParameter('status', '1');

    return $query;
  }

  protected function configureFormFields(FormMapper $formMapper): void {
    $formMapper
      ->with("-",['class' => 'col-md-6 '])
      ->add('nombre', TextType::class,array('label'=>'Nombre'))
      ->end()
      ->with(" ",['class' => 'col-md-6 '])
      ->add('precio',MoneyType::class,array(
        'label' => 'Precio',
        'currency'=>"MXN",
        'grouping'=>true,
        'attr' => array(
          'class'=>'form-control',
          'onkeydown' => "validarNumero(this);",
          'onkeyup' => "validarNumero(this);"
        )
      ))
      ->end()
    ;
  }

  protected function configureDatagridFilters(DatagridMapper $datagridMapper): void {
      $datagridMapper
          ->add('nombre',null, ['label' => 'Tratamiento', 'show_filter' => true,])
      ;
  }

  protected function configureListFields(ListMapper $listMapper): void {
    $listMapper
      ->addIdentifier('nombre',null,array('label'=>'Nombre'))
      ->add('Precio',null,array('label'=>'Precio'))
      ->add(ListMapper::NAME_ACTIONS, null, [ 'label' => "Opciones",
          'actions' => [
              'edit' => [],
              'delete' => []
          ]
      ])
    ;
  }

  public function preValidate($object): void {
    //var_dump($object);
    //    exit("");
    //quitar signos a precio
    $object->setPrecio(str_replace("$","",$object->getPrecio()));
    $object->setPrecio(str_replace(",","",$object->getPrecio()));
    /*return $object;*/
  }

  function prePersist($object): void { 
    /* return $object;*/
  }

  public function getExportFormats(): array {
    return ['xlsx'];
  }

  protected function configureExportFields(): array {
    return array(
        'Nombre'=> 'nombre',
        'Precio'=> 'precio',
    );
}
 
}
