<?php
// src/Admin/CategoryAdmin.php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use App\Entity\Membresia;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;

final class MembresiaAdmin extends AbstractAdmin 
{
    protected function configureDatagridFilters(DatagridMapper $datagridMapper): void 
    {
        /*  
        $datagridMapper->add('codigo')
            ->add('nombre')
            ->add('telefono')
            ->add('email')
            ->add('direccion')
            ->add('ciudad')
        ;
        */
    }

    protected function configureListFields(ListMapper $listMapper): void 
    {
        $listMapper
            ->addIdentifier('nombre')
            ->add('descuento')
        ;
    }

    protected function configureFormFields(FormMapper $formMapper): void 
    {
        $formMapper
            ->with("Membresia",['class' => 'col-md-6'])
                ->add('nombre', TextType::class,[])
            ->end()

            ->with(" ",['class' => 'col-md-6'])
                ->add('descuento', NumberType::class,[])
            ->end()
        ;
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
        $query = parent::configureQuery($query);
    
        $rootAlias = current($query->getRootAliases());
    
        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );

        $query->setParameter('status', '1');
    
        return $query;
    }

    protected function configureBatchActions($actions): array 
    {
      if (
            $this->hasRoute('edit') && $this->hasAccess('edit') &&
            $this->hasRoute('delete') && $this->hasAccess('delete')
      ) {
            $actions['delete'] = [
                'ask_confirmation' => true
            ];
        }

        return $actions;
    }

    public function toString($object): string 
    {
        return $object instanceof Membresia
        ? $object->getNombre()
        : 'Membresia'; // shown in the breadcrumb on the create view
    }
}