<?php

declare(strict_types=1);

namespace App\Admin;

use App\Entity\Marca;
use App\Entity\Sucursal;
use App\Entity\Stock;
use Doctrine\DBAL\Types\IntegerType;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Filter\Model\FilterData;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\AdminBundle\Form\Type\ModelType;
use Sonata\DoctrineORMAdminBundle\Filter\CallbackFilter;
use Sonata\DoctrineORMAdminBundle\Filter\StringListFilter;
use Sonata\Form\Type\DateRangePickerType;
use App\Entity\Usuarioempresapermiso;
use App\Entity\Empresa;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Sonata\DoctrineORMAdminBundle\Filter\ChoiceFilter;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Sonata\DoctrineORMAdminBundle\Filter\ModelAutocompleteFilter;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Sonata\DoctrineORMAdminBundle\Filter\DateTimeFilter;
use Sonata\AdminBundle\Form\Type\Filter\NumberType;
use Sonata\AdminBundle\Form\Type\Filter\TextType;
use Sonata\DoctrineORMAdminBundle\Filter\NumberFilter;
use Sonata\DoctrineORMAdminBundle\Filter\CountFilter;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;


final class StockAdmin extends AbstractAdmin
{
    private $tokenStorage;
    private $em;
    private $empresasg;
    private $report;
    protected $baseRouteName = "app";
    protected $baseRoutePattern = "stock";

    public $stockStates = [];

    public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage, string $report, EntityManagerInterface $em)
    {
        parent::__construct($code, $class, $baseControllerName);
        $this->tokenStorage = $tokenStorage;
        $this->baseRouteName = $report;
        $this->baseRoutePattern = $report;
        $this->report = $report;
        $this->em = $em;
        $this->stockStates = $this->loadStockStates();


    }
    public function loadStockStates()
    {
        $query = $this->em->createQuery(
            'SELECT st.name, st.idstockstate, st.status
           FROM App\Entity\Stockstate st
           '
        );
        $stockStates = $query->getResult();

        return $stockStates;
    }
    public function buscarEmpresas()
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $query = $this->em->createQuery(
            'SELECT e.idempresa
           FROM App\Entity\Usuarioempresapermiso up
           INNER JOIN up.empresaIdempresa e
           WHERE up.status =:status AND up.usuarioIdusuario =:idusuario'
        )->setParameters([
            'status' => "1",
            'idusuario' => $user->getIdusuario()
        ]);

        $empresas = $query->getResult();

        $this->empresasg = array_column($empresas, 'idempresa');
    }
    protected function configureDatagridFilters(DatagridMapper $datagridMapper): void
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $this->buscarEmpresas();

        $datagridMapper
            ->add('creacion', DateRangeFilter::class, array(
                'label' => "Fecha de alta",
                'show_filter' => true,
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('productoIdproducto.modelo', null, ['show_filter' => true, 'label' => "Modelo"])
            ->add('productoIdproducto.clave', null, ['label' => "Clave"])
            ->add('codigobarras', null, ['show_filter' => true, 'label' => "SKU"])
            ->add('serie', null, ['label' => "Numero de serie"])
            ->add('sucursalIdsucursal.empresaIdempresa', ModelFilter::class, [
                'label' => 'Empresas',
                'field_options' => [
                    'class' => 'App\Entity\Empresa',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        return $repository->createQueryBuilder('e')
                            ->andWhere('e.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('empresa', $this->empresasg)
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('sucursalIdsucursal', ModelFilter::class, [
                'label' => 'Sucursales',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('s')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('s.status = :status')
                            ->andWhere('s.tipo = :tipo')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('tipo', 'sucursal')
                            ->setParameter('empresa', $this->empresasg)
                            ->orderBy('s.nombre', 'ASC')
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('campana', ModelFilter::class, [
                'label' => 'Campañas',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('s')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('s.status = :status')
                            ->andWhere('s.tipo = :tipo')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('tipo', 'campaña')
                            ->setParameter('empresa', $this->empresasg)
                            ->orderBy('s.nombre', 'ASC')
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('bodega', ModelFilter::class, [
                'label' => 'Bodegas',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        return $repository->createQueryBuilder('s')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('s.status = :status')
                            ->andWhere('s.tipo = :tipo')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('tipo', 'bodega')
                            ->setParameter('empresa', $this->empresasg)
                            ->orderBy('s.nombre', 'ASC')
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('productoIdproducto.marcaIdmarca', ModelFilter::class, [
                'label' => "Marca",
                'field_options' => [
                    'class' => 'App\Entity\Marca',
                    'query_builder' => function (EntityRepository $repository) {
                        return $repository->createQueryBuilder('m')
                            ->andWhere('m.status = :status')
                            ->setParameters(['status' => '1'])
                            ->orderBy('m.nombre', 'ASC')
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('productoIdproducto.codigobarrasuniversal', null, ['show_filter' => true, 'label' => "UPC"])
            ->add('cantidad', NumberFilter::class, ['input_type' => 'integer', 'label' => 'Cantidad'])
            ->add('stockstateIdstockstate', ModelFilter::class, [
                'label' => "Estado",
                'field_options' => [
                    'class' => 'App\Entity\Stockstate',
                    'query_builder' => function (EntityRepository $repository) {
                        return $repository->createQueryBuilder('st')
                            ->andWhere('st.status = :status')
                            ->setParameters(['status' => '1'])
                            ->orderBy('st.name', 'ASC')
                        ;
                    },
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
        ;
    }

    protected function configureListFields(ListMapper $listMapper): void
    {
        $curReport = $this->report;
        $Usuario = $this->tokenStorage->getToken()->getUser();
        $listMapper
            ->add(ListMapper::NAME_ACTIONS, null, [
                'label' => "Opciones",
                'actions' => [
                    'detalleStock' => ['template' => 'CRUD/list__action_detalleStock.html.twig'],
                    'changeDefective' => ['template' => 'CRUD/list__action_defective.html.twig'],
                    'changePrecio' => ['template' => 'CRUD/list__action_price.html.twig'],
                ]
            ])
            ->addIdentifier('productoIdproducto.clave', null, ['label' => "Clave"])
            ->addIdentifier('productoIdproducto.marcaIdmarca.nombre', null, ['label' => "Marca"])
            ->addIdentifier('productoIdproducto.modelo', null, ['label' => "Modelo"])
            ->add('productoIdproducto.color', null, ['label' => "Color"])
            ->add('productoIdproducto.medidaIdmedida.nombre', null, ['label' => "Medida"])
            ->add('productoIdproducto.descripcion', null, ['label' => "Descripcion"])
            ->add('productoIdproducto.codigobarrasuniversal', null, ['label' => "UPC"])
            ->add('productoIdproducto.categoriaIdcategoria.claseIdclase.nombre', null, ['label' => "Categoría"])
            ->add('productoIdproducto.categoriaIdcategoria.nombre', null, ['label' => "Subcategoría"])
            ->add('codigobarras', null, ['label' => "SKU"])
            ->add('serie', null, ['label' => "Número de serie"])
            ->add('cantidad', null, ['label' => "Cantidad"])
            ->add('tipo', null, ['label' => 'Tipo'])
            ->add('apartados', null, ['label' => "Cantidad apartada"]);

        if ($Usuario->getRol() != 'ROLE_VENDEDOR') {
            $listMapper
                ->add('getIvaPrecio', null, ['label' => "IVA"])
                ->add('getPrecioMasIva', null, array('label' => 'Precio final'));
        }


        $listMapper
            ->add('sucursalIdsucursal.nombre', null, ['label' => "Sucursal"])
            ->add('creacion', 'date', [
                'label' => 'Fecha de alta',
                'format' => 'd-m-Y  H:i a',
                'timezone' => 'America/Mexico_City',
                'sortable' => 'creacion',
            ])
            ->add('stockstateIdstockstate', 'callback', [
                'label' => 'Estado',
                'callback' => function ($stockstate) {
                    return $stockstate ? $stockstate->getName() : 'N/A';
                }
            ]);

        if ($curReport != "stockAlmacen") {

            if ($Usuario->getRol() != 'ROLE_VENDEDOR')
            {$listMapper
            ->add('costo', 'callback', [
                'label' => 'Costo en inventario',
                'callback' => function ($value, $item) {
                    if ($value == 0.0) {
                        // Return a different display or modify the value, e.g., show a default or placeholder value
                        return 'productoIdproducto.costo'; // Modify as needed to fit the intended display
                    }
                    return $value; // Normally display the value
                },
                'sortable' => true,
            ]);}
            
            $listMapper
            ->add('precio', 'callback', [
                'label' => 'Precio sin IVA',
                'callback' => function ($value, $item) {
                    if ($value == 0.0) {
                        // Return a different display or modify the value, e.g., show a default or placeholder value
                        return 'productoIdproducto.precio'; // Modify as needed to fit the intended display
                    }
                    return $value; // Normally display the value
                },
                'sortable' => true,
            ]);
        }
    }

    protected function configureFormFields(FormMapper $formMapper): void
    {
    }

    protected function configureShowFields(ShowMapper $showMapper): void
    {
        $showMapper
            ->add('productoIdproducto.modelo', null, ['label' => "Modelo"])
            ->add('codigobarras', null, ['label' => "SKU"])
            ->add('productoIdproducto.precio', null, ['label' => "Precio"])
            ->add('sucursalIdsucursal.nombre', null, ['label' => "Sucursal"]);
    }

    protected function configureExportFields(): array
    {
        $curReport = $this->report;

        $exportFields = [
            'Modelo' => 'productoIdproducto.modelo',
            'Descripción' => 'productoIdproducto.descripcion',
            'Categoría' => 'productoIdproducto.categoriaIdcategoria.claseIdclase.nombre',
            'Subcategoría' => 'productoIdproducto.categoriaIdcategoria.nombre',
            'Medida' => 'productoIdproducto.medidaIdmedida.nombre',
            'UPC' => 'productoIdproducto.codigobarrasuniversal',
            'Número de serie' => 'serie',
            'Cantidad' => 'cantidad',
            '# Apartados' => 'apartados',
            'SKU' => 'codigobarras',
            'Costo' => 'productoIdproducto.costo',
            'Precio sin IVA' => 'productoIdproducto.precio',
            'IVA' => 'getIvaPrecio',
            'Precio Final' => 'getPrecioMasIva',
            'Sucursal' => 'sucursalIdsucursal.nombre',
            'Marca' => 'productoIdproducto.marcaIdmarca.nombre',
            'Fecha de creación' => 'CreatedAtForExport',
            'Estado' => 'stockStateName'
        ];

        if ($curReport != "stockAlmacen") {
            $exportFields["Precio especial"] = 'precio';
            $exportFields["Costo especial"] = 'costo';
        }

        return $exportFields;
    }

    public function getExportFormats(): array
    {
        return ['xlsx'];
    }

    protected function configureRoutes(RouteCollectionInterface $collection): void
    {
        // Removing the export route will disable exporting entities.
        $collection->remove('create');
        $collection->remove('delete');
        $collection->add('detalleStock', $this->getRouterIdParameter() . '/detalleStock');
        $collection->add('changePrecio', $this->getRouterIdParameter() . '/change-precio');

    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
        $Usuario = $this->tokenStorage->getToken()->getUser();
        $idusuario = $Usuario->getIdusuario();
        $mm = $this->getModelManager();

        // Subquery para obtener la empresa del usuario autenticado
        $subQueryBuilder = $mm->getEntityManager($this->getClass())->createQueryBuilder();
        $subQueryBuilder->select('empresa')
            ->from('App\Entity\Usuarioempresapermiso', 'eup')
            ->innerJoin('eup.empresaIdempresa', 'empresa')
            ->innerJoin('eup.usuarioIdusuario', 'u')
            ->where('u.idusuario = :idusuario')
            ->setParameter('idusuario', $idusuario);

        $query = parent::configureQuery($query);
        $rootAlias = current($query->getRootAliases());

        // Inner join a través de sucursal y empresa
        $query->innerJoin($rootAlias . '.sucursalIdsucursal', 's')
            ->innerJoin('s.empresaIdempresa', 'e');


        $query->andWhere($query->expr()->in("e", $subQueryBuilder->getDQL()));

        $query->innerJoin($rootAlias . '.productoIdproducto', 'p');
        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );
        $query->andWhere(
            $query->expr()->eq('p.tipoproducto', ':tipoproducto')
        );
        // Ahora pasamos el valor de idusuario al metodo setParameters
        $query->setParameters(['status' => "1", 'tipoproducto' => "1", 'idusuario' => $idusuario]);
        $query->addOrderBy($rootAlias . '.creacion', 'DESC');

        return $query;
    }


    public function prePersist($object): void
    {
        $object->setCreacion(new \DateTime("now"));
        $object->setModificacion(new \DateTime("now"));
    }

    public function preUpdate($object): void
    {
        //$object->setCreacion(new \DateTime("now"));
        $object->setModificacion(new \DateTime("now"));
    }

    public function toString($object): string
    {
        return $object instanceof Stock
            ? $object->getProductoIdproducto()->getModelo()
            : 'Stock'; // shown in the breadcrumb on the create view
    }
}
