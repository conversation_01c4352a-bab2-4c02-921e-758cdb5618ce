<?php

declare(strict_types=1);

namespace App\Admin;

use DateTime;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Sonata\Form\Type\DateRangePickerType;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Vich\UploaderBundle\Mapping\Annotation as Vich;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;

final class DocumentosAdmin extends AbstractAdmin
{

    private $ts;
    private $tokenStorage;
    private $em;
    private $empresasg;

    public function __construct( $ts,TokenStorageInterface $tokenStorage,EntityManagerInterface $em){
        $this->ts = $ts;
        $this->tokenStorage = $tokenStorage;
        $this->em = $em;

    }

    public function buscarEmpresas(){

        $user=$this->tokenStorage->getToken()->getUser();

        $query = $this-> em->createQuery(
            'SELECT e.idempresa
               FROM App\Entity\Usuarioempresapermiso up
               INNER JOIN up.empresaIdempresa e
               where up.status =:status and up.usuarioIdusuario =:idusuario
               '
        )->setParameters(['status'=>"1",'idusuario'=>$user->getIdusuario()]);
        $empresas= $query->getResult();


        $this->empresasg=$empresas;

    }

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $filter
            ->add('fechacreacion', DateRangeFilter::class, array(
                'label'=>"Fecha de carga",
                'show_filter' => true,
                'field_type' => DateRangePickerType::class,[
                    'dp_side_by_side'       => true,
                    'dp_use_current'        => true,
                    'dp_use_seconds'        => false,
                    'dp_collapse'           => true,
                    'dp_calendar_weeks'     => false,
                    'dp_view_mode'          => 'days',
                    'dp_min_view_mode'      => 'days',
                ]
            ))
            ->add('nombredocumento', null, array('label'=>'Nombre del documento','show_filter' => true,))
            ->add('usuarioIdusuario.sucursalIdsucursal.empresaIdempresa', ModelFilter::class, [
                'label' => 'Empresas',
                'field_options' => [
                    'class' => 'App\Entity\Empresa',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('e')
                            ->andWhere('e.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('empresa', $this->empresasg);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ],
            ])
            ->add('categoriadocumentosIdcategoriadocumentos', ModelFilter::class, [
                'show_filter' => true,
                'label' => 'Categoría',
                'field_options' => [
                    'query_builder' => function (EntityRepository $repository) {
                        return $repository->createQueryBuilder('c')
                            ->where('c.status = :status')
                            ->setParameter('status', 1);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true
                ]
            ])
            ->add('fechaactualizacion', DateRangeFilter::class, array(
                'label'=>"Fecha de actualización",
                'field_type' => DateRangePickerType::class,[
                    'dp_side_by_side'       => true,
                    'dp_use_current'        => true,
                    'dp_use_seconds'        => false,
                    'dp_collapse'           => true,
                    'dp_calendar_weeks'     => false,
                    'dp_view_mode'          => 'days',
                    'dp_min_view_mode'      => 'days',
                ]
            ))
            ->add('usuarioIdusuario', ModelFilter::class, [
                'label' => '¿Quién lo subió?',
                'field_options' => [
                    'query_builder' => function (EntityRepository $repository) {
                        return $repository->createQueryBuilder('u')
                            ->where('u.status = :status')
                            ->setParameter('status', 1);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true
                ]
            ])
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('nombredocumento', null, array('label'=>'Nombre del documento'))
            ->add('categoriadocumentosIdcategoriadocumentos', null, array('label'=>'Categoría'))
            ->add('fechacreacion', 'date', array('label'=>'Fecha de carga', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('fechaactualizacion', 'date', array('label'=>'Fecha de actualización', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('archivo', FileType::class, ['label'=>"Archivo"])
            ->add('usuarioIdusuario.nombre', null, array('label'=>'¿Quién lo subió?'))
            ->add('categoriadocumentosIdcategoriadocumentos.empresaIdempresa.nombre', null, array('label'=>'Empresa'))
            ->add(ListMapper::NAME_ACTIONS, null, [
                'label' => "Opciones",
                'actions' => [
                    'delete'=>[],
                    'visorDoc' => ['template' => 'abrir_visor/list-action-detalle-documentos.html.twig'],
                    'ligarSucursales' => ['template' => 'CRUD/list-action-sucursales.html.twig'],
                ],
            ])
        ;
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->add('nombredocumento', TextType::class,['label' => 'Nombre del documento','required' => true])
            ->add('categoriadocumentosIdcategoriadocumentos', ModelListType::class,['required' => true, 'btn_list' => true, 'label'=>"Categoria",],['delete'])
            ->add('file', FileType::class, array('data_class' => null, 'label'=>"Archivo", 'required' => true))
        ;
    }

    public function prePersist(object $image): void
    {
        // exit();
        $image->setFechacreacion(New \DateTime); // Se le agrega un valor con new DateTime diagonal invertida php
        $image->setFechaactualizacion(New \DateTime); // Se le agrega un valor con new DateTime diagonal invertida php
        $Usuario = $this->ts->getToken()->getUser();
        $image->setUsuarioIdusuario($Usuario);
        $this->manageFileUpload($image);
    }

    public function preUpdate(object $image): void
    {
        $this->manageFileUpload($image);
    }

    private function manageFileUpload(object $image): void
    {
        if ($image->getFile()) {
            $image->upload();
        }
    }

    protected function configureExportFields(): array {
        return array (
            'Nombre del documento'=> 'nombredocumento',
            'Categoría'=>'categoriadocumentosIdcategoriadocumentos',
            'Fecha de carga'=>'fechacreacion',
            'Fecha de actualización'=>'fechaactualizacion',
            'Archivo'=>'archivo',
            '¿Quién lo subió?'=>'usuarioIdusuario.nombre',
        );
    }

    public function getExportFormats():array
    {
        return ['xlsx'];
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {


        $Usuario = $this->tokenStorage->getToken()->getUser();
        $idusuario = $Usuario->getIdusuario();
        $mm = $this->getModelManager();

        // Subquery to get the company of the authenticated user
        $subQuery = $mm->getEntityManager($this->getClass())->createQueryBuilder();
        $subQuery->select('empresa')
            ->from('App\Entity\Usuarioempresapermiso', 'eup')
            ->innerJoin('eup.empresaIdempresa', 'empresa')
            ->innerJoin('eup.usuarioIdusuario', 'u')
            ->where('u.idusuario = :idusuario')
            ->setParameter('idusuario', $idusuario);

        $query = parent::configureQuery($query);
        $rootAlias = current($query->getRootAliases());

        // Inner join through category and company
        $query->innerJoin($rootAlias . '.categoriadocumentosIdcategoriadocumentos', 'c')
            ->innerJoin('c.empresaIdempresa', 'e');

        // Add a restriction to only show documents related to the company of the authenticated user
        $query->where($query->expr()->in("e", $subQuery->getDQL()));

        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );
        $query->setParameters(['status' => '1', 'idusuario' => $idusuario]);

        $rootAlias = current($query->getRootAliases());

        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );

        $query->setParameter('status', '1');

        return $query;
    }

    protected function configureRoutes(RouteCollectionInterface $collection):void {
        $collection->add('visorDoc', $this->getRouterIdParameter().'/visorDoc');
        $collection->add('ligarSucursales', $this->getRouterIdParameter() . '/ligarSucursales');
    }


}