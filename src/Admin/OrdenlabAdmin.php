<?php

declare(strict_types=1);

namespace App\Admin;

use App\Entity\Ordenlaboratorio;
use Doctrine\ORM\EntityRepository;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\DatagridInterface;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Sonata\Form\Type\DateRangePickerType;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Doctrine\ORM\EntityManagerInterface;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Sonata\DoctrineORMAdminBundle\Filter\ChoiceFilter;
use Sonata\AdminBundle\Form\Type\ChoiceFieldMaskType;
use Sonata\AdminBundle\Form\Type\ModelAutocompleteType;

    final class OrdenlabAdmin extends AbstractAdmin{
    private EntityManagerInterface $entityManager;

    public $orderStages = [
        'Orden creada',
        'Completar flujo',
        'Sin micas',
        'Micas asignadas',
        'Laboratorio Esperando Material',
        'Pendiente',
        'Procesando',
        'Calidad',
        'Terminado',
        'Pendiente de graduar',
        'Entregado'
    ];


    public function setEntityManager(EntityManagerInterface $entityManager): void
    {
        $this->entityManager = $entityManager;
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
        $query = parent::configureQuery($query);

        $rootAlias = current($query->getRootAliases());

        $query->leftJoin($rootAlias . '.stockventaordenlaboratorios', 'svol')
              ->leftJoin('svol.stockventaIdstockventa', 'sv')
              ->leftJoin('sv.ventaIdventa', 'v')
              ->leftJoin('v.sucursalIdsucursal', 's')
              ->leftJoin('v.tipoventaIdtipoventa', 'tv');

        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );

        $query->setParameter('status', '1');
        $query->addOrderBy($rootAlias . '.creacion', 'DESC');

        return $query;
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('creacion', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de creación de la orden",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('stockventaordenlaboratorios.stockventaIdstockventa.ventaIdventa.folio', null, [
                'label' => 'Folio de venta'
            ])
            ->add('stockventaordenlaboratorios.stockventaIdstockventa.ventaIdventa.fecha', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de venta",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('stockventaordenlaboratorios.stockventaIdstockventa.ventaIdventa.sucursalIdsucursal', ModelFilter::class, [
                'label' => "Sucursal de la venta",
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) {
                        return $repository->createQueryBuilder('s')
                            ->where('s.status = :status')
                            ->setParameter('status', '1')
                            ->orderBy('s.nombre', 'ASC');
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true
                ]
            ])
            ->add('stockventaordenlaboratorios.stockventaIdstockventa.ventaIdventa.tipoventaIdtipoventa', ModelFilter::class, [
                'label' => "Tipo de venta",
                'field_options' => [
                    'class' => 'App\Entity\Tipoventa',
                    'query_builder' => function (EntityRepository $repository) {
                        return $repository->createQueryBuilder('tv')
                            ->where('tv.status = :status')
                            ->setParameter('status', '1')
                            ->orderBy('tv.nombre', 'ASC');
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true
                ]
            ])
            ->add('actualizacion', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de actualización de la orden",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side' => true,
                    'dp_use_current' => true,
                    'dp_use_seconds' => false,
                    'dp_collapse' => true,
                    'dp_calendar_weeks' => false,
                    'dp_view_mode' => 'days',
                    'dp_min_view_mode' => 'days',
                ]
            ))
            ->add('nombreOptometrista', ModelFilter::class, [
                'label' => "Optometrista",
                'field_options' => [
                    'class' => 'App\Entity\Usuario',
                    'query_builder' => function (EntityRepository $repository) {
                        return $repository->createQueryBuilder('u')
                            ->where('u.status = :status')
                            ->setParameter('status', '1')
                            ->orderBy('u.nombre', 'ASC')
                        ;
                    },
                    'expanded' => false,
                    'multiple' => true
                ]
            ])
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add(ListMapper::NAME_ACTIONS, null, [
                'label' => 'Opciones',
                'actions' => [
                    'flujoe' => [
                        'changeStages' => ['template' => 'CRUD/list__action_changeStages.html.twig'],
                        'template' => 'dashboard_flujo_expediente/list-action-flujo2.html.twig',
                    ],
                    'delete' => [],
                ],
            ])
            ->add('stage', 'choice', [
                'label' => 'Estado',
                'choices' => [
                    '' => '',
                    '0' => '',
                    '1' => 'Orden creada',
                    '2' => 'Completar flujo',
                    '3' => 'Sin micas',
                    '4' => 'Micas asignada',
                    '5' => 'Laboratorio Esperando Material',
                    '6' => 'Pendiente',
                    '7' => 'Procesando',
                    '8' => 'Calidad',
                    '9' => 'Terminado',
                    '10'=> 'Pendiente de graduar',
                    '11'=> 'Entregado',
                ]
            ])
            ->add('getFoliosFromStocks', null, [
                'label' => 'Folio(s)',
                'mapped' => false,
                'sortable' => false,
                'virtual_field' => true,
                'template' => 'admin/ordenlab/get_folios_order.html.twig',
            ])
            ->add('clienteIdcliente', null, ['label' => 'Cliente'])
            ->add('beneficiarioDesdeVenta', null, ['label' => 'Beneficiario'])
            ->add('nombreOptometrista', null, ['label' => 'Optometrista'])
            ->add('sucursalNombreDesdeVenta', null, ['label' => 'Sucursal'])
            ->add('tipoVentaNombreDesdeVenta', null, ['label' => 'Tipo de venta'])
            ->add('creacion', 'date', array(
                'label' => 'Fecha de creación',
                'format' => 'd-m-Y H:i',
                'timezone' => 'America/Mexico_City'
            ))
            ->add('fechaDesdeVenta', 'date', [
                'label' => 'Fecha de venta',
                'format' => 'd-m-Y H:i',
                'timezone' => 'America/Mexico_City'
            ])
            ->add('esferaod', null, ['label' => 'Esfera OD'])
            ->add('esferaoi', null, ['label' => 'Esfera OI'])
            ->add('cilindrood', null, ['label' => 'Cilindro OD'])
            ->add('cilindrooi', null, ['label' => 'Cilindro OI'])
            ->add('ejeod', null, ['label' => 'Eje OD'])
            ->add('ejeoi', null, ['label' => 'Eje OI'])
            ->add('avcercacaddod', null, ['label' => 'Add OD'])
            ->add('avcercacaddoi', null, ['label' => 'Add OI'])
            ->add('dip', null, ['label' => 'DIP'])
            ->add('ao', null, ['label' => 'AO'])
            ->add('aco', null, ['label' => 'ACO'])
            ->add('cb', null, ['label' => 'Curva base'])
            ->add('diam', null, ['label' => 'Diámetro'])
            ->add('diagnosis', null, ['label' => 'Diagnostico'])
            ->add('notes', null, ['label' => 'Notas'])
            ->add('getTipoLenteContactoFormateado', null, ['label' => 'Tipo de lente de contacto'])
            ->add('getArmazonClienteFormateado', null, ['label' => 'Armazón propio del cliente'])
        ;
    }

    protected function configureExportFields(): array
    {
        $exportFields = array(
            'Estado' => 'stage',
            'Folio(s)' => 'getFoliosFromStocksForExport',
            'Cliente' => 'clienteIdcliente',
            'Beneficiario' => 'beneficiarioDesdeVenta',
            'Optometrista' => 'nombreOptometrista',
            'Sucursal' => 'sucursalNombreDesdeVenta',
            'Tipo de venta' => 'tipoVentaNombreDesdeVenta',
            'Fecha de creación' => 'creacion',
            'Fecha de venta' => 'fechaDesdeVenta',
            'Esfera OD' => 'esferaod',
            'Esfera OI' => 'esferaoi',
            'Cilindro OD' => 'cilindrood',
            'Cilindro OI' => 'cilindrooi',
            'Eje OD' => 'ejeod',
            'Eje OI' => 'ejeoi',
            'Add OD' => 'avcercacaddod',
            'Add OI' => 'avcercacaddoi',
            'DIP' => 'dip',
            'AO' => 'ao',
            'ACO' => 'aco',
            'Curva base' => 'cb',
            'Diámetro' => 'diam',
            'Diagnostico' => 'diagnosis',
            'Notas' => 'notes',
            'Tipo de lente de contacto' => 'getTipoLenteContactoFormateado',
            'Armazón propio del cliente' => 'getArmazonClienteFormateado',
        );

        return $exportFields;
    }

    public function getExportFormats(): array
    {
        return ['csv', 'xlsx'];
    }

    public function getFoliosFromStocks(Ordenlaboratorio $orden): array
    {
        try {
            $sql = "SELECT DISTINCT v.folio
                    FROM stockVentaOrdenLaboratorio svol
                    INNER JOIN stockVenta sv ON svol.stockVenta_idstockVenta = sv.idstockVenta
                    INNER JOIN venta v ON sv.venta_idventa = v.idventa
                    WHERE svol.ordenLaboratorio_idordenLaboratorio = ?
                    AND v.status = '1'
                    ORDER BY v.folio";

            $stmt = $this->entityManager->getConnection()->prepare($sql);
            $result = $stmt->executeQuery([$orden->getIdordenlaboratorio()])->fetchAllAssociative();

            return !empty($result) ? array_column($result, 'folio') : [];

        } catch (\Exception $e) {
            error_log("Error en getFoliosFromStocks: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Método para obtener folios formateados para exportación
     */
    public function getFoliosFromStocksForExport(Ordenlaboratorio $orden): string
    {
        $folios = $this->getFoliosFromStocks($orden);
        return !empty($folios) ? implode(', ', $folios) : 'Sin folios';
    }

    /**
     * Obtiene los productos de la venta relacionados con la orden
     */
    public function getProductosVentaForExport(Ordenlaboratorio $orden): string
    {
        try {
            $sql = "SELECT DISTINCT CONCAT(p.nombre, ' - ', p.modelo) as producto
                    FROM stockVentaOrdenLaboratorio svol
                    INNER JOIN stockVenta sv ON svol.stockVenta_idstockVenta = sv.idstockVenta
                    INNER JOIN venta v ON sv.venta_idventa = v.idventa
                    INNER JOIN stock s ON sv.stock_idstock = s.idstock
                    INNER JOIN producto p ON s.producto_idproducto = p.idproducto
                    WHERE svol.ordenLaboratorio_idordenLaboratorio = ?
                    AND v.status = '1'";

            $stmt = $this->entityManager->getConnection()->prepare($sql);
            $result = $stmt->executeQuery([$orden->getIdordenlaboratorio()])->fetchAllAssociative();

            if (!empty($result)) {
                return implode(', ', array_column($result, 'producto'));
            }

            return 'Sin productos';

        } catch (\Exception $e) {
            return 'Error al obtener productos';
        }
    }

    /**
     * Obtiene información completa del material y tratamiento
     */
    public function getMaterialTratamientoForExport(Ordenlaboratorio $orden): string
    {
        $material = $orden->getMaterialIdmaterial();
        $tratamiento = $orden->getTratamientoIdtratamiento();

        $info = [];
        if ($material) {
            $info[] = 'Material: ' . $material->getNombre();
        }
        if ($tratamiento) {
            $info[] = 'Tratamiento: ' . $tratamiento->getNombre();
        }

        return !empty($info) ? implode(' | ', $info) : 'Sin especificar';
    }

    /**
     * Obtiene el tipo de lente de contacto formateado
     */
    public function getTipoLenteContactoFormateado(Ordenlaboratorio $orden): string
    {
        $tipo = $orden->getTipolentecontacto();

        switch ($tipo) {
            case '1':
                return 'Blando';
            case '2':
                return 'Rígido';
            default:
                return 'No aplica';
        }
    }

    /**
     * Obtiene el tipo de orden formateado
     */
    public function getTipoOrdenFormateado(Ordenlaboratorio $orden): string
    {
        $tipo = $orden->getTipoorden();

        switch ($tipo) {
            case '1':
                return 'Armazón';
            case '2':
                return 'Lente de contacto';
            default:
                return 'No Aplica';
        }
    }

    /**
     * Configuración para ordenamiento por defecto
     */
    protected function configureDefaultSortValues(array &$sortValues): void
    {
        $sortValues[DatagridInterface::SORT_ORDER] = 'DESC';
        $sortValues[DatagridInterface::SORT_BY] = 'creacion';
    }

    /**
     * Configuración para mostrar más registros por página
     */
    protected function configureDefaultFilterValues(array &$filterValues): void
    {
        $filterValues['stage'] = ['value' => '1'];
    }
}
