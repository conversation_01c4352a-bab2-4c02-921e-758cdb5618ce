<?php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\AdminBundle\Form\Type\ModelListType;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Sonata\Form\Type\DateRangePickerType;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Sonata\DoctrineORMAdminBundle\Filter\ChoiceFilter;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Mime\Email;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\Mime\Address;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\Regex;
use Vich\UploaderBundle\Form\Type\WysiwygType;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\File\File;
use Sonata\AdminBundle\Admin\Pool;
use Symfony\Component\DependencyInjection\ContainerInterface;


final class AnunciosAdmin extends AbstractAdmin
{



    private $mailer;
    private $tokenStorage;

    private $em;
    private $empresasg;
    private $container; // Inyectar el contenedor


    public function __construct(MailerInterface $mailer, TokenStorageInterface $tokenStorage,EntityManagerInterface $em,ContainerInterface $container)

    {
        $this->container = $container;
        $this->mailer = $mailer;
        $this->tokenStorage = $tokenStorage;
        $this->em = $em;


    }

    public function buscarEmpresas(){

        $user=$this->tokenStorage->getToken()->getUser();

        $query = $this-> em->createQuery(
            'SELECT e.idempresa
               FROM App\Entity\Usuarioempresapermiso up
               INNER JOIN up.empresaIdempresa e
               where up.status =:status and up.usuarioIdusuario =:idusuario
               '
        )->setParameters(['status'=>"1",'idusuario'=>$user->getIdusuario()]);
        $empresas= $query->getResult();


        $this->empresasg=$empresas;

    }

    protected function configureDatagridFilters(DatagridMapper $datagridMapper): void
    {
        $user = $this->tokenStorage->getToken()->getUser();

        $datagridMapper
            ->add('sucursalIdsucursal.empresaIdempresa', ModelFilter::class, [
                'label' => 'Empresas',
                'field_options' => [
                    'class' => 'App\Entity\Empresa',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('e')
                            ->andWhere('e.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('empresa', $this->empresasg)
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('titulo', null, array('label'=>'Titulo'))
            ->add('fechacreacion', DateRangeFilter::class, array(
                'label'=>"Fecha de creación",
                'show_filter' => true,
                'field_type' => DateRangePickerType::class,[
                    'dp_side_by_side'       => true,
                    'dp_use_current'        => true,
                    'dp_use_seconds'        => false,
                    'dp_collapse'           => true,
                    'dp_calendar_weeks'     => false,
                    'dp_view_mode'          => 'days',
                    'dp_min_view_mode'      => 'days',
                ]
            ))
            ->add('fechaactualizacion', DateRangeFilter::class, array(
                'label'=>"Fecha de actualización",
                'field_type' => DateRangePickerType::class,[
                    'dp_side_by_side'       => true,
                    'dp_use_current'        => true,
                    'dp_use_seconds'        => false,
                    'dp_collapse'           => true,
                    'dp_calendar_weeks'     => false,
                    'dp_view_mode'          => 'days',
                    'dp_min_view_mode'      => 'days',
                ]
            ))
            ->add('categoriaanuncioIdcategoriaanuncio', ModelFilter::class, [
                'label' => 'Categoría',
                'show_filter' => true,
                'field_options' => [
                    'query_builder' => function (EntityRepository $repository) {
                        return $repository->createQueryBuilder('c')
                            ->where('c.status = :status')
                            ->setParameter('status', 1)
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true
                ]
            ])
            ->add('enviocorreo', ChoiceFilter::class, [
                'label' => 'Enviado',
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        'No' => '0', 'Si' => '1'
                    ],
                ],
            ])
        ;
    }

    public function getPlainText($entity)
    {
        return strip_tags($entity->getTexto());
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('titulo', null, array('label'=>'Titulo'))
            ->add('fechacreacion', 'date', array('label'=>'Fecha de carga', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('fechaactualizacion', 'date', array('label'=>'Fecha de actualización', 'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('enviocorreo', 'choice', array('label'=>'Enviado', 'choices' => [
                '1' => 'Si',
                '0' => 'No'
            ]))
            ->add('empresaIdempresa.nombre', null, array('label'=>'Empresa'))
            ->add('categoriaanuncioIdcategoriaanuncio', null, array('label'=>'Categoría'))
            ->add('_action', 'actions', [
                'label' => "Opciones",
                'actions' => [
                    'edit' => [],
                    'delete' => [],
                    'visorAnuncio' => ['template' => 'visor_anuncio/list-action-detalle-anuncio.html.twig'],
                ],
            ]);
    }

        protected function configureFormFields(FormMapper $form): void
        {
            $form
                ->add('titulo', TextType::class,['label' => 'Titulo', 'required' => true])
                ->add('texto', TextareaType::class, [
                    'label' => 'Texto',
                    'required' => false,
                    'attr' => [
                        'class' => 'tiny',
                        'tinymce' => true,
                        'data-theme' => 'advanced',
                        'novalidate' => 'novalidate',
                    ],
                    'property_path' => 'texto',
                    'mapped' => true,
                ])
                //->add('texto', TextType::class,['label' => 'Titulo', 'required' => false])
                ->add('categoriaanuncioIdcategoriaanuncio', ModelListType::class,['required' => true, 'btn_list' => true, 'label'=>"Categoria",],['delete'])
                ->add('empresaIdempresa', ModelListType::class, ['required' => true, 'btn_list' => true, 'label'=>"Empresa",], ['delete'])
                ->add('enviocorreo', ChoiceType::class, [
                    'label' => 'Enviar Correo',
                    'choices' => [
                        'No' => '0',
                        'Si' => '1'
                    ],
                    'expanded' => true, // Agrega esta opción para que el campo sea un checkbox
                    'multiple' => false, // Agrega esta opción para que solo se pueda seleccionar una opción
                    'required' => true // Puedes establecer si el campo es requerido o no
                ])
                ->add('imagenPrincipal', FileType::class, [
                    'data_class' => null,
                    'label'      => 'Archivo',
                    'required'   => false,
                    'property_path' => 'file',
                ])
            ;
        }

    public function prePersist($object): void
    {
        $object->upload($this->container->getParameter('carpetaAnuncios'));
        $object->setFechacreacion(New \DateTime);
        $object->setFechaactualizacion(New \DateTime);
    }

    public function preUpdate($object): void
    {
        $object->upload($this->container->getParameter('carpetaAnuncios'));
    }



    private function manageFileUpload(object $image): void
    {
        if ($image->getImagenprincipal()) {
            $image->upload();
        }
    }

    public function getExportFormats():array
    {
        return ['xlsx'];
    }

    protected function configureExportFields(): array {
        return array (
            'Titulo'=> 'titulo',
            'Fecha de carga'=>'fechacreacion',
            'Fecha de actualización'=>'fechaactualizacion',
            'Texto'=>'texto',
            'Enviado'=>'enviocorreo',
            'Categoría'=>'categoriaanuncioIdcategoriaanuncio',
        );
    }

    public function toString($object): string
    {
        return $object instanceof Categoriaanuncio
        ? $object->getTitulo()
        : 'Titulo'; // shown in the breadcrumb on the create view
    }


    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {

        $Usuario=$this->tokenStorage->getToken()->getUser();

        $idusuario=$Usuario->getIdusuario();
        $mm = $this->getModelManager();
        $query = parent::configureQuery($query);
        $queryBuilder = $mm->getEntityManager($this->getClass())->createQueryBuilder();

        // Realiza una consulta para obtener la empresa del usuario autenticado.
        $queryBuilder->select('empresa')
            ->from('App\Entity\Usuarioempresapermiso', 'eup')
            ->innerJoin('eup.empresaIdempresa', 'empresa')
            ->innerJoin('eup.usuarioIdusuario', 'u')
            ->where('u.idusuario='.$idusuario);

        $rootAlias = current($query->getRootAliases());

        // Se une a la empresa a través de la relación de anuncio y empresa.
        $query->innerJoin($rootAlias.'.empresaIdempresa', 'e');

        // Añade una restricción para que solo se muestren los anuncios relacionados con la empresa del usuario autenticado.
        $query->andWhere($query->expr()->in('e', $queryBuilder->getDQL()));

        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );
        $query->setParameter('status', '1');

        $query = parent::configureQuery($query);

        $rootAlias = current($query->getRootAliases());

        $query->andWhere(
        $query->expr()->eq($rootAlias . '.status', ':status')
        );

        $query->setParameter('status', '1');

        return $query;
    }
}