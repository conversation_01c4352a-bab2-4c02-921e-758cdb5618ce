<?php

declare(strict_types=1);

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Form\Type\ModelAutocompleteType;
use Sonata\AdminBundle\Show\ShowMapper;
use Sonata\DoctrineORMAdminBundle\Filter\ModelAutocompleteFilter;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;


final class VentacuponAdmin extends AbstractAdmin
{

    private $tokenStorage;

    public function __construct(?string $code = null, ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage)
    {
      parent::__construct($code, $class, $baseControllerName);
      $this->tokenStorage = $tokenStorage;
    }
    
    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface{

        $Usuario=$this->tokenStorage->getToken()->getUser();
        $idusuario=$Usuario->getIdusuario();
        $mm = $this->getModelManager();
        $query = parent::configureQuery($query);
        $queryBuilder = $mm->getEntityManager($this->getClass())->createQueryBuilder();

        // Realiza una consulta para obtener la empresa del usuario autenticado.
        $queryBuilder->select('empresa')
            ->from('App\Entity\Usuarioempresapermiso', 'eup')
            ->innerJoin('eup.empresaIdempresa', 'empresa')
            ->innerJoin('eup.usuarioIdusuario', 'u')
            ->where('u.idusuario='.$idusuario);
    
        $rootAlias = current($query->getRootAliases());
    
        // Se une a la empresa a través de la relación de categoría, clase y empresa.
        $query->innerJoin($rootAlias.'.ventaIdventa', 've')
              ->innerJoin('ve.sucursalIdsucursal', 'su')
              ->innerJoin('su.empresaIdempresa', 'e');
    
        // Añade una restricción para que solo se muestren los productos relacionados con la empresa del usuario autenticado.
        $query->where($queryBuilder->expr()->in("e", $queryBuilder->getDQL()));
        
        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );
        $query->setParameter('status', '1'); 


        $query = parent::configureQuery($query);

        $rootAlias = current($query->getRootAliases());
        $query->innerJoin($rootAlias.'.ventaIdventa', 'v');
        $query->andWhere(
            'v.cotizacion!=:cotizacion and '.$rootAlias.".status=:status"
        );
        $query->setParameters(['cotizacion'=>'1','status'=>"1"]);

        return $query;
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('cuponIdcupon.codigo', null, array('label' => 'Cupón', 'show_filter' => true,))
            ->add('ventaIdventa.folio', null, array('label' => 'Folio', 'show_filter' => true,))
        ;
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->add('cuponIdcupon.codigo',null,array('label'=>'Cupón'))
            ->add('ventaIdventa.folio',null,array('label'=>'Folio'))
            ->add('ventaIdventa.total',null,array('label'=>'Venta total'))
            ->add('ventaIdventa.fecha',null,array('label'=>'Fecha de venta' ,'format' => 'd-m-Y  H:i a', 'timezone' => 'America/Mexico_City'))
            ->add('ventaIdventa.clienteIdcliente.nombre',null,array('label'=>'Cliente'))
        ;
        

    }

    protected function configureFormFields(FormMapper $form): void
    {
        $form
            ->add('idventacupon')
            ->add('fechacreacion')
            ->add('fechaactualizacion')
            ->add('porcentajedescuento')
            ->add('status')
            ;
    }

    protected function configureShowFields(ShowMapper $show): void
    {
        $show
            ->add('idventacupon')
            ->add('fechacreacion')
            ->add('fechaactualizacion')
            ->add('porcentajedescuento')
            ->add('status')
            ;
    }
    protected function configureExportFields(): array {
        return array(
            'Cupón'=> 'cuponIdcupon.codigo',
            'Número de ticket'=>'ventaIdventa.folio',
            'Venta total'=>'ventaIdventa.total',
            'Fecha de venta'=>'ventaIdventa.fecha',
            'Cliente'=>'ventaIdventa.clienteIdcliente.nombre'

        );
    }

    public function getExportFormats(): array {
        return ['xlsx'];
    }
}
