<?php
// src/Admin/CategoryAdmin.php

namespace App\Admin;

use App\Entity\Disenolente;
use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;

final class DisenolenteAdmin extends AbstractAdmin 
{
    protected function configureDatagridFilters(DatagridMapper $datagridMapper) : void 
    {
        $datagridMapper
            ->add('nombre',null, ['label' => 'Diseño de lentes', 'show_filter' => true,])
        ;
    }

    protected function configureListFields(ListMapper $listMapper) : void 
    {
        $listMapper
            ->addIdentifier('nombre')
            ->add(ListMapper::NAME_ACTIONS, null, [ 'label' => "Opciones",
            'actions' => [
                'edit' => [],
                'delete' => []
            ]
        ])
        ;
    }

    protected function configureFormFields(FormMapper $formMapper): void 
    {
        $formMapper
            ->with("-",['class' => 'col-md-6'])
                ->add('nombre', TextType::class,[])
            ->end()

            ->with(" ",['class' => 'col-md-6'])
            ->end()
        ;
    }
    
    protected function configureExportFields(): array 
    {
        return array(
            'Nombre'=> 'nombre',
        );
    }

    public function getExportFormats(): array 
    {
        return ['xlsx'];
    }

    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface
    {
        $query = parent::configureQuery($query);
    
        $rootAlias = current($query->getRootAliases());
    
        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );

        $query->setParameter('status', '1');
    
        return $query;
    }

    protected function configureBatchActions($actions): array 
    {
      if (
        $this->hasRoute('edit') && $this->hasAccess('edit') &&
        $this->hasRoute('delete') && $this->hasAccess('delete')
      ) {
            $actions['delete'] = [
                'ask_confirmation' => true
            ];
        }
        return $actions;
    }

    

    public function toString($object): string 
    {
        return $object instanceof Disenolente
        ? $object->getNombre()
        : 'Diseño Lente'; // shown in the breadcrumb on the create view
    }
}
