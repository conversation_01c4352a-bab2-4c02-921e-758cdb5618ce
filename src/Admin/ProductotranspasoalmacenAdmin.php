<?php
// src/Admin/CategoryAdmin.php

namespace App\Admin;

use Sonata\AdminBundle\Admin\AbstractAdmin;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Form\FormMapper;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use App\Entity\Membresia;
use App\Entity\Productostranspasoalmacen;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Sonata\DoctrineORMAdminBundle\Filter\ModelFilter;
use Sonata\AdminBundle\Datagrid\ProxyQueryInterface;
use Doctrine\ORM\EntityRepository;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Sonata\DoctrineORMAdminBundle\Filter\ChoiceFilter;
use App\Repository\ProductoTranspasoAlmacenRepository;
use Sonata\AdminBundle\Route\RouteCollection;
use Sonata\AdminBundle\Filter\FilterInterface;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Sonata\AdminBundle\Form\Type\Filter\DateRangeType;
use Sonata\Form\Type\DateRangePickerType;

final class ProductotranspasoalmacenAdmin extends AbstractAdmin {
    private $tokenStorage;
    private $em;
    private $empresasg;
    protected $productotranspasoalmacenRepository;

    public function __construct(?string $code = null,
    ?string $class = null, ?string $baseControllerName = null, TokenStorageInterface $tokenStorage, EntityManagerInterface $em)

{
    parent::__construct($code, $class, $baseControllerName);
    $this->tokenStorage = $tokenStorage;
    $this->em = $em;

}

public function buscarEmpresas() {
    $user=$this->tokenStorage->getToken()->getUser();

    $query = $this-> em->createQuery(
        'SELECT e.idempresa
           FROM App\Entity\Usuarioempresapermiso up
           INNER JOIN up.empresaIdempresa e
           where up.status =:status and up.usuarioIdusuario =:idusuario
           '
    )->setParameters(['status'=>"1",'idusuario'=>$user->getIdusuario()]);
    $empresas= $query->getResult();

    $this->empresasg=$empresas;
}

    protected function configureDatagridFilters(DatagridMapper $datagridMapper): void {
        $user = $this->tokenStorage->getToken()->getUser();

        $datagridMapper
            ->add('transpasoalmacenIdtranspasoalmacen.creacion', DateRangeFilter::class, array(
                'show_filter' => true,
                'label' => "Fecha de traspaso",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side'       => true,
                    'dp_use_current'        => true,
                    'dp_use_seconds'        => false,
                    'dp_collapse'           => true,
                    'dp_calendar_weeks'     => false,
                    'dp_view_mode'          => 'days',
                    'dp_min_view_mode'      => 'days',
                ]
            ))
            ->add('stockIdstock.sucursalIdsucursal.empresaIdempresa', ModelFilter::class, [
                'label' => 'Empresas',
                'field_options' => [
                    'class' => 'App\Entity\Empresa',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('e')
                            ->andWhere('e.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', '1')
                            ->setParameter('empresa', $this->empresasg)
                        ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('stockIdstock.productoIdproducto.modelo', null, array('label' => 'Modelo', 'show_filter' => true))
            ->add('stockIdstock.codigobarras', null, array('label' => 'SKU', 'show_filter' => true))
            ->add('stockIdstock.productoIdproducto.codigobarrasuniversal', null, array('label' => 'UPC', 'show_filter' => true))
            ->add('transpasoalmacenIdtranspasoalmacen.modificacion', DateRangeFilter::class, array(
                'label' => "Fecha de actualización",
                'field_type' => DateRangePickerType::class, [
                    'dp_side_by_side'       => true,
                    'dp_use_current'        => true,
                    'dp_use_seconds'        => false,
                    'dp_collapse'           => true,
                    'dp_calendar_weeks'     => false,
                    'dp_view_mode'          => 'days',
                    'dp_min_view_mode'      => 'days',
                ]
            ))
            ->add('stockIdstock.cantidad', ChoiceFilter::class, [
                'label' => 'En existencia',
                'field_type' => ChoiceType::class,
                'field_options' => [
                    'choices' => [
                        'En existencia' => '1',
                        'Sin existencia' => '<0',
                    ],
                    'placeholder' => 'Seleccione una opción',
                ]
            ])
            ->add('transpasoalmacenIdtranspasoalmacen.sucursalIdsucursalorigen', ModelFilter::class, [
                'label'=>'Sucursal de origen',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('so')
                            ->andWhere('so.status = :status')
                            ->andWhere('IDENTITY(so.empresaIdempresa) IN (:empresa)')
                            ->setParameter('status', 1)
                            ->setParameter('empresa', $this->empresasg)
                            ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('transpasoalmacenIdtranspasoalmacen.sucursalIdsucursaldestino', ModelFilter::class, [
                'label'=>'Sucursal de destino',
                'field_options' => [
                    'class' => 'App\Entity\Sucursal',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('sd')
                            ->andWhere('sd.status = :status')
                            ->andWhere('IDENTITY(sd.empresaIdempresa) IN (:empresa)')
                            ->setParameter('status', 1)
                            ->setParameter('empresa', $this->empresasg)
                            ;
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
            ->add('transpasoalmacenIdtranspasoalmacen.usuarioIdusuario', ModelFilter::class, [
                'label'=>'Responsable',
                'field_options' => [
                    'class' => 'App\Entity\Usuario',
                    'query_builder' => function (EntityRepository $repository) use ($user) {
                        $this->buscarEmpresas();
                        return $repository->createQueryBuilder('u')
                            ->join('u.sucursalIdsucursal', 's')
                            ->join('s.empresaIdempresa', 'e')
                            ->andWhere('u.status = :status')
                            ->andWhere('e.idempresa IN (:empresa)')
                            ->setParameter('status', 1)
                            ->setParameter('empresa', $this->empresasg);
                    },
                    'choice_label' => 'nombre',
                    'expanded' => false,
                    'multiple' => true,
                ]
            ])
        ;
    }

    protected function configureListFields(ListMapper $listMapper): void {
        $listMapper
            ->add('stockIdstock.sucursalIdsucursal.empresaIdempresa', null, array('label' => 'Empresa',))
            ->add('transpasoalmacenIdtranspasoalmacen.creacion', 'date', array('label' => 'Fecha de traspaso', 'format' => 'd-m-Y  H:i ', 'timezone' => 'America/Mexico_City'))
            ->add('transpasoalmacenIdtranspasoalmacen.notas', null, array('label' => 'Nota'))
            ->add('aceptada', 'text', [
                'label' => 'Estado',
                'template' => 'admin/custom_field.html.twig',
                'sortable' => false,])
            ->add('transpasoalmacenIdtranspasoalmacen.sucursalIdsucursalorigen.nombre', null, array('label' => 'Sucursal de origen'))
            ->add('transpasoalmacenIdtranspasoalmacen.sucursalIdsucursaldestino.nombre', null, array('label' => 'Sucursal de destino'))
            ->add('transpasoalmacenIdtranspasoalmacen.usuarioIdusuario.nombre', null, array('label' => 'Responsable'))
            ->add('stockIdstock.productoIdproducto.modelo', null, array('label' => 'Modelo'))
            ->add('stockIdstock.codigobarras', null, array('label' => 'SKU'))
            ->add('stockIdstock.productoIdproducto.codigobarrasuniversal', null, array('label' => 'UPC'))
            ->add('cantidad', null, array('label' => 'Cantidad'))
            ->add('stockIdstock.productoIdproducto.marcaIdmarca.nombre', null, array('label' => 'Marca'))
        ; 
    }

    protected function configureFormFields(FormMapper $formMapper): void {
        
    }

    protected function configureExportFields(): array {
        return array(
            'Empresa' => 'stockIdstock.sucursalIdsucursal.empresaIdempresa',
            'Fecha de traspaso' => 'transpasoalmacenIdtranspasoalmacen.creacion',
            'Nota' => 'transpasoalmacenIdtranspasoalmacen.notas',
            'Estado' => 'aceptada',
            'Sucursal de origen' => 'transpasoalmacenIdtranspasoalmacen.sucursalIdsucursalorigen.nombre',
            'Sucursal de destino' => 'transpasoalmacenIdtranspasoalmacen.sucursalIdsucursaldestino.nombre',
            'Responsable' => 'transpasoalmacenIdtranspasoalmacen.usuarioIdusuario.nombre',
            'Modelo' => 'stockIdstock.productoIdproducto.modelo',
            'SKU' => 'stockIdstock.codigobarras',
            'Código de barras universal' => 'stockIdstock.productoIdproducto.codigobarrasuniversal',
            'Cantidad' => 'cantidad',
            'Marca' => 'stockIdstock.productoIdproducto.marcaIdmarca.nombre'
        );
    }
    
    
    public function getExportFormats(): array {
        return ['xlsx'];
    }

    protected function configureRoutes(RouteCollectionInterface $collection): void {
        $collection->remove('create');
        $collection->remove('delete');
    }
    
    protected function configureQuery(ProxyQueryInterface $query): ProxyQueryInterface {
        $Usuario=$this->tokenStorage->getToken()->getUser();
        $idusuario=$Usuario->getIdusuario();
        $mm = $this->getModelManager();
        $query = parent::configureQuery($query);
        $queryBuilder = $mm->getEntityManager($this->getClass())->createQueryBuilder();

        $queryBuilder->select('empresa')
        ->from('App\Entity\Usuarioempresapermiso', 'eup')
        ->innerJoin('eup.empresaIdempresa', 'empresa')
        ->innerJoin('eup.usuarioIdusuario', 'u')
        ->where('u.idusuario='.$idusuario);

        $rootAlias = current($query->getRootAliases());

        // Se une a la empresa a través de la relación de categoría, clase y empresa.
        $query->innerJoin($rootAlias.'.transpasoalmacenIdtranspasoalmacen', 'tr')
            ->innerJoin('tr.sucursalIdsucursalorigen', 'su')
            ->innerJoin('su.empresaIdempresa', 'e');

        // Añade una restricción para que solo se muestren los productos relacionados con la empresa del usuario autenticado.
        $query->where($queryBuilder->expr()->in("e", $queryBuilder->getDQL()));
        
        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );
        $query->setParameter('status', '1');

        $query = parent::configureQuery($query);
    
        $rootAlias = current($query->getRootAliases());
    
        $query->andWhere(
            $query->expr()->eq($rootAlias . '.status', ':status')
        );

        $query->orderBy('tr.creacion', 'DESC');

        $query->setParameter('status', '1');

        return $query;
    }

    protected function configureBatchActions($actions): array {
      if (
        $this->hasRoute('edit') && $this->hasAccess('edit') &&
        $this->hasRoute('delete') && $this->hasAccess('delete')
      ) {
        $actions['delete'] = [
            'ask_confirmation' => true
        ];
      }
      return $actions;
    }

    public function toString($object): string {
        return $object instanceof Productostranspasoalmacen
        ? $object->getNotas()
        : 'Producto Traspaso Almacen'; // shown in the breadcrumb on the create view
    }

    public function getAceptada($idtranspasoalmacen) {

        $repository = $this->em->getRepository(Productostranspasoalmacen::class);
        
        return $repository->obtenerAceptadaPorProducto($idtranspasoalmacen);
    }  
}
