<?php

// src/Service/FileUploader.php
namespace App\Service;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Http\Authentication\AuthenticationSuccessHandlerInterface;

class RedirectService
{
    private $router;
    private $authorizationChecker;

    public function __construct(RouterInterface $router, AuthorizationCheckerInterface $authorizationChecker)
    {
        $this->router = $router;
        $this->authorizationChecker = $authorizationChecker;
    }

    public function getRedirect(): RedirectResponse
    {
        if ($this->authorizationChecker->isGranted('ROLE_ADMIN')) {
            return new RedirectResponse($this->router->generate('sonata_admin_dashboard'));
        } elseif ($this->authorizationChecker->isGranted('ROLE_SUPERVISOR')) {
            return new RedirectResponse($this->router->generate('app_oficial'));
        } elseif ($this->authorizationChecker->isGranted('ROLE_MENSAJERO')) {
            return new RedirectResponse($this->router->generate('shipment-get-delivery-dashboard'));
        } elseif ($this->authorizationChecker->isGranted('ROLE_LAB') || $this->authorizationChecker->isGranted('ROLE_CALIDAD')) {
            return new RedirectResponse($this->router->generate('app_lab_dashboarda'));
        }

        return new RedirectResponse($this->router->generate('nueva-venta'));
    }
}