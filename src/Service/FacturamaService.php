<?php

namespace App\Service;

use Facturama\Client;

class FacturamaService
{
    private $username;
    private $password;
    private $isDevelopment;

    public function __construct(string $username, string $password, bool $isDevelopment)
    {
        $this->username = $username;
        $this->password = $password;
        $this->isDevelopment = $isDevelopment;
    }

    public function getClient(): Client
    {
        $client = new Client($this->username, $this->password);
        
        if ($this->isDevelopment) {
            $client->setApiUrl('https://apisandbox.facturama.mx/');
        } else {
            $client->setApiUrl('https://api.facturama.mx/');
        }
        
        return $client;
    }
} 