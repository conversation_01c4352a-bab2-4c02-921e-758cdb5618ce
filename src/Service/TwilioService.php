<?php

// src/Service/FileUploader.php
namespace App\Service;

use Twilio\Rest\Client;
use SendGrid\Mail\Mail;
use Symfony\Component\DependencyInjection\ContainerInterface;

class TwilioService
{
    private Client $twilio;
    private $twilioPHONE;
    private $twilioWHATSAPP;
    private $appEnv;
    private $sendgrid;
    private $twilioMessagingServiceSid;
    public function __construct(string $twilioPHONE, string $twilioSID, string $twilioTOKEN, string $appEnv, string $sendGridApiKey, string $twilioWHATSAPP, string $twilioMessagingServiceSid = null)
    {
        $this->twilio = new Client($twilioSID, $twilioTOKEN);
        //$this->sendgrid = new \SendGrid($sendGridApiKey);
        $this->twilioPHONE = $twilioPHONE;
        $this->twilioWHATSAPP = $twilioWHATSAPP;
        $this->twilioMessagingServiceSid = $twilioMessagingServiceSid;
        $this->appEnv = $appEnv;
    }

    public function sendThanksPurchase(string $to, string $location, string $clientName)
    {
        if ($this->appEnv == 'prod' || $this->appEnv == 'PROD') {
            $bodyMessage = "Hola {{1}},\n\n¡Gracias por tu compra en {{2}}! Valoramos tu preferencia y esperamos que disfrutes de tus productos.\n\n¡Gracias nuevamente y te deseamos un excelente día!\n\nAtentamente,\nÓpticas Óptimas";
            $bodyMessage = str_replace("{{1}}", $clientName, $bodyMessage);
            $bodyMessage = str_replace("{{2}}", $location, $bodyMessage);

            $message = $this->twilio->messages
                ->create(
                    "whatsapp:+52" . $to,
                    [
                        "from" => "whatsapp:" . $this->twilioPHONE,
                        "body" => $bodyMessage
                    ]
                );
        }
    }

    public function mensajeTmp(string $to)
    {
        $bodyMessage = "Buen día, nos comunicamos de Optimo Ópticas.
Si sacaste prestación con nosotros y no has entregado el vale de autorización, estaremos en la 
Coordinación de Recursos Humanos, Edificio A de 9:00 am a 3:00 pm.";

        $message = $this->twilio->messages
            ->create(
                "whatsapp:+52" . $to,
                [
                    "from" => "whatsapp:" . $this->twilioWHATSAPP,
                    "body" => $bodyMessage
                ]
            );
    }

    public function sendThanksPurchase1(string $to, string $clientName)
    {
        if ($this->appEnv == 'prod' || $this->appEnv == 'PROD') {
            $message = $this->twilio->messages
                ->create(
                    "+52" . $to,
                    [
                        "from" => "" . $this->twilioPHONE,
                        "body" => "Hola " . $clientName . ",

                    ¡Gracias por tu compra en ! Valoramos tu preferencia y esperamos que disfrutes de tus productos.

                    ¡Gracias nuevamente y te deseamos un excelente día!

                    Atentamente,
                    Optimo Ópticas"
                    ]
                );
        }
    }

    public function sendOrderReady(string $to, string $location, string $clientName, string $address)
    {
        //if ($this->appEnv == 'prod' || $this->appEnv == 'PROD'){
        $message = $this->twilio->messages
            ->create(
                "whatsapp:+52" . $to,
                [
                    "from" => "whatsapp:" . $this->twilioPHONE,
                    "body" => "¡Hola " . $clientName . "!

                    Nos complace informarte que tu pedido está listo para ser entregado. Puedes pasar a recogerlo cuando te sea conveniente en nuestra " . $location . " ubicada en " . $address . ". Estaremos encantados de atenderte.

                    Gracias por elegirnos y esperamos que disfrutes de tu compra. Si tienes alguna pregunta o necesitas asistencia adicional, no dudes en ponerte en contacto con nosotros.

                    ¡Que tengas un excelente día!

                    Atentamente,
                    Optimo óticas"
                ]
            );
    }

    public function sendTicket(string $to, string $path)
    {
        if ($this->appEnv == 'prod' || $this->appEnv == 'PROD') {
            $message = $this->twilio->messages
                ->create(
                    "whatsapp:+52" . $to,
                    [
                        "mediaUrl" => ["https://images.unsplash.com/photo-1545093149-618ce3bcf49d?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=668&q=80"],
                        "from" => "whatsapp:" . $this->twilioPHONE,
                    ]
                );
        }
    }

    public function sendMessage(string $body, array $variables, array $clientData, bool $whatsapp)
    {
        $fromPhone = ($whatsapp) ? $this->twilioWHATSAPP : $this->twilioPHONE;
        $whatsappText = ($whatsapp) ? "whatsapp:" : '';

        foreach ($variables as $variable) {
            $tempVarNum = $variable[0];
            $tempVar = $variable[1];

            $pattern = '/\{\{' . $tempVarNum . '\}\}/';
            $body = preg_replace($pattern, $clientData[$tempVar], $body);
        }

        //if ($this->appEnv == 'prod' || $this->appEnv == 'PROD'){
        $message = $this->twilio->messages
            ->create(
                $whatsappText . "+52" . $clientData["phone"],
                [
                    "from" => $whatsappText . $fromPhone,
                    "body" => $body,
                    "mediaUrl" => ["https://marketing.optimoopticas.mx/avisoscampana/entregalerma.png"],
                ]
            );
        return $message->sid ?? "";
        //}
    }

    public function sendEmail()
    {

        $email = new Mail();
        $email->setFrom("<EMAIL>", "Optimo Ópticas");
        $email->setSubject("Test de tracking con sedgrid");
        $email->setReplyTo("<EMAIL>");
        $email->addTo("<EMAIL>");

        $emailContent = '
            <html>
            <body>
                <h1>SENDGRID</h1>
                <p>Hola inge</p>
                <a href="https://optimoopticas.mx/">Optimo</a>
            </body>
            </html>
        ';

        $email->addContent("text/html", $emailContent);

        $email->setTrackingSettings([
            "click_tracking" => ["enable" => true],
            "open_tracking" => ["enable" => true]
        ]);

        try {
            $response = $this->sendgrid->send($email);
            $headers = $response->headers();
            var_dump($headers);
            $messageIdPrefix = $headers[5];
            $prefix = "X-Message-Id: ";
            $position = strpos($messageIdPrefix, $prefix);
            if ($position !== false)  $messageId = substr($messageIdPrefix, $position + strlen($prefix));
        } catch (\Exception $e) {
            $error = 'Caught exception: ' . $e->getMessage() . "\n";
            var_dump($error);
        }

        exit();

        return $messageId ?? "";
    }

    public function sendMsgLerma(string $to, string $name)
    {
        // Check if twilioMessagingServiceSid is set
        if (!$this->twilioMessagingServiceSid) {
            throw new \Exception('Environment variable not found: "TWILIO_MESSAGING_SERVICE_SID". Please add it to your .env.dev file.');
        }

        //if ($this->appEnv == 'prod' || $this->appEnv == 'PROD'){
        $message = $this->twilio->messages
            ->create(
                "whatsapp:+52" . $to,
                [
                    "from" => $this->twilioMessagingServiceSid,
                    "contentSid" => "HX2e4da79e78343548089c2d288ae5bf62",
                    "contentVariables" => json_encode([
                        "1" => "entregalerma1.png",
                        "2" => $name,
                        "3" => "UAM UNIDAD LERMA",
                        "4" => "jueves 2 de mayo",
                    ]),
                ]
            );
        //}
    }

    public function sendMessage1(string $body, array $variables, array $clientData, bool $whatsapp)
    {
        $fromPhone = ($whatsapp) ? $this->twilioWHATSAPP : $this->twilioPHONE;
        $whatsappText = ($whatsapp) ? "whatsapp:" : '';

        foreach ($variables as $variable) {
            $tempVarNum = $variable[0];
            $tempVar = $variable[1];

            $pattern = '/\{\{' . $tempVarNum . '\}\}/';
            $body = preg_replace($pattern, $clientData[$tempVar], $body);
        }

        //if ($this->appEnv == 'prod' || $this->appEnv == 'PROD'){
        $message = $this->twilio->messages
            ->create(
                $whatsappText . "+52" . $clientData["phone"],
                [
                    "from" => "MG42cdf1157dd1084f42615e2dc56c3b8a",
                    "contentSid" => "HX2e4da79e78343548089c2d288ae5bf62",
                    "contentVariables" => json_encode([
                        "1" => "entregalerma.png",
                        "2" => "JOSE LENIN CERVANTES GONZALEZ",
                        "3" => "UAM UNIDAD LERMA",
                        "4" => "jueves 2 de mayo",
                    ]),

                ]
            );
        return $message->sid ?? "";
        //}
    }
}
