<?php

namespace App\Service;

use App\Enum\TraspasoErrorCode;
use App\Enum\TraspasoStatus;

/**
 * Servicio para manejar alertas de SweetAlert2 específicas del módulo de Traspaso
 * Extiende el AlertService general para proporcionar funcionalidad específica de traspasos
 */
class TraspasoAlertService extends AlertService
{
    /**
     * Genera configuración para alerta de éxito (mantiene compatibilidad)
     */
    public function successAlert(string $title, string $text = null): array
    {
        return $this->success($title, $text);
    }

    /**
     * Genera configuración para alerta de error usando código de error
     */
    public function errorAlert(string $errorCode, string $customMessage = null): array
    {
        $message = $customMessage ?? TraspasoErrorCode::getMessage($errorCode);

        return $this->error('Error en Traspaso', $message, [
            'footer' => "Código de error: {$errorCode}"
        ]);
    }

    /**
     * Alerta para producto agregado exitosamente
     */
    public function productAddedAlert(string $codigo, string $modelo = null): array
    {
        $text = $modelo ? "Producto: {$modelo} (Código: {$codigo})" : "Código: {$codigo}";

        return $this->successAlert('Producto Agregado', $text);
    }

    /**
     * Alerta para múltiples productos procesados
     */
    public function multipleProductsAlert(array $successful, array $errors): array
    {
        $totalSuccessful = count($successful);
        $totalErrors = count($errors);

        if ($totalErrors === 0) {
            return $this->successAlert(
                'Productos Procesados',
                "Se agregaron {$totalSuccessful} productos exitosamente"
            );
        }

        if ($totalSuccessful === 0) {
            return $this->error(
                'Error al Procesar Productos',
                "No se pudo agregar ningún producto. {$totalErrors} errores encontrados.",
                ['footer' => 'Revise los códigos e intente nuevamente']
            );
        }

        return $this->warning(
            'Procesamiento Parcial',
            "Se agregaron {$totalSuccessful} productos. {$totalErrors} productos presentaron errores.",
            ['footer' => 'Revise los errores en la tabla inferior']
        );
    }

    /**
     * Alerta de confirmación para traspaso
     */
    public function confirmTransferAlert(string $sucursalOrigen, string $sucursalDestino, int $totalProductos): array
    {
        return $this->confirm('¿Confirmar Traspaso?', null, [
            'html' => "
                <div class='text-left'>
                    <p><strong>Origen:</strong> {$sucursalOrigen}</p>
                    <p><strong>Destino:</strong> {$sucursalDestino}</p>
                    <p><strong>Productos:</strong> {$totalProductos}</p>
                </div>
            ",
            'confirmButtonText' => 'Sí, realizar traspaso',
            'cancelButtonText' => 'Cancelar'
        ]);
    }

    /**
     * Alerta para traspaso completado
     */
    public function transferCompletedAlert(int $idTraspaso): array
    {
        return $this->success(
            'Traspaso Completado',
            "El traspaso #{$idTraspaso} se ha creado exitosamente",
            [
                'timer' => 5000,
                'showConfirmButton' => true,
                'confirmButtonText' => 'Ver Detalles'
            ]
        );
    }

    /**
     * Alerta para orden de salida aceptada
     */
    public function exitOrderAcceptedAlert(): array
    {
        return $this->successAlert(
            'Orden Aceptada',
            'La orden de salida ha sido aceptada y los productos han sido transferidos'
        );
    }

    /**
     * Alerta para orden de salida rechazada
     */
    public function exitOrderRejectedAlert(): array
    {
        return $this->info(
            'Orden Rechazada',
            'La orden de salida ha sido rechazada y los productos han sido liberados'
        );
    }

    /**
     * Alerta para errores de Google Sheets
     */
    public function googleSheetsErrorAlert(string $errorCode, string $details = null): array
    {
        $baseMessage = TraspasoErrorCode::getMessage($errorCode);
        $text = $details ? "{$baseMessage}\n\nDetalles: {$details}" : $baseMessage;

        return $this->error('Error de Google Sheets', $text, [
            'footer' => "Código: {$errorCode}"
        ]);
    }

    /**
     * Alerta para productos con stock insuficiente
     */
    public function insufficientStockAlert(string $codigo, int $disponible, int $solicitado): array
    {
        return $this->warning('Stock Insuficiente', null, [
            'html' => "
                <div class='text-left'>
                    <p><strong>Código:</strong> {$codigo}</p>
                    <p><strong>Disponible:</strong> {$disponible}</p>
                    <p><strong>Solicitado:</strong> {$solicitado}</p>
                </div>
            "
        ]);
    }

    /**
     * Alerta de confirmación para eliminar producto del traspaso
     */
    public function confirmRemoveProductAlert(string $codigo): array
    {
        return $this->confirm(
            '¿Eliminar Producto?',
            "¿Está seguro de eliminar el producto {$codigo} del traspaso?",
            [
                'confirmButtonText' => 'Sí, eliminar',
                'confirmButtonColor' => '#dc3545'
            ]
        );
    }

    /**
     * Genera el JavaScript para mostrar la alerta (mantiene compatibilidad)
     */
    public function generateAlertScript(array $config): string
    {
        return $this->generateScript($config);
    }

    /**
     * Genera JavaScript para alerta con callback (mantiene compatibilidad)
     */
    public function generateAlertWithCallback(array $config, string $callback): string
    {
        return $this->generateScriptWithCallback($config, $callback);
    }
}
