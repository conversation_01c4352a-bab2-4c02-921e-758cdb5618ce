<?php

namespace App\Service;

use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Swift_SmtpTransport;
use Swift_Mailer;
use Swift_Message;
use Swift_Attachment;
use SwiftmailerBundle;
use App\Entity\Authstage;
use Twig\Environment;

class MailService
{


    private $params;
    private $twig;

    public function __construct(ParameterBagInterface $params, Environment $twig)
    {

        $this->params = $params;
        $this->twig = $twig;
    }

    public function sendEmail($emailData, $attachmentPath = null)
    {
        $environment = $this->params->get('APP_ENV');
        if ($environment === 'dev') {
            $recipient = $this->params->get('dev_email');
        }


        // Obtener los valores desde los parámetros del contenedor
        $smtpUser = '<EMAIL>';
        $smtpPassword = 'jbnefpwybhunaffk';

        if (!$smtpUser) {
            throw new \Exception('El parámetro "SMTP_USER" debe estar definido.');
        }

        if (!isset($emailData['recipient'], $emailData['subject'], $emailData['body'])) {
            throw new \InvalidArgumentException("Faltan datos obligatorios del correo.");
        }

        
        $recipient = $emailData['recipient'];
        $subject = $emailData['subject'];
        $body = $emailData['body'];
        $attachment = $emailData['attachment'] ?? null;
        $extension = $emailData['extension'] ?? 'pdf';
        $isHtml = $emailData['isHtml'] ?? false;

        $environment = $this->params->get('APP_ENV');
        if ($environment === 'dev') {
            $recipient = $this->params->get('dev_email');
        }

        $transport = (new Swift_SmtpTransport('smtp.gmail.com', 465, 'ssl'))
            ->setUsername($smtpUser)
            ->setPassword($smtpPassword);

        $mailer = new Swift_Mailer($transport);

        $message = (new Swift_Message($subject))
            ->setFrom($smtpUser)
            ->setTo($recipient)
            ->setBody($body, $isHtml ? 'text/html' : 'text/plain');

        if ($attachmentPath !== null && file_exists($attachmentPath)) {

            $attachment = new Swift_Attachment(file_get_contents($attachmentPath), basename($attachmentPath), mime_content_type($attachmentPath));
            $message->attach($attachment);

        } elseif ($attachment !== null) {

            $attachment = new Swift_Attachment($attachment, 'attachment.'.$extension, 'application/pdf');
            $message->attach($attachment);
        }

        try {
            $result = $mailer->send($message);
        } catch (\Exception $e) {
            throw new \RuntimeException("Error al enviar el correo: " . $e->getMessage());
        }

        return $result;
    }



    public function sendEmailUpdateAuthStage($emailData, $subscribers)
    {

        $subject = $emailData['subject'];
        $body = $emailData['body'];

        $environment = $this->params->get('APP_ENV');
        if ($environment === 'dev') {
            $recipient = $this->params->get('dev_email');
        }

        $transport = (new Swift_SmtpTransport('smtp.gmail.com', 465, 'ssl'))
            ->setUsername('<EMAIL>')
            ->setPassword('jbnefpwybhunaffk'); //xkazzdgozdgbzvpq
        $mailer = new Swift_Mailer($transport);

        $message = (new Swift_Message($subject))
            ->setFrom('<EMAIL>')
            ->setTo('<EMAIL>')
            ->setCc($subscribers);

        //$message->setBody($body, ($isHtml && $isFactura) ? 'text/html' : 'text/plain');
        $message->setBody($body, 'text/plain');


        $result = $mailer->send($message);
        return $result;
    }

    public function sendEmailCitaSucursal($emailContent)
    {

        $environment = $this->params->get('APP_ENV');
        if ($environment === 'dev') {
            $recipient = $this->params->get('dev_email');
        }

        $transport = (new Swift_SmtpTransport('smtp.gmail.com', 465, 'ssl'))
            ->setUsername('<EMAIL>')
            ->setPassword('jbnefpwybhunaffk');
        $mailer = new Swift_Mailer($transport);

        $message = (new Swift_Message($emailContent["subject"]))
            ->setFrom('<EMAIL>')
            ->setTo($emailContent["emailS"]);
        $message->setBody($emailContent["bodyS"], 'text/plain');
        $result = $mailer->send($message);

        $message = (new Swift_Message($emailContent["subject"]))
            ->setFrom('<EMAIL>')
            ->setTo($emailContent["emailC"]);
        $message->setBody($emailContent["bodyC"], 'text/plain');
        $result = $mailer->send($message);


        return $result;
    }

    public function sendEmailGraduation($emailContext)
    {

        /*$environment = $this->params->get('APP_ENV');
        if ($environment === 'dev') {
            $recipient = $this->params->get('dev_email');
        }*/

        $transport = (new Swift_SmtpTransport('smtp.gmail.com', 465, 'ssl'))
            ->setUsername('<EMAIL>')
            ->setPassword('jbnefpwybhunaffk');
        $mailer = new Swift_Mailer($transport);

        $body = $this->twig->render('emails/graduationNotification.html.twig', [
            'context' => $emailContext
        ]);

        $message = (new Swift_Message($emailContext["subject"]))
            ->setFrom('<EMAIL>')
            ->setTo("<EMAIL>");
        $message ->setBody($body, 'text/html');

        foreach ($emailContext["filesReceived"] as $file){
            $attachmentPath = $emailContext["path"].$file;
            $attachment = new Swift_Attachment(file_get_contents($attachmentPath), basename($attachmentPath), mime_content_type($attachmentPath));
            $message->attach($attachment);
        }

        $result = $mailer->send($message);

        return $result;
    }

}