<?php

namespace App\Service;

/**
 * Servicio centralizado para manejar alertas de SweetAlert2 en toda la aplicación
 * 
 * Este servicio proporciona una interfaz unificada para crear alertas consistentes
 * en toda la aplicación usando SweetAlert2.
 */
class AlertService
{
    /**
     * Configuraciones base para diferentes tipos de alerta
     */
    protected const ALERT_CONFIGS = [
        'success' => [
            'icon' => 'success',
            'confirmButtonColor' => '#28a745',
            'timer' => 3000,
            'showConfirmButton' => false,
            'timerProgressBar' => true
        ],
        'error' => [
            'icon' => 'error',
            'confirmButtonColor' => '#dc3545',
            'showConfirmButton' => true
        ],
        'warning' => [
            'icon' => 'warning',
            'confirmButtonColor' => '#ffc107',
            'showConfirmButton' => true
        ],
        'info' => [
            'icon' => 'info',
            'confirmButtonColor' => '#17a2b8',
            'showConfirmButton' => true
        ],
        'question' => [
            'icon' => 'question',
            'showCancelButton' => true,
            'confirmButtonColor' => '#007bff',
            'cancelButtonColor' => '#6c757d',
            'confirmButtonText' => 'Sí, continuar',
            'cancelButtonText' => 'Cancelar'
        ]
    ];

    /**
     * Genera configuración para alerta de éxito
     */
    public function success(string $title, string $text = null, array $options = []): array
    {
        return array_merge(self::ALERT_CONFIGS['success'], [
            'title' => $title,
            'text' => $text
        ], $options);
    }

    /**
     * Genera configuración para alerta de error
     */
    public function error(string $title, string $text = null, array $options = []): array
    {
        return array_merge(self::ALERT_CONFIGS['error'], [
            'title' => $title,
            'text' => $text
        ], $options);
    }

    /**
     * Genera configuración para alerta de advertencia
     */
    public function warning(string $title, string $text = null, array $options = []): array
    {
        return array_merge(self::ALERT_CONFIGS['warning'], [
            'title' => $title,
            'text' => $text
        ], $options);
    }

    /**
     * Genera configuración para alerta informativa
     */
    public function info(string $title, string $text = null, array $options = []): array
    {
        return array_merge(self::ALERT_CONFIGS['info'], [
            'title' => $title,
            'text' => $text
        ], $options);
    }

    /**
     * Genera configuración para alerta de confirmación
     */
    public function confirm(string $title, string $text = null, array $options = []): array
    {
        return array_merge(self::ALERT_CONFIGS['question'], [
            'title' => $title,
            'text' => $text
        ], $options);
    }

    /**
     * Alerta de confirmación para eliminar elemento
     */
    public function confirmDelete(string $itemName = 'elemento', array $options = []): array
    {
        return array_merge(self::ALERT_CONFIGS['question'], [
            'title' => '¿Eliminar ' . $itemName . '?',
            'text' => "Esta acción no se puede deshacer",
            'icon' => 'warning',
            'confirmButtonText' => 'Sí, eliminar',
            'confirmButtonColor' => '#dc3545',
            'cancelButtonText' => 'Cancelar'
        ], $options);
    }

    /**
     * Alerta para operación exitosa
     */
    public function operationSuccess(string $operation = 'Operación', array $options = []): array
    {
        return $this->success(
            $operation . ' Exitosa',
            'La operación se completó correctamente',
            $options
        );
    }

    /**
     * Alerta para operación fallida
     */
    public function operationError(string $operation = 'Operación', string $details = null, array $options = []): array
    {
        $text = $details ? "Error: {$details}" : 'La operación no pudo completarse';
        
        return $this->error(
            $operation . ' Fallida',
            $text,
            $options
        );
    }

    /**
     * Alerta para validación de formulario
     */
    public function validationError(array $errors = [], array $options = []): array
    {
        $text = empty($errors) 
            ? 'Por favor, corrija los errores en el formulario'
            : 'Errores encontrados: ' . implode(', ', $errors);

        return $this->error(
            'Error de Validación',
            $text,
            $options
        );
    }

    /**
     * Alerta para permisos insuficientes
     */
    public function accessDenied(string $resource = 'recurso', array $options = []): array
    {
        return $this->error(
            'Acceso Denegado',
            "No tiene permisos para acceder a este {$resource}",
            $options
        );
    }

    /**
     * Alerta para sesión expirada
     */
    public function sessionExpired(array $options = []): array
    {
        return $this->warning(
            'Sesión Expirada',
            'Su sesión ha expirado. Por favor, inicie sesión nuevamente',
            array_merge([
                'showConfirmButton' => true,
                'confirmButtonText' => 'Iniciar Sesión',
                'allowOutsideClick' => false,
                'allowEscapeKey' => false
            ], $options)
        );
    }

    /**
     * Alerta para carga/procesamiento
     */
    public function loading(string $message = 'Procesando...', array $options = []): array
    {
        return array_merge([
            'title' => $message,
            'allowOutsideClick' => false,
            'allowEscapeKey' => false,
            'showConfirmButton' => false,
            'didOpen' => 'Swal.showLoading()'
        ], $options);
    }

    /**
     * Genera el JavaScript para mostrar la alerta
     */
    public function generateScript(array $config): string
    {
        $configJson = json_encode($config, JSON_UNESCAPED_UNICODE);
        
        return "
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                Swal.fire({$configJson});
            });
        </script>
        ";
    }

    /**
     * Genera JavaScript para alerta con callback
     */
    public function generateScriptWithCallback(array $config, string $callback): string
    {
        $configJson = json_encode($config, JSON_UNESCAPED_UNICODE);
        
        return "
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                Swal.fire({$configJson}).then((result) => {
                    if (result.isConfirmed) {
                        {$callback}
                    }
                });
            });
        </script>
        ";
    }

    /**
     * Genera JavaScript para alerta con múltiples callbacks
     */
    public function generateScriptWithCallbacks(array $config, array $callbacks = []): string
    {
        $configJson = json_encode($config, JSON_UNESCAPED_UNICODE);
        
        $callbackScript = '';
        if (isset($callbacks['confirmed'])) {
            $callbackScript .= "if (result.isConfirmed) { {$callbacks['confirmed']} }";
        }
        if (isset($callbacks['dismissed'])) {
            $callbackScript .= "if (result.isDismissed) { {$callbacks['dismissed']} }";
        }
        if (isset($callbacks['denied'])) {
            $callbackScript .= "if (result.isDenied) { {$callbacks['denied']} }";
        }
        
        return "
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                Swal.fire({$configJson}).then((result) => {
                    {$callbackScript}
                });
            });
        </script>
        ";
    }

    /**
     * Convierte configuración a JSON para uso en AJAX
     */
    public function toJson(array $config): string
    {
        return json_encode($config, JSON_UNESCAPED_UNICODE);
    }

    /**
     * Crea una alerta personalizada con configuración completa
     */
    public function custom(array $config): array
    {
        return $config;
    }

    /**
     * Alerta tipo toast (notificación pequeña)
     */
    public function toast(string $message, string $type = 'success', array $options = []): array
    {
        return array_merge([
            'toast' => true,
            'position' => 'top-end',
            'showConfirmButton' => false,
            'timer' => 3000,
            'timerProgressBar' => true,
            'icon' => $type,
            'title' => $message
        ], $options);
    }

    /**
     * Obtiene las configuraciones base
     */
    public function getBaseConfigs(): array
    {
        return self::ALERT_CONFIGS;
    }
}
