<?php

// src/Service/FileUploader.php
namespace App\Service;

use App\Entity\Pago;
use App\Entity\Venta;
use Doctrine\ORM\EntityManagerInterface;

class PagoEraser
{
    private $em;

    public function __construct(EntityManagerInterface $em)
    {
        $this->em = $em;
    }

    public function erasePago(Pago $pago)
    {
        $em = $this->em;
        $Venta = $pago->getVentaIdventa();
        $Client = $Venta->getClienteIdcliente();

        if ($Venta->getCredito() == '1' && $Venta->getCotizacion() != '1') $Client->setDebt($Client->getDebt() + $pago->getMonto());

        $pago->setStatus('0');
        //$pago->setAutomatic('0');
        $this->em->persist($pago);
        $this->em->persist($Client);
        $this->em->flush();

        $query = $em->createQuery(

            'SELECT p
            FROM App\Entity\Pago p
            inner join  p.ventaIdventa v
            where p.status=:status and v.idventa=:idventa'

        )->setParameters(['status'=>'1','idventa'=>$Venta->getIdventa()]);

        $pagos = $query->getResult();

        $totalPagado=0;

        foreach ($pagos as $key =>$Pago){
            $totalPagado+=$Pago->getMonto();
        }

        // COMENTADO: Los triggers de BD ya manejan automáticamente el estado de liquidación
        // No necesitamos actualizar manualmente, los triggers lo hacen correctamente
        // if($totalPagado >= $Venta->getPagado()){
        //     $Venta->setLiquidada('1');
        //     // La deuda se actualiza automáticamente por el trigger de la base de datos
        // }else $Venta->setLiquidada('0');

        // COMENTADO: Los triggers actualizan automáticamente cuando se modifica un pago
        // $this->em->persist($Venta);
        // $this->em->flush();
    }
}
