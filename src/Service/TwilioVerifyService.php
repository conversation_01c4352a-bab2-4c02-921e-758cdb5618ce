<?php

namespace App\Service;

use App\Exception\PhoneVerificationException;
use App\Exception\TwilioVerifyException;
use Psr\Log\LoggerInterface;
use Twilio\Exceptions\TwilioException;
use Twilio\Rest\Client;

/**
 * Service for Twilio Verify integration
 */
class TwilioVerifyService
{
    /**
     * Default code expiration time in seconds (5 minutes)
     */
    private const DEFAULT_CODE_EXPIRATION = 300;

    /**
     * @var Client
     */
    private $twilioClient;

    /**
     * @var string
     */
    private $verifyServiceSid;

    /**
     * @var string
     */
    private $serviceName;

    /**
     * @var int
     */
    private $codeLength;

    /**
     * @var string|null
     */
    private $defaultTemplateSid;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * Format phone number to E.164 format
     *
     * @param string $phoneNumber
     * @return string
     */
    private function formatPhoneNumber(string $phoneNumber): string
    {

        $digits = preg_replace('/\D/', '', $phoneNumber);


        if (strpos($digits, '52') !== 0) {
            $digits = '52' . $digits;
        }


        if (strpos($digits, '521') !== 0) {
            $digits = '521' . substr($digits, 2); // inserta 1 después de 52
        }

        return '+' . $digits;
    }


    /**
     * TwilioVerifyService constructor.
     *
     * @param string $accountSid
     * @param string $authToken
     * @param string $verifyServiceSid
     * @param string $serviceName
     * @param int $codeLength
     * @param string|null $defaultTemplateSid
     * @param LoggerInterface $logger
     */
    public function __construct(
        string $accountSid,
        string $authToken,
        string $verifyServiceSid,
        string $serviceName,
        int $codeLength,
        ?string $defaultTemplateSid,
        LoggerInterface $logger
    ) {
        $this->twilioClient = new Client($accountSid, $authToken);
        $this->verifyServiceSid = $verifyServiceSid;
        $this->serviceName = $serviceName;
        $this->codeLength = $codeLength;
        $this->defaultTemplateSid = $defaultTemplateSid;
        $this->logger = $logger;

        // If no service SID is provided, try to find or create one
        if (empty($this->verifyServiceSid)) {
            $this->verifyServiceSid = $this->findOrCreateVerifyService();
        }
    }

    /**
     * Find or create a Verify Service
     *
     * @return string
     * @throws PhoneVerificationException
     */
    private function findOrCreateVerifyService(): string
    {
        try {
            // Try to find an existing service with the same name
            $services = $this->twilioClient->verify->v2->services->read(['friendlyName' => $this->serviceName]);

            if (!empty($services)) {
                $this->logger->info('Found existing Verify Service', [
                    'serviceName' => $this->serviceName,
                    'serviceSid' => $services[0]->sid
                ]);
                return $services[0]->sid;
            }

            // Create a new service
            $serviceParams = [
                'friendlyName' => $this->serviceName,
                'codeLength' => $this->codeLength
            ];

            if ($this->defaultTemplateSid) {
                $serviceParams['defaultTemplateSid'] = $this->defaultTemplateSid;
            }

            $service = $this->twilioClient->verify->v2->services->create($serviceParams);

            $this->logger->info('Created new Verify Service', [
                'serviceName' => $this->serviceName,
                'serviceSid' => $service->sid
            ]);

            return $service->sid;
        } catch (TwilioException $e) {
            $this->logger->error('Failed to find or create Verify Service', [
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);
            throw TwilioVerifyException::fromTwilioException($e);
        }
    }

    /**
     * Send verification code
     *
     * @param string $phoneNumber
     * @return array
     * @throws PhoneVerificationException
     */
    public function sendVerificationCode(string $phoneNumber): array
    {
        try {
            $formattedPhoneNumber = $this->formatPhoneNumber($phoneNumber);
            $this->logger->info('Sending verification code', [
                'phoneNumber' => $phoneNumber,
                'formattedPhoneNumber' => $formattedPhoneNumber
            ]);

            $verification = $this->twilioClient->verify->v2->services($this->verifyServiceSid)
                ->verifications
                ->create($formattedPhoneNumber, 'sms', ['locale' => 'es']);

            $this->logger->info('Sent verification code', [
                'phoneNumber' => $phoneNumber,
                'status' => $verification->status
            ]);

            return [
                'status' => $verification->status,
                'expiresAt' => date('c', time() + self::DEFAULT_CODE_EXPIRATION)
            ];
        } catch (TwilioException $e) {
            $this->logger->error('Failed to send verification code', [
                'phoneNumber' => $phoneNumber,
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);
            throw TwilioVerifyException::fromTwilioException($e);
        }
    }

    /**
     * Check verification code
     *
     * @param string $phoneNumber
     * @param string $code
     * @return string
     * @throws PhoneVerificationException
     */
    public function checkVerificationCode(string $phoneNumber, string $code): string
    {
        try {
            $formattedPhoneNumber = $this->formatPhoneNumber($phoneNumber);
            $this->logger->info('Checking verification code', [
                'phoneNumber' => $phoneNumber,
                'formattedPhoneNumber' => $formattedPhoneNumber
            ]);

            $verification = $this->twilioClient->verify->v2->services($this->verifyServiceSid)
                ->verificationChecks
                ->create([
                    'to' => $formattedPhoneNumber,
                    'code' => $code
                ]);

            $this->logger->info('Checked verification code', [
                'phoneNumber' => $phoneNumber,
                'status' => $verification->status
            ]);

            return $verification->status;
        } catch (TwilioException $e) {
            $this->logger->error('Failed to check verification code', [
                'phoneNumber' => $phoneNumber,
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);
            throw TwilioVerifyException::fromTwilioException($e);
        }
    }

    /**
     * Get verification status
     *
     * @param string $phoneNumber
     * @return array
     * @throws PhoneVerificationException
     */
    public function getVerificationStatus(string $phoneNumber): array
    {
        try {
            $formattedPhoneNumber = $this->formatPhoneNumber($phoneNumber);
            $this->logger->info('Getting verification status', [
                'phoneNumber' => $phoneNumber,
                'formattedPhoneNumber' => $formattedPhoneNumber
            ]);

            $verifications = $this->twilioClient->verify->v2->services($this->verifyServiceSid)
                ->verifications($formattedPhoneNumber)
                ->fetch();

            $this->logger->info('Got verification status', [
                'phoneNumber' => $phoneNumber,
                'status' => $verifications->status
            ]);

            return [
                'status' => $verifications->status,
                'expiresAt' => date('c', strtotime($verifications->dateCreated) + self::DEFAULT_CODE_EXPIRATION)
            ];
        } catch (TwilioException $e) {
            // If the verification is not found, return a pending status
            if ($e->getCode() === 20404) {
                return [
                    'status' => 'pending',
                    'expiresAt' => null
                ];
            }

            $this->logger->error('Failed to get verification status', [
                'phoneNumber' => $phoneNumber,
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);
            throw TwilioVerifyException::fromTwilioException($e);
        }
    }
}
