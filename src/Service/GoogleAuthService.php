<?php

namespace App\Service;

use App\Entity\Cliente;
use App\Exception\InvalidGoogleTokenException;
use Doctrine\ORM\EntityManagerInterface;
use Google_Client;
use Psr\Log\LoggerInterface;

class GoogleAuthService
{
    private EntityManagerInterface $em;
    private LoggerInterface $logger;

    private array $clientIds = [
        '19241577668-bujo8m962nsl1iinoaijliskbvbb1hva.apps.googleusercontent.com', // Web client ID
        '19241577668-pdnad04rdftld6d87pv8rjiiupqgpmfl.apps.googleusercontent.com',  // Android/React Native client ID
        '575830005901-xxxxxxxxxxxxxxxxxxxxxxxxxxxxx.apps.googleusercontent.com'  // Firebase Android client ID (1:575830005901:android:d76983c71deeb484af6ac4)
    ];

    public function __construct(EntityManagerInterface $em, LoggerInterface $logger)
    {
        $this->em = $em;
        $this->logger = $logger;
    }

    /**
     * @throws InvalidGoogleTokenException
     */
    public function login(string $idToken): Cliente
    {
        $client = new \Google_Client();
        $payload = null;
        $lastException = null;

        // Try each client ID until one works
        foreach ($this->clientIds as $clientId) {
            $client->setClientId($clientId);

            try {
                $payload = $client->verifyIdToken($idToken);
                if ($payload) {
                    break; // Successfully verified the token
                }
            } catch (\Throwable $e) {
                $lastException = $e;
                $this->logger->info('Token verification failed with client ID', [
                    'client_id' => $clientId,
                    'error' => $e->getMessage()
                ]);
                // Continue to the next client ID
            }
        }

        // If all client IDs failed
        if (!$payload) {
            $errorDetails = [
                'error' => $lastException ? $lastException->getMessage() : 'All client IDs failed',
                'token_length' => strlen($idToken)
            ];
            $this->logger->error('Google ID Token invalid with all client IDs', $errorDetails);
            throw new InvalidGoogleTokenException(
                'Token no válido', 
                null, 
                $errorDetails
            );
        }

        if (!$payload || empty($payload['email'])) {
            $errorDetails = [
                'payload_empty' => empty($payload),
                'email_missing' => !empty($payload) && empty($payload['email'])
            ];
            $this->logger->error('Token payload invalid', $errorDetails);
            throw new InvalidGoogleTokenException(
                'Token inválido o sin email', 
                null, 
                $errorDetails
            );
        }

        $email = $payload['email'];
        $nombre = $payload['name'] ?? 'Usuario';
        $telefono = $payload['phone_number'] ?? '';

        $cliente = $this->em->getRepository(Cliente::class)->findOneBy(['email' => $email]);

        if (!$cliente) {
            $cliente = new Cliente();
            $cliente->setNombre($nombre);
            $cliente->setEmail($email);
            $cliente->setTelefono($telefono);

            $this->em->persist($cliente);
            $this->em->flush();
        }

        return $cliente;
    }

}
