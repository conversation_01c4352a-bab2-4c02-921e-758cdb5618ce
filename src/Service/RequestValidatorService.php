<?php

namespace App\Service;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use App\Service\ErrorResponseService;
use App\Enum\ErrorCodes\RequestValidatorErrorCodes;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class RequestValidatorService
{
    private ValidatorInterface $validator;
    private ErrorResponseService $errorResponseService;
    private array $reflectionCache = [];

    public function __construct(ValidatorInterface $validator, ErrorResponseService $errorResponseService)
    {
        $this->validator = $validator;
        $this->errorResponseService = $errorResponseService;
    }

    public function validateAndMap(Request $request, string $dtoClass, bool $useQueryParams = false): object
    {
        if ($useQueryParams) {
            $data = $request->query->all();
        } elseif ($request->getContentTypeFormat() === 'form') {
            $data = array_merge($request->request->all(), $request->files->all());
        } else {
            $data = json_decode($request->getContent(), true);
        }

        if (!is_array($data)) {
            return $this->errorResponseService->createErrorResponse(RequestValidatorErrorCodes::REQUEST_VALIDATOR_INVALID_DATA_FORMAT,
                [
                    'data' => $data,
                ]
            );
        }

        $reflectionClass = $this->reflectionCache[$dtoClass] ??= new \ReflectionClass($dtoClass);
        $dto = new $dtoClass(); //TODO: Check if the class exists and is instantiable

        foreach ($data as $key => $value) {
            if ($reflectionClass->hasProperty($key)) {
                $property = $reflectionClass->getProperty($key);
                $propertyType = $property->getType();

                if ($propertyType) {
                    $typeName = $propertyType->getName();

                    switch($typeName) {
                        case UploadedFile::class:
                            if ($value instanceof UploadedFile) {
                                $dto->$key = $value;
                            }
                            break;
                        case 'int':
                            $dto->$key = is_numeric($value) ? (int)$value : null;
                            break;
                        case 'float': 
                            $dto->$key = is_numeric($value) ? (float)$value : null;
                            break;
                        case 'bool':
                            $dto->$key = filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
                            break;
                        case 'string':
                            $dto->$key = is_string($value) ? $value : null;
                            break;
                        case 'array':
                            $dto->$key = is_array($value) ? $value : null;
                            break;
                        default:
                            $dto->$key = $value;
                            break;
                    }
                } else {
                    $dto->$key = $value;
                }
            }
        }

        $errors = $this->validator->validate($dto);
        if (count($errors) > 0) {
            $errorMessages = array_map(fn($error) => $error->getMessage(), iterator_to_array($errors));
            return $this->errorResponseService->createErrorResponse(RequestValidatorErrorCodes::REQUEST_VALIDATOR_VALIDATION_ERROR, 
                [
                    'errors' => $errorMessages,
                ]
            );
        }

        return $dto;
    }
}