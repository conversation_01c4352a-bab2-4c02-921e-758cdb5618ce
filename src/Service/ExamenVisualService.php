<?php
// src/Service/ExamenVisualService.php

namespace App\Service;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Twig\Environment;
use App\Entity\Cliente;
use Swift_Mailer;
use Swift_Message;
use Swift_SmtpTransport;

class ExamenVisualService
{
    private $mailer;
    private $params;
    private $twig;
    private $entityManager;

    public function __construct(ParameterBagInterface $params,Swift_Mailer $mailer, Environment $twig, EntityManagerInterface $entityManager)
    {
        $this->mailer = $mailer;
        $this->twig = $twig;
        $this->params = $params;
        $this->entityManager = $entityManager;
    }

    public function sendEmailExamen(array $emailId): void
    {

        $environment = $this->params->get('APP_ENV');
        if ($environment === 'dev') {
            $recipient = $this->params->get('dev_email');
        }

        $cliente = $this->entityManager->getRepository(Cliente::class)->find($emailId['objectId']);

        var_dump($cliente);
        die;

        $smtpUser = '<EMAIL>';
        $smtpPassword = 'jbnefpwybhunaffk';

        if (!$cliente) {
            throw new \Exception("No se encontró el cliente con ID: " . $emailId['objectId']);
        }

        $asunto = 'Examen Visual';

        $datos = [
            'asunto' => $asunto,
            'titulo' => 'Este es su Examen Visual',
            'mensaje' => 'Este es un correo de prueba.'
        ];

        $transport = (new Swift_SmtpTransport('smtp.gmail.com', 465, 'ssl'))
            ->setUsername('<EMAIL>')
            ->setPassword('jbnefpwybhunaffk');

        $mailer = new Swift_Mailer($transport);

        $message = (new Swift_Message($asunto))
            ->setFrom($smtpUser)
            ->setTo($cliente->getEmail())
            ->setBody("<p>Hola, este es tu examen visual.</p>", 'text/html');


        $result = $mailer->send($message);
        if ($result === 1) {
            die("Sí se pudo enviar el correo");
        } else {
            die("No se pudo enviar el correo. Código de error: " . $result);
        }



    }
}

