<?php

// src/Service/FileUploader.php
namespace App\Service;

use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\DependencyInjection\ContainerInterface;

class FileUploader
{
    private $targetDirectory;
    private $container;

    /*public function __construct($targetDirectory)
    {
        $this->targetDirectory = $targetDirectory;
    }*/

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
    }

    public function setDirectory($targetDirectory){

        $this->targetDirectory = $targetDirectory;
        
    }

    public function upload(UploadedFile $file, $tipoDocumento)
    {
        $originalFilename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $safeFilename = transliterator_transliterate('Any-Latin; Latin-ASCII; [^A-Za-z0-9_] remove; Lower()', $originalFilename);
        $fileName = $safeFilename.'-'.uniqid().'.'.$file->guessExtension();
        
        
        if($tipoDocumento == "constancia"){
            $this->targetDirectory = $this->container->getParameter('carpetaConstancias');;
        }
        else if($tipoDocumento == "documento"){
            $this->targetDirectory = $this->container->getParameter('carpetaDocumentos');
        }
        else if($tipoDocumento == "ticket"){
            $this->targetDirectory = $this->container->getParameter('carpetaTickets');
        }
        else if($tipoDocumento == "zip"){
            $carpeta = $this->container->getParameter('carpetaZipFacturas');

            $this->targetDirectory = $carpeta."/".$fileName;
        }else if($tipoDocumento == "autorizacion"){
            $carpeta = $this->container->getParameter('carpetaArchivoAutorizacion');

            $this->targetDirectory = $carpeta."/".$fileName;
        }
        

        try {
            $file->move($this->getTargetDirectory(), $fileName);
        } catch (FileException $e) {
            // ... handle exception if something happens during file upload
        }

        return $fileName;
    }

    public function getTargetDirectory()
    {
        return $this->targetDirectory;
    }
}