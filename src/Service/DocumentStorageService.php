<?php

namespace App\Service;

use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class DocumentStorageService
{
    private $params;
    private $baseDirectory;

    public function __construct(ParameterBagInterface $params)
    {
        $this->params = $params;
        $this->baseDirectory = $params->get('carpetaDocumentosVenta');
    }

    /**
     * Get document path with fallback locations
     * 
     * @param string $filename Document filename
     * @param string $userId User ID
     * @param string|null $saleId Optional sale ID for specific sale documents
     * @return array|null Returns ['absolute' => string, 'relative' => string] or null if not found
     */
    public function getDocumentPath($filename, $userId, $saleId = null)
    {
        $locations = [
            // Sale-specific path
            $this->baseDirectory . '/' . $userId . '/' . $saleId . '/' . $filename,
            // User-specific path
            $this->baseDirectory . '/' . $userId . '/' . $filename,
            // Base path
            $this->baseDirectory . '/' . $filename
        ];

        foreach ($locations as $location) {
            if (file_exists($location)) {
                return [
                    'absolute' => $location,
                    'relative' => str_replace($this->baseDirectory, '/uploads/carpetaDocumentosVenta', $location)
                ];
            }
        }

        return null;
    }

    /**
     * Create directory if it doesn't exist
     * 
     * @param string $path Directory path to create
     * @throws \RuntimeException If directory creation fails
     */
    public function ensureDirectoryExists($path)
    {
        if (!is_dir($path)) {
            if (!@mkdir($path, 0777, true)) {
                throw new \RuntimeException(sprintf('Unable to create directory "%s"', $path));
            }
        }
    }

    /**
     * Get file content type based on extension
     * 
     * @param string $filename 
     * @return string MIME type
     */
    public function getContentType($filename): string 
    {
        $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        switch ($ext) {
            case 'pdf':
                return 'application/pdf';
            case 'png':
                return 'image/png';
            case 'jpg':
            case 'jpeg':
                return 'image/jpeg';
            case 'gif':
                return 'image/gif';
            default:
                return 'application/octet-stream';
        }
    }

    /**
     * Check if file exists in any of the possible storage locations
     * 
     * @param string $filename Document filename
     * @param string $userId User ID 
     * @param string|null $saleId Optional sale ID
     * @return bool True if file exists in any location
     */
    public function documentExists($filename, $userId, $saleId = null): bool
    {
        return null !== $this->getDocumentPath($filename, $userId, $saleId);
    }
}
