<?php

namespace App\Service;
use Doctrine\ORM\EntityManagerInterface;

class SalesService
{
    private $em;

    public function __construct(EntityManagerInterface $em)
    {
        $this->em = $em;
    }

    public function getSoldProductos($idventa,$typeProduct){

        $numberProducts=0;
        $query = $this->em->createQuery(
            '   SELECT SUM(sv.cantidad) as quantity
                    FROM App\Entity\Stockventa sv
                    inner join sv.ventaIdventa v
                    inner join sv.stockIdstock s
                    inner join s.productoIdproducto p
                    WHERE v.idventa = :idventa AND p.tipoproducto=:tipoproducto AND sv.status =:status AND v.status=:status
                '
        )->setParameters(["idventa" => $idventa,'tipoproducto'=>$typeProduct, "status" => '1'])->setMaxResults(1);
        $StockVentas = $query->getOneOrNullResult();
        
        $numberProducts = (isset($StockVentas['quantity'])) ? $StockVentas['quantity'] : 0;

        return $numberProducts;

    }

    public function getLastPaymentDate($idventa){

        $query = $this->em->createQuery(
            '   SELECT p.fecha
                    FROM App\Entity\Pago p
                    INNER JOIN p.ventaIdventa v
                    WHERE v.idventa = :idventa AND v.liquidada=:status AND p.status =:status
                    ORDER BY p.idpago DESC
                '
        )->setParameters(["idventa" => $idventa, "status" => '1'])->setMaxResults(1);
        $paymentDate = $query->getOneOrNullResult();
        
        //\DateTime::createFromFormat('d/m/Y', $paymentDate["fecha"]);

        $liquidatedDate = (isset($paymentDate["fecha"])) ? $paymentDate["fecha"]->format('d/m/Y') : '';

        return $liquidatedDate;

    }

}