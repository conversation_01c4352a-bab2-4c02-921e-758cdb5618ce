<?php
// src/Service/CodigoPostalService.php
namespace App\Service;

class CodigoPostalService
{
    private array $datos;

    public function __construct()
    {
        $archivo = __DIR__ . '/../Data/codigos_postales_array.php';
        if (file_exists($archivo)) {
            $this->datos = include $archivo;
        } else {
            $this->datos = [];
        }
    }

    public function buscarPorCp(string $cp): ?array
    {
        return $this->datos[$cp] ?? null;
    }
}
