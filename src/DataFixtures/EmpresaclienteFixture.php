<?php

namespace App\DataFixtures;

use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

use App\Entity\Empresacliente; 

class EmpresaclienteFixture extends Fixture
{
    public function load(ObjectManager $manager): void
    {
        /*$ClientEnterprise = new Empresacliente();

        $ClientEnterprise->setNombre("Sin empresa");
        
        $manager->persist($ClientEnterprise);

        $manager->flush();*/
    }
}
