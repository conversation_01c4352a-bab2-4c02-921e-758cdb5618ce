<?php

namespace App\DataFixtures;

use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

use App\Entity\Cliente;
use App\Entity\Empresacliente; 

class ClienteFixture extends Fixture
{
    public function load(ObjectManager $manager): void
    {
        $ClientEnterprise = new Empresacliente();

        $ClientEnterprise->set<PERSON><PERSON><PERSON>("Sin empresa");
        
        $manager->persist($ClientEnterprise);

        $Client = new Cliente();
        $Client->setNombre('DIOGENES');
        $Client->setApellidopaterno('GRAJALES');
        $Client->setApellidomaterno('CORONA');
        $Client->setTelefono('5573892357');
        $Client->setEmail('<EMAIL>');
        $Client->setNumeroempleado('1027');
        $Client->setEmpresaclienteIdempresacliente($ClientEnterprise);

        $manager->persist($Client);

        $Client = new Cliente();
        $Client->setNombre('SANDRA XIMENA');
        $Client->setApellidopaterno('MARTINEZ');
        $Client->setApellidomaterno('RAMOS');
        $Client->setTelefono('5541408432');
        $Client->setEmail('<EMAIL>');
        $Client->setNumeroempleado('0930');
        $Client->setEmpresaclienteIdempresacliente($ClientEnterprise);

        $manager->persist($Client);

        $manager->flush();
    }
}
