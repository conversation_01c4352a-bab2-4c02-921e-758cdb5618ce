<?php

namespace App\DataFixtures;

use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

use App\Entity\Usuario;

use App\Entity\Empresa;

use App\Entity\Sucursal;

class AppFixtures extends Fixture
{
    public function load(ObjectManager $manager): void
    {
        $Enterprise = new Empresa();
        $Enterprise->setNombre('OPTIMO');

        $manager->persist($Enterprise);

        $Location = new Sucursal();
        $Location->setNombre('SUCURSAL COAPA');
        $Location->setCodigo('CODIGO COAPA');
        $Location->setTelefono('5573892357');
        $Location->setDireccion('Alfonso XIII 90');
        $Location->setCiudad('Ciudad de México');
        $Location->setTipo('sucursal');
        $Location->setEmpresaIdempresa($Enterprise);
        
        $manager->persist($Location);

        $User = new Usuario();
        $User->setSucursalIdsucursal($Location);
        $User->setNombre('Antonio');
        $User->setApellidopaterno('Muñoz');
        $User->setApellidomaterno('Alonzo');
        $User->setEmail('<EMAIL>');
        $User->setRol('ROLE_SUPER_ADMIN');
        $User->setContrasena('$2y$13$dHt7Q37UiPJsOSjoxR3NxOUyM4OQhCtuElfUhD2NcmKH7TwzdQpvy');
        $User->setPuesto('Director de Informatica');

        $manager->persist($User);


        $manager->flush();
    }
}
