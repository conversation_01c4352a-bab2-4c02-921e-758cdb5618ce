<?php

namespace App\Command;

use App\Repository\VentaRepository;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

/**
 * Command to update the liquidada field in Venta entities based on payments
 */
class UpdateVentaLiquidadaCommand extends Command
{
    protected static $defaultName = 'app:update-venta-liquidada';
    protected static $defaultDescription = 'Updates the liquidada field in Venta entities based on payments';

    private $ventaRepository;

    public function __construct(VentaRepository $ventaRepository)
    {
        parent::__construct();
        $this->ventaRepository = $ventaRepository;
    }

    protected function configure()
    {
        $this
            ->setDescription(self::$defaultDescription)
            ->setHelp('This command updates the liquidada field in Venta entities. If the sum of payments equals or exceeds the total, liquidada is set to 1, otherwise it is set to 0.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Updating liquidada field in Venta entities');

        try {
            $updatedCount = $this->ventaRepository->updateLiquidadaField();
            $io->success(sprintf('Successfully updated %d sales.', $updatedCount));

            return 0; // Éxito
        } catch (\Exception $e) {
            $io->error('An error occurred: ' . $e->getMessage());

            return 1; // Error
        }
    }

}