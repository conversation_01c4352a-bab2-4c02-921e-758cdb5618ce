<?php

namespace App\Form;

use App\Entity\Cliente;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;

class AgregarClienteType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('nombre',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
            'label' => 'Nombre '])
            ->add('apellidopaterno',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
            'label' => 'Apellido paterno '])
            ->add('apellidomaterno',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
            'label' => 'Apellido materno ',
            ])
            ->add('telefono',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Teléfono '])
            ->add('email',EmailType::class, [
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Correo electrónico',
                'required' => false
                ])
            ->add('numeroempleado',TextType::class,[
                    'attr' => [
                        'class'=>'form-control'
                    ],
                    'label_attr'=>[
                        'class'=>'form-label',
                    ],
                    'label' => 'Número de empleado ',
                    'required' => false
                    
                ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Cliente::class,
        ]);
    }
}
