<?php
namespace App\Form\DataTransformer;

use App\Entity\Empresacliente;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\DataTransformerInterface;
use Symfony\Component\Form\Exception\TransformationFailedException;

class EmpresaClienteToStringTransformer implements DataTransformerInterface
{
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /**
     * Transforms an Empresacliente object to a string (nombre).
     */
    public function transform($empresa): string
    {
        if (null === $empresa) {
            return '';
        }
        return $empresa->getNombre();
    }

    /**
     * Transforms a string (nombre) to an Empresacliente object.
     */
    public function reverseTransform($nombre)
    {
        // Manejar valores nulos, vacíos o solo espacios en blanco
        if (!$nombre || trim($nombre) === '') {
            return null;
        }

        $nombreTrimmed = trim($nombre);
        $empresa = $this->entityManager
            ->getRepository(Empresacliente::class)
            ->findOneBy(['nombre' => $nombreTrimmed]);
        if (null === $empresa) {
            // Si no existe, la creamos
            $empresa = new Empresacliente();
            $empresa->setNombre($nombreTrimmed);
            $this->entityManager->persist($empresa);
            $this->entityManager->flush();
        }
        return $empresa;
    }
}
