<?php

namespace App\Form;

use App\Entity\Cliente;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\FileType;

use Symfony\Component\Validator\Constraints\File;

class SedenaType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('nombre',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Nombre(s)'
            ])
            ->add('apellidopaterno',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Apellido paterno '
            ])
            ->add('apellidomaterno',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3 employee-number',
                ],
                'label' => 'Apellido materno ',
                'required' => false,
            ])
            ->add('numeroempleado',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Matrícula ',
            ])
            ->add('edad',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Edad '
            ]) 
            ->add('genero',ChoiceType::class,[
                'attr' => [
                    'class'=>'form-select'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'choices'  => [
                    'Femenino' => 'Femenino',
                    'Masculino' => 'Masculino',
                ],
                'label' => 'Sexo ',
            ])
            ->add('telefono',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Teléfono '
            ])
            ->add('comentarios',ChoiceType::class,[
                'attr' => [
                    'class'=>'form-select'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'choices'  => [
                    'Activo' => 'ACTIVO',
                    'Inactivo' => 'INACTIVO',
                ],
                'label' => 'Situación del militar ',
            ])
            ->add('beneficiarytype',ChoiceType::class,[
                'attr' => [
                    'class'=>'form-select'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'choices'  => [
                    'Titular' => 'HOLDER',
                    'Hijo' => 'HIJO',
                    'Cónyuge' => "CONYUGE",
                    'Hermanos' => 'HERMANOS',
                ],
                'label' => 'Derechohabiente ',
            ])
            ->add('doctorname',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Nombre del médico cirujano oftalmólogo tratante ',
            ])
            ->add('file', FileType::class, [
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Receta', 
                'mapped' => false,
                'constraints' => [
                    new File([
                        'maxSize' => '5024k',
                        'mimeTypes' => [
                            'application/pdf',
                            'application/x-pdf',
                            'image/jpeg',
                            'image/png'
                        ],
                        'mimeTypesMessage' => 'Sube un archivo válido',
                        
                    ])
                ],
            ])
            ->add('Enviar', SubmitType::class,[
                'attr' => [
                    'class'=>'submit-button mt-3',
                ],
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Cliente::class,
        ]);
    }
}
