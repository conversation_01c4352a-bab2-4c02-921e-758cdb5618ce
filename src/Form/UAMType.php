<?php

namespace App\Form;

use App\Entity\Cliente;
use App\Entity\Unidad;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Doctrine\ORM\EntityRepository;

use Symfony\Component\Validator\Constraints\File;

class UAMType extends AbstractType
{

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('nombre',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Nombre(s)'
            ])
            ->add('apellidopaterno',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Apellido paterno '
            ])
            ->add('apellidomaterno',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3 employee-number',
                ],
                'label' => 'Apellido materno ',
                'required' => false,
            ])
            ->add('unidadIdunidad',EntityType::class,[
                'class' => Unidad::class,
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Unidad académica',
                'query_builder' => function (EntityRepository $er) use ($options) {
                    // Define your custom query here to retrieve specific entities.
                    return $er->createQueryBuilder('u')
                        ->innerJoin('u.empresaclienteIdempresacliente','e')
                        ->where('e.nombre LIKE :schoolName')
                        ->setParameter('schoolName', '%' . trim($options['school']) . '%'); // Note the '%' symbols for a partial match
                },
            ])
         /*   ->add('ocupacion',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Carrera '
            ])
*/
         ->add('ocupacion', ChoiceType::class, [
             'attr' => [
                 'class'=>'form-control'
             ],
             'label_attr'=>[
                 'class'=>'form-label mt-3',
             ],
             'label' => 'Carrera ',
             'choices'  => [
                 'Licenciatura en Administración Industrial' => 'Licenciatura en Administración Industrial',
                 'Ingeniería en Informática' => 'Ingeniería en Informática',
                 'Ingeniería en Transporte' => 'Ingeniería en Transporte',
                 'Ingeniería Ferroviaria' => 'Ingeniería Ferroviaria',
                 'Ingeniería Industrial' => 'Ingeniería Industrial',
                 'Licenciatura en Ciencias de la Informática' => 'Licenciatura en Ciencias de la Informática'
             ],
             'placeholder' => 'Seleccione una opción'
         ])
            /*->add('schoolyearsemester',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Trimestre ',
            ])*/
            ->add('schoolyearsemester', ChoiceType::class, [
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Trimestre ',
                'choices'  => [
                    '1' => '1',
                    '2' => '2',
                    '3' => '3',
                    '4' => '4',
                    '5' => '5',
                    '6' => '6',
                    '7' => '7',
                    '8' => '8',
                    '9' => '9',
                    '10' => '10'
                ],
                'placeholder' => 'Seleccione una opción'
            ])
            ->add('numeroempleado',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Matrícula ',
            ])
            ->add('genero',ChoiceType::class,[
                'attr' => [
                    'class'=>'form-select'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'choices'  => [
                    'Femenino' => 'Femenino',
                    'Masculino' => 'Masculino',
                    'Otro' => 'Otro',
                ],
                'label' => 'Género',
            ])
            ->add('edad',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Edad '
            ])
            ->add('email',EmailType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Correo electrónico '
            ])
            ->add('telefono',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Teléfono '
            ])

            ->add('codigopostal',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Código postal '
            ])

            ->add('calle',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Calle ',
            ])

            ->add('numero',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'No. exterior ',
            ])
            
            ->add('colonia',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label mt-3',
                ],
                'label' => 'Colonia '
            ])
            ->add('Enviar', SubmitType::class,[
                'attr' => [
                    'class'=>'submit-button mt-3',
                ],
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Cliente::class,
            'school' => 'test',
        ]);
    }
}
