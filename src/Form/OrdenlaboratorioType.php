<?php

namespace App\Form;

use App\Entity\Stockventa;
use App\Entity\Material;
use App\Entity\Disenolente;
use App\Entity\Tratamiento;

use App\Entity\Ordenlaboratorio;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;

use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Form\FormError;

class OrdenlaboratorioType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $addOptions = [];
        for ($i = 0; $i <= 3; $i += 0.25) {
            $addOptions[(string)$i] = $i;
        }

        $esfOptions = [];
        for ($i = -15; $i <= 15; $i += 0.25) {
            $esfOptions[(string)$i] = $i;
        }

        $cilOptions = [];
        for ($i = 0; $i >= -15; $i -= 0.25) {
            $cilOptions[(string)$i] = $i;
        }

        $ejeOptions = [];
        for ($i = 0; $i <= 180; $i += 5) {
            $ejeOptions[(string)$i] = $i;
        }


        $builder
            ->add('tipoorden', ChoiceType::class, [
                'attr' => [
                    'class' => 'form-control'
                ],
                'label_attr' => [
                    'class' => 'form-label',
                ],
                'choices' => [
                    'Armazón' => '1',
                    'Lente de contacto' => '2',
                ],
                'label' => 'Tipo de orden '
            ])
            ->add('tipolentecontacto', ChoiceType::class, [
                'attr' => [
                    'class' => 'form-control lente-contacto'
                ],
                'label_attr' => [
                    'class' => 'form-label lente-contacto',
                ],
                'choices' => [
                    'Blando' => '1',
                    'Rígido' => '2',
                ],
                'required' => false,
                'label' => 'Tipo de lente de contacto '
            ])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++   
            ->add('esferaod', ChoiceType::class, [
                'choices' => $esfOptions,
                'attr' => [
                    'class' => 'form-control ordenl-select'
                ],
                'label_attr' => [
                    'class' => 'form-label d-none',
                ],
                'label' => 'Esfera ojo derecho '
            ])
            ->add('cilindrood', ChoiceType::class, [
                'choices' => $cilOptions,
                'attr' => [
                    'class' => 'form-control ordenl-select'
                ],
                'label_attr' => [
                    'class' => 'form-label d-none',
                ],
                'label' => 'Cilindro ojo derecho '
            ])
            ->add('ejeod', ChoiceType::class, [
                'choices' => $ejeOptions,
                'attr' => [
                    'class' => 'form-control ordenl-select'
                ],
                'label_attr' => [
                    'class' => 'form-label d-none',
                ],
                'label' => 'Eje ojo derecho '
            ])
            ->add('addordenlaboratorio', ChoiceType::class, [
                'choices' => $addOptions,
                'attr' => [
                    'class' => 'form-control ordenl-select'
                ],
                'label_attr' => [
                    'class' => 'form-label d-none',
                ],
                'label' => 'Adición'
            ])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++  
            ->add('esferaoi', ChoiceType::class, [
                'choices' => $esfOptions,
                'attr' => [
                    'class' => 'form-control ordenl-select'
                ],
                'label_attr' => [
                    'class' => 'form-label d-none',
                ],
                'label' => 'Esfera ojo izquierdo '
            ])
            ->add('cilindrooi', ChoiceType::class, [
                'choices' => $cilOptions,
                'attr' => [
                    'class' => 'form-control ordenl-select'
                ],
                'label_attr' => [
                    'class' => 'form-label d-none',
                ],
                'label' => 'Cilindro ojo izquierdo '
            ])
            ->add('ejeoi', ChoiceType::class, [
                'choices' => $ejeOptions,
                'attr' => [
                    'class' => 'form-control ordenl-select '
                ],
                'label_attr' => [
                    'class' => 'form-label d-none',
                ],
                'label' => 'Eje ojo izquierdo '
            ])
            ->add('cb', TextType::class, [
                'attr' => [
                    'class' => 'form-control lente-contacto'
                ],
                'label_attr' => [
                    'class' => 'form-label d-none',
                ],
                'required' => false,
                'label' => 'Curva base'
            ])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            /*->add('avlejosoi',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'AV lejos ojo izquierdo '
            ])
            ->add('avcercasaddoi',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'AV cerca s/Add ojo izquierdo '
            ])
            ->add('avcercacaddoi',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'AV cerca c/Add ojo izquierdo '
            ])*/
            ->add('diam', TextType::class, [
                'attr' => [
                    'class' => 'form-control lente-contacto'
                ],
                'label_attr' => [
                    'class' => 'form-label d-none',
                ],
                'required' => false,
                'label' => 'Diámetro '
            ])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('armazoncliente', ChoiceType::class, [
                'attr' => [
                    'class' => 'form-control lente-armazon'
                ],
                'label_attr' => [
                    'class' => 'form-label lente-armazon',
                ],
                'choices' => [
                    'No' => '0',
                    'Sí' => '1',
                ],
                'label' => 'Armazón propio del cliente '
            ])
            ->add('dip', TextType::class, [
                'attr' => [
                    'class' => 'form-control lente-armazon'
                ],
                'label_attr' => [
                    'class' => 'form-label d-none',
                ],
                'required' => false,
                'label' => 'DIP'
            ])
            ->add('ao', TextType::class, [
                'attr' => [
                    'class' => 'form-control lente-armazon'
                ],
                'label_attr' => [
                    'class' => 'form-label d-none',
                ],
                'required' => false,
                'label' => 'AO '
            ])
            ->add('aco', TextType::class, [
                'attr' => [
                    'class' => 'form-control lente-armazon'
                ],
                'label_attr' => [
                    'class' => 'form-label d-none',
                ],
                'required' => false,
                'label' => 'ACO '
            ])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('disenolenteIddisenolente', EntityType::class, [
                'class' => Disenolente::class,
                'attr' => [
                    'class' => 'form-control lente-armazon'
                ],
                'label_attr' => [
                    'class' => 'form-label lente-armazon',
                ],
                'label' => 'Diseño ',
                'required' => false,
            ])
            ->add('materialIdmaterial', EntityType::class, [
                'class' => Material::class,
                'attr' => [
                    'class' => 'form-control lente-armazon'
                ],
                'label_attr' => [
                    'class' => 'form-label lente-armazon',
                ],
                'label' => 'Material ',
                'required' => false,
            ])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('observaciones', TextareaType::class, [
                'attr' => [
                    'class' => 'form-control '
                ],
                'label_attr' => [
                    'class' => 'form-label form',
                ],
                'label' => 'Observaciones',
                'required' => false,
            ])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('idflujo', HiddenType::class, [
                'attr' => [
                    'class' => 'form-control'
                ],
                'label_attr' => [
                    'class' => 'form-label',
                ],
                'label' => 'IDflujo ',
                'mapped' => false,
            ])
            ->add('idstockventa', HiddenType::class, [
                'attr' => [
                    'class' => 'form-control'
                ],
                'label_attr' => [
                    'class' => 'form-label',
                ],
                'label' => 'IDSV ',

                'mapped' => false,
            ])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('suggestions', TextareaType::class, [
                'attr' => [
                    'class' => 'form-control '
                ],
                'label_attr' => [
                    'class' => 'form-label form',
                ],
                'label' => 'Sugerencias',
                'required' => false,
            ])
            ->add('Enviar', SubmitType::class, [
                'attr' => [
                    'class' => 'form-control btn'
                ],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Ordenlaboratorio::class,
        ]);
    }
}
