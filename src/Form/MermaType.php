<?php

namespace App\Form;

use App\Entity\Merma;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\ButtonType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;

class MermaType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            /*->add('tipo',ChoiceType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'choices'  => [
                    'Robo' => '0',
                    'Errores' => '1',
                    'Extravío' => '2',
                    
                ],
            'label' => 'Tipo de merma '])*/
            ->add('detalleincidencia', TextareaType::class, [
                'attr' => [
                    'class'=>'form-control '
                ],
                'label_attr'=>[
                    'class'=>'form-label form',
                ],
                'label' => 'Detalle de merma', 

            ])
            ->add('fechaincidencia',DateType::class,[
                'attr' => [
                    'class'=>'form-control fecha',
                    'autocomplete'=>"off",
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Fecha de incidencia ',
             'widget' => 'single_text',
             'html5' => false,
             'format' => 'dd/MM/yyyy',
             ])

            /*->add('cantidad',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Cantidad ',
            
             
             ])*/

            ->add('Guardar', ButtonType::class,[
                'attr' => [
                    'class'=>'form-control btn btn-primary',
                    'onclick'=>'guardarFormularioMerma()',
                ],
            ])

            //->add('stockIdstock')
            //->add('usuarioIdusuario')
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Merma::class,
        ]);
    }
}
