<?php

namespace App\Form;

use App\Entity\Cliente;
use App\Entity\Empresacliente;
use App\Form\DataTransformer\EmpresaClienteToStringTransformer;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\ButtonType;
class ClientSaleType extends AbstractType
{
    private $transformer;

    public function __construct(EmpresaClienteToStringTransformer $transformer)
    {
        $this->transformer = $transformer;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('nombre',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Nombre(s)'
            ])
            ->add('apellidopaterno',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Apellido paterno '
            ])
            ->add('apellidomaterno',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Apellido materno ',
            ])
            ->add('telefono',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Teléfono '
            ])  
            ->add('email',EmailType::class, [
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Correo electrónico ',
                'required' => false,
            ])
            ->add('numeroempleado',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label employee-number',
                ],
                'label' => 'Número de empleado ',
                'required' => false,
            ])
            ->add('empresaclienteIdempresacliente', TextType::class, [
                'attr' => [
                    'class' => 'form-control',
                    'required' => false,
                ],
                'label_attr' => [
                    'class' => 'form-label',
                ],
                'label' => 'Empresa del cliente',
                'required' => false,
            ])
            ->add('fechanacimiento', DateType::class,[
                'attr' => [
                    'class'=>' fecha form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Fecha de nacimiento ',
                'widget' => 'single_text',
                'html5' => false,
                'format' => 'dd/MM/yyyy',
            ])
            ->add('ocupacion',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Ocupación ',
                'required' => false
            ])
            ->add('genero',ChoiceType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label ',
                ],
                'choices'  => [
                    'Femenino' => 'Femenino',
                    'Masculino' => 'Masculino',
                    'Otro' => 'Otro',
                    
                ],
                'label' => 'Género ',
                'required' => false,
            ])
            ->add('beneficiarytype',ChoiceType::class,[
                'attr' => [
                    'class'=>' beneficiary form-control d-none'
                ],
                'label_attr'=>[
                    'class'=>'form-label beneficiary d-none',
                ],
                'choices'  => [
                    'Cónyuge' => "CONYUGE",
                    'Padres' => "PADRES",
                    'Hijo' => "HIJO",
                    'Concubina' => "CONCUBINA",
                    'Otro' => 'OTRO',
                ],
                'label' => 'Tipo de beneficiario ',
                'required' => false,
            ])
            ->add('Guardar', ButtonType::class,[
                'attr' => [
                    'class'=>' btn btn-primary ',
                    'onclick'=>'guardarFormularioBeneficiario()',
                    //'data-bs-dismiss'=>"mod",
                    
                ],
            ])
        ;
        $builder->get('empresaclienteIdempresacliente')
            ->addModelTransformer($this->transformer);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Cliente::class,
        ]);
    }
}
