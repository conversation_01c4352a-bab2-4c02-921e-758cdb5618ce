<?php

namespace App\Form;

use App\Entity\Proveedortelefono;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use App\Entity\Proveedorcontacto; 
use Symfony\Component\Form\Extension\Core\Type\HiddenType;


class TelefonoType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {

        $idproveedorcontacto = $options['idproveedorcontacto'];

        $builder
        ->add('nombre', TextType::class, [
            'attr' => ['class' => 'form-control'],
            'label_attr' => ['class' => 'form-label'],
            'label' => 'Nombre',
            'required' => true,
        ])
        ->add('telefono', TextType::class, [
            'attr' => ['class' => 'form-control'],
            'label_attr' => ['class' => 'form-label'],
            'label' => 'Teléfono',
            'required' => true,
        ])
        ->add('proveedorcontactoIdproveedorcontacto', EntityType::class, [
            'class' => Proveedorcontacto::class,
            'attr' => ['style' => 'display:none'],
        ])    
        ->add('Enviar', SubmitType::class, [
            'attr' => [
                'class' => 'form-control',
                'label' => 'Guardar Teléfono'
            ],
        ]);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => Proveedortelefono::class,
            'idproveedorcontacto' => null,
        ]);
    }
}
