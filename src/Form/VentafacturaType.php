<?php


namespace App\Form;

use App\Entity\Ventafactura;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

use Symfony\Component\Form\Extension\Core\Type\SubmitType;


class VentafacturaType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('montoventa', NumberType::class, [
                'label' => 'Monto',
                'attr' => ['class' => 'form-control'],
                'row_attr' => ['class' => 'form-group ']
            ])
            ->add('rfcemisor', TextType::class, [
                'label' => 'RFC Emisor',
                'attr' => ['class' => 'form-control'],
                'row_attr' => ['class' => 'form-group ']
            ])
            ->add('rfcreceptor', TextType::class, [
                'label' => 'RFC Receptor',
                'attr' => ['class' => 'form-control'],
                'row_attr' => ['class' => 'form-group ']
            ])
            ->add('regimenfiscal', TextType::class, [
                'label' => 'Régimen Fiscal',
                'attr' => ['class' => 'form-control'],
                'row_attr' => ['class' => 'form-group ']
            ])
            ->add('usucfdi', TextType::class, [
                'label' => 'Usuario CFDI',
                'attr' => ['class' => 'form-control'],
                'row_attr' => ['class' => 'form-group ']
            ])
            ->add('nombreemisor', TextType::class, [
                'label' => 'Nombre Emisor',
                'attr' => ['class' => 'form-control'],
                'row_attr' => ['class' => 'form-group ']
            ])
            ->add('nombrereceptor', TextType::class, [
                'label' => 'Nombre Receptor',
                'attr' => ['class' => 'form-control'],
                'row_attr' => ['class' => 'form-group ']
            ])
            ->add('metodopago', TextType::class, [
                'label' => 'Método de Pago',
                'attr' => ['class' => 'form-control'],
                'row_attr' => ['class' => 'form-group ']
            ])
            ->add('formapago', TextType::class, [
                'label' => 'Forma de Pago',
                'attr' => ['class' => 'form-control'],
                'row_attr' => ['class' => 'form-group ']
            ])
            ->add('lugarexpedicion', TextType::class, [
                'label' => 'Lugar de Expedición',
                'attr' => ['class' => 'form-control'],
                'row_attr' => ['class' => 'form-group ']
            ])
            ->add('fechacomprobante', DateTimeType::class, [
                'widget' => 'single_text',
                'label' => 'Fecha del Comprobante',
                'attr' => ['class' => 'form-control'],
                'row_attr' => ['class' => 'form-group ']
            ])
            ->add('file', FileType::class, [
                'label' => 'Archivo ZIP',
                'required' => true,
                'attr' => ['class' => 'form-control'],
                'row_attr' => ['class' => 'form-group ']
            ]);
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => Ventafactura::class,
        ]);
    }
}

