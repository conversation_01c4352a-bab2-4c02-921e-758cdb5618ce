<?php

namespace App\Form;

use App\Entity\Cliente;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Doctrine\ORM\EntityRepository;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;

class AgregarBeneficiarioType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('nombre', TextType::class, [
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Ingrese el nombre'
                ],
                'label_attr' => [
                    'class' => 'form-label fw-bold',
                ],
                'label' => 'Nombre ',
                'row_attr'=> ['class' => 'col-12 col-md-4']
            ])
            ->add('apellidopaterno', TextType::class, [
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Ingrese el apellido paterno'
                ],
                'label_attr' => [
                    'class' => 'form-label fw-bold',
                ],
                'label' => 'Apellido Paterno ',
                'row_attr'=> ['class' => 'col-6 col-md-4']
            ])
            ->add('apellidomaterno', TextType::class, [
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Ingrese el apellido materno'
                ],
                'label_attr' => [
                    'class' => 'form-label fw-bold',
                ],
                'label' => 'Apellido Materno ',
                'row_attr'=> ['class' => 'col-6 col-md-4']
            ])
            ->add('telefono', NumberType::class, [
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Ingrese el número de teléfono'
                ],
                'label_attr' => [
                    'class' => 'form-label fw-bold my-4',
                ],
                'label' => 'Teléfono ',
                'required' => false,
                'row_attr'=> ['class' => 'col-6 col-md-4']
            ])
            ->add('email', EmailType::class, [
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Ingrese el correo electrónico'
                ],
                'label_attr' => [
                    'class' => 'form-label fw-bold my-4',
                ],
                'label' => 'Correo Electrónico',
                'required' => false,
                'row_attr'=> ['class' => 'col-6 col-md-4']
            ])
            ->add('numeroempleado', TextType::class, [
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Ingrese el número de empleado'
                ],
                'label_attr' => [
                    'class' => 'form-label fw-bold my-4',
                ],
                'label' => 'Número de Empleado ',
                'required' => false,
                'row_attr'=> ['class' => 'col-6 col-md-4']
            ])
            ->add('holder', EntityType::class, [
                'class' => Cliente::class,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('c')
                        ->where('c.holder IS NULL') 
                        ->andWhere('c.email != :emailvalue')
                        ->andWhere('c.email IS NOT NULL')
                        ->andWhere('c.numeroempleado IS NOT NULL')
                        ->andWhere('c.numeroempleado != :emailvalue')
                        ->andWhere('c.status = 1')
                        ->setParameter('emailvalue', "");
                },
                'choice_label' => function (Cliente $cliente) {
                    return sprintf(
                        '%s - %s %s (%s)', 
                        
                        $cliente->getNumeroempleado(),
                        $cliente->getNombre(), 
                        $cliente->getApellidopaterno(), 
                        $cliente->getEmail() ?? 'Sin correo'
                    );
                },
                'placeholder' => 'Seleccione un cliente',
                'attr' => [
                    'class' => 'form-control ', 
                    'data-placeholder' => 'Seleccione un cliente', 
                ],
                'label_attr' => [
                    'class' => 'form-label my-4',
                ],
                'label' => 'Titular Asociado',
                'required' => false,
                'row_attr'=> ['class' => 'col']
            ])
            ->add('beneficiarytype',ChoiceType::class,[
                'attr' => [
                    'class'=>' form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label my-4',
                ],
                'choices'  => [
                    'Cónyuge' => "CONYUGE",
                    'Padres' => "PADRES",
                    'Hijo' => "HIJO",
                    'Concubina' => "CONCUBINA",
                    'Otro' => 'OTRO',
                ],
                'label' => 'Relación',
                'required' => false,
                'row_attr'=> ['class' => 'col-4']
            ])            
            ->add('submit', SubmitType::class, [
                'attr' => [
                    'class' => 'btn btn-primary mt-3'
                ],'label' => 'Crear Nuevo Cliente'
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Cliente::class,
        ]);
    }
}
