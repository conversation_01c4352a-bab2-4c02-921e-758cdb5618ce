<?php

namespace App\Form;

use App\Entity\Beneficiario;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\ButtonType;

class BeneficiarioType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('nombre', TextType::class, [
                'attr' => [
                    'class' => 'form-control',
                    'autocomplete' => 'off'
                ],
                'label_attr' => [
                    'class' => 'form-label',
                ],
                'label' => 'Nombre '
            ])
            ->add('fechanacimiento', DateType::class, [
                'attr' => [
                    'class' => 'form-control fecha',
                    'autocomplete' => 'off'
                ],
                'label_attr' => [
                    'class' => 'form-label',
                ],
                'label' => 'Fecha de nacimiento ',
                'widget' => 'single_text',
                'html5' => false,
                'format' => 'dd/MM/yyyy',
                'required' => false,
            ])
            ->add('tipo', ChoiceType::class, [
                'attr' => [
                    'class' => 'form-control'
                ],
                'label_attr' => [
                    'class' => 'form-label',
                ],
                'choices' => [
                    'Conyuge' => "CONYUGE",
                    'Padres' => "PADRES",
                    'Hijo' => "HIJO",
                    'Concubina' => "CONCUBINA",
                    'Otro' => 'OTRO',
                ],
                'label' => 'Tipo de beneficiario '
            ])
            ->add('sexo', ChoiceType::class, [
                'attr' => [
                    'class' => 'form-control'
                ],
                'label_attr' => [
                    'class' => 'form-label',
                ],
                'choices' => [
                    'Femenino' => "F",
                    'Masculino' => "M",
                ],
                'label' => 'Sexo '
            ])
            ->add('Guardar', ButtonType::class, [
                'attr' => [
                    'class' => 'form-control btn btn-primary ',
                    'onclick' => 'guardarFormularioBeneficiario()',
                    'data-bs-dismiss' => "mod",

                ],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Beneficiario::class,
        ]);
    }
}
