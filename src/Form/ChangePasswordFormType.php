<?php

namespace App\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\RepeatedType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Regex;

class ChangePasswordFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('plainPassword', RepeatedType::class, [
                'type' => PasswordType::class,
                'first_options' => [
                    'attr' => ['autocomplete' => 'new-password', 'class' => 'form-control', "onkeydown" => "checkPassword()", "onkeyup" => "checkPassword()"],
                    'label_attr'=>[
                        'class'=>'form-label mt-3 text-white fs-4',
                    ],
                    'constraints' => [
                        new NotBlank([
                            'message' => 'Por favor ingresa una contraseña',
                        ]),
                        new Length([
                            'min' => 8,
                            'minMessage' => 'La contraseña debe tener al menos {{ limit }} caracteres.',
                            // max length allowed by Symfony for security reasons
                            'max' => 4096,
                        ]),
                        new Regex([
                            'pattern' => '/[A-Z]/',
                            'message' => 'La contraseña debe contener al menos una letra mayúscula.'
                        ]),
                        new Regex([
                            'pattern' => '/[a-z]/',
                            'message' => 'La contraseña debe contener al menos una letra minúscula.'
                        ]),
                        new Regex([
                            'pattern' => '/\d/',
                            'message' => 'La contraseña debe contener al menos un número.'
                        ]),
                        new Regex([
                            'pattern' => '/[^a-zA-Z\d]/',
                            'message' => 'La contraseña debe contener al menos un carácter especial.'
                        ])
                    ],
                    'error_bubbling' => true,
                    'label' => 'Nueva contraseña',
                ],
                'second_options' => [
                    'attr' => ['autocomplete' => 'new-password', 'class' => 'form-control', "onkeydown" => "checkPassword()", "onkeyup" => "checkPassword()"],
                    'label' => 'Repita la nueva contraseña',
                    'label_attr'=>[
                        'class'=>'form-label mt-3 text-white fs-4',
                    ]
                ],
                'invalid_message' => 'Los campos de contraseña deben coincidir',
                // Instead of being set onto the object directly,
                // this is read and encoded in the controller
                'mapped' => false,
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([]);
    }
}
