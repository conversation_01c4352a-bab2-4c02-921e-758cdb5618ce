<?php

namespace App\Form;

use App\Entity\Cliente;
use App\Entity\Empresacliente;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\ButtonType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Validator\Constraints\Regex;

class ClienteExpedienteType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        
        $builder
        
            ->add('tipocliente',ChoiceType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'choices'  => [
                    'Público general' => 'Público general',
                    'Corporativo' => 'Corporativo',
                ],
                'label' => 'Tipo de cliente '
            ])

            ->add('empresaclienteIdempresacliente',EntityType::class,[
                'class' => Empresacliente::class,
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Empresa del cliente ',
                'required' => false
            ])

            ->add('ocupacion',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Ocupación '
            ])
            
            ->add('isstudent',ChoiceType::class,[
                'attr' => [
                    'class'=>'form-control', 'onchange' => "checkIsStudent()"
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'choices'  => [
                    'Sí' => '1',
                    'No' => '0'
                ],
                'label' => '¿Es estudiante?'
            ])

            ->add('groupclient',TextType::class,[
                'attr' => [
                    'class'=>'form-control student-input'
                ],
                'label_attr'=>[
                    'class'=>'form-label employee-number',
                ],
                'label' => 'Grupo ',
                'required' => false,
            ])

            ->add('schoolyearsemester',TextType::class,[
                'attr' => [
                    'class'=>'form-control student-input'
                ],
                'label_attr'=>[
                    'class'=>'form-label employee-number',
                ],
                'label' => 'Año escolar / Semestre ',
                'required' => false,
            ])

            ->add('scholarlid',TextType::class,[
                'attr' => [
                    'class'=>'form-control student-input'
                ],
                'label_attr'=>[
                    'class'=>'form-label employee-number',
                ],
                'label' => 'Matrícula ',
                'required' => false,
            ])

            ->add('schedule',ChoiceType::class,[
                'attr' => [
                    'class'=>'form-control student-input'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'choices'  => [
                    'Matutino' => 'Matutino',
                    'Vespertino' => 'Vespertino',
                ],
                'label' => 'Turno ',
                'required' => false
            ])

            ->add('numeroempleado',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label employee-number',
                ],
                'label' => 'Número de empleado ',
                'required' => false,
            ])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++            
            ->add('apellidopaterno',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Apellido paterno '
            ])
            ->add('apellidomaterno',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Apellido materno ',
            ])
            ->add('nombre',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Nombre(s)'
            ])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('fechanacimiento', DateType::class,[
                'attr' => [
                    'class'=>'form-control fecha'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Fecha de nacimiento ',
                'widget' => 'single_text',
                'html5' => false,
                'format' => 'dd/MM/yyyy',
            ])
            ->add('genero',ChoiceType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'choices'  => [
                    'Femenino' => 'Femenino',
                    'Masculino' => 'Masculino',
                    'Otro' => 'Otro',
                    
                ],
                'label' => 'Género '
            ])
            ->add('email',EmailType::class, [
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Correo electrónico '
            ])
            ->add('telefono',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Teléfono '
            ])    
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('tipocalle',ChoiceType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'choices'  => [
                    'Avenida' => 'Avenida',
                    'Calle' => 'Calle',
                ],
                'label' => 'Tipo de calle ',
                'required' => false,
            ])
            ->add('calle',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Calle ',
                'required' => false,
            ])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('numero',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Número exterior ',
                'required' => false,
            ])
            ->add('codigopostal',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Código postal ',
                'constraints' => [
                    new Regex([
                        'pattern' => '/^(?=(?:.*\d){5})(?!.*\d{6,}).*$/',
                        'message' => 'El código postal debe contener 5 números.'
                    ])
                ],
                
            ])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

            ->add('alcaldia',HiddenType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Alcaldia h ',
            ])
            ->add('colonia',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Colonia '
            ])
            
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('entidadfederativa',ChoiceType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'choices'  => [
                    "Aguascalientes"=>"Aguascalientes","Baja California"=>"Baja California","Baja California Sur"=>"Baja California Sur","Campeche"=>"Campeche","Coahuila"=>"Coahuila","Colima"=>"Colima","Chiapas"=>"Chiapas","Chihuahua"=>"Chihuahua","Ciudad de Mexico"=>"Ciudad de Mexico","Durango"=>"Durango","Guanajuato"=>"Guanajuato","Guerrero"=>"Guerrero","Hidalgo"=>"Hidalgo","Jalisco"=>"Jalisco","Estado de Mexico"=>"Estado de Mexico","Michoacan"=>"Michoacan","Morelos"=>"Morelos","Nayarit"=>"Nayarit","Nuevo Leon"=>"Nuevo Leon","Oaxaca"=>"Oaxaca","Puebla"=>"Puebla","Queretaro"=>"Queretaro","Quintana Roo"=>"Quintana Roo","San Luis Potosi"=>"San Luis Potosi","Sinaloa"=>"Sinaloa","Sonora"=>"Sonora","Tabasco"=>"Tabasco","Tamaulipas"=>"Tamaulipas","Tlaxcala"=>"Tlaxcala","Veracruz"=>"Veracruz","Yucatan"=>"Yucatan","Zacatecas"=>"Zacatecas",
                ],
                'label' => 'Estado ',
            ])
            
            /*->add('localidad',TextType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'Localidad '
            ])*/
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('idflujo',HiddenType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'IDflujo ',
                'mapped' => false,
            ])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('Enviar', SubmitType::class,[
                'attr' => [
                    'class'=>'form-control btn',
                ],
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Cliente::class,
        ]);
    }
}