<?php

namespace App\Form;

use App\Entity\Material;
use App\Entity\Disenolente;


use App\Entity\Graduacion;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;

class GraduacionType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        
        $builder
            ->add('anamnesis', TextareaType::class, [
                'attr' => [
                    'class'=>'form-control '
                ],
                'label_attr'=>[
                    'class'=>'form-label form',
                ],
                'label' => 'Anamnesis', 

            ])
//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ 
//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ 
            
            ->add('esferaod',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Esfera ojo derecho '])
            
            ->add('esferaoi',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Esfera ojo izquierdo '])
//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ 
   
            ->add('clindrood',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Cilindro ojo derecho '])

            ->add('cilindrooi',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Cilindro ojo izquierdo '])
//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ 

            ->add('ejeod',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Eje ojo derecho '])

            ->add('ejeoi',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Eje ojo izquierdo '])
//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ 

            ->add('addod',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Add ojo derecho '])

            ->add('addoi',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Add ojo izquierdo '])
//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ 

            ->add('avlejosod',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'AV lejos ojo derecho '])

            ->add('avlejosoi',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'AV lejos ojo izquierdo '])
//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ 

            ->add('avcercaod',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'AV cerca ojo derecho '])


            ->add('avcercaoi',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'AV cerca ojo izquierdo '])
//+++++++++++++++++++++++++++ RECOMENDACION ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

            ->add('materialIdmaterial',EntityType::class,[
                'class' => Material::class,
                'required'   => false,
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Recomendación de material '])             
            ->add('tratamiento',TextType::class,[
                'required'   => false,
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
            'label' => 'Recomendación de tratamiento '])

            ->add('disenolenteIddisenolente',EntityType::class,[
                'class' => Disenolente::class,
                'required'   => false,
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Recomendación de diseño ']) 
            
//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            //->add('avsrx')

            ->add('lejosod',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Lejos ojo derecho '])

            ->add('lejosoi',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Lejos ojo izquierdo '])

            ->add('cercaod',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Cerca ojo derecho '])

            ->add('cercaoi',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Cerca ojo izquierdo '])
            
            ->add('cvod',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'CV ojo izquierdo '])
            
            ->add('cvoi',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'CV ojo izquierdo '])
//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('retinoscopiaod',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Retinoscopia ojo derecho '])
            ->add('retinoscopiaoi',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Retinoscopia ojo izquierdo '])

            ->add('subjetivaod',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Subjetiva ojo derecho '])
            ->add('subjetivaoi',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'Subjetiva ojo izquierdo '])

            ->add('avlejanacrxOD',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'AV lejana c/rx ojo derecho '])

             ->add('avlejanacrxOI',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'AV lejana c/rx ojo izquierdo '])

            ->add('avcercasaddOD',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'AV cerca s/add ojo derecho '])

            ->add('avcercasaddOI',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'AV cerca s/add ojo izquierdo '])
            
            ->add('avcercacaddOD',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'AV cerca c/add ojo derecho '])

            ->add('avcercacaddOI',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'AV cerca c/add ojo izquierdo '])
//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('add',NumberType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label'=> 'adiciónjiji'])

             ->add('observaciones', TextareaType::class, [
                'attr' => [
                    'class'=>'form-control '
                ],
                'label_attr'=>[
                    'class'=>'form-label form',
                ],
                'label' => 'Observaciones', 

            ])

             ->add('idflujo',HiddenType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
             'label' => 'IDflujo ',
             
             'mapped' => false,
             
             ])
//+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('Enviar', SubmitType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Graduacion::class,
        ]);
    }
}
