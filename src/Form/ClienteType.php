<?php

namespace App\Form;

use App\Entity\Cliente;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
class ClienteType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('nombre',TextType::class,['label'=>"Nombre del Cliente"])
            ->add('telefono',TextType::class,['label'=>"Número de Teléfono"])
            ->add('email',EmailType::class,['label'=>"Correo Electrónico"])
            ->add('comentarios',TextareaType::class,[ 'label' => 'Notas','required'=>true])
            ->add('numeroempleado',NumberType::class,[ 'label' => 'Número de Empleado','required'=>false])
            ->add('comonosconocio', ChoiceType::class, ['label'=>"cómo nos conoció?",
                'choices'  => [
                    'Redes Sociales' => 'Redes Sociales',
                    'Recomendación' => 'Recomendación',
                    'Facebook' => 'Facebook',
                    'Instagram' => 'Instagram',
                    'Nos encontró' => 'Nos encontró',
                    'Google' => 'Google',
                    'Periódico' => 'Periódico',
                    'Revista' => 'Revista',
                ],
            ])

            ->add('fechanacimiento',DateType::class,['label'=>"Fecha de nacimiento", 'widget' => 'choice',])
            ->add('save', SubmitType::class)
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Cliente::class,
        ]);
    }
}
