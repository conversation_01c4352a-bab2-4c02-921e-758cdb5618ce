<?php

namespace App\Form;

use App\Entity\Proveedoremail;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use App\Entity\Proveedorcontacto; 
use Symfony\Bridge\Doctrine\Form\Type\EntityType;


class EmailType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $idproveedorcontacto = $options['idproveedorcontacto']; // Aquí accedes a la opción
    
        $builder
            ->add('email', TextType::class, [
                'attr' => ['class' => 'form-control'],
                'label_attr' => ['class' => 'form-label'],
                'label' => 'Email',
                'required' => true,
            ])
            ->add('proveedorcontactoIdproveedorcontacto', EntityType::class, [
                'class' => Proveedorcontacto::class,
                'attr' => ['style' => 'display:none'],
            ])            
            ->add('Enviar', SubmitType::class, [
                'attr' => ['class' => 'form-control'],
                'label' => 'Guardar Email',
            ]);
            

    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => Proveedoremail::class,
            'idproveedorcontacto' => null,
        ]);
        
    }
    
}
