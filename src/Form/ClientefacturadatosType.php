<?php

namespace App\Form;

use App\Entity\Clientefacturadatos;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\ButtonType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\HttpFoundation\Request;
use Sonata\AdminBundle\Form\Type\ChoiceFieldMaskType;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Form\FormError;

use Symfony\Component\Validator\Constraints\File;

class ClientefacturadatosType extends AbstractType
{

    public function buildForm(FormBuilderInterface $builder, array $options): void

    {
        $builder
            ->add('folio', NumberType::class, [
                'attr' => [
                    'class' => 'form-control',
                    'step' => 1,
                ],
                'label_attr' => [
                    'class' => 'form-label',
                ],
                'label' => 'Folio de venta ',
                'mapped' => false
            ])
            ->add('email', EmailType::class, [
                'attr' => [
                    'class' => 'form-control'
                ],
                'label_attr' => [
                    'class' => 'form-label',
                ],
                'label' => 'Correo electrónico'
            ])
            ->add('razonsocial', TextType::class, [
                'attr' => [
                    'class' => 'form-control',
                    'oninput' => 'this.value = this.value.toUpperCase()'
                ],
                'label_attr' => [
                    'class' => 'form-label',
                ],
                'label' => 'Razón social'
            ])
            ->add('rfc', TextType::class, [
                'attr' => [
                    'class' => 'form-control',
                    'oninput' => 'this.value = this.value.toUpperCase()'
                ],
                'label_attr' => [
                    'class' => 'form-label',
                ],
                'label' => 'RFC'
            ])
            ->add('codigopostal', NumberType::class, [
                'attr' => [
                    'class' => 'form-control'
                ],
                'label_attr' => [
                    'class' => 'form-label',
                ],
                'label' => 'Código postal'
            ])
            ->add('tipoPersona', ChoiceFieldMaskType::class, [
                'choices' => [
                    'Fisica' => '0',
                    'Moral' => '1',
                ],
                'placeholder' => 'Selecciona un tipo de persona',
                'label' => 'Tipo de persona',
                'attr' => [
                    'class' => 'form-control',
                ],
                'label_attr' => [
                    'class' => 'form-label',
                ],
            ])
            ->add('usocfdi', HiddenType::class)
            ->add('regimenfiscal', HiddenType::class)
            ->add('brochure', FileType::class, [
                'attr' => [
                    'class' => 'form-control'
                ],
                'label_attr' => [
                    'class' => 'form-label',
                ],
                'label' => 'Constancia de situación fiscal',
                'mapped' => false,
                'required' => false,
                'constraints' => [
                    new File([
                        'maxSize' => '1024k',
                        'mimeTypes' => [
                            'application/pdf',
                            'application/x-pdf',
                        ],
                        'mimeTypesMessage' => 'Sube un archivo PDF válido',
                    ])
                ],
            ])
            ->add('notas', TextareaType::class, [
                'attr' => [
                    'class' => 'form-control '
                ],
                'label_attr' => [
                    'class' => 'form-label form',
                ],
                'label' => 'Comentarios adicionales', 'mapped' => false, 'required' => true,])
            ->add('Enviar', SubmitType::class, [
                'attr' => ['class' => 'form-control'],
            ])
            ->addEventListener(
                FormEvents::PRE_SUBMIT,
                function (FormEvent $event) {
                    $form = $event->getForm();
                    $data = $event->getData();

                    if ($data['usocfdi'] == 'Selecciona una opción' || $data['usocfdi'] == "" || $data['usocfdi'] == "") {
                        // If the field value is the specific value, add error
                        $form->addError(new FormError('Selecciona un uso de CDFI válido'));
                    }

                    if ($data['regimenfiscal'] == 'Selecciona una opción' || $data['regimenfiscal'] == "Selecciona un tipo de persona" || $data['usocfdi'] == "Selecciona un tipo de persona") {
                        // If the field value is the specific value, add error
                        $form->addError(new FormError('Selecciona un regimen fiscal válido'));
                    }
                }
            )//->add('Enviar', ButtonType::class, ['attr' => ['onClick' => "guardar()"]])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'selectedUsoCfdi' => null,
            'selectedRegimenFiscal' => null,
        ]);
    }
}
