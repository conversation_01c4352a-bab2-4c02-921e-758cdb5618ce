<?php

namespace App\Form;


use App\Entity\Proveedorcontacto;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;

class ProveedorContactoType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
        ->add('nombre', TextType::class, [
            'attr' => [
                'class' => 'form-control'
            ],
            'label_attr' => [
                'class' => 'form-label',
            ],
            'label' => 'Nombre ',
            'required' => true,
        ])
        ->add('apellidopaterno', TextType::class, [
            'attr' => [
                'class' => 'form-control'
            ],
            'label_attr' => [
                'class' => 'form-label',
            ],
            'label' => 'Apellido Paterno ',
            'required' => true,
        ])
        ->add('apelliidomaterno', TextType::class, [
            'attr' => [
                'class' => 'form-control'
            ],
            'label_attr' => [
                'class' => 'form-label',
            ],
            'label' => 'Apellido Materno ',
            'required' => true,
        ])
        ->add('puesto', TextType::class, [
            'attr' => [
                'class' => 'form-control'
            ],
            'label_attr' => [
                'class' => 'form-label',
            ],
            'label' => 'Puesto',
            'required' => true, 
        ])
        ->add('nota', TextType::class, [
            'attr' => [
                'class' => 'form-control'
            ],
            'label_attr' => [
                'class' => 'form-label',
            ],
            'label' => 'Nota',
            'required' => false, // Campo no requerido
        ])
        ->add('Enviar', SubmitType::class,[
            'attr' => [
                'class'=>'form-control'
            ],
            ])
    ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Proveedorcontacto::class,
        ]);
    }
}
