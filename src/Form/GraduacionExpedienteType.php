<?php

namespace App\Form;

use App\Entity\Material;
use App\Entity\Disenolente;
use App\Entity\Tratamiento;
use App\Entity\Graduacion;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;

class GraduacionExpedienteType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $addOptions = [];
        for ($i = 0; $i <= 3; $i += 0.25) {
            $addOptions['+' . (string) $i] = '+' . $i;
        }

        $esfOptions = [];
        for ($i = -15; $i <= 15; $i += 0.25) {
            if($i > 0)
            {
                $esfOptions['+' . (string) $i] = '+' . $i;
            }
            else
            {
                $esfOptions[(string) $i] = $i;
            }
        }

        $cilOptions = [];
        for ($i = 0; $i >= -15; $i -= 0.25) {

            if($i > 0)
            {
                $cilOptions['+' . (string) $i] = '+' . $i;
            }
            else
            {
                $cilOptions[(string) $i] = $i;
            }

        }

        $ejeOptions = [];
        for ($i = 0; $i <= 180; $i += 5) {

            if($i > 0)
            {
                $ejeOptions['' . (string) $i] = '+' . $i;
            }
            else
            {
                $ejeOptions[(string) $i] = $i;
            }

        }

        $builder
            ->add('anamnesis', TextareaType::class, [
                'attr' => [
                    'class'=>'form-control '
                ],
                'label_attr'=>[
                    'class'=>'form-label form',
                ],
                'label' => 'Anamnesis', 

            ])
           //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
           ->add('isglassesuser',ChoiceType::class,[
                'attr' => [
                    'class'=>'form-control', 'onchange' => "checkPrevData()"
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'choices'  => [
                    'Sí' => '1',
                    'No' => '0'
                ],
                'label' => '¿Usa lentes actualmente?'
            ])

            ->add('hasglassesnow',ChoiceType::class,[
                'attr' => [
                    'class'=>'form-control', 'onchange' => "checkPrevData()"
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'choices'  => [
                    'Sí' => '1',
                    'No' => '0'
                ],
                'label' => '¿Los trae consigo?'
            ])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('gpesferaod',ChoiceType::class,[
                'required'   => false,
                'choices' => $esfOptions,
                'attr' => [
                    'class'=>'form-control prev-grad-input graduation-select'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Esfera ojo derecho '])
            ->add('gpcilindrood',ChoiceType::class,[
                'required'   => false,
                'choices' => $cilOptions,
                'attr' => [
                    'class'=>'form-control prev-grad-input graduation-select'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Cilindro ojo derecho '])
            ->add('gpejeod',ChoiceType::class,[
                'required'   => false,
                'choices' => $ejeOptions,
                'attr' => [
                    'class'=>'form-control prev-grad-input graduation-select'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Eje ojo derecho '])
            ->add('gpaddod',ChoiceType::class,[
                'required'   => false,
                'choices' => $addOptions,
                'attr' => [
                    'class'=>'form-control prev-grad-input graduation-select'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Add '])
            ->add('gpavlejosod',TextType::class,[
                'required'   => false,
                'attr' => [
                    'class'=>'form-control prev-grad-input'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'AV lejos ojo derecho '])
            ->add('gpavcercaod',TextType::class,[
                'required'   => false,
                'attr' => [
                    'class'=>'form-control prev-grad-input'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'AV cerca ojo derecho '])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('gpesferaoi',ChoiceType::class,[
                'required'   => false,
                'choices' => $esfOptions,
                'attr' => [
                    'class'=>'form-control prev-grad-input graduation-select'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Esfera ojo izquierdo '])
            
            ->add('gpcilindrooi',ChoiceType::class,[
                'required'   => false,
                'choices' => $cilOptions,
                'attr' => [
                    'class'=>'form-control prev-grad-input graduation-select'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Cilindro ojo izquierdo '])
            
            ->add('gpejeoi',ChoiceType::class,[
                'required'   => false,
                'choices' => $ejeOptions,
                'attr' => [
                    'class'=>'form-control prev-grad-input graduation-select'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Eje ojo izquierdo '])
            
            ->add('gpavlejosoi',TextType::class,[
                'required'   => false,
                'attr' => [
                    'class'=>'form-control prev-grad-input'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'AV lejos ojo izquierdo '])
            
            ->add('gpavcercaoi',TextType::class,[
                'required'   => false,
                'attr' => [
                    'class'=>'form-control prev-grad-input'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'AV cerca ojo izquierdo '])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('tipolente', ChoiceType::class, [
                'attr' => [
                    'class' => 'form-control prev-grad-input'
                ],
                'label_attr' => [
                    'class' => 'form-label',
                ],
                'choices'  => [
                    'Armazón' => '1',
                    'Lente de contacto' => '2',
                ],
                'label' => 'Tipo de lente',
                'required' => false, // Hacer el campo no requerido
            ])            
            ->add('materialIdmaterial',EntityType::class,[
                'class' => Material::class,
                'required'   => false,
                'attr' => [
                    'class'=>'form-control prev-grad-input frame-lens'
                ],
                'label_attr'=>[
                    'class'=>'form-label frame-lens',
                ],
             'label' => 'Material ']) 
            ->add('disenolenteIddisenolente',EntityType::class,[
                'class' => Disenolente::class,
                'required'   => false,
                'attr' => [
                    'class'=>'form-control prev-grad-input frame-lens'
                ],
                'label_attr'=>[
                    'class'=>'form-label frame-lens',
                ],
             'label' => 'Diseño']) 
            ->add('tratamientoIdtratamiento',EntityType::class,[
                'class' => Tratamiento::class,
                'required'   => false,
                'attr' => [
                    'class'=>'form-control prev-grad-input frame-lens'
                ],
                'label_attr'=>[
                    'class'=>'form-label frame-lens',
                ],
             'label' => 'Tratamiento '])

            ->add('tipolentecontacto',ChoiceType::class,[
                'attr' => [
                    'class'=>'form-control contact-lens'
                ],
                'label_attr'=>[
                    'class'=>'form-label contact-lens',
                ],
                'choices'  => [
                    'Blando' => '1',
                    'Rígido' => '2',
                ],
                'required' => false, 
                'label' => 'Tipo de lente de contacto '
            ])

            ->add('cb',TextType::class,[
                'attr' => [
                    'class'=>'form-control contact-lens'
                ],
                'label_attr'=>[
                    'class'=>'form-label contact-lens',
                ],
                'required' => false,
                'label' => 'Curva base'
            ])

            ->add('diam',TextType::class,[
                'attr' => [
                    'class'=>'form-control contact-lens'
                ],
                'label_attr'=>[
                    'class'=>'form-label contact-lens',
                ],
                'required' => false,
                'label' => 'Diámetro '
            ])
            //++

            ->add('apobservaciones', TextareaType::class, [
                'attr' => [
                    'class'=>'form-control prev-grad-input '
                ],
                'label_attr'=>[
                    'class'=>'form-label form',
                ],
                'label' => 'Observaciones del armazón previo', 
                'required' => false,
            ])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('esfodsf',ChoiceType::class,[
                'required'   => true,
                'choices' => $esfOptions,
                'attr' => [
                    'class'=>'form-control graduation-select'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Esfera ojo derecho subjetiva final'])
            ->add('esfoisf',ChoiceType::class,[
                'required'   => true,
                'choices' => $esfOptions,
                'attr' => [
                    'class'=>'form-control graduation-select'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Esfera ojo izquierdo subjetiva final'])
            ->add('cilodsf',ChoiceType::class,[
                'required'   => true,
                'choices' => $cilOptions,
                'attr' => [
                    'class'=>'form-control graduation-select'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Cilindro ojo derecho subjetiva final'])
            ->add('ciloisf',ChoiceType::class,[
                'required'   => true,
                'choices' => $cilOptions,
                'attr' => [
                    'class'=>'form-control graduation-select'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Cilindro ojo izquierdo subjetiva final'])
            ->add('ejeodsf',ChoiceType::class,[
                'required'   => true,
                'choices' => $ejeOptions,
                'attr' => [
                    'class'=>'form-control graduation-select'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Eje ojo derecho subjetiva final'])
            ->add('ejeoisf',ChoiceType::class,[
                'required'   => true,
                'choices' => $ejeOptions,
                'attr' => [
                    'class'=>'form-control graduation-select'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Eje ojo izquierdo subjetiva final'])
            ->add('avlodsf',TextType::class,[
                'required'   => true,
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Av lejos ojo derecho subjetiva final'])
            ->add('avloisf',TextType::class,[
                'required'   => true,
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Av lejos ojo izquierdo subjetiva final'])
            ->add('avcsaodsf',TextType::class,[
                'required'   => true,
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Av cerca s/add ojo derecho subjetiva final'])
            ->add('avcsaoisf',TextType::class,[
                'required'   => true,
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Av cerca s/add ojo izquierdo subjetiva final'])
            ->add('avccaodsf',TextType::class,[
                'required'   => true,
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Av cerca c/add ojo derecho subjetiva final'])
            ->add('avccaoisf',TextType::class,[
                'required'   => true,
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Av cerca c/add ojo izquierdo subjetiva final'])
            ->add('addsf',ChoiceType::class,[
                'required'   => true,
                'choices' => $addOptions,
                'attr' => [
                    'class'=>'form-control graduation-select'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'ADD subjetiva final'])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('avsrxlejosod',TextType::class,[
                'required'   => true,
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Lejos ojo derecho '])
            ->add('avsrxcercaod',TextType::class,[
                'required'   => true,
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Cerca ojo derecho '])
            ->add('avsrxcvod',TextType::class,[
                'required'   => true,
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'CV ojo derecho '])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('avsrxlejosoi',TextType::class,[
                'required'   => true,
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Lejos ojo izquierdo '])
            ->add('avsrxcercaoi',TextType::class,[
                'required'   => true,
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'Cerca ojo izquierdo '])
            ->add('avsrxcvoi',TextType::class,[
                'required'   => true,
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label d-none',
                ],
            'label' => 'CV ojo izquierdo '])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('sugerencias', TextareaType::class, [
                'required'   => false,
                'attr' => [
                    'class'=>'form-control '
                ],
                'label_attr'=>[
                    'class'=>'form-label form',
                ],
                'label' => 'Sugerencias',

            ])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('idflujo',HiddenType::class,[
                'attr' => [
                    'class'=>'form-control'
                ],
                'label_attr'=>[
                    'class'=>'form-label',
                ],
                'label' => 'IDflujo ',
                
                'mapped' => false,
                
            ])
            //+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            ->add('Enviar', SubmitType::class,[
                'attr' => [
                    'class'=>'btn form-control'
                ],
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Graduacion::class,
        ]);
    }
}
