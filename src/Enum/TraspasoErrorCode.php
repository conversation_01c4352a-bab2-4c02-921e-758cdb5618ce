<?php

namespace App\Enum;

/**
 * Códigos de error para el módulo de Traspaso
 */
class TraspasoErrorCode
{
    // Errores de productos
    public const PRODUCT_NOT_FOUND = 'TRASPASO_001';
    public const PRODUCT_NOT_IN_ORIGIN_BRANCH = 'TRASPASO_002';
    public const PRODUCT_RESERVED = 'TRASPASO_003';
    public const PRODUCT_ALREADY_IN_PENDING_TRANSFER = 'TRASPASO_004';
    public const PRODUCT_UNITARIO_REQUIRES_SKU = 'TRASPASO_005';
    public const PRODUCT_MULTIPLE_STOCK_ENTRIES = 'TRASPASO_006';
    public const PRODUCT_INSUFFICIENT_STOCK = 'TRASPASO_007';
    public const PRODUCT_STOCK_OCCUPIED = 'TRASPASO_008';

    // Errores de autenticación y autorización
    public const USER_NOT_AUTHENTICATED = 'TRASPASO_101';
    public const USER_NOT_AUTHORIZED = 'TRASPASO_102';

    // Errores de entidades no encontradas
    public const BRANCH_NOT_FOUND = 'TRASPASO_201';
    public const TRANSFER_ORDER_NOT_FOUND = 'TRASPASO_202';
    public const WAREHOUSE_TRANSFER_NOT_FOUND = 'TRASPASO_203';
    public const EXIT_ORDER_NOT_FOUND = 'TRASPASO_204';

    // Errores de validación de datos
    public const INVALID_QUANTITY = 'TRASPASO_301';
    public const INVALID_BRANCH_SELECTION = 'TRASPASO_302';
    public const MISSING_REQUIRED_FIELDS = 'TRASPASO_303';

    // Errores de estado del sistema
    public const DATABASE_ERROR = 'TRASPASO_401';
    public const TRANSACTION_FAILED = 'TRASPASO_402';
    public const SYSTEM_ERROR = 'TRASPASO_403';

    // Errores de Google Sheets
    public const GOOGLE_SHEETS_INVALID_RANGE = 'TRASPASO_501';
    public const GOOGLE_SHEETS_API_ERROR = 'TRASPASO_502';
    public const GOOGLE_SHEETS_AUTHENTICATION_ERROR = 'TRASPASO_503';

    /**
     * Obtiene el mensaje de error correspondiente al código
     */
    public static function getMessage(string $code): string
    {
        $messages = [
            self::PRODUCT_NOT_FOUND => 'Producto no encontrado',
            self::PRODUCT_NOT_IN_ORIGIN_BRANCH => 'El producto no existe en la sucursal de salida',
            self::PRODUCT_RESERVED => 'El producto está apartado',
            self::PRODUCT_ALREADY_IN_PENDING_TRANSFER => 'El producto ya está en un traspaso pendiente',
            self::PRODUCT_UNITARIO_REQUIRES_SKU => 'Este producto es unitario, debe escanear el SKU',
            self::PRODUCT_MULTIPLE_STOCK_ENTRIES => 'No debe haber más de un producto en stock con este código, verificar inventario',
            self::PRODUCT_INSUFFICIENT_STOCK => 'Stock insuficiente para realizar el traspaso',
            self::PRODUCT_STOCK_OCCUPIED => 'Stock ocupado por otros traspasos pendientes',

            self::USER_NOT_AUTHENTICATED => 'Debe iniciar sesión para realizar el traspaso',
            self::USER_NOT_AUTHORIZED => 'No tiene permisos para realizar esta acción',

            self::BRANCH_NOT_FOUND => 'Sucursal no encontrada',
            self::TRANSFER_ORDER_NOT_FOUND => 'Orden de traspaso no encontrada',
            self::WAREHOUSE_TRANSFER_NOT_FOUND => 'Traspaso de almacén no encontrado',
            self::EXIT_ORDER_NOT_FOUND => 'Orden de salida no encontrada',

            self::INVALID_QUANTITY => 'Cantidad inválida',
            self::INVALID_BRANCH_SELECTION => 'Selección de sucursal inválida',
            self::MISSING_REQUIRED_FIELDS => 'Faltan campos requeridos',

            self::DATABASE_ERROR => 'Error en la base de datos',
            self::TRANSACTION_FAILED => 'Error al procesar la transacción',
            self::SYSTEM_ERROR => 'Error del sistema',

            self::GOOGLE_SHEETS_INVALID_RANGE => 'Rango de Google Sheets inválido',
            self::GOOGLE_SHEETS_API_ERROR => 'Error en la API de Google Sheets',
            self::GOOGLE_SHEETS_AUTHENTICATION_ERROR => 'Error de autenticación con Google Sheets',
        ];

        return $messages[$code] ?? 'Error desconocido';
    }

    /**
     * Obtiene todos los códigos de error disponibles
     */
    public static function getAllCodes(): array
    {
        return [
            self::PRODUCT_NOT_FOUND,
            self::PRODUCT_NOT_IN_ORIGIN_BRANCH,
            self::PRODUCT_RESERVED,
            self::PRODUCT_ALREADY_IN_PENDING_TRANSFER,
            self::PRODUCT_UNITARIO_REQUIRES_SKU,
            self::PRODUCT_MULTIPLE_STOCK_ENTRIES,
            self::PRODUCT_INSUFFICIENT_STOCK,
            self::PRODUCT_STOCK_OCCUPIED,
            self::USER_NOT_AUTHENTICATED,
            self::USER_NOT_AUTHORIZED,
            self::BRANCH_NOT_FOUND,
            self::TRANSFER_ORDER_NOT_FOUND,
            self::WAREHOUSE_TRANSFER_NOT_FOUND,
            self::EXIT_ORDER_NOT_FOUND,
            self::INVALID_QUANTITY,
            self::INVALID_BRANCH_SELECTION,
            self::MISSING_REQUIRED_FIELDS,
            self::DATABASE_ERROR,
            self::TRANSACTION_FAILED,
            self::SYSTEM_ERROR,
            self::GOOGLE_SHEETS_INVALID_RANGE,
            self::GOOGLE_SHEETS_API_ERROR,
            self::GOOGLE_SHEETS_AUTHENTICATION_ERROR,
        ];
    }

    /**
     * Verifica si un código de error es válido
     */
    public static function isValidCode(string $code): bool
    {
        return in_array($code, self::getAllCodes());
    }
}
