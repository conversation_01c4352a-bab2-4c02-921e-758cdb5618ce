<?php

namespace App\Enum;

/**
 * Códigos de respuesta estándar para la aplicación
 */
class ResponseCode
{
    // Códigos de éxito
    public const SUCCESS = 'SUCCESS';
    public const CREATED = 'CREATED';
    public const UPDATED = 'UPDATED';
    public const DELETED = 'DELETED';

    // Códigos de error de cliente (4xx)
    public const BAD_REQUEST = 'BAD_REQUEST';
    public const UNAUTHORIZED = 'UNAUTHORIZED';
    public const FORBIDDEN = 'FORBIDDEN';
    public const NOT_FOUND = 'NOT_FOUND';
    public const CONFLICT = 'CONFLICT';
    public const VALIDATION_ERROR = 'VALIDATION_ERROR';

    // Códigos de error de servidor (5xx)
    public const INTERNAL_ERROR = 'INTERNAL_ERROR';
    public const DATABASE_ERROR = 'DATABASE_ERROR';
    public const EXTERNAL_API_ERROR = 'EXTERNAL_API_ERROR';

    /**
     * Mapeo de códigos a códigos HTTP
     */
    public static function getHttpCode(string $code): int
    {
        $httpCodes = [
            self::SUCCESS => 200,
            self::CREATED => 201,
            self::UPDATED => 200,
            self::DELETED => 200,
            
            self::BAD_REQUEST => 400,
            self::UNAUTHORIZED => 401,
            self::FORBIDDEN => 403,
            self::NOT_FOUND => 404,
            self::CONFLICT => 409,
            self::VALIDATION_ERROR => 422,
            
            self::INTERNAL_ERROR => 500,
            self::DATABASE_ERROR => 500,
            self::EXTERNAL_API_ERROR => 502,
        ];

        return $httpCodes[$code] ?? 500;
    }

    /**
     * Obtiene el mensaje por defecto para un código
     */
    public static function getDefaultMessage(string $code): string
    {
        $messages = [
            self::SUCCESS => 'Operación exitosa',
            self::CREATED => 'Recurso creado exitosamente',
            self::UPDATED => 'Recurso actualizado exitosamente',
            self::DELETED => 'Recurso eliminado exitosamente',
            
            self::BAD_REQUEST => 'Solicitud incorrecta',
            self::UNAUTHORIZED => 'No autorizado',
            self::FORBIDDEN => 'Acceso prohibido',
            self::NOT_FOUND => 'Recurso no encontrado',
            self::CONFLICT => 'Conflicto en la operación',
            self::VALIDATION_ERROR => 'Error de validación',
            
            self::INTERNAL_ERROR => 'Error interno del servidor',
            self::DATABASE_ERROR => 'Error en la base de datos',
            self::EXTERNAL_API_ERROR => 'Error en API externa',
        ];

        return $messages[$code] ?? 'Error desconocido';
    }
}
