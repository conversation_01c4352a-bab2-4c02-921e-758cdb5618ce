<?php

namespace App\Enum;

/**
 * Estados para el módulo de Traspaso
 */
class TraspasoStatus
{
    // Estados de Traspaso de Almacén
    public const TRANSFER_PENDING = '0';
    public const TRANSFER_COMPLETED = '1';
    public const TRANSFER_CANCELLED = '2';

    // Estados de Orden de Salida
    public const EXIT_ORDER_REJECTED = '0';
    public const EXIT_ORDER_ACCEPTED = '1';
    public const EXIT_ORDER_PENDING = '2';

    // Estados de Productos en Traspaso
    public const PRODUCT_TRANSFER_PENDING = '0';
    public const PRODUCT_TRANSFER_ACCEPTED = '1';
    public const PRODUCT_TRANSFER_REJECTED = '2';

    // Estados de Stock
    public const STOCK_ACTIVE = '1';
    public const STOCK_INACTIVE = '0';

    // Estados de Apartado
    public const PRODUCT_NOT_RESERVED = '0';
    public const PRODUCT_RESERVED = '1';

    // Tipos de Producto (masivounico)
    public const PRODUCT_TYPE_UNITARIO = '1';
    public const PRODUCT_TYPE_MASIVO = '2';

    /**
     * Obtiene la descripción del estado de traspaso de almacén
     */
    public static function getTransferStatusDescription(string $status): string
    {
        $descriptions = [
            self::TRANSFER_PENDING => 'Pendiente',
            self::TRANSFER_COMPLETED => 'Completado',
            self::TRANSFER_CANCELLED => 'Cancelado',
        ];

        return $descriptions[$status] ?? 'Estado desconocido';
    }

    /**
     * Obtiene la descripción del estado de orden de salida
     */
    public static function getExitOrderStatusDescription(string $status): string
    {
        $descriptions = [
            self::EXIT_ORDER_REJECTED => 'Rechazada',
            self::EXIT_ORDER_ACCEPTED => 'Aceptada',
            self::EXIT_ORDER_PENDING => 'Pendiente',
        ];

        return $descriptions[$status] ?? 'Estado desconocido';
    }

    /**
     * Obtiene la descripción del estado de producto en traspaso
     */
    public static function getProductTransferStatusDescription(string $status): string
    {
        $descriptions = [
            self::PRODUCT_TRANSFER_PENDING => 'Pendiente',
            self::PRODUCT_TRANSFER_ACCEPTED => 'Aceptado',
            self::PRODUCT_TRANSFER_REJECTED => 'Rechazado',
        ];

        return $descriptions[$status] ?? 'Estado desconocido';
    }

    /**
     * Obtiene la descripción del tipo de producto
     */
    public static function getProductTypeDescription(string $type): string
    {
        $descriptions = [
            self::PRODUCT_TYPE_UNITARIO => 'Unitario',
            self::PRODUCT_TYPE_MASIVO => 'Masivo',
        ];

        return $descriptions[$type] ?? 'Tipo desconocido';
    }

    /**
     * Verifica si un estado de traspaso es válido
     */
    public static function isValidTransferStatus(string $status): bool
    {
        return in_array($status, [
            self::TRANSFER_PENDING,
            self::TRANSFER_COMPLETED,
            self::TRANSFER_CANCELLED,
        ]);
    }

    /**
     * Verifica si un estado de orden de salida es válido
     */
    public static function isValidExitOrderStatus(string $status): bool
    {
        return in_array($status, [
            self::EXIT_ORDER_REJECTED,
            self::EXIT_ORDER_ACCEPTED,
            self::EXIT_ORDER_PENDING,
        ]);
    }

    /**
     * Verifica si un estado de producto en traspaso es válido
     */
    public static function isValidProductTransferStatus(string $status): bool
    {
        return in_array($status, [
            self::PRODUCT_TRANSFER_PENDING,
            self::PRODUCT_TRANSFER_ACCEPTED,
            self::PRODUCT_TRANSFER_REJECTED,
        ]);
    }

    /**
     * Obtiene todos los estados de traspaso disponibles
     */
    public static function getAllTransferStatuses(): array
    {
        return [
            self::TRANSFER_PENDING => self::getTransferStatusDescription(self::TRANSFER_PENDING),
            self::TRANSFER_COMPLETED => self::getTransferStatusDescription(self::TRANSFER_COMPLETED),
            self::TRANSFER_CANCELLED => self::getTransferStatusDescription(self::TRANSFER_CANCELLED),
        ];
    }

    /**
     * Obtiene todos los estados de orden de salida disponibles
     */
    public static function getAllExitOrderStatuses(): array
    {
        return [
            self::EXIT_ORDER_REJECTED => self::getExitOrderStatusDescription(self::EXIT_ORDER_REJECTED),
            self::EXIT_ORDER_ACCEPTED => self::getExitOrderStatusDescription(self::EXIT_ORDER_ACCEPTED),
            self::EXIT_ORDER_PENDING => self::getExitOrderStatusDescription(self::EXIT_ORDER_PENDING),
        ];
    }
}
