<?php
// src/AppBundle/Repository/ProductRepository.php
namespace App\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use App\Entity\Venta;
use App\Entity\Beneficiarioventa;
use App\Entity\Pago;

class VentaRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Venta::class);
    }

    /**
     * Update the liquidada field for all sales based on payments
     * If sum of payments equals total, set liquidada to 1, otherwise set to 0
     * 
     * @return int Number of updated sales
     */
    public function updateLiquidadaField(): int
    {
        $entityManager = $this->getEntityManager();

        // First, get all active sales that are not quotations
        $qb = $entityManager->createQueryBuilder();
        $qb->select('v')
           ->from(Venta::class, 'v')
           ->where('v.status = :status')
           ->setParameter('status', '1');

        $ventas = $qb->getQuery()->getResult();
        $updatedCount = 0;

        foreach ($ventas as $venta) {
            // Get sum of payments for this sale
            $qbPagos = $entityManager->createQueryBuilder();
            $qbPagos->select('SUM(p.monto)')
                   ->from(Pago::class, 'p')
                   ->where('p.ventaIdventa = :venta')
                   ->andWhere('p.status = :status')
                   ->setParameter('venta', $venta)
                   ->setParameter('status', '1');

            $sumPagos = $qbPagos->getQuery()->getSingleScalarResult();

            // If sum is null, set it to 0
            if ($sumPagos === null) {
                $sumPagos = 0;
            }

            // Compare sum of payments with total
            $total = floatval($venta->getTotal());
            $sumPagos = floatval($sumPagos);

            // Update liquidada field
            $liquidada = ($sumPagos >= $total) ? '1' : '0';

            if ($venta->getLiquidada() !== $liquidada) {
                $venta->setLiquidada($liquidada);
                $updatedCount++;
            }
        }

        // Flush changes to database
        $entityManager->flush();

        return $updatedCount;
    }

    public function findByBeneficiariosForVenta($ventaId)
    {
        $beneficiarios = $this->createQueryBuilder('v')
            ->select('bv')
            ->leftJoin('App\Entity\Beneficiarioventa', 'bv', 'WITH', 'bv.ventaIdventa = v.idventa')
            ->where('v.idventa = :ventaId')
            ->setParameter('ventaId', $ventaId)
            ->getQuery()
            ->getResult();

        $beneficiarioNames = [];

        foreach ($beneficiarios as $beneficiario) {
            $beneficiarioNames[] = $beneficiario->getNombre(); // Changed from getName() to getNombre()
        }

        return implode(', ', $beneficiarioNames);
    }


    public function obtenerPiezasVendidas($idventa)
    {
        $query = $this->getEntityManager()->createQuery(
            '   SELECT sv
                    FROM App\Entity\Stockventa sv
                    inner join sv.ventaIdventa v
                    WHERE v.idventa = :idventa
                '
        )->setParameters(["idventa" => $idventa]);
        $StockVentas = $query->getResult();


        return $StockVentas;
    }

    public function canceladoActivo()
    {
        $result = "";
        if ($this->getStatus() == "1") {
            $result = "Activo";
        } else {
            $result = "Cancelada";
        }
        return $result;
    }

    public function esCotizacionVenta()
    {
        $result = "";
        if ($this->getCotizacion() == "1") {
            $result = "Cotizacion";
        } else {
            $result = "Venta";
        }
        return $result;
    }

    public function getPagoConIva()
    {
        return $this->total + $this->iva;
    }

    public function getFechaVentaForExport()
    {
        $formattedDate = ($this->fechaventa) ? $this->fechaventa->format('d/m/Y') : '';
        return $formattedDate;
    }

    public function getFechaCotizacionForExport()
    {
        $formattedDate = ($this->fechacreacion) ? $this->fechacreacion->format('d/m/Y') : '';
        return $formattedDate;
    }

    public function getFormattedStatus()
    {
        $formattedStatus = ($this->status == '1') ? "Activa" : "Cancelada";
        return $formattedStatus;
    }

    public function getFormattedQuotation()
    {
        $formattedStatus = ($this->cotizacion == '1') ? "Cotización" : "Venta";
        return $formattedStatus;
    }

    public function getFormattedLiquidada()
    {
        $formattedLiquidada = ($this->liquidada == '1') ? "Sí" : "No";
        return $formattedLiquidada;
    }

    public function getFechaCreacionForExport()
    {
        $formattedDateC = ($this->fechacreacion) ? $this->fechacreacion->format('d/m/Y') : '';
        return $formattedDateC;
    }

    public function getFechaCancelacionForExport()
    {
        $formattedDateC = ($this->fechacancelacion) ? $this->fechacancelacion->format('d/m/Y') : '';
        return $formattedDateC;
    }


    public function findAllWithOptometrista()
    {
        $qb = $this->createQueryBuilder('v')
            ->select('v', 'u.nombre AS optometristaNombre')
            ->leftJoin('App\Entity\Stockventaordenlaboratorio', 'sv', 'WITH', 'sv.venta = v.idventa')
            ->leftJoin('sv.ordenLaboratorio', 'ol')
            ->leftJoin('ol.flujoexpediente', 'fe')
            ->leftJoin('fe.usuario', 'u')
            ->getQuery();

        return $qb->getResult();
    }

    /**
     * Find a sale by folio and enterprise with its special ticket information
     */
    public function findTicketsByFolioAndEmpresa(string $folio, int $idempresa): ?array
    {
        return $this->createQueryBuilder('v')
            ->select('v.ticketpdf', 'v.archivoautorizacion', 'v.tickerpdfespecial')
            ->innerJoin('v.sucursalIdsucursal', 's')
            ->innerJoin('s.empresaIdempresa', 'e')
            ->where('v.folio = :folio')
            ->andWhere('e.idempresa = :idempresa')
            ->andWhere('v.status = :status')
            ->setParameters([
                'folio' => $folio,
                'idempresa' => $idempresa,
                'status' => '1'
            ])
            ->getQuery()
            ->getOneOrNullResult();
    }

}
