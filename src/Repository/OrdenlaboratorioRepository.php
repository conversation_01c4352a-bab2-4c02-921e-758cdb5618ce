<?php

namespace App\Repository;

use App\Entity\Ordenlaboratorio;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class OrdenlaboratorioRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Ordenlaboratorio::class);
    }

    public function getProducts($idflujoexpediente)
    {
        $exito = false;
        $msj = "";
        $result = [];
        $entityManager = $this->getEntityManager();
    
        $query = $entityManager->createQuery(
            'SELECT v.folio, v.idventa
             FROM App\Entity\Flujoexpedienteventa fv
             JOIN fv.ventaIdventa v
             JOIN fv.flujoexpedienteIdflujoexpediente fe
             WHERE fe.idflujoexpediente = :idflujoexpediente'
        )->setParameter('idflujoexpediente', $idflujoexpediente);
    
        try {
            $ventas = $query->getResult();

            foreach($ventas as $venta){
                $query = $entityManager->createQuery(
                    'SELECT p.modelo, p.codigobarrasuniversal, s.codigobarras
                     FROM App\Entity\Stockventa sv
                     JOIN sv.ventaIdventa v
                     JOIN sv.stockIdstock s
                     JOIN s.productoIdproducto p
                     WHERE v.idventa = :idventa'
                )->setParameter('idventa', $venta['idventa']);
            
                try {
                    $resultAux = $query->getResult();
                    
                    if($resultAux){
                        foreach($resultAux as $prod){
                              $result[]=$prod;      
                        }
                    }
                } catch (\Doctrine\ORM\NoResultException $e) {
                    $msj = $e->getMessage();  
                } catch (\Doctrine\ORM\NonUniqueResultException $e) {
                    $msj = $e->getMessage(); 
                }
            }
            
            $exito = true;

        } catch (\Doctrine\ORM\NoResultException $e) {
            $msj = $e->getMessage();  
        } catch (\Doctrine\ORM\NonUniqueResultException $e) {
            $msj = $e->getMessage(); 
        }

        return ["exito"=>$exito,"msj"=>$msj,"result"=>$result];
    }

    public function getProductsOrder($idordenlaboratorio)
    {
        $exito = false;
        $msj = "";
        $result = [];
        $entityManager = $this->getEntityManager();
    
        $query = $entityManager->createQuery(
            'SELECT p.modelo, p.codigobarrasuniversal, s.codigobarras
             FROM App\Entity\Stockventaordenlaboratorio svo
             JOIN svo.ordenlaboratorioIdordenlaboratorio o
             JOIN svo.stockventaIdstockventa sv
             JOIN sv.stockIdstock s
             JOIN s.productoIdproducto p
             WHERE o.idordenlaboratorio = :idordenlaboratorio'
        )->setParameter('idordenlaboratorio', $idordenlaboratorio);
    
        try {
            $result = $query->getResult();
            $exito = true;
           
        } catch (\Doctrine\ORM\NoResultException $e) {
            $msj = $e->getMessage();  
        } catch (\Doctrine\ORM\NonUniqueResultException $e) {
            $msj = $e->getMessage(); 
        }

        return ["exito"=>$exito,"msj"=>$msj,"result"=>$result];
    }

    public function getVenta($idflujoexpediente)
    {
        $exito = false;
        $msj = "";
        $result = [];
        $entityManager = $this->getEntityManager();
    
        $query = $entityManager->createQuery(
            'SELECT v.folio
             FROM App\Entity\Flujoexpedienteventa fv
             JOIN fv.ventaIdventa v
             JOIN fv.flujoexpedienteIdflujoexpediente fe
             WHERE fe.idflujoexpediente = :idflujoexpediente'
        )->setParameter('idflujoexpediente', $idflujoexpediente);
    
        try {
            $result = $query->getResult();
            $exito = true;
           
        } catch (\Doctrine\ORM\NoResultException $e) {
            $msj = $e->getMessage();  
        } catch (\Doctrine\ORM\NonUniqueResultException $e) {
            $msj = $e->getMessage(); 
        }

        return ["exito"=>$exito,"msj"=>$msj,"result"=>$result];
    }

    public function prueba()
    {
        return "Hola";
    }

    /**
     * Obtiene las ventas (folios) relacionadas con una orden de laboratorio usando LEFT JOIN
     */
    public function getVentasFoliosByOrdenLaboratorio($idordenlaboratorio)
    {
        $exito = false;
        $msj = "";
        $result = [];
        $entityManager = $this->getEntityManager();

        $query = $entityManager->createQuery(
            'SELECT DISTINCT v.folio, v.idventa
             FROM App\Entity\Ordenlaboratorio ol
             LEFT JOIN ol.stockventaordenlaboratorios svol
             LEFT JOIN svol.stockventaIdstockventa sv
             LEFT JOIN sv.ventaIdventa v
             WHERE ol.idordenlaboratorio = :idordenlaboratorio
             AND v.status = :status
             ORDER BY v.folio'
        )->setParameter('idordenlaboratorio', $idordenlaboratorio)
         ->setParameter('status', '1');

        try {
            $result = $query->getResult();
            $exito = true;

        } catch (\Doctrine\ORM\NoResultException $e) {
            $msj = $e->getMessage();
        } catch (\Doctrine\ORM\NonUniqueResultException $e) {
            $msj = $e->getMessage();
        } catch (\Exception $e) {
            $msj = $e->getMessage();
        }

        return ["exito"=>$exito,"msj"=>$msj,"result"=>$result];
    }

    /**
     * Obtiene solo los folios de las ventas relacionadas con una orden de laboratorio
     */
    public function getFoliosByOrdenLaboratorio($idordenlaboratorio)
    {
        $exito = false;
        $msj = "";
        $result = [];
        $entityManager = $this->getEntityManager();

        $query = $entityManager->createQuery(
            'SELECT DISTINCT v.folio
             FROM App\Entity\Ordenlaboratorio ol
             LEFT JOIN ol.stockventaordenlaboratorios svol
             LEFT JOIN svol.stockventaIdstockventa sv
             LEFT JOIN sv.ventaIdventa v
             WHERE ol.idordenlaboratorio = :idordenlaboratorio
             AND v.status = :status
             ORDER BY v.folio'
        )->setParameter('idordenlaboratorio', $idordenlaboratorio)
         ->setParameter('status', '1');

        try {
            $result = $query->getResult();
            $exito = true;

        } catch (\Doctrine\ORM\NoResultException $e) {
            $msj = $e->getMessage();
        } catch (\Doctrine\ORM\NonUniqueResultException $e) {
            $msj = $e->getMessage();
        } catch (\Exception $e) {
            $msj = $e->getMessage();
        }

        return ["exito"=>$exito,"msj"=>$msj,"result"=>$result];
    }
}
