<?php
//region local
// src/Repository/ProductoTranspasoAlmacenRepository.php

namespace App\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use App\Entity\Productostranspasoalmacen;

class ProductoTranspasoAlmacenRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Productostranspasoalmacen::class);
    }

    public function obtenerAceptadaPorProducto($idproductotranspasoalmacen)
    {
        $entityManager = $this->getEntityManager();
    
        $query = $entityManager->createQuery(
            'SELECT os.aceptada
             FROM App\Entity\Productostranspasoalmacen pta
             JOIN pta.transpasoalmacenIdtranspasoalmacen ta
             JOIN App\Entity\Ordensalida os WITH os.transpasoalmacenIdtranspasoalmacen = ta
             WHERE pta.idproductostranspasoalmacen = :idProductoTranspasoAlmacen'
        )->setParameter('idProductoTranspasoAlmacen', $idproductotranspasoalmacen);
    
        try {
            $result = $query->getOneOrNullResult();
            return $result ? $result['aceptada'] : null;
        } catch (\Doctrine\ORM\NoResultException $e) {
            return null;  // No result found
        } catch (\Doctrine\ORM\NonUniqueResultException $e) {
            return null;  // Multiple results found, handle accordingly
        }
    }
    
    
    
}
