<?php

namespace App\Tests\Controller;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use App\Entity\Stock;
use App\Entity\Stockmovimiento;

class ActualizacionControllerTest extends WebTestCase
{
    /**
     * Test para verificar que la página de carga de Excel se muestra correctamente
     */
    public function testTraspasoMasivoExcelPageLoads()
    {
        $client = static::createClient();
        $client->request('GET', '/actualizacion/traspaso-masivo-excel');

        $this->assertEquals(200, $client->getResponse()->getStatusCode());
        $this->assertSelectorTextContains('h3.card-title', 'Traspaso Masivo por Excel');
    }

    /**
     * Test para verificar que se rechaza un archivo que no es Excel
     */
    public function testRechazaArchivoNoExcel()
    {
        $client = static::createClient();
        
        // Crear un archivo temporal que no es Excel
        $filePath = tempnam(sys_get_temp_dir(), 'test');
        file_put_contents($filePath, 'Este no es un archivo Excel');
        
        $uploadedFile = new UploadedFile(
            $filePath,
            'test.txt',
            'text/plain',
            null,
            true
        );
        
        $client->request(
            'POST',
            '/actualizacion/procesar-excel-traspaso',
            [],
            ['archivo_excel' => $uploadedFile]
        );
        
        $this->assertStringContainsString('El archivo debe ser un Excel', $client->getResponse()->getContent());
    }
    
    /**
     * Test para verificar que se procesa correctamente un archivo Excel con SKUs
     */
    public function testProcesaExcelConSkus()
    {
        // Este test requeriría crear un archivo Excel real con SKUs
        // y verificar que se encuentran productos con cantidad 1
        
        // Ejemplo de cómo sería el test:
        /*
        $client = static::createClient();
        
        // Crear un archivo Excel con SKUs
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setCellValue('B2', '111213166204');
        $sheet->setCellValue('C2', '111753812252');
        
        $filePath = tempnam(sys_get_temp_dir(), 'test');
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save($filePath);
        
        $uploadedFile = new UploadedFile(
            $filePath,
            'test.xlsx',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            null,
            true
        );
        
        $client->request(
            'POST',
            '/actualizacion/procesar-excel-traspaso',
            [],
            ['archivo_excel' => $uploadedFile]
        );
        
        $this->assertSelectorExists('table#tabla-productos');
        */
        
        $this->markTestIncomplete('Este test requiere un entorno con datos reales.');
    }
    
    /**
     * Test para verificar que se ejecuta correctamente el traspaso
     */
    public function testEjecutaTraspasoCorrectamente()
    {
        // Este test requeriría un entorno con datos reales
        // y verificar que los productos se transfieren correctamente
        
        // Ejemplo de cómo sería el test:
        /*
        $client = static::createClient();
        
        // Obtener IDs de stock reales con cantidad 1
        $entityManager = static::$container->get('doctrine')->getManager();
        $stocks = $entityManager->getRepository(Stock::class)->findBy([
            'cantidad' => 1,
            'status' => '1'
        ], [], 2);
        
        if (count($stocks) < 2) {
            $this->markTestSkipped('No hay suficientes stocks para probar.');
        }
        
        $stockIds = array_map(function($stock) {
            return $stock->getIdstock();
        }, $stocks);
        
        $client->request(
            'POST',
            '/actualizacion/ejecutar-traspaso-masivo',
            ['productos' => $stockIds]
        );
        
        // Verificar que se crearon los registros en Stockmovimiento
        $movimientos = $entityManager->getRepository(Stockmovimiento::class)->findBy([
            'stockIdstock' => $stocks[0]
        ], ['creacion' => 'DESC'], 1);
        
        $this->assertCount(1, $movimientos);
        $this->assertEquals('salida', $movimientos[0]->getTipo());
        
        // Verificar que el stock original ahora tiene cantidad 0
        $entityManager->refresh($stocks[0]);
        $this->assertEquals(0, $stocks[0]->getCantidad());
        */
        
        $this->markTestIncomplete('Este test requiere un entorno con datos reales.');
    }
}