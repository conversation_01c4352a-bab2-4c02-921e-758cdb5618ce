<?php

namespace App\Tests\Controller;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\BrowserKit\Cookie;
use Doctrine\Persistence\ObjectManager;
use App\Entity\Cliente;

use App\Entity\Usuario; // Replace with your User entity

use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;

class VentasControllerTest extends WebTestCase
{
    private $client = null;
    private $entityManager;

    protected function setUp(): void

    {
        $this->client = static::createClient();
        $this->entityManager = $this->client->getContainer()->get('doctrine')->getManager();
    }

    public function testNuevaVenta()
    {
        $this->logIn();
        $this->client->request('GET', '/venta/nueva-venta');
        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());
    }

    public function testBuscarCliente()
    {
        $this->logIn();

        $requestData = [
            'nombre' => 'DIOGENES',
            'apellidop' => 'GRAJALES',
            'apellidom' => 'CORONA',
            'email' => '<EMAIL>',
            'telefono' => '5573892357',

        ];

        $this->client->request('POST', '/venta/buscar-cliente', $requestData);

        $data = json_decode($this->client->getResponse()->getContent(), true);

        $name = $data["suggestions"][0]['value'];

        $this->assertEquals('DIOGENES', $name);
    }

    public function testSeleccionarClientes()
    {
        $this->logIn();

        $requestData = [
            'query' => 'SANDRA',
        ];

        $this->client->request('POST', '/venta/seleccionar-clientes', $requestData);

        $data = json_decode($this->client->getResponse()->getContent(), true);

        $name = $data["suggestions"][0]['nombre'];

        $this->assertEquals('SANDRA XIMENA', $name);
    }

    public function testSeleccionarEmail()
    {
        $this->logIn();

        $requestData = [
            'query' => 'SMARTINEZ',
        ];

        $this->client->request('POST', '/venta/seleccionar-email', $requestData);

        $data = json_decode($this->client->getResponse()->getContent(), true);

        $name = $data["suggestions"][0]['nombre'];

        $this->assertEquals('SANDRA XIMENA', $name);
    }

    public function testSeleccionarTelefono()
    {
        $this->logIn();

        $requestData = [
            'query' => '5541408432',
        ];

        $this->client->request('POST', '/venta/seleccionar-telefono', $requestData);

        $data = json_decode($this->client->getResponse()->getContent(), true);

        $name = $data["suggestions"][0]['nombre'];

        $this->assertEquals('SANDRA XIMENA', $name);
    }

    public function testBuscarNumeroCliente()
    {
        $this->logIn();

        $requestData = [
            'query' => '0930',
        ];

        $this->client->request('POST', '/venta/buscar-numero-cliente', $requestData);

        $data = json_decode($this->client->getResponse()->getContent(), true);

        $name = $data["suggestions"][0]['nombre'];

        $this->assertEquals('SANDRA XIMENA', $name);
    }


    private function logIn()
    {
        $session = $this->client->getContainer()->get('session');

        $container = $this->client->getContainer();
        //$entityManager = $container->get('doctrine')->getManager();

        $user = $this->entityManager->getRepository(Usuario::class)->findOneBy(array('email' => "<EMAIL>"));

        $firewallName = 'admin';
        $firewallContext = 'admin';
        $token = new UsernamePasswordToken($user, null, $firewallName, $user->getRoles());
        $session->set('_security_'.$firewallContext, serialize($token));
        $session->save();

        $cookie = new Cookie($session->getName(), $session->getId());
        $this->client->getCookieJar()->set($cookie);
    }

}





