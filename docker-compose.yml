services:
  mysql:
    image: mysql:8.0.35
    command: --default-authentication-plugin=mysql_native_password --sql-mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
    container_name: pv360_mysql
    ports:
      - "33306:3306"
    networks:
      - pv360_network
    environment:
      MYSQL_DATABASE: grupooptimo_pv360_v13
      MYSQL_ROOT_PASSWORD: root
      MYSQL_USER: user
      MYSQL_PASSWORD: root
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init-scripts:/docker-entrypoint-initdb.d
    restart: always

  phpmyadmin:
    image: phpmyadmin:latest
    container_name: pv360_phpmyadmin
    ports:
      - "8080:80"
    networks:
      - pv360_network
    environment:
      PMA_HOST: mysql
      UPLOAD_LIMIT: 100M
      PMA_ABSOLUTE_URI: ""
    depends_on:
      - mysql
    restart: always

  app:
    build:
      context: .
      dockerfile: .docker/Dockerfile
    image: pv360
    container_name: pv360_app_1
    ports:
      - "8000:80"
    networks:
      - pv360_network
    volumes:
      - .:/var/www/html/
    depends_on:
      - mysql
    restart: always

volumes:
  mysql_data:
    driver: "local"

networks:
  pv360_network:
    driver: bridge