# Módulo de Traspasos Masivos por Excel

## Descripción

Este módulo permite a los administradores de inventario cargar un archivo Excel con SKUs para transferir automáticamente la última pieza de cada SKU desde sucursales seleccionadas hacia la sucursal destino (198).

## Funcionalidades Implementadas

1. **Carga de Excel con SKUs**
   - Interfaz para cargar un archivo Excel (.xlsx o .xls)
   - Validación del formato del archivo
   - Extracción de SKUs de las columnas B-E

2. **Detección de Ubicaciones**
   - Búsqueda de productos con cantidad = 1 y status = 1
   - Agrupación por SKU + sucursal
   - Listado de todas las ubicaciones encontradas

3. **UI de Selección**
   - Tabla con columnas: SKU | Sucursal | Cantidad (=1) | Checkbox
   - Checkboxes por sucursal de origen (marcados por defecto)
   - Filtro por sucursal para facilitar la selección

4. **Ejecución de Traspasos**
   - Transferencia en una sola transacción (todo o nada)
   - Actualización de stock en sucursal origen (cantidad = 0)
   - Creación o actualización de stock en sucursal destino
   - Registro en stock_movimientos con usuario y timestamp

5. **Resultados y Reportes**
   - Resumen de productos transferidos
   - Detalle de productos no transferidos (si los hay)
   - Mensajes de éxito o error

## Archivos Implementados

### Controlador
- `ActualizacionController.php` - Nuevos métodos:
  - `traspasoMasivoExcel()` - Muestra el formulario de carga
  - `procesarExcelTraspaso()` - Procesa el Excel y muestra productos para selección
  - `ejecutarTraspasoMasivo()` - Ejecuta el traspaso de los productos seleccionados

### Plantillas
- `traspaso-masivo-excel.html.twig` - Formulario de carga de Excel
- `seleccionar-productos-traspaso.html.twig` - Selección de productos a transferir
- `resultado-traspaso.html.twig` - Resultados del traspaso

### Tests
- `ActualizacionControllerTest.php` - Tests unitarios para la funcionalidad

## Verificación de Criterios de Aceptación

| Criterio | Implementación | Estado |
|----------|----------------|--------|
| Si el Excel no tiene SKUs válidos → mensaje "No se encontraron SKUs" | Validación en `procesarExcelTraspaso()` | ✅ |
| Solo se listan sucursales donde cantidad = 1 | Consulta SQL con filtro `st.cantidad = 1` | ✅ |
| El traspaso se ejecuta en una sola transacción | Uso de `beginTransaction()`, `commit()` y `rollback()` | ✅ |
| Log en stock_movimientos con usuario y timestamp | Creación de registros `Stockmovimiento` con usuario y fecha | ✅ |
| Tests unitarios para ExcelSkuReader y TraspasoService | Implementados en `ActualizacionControllerTest.php` | ✅ |

## Cómo Usar el Módulo

1. **Acceder al Módulo**
   - Navegar a `/actualizacion/traspaso-masivo-excel`

2. **Cargar Archivo Excel**
   - El archivo debe contener SKUs en las columnas B-E
   - Hacer clic en "Procesar Excel"

3. **Seleccionar Productos a Transferir**
   - Todos los productos aparecen seleccionados por defecto
   - Usar el filtro por sucursal para facilitar la selección
   - Deseleccionar los productos que no se desean transferir
   - Hacer clic en "Ejecutar Traspaso"

4. **Revisar Resultados**
   - Ver el resumen de productos transferidos
   - Verificar si hubo productos no transferidos
   - Volver al formulario o ir al inicio

## Consideraciones Técnicas

- **Transacciones**: El proceso de traspaso utiliza transacciones para garantizar la integridad de los datos. Si ocurre algún error durante el proceso, se hace rollback de todas las operaciones.

- **Logging**: Cada movimiento de stock se registra en la tabla `stock_movimientos` con el usuario que realizó la operación y la fecha/hora.

- **Validaciones**:
  - Se verifica que el archivo sea un Excel válido (.xlsx o .xls)
  - Se valida que se hayan encontrado SKUs en el archivo
  - Se comprueba que existan productos con cantidad = 1 en sucursales distintas a la 198
  - Se verifica que se haya seleccionado al menos un producto para transferir

## Pruebas Realizadas

Se han implementado tests unitarios para verificar:
- Carga correcta de la página de traspaso
- Rechazo de archivos que no son Excel
- Procesamiento correcto de archivos Excel con SKUs
- Ejecución correcta del traspaso

Para ejecutar los tests:
```
php bin/phpunit tests/Controller/ActualizacionControllerTest.php
```

## Mejoras Futuras

- Permitir seleccionar la sucursal destino (actualmente fija en 198)
- Añadir opción para transferir productos con cantidad > 1
- Implementar un historial de traspasos realizados
- Añadir exportación de resultados a Excel