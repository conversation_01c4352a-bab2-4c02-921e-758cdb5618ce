{"type": "project", "license": "proprietary", "require": {"php": ">=7.4", "ext-ctype": "*", "ext-iconv": "*", "api-platform/core": "^2.7", "beberlei/doctrineextensions": "^1.5", "composer/package-versions-deprecated": "^1.11", "dama/doctrine-test-bundle": "*", "doctrine/annotations": "^1.14", "doctrine/doctrine-bundle": "^2.1", "doctrine/doctrine-migrations-bundle": "^3.0", "doctrine/orm": "^2.7", "dompdf/dompdf": "*", "facturama/facturama-php-sdk": "^2.0@dev", "google/apiclient": "^2.14", "guzzlehttp/guzzle": "*", "knplabs/knp-snappy-bundle": "^1.9", "lexik/jwt-authentication-bundle": "^2.21", "nelmio/api-doc-bundle": "*", "nelmio/cors-bundle": "^2.3", "phpdocumentor/reflection-docblock": "^5.2", "phpoffice/phpexcel": "^1.8", "phpoffice/phpspreadsheet": "^1.29", "scheb/2fa-bundle": "^5.13", "scheb/2fa-google-authenticator": "^5.13", "scheb/2fa-qr-code": "^5.13", "scheb/2fa-trusted-device": "^5.13", "sensio/framework-extra-bundle": "^6.2", "sonata-project/admin-bundle": "^4.22", "sonata-project/core-bundle": "3.*", "sonata-project/doctrine-orm-admin-bundle": "4.*", "sonata-project/exporter": "^2.14", "symfony/asset": "4.4.*", "symfony/console": "4.4.*", "symfony/dotenv": "4.4.*", "symfony/expression-language": "4.4.*", "symfony/flex": "^1.3.1", "symfony/form": "4.4.*", "symfony/framework-bundle": "4.4.*", "symfony/google-mailer": "^5.2", "symfony/http-client": "4.4.*", "symfony/intl": "4.4.*", "symfony/mailer": "4.4.*", "symfony/mime": "4.4.*", "symfony/monolog-bundle": "^3.1", "symfony/process": "4.4.*", "symfony/property-access": "4.4.*", "symfony/property-info": "4.4.*", "symfony/security-bundle": "4.4.*", "symfony/serializer": "4.4.*", "symfony/swiftmailer-bundle": "*", "symfony/templating": "4.4.*", "symfony/translation": "4.4.*", "symfony/twig-bundle": "^4.4", "symfony/validator": "4.4.*", "symfony/web-link": "4.4.*", "symfony/yaml": "4.4.*", "symfonycasts/reset-password-bundle": "^1.14", "twig/extra-bundle": "^2.12|^3.0", "twig/twig": "^2.12|^3.0", "twilio/sdk": "^8.2", "vich/uploader-bundle": "^1.23"}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "^3.4", "phpunit/phpunit": "^9.6", "symfony/browser-kit": "^4.4", "symfony/css-selector": "^4.4", "symfony/debug-bundle": "^4.4", "symfony/maker-bundle": "^1.39", "symfony/phpunit-bridge": "^5.1", "symfony/stopwatch": "^4.4", "symfony/var-dumper": "^4.4", "symfony/web-profiler-bundle": "^4.4"}, "config": {"preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "symfony/flex": true}}, "autoload": {"psr-4": {"App\\": "src/", "PHPExcel\\": "src/PhpSpreadsheet"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/", "PHPExcel\\": "src/PhpSpreadsheet"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": true, "require": "4.4.*"}}}