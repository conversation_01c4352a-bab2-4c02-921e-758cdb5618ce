<!-- globales -->

<div class="card text-dark bg-light border-info mb-3">
    <div class="card-header">
    
        <h3>Resultado global</h3>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h4 class="mx-4">Resultados por tipo de cliente</h4>
                
                {% if   conveniosDataGlobal is defined and  conveniosDataGlobal is not empty %}
                    <div class="table-responsive p-0">
                        <table class="table table-striped table-hover align-items-center mb-0">
                            <thead>
                                <tr class="text-center">
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Tipo de Venta</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">No Ventas</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Total</th>
                                    <th class="text-secondary opacity-7"></th>
                                </tr>
                            </thead>
                        <tbody>
                        {% set totalConvenio =  0 %}
                        {% set totalNoVentas =  0 %}
                        {% for convenio in conveniosDataGlobal %}
                            {% set totalConvenio =  convenio.totalPagosMonto + totalConvenio %}
                            {% set totalNoVentas =  convenio.noVentas + totalNoVentas %}
                            <tr>
                                <td class="text-center">
                                    <h6 class="mb-0 text-sm">{% if convenio.nombre =="UAM" %}Prestación UAM{% elseif convenio.nombre ==""  %}Público General{% else %}{{ convenio.nombre }}{% endif %}</h6>
                                </td>
                                <td class=" ">
                                    <p class="text-center font-weight-bold mb-0 ">{{ convenio.noVentas }}</p>
                                </td>
                                <td class="align-middle text-center text-sm">
                                    <p class="text-xs font-weight-bold mb-0">${{ convenio.totalPagosMonto | number_format(2, '.', ',') }}</p>
                                </td>
                                <td class="align-middle text-center">
                                    <a href="javascript:;" class="text-secondary font-weight-bold text-xs"  data-bs-toggle="mod" data-bs-target="#modal-convenio-{{ loop.index }}" >
                                        Detalle
                                    </a>
                                    <!-- Modal -->
                                    <div class="mod" id="modal-convenio-{{ loop.index }}" tabindex="-1" aria-labelledby="modal-convenio-{{ loop.index }}" aria-hidden="true">
                                        <div class="mod-dialog  modal-xl">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="exampleModalLabel">DETALLE</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="mod" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="table-responsive">
                                                        <table class="table  table-striped table-hover ">
                                                            <thead>
                                                            <tr>
                                                                <th>FOLIO</th>
                                                                <th>PAGOS</th>
                                                                <th>TIPO DE VENTA</th>
                                                                <th>FACTURA</th>
                                                                <th>FECHA</th>
                                                                <th>SUBTOTAL</th>
                                                                <th>IVA</th>
                                                                <th>TOTAL</th>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            {% for venta in convenio.ventas %}
                                                                <tr>
                                                                    <td>{{ venta.folio }}</td>
                                                                    <td>
                                                                        {% if venta.pagos is defined  %}
                                                                            <table class="table  table-striped table-hover ">
                                                                                <tr>
                                                                                    <th>Fecha</th>
                                                                                    <th>Monto</th>
                                                                                    <th>Tipo de Pago</th>
                                                                                </tr>
                                                                                {% if convenio.nombre =="UAM" %}
                                                                                    <tr>
                                                                                        <td>{{ venta.fechaventa | date("d/m/Y g:ia ")}}</td>
                                                                                        <td>${{ venta.pagado  | number_format(2, '.', ',') }}</td>
                                                                                        <td>CONVENIO</td>
                                                                                    </tr>
                                                                                {% else%}

                                                                                    {% for  pago in venta.pagos %}
                                                                                        <tr>
                                                                                            <td>{{ pago.fecha | date("d/m/Y g:ia ")}}</td>
                                                                                            <td>${{ pago.monto  | number_format(2, '.', ',') }}</td>
                                                                                            <td>{{ pago.tipopago }}</td>
                                                                                        </tr>
                                                                                    {% endfor %}
                                                                                {% endif %}


                                                                            </table>
                                                                        {% endif %}
                                                                    </td>
                                                                    <td>{{ venta.convenio }}</td>
                                                                    <td>{% if venta.pidiofactura =="1" %}Si{% else %}No{% endif %}</td>
                                                                    <td>{{ venta.fechaventa |date("d/m/Y g:ia ") }}</td>
                                                                    <td>
                                                                        ${{ venta.total  | number_format(2, '.', ',')}}
                                                                    </td>
                                                                    <td>  ${{ venta.iva | number_format(2, '.', ',') }}</td>
                                                                    <td>  ${{ (venta.total   + venta.iva) | number_format(2, '.', ',') }}</td>
                                                                </tr>
                                                            {% endfor %}
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="mod">Cerrar</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>

                        {% endfor %}
                          <tr class="text-center negritas">
                              <td>
                                  Total
                              </td>
                              <td>
                                  {{ totalNoVentas  }}
                              </td>
                              <td>
                                  ${{  totalConvenio | number_format(2, '.', ',') }}
                              </td>
                              <td>
                                  
                              </td>
                          </tr>
                          </tbody>
                      </table>

                  </div>
              {% else %}
                  <div class="row">
                      <div class="col-md-12 text-center">
                          <h4>Sin resultados</h4>
                      </div>
                  </div>

              {% endif  %}
          </div>
          <!---->
          <div class="col-md-6">
              <h4 class="mx-4">Resultados por tipo de pago</h4>
              {% if tipopagoDataGlobal is not empty %}
                  <div class="table-responsive p-0">

                      <table class="table table-striped table-hover align-items-center mb-0">

                          <thead>
                          <tr class="text-center">
                              <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Tipo de Pago</th>
                              <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">No Ventas</th>
                              <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">No Pagos</th>
                              <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Total</th>

                          </tr>
                          </thead>
                          <tbody>
                          {% set totalConvenio =  0 %}
                          {% set totalNoVentas =  0 %}
                          {% set totalNoPagos =  0 %}
                          {% set totalPagosMonto =  0 %}
                          {% for tipopago in tipopagoDataGlobal %}
                              {% set totalNoVentas = totalNoVentas +  (tipopago.noVentas  | length) %}
                              {% set totalNoPagos = totalNoPagos +  tipopago.noPagos %}
                              {% set totalPagosMonto = totalPagosMonto + tipopago.totalPagosMonto %}
                              <tr>
                                  <td class="text-center">
                                      <h6 class="mb-0 text-sm">{{ tipopago.nombre }}</h6>
                                  </td>
                                  <td class=" ">
                                      <p class="text-center font-weight-bold mb-0 ">{{ tipopago.noVentas | length }}</p>

                                  </td>
                                  <td class=" ">
                                      <p class="text-center font-weight-bold mb-0 ">{{ tipopago.noPagos }}</p>

                                  </td>
                                  <td class="align-middle text-center text-sm">
                                      <p class="text-xs font-weight-bold mb-0">${{ tipopago.totalPagosMonto | number_format(2, '.', ',')  }}</p>
                                  </td>


                              </tr>
                          {% endfor %}
                          <tr class="text-center negritas">
                              <td>
                                  Total
                              </td>
                              <td>
                                  {{ totalNoVentas }}
                              </td>
                              <td>
                                  {{ totalNoPagos }}
                              </td>
                              <td>
                                  ${{ totalPagosMonto | number_format(2, '.', ',')  }}
                              </td>
                          </tr>

                          </tbody>
                      </table>

                  </div>
              {% else %}
                  <div class="row">
                      <div class="col-md-12 text-center">
                          <h4>Sin Resultados</h4>
                      </div>
                  </div>

              {% endif  %}
          </div>
      </div>
    </div>
</div>

<!-- sucursales -->


{% set indexAux=0 %}
{% for sucursal in sucursalesData %}


    {% if   sucursal.conveniosData is defined and  sucursal.conveniosData is not empty %}

        {% set indexAux=indexAux +1 %}
        <div class="card text-dark bg-light border-info mb-3">
        <div class="row">
            
            <div class="col-md-6  text-center">
                <h3>Sucursal {{ sucursal.nombre  }}</h3>

            </div>
            <div class="col-md-6 ">

                <table class="table  table-striped table-hover text-center">
                    <tr>
                        <th>Número de ventas</th>
                        <td>                    {{ sucursal.ventas | length }}
                        </td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h4 class="mx-4">Resultados por tipo de cliente</h4>

                {% if   sucursal.conveniosData is defined and  sucursal.conveniosData is not empty %}

                    <div class="table-responsive p-0">

                        <table class="table  table-striped table-hover  align-items-center mb-0">

                            <thead>
                            <tr class="text-center">
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Tipo de Venta</th>
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">No Ventas</th>
                                <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Total</th>
                                <th class="text-secondary opacity-7"></th>
                                
                            </tr>
                            </thead>
                            <tbody>
                            {% set totalConvenio =  0 %}
                            {% set totalNoVentas =  0 %}



                            {% for convenio in sucursal.conveniosData %}
                                {% set totalConvenio =  convenio.totalPagosMonto + totalConvenio %}
                                {% set totalNoVentas =  convenio.noVentas + totalNoVentas %}

                                <tr>
                                    <td class="text-center">
                                        <h6 class="mb-0 text-sm">{% if convenio.nombre =="UAM" %}Prestación UAM{% elseif convenio.nombre ==""  %}Público General{% else %}{{ convenio.nombre }}{% endif %}</h6>
                                    </td>
                                    <td class=" ">
                                        <p class="text-center font-weight-bold mb-0 ">{{ convenio.noVentas }}</p>

                                    </td>
                                    <td class="align-middle text-center text-sm">
                                        <p class="text-xs font-weight-bold mb-0">${{ convenio.totalPagosMonto | number_format(2, '.', ',') }}</p>
                                    </td>

                                    <td class="align-middle text-center">
                                        <a href="javascript:;" class="text-secondary font-weight-bold text-xs"  data-bs-toggle="mod" data-bs-target="#modal-convenio-{{ indexAux }}-{{ loop.index }}" >
                                            Detalle
                                        </a>
                                        <!-- Modal -->
                                        <div class="mod" id="modal-convenio-{{ indexAux }}-{{ loop.index }}" tabindex="-1" aria-labelledby="modal-convenio-{{ indexAux }}-{{ loop.index }}" aria-hidden="true">
                                            <div class="mod-dialog  modal-xl">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="exampleModalLabel">Modal title</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="mod" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="table-responsive">
                                                            <table class="table  table-striped table-hover ">
                                                                <thead>
                                                                <tr>
                                                                    <th>FOLIO</th>
                                                                    <th>PAGOS</th>
                                                                    <th>CONVENIO</th>
                                                                    <th>FACTURA</th>
                                                                    <th>FECHA</th>
                                                                    <th>SUBTOTAL</th>
                                                                    <th>IVA</th>
                                                                    <th>TOTAL</th>
                                                                </tr>
                                                                </thead>
                                                                <tbody>
                                                                {% for venta in convenio.ventas %}
                                                                    <tr>
                                                                        <td>{{ venta.folio }}</td>
                                                                        <td>
                                                                            {% if venta.pagos is defined  %}
                                                                                <table class="table  table-striped table-hover ">
                                                                                    <tr>
                                                                                        <th>Fecha</th>
                                                                                        <th>Monto</th>
                                                                                        <th>Tipo de Pago</th>
                                                                                    </tr>

                                                                                    {% for  pago in venta.pagos %}
                                                                                        <tr>
                                                                                            <td>{{ pago.fecha | date("d/m/Y g:ia ")}}</td>
                                                                                            <td>${{ pago.monto  | number_format(2, '.', ',') }}</td>
                                                                                            <td>{{ pago.tipopago }}</td>
                                                                                        </tr>
                                                                                    {% endfor %}

                                                                                </table>
                                                                            {% endif %}
                                                                        </td>
                                                                        <td>{{ venta.convenio }}</td>
                                                                        {{ dump(venta.convenio) }}
                                                                        <td>{% if venta.pidiofactura =="1" %}Si{% else %}No{% endif %}</td>
                                                                        <td>{{ venta.fechaventa |date("d/m/Y g:ia ") }}</td>
                                                                        <td>
                                                                            ${{ venta.total  | number_format(2, '.', ',')}}

                                                                        </td>
                                                                        <td>  ${{ venta.iva | number_format(2, '.', ',') }}</td>
                                                                        <td>  ${{ (venta.total   + venta.iva) | number_format(2, '.', ',') }}</td>
                                                                    </tr>
                                                                {% endfor %}
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="mod">Cerrar</button>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>

                            {% endfor %}
                            <tr class="text-center negritas">
                                <td>
                                    Total
                                </td>
                                <td>
                                    {{ totalNoVentas  }}
                                </td>
                                <td>
                                    ${{  totalConvenio | number_format(2, '.', ',') }}
                                </td>
                                <td>
                                  
                              </td>
                            </tr>

                            </tbody>
                        </table>

                    </div>
                {% else %}
                    <div class="row">
                        <div class="col-md-12 text-center">
                            <h4>Sin Resultados</h4>
                        </div>
                    </div>

                {% endif  %}
            </div>
            <!---->
            <div class="col-md-6">
                <h4 class="mx-4">Resultados por tipo de pago</h4>
                {% if  sucursal.tipopagoData is not empty %}
                    <div class="table-responsive p-0">

                        <table class="table  table-striped table-hover  align-items-center mb-0">

                            <thead>
                            <tr class="text-center">
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Tipo de Pago</th>
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">No Ventas</th>
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">No Pagos</th>
                                <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Total</th>

                            </tr>
                            </thead>
                            <tbody>
                            {% set totalConvenio =  0 %}
                            {% set totalNoVentas =  0 %}
                            {% set totalNoPagos =  0 %}
                            {% set totalPagosMonto =  0 %}
                            {% for tipopago in sucursal.tipopagoData %}
                                {% set totalNoVentas = totalNoVentas +  (tipopago.noVentas  | length) %}
                                {% set totalNoPagos = totalNoPagos +  tipopago.noPagos %}
                                {% set totalPagosMonto = totalPagosMonto + tipopago.totalPagosMonto %}
                                <tr>
                                    <td class="text-center">
                                        <h6 class="mb-0 text-sm">{{ tipopago.nombre }}</h6>
                                    </td>
                                    <td class=" ">
                                        <p class="text-center font-weight-bold mb-0 ">{{ tipopago.noVentas | length }}</p>

                                    </td>
                                    <td class=" ">
                                        <p class="text-center font-weight-bold mb-0 ">{{ tipopago.noPagos }}</p>

                                    </td>
                                    <td class="align-middle text-center text-sm">
                                        <p class="text-xs font-weight-bold mb-0">${{ tipopago.totalPagosMonto | number_format(2, '.', ',')  }}</p>
                                    </td>


                                </tr>
                            {% endfor %}
                            <tr class="text-center negritas">
                                <td>
                                    Total
                                </td>
                                <td>
                                    {{ totalNoVentas }}
                                </td>
                                <td>
                                    {{ totalNoPagos }}
                                </td>
                                <td>
                                    ${{ totalPagosMonto | number_format(2, '.', ',')  }}
                                </td>
                            </tr>

                            </tbody>
                        </table>

                    </div>
                {% else %}
                    <div class="row">
                        <div class="col-md-12 text-center">
                            <h4>Sin Resultados</h4>
                        </div>
                    </div>

                {% endif  %}
            </div>
        </div>
        </div>
       
    {% endif %}


{% endfor  %}
</div>