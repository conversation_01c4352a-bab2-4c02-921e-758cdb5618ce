<link rel="stylesheet" href="{{ asset('css/comisiones.css') }}">
<br>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="table-responsive">
                <table id="apartadosTable" class="table table-striped text-center">
                    <thead>
                    <tr>
                        <th>Empresa</th>
                        <th>Sucursal</th>
                        <th>Modelo</th>
                        <th>Sku</th>
                        <th>Fecha de creación de Venta</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for apartado in productosApartados %}
                        <tr>
                            <td>{{ apartado.Empresa }}</td>
                            <td>{{ apartado.Sucursal }}</td>
                            <td>{{ apartado.modelo }}</td>
                            <td>{{ apartado.Sku }}</td>
                            <td>{{ apartado.FechaCreacion|date('d/m/Y') }}</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    $(document).ready(function() {

        $('#comisionesTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "lengthChange": false,
            "pageLength": 15,
            "language": {
                "lengthMenu": "Mostrar _MENU_ registros por página",
                "zeroRecords": "No se encontraron resultados",
                "info": "Mostrando página _PAGE_ de _PAGES_",
                "infoEmpty": "No hay registros disponibles",
                "infoFiltered": "(filtrado de _MAX_ registros en total)",
                "search": "Buscar:",
                "paginate": {
                    "first": "Primero",
                    "last": "Último",
                    "next": "Siguiente",
                    "previous": "Anterior"
                }
            }
        });
    });
</script>
