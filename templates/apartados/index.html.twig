{% extends 'admin/layout.html.twig' %}

{% block title %}| Apartados{% endblock %}

{% block content %}
    <link rel="stylesheet" href="{{ asset('css/comisiones.css') }}">
    <div class="bodyb">
        <h1 class="text-center text-white mb-4">Reporte de Apartados</h1>
        <div class="container mt-4">
            <h2 class="text-center mb-4">FILTROS</h2>
            <div class="row mb-3">
                <div class="col-md-6 mb-2">
                    <select id="empresaD" class="form-control custom-select">
                        <option value="-1">Selecciona una empresa</option>
                        {% for empresa in empresas %}
                            <option value="{{ empresa.idempresa }}">{{ empresa.nombre }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 mb-2">
                    <select id="sucursal" class="form-control custom-select">
                        <option value="-1">Selecciona la sucursal</option>
                    </select>
                </div>
            </div>
            <div class="row mb-3 justify-content-center align-items-center">
                <div class="col-md-4 d-flex justify-content-center align-items-center">
                    <label for="modelo" class="m-0 rangoFecha">Modelo: </label>
                    <input id="modeloId" name="modelo" type="text" class="form-control">
                </div>
            </div>
            <div class="row mb-3 justify-content-center align-items-center">
                <div class="col-md-8 d-flex justify-content-center align-items-center">
                    <label for="rango" class="m-0 rangoFecha">Rango de Fecha:</label>
                    <input id="fecha-inicio-rango-dia" type="text" autocomplete="off" class="input-sm form-control mx-2" name="start"/>
                    <span class="rangoFecha">a</span>
                    <input id="fecha-fin-rango-dia" type="text" autocomplete="off" class="input-sm form-control mx-2" name="end"/>
                    <button class="btn btn-warning" onclick="resetRangoFechaDias()">
                        <i class="fa fa-eraser" aria-hidden="true"></i>
                    </button>
                </div>
            </div>
            <div class="row mb-3 justify-content-center">
                <div class="col-md-4 text-center">
                    <button class="btn btn-primary btn-block" id="buscarBtn" onclick="buscarComisiones();">Buscar</button>
                </div>
            </div>
        </div>
        <br>
        <div id="tablaComisiones" class="container"></div>
    </div>


    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(function() {
            var availableModels = {{ productos|json_encode|raw }};
            $("#modeloId").autocomplete({
                source: availableModels
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            var sucursalesUrl = '{{ path('get_sucursales', {'empresaId': 0}) }}';
            var vendedoresUrl = '{{ path('get_vendedores', {'sucursalId': 0}) }}';

            $('#empresaD').change(function() {
                var empresaId = $(this).val();
                if (empresaId) {
                    var url = sucursalesUrl.replace('/0', '/' + empresaId);
                    $.ajax({
                        url: url,
                        method: 'GET',
                        success: function(data) {
                            var sucursalSelect = $('#sucursal');
                            sucursalSelect.empty();
                            sucursalSelect.append('<option value="">Selecciona la sucursal</option>');
                            $.each(data, function(index, sucursal) {
                                sucursalSelect.append('<option value="' + sucursal.idsucursal + '">' + sucursal.nombre + '</option>');
                            });
                        }
                    });
                } else {
                    var sucursalSelect = $('#sucursal');
                    sucursalSelect.empty();
                    sucursalSelect.append('<option value="">Selecciona la sucursal</option>');
                }
            });
        });

        function buscarComisiones() {
            var sucursal = $('#sucursal').val();
            var fechaInicio = $('#fecha-inicio-rango-dia').val();
            var fechaFin = $('#fecha-fin-rango-dia').val();
            var modelo = $('#modeloId').val();

            var datos = {
                sucursal: sucursal,
                fechaInicio:fechaInicio,
                fechaFin:fechaFin,
                modelo:modelo
            };

            $.ajax({
                url: '{{ path('app-tabla-apartados') }}',
                method: 'POST',
                data: datos,
                success: function(response) {
                    $("#tablaComisiones").html(response);
                }
            });
        }

        jQuery(function ($) {
            $('#fecha-inicio-rango-dia').datetimepicker({
                "pickTime": false,
                "pickDate": true,
                "minDate": "1/1/1900",
                "maxDate": null,
                "showToday": true,
                "language": "es_MX",
                "defaultDate": "",
                "disabledDates": [],
                "enabledDates": [],
                "icons": {
                    "time": "fa fa-clock-o",
                    "date": "fa fa-calendar",
                    "up": "fa fa-chevron-up",
                    "down": "fa fa-chevron-down"
                },
                "useStrict": false,
                "sideBySide": false,
                "daysOfWeekDisabled": [],
                "collapse": true,
                "calendarWeeks": false,
                "viewMode": "days",
                "minViewMode": "days",
                "useCurrent": false,
                "useSeconds": false
            });

            $('#fecha-fin-rango-dia').datetimepicker({
                "pickTime": false,
                "pickDate": true,
                "minDate": "1/1/1900",
                "maxDate": null,
                "showToday": true,
                "language": "es_MX",
                "defaultDate": "",
                "disabledDates": [],
                "enabledDates": [],
                "icons": {
                    "time": "fa fa-clock-o",
                    "date": "fa fa-calendar",
                    "up": "fa fa-chevron-up",
                    "down": "fa fa-chevron-down"
                },
                "useStrict": false,
                "sideBySide": false,
                "daysOfWeekDisabled": [],
                "collapse": true,
                "calendarWeeks": false,
                "viewMode": "days",
                "minViewMode": "days",
                "useCurrent": false,
                "useSeconds": false
            });
        });

        function resetRangoFechaDias() {
            $("#fecha-inicio-rango-dia").val("");
            $("#fecha-fin-rango-dia").val("");
        }
    </script>
{% endblock %}
