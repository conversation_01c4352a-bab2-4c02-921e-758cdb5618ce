{% extends 'base.html.twig' %}

{% block title %}Cotización de Venta{% endblock %}

{% block stylesheets %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
{% endblock %}

{% block body %}

    <div class="container mt-3">
        <h1 class="text-center mb-4">Cotización de Venta</h1>
        {% for message in app.flashes('success') %}
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                Listo! {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}

        <form id="cotizacionForm" method="post" action="{{ path('pre_auth_uam',{vendedor:vendedor}) }}" autocomplete="off" class="mt-4">
            <input type="hidden" id="idCliente" name="idCliente" value="">

            <div class="row g-3">
                <div class="col-12">
                    <div id="loading" class="text-center">
                        <img src="https://i.gifer.com/ZZ5H.gif" width="50" alt="Cargando...">
                    </div>
                    <div id="message" class="alert"></div>
                </div>
                <div class="col-12 col-md-6">
                    <label for="numeroEmpleado" class="form-label">Número de empleado *:</label>
                    <input type="text" class="form-control" id="numeroEmpleado" name="numeroEmpleado" autocomplete="off" required>
                </div>
                <div class="col-12 col-md-6">
                    <label for="nombre" class="form-label">Nombre(s) *:</label>
                    <input type="text" class="form-control" id="nombre" name="nombre" autocomplete="off" required>
                </div>
                <div class="col-12 col-md-6">
                    <label for="apellidoPaterno" class="form-label">Apellido Paterno *:</label>
                    <input type="text" class="form-control" id="apellidoPaterno" name="apellidoPaterno" autocomplete="off" required>
                </div>
                <div class="col-12 col-md-6">
                    <label for="apellidoMaterno" class="form-label">Apellido Materno :</label>
                    <input type="text" class="form-control" id="apellidoMaterno" name="apellidoMaterno" autocomplete="off">
                </div>
                <div class="col-12 col-md-6">
                    <label for="telefono" class="form-label">Teléfono*:</label>
                    <input type="text" class="form-control" id="telefono" name="telefono" autocomplete="off">
                </div>
                <div class="col-12 col-md-6">
                    <label for="email" class="form-label">Email:</label>
                    <input type="email" class="form-control" id="email" name="email" autocomplete="off">
                </div>
                <div class="col-12 col-md-6">
                    <label for="beneficiario" class="form-label">Beneficiario:</label>
                    <select class="form-select" id="beneficiario" name="beneficiario">
                        <option value="">Sin beneficiario</option>
                        <option value="ADD">Agregar Beneficiario</option>
                    </select>
                </div>
            </div>

            <div id="beneficiarioInfo" class="mt-4 border rounded p-3 bg-light">
                <h4 class="text-center mb-3">Nuevo Beneficiario</h4>
                <div class="row g-3">
                    <div class="col-12 col-md-4">
                        <label for="nombreBeneficiario" class="form-label">Nombre(s)*:</label>
                        <input type="text" class="form-control" id="nombreBeneficiario" name="nombreBeneficiario">
                    </div>
                    <div class="col-12 col-md-4">
                        <label for="apellidoPaternoBeneficiario" class="form-label">Apellido Paterno:*</label>
                        <input type="text" class="form-control" id="apellidoPaternoBeneficiario" name="apellidoPaternoBeneficiario">
                    </div>
                    <div class="col-12 col-md-4">
                        <label for="apellidoMaternoBeneficiario" class="form-label">Apellido Materno:</label>
                        <input type="text" class="form-control" id="apellidoMaternoBeneficiario" name="apellidoMaternoBeneficiario">
                    </div>
                    <div class="col-12 col-md-6">
                        <label for="sexoBeneficiario" class="form-label">Sexo:</label>
                        <select class="form-select" id="sexoBeneficiario" name="sexoBeneficiario">
                            <option value="M">Masculino</option>
                            <option value="F">Femenino</option>
                            <option value="O">Otro</option>
                        </select>
                    </div>
                    <div class="col-12 col-md-6">
                        <label for="tipoBeneficiario" class="form-label">Tipo de Beneficiario:</label>
                        <select class="form-select" id="tipoBeneficiario" name="tipoBeneficiario">
                            <option value="hijo">Hijo</option>
                            <option value="hija">Hija</option>
                            <option value="conyuge">Cónyuge</option>
                            <option value="padre">Padre</option>
                            <option value="madre">Madre</option>
                            <option value="otro">Otro</option>
                        </select>
                    </div>
                    <div class="col-12 col-md-6">
                        <label for="fechaNacimientoBeneficiario" class="form-label">Fecha de Nacimiento:</label>
                        <input type="date" class="form-control" id="fechaNacimientoBeneficiario" name="fechaNacimientoBeneficiario">
                    </div>
                </div>
            </div>

            <div class="row g-3 mt-4">
                <div class="col-12 col-md-6">
                    <label for="unidad" class="form-label">Unidad:</label>
                    <select class="form-select" id="unidad" name="unidad">
                        {% for unidad in unidades %}
                            <option value="{{ unidad.idunidad }}">{{ unidad.nombre }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-12 col-md-6">
                    <label for="tipoPrestacion" class="form-label">Tipo de Prestación:</label>
                    <select class="form-select form-control" id="tipoPrestacion" name="tipoPrestacion">
                        <option value="auditivos">Auditivos</option>
                        <option value="lentes_oftalmicos">Lentes Oftálmicos</option>
                    </select>
                </div>
            </div>

            <!-- Opciones dinámicas según el tipo de prestación seleccionado -->
            <div id="opcionesAuditivos" class="mt-3" style="display: none;">
                <label>Auditivos:</label>
                <select name="opcionesAuditivosNum" class="form-control">
                    <option value="1">Un oído</option>
                    <option value="2">Dos oídos</option>
                </select>
                <input type="hidden" name="opcionesAuditivos[]" value="1984">
            </div>

            <div id="opcionesLentesOftalmicos" class="mt-3" style="display: none;">
                <label>Lentes Oftálmicos:</label><br>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="opcionesLentesOftalmicos[]" value="1338" checked>
                    <label class="form-check-label">BLUE UAM</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="opcionesLentesOftalmicos[]" value="5312" checked>
                    <label class="form-check-label">MICA (obligatorio)</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="opcionesLentesOftalmicos[]" value="4630" checked>
                    <label class="form-check-label">POLICARBONATO UAM</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="opcionesLentesOftalmicos[]" value="6455" checked>
                    <label class="form-check-label">ARMAZÓN</label>
                </div>
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary">Enviar</button>
            </div>
        </form>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#message').hide(); // Ocultar mensajes previos
            $('#loading').hide();
            $('#beneficiarioInfo').hide();
            $('#numeroEmpleado').on('change', function() {
                var numeroEmpleado = $(this).val().trim();
                if(numeroEmpleado !== '') {
                    $('#loading').show(); // Mostrar el GIF de carga
                    $('#message').hide(); // Ocultar mensajes previos

                    $.ajax({
                        url: '{{ path("employee_autofill") }}',
                        method: 'GET',
                        data: { numeroEmpleado: numeroEmpleado },

                        success: function(response) {
                            $('#loading').hide();
                            if(response.success) {
                                $('#message').removeClass('alert-danger').addClass('alert-success')
                                    .text('Datos obtenidos correctamente.').show();

                                $('#apellidoPaterno').val(response.data.apellidoPaterno || '');
                                $('#apellidoMaterno').val(response.data.apellidoMaterno || '');
                                $('#nombre').val(response.data.nombre || '');
                                $('#telefono').val(response.data.telefono || '');
                                $('#email').val(response.data.email || '');
                                $('#idCliente').val(response.data.id || '');

                                var beneficiarioSelect = $('#beneficiario');
                                beneficiarioSelect.empty().append('<option value="">Seleccionar beneficiario</option><option value="ADD">Agregar beneficiario</option>');


                                if(response.data.beneficiarios && response.data.beneficiarios.length) {
                                    response.data.beneficiarios.forEach(function(item) {
                                        beneficiarioSelect.append($('<option>', {
                                            value: item.idcliente,
                                            text: item.nombre + " - " + item.tipo
                                        }));
                                    });
                                    beneficiarioSelect.prop('disabled', false);
                                } else {
                                    // beneficiarioSelect.prop('disabled', true);
                                }
                            } else {
                                /*    $('#message').removeClass('alert-success').addClass('alert-danger')
                                        .text('No se encontraron datos para el empleado.').show();*/
                            }
                        },
                        error: function() {
                            $('#loading').hide();
                            $('#message').removeClass('alert-success').addClass('alert-danger')
                                .text('Error en la solicitud AJAX.').show();
                        }
                    });
                }
            });

            // Show or hide options according to the selected "Tipo de Prestación"
            $('#tipoPrestacion').on('change', function() {
                var valor = $(this).val();
                $('#opcionesAuditivos, #opcionesLentesOftalmicos, #opcionesLentesContactoBlandos, #opcionesLentesContactoRigidos').hide();
                if(valor === 'auditivos'){
                    $('#opcionesAuditivos').show();
                } else if(valor === 'lentes_oftalmicos'){
                    $('#opcionesLentesOftalmicos').show();
                } else if(valor === 'lentes_contacto_blandos'){
                    $('#opcionesLentesContactoBlandos').show();
                } else if(valor === 'lentes_contacto_rigidos'){
                    $('#opcionesLentesContactoRigidos').show();
                }
            }).trigger('change'); // Establish initial visibility

            $('#beneficiario').on('change', function() {
                if(  $('#beneficiario').val() ===""){
                    $('#beneficiarioInfo').hide();
                }else if($('#beneficiario').val() ==="ADD"){
                    $('#beneficiarioInfo').show();
                }

            });
        });
    </script>
{% endblock %}