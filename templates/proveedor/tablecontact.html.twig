{# proveedor/tablecontact.html.twig #}


{% block body %}
<input id="form-email" type="hidden" class="form-control" value="/proveedor/form-email">
<input id="form-phone" type="hidden" class="form-control" value="/proveedor/form-phone">


    {% if message %}
        <div class="alert alert-warning">
            {{ message }}
        </div>
    {% endif %}



    <table class="table table-bordered" id="tableContact" border="1">
        <thead>
            <tr>
                <th>ID Proveedor</th>
                <th>Nombre</th>
                <th>Apellido Paterno</th>
                <th>Apellido Materno</th>
                <th>Puesto</th>
                <th>Nota</th>
                <th>Emails</th>
                <th>Teléfonos</th>
                <th>Opciones</th>
            </tr>
        </thead>
        <tbody>
        {{ dump(proveedoresContactos) }}
            {% for contacto in proveedoresContactos %}
                <tr>
                    <td>{{ contacto.idproveedor }}</td>
                    <td>{{ contacto.nombre }}</td>
                    <td>{{ contacto.apellidopaterno }}</td>
                    <td>{{ contacto.materno }}</td>
                    <td>{{ contacto.puesto }}</td>
                    <td>{{ contacto.nota }}</td>
                    <td>{{ contacto.idproveedorcontacto }}</td>
                    <td></td>
                    <td>
                        <button data-toggle="modal" class="btn btn-primary m-2" dataid="{{ contacto.idproveedorcontacto }}" data-target="#mod-form-email" onclick="abrirFormularioEmail({{ contacto.idproveedorcontacto }})">Email</button>
                        <button data-toggle="modal"  class="btn btn-primary"dataid="{{ contacto.idproveedorcontacto }}" data-target="#mod-form-phone" onclick="abrirFormularioPhone({{ contacto.idproveedorcontacto }})">Teléfono</button>
                    </td>
                </tr>
            {% endfor %}
        </tbody>
    </table>


<script src="https://coe.jquery.com/jquery-3.6.0.min.js">
d
    let table = $('#tableContact').DataTable({
        language: {
        url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/es-ES.json',
        },
    })

$(document).ready(function() {
    $('#mod-formulario-contacts').on('show.bs.modal', function() {
        var proveedorCId = $(this).data('dataid'); // Obtén el valor del atributo data-id del botón
        
        $.ajax({
            url: '/proveedor/table-contacts',
            method: 'GET',
            success: function(data) {
                $('#mod-formulario-contacts .modal-body').html(data);
            },
            error: function() {
                console.log('Error al cargar la tabla de contactos');
            }
        });
    });
});



</script>

{% endblock %}

