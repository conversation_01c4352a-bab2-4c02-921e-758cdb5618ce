{% extends 'admin/layout.html.twig' %}

{% block content %}

    <script src="{{ asset('lib/dataTables/dataTables.min.js') }}"></script>
    <link rel="stylesheet" href="{{ asset('lib/dataTables/dataTables.min.css') }}">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header card-header-info bg-primary">
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="card-title">REGISTRO DE PROVEEDORES</h4>
                            </div>
                        </div>
                    </div>
                    <div class="collapse" id="proveedor">
                        <div class="container-fluid" id="tablaProveedor1"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function () {
            tableProveedor();
            $("#proveedor").collapse('show');
        });

        /**
         * Función que realiza una solicitud AJAX para obtener y mostrar la tabla de proveedores.
         */
        function tableProveedor() { // Realiza una solicitud AJAX.
            $.ajax({
                url: "{{ path('tabla-proveedores') }}", // URL a la que se hace la solicitud.
                type: 'GET', // Tipo de método
                beforeSend: loadingGif("tablaProveedor"), // Función a ejecutar antes de enviar la solicitud. Asume que tienes una función llamada 'loadingGif' que muestra un GIF de carga.
                data: {}, // Datos que se envían al servidor.
                dataType: "html" // Tipo de datos esperados de la respuesta del servidor.
            }).done(function (html) {
                // Función a ejecutar si la solicitud tiene éxito.
                // Actualiza el contenido del elemento con id 'tablaProveedor1' con la respuesta del servidor.
                $("#tablaProveedor1").html(html);

            }).fail(function () {
                alert("error");
            });
        }
    </script>


{% endblock %}
