{# formulario_proveedor.html.twig #}


{% extends 'base.html.twig' %}

{% block body %}


{{ form_start(form, {'attr': {'id': 'add-provider-form', 'class': 'mt-4'}}) }}
{{ form_errors(form) }}

<div class="row"> 
    <p>
  <a class="btn btn-primary" id="btnDatosPersonales" data-bs-toggle="collapse" href="#datosPersonales" role="button" aria-expanded="false" aria-controls="datosPersonales">
    Datos Personales
  </a>
  <a class="btn btn-primary" id="btnDireccion" data-bs-toggle="collapse" href="#direccion" role="button" aria-expanded="false" aria-controls="direccion">
    Dirección
  </a>
  <a class="btn btn-primary" id="btnDireccion2" data-bs-toggle="collapse" href="#direccion2" role="button" aria-expanded="false" aria-controls="direccion2">
    Dirección 2
  </a>
</p>

<div class="collapse" id="datosPersonales">
  <div class="card card-body">
    <div class="col-md-12 form-card">
        <h4 class="mb-3">Datos Personales</h4>
        {% for field in [form.nombre, form.correoelectronico, form.telefono, form.movil, form.sitioweb, form.rfc] %}
            <div class="form-group">
                {{ form_label(field) }}
                {{ form_widget(field, {'attr': {'class': 'form-control small-input'}}) }}
                {{ form_errors(field) }}
            </div>
        {% endfor %}
        </div>
    </div>
</div>

<div class="collapse" id="direccion">
  <div class="card card-body">
    <div class="col-md-12 form-card">
        <h4 class="mb-3">Dirección</h4>
        {% for field in [form.calle, form.calle2, form.numeroexterior, form.numerointerior, form.colonia, form.municipio] %}
            <div class="form-group">
                {{ form_label(field) }}
                {{ form_widget(field, {'attr': {'class': 'form-control small-input'}}) }}
                {{ form_errors(field) }}
            </div>
        {% endfor %}
      </div>
    </div>
</div>


    <div class="collapse" id="direccion2">
      <div class="card card-body">
          <div class="col-md-12 form-card">
        <h4 class="mb-12">Dirección 2</h4>
        {% for field in [form.estado, form.codigopostal, form.pais, form.razonsocial, form.ciudad,form.notas] %}
            <div class="form-group">
                {{ form_label(field) }}
                {{ form_widget(field, {'attr': {'class': 'form-control small-input'}}) }}
                {{ form_errors(field) }}
            </div>
        {% endfor %}
    </div>

</div> 

<div class="form-group mb-3">
    {{ form_row(form.Enviar, {'attr': {'class': 'btn btn-primary'}}) }}
</div>

{{ form_end(form) }}




<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<link rel="stylesheet" href="{{ asset('/css/proveedores.css') }}">



<script>



    var urlObtenerFormularioProveedor = "{{ path('add-provider') }}";

    $(document).ready(function(){
        $("#add-provider-form").submit(function(e) {
            var formData = new FormData(this);

            $.ajax({
                url: urlObtenerFormularioProveedor,
                type: 'POST', 
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    // Puedes realizar acciones adicionales aquí en función de la respuesta del servidor.
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log('Error: ' + errorThrown);
                }
            });
        });
    })
</script>

<script>
document.getElementById('btnDatosPersonales').addEventListener('click', function() {
    let direccion = new bootstrap.Collapse(document.getElementById('direccion'), {toggle: false});
    let direccion2 = new bootstrap.Collapse(document.getElementById('direccion2'), {toggle: false});
    direccion.hide();
    direccion2.hide();
});

document.getElementById('btnDireccion').addEventListener('click', function() {
    let datosPersonales = new bootstrap.Collapse(document.getElementById('datosPersonales'), {toggle: false});
    let direccion2 = new bootstrap.Collapse(document.getElementById('direccion2'), {toggle: false});
    datosPersonales.hide();
    direccion2.hide();
});

document.getElementById('btnDireccion2').addEventListener('click', function() {
    let datosPersonales = new bootstrap.Collapse(document.getElementById('datosPersonales'), {toggle: false});
    let direccion = new bootstrap.Collapse(document.getElementById('direccion'), {toggle: false});
    datosPersonales.hide();
    direccion.hide();
});
</script>

{% endblock %}
