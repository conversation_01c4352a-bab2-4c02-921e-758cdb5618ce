{# templates/form/emailForm.html.twig #}
<link rel="stylesheet" href="{{ asset('/css/proveedores.css') }}"> 



<input type="hidden" name="idproveedorcontacto" value="{{ idproveedorcontacto }}"/>


{{ form_start(form, {'attr': {'id': 'add-email-form'}}) }}


    <div class="form-group">
        {{ form_label(form.email) }} {# Renderiza la etiqueta del campo email #}
        {{ form_widget(form.email) }} {# Renderiza el campo de entrada del email #}
        {{ form_errors(form.email) }} {# Renderiza los errores del campo email, si los hay #}
    </div>

    
    <div class="form-group d-none">
        {{ form_label(form.proveedorcontactoIdproveedorcontacto) }} {# Renderiza la etiqueta del campo email #}
        {{ form_widget(form.proveedorcontactoIdproveedorcontacto) }} {# Renderiza el campo de entrada del email #}
        {{ form_errors(form.proveedorcontactoIdproveedorcontacto) }} {# Renderiza los errores del campo email, si los hay #}
    </div>

    <div>
        {{ form_label(form.Enviar) }}
        {{ form_row(form.Enviar) }}
        {{ form_widget(form.Enviar) }} 
    </div>


{{ form_end(form) }}



    <table class="table">
        <thead>
            <tr>
                <th>ID Email</th>
                <th>Email</th>
            </tr>
        </thead>
        <tbody>
        {{ dump(results) }}
            {# Itera sobre cada item en proveedoresEmails y renderiza los datos en filas de tabla #}
            {% for proveedorEmail in results %}
                <tr>
                    <td>{{ proveedorEmail.idproveedoremail }}</td>
                    <td>{{ proveedorEmail.email }}</td>
                </tr>
            {% endfor %}
        </tbody>
    </table>



<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>

var urlObtenerFormEmail = "{{ path('form-email') }}";

$("#add-email-form").submit(function(e) {
    var formData = new FormData(this);

    $.ajax({
        url: urlObtenerFormEmail,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Error en la solicitud AJAX:', errorThrown);
            // Muestra un mensaje de error al usuario.
            alert('Hubo un error al enviar el email. Por favor, intenta de nuevo.'); // Puedes reemplazar esto con tu propio manejo de mensajes de usuario.
        }
    });
});
    
</script>

{{ form_end(form) }}