<input id="agregar-contacto-id" type="hidden" class="form-control">
<input id="add-provider" type="hidden" class="form-control" value="/proveedor/add-provider">
<input id="edit-provider" type="hidden" class="form-control" value="/proveedor/edit-provider">
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.x/font/bootstrap-icons.css" rel="stylesheet">
<link rel="stylesheet" href="{{ asset('/css/proveedores.css') }}">

<div class="table-responsive">
    <div class="container text-center ">


        <div class="row justify-content-center">

            <table id="tablaProveedor" class="table mx-auto">

                <a class="btn btn-sm btn-primary m-3" href="#" data-toggle="modal"
                   data-target="#mod-formulario-proveedor">
                    <i class="fa-solid fa-user-tie"></i> Agregar Proveedor
                </a>
                <thead>
                <tr class="text-start">
                    <th class="text-start text-center" scope="col">Nombre Comercial</th>
                    <th class="text-start text-center" scope="col">RFC</th>
                    <th class="text-start text-center" scope="col">Teléfono</th>
                    <th class="text-start text-center" scope="col">Email</th>
                    <th class="text-start text-center" scope="col">Opciones</th>
                </tr>
                </thead>
                <tbody>
                {% for proveedor in proveedores %}
                    <tr>
                        <td class="text-center align-middle">{{ proveedor.NombreComercial }}</td>
                        <td class="text-center align-middle">{{ proveedor.RFC }}</td>
                        <td class="text-center align-middle">{{ proveedor.telefono }}</td>
                        <td class="text-center align-middle">{{ proveedor.correoelectronico }}</td>
                        <td id="td-{{ proveedor.idproveedor }}" class="text-center align-middle">

                            <a href="#" class="Remove" data-id="{{ proveedor.idproveedor }}">
                                <i class="bi bi-trash" style="font-size: 24px;"></i>
                            </a>

                            <a href="#" data-id="{{ proveedor.idproveedor }}" data-toggle="modal"
                               data-target="#mod-formulario-contacts">
                                <i class="fa-regular fa-address-book" style="font-size: 24px;"></i>
                            </a>
                            <a href="#" data-id="{{ proveedor.idproveedor }}" data-toggle="modal"
                               data-target="#mod-editar-proveedor">
                                <i class="bi bi-pencil" style="font-size: 24px;"></i>
                            </a>
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>


<!-- Modal Agregar Nuevo Contacto Proveedor -->
<div class="modal fade" id="mod-formulario" tabindex="-1" role="dialog" aria-labelledby="mod-formulario"
     aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Agregar Contacto Proveedor</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Cerrar">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body"></div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>


<!-- Modal Agregar Nuevo Proveedor -->
<div class="modal fade" id="mod-formulario-proveedor" tabindex="-1" role="dialog" aria-labelledby="mod-formulario"
     aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Agregar Proveedor</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Cerrar">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body"></div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Editar Proveedor -->
<div class="modal fade" id="mod-editar-proveedor" tabindex="-1" role="dialog" aria-labelledby="mod-formulario"
     aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Editar Proveedor</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Cerrar">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body"></div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>


<!-- CONTACTOS -->
<div class="modal fade" id="mod-formulario-contacts" tabindex="-1" role="dialog" aria-labelledby="mod-formulario"
     aria-hidden="true">
    <div
            class="modal-dialog modal-lg" role="document">
        <!-- Aquí se añade la clase modal-lg -->
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Contactos</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Cerrar">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body"></div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<!-- EMAIL -->
<div class="modal" id="mod-form-email" tabindex="-1" role="dialog" aria-labelledby="mod-form-email"
     aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Email</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Cerrar">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body"></div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>


<!-- PHONE -->
<div class="mod" id="mod-form-phone" tabindex="-1" role="dialog" aria-labelledby="mod-form-phone"
     aria-hidden="true">
    <div class="mod-dialog mod-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Teléfono</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Cerrar">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body"></div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>


<!--CRUD
CREAT
READ
UPDATE
DELETE
-->

<script>
    $(document).ready(function () {

        let table = new DataTable('#tablaProveedor', {
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/es-ES.json',
            },
        });

        $(".Remove").click(function () {
            var proveedorid = $(this).data("id");

            console.log(proveedorid);

            var url = "{{ path('cambiar_status_proveedor', {'id': 'PLACEHOLDER'}) }}".replace('PLACEHOLDER', proveedorid);

            Swal.fire({
                title: 'Estás seguro de que deseas eliminar el Proveedor?',
                showDenyButton: true,
                type: 'question',
                showCancelButton: true,
                confirmButtonText: 'Sí',
            }).then((result) => {
                console.log(result.value);
                if (result.value) {
                    $.ajax({
                        url: url,
                        method: 'POST',
                        success: function (response) {
                            const Toast = Swal.mixin({
                                toast: true,
                                position: 'top-end',
                                showConfirmButton: false,
                                timer: 2000,
                                width: '800px',
                                height: '800px',
                                timerProgressBar: true,
                                didOpen: (toast) => {
                                    toast.addEventListener('mouseenter', Swal.stopTimer)
                                    toast.addEventListener('mouseleave', Swal.resumeTimer)
                                }
                            });
                            tableProveedor();
                            Toast.fire({
                                type: 'success',
                                title: 'Borrado Correctamente'
                            });
                        },
                        error: function (error) {
                            console.error("Error al cambiar el estado:", error);
                        }
                    });
                }
            });
        });


    });


    $('#mod-formulario').on('show.bs.modal', function (event) {

        var button = $(event.relatedTarget); // Botón que activó el modal
        var idProveedor = button.data('id'); // Obtén el ID del proveedor desde el botón

        console.log(idProveedor);

        // Realiza una solicitud AJAX para cargar el formulario
        $.ajax({
            url: '{{ path('agregar_contacto', {'idproveedor': 0}) }}'.replace('0', idProveedor), // Reemplaza 0 con el ID del proveedor
            method: 'GET',
            success: function (data) {
                // Inserta el contenido del formulario en el cuerpo del modal
                $('#mod-formulario .modal-body').html(data);
            },
            error: function () {
                alert('Error al cargar el formulario.');
            }
        });
    });

    // Cuando el modal se muestra...
    $('#mod-formulario-proveedor').on('show.bs.modal', function (event) {
        var buttonProvider = $(event.relatedTarget); // Botón que activó el modal
        var idProveedor = buttonProvider.data('id'); // Obtén el ID del proveedor desde el botón

        // Obtiene la URL base desde el input
        var baseUrl = $('#add-provider').val();

        // Construye la URL completa reemplazando '0' con el ID del proveedor
        var completeUrl = baseUrl.replace('0', idProveedor);

        // Realiza una solicitud AJAX para cargar el formulario
        $.ajax({
            url: completeUrl,
            method: 'GET',
            success: function (data) {
                // Inserta el contenido del formulario en el cuerpo del modal
                $('#mod-formulario-proveedor .modal-body').html(data);
            },
            error: function () {
                alert('Error al cargar el formulario.');
            }
        });
    });

    $('#mod-formulario-contacts').on('show.bs.modal', function (event) {
        var buttonProvider = $(event.relatedTarget); // Botón que activó el modal
        var idProveedor = buttonProvider.data('id'); // Obtén el ID del proveedor desde el botón
        console.log(idProveedor);
        $.ajax({
            url: '/proveedor/table-contacts',
            method: 'GET',
            data: {idproveedor: idProveedor},  // Pasamos el ID del proveedor como parámetro.
            success: function (data) {
                $('#mod-formulario-contacts .modal-body').html(data);
            },
            error: function () {
                console.log('Error al cargar la tabla de contactos');
            }
        });
    });


    $('#mod-editar-proveedor').on('show.bs.modal', function (event) {

        var button = $(event.relatedTarget); // Botón que activó el modal
        var idProveedor = button.data('id'); // Obtén el ID del proveedor desde el botón

        console.log(idProveedor);

        // Realiza una solicitud AJAX para cargar el formulario
        $.ajax({
            url: '{{ path('edit-provider', {'idproveedor': 0}) }}'.replace('0', idProveedor),
            method: 'GET',
            success: function (data) {
                // Inserta el contenido del formulario en el cuerpo del modal
                $('#mod-editar-proveedor .modal-body').html(data);
            },
            error: function () {
                alert('Error al cargar el formulario.');
            }
        });
    });


    function abrirFormularioEmail(idproveedorcontacto) {
        console.log('La función abrirFormularioEmail ha sido llamada con id:', idproveedorcontacto);
        var baseUrl = $('#form-email').val();
        var completeUrl = baseUrl + "?idproveedorcontacto=" + idproveedorcontacto;

        console.log('La URL completa es:', completeUrl); // Verifica que la URL se construye correctamente

        $.ajax({
            url: completeUrl,
            method: 'GET',
            success: function (data) {
                $('#mod-form-email .modal-body').html(data);
            },
            error: function (jqXHR) {
                console.error(jqXHR.responseText); // Imprime el mensaje de error del servidor
            }
        });
    }


    function abrirFormularioPhone(idproveedorcontacto) {
        console.log('La función abrirFormularioPhone ha sido llamada con id:', idproveedorcontacto);
        var baseUrl = $('#form-phone').val();
        var completeUrl = baseUrl + "?idproveedorcontacto=" + idproveedorcontacto;

        console.log('La URL completa es:', completeUrl);

        $.ajax({
            url: completeUrl,
            method: 'GET',
            success: function (data) {
                $('#mod-form-phone .modal-body').html(data);
            },
            error: function (jqXHR) {
                console.error(jqXHR.responseText);
            }
        });
    }


</script>
