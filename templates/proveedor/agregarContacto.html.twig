{# proveedor_contacto_form.html.twig #}

{% extends 'base.html.twig' %}

{% block body %}
{{ form_start(form, {'attr': {'id': 'FormularioProveedorContacto' }}) }}
{{ form_errors(form) }}

<div class="form-group">
    {{ form_label(form.nombre) }}
    {{ form_widget(form.nombre, {'attr': {'required': 'required'}}) }}
    {{ form_errors(form.nombre) }}
</div>

<div class="form-group">
    {{ form_label(form.apellidopaterno) }}
    {{ form_widget(form.apellidopaterno, {'attr': {'required': 'required'}}) }}
    {{ form_errors(form.apellidopaterno) }}
</div>

<div class="form-group">
    {{ form_label(form.apelliidomaterno) }}
    {{ form_widget(form.apelliidomaterno, {'attr': {'required': 'required'}}) }}
    {{ form_errors(form.apelliidomaterno) }}
</div>

<div class="form-group">
    {{ form_label(form.puesto) }}
    {{ form_widget(form.puesto, {'attr': {'required': 'required'}}) }}
    {{ form_errors(form.puesto) }}
</div>

<div class="form-group">
    {{ form_label(form.nota) }}
    {{ form_widget(form.nota) }}
    {{ form_errors(form.nota) }}
</div>


    {{ form_label(form.Enviar) }}
    {{ form_row(form.Enviar) }}

    {{ form_end(form) }}

<script>
  
    var urlObtenerFormularioContacto = "{{ path('agregar_contacto',{'idproveedor': 0}) }}";
    urlObtenerFormularioContacto=urlObtenerFormularioContacto.replace("0","{{idproveedor}}");

$(document).ready(function(){



$("#FormularioProveedorContacto").submit(function(e) {
    console.log("Diogenes <3");

    var formData = new FormData(this); // Crea un objeto FormData a partir del formulario. Este objeto facilita el envío de datos del formulario.
    var proveedorId = $(this).data('id'); // Obtén el valor del atributo data-id del botón
    $.ajax({ // Realiza una solicitud AJAX.
        url: urlObtenerFormularioContacto, // La URL a la que se realiza la solicitud. Esta es la ruta al método de tu controlador en el backend.
        type: 'POST', 
        data: formData, // Los datos que se enviarán al servidor. Aquí, se envían los datos del formulario.
        processData: false, // Indica a jQuery que no procese los datos, ya que estás utilizando FormData.
        contentType: false, // Indica a jQuery que no establezca automáticamente el encabezado Content-Type, ya que estás utilizando FormData.

        success: function(response) { // Define una función para manejar una respuesta exitosa del servidor.

            // Puedes realizar acciones adicionales aquí en función de la respuesta del servidor.
        },
        
        error: function(jqXHR, textStatus, errorThrown) { // Define una función para manejar un error en la solicitud AJAX.
            console.log('Error: ' + errorThrown); // Imprime el mensaje de error en la consola.
        }
    });
});
})
    </script>
{% endblock %}

