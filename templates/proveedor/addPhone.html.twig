{# templates/proveedor/addPhone.html.twig #}

<input type="hidden" name="idproveedorcontacto" value="{{ idproveedorcontacto }}"/>


{{ form_start(form, {'attr': {'id': 'add-phone-form'}}) }}

    <div class="form-group">
        {{ form_label(form.nombre) }} {# Renderiza la etiqueta del campo nombre #}
        {{ form_widget(form.nombre) }} {# Renderiza el campo de entrada del nombre #}
        {{ form_errors(form.nombre) }} {# Renderiza los errores del campo nombre, si los hay #}
    </div>

    <div class="form-group">
        {{ form_label(form.telefono) }} {# Renderiza la etiqueta del campo teléfono #}
        {{ form_widget(form.telefono) }} {# Renderiza el campo de entrada del teléfono #}
        {{ form_errors(form.telefono) }} {# Renderiza los errores del campo teléfono, si los hay #}
    </div>

        <div class="form-group d-none">
        {{ form_label(form.proveedorcontactoIdproveedorcontacto) }} {# Renderiza la etiqueta del campo teléfono #}
        {{ form_widget(form.proveedorcontactoIdproveedorcontacto) }} {# Renderiza el campo de entrada del teléfono #}
        {{ form_errors(form.proveedorcontactoIdproveedorcontacto) }} {# Renderiza los errores del campo teléfono, si los hay #}
    </div>

    <div class="form-group">
        {{ form_widget(form.Enviar) }} {# Renderiza el botón de enviar #}
    </div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>

var urlObtenerFormPhone = "{{ path('form-phone') }}";

$("#add-phone-form").submit(function(e) {
    var formData = new FormData(this);

    $.ajax({
        url: urlObtenerFormPhone,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            console.log("Respuesta del servidor:", response);
            // Cierra el modal o realiza cualquier otra acción en caso de éxito.
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.error('Error en la solicitud AJAX:', errorThrown);
            // Muestra un mensaje de error al usuario.
            alert('Hubo un error al enviar el email. Por favor, intenta de nuevo.'); // Puedes reemplazar esto con tu propio manejo de mensajes de usuario.
        }
    });
});
    
</script>

{{ form_end(form) }} {# Finaliza el formulario #}
