{# Este template contiene solo el fragmento del formulario para cargas AJAX #}

{# Mostrar Flash Messages #}
carga esto
{% for message in app.flashes('error') %}

    <script>
        $(document).ready(function() {
            showFolioValidationMessage('{{ message|escape('js') }}', 'error');
        });
    </script>
{% endfor %}

{% for message in app.flashes('success') %}
    <script>
        $(document).ready(function() {
            showFolioValidationMessage('{{ message|escape('js') }}', 'success');
        });
    </script>
{% endfor %}

<!-- Important Notice about Payment -->
<div class="important-notice">
    <div class="notice-content">
        <div class="notice-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="notice-text">
            <div class="notice-title">¡IMPORTANTE!</div>
            <p class="notice-description">
                Tu venta debe estar completamente liquidada para poder generar la factura.
                Verifica que el pago esté registrado antes de continuar.
            </p>
        </div>
    </div>
</div>

{{ form_start(form, {attr: {'id': 'formularioClienteFactura', 'enctype': 'multipart/form-data', 'class': 'row', 'action': path('app_invoice_formulario_empresa', {'e': prefijo}) }}) }} {{ form_errors(form) }}

<!-- Container for validation messages -->
<div id="message-container"></div>

<!-- Folio field with responsive layout -->
<div class="col-12">
    {{ form_label(form.folio, null, {'label_attr': {'class': 'form-label'}}) }}
    <div class="d-flex align-items-center">
        <span class="font-weight-bold text-primary me-2">{{ prefijo|upper }} </span>
        {{ form_widget(form.folio, {'attr': {'class': 'form-control flex-grow-1'}}) }}
    </div>
    {% if form.folio.vars.help %}
        <small class="form-text text-muted">{{ form_help(form.folio) }}</small>
    {% endif %}
</div>

<!-- Campos adicionales del formulario (ocultos inicialmente) -->
<div id="additional-fields" style="display: none;">
    <!-- Email field -->
    <div class="col-12 ">
        {{ form_row(form.email) }}
    </div>

    <!-- Razón social field -->
    <div class="col-12">
        {{ form_row(form.razonsocial) }}
    </div>

    <!-- RFC field -->
    <div class="col-12">
        {{ form_row(form.rfc) }}
    </div>

    <!-- Código postal field -->
    <div class="col-12">
        {{ form_row(form.codigopostal) }}
    </div>

    <!-- Tipo de persona field -->
    <div class="col-12">
        {{ form_row(form.tipoPersona) }}
    </div>



    <!-- Dynamic content for fiscal regime and CFDI use -->
    <div id="tipoFM" class="col-12">
        <!-- Content loaded dynamically via AJAX -->
    </div>

    <!-- Hidden fields for usocfdi and regimenfiscal -->
    {{ form_widget(form.usocfdi) }}
    {{ form_widget(form.regimenfiscal) }}

    <!-- Notas field -->
    <div class="col-12">
        {{ form_row(form.notas) }}
    </div>

    <!-- Submit button -->
    <div class="col-12 text-center mt-4">
        <button type="submit" class="btn btn-primary btn-lg">
            <i class="fas fa-file-invoice me-2"></i>
            Generar Factura
        </button>
    </div>
</div>

{{ form_end(form) }}

<script>
// JavaScript específico para el formulario
$(document).ready(function() {
    // Variables globales para el estado del folio
    window.folioValidado = false;
    let folioValidationTimeout;



    // Función para validar folio
    function validateFolio(folio, prefijo) {
        $.ajax({
            url: "{{ path('app_invoice_validar_folio', {'e': '__PREFIJO__'}) }}".replace('__PREFIJO__', prefijo),
            type: 'POST',
            data: { folio: folio },
            dataType: 'json',
            success: function(response) {
                console.log('Respuesta del servidor:', response);
                const folioInput = $('#invoice_clientefacturadatos_folio');

                if (response.valid) {
                    window.folioValidado = true;
                    folioInput.addClass('is-valid').removeClass('is-invalid');
                    showFolioValidationMessage(response.message, 'success');

                    // Mostrar los campos adicionales con animación
                    showAdditionalFields();
                } else {
                    window.folioValidado = false;
                    folioInput.addClass('is-invalid').removeClass('is-valid');
                    console.log('Mostrando mensaje de error:', response.message);
                    showFolioValidationMessage(response.message, 'error');

                    // Ocultar los campos adicionales si están visibles
                    hideAdditionalFields();
                }
            },
            error: function(xhr, status, error) {
                console.error('Error en validación de folio:', error);
                window.folioValidado = false;
                $('#invoice_clientefacturadatos_folio').addClass('is-invalid').removeClass('is-valid');
                showFolioValidationMessage('Error al validar el folio. Por favor, intenta nuevamente.', 'error');
                hideAdditionalFields();
            }
        });
    }

    // Event listener para tipo de persona
    $(document).on('change', '#invoice_clientefacturadatos_tipoPersona', function() {
        selectRFCDFI();
    });

    // Event listener para el campo folio (usando delegación de eventos)
    $(document).on('input', '#invoice_clientefacturadatos_folio', function() {
        const folio = $(this).val().trim();
        const prefijo = $('#prefijo').val() || '{{ prefijo }}';

        // Limpiar timeout anterior
        clearTimeout(folioValidationTimeout);

        // Resetear estado
        window.folioValidado = false;
       // $('#folio-validation-message').remove();
        $(this).removeClass('is-valid is-invalid');

        // Ocultar campos adicionales si están visibles
        hideAdditionalFields();

        if (folio.length >= 3) {
            // Mostrar mensaje de validación
            showFolioValidationMessage('Validando folio...', 'info');

            // Validar después de un delay
            folioValidationTimeout = setTimeout(function() {
                validateFolio(folio, prefijo);
            }, 500);
        }
    });

    // Función para mostrar campos adicionales
    function showAdditionalFields() {
        $('#additional-fields').slideDown(400);
    }

    // Función para ocultar campos adicionales
    function hideAdditionalFields() {
        $('#additional-fields').slideUp(400);
    }

    // Interceptar envío del formulario y enviarlo por AJAX
    $(document).on('submit', '#formularioClienteFactura', function(e) {
        e.preventDefault(); // Siempre prevenir el envío tradicional

        console.log('Form submission intercepted');

        if (!window.folioValidado) {
            Swal.fire({
                title: 'Validación requerida',
                text: 'Por favor, ingresa un folio válido antes de continuar.',
                icon: 'warning',
                confirmButtonText: 'Entendido'
            });
            return false;
        }

        // Enviar formulario por AJAX
        submitFormAjax();
    });

    // Función para enviar el formulario por AJAX
    function submitFormAjax() {
        const form = $('#formularioClienteFactura');
        const formData = new FormData(form[0]);
        const actionUrl = form.attr('action');

        console.log('Submitting form to:', actionUrl);
        console.log('Form data:', formData);

        // Mostrar indicador de carga
        showSubmitLoadingIndicator();

        // Deshabilitar el botón de envío
        const submitButton = form.find('button[type="submit"], input[type="submit"]');
        submitButton.prop('disabled', true);

        $.ajax({
            url: actionUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'html',
            success: function(html) {
                console.log('Respuesta del servidor recibida');
                console.log('Longitud de la respuesta:', html.length);
                console.log('Primeros 200 caracteres:', html.substring(0, 200));

                hideSubmitLoadingIndicator();
                submitButton.prop('disabled', false);

                // Actualizar el formulario con la nueva respuesta HTML
            //    $("#formulario").html(html);
            },
            error: function(xhr, status, error) {
                hideSubmitLoadingIndicator();
                submitButton.prop('disabled', false);

                let errorMessage = 'Error al procesar la solicitud.';

                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    if (errorResponse.message) {
                        errorMessage = errorResponse.message;
                    }
                } catch (e) {
                    // Si no se puede parsear, usar mensaje genérico
                    if (xhr.status === 500) {
                        errorMessage = 'Error interno del servidor. Por favor, intenta nuevamente.';
                    } else if (xhr.status === 422) {
                        errorMessage = 'Datos del formulario no válidos. Verifica la información ingresada.';
                    }
                }

                Swal.fire({
                    title: 'Error',
                    text: errorMessage,
                    icon: 'error',
                    confirmButtonText: 'Aceptar'
                });
            }
        });
    }

    // Función para mostrar indicador de carga durante el envío
    function showSubmitLoadingIndicator() {
        // Crear overlay de carga
        const loadingOverlay = `
            <div id="submit-loading-overlay" class="submit-loading-overlay">
                <div class="submit-loading-content">
                    <div class="submit-loading-spinner">
                        <div class="spinner-border text-light" role="status">
                            <span class="sr-only">Procesando...</span>
                        </div>
                    </div>
                    <div class="submit-loading-text">
                        Generando factura...
                    </div>
                </div>
            </div>
        `;
        
        $('body').append(loadingOverlay);
        $('#submit-loading-overlay').fadeIn(200);
    }

    // Función para ocultar indicador de carga
    function hideSubmitLoadingIndicator() {
        $('#submit-loading-overlay').fadeOut(200, function() {
            $(this).remove();
        });
    }

    // Función para cargar opciones de régimen fiscal y uso CFDI
    function selectRFCDFI() {
        var tipoPersona = $("#invoice_clientefacturadatos_tipoPersona").val();

        if (!tipoPersona) return;

        var request = $.ajax({
            url: "{{ path('app_invoice_plantilla_select') }}/" + tipoPersona,
            method: 'POST',
            dataType: 'html',
            data: { tipoPersona: tipoPersona },
        });

        request.done(function(html) {
            $("#tipoFM").html(html);

            // Asignar valores previos si existen
            var usoCFDI = $("#invoice_clientefacturadatos_usocfdi").val();
            var Rf = $("#invoice_clientefacturadatos_regimenfiscal").val();

            if (usoCFDI) {
                $("#usocdfi").val(usoCFDI);
            }

            if (Rf) {
                $("#regimenfiscal").val(Rf);
            }
        });

        request.fail(function(jqXHR, textStatus) {
            console.error("Error cargando opciones: " + textStatus);
        });
    }

    // Función para asignar valor de uso CFDI
    window.asignarValorUsoCfdi = function(valor) {
        $("#invoice_clientefacturadatos_usocfdi").val(valor);
    };

    // Función para asignar valor de régimen fiscal
    window.asignarValorRegimenFiscal = function(valor) {
        $("#invoice_clientefacturadatos_regimenfiscal").val(valor);
    };
});
</script>
