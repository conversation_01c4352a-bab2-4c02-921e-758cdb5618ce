{# Template para mostrar cuando la factura se genera exitosamente #}

<div class="success-container text-center py-5">
    <div class="success-icon mb-4">
        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
    </div>
    
    <h2 class="text-success mb-3">¡Factura Generada Exitosamente!</h2>
    
    <div class="alert alert-success mx-auto" style="max-width: 600px;">
        <h5 class="alert-heading text-white">
            <i class="fas fa-envelope me-2"></i>
            Factura Enviada
        </h5>
        <p class="mb-2 text-white">
            Tu factura ha sido generada correctamente y se ha enviado a tu correo electrónico.
        </p>
        <hr>
        <p class="mb-0">
            <small class="text-white">
                <i class="fas fa-info-circle me-1"></i>
                <strong>Nota:</strong> Recuerda revisar tu carpeta de spam si no recibes el correo en los próximos minutos.
            </small>
        </p>
    </div>
    
    <div class="mt-4">
        <button type="button" class="btn btn-primary btn-lg" id="facturar-otra-venta">
            <i class="fas fa-plus me-2"></i>
            Facturar Otra Venta
        </button>
    </div>
</div>

<script>
$(document).ready(function() {
    // Manejar clic en "Facturar Otra Venta"
    $('#facturar-otra-venta').on('click', function() {
        // Mostrar indicador de carga
        $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Cargando...');
        
        // Cargar el formulario nuevamente por AJAX
        $.ajax({
            url: "{{ path('app_invoice_formulario_empresa', {'e': prefijo}) }}",
            type: 'GET',
            dataType: 'html',
            success: function(html) {
                // Reemplazar el contenido con el formulario
                $("#formulario").html(html);
            },
            error: function(xhr, status, error) {
                console.error('Error al cargar el formulario:', error);
                alert('Error al cargar el formulario. Por favor, recarga la página.');
                
                // Rehabilitar el botón
                $('#facturar-otra-venta').prop('disabled', false).html('<i class="fas fa-plus me-2"></i>Facturar Otra Venta');
            }
        });
    });
});
</script>

<style>
.success-container {
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.success-icon {
    animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.btn-primary {
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}
</style>
