<script>
    // Variables globales para el estado del folio
    let folioValidationTimeout;
    let folioValidado = false;

    // Validación en tiempo real del folio
    $(document).on('input', '#invoice_clientefacturadatos_folio', function() {
        const folio = $(this).val();
        const prefijo = '{{ prefijo }}';


        // Limpiar timeout anterior
        clearTimeout(folioValidationTimeout);

        // Resetear estado
        folioValidado = false;
        $('#folio-validation-message').remove();
        $(this).removeClass('is-valid is-invalid');

        // Ocultar campos adicionales si están visibles
        hideAdditionalFields();

        if (folio && folio.length >= 3) {
            // Mostrar indicador de carga
            showFolioValidationMessage('Validando folio...', 'info');

            // Validar después de 800ms de inactividad
            folioValidationTimeout = setTimeout(function() {
                validateFolio(folio, prefijo);
            }, 800);
        }
    });

    // Interceptar envío del formulario y enviarlo por AJAX
    $(document).on('submit', '#formularioClienteFactura', function(e) {
        e.preventDefault(); // Siempre prevenir el envío tradicional

        if (!folioValidado) {
            Swal.fire({
                title: 'Validación requerida',
                text: 'Por favor, ingresa un folio válido antes de continuar.',
                icon: 'warning',
                confirmButtonText: 'Entendido'
            });
            return false;
        }

        // Verificar que el token CSRF esté presente
        const csrfToken = $('input[name*="[_token]"]').val();
        if (!csrfToken) {
            Swal.fire({
                title: 'Error de seguridad',
                text: 'Token de seguridad no encontrado. Por favor, recarga la página e intenta nuevamente.',
                icon: 'error',
                confirmButtonText: 'Recargar página'
            }).then((result) => {
                if (result.isConfirmed) {
                    location.reload();
                }
            });
            return false;
        }

        // Validar campos dinámicos
        const regimenFiscal = $('select[name="regimenFiscal"]').val();
        const usocfdiMoral = $('select[name="usocfdiMoral"]').val();

        if (!regimenFiscal || regimenFiscal === 'Selecciona una opción' || regimenFiscal === '') {
            Swal.fire({
                title: 'Campo requerido',
                text: 'Por favor, selecciona un régimen fiscal válido.',
                icon: 'warning',
                confirmButtonText: 'Entendido'
            });
            return false;
        }

        if (!usocfdiMoral || usocfdiMoral === 'Selecciona una opción' || usocfdiMoral === '') {
            Swal.fire({
                title: 'Campo requerido',
                text: 'Por favor, selecciona un uso de CFDI válido.',
                icon: 'warning',
                confirmButtonText: 'Entendido'
            });
            return false;
        }

        // Enviar formulario por AJAX
        submitFormAjax();
    });

    $("#invoice_clientefacturadatos_tipoPersona").change(function () {
        cargarSelectsDinamicos();
    });

    // Función para cargar los selects dinámicos manteniendo valores seleccionados
    function cargarSelectsDinamicos() {
        var tipoPersona = $("#invoice_clientefacturadatos_tipoPersona").val();

        if (!tipoPersona) {
            return;
        }

        $.ajax({
            url: "{{ path('app_invoice_plantilla_select') }}/" + tipoPersona,
            type: 'POST',
            dataType: "html",
            beforeSend: function() {
                showLoadingIndicator();
            },
            success: function (html) {
                setTimeout(function() {
                    $("#tipoFM").html(html);
                    hideLoadingIndicator();

                    // Aplicar estilos mejorados a los selects dinámicos
                    applyEnhancedStyles();

                    // Restaurar valores seleccionados si existen
                    restaurarValoresSeleccionados();

                    // Animar la aparición de los nuevos campos
                    $("#tipoFM").hide().fadeIn(400);
                }, 300);
            },
            error: function (xhr, status, error) {
                hideLoadingIndicator();
                Swal.fire({
                    title: 'Error al cargar tipo de persona',
                    text: 'Ocurrió un error: ' + error,
                    icon: 'error',
                    confirmButtonText: 'Aceptar'
                });
            }
        });
    }

    // Función para aplicar estilos mejorados a los selects dinámicos
    function applyEnhancedStyles() {
        // Aplicar estilos a los selects dinámicos
        $('#tipoFM select').each(function() {
            $(this).addClass('enhanced-select');
        });

        // Agregar labels mejorados si no existen
        if (!$('#tipoFM .enhanced-label').length) {
            // Régimen Fiscal
            const regimenSelect = $('#regimenfiscal');
            if (regimenSelect.length) {
                regimenSelect.before(`
                    <label class="enhanced-label">
                        <i class="fas fa-university field-icon"></i>
                        Régimen Fiscal
                        <span class="required-indicator">*</span>
                    </label>
                `);
                regimenSelect.after(`

                `);
            }

            // Uso CFDI
            const usoCfdiSelect = $('#usocdfi');
            if (usoCfdiSelect.length) {
                usoCfdiSelect.before(`
                    <label class="enhanced-label">
                        <i class="fas fa-file-alt field-icon"></i>
                        Uso de CFDI
                        <span class="required-indicator">*</span>
                    </label>
                `);
                usoCfdiSelect.after(`

                `);
            }
        }
    }

    // Función para restaurar los valores seleccionados en los selects
    function restaurarValoresSeleccionados() {
        // Obtener valores de los campos hidden del formulario Symfony
        var regimenFiscalValue = $("#invoice_clientefacturadatos_regimenfiscal").val();
        var usoCfdiValue = $("#invoice_clientefacturadatos_usocfdi").val();

        // Restaurar valor del régimen fiscal si existe
        if (regimenFiscalValue && regimenFiscalValue !== '') {
            setTimeout(function() {
                $("#regimenfiscal").val(regimenFiscalValue);
                console.log('Régimen fiscal restaurado:', regimenFiscalValue);
            }, 100);
        }

        // Restaurar valor del uso CFDI si existe
        if (usoCfdiValue && usoCfdiValue !== '') {
            setTimeout(function() {
                $("#usocdfi").val(usoCfdiValue);
                console.log('Uso CFDI restaurado:', usoCfdiValue);
            }, 100);
        }
    }

    // Función para mostrar indicador de carga
    function showLoadingIndicator() {
        const loadingHtml = `
            <div id="loading-indicator" class="text-center py-4">
                <div class="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Cargando...</span>
                    </div>
                </div>
                <div class="loading-text mt-3">
                    <p class="text-muted mb-1">
                        <i class="fas fa-cog fa-spin me-2"></i>
                        Cargando opciones de facturación...
                    </p>
                    <small class="text-muted">Preparando régimen fiscal y uso CFDI</small>
                </div>
            </div>
        `;
        $("#tipoFM").html(loadingHtml);
    }

    // Función para ocultar indicador de carga
    function hideLoadingIndicator() {
        $("#loading-indicator").fadeOut(200);
    }

    // Función para mostrar mensajes de validación del folio
    function showFolioValidationMessage(message, type) {
        console.log('showFolioValidationMessage llamada con:', message, type);
        $('#folio-validation-message').remove();

        let iconClass = '';
        let colorClass = '';
        let bgClass = '';

        switch(type) {
            case 'success':
                iconClass = 'fas fa-check-circle';
                colorClass = 'text-success';
                bgClass = 'alert-success';
                break;
            case 'error':
                iconClass = 'fas fa-exclamation-circle';
                colorClass = 'text-danger';
                bgClass = 'alert-danger';
                break;
            case 'info':
                iconClass = 'fas fa-spinner fa-spin';
                colorClass = 'text-info';
                bgClass = 'alert-info';
                break;
        }

        const messageHtml = `
            <div id="folio-validation-message" class="alert ${bgClass} alert-dismissible fade show mt-3" role="alert">
                <i class="${iconClass} me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        $('#message-container').html(messageHtml);
    }

    // Función para mostrar campos adicionales
    function showAdditionalFields() {
        const additionalFields = $('#additional-fields');
        additionalFields.show();
        setTimeout(function() {
            additionalFields.addClass('show');
        }, 50);
        setTimeout(function() {
            $('html, body').animate({
                scrollTop: additionalFields.offset().top - 100
            }, 500);
        }, 300);
    }

    // Función para ocultar campos adicionales
    function hideAdditionalFields() {
        const additionalFields = $('#additional-fields');
        additionalFields.removeClass('show');
        setTimeout(function() {
            additionalFields.hide();
            $('#additional-fields input, #additional-fields select, #additional-fields textarea').val('');
            $('#tipoFM').html('');
        }, 600);
    }

    // Función para validar folio
    function validateFolio(folio, prefijo) {
        $.ajax({
            url: "{{ path('app_invoice_validar_folio', {'e': '__PREFIJO__'}) }}".replace('__PREFIJO__', prefijo),
            type: 'POST',
            data: { folio: folio },
            dataType: 'json',
            success: function(response) {
                console.log('Respuesta del servidor:', response);
                const folioInput = $('#invoice_clientefacturadatos_folio');

                if (response.valid) {
                    folioValidado = true;
                    folioInput.addClass('is-valid').removeClass('is-invalid');
                    showFolioValidationMessage(response.message, 'success');
                    showAdditionalFields();

                    // Disparar evento personalizado para el folio válido
                    $(document).trigger('folioValidated', [true]);
                } else {
                    folioValidado = false;
                    folioInput.addClass('is-invalid').removeClass('is-valid');
                    console.log('Mostrando mensaje de error:', response.message);
                    showFolioValidationMessage(response.message, 'error');
                    hideAdditionalFields();

                    // Disparar evento personalizado para el folio inválido
                    $(document).trigger('folioValidated', [false]);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error en validación de folio:', error);
                folioValidado = false;
                $('#invoice_clientefacturadatos_folio').addClass('is-invalid').removeClass('is-valid');
                showFolioValidationMessage('Error al validar el folio. Por favor, intenta nuevamente.', 'error');
                hideAdditionalFields();
            }
        });
    }

    // Función para enviar el formulario por AJAX
    function submitFormAjax() {
        const form = $('#formularioClienteFactura');
        const formData = new FormData(form[0]);
        const prefijo = '{{ prefijo }}';

        // Mostrar indicador de carga
        showSubmitLoadingIndicator();

        // Deshabilitar el botón de envío
        const submitButton = form.find('button[type="submit"], input[type="submit"]');
        submitButton.prop('disabled', true);

        $.ajax({
            url: "{{ path('app_invoice_formulario_empresa', {'e': prefijo}) }}",
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'html',
            success: function(html) {
                console.log('Respuesta del servidor recibida');
                hideSubmitLoadingIndicator();
                submitButton.prop('disabled', false);

                // Actualizar el formulario con la nueva respuesta HTML
                $("#formulario").html(html);
            },
            error: function(xhr, status, error) {
                hideSubmitLoadingIndicator();
                submitButton.prop('disabled', false);

                let errorMessage = 'Error al procesar la solicitud.';

                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    if (errorResponse.message) {
                        errorMessage = errorResponse.message;
                    }
                } catch (e) {
                    if (xhr.status === 500) {
                        errorMessage = 'Error interno del servidor. Por favor, intenta nuevamente.';
                    } else if (xhr.status === 422) {
                        errorMessage = 'Datos del formulario no válidos. Verifica la información ingresada.';
                    }
                }

                Swal.fire({
                    title: 'Error',
                    text: errorMessage,
                    icon: 'error',
                    confirmButtonText: 'Aceptar'
                });
            }
        });
    }

    // Función para mostrar indicador de carga durante el envío
    function showSubmitLoadingIndicator() {
        const loadingOverlay = `
            <div id="submit-loading-overlay" class="submit-loading-overlay">
                <div class="submit-loading-content">
                    <div class="submit-loading-spinner">
                        <div class="spinner-border text-light" role="status">
                            <span class="visually-hidden">Procesando...</span>
                        </div>
                    </div>
                    <div class="submit-loading-text">
                        <h5 class="text-white mb-2">
                            <i class="fas fa-file-invoice me-2"></i>
                            Generando factura...
                        </h5>
                        <p class="text-white-50 mb-0">
                            Por favor espera mientras procesamos tu solicitud
                        </p>
                    </div>
                </div>
            </div>
        `;

        $('body').append(loadingOverlay);
        $('#submit-loading-overlay').fadeIn(300);
    }

    // Función para ocultar indicador de carga durante el envío
    function hideSubmitLoadingIndicator() {
        $('#submit-loading-overlay').fadeOut(300, function() {
            $(this).remove();
        });
    }

    // Funciones para mejorar la experiencia del campo folio
    function initFolioEnhancements() {
        const folioContainer = $('#folio-container');
        const folioInput = $('#invoice_clientefacturadatos_folio');

        // Agregar efecto de pulso si el campo está vacío
        function checkFolioEmpty() {
            if (!folioInput.val() || folioInput.val().trim() === '') {
                folioContainer.addClass('pulse');
            } else {
                folioContainer.removeClass('pulse');
            }
        }

        // Verificar al cargar la página
        checkFolioEmpty();

        // Verificar cuando el usuario escribe
        folioInput.on('input', function() {
            checkFolioEmpty();

            // Agregar efecto visual cuando el usuario empieza a escribir
            if ($(this).val().length > 0) {
                folioContainer.removeClass('pulse');
                $('.folio-input-wrapper').addClass('active');
            } else {
                $('.folio-input-wrapper').removeClass('active');
            }
        });

        // Efectos de focus
        folioInput.on('focus', function() {
            folioContainer.removeClass('pulse');
            $('.folio-title').html('<i class="fas fa-edit folio-icon"></i> ¡Perfecto! Escribe tu folio');
            $('.folio-subtitle').text('Estamos listos para procesar tu factura');
        });

        // Efectos de blur
        folioInput.on('blur', function() {
            if (!$(this).val() || $(this).val().trim() === '') {
                $('.folio-title').html('<i class="fas fa-receipt folio-icon"></i> ¡Comencemos con tu factura!');
                $('.folio-subtitle').text('Ingresa el folio de tu venta para continuar');
                setTimeout(() => {
                    folioContainer.addClass('pulse');
                }, 1000);
            }
        });

        // Animación de éxito cuando el folio es válido
        $(document).on('folioValidated', function(e, isValid) {
            if (isValid) {
                folioContainer.removeClass('pulse');
                $('.folio-title').html('<i class="fas fa-check-circle folio-icon text-success"></i> ¡Folio válido!');
                $('.folio-subtitle').text('Ahora completa los datos adicionales');
                $('.folio-steps small').html('<i class="fas fa-check me-1"></i> Paso 1 completado - Continúa con los datos personales');

                // Actualizar indicador de progreso y mostrar paso 2
                $('#step-1').removeClass('active').addClass('completed');
                $('#line-1').addClass('completed');
                $('#step-2').addClass('active');

                // Mostrar automáticamente el paso 2 después de un breve delay
                setTimeout(() => {
                    currentStep = 2;
                    showStep(2);
                }, 1500);

                // Efecto de éxito
                folioContainer.css('background', 'linear-gradient(135deg, #48bb78 0%, #38a169 100%)');
                setTimeout(() => {
                    folioContainer.css('background', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)');
                }, 2000);
            } else {
                $('.folio-title').html('<i class="fas fa-exclamation-triangle folio-icon text-warning"></i> Folio no encontrado');
                $('.folio-subtitle').text('Verifica el número e intenta nuevamente');
                $('.folio-steps small').html('<i class="fas fa-times me-1"></i> Paso 1 de 4: Verifica el folio e intenta nuevamente');

                // Resetear indicador de progreso
                $('#step-1').removeClass('completed').addClass('active');
                $('#line-1').removeClass('completed');
                $('#step-2').removeClass('active');
            }
        });
    }

    // Funciones para navegación entre pasos
    let currentStep = 2; // Empezamos en el paso 2 (datos personales)

    function nextStep(step) {
        if (validateCurrentStep(step)) {
            hideCurrentStep();
            currentStep = step + 1;
            showStep(currentStep);
            updateProgressIndicator();
        }
    }

    function previousStep(step) {
        hideCurrentStep();
        currentStep = step - 1;
        if (currentStep < 1) currentStep = 1;
        showStep(currentStep);
        updateProgressIndicator();
    }

    function showStep(stepNumber) {
        $('.step-container').removeClass('active').hide();
        $(`#step-${stepNumber}-container`).addClass('active').show();

        // Scroll suave hacia el paso
        $('html, body').animate({
            scrollTop: $(`#step-${stepNumber}-container`).offset().top - 100
        }, 500);
    }

    function hideCurrentStep() {
        $('.step-container').removeClass('active').hide();
    }

    function updateProgressIndicator() {
        // Resetear todos los pasos
        $('.progress-step').removeClass('active completed');
        $('.progress-line').removeClass('completed');

        // Marcar pasos completados
        for (let i = 1; i < currentStep; i++) {
            $(`#step-${i}`).addClass('completed');
            if (i < 4) {
                $(`#line-${i}`).addClass('completed');
            }
        }

        // Marcar paso actual
        $(`#step-${currentStep}`).addClass('active');
    }

    function validateCurrentStep(step) {
        let isValid = true;
        let errorMessage = '';

        switch(step) {
            case 2: // Datos Personales
                const email = $('#invoice_clientefacturadatos_email').val();
                const razonsocial = $('#invoice_clientefacturadatos_razonsocial').val();
                const codigopostal = $('#invoice_clientefacturadatos_codigopostal').val();

                if (!email || email.trim() === '') {
                    errorMessage = 'El correo electrónico es requerido.';
                    isValid = false;
                } else if (!razonsocial || razonsocial.trim() === '') {
                    errorMessage = 'El nombre completo o razón social es requerido.';
                    isValid = false;
                } else if (!codigopostal || codigopostal.trim() === '') {
                    errorMessage = 'El código postal es requerido.';
                    isValid = false;
                }
                break;

            case 3: // Datos Fiscales
                const rfc = $('#invoice_clientefacturadatos_rfc').val();
                const tipoPersona = $('#invoice_clientefacturadatos_tipoPersona').val();
                const regimenFiscal = $('select[name="regimenFiscal"]').val();
                const usocfdiMoral = $('select[name="usocfdiMoral"]').val();

                if (!rfc || rfc.trim() === '') {
                    errorMessage = 'El RFC es requerido.';
                    isValid = false;
                } else if (!tipoPersona || tipoPersona === '') {
                    errorMessage = 'Debe seleccionar el tipo de persona.';
                    isValid = false;
                } else if (!regimenFiscal || regimenFiscal === 'Selecciona una opción' || regimenFiscal === '') {
                    errorMessage = 'Debe seleccionar un régimen fiscal válido.';
                    isValid = false;
                } else if (!usocfdiMoral || usocfdiMoral === 'Selecciona una opción' || usocfdiMoral === '') {
                    errorMessage = 'Debe seleccionar un uso de CFDI válido.';
                    isValid = false;
                }
                break;
        }

        if (!isValid) {
            Swal.fire({
                title: 'Campos requeridos',
                text: errorMessage,
                icon: 'warning',
                confirmButtonText: 'Entendido'
            });
        }

        return isValid;
    }

    // Función para inicializar el aviso importante
    function initImportantNotice() {
        const notice = $('.enhanced-notice');

        // Agregar efecto de entrada
        notice.css('opacity', '0').css('transform', 'translateY(30px)');

        setTimeout(() => {
            notice.animate({
                opacity: 1
            }, 600).css('transform', 'translateY(0)');
        }, 500);

        // Efecto de click en el aviso para mostrar más información
        notice.on('click', function() {
            Swal.fire({
                title: '💳 Verificación de Pago',
                html: `
                    <div style="text-align: left; color: #4a5568;">
                        <p><strong>¿Por qué es necesario que la venta esté liquidada?</strong></p>
                        <ul style="margin: 15px 0; padding-left: 20px;">
                            <li>Es un requisito fiscal obligatorio</li>
                            <li>Garantiza la validez legal de la factura</li>
                            <li>Evita problemas con el SAT</li>
                        </ul>

                        <p><strong>¿Cómo verificar que mi pago está registrado?</strong></p>
                        <ul style="margin: 15px 0; padding-left: 20px;">
                            <li>Revisa tu ticket de compra</li>
                            <li>Confirma que aparece como "PAGADO"</li>
                            <li>Si pagaste hace poco, espera unos minutos</li>
                        </ul>

                        <div style="background: #f7fafc; padding: 15px; border-radius: 8px; margin-top: 15px;">
                            <p style="margin: 0; font-size: 0.9rem; color: #718096;">
                                <i class="fas fa-phone" style="color: #667eea;"></i>
                                <strong>¿Necesitas ayuda?</strong><br>
                                Contacta a nuestro equipo de soporte para verificar el estado de tu pago.
                            </p>
                        </div>
                    </div>
                `,
                icon: 'info',
                confirmButtonText: 'Entendido',
                confirmButtonColor: '#667eea',
                width: '500px'
            });
        });

        // Agregar cursor pointer para indicar que es clickeable
        notice.css('cursor', 'pointer');

        // Tooltip para indicar que es clickeable
        notice.attr('title', 'Haz clic para más información sobre la verificación de pago');
    }

    // Inicializar mejoras del folio cuando el documento esté listo
    $(document).ready(function() {
        initFolioEnhancements();
        initImportantNotice();

        // Inicializar navegación de pasos
        updateProgressIndicator();
    });


</script>

{% if app.session.flashBag.has('notice') %}

    {% for flash_message in app.session.flashbag.get('notice') %}
        <div class="flash-notice">
            {{ flash_message }}
            <button type="button" id="aceptar" onclick='formulario()'>Aceptar</button>
        </div>
    {% endfor %}

{% else %}

    {% if exito != true %}
        {% if msj != "" %}
            <script>
                // Mostrar mensaje de error en el contenedor
                $(document).ready(function() {
                    showFolioValidationMessage('{{ msj|escape('js') }}', 'error');
                    showAdditionalFields();

                    // Cargar los selects dinámicos manteniendo los valores seleccionados
                    cargarSelectsDinamicos();
                });

            </script>
        {% endif %}
    {% else %}
        {% if formularioGuardado %}
            <script>
                $(document).ready(function() {
                    showFolioValidationMessage('Tus datos se han mandado correctamente y se enviarán por correo en 24 horas. Nota: recuerda revisar la carpeta de spam', 'success');
                    // Limpiar formulario después de éxito
                    setTimeout(function() {
                        location.reload();
                    }, 3000);
                });
            </script>
        {% endif %}
    {% endif %}

    <!-- Important Notice about Payment -->
    <!-- Enhanced Important Notice -->
    <div class="enhanced-notice d-none">
        <div class="notice-header">
            <div class="notice-icon-wrapper">
                <i class="fas fa-exclamation-triangle notice-icon"></i>
            </div>
            <p class="notice-title">
                <i class="fas fa-shield-alt me-2"></i>
                ¡INFORMACIÓN IMPORTANTE!
            </p>
        </div>

        <div class="notice-content">
            <div class="notice-main-text">
                <i class="fas fa-credit-card me-2"></i>
                <strong>Tu venta debe estar completamente liquidada</strong> para poder generar la factura.
            </div>


        </div>
    </div>

    {{ form_start(form, {attr: {'id': 'formularioClienteFactura', 'enctype': 'multipart/form-data', 'onsubmit': 'handleFormSubmit(event)', 'class': 'col-12'}, 'action': path('app_invoice_formulario_empresa') }) }} {{ form_errors(form) }}
    <!-- Container for validation messages -->
    <div id="message-container"></div>

    <!-- Enhanced Folio field -->
    <div class="col-12">
        <div class="folio-container" id="folio-container">
            <!-- Progress Indicator -->
            <div class="progress-indicator">
                <div class="progress-step active" id="step-1">
                    <i class="fas fa-receipt"></i>
                </div>
                <div class="progress-line" id="line-1"></div>
                <div class="progress-step" id="step-2">
                    <i class="fas fa-user"></i>
                </div>
                <div class="progress-line" id="line-2"></div>
                <div class="progress-step" id="step-3">
                    <i class="fas fa-id-card"></i>
                </div>
                <div class="progress-line" id="line-3"></div>
                <div class="progress-step" id="step-4">
                    <i class="fas fa-file-invoice"></i>
                </div>
            </div>

            <div class="folio-header">
                <h2 class="folio-title">
                    <i class="fas fa-receipt folio-icon"></i>
                    ¡Comencemos con tu factura!
                </h2>
                <p class="folio-subtitle">
                    Ingresa el folio de tu venta para continuar
                </p>
                <div class="folio-steps mt-3">
                    <small class="text-white-50">
                        <i class="fas fa-arrow-right me-1"></i>
                        Paso 1 de 4: Verificación del folio
                    </small>
                </div>
            </div>

            <div class="folio-input-group">
                {{ form_label(form.folio, 'Folio de Venta', {'label_attr': {'class': 'folio-label'}}) }}
                <div class="folio-input-wrapper">
                    <span class="folio-prefix">{{ prefijo|upper }}</span>
                    {{ form_widget(form.folio, {
                        'attr': {
                            'class': 'folio-input',
                            'placeholder': '#####',
                            'autocomplete': 'off'
                        }
                    }) }}
                </div>
                <div class="folio-help-text">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-lightbulb me-2"></i>
                        <span>Encuentra el folio en tu ticket de compra</span>
                    </div>
                    {% if form.folio.vars.help %}
                        <div class="mt-2">
                            <i class="fas fa-info-circle me-1"></i>
                            {{ form_help(form.folio) }}
                        </div>
                    {% endif %}

                </div>
            </div>
        </div>
    </div>

    <!-- Campos adicionales del formulario organizados por pasos -->
    <div class="col-12">
        <div id="additional-fields" style="display: none;" class="col-12">

            <!-- PASO 2: Datos Personales -->
            <div class="step-container active" id="step-2-container">
                <div class="step-header">
                    <h3 class="step-title">
                        <i class="fas fa-user"></i>
                        Paso 2: Datos Personales
                    </h3>
                    <p class="step-subtitle">
                        Ingresa tu información personal para la factura
                    </p>
                </div>

                <div class="step-content">
                    <!-- Email field -->
                    <div class="field-group">
                        <label class="enhanced-label">
                            <i class="fas fa-envelope field-icon"></i>
                            Correo Electrónico
                            <span class="required-indicator">*</span>
                        </label>
                        {{ form_widget(form.email, {'attr': {'class': 'enhanced-input', 'placeholder': '<EMAIL>'}}) }}
                        <div class="field-help">
                            <i class="fas fa-info-circle"></i>
                            Aquí recibirás tu factura electrónica
                        </div>
                    </div>

                    <!-- Razón social field -->
                    <div class="field-group">
                        <label class="enhanced-label">
                            <i class="fas fa-building field-icon"></i>
                            Nombre Completo o Razón Social
                            <span class="required-indicator">*</span>
                        </label>
                        {{ form_widget(form.razonsocial, {'attr': {'class': 'enhanced-input', 'placeholder': 'Nombre completo o razón social'}}) }}

                    </div>

                    <!-- Código postal field -->
                    <div class="field-group">
                        <label class="enhanced-label">
                            <i class="fas fa-map-marker-alt field-icon"></i>
                            Código Postal
                            <span class="required-indicator">*</span>
                        </label>
                        {{ form_widget(form.codigopostal, {'attr': {'class': 'enhanced-input', 'placeholder': '12345'}}) }}

                    </div>

                    <div class="step-navigation">
                        <button type="button" class="step-btn" onclick="previousStep(2)">
                            <i class="fas fa-arrow-left"></i>
                            Anterior
                        </button>
                        <button type="button" class="step-btn" onclick="nextStep(2)">
                            Siguiente
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- PASO 3: Datos Fiscales -->
            <div class="step-container" id="step-3-container">
                <div class="step-header">
                    <h3 class="step-title">
                        <i class="fas fa-id-card"></i>
                        Paso 3: Datos Fiscales
                    </h3>
                    <p class="step-subtitle">
                        Información fiscal requerida para tu factura
                    </p>
                </div>

                <div class="step-content">
                    <!-- RFC field -->
                    <div class="field-group">
                        <label class="enhanced-label">
                            <i class="fas fa-id-badge field-icon"></i>
                            RFC (Registro Federal de Contribuyentes)
                            <span class="required-indicator">*</span>
                        </label>
                        {{ form_widget(form.rfc, {'attr': {'class': 'enhanced-input', 'placeholder': 'ABCD123456EF7'}}) }}

                    </div>

                    <!-- Tipo de persona field -->
                    <div class="field-group">
                        <label class="enhanced-label">
                            <i class="fas fa-users field-icon"></i>
                            Tipo de Persona
                            <span class="required-indicator">*</span>
                        </label>
                        {{ form_widget(form.tipoPersona, {'attr': {'class': 'enhanced-select'}}) }}

                    </div>

                    <!-- Dynamic content for fiscal regime and CFDI use -->
                    <div id="tipoFM" class="field-group">
                        <!-- Content loaded dynamically via AJAX -->
                    </div>

                    <!-- Comentarios adicionales field -->
                    <div class="field-group">
                        <label class="enhanced-label">
                            <i class="fas fa-comment field-icon"></i>
                            Comentarios Adicionales
                        </label>
                        {{ form_widget(form.notas, {'attr': {'class': 'enhanced-textarea', 'placeholder': 'Información adicional (opcional)'}}) }}

                    </div>

                    <div class="step-navigation">
                        <button type="button" class="step-btn" onclick="previousStep(3)">
                            <i class="fas fa-arrow-left"></i>
                            Anterior
                        </button>
                        <button type="button" class="step-btn" onclick="nextStep(3)">
                            Siguiente
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- PASO 4: Generar Factura -->
            <div class="step-container" id="step-4-container">
                <div class="step-header">
                    <h3 class="step-title">
                        <i class="fas fa-file-invoice"></i>
                        Paso 4: Generar Factura
                    </h3>
                    <p class="step-subtitle">
                        ¡Todo listo! Genera tu factura electrónica
                    </p>
                </div>

                <div class="step-content">
                    <div class="text-center">
                        <div class="mb-4">
                            <i class="fas fa-check-circle text-success" style="font-size: 4rem; color: #48bb78;"></i>
                        </div>
                        <h4 style="color: #2d3748; margin-bottom: 20px;">
                            Información Completa
                        </h4>
                        <p style="color: #718096; margin-bottom: 30px;">
                            Hemos verificado todos tus datos. Haz clic en el botón para generar tu factura electrónica.
                        </p>
<div style="display: flex; justify-content: center;">
    {{ form_widget(form.Enviar, {
        'attr': {
            'class': 'step-btn btn-success',
            'style': 'font-size: 1.2rem; padding: 20px 40px;'
        },
        'label': "Generar Factura"
    }) }}
</div>
                        <div class="mt-3">
                            <small style="color: #718096;">
                                <i class="fas fa-shield-alt"></i>
                                Tu información está protegida y será procesada de forma segura
                            </small>
                        </div>
                    </div>

                    <div class="step-navigation">
                        <button type="button" class="step-btn" onclick="previousStep(4)">
                            <i class="fas fa-arrow-left"></i>
                            Anterior
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{ form_rest(form) }}
    {{ form_end(form, {'render_rest': false}) }}





{% endif %}