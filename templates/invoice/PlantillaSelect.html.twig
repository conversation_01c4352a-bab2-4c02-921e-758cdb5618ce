<!-- Responsive fiscal regime and CFDI use fields -->
<div class="row">
    <!-- Régimen Fiscal field -->
    <div class="col-12 col-md-12 mb-3 mb-md-0">
        <label class="form-label required" for="regimenfiscal">Régimen Fiscal</label>
        <select name="regimenFiscal" class="form-control w-100" onchange="asignarValorRegimenFiscal(this.value)" id="regimenfiscal">
            {% for key, value in regimenFiscal %}
                <option value="{{ key }}">{{ value }}</option>
            {% endfor %}
        </select>
    </div>

    <!-- Uso CFDI field -->
    <div class="col-12 col-md-12">
        <label class="form-label required" for="usocdfi">Uso CFDI</label>
        <select name="usocfdiMoral" class="form-control w-100" onchange="asignarValorUsoCfdi(this.value)" id="usocdfi">
            {% for key, value in opcionesUsoCfdi %}
                <option value="{{ key }}">{{ value }}</option>
            {% endfor %}
        </select>
    </div>
</div>

<script>

var regimenFiscalvalues = Object.values({{ regimenFiscal|json_encode|raw }});
var regimenFiscalkeys = Object.keys({{ regimenFiscal|json_encode|raw }});

var selectUsoCDFI = document.getElementById('usocdfi');
var selectRF = document.getElementById('regimenfiscal');

function TwoFunction(value){
  asignarValorUsoCfdi(value);
  ChangeRF();
}


function ChangeRF(){

  check = $("#usocdfi").val();

  console.log(check);
  if(check == "D01"){
    $("#regimenfiscal").empty();

    for (var i = 0; i < regimenFiscalvalues.length; i++) {
      var opt = document.createElement('option');
      opt.value = regimenFiscalkeys[i];
      opt.innerHTML = regimenFiscalvalues[i];
      selectRF.appendChild(opt);

    }

  }
  else{
    $("#regimenfiscal").empty();

    for (var i = 0; i < regimenFiscalvalues.length; i++) {
      var opt = document.createElement('option');
      opt.value = regimenFiscalkeys[i];
      opt.innerHTML = regimenFiscalvalues[i];
      //if ( regimenFiscalkeys[i] != "605") selectRF.appendChild(opt);

    }
  }

  check = $("#regimenfiscal").val();

  console.log(check);

  asignarValorRegimenFiscal(check)

}

</script>

