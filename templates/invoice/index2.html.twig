{% extends 'layoutFactura.html.twig' %} 


{% block stylesheets %}
    <style>
        /* Mobile-first responsive improvements for invoice form */
        @media (max-width: 767.98px) {
            .card-body {
                padding: 1rem !important;
            }

            /* Ensure all form controls take full width on mobile */
            .form-control, .form-select {
                font-size: 16px !important; /* Prevents zoom on iOS */
                padding: 0.75rem !important;
                width: 100% !important;
                max-width: 100% !important;
            }

            .form-label {
                font-weight: 600;
                margin-bottom: 0.5rem;
                font-size: 0.9rem;
                width: 100%;
                display: block;
            }

            /* Remove any row constraints that might limit width */
            .mb-3 {
                margin-bottom: 1.5rem !important;
                width: 100% !important;
            }

            /* Ensure flex containers don't constrain width */
            .d-flex {
                width: 100% !important;
            }

            .flex-grow-1 {
                flex-grow: 1 !important;
                width: auto !important;
                min-width: 0 !important;
            }

            /* Improve button spacing on mobile */
            .btn {
                padding: 0.75rem 1.5rem;
                font-size: 1rem;
                width: 100%;
                margin-top: 1rem;
                max-width: 300px; /* Limit button width on mobile */
            }

            /* Ensure textarea takes full width */
            textarea.form-control {
                width: 100% !important;
                min-height: 100px;
                resize: vertical;
            }

            /* Better spacing for error messages */
            .form-error {
                margin-bottom: 1rem;
                width: 100%;
            }

            /* Improve select dropdown appearance on mobile */
            select.form-control, select.form-select {
                background-size: 16px 12px;
                padding-right: 2.5rem;
            }

            /* Ensure prefix text doesn't take too much space */
            .me-2 {
                margin-right: 0.5rem !important;
                flex-shrink: 0;
                white-space: nowrap;
            }
        }

        /* Tablet improvements */
        @media (min-width: 768px) and (max-width: 991.98px) {
            .card-body {
                padding: 1.5rem !important;
            }
        }

        /* General improvements for all screen sizes */
        .form-label.required::after {
            content: " *";
            color: #dc3545;
        }

        .text-primary {
            color: #e91e63 !important;
        }

        /* Improve focus states */
        .form-control:focus, .form-select:focus {
            border-color: #e91e63;
            box-shadow: 0 0 0 0.2rem rgba(233, 30, 99, 0.25);
        }

        /* Ensure full width for all form elements */
        .form-control, .form-select {
            width: 100% !important;
            box-sizing: border-box;
        }

        /* Override any Bootstrap constraints */
        .w-100 {
            width: 100% !important;
        }

        /* Ensure containers don't constrain form elements */
        #formularioClienteFactura {
            width: 100%;
        }

        #formularioClienteFactura .mb-3,
        #formularioClienteFactura .form-group {
            width: 100%;
        }

        /* Button styling improvements */
        .btn-primary {
            background: linear-gradient(45deg, #e91e63, #f06292);
            border: none;
            border-radius: 25px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(45deg, #c2185b, #e91e63);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
        }

        .btn-primary:focus {
            box-shadow: 0 0 0 0.2rem rgba(233, 30, 99, 0.5);
        }

        /* Textarea improvements */
        textarea.form-control {
            border-radius: 8px;
            border: 2px solid #e0e0e0;
            transition: border-color 0.3s ease;
        }

        textarea.form-control:focus {
            border-color: #e91e63;
            box-shadow: 0 0 0 0.2rem rgba(233, 30, 99, 0.25);
        }

        /* Center button container */
        .text-center {
            text-align: center !important;
        }

        /* Important Notice Styles */
        .important-notice {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            border: none;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            animation: noticeGlow 2s ease-in-out infinite alternate;
            position: relative;
            overflow: hidden;
        }

        .important-notice::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 3s infinite;
        }

        .notice-content {
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
        }

        .notice-icon {
            font-size: 2.5rem;
            margin-right: 15px;
            animation: bounce 2s infinite;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        .notice-text {
            flex: 1;
        }

        .notice-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .notice-description {
            font-size: 1rem;
            font-weight: 500;
            margin: 0;
            opacity: 0.95;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        @keyframes noticeGlow {
            0% {
                box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            }
            100% {
                box-shadow: 0 8px 35px rgba(255, 107, 107, 0.5);
            }
        }

        @keyframes shimmer {
            0% {
                left: -100%;
            }
            100% {
                left: 100%;
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        /* Mobile responsiveness for notice */
        @media (max-width: 768px) {
            .important-notice {
                padding: 15px;
                margin-bottom: 20px;
                border-radius: 12px;
            }

            .notice-content {
                flex-direction: column;
                text-align: center;
            }

            .notice-icon {
                font-size: 2rem;
                margin-right: 0;
                margin-bottom: 10px;
            }

            .notice-title {
                font-size: 1.2rem;
            }

            .notice-description {
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .important-notice {
                padding: 12px;
                margin-bottom: 15px;
            }

            .notice-icon {
                font-size: 1.8rem;
            }

            .notice-title {
                font-size: 1.1rem;
            }

            .notice-description {
                font-size: 0.85rem;
            }
        }

        /* Validation styles */
        .form-control.is-valid {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        .form-control.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        #folio-validation-message {
            font-size: 0.9rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        #folio-validation-message i {
            font-size: 1rem;
        }

        .text-success {
            color: #28a745 !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        .text-info {
            color: #17a2b8 !important;
        }

        /* Animation for validation messages */
        #folio-validation-message {
            animation: fadeInUp 0.3s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Loading spinner animation */
        .fa-spinner.fa-spin {
            animation: fa-spin 1s infinite linear;
        }

        @keyframes fa-spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        /* Additional fields container */
        #additional-fields {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s ease;
        }

        #additional-fields.show {
            opacity: 1;
            transform: translateY(0);
        }

        /* Progressive field animation */
        #additional-fields .col-12,
        #additional-fields .row {
            opacity: 0;
            transform: translateY(15px);
            transition: all 0.4s ease;
        }

        #additional-fields.show .col-12,
        #additional-fields.show .row {
            opacity: 1;
            transform: translateY(0);
        }

        /* Staggered animation delay for each field */
        #additional-fields .col-12:nth-child(1) { transition-delay: 0.1s; }
        #additional-fields .col-12:nth-child(2) { transition-delay: 0.2s; }
        #additional-fields .row:nth-child(3) { transition-delay: 0.3s; }
        #additional-fields .col-12:nth-child(4) { transition-delay: 0.4s; }
        #additional-fields .col-12:nth-child(5) { transition-delay: 0.5s; }
        #additional-fields .col-12:nth-child(6) { transition-delay: 0.6s; }
        #additional-fields .col-12:nth-child(7) { transition-delay: 0.7s; }

        /* Success message enhancement */
        .folio-success-indicator {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            animation: successPulse 2s ease-in-out infinite alternate;
        }

        @keyframes successPulse {
            0% {
                box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            }
            100% {
                box-shadow: 0 4px 20px rgba(40, 167, 69, 0.5);
            }
        }

        /* Validation message styles */
        #folio-validation-message {
            border-radius: 10px;
            border: none;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            animation: slideInDown 0.4s ease-out;
            position: relative;
            z-index: 10;
        }

        #folio-validation-message.alert-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            border: 2px solid #b21e2d;
        }

        #folio-validation-message.alert-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: 2px solid #1e7e34;
        }

        #folio-validation-message.alert-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            border: 2px solid #117a8b;
        }

        #folio-validation-message .btn-close {
            filter: brightness(0) invert(1);
            opacity: 0.8;
        }

        #folio-validation-message .btn-close:hover {
            opacity: 1;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Ensure message is always visible */
        #folio-validation-message {
            display: block !important;
            visibility: visible !important;
        }

        /* Loading indicator styles */
        #loading-indicator {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px dashed #dee2e6;
            border-radius: 15px;
            padding: 30px 20px;
            margin: 20px 0;
            animation: loadingPulse 2s ease-in-out infinite alternate;
        }

        .loading-spinner .spinner-border {
            width: 3rem;
            height: 3rem;
            border-width: 0.3em;
            animation: spin 1s linear infinite;
        }

        .loading-text {
            color: #6c757d;
        }

        .loading-text p {
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .loading-text small {
            font-size: 0.875rem;
            opacity: 0.8;
        }

        .loading-text .fa-cog {
            color: #007bff;
        }

        @keyframes loadingPulse {
            0% {
                background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                border-color: #dee2e6;
            }
            100% {
                background: linear-gradient(135deg, #e9ecef, #f8f9fa);
                border-color: #ced4da;
            }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive loading indicator */
        @media (max-width: 768px) {
            #loading-indicator {
                padding: 20px 15px;
                margin: 15px 0;
            }

            .loading-spinner .spinner-border {
                width: 2.5rem;
                height: 2.5rem;
            }

            .loading-text p {
                font-size: 0.9rem;
            }

            .loading-text small {
                font-size: 0.8rem;
            }
        }

        /* Smooth transitions for content changes */
        #tipoFM {
            transition: all 0.3s ease;
        }

        /* Loading dots animation */
        .loading-dots::after {
            content: '';
            animation: loadingDots 1.5s infinite;
        }

        @keyframes loadingDots {
            0%, 20% {
                content: '.';
            }
            40% {
                content: '..';
            }
            60%, 100% {
                content: '...';
            }
        }

        /* Submit loading overlay styles */
        .submit-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            z-index: 9999;
            display: none;
            justify-content: center;
            align-items: center;
        }

        .submit-loading-content {
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .submit-loading-spinner .spinner-border {
            width: 4rem;
            height: 4rem;
            border-width: 0.4em;
            margin-bottom: 20px;
        }

        .submit-loading-text h5 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .submit-loading-text p {
            font-size: 1rem;
            opacity: 0.9;
        }

        /* Responsive submit loading */
        @media (max-width: 768px) {
            .submit-loading-content {
                padding: 30px 20px;
                margin: 20px;
            }

            .submit-loading-spinner .spinner-border {
                width: 3rem;
                height: 3rem;
            }

            .submit-loading-text h5 {
                font-size: 1.3rem;
            }

            .submit-loading-text p {
                font-size: 0.9rem;
            }
        }
        .required {
            padding: 0;
        }

    </style>
    <style>
        /* Loading indicator styles */
        #loading-indicator {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px dashed #dee2e6;
            border-radius: 15px;
            padding: 30px 20px;
            margin: 20px 0;
            animation: loadingPulse 2s ease-in-out infinite alternate;
        }

        .loading-spinner .spinner-border {
            width: 3rem;
            height: 3rem;
            border-width: 0.3em;
            animation: spin 1s linear infinite;
        }

        .loading-text {
            color: #6c757d;
        }

        .loading-text p {
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .loading-text small {
            font-size: 0.875rem;
            opacity: 0.8;
        }

        .loading-text .fa-cog {
            color: #007bff;
        }

        @keyframes loadingPulse {
            0% {
                background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                border-color: #dee2e6;
            }
            100% {
                background: linear-gradient(135deg, #e9ecef, #f8f9fa);
                border-color: #ced4da;
            }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Submit loading overlay styles */
        .submit-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            z-index: 9999;
            display: none;
            justify-content: center;
            align-items: center;
        }

        .submit-loading-content {
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .submit-loading-spinner .spinner-border {
            width: 4rem;
            height: 4rem;
            border-width: 0.4em;
            margin-bottom: 20px;
        }

        /* Responsive loading indicator */
        @media (max-width: 768px) {
            #loading-indicator {
                padding: 20px 15px;
                margin: 15px 0;
            }

            .loading-spinner .spinner-border {
                width: 2.5rem;
                height: 2.5rem;
            }

            .loading-text p {
                font-size: 0.9rem;
            }

            .loading-text small {
                font-size: 0.8rem;
            }
        }

        /* Smooth transitions for content changes */
        #tipoFM {
            transition: all 0.3s ease;
        }

        /* Additional fields animation */
        #additional-fields {
            transition: all 0.4s ease;
        }

        #additional-fields.show {
            opacity: 1;
            transform: translateY(0);
        }

        /* Folio field enhanced styles */
        .folio-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
            animation: folioGlow 3s ease-in-out infinite alternate;
        }

        .folio-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: folioShine 4s ease-in-out infinite;
        }

        .folio-header {
            text-align: center;
            margin-bottom: 25px;
            position: relative;
            z-index: 2;
        }

        .folio-title {
            color: white;
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .folio-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1rem;
            font-weight: 400;
            margin-bottom: 0;
        }

        .folio-input-group {
            position: relative;
            z-index: 2;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .folio-label {
            color: #4a5568;
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 15px;
            display: block;
        }

        .folio-input-wrapper {
            display: flex;
            align-items: center;
            background: white;
            border: 3px solid #e2e8f0;
            border-radius: 12px;
            padding: 5px;
            transition: all 0.3s ease;
            position: relative;
        }

        .folio-input-wrapper:focus-within {
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .folio-prefix {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            font-weight: 700;
            font-size: 1.1rem;
            margin-right: 15px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            min-width: 80px;
            text-align: center;
        }

        .folio-input {
            border: none !important;
            outline: none !important;
            padding: 15px 20px !important;
            font-size: 1.2rem !important;
            font-weight: 600 !important;
            color: #2d3748 !important;
            background: transparent !important;
            flex: 1;
            box-shadow: none !important;
        }

        .folio-input::placeholder {
            color: #a0aec0 !important;
            font-weight: 400 !important;
        }

        .folio-help-text {
            color: #000;
            font-size: 0.9rem;
            text-align: center;
            margin-top: 15px;
            position: relative;
            z-index: 2;
        }

        .folio-icon {
            font-size: 2rem;
            animation: folioIconBounce 2s ease-in-out infinite;
        }

        @keyframes folioGlow {
            0% {
                box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
            }
            100% {
                box-shadow: 0 20px 45px rgba(102, 126, 234, 0.5);
            }
        }

        @keyframes folioShine {
            0% {
                transform: translateX(-100%) translateY(-100%) rotate(45deg);
            }
            50% {
                transform: translateX(100%) translateY(100%) rotate(45deg);
            }
            100% {
                transform: translateX(-100%) translateY(-100%) rotate(45deg);
            }
        }

        @keyframes folioIconBounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        /* Responsive design for folio */
        @media (max-width: 768px) {
            .folio-container {

                margin-bottom: 20px;
            }

            .folio-title {
                font-size: 1.5rem;
            }

            .folio-input-wrapper {
                flex-direction: column;
                gap: 10px;
            }

            .folio-prefix {
                margin-right: 0;
                margin-bottom: 10px;
                width: 100%;
            }

            .folio-input {
                text-align: center;
            }
        }

        /* Pulse animation for empty folio */
        .folio-container.pulse {
           /* animation: folioPulse 1.5s ease-in-out infinite;*/
        }

        @keyframes folioPulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.02);
            }
            100% {
                transform: scale(1);
            }
        }

        /* Active state for input wrapper */
        .folio-input-wrapper.active {
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        /* Success state for folio container */
        .folio-container.success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%) !important;
        }

        /* Error state for folio container */
        .folio-container.error {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%) !important;
        }

        /* Floating label effect */
        .folio-input:focus + .floating-label,
        .folio-input:not(:placeholder-shown) + .floating-label {
            transform: translateY(-25px) scale(0.8);
            color: #667eea;
        }

        /* Tooltip styles for folio help */
        .folio-tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
        }

        .folio-tooltip .tooltip-text {
            visibility: hidden;
            width: 200px;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            text-align: center;
            border-radius: 8px;
            padding: 8px;
            position: absolute;
            z-index: 1000;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.8rem;
        }

        .folio-tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }

        /* Gradient text effect */
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Progress indicator styles */
        .progress-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
            position: relative;
            z-index: 2;
        }

        .progress-step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin: 0 10px;
            position: relative;
            transition: all 0.3s ease;
        }

        .progress-step.active {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            transform: scale(1.1);
        }

        .progress-step.completed {
            background: #48bb78;
            color: white;
        }

        .progress-line {
            width: 60px;
            height: 2px;
            background: rgba(255, 255, 255, 0.3);
            position: relative;
        }

        .progress-line.completed {
            background: #48bb78;
        }

        /* Folio examples animation */
        .folio-examples {
            animation: fadeInUp 0.5s ease-out 0.5s both;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Step containers styles */
        .step-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
            display: none;
            animation: slideInUp 0.6s ease-out;
        }

        .step-container.active {
            display: block;
        }

        .step-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: stepShine 4s ease-in-out infinite;
        }

        .step-header {
            text-align: center;
            margin-bottom: 25px;
            position: relative;
            z-index: 2;
        }

        .step-title {
            color: white;
            font-size: 1.6rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .step-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1rem;
            font-weight: 400;
            margin-bottom: 0;
        }

        .step-content {
            position: relative;
            z-index: 2;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .field-group {
            margin-bottom: 25px;
        }

        .field-group:last-child {
            margin-bottom: 0;
        }

        .enhanced-label {
            color: #4a5568;
            font-weight: 600;
            font-size: 1rem;
            margin-bottom: 10px;
            display: block;
            position: relative;
        }

        .enhanced-label .required-indicator {
            color: #e53e3e;
            margin-left: 5px;
        }

        .enhanced-input {
            width: 100%;
            border: 2px solid #e2e8f0 !important;
            border-radius: 10px !important;
            padding: 15px 20px !important;
            font-size: 1rem !important;
            font-weight: 500 !important;
            color: #2d3748 !important;
            background: white !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
        }

        .enhanced-input:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
            transform: translateY(-2px) !important;
            outline: none !important;
        }

        .enhanced-input::placeholder {
            color: #a0aec0 !important;
            font-weight: 400 !important;
        }

        .enhanced-select {
            width: 100%;
            border: 2px solid #e2e8f0 !important;
            border-radius: 10px !important;
            padding: 15px 20px !important;
            font-size: 1rem !important;
            font-weight: 500 !important;
            color: #2d3748 !important;
            background: white !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 50px !important;
        }

        .enhanced-select:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
            transform: translateY(-2px) !important;
            outline: none !important;
        }

        .enhanced-textarea {
            width: 100%;
            border: 2px solid #e2e8f0 !important;
            border-radius: 10px !important;
            padding: 15px 20px !important;
            font-size: 1rem !important;
            font-weight: 500 !important;
            color: #2d3748 !important;
            background: white !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
            resize: vertical;
            min-height: 100px;
        }

        .enhanced-textarea:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
            transform: translateY(-2px) !important;
            outline: none !important;
        }

        .field-help {
            color: #718096;
            font-size: 0.875rem;
            margin-top: 8px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .field-icon {
            color: #667eea;
            margin-right: 10px;
            font-size: 1.1rem;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes stepShine {
            0% {
                transform: translateX(-100%) translateY(-100%) rotate(45deg);
            }
            50% {
                transform: translateX(100%) translateY(100%) rotate(45deg);
            }
            100% {
                transform: translateX(-100%) translateY(-100%) rotate(45deg);
            }
        }

        /* Step navigation buttons */
        .step-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 25px;
            position: relative;
            z-index: 2;
        }

        .step-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .step-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .step-btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .step-btn.btn-success {
            background: linear-gradient(135deg, #48bb78, #38a169);
            box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
        }

        .step-btn.btn-success:hover {
            box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);
        }

        /* Responsive design for steps */
        @media (max-width: 768px) {
            .step-container {
                padding: 20px;
                margin-bottom: 20px;
            }

            .step-title {
                font-size: 1.4rem;
                flex-direction: column;
                gap: 10px;
            }

            .step-content {
                padding: 20px;
            }

            .step-navigation {
                flex-direction: column;
                gap: 15px;
            }

            .step-btn {
                width: 100%;
                justify-content: center;
            }

            .progress-indicator {
                flex-wrap: wrap;
                gap: 10px;
            }

            .progress-step {
                width: 35px;
                height: 35px;
                margin: 0 5px;
            }

            .progress-line {
                width: 40px;
            }

            .enhanced-input,
            .enhanced-select,
            .enhanced-textarea {
                padding: 12px 15px !important;
                font-size: 0.9rem !important;
            }

            .field-group {
                margin-bottom: 20px;
            }
        }

        /* Animation for step transitions */
        .step-container {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s ease;
        }

        .step-container.active {
            opacity: 1;
            transform: translateY(0);
        }

        /* Enhanced focus states */
        .enhanced-input:focus,
        .enhanced-select:focus,
        .enhanced-textarea:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
            transform: translateY(-2px) !important;
        }

        /* Field validation states */
        .enhanced-input.is-valid,
        .enhanced-select.is-valid,
        .enhanced-textarea.is-valid {
            border-color: #48bb78 !important;
            box-shadow: 0 0 0 3px rgba(72, 187, 120, 0.1) !important;
        }

        .enhanced-input.is-invalid,
        .enhanced-select.is-invalid,
        .enhanced-textarea.is-invalid {
            border-color: #f56565 !important;
            box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1) !important;
        }

        /* Enhanced Important Notice Styles */
        .enhanced-notice {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            border-radius: 20px;
            padding: 0;
            margin: 30px 0;
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.3);
            position: relative;
            overflow: hidden;
            animation: noticeGlow 3s ease-in-out infinite alternate;
        }

        .enhanced-notice::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: noticeShine 4s ease-in-out infinite;
        }

        .notice-header {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px 30px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 20px;
            position: relative;
            z-index: 2;
        }

        .notice-icon-wrapper {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: noticeIconPulse 2s ease-in-out infinite;
        }

        .notice-icon {
            font-size: 1.8rem;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }


        .notice-title {
            color: white;
            font-size: 1rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            flex: 1;
        }

        .notice-content {
            padding: 25px 30px;
            position: relative;
            z-index: 2;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 0 0 20px 20px;
        }

        .notice-main-text {
            font-size: 0.9rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(135deg, #fff5f5, #fed7d7);
            border-radius: 10px;
            border-left: 4px solid #f56565;
            display: flex;
            align-items: center;
        }

        .notice-details {
            margin: 20px 0;
        }

        .notice-point {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            color: #4a5568;
            font-size: 0.95rem;
            padding: 8px 0;
        }

        .notice-point i {
            color: #48bb78;
            font-size: 1rem;
            width: 20px;
        }

        .notice-footer {
            text-align: center;
            padding-top: 15px;
            border-top: 1px solid #e2e8f0;
            margin-top: 20px;
        }

        .notice-footer small {
            color: #718096;
            font-style: italic;
        }

        @keyframes noticeGlow {
            0% {
                box-shadow: 0 15px 35px rgba(255, 107, 107, 0.3);
            }
            100% {
                box-shadow: 0 20px 45px rgba(255, 107, 107, 0.5);
            }
        }

        @keyframes noticeShine {
            0% {
                transform: translateX(-100%) translateY(-100%) rotate(45deg);
            }
            50% {
                transform: translateX(100%) translateY(100%) rotate(45deg);
            }
            100% {
                transform: translateX(-100%) translateY(-100%) rotate(45deg);
            }
        }

        @keyframes noticeIconPulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        /* Responsive design for notice */
        @media (max-width: 768px) {
            .enhanced-notice {
                margin: 20px 0;
            }

            .notice-header {
                padding: 15px 20px;
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .notice-icon-wrapper {
                width: 50px;
                height: 50px;
            }

            .notice-icon {
                font-size: 1.5rem;
            }


            .notice-content {
                padding: 20px;
            }

            .notice-main-text {
                font-size: 1rem;
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .notice-point {
                font-size: 0.9rem;
            }
        }

        /* Hover effect for the entire notice */
        .enhanced-notice:hover {
            transform: translateY(-2px);
            transition: transform 0.3s ease;
        }

        /* MOBILE RESPONSIVENESS FIXES - Conservative approach */

        /* Prevent horizontal scroll only when necessary */
        @media (max-width: 768px) {
            /* Ensure body doesn't scroll horizontally */
            body {
                overflow-x: hidden;
            }

            /* Make sure form elements don't exceed viewport */
            .enhanced-input,
            .enhanced-select,
            .enhanced-textarea {
                max-width: 100%;
                box-sizing: border-box;
            }

            /* Ensure containers don't cause overflow */
            .folio-container,
            .step-container,
            .enhanced-notice {
                max-width: 100%;
                box-sizing: border-box;
            }

            /* Fix any wide elements */
            * {
                max-width: 100%;
                box-sizing: border-box;
            }

            /* Specific fixes for elements that commonly cause issues */
            .card-body {
                overflow-x: hidden;
            }

            #formulario {
                overflow-x: hidden;
            }
        }

        /* Mobile specific fixes - Only essential changes */
        @media (max-width: 768px) {
            /* Folio container mobile adjustments */
            .folio-input-wrapper {
                flex-direction: column;
                gap: 10px;
            }

            .folio-prefix {
                margin-right: 0;
                width: 100%;
                text-align: center;
            }

            .folio-input {
                text-align: center;
            }

            /* Progress indicator mobile */
            .progress-indicator {
                flex-wrap: wrap;
                gap: 8px;
                justify-content: center;
            }

            .progress-step {
                width: 30px;
                height: 30px;
                margin: 0 3px;
                font-size: 0.8rem;
            }

            .progress-line {
                width: 30px;
            }

            /* Step navigation mobile */
            .step-navigation {
                flex-direction: column;
                gap: 10px;
            }

            .step-btn {
                width: 100%;
                justify-content: center;
            }

            /* Notice mobile adjustments */
            .notice-header {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .notice-main-text {
                flex-direction: column;
                text-align: center;
                gap: 8px;
            }
        }

        /* Extra small mobile devices - minimal changes */
        @media (max-width: 480px) {
            .progress-step {
                width: 25px;
                height: 25px;
                font-size: 0.7rem;
            }

            .progress-line {
                width: 20px;
            }
        }


        .container, .container-fluid, .container-sm, .container-md, .container-lg, .container-xl, .container-xxl {
            --bs-gutter-x: 0;
        }
    </style>

{% endblock %}

{% block javascripts %}
    <script src={{asset('js/jquery.js')}}></script>






    <script>
        function opcionesTipoCliente(select){
            var tipoPersona = $(select).val();

            // Mostrar indicador de carga
            showLoadingIndicator();

            $.ajax({
                url: "{{ path('app_invoice_plantilla_select') }}/" + tipoPersona,
                type: 'POST',
                dataType: "html",
                beforeSend: function() {
                    // Asegurar que el indicador de carga esté visible
                    showLoadingIndicator();
                },
                success: function (html) {
                    // Simular un pequeño delay para mejor UX (opcional)
                    setTimeout(function() {
                        $("#tipoFM").html(html);
                        hideLoadingIndicator();

                        // Animar la aparición de los nuevos campos
                        $("#tipoFM").hide().fadeIn(400);
                    }, 300);
                },
                error: function (xhr, status, error) {
                    hideLoadingIndicator();
                    Swal.fire({
                        title: 'Error al cargar tipo de persona',
                        text: 'Ocurrió un error: ' + error,
                        icon: 'error',
                        confirmButtonText: 'Aceptar'
                    });
                }
            });
        }
        // Función global para mostrar mensajes de validación
        function showFolioValidationMessage(message, type) {
            console.log('showFolioValidationMessage llamada con:', message, type);

            // Remover mensaje anterior si existe
            $('#folio-validation-message').remove();

            // Determinar clases CSS según el tipo
            let alertClass = '';
            let iconClass = '';

            switch(type) {
                case 'success':
                    alertClass = 'alert-success';
                    iconClass = 'fas fa-check-circle';
                    break;
                case 'error':
                    alertClass = 'alert-danger';
                    iconClass = 'fas fa-exclamation-circle';
                    break;
                case 'info':
                    alertClass = 'alert-info';
                    iconClass = 'fas fa-info-circle';
                    break;
                default:
                    alertClass = 'alert-secondary';
                    iconClass = 'fas fa-info-circle';
            }

            // Crear el HTML del mensaje
            const messageHtml = `
                <div id="folio-validation-message" class="alert ${alertClass} alert-dismissible fade show mt-3" role="alert">
                    <i class="${iconClass} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;

            // Colocar el mensaje en el contenedor designado
            $('#message-container').html(messageHtml);

            console.log('Mensaje agregado al contenedor');
        }
    </script>
    <script>

     $(document).ready(function(){
        formulario();

        $("#prefijo").val('{{prefijo}}');

    });


function selectRFCDFI(){

    var tipoPersona = $("#invoice_clientefacturadatos_tipoPersona").val();


    var request = $.ajax({
        url: "{{path('app_invoice_plantilla_select')}}/"+tipoPersona,
        method: 'POST',
        dataType:'html',
        data: { tipoPersona: tipoPersona },

    });

    request.done(function(html){
        $("#tipoFM").html(html);
          usoCFDI = $("#invoice_clientefacturadatos_usocfdi").val();
          Rf = $("#invoice_clientefacturadatos_regimenfiscal").val();

          if(usoCFDI) $("#usocdfi").val(usoCFDI);
          else{

            var newOption = $('<option>', {
                value: 'Selecciona un tipo de persona primero',
                text: 'Selecciona un tipo de persona primero'
            });
            $('#usocdfi').append(newOption);
            $("#usocdfi").val("Selecciona un tipo de persona primero");
          }

          if(Rf) $("#regimenfiscal").val(Rf);
          else{
            var newOption = $('<option>', {
                value: 'Selecciona un tipo de persona primero',
                text: 'Selecciona un tipo de persona primero'
            });
            $('#regimenfiscal').append(newOption);
            $("#regimenfiscal").val("Selecciona un tipo de persona primero");
          } 


    });

    request.fail(function(jqXHR, textStatus){
        alert("Request failed: "+textStatus);
    });
}

function formulario(){

   // prefijo = $("#prefijo").val();

    var request = $.ajax({
        url: "{{path('app_invoice_formulario_empresa',{'e':prefijo})}}",
        method: 'GET',
        dataType:'html',

    });

    request.done(function(html){
        $("#formulario").html(html);
       // selectRFCDFI();
    });

    request.fail(function(jqXHR, textStatus){
        alert("Request failed: "+textStatus);
    });
}


function asignarValorUsoCfdi(valor){

    $("#invoice_clientefacturadatos_usocfdi").val(valor);
}

function asignarValorRegimenFiscal(valor){

    $("#invoice_clientefacturadatos_regimenfiscal").val(valor);

    console.log( valor );
    console.log( $("#invoice_clientefacturadatos_regimenfiscal").val());
}




     function validateFolio(folio, prefijo) {
         $.ajax({
             url: "{{ path('app_invoice_validar_folio', {'e': '__PREFIJO__'}) }}".replace('__PREFIJO__', prefijo),
             type: 'POST',
             data: { folio: folio },
             dataType: 'json',
             success: function(response) {
                 console.log('Respuesta del servidor:', response);
                 const folioInput = $('#invoice_clientefacturadatos_folio');

                 if (response.valid) {
                     folioValidado = true;
                     folioInput.addClass('is-valid').removeClass('is-invalid');
                  //   showFolioValidationMessage(response.message, 'success');

                     // Mostrar los campos adicionales con animación
                     showAdditionalFields();
                 } else {
                     folioValidado = false;
                     folioInput.addClass('is-invalid').removeClass('is-valid');
                     console.log('Mostrando mensaje de error:', response.message);
                     showFolioValidationMessage(response.message, 'error');

                     // Ocultar los campos adicionales si están visibles
                     hideAdditionalFields();
                 }
             },
             error: function(xhr, status, error) {
                 console.log('Error AJAX:', xhr.responseText);
                 folioValidado = false;
                 $('#invoice_clientefacturadatos_folio').addClass('is-invalid').removeClass('is-valid');

                 // Intentar parsear el error del servidor
                 let errorMessage = 'Error al validar el folio. Intenta nuevamente.';
                 try {
                     const errorResponse = JSON.parse(xhr.responseText);
                     if (errorResponse.message) {
                         errorMessage = errorResponse.message;
                     }
                 } catch (e) {
                     console.log('No se pudo parsear la respuesta de error');
                 }

                 showFolioValidationMessage(errorMessage, 'error');
             }
         });
     }

     function showFolioValidationMessage(message, type) {
         console.log('showFolioValidationMessage llamada con:', message, type);
         $('#folio-validation-message').remove();

         let iconClass = '';
         let colorClass = '';
         let bgClass = '';

         switch(type) {
             case 'success':
                 iconClass = 'fas fa-check-circle';
                 colorClass = 'text-success';
                 bgClass = 'alert-success';
                 break;
             case 'error':
                 iconClass = 'fas fa-exclamation-circle';
                 colorClass = 'text-danger';
                 bgClass = 'alert-danger';
                 break;
             case 'info':
                 iconClass = 'fas fa-spinner fa-spin';
                 colorClass = 'text-info';
                 bgClass = 'alert-info';
                 break;
         }

         const messageHtml = `
                <div id="folio-validation-message" class="alert ${bgClass} alert-dismissible fade show mt-3 mb-3" role="alert">
                    <i class="${iconClass} me-2"></i>
                    <strong>${type === 'error' ? 'Error:' : type === 'success' ? 'Éxito:' : 'Info:'}</strong> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;

         // Colocar el mensaje en el contenedor designado
         $('#message-container').html(messageHtml);

         console.log('Mensaje agregado debajo de important-notice');

         // Scroll hacia el mensaje para asegurar que sea visible
         if (type === 'error') {
             $('html, body').animate({
                 scrollTop: $('#folio-validation-message').offset().top - 100
             }, 300);
         }
     }

     // Función para mostrar campos adicionales
     function showAdditionalFields() {
         const additionalFields = $('#additional-fields');

         // Mostrar el contenedor
         additionalFields.show();

         // Agregar clase para activar animaciones CSS
         setTimeout(function() {
             additionalFields.addClass('show');
         }, 50);

         // Scroll suave hacia los campos adicionales después de la animación
         setTimeout(function() {
             $('html, body').animate({
                 scrollTop: additionalFields.offset().top - 100
             }, 500);
         }, 300);
     }

     // Función para ocultar campos adicionales
     function hideAdditionalFields() {
         const additionalFields = $('#additional-fields');

         // Remover clase de animación
         additionalFields.removeClass('show');

         // Ocultar después de la animación
         setTimeout(function() {
             additionalFields.hide();

             // Limpiar valores de los campos cuando se ocultan
             $('#additional-fields input, #additional-fields select, #additional-fields textarea').val('');
             $('#tipoFM').html(''); // Limpiar contenido dinámico
         }, 600);
     }


     // Función para mostrar indicador de carga
     function showLoadingIndicator() {
         const loadingHtml = `
                <div id="loading-indicator" class="text-center py-4">
                    <div class="loading-spinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                    </div>
                    <div class="loading-text mt-3">
                        <p class="text-muted mb-1">
                            <i class="fas fa-cog fa-spin me-2"></i>
                            Cargando opciones de facturación...
                        </p>
                        <small class="text-muted">Preparando régimen fiscal y uso CFDI</small>
                    </div>
                </div>
            `;
         $("#tipoFM").html(loadingHtml);
     }

     // Función para ocultar indicador de carga
     function hideLoadingIndicator() {
         $("#loading-indicator").fadeOut(200);
     }

     // Función para manejar el envío del formulario (referenciada en form_start)
     function handleFormSubmit(event) {
         // Esta función se ejecuta cuando se envía el formulario
         // Las validaciones ya se manejan en el event listener de submit
         console.log('Formulario enviado');
         return true;
     }

     // Función para enviar el formulario por AJAX
     function submitFormAjax() {
         const form = $('#formularioClienteFactura');
         const formData = new FormData(form[0]);
         const prefijo =$("#prefijo").val();


         // Mostrar indicador de carga
         showSubmitLoadingIndicator();

         // Deshabilitar el botón de envío
         const submitButton = form.find('button[type="submit"], input[type="submit"]');
         submitButton.prop('disabled', true);

         $.ajax({
             url: "{{path('app_invoice_formulario_empresa',{'e':prefijo})}}",
             type: 'POST',
             data: formData,
             processData: false,
             contentType: false,
             dataType: 'html',
             success: function(html) {
                 console.log('Respuesta del servidor recibida');
                 console.log('Longitud de la respuesta:', html.length);
                 console.log('Primeros 200 caracteres:', html.substring(0, 200));
                 hideSubmitLoadingIndicator();
                 submitButton.prop('disabled', false);

                 // Actualizar el formulario con la nueva respuesta HTML
                 $("#formulario").html(html);
             },
             error: function(xhr, status, error) {
                 hideSubmitLoadingIndicator();
                 submitButton.prop('disabled', false);

                 let errorMessage = 'Error al procesar la solicitud.';

                 try {
                     const errorResponse = JSON.parse(xhr.responseText);
                     if (errorResponse.message) {
                         errorMessage = errorResponse.message;
                     }
                 } catch (e) {
                     // Si no se puede parsear, usar mensaje genérico
                     if (xhr.status === 500) {
                         errorMessage = 'Error interno del servidor. Por favor, intenta nuevamente.';
                     } else if (xhr.status === 422) {
                         errorMessage = 'Datos del formulario no válidos. Verifica la información ingresada.';
                     }
                 }

                 Swal.fire({
                     title: 'Error',
                     text: errorMessage,
                     icon: 'error',
                     confirmButtonText: 'Aceptar'
                 });
             }
         });
     }

     // Función para mostrar indicador de carga durante el envío
     function showSubmitLoadingIndicator() {
         // Crear overlay de carga
         const loadingOverlay = `
                <div id="submit-loading-overlay" class="submit-loading-overlay">
                    <div class="submit-loading-content">
                        <div class="submit-loading-spinner">
                            <div class="spinner-border text-light" role="status">
                                <span class="visually-hidden">Procesando...</span>
                            </div>
                        </div>
                        <div class="submit-loading-text">
                            <h5 class="text-white mb-2">
                                <i class="fas fa-file-invoice me-2"></i>
                                Generando factura...
                            </h5>
                            <p class="text-white-50 mb-0">
                                Por favor espera mientras procesamos tu solicitud
                            </p>
                        </div>
                    </div>
                </div>
            `;

         $('body').append(loadingOverlay);
         $('#submit-loading-overlay').fadeIn(300);
     }

     // Función para ocultar indicador de carga durante el envío
     function hideSubmitLoadingIndicator() {
         $('#submit-loading-overlay').fadeOut(300, function() {
             $(this).remove();
         });
     }
 
</script>
    
{% endblock %}

{% block content %}

<input disabled type="hidden" class="form-control reset text-center d-none" id="prefijo">

<!--<img width="150" src="{{logo64}}" alt="" />

<h1>{{nombre}}</h1> -->


<div id="formulario"></div>

<p id="mensajes"></p>
<script>

function loadingGif(idcontenedor){

    $("#" + idcontenedor).html('<div align="center"><img style="max-width: 100%;" src="/img/log.gif"></div>');

}

function handleFormSubmit(event) {
    console.log("Form submission triggered"); // Debugging log
    event.preventDefault(); // Prevent the default form submission
    
    // Show loading gif
    loadingGif("formulario");

    // Get prefijo value
    var prefijo = $("#prefijo").val();

    // Construct the URL
    var url = "{{ path('app_invoice_formulario_empresa', {'e': '__PREFIJO__'}) }}".replace('__PREFIJO__', prefijo);

    // Get form data
    var formData = new FormData(event.target); // Using 'this' to get the current form element

    // Perform AJAX request
    $.ajax({
      url: url,
      type: 'POST',
      data: formData,
      contentType: false, // These two settings are necessary when using FormData
      processData: false, // to send files properly
      dataType: 'html' // Expecting HTML in response
    })
    .done(function(html) {
      // Update the form with the new HTML
      $("#formulario").html(html);
      
      // Update the mensajes field
      $("#mensajes").val("{{ msj }}");
    })
    .fail(function(xhr, status, error) {
      // Handle error
      console.error('Error:', error);
    });
  }

</script>


{% endblock %}
