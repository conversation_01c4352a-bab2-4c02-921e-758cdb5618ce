{% extends 'admin/layout.html.twig' %}
{% block titleHead %}Cargar Datos | comsiones {% endblock %}
{% block title %}Cargar Datos{% endblock %}
{% block stylesheets %}
{{parent()}}
<link href="{{asset('lib/jQuery-Upload-File/4.0.11/uploadfile.css')}}" rel="stylesheet">

{% endblock %}
{% block content %}
<div class="card">
  <input id="url-subir-documento" type="hidden" value="{{path('subir-documento')}}">
  <div class="row file">
    <div class="col-md-12">
      <h1 class="titlefile">Sube los documentos para cargar la información</h1>
      <p></p>
    </div>
  </div>
  <div class="row file">
    <div class="col-md-6 col-sm-6 text-center"></div ><!-- fin de diov col-md-6 -->
    <div class="col-md-6 col-sm-6 text-center">
      <button  id="extrabutton" class="btn btn-success" >Procesar los documentos</button>
    </div><!--fin de diov col-md-6-->
  </div>
  <div class="row file">
    <div class="col-md-12 col-sm-12 text-center">
    <div id="documento1"></div>
  </div><!--fin de diov col-md-6-->

</div>

{% endblock %}
{% block javascripts %}
{{parent()}}
<script src="{{asset('lib/jQuery-Upload-File/4.0.11/jquery.uploadfile.min.js')}}"></script>
<script>

var url=$("#url-subir-documento").val();
var documento1Cargado1="";
var documento1Cargado2="";
var errorSubirDocumentos="";

var archivo1Seleccionado1=false;
var archivo1Seleccionado2=false;
var nombreDocumento1="";
var nombreDocumento2="";
$(document).ready(function(){
var uploadObj=$("#documento1").uploadFile({
  url:url,
  multiple:true,
  dragDrop:true,
  fileName:"file1",
  showFileSize:true,
  showFileCounter:true,
  sequential:true,
  sequentialCount:1,
  acceptFiles:"application/vnd.ms-excel",
  uploadStr:"Reporte",
  onSelect:function(files)
  {
    $("#extrabutton").removeClass("d-none");
  },
  extraHTML:function()
  {
    var d = new Date();
    var n = d.getTime();
    var id=Math.floor(Math.random() * 100000)+n;
      	var html = '<div id="'+id+'">';
  		html += "<input type='hidden' name='id' value='"+id+"' />";
  		html += "</div>";
  		return html;
  },
  onError: function(files,status,errMsg,pd)
  {
    errorSubirDocumentos=true;
      //files: list of files
      //status: error status
      //errMsg: error message
      Swal.fire({
        type: 'error',
        title: files+" "+errMsg,
        showConfirmButton: false,
        timer: 1500
      });
  },
  autoSubmit:false,
  onSubmit: function(){
    $(".log").html("Procesando...");
  },
  onSelect:function(files){
    files[0].name;
    files[0].size;
    console.log(files[0].name);
    archivo1Seleccionado1=true;
  },
  onSuccess:function(files,data,xhr,pd)
  {
      //files: list of files
    /*  console.log(JSON.stringify(files));
      console.log(JSON.stringify(data));
      console.log(JSON.stringify(xhr));
      console.log(JSON.stringify(pd));*/
      //data: response from server
      //xhr : jquer xhr object
      console.log(data);

      if(data.exito){
          $("#"+data.idVista).html("Listo <br>Reporte: "+data.tipoDocumento);
          $("#"+data.idVista).closest(".ajax-file-upload-statusbar").addClass( "bg-success text-white" );
          documento1Cargado1=true;
          nombreDocumento1=data.nombreDocumento;
          console.log("Nombre del documento "+data.nombreDocumento);

      }else{
        $("#"+data.idVista).closest(".ajax-file-upload-statusbar").addClass( "bg-warning text-dark" );

        $("#"+data.idVista).html(data.msj);
      //  errorSubirDocumentos=true;
          documento1Cargado1=false;
      }

  }
  });


  //validamos que no esten vacios los Documentos
  $("#extrabutton").click(function()
  {

    Swal.fire({
      title: 'Espere por favor..',
      html: '<div class="log"></div>',
      onBeforeOpen: () => {
        Swal.showLoading()

      },
      onClose: () => {
      //  clearInterval(timerInterval)
      }
    }).then((result) => {
      console.log(result);


    });


    console.log("entra aqui 2");
    //  if($("#documento1").val() !="" && $("#documento2").val() !=""){
        uploadObj.startUpload();

        verificarCargaDocumentos();
    //  }else{
        /*Swal.fire({
          title: 'Debe seleccionar los documentos',
          animation: true,
           type: 'warning',
          customClass: {
            popup: 'animated tada'
          }
        });*/
      //}

  });
  function verificarCargaDocumentos(){
    $(".log").html("Verificando Carga");
    console.log("Entra a verificar carha de documentos");
    console.log("documento1Cargado1 "+documento1Cargado1);

    console.log("errorSubirDocumentos "+errorSubirDocumentos);

      if(archivo1Seleccionado1== true){
        if((documento1Cargado1===true) && errorSubirDocumentos!==true){
           unirDocumentos();

         }else if(documento1Cargado1 === false){
             console.log("Error al subir docuentos 1");
             if(documento1Cargado1===false){
               console.log("error al subir documentos");

               Swal.fire({
                 type: 'warning',
                 title: "Error al subir documentos",
                 showConfirmButton: true
               });

                uploadObj.reset();
             }
         }else if(errorSubirDocumentos===true){
            console.log("Error de sistema al subir docuentos 2");
            Swal.fire({
              type: 'error',
              title: "Error de sistema al subir docuentos",
              showConfirmButton: true
            });
          }else{

            console.log("entra a else de verificar documentos");
            setTimeout(function(){
              verificarCargaDocumentos()
            },2000);
          }
      }else{
        Swal.fire({
          type: 'warning',
          title: "Debe seleccionar los dos documentos",
          showConfirmButton: true
        });
      }




  }


  function unirDocumentos(){
     documento1Cargado1="";

     errorSubirDocumentos="";

     archivo1Seleccionado1=false;



//     uploadObj.reset();

    $(".log").html("Uniendo Documentos");
    Swal.fire({
      type: 'success',
      title: "Documentos procesados..",
      showConfirmButton: true
    });
    //crearExcel();

  }
});


function crearExcel(){
//me quede aqui
var url=$("#url-crear-excel").val();
$.ajax({
  url: url,
  data:{nombreDocumento1:nombreDocumento1,nombreDocumento2:nombreDocumento2}
}).done(function( data ) {
    console.log(data);
    console.log(data.exito);
    if(data.exito==true){

      Swal.fire({
        type: 'success',
        title: "Documentos subidos exitosamente",
        showConfirmButton: true,
        html:
         '<div class="resultado">' +
                  '</div>',
      });
    }else{
      Swal.fire({
        type: 'warning',
        title: data.msj,
        showConfirmButton: true
      });
    }
    $(".resultado").html('<a terget="new" href="{{asset(uploads_front~"/")}}'+data.nombreFinal+'">Descargar</a> ');
  }).fail(function(jqXHR, textStatus, errorThrown ) {
    Swal.fire({
      type: 'error',
      title: JSON.stringify(jqXHR)+JSON.stringify(textStatus)+JSON.stringify(errorThrown),
      showConfirmButton: true
    });
  })
}



</script>
</div>
{% endblock %}
