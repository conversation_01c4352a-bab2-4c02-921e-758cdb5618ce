{# templates/security/login.html.twig #}

{% block sonata_nav %}
{% endblock sonata_nav %}
{% block logo %}
{% endblock logo %}
{% block sonata_left_side %}
{% endblock sonata_left_side %}
{% block body_attributes %}{% endblock %}
{% block sonata_wrapper %}
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ asset('login-theme/fonts/icomoon/style.css') }}">
    <link rel="stylesheet" href="{{ asset('login-theme/css/owl.carousel.min.css') }}">
    <link rel="stylesheet" href="{{ asset('login-theme/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('login-theme/css/style.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('lib/fontawesome-free-5.9.0-web/css/all.css') }}">

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    {% if birthdays is not empty %}



    <script type="module">
    import { confetti } from "https://cdn.jsdelivr.net/npm/tsparticles-confetti/+esm";

    const colors = ["#70bcff", "#ffffff"];

    const run = () => {
        const end = Date.now() + 10 * 1000;
        console.log("Running");
        (function frame() {
            confetti({
                particleCount: 2,
                angle: 60,
                spread: 55,
                origin: { x: 0 },
                colors: colors
            });

            confetti({
                particleCount: 2,
                angle: 120,
                spread: 55,
                origin: { x: 1 },
                colors: colors
            });

            if (Date.now() < end) {
                requestAnimationFrame(frame);
            }
        })();
    };

    document.getElementById("run").addEventListener("click", run);

    run();

</script>

    
    {% endif %}

    <div class="container-fluid position-relative p-0 d-flex flex-column">
        <div class="row login">
            <div class="col-md-6 image-log p-0" style="background-image:url('{{ asset('login-theme/images/ImagenSan-Luis-Potosi.png') }}');background-size:cover;background-position:center;clip-path: polygon(0% 0%, 80% 0%, 100% 100%, 0% 100%);" >
                {#<img src="" alt="Image" class="w-100 login">#}
            </div>
            <div class="col-md-6 movil d-flex flex-column align-items-center justify-content-center">
                <div class="col-md-8 acceso">
                    <div class="mb-4 grupo">
                        <img src="{{ asset('login-theme/images/grupooptimo_login.png') }}" alt="Image" class="img-fluid">
                    </div>
                    {% block sonata_user_login_form %}
                    {% block sonata_user_login_error %}
                        {% if error %}
                            <div class="alert alert-danger">
                                {{ error.messageKey|trans(error.messageData, 'security') }}
                            </div>
                        {% endif %}
                    {% endblock %}
                    {% for label, flashes in app.session.flashbag.all %}
                        {% for flash in flashes %}
                            <div class="alert alert-{{ label }}">
                                {{ flash }}
                            </div>
                        {% endfor %}
                    {% endfor %}
                    <p class="login-box-msg">{{ 'Authentication'|trans }}</p>
                    <form action="{{ path("admin_login") }}" method="post">
                        {{ form_row(form._token) }}
                        <div class="form-group first d-flex flex-column align-items-center">
                            <label for="username" class="form-label">Correo electrónico</label>
                            <input type="text" class="form-control" id="username" name="{{ form.email.vars.full_name }}" value="{{ last_username }}" required="required" style="border-radius: 25px;">
                        </div>
                        <div class="form-group last mb-4 d-flex flex-column align-items-center">
                            <label for="password" class="form-label">Contraseña</label>
                            <div class="input-group input-group-password">
                                <input type="password" class="form-control" id="password" name="{{ form.contrasena.vars.full_name }}" required="required" style="border-radius: 25px;">
                                <button class="btn btn-toggle-password" type="button" id="togglePassword">
                                    <i class="fa-solid fa-eye" aria-hidden="true"></i>
                                </button>
                            </div>
                        </div>
                        <div>
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary">Ingresar</button>
                            </div>
                            <div class="text-center mt-3">
                                <a href="{{ path("app_forgot_password_request") }}" class="text-white">Recupera tu contraseña</a>
                            </div>
                        </div>
                        
                    </form>
                    {% endblock %}
                </div>
            </div>
        </div>
        <div class="row logos position-absolute d-flex flex-row align-items-center justify-content-around">
            <div class="col-3">
                <img src="{{ asset('login-theme/images/orthopedie.png') }}" alt="Image" class="img-fluid logo-empresa">
            </div>
            <div class="col-3">
                <img src="{{ asset('login-theme/images/abathon.png') }}" alt="Image" class="img-fluid logo-empresa">
            </div>

            <div class="col-3">
                <img src="{{ asset('login-theme/images/optimo.png') }}" alt="Image" class="img-fluid logo-empresa">
            </div>
        </div>
    </div>
   
    <script src="{{ asset('login-theme/js/jquery-3.3.1.min.js') }}"></script>
    <script src="{{ asset('login-theme/js/popper.min.js') }}"></script>
    <script src="{{ asset('login-theme/js/bootstrap.min.js') }}"></script>
    <script src="{{ asset('login-theme/js/main.js') }}"></script>
    <script>
        $(document).ready(function() {
            $('#togglePassword').click(function() {
                const tipo = $('#password').attr('type') === 'password' ? 'text' : 'password';
                $('#password').attr('type', tipo);
                $(this).find('i').toggleClass('fa-eye fa-eye-slash');
            });
        });
    </script>
{% endblock sonata_wrapper %}