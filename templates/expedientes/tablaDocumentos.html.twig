<div class="row justify-content-md-center">
    <div class="col-md-10 table-responsive">
        <table class="table" id="tablaDocumentos">
            <thead>
                <tr class="text-start">
                    <th scope="col">#</th>
                    <th scope="col">Categoria</th>
                    <th scope="col"></th>
                </tr>
            </thead>
            <tbody id = "tableBodyDocumentos">


                {% for index, documento in docuementosExpediente %}

                    <tr>

                        <td class="text-center align-middle ">{{index + 1}}</td>

                        <td class="text-center align-middle ">{{documento.tipodocumento}}</td>

                        <td class="text-center align-middle ">
                            <button type="button" class="btn btn-primary" onclick="mostrarDocumento('{{documento.nombredocumento}}','{{documento.tipodocumento}}')" data-toggle="modal" data-target="#modal-visor-documentos"><i class="fa-solid fa-eye"></i></button>
                            <button type="button" class="btn btn-danger" onclick="eliminarDocumentos({{documento.iddocumentosexamenvisual}})"><i class="fa fa-trash"></i></button>

                        </td>

                    </tr>

                {% endfor %}
                {% for index, documento in documentsGraduation %}

                    <tr>

                        <td class="text-center ">{{index + 1}}</td>

                        <td class="text-center ">{{documento.documenttype}}</td>

                        <td class="text-center ">
                            <button type="button" class="btn btn-primary" onclick="mostrarDocumento('{{documento.filename}}','{{documento.documenttype}}')" data-toggle="modal" data-target="#modal-visor-documentos"><i class="fa-solid fa-eye"></i></button>
                            <button type="button" class="btn btn-danger" onclick="eliminarDocumentos({{documento.iddocumentosgraduacion}})"><i class="fa fa-trash"></i></button>

                        </td>

                    </tr>

                {% endfor %}
             
            </tbody>
        </table>

    </div>
</div>