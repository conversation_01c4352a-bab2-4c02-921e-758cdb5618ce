{% extends 'admin/layout.html.twig' %}

{% block sonata_admin_content %}
    <div class="container-fluid">
        <input id="flujo-expediente-id" type="hidden" class="form-control">
        <input id="idordenlaboratorio" type="hidden" class="form-control">
        <input id="saleid" type="hidden" class="form-control"
                {% if Flujoexpediente.ventaIdventa.idventa is defined %}
                    value="{{ Flujoexpediente.ventaIdventa.idventa }}"
                {% endif %}>
        <input id="client-name" type="hidden" class="form-control"
                {% if Flujoexpediente.clienteIdcliente.nombre is defined %}
                    value="{{ Flujoexpediente.clienteIdcliente.nombre }}"
                {% endif %}>

        <input id="url-ventas-upload-sale-document" type="hidden" value="{{ path('ventas-upload-sale-document') }}">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header card-header-info">
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="card-title" id="clinical-record-flow-title">EXPEDIENTE CLÍNICO
                                    {% if Flujoexpediente.fechaentrega != null %}
                                        - ENTREGADO EL {{ Flujoexpediente.fechaentrega|date("d/m/Y") }}
                                    {% endif %}
                                </h4>
                            </div>
                        </div>
                    </div>
                    <ul class="nav mb-3" id="ex-with-icons" role="tablist">
                        <li class="nav-item" role="presentation" id="tab1">
                            <a id="collapse-button-1" class="btn-collapse" data-bs-toggle="collapse" href="#etapa-1"
                               role="button" aria-expanded="false" aria-controls="etapa-1"><i class="fa fa-user me-3"
                                                                                              aria-hidden="true"></i></i>
                                DATOS PERSONALES</a>
                        </li>
                        <li class="nav-item" role="presentation" id="tab2">
                            <a id="collapse-button-2" class="btn-collapse" data-bs-toggle="collapse" href="#etapa-2"
                               role="button" aria-expanded="false" aria-controls="etapa-2" o><i class="fa fa-file me-3"
                                                                                                aria-hidden="true"></i>EXAMEN
                                VISUAL</a>
                        </li>
                        <li class="nav-item" role="presentation" id="tab3">
                            <a id="collapse-button-3" class="btn-collapse" data-bs-toggle="collapse" href="#etapa-3"
                               role="button" aria-expanded="false" aria-controls="etapa-3"><i
                                        class="fa fa-check-square-o me-3" aria-hidden="true"></i>ORDEN DE
                                LABORATORIO</a>
                        </li>
                        <li class="nav-item" role="presentation" id="tab4">
                            <a id="collapse-button-4" class="btn-collapse" data-bs-toggle="collapse" href="#etapa-4"
                               role="button" aria-expanded="false" aria-controls="etapa-4"><i
                                        class="fa fa-shopping-bag  me-3" aria-hidden="true"></i>ORDEN DE COMPRA</a>
                        </li>
                        <li class="nav-item" role="presentation" id="tab5">
                            <a id="collapse-button-5" class="btn-collapse" data-bs-toggle="collapse" href="#etapa-5"
                               role="button" aria-expanded="false" aria-controls="etapa-5"><i class="fa fa-pencil me-3"
                                                                                              aria-hidden="true"></i>FIRMA</a>
                        </li>
                    </ul>
                    <div class="tab-content" id="ex-with-icons-content">
                        <!-- Personal information -->
                        <div class="collapse" id="etapa-1">
                            <div class="container-fluid" id="datospersonales">

                            </div>
                        </div>
                        <!-- Eye exam -->
                        <div class="collapse" id="etapa-2">

                            {% if prevGraduations|length > 0 %}
                                <div class="container">
                                    <select class="form-select" id="select-graduation"
                                            onchange="obtenerFormularioExamenVisual()">
                                        <option value="current"><p>Registro actual</p></option>
                                        <option value="-1"><p>Nuevo registro</p></option>
                                        {% for graduation in prevGraduations %}
                                            <option style="color:black;" value="{{ graduation.idgraduacion }}">
                                                <p>Registro tomado el&nbsp;{{ graduation.creacion|date("d/m/Y") }}</p>
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            {% endif %}

                            <div class="container-fluid" id="examenvisual">

                            </div>
                        </div>
                        <!-- Order laboratory-->
                        <div class="collapse" id="etapa-3">
                            <div class="container-fluid" id="tablaordenlaboratorio"></div>
                            <div class="container" id="subjetiva-final-preview"></div>
                            <div class="container-fluid" id="ordenlaboratorio"></div>
                        </div>
                        <!-- Purchase order -->
                        <div class="collapse" id="etapa-4">
                            <div class="row purchase-order">
                                <div class="col-11 purchase" id="folioventa">
                                    <h3 class="subtitles-text proceedings-text">Agregar folio de venta</h3>
                                </div>
                            </div>
                            <div class="row purchase-order form-space">
                                <div class="col-5 purchase" id="folioventa">
                                    <label for="folio-venta">Folio: </label>
                                    <input id="folio-venta" class="form-control" placeholder="#######"
                                           style="border-bottom: 1px solid #c3c3c3;">
                                    <p id="mensajeErrorVenta"></p>
                                </div>
                                <div class="col-2 purchase icon-margin" id="folioventa">
                                    <button id="buscar-cotizacion-button" type="button" class="btn new-button"
                                            onclick="buscarCotizacion()"><i class="fa-solid fa-magnifying-glass fa-lg"
                                                                            style="color: #115fd1;"></i></button>
                                </div>
                                <div id="flow-sales-table-container" class="col-7"></div>
                            </div>

                        </div>
                        <!-- Signature -->
                        <div class="collapse" id="etapa-5">
                            <div class="row signature-end">
                                <div class="col-11 signature">
                                    <h3 class="subtitles-text proceedings-text">Proceso de firma</h3>
                                </div>
                            </div>
                            <div class="row signature-end">
                                <div class="card signature" style="width: 20rem;">
                                    <p class="signature-number">1</p>
                                    <div class="card-body signature">
                                        <div id="formato"></div>
                                        <p class="subtable">Descarga el archivo</p>
                                        <div class="col-md-12 boton">
                                            <button typ="button" class="btn new-button" onclick="downloadFile()"><i
                                                        class="fa-solid fa-file-pdf fa-2xl" style="color: #bb2d3b;"></i>
                                            </button>
                                            <a id="downloadLink"
                                               href="{{ path('expedientes-documento-expediente') ~ '?idflujoexpediente=' ~ idflujoexpediente }}"
                                               class="d-none" download>Descargar</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card signature large-card" style="width: 60rem;">
                                    <p class="signature-number">2</p>
                                    <div class="card-body signature">
                                        <p class="subtable">Subir el archivo firmado</p>
                                        <form id="uploadPDF" enctype="multipart/form-data"
                                              class="needs-validation signature" novalidate>
                                            <input type="file" name="file" class="form-control text-center" required>
                                            <div class="invalid-feedback">Selecciona un archivo</div>
                                            <p id="mensajeError">
                                            <p>
                                                <!--<button typ="button" class="new-button" onclick="downloadFile()"><i class="fa-solid fa-check fa-lg" style="color: #115fd1;"></i></button> -->
                                                <button type="submit" class="btn new-button signature" id="submitPDF"><i
                                                            class="fa-solid fa-arrow-up-from-bracket fa-lg"
                                                            style="color: #115fd1;"></i></button>
                                        </form>
                                    </div>
                                </div>
                                <div class="card signature d-none after-signed-document" style="width: 25rem;">
                                    <p class="signature-number">3</p>
                                    <div class="card-body signature">
                                        <p class="subtable">Visualizar archivos firmados</p>
                                        <div class="col-md-12 boton">
                                            <button type="button" class="new-button"
                                                    onclick="getFlowSignedDocumentTable()" data-toggle="modal"
                                                    data-target="#modal-flow-signed-document-table">
                                                <i class="fa-solid fa-file-pdf fa-2xl" style="color: #bb2d3b;"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card signature large-card d-none" id="mark-as-delivery-container"
                                     style="width: 60rem; height: 22rem;">
                                    <p class="signature-number">4</p>
                                    <div class="card-body signature">
                                        <p class="subtable">Marcar como entregado</p>
                                        <div class="form-floating">
                                            <textarea class="form-control" placeholder="Pon tus observaciones aquí"
                                                      id="observations-textarea" style="height: 100px"></textarea>
                                            <label for="floatingTextarea2">Observaciones</label>

                                        </div>
                                        <button id="button-delivered" type="button" class="btn btn-success"
                                                onclick="setDeliverStatus()">Terminar flujo</i></button>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mod " id="modal-visor-documentos" tabindex="-1" aria-labelledby="" aria-hidden="true">
        <div class="mod-dialog">
            <div class="modal-content" style="border-radius:10px">
                <div class="modal-header bg-primary">
                    <h1 class="modal-title fs-5" id="modalDocumentoTitle"></h1>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

                <div class="modal-body" id="modal-visor-body">

                    <div id="documento"></div>

                </div>


            </div>
        </div>
    </div>

    <div class="mod " id="modal-flow-signed-document-table" tabindex="-1" aria-labelledby="" aria-hidden="true">
        <div class="mod-dialog">
            <div class="modal-content" style="border-radius:10px">
                <div class="modal-header bg-primary">
                    <h1 class="modal-title fs-5">Documentos Firmados</h1>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

                <div class="modal-body">
                    <div id="flow-signed-document-table-container"></div>
                </div>


            </div>
        </div>
    </div>

    <div class="mod " id="modal-refuse-order" tabindex="-1" aria-labelledby="" aria-hidden="true">
        <div class="mod-dialog">
            <div class="modal-content" style="border-radius:10px">
                <div class="modal-header bg-primary">
                    <h1 class="modal-title fs-5">Rechazar orden</h1>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

                <div class="modal-body">
                    <div id="refuse-form-container"></div>
                </div>


            </div>
        </div>
    </div>



    <script>


        var isSignedDocument = 0;
        const studentMap = new Map();
        const graduationMap = new Map();

        document.querySelectorAll('.collapse').forEach(ocurrence => {
            ocurrence.addEventListener('show.bs.collapse', function (event) {

                $(".collapse").removeClass("show");
                $(".btn-collapse").removeClass("bg-primary");
                $(".btn-collapse").removeClass("text-white");

                idDString = event.target.id;
                match = idDString.match(/etapa-(\d+)/);

                if (match) {
                    extractedNumber = match[1];
                    number = parseInt(extractedNumber, 10);
                    $("#collapse-button-" + number).addClass("bg-primary");
                    $("#collapse-button-" + number).addClass("text-white");
                }

                {% if Flujoexpediente.fechaentrega != null %}
                $("#button-delivered").removeClass("btn-success");
                $("#button-delivered").addClass("btn-warning");
                $("#button-delivered").text("Actualizar flujo");
                {% endif %}

            })
        })

        $(document).ready(function () {

            if ("{{ idflujoexpediente }}" != "") $("#flujo-expediente-id").val("{{ idflujoexpediente }}");
            else $("#flujo-expediente-id").val("0");

            {% if flowDocuments|length > 0 %}
            $(".after-signed-document").removeClass("d-none");
            {% endif %}

            obtenerFormularioAgregarCliente();
            obtenerFormularioExamenVisual();
            obtenerOrdenLaboratorio();
            getFlowSalesTable()
            getFinalSubjetivePreview()

            {% if etapa >= 7 %}
            $("#mark-as-delivery-container").removeClass("d-none");
            {% endif %}

            {% if etapa >= 5 %}
            $("#etapa-5").collapse('show');
            {% else %}
            $("#etapa-{{ etapa }}").collapse('show');
            {% endif %}

            for (i = 1; i <= 5; i++) {

                if (3 == parseInt("{{ etapa }}", 10) && i == 4) continue;

                if (i > parseInt("{{ etapa }}", 10)) $("#tab" + i).addClass("d-none");

            }


            {% if Flujoexpediente.observaciones is defined %}

            $("#observations-textarea").val("{{ Flujoexpediente.observaciones }}");

            {% endif %}

            $('#modal-visor-documentos').on('hidden.bs.modal', function () {
                if (isSignedDocument == 1) $("#modal-flow-signed-document-table").modal("show");
            })

        });

        var etapa = parseInt("{{ etapa }}", 10);

        function obtenerFormularioAgregarCliente() {

            var idflujoexpediente = $("#flujo-expediente-id").val();


            $.ajax({
                url: "{{ path('expedientes-agregar-cliente') }}?idflujoexpediente=" + idflujoexpediente,
                type: 'GET',
                //beforeSend: loadingGif("etapa-"+(etapa-1)),
                dataType: "html"
            }).done(function (html) {
                $("#datospersonales").html(html);


            }).fail(function () {
                alert("error");
            });
        }

        function getFlowSalesTable() {

            var idflujoexpediente = $("#flujo-expediente-id").val();

            $.ajax({
                url: "{{ path('expedientes-get-flow-sales-table') }}",
                type: 'POST',
                data: {idflujoexpediente: idflujoexpediente},
                beforeSend: loadingGif("flow-sales-table-container"),
                dataType: "html"
            }).done(function (html) {
                $("#flow-sales-table-container").html(html);
            }).fail(function () {
                alert("error");
            });
        }

        function obtenerFormularioExamenVisual() {

            var idflujoexpediente = $("#flujo-expediente-id").val();
            var curGraduationSelection = $("#select-graduation").val();
            var graduationId = (curGraduationSelection) ? curGraduationSelection : -1;

            $.ajax({
                url: "{{ path('expedientes-agregar-graduacion') }}",
                type: 'POST',
                data: {idflujoexpediente: idflujoexpediente, graduationId: graduationId},
                //beforeSend: loadingGif("etapa-"+(etapa-1)),  
                dataType: "html"
            }).done(function (html) {
                $("#examenvisual").html(html);


            }).fail(function () {
                alert("error");
            });
        }

        function obtenerFormularioOrdenLaboratorio(idordenlaboratorio = 0, firma = 0) {

            if (firma == 0) changeButton("agregarorden", 0, 1);
            else changeButton(idordenlaboratorio, 0, 1);

            var idflujoexpediente = $("#flujo-expediente-id").val();
            $("#idordenlaboratorio").val(idordenlaboratorio);

            //var idstockventa = $("#select-productos").val();

            //console.log(idstockventa);

            $.ajax({
                url: "{{ path('expedientes-agregar-ordenlaboratorio') }}",

                data: {
                    idflujoexpediente: idflujoexpediente,
                    idordenlaboratorio: idordenlaboratorio/*,idstockventa:idstockventa*/
                },

                beforeSend: loadingGif("ordenlaboratorio"),

                dataType: "html"
            }).done(function (html) {

                if (firma == 0) changeButton("agregarorden", 1, 1);
                else changeButton(idordenlaboratorio, 1, 1);

                $("#ordenlaboratorio").html(html);


            }).fail(function () {
                alert("error");
            });
        }

        function eliminarOrdenLaboratorio(idordenlaboratorio = 0, refuse = 0) {
            const alertMsg = (refuse == 0) ? "Se eliminará la venta del flujo" : "La orden se marcará como rechazada"

            let refuseReason = $("#refuse-reason-textarea").val();

            console.log(refuseReason);

            if (refuseReason != '' || idordenlaboratorio == 0) {
                Swal.fire({
                    title: '¿Está seguro?',
                    text: alertMsg,
                    type: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#28B463',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Aceptar',
                    cancelButtonText: 'Cancelar'
                }).then((result) => {
                    if (result.value) {
                        changeButton("borrar-" + idordenlaboratorio, 0, 1);

                        $.ajax({
                            url: "{{ path('expedientes-eliminar-ordenlaboratorio') }}",

                            data: {idordenlaboratorio: idordenlaboratorio, refuse: refuse, refuseReason: refuseReason},

                            dataType: "html"
                        }).done(function (html) {
                            if (idordenlaboratorio = 1) $("#modal-refuse-order").modal("hide");
                            changeButton("borrar-" + idordenlaboratorio, 1, 1);
                            obtenerOrdenLaboratorio();
                            getFlowSalesTable()


                        }).fail(function () {
                            alert("error");
                        });
                    }
                })
            } else {
                Swal.fire({
                    title: 'Hubo un problema',
                    text: "Necesitas dar una razón para rechazar la orden",
                    type: 'warning',
                })
            }

        }

        function obtenerOrdenLaboratorio() {

            var idflujoexpediente = $("#flujo-expediente-id").val();


            $.ajax({
                url: "{{ path('expedientes-obtener-ordenlaboratorio') }}",

                data: {idflujoexpediente: idflujoexpediente},

                beforeSend: loadingGif("tablaordenlaboratorio"),

                dataType: "html"
            }).done(function (html) {
                $("#tablaordenlaboratorio").html(html);


            }).fail(function () {
                alert("error");
            });
        }

        function cambiarEtapa(etapa, firma = 0) {

            idflujoexpediente = parseInt("{{ idflujoexpediente }}", 10);

            if (firma == 1) changeButton("firmarorden", 0, 1);

            $.ajax({
                url: "{{ path('expedientes-cambiar-etapa') }}",

                data: {idflujoexpediente: idflujoexpediente, etapa: etapa},

                dataType: "html",

            }).done(function (html) {

                if (firma == 1) changeButton("firmarorden", 1, 1);
                showFormulario(etapa);

            }).fail(function () {
                alert("error");
            });
        }

        function showFormulario(etapa) {
            $("#tab" + etapa).removeClass("d-none");

            if (etapa == 3) $("#tab4").removeClass("d-none");

            $("#etapa-" + etapa).collapse('show');
        }

        function buscarCotizacion() {

            var numeroFolio = quitarFormato($("#folio-venta").val());

            var idempresa = 1;

            changeButton("buscar-cotizacion-button", 0, 1);

            $.ajax({
                method: "POST",
                data: {numeroFolio: numeroFolio, idempresa: idempresa},
                url: "{{ path('buscar-venta') }}",
                beforeSend: function (xhr) {

                }
            }).done(function (response) {
                //console.log(response);
                changeButton("buscar-cotizacion-button", 1, 1);

                if (response.exito) {
                    $("#saleid").val(response.venta.idventa);
                    agregarVenta(response.venta.idventa)
                } else {
                    $("#mensajeErrorVenta").text(response.msj);
                    $("#mensajeErrorVenta").css('color', 'red');
                    $("#folio-venta").css('color', 'red');

                    /*Swal.fire(
                        "Error al buscar el ticket: " ,
                        response.msj ,
                        'warning'
                    )*/
                }

            }).fail(function (jqXHR, textStatus) {

                $("#mensajeErrorVenta").text(JSON.stringify(jqXHR) + JSON.stringify(textStatus));
                /*Swal.fire(
                        "Error al buscar la venta: " ,
                        JSON.stringify(jqXHR)+JSON.stringify(textStatus) ,
                        'warning'
                )*/
            });


        }


        function agregarVenta(id) {

            idflujoexpediente = parseInt("{{ idflujoexpediente }}", 10);

            $.ajax({
                method: "POST",
                data: {idventa: id, idflujoexpediente: idflujoexpediente},
                url: "{{ path('expedientes-agregar-venta') }}",
                //beforeSend: loadingGif("etapa-"+(etapa-1)),

            }).done(function (response) {

                if (response.exito) {
                    //obtenerFormularioOrdenLaboratorio();
                    obtenerOrdenLaboratorio();
                    getFlowSalesTable()
                    cambiarEtapa(4);

                    $("#mensajeErrorVenta").text('Venta agregada exitosamente');
                    $("#mensajeErrorVenta").css('color', 'green');
                    $("#folio-venta").css('color', 'green');
                    /*Swal.fire(
                        '¡Listo!',
                        'Venta agregada exitosamente',
                        'success'
                    )*/

                } else {
                    $("#mensajeErrorVenta").text(response.msj);
                    $("#mensajeErrorVenta").css('color', 'red');
                    $("#folio-venta").css('color', 'red');
                    /*Swal.fire(
                        "Error al buscar el ticket: " ,
                        response.msj ,
                        'warning'
                    )*/
                }

            }).fail(function (jqXHR, textStatus) {

                $("#mensajeErrorVenta").text(JSON.stringify(jqXHR) + JSON.stringify(textStatus));

                /*Swal.fire(
                        "Error al buscar la venta: " ,
                        JSON.stringify(jqXHR)+JSON.stringify(textStatus) ,
                        'warning'
                )*/
            });

        }

        function documentoExpediente() {

            var idflujoexpediente = $("#flujo-expediente-id").val();

            $.ajax({
                url: "{{ path('expedientes-documento-expediente') }}",
                data: {idflujoexpediente: idflujoexpediente},
                //beforeSend: loadingGif("etapa-"+(etapa-1)),
                dataType: "html"
            }).done(function (html) {
                $("#formato").html(html);


            }).fail(function () {
                alert("error");
            });
        }

        function downloadFile() {
            var downloadLink = document.getElementById('downloadLink');
            downloadLink.click();
        }

        $("#uploadPDF").submit(function (e) {
            e.preventDefault();

            changeButton("submitPDF", 0, 1);

            var form = e.target;
            var formData = new FormData(form);

            var idflujoexpediente = $("#flujo-expediente-id").val();

            var url = $("#url-ventas-upload-sale-document").val();
            saleid = $("#saleid").val();

            $.ajax({
                url: "{{ path('upload_file') }}?idflujoexpediente=" + idflujoexpediente + "&tipoarchivo=expediente",
                type: 'POST',
                data: formData,
                success: function (response) {
                    $('#submitPDF').prop("disabled", false);
                    $('#submitPDF').removeClass("disabled");


                    if (response.exito) {
                        // Clear validation state and show success message
                        $(form).removeClass('was-validated');
                        $(form).addClass('needs-validation');

                        $("#mensajeError").text("Documento subido correctamente");
                        $("#mensajeError").css('color', 'green');

                        setTimeout(function () {
                            $("#mensajeError").text('');
                        }, 1500);

                        $(".after-signed-document").removeClass("d-none");

                        clientName = $("#client-name").val();

                        formData.append("document-categories", "PRO 22 A - " + clientName);

                        $.ajax({

                            url: url + "?saleid=" + saleid,
                            type: 'POST',
                            data: formData,
                            success: function (response) {
                                //console.log(response);
                            },
                            cache: false,
                            contentType: false,
                            processData: false
                        });
                        /*Swal.fire(
                            '¡Listo!',
                            'Documento cargado exitosamente',
                            'success'
                        );*/
                    } else {
                        // Show validation error message
                        $(form).addClass('was-validated');
                        $("#mensajeError").text("");
                        /*Swal.fire(
                            "Error al subir el documento",
                            response.msj,
                            'warning'
                        );*/
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    // Handle error response
                    $("#mensajeError").text(textStatus + " - " + errorThrown);
                    /*Swal.fire(
                        "Error al imprimir:",
                        textStatus + " - " + errorThrown,
                        'warning'
                    );*/
                },
                cache: false,
                contentType: false,
                processData: false
            });


        });

        function mostrarDocumento(nombredocumento, tipodocumento, firma = -1) {

            $("#documento").html("");
            $("#modalDocumentoTitle").text(tipodocumento);

            var idflujoexpediente = $("#flujo-expediente-id").val();

            if (firma == 1) {
                $("#modal-flow-signed-document-table").modal("hide");
                isSignedDocument = 1;
            } else isSignedDocument = 0;

            $.ajax({
                url: "{{ path('abrir-documento-examen') }}",
                data: {idflujoexpediente: idflujoexpediente, nombredocumento: nombredocumento, firma: firma},
                //beforeSend: loadingGif("tablaDocumentos"),
                dataType: "html"
            }).done(function (html) {
                $("#documento").html(html);


            }).fail(function () {
                alert("error");
            });
        }

        function getAnamnesis() {

            $("#documento").html("");
            $("#modalDocumentoTitle").text("Anamnesis");

            var graduationId = $("#idgraduacion").val();

            $.ajax({
                url: "{{ path('expedientes-get-anamnesis') }}",
                data: {graduationId: graduationId},
                beforeSend: loadingGif("documento"),
                dataType: "html"
            }).done(function (html) {
                $("#documento").html(html);


            }).fail(function () {
                alert("error");
            });
        }


        function setDeliverStatus() {

            clinicalRecordId = $("#flujo-expediente-id").val();

            observations = $("#observations-textarea").val();

            changeButton("button-delivered", 0, 1);

            $.ajax({
                url: "{{ path('expedientes-set-deliver-status') }}",
                data: {clinicalRecordId: clinicalRecordId, observations: observations},
                dataType: "json"
            }).done(function (response) {
                $("#clinical-record-flow-title").text("EXPEDIENTE CLÍNICO - ENTREGADO EL " + response.deliverDate);
                Swal.fire('Se han entregado los productos de este flujo', '', 'success')
                changeButton("button-delivered", 1, 1);
                $("#button-delivered").removeClass("btn-success");
                $("#button-delivered").addClass("btn-warning");
                $("#button-delivered").text("Actualizar flujo");

            });

        }

        function deleteFlowSale(flowSaleId) {
            Swal.fire({
                title: '¿Está seguro?',
                text: "Se eliminará la venta del flujo",
                type: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#28B463',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Aceptar',
                cancelButtonText: 'Cancelar'
            }).then((result) => {
                if (result.value) {
                    $.ajax({
                        url: "{{ path('expedientes-delete-flow-sale') }}",
                        data: {flowSaleId: flowSaleId},
                        dataType: "json"
                    }).done(function (response) {
                        if (response.success) {
                            getFlowSalesTable()
                            obtenerOrdenLaboratorio();
                        } else console.log(response)
                    }).fail(function () {
                        alert("error");
                    });
                }
            })
        }

        function getFlowSignedDocumentTable() {

            flowId = $("#flujo-expediente-id").val();

            $.ajax({
                url: "{{ path('expedientes-get-flow-signed-document-table') }}",
                data: {flowId: flowId},
                dataType: "html",
                beforeSend: loadingGif("flow-signed-document-table-container"),
            }).done(function (html) {
                $("#flow-signed-document-table-container").html(html);
            });
        }

        function eliminarDocumentos(documentId = -1, signDocument = -1) {
            Swal.fire({
                title: '¿Está seguro?',
                text: "Se eliminará el documento",
                type: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28B463',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Aceptar'
            }).then((result) => {
                if (result.value) {

                    $.ajax({
                        url: "{{ path('eliminar-documento-examen') }}",
                        method: "POST",
                        data: {documentId: documentId, signDocument: signDocument},
                        success: function (response) {
                            if (response.success) {
                                if (signDocument == 1) getFlowSignedDocumentTable()
                                else obtenerTablaDocumentos();
                            }
                        },

                    });
                }
            })
        }

        function getRefuseForm(orderId) {

            $.ajax({
                url: "{{ path('expedientes-get-refuse-form') }}",
                data: {orderId: orderId},
                dataType: "html",
                beforeSend: loadingGif("refuse-form-container"),
            }).done(function (html) {
                $("#refuse-form-container").html(html);
            });
        }

        function getFinalSubjetivePreview() {

            var idflujoexpediente = $("#flujo-expediente-id").val();

            $.ajax({
                url: "{{ path('expedientes-get-final-subjetive-preview') }}",
                data: {idflujoexpediente: idflujoexpediente},
                beforeSend: loadingGif("subjetiva-final-preview"),
                dataType: "html"
            }).done(function (html) {
                $("#subjetiva-final-preview").html(html);
            }).fail(function () {
                alert("error");
            });
        }

    </script>

{% endblock %}