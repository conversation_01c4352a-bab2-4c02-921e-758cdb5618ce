{% block content %}
<script src="{{asset('theme/material-dashboard-master/assets/js/core/bootstrap-material-design.min.js')}}"></script>
<style>
    .container-docs {
        color: black;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 12px;
    }
    .txt-docs {
        color: #1A2649;;
        border-bottom: 1px solid #3A5FAC;
    }
    table {
        width: 100%;
        border: 1px solid #000;
    }
    th, td {
        width: 25%;
        text-align: left;
        vertical-align: top;
        border: 1px solid #000;
        border-collapse: collapse;
        padding: 0.3em;
        caption-side: bottom;
    }
    caption {
        padding: 0.3em;
        color: #fff;
        background: #ADB8DC;
        color: black;
        font-family: Arial, Helvetica, sans-serif;
    }
    th {
        background: #eee;
    }
    .container-docs-padre-4 {
        margin-top: 15px;
    }
    .container-docs-padre-5 {
        margin-top: 15px;
        margin-bottom: 15px;
    }
    .container-docs-padre-6 {
        margin-top: 15px;
    }
    .container-docs-padre-7 {
        margin-top: 15px;
    }
    .text-center {
        text-align: center;
    }
    .section-6-margin {
        margin-bottom: 15px;
    }
    .line-firma-docs td{
        height: 30px;
        padding-top:50px;
        align-items: end;
    }
</style>

<div class="container-docs">
    <div class="container-docs-padre">
        <div class="container-docs-padre-1" style="text-align:center;">
            <img src="{{logo}}" width="300px">
        </div>
        <div class="container-docs-padre-2">
            <h3 class="txt-docs">PRO 22 A</h3> 
            <table class="table caption-top">
                <tbody style="">
                    <tr class="txt-border-docs" style="">
                        <td><strong>Folio: </strong></td>
                        <td>
                            {% for index, Sale in sales %}
                                {{Sale.folio}}{% if sales|length - 1 != index %}/{% endif %}
                            {% endfor %}
                        </td>
                    </tr>

                    <tr>
                        <td><strong>Fecha: </strong></td>
                        <td>{{sales[0].fechacreacion|date('d/m/Y')}} </td>
                    </tr>

                    {% if flujoexpediente.clienteIdcliente.numeroempleado != "" %}
                    <tr>
                        <td><strong>Número de trabajador: </strong></td>
                        <td>{{flujoexpediente.clienteIdcliente.numeroempleado}} </td>
                    </tr>
                    {% endif %}

                    <tr>
                        <td><strong>Procedencia: </strong></td>
                        <td>{{flujoexpediente.clienteIdcliente.entidadfederativa}} </td>
                    </tr>
                    {% if flujoexpediente.clienteIdcliente.empresaclienteIdempresacliente.nombre is defined %}
                    <tr>
                        <td><strong>Empresa: </strong></td>
                        <td>{{flujoexpediente.clienteIdcliente.empresaclienteIdempresacliente.nombre}} </td>
                    </tr>
                    {% endif %}
                    {% if flujoexpediente.clienteIdcliente.unidadIdunidad.nombre is defined %}
                    <tr>
                        <td><strong>Unidad: </strong></td>
                        <td>{{flujoexpediente.clienteIdcliente.unidadIdunidad.nombre}} </td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
        <div class="container-docs-padre-3">
            <h3 class="txt-docs"> Expediente clínico</h3>
            <table class="table">

                <tr>
                    <td><strong>Tipo de cliente: </strong></td>
                    <td>{{flujoexpediente.clienteIdcliente.tipocliente}} </td>
                </tr>
                <tr>
                    <td><strong>Nombre titular: </strong></td>
                    <td>{{flujoexpediente.clienteIdcliente.nombre}} {{flujoexpediente.clienteIdcliente.apellidopaterno}} {{flujoexpediente.clienteIdcliente.apellidomaterno}} </td>
                </tr>
                <tr>
                    <td><strong>Ocupación o puesto: </strong></td>
                    <td>{{flujoexpediente.clienteIdcliente.ocupacion}} </td>
                </tr>
                <tr>
                    <td><strong>Genero: </strong></td>
                    <td>{{flujoexpediente.clienteIdcliente.genero}} </td>
                </tr>
                <tr>
                    <td><strong>Edad: </strong></td>
                    <td>{{flujoexpediente.clienteIdcliente.edad}} años</td>
                </tr>
                <tr>
                    <td><strong>Domicilo: </strong></td>
                    <td>{{flujoexpediente.clienteIdcliente.calle}} #{{flujoexpediente.clienteIdcliente.numero}}, 
                    col. {{flujoexpediente.clienteIdcliente.colonia}}, {{flujoexpediente.clienteIdcliente.alcaldia}}, 
                    {{flujoexpediente.clienteIdcliente.entidadfederativa}}.
                    </td>
                </tr>
                <tr>
                    <td><strong>Correo: </strong></td>
                    <td>{{flujoexpediente.clienteIdcliente.email}} </td>
                </tr>
                <tr>
                    <td><strong>Cel/Tel: </strong></td>
                    <td>{{flujoexpediente.clienteIdcliente.telefono}} </td>
                </tr>

                <tr>
                    <td><strong>Observaciones: </strong></td>
                    <td>{{flujoexpediente.graduacionIdgraduacion.anamnesis}} </td>
                </tr>

            </table>
        </div>
        <div class="container-docs-padre-4">
            <!-- GRADUACIÓN PREVIA -->
            <table class="table">
                <td>
                    <table class="table caption-top">
                        <caption >Graduación previa</caption>
                        <thead>
                            <th>Rx ant</th>
                            <th>Esfera</th>
                            <th>Cilindro</th>
                            <th>Eje</th>
                            <th>Add</th>
                            <th>AV lejos</th>
                            <th>AV cerca</th>
                        </thead>
                        <tbody>
                            <tr>
                                <td class = "text-center">OD</td>
                                <td class = "text-center">{{flujoexpediente.graduacionIdgraduacion.gpesferaod}}</td>
                                <td class = "text-center">{{flujoexpediente.graduacionIdgraduacion.gpcilindrood}}</td>
                                <td class = "text-center">{{flujoexpediente.graduacionIdgraduacion.gpejeod}}</td>
                                <td rowspan="2" class = "text-center">{{flujoexpediente.graduacionIdgraduacion.gpaddod}}</td>
                                <td class = "text-center">{{flujoexpediente.graduacionIdgraduacion.gpavlejosod}}</td>
                                <td class = "text-center">{{flujoexpediente.graduacionIdgraduacion.gpavcercaod}}</td>
                            </tr>

                            <tr>
                                <td class = "text-center">OI</td>
                                <td class = "text-center">{{flujoexpediente.graduacionIdgraduacion.gpesferaoi}}</td>
                                <td class = "text-center">{{flujoexpediente.graduacionIdgraduacion.gpcilindrooi}}</td>
                                <td class = "text-center">{{flujoexpediente.graduacionIdgraduacion.gpejeoi}}</td>
                                <td class = "text-center">{{flujoexpediente.graduacionIdgraduacion.gpavlejosoi}}</td>
                                <td class = "text-center">{{flujoexpediente.graduacionIdgraduacion.gpavcercaoi}}</td>
                            </tr>

                        </tbody>

                    </table>
                </td>
                <td>
                
                    <table class="table caption-top">
                        {% if flujoexpediente.graduacionIdgraduacion.tipolente == '2' %}

                            <caption >Datos lente de contacto previo</caption>
                            <tr>
                                <td><strong>Tipo de lente de contacto: </strong></td>
                                <td>
                                    {% if flujoexpediente.graduacionIdgraduacion.tipolentecontacto == '1' %}
                                        Blando
                                    {% else %}
                                        Rígido
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Curva base: </strong></td>
                                <td>{{flujoexpediente.graduacionIdgraduacion.cb}}</td>
                            </tr>
                            <tr>
                                <td><strong>Diámetro: </strong></td>
                                <td>{{flujoexpediente.graduacionIdgraduacion.diam}}</td>
                            </tr>
                            <tr>
                                <td><strong>Observaciones: </strong></td>
                                <td>{{flujoexpediente.graduacionIdgraduacion.apobservaciones}}</td>
                            </tr>

                        {% else %}

                            <caption >Datos armazón previo</caption>
                            <tr>
                                <td><strong>Material: </strong></td>
                                <td>{{flujoexpediente.graduacionIdgraduacion.materialIdmaterial}}</td>
                            </tr>
                            <tr>
                                <td><strong>Diseño: </strong></td>
                                <td>{{flujoexpediente.graduacionIdgraduacion.disenolenteIddisenolente}}</td>
                            </tr>
                            <tr>
                                <td><strong>Tratamiento: </strong></td>
                                <td>{{flujoexpediente.graduacionIdgraduacion.tratamientoIdtratamiento}}</td>
                            </tr>
                            <tr>
                                <td><strong>Observaciones: </strong></td>
                                <td>{{flujoexpediente.graduacionIdgraduacion.apobservaciones}}</td>
                            </tr>
                        {% endif %}
                    </table>
                </td>
            </table>
        </div>
        <div class="container-docs-padre-5">
            <table class="table caption-top">
                <caption >Agudeza visual</caption>
                <thead>
                    <th>AV s/Rx</th>
                    <th>Lejos</th>
                    <th>Cerca</th>
                    <th>CV</th>
                </thead>
                <tbody>
                <tr>
                    <td class = "text-center">OD</td>
                    <td class = "text-center">{{flujoexpediente.graduacionIdgraduacion.avsrxlejosod}}</td>
                    <td class = "text-center">{{flujoexpediente.graduacionIdgraduacion.avsrxcercaod}}</td>
                    <td class = "text-center">{{flujoexpediente.graduacionIdgraduacion.avsrxcvod}}</td>
                </tr>
                <tr>
                    <td class = "text-center">OI</td>
                    <td class = "text-center">{{flujoexpediente.graduacionIdgraduacion.avsrxlejosoi}}</td>
                    <td class = "text-center">{{flujoexpediente.graduacionIdgraduacion.avsrxcercaoi}}</td>
                    <td class = "text-center">{{flujoexpediente.graduacionIdgraduacion.avsrxcvoi}}</td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="container-docs-padre-6">
            {% for orden in ordeneslaboratorio %}
                {% if productos[orden.idordenlaboratorio] is defined or orden.armazoncliente == "1" %}
                    <table class="table caption-top">
                        <caption >
                            {% if orden.armazoncliente == "1" %}
                            Receta final para armazón propio
                            {% else %}
                            Receta final para lente con código:
                                {% if productos[orden.idordenlaboratorio][0]["codigobarras"] != "" %}
                                    {{ productos[orden.idordenlaboratorio][0]["codigobarras"] }}
                                {% elseif productos[orden.idordenlaboratorio][0]["codigobarrasuniversal"] != "" %}
                                    {{ productos[orden.idordenlaboratorio][0]["codigobarrasuniversal"] }}
                                {% else %}
                                    {{ productos[orden.idordenlaboratorio][0]["modelo"] }}
                                {% endif %}
                            {% endif %}
                        </caption>
                        <thead>
                            <th></th>
                            <th>Esfera</th>
                            <th>Cilindro</th>
                            <th>Eje</th>
                            <th>AV lejos</th>
                            <th>AV cerca s/Add</th>
                            <th>AV cerca c/Add</th>
                            <th>Add</th>
                            <td class = "text-center">
                            {% if orden.tipoorden == "1" %}
                            DIP: {{ orden.dip }}
                            {% endif %}
                            </td>
                        </thead>
                        <tbody class="section-6-margin">
                            <tr>
                                <td class = "text-center">OD</td>
                                <td class = "text-center">{{ orden.esferaod }}</td>
                                <td class = "text-center">{{ orden.cilindrood }}</td>
                                <td class = "text-center">{{ orden.ejeod }}</td>
                                <td class = "text-center">{{ flujoexpediente.graduacionIdgraduacion.avlodsf }}</td>
                                <td class = "text-center">{{ flujoexpediente.graduacionIdgraduacion.avcsaodsf }}</td>
                                <td class = "text-center">{{ flujoexpediente.graduacionIdgraduacion.avccaodsf }}</td>
                                <td rowspan="2" class = "text-center">{{ orden.addordenlaboratorio }}</td>
                                <td class = "text-center">
                                
                                {% if orden.tipoorden == "1" %}
                                AO: {{ orden.ao }}
                                {% else %}
                                CB: {{ orden.cb }}
                                {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class = "text-center">OI</td>
                                <td class = "text-center">{{ orden.esferaoi }}</td>
                                <td class = "text-center">{{ orden.cilindrooi }}</td>
                                <td class = "text-center">{{ orden.ejeoi }}</td>
                                <td class = "text-center">{{ flujoexpediente.graduacionIdgraduacion.avloisf }}</td>
                                <td class = "text-center">{{ flujoexpediente.graduacionIdgraduacion.avcsaoisf }}</td>
                                <td class = "text-center">{{ flujoexpediente.graduacionIdgraduacion.avccaoisf }}</td>
                                
                                <td class = "text-center">
                                {% if orden.tipoorden == "1" %}
                                ACO: {{ orden.aco }}
                                {% else %}
                                DIAM: {{ orden.diam }}
                                {% endif %}
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <table class="table">
                        <tr>
                            <td><strong>Marca de armazón: </strong></td>
                            <td>
                                {% if orden.armazoncliente == "1" %}
                                    Armazón propio
                                    {% else %}
                                    {{ productos[orden.idordenlaboratorio][0]["marca"] }}
                                {% endif %}
                                
                            </td>
                        </tr>
                        {% if orden.tipoorden == "1" %}
                            <tr>
                                <td><strong>Armazón: </strong></td>
                                <td>
                                {% if orden.armazoncliente == "1" %}
                                    Armazón propio, {{ orden.color }}
                                    {% else %}
                                    {{ productos[orden.idordenlaboratorio][0]["modelo"] }}, {{ orden.color }}
                                {% endif %}
                                
                                </td>
                                <tr>
                                    <td><strong>Mica: </strong></td>
                                    <td>{{ orden.disenolenteIddisenolente }}, {{ orden.materialIdmaterial }}
                                    

                                    {% if servicios[orden.idordenlaboratorio] is defined %}
                                    ,

                                        {% for servicio in servicios[orden.idordenlaboratorio] %}
                                            {{servicio}}{{ not loop.last ? ',' : '' }}
                                        {% endfor %}
                                    {% endif %}
                                    </td>
                                </tr>
                            </tr>
                        {% else %}
                            <tr>
                                <td><strong>Tipo de lente de contacto: </strong></td>
                                <td>
                                {% if orden.tipolentecontacto == "1"%}
                                Blando
                                {% else %}
                                Rígido
                                {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Modelo: </strong></td>
                                <td>
                                {{ productos[orden.idordenlaboratorio][0]["modelo"] }}
                                </td>
                            </tr>
                        {% endif %}
                            <tr>
                                <td><strong>Observaciones: </strong></td>
                                <td>{{ orden.observaciones }}</td>
                            </tr>
                    </table>
                    <br>
                {% endif %}
            {% endfor %}
        </div>
        <div class="container-docs-padre-7">
            <table class="table">
                <tr class="line-firma-docs">
                    <td class = "align-middle text-center"> __________________________ <br></td>
                    <td class = "align-middle text-center"> __________________________ <br></td>
                    <td class = "align-middle text-center"> __________________________ <br></td>
                    
                </tr>
                <tr>
                    <td class = "align-middle text-center">Nombre y firma de líder de sucursal</td>
                    <td class = "align-middle text-center">Nombre y firma de conformidad</td>
                    <td class = "align-middle text-center">Entrega de lentes fecha y firma recibido</td>
                </tr>
            </table>
        </div>
       
    </div>
    
</div>














{% endblock %}

