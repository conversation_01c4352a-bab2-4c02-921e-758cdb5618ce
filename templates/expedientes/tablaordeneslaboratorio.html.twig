<div class="row order-laboratory proceedings-text">
    <div class="col-md-10 order">
        <h3 class="subtitles-text">Productos</h3>
    </div>
    <div class="col-md-2 order">
        <button id="agregarorden" type="button" class="btn btn-success" onclick="obtenerFormularioOrdenLaboratorio()">
            Agregar</i></button>
        <button id="firmarorden" type="button" class="btn btn-primary" onclick="cambiarEtapa(5, 1)">Firmar</button>
    </div>
</div>

<div class="row order-laboratory justify-content-md-center">
    <div class="col-md-10 order table-responsive">
        <table class="table table-hover">
            <thead>
            <tr class="text-start">
                <th class="text-start text-center" scope="col">#</th>
                <th class="text-start text-center" scope="col">Código</th>
                <th class="text-start text-center" scope="col">Producto</th>
                <th class="text-start text-center" scope="col">Tipo de orden</th>
                <th class="text-start text-center" scope="col"></th>
                <th class="text-start text-center" scope="col"></th>
            </tr>
            </thead>
            <tbody id="tableBodyBuscarCliente">
            {% set index = 1 %}
            {% for orden in ordeneslaboratorio %}
                <tr>
                    <td>{{ index }}</td>
                    <td class="text-center">

                        {% if productosOrdenes[orden.idordenlaboratorio]["codigobarras"] is defined %}
                            {{ productosOrdenes[orden.idordenlaboratorio]["codigobarras"] }}
                        {% elseif productosOrdenes[orden.idordenlaboratorio]["codigobarrasuniversal"] is defined %}
                            {{ productosOrdenes[orden.idordenlaboratorio]["codigobarrasuniversal"] }}
                        {% elseif orden.armazoncliente == 1 %}
                            Armazón de cliente
                        {% else %}
                            ----
                        {% endif %}
                    </td>
                    <td class="text-center">
                        {% if productosOrdenes[orden.idordenlaboratorio]["modelo"] is defined %}
                            {{ productosOrdenes[orden.idordenlaboratorio]["modelo"] }}
                        {% elseif orden.armazoncliente == 1 %}
                            Armazón de cliente
                        {% else %}
                            ----
                        {% endif %}
                    </td>
                    <td class="text-center">
                        {% if orden.tipoorden == "1" %}
                            Armazón
                        {% else %}
                            Lente de contacto
                        {% endif %}
                    </td>
                    <td class="text-start">
                        <button type="button" class="btn new-button" id="{{ orden.idordenlaboratorio }}"
                                onclick="obtenerFormularioOrdenLaboratorio(this.id, 1)">
                            {% if orden.refusedclient == '1' %}
                                <i class="fa-solid fa-eye fa-lg" style="color: #375fd1;"></i>
                            {% elseif productosOrdenes[orden.idordenlaboratorio]["idstockventa"] is defined or orden.armazoncliente == 1 %}
                                <i class="fa-solid fa-pen fa-lg" style="color: #375fd1;"></i>
                            {% else %}
                                <i class="fa-solid fa-plus fa-lg" style="color: #008d4c;"></i>
                            {% endif %}
                        </button>
                        {% if orden.refusedclient == '0' %}
                            <button type="button" class="btn new-button" id="borrar-{{ orden.idordenlaboratorio }}"
                                    onclick="eliminarOrdenLaboratorio('{{ orden.idordenlaboratorio }}')"><i
                                        class="fa-solid fa-trash-can fa-lg" style="color: #e60000;"></i></button>
                            {% if orden.etapa >= 15 %}
                                <button type="button" class="btn new-button" id="refurse-{{ orden.idordenlaboratorio }}"
                                        onclick="getRefuseForm('{{ orden.idordenlaboratorio }}')" data-toggle="modal"
                                        data-target="#modal-refuse-order">
                                    <i class="fa-solid fa-ban fa-lg" style="color: #ff0000;"></i>
                                </button>
                            {% endif %}
                        {% endif %}
                    </td>
                    {% set index = index + 1 %}
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<script>

    $(document).ready(function () {

        {% if hasVenta and hasStockVenta %}
        $("#firmarorden").removeClass("d-none");
        {% else %}
        $("#firmarorden").addClass("d-none");
        {% endif %}

    });


</script>

