{% block content %}

{{ form_start(form, {'attr': {'id': 'FormularioAgregarGraduacion'}}) }}
{{ form_errors(form) }}
<input id="idgraduacion" type="hidden" class="form-control" value="{{idgraduacion}}" disabled>
    <div class="row justify-content-center">
        <div class="col-md-5 col-10 graduation">
            {{ form_row(form.anamnesis) }}
        </div>
        {% if anamnesisJsonExist %}
        <div class="col-md-1 col-2 graduation pt-5">
            <button type="button" class="btn btn-primary mt-5" onclick="getAnamnesis()" data-toggle="modal" data-target="#modal-visor-documentos">
                <i class="fa-solid fa-eye"></i>
            </button>
        </div>
        {% endif %}
        <div class="col-md-6 mt-md-2 mt-3 graduation">
            <div id="formularioDocumentos"></div>
            <div id="tablaDocumentos"></div>
        </div>
    </div>
    <div class="row graduation-form">
        <div class="col-md-6">
            {{ form_row(form.isglassesuser) }}
        </div>
        <div id="hasglassesnow-container" class="col-md-6">
            {{ form_row(form.hasglassesnow) }}
        </div>
    </div>
    <div id="prev-data">
        
        <div class="row graduation-form">
            <h3 class="subtitles-text proceedings-text">Graduación previa</h3>
            <table class="table">
                <thead>
                    <th>Rx ant</th>
                    <th>Esfera</th>
                    <th>Cilindro</th>
                    <th>Eje</th>
                    <th>Add</th>
                    <th>AV lejos</th>
                    <th>AV cerca</th>
                </thead>
                <tbody>
                    <tr>
                        <td class="text-center">OD</td>
                        <td>{{ form_row(form.gpesferaod) }}</td>
                        <td>{{ form_row(form.gpcilindrood) }}</td>
                        <td>{{ form_row(form.gpejeod) }}</td>
                        <td rowspan="2">{{ form_row(form.gpaddod) }}</td>
                        <td>{{ form_row(form.gpavlejosod) }}</td>
                        <td>{{ form_row(form.gpavcercaod) }}</td>
                    </tr>
                    <tr>
                        <td class="text-center">OI</td>
                        <td>{{ form_row(form.gpesferaoi) }}</td>
                        <td>{{ form_row(form.gpcilindrooi) }}</td>
                        <td>{{ form_row(form.gpejeoi) }}</td>
                        <td>{{ form_row(form.gpavlejosoi) }}</td>
                        <td>{{ form_row(form.gpavcercaoi) }}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="row graduation-form">
            <h3 class="subtitles-text proceedings-text">Datos de armazón previo</h3> 
            <div class="col-6 graduation">
                {{ form_row(form.tipolente) }}
            </div>
            <div class="col-6 graduation">
                {{ form_row(form.apobservaciones) }}
            </div>
        </div>
        <div class="row graduation-form">
            <div class="col-4 no-required graduation">
                {{ form_row(form.tipolentecontacto) }}
            </div>
            <div class="col-4 no-required graduation">
                {{ form_row(form.cb) }}
            </div>
            <div class="col-4 no-required graduation">
                {{ form_row(form.diam) }}
            </div>
        </div>

        
        <div class="row graduation-form">
            <h3 class="subtitles-text proceedings-text frame-lens">Datos de micas previas</h3> 
            <div class="col-4 no-required graduation">
                {{ form_row(form.materialIdmaterial) }}
            </div>
            <div class="col-4 no-required graduation">
                {{ form_row(form.disenolenteIddisenolente) }}
            </div>
            <div class="col-4 no-required graduation">
                {{ form_row(form.tratamientoIdtratamiento) }}
            </div>
        </div>
    </div>
    <div class="row graduation-form">
        <h3 class="subtitles-text proceedings-text">Agudeza visual sin Rx</h3>
        <table class="table">
            <thead>
                <th>AV s/Rx</th>
                <th>Lejos</th>
                <th>Cerca</th>
                <th>CV</th>
            </thead>
            <tbody>
                <tr>
                    <td class="text-center">OD</td>
                    <td>{{ form_row(form.avsrxlejosod) }}</td>
                    <td>{{ form_row(form.avsrxcercaod) }}</td>
                    <td>{{ form_row(form.avsrxcvod) }}</td>
                </tr>
                <tr>
                    <td class="text-center">OI</td>
                    <td>{{ form_row(form.avsrxlejosoi) }}</td>
                    <td>{{ form_row(form.avsrxcercaoi) }}</td>
                    <td>{{ form_row(form.avsrxcvoi) }}</td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="row graduation-form">
        <h3 class="subtitles-text proceedings-text">Subjetiva final</h3>
        <table class="table">
            <thead>
                <th></th>
                <th>Esfera</th>
                <th>Cilindro</th>
                <th>Eje</th>
                <th>AV lejos</th>
                <th>AV cerca s/Add</th>
                <th>AV cerca c/Add</th>
                <th>Add</th>
            </thead>
            <tbody>
                <tr>
                    <td class="text-center">OD</td>
                    <td>{{ form_row(form.esfodsf) }}</td>
                    <td>{{ form_row(form.cilodsf) }}</td>
                    <td>{{ form_row(form.ejeodsf) }}</td>
                    <td>{{ form_row(form.avlodsf) }}</td>
                    <td>{{ form_row(form.avcsaodsf) }}</td>
                    <td>{{ form_row(form.avccaodsf) }}</td>
                    <td rowspan="2">{{ form_row(form.addsf) }}</td>
                </tr>
                <tr>
                    <td class="text-center">OI</td>
                    <td>{{ form_row(form.esfoisf) }}</td>
                    <td>{{ form_row(form.ciloisf) }}</td>
                    <td>{{ form_row(form.ejeoisf) }}</td>
                    <td>{{ form_row(form.avloisf) }}</td>
                    <td>{{ form_row(form.avcsaoisf) }}</td>
                    <td>{{ form_row(form.avccaoisf) }}</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="row graduation-form">
        <div class="col-9 graduation">
            {{ form_row(form.sugerencias) }}
        </div>
    </div>
    <div class="row send-button personal-data-form">
        <div class="col-2 input-form">
            {{ form_row(form.Enviar) }}
        </div>
    </div>
{{ form_widget(form) }}
{{ form_end(form) }}

{% if exito %}
    <script>
        cambiarEtapa(3);
        $("#select-graduation").val('current');
        $("#select-graduation").trigger('change');
    </script>
{% endif %}

{% endblock %}

<script>
    $('#graduacion_expediente_tipolente').on('change', function() {
        //console.log( this.value );

        if ( this.value == '1') {
            $(".contact-lens").hide();
            
            $(".frame-lens").show();
        }
        else if ( this.value == '2') {
            $(".contact-lens").show();
            
            $(".frame-lens").hide();
        } else {
            $(".frame-lens").hide();
            $(".contact-lens").hide();

        }

    });

    $(document).ready(function(){
        exito = {{ exito|json_encode() }}

        $(".graduation-select").select2({width: "100%"}); 
        //console.log(exito);

        lenstype = $('#graduacion_expediente_tipolente').val();

        if ( lenstype == '1') {
            $(".contact-lens").hide();
            $('.contact-lens').val('');
            $(".frame-lens").show();
        }
        else if (lenstype == '2'){
            $(".contact-lens").show();
            $('.frame-lens').val('');
            $(".frame-lens").hide();
        } else {
            $('.contact-lens').val('');
            $('.frame-lens').val('');
            $(".frame-lens").hide();
            $(".contact-lens").hide();
        }
        
        obtenerFormularioDocumentos();
        obtenerTablaDocumentos();

        const inputs = $('.student-input')
        inputs.each(function() {
            studentMap.set($(this).attr('id'), $(this).val());
        });
        checkPrevData();

    });

    $("#FormularioAgregarGraduacion").submit(function(e) {
        e.preventDefault();
        changeButton("graduacion_expediente_Enviar");

        lenstype = $('#graduacion_expediente_tipolente').val();

        if ( lenstype == '1') {
            $(".contact-lens").hide();
            $('.contact-lens').val('');
            $(".frame-lens").show();
        }
        else if (lenstype == '2') {
            $(".contact-lens").show();
            $('.frame-lens').val('');
            $(".frame-lens").hide();
        } else {
            $('.contact-lens').val('');
            $('.frame-lens').val('');
            $(".frame-lens").hide();
            $(".contact-lens").hide();
        }

        var formData = new FormData(this);

        var idflujoexpediente = $("#flujo-expediente-id").val();
        var curGraduationSelection = $("#select-graduation").val();
        var graduationId = (curGraduationSelection) ? curGraduationSelection : -1;

        $.ajax({
            url: "{{path('expedientes-agregar-graduacion')}}?idflujoexpediente="+idflujoexpediente+"&graduationId="+graduationId,
            type: 'POST',
            data: formData,
            success: function (html) {
                changeButton("graduacion_expediente_Enviar", 1);
                getFinalSubjetivePreview()
                const prevInputs = $('.prev-grad-input')
                prevInputs.each(function() {
                    graduationMap.set($(this).attr('id'), $(this).val());
                });
                $("#examenvisual").html(html);

            },
            cache: false,
            contentType: false,
            processData: false
        });
    });

    function obtenerTablaDocumentos(){
        
        var idflujoexpediente = $("#flujo-expediente-id").val();

        $.ajax({
            url: "{{path('tabla-documentos')}}", 
            data: {idflujoexpediente:idflujoexpediente},
            beforeSend: loadingGif("tablaDocumentos"),
            dataType: "html"
        }).done(function( html ) {
            changeButton("graduacion_expediente_Enviar", 1);
            $("#tablaDocumentos").html(html);


        }).fail(function() {
            alert( "error" );
        });
    }

    function obtenerFormularioDocumentos(){
        
        $.ajax({
            url: "{{path('formulario-documentos')}}", 
            type: 'GET',
            beforeSend: loadingGif("formularioDocumentos"),
            dataType: "html"
        }).done(function( html ) {
            $("#formularioDocumentos").html(html);


        }).fail(function() {
            alert( "error" );
        });
    }

    function checkPrevData(){
        const isGlassesUser = $("#graduacion_expediente_isglassesuser").val() == "1";
        const hasGlassesNow = $("#graduacion_expediente_hasglassesnow").val() == "1";

        if (isGlassesUser) $("#hasglassesnow-container").removeClass("d-none")
        else $("#hasglassesnow-container").addClass("d-none")

        if (hasGlassesNow && isGlassesUser) {
            for (const [id, value] of graduationMap) {
                $(`#${id}`).val(value)
            }
            $("#prev-data").removeClass("d-none");
        } else {
            const prevInputs = $('.prev-grad-input')
            $("#prev-data").addClass("d-none");
            prevInputs.each(function() {
                graduationMap.set($(this).attr('id'), $(this).val());
            });
            $(".prev-grad-input").val("")
        }
    }

</script>

<style>

li{
    color:red;
}
.select2-results__option {
    color: black;
}

</style>