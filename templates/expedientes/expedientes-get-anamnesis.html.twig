<form id="update-anamnesis-form" class="needs-validation signature">
<div class="row graduation-form">
{% for question, answer in anamnesis %}
    <div class="col-xl-6 graduation">
        <label class="form-label form mt-3" for="{{answer}}">{{question}}</label>
        <input type="text" id="{{answer}}" name="{{question}}" value="{{answer}}" class="form-control rounded" required>
    </div>
{% endfor %}
    <div class="row">
        <button type="submit" class="btn btn-primary col-xl-3 mt-5 mx-auto" id="update-anamnesis-submit">
            Guardar
        </button>
    </div>
</div>
</form>

<script>
    $("#update-anamnesis-form").submit(function(e) {
    e.preventDefault();

    changeButton("update-anamnesis-submit", 0, 1);

    var form = e.target;
    var formData = new FormData(form);

    var graduationId = $("#idgraduacion").val();

    $.ajax({
        url: "{{path('expedientes-update-anamnesis')}}?graduationId=" + graduationId,
        type: 'POST',
        data: formData,
        processData: false,  // Importante para manejar FormData correctamente
        contentType: false,  // Importante para manejar FormData correctamente
        success: function(response) {
            changeButton("update-anamnesis-submit", 1, 1);
            console.log(response);
            //if (response.success) getAnamnesis();
        },
        error: function(jqXHR, textStatus, errorThrown) {
            // Manejo del error
            changeButton("update-anamnesis-submit", 1, 1);
        },
    });
});
</script>