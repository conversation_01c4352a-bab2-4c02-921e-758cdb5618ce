{% if sales|length > 0 %}
<div class="table-responsive">
    <table class="table container mt-4">
        <thead>
            <th>#</th>
            <th>Folio</th>
            <th>Productos</th>
            <th></th>
        </thead>
        <tbody>
            {% for index, sale in sales %}
            <tr>
                <td class="text-center">{{index + 1}}</td>
                <td class="text-center">{{sale.folio}}</td>
                <td>
                    <table class="table">
                        <thead>
                            <tr class="row">
                                <th class="text-start col-1">#</th>
                                <th class="text-start col-2">Marca</th>
                                <th class="text-start col-3">
                                    SKU / código de barras
                                </th>
                                <th class="text-start col-3">Modelo</th>
                                <th class="text-start col-3">Cantidad disponible</th>
                            </tr>
                        </thead>
                        <tbody>
                        {% for index, product in mappedSales[sale.idventa] %}
                            {% set quantity = (mappedQuantities[product.idstockventa] is defined) ? mappedQuantities[product.idstockventa] : 0 %}
                            <tr class="row">
                                <th class="text-start col-1">{{index + 1}}</th>
                                <td class="text-start col-2">{{product.brand}}</td>
                                <td class="text-start col-3">
                                    {% if product.sku != null %}
                                        {{product.sku}}
                                    {% else %}
                                        {{product.codigobarrasuniversal}}
                                    {% endif %}
                                </td>
                                <td class="text-start col-3">{{product.modelo}}</td>
                                <td class="text-start col-3">{{product.cantidad - quantity}} disponible(s)</td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </td>
                <td class="text-center">
                    <button type="button" class="btn" onclick = "deleteFlowSale('{{sale.idflujoexpedienteventa}}')">
                        <i class="fa-solid fa-trash-can fa-lg" style="color: #e60000;"></i>
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}