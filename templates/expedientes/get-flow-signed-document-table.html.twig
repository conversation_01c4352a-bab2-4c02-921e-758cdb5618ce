{% if flowSignedDocuments|length > 0 %}
<table class="table" id="tablaDocumentos">
    <thead>
        <th scope="col">#</th>
        <th scope="col">Fecha del documento</th>
        <th scope="col"></th>

    </thead>
    <tbody id = "tableBodyDocumentos">
        {% for index, FlowSignedDoc in flowSignedDocuments %}
            <tr>
                <td class="text-center align-middle ">{{index + 1}}</td>
                <td class="text-center align-middle ">{{FlowSignedDoc.creation|date("d/m/Y H:i")}}</td>
                <td class="text-center align-middle ">
                    <button type="button" class="btn btn-primary" onclick="mostrarDocumento('{{FlowSignedDoc.filename}}','Documento firmado',1)" data-toggle="modal" data-target="#modal-visor-documentos"><i class="fa-solid fa-eye"></i></button>
                    <button type="button" class="btn btn-danger" onclick="eliminarDocumentos('{{FlowSignedDoc.idflowsigneddocuments}}',1)"><i class="fa fa-trash"></i></button>
                </td>
            </tr>
        {% endfor %}        
    </tbody>
</table>
{% else %}
    <h4 class="text-center">No hay documentos cargados para este flujo</h4>
{% endif %}