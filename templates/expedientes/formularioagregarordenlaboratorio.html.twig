{% block content %}
	{% if isRefused == '0' %}
		{% if ordenlaboratorio.etapa != 1 and ordenlaboratorio.armazoncliente != "1" and productos is not empty %}
			{% if productosdisponibles[ordenlaboratorio.tipoorden] > 0 %}
				<div class="row order-laboratory">
					<div class="col-4 order not-required">
						<label class="form-label" for="select-productos">
							Productos disponibles
						</label>

						<select class="form-control col-2" id="select-productos">
							{% for producto in productos %}

								{% if ordenlaboratorio.tipoorden == producto.tipoproducto %}
									{% if ordenlaboratorio.idordenlaboratorio in producto.Ordenlaboratorios %}
										{% set cantidad = producto.quantity + 1 %}
									{% else %}
										{% set cantidad = producto.quantity %}
									{% endif %}
									{% for i in 1..cantidad %}
										{% if cantidad > 0 %}
											<option value={{producto.idstockventa}}>
												{% if producto.tipoproducto == "1" %}
													{{producto.marca}}
													-
													{{producto.modelo}}
													-
													{{producto.sku}}
												{% else %}
													{{producto.modelo}}
												{% endif %}
											</option>
										{% endif %}
									{% endfor %}

								{% endif %}

							{% endfor %}
						</select>

					</div>
				</div>

			{% endif %}
		{% endif %}

		{% if ordenlaboratorio.etapa != 1 and serviciosdisponibles > 0 or ordenlaboratorio.idordenlaboratorio in laboratoryOrdersService %}
			<div class="row order-laboratory">
				<div class="col-3 order not-required lente-armazon">
					<label class="form-label" for="tratamientos">
						Tratamientos
					</label>
					<fieldset id="tratamientos">
						<label class="check" for="todosServicios">
							<input type="checkbox" name="tratamiento" onclick="toggleServicios(this)" id="todosServicios">
							TODOS
						</label>
						{% for servicio in servicios %}
							{% if ordenlaboratorio.idordenlaboratorio in servicio.Ordenlaboratorios or servicio.Ordenlaboratorios == -1 or servicio.quantity > 0 %}
								<label class="check" for="{{ servicio.codigo }}">
									<input type="checkbox" id="{{ servicio.codigo }}" name="servicio" value="{{ servicio.idstockventa }}" {% if ordenlaboratorio.idordenlaboratorio in servicio.Ordenlaboratorios %} checked {% endif %}>
									{{ servicio.modelo }}
								</label>
							{% endif %}
						{% endfor %}
					</fieldset>
				</div>
			</div>

		{% endif %}

	{% endif %}

	{{ form_start(form, {'attr': {'id': 'FormularioAgregarOrdenLaboratorio'}}) }}
	{{ form_errors(form) }}
	<div class="row order-laboratory">
		<div class="col-3 order">
			{{ form_row(form.tipoorden) }}
		</div>
		<div class="col-3 order not-required mt-3">
			{{ form_row(form.tipolentecontacto) }}
		</div>
		{% if refuseReason != null %}
		<div class="col-6 ">
			<label for="refuse-reason-textarea" class="form-label required">Razón de rechazo</label>
			<textarea class="form-control" id="refuse-reason-textarea">
				{{refuseReason}}
			</textarea>
		</div>
		{% endif %}
	</div>

	<div class="row graduation-form">
		<h3 class="subtitles-text proceedings-text">Receta final</h3>
		<table class="table">
			<thead>
				<th>Rx ant</th>
				<th>Esfera</th>
				<th>Cilindro</th>
				<th>Eje</th>
				<th>Add</th>
				<th class="lente-armazon">DIP</th>
				<th class="lente-contacto"></th>
				<th>{{ form_row(form.dip) }}</th>
			</thead>
			<tbody>
				<tr>
					<td class="text-center">OD</td>
					<td>{{ form_row(form.esferaod) }}</td>
					<td>{{ form_row(form.cilindrood) }}</td>
					<td>{{ form_row(form.ejeod) }}</td>
					<td rowspan="2">{{ form_row(form.addordenlaboratorio) }}</td>
					<th class="lente-armazon">AO</th>
					<th class="lente-contacto">Curva base</th>
					<td>{{ form_row(form.ao) }}{{ form_row(form.cb) }}</td>

				</tr>
				<tr>
					<td class="text-center">OI</td>
					<td>{{ form_row(form.esferaoi) }}</td>
					<td>{{ form_row(form.cilindrooi) }}</td>
					<td>{{ form_row(form.ejeoi) }}</td>
					<th class="lente-armazon">ACO</th>
					<th class="lente-contacto">Diámetro</th>
					<td>{{ form_row(form.aco) }}{{ form_row(form.diam) }}</td>

				</tr>
			</tbody>
		</table>
	</div>
	
	<div class="row order-laboratory form-space dtd-a align-items-center lente-armazon">
		<h3 class="subtitles-text proceedings-text ">Datos de armazón</h3>
		<div class="col-4 order not-required">
			{{ form_row(form.armazoncliente) }}
		</div>
		<div class="col-8 order">
			{{ form_row(form.observaciones) }}
		</div>
	</div>

	
	<div class="row order-laboratory form-spacer lente-armazon">
		<h3 class="subtitles-text proceedings-text ">Datos de micas</h3> 
		<div class="col-6 order not-required">
			{{ form_row(form.disenolenteIddisenolente) }}
		</div>
		<div class="col-6 order not-required">
			{{ form_row(form.materialIdmaterial) }}
		</div>
	</div>

	<div class="row order-laboratory form-spacer">
		<h3 class="subtitles-text proceedings-text ">Sugerencias</h3> 
		<div class="col-12 order not-required">
			{{ form_row(form.suggestions) }}
		</div>
	</div>

	<div class="row send-button personal-data-form">
		<div class="col-2 input-form">
			{{ form_row(form.Enviar) }}
		</div>
	</div>
	{{ form_widget(form) }}
	{{ form_widget(form.Enviar, { 'label': 'Click me' }) }}
	{{ form_end(form) }}

	{% if exito %}
		<script>
			// obtenerOrdenLaboratorio();
			$("#ordenlaboratorio").html("");
			// cambiarEtapa(3);
		</script>
	{% endif %}

{% endblock %}
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>

	$('#ordenlaboratorio_tipoorden').on('change', function () {

		if (this.value == '1') {
			$(".lente-contacto").hide();
			$(".lente-contacto").addClass("d-none");
			$('.lente-armazon').removeClass("d-none");
			$('.lente-contacto').val('');
			$(".lente-armazon").show();
		} else {
			$(".lente-contacto").show();
			$('.lente-armazon').val('');
			$(".lente-armazon").hide();
			$(".lente-armazon").addClass("d-none");
			$('.lente-contacto').removeClass("d-none");
		}

	});

	$(document).ready(function () {
		exito = {{ exito|json_encode() }}
		$(".ordenl-select").select2({width: "100%"}); 
		let isRefused = "{{isRefused}}"

		orderDisabled = "{{ orderDisabled ? 'true' : 'false' }}"
		if (isRefused == '1' || orderDisabled == "true"){
			$("#ordenlaboratorio_Enviar").addClass("d-none")
			console.log(Swal);
			//var form = document.getElementById('FormularioAgregarOrdenLaboratorio');
			$(':input','#FormularioAgregarOrdenLaboratorio')
			.not(':button, :submit, :reset, :hidden')
			.prop("disabled", true)
		}

		tipoorden = $('#ordenlaboratorio_tipoorden').val();

		if (tipoorden == '1') {
			$(".lente-contacto").hide();
			$(".lente-contacto").addClass("d-none");
			$('.lente-armazon').removeClass("d-none");
			$('.lente-contacto').val('');
			$(".lente-armazon").show();
		} else {
			$(".lente-contacto").show();
			$('.lente-armazon').val('');
			$(".lente-armazon").hide();
			$(".lente-armazon").addClass("d-none");
			$('.lente-contacto').removeClass("d-none");
		}

	});

	$("#FormularioAgregarOrdenLaboratorio").submit(function (e) {
		e.preventDefault();
		changeButton("ordenlaboratorio_Enviar");
		console.log(Swal);

		var formData = new FormData(this);

		var idflujoexpediente = $("#flujo-expediente-id").val();
		var idordenlaboratorio = $("#idordenlaboratorio").val();

		var productoseleccionado = "";{% if ordenlaboratorio.etapa != 1 and ordenlaboratorio.armazoncliente != "1" %}var idstockventa = $("#select-productos").val();
		productoseleccionado = "&idstockventa=" + idstockventa;{% endif %}

		Swal.fire({
			title: '¿Estás seguro de que la información es correcta?',
			text: "No podrás cambiarla después de que el cliente firme",
			icon: 'warning',
			showCancelButton: true,
			confirmButtonColor: '#28B463',
			cancelButtonColor: '#d33',
			confirmButtonText: 'Aceptar',
			cancelButtonText: 'Cancelar'
		}).then((result) => {

			if (result.isConfirmed) {
				$.ajax({
					url: "{{ path('expedientes-agregar-ordenlaboratorio') }}?idflujoexpediente=" + idflujoexpediente + "&idordenlaboratorio=" + idordenlaboratorio + productoseleccionado,
					type: 'POST',
					data: formData,
				success: function (html) {
					changeButton("ordenlaboratorio_Enviar", 1);
					guardarServicios();
					$("#ordenlaboratorio").html(html);
					// $("#ordenlaboratorio").html("");

					obtenerOrdenLaboratorio();
					getFlowSalesTable()

				},

				cache: false,
				contentType: false,
				processData: false
				});
			} else changeButton("ordenlaboratorio_Enviar", 1);
		})
	});

	function toggleServicios(source) {
		checkboxes = document.getElementsByName('servicio');
		for (var i = 0, n = checkboxes.length; i < n; i++) {
			checkboxes[i].checked = source.checked;
		}
	}

	function guardarServicios() {

		servicios = [];

		checkboxesServicios = document.getElementsByName('servicio');

		for (var i = 0, n = checkboxesServicios.length; i < n; i++) {

			if (checkboxesServicios[i].checked) {
				servicios.push(checkboxesServicios[i].value);
			}

		}

		var idordenlaboratorio = $("#idordenlaboratorio").val();

		var url = $("#url-almacen-obtener-informacion-stock").val();

		$.ajax({
			url: "{{ path('guardar-servicios') }}",
			data: {
			servicios: servicios,
			idordenlaboratorio: idordenlaboratorio
			},
			dataType: "json",
			//beforeSend: function (xhr) { // loadingGif("contenedor-graficas");}
		}).done(function (response) {
			// $("#contenedor-graficas").html(html);
			// console.log(response.msj);

		}).fail(function () {
			alert("error");
		});

	}

</script>

<style>

li{
    color:red;
}
.select2-results__option {
    color: black;
}
</style>
