
<div class="card p-5 mb-3 mt-md-5">
    <p class="subtable">Subir documentos de examen</p>
    <form id="uploadDocumentosExamen" enctype="multipart/form-data" class="needs-validation signature" novalidate>
        <div class="row">
            <div class="col-5">
                <select name="categoriaDocumentos" id="categoriaDocumentos" form="uploadDocumentosExamen" class="form-control">
                    <option value="Armazon previo">Armazón previo</option>
                    <option value="Lensometro">Lensómetro</option>
                    <option value="Autorefractómetro">Autorefractómetro</option>
                    <option value="Receta">Receta</option>
                    <option value="Otro">Otro</option>
                </select>
            </div>
            <div class="col-5">
                <input id="file" type="file" name="file" class="form-control text-center" required>
            </div>
            <div class="col-2">
                <button type="submit" class="new-button signature btn" id="submitDocumentos">
                    <i class="fa-solid fa-arrow-up-from-bracket fa-lg" style="color: #115fd1;"></i>
                </button>
            </div>
        </div>
        <div class="invalid-feedback">Selecciona un archivo</div>
        <p id="mensajeErrorDocumento"></p>
    </form>         
</div>

<script>

    $("#uploadDocumentosExamen").submit(function(e) {

        
        e.preventDefault();

        changeButton("submitDocumentos", 0, 1);

        var form = e.target;
        var formData = new FormData(form);

        var idflujoexpediente = $("#flujo-expediente-id").val();

        $.ajax({
            
            url: "{{path('upload_file')}}?idflujoexpediente=" + idflujoexpediente+"&tipoarchivo=examen",
            type: 'POST',
            data: formData,
            success: function(response) {
                $('#submitDocumentos').prop("disabled", false);
                $('#submitDocumentos').removeClass("disabled");
                //$('#submitPDF').prop("disabled", false);

                if (response.exito) {
 
                    $("#file").val(null);

                    obtenerTablaDocumentos();

                } 
            },
            error: function(jqXHR, textStatus, errorThrown) {

                $("#mensajeErrorDocumento").text(textStatus + " - " + errorThrown);

            },
            cache: false,
            contentType: false,
            processData: false
        });
    });

</script>