{# proveedor_contacto_form.html.twig #}

{% extends 'base.html.twig' %}
lalala
{% block body %}


<script>
  
    var idproveedor = $(this).data('idproveedor');

    var urlObtenerFormularioContacto = "{{ path('add-contact-email-phone',{'idproveedor': 0}) }}";
    urlObtenerFormularioContacto=urlObtenerFormularioContacto.replace("0","{{idproveedor}}");

$(document).ready(function(){



$("#FormularioProveedorContacto").submit(function(e) {
    e.preventDefault();
    console.log("Diogenes <3");
    console.log("aquí esta,",idproveedor);

    var formData = new FormData(this);


    e.preventDefault();
    $.ajax({ 
        url: urlObtenerFormularioContacto, 
        type: 'POST', 
        data: formData,
        processData: false,
        contentType: false, 
        success: function(response) {

        },
        
        error: function(jqXHR, textStatus, errorThrown) { 
            console.log('Error: ' + errorThrown); 
        }
    });
});
})
    </script>
{% endblock %}

