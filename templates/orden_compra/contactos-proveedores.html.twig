<h4 class="mb-3">Contactos del Proveedor:</h4>
{% if contactosConEmail %}
    {% for contacto in contactosConEmail %}
        <div class="form-check mb-4">
            <input type="checkbox" class="form-check-input" id="contacto" name="contacto"
                   value="{{ contacto.idproveedorcontacto }}">
            <label class="form-check-label check" for="{{ contacto.idproveedorcontacto }}">
                {{ contacto.NombreContacto }} // {{ contacto.NombreEmail }} // {{ contacto.NombreTelefono }}
            </label>
        </div>

        <div class="col-md-4">
            <div class="form-group">
                <button type="button" id="descargarArchivo" class="btn btn-primary mt-3 float-left"
                        onclick="crearOrdenCompraPdf();">Descargar Archivo
                </button>
                <button type="button" id="sendEmail" class="btn btn-primary mt-3 float-right" onclick="sendEmail();"
                        disabled>Enviar Email
                </button>
            </div>
        </div>
    {% endfor %}
{% else %}
    <div class="form-check mb4">
        <h5 class="mb-3">No tienes contactos</h5>
        <a href="#" type="button" class="btn btn-success  mt-3 float-right" data-toggle="modal"
           data-target="#mod-formulario-contact">Agregar Contactos</a>
    </div>
{% endif %}


<div class="modal fade" id="mod-formulario-contact" tabindex="-1" role="dialog" aria-labelledby="mod-formulario-contact"
     aria-hidden="true">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Agregar Contacto Proveedor</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Cerrar">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10"></script>


<script>

    function crearOrdenCompraPdf() {

        var checkboxesMarcados = $("input[name='selectedOrdenes[]']:checked");
        var checkboxesTotal = [];
        var valoresMarcados = [];
        var checkboxesTotalContacto = [];
        var idproveedor = $("#idproveedor").val();
        $('#idproveedor').val(idproveedor);
        var idtipopago = $("#tipo-pago-anticipo").val();
        $('#tipo-pago-anticipo').val(idtipopago);
        var plazopago = $("#plazo-pago").val();
        $('#plazo-pago').val(plazopago);
        var idempresa = $("#idempresa").val();
        $('#idempresa').val(idempresa);
        var idsucursal = $("#idsucursal").val();
        $('#idsucursal').val(idsucursal);


        if (idproveedor === "-1") {
            Swal.fire("Error", "Por favor, seleccione un proveedor.", "error");
            return;
        }

        if (idtipopago === "-1") {
            Swal.fire("Error", "Por favor, seleccione un tipo de pago.", "error");
            return;
        }

        if (plazopago === "-1") {
            Swal.fire("Error", "Por favor, seleccione un plazo de pago.", "error");
            return;
        }
        checkboxesMarcados.each(function () {
            valoresMarcados.push($(this).val());
            checkboxesTotal = valoresMarcados;
        });

        var seleccionados = [];
        $('input[name="contacto"]:checked').each(function () {
            seleccionados.push($(this).val());
        });
        var datos = {
            checkboxesTotal: checkboxesTotal,
            idproveedor: idproveedor,
            idtipopago: idtipopago,
            plazopago: plazopago,
            seleccionados: seleccionados,
            idempresa: idempresa,
            idsucursal: idsucursal,
        }
        console.log(datos);

        $.ajax({
            url: "{{ path('obtener-compras-pdf') }}",
            data: datos,
            type: "POST",
            xhrFields: {
                responseType: 'blob'
            },
        }).done(function (blob) {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'ordenesCompra.pdf';
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);
        }).fail(function (jqXHR) {
            if (jqXHR.responseJSON && jqXHR.responseJSON.error) {
                Swal.fire("Error", jqXHR.responseJSON.error, "error");
            } else {
                Swal.fire("Error", "Error al generar el PDF", "error");
            }
        });
    }

    function sendEmail() {
        var checkboxesMarcados = $("input[name='selectedOrdenes[]']:checked");
        var checkboxesTotal = [];
        var valoresMarcados = [];
        var checkboxesTotalContacto = [];

        var idproveedor = $("#idproveedor").val();
        $('#idproveedor').val(idproveedor);

        var idtipopago = $("#tipo-pago-anticipo").val();
        $('#tipo-pago-anticipo').val(idtipopago);

        var plazopago = $("#plazo-pago").val();
        $('#plazo-pago').val(plazopago);

        var idempresa = $("#idempresa").val();
        $('#idempresa').val(idempresa);

        var idsucursal = $("#idsucursal").val();
        $('#idsucursal').val(idsucursal);


        checkboxesMarcados.each(function () {
            valoresMarcados.push($(this).val());
            checkboxesTotal = valoresMarcados;
        });

        var seleccionados = [];
        $('input[name="contacto"]:checked').each(function () {
            seleccionados.push($(this).val());
        });

        var datos = {
            checkboxesTotal: checkboxesTotal,
            idproveedor: idproveedor,
            idtipopago: idtipopago,
            plazopago: plazopago,
            seleccionados: seleccionados,
            idempresa: idempresa,
            idsucursal: idsucursal,
        }

        $.ajax({
            url: "{{ path('send-email-pdf') }}",
            type: 'POST',
            data: datos,
            success: function (response) {
                console.log("Email correcto");
            },
            error: function (xhr, status, error) {
                console.error("Error enviandose email: " + error);
            }
        });
    }


    $('#mod-formulario-contact').on('show.bs.modal', function (event) {


        var idproveedor = $("#idproveedor").val();
        $('#idproveedor').val(idproveedor);

        $('#FormularioProveedorContacto').data('idproveedor', idproveedor);

        $.ajax({
            url: '{{ path('agregar_contacto', {'idproveedor': 0}) }}'.replace('0', idproveedor),
            method: 'GET',
            success: function (data) {

                $('#mod-formulario-contact .modal-body').html(data);
            },
            error: function () {
                alert('Error al cargar el formulario.');
            }
        });
    });


</script>