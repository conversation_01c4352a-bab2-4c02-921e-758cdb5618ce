{% extends 'admin/layout.html.twig' %}

{% block content %}

    <link rel="stylesheet" href="{{ asset('/css/ordenesdecompra.css') }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.x/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.3.7/css/buttons.dataTables.min.css">

    <!-- DataTables Scripts -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.3.7/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.3.7/js/buttons.html5.min.js"></script>
    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>



    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header card-header-info bg-primary">
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="card-title">ÓRDENES DE COMPRA</h4>
                            </div>
                        </div>
                    </div>
                    <br>
                    <div class="collapse" id="filtrosOrdenes">
                        <div class="container-fluid" id="filtrosOrdenesCompra"></div>
                    </div>
                    <br>
                </div>
            </div>
        </div>
    </div>

    <!-- SheetJS para exportar a Excel -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script>
        $(document).ready(function () {
            filtrosOrdenes();
            $("#filtrosOrdenes").collapse('show');
        });



        function filtrosOrdenes() {
            $.ajax({
                url: "{{ path('filtros_Ordenes_Compra') }}",
                type: 'GET',
                beforeSend: loadingGif("tablaOrdenes"),
                data: {},
                dataType: "html"
            }).done(function (html) {
                $("#filtrosOrdenesCompra").html(html);
            }).fail(function () {
                alert("error");
            });
        }


    </script>

{% endblock %}
