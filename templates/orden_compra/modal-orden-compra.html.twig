<link rel="stylesheet" href="{{ asset('/css/puntodeventa/filter_bussines.css') }}">
<link rel="stylesheet" href="{{ asset('/css/ordenesdecompra.css') }}">
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.x/font/bootstrap-icons.css" rel="stylesheet">
<input id="idsucursal" type="hidden" value="{{ idsucursal }}">

<div class="col-md-12" id="obtener-ordenes-compra">
    <div class="card">
        <div class="card-body"></div>
        <div id="obtener-ordenes-compra"></div>
    </div>
</div>


<!-- Modal -->
<div class="modal fade" id="mod-formulario-proveedor" tabindex="-1" role="dialog"
     aria-labelledby="mod-formulario-proveedor" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="mod-formulario-proveedor-title"></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Proveedor</label>
                            <select id="idproveedor" class="form-control margen-superior"
                                    onchange="obtenerContactos();">
                                <option value="-1">Seleccione un Proveedor</option>
                                {% for proveedor in proveedores %}
                                    <option value="{{ proveedor.idproveedor }}">{{ proveedor.NombreProveedor }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div id="contactosProveedores"></div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="tipo-pago-anticipo">Tipo de Pago</label>
                            <select name="" id="tipo-pago-anticipo" class="form-control" data-style="btn btn-link">
                                <option value="-1">Seleccione un pago</option>
                                <option value="Efectivo">Efectivo</option>
                                <option value="Depósito">Depósito</option>
                                <option value="Tarjeta de Débito">Tarjeta de Débito</option>
                                <option value="Tarjeta de Crédito">Tarjeta de Crédito</option>
                                <option value="Trasferencia">Trasferencia</option>
                                <option value="Convenio">Convenio</option>
                                <option value="Vales">Vales</option>
                                <option value="Pago con link de cobro CLIP">Pago con link de cobro CLIP</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="plazo-pago">Plazo de pago</label>
                            <select name="" id="plazo-pago" class="form-control" data-style="btn btn-link">
                                <option value="-1">Seleccione un plazo</option>
                                <option value="15 días">15 Días</option>
                                <option value="1 Semana">1 Semana</option>
                                <option value="1 Mes">1 meses</option>
                                <option value="2 Meses">2 meses</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>

    function obtenerContactos() {
        var idproveedor = $("#idproveedor").val();
        $('#idproveedor').val(idproveedor);

        $.ajax({
            url: "{{ path('contactos-proveedores') }}",
            data: {idproveedor: idproveedor},
            dataType: "html",
        }).done(function (html) {
            $("#contactosProveedores").html(html);
        }).fail(function () {
            alert("error");
        });
    }
</script>

