<div class="table-responsive">

    <table class="table table-striped" id="tablaOrdenes">
        <thead>
        <tr class="text-start">
            <th class="text-center" scope="col">Seleccionar</th>
            <th class="text-center" scope="col">Folio</th>
            <th class="text-center" scope="col">Nombre de Sucursal</th>
            <th class="text-center" scope="col">Tipo de Venta</th>

            <th class="text-center" scope="col">Nombre del Cliente</th>
            <th class="text-center" scope="col">Beneficiario</th>
            <th class="text-center" scope="col">Número de Empleado</th>
            <th class="text-center" scope="col">Unidad</th>

            <th class="text-center" scope="col">Producto</th>
            <th class="text-center" scope="col">Modelo</th>
            <th class="text-center" scope="col">Marca</th>
            <th class="text-center" scope="col">Diseño/Material/Tratamiento</th>

            <th class="text-center" scope="col">Base</th>
            <th class="text-center" scope="col">Esfera D</th>
            <th class="text-center" scope="col">Cilindro D</th>
            <th class="text-center" scope="col">Eje D</th>

            <th class="text-center" scope="col">Esfera I</th>
            <th class="text-center" scope="col">Cilindro I</th>
            <th class="text-center" scope="col">Eje I</th>
            <th class="text-center" scope="col">Add</th>
            <th class="text-center" scope="col">Fecha de Venta/Orden de Laboratorio</th>
        </tr>
        </thead>
        <tbody>
        {% for orden in ordenesdeCompra %}
            <tr class="text-center align-middle">
                <td class="custom-checkbox">
                    <input type="checkbox" class="checkbox-orden" name="selectedOrdenes[]"
                           value="{{ orden.Folio }}">
                </td>
                <td>{{ orden.Folio }}</td>
                <td>{{ orden.Sucursal }}</td>
                <td>{{ orden.TipodeVenta }}</td>

                <td>{{ orden.PrimerNombre ~ ' ' ~ orden.ApellidoPaterno ~ ' ' ~ orden.ApellidoMaterno }}</td>
                <td>{{ orden.Beneficiario }}</td>
                <td>{{ orden.NumerodeEmpleado }}</td>
                <td>{{ orden.Unidad }}</td>

                <td>{{ orden.Producto }}</td>
                <td>{{ orden.Marca }}</td>
                <td>{{ orden.Modelo }}</td>
                <td>{{ orden.DisenoLente ~ ' / ' ~ orden.Material ~ ' / ' ~ orden.Tratamiento }}</td>

                <td>{{ orden.Base }}</td>
                <td>{{ orden.EsferaD }}</td>
                <td>{{ orden.CilD }}</td>
                <td>{{ orden.EjeD }}</td>

                <td>{{ orden.EsfI }}</td>
                <td>{{ orden.CilI }}</td>
                <td>{{ orden.EjeI }}</td>
                <td>{{ orden.AddOrden }}</td>
                <td>{{ orden.Fecha|date('d/m/Y') }}</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
    <div class="card-footer text-center">
        {% if hayResultados %}
            <button id="botonCrearOrden" type="button" class="btn btn-primary" data-toggle="modal"
                    data-target="#mod-formulario-proveedor">
                <i class="bi bi-file-earmark-plus" style="font-size: 18px;"></i> CREAR ORDEN DE COMPRA
            </button>
            <button id="exportarExcel" class="btn btn-success">
                <i class="bi bi-file-earmark-excel"></i> Exportar a Excel
            </button>
        {% endif %}
    </div>
</div>

<script>
    document.getElementById('exportarExcel').addEventListener('click', function () {

        var tabla = document.getElementById('tablaOrdenes');
        var wb = XLSX.utils.table_to_book(tabla, { sheet: "Órdenes de Compra" });


        XLSX.writeFile(wb, 'Reporte.xlsx');
    });
</script>

<script>

    $(document).ready(function () {

        $('#tablaOrdenes').DataTable({
            dom: 'Bfrtip',
            buttons: [
                {
                    extend: 'excelHtml5',
                    title: 'Reporte UAM',
                    text: 'Exportar a Excel',
                    exportOptions: {
                        columns: ':visible'
                    }
                }
            ]
        });

        $('.checkbox-orden').change(function () {
            let currentFolio = $(this).val();
            let checkado = $(this).is(':checked');

            $('.checkbox-orden').not(this).each(function () {
                if ($(this).val() === currentFolio) {
                    $(this).prop('disabled', checkado);
                }
            });
        });
    });

</script>


