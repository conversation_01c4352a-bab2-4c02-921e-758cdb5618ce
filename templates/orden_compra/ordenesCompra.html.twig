{% block content %}
<script src="{{ asset('theme/material-dashboard-master/assets/js/core/bootstrap-material-design.min.js') }}"></script>
<style>
    /* Reset margins and padding for all elements */
    * {
        margin: 0;
        padding: 0;
    }


    table {
        width: 60%;
        border-collapse: collapse;
        margin: auto;
    }

    th, td {
        border: 1px solid black;
        text-align: center;
        padding: 5px;
        font-size: 10pt;
    }

    .header {
        background-color: #385EE6;
        color: white;
    }


    .container-lado-a-lado {
        width: 100%;
    }

    .bloque-izquierdo, .bloque-derecho {
        display: inline-block;
        width: 49%;
        vertical-align: top;
    }

    #Solicitante, #Proveedor, #metodo-pago {
        font-size: 10pt;
    }

    #contenido th[scope="col"]:nth-child(6),
    #contenido td:nth-child(6) {
        width: 10%;
        font-size: 10pt;
    }


    #contenido {
        width: 70%;
        font-size: 10pt;
    }

    .header-cell {
        background-color: #385EE6; /* Este es el color azul que ya usabas */
        color: white;

    }

    #contenido th, #contenido td {
        padding: 5px; /* Asegúrate de que haya suficiente espacio para el texto */
        word-wrap: break-word; /* Para que el texto largo se ajuste dentro de la celda */
        max-width: 150px; /* O un valor que se ajuste a tu diseño */
        font-size: 9pt;

    }

    .header-cell {
        background-color: #385EE6;
        color: white;
        font-size: 9pt;
    }

    #contenido td {
        overflow: visible;
        font-size: 9pt;
    }

    /* Existing styles... */

    #contenido th, #contenido td {
        padding: 5px; /* Adjust padding as needed */
    }


    #contenido th:last-child, #contenido td:last-child {
        width: 5%;
    }


    @page {
        size: landscape;
        margin: 0;
    }


    @media print {
        html, body {
            width: 100%;
            margin: 0;
            padding: 0;
        }

        .container-lado-a-lado {
            page-break-inside: avoid;
        }

        .container-principal {
            margin-top: 15mm; /* Add top margin here */
        }
    }
</style>


</style>

<div class="container-principal">
    <div class="container-lado-a-lado">
        <div class="bloque-izquierdo">
            <br>
            <table id="Solicitante">
                <tr class="header">
                    <th colspan="2">SOLICITANTE</th>
                </tr>
                <tr>
                    <td>EMPRESA:</td>
                    <td>Optimo Ópticas</td>
                </tr>
                <tr>
                    <td>DOMICILIO:</td>
                    <td>AV. INSURGENTES SUR 1338, ACTIPAN, BENITO JUÁREZ, 03230, PISO 2, CDMX</td>
                </tr>
                <tr>
                    <td>CONTACTO:</td>
                    <td><EMAIL> / 55 8045-1553</td>
                </tr>
                <tr>
                    <td>RFC:</td>
                    <td>GOM180220DD4</td>
                </tr>
            </table>
        </div>


        <div class="bloque-derecho">
            <br>
            {% for orden in contactosProveedores %}
                <table id="Proveedor">
                    <tr class="header">
                        <th colspan="2">PROVEEDOR</th>
                    </tr>
                    <tr>
                        <td>EMPRESA:</td>
                        <td>{{ orden.NombreProveedor }}</td>
                    </tr>
                    <tr>
                        <td>DOMICILIO:</td>
                        <td>{{ orden.Calle  ~ ', ' ~ orden.CodigoPostal ~ ', ' ~ orden.Ciudad }}</td>
                    </tr>
                    <tr>
                        <td>CONTACTO:</td>
                        <td>{{ orden.nombre }}</td>
                    </tr>
                    <tr>
                        <td>RFC:</td>
                        <td>{{ orden.RFC }}</td>
                    </tr>
                </table>
            {% endfor %}
        </div>
    </div>
    <br>
    <div>
        <table id="metodo-pago">
            <tr class="header">
                <th>MÉTODO DE PAGO</th>
                <th>Plazo de pago</th>
            </tr>
            <tr>
                <td>{{ tipoPago }}</td>
                <td>{{ plazoPago }}</td>
            </tr>
        </table>
    </div>

    <br>


    <table border="1" cellspacing="0" cellpadding="6" id="contenido">
        <thead>
        <tr class="text-center">
            <th scope="col" class="header-cell"></th>
            <th scope="col" class="header-cell">Folio</th>
            <th scope="col" class="header-cell">Sucursal/Campaña</th>
            <th scope="col" class="header-cell">Nombre del Titular</th>
            <th scope="col" class="header-cell">Nombre Beneficiario</th>

            <th scope="col" class="header-cell">Unidad Academica</th>
            <th scope="col" class="header-cell">Marca</th>
            <th scope="col" class="header-cell">Diseño/Material/Tratamiento</th>
            <th scope="col" class="header-cell">Base</th>

            <th scope="col" class="header-cell">Esfera D</th>
            <th scope="col" class="header-cell">Cilindro D</th>
            <th scope="col" class="header-cell">Eje D</th>
            <th scope="col" class="header-cell">Esfera I</th>

            <th scope="col" class="header-cell">Cilindro I</th>
            <th scope="col" class="header-cell">Eje I</th>
            <th scope="col" class="header-cell">Add</th>
            <th scope="col" class="header-cell">Observaciones</th>
        </tr>
        </thead>

        <tbody>
        {% for orden in ordenesdeCompraPdf %}
            <tr>
                <td>{{ loop.index }}</td>
                <td>{{ orden.Folio }}</td>
                <td>{{ orden.Sucursal }}</td>
                <td>{{ orden.PrimerNombre ~ ' ' ~ orden.ApellidoPaterno ~ ' ' ~ orden.ApellidoMaterno }}</td>
                <td>{{ orden.Beneficiario }}</td>

                <td>{{ orden.Unidad }}</td>
                <td>{{ orden.Marca }}</td>

                <td>
                    {% if orden.DisenoLente %}
                        {{ orden.DisenoLente }}
                    {% elseif true %}
                        No hay Diseño
                    {% endif %}
                    /
                    {% if orden.Material %}
                        {{ orden.Material }}
                    {% elseif true %}
                        No hay Material
                    {% endif %}
                    /
                    {% if orden.Tratamiento %}
                        {{ orden.Tratamiento }}
                    {% elseif true %}
                        No hay Tratamiento
                    {% endif %}
                </td>


                <td>{{ orden.Base }}</td>

                <td>{{ orden.EsferaD }}</td>
                <td>{{ orden.CilD }}</td>
                <td>{{ orden.EjeD }}</td>

                <td>{{ orden.EsfI }}</td>
                <td>{{ orden.CilI }}</td>
                <td>{{ orden.EjeI }}</td>
                <td>{{ orden.AddOrden }}</td>
                <td>{{ orden.Observaciones }}</td>

            </tr>
        {% endfor %}
        </tbody>
    </table>


    {% endblock %}
