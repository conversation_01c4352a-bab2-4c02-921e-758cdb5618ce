
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="table-responsive">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Filtros</h5>
                            <div class="row">

                                <div class="col-md-4 d-flex align-items-center">
                                    <select name="empresa" id="idempresa" class="form-control margen-superior">
                                        <option value="-1">Seleccione una empresa</option>
                                        {% for filtroEmpresa in filtrosEmpresas %}
                                            <option value="{{ filtroEmpresa.idempresa }}">{{ filtroEmpresa.Empresa }}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <div class="col-md-4 d-flex align-items-center">
                                    <select name="sucursal" id="idsucursal" class="form-control margen-superior"
                                            onchange="obtenerProveedores();">
                                        <option value="-1">Seleccione una sucursal</option>
                                        <option select="selected" value="todasSucursales">TODAS LAS SUCURSALES</option>
                                        {% for filtroSucursal in filtroSucursales %}
                                            <option value="{{ filtroSucursal.idsucursal }}">{{ filtroSucursal.Sucursal }}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <div class="col-md-4 d-flex align-items-center">
                                    <button type="button" class="btn btn-primary mt-3"
                                            onclick="obtenerOrdenesCompra();">Buscar
                                    </button>
                                </div>
                                <div class="d-flex align-items-center justify-content-between">
                                    <label class="lab reporte">Rango por días:</label>
                                </div>

                                <div class="input-daterange input-group rango-tiempo" id="idfilter-calender">
                                    <input id="fecha-inicio-rango-dia" type="text" autocomplete="off"
                                           class="input-sm form-control" name="start"/>

                                    <span class="input-group-addon"> a </span>

                                    <input id="fecha-fin-rango-dia" type="text" autocomplete="off"
                                           class="input-sm form-control" name="end"/>

                                    <button class="btn btn-warning" onclick="resetRangoFechaDias()"><i
                                                class="fa fa-eraser" aria-hidden="true"></i></button>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="contactosProveedores1"></div>
                </div>
            </div>
        </div>
    </div>
</div>


<script>

    jQuery(function ($) {
        $('#fecha-inicio-rango-dia').datetimepicker({
            "pickTime": false,
            "pickDate": true,
            "minDate": "1/1/1900",
            "maxDate": null,
            "showToday": true,
            "language": "es_MX",
            "defaultDate": "",
            "disabledDates": [],
            "enabledDates": [],
            "icons": {
                "time": "fa fa-clock-o",
                "date": "fa fa-calendar",
                "up": "fa fa-chevron-up",
                "down": "fa fa-chevron-down"
            },
            "useStrict": false,
            "sideBySide": false,
            "daysOfWeekDisabled": [],
            "collapse": true,
            "calendarWeeks": false,
            "viewMode": "days",
            "minViewMode": "days",
            "useCurrent": false,
            "useSeconds": false
        });

        $('#fecha-fin-rango-dia').datetimepicker({
            "pickTime": false,
            "pickDate": true,
            "minDate": "1/1/1900",
            "maxDate": null,
            "showToday": true,
            "language": "es_MX",
            "defaultDate": "",
            "disabledDates": [],
            "enabledDates": [],
            "icons": {
                "time": "fa fa-clock-o",
                "date": "fa fa-calendar",
                "up": "fa fa-chevron-up",
                "down": "fa fa-chevron-down"
            },
            "useStrict": false,
            "sideBySide": false,
            "daysOfWeekDisabled": [],
            "collapse": true,
            "calendarWeeks": false,
            "viewMode": "days",
            "minViewMode": "days",
            "useCurrent": false,
            "useSeconds": false
        });
    });

    function resetRangoFechaDias() {
        $("#fecha-inicio-rango-dia").val("");
        $("#fecha-fin-rango-dia").val("");
    }

    function obtenerProveedores() {

        var idsucursal = $("#idsucursal").val();
        $('#idsucursal').val(idsucursal);
        console.log("asdas");

        $.ajax({
            url: "{{ path('modal-orden-compra') }}",
            data: {idsucursal: idsucursal},
            dataType: "html"
        }).done(function (html) {

            $("#contactosProveedores1").html(html);
        }).fail(function () {
            alert("error");
        });
    }

    var idempresa = $("#idempresa").val();
    $('#idempresa').val(idempresa);


    function obtenerOrdenesCompra() {

        let fechaInicio = document.getElementById('fecha-inicio-rango-dia').value;
        let fechaFin = document.getElementById('fecha-fin-rango-dia').value;

        var idempresa = $('#idempresa').val();
        var idsucursal = $("#idsucursal").val();
        $('#idsucursal').val(idsucursal);

        var datos = {
            fechaInicio: fechaInicio,
            fechaFin: fechaFin,
            idsucursal: idsucursal,
            idempresa: idempresa,
        };


        $.ajax({
            url: "{{ path('obtener-ordenes-compra') }}",
            data: datos,
            dataType: "html",
            beforeSend: function (xhr) {
                loadingGif("contenedor-graficas");
            }
        }).done(function (html) {
            $("#obtener-ordenes-compra").html(html);
            $('#botonCrearOrden').toggleClass('d-none', !html.trim());

        }).fail(function (jqXHR, textStatus, errorThrown) {
            alert("Error en obtener-ordenes-compra: " + textStatus + " " + errorThrown);
        });
    }


</script>

