{% extends 'base.html.twig' %}

{% block title %}Unificar clientes{% endblock %}

{% block body %}

<!DOCTYPE html>
<html lang="es">
<head>
    <link rel="stylesheet" href="{{ asset('/css/update-clients.css') }}">
    <title>Contar duplicados</title>
</head>
<body>
    <section>
        <div class="form-box">
            <div class="form-value">
                <form action="">
                    <h2>Unificar clientes</h2>

                    <div class="inputbox">
                        <ion-icon name="call-outline"></ion-icon>
                        <input id="phone-input" type="text" required>
                        <label for="">Teléfono</label>
                    </div>

                    <button type="button" onclick="countDuplicates()">Unificar</button>


                    <br>
                    <br>
                    
                    <h4 id="response-container"></h4>

                </form>
            </div>
        </div>
    </section>
    <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</body>
</html>

<script>

    $(document).ready(function(){

    });

    function countDuplicates(){
        var phone = $("#phone-input").val();

        $.ajax({
            url: "{{path('app_contar_duplicados')}}", 
            type: 'POST',
            data: {phone:phone},

        }).done(function( response ) {

            $("#response-container").text(response);
            
        });
    }

</script>


{% endblock %}