{% extends 'admin/layout.html.twig' %}
{% block content %}

    <div class="card">
        <div class="row">
            <div class="col-md-12">
                <h1>Lista de Ventas</h1>
                <div class="table-responsive">
                    <table id="ventaTable" class="display" style="width:100%">
                        <thead>
                        <tr class="text-start">
                            <th class="text-center">ID Venta</th>
                            <th class="text-center">Total Pagos</th>
                            <th class="text-center">Total Automáticos</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for row in resultado %}
                            {% set venta = row.venta %}
                            {% set totalPagos = row.totalPagos %}
                            {% set totalAutomatic = row.totalAutomatic %}
                            <tr>
                                <td class="text-center align-middle">{{ venta }}</td>
                                <td class="text-center align-middle">{{ totalPagos }}</td>
                                <td class="text-center align-middle">{{ totalAutomatic }}</td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                    <a class="btn btn-primary" onclick="actualizarPagos()">Actualizar Pagos</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function () {
            $('#ventaTable').DataTable({
                pageLength: 10,
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json'
                }
            });
        });

        function actualizarPagos() {
            console.log("Estoy dentro")
            var buttonClick = 1;
            $.ajax({
                url: '{{ path('ventas-pagos-repetidos-uam') }}',
                method: "POST",
                dataType: "json",
                data: { buttonClick: buttonClick },
                beforeSend: function() {
                }
            }).done(function(response) {
                if (response.exito) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Se cambiaron los pagos',
                        showConfirmButton: false,
                        timer: 2500
                    });
                    setTimeout(function() {
                        location.reload();
                    }, 2500);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: response.msj,
                    });
                }
            });
        }
    </script>

{% endblock %}