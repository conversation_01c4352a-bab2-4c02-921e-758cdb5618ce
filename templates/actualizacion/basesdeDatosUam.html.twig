{% extends 'admin/layout.html.twig' %}
{% block content %}
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.1.1/css/buttons.dataTables.min.css">
    <h1>Base de Datos UAM (Ventas)</h1>
    <div class="table-responsive">

        <table id="clientes-table" class="display">
            <thead>
            <tr>
                <th>Nombre</th>
                <th>Apellido Paterno</th>
                <th>Apellido Materno</th>
                <th>Número de Empleado</th>
                <th>Número de Teléfono</th>
                <th>Unidad</th>
                <th>Fecha de Venta</th>
                <th>Tipo de Venta</th>
            </tr>
            </thead>
            <tbody>
            {% for cliente in clientes %}
                <tr>
                    <td>{{ cliente.nombre }}</td>
                    <td>{{ cliente.apellidopaterno }}</td>
                    <td>{{ cliente.apellidomaterno }}</td>
                    <td>{{ cliente.numeroempleado }}</td>
                    <th>{{ cliente.numeroTelefono }}</th>
                    <td>{{ cliente.Unidad }}</td>
                    <td>{{ cliente.fechacreacion|date('Y-m-d') }}</td>
                    <td>{{ cliente.Tipoventa }}</td>
                </tr>
            {% endfor %}
            </tbody>
        </table>

    </div>


{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.1.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.1.1/js/buttons.flash.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.1.1/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.1.1/js/buttons.print.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#clientes-table').DataTable({
                dom: 'Bfrtip',
                buttons: [
                    'excelHtml5'
                ],
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/Spanish.json"
                }
            });
        });
    </script>
{% endblock %}

