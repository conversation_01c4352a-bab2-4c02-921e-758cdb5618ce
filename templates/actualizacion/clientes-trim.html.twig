{% extends 'admin/layout.html.twig' %}
{% block titleHead %}

{% endblock %}
{% block title %}
  CLIENTES TRIM
{% endblock %}

{% block content %}
  <!DOCTYPE html>
  <html lang="en">
    <head>
      <!-- Incluir CSS de DataTables -->
      <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.12.1/css/jquery.dataTables.css" />

      <!-- Incluir jQuery y JS de DataTables -->
      <script type="text/javascript" charset="utf8" src="https://code.jquery.com/jquery-3.5.1.js"></script>
      <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.12.1/js/jquery.dataTables.js"></script>

      <meta charset="UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>Document</title>
    </head>
    <body>
      <div class="container">
        <table id="tablaClientes" class="display">
          <thead>
            <tr>
              <th>Nombre</th>
              <th>Apellido Paterno</th>
              <th>Apellido Materno</th>
              <th>Tipo cliente</th>
            </tr>
          </thead>
          <tbody>
            {% for cliente in clientes %}
              <tr>
                <td>{{ cliente.nombre }}</td>
                <td>{{ cliente.apellidopaterno }}</td>
                <td>{{ cliente.apellidomaterno }}</td>
                <td>{{ cliente.tipocliente }}</td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </body>
    <script>
      $(document).ready(function () {
        $('#tablaClientes').DataTable({
          language: {
            url: '//cdn.datatables.net/plug-ins/1.12.1/i18n/es-ES.json'
          }
        })
      })
    </script>
  </html>
{% endblock %}
