{% extends 'base.html.twig' %}

{% block title %}Traspaso Masivo por Excel{% endblock %}

{% block body %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Traspaso Masivo por Excel</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h4><i class="icon fa fa-info"></i> Instrucciones</h4>
                        <p>Este módulo permite cargar un archivo Excel con SKUs para transferir productos con cantidad = 1 desde sucursales seleccionadas hacia la sucursal destino ({{ sucursalDestino.nombre }}).</p>
                        <ol>
                            <li>Cargue un archivo Excel (.xlsx o .xls) que contenga los SKUs en las columnas B-E.</li>
                            <li>El sistema buscará productos con esos SKUs que tengan cantidad = 1 en sucursales distintas a {{ sucursalDestino.nombre }}.</li>
                            <li>Seleccione las sucursales de origen de las cuales desea transferir los productos.</li>
                            <li>Ejecute el traspaso para mover los productos a la sucursal {{ sucursalDestino.nombre }}.</li>
                        </ol>
                    </div>

                    {% for label, messages in app.flashes %}
                        {% for message in messages %}
                            <div class="alert alert-{{ label }}">
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endfor %}

                    <form action="{{ path('procesar_excel_traspaso') }}" method="post" enctype="multipart/form-data">
                        <div class="form-group">
                            <label for="archivo_excel">Seleccione el archivo Excel con los SKUs:</label>
                            <div class="input-group">
                                <div class="custom-file">
                                    <input type="file" class="custom-file-input" id="archivo_excel" name="archivo_excel" accept=".xlsx, .xls" required>
                                    <label class="custom-file-label" for="archivo_excel">Elegir archivo...</label>
                                </div>
                            </div>
                            <small class="form-text text-muted">El archivo debe ser un Excel (.xlsx o .xls) con los SKUs en las columnas B-E.</small>
                        </div>
                        <button type="submit" class="btn btn-primary">Procesar Excel</button>
                        <a href="{{ path('homepage') }}" class="btn btn-secondary">Cancelar</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
    // Mostrar el nombre del archivo seleccionado
    $(document).ready(function() {
        $('.custom-file-input').on('change', function() {
            var fileName = $(this).val().split('\\').pop();
            $(this).next('.custom-file-label').html(fileName);
        });
    });
</script>
{% endblock %}