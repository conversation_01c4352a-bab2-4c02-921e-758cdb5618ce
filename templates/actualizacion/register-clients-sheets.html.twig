{% extends 'layout-form.html.twig' %}

{% block title %}Cargar clientes{% endblock %}

{% block content %}
    <section class="login-container"></section>
    <div class="container-fluid">
        <div class="page row">
            <div class="form-box col-md-6 col-10 mx-auto vertical-center">
                
                    <div id="uam-form-container" class="p-4" style="height:90vh">
                        <br>
                        <button id="picker_button" class="btn btn-success d-none" onclick="createPicker()">Buscar archivo</button>
                        <button id="authorize_button" class="btn btn-primary d-none" onclick="handleAuthClick()">Iniciar sesión</button>
                        <button id="signout_button" class="btn btn-danger d-none" onclick="handleSignoutClick()">Cerrar sesión</button>
                        <p id="cur-clients-count" class="mt-5 mb-5"></p>
                        <label for="client-enterprise-field">Empresa del cliente</label>
                        <input type="text" class="form-control" id="client-enterprise-field"/>
                        <label for="client-unit-field">Unidad del cliente</label>
                        <input type="text" class="form-control" id="client-unit-field"/>
                        <br>
                        <button class="submit-button" onclick="addClientsSheets()">Guardar clientes</button>
                    </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block scripts %}
    {{parent()}}
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="{{ asset('js/bootstrap.bundle.js') }}"></script>
    <script>
        const CLIENT_ID = '920138527269-euigacmlshkhcm2493hrj2kuebooknro.apps.googleusercontent.com';
        const API_KEY = 'AIzaSyB1-aw7lXPz5acAYPnQF5XjPE66stFeAVk';
        const DISCOVERY_DOC = 'https://sheets.googleapis.com/$discovery/rest?version=v4';
        const SCOPES = 'https://www.googleapis.com/auth/drive.readonly https://www.googleapis.com/auth/spreadsheets.readonly';
        const APP_ID = 'pv360-416621';

        let tokenClient;
        let accessToken = null;
        let pickerInited = false;
        let gisInited = false;
        let gapiInited = false;

        let curClients = [];

        $(document).ready(function(){

        });

        function addClientsSheets(){
            let clientEnterprise = $("#client-enterprise-field").val();
            let unity = $("#client-unit-field").val();

            console.log(clientEnterprise)
            console.log(unity)

            if (clientEnterprise != ''){
                $.ajax({
                url: "{{path('actualizacion-add-clients-sheets')}}", 
                type: 'POST',
                data: {
                    clientEnterprise:clientEnterprise,
                    unity:unity,
                    curClients:curClients.join('*'),
                },
                dataType: "json"
                }).done(function( response ) {
                    if (response.success){
                        Swal.fire({
                            title: "Registro exitoso",
                            text: "Se guardaron "+response.updatedClients+" cliente(s)",
                            icon: "success"
                        });
                    }

                }).fail(function() {
                    alert( "error" );
                });
            } else {

                Swal.fire({
                    title: "¡Cuidado!",
                    text: "Para seguir con el proceso debes ingresar una empresa de cliente",
                    icon: "warning"
                });
            }

            
        }
        
        function gapiLoaded() {
            gapi.load('client:picker', initializePicker);
            gapi.load('client', initializeGapiClient);
        }

        async function initializeGapiClient() {
            await gapi.client.init({
                apiKey: API_KEY,
                discoveryDocs: [DISCOVERY_DOC],
            });
            gapiInited = true;
            maybeEnableButtons();
        }

        async function initializePicker() {
            await gapi.client.load('https://www.googleapis.com/discovery/v1/apis/drive/v3/rest');
            pickerInited = true;
            maybeEnableButtons();
        }

        function gisLoaded() {
            tokenClient = google.accounts.oauth2.initTokenClient({
            client_id: CLIENT_ID,
            scope: SCOPES,
            callback: '', // defined later
            });
            gisInited = true;
            maybeEnableButtons();
        }


        function maybeEnableButtons() {
            if (pickerInited && gisInited && gapiInited) {
                $("#authorize_button").removeClass("d-none")
            }
        }


        function handleAuthClick() {
            tokenClient.callback = async (response) => {
            if (response.error !== undefined) {
                throw (response);
            }
            accessToken = response.access_token;
            $("#signout_button").removeClass("d-none")
            $("#picker_button").removeClass("d-none")
            $("#authorize_button").text("Cambiar de cuenta")
        
            await createPicker();
            };

            if (accessToken === null) tokenClient.requestAccessToken({prompt: 'consent'});
            else tokenClient.requestAccessToken({prompt: ''});
        }

        function handleSignoutClick() {
            if (accessToken) {
                accessToken = null;
                google.accounts.oauth2.revoke(accessToken);
                $("#authorize_button").text("Iniciar sesión")
                $("#signout_button").addClass("d-none")
                $("#picker_button").addClass("d-none")
                $("#error_button").addClass("d-none")
            }
        }

        function createPicker() {
            const view = new google.picker.View(google.picker.ViewId.DOCS);
            view.setMimeTypes('application/vnd.google-apps.spreadsheet');
            const picker = new google.picker.PickerBuilder()
                .enableFeature(google.picker.Feature.NAV_HIDDEN)
                .setDeveloperKey(API_KEY)
                .setAppId(APP_ID)
                .setOAuthToken(accessToken)
                .addView(view)
                .addView(new google.picker.DocsUploadView())
                .setCallback(pickerCallback)
                .build();
            picker.setVisible(true);
        }

        function updateCurClients(newClients){
            curClients = newClients;
            if (Array.isArray(curClients)) $("#cur-clients-count").text("Se encontró: "+curClients.length+" cliente(s)");
        }

        function test(){
            console.log(curClients)
        }

        async function pickerCallback(data) {
            if (data.action === google.picker.Action.PICKED) {
                const document = data[google.picker.Response.DOCUMENTS][0];
                const fileId = document[google.picker.Document.ID];

                // Retrieve the content of the cells in the Google Sheets file
                const response = await gapi.client.sheets.spreadsheets.values.get({
                    spreadsheetId: fileId,
                    range: 'Sheet1!A:Z',
                    majorDimension: 'COLUMNS',
                });

                const values = response.result.values;

                if (values) {


                    for (let name of values[0]){
                        name.replace(/[^a-zA-Z]/g, ' ');
                    }

                    console.log(values[0]);
                    
                    updateCurClients(values[0]);
                }
                

            }
        }

    </script>
    <script async defer src="https://apis.google.com/js/api.js" onload="gapiLoaded()"></script>
    <script async defer src="https://accounts.google.com/gsi/client" onload="gisLoaded()"></script>

{% endblock %}


