{% extends 'base.html.twig' %}

{% block title %}Resultado del Traspaso Masivo{% endblock %}

{% block body %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Resultado del Traspaso Masivo a {{ sucursalDestino.nombre }}</h3>
                </div>
                <div class="card-body">
                    {% for label, messages in app.flashes %}
                        {% for message in messages %}
                            <div class="alert alert-{{ label }}">
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endfor %}

                    {% if exito %}
                        <div class="alert alert-success">
                            <h4><i class="icon fa fa-check"></i> Traspaso Exitoso</h4>
                            <p>{{ mensaje }}</p>
                        </div>
                    {% else %}
                        <div class="alert alert-danger">
                            <h4><i class="icon fa fa-ban"></i> Error en el Traspaso</h4>
                            <p>{{ mensaje }}</p>
                        </div>
                    {% endif %}

                    {% if productosTransferidos|length > 0 %}
                        <h4>Productos Transferidos ({{ productosTransferidos|length }})</h4>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="tabla-transferidos">
                                <thead>
                                    <tr>
                                        <th>SKU</th>
                                        <th>Nombre</th>
                                        <th>Sucursal Origen</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for producto in productosTransferidos %}
                                        <tr>
                                            <td>{{ producto.codigo }}</td>
                                            <td>{{ producto.nombre }}</td>
                                            <td>{{ producto.sucursal_origen }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% endif %}

                    {% if productosNoTransferidos|length > 0 %}
                        <h4 class="mt-4">Productos No Transferidos ({{ productosNoTransferidos|length }})</h4>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="tabla-no-transferidos">
                                <thead>
                                    <tr>
                                        <th>ID Stock</th>
                                        <th>Motivo</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for producto in productosNoTransferidos %}
                                        <tr>
                                            <td>{{ producto.idstock }}</td>
                                            <td>{{ producto.motivo }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% endif %}

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <a href="{{ path('traspaso_masivo_excel') }}" class="btn btn-primary">
                                <i class="fa fa-arrow-left"></i> Volver al Formulario de Carga
                            </a>
                            <a href="{{ path('homepage') }}" class="btn btn-secondary">
                                <i class="fa fa-home"></i> Ir al Inicio
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
    $(document).ready(function() {
        // Inicializar DataTable para productos transferidos
        if ($('#tabla-transferidos').length) {
            $('#tabla-transferidos').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Spanish.json"
                },
                "pageLength": 25
            });
        }
        
        // Inicializar DataTable para productos no transferidos
        if ($('#tabla-no-transferidos').length) {
            $('#tabla-no-transferidos').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Spanish.json"
                },
                "pageLength": 25
            });
        }
    });
</script>
{% endblock %}