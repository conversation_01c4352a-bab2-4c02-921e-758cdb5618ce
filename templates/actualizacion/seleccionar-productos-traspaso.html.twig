{% extends 'base.html.twig' %}

{% block title %}Seleccionar Productos para Traspaso{% endblock %}

{% block body %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Seleccionar Productos para Traspaso a {{ sucursalDestino.nombre }}</h3>
                </div>
                <div class="card-body">
                    {% for label, messages in app.flashes %}
                        {% for message in messages %}
                            <div class="alert alert-{{ label }}">
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endfor %}

                    {% if not exito %}
                        <div class="alert alert-danger">
                            {{ mensaje }}
                        </div>
                        <a href="{{ path('traspaso_masivo_excel') }}" class="btn btn-primary">Volver a cargar Excel</a>
                    {% else %}
                        <div class="alert alert-success">
                            {{ mensaje }}
                        </div>

                        <form action="{{ path('ejecutar_traspaso_masivo') }}" method="post" id="form-traspaso">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Sucursal Destino:</label>
                                        <input type="text" class="form-control" value="{{ sucursalDestino.nombre }}" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6 text-right">
                                    <div class="btn-group mt-4">
                                        <button type="button" class="btn btn-primary" id="seleccionar-todos">Seleccionar Todos</button>
                                        <button type="button" class="btn btn-secondary" id="deseleccionar-todos">Deseleccionar Todos</button>
                                    </div>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-bordered table-striped" id="tabla-productos">
                                    <thead>
                                        <tr>
                                            <th>Seleccionar</th>
                                            <th>SKU</th>
                                            <th>Nombre</th>
                                            <th>Sucursal Actual</th>
                                            <th>Cantidad</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for producto in productos %}
                                            <tr>
                                                <td class="text-center">
                                                    <div class="form-check">
                                                        <input class="form-check-input producto-checkbox" type="checkbox" name="productos[]" value="{{ producto.idstock }}" id="producto-{{ producto.idstock }}" checked>
                                                    </div>
                                                </td>
                                                <td>{{ producto.codigo }}</td>
                                                <td>{{ producto.nombre }}</td>
                                                <td>{{ producto.sucursal_nombre }}</td>
                                                <td class="text-center">{{ producto.cantidad }}</td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="filtro-sucursal">Filtrar por Sucursal:</label>
                                        <select class="form-control" id="filtro-sucursal">
                                            <option value="">Todas las sucursales</option>
                                            {% set sucursales = [] %}
                                            {% for producto in productos %}
                                                {% if producto.sucursal_nombre not in sucursales %}
                                                    {% set sucursales = sucursales|merge([producto.sucursal_nombre]) %}
                                                    <option value="{{ producto.sucursal_nombre }}">{{ producto.sucursal_nombre }}</option>
                                                {% endif %}
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <button type="submit" class="btn btn-success" id="btn-ejecutar-traspaso">
                                        <i class="fa fa-exchange-alt"></i> Ejecutar Traspaso
                                    </button>
                                    <a href="{{ path('traspaso_masivo_excel') }}" class="btn btn-secondary">Cancelar</a>
                                </div>
                            </div>
                        </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
    $(document).ready(function() {
        // Inicializar DataTable
        var table = $('#tabla-productos').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Spanish.json"
            },
            "pageLength": 25
        });
        
        // Seleccionar todos los productos
        $('#seleccionar-todos').click(function() {
            $('.producto-checkbox').prop('checked', true);
        });
        
        // Deseleccionar todos los productos
        $('#deseleccionar-todos').click(function() {
            $('.producto-checkbox').prop('checked', false);
        });
        
        // Filtrar por sucursal
        $('#filtro-sucursal').change(function() {
            var sucursal = $(this).val();
            table.column(3).search(sucursal).draw();
        });
        
        // Validar que al menos un producto esté seleccionado
        $('#form-traspaso').submit(function(e) {
            if ($('.producto-checkbox:checked').length === 0) {
                e.preventDefault();
                alert('Debe seleccionar al menos un producto para realizar el traspaso.');
                return false;
            }
            
            return true;
        });
    });
</script>
{% endblock %}