{% extends 'admin/layout.html.twig' %}

{% block content %}
    <div class="row">
        <div class="col-md-12">
            <h2>Actualizar Productos desde JSON</h2>

            <!-- Botón para cargar los datos desde el JSON en el servidor -->
            <button id="updateFromJsonButton">Actualizar desde JSON</button>

            <hr>

            <!-- Tabla para mostrar los datos -->
            <table id="productosTable" class="display" style="width:100%">
                <thead>
                <tr>
                    <th>Modelo</th>
                    <th>Marca</th>
                    <th>Género</th>
                    <th>Diseño</th>
                    <th>Estilo</th>
                    <th>Material</th>
                    <th>Color</th>
                    <th>Medidas</th>
                    <th>Descripción</th>
                    <th>¿Tiene precio?</th>
                </tr>
                </thead>
                <tbody>
                {% for producto in productos %}
                    <tr>
                        <td>{{ producto.productoIdproducto.modelo }}</td>
                        <td>{{ producto.productoIdproducto.marcaIdmarca.nombre }}</td>
                        <td>{{ producto.productoIdproducto.gender }}</td>
                        <td>{{ producto.productoIdproducto.moreinfo }}</td>
                        <td>{{ producto.productoIdproducto.design }}</td>
                        <td>
                            {% if producto.productoIdproducto.framematerialIdframematerial is not null %}
                                {{ producto.productoIdproducto.framematerialIdframematerial.material }}
                            {% else %}
                                No especificado
                            {% endif %}
                        </td>
                        <td>
                            {% if producto.productoIdproducto.framecolorIdframecolor is not null %}
                                {{ producto.productoIdproducto.framecolorIdframecolor.color }}
                            {% else %}
                                No especificado
                            {% endif %}
                        </td>
                        <td>
                            {% if producto.productoIdproducto.medidaIdmedida is not null %}
                                {{ producto.productoIdproducto.medidaIdmedida.nombre }}
                            {% else %}
                                No especificado
                            {% endif %}
                        </td>
                        <td>{{ producto.productoIdproducto.descripcion }}</td>
                        <td>{{ producto.productoIdproducto.precio ? 'Sí' : 'No' }}</td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <script>
        $(document).ready(function () {
            $('#productosTable').DataTable({
                "pageLength": 10,
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/Spanish.json"
                }
            });

            // Manejar el clic en el botón para actualizar desde JSON
            $('#updateFromJsonButton').on('click', function () {
                $.ajax({
                    url: '{{ path("actualizar_productos_json") }}',  // Ruta al controlador que carga el JSON
                    type: 'POST',
                    success: function (response) {
                        if (response.success) {
                            const table = $('#productosTable').DataTable();
                            table.clear();

                            // Agregar los productos actualizados a la tabla
                            response.modelos.forEach(function (modelo) {
                                table.row.add([
                                    modelo,
                                    "Actualizado",  // Puedes personalizar qué mostrar para cada columna
                                    "-", "-", "-", "-", "-", "-", "-", "-"
                                ]).draw();
                            });

                            alert("Productos actualizados correctamente.");
                        } else {
                            alert(response.message);
                        }
                    },
                    error: function () {
                        alert('Error al actualizar productos desde el JSON.');
                    }
                });
            });
        });
    </script>

{% endblock %}
