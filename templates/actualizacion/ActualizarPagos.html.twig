{% extends 'admin/layout.html.twig' %}

{% block title %}Actualizar Pagos de Ventas no liquidadas{% endblock %}

{% block content %}
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css"/>
    <input type="hidden" id="url-app_actualizacion_pagos" value="actualizacion-pagos">
    <h1 class="text-center">Actualización de Pagos</h1>

    <div class="card">
        <div class="row">
            <div class="col-md-12">
                {% if ventasNoLiquidadasConPagos is not empty %}
                    <table class="table table-responsive-md" id="ventasNoLiquidadas">
                        <thead>
                        <tr class="text-center">
                            <th class="text-center">Folio</th>
                            <th class="text-center">Liquidada</th>
                            <th class="text-center">Total con IVA/Deuda</th>
                            <th class="text-center">Tipo de Venta</th>
                            <th class="text-center">Numero de Pagos</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for venta in ventasNoLiquidadasConPagos %}
                            <tr class="text-center">
                                <td>{{ venta.folio }}</td>
                                <td>{{ venta.liquidada ? 'Sí' : 'No' }}</td>
                                <td>{{ venta.totalConIVA | number_format(2, '.', ',') }}</td>
                                <td>{{ venta.Tipoventa }}</td>
                                <td>{{ venta.NumeroPagos }}</td> <!-- Mostrar el número de pagos -->
                            </tr>
                        {% endfor %}

                        </tbody>
                    </table>
                {% else %}
                    <p class="text-center">No se encontraron ventas no liquidadas.</p>
                {% endif %}
            </div>
            <div class="col-md-12 text-center mt-3">
                <button class="btn btn-success" onclick="correrProcesoVentas()">Confirmar</button>
            </div>
        </div>
    </div>

    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.js"></script>
    <script src="https://cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json"></script>
    <script>
        let table = new DataTable('#ventasNoLiquidadas', {
            "language": {
                "url": "https://cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json"
            }
        });
        function correrProcesoVentas(){
            let url = $("#url-app_actualizacion_pagos").val();
            let confirmar = true;
            $.ajax({
                url: url,
                method: "POST",
                data: {
                    confirmar: confirmar
                },
                dataType: "json"
            }).done(function (response) {
                if(response.exito) {
                    Swal.fire({
                        title: 'Proceso Completo',
                        text: response.mensaje + '\nTotal de cambios: ' + response.totalCambios,
                        icon: 'success',
                        confirmButtonText: 'Aceptar'
                    });
                } else {
                    Swal.fire({
                        title: 'Error',
                        text: 'Ocurrió un error: ' + response.mensaje,
                        icon: 'error',
                        confirmButtonText: 'Aceptar'
                    });
                }
            }).fail(function (jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error en la Solicitud',
                    text: 'Hubo un problema al procesar la solicitud. Error: ' + errorThrown,
                    icon: 'error',
                    confirmButtonText: 'Aceptar'
                });
            });
        }
    </script>
{% endblock %}
