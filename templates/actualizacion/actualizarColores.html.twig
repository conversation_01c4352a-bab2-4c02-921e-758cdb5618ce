{% extends 'admin/layout.html.twig' %}

{% block title %}| Tienda en Línea{% endblock %}

{% block content %}
    <link href="{{ asset('css/styles/comisiones.css') }}?version={{ version }}" rel="stylesheet"/>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.css"/>

    <div class="body d-flex flex-column align-items-center pt-4">
        <div class="container-fluid bg-white rounded-4 py-4 shadow">
            <h1 class="text-center page-title mb-5">Configuración Tienda en Línea</h1>
            <br>
            <div id="tablaComisiones" class="container-fluid">
                <table id="productosTable" class="table table-bordered table-striped display table-responsive"
                       style="width:100%">
                    <thead class="thead-dark">
                    <tr>
                        <th class="text-center">Modelo</th>
                        <th class="text-center"><PERSON><PERSON></th>
                        <th class="text-center"><PERSON><PERSON><PERSON></th>
                        <th class="text-center">Diseño</th>
                        <th class="text-center">Estilo</th>
                        <th class="text-center">Id Material Frame</th>
                        <th class="text-center">Material</th>
                        <th class="text-center">Id Color Frame</th>
                        <th class="text-center">Color</th>
                        <th class="text-center">Medidas</th>
                        <th class="text-center">Descripción</th>
                        <th class="text-center">¿Tiene precio?</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for producto in productos %}
                        <tr>
                            <td class="text-center">{{ producto.productoIdproducto.modelo }}</td>
                            <td class="text-center">{{ producto.productoIdproducto.marcaIdmarca.nombre }}</td>
                            <td class="text-center">{{ producto.productoIdproducto.gender }}</td>
                            <td class="text-center">{{ producto.productoIdproducto.moreinfo }}</td>
                            <td class="text-center">{{ producto.productoIdproducto.design }}</td>
                            <td class="text-center">
                                {% if producto.productoIdproducto.framematerialIdframematerial is not null %}
                                    {{ producto.productoIdproducto.framematerialIdframematerial.idframematerial }}
                                {% else %}
                                    No especificado
                                {% endif %}
                            </td>
                            <td class="text-center">

                                {% set curLocation = producto.productoIdproducto.framematerialIdframematerial is not null
                                    ? producto.productoIdproducto.framematerialIdframematerial.idframematerial
                                    : -1 %}

                                <select id="location-select-{{ producto.productoIdproducto.idproducto }}"
                                        onchange="actualizarProducto(this, 'material')">

                                    {% for material in materiales %}
                                        <option value="{{ material.idframematerial }}"
                                                {{ curLocation == material.idframematerial ? 'selected' : '' }}>
                                            {{ material.material }} ({{ material.idframematerial }})
                                        </option>
                                    {% endfor %}
                                </select>

                                <i id="check-location-{{ producto.productoIdproducto.idproducto }}"
                                   class="fa-solid fa-check d-none"
                                   style="color: green"></i>

                            </td>
                            <td class="text-center">
                                {% if producto.productoIdproducto.framecolorIdframecolor is not null %}
                                    {{ producto.productoIdproducto.framecolorIdframecolor.idframecolor }}
                                {% else %}
                                    No especificado
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% set curLocation = producto.productoIdproducto.framecolorIdframecolor is not null
                                    ? producto.productoIdproducto.framecolorIdframecolor.idframecolor
                                    : -1 %}

                                    <select id="location-select-{{ producto.productoIdproducto.idproducto }}"
                                            onchange="actualizarProducto(this, 'color')">
                                    {% for color in colores %}
                                        <option value="{{ color.idframecolor }}"
                                                {{ curLocation == color.idframecolor ? 'selected' : '' }}>
                                            {{ color.color }} ({{ color.idframecolor }})
                                        </option>
                                    {% endfor %}
                                </select>

                                <i id="check-location-{{ producto.productoIdproducto.idproducto }}"
                                   class="fa-solid fa-check d-none"
                                   style="color: green"></i>

                            </td>
                            <td class="text-center">
                                {% if producto.productoIdproducto.medidaIdmedida is not null %}
                                    {{ producto.productoIdproducto.medidaIdmedida.nombre }}
                                {% else %}
                                    No especificado
                                {% endif %}
                            </td>
                            <td class="text-center">{{ producto.productoIdproducto.descripcion }}</td>
                            <td class="text-center">{{ producto.productoIdproducto.precio ? 'Sí' : 'No' }}</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>



    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


    <script>
        $(document).ready(function () {

            $('#productosTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                    lengthMenu: 'Mostrar _MENU_ ordenes por página',
                },
                dom: 'Bfrtip',
                buttons: [
                    {
                        className: 'btn-primary btn',
                        filename: 'laboratory_order',
                        extend: 'excelHtml5',
                        text: 'Exportar excel',
                    }
                ]
            });

        });

        /*function setColor(idproducto) {
            // Obtén el valor seleccionado del select correspondiente

            let idframecolorNew = idproducto.value;
            let productoId = idproducto.id;

            let numeroProductoId = productoId.split('-').pop();


            $.ajax({
                url: "{{ path('cambioProducto') }}",
                type: 'GET',
                dataType: 'json',
                data: {
                    productoId: numeroProductoId,
                    productoSelect: idframecolorNew
                },
            }).done(function (response) {
                Swal.fire({
                    title: "Color agregado Correctamente",
                    text: "Clickeame",
                    icon: "success"
                });
                if (response.success) {
                    console.log(response)
                    $('#location-select-' + idproducto).css({'background-color': 'rgba(139, 242, 113, 0.5)'});
                    $('#check-location-' + idproducto).removeClass('d-none');
                } else {

                    $('#location-select-' + idproducto).css({'background-color': 'rgba(242, 113, 113, 0.5)'});
                }


                setTimeout(function () {
                    $('#location-select-' + idproducto).css({'background-color': '#FFFFFF'});
                    $('#check-location-' + idproducto).addClass('d-none');
                }, 1500);
            }).fail(function (response) {
                alert('Error al actualizar el producto');
                console.log(response);
            });
        }

        function setMaterial(idproducto) {
            // Obtén el valor seleccionado del select correspondiente

            let idframeMaterialNew = idproducto.value;
            let productoId = idproducto.id;

            let numeroProductoId = productoId.split('-').pop();


            $.ajax({
                url: "{{ path('cambioProducto') }}",
                type: 'GET',
                dataType: 'json',
                data: {
                    productoId: numeroProductoId,
                    idframeMaterialNew: idframeMaterialNew
                },
            }).done(function (response) {
                Swal.fire({
                    title: "Color agregado Correctamente",
                    text: "Clickeame",
                    icon: "success"
                });
                if (response.success) {
                    console.log(response)
                    $('#location-select-' + idproducto).css({'background-color': 'rgba(139, 242, 113, 0.5)'});
                    $('#check-location-' + idproducto).removeClass('d-none');
                } else {

                    $('#location-select-' + idproducto).css({'background-color': 'rgba(242, 113, 113, 0.5)'});
                }


                setTimeout(function () {
                    $('#location-select-' + idproducto).css({'background-color': '#FFFFFF'});
                    $('#check-location-' + idproducto).addClass('d-none');
                }, 1500);
            }).fail(function (response) {
                alert('Error al actualizar el producto');
                console.log(response);
            });
        }*/

        function actualizarProducto(idSelect, tipoCambio) {
            const valorSeleccionado = idSelect.value;
            const idProducto = idSelect.id.split('-').pop();

            const data = {
                productoId: idProducto,
            };

            if (tipoCambio === "color") {
                data.productoSelect = valorSeleccionado;
            } else if (tipoCambio === "material") {
                data.idframeMaterialNew = valorSeleccionado;
            }

            $.ajax({
                url: "{{ path('cambioProducto') }}",
                type: 'GET',
                dataType: 'json',
                data: data,
            }).done(function (response) {
                Swal.fire({
                    title: response.success ? "Cambio realizado correctamente" : "Error en la actualización",
                    text: response.success ? "El producto se actualizó con éxito" : response.msj || "Intenta de nuevo",
                    icon: response.success ? "success" : "error",
                });

                // Cambiar estilo visual según el resultado
                const backgroundColor = response.success
                    ? 'rgba(139, 242, 113, 0.5)'  // Éxito: Verde claro
                    : 'rgba(242, 113, 113, 0.5)'; // Error: Rojo claro

                $(`#${idSelect.id}`).css({ 'background-color': backgroundColor });
                $(`#check-location-${idProducto}`).toggleClass('d-none', !response.success);

                // Restaurar el estilo original después de 1.5s
                setTimeout(() => {
                    $(`#${idSelect.id}`).css({ 'background-color': '#FFFFFF' });
                    $(`#check-location-${idProducto}`).addClass('d-none');
                }, 1500);
            }).fail(function (response) {
                Swal.fire({
                    title: "Error en el servidor",
                    text: "No fue posible realizar el cambio",
                    icon: "error",
                });
                console.error("Error en AJAX: ", response);
            });
        }


    </script>

{% endblock %}
