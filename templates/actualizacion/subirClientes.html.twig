{% extends 'admin/layout.html.twig' %}

{% block content %}
    <input id="guardar-clientes" type="hidden" class="form-control" value="/actualizacion/clientes/guardar">


    <div class="row">
        <div class="col-md-12 container-fluid">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <div class="row">
                        <div class="col-md-12">
                            <h4 class="card-title mb-0">Subir Clientes Excel</h4>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row my-3">
                        <div class="col-md-12 text-center">
                            <!-- Botón para buscar un archivo -->
                            <button id="picker_button" class="btn btn-success d-none" onclick="createPicker()">
                                <i class="fas fa-search"></i> Buscar archivo
                            </button>

                            <!-- Botón para iniciar sesión -->
                            <button id="authorize_button" class="btn btn-primary d-none" onclick="handleAuthClick()">
                                <i class="fab fa-google"></i> Iniciar sesión
                            </button>

                            <!-- Botón para cerrar sesión -->
                            <button id="signout_button" class="btn btn-danger d-none" onclick="handleSignoutClick()">
                                <i class="fas fa-sign-out-alt"></i> Cerrar sesión
                            </button>
                        </div>
                    </div>

                    <br>

                    <div class="row">
                        <div class="col-md-12">
                            <table id="previewTable" class="table table-hover table-bordered table-striped">
                                <thead class="table-dark">
                                <tr>
                                    <th>Nombre</th>
                                    <th>Apellido Paterno</th>
                                    <th>Apellido Materno</th>
                                    <th>Puesto</th>
                                    <th>Teléfono</th>
                                    <th>Email</th>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                            <button id="SubirArchivos" class="btn btn-success d-none position-absolute bottom-0 end-0" onclick="ActualizarClientes()">
                                <i class="fa fa-check" aria-hidden="true"></i> Subir Clientes
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>

        $(document).ready(function () {
            $('#previewTable').DataTable({
                paging: true,        // Habilitar paginación
                searching: true,     // Habilitar búsqueda
                ordering: true,      // Habilitar ordenamiento
                responsive: true,    // Hacer la tabla responsive
                dom: 'Bfrtip',       // Mostrar botones de exportación
                buttons: [],
                language: {
                    url: "https://cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json" // Idioma español
                }
            });
        });


        // Configuración de la API de Google
        const CLIENT_ID = '920138527269-euigacmlshkhcm2493hrj2kuebooknro.apps.googleusercontent.com';
        const API_KEY = 'AIzaSyB1-aw7lXPz5acAYPnQF5XjPE66stFeAVk';
        const DISCOVERY_DOC = 'https://sheets.googleapis.com/$discovery/rest?version=v4';
        const SCOPES = 'https://www.googleapis.com/auth/drive.readonly https://www.googleapis.com/auth/spreadsheets.readonly';
        const APP_ID = 'pv360-416621';

        // Variables globales
        let tokenClient;
        let accessToken = null;
        let pickerInited = false;
        let gisInited = false;
        let gapiInited = false;

        // Cargar el cliente de la API de Google
        function gapiLoaded() {
            gapi.load('client:picker', initializePicker);
            gapi.load('client', initializeGapiClient);
        }

        async function initializeGapiClient() {
            await gapi.client.init({apiKey: API_KEY, discoveryDocs: [DISCOVERY_DOC]});
            gapiInited = true;
            maybeEnableButtons();
        }

        async function initializePicker() {
            await gapi.client.load('https://www.googleapis.com/discovery/v1/apis/drive/v3/rest');
            pickerInited = true;
            maybeEnableButtons();
        }

        // Configurar el cliente de autenticación de Google
        function gisLoaded() {
            tokenClient = google.accounts.oauth2.initTokenClient({
                client_id: CLIENT_ID,
                scope: SCOPES,
                callback: '', // Se define más adelante
            });
            gisInited = true;
            maybeEnableButtons();
        }

        // Habilitar los botones después de inicializar las APIs
        function maybeEnableButtons() {
            if (pickerInited && gisInited && gapiInited) {
                $("#authorize_button").removeClass("d-none");
            }
        }

        // Manejar el inicio de sesión
        function handleAuthClick() {
            tokenClient.callback = async (response) => {
                if (response.error !== undefined) {
                    throw (response);
                }
                accessToken = response.access_token;
                $("#signout_button").removeClass("d-none");
                $("#picker_button").removeClass("d-none");
                $("#authorize_button").text("Cambiar de cuenta");

                await createPicker();
            };

            if (accessToken === null) {
                tokenClient.requestAccessToken({prompt: 'consent'});
            } else {
                tokenClient.requestAccessToken({prompt: ''});
            }
        }

        // Manejar el cierre de sesión
        function handleSignoutClick() {
            if (accessToken) {
                accessToken = null;
                google.accounts.oauth2.revoke(accessToken);
                $("#authorize_button").text("Iniciar sesión");
                $("#signout_button").addClass("d-none");
                $("#picker_button").addClass("d-none");
            }
        }

        // Crear el selector de archivos
        function createPicker() {
            const view = new google.picker.View(google.picker.ViewId.DOCS);
            view.setMimeTypes('application/vnd.google-apps.spreadsheet');
            const picker = new google.picker.PickerBuilder()
                .enableFeature(google.picker.Feature.NAV_HIDDEN) // Ocultar barra de navegación
                .setDeveloperKey(API_KEY) // Clave del desarrollador
                .setAppId(APP_ID) // ID de la aplicación
                .setOAuthToken(accessToken) // Token de autenticación OAuth
                .addView(view) // Agregar vista configurada
                .addView(new google.picker.DocsUploadView()) // Agregar vista para subir documentos
                .setCallback(pickerCallback) // Callback para manejar la selección
                .build();
            picker.setVisible(true);
        }

        function populateTable(data) {
            const table = $('#previewTable').DataTable();
            table.clear(); // Limpia la tabla antes de llenarla

            data.forEach(cliente => {
                table.row.add([
                    cliente.nombre,
                    cliente.apellidoPaterno,
                    cliente.apellidoMaterno,
                    cliente.puesto,
                    cliente.telefono,
                    cliente.email,
                ]).draw(false); // Agrega cada fila y actualiza la tabla
            });
        }


        // Separar nombres completos en componentes
        function splitFullName(fullName) {
            const parts = fullName.trim().split(/\s+/); // Divide por espacios, ignorando extras
            const apellidoMaterno = parts.pop(); // Último elemento
            const apellidoPaterno = parts.pop(); // Penúltimo elemento
            const nombre = parts.join(' '); // Todo lo que queda es el nombre
            return { nombre, apellidoPaterno, apellidoMaterno };
        }



        // Callback para manejar la selección del archivo
        async function pickerCallback(data) {
            if (data.action === google.picker.Action.PICKED) {
                const document = data[google.picker.Response.DOCUMENTS][0];
                const fileId = document[google.picker.Document.ID];

                try {
                    // Obtenemos metadata del archivo (incluye nombres de las hojas)
                    const metadataResponse = await gapi.client.sheets.spreadsheets.get({
                        spreadsheetId: fileId,
                    });

                    console.log('Metadata:', metadataResponse);

                    // Obtener el nombre de la primera hoja
                    const sheets = metadataResponse.result.sheets;
                    if (!sheets || sheets.length === 0) {
                        console.error('El archivo no contiene hojas.');
                        return;
                    }
                    const sheetName = sheets[0].properties.title; // Primera hoja
                    console.log('Usando la hoja:', sheetName);

                    // Leer datos de la hoja seleccionada
                    const response = await gapi.client.sheets.spreadsheets.values.get({
                        spreadsheetId: fileId,
                        range: `${sheetName}!A2:Z`,
                        majorDimension: 'ROWS',
                    });

                    const values = response.result.values;

                    if (values && values.length > 0) {
                        const processedData = processClientData(values);
                        $("#SubirArchivos").removeClass("d-none");

                        // Llenar la tabla con los datos procesados
                        populateTable(processedData);

                        // Opcional: Enviar los datos al backend
                        // await sendDataToBackend(processedData);
                    }
                } catch (error) {
                    console.error('Error al obtener datos de la hoja:', error);
                }
            }
        }




        // Procesar los datos del cliente desde Google Sheets
        function processClientData(sheetData) {
            return sheetData.map(row => {
                const {nombre, apellidoPaterno, apellidoMaterno} = splitFullName(row[2]);

                console.log({nombre, apellidoPaterno, apellidoMaterno});
                return {
                    nombre,
                    apellidoPaterno,
                    apellidoMaterno,
                    puesto: row[3],
                    edad: row[5],
                    email: row[7],
                    telefono: row[8],
                };
            });
        }


        function ActualizarClientes() {
            console.log('Actualizar clientes LENON');
            const table = $('#previewTable').DataTable();
            const data = [];

            // Iterar sobre cada fila de la tabla para extraer los datos
            table.rows().every(function () {
                const rowData = this.data();
                data.push({
                    nombre: rowData[0],
                    apellidoPaterno: rowData[1],
                    apellidoMaterno: rowData[2],
                    puesto: rowData[3],
                    celular: rowData[4],
                    correoElectronico: rowData[5],
                    // Puedes añadir más campos aquí si son necesarios
                });
            });

            // Validar si hay datos en la tabla
            if (data.length === 0) {
                alert('No hay datos para enviar. Por favor, cargue un archivo primero.');
                return;
            }

            var url = $("#guardar-clientes").val();

            // Enviar los datos al controlador mediante AJAX
            $.ajax({
                url:url, // Ruta del controlador
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ clientes: data }), // Datos enviados en formato JSON
                success: function (response) {
                    if (response.status === 'success') {
                        alert('Clientes guardados exitosamente.');
                        // Opcional: Limpiar la tabla
                        table.clear().draw();
                    } else {
                        alert('Error al guardar los clientes. Inténtelo de nuevo.');
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error al enviar los datos al servidor:', error);
                    alert('Ocurrió un error al enviar los datos. Consulte la consola para más detalles.');
                },
            });
        }

    </script>

    <script async defer src="https://apis.google.com/js/api.js" onload="gapiLoaded()"></script>
    <script async defer src="https://accounts.google.com/gsi/client" onload="gisLoaded()"></script>
    <script src="https://cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>

{% endblock %}
