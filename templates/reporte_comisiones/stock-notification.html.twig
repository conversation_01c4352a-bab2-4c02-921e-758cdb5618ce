{% extends 'base.html.twig' %}

{% block title %}Notificación de Stock{% endblock %}

{% block body %}
<div class="container">
    <h1>Notificación de Stock Bajo</h1>

    {% if productosBajoStock %}
    <h2>Productos con stock bajo:</h2>
    <table class="table" border="1">
        <thead>
            <tr>
                <th>Producto</th>
                <th>Codigo de Barras</th>
                <th>Stock Actual</th>
                <th>Stock Mínimo Requerido</th>
                
            </tr>
        </thead>
        <tbody>
            {% for producto in productosBajoStock %}
            <tr>
                <td>{{ producto.ProductoNombre }}</td>
                <td>{{ producto.CodigodeBarras }}</td>
                <td>{{ producto.StockCantidad }}</td>
                <td>{{ producto.Cantidadminima }}</td>

            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p>No hay productos con stock bajo.</p>
    {% endif %}


    <h2>Status del correo:</h2>
    {% if sent %}
    <p>El correo ha sido enviado con la notificación de stock bajo.</p>
    {% else %}
    <p>Ocurrió un error al enviar el correo.</p>
    {% endif %}

</div>
{% endblock %}
