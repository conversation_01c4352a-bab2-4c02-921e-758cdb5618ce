{% extends 'admin/layout.html.twig' %}

{% block title %}| Comisiones{% endblock %}

{% block content %}
    <link rel="stylesheet" href="{{ asset('css/comisiones.css') }}">
    <div class="bodyb">
        <h1 class="text-center text-white mb-4">Reporte de Comisiones</h1>
        <div class="container mt-4">
            <h2 class="text-center mb-4">FILTROS</h2>
            <div class="row mb-3">
                <div class="col-md-4 mb-2">
                    <select id="empresaD" class="form-control custom-select">
                        <option value="-1">Selecciona una empresa</option>
                        {% for empresa in empresas %}
                            <option value="{{ empresa.idempresa }}">{{ empresa.nombre }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4 mb-2">
                    <select id="sucursal" class="form-control custom-select">
                        <option value="-1">Selecciona la sucursal</option>
                    </select>
                </div>
                <div class="col-md-4 mb-2">
                    <select id="vendedor" class="form-control custom-select">
                        <option value="-1">Selecciona al vendedor</option>
                    </select>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-6 mb-2">
                    <select id="tipoPago" class="form-control custom-select">
                        <option value="-1">Tipo de Pago</option>
                        {% for tipopagos in tipopago %}
                            <option value="{{ tipopagos.idpaymenttype }}">{{ tipopagos.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 mb-2">
                    <select id="ventaLiquidada" class="form-control custom-select">
                        <option value="-1">Tipo de venta</option>
                        <option value="1">Venta Liquidada</option>
                        <option value="0">No Liquidada</option>
                    </select>
                </div>
            </div>
            <div class="row mb-3 justify-content-center align-items-center">
                <div class="col-md-8 d-flex justify-content-center align-items-center">
                    <label for="rango" class="m-0 rangoFecha">Rango de Fecha:</label>
                    <input id="fecha-inicio-rango-dia" type="text" autocomplete="off" class="input-sm form-control mx-2" name="start"/>
                    <span class="rangoFecha">a</span>
                    <input id="fecha-fin-rango-dia" type="text" autocomplete="off" class="input-sm form-control mx-2" name="end"/>
                    <button class="btn btn-warning" onclick="resetRangoFechaDias()">
                        <i class="fa fa-eraser" aria-hidden="true"></i>
                    </button>
                </div>
            </div>
            <div class="row mb-3 justify-content-center">
                <div class="col-md-4 text-center">
                    <button class="btn btn-primary btn-block" id="buscarBtn" onclick="buscarComisiones();">Buscar</button>
                </div>
            </div>
        </div>
        <br>
        <div id="tablaComisiones" class="container"></div>
    </div>

    <script>
        $(document).ready(function() {
            var sucursalesUrl = '{{ path('get_sucursales', {'empresaId': 0}) }}';
            var vendedoresUrl = '{{ path('get_vendedores', {'sucursalId': 0}) }}';

            $('#empresaD').change(function() {
                var empresaId = $(this).val();
                if (empresaId) {
                    var url = sucursalesUrl.replace('/0', '/' + empresaId);
                    $.ajax({
                        url: url,
                        method: 'GET',
                        success: function(data) {
                            var sucursalSelect = $('#sucursal');
                            sucursalSelect.empty();
                            sucursalSelect.append('<option value="">Selecciona la sucursal</option>');
                            $.each(data, function(index, sucursal) {
                                sucursalSelect.append('<option value="' + sucursal.idsucursal + '">' + sucursal.nombre + '</option>');
                            });
                        }
                    });
                } else {
                    var sucursalSelect = $('#sucursal');
                    sucursalSelect.empty();
                    sucursalSelect.append('<option value="">Selecciona la sucursal</option>');
                }
            });

            $('#sucursal').change(function() {
                var sucursalId = $(this).val();
                if (sucursalId) {
                    var url = vendedoresUrl.replace('/0', '/' + sucursalId);
                    $.ajax({
                        url: url,
                        method: 'GET',
                        success: function(data) {
                            var vendedorSelect = $('#vendedor');
                            vendedorSelect.empty();
                            vendedorSelect.append('<option value="">Selecciona al vendedor</option>');
                            $.each(data, function(index, vendedor) {
                                vendedorSelect.append('<option value="' + vendedor.idusuario + '">' + vendedor.nombre + ' ' + vendedor.apellidopaterno + ' ' + vendedor.apellidomaterno + '</option>');
                            });
                        }
                    });
                } else {
                    var vendedorSelect = $('#vendedor');
                    vendedorSelect.empty();
                    vendedorSelect.append('<option value="">Selecciona al vendedor</option>');
                }
            });
        });

        function buscarComisiones() {
            var sucursal = $('#sucursal').val();
            var vendedor = $('#vendedor').val();
            var tipoPago = $('#tipoPago').val();
            var ventaLiquidada = $('#ventaLiquidada').val();
            var fechaInicio = $('#fecha-inicio-rango-dia').val();
            var fechaFin = $('#fecha-fin-rango-dia').val();

            var datos = {
                sucursal: sucursal,
                vendedor: vendedor,
                ventaLiquidada: ventaLiquidada,
            };

            console.log(datos); // Aquí puedes ver los datos recolectados en la consola

            $.ajax({
                url: '{{ path('tablaComisiones') }}',
                method: 'POST',
                data: datos,
                success: function(response) {
                    console.log(response);
                    var html = '<table id="comisionesTable" class="table table-striped table-bordered">';
                    html += '<thead><tr><th>Folio de venta</th><th>Sucursal</th><th>Vendedor</th><th>Liquidado</th><th>Fecha de venta</th><th>Número de pagos</th><th>Monto de pago</th></tr></thead>';
                    html += '<tbody>';

                    $.each(response.ventas, function(index, venta) {
                        var fechaVenta = venta.fechaventa ? new Date(venta.fechaventa) : null;
                        var fechaVentaStr = fechaVenta && !isNaN(fechaVenta) ? fechaVenta.toISOString().slice(0, 10) : 'N/A';

                        html += '<tr>';
                        html += '<td>' + venta.folio + '</td>';
                        html += '<td>' + venta.sucursal + '</td>';
                        html += '<td>' + venta.vendedor + '</td>';
                        html += '<td>' + (venta.liquidada ? 'Sí' : 'No') + '</td>';
                        html += '<td>' + fechaVentaStr + '</td>';
                        html += '<td>' + venta.numeroPagos + '</td>';
                        html += '<td>' + venta.monto + '</td>';
                        html += '</tr>';
                    });

                    html += '</tbody></table>';

                    $("#tablaComisiones").html(html);

                    // Inicializar DataTables después de agregar el nuevo contenido
                    $('#comisionesTable').DataTable({
                        "paging": true,
                        "searching": true,
                        "ordering": true,
                        "info": true,
                        "autoWidth": false,
                        "lengthChange": true,
                        "language": {
                            "lengthMenu": "Mostrar _MENU_ registros por página",
                            "zeroRecords": "No se encontraron resultados",
                            "info": "Mostrando página _PAGE_ de _PAGES_",
                            "infoEmpty": "No hay registros disponibles",
                            "infoFiltered": "(filtrado de _MAX_ registros en total)",
                            "search": "Buscar:",
                            "paginate": {
                                "first": "Primero",
                                "last": "Último",
                                "next": "Siguiente",
                                "previous": "Anterior"
                            }
                        }
                    });
                }
            });
        }
    </script>

    <script>
        jQuery(function ($) {
            $('#fecha-inicio-rango-dia').datetimepicker({
                format: 'Y-m-d',
                timepicker: false,
                minDate: '1900/01/01',
                maxDate: new Date(),
                lang: 'es'
            });

            $('#fecha-fin-rango-dia').datetimepicker({
                format: 'Y-m-d',
                timepicker: false,
                minDate: '1900/01/01',
                maxDate: new Date(),
                lang: 'es'
            });
        });

        function resetRangoFechaDias() {
            $("#fecha-inicio-rango-dia").val("");
            $("#fecha-fin-rango-dia").val("");
        }
    </script>
{% endblock %}
