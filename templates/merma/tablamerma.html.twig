<div class="container mt-3">
	<div class="my-3">
		<button class="btn btn-success" data-toggle="modal" data-target="#modalAgregarMerma">
			<i class="fa-solid fa-plus"></i>Agregar <PERSON>
		</button>
	</div>

	<div class="card">
		<div class="card-body table-responsive">
			<table class="table" id="tablaMermas">
				<thead>
					<tr class="text-start">
						<th class="text-start text-center" scope="col|">#</th>
						<th class="text-start text-center" scope="col">Folio</th>
						<th class="text-start text-center" scope="col">Tipo</th>
						<th class="text-start text-center" scope="col">Producto</th>
						<th class="text-start text-center" scope="col">SKU</th>
						<th class="text-start text-center" scope="col">Código de <PERSON></th>
						<th class="text-start text-center" scope="col">Clave</th>
						<th class="text-start text-center" scope="col">Fecha</th>
						<th class="text-start text-center" scope="col">Responsable</th>
						<th class="text-start text-center" scope="col">Cantidad</th>
						<th class="text-start text-center" scope="col">Costo unitario</th>
						<th class="text-start text-center" scope="col">Perdida Total</th>
						<th class="text-start text-center" scope="col">Opciones</th>
					</tr>
				</thead>
				<tbody id="tableBodyMerma">
					{% set suma = 0 %}
					{% for index,merma in mermas %}
						<tr>
							<td class="text-center align-middle ">{{index + 1}}</td>
							<td class="text-center align-middle ">{{merma.folio}}</td>
							<td class="text-center align-middle ">{{merma.departurecauseIddeparturecause.name}}</td>
							<td class="text-center align-middle ">{{merma.stockIdstock.productoIdproducto.modelo}}</td>
							<td class="text-center align-middle ">{{merma.stockIdstock.codigobarras}}</td>
							<td class="text-center align-middle ">{{merma.stockIdstock.productoIdproducto.codigobarrasuniversal}}</td>
							<td class="text-center align-middle ">{{merma.stockIdstock.productoIdproducto.clave}}</td>
							<td class="text-center align-middle ">{{merma.fechaincidencia|date("d/m/Y")}}</td>
							<td class="text-center align-middle ">{{merma.usuarioIdusuario.nombre}}</td>
							<td class="text-center align-middle ">{{merma.cantidad}}</td>
							<td class="text-center align-middle ">${{merma.stockIdstock.productoIdproducto.costo}}</td>
							<td class="text-center align-middle ">${{(merma.stockIdstock.productoIdproducto.costo * merma.cantidad)|number_format(2, '.', ',')}}</td>
							{% set suma = suma + merma.stockIdstock.productoIdproducto.costo * merma.cantidad %}
							<td class="text-center align-middle ">
								{% if rol == "ROLE_SUPER_ADMIN" and merma.status == '1' %}
									<button class="btn btn-danger" data-toggle="modal" data-target="#modalEliminarMerma" onclick="mostrarEliminarMermaFormulario({{merma.idmerma}})">
										<i class="fa fa-trash"></i>
									</button>
								{% endif %}
								<button onclick="mostrarDetalleMerma({{merma.idmerma}})" type="button" class="btn btn-primary" data-toggle="modal" data-target="#modalDetalleMerma">
									Detalle de salida
								</button>
							</td>
						</tr>
					{% endfor %}
				</tbody>
			</table>
		</div>
	</div>
</div>

<div class="col-md-12 text-center align-middle">
	<h4>Total de perdidas = ${{suma|number_format(2, '.', ',')}}</h4>
	<br>
</div>

{% include 'merma/modal_formulario_merma.html.twig' %}

<!-- Modal -->
<div class="mod fade" id="modalDetalleMerma" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true"><div class="modal-dialog modal-lg" role="document">
	<div class="modal-content" style="border-radius:10px">
		<div class="modal-header bg-primary">
			<h1 class="modal-title fs-5" id="exampleModalLabel">DETALLE DE SALIDA</h1>
			<button type="button" class="close btn-close-white" data-dismiss="modal" aria-label="Close">
				<span aria-hidden="true">&times;</span>
			</button>
		</div>
		<div class="modal-body">
			<div id="detalleMerma"></div>
		</div>
		<div class="modal-footer">
			<button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
		</div>
	</div>
</div>

<div class="mod fade" id="modalEliminarMerma" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true"><div class="modal-dialog" role="document">
	<div class="modal-content" style="border-radius:10px">
		<div class="modal-header bg-primary">
			<h1 class="modal-title fs-5" id="exampleModalLabel">CANCELAR SALIDA</h1>
			<button type="button" class="close btn-close-white" data-dismiss="modal" aria-label="Close">
				<span aria-hidden="true">&times;</span>
			</button>
		</div>
		<div class="modal-body">
			<div id="eliminarMermaFormulario"></div>
		</div>
		<div class="modal-footer">
			<button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
		</div>
	</div>
</div>


<script>
	var formSubmitted = 0;
	var globalIdMerma = null;

	$(document).ready(function () {
		$('#tablaMermas').DataTable({
			"order": [
				[4, "desc"]
			],
			"paging": true,
			"searching": true,
			"columnDefs": [
				{
					"targets": -1,
					"orderable": false
				}
			],
			dom: 'Bfrtip',
			buttons: [
				{
					extend: 'excel',
					text: 'Excel',
					className: 'btn btn-success'
				}
			]
		});

		obtenerFormularioAgregarMerma();
		selectOutputType();
	});

	function setFormFlag(value) {
		formSubmitted = value;
	}

	$('#modalAgregarMerma').on('hidden.bs.modal', function () {
		if (formSubmitted == 1)
			obtenerTablaMerma()
		;
	})

	function mostrarDetalleMerma(idmerma) {
		globalIdMerma = idmerma;

		$.ajax({
			url: "{{ path('detalle-merma') }}",
			method: "POST",
			data: {
				idmerma: idmerma
			},
			beforeSend: function () {
				loadingGif("detalleMerma");
			},
			dataType: "html",
			success: function (html) {
				$("#detalleMerma").html(html);
			},
			error: function (xhr, status, error) {
				console.error("Error al obtener el detalle de la merma:", error);
			}
		});
	}

	function mostrarEliminarMermaFormulario(idmerma) {
		$.ajax({
			url: "{{ path('eliminar-merma-formulario') }}",
			method: "POST",
			data: {
				idmerma: idmerma
			},
			beforeSend: loadingGif("eliminarMermaFormulario"),
			dataType: "html",
			success: function (html) {
				$("#eliminarMermaFormulario").html(html);
			}

		});
	}

	document.addEventListener("DOMContentLoaded", function () {
		document.querySelector('[data-target="#modalAgregarMerma"]').addEventListener("click", function () {
			$("#modalAgregarMerma").modal("show");
		});
	});
</script>
