

{% if sucursales != null %}

<select class="form-control" id="select-sucursal">

    {% for index, sucursal in sucursales %}

        <option value={{index}}>
        {{sucursal}}
        </option>


    {% endfor %}

</select>

{% endif %}

{% if producto != null %}
<input id="idstock" type="hidden" value="{{producto.idstock}}" disabled>
<table class="table" >
<tr>
<td><strong>Modelo: </strong></td>
<td>{{producto.modelo}}</td>
</tr>
<tr>
<td><strong>{% if producto.masivounico == "1" %}SKU: {% else %}Código: {% endif %} </strong></td>
<td>{{codigo}}</td>
</tr>
<tr>
<td><strong>Sucursal: </strong></td>
<td>{{producto.sucursal}}</td>
</tr>
<tr>
<td><strong>Cantidad: </strong></td>
<td><input id="cantidad" class="form-control" min="1" max="{{ producto.cantidad }}" type="number" value="1" {% if producto.masivounico == "1" %}disabled {% endif %} ></td>
</tr>

<table>

{% endif %}

<script>
    
    MensajeProducto("{{msj}}",{{exito}});

    cantidad = 0;

    $('#select-sucursal').on('change', function() {
        //console.log( this.value );

        idstock = $("#idstock").val();
        
        setIdstock(idstock,cantidad);

        BuscarProducto(this.value);

    });

    $('#cantidad').on('change', function() {
        
        cantidadInput = $("#cantidad").val();
        cantidad = cantidadInput;
    });

    $(document).ready(function(){

        idstock = $("#idstock").val();

        cantidadInput = $("#cantidad").val();
        cantidad = cantidadInput;

        setIdstock(idstock,cantidad);

        

        {% if sucursales != null %}

            {% if sucursal != -1 %}

                $('#select-sucursal').val({{sucursal}});
            
            {% endif %}

        {% endif %}

    });

    

</script>
