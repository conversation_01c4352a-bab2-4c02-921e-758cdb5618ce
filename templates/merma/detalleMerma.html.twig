<table class="table">
	<caption class="caption-top">Salida</caption>
	<tr>
		<table class="table">


			<tbody class="text-center align-middle ">

				<tr>
					<td>
						<span style="font-weight:bold">Tipo de salida:
						</span>
						{{merma.departurecauseIddeparturecause.departuretypeIddeparturetype.name}}
					</td>
					<td>
						<span style="font-weight:bold">Folio:
						</span>
						{{merma.folio}}</td>
				</tr>
				<tr>
					<td>
						<span style="font-weight:bold">Fecha de incidencia:
						</span>
						{{merma.fechaincidencia|date("d/m/Y")}}</td>

					<td>
						<span style="font-weight:bold">Responsable:
						</span>
						{{merma.usuarioIdusuario.nombre}}
						{{merma.usuarioIdusuario.apellidopaterno}}
						{{merma.usuarioIdusuario.apellidomaterno}}</td>
				</tr>
				<tr>
					<td>
						<span style="font-weight:bold">Salida:
						</span>
						{{merma.departurecauseIddeparturecause.name}}
					</td>
					<td>
						<span style="font-weight:bold">Detalle de incidencia:
						</span>
						{{merma.detalleincidencia}}</td>
				</tr>
			</tbody>

		</table>
	</tr>

	<tr class="align-middle text-center">

		<table class="table">
			<caption class="caption-top">Stock</caption>
			<thead class="align-middle text-center">

				<th>Cantidad</th>
				<th>Producto</th>
				<th>Costo unitario</th>
				<th>Perdida total</th>

			</thead>
			<tbody class="align-middle text-center">

				<td>{{merma.cantidad}}</td>
				<td>{{merma.stockIdstock.productoIdproducto.modelo}}</td>
				<td>${{merma.stockIdstock.productoIdproducto.costo}}</td>
				<td>${{(merma.stockIdstock.productoIdproducto.costo * merma.cantidad)|number_format(2, '.', ',')}}</td>

			</tbody>

		</table>
	</tr>

	{% if StockVenta != null %}
	<tr class="align-middle text-center">

		<table class="table">
			<caption class="caption-top">Venta</caption>
			<thead class="align-middle text-center">

				<th>Folio</th>
				<th>SKU / Código de barras </th>
				<th>Modelo</th>
				<th>Perdida total</th>

			</thead>
			<tbody class="align-middle text-center">

				<td>{{StockVenta.ventaIdventa.folio}}</td>
				<td>{{barcode}}</td>
				<td>{{StockVenta.stockIdstock.productoIdproducto.modelo}}</td>
				<td>${{(StockVenta.costo * StockVenta.cantidad)|number_format(2, '.', ',')}}</td>

			</tbody>

		</table>
	</tr>
	{% endif %}

	{% if merma.fechaborrado != null %}
		<tr>
			<table class="table">
				<caption class="caption-top">Cancelación</caption>
				<thead class="align-middle text-center">

					<th>Fecha</th>
					<th>Responsable</th>
					<th>Motivo</th>

				</thead>
				<tbody class="align-middle text-center">

					<td>{{merma.fechaborrado|date("d/m/Y")}}</td>
					<td>{{merma.usuarioresponsableborrar.nombre}}
						{{merma.usuarioresponsableborrar.apellidopaterno}}
						{{merma.usuarioresponsableborrar.apellidomaterno}}</td>
					<td>{{merma.detalleborrado}}</td>

				</tbody>

			</table>

		</tr>
	{% endif %}

</table>

<div class="row">
	<div class="col-md-6">
		<form id="form-upload-merma-document" method="POST" enctype="multipart/form-data">
			<input type="hidden" name="mermaId" value="{{ merma.idmerma }}">
			<div class="mb-3">
				<label for="mermaDocument" class="form-label">Agregar Documento de Merma</label>
				<input class="form-control" type="file" id="mermaDocument" name="file">
			</div>
            <button type="button" onclick="agregarDocumento();" class="btn btn-primary">Subir Documentos</button>
		</form>
	</div>
    <div class="col-md-6">
    <table class="table" id="docMerma">
        <thead>
            <tr>
                <th>Nombre del Archivo</th>
                <th>Fecha de Creación</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tbody>
            {% for documento in Documentosmerma %}
                <tr>
                    <td>{{ documento.nombre }}</td>
                    <td>{{ documento.creacion|date('Y-m-d H:i:s') }}</td>
                    <td>
                        <button class="btn btn-primary" onclick="abrirVisualizadorDocumento('{{ documento.idusuario }}', '{{ documento.idmerma }}', '{{ documento.filemerma }}')">
                            Visualizar
                        </button>
                    </td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
</div>

<!-- Modal para Visualizar Documento -->
<div class="modal fade" id="visualizarDocumentoModal" tabindex="-1" aria-labelledby="visualizarDocumentoModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="visualizarDocumentoModalLabel">Visualizar Documento</h5>
        <button type="button" class="close" data-dismiss="modalVisualizar" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <iframe id="documentoFrame" src="" width="100%" height="500px" frameborder="0"></iframe>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modalVisualizar">Cerrar</button>
      </div>
    </div>
  </div>
</div>




<script>

    function agregarDocumento() {
        var form = document.getElementById('form-upload-merma-document');
        var formData = new FormData(form);

    
        var url = '/subir-documento-merma';
    
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                Swal.fire({
                    title: '¡Éxito!',
                    text: 'Documento subido con éxito.',
                    icon: 'success',
                    confirmButtonText: 'Ok'
                });
                $("#mermaDocument").val(null);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error',
                    text: 'Error al subir el documento: ' + textStatus + ', ' + errorThrown,
                    icon: 'error',
                    confirmButtonText: 'Ok'
                });
            }
        });
    }

    function abrirVisualizadorDocumento(userId, mermaId, fileName) {
        const filePath = `/uploads/salidas/${userId}/${mermaId}/${fileName}`;
        
        $('#documentoFrame').attr('src', filePath);
        
        $('#visualizarDocumentoModal').modal('show');
    }
    
</script>