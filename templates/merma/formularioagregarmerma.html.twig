{{ form_start(form, {'attr': {'id': 'FormularioAgregarMerma'}}) }}
{{ form_errors(form) }}
<div class="row">
	<div class="col-md-6 ">
		<div id="departure-cause-selector">
			<p class='text-center pt-7'>Cargando...</p>
		</div>
		<p id="errorDepartureMessage"></p>
	</div>
	<div class="col-md-6 ">
		{{ form_row(form.fechaincidencia) }}
	</div>
</div>
{{ form_row(form.detalleincidencia) }}</div><div class="col-md-12 ">
<br></div><div class="row">

<div class="col-md-6 ">
	<input type="text" class="form-control" id="codigoBarras" placeholder="Ingresa el código o SKU del producto" autocomplete="off">
	<p id="mensajeErrorProducto"></p>
	<button type="button" class="btn btn-primary" onclick="BuscarProducto()">Buscar</button>
</div>

<div class="col-md-6">
	<div class="card-body signature">
		<p class="subtable">Subir Documentos Salidas</p>
		<form id="form-upload-sale-document" enctype="multipart/form-data" class="needs-validation signature" novalidate>
			<div class="row">
				<div class="col-12">
					<input id="file" type="file" name="file" class="form-control text-center" required>
				</div>
			</div>
			<div class="invalid-feedback">Selecciona un archivo</div>
			<p id="sale-document-error-message"></p>
		</form>
	</div>
</div>

<div id="producto" class="col-md-6 "></div>

<div class="col-12 row d-none" id="warranty-container">
  <h5 class="text-center fw-bold">Escoger producto que se cambiará por garantía</h5>
  <div class="col-md-6 ">
    <select id="enterprise-select" class="form-control">
        {% for Enterprise in enterprises %}
            <option value='{{Enterprise.idempresa}}'>{{Enterprise.nombre}}</option>
        {% endfor %}
    </select>
    <p id="foil-msg"></p>
    <button type="button" class="btn btn-primary" onclick="searchSale()">Buscar</button>
  </div>
  <div class="col-md-6 ">
    <input type="text" class="form-control" id="foil-field" placeholder="Ingresa el folio de la venta" autocomplete="off">
  </div>
  <div class="col-12 " id="products-container"></div>
</div>

<div class="col-md-12">
	<br>
</div>
{{ form_end(form) }}

<script>

  idstock = 0;
  idstockventa = 0;
  cantidad = 0;
  needsSale = false;
  
  jQuery(function ($) {
          $('.fecha').datetimepicker(
              {"pickTime":false,
              "pickDate":true,
              "minDate":"1\/1\/1900",
              "maxDate":null,
              "showToday":true,
              "language":"es_MX",
              "defaultDate":"",
              "disabledDates":[],
              "enabledDates":[],
              "icons":{"time":"fa fa-clock-o","date":"fa fa-calendar","up":"fa fa-chevron-up","down":"fa fa-chevron-down"},
              "useStrict":false,
              "sideBySide":false,
              "daysOfWeekDisabled":[],
              "collapse":true,
              "calendarWeeks":false,
              "viewMode":"days",
              "minViewMode":"days"
              ,"useCurrent":false,
              "useSeconds":false});
  
      });
  
      $(document).ready(function(){
  
          selectDepartureCause();
          {% if exito %}
            $("#modalAgregarMerma").modal('hide');
            setFormFlag(1);
          {% endif %}
      });
      
  
     document.addEventListener("DOMContentLoaded", function () {
          var form = document.getElementById("FormularioAgregarMerma");
  
          form.addEventListener("keydown", function (event) {
              if (event.key === "Enter" || e.keyCode === 13) {
                  event.preventDefault();
              }
          });
  
  
      });

      function guardarFormularioMerma() {
        var form = document.getElementById("FormularioAgregarMerma");
        var departureCauseId = $("#departure-cause-select").val();
        
        if (form.checkValidity()) {
        
          var formData = new FormData(form);

          if (needsSale && idstockventa == 0) foilMessage("No se ha agregado un producto",false)
          else if (departureCauseId > 0){        
            if (idstock > 0){
              Swal.fire({
                title: '¿Está seguro?',
                text: "Se agregará la salida",
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28B463',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Guardar'
              }).then((result) => {
                if (result.value) {
        
                  // Realiza una petición AJAX al servidor para agregar la merma
                  $.ajax({
                    url: "{{path('agregar-merma')}}?idstock="+idstock+"&cantidad="+cantidad+"&idmerma={{idmerma}}"+"&departureCauseId="+departureCauseId+"&idstockventa="+idstockventa,
                    type: 'POST',
                    data: formData,
                    success: function (html) {        
                      // En caso de éxito, actualiza el contenido de un elemento con ID formularioMerma con el HTML devuelto.
                      $("#formularioMerma").html(html);
        
                    },
                    cache: false,
                    contentType: false,
                    processData: false
                  });
        
                } else {
                  // Si se cancela la operación, muestra nuevamente el modal de agregar merma
                  $("#modalAgregarMerma").modal('show');
                }
              })
        
            } else {
              // Muestra un mensaje de error si no se ha agregado un producto
              MensajeProducto("No se ha agregado un producto", false);
            }
        
          } else {
            // Muestra un mensaje pidiendo seleccionar un tipo de salida si no se ha elegido uno
            DepartureMessage("Selecciona un tipo de salida", false);
            $("#departure-cause-select").focus();
          }
                              
        } else {
          // Si el formulario no es válido, solicita al navegador que muestre los mensajes de validación HTML5
          form.reportValidity();
        }
          
      }

  
      function BuscarProducto(sucursal = -1){
  
          var codigo = $("#codigoBarras").val();
  
          $.ajax({
              url: "{{path('merma-agregar-producto')}}/"+codigo+"?sucursalIndex="+sucursal,
              success: function (html) {
  
                  $("#producto").html(html);
  
              },
              data: {sucursalIndex:sucursal},
              cache: false,
              contentType: false,
              processData: false
          });
      }
  
      function MensajeProducto(text,exito){
        color = "red";
  
        if (exito) color = "green";
        $("#mensajeErrorProducto").text(text);
        $("#mensajeErrorProducto").css('color', color);
        $("#codigoBarras").css('color', color);
      }
  
      function DepartureMessage(text,exito){
        color = "red";
  
        if (exito) color = "green";
        $("#errorDepartureMessage").text(text);
        $("#errorDepartureMessage").css('color', color);
      }

      function foilMessage(text,exito){
        color = (exito) ? "green" : "red";
        $("#foil-msg").text(text);
        $("#foil-msg").css('color', color);
      }
  
      $('#producto').on('change', '#idstock', function() {
          idstock = $(this).val();
          // Do something with the value from the child input
      });
  
      function setIdstock(idstockChild,cantidadChild){
        idstock = idstockChild;
        cantidad = cantidadChild;
      }

      function showWarranty(){
        const departureCauseId = $("#departure-cause-select").val()
        const departureCause = $("#departure-cause-select option[value='" + departureCauseId + "']").text()
        idstockventa = 0;
        if (departureCause == "Garantía de venta") {
          $("#warranty-container").removeClass("d-none");
          needsSale = true
        } else {
          $("#warranty-container").addClass("d-none");
          needsSale = false
        }
      }
  
      $("#codigoBarras").on('keyup', function (e) {
        e.preventDefault();
        if (e.key === 'Enter' || e.keyCode === 13) {
          BuscarProducto();
        }
      });

      function changeStockVenta() {
        var selectedProduct = $('input[name="product"]:checked').val()
        idstockventa = selectedProduct
      }

      function selectDepartureCause(){

        departureTypeId = $("#departure-type-select").val();
        $("#warranty-container").addClass("d-none");
        needsSale = false
        $.ajax({
            url: "{{path('merma-select-departure-cause')}}",
            type: 'POST',
            data: {departureTypeId:departureTypeId},
            dataType: "html"
        }).done(function( html ) {
            $("#departure-cause-selector").html(html);


        }).fail(function() {
            alert( "error" );
        });
      }
  
  </script>