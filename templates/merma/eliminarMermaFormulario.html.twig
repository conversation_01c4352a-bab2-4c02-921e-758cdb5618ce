<div>
    <label class="form-label fs-5" for="detalleBorradoMerma">Motivo de cancelción</label>
    <textarea id="detalleBorradoMerma" class="form-control ">
    </textarea>
</div>
<br>
<div>
    <button type="button" class="form-control btn btn-danger" onclick="eliminarMerma({{idmerma}})" data-dismiss="modal">
        Eliminar
    </button>
</div>

<script>


    function eliminarMerma(idmerma){

      var detalleborrado = $("#detalleBorradoMerma").val();
      detalleborrado = detalleborrado.trim();

      Swal.fire({
            title: '¿Está seguro?',
            text: "Se eliminará la salida",
            type: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28B463',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Aceptar'
          }).then((result) => {
            if (result.value) {

               $.ajax({
                url: "{{path('eliminar-merma')}}",
                method: "POST",
                data: {idmerma:idmerma,detalleborrado:detalleborrado},
                dataType: "html",
                success: function (response) {
                  obtenerTablaMerma();
                },

              });
            }else $("#modalEliminarMerma").modal('show');
          })

    }


</script>