{% block content %}
    <input id="url-almacen-obtener-informacion-stock" type="hidden" value="{{ path('almacen-obtener-informacion-stock') }}">
    <input id="url-autocompletar-modelo" type="hidden" value="{{path('autocompletar-modelo')}}">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.css" />
    <div class="container-fluid">
        <br>
        <div class="row">
            <label class="">Filtros: </label>  
            <br>
            <div class="col-md-3 ">
                <label class="" for="idempresa">Empresas: </label><br>
                    {% if empresas is not empty %}
                        {# Si hay empresas en la lista, se creará una opción en el select para cada una de ellas. #}
                        <select name="empresa" id="idempresa" class="form-control" onchange="obtenerSucursales()">
                            <option value=-1>Seleccione una empresa</option>
                            {% for empresa in empresas %}
                                <option value="{{ empresa.idempresa }}">{{ empresa.nombre }}</option>
                            {% endfor %}
                        </select>
                    {% else %}
                        {# Si la lista de empresas está vacía (es decir, el usuario no tiene permiso para ninguna empresa), se mostrará este mensaje. #}
                        <h4>Aún no tienes empresas asignadas</h4>
                    {% endif %}
            </div>

            <div class="col-md-3 ">
                <label class="" for="estadoMerma">Estado de salida: </label><br>
                    <select id="estadoMerma" class="form-control">
                        <option value='1'>Activa</option>
                        <option value='0'>Cancelada</option>
                    </select>
            </div>

            <div class="col-md-5">
                <label class="" for="datepicker-rango" style="padding: 0 0 0 10px">Rango por días: </label>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="input-daterange input-group rango-tiempo" id="datepicker-rango">
                                <input id="fecha-inicio-rango-dia" type="text" autocomplete="off" class="input-sm form-control fecha" name="start" />
                                <span class="input-group-addon"> a </span>
                                <input id="fecha-fin-rango-dia" type="text" autocomplete="off" class="input-sm form-control fecha" name="end" />
                                <button class="btn btn-warning" onclick="resetRangoFechaDias()"><i class="fa fa-eraser" aria-hidden="true"></i></button>
                                
                            </div>
                        </div>
                    </div>
            </div>
            <div class="col-md-1 button-buscar">
                <button style="margin: 25px 0px 8px 0px" class="btn btn-primary reporte btn-md" onClick="obtenerMermas(true)">Buscar</button>
            </div>
        </div>
 
        </div>
            <br>
            <div class="col-md-12 filter">
                <div class="row">
                    <div class="col-12 col-sm-6 col-md-4">
                        <label class="filter-title">Tipos de salida:</label>
                        <div class="col-md-12 miLista">
                            <fieldset id="departure-fieldset">
                                <label class="check" for="allDepartures">
                                    <input type="checkbox" name="allDepartures" onClick="toggleDeparture(this)" id="allDepartures" >
                                    TODAS
                                </label>
                                {% for departureType in departureTypes %}
                                    <label class="check" for="{{ departureType.iddeparturetype }}" >
                                        <input type="checkbox" id="{{ departureType.iddeparturetype }}" name="departure" value="{{ departureType.iddeparturetype }}" >
                                        {{ departureType.name }}
                                    </label>
                                {% endfor %}
                            </fieldset>
                        </div>
                    </div>
                    <div id="sucursales" class="col-12 col-sm-6 col-md-4"></div>
                </div>
                <br>
            </div>
        
        </div> 
    </div>
    
{% endblock %}

{% block javascripts %}

    <script src="{{ asset('lib/apexcharts-bundle/dist/apexcharts.js') }}"></script>
    <script src="{{ asset('js/funciones.js') }}"></script>
    <script src="{{asset('lib/jQuery-Autocomplete-master/dist/jquery.autocomplete.min.js')}}"></script>
    <script src="{{asset('lib/jQuery-Autocomplete-master/dist/jquery.autocomplete.min.js')}}"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.js"></script>
    <script src="https://cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json"></script>
    <script>
    
        document.querySelectorAll('.collapse').forEach (ocurrence => {
            ocurrence.addEventListener('show.bs.collapse', function(event) {
                
                $(".collapse").removeClass("show");
                $(event.target.id).addClass("show");
            })
        })

        jQuery(function ($) {
            $('.fecha').datetimepicker(
                {"pickTime":false,
                "pickDate":true,
                "minDate":"1\/1\/1900",
                "maxDate":null,
                "showToday":true,
                "language":"es_MX",
                "defaultDate":"",
                "disabledDates":[],
                "enabledDates":[],
                "icons":{"time":"fa fa-clock-o","date":"fa fa-calendar","up":"fa fa-chevron-up","down":"fa fa-chevron-down"},
                "useStrict":false,
                "sideBySide":false,
                "daysOfWeekDisabled":[],
                "collapse":true,
                "calendarWeeks":false,
                "viewMode":"days",
                "minViewMode":"days"
                ,"useCurrent":false,
                "useSeconds":false});

        });
    

        $(document).ready(function(){
            
            $("#contenedor-graficas").addClass("d-none");
            obtenerMermas();

        });

        function toggleDeparture(source) 
        {
            checkboxes = document.getElementsByName('departure');
            for(var i=0, n=checkboxes.length;i<n;i++) {
                checkboxes[i].checked = source.checked;
            }
        }

        function obtenerSucursales(){
            var idempresa=$("#idempresa").val();

            $.ajax({
                url: "{{path('almacen-obtener-sucursal')}}",
                
                data: {idempresa:idempresa},
                dataType: "html"
            }).done(function( html ) {
                $("#sucursales").html(html);

            }).fail(function() {
                alert( "error" );
            });
        }

        function resetRangoFechaDias() {
            $("#fecha-inicio-rango-dia").val("");
            $("#fecha-fin-rango-dia").val("");
        }

        function obtenerMermas(botonBuscar = false){

            sucursales = [];
            checkboxesSucursal = document.getElementsByName('sucursal');
            checkboxesBodega = document.getElementsByName('bodega');
            checkboxesCampana = document.getElementsByName('campaña');      

            for(var i=0, n=checkboxesSucursal.length;i<n;i++) {
                
                if(checkboxesSucursal[i].checked)
                {
                    sucursales.push(checkboxesSucursal[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesBodega.length;i<n;i++) {
                
                if(checkboxesBodega[i].checked)
                {
                    sucursales.push(checkboxesBodega[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesCampana.length;i<n;i++) {
                
                if(checkboxesCampana[i].checked)
                {
                    sucursales.push(checkboxesCampana[i].value);
                }
                    
            }
            
            var departures =  [];
            checkboxesDeparture = document.getElementsByName('departure');

            for(var i=0, n=checkboxesDeparture.length;i<n;i++) {
                
                if(checkboxesDeparture[i].checked)
                {
                    departures.push(checkboxesDeparture[i].value);
                }

            }
            

            var fechaInicio = document.getElementById('fecha-inicio-rango-dia').value;
            var fechaFin = document.getElementById('fecha-fin-rango-dia').value;
            estadoMerma = $("#estadoMerma").val();

            if (botonBuscar) obtenerTablaMerma(sucursales,fechaInicio,fechaFin,estadoMerma,departures);

            $("#contenedor-graficas").removeClass("d-none");
            
            $.ajax({
                url: "{{path('merma-obtener-categorias')}}",
                beforeSend: loadingGif("grafica"),
                data:
                {
                    sucursales:sucursales,
                    fechaInicio:fechaInicio,
                    fechaFin:fechaFin,
                    estadoMerma:estadoMerma,
                    departures:departures
                },
                dataType: "html"
            }).done(function( html ) {
                
                $("#grafica").html(html);

            }).fail(function() {
                alert( "error" );
            });

        }

        
    </script>
{% endblock %}