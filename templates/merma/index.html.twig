{% extends 'admin/layout.html.twig' %}

{% block content %}
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.3/css/jquery.dataTables.min.css">
<script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
<link rel="stylesheet" href="{{ asset('css/merma.css') }}">


<div class="container-fluid">
    <input id="flujo-expediente-id" type="hidden" class="form-control">
    <input id="idordenlaboratorio" type="hidden" class="form-control">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header card-header-info bg-primary">
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="card-title">REGISTRO DE SALIDAS</h4>
                            </div>
                        </div>
                    </div>

                    <div id="filtros" class="border-bottom mb-5"></div>
                    
                    <div class="container mb-3">
                        <div class="w-100 card p-3">
                            <h4 class="m-0 text-center">Agregar mermas desde Google Sheets</h4>
                            <a 
                                target="_blank" 
                                href="https://docs.google.com/spreadsheets/d/1CRhW7XlbW2lLh8N9O2L0DL1Ulq4mJBkAzS0xTPfUUNM/edit?usp=sharing"
                                class="btn btn-link mb-3"
                            >
                                Plantilla
                            </a>
                            <div class="d-flex justify-content-around">
                                <button id="picker_button" class="btn btn-info d-none" onclick="createPicker()">Buscar archivo</button>
                                <button id="authorize_button" class="btn btn-primary d-none" onclick="handleAuthClick()">Iniciar sesión</button>
                                <button id="signout_button" class="btn btn-danger d-none" onclick="handleSignoutClick()">Cerrar sesión</button>
                                <button 
                                    id="error_button"
                                    class="btn btn-warning d-none"
                                    data-toggle="modal"
                                    data-target="#modal-error-detail"
                                    onclick = "makeDataTable()"
                                ></button>
                            </div>
                        </div>
                    </div>
                    
                    <ul class="nav nav-tabs mb-3" id="ex-with-icons" role="tablist">
                        <li class="nav-item" role="presentation" id = "tab1">
                            <a class="btn-collapse" data-bs-toggle="collapse" href="#mermas" role="button" aria-expanded="false" aria-controls="mermas" o><i class="fa fa-file me-3" aria-hidden="true"></i>SALIDAS</a>
                        </li>
                        <li class="nav-item" role="presentation" id = "tab2">
                            <a class="btn-collapse" data-bs-toggle="collapse" href="#estadisticas" role="button" aria-expanded="false" aria-controls="estadisticas" ><i class="fa fa-pie-chart me-3" aria-hidden="true"></i></i>ESTADÍSTICAS</a>
                        </li>
                    </ul>
                    
                        <!-- MERMAS -->
                        <div class="collapse" id="mermas">

                            <div class="container-fluid" id="tablaMerma">
                                
                            </div>
                            
                        </div>

                        <!-- ESTADÍSTICAS -->
                        <div class="collapse" id="estadisticas">
                            <div class="container-fluid" id="grafica"></div>
                            <br>
                        </div>
                </div>
            </div>
        </div>
    </div>
    <div class="mod " id="modal-error-detail" tabindex="-1" aria-labelledby="" aria-hidden="true">
		<div class="mod-dialog modal-dialog-scrollable">
			<div class="modal-content" style="border-radius:10px">
				<div class="modal-header bg-primary">
					<h1 class="modal-title fs-5">Detalle de errores</h1>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<table class="table" id="error-table">
						<thead>
							<th>Código</th>
							<th>Mensaje</th>
						</thead>
						<tbody id="error-table-body">
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>



<script>

    const CLIENT_ID = '************-euigacmlshkhcm2493hrj2kuebooknro.apps.googleusercontent.com';
	const API_KEY = 'AIzaSyB1-aw7lXPz5acAYPnQF5XjPE66stFeAVk';
	const DISCOVERY_DOC = 'https://sheets.googleapis.com/$discovery/rest?version=v4';
	const SCOPES = 'https://www.googleapis.com/auth/drive.readonly https://www.googleapis.com/auth/spreadsheets.readonly';
	const APP_ID = 'pv360-416621';

	let tokenClient;
	let accessToken = null;
	let pickerInited = false;
	let gisInited = false;
	let gapiInited = false;
    var errorCount = 0

    document.querySelectorAll('.collapse').forEach (ocurrence => {
        ocurrence.addEventListener('show.bs.collapse', function(event) {
            $(".collapse").removeClass("show");
            $(event.target.id).addClass("show");
        })
    })

    $(document).ready(function(){

        $("#error_button").html("<i class='fa-solid fa-triangle-exclamation me-2'></i>"+errorCount);
        obtenerTablaMerma();
        obtenerFiltros();

        $("#mermas").collapse('show');

    });

    function obtenerFormularioAgregarMerma(idmerma = -1){
        
        $.ajax({
            url: "{{path('agregar-merma')}}"+"?idmerma="+idmerma, 
            type: 'GET',
            beforeSend: loadingGif("formularioMerma"),
            dataType: "html"
        }).done(function( html ) {
            $("#formularioMerma").html(html);


        }).fail(function() {
            alert( "error" );
        });
    }

    function selectOutputType(){
        
        $.ajax({
            url: "{{path('merma-select-output-type')}}",
            type: 'GET',
            beforeSend: loadingGif("departureSelection"),
            dataType: "html"
        }).done(function( html ) {
            $("#departureSelection").html(html);


        }).fail(function() {
            alert( "error" );
        });
    }

    

    function obtenerTablaMerma(sucursales = [], fechaInicio = null, fechaFin = null, estadoMerma = '1', departures = []){

        $.ajax({
            url: "{{path('tabla-merma')}}", 
            type: 'GET',
            beforeSend: loadingGif("tablaMerma"),
            data: 
            {
                sucursales:sucursales,
                fechaInicio:fechaInicio,
                fechaFin:fechaFin,
                estadoMerma:estadoMerma,
                departures:departures
            },
            dataType: "html"
        }).done(function( html ) {
            $("#tablaMerma").html(html);
            $(".modal-backdrop").removeClass("modal-backdrop");

        }).fail(function() {
            alert( "error" );
        });
    }

    function obtenerFiltros(){

        $.ajax({
            url: "{{path('merma-estadisticas')}}", 
            type: 'GET',
            //beforeSend: loadingGif("mermaEstadisticas"),
            dataType: "html"
        }).done(function( html ) {
            $("#filtros").html(html);

        }).fail(function() {
            alert( "error" );
        });
    }

    function searchSale(){
        const foil = $("#foil-field").val();
        const enterpriseId = $("#enterprise-select").val();

        if (foil != ''){
            $.ajax({
                url: "{{path('merma-select-sale-products')}}", 
                type: 'POST',
                data: {foil:foil,enterpriseId:enterpriseId},
                beforeSend: loadingGif("products-container"),
                dataType: "html"
            }).done(function( html ) {
                $("#products-container").html(html);
            }).fail(function() {
                alert( "error" );
            });
        } else {
            foilMessage("Necesitas ingresar un folio válido", false)
        }
        
    }

    function postBatchDepartures(bachDepartures){
        $("#error-table").dataTable().fnDestroy();
        $("#error-table-body").html('')
        errorCount = 0

        if (bachDepartures.length > 0 ) {
            $("#error_button").addClass("d-none")
            $.ajax({
                url: "{{path('merma-post-batch-departures')}}", 
                type: 'POST',
                data: {bachDepartures:bachDepartures},
                dataType: "json"
            }).done(function( response ) {
                if (response.errors.length > 0) Swal.fire("No todas las salidas su pudieron completar", "Revise el boton de errores para ver los detalles", "warning");
                else Swal.fire("Salidas agregadas", "Se agregaron "+response.successfulDepartures+" salidas", "success");
                if (response.successfulDepartures > 0) obtenerTablaMerma();
                displayErrorsTable(response.errors)
            }).fail(function() {
                alert( "error" );
            });
        } else {
            Swal.fire("No se encontraron datos para procesar", "Pruebe con otra plantilla", "warning");
        }
    }

    function displayErrorsTable(errors){
        let html = ""
        errorCount+=errors.length
        if (errorCount > 0) {
            $("#error_button").removeClass("d-none")
            
        }
        errors.forEach((product) => {
            html+="<tr><td>"+product.code+"</td><td>"+product.msg+"</td></tr>"
        })
        $("#error_button").html("<i class='fa-solid fa-triangle-exclamation me-2'></i>"+errorCount);
        $("#error-table-body").append(html);
    }

    function makeDataTable(){
        if ( ! $.fn.DataTable.isDataTable( '#error-table' ) ){
            $('#error-table').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                    lengthMenu: 'Mostrar _MENU_ códigos por página',
                },
                dom: 'Bfrtip',
                buttons: [
                    {
                        className: 'btn-primary btn',
                        filename: 'codigos_errores',
                        extend: 'excelHtml5',
                        text: 'Exportar excel',
                    }
                ]
            });
        }
    }

    function gapiLoaded() {
		gapi.load('client:picker', initializePicker);
		gapi.load('client', initializeGapiClient);
	}

	async function initializeGapiClient() {
		await gapi.client.init({
			apiKey: API_KEY,
			discoveryDocs: [DISCOVERY_DOC],
		});
		gapiInited = true;
		maybeEnableButtons();
	}

	async function initializePicker() {
		await gapi.client.load('https://www.googleapis.com/discovery/v1/apis/drive/v3/rest');
		pickerInited = true;
		maybeEnableButtons();
	}

	function gisLoaded() {
		tokenClient = google.accounts.oauth2.initTokenClient({
		client_id: CLIENT_ID,
		scope: SCOPES,
		callback: '', // defined later
		});
		gisInited = true;
		maybeEnableButtons();
	}


	function maybeEnableButtons() {
		if (pickerInited && gisInited && gapiInited) {
			$("#authorize_button").removeClass("d-none")
		}
	}


	function handleAuthClick() {
		tokenClient.callback = async (response) => {
		if (response.error !== undefined) {
			throw (response);
		}
		accessToken = response.access_token;
		$("#signout_button").removeClass("d-none")
		$("#picker_button").removeClass("d-none")
		$("#authorize_button").text("Cambiar de cuenta")
	
		await createPicker();
		};

		if (accessToken === null) tokenClient.requestAccessToken({prompt: 'consent'});
		else tokenClient.requestAccessToken({prompt: ''});
	}

	function handleSignoutClick() {
		if (accessToken) {
			accessToken = null;
			google.accounts.oauth2.revoke(accessToken);
			$("#authorize_button").text("Iniciar sesión")
			$("#signout_button").addClass("d-none")
			$("#picker_button").addClass("d-none")
			$("#error_button").addClass("d-none")
		}
	}

	function createPicker() {
		const view = new google.picker.View(google.picker.ViewId.DOCS);
		view.setMimeTypes('application/vnd.google-apps.spreadsheet');
		const picker = new google.picker.PickerBuilder()
			.enableFeature(google.picker.Feature.NAV_HIDDEN)
			.setDeveloperKey(API_KEY)
			.setAppId(APP_ID)
			.setOAuthToken(accessToken)
			.addView(view)
			.addView(new google.picker.DocsUploadView())
			.setCallback(pickerCallback)
			.build();
		picker.setVisible(true);
	}

	async function pickerCallback(data) {
		if (data.action === google.picker.Action.PICKED) {
			const document = data[google.picker.Response.DOCUMENTS][0];
			const fileId = document[google.picker.Document.ID];

			// Retrieve the content of the cells in the Google Sheets file
			const response = await gapi.client.sheets.spreadsheets.values.get({
				spreadsheetId: fileId,
				range: 'Sheet1!A:Z',
				majorDimension: 'ROWS',
			});

			const values = response.result.values;
			if (values){
				const formatedCodes = values[0].slice(1).map(value => value.trim())
                postBatchDepartures(values.slice(1));
                //console.log(values.slice(1))
			}
			

		}
	}

</script>
<script async defer src="https://apis.google.com/js/api.js" onload="gapiLoaded()"></script>
<script async defer src="https://accounts.google.com/gsi/client" onload="gisLoaded()"></script>
{% endblock %}





