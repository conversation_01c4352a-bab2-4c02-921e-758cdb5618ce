<div id = "mensaje">
    <h4>No hay datos disponibles</h4>
</div>
<div class="container-fluid mt-2 " id="graficas">
    <div class="card badge bg-light">
        <div class="card-body col-md-12" id="contenedor-graficas" >
            <div class="row">
                <div id="chart"></div>

            </div>
        </div>
    </div>

</div>


<script>

    $(document).ready(function(){

        $("#mensaje").addClass("d-none");
        $("#graficas").addClass("d-none");

        {% if datos[0] is defined %}
        
            datos = {{ datos|json_encode|raw }};

            categorias = [];
            cantidades = [];

            for (i = 0; i < datos.length; i++ ){
                categorias.push(datos[i].categoria);
                cantidades.push(parseInt(datos[i].cantidad));
            }

            var optionDonut = {
                chart: {
                    type: 'pie',
                    width: '100%',
                },
                dataLabels: {
                    enabled: false,
                },
                plotOptions: {
                    pie: {
                        customScale: 0.8,
                        offsetY: 20,
                    },
                    stroke: {
                        colors: undefined
                    }
                },
                title: {
                    text: "Categoría de productos con salida",
                    align: 'left',
                    margin: 1,
                    offsetX: 0,
                    offsetY: 0,
                    floating: true,
                    style: {
                        
                        
                        
                        fontFamily:  undefined,
                        color:  '#263238'   
                    },
                },
                series: cantidades,
                labels: categorias,
                legend: {
                    position: 'left',
                    offsetY: 80
                },
                responsive: [{
                    breakpoint: 480, // Set your desired breakpoint
                    options: {
                        title: {
                            offsetY: 10, // Adjust as needed
                        }
                    }
                }]
            };

            var donut = new ApexCharts(
                document.querySelector("#chart"),
                optionDonut
            );
            donut.render();
            
            $("#graficas").removeClass("d-none");
        {% else %}

            
            $("#mensaje").removeClass("d-none");

        {% endif %}

    });



</script>