<fieldset id="stock-venta-container" class="mt-3" onchange="changeStockVenta()">
    {% for Product in products %}
        <div class="d-flex align-items-start">
            {% set code = (Product.codigobarras != null) ? Product.codigobarras : Product.codigobarrasuniversal %}
            <input type="radio" id="{{Product.idstockventa}}" name="product" value="{{Product.idstockventa}}" />
            <label class="ms-3 fw-normal" for="{{Product.idstockventa}}">{{Product.brand}}-{{Product.modelo}}-{{code}}</label>
        </div>
    {% endfor %}
</fieldset>

<script>
    {% if products|length > 0 %}
        foilMessage("Venta agregada correctamente", true)
    {% else %}
        foilMessage("No se encontraron productos en esta venta", false)
    {% endif %}
    idstockventa = 0

</script>