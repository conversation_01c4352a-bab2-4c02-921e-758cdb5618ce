<div class="card">
    <div class="card-body">
        <h3>Categorias y Marcas:</h3>
        <br>
        <div id="chart"></div>
    </div>
</div>
<br>
<div class="card">
    <div class="card-body">
        <div class="row">
            <div class="col-md-12 mt-3">
                <table id="tablaProductos" class="display">
                    <thead>
                        <tr class="text-center">
                            <th>#</th>
                            <th>Cantidad</th>
                            <th>SKU</th>
                            <th>Modelo</th>
                            <th>Marca</th>
                            <th>categorias</th>
                            <th>Creación</th>
                            <th>Codigo <PERSON>as</th>
                            <th>Nombre Sucursal</th>
                            <th>Nombre Empresa</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in fecha %}
                        <tr class="text-center">
                            <td>{{ loop.index }}</td>
                            <td>{{ item.cantidad }}</td>
                            <td>{{ item.SKU }}</td>
                            <td>{{ item.modelo }}</td>
                            <td>{{ item.Marcanombre }}</td>
                            <td>{{ item.Categoria }}</td>
                            <td>{{ item.creacion | date("d-m-Y H:i:s") }}</td>
                            <td>{{ item.CodigoBarras }}</td>
                            <td>{{ item.NombreSucursal }}</td>
                            <td>{{ item.NombreEmpresa }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" href="https://cdn.datatables.net/1.12.1/css/jquery.dataTables.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.3.3/css/buttons.dataTables.min.css">
<script src="https://code.jquery.com/jquery-3.5.1.js"></script>
<script src="https://cdn.datatables.net/1.12.1/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.3/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.3/js/buttons.html5.min.js"></script>

<script>
    $(document).ready(function() {
        $('#tablaProductos').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "lengthChange": false,
            "pageLength": 15,
            "language": {
                "lengthMenu": "Mostrar _MENU_ registros por página",
                "zeroRecords": "No se encontraron resultados",
                "info": "Mostrando página _PAGE_ de _PAGES_",
                "infoEmpty": "No hay registros disponibles",
                "infoFiltered": "(filtrado de _MAX_ registros en total)",
                "search": "Buscar:",
                "paginate": {
                    "first": "Primero",
                    "last": "Último",
                    "next": "Siguiente",
                    "previous": "Anterior"
                }
            },
            dom: 'Bfrtip',
            buttons: [
                {
                    extend: 'excelHtml5',
                    text: 'Exportar a Excel',
                    className: 'btn-excel'
                }
            ]
        });
        
        var datos = {};
        $('#tablaProductos tbody tr').each(function() {
            var marca = $(this).find('td').eq(4).text(); 
            var categoria = $(this).find('td').eq(5).text(); 
            var cantidad = parseInt($(this).find('td').eq(1).text(), 10); 
            
            var clave = marca + ' - ' + categoria;
            
            if (!datos[clave]) {
                datos[clave] = 0;
            }
            datos[clave] += cantidad;
        });
        
        var options = {
            chart: {
                type: 'pie',
                width: 600,
                height: 600 
            },
            series: Object.values(datos),
            labels: Object.keys(datos), 
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: '100%', 
                        height: 300 
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }],
        };
        
        var chart = new ApexCharts(document.querySelector("#chart"), options);
        chart.render();
    });
</script>
