{% extends 'admin/layout.html.twig' %}
{% block titleHead %}{% endblock %}
{% block title %}Reporte de Productos Rezagados
{% endblock %}
{% block content %}
<style></style>
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css"/>
<input id="url-reporte-rezagados" type="hidden" value="{{ path('reporte-rezagados') }}">
<div class="container-fluid">
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-12">
                    <h3 class="card-title">REPORTE DE PRODUCTOS REZAGADOS</h3>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="empresa" class="lab reporte">Empresas:</label><br>
                            {% if empresas is not empty %}
                                <select name="empresa" id="idempresa" class="form-control"
                                        onchange="obtenerSucursales();">
                                    <option value="-1">Seleccione una empresa</option>
                                    {% for empresa in empresas %}
                                        <option value="{{ empresa.idempresa }}">{{ empresa.nombre }}</option>
                                    {% endfor %}
                                </select>
                            {% else %}
                                <h4>Aún no tienes empresas asignadas</h4>
                            {% endif %}
                        </div>
                        <div class="col-md-3 mb-3" style="position: relative; padding-left: 6em; padding-top: 6px;">
                            <label for="fecha" class="lab reportefecha">Fecha:</label><br>
                            <select name="fecha" id="idfecha" class="form-control">
                                <option value="-1">Selecciona una fecha</option>
                                <option value="1">Más de 1 mes</option>
                                <option value="2">6 meses en adelante</option>
                                <option value="4">más de 1 año</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3" style="position: relative; padding-left: 6em; padding-top: 6px;">
                            <label for="productos" class="lab reportefecha">Productos:</label><br>
                            <select name="productos" id="productos" class="form-control">
                                <option value="-1">Selecciona una opción</option>
                                <option value="2">Masivo</option>
                                <option value="1">Único</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-5 d-flex align-items-end">
                            <button class="btn btn-primary" id="idBuscar" onclick="obtenerProductosRezagados()">Buscar
                            </button>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="sucursales" id="sucursales">
                                <div id="sucursales"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <br>
            <div class="card">
                <div class="card-body">
                    <div id="tableRezagados"></div>
                </div>
            </div>
        </div>
    </div>


    <script src="{{ asset('lib/apexcharts-bundle/dist/apexcharts.js') }}"></script>
    <script src="{{ asset('js/funciones.js') }}"></script>
    <script src="{{ asset('lib/jQuery-Autocomplete-master/dist/jquery.autocomplete.min.js') }}"></script>
    <script src="https://cdn.datatables.net/buttons/2.3.3/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.js"></script>
    <script src="https://cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json"></script>

    <script>

        function obtenerSucursales() {
            var idempresa = $("#idempresa").val();
            $.ajax({
                url: "{{ path('almacen-obtener-sucursal') }}",
                data: {
                    idempresa: idempresa
                },
                dataType: "html"
            }).done(function (html) {
                $("#sucursales").html(html);
            }).fail(function () {
                alert("error");
            });
        }

        function obtenerProductosRezagados() {

            var url = "{{ path('reporte-rezagados') }}"
            var idempresa = $('#idempresa').val();
            var fechaId = $('#idfecha').val();
            var sucursal = obtenerSeleccionados('sucursal');
            var bodegas = obtenerSeleccionados('bodega');
            var campanas = obtenerSeleccionados('campaña');
            var masivoUnico = $('#productos').val();

            $.ajax({
                url: url,
                type: 'POST',
                data: {
                    idempresa: idempresa,
                    fechaId: fechaId,
                    sucursal: sucursal,
                    bodegas: bodegas,
                    masivoUnico: masivoUnico,
                    campanas: campanas
                }
            }).done(function (data) {
                $('#tableRezagados').html(data);
            }).fail(function () {
                alert("error");
            });

        }


        function obtenerSeleccionados(nombre) {
            var seleccionados = [];
            var checkboxes = document.getElementsByName(nombre);
            for (var i = 0, n = checkboxes.length; i < n; i++) {
                if (checkboxes[i].checked) {
                    seleccionados.push(checkboxes[i].value);
                }
            }
            return seleccionados;
        }

    </script>
    {% endblock %}
