<div class="mod " id="modalDetalle" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="mod-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="detalle-modal-title"></h1>
                <button type="button" class="btn-close" data-bs-dismiss="mod" aria-label="Cerrar"></button>
            </div>
            <div class="modal-body">
                <div class="row justify-content-md-center">
                    <div class="col-md-12">
                        <table class="table" id="tablaDetalles">
                            <thead>
                                <tr class="text-start">
                                    <th class="text-start" scope="col">#</th>
                                    <th class="text-start" scope="col">Cantidad</th>
                                    <th class="text-start" scope="col">Código</th>
                                    <th class="text-start" scope="col">Modelo</th>
                                    <th class="text-start" scope="col">Sucursal</th>
                                    <th class="text-start" scope="col">Marca</th>
                                </tr>
                            </thead>
                            <tbody id = "tableBodyDetalles"></tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="mod">Cerrar</button>
            </div>
        </div>
    </div>
</div>


<div class="card col-12">
  <div class="card-body">
    <h2 class="titulos-reportes">Productos Vendidos por Sucursal</h2>
    <div id="grafica-marca-sucursal"></div>
  </div>
</div>

<hr>
<div class="row d-flex justify-content-center">
    <div class="col-md-12 text-center mt-4 mb-4">
        <h2 class="titulos-reportes">Productos Vendidos por Marca</h2> 
        </div>
   <div class="col-md-6 text-center">
        <div class="card mb-4">
            <div class="card-body">

                <div id="grafica-inventario-marca"></div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-body">
                <table class="table table-borderless" id="marcaTable">
                    <thead>
                        <tr>
                            <th>Marca</th>
                            <th>Cantidad</th>
                            <th></th>
                        </tr>
                    </thead>
                        <tbody>
                        {% set total = 0 %}
                        {% for s in stock %}
                            {% if s.cantidad > 0 %}
                                <tr>
                                    <td>{{ s.marca }}</td>
                                    <td>{{ s.cantidad }}</td>
                                    <td>
                                    <button id="{{ s.marca }}"  type="button" class="btn btn-success btn-nuevo-cliente" data-bs-toggle="mod" data-bs-target="#modalDetalle" onclick="mostrarDetallesMarca(this.id)">Detalles</button>
                                    </td>
                                    {% set total = total + s.cantidad %} 
                                </tr>
                            {% endif %}
                        {% endfor %}
                        </tbody>
                </table>
                <div class="d-flex justify-content-center align-items-center">
                    <h5 class="me-3"> Total: {{ total }} <h5>
                    <button id="Total"  type="button" class="btn btn-info btn-nuevo-cliente" data-bs-toggle="mod" data-bs-target="#modalDetalle" onclick="mostrarDetallesMarca(this.id)">Detalles</button>
                </div>
            </div>
        </div>
    </div>
</div>

<hr>
<div class="row d-flex justify-content-center">
    <div class="col-md-12 text-center mt-4 mb-4">
    <h2 class="titulos-reportes">Productos Vendidos por Categoría</h2>
    </div>
    <div class="col-md-6 text-center">
        <div class="card mb-4">
            <div class="card-body">

                <div id="grafica-inventario-categoria"></div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-body">
                <table class="table table-borderless" id="categoriaTable">
                    <thead>
                        <tr>
                            <th>Categoría</th>
                            <th>Cantidad</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                    {% set total = 0 %}
                    {% for s in stockCategoria %}
                        <tr>
                            <td>{{ s.categoria }}</td>
                            <td>{{ s.cantidad }}</td>
                            <td>
                                <button id="{{ s.categoria }}"  type="button" class="btn btn-success btn-nuevo-cliente" data-bs-toggle="mod" data-bs-target="#modalDetalle" onclick="mostrarDetallesCategoria(this.id)">Detalles</button>
                            </td>
                            {% set total = total + s.cantidad %}
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
                <div class="d-flex justify-content-center align-items-center">
                    <h5 class="me-3"> Total: {{ total }} <h5>
                   <button id="Total"  type="button" class="btn btn-info btn-nuevo-cliente" data-bs-toggle="mod" data-bs-target="#modalDetalle" onclick="mostrarDetallesCategoria(this.id)">Detalles</button>
                </div>
            </div>
        </div>
    </div>

</div>

<hr>
<div class="row d-flex justify-content-center">
    <div class="col-md-12 text-center mt-4 mb-4">
        <h2 class="titulos-reportes">Inventario por Subcategoría</h2>
    </div>
    <div class="col-md-6 text-center">
        <div class="card mb-4">
            <div class="card-body">
                <div id="grafica-inventario-subcategoria"></div>
            </div>
        </div>
    </div>

    
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-body">
                <table class="table table-borderless" id="subcategoriaTable">
                    <thead>
                        <tr>
                            <th>Subcategoría</th>
                            <th>Cantidad</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                    {% set total = 0 %}
                    {% for sc in stockSubcategoria %}
                        {% if sc.cantidad > 0 %}
                            <tr>
                                <td>{{ sc.subcategoria }}</td>
                                <td>{{ sc.cantidad }}</td>
                                <td>
                                <button id="{{ sc.subcategoria }}"  type="button" class="btn btn-success btn-nuevo-cliente" data-bs-toggle="mod" data-bs-target="#modalDetalle" onclick="mostrarDetallesSubcategoria(this.id)">Detalles</button>
                                </td>
                                {% set total = total + sc.cantidad %}
                            </tr>
                        {% endif %}
                    {% endfor %}
                    </tbody>
                </table>
                <div class="d-flex justify-content-center align-items-center">
                    <h5 class="me-3"> Total: {{ total }} <h5>
                    <button id="Total"  type="button" class="btn btn-info btn-nuevo-cliente" data-bs-toggle="mod" data-bs-target="#modalDetalle" onclick="mostrarDetallesSubcategoria(this.id)">Detalles</button>
                </div>
            </div>
        </div>
    </div>
</div>

<hr>
<div class="row d-flex justify-content-center">
    <div class="col-md-12 text-center mt-4 mb-4">
        <h2 class="titulos-reportes">Productos Vendidos por Tipo de Venta</h2>
    </div>
<div class="col-md-6 text-center">
  <div class="card mb-4">
    <div class="card-body">
      <div id="grafica-inventario-tipoventa"></div>
    </div>
  </div>
</div>
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-body">
                <table class="table table-borderless" id="tipoVentaTable">
                    <thead>
                        <tr>
                            <th>Tipo de venta</th>
                            <th>Cantidad</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                    {% set total = 0 %}
                    {% for tv in stockTipoVenta %}
                        {% if tv.cantidad > 0 %}
                            <tr>
                                <td>{{ tv.categoria }}</td>
                                <td>{{ tv.cantidad }}</td>
                                <td>
                                    <button id="{{ tv.categoria }}"  type="button" class="btn btn-success btn-nuevo-cliente" data-bs-toggle="mod" data-bs-target="#modalDetalle" onclick="mostrarDetallesTipoVenta(this.id)">Detalles</button>
                                </td>
                                {% set total = total + tv.cantidad %}
                            </tr>
                        {% endif %}
                    {% endfor %}
                    </tbody>
                </table>
                <div class="d-flex justify-content-center align-items-center">
                    <h5 class="me-3"> Total: {{ total }} <h5>
                    <button id="Total"  type="button" class="btn btn-info btn-nuevo-cliente" data-bs-toggle="mod" data-bs-target="#modalDetalle" onclick="mostrarDetallesTipoVenta(this.id)">Detalles</button>
                </div>
            </div>
        </div>
    </div>
</div>
</div>





<script>
/*
En resumen, este código convierte las tablas con ID marcaTable,
categoriaTable y subcategoriaTable en tablas interactivas utilizando el plugin DataTables,
y establece el lenguaje de las tablas en español.
*/

    $(document).ready( function () {
        setMainDataTables();
        setMainGraphs();
        setGraphSoldProductsPerSucursal();
    });

    function mostrarDetallesMarca(id) {
        $("#detalle-modal-title").text("DETALLE "+id);
        var tableBody = document.querySelector("#tableBodyDetalles");
        $("#tablaDetalles").dataTable().fnDestroy();
        tableBody.innerHTML = "";
        var marcaSeleccionada = id;
        let productoSucursal = Object.entries({{ modeloSucursalMapped|json_encode|raw }});
        productoSucursal.forEach((item) => {
            var marca = item[0];
            if (marca == marcaSeleccionada || marcaSeleccionada == "Total") {
                for (var i = 0; i < item[1].length; i++) {
                    //if (item[1][i]['cantidad'] == 1) {
                        var newRow = document.createElement("tr");
                        newRow.setAttribute('scope', 'row');

                        var newCell = document.createElement("td");
                        newCell.textContent = i + 1;
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.textContent = item[1][i]['cantidad'];
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        var cellData = (item[1][i]['codigo']) ? item[1][i]['codigo'] : item[1][i]['codigobarrasuniversal']
                        newCell.textContent = cellData;
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.textContent = item[1][i]['modelo'];
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.textContent = item[1][i]['sucursal'];
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.textContent = item[1][i]['marca'];
                        newRow.appendChild(newCell);

                        tableBody.appendChild(newRow);
                    //}
                }
            }
        });

        $('#tablaDetalles').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ productos por página',
            },
            dom: 'Bfrtip',
            buttons: [
                {
                    className: 'btn-primary btn',
                    filename: 'productos_data_marca_'+id,
                    extend: 'excelHtml5',
                    text: 'Exportar excel',
                }
            ]
        });
    }
    
    function mostrarDetallesCategoria(id){
        $("#detalle-modal-title").text("DETALLE "+id);
        var tableBody = document.querySelector("#tableBodyDetalles");
        tableBody.innerHTML = "";
        $("#tablaDetalles").dataTable().fnDestroy();
        tableBody.innerHTML = "";

        let productoSucursal = Object.entries({{ categoriaModeloSucursalMapped|json_encode|raw }});

        productoSucursal.forEach((categoria) => {
            
            if(categoria[0] == id || id == "Total")
            {
                for(var i = 0; i < categoria[1].length; i++){

                    var newRow = document.createElement("tr");
                    newRow.setAttribute('scope', 'row');

                    var newCell = document.createElement("td");
                    newCell.textContent = i + 1;
                    newRow.appendChild(newCell);

                    var newCell = document.createElement("td");
                    newCell.textContent = categoria[1][i]['cantidad'];
                    newRow.appendChild(newCell);

                    var newCell = document.createElement("td");
                    var cellData = (categoria[1][i]['codigo']) ? categoria[1][i]['codigo'] : categoria[1][i]['codigobarrasuniversal']
                    newCell.textContent = cellData;
                    newRow.appendChild(newCell);

                    var newCell = document.createElement("td");
                    newCell.textContent = categoria[1][i]['modelo'];
                    newRow.appendChild(newCell);

                    var newCell = document.createElement("td");
                    newCell.textContent = categoria[1][i]['sucursal'];
                    newRow.appendChild(newCell);

                    var newCell = document.createElement("td");
                    newCell.textContent = categoria[1][i]['marca'];
                    newRow.appendChild(newCell);

                    tableBody.appendChild(newRow);

                }
                
            }

        });

        $('#tablaDetalles').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ productos por página',
            },
            dom: 'Bfrtip',
            buttons: [
                {
                    className: 'btn-primary btn',
                    filename: 'productos_data_categoria_'+id,
                    extend: 'excelHtml5',
                    text: 'Exportar excel',
                }
            ]
        });
    }

    function mostrarDetallesSubcategoria(id){
        $("#detalle-modal-title").text("DETALLE "+id);
        var tableBody = document.querySelector("#tableBodyDetalles");
        
        $("#tablaDetalles").dataTable().fnDestroy();
        tableBody.innerHTML = "";

        let productoSucursal = Object.entries({{ subcategoriaModeloSucursalMapped|json_encode|raw }});


        productoSucursal.forEach((subcategoria) => {
            
            if(subcategoria[0] == id  || id == "Total")
            {
                for(var i = 0; i < subcategoria[1].length; i++){

                    var newRow = document.createElement("tr");
                    newRow.setAttribute('scope', 'row');

                    var newCell = document.createElement("td");
                    newCell.textContent = i + 1;
                    newRow.appendChild(newCell);

                    var newCell = document.createElement("td");
                    newCell.textContent = subcategoria[1][i]['cantidad'];
                    newRow.appendChild(newCell);

                    var newCell = document.createElement("td");
                    var cellData = (subcategoria[1][i]['codigo']) ? subcategoria[1][i]['codigo'] : subcategoria[1][i]['codigobarrasuniversal']
                    newCell.textContent = cellData;
                    newRow.appendChild(newCell);

                    var newCell = document.createElement("td");
                    newCell.textContent = subcategoria[1][i]['modelo'];
                    newRow.appendChild(newCell);

                    var newCell = document.createElement("td");
                    newCell.textContent = subcategoria[1][i]['sucursal'];
                    newRow.appendChild(newCell);

                    var newCell = document.createElement("td");
                    newCell.textContent = subcategoria[1][i]['marca'];
                    newRow.appendChild(newCell);

                    tableBody.appendChild(newRow);

                }
                
            }

        });

        $('#tablaDetalles').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ productos por página',
            },
            dom: 'Bfrtip',
            buttons: [
                {
                    className: 'btn-primary btn',
                    filename: 'productos_data_subcategoria_'+id,
                    extend: 'excelHtml5',
                    text: 'Exportar excel',
                }
            ]
        });




    }

    function mostrarDetallesTipoVenta(id) {
        $("#detalle-modal-title").text("DETALLE "+id);
        var tableBody = document.querySelector("#tableBodyDetalles");

        $("#tablaDetalles").dataTable().fnDestroy();
        tableBody.innerHTML = "";

        let saletypeSucursal = Object.entries({{ saleTypeSucursalMapped|json_encode|raw }});

        saletypeSucursal.forEach((saletype) => {
            if (saletype[0] == id || id == "Total") {
                for (var i = 0; i < saletype[1].length; i++) {
                    var newRow = document.createElement("tr");
                    newRow.setAttribute('scope', 'row');

                    var newCell = document.createElement("td");
                    newCell.textContent = i + 1;
                    newRow.appendChild(newCell);

                    var newCell = document.createElement("td");
                    newCell.textContent = saletype[1][i]['cantidad'];
                    newRow.appendChild(newCell);

                    var newCell = document.createElement("td");
                    var valueCell = (saletype[1][i]['codigo'] != null) ?  saletype[1][i]['codigo'] : saletype[1][i]['codigobarrasuniversal'];
                    newCell.textContent = valueCell;
                    newRow.appendChild(newCell);

                    var newCell = document.createElement("td");
                    newCell.textContent = saletype[1][i]['modelo'];
                    newRow.appendChild(newCell);

                    var newCell = document.createElement("td");
                    newCell.textContent = saletype[1][i]['sucursal'];
                    newRow.appendChild(newCell);

                    var newCell = document.createElement("td");
                    newCell.textContent = saletype[1][i]['marca'];
                    newRow.appendChild(newCell);

                    tableBody.appendChild(newRow);
                }
            }
        });

        $('#tablaDetalles').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ productos por página',
            },
            dom: 'Bfrtip',
            buttons: [
                {
                    className: 'btn-primary btn',
                    filename: 'productos_data_tipo_venta_'+id,
                    extend: 'excelHtml5',
                    text: 'Exportar excel',
                }
            ]
        });
    }

    function setMainDataTables(){
        $('#marcaTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ marcas por página',
                
            },
            dom: 'Bfrtip',
            buttons: [
                {
                    className: 'btn-primary btn',
                    filename: 'marca_data',
                    extend: 'excelHtml5',
                    text: 'Exportar excel',
                }
            ]
        });
        $('#categoriaTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ categorías por página',
            },
            dom: 'Bfrtip',
            buttons: [
                {
                    className: 'btn-primary btn',
                    filename: 'categoria_data',
                    extend: 'excelHtml5',
                    text: 'Exportar excel',
                }
            ]
        });
        $('#subcategoriaTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ subcategorías por página',
            },
            dom: 'Bfrtip',
            buttons: [
                {
                    className: 'btn-primary btn',
                    filename: 'subcategoria_data',
                    extend: 'excelHtml5',
                    text: 'Exportar excel',
                }
            ]
        });
        $('#tipoVentaTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ tipos de venta por página',
            },
            dom: 'Bfrtip',
            buttons: [
                {
                    className: 'btn-primary btn',
                    filename: 'tipo_venta_data',
                    extend: 'excelHtml5',
                    text: 'Exportar excel',
                }
            ]
        });
    }

    function setMainGraphs(){
        var cantidadesMarcas=[];
        var marcas=[];

        var cantidadesCategoria=[];
        var categorias=[];

        var cantidadesSubcategoria=[];
        var subcategorias=[];

        var cantidadesTipoventa=[];
        var tipoventas=[];

        {% for s in stock %}
            cantidadesMarcas.push({{s.cantidad}});
            marcas.push("{{s.marca}}");    
        {% endfor %}

        {% for s in stockCategoria %}
        cantidadesCategoria.push({{s.cantidad}});
        categorias.push("{{s.categoria}}");
        {% endfor %}

        {% for sc in stockSubcategoria %}
        cantidadesSubcategoria.push({{sc.cantidad}});
        subcategorias.push("{{sc.subcategoria}}");
        {% endfor %}

        // Obtén los datos del resultado de tu consulta y conviértelos en el formato requerido por ApexCharts
        var stockTipoVenta = {{ stockTipoVenta|json_encode|raw }};

        // Extrae las cantidades y categorías del resultado de la consulta
        var cantidadesTipoVenta = stockTipoVenta.map(function(item) {
            return  parseInt(item.cantidad,10);
        });

        var tipoventas = stockTipoVenta.map(function(item) {
            return item.categoria;
        });

        // Configuración de ApexCharts
        var options = {
            series: cantidadesTipoVenta,
            labels: tipoventas,
            chart: {
                type: 'donut',
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }]
        };

        // Renderizar la gráfica en el elemento con ID "grafica-inventario-tipoventa"
        var chart = new ApexCharts(document.querySelector("#grafica-inventario-tipoventa"), options);
        chart.render();


        //grafica inventario por marca

        var options = {
            series: cantidadesMarcas,
            labels: marcas,
            chart: {
                width: "100%",
                type: 'donut',
            },
            dataLabels: {
                enabled: false
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        show: false
                    }
                }
            }],
            legend: {
                position: 'right',
                offsetY: 0,
                height: 230,
            }
        };

        var chart = new ApexCharts(document.querySelector("#grafica-inventario-marca"), options);
        chart.render();


        //grafica inventario por categoria
        var options = {

            chart: {
                width: "100%",
                type: 'donut',
            },
            labels: categorias,
            series: cantidadesCategoria,
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }]
        };

        var chart = new ApexCharts(document.querySelector("#grafica-inventario-categoria"), options);
        chart.render();

        //grafica inventario por subcategoria

        var options = {
            labels: subcategorias,
            series: cantidadesSubcategoria,
            chart: {
                width: "100%",
                type: 'donut',
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }]
        };  

        var chart = new ApexCharts(document.querySelector("#grafica-inventario-subcategoria"), options);
        chart.render();
    }

    function setGraphSoldProductsPerSucursal(){
        let stock1 = Object.values({{ stock1|json_encode|raw }});

        cantidades = [];
        let sucursales = [];

        stock1.forEach((venta) => {
            cantidades.push(parseInt(venta.cantidad, 10));
            sucursales.push(venta.sucursal);
        });

        var options = {
          series: [{
          data: cantidades
        }],
          chart: {
          type: 'bar',
          height: 380
        },
        plotOptions: {
          bar: {
            barHeight: '100%',
            distributed: true,
            horizontal: true,
            dataLabels: {
              position: 'bottom'
            },
          }
        },
        
        dataLabels: {
          enabled: true,
          textAnchor: 'start',
          style: {
            colors: ['#000']
          },
          formatter: function (val, opt) {
            var label = (val == 1) ? " producto" : " productos";
            return opt.w.globals.labels[opt.dataPointIndex] + ":  " + val+ label
          }, 
          offsetX: 0,
        },
        stroke: {
          colors: ['#000']
        },
        xaxis: {
          categories: sucursales,
        },
        yaxis: {
          labels: {
            show: false
          }
        },
        tooltip: {
          theme: 'dark',
          x: {
            show: false
          },
          y: {
            title: {
              formatter: function (val, opt) {
                return ''
              }
            }
          }
        }
        };

        var chart = new ApexCharts(document.querySelector("#grafica-marca-sucursal"), options);
        chart.render();
    }

</script>


