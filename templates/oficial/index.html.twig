{% if is_supervisor %}
{% extends 'supervisor_layout.html.twig' %}
{% endif %}

{% block titleHead %}{% endblock %}
{% block title %}Reporte de Productos{% endblock %}

{% block stylesheets %}
{{ parent() }}
<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<!-- DataTables CSS -->
<link href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" rel="stylesheet" />
<link href="https://cdn.datatables.net/buttons/2.3.2/css/buttons.bootstrap5.min.css" rel="stylesheet" />
<!-- SweetAlert2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet" />
<!-- Dashboard Oficial CSS -->
<link href="{{ asset('css/puntodeventa/dashboard-oficial.css') }}" rel="stylesheet" />
{% endblock %}

{% block content %}
<!-- Executive Dashboard -->
<div class="executive-dashboard" data-theme="light">

  <!-- Header con controles -->
  <div style="position: relative; margin-bottom: 1rem;">
    <!-- Theme Toggle Button - Posición ajustada -->
    <button class="theme-toggle" onclick="toggleTheme()" title="Cambiar tema" style="position: fixed; top: 80px; right: 20px; z-index: 1000;">
      <span id="theme-icon">🌙</span>
    </button>

    <!-- Botón de Cerrar Sesión - Posición prominente -->
    <div style="position: absolute; top: 0; right: 0; z-index: 999;">
      <button onclick="confirmarCerrarSesion()" style="padding: 0.75rem 1.5rem; background: var(--danger-red); color: white; border: none; border-radius: 12px; font-size: 0.875rem; font-weight: 600; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; gap: 0.5rem; box-shadow: var(--shadow-lg);" onmouseover="this.style.background='#c53030'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(220, 53, 69, 0.3)'" onmouseout="this.style.background='var(--danger-red)'; this.style.transform='translateY(0)'; this.style.boxShadow='var(--shadow-lg)'">
        <i class="fas fa-sign-out-alt"></i>
        Cerrar Sesión
      </button>
    </div>
  </div>

  <!-- Hero Section - Métricas Principales -->
  <section class="hero-metrics">
    <div class="hero-header">
      <div>
        <h1 class="hero-title">
          <i class="fas fa-tachometer-alt" style="color: var(--primary-blue); margin-right: 0.5rem;"></i>
          {% if is_supervisor %}Dashboard Supervisor{% else %}Dashboard Administrador{% endif %}
        </h1>
        <p class="hero-subtitle">
          <i class="fas fa-building" style="margin-right: 0.25rem;"></i>
          Optimo Opticas - Resumen Ejecutivo
        </p>
      </div>
      <div style="text-align: right; color: var(--gray-600); font-size: 0.875rem; margin-right: 180px;">
        <div><i class="fas fa-calendar"></i> <span id="current-date"></span></div>
        <div><i class="fas fa-clock"></i> <span id="current-time"></span></div>
      </div>
    </div>

    <!-- Métricas Principales -->
    <div class="metrics-grid">
      <div class="metric-card success">
        <div class="metric-header">
          <div class="metric-icon success">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="metric-trend positive" id="ventas-trend">
            <i class="fas fa-arrow-up"></i> +12.5%
          </div>
        </div>
        <div class="metric-value" id="ventas-total">$0.00</div>
        <div class="metric-label">Ventas del Día</div>
        <div class="metric-description">Total de ventas registradas hoy</div>
      </div>

      <div class="metric-card primary">
        <div class="metric-header">
          <div class="metric-icon primary">
            <i class="fas fa-money-bill-wave"></i>
          </div>
          <div class="metric-trend positive" id="pagos-trend">
            <i class="fas fa-arrow-up"></i> +8.3%
          </div>
        </div>
        <div class="metric-value" id="pagos-total">$0.00</div>
        <div class="metric-label">Pagos Recibidos</div>
        <div class="metric-description">Total de pagos cobrados hoy</div>
      </div>

      <div class="metric-card warning">
        <div class="metric-header">
          <div class="metric-icon warning">
            <i class="fas fa-clock"></i>
          </div>
          <div class="metric-trend neutral" id="deuda-trend">
            <i class="fas fa-minus"></i> 0%
          </div>
        </div>
        <div class="metric-value" id="por-cobrar-total">$0.00</div>
        <div class="metric-label">Por Cobrar</div>
        <div class="metric-description">Pendiente de cobro</div>
      </div>
    </div>
  </section>

  <!-- Quick Actions Section -->
  <section class="quick-actions" style="margin-bottom: 2rem;">
    <div style="background: var(--white); border-radius: 12px; padding: 1.5rem; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200);">
      <h3 style="font-size: 1.125rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
        <i class="fas fa-sliders-h" style="color: var(--primary-blue);"></i>
        Acciones Rápidas
      </h3>

      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
        <div>
          <label for="fecha-hoy" style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
            <i class="fas fa-calendar"></i> Fecha:
          </label>
          <input type="date" id="fecha-hoy" style="width: 100%; padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--white); color: var(--gray-900);">
        </div>

        <div>
          <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
            <i class="fas fa-store"></i> Sucursales:
          </label>
          <div id="sucursales-summary" style="padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--gray-50); color: var(--gray-700); cursor: pointer;" onclick="toggleSucursalesPanel()">
            <span id="sucursales-count">Cargando...</span>
            <i class="fas fa-chevron-down" style="float: right; margin-top: 0.125rem;"></i>
          </div>
        </div>

        <div>
          <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
            <i class="fas fa-building"></i> Empresa:
          </label>
          {% if not is_hardcoded %}
            <select name="empresa" id="idempresa" style="width: 100%; padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--white); color: var(--gray-900);" onchange="cargaDefiltros();">
              <option value="-1">Seleccione una empresa</option>
              {% for empresa in empresas %}
                <option value="{{ empresa.idempresa }}">{{ empresa.nombre }}</option>
              {% endfor %}
            </select>
          {% else %}
            <input type="hidden" id="idempresa" value="{{ empresas[0].idempresa }}">
            <div style="padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--gray-50); color: var(--gray-700);">
              <i class="fas fa-lock" style="margin-right: 0.5rem;"></i>
              {{ empresas[0].nombre }} (BIMBO)
            </div>
          {% endif %}
        </div>

        <div>
          <button onclick="buscarIngresosDiarios()" style="width: 100%; padding: 0.75rem 1rem; background: var(--primary-blue); color: white; border: none; border-radius: 8px; font-size: 0.875rem; font-weight: 500; cursor: pointer; transition: all 0.2s ease; display: flex; align-items: center; justify-content: center; gap: 0.5rem;" onmouseover="this.style.background='var(--primary-blue-dark)'" onmouseout="this.style.background='var(--primary-blue)'">
            <i class="fas fa-sync-alt"></i>
            Actualizar
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Sucursales Panel (Collapsible) -->
  <div id="sucursales-panel" style="display: none; margin-bottom: 2rem;">
    <div style="background: var(--white); border-radius: 12px; padding: 1.5rem; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200);">
      <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0;">
        <i class="fas fa-store"></i> Seleccionar Sucursales
      </h4>
      <div id="sucursales"></div>
    </div>
  </div>

  <!-- Hidden inputs for compatibility -->
  <input type="hidden" id="tipo-venta" value="BIMBO">

  <!-- Detailed Analysis Section (Open by default) -->
  <section class="detailed-analysis">
    <details class="analysis-section" open style="background: var(--white); border-radius: 12px; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200); margin-bottom: 2rem;">
      <summary style="padding: 1.5rem; cursor: pointer; font-size: 1.125rem; font-weight: 600; color: var(--gray-900); display: flex; align-items: center; gap: 0.5rem; user-select: none;">
        <i class="fas fa-chart-pie" style="color: var(--primary-blue);"></i>
        📊 Análisis de Ingresos Diarios
        <i class="fas fa-chevron-down" style="margin-left: auto; transition: transform 0.3s ease;"></i>
      </summary>

      <div style="padding: 0 1.5rem 1.5rem 1.5rem; border-top: 1px solid var(--gray-200);">
        <!-- Charts Grid -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1.5rem; margin-top: 1.5rem;">

          <!-- Ingresos por Sucursal -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-store" style="color: var(--success-green);"></i>
              Ingresos por Sucursal
            </h4>
            <div id="graficaSucursal" style="height: 300px; min-height: 300px;"></div>
          </div>

          <!-- Tipos de Pago -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-credit-card" style="color: var(--primary-blue);"></i>
              Tipos de Pago
            </h4>
            <div id="graficaTipoPago" style="height: 300px; min-height: 300px;"></div>
          </div>

          <!-- Deuda por Sucursal -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200); grid-column: 1 / -1;">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-exclamation-triangle" style="color: var(--warning-amber);"></i>
              Deuda por Sucursal
            </h4>
            <div id="deudaTotal" style="height: 300px; min-height: 300px; display: flex; align-items: center; justify-content: center; color: var(--gray-500);">
              <div style="text-align: center;">
                <i class="fas fa-spinner fa-spin fa-2x" style="margin-bottom: 1rem;"></i>
                <p>Cargando datos...</p>
              </div>
            </div>
          </div>

        </div>

        <!-- DataTable Section -->
        <div style="margin-top: 2rem;">
          <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-table" style="color: var(--info);"></i>
            Detalle de Ingresos por Sucursal
          </h4>

          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <table id="tablaIngresosDiarios" class="table table-striped table-hover" style="width: 100%; margin: 0;">
              <tbody>
                <!-- DataTables manejará el contenido -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </details>
  </section>

  <!-- Análisis Anual Section (Open by default) -->
  <section class="advanced-analysis">
    <details class="analysis-section" open style="background: var(--white); border-radius: 12px; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200); margin-bottom: 2rem;">
      <summary style="padding: 1.5rem; cursor: pointer; font-size: 1.125rem; font-weight: 600; color: var(--gray-900); display: flex; align-items: center; gap: 0.5rem; user-select: none;">
        <i class="fas fa-chart-bar" style="color: var(--success-green);"></i>
        📈 Análisis Anual
        <i class="fas fa-chevron-down" style="margin-left: auto; transition: transform 0.3s ease;"></i>
      </summary>

      <div style="padding: 0 1.5rem 1.5rem 1.5rem; border-top: 1px solid var(--gray-200);">
        <!-- Year Selector -->
        <div style="margin-bottom: 1.5rem;">
          <label for="year-select" style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
            <i class="fas fa-calendar"></i> Seleccionar Año:
          </label>
          <select id="year-select" name="year" style="padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--white); color: var(--gray-900); min-width: 150px;">
            <option value="2025" selected>2025</option>
            <option value="2024">2024</option>
            <option value="2023">2023</option>
            <option value="2022">2022</option>
          </select>
          <button onclick="buscarVentasAnuales()" style="margin-left: 1rem; padding: 0.75rem 1rem; background: var(--success-green); color: white; border: none; border-radius: 8px; font-size: 0.875rem; font-weight: 500; cursor: pointer;">
            <i class="fas fa-search"></i> Buscar
          </button>
        </div>

        <!-- Charts Grid -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1.5rem;">

          <!-- Ventas Anuales por Mes -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200); grid-column: 1 / -1;">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-chart-line" style="color: var(--success-green);"></i>
              Ventas Anuales por Mes
            </h4>
            <div id="ventasAnuales" style="height: 400px; min-height: 400px;"></div>
          </div>

          <!-- Comparativo Anual -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-balance-scale" style="color: var(--primary-blue);"></i>
              Comparativo Anual
            </h4>
            <div id="comparativoAnual" style="height: 300px; min-height: 300px;"></div>
          </div>

          <!-- Tendencia de Crecimiento -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-trending-up" style="color: var(--warning-amber);"></i>
              Tendencia de Crecimiento
            </h4>
            <div id="tendenciaCrecimiento" style="height: 300px; min-height: 300px;"></div>
          </div>

        </div>
      </div>
    </details>
  </section>

  <!-- Análisis de Productos Section -->
  <section class="products-analysis">
    <details class="analysis-section" style="background: var(--white); border-radius: 12px; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200); margin-bottom: 2rem;">
      <summary style="padding: 1.5rem; cursor: pointer; font-size: 1.125rem; font-weight: 600; color: var(--gray-900); display: flex; align-items: center; gap: 0.5rem; user-select: none;">
        <i class="fas fa-boxes" style="color: var(--warning-amber);"></i>
        📦 Análisis de Productos
        <i class="fas fa-chevron-down" style="margin-left: auto; transition: transform 0.3s ease;"></i>
      </summary>

      <div style="padding: 0 1.5rem 1.5rem 1.5rem; border-top: 1px solid var(--gray-200);">
        <!-- Product Filters -->
        <div style="margin-bottom: 1.5rem; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
          <div>
            <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
              <i class="fas fa-calendar"></i> Fecha Inicio:
            </label>
            <input type="date" id="fecha-inicio-productos" style="width: 100%; padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem;">
          </div>
          <div>
            <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
              <i class="fas fa-calendar"></i> Fecha Fin:
            </label>
            <input type="date" id="fecha-fin-productos" style="width: 100%; padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem;">
          </div>
          <div>
            <button onclick="buscarProductos()" style="width: 100%; padding: 0.75rem 1rem; background: var(--warning-amber); color: white; border: none; border-radius: 8px; font-size: 0.875rem; font-weight: 500; cursor: pointer;">
              <i class="fas fa-search"></i> Buscar Productos
            </button>
          </div>
        </div>

        <!-- Charts Grid -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1.5rem;">

          <!-- Top Productos -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-trophy" style="color: var(--warning-amber);"></i>
              Top 10 Productos
            </h4>
            <div id="topProductos" style="height: 400px; min-height: 400px;"></div>
          </div>

          <!-- Categorías de Productos -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-tags" style="color: var(--primary-blue);"></i>
              Categorías de Productos
            </h4>
            <div id="categoriasProductos" style="height: 400px; min-height: 400px;"></div>
          </div>

        </div>

        <!-- DataTable Section -->
        <div style="margin-top: 2rem;">
          <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-table" style="color: var(--info);"></i>
            Detalle de Productos Vendidos
          </h4>

          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <table id="tablaProductos" class="table table-striped table-hover" style="width: 100%; margin: 0;">
              <thead>
                <tr style="background: var(--primary-blue); color: white;">
                  <th>Producto</th>
                  <th>Cantidad</th>
                  <th>Total Vendido</th>
                  <th>Precio Promedio</th>
                  <th>Sucursal</th>
                </tr>
              </thead>
              <tbody>
                <!-- DataTables manejará el contenido -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </details>
  </section>

  <!-- Análisis de Facturación Section -->
  <section class="billing-analysis">
    <details class="analysis-section" style="background: var(--white); border-radius: 12px; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200); margin-bottom: 2rem;">
      <summary style="padding: 1.5rem; cursor: pointer; font-size: 1.125rem; font-weight: 600; color: var(--gray-900); display: flex; align-items: center; gap: 0.5rem; user-select: none;">
        <i class="fas fa-file-invoice-dollar" style="color: var(--danger-red);"></i>
        💰 Análisis de Facturación
        <i class="fas fa-chevron-down" style="margin-left: auto; transition: transform 0.3s ease;"></i>
      </summary>

      <div style="padding: 0 1.5rem 1.5rem 1.5rem; border-top: 1px solid var(--gray-200);">
        <!-- Billing Filters -->
        <div style="margin-bottom: 1.5rem; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
          <div>
            <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
              <i class="fas fa-calendar"></i> Fecha Inicio:
            </label>
            <input type="date" id="fecha-inicio-facturacion" style="width: 100%; padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem;">
          </div>
          <div>
            <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
              <i class="fas fa-calendar"></i> Fecha Fin:
            </label>
            <input type="date" id="fecha-fin-facturacion" style="width: 100%; padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem;">
          </div>
          <div>
            <button onclick="buscarFacturacion()" style="width: 100%; padding: 0.75rem 1rem; background: var(--danger-red); color: white; border: none; border-radius: 8px; font-size: 0.875rem; font-weight: 500; cursor: pointer;">
              <i class="fas fa-search"></i> Buscar Facturación
            </button>
          </div>
        </div>

        <!-- Charts Grid -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1.5rem;">

          <!-- Facturación por Período -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200); grid-column: 1 / -1;">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-chart-area" style="color: var(--danger-red);"></i>
              Facturación por Período
            </h4>
            <div id="facturacionPeriodo" style="height: 400px; min-height: 400px;"></div>
          </div>

          <!-- Estado de Facturas -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-clipboard-check" style="color: var(--success-green);"></i>
              Estado de Facturas
            </h4>
            <div id="estadoFacturas" style="height: 300px; min-height: 300px;"></div>
          </div>

          <!-- Métodos de Pago -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-money-check-alt" style="color: var(--primary-blue);"></i>
              Métodos de Pago
            </h4>
            <div id="metodosPago" style="height: 300px; min-height: 300px;"></div>
          </div>

        </div>

        <!-- DataTable Section -->
        <div style="margin-top: 2rem;">
          <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-table" style="color: var(--info);"></i>
            Detalle de Facturación
          </h4>

          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <table id="tablaFacturacion" class="table table-striped table-hover" style="width: 100%; margin: 0;">
              <thead>
                <tr style="background: var(--primary-blue); color: white;">
                  <th>Fecha</th>
                  <th>Número</th>
                  <th>Cliente</th>
                  <th>Total</th>
                  <th>Estado</th>
                  <th>Método Pago</th>
                  <th>Sucursal</th>
                </tr>
              </thead>
              <tbody>
                <!-- DataTables manejará el contenido -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </details>
  </section>

  <!-- Configuración Avanzada Section -->
  <section class="advanced-config">
    <details class="analysis-section" style="background: var(--white); border-radius: 12px; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200); margin-bottom: 2rem;">
      <summary style="padding: 1.5rem; cursor: pointer; font-size: 1.125rem; font-weight: 600; color: var(--gray-900); display: flex; align-items: center; gap: 0.5rem; user-select: none;">
        <i class="fas fa-cogs" style="color: var(--gray-600);"></i>
        ⚙️ Configuración Avanzada
        <i class="fas fa-chevron-down" style="margin-left: auto; transition: transform 0.3s ease;"></i>
      </summary>

      <div style="padding: 0 1.5rem 1.5rem 1.5rem; border-top: 1px solid var(--gray-200);">
        <!-- Configuration Options -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">

          <!-- Export Options -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-download" style="color: var(--success-green);"></i>
              Exportar Datos
            </h4>
            <div style="display: flex; flex-direction: column; gap: 0.75rem;">
              <button onclick="exportarExcel()" style="padding: 0.75rem 1rem; background: var(--success-green); color: white; border: none; border-radius: 8px; font-size: 0.875rem; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-file-excel"></i> Exportar a Excel
              </button>
              <button onclick="exportarPDF()" style="padding: 0.75rem 1rem; background: var(--danger-red); color: white; border: none; border-radius: 8px; font-size: 0.875rem; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-file-pdf"></i> Exportar a PDF
              </button>
              <button onclick="exportarCSV()" style="padding: 0.75rem 1rem; background: var(--primary-blue); color: white; border: none; border-radius: 8px; font-size: 0.875rem; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-file-csv"></i> Exportar a CSV
              </button>
            </div>
          </div>

          <!-- Report Settings -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-chart-line" style="color: var(--primary-blue);"></i>
              Configuración de Reportes
            </h4>
            <div style="display: flex; flex-direction: column; gap: 0.75rem;">
              <label style="display: flex; align-items: center; gap: 0.5rem; font-size: 0.875rem; color: var(--gray-700);">
                <input type="checkbox" id="auto-refresh" checked style="margin: 0;">
                Auto-actualizar cada 5 minutos
              </label>
              <label style="display: flex; align-items: center; gap: 0.5rem; font-size: 0.875rem; color: var(--gray-700);">
                <input type="checkbox" id="show-trends" checked style="margin: 0;">
                Mostrar tendencias
              </label>
              <label style="display: flex; align-items: center; gap: 0.5rem; font-size: 0.875rem; color: var(--gray-700);">
                <input type="checkbox" id="detailed-view" style="margin: 0;">
                Vista detallada por defecto
              </label>
            </div>
          </div>

        </div>
      </div>
    </details>
  </section>

</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<!-- ApexCharts -->
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.2/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.2/js/buttons.print.min.js"></script>

<script>
// ===== VARIABLES GLOBALES =====
let chartSucursal, chartTipoPago, chartDeuda, chartVentasAnuales, chartComparativo, chartTendencia;
let chartTopProductos, chartCategorias, chartFacturacion, chartEstadoFacturas, chartMetodosPago;
let tablaIngresosDiarios, tablaProductos, tablaFacturacion;

// ===== CONFIGURACIÓN DE TEMA =====
let currentTheme = localStorage.getItem('dashboard-theme') || 'light';

function initializeTheme() {
    document.documentElement.setAttribute('data-theme', currentTheme);
    updateThemeIcon();
}

function toggleTheme() {
    currentTheme = currentTheme === 'light' ? 'dark' : 'light';
    document.documentElement.setAttribute('data-theme', currentTheme);
    localStorage.setItem('dashboard-theme', currentTheme);
    updateThemeIcon();

    // Actualizar gráficas con el nuevo tema
    updateChartsTheme();
}

function updateThemeIcon() {
    const icon = document.getElementById('theme-icon');
    icon.textContent = currentTheme === 'light' ? '🌙' : '☀️';
}

function updateChartsTheme() {
    // Actualizar todas las gráficas con el nuevo tema
    if (chartSucursal) chartSucursal.updateOptions(getChartThemeOptions());
    if (chartTipoPago) chartTipoPago.updateOptions(getChartThemeOptions());
    if (chartDeuda) chartDeuda.updateOptions(getChartThemeOptions());
    if (chartVentasAnuales) chartVentasAnuales.updateOptions(getChartThemeOptions());
    if (chartComparativo) chartComparativo.updateOptions(getChartThemeOptions());
    if (chartTendencia) chartTendencia.updateOptions(getChartThemeOptions());
    if (chartTopProductos) chartTopProductos.updateOptions(getChartThemeOptions());
    if (chartCategorias) chartCategorias.updateOptions(getChartThemeOptions());
    if (chartFacturacion) chartFacturacion.updateOptions(getChartThemeOptions());
    if (chartEstadoFacturas) chartEstadoFacturas.updateOptions(getChartThemeOptions());
    if (chartMetodosPago) chartMetodosPago.updateOptions(getChartThemeOptions());
}

function getChartThemeOptions() {
    const isDark = currentTheme === 'dark';
    return {
        theme: {
            mode: isDark ? 'dark' : 'light'
        },
        chart: {
            background: 'transparent'
        }
    };
}

// ===== INICIALIZACIÓN =====
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
    updateDateTime();
    setInterval(updateDateTime, 1000);

    // Establecer fecha actual
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('fecha-hoy').value = today;

    // Establecer fechas para análisis de productos
    document.getElementById('fecha-inicio-productos').value = today;
    document.getElementById('fecha-fin-productos').value = today;

    // Establecer fechas para análisis de facturación
    document.getElementById('fecha-inicio-facturacion').value = today;
    document.getElementById('fecha-fin-facturacion').value = today;

    // Cargar datos iniciales
    cargaDefiltros();
});

function updateDateTime() {
    const now = new Date();
    const dateOptions = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };
    const timeOptions = {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    };

    document.getElementById('current-date').textContent = now.toLocaleDateString('es-ES', dateOptions);
    document.getElementById('current-time').textContent = now.toLocaleTimeString('es-ES', timeOptions);
}

// ===== FUNCIONES DE CARGA DE DATOS =====
function cargaDefiltros() {
    const idempresa = document.getElementById('idempresa').value;
    if (idempresa && idempresa !== '-1') {
        cargarSucursales(idempresa);
        buscarIngresosDiarios();
    }
}

function cargarSucursales(idempresa) {
    // Usar ruta específica para supervisores o la ruta general para otros roles
    const isSupervisor = {{ is_supervisor ? 'true' : 'false' }};
    const route = isSupervisor ?
        `{{ path('almacen-obtener-sucursal-supervisor') }}?idempresa=${idempresa}` :
        `{{ path('almacen-obtener-sucursal') }}?idempresa=${idempresa}`;

    fetch(route)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(html => {
            const sucursalesDiv = document.getElementById('sucursales');
            const sucursalesCount = document.getElementById('sucursales-count');

            // Agregar opción "Todas las sucursales"
            const allCheckbox = document.createElement('div');
            allCheckbox.className = 'form-check mb-2';
            allCheckbox.innerHTML = `
                <input class="form-check-input" type="checkbox" id="sucursal-all" value="all" checked onchange="toggleAllSucursales(this)">
                <label class="form-check-label fw-bold" for="sucursal-all">
                    <i class="fas fa-building"></i> Todas las Sucursales
                </label>
            `;

            // Agregar separador
            const separator = document.createElement('hr');
            separator.className = 'my-2';

            // Crear contenedor para el HTML de sucursales
            const sucursalesContainer = document.createElement('div');
            sucursalesContainer.innerHTML = html;

            // Limpiar y agregar contenido
            sucursalesDiv.innerHTML = '';
            sucursalesDiv.appendChild(allCheckbox);
            sucursalesDiv.appendChild(separator);
            sucursalesDiv.appendChild(sucursalesContainer);

            // Contar sucursales
            const checkboxes = sucursalesDiv.querySelectorAll('input[type="checkbox"]:not(#sucursal-all)');
            sucursalesCount.textContent = `${checkboxes.length} sucursales seleccionadas`;

            // Agregar clase CSS a los checkboxes individuales para el manejo
            checkboxes.forEach(checkbox => {
                checkbox.classList.add('sucursal-individual');
                checkbox.setAttribute('onchange', 'updateSucursalesSelection()');
                checkbox.checked = true; // Marcar todos por defecto
            });
        })
        .catch(error => {
            console.error('Error al cargar sucursales:', error);

            // Mostrar sucursales de ejemplo en caso de error
            const sucursalesDiv = document.getElementById('sucursales');
            const sucursalesCount = document.getElementById('sucursales-count');

            sucursalesDiv.innerHTML = `
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    No se pudieron cargar las sucursales. Usando datos de ejemplo.
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="sucursal-all" value="all" checked onchange="toggleAllSucursales(this)">
                    <label class="form-check-label fw-bold" for="sucursal-all">
                        <i class="fas fa-building"></i> Todas las Sucursales
                    </label>
                </div>
                <hr class="my-2">
                <div class="form-check mb-1">
                    <input class="form-check-input sucursal-individual" type="checkbox" id="sucursal-1" value="1" checked onchange="updateSucursalesSelection()">
                    <label class="form-check-label" for="sucursal-1">
                        <i class="fas fa-store"></i> Sucursal Centro
                    </label>
                </div>
                <div class="form-check mb-1">
                    <input class="form-check-input sucursal-individual" type="checkbox" id="sucursal-2" value="2" checked onchange="updateSucursalesSelection()">
                    <label class="form-check-label" for="sucursal-2">
                        <i class="fas fa-store"></i> Sucursal Norte
                    </label>
                </div>
                <div class="form-check mb-1">
                    <input class="form-check-input sucursal-individual" type="checkbox" id="sucursal-3" value="3" checked onchange="updateSucursalesSelection()">
                    <label class="form-check-label" for="sucursal-3">
                        <i class="fas fa-store"></i> Sucursal Sur
                    </label>
                </div>
            `;
            sucursalesCount.textContent = '3 sucursales seleccionadas (ejemplo)';
        });
}

function toggleSucursalesPanel() {
    const panel = document.getElementById('sucursales-panel');
    const icon = document.querySelector('#sucursales-summary i');

    if (panel.style.display === 'none' || panel.style.display === '') {
        panel.style.display = 'block';
        icon.style.transform = 'rotate(180deg)';
    } else {
        panel.style.display = 'none';
        icon.style.transform = 'rotate(0deg)';
    }
}

function toggleAllSucursales(checkbox) {
    const individualCheckboxes = document.querySelectorAll('.sucursal-individual');
    individualCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
    updateSucursalesSelection();
}

function updateSucursalesSelection() {
    const allCheckbox = document.getElementById('sucursal-all');
    const individualCheckboxes = document.querySelectorAll('.sucursal-individual');
    const checkedIndividual = document.querySelectorAll('.sucursal-individual:checked');
    const sucursalesCount = document.getElementById('sucursales-count');

    // Actualizar el checkbox "Todas"
    if (checkedIndividual.length === individualCheckboxes.length) {
        allCheckbox.checked = true;
        allCheckbox.indeterminate = false;
    } else if (checkedIndividual.length === 0) {
        allCheckbox.checked = false;
        allCheckbox.indeterminate = false;
    } else {
        allCheckbox.checked = false;
        allCheckbox.indeterminate = true;
    }

    // Actualizar el contador
    if (checkedIndividual.length === individualCheckboxes.length) {
        sucursalesCount.textContent = `${individualCheckboxes.length} sucursales seleccionadas`;
    } else {
        sucursalesCount.textContent = `${checkedIndividual.length} de ${individualCheckboxes.length} sucursales seleccionadas`;
    }
}

// ===== FUNCIONES DE BÚSQUEDA =====
function buscarIngresosDiarios() {
    const fecha = document.getElementById('fecha-hoy').value;
    const idempresa = document.getElementById('idempresa').value;
    const tipoVenta = document.getElementById('tipo-venta').value;

    if (!fecha || !idempresa || idempresa === '-1') {
        if (typeof Swal !== 'undefined') {
            Swal.fire('Error', 'Por favor selecciona una fecha y empresa válidas', 'error');
        } else {
            alert('Por favor selecciona una fecha y empresa válidas');
        }
        return;
    }

    // Obtener sucursales seleccionadas
    const sucursalesSeleccionadas = Array.from(document.querySelectorAll('.sucursal-individual:checked'))
        .map(cb => cb.value);

    if (sucursalesSeleccionadas.length === 0) {
        if (typeof Swal !== 'undefined') {
            Swal.fire('Error', 'Por favor selecciona al menos una sucursal', 'error');
        } else {
            alert('Por favor selecciona al menos una sucursal');
        }
        return;
    }

    const params = new URLSearchParams({
        fecha: fecha,
        idempresa: idempresa,
        tipo_venta: tipoVenta,
        sucursales: sucursalesSeleccionadas.join(',')
    });

    // Mostrar loading en métricas
    showLoadingMetrics();

    fetch(`/cliente-api/get-ingreso-diario-overview?todayDate=${fecha}&sucursales=${sucursalesSeleccionadas.join(',')}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Usar la estructura de datos de la API existente
            const metricas = {
                ventas_total: data.sumaVentaCobrar?.totalVenta || 0,
                pagos_total: data.sumaPagos?.totalCobrado || 0,
                por_cobrar_total: data.sumaVentaCobrar?.porCobrar || 0,
                ventas_trend: 0, // La API original no incluye tendencias
                pagos_trend: 0,
                deuda_trend: 0
            };

            updateMetrics(metricas);

            // Cargar datos detallados para gráficas
            cargarDatosDetallados();
        })
        .catch(error => {
            console.error('Error:', error);

            // En caso de error, cargar datos de ejemplo
            const datosEjemplo = {
                metricas: {
                    ventas_total: 125000.50,
                    pagos_total: 98000.25,
                    por_cobrar_total: 27000.25,
                    ventas_trend: 12.5,
                    pagos_trend: 8.3,
                    deuda_trend: 0
                },
                graficas: {
                    sucursales: [
                        {sucursal: 'Sucursal Centro', total: 45000},
                        {sucursal: 'Sucursal Norte', total: 38000},
                        {sucursal: 'Sucursal Sur', total: 42500}
                    ],
                    tipos_pago: [
                        {tipo: 'Efectivo', total: 45000},
                        {tipo: 'Tarjeta', total: 35000},
                        {tipo: 'Transferencia', total: 18000},
                        {tipo: 'Crédito', total: 27000}
                    ],
                    deuda: [
                        {sucursal: 'Sucursal Centro', deuda: 12000},
                        {sucursal: 'Sucursal Norte', deuda: 8000},
                        {sucursal: 'Sucursal Sur', deuda: 7000}
                    ]
                },
                tabla: [
                    {sucursal: 'Sucursal Centro', ventas: 45000, pagos: 35000, por_cobrar: 10000},
                    {sucursal: 'Sucursal Norte', ventas: 38000, pagos: 30000, por_cobrar: 8000},
                    {sucursal: 'Sucursal Sur', ventas: 42500, pagos: 33250, por_cobrar: 9250}
                ]
            };

            updateMetrics(datosEjemplo.metricas);
            updateCharts(datosEjemplo.graficas);
            updateTable(datosEjemplo.tabla);

            if (typeof Swal !== 'undefined') {
                Swal.fire('Información', 'No se pudo conectar al servidor. Mostrando datos de ejemplo.', 'info');
            } else {
                console.log('No se pudo conectar al servidor. Mostrando datos de ejemplo.');
            }
        });
}

function cargarDatosDetallados() {
    const fecha = document.getElementById('fecha-hoy').value;
    const sucursalesSeleccionadas = Array.from(document.querySelectorAll('.sucursal-individual:checked'))
        .map(cb => cb.value);

    if (sucursalesSeleccionadas.length === 0) return;

    // Cargar datos detallados para gráficas y tablas
    fetch(`/cliente-api/get-ingreso-diario-details?todayDate=${fecha}&sucursales=${sucursalesSeleccionadas.join(',')}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Procesar datos para gráficas
            if (data.dataPerBranch) {
                updateSucursalChart(data.dataPerBranch);
            }
            if (data.dataPerPaymentType) {
                updateTipoPagoChart(data.dataPerPaymentType);
            }
            if (data.debtPerBranch) {
                updateDeudaChart(data.debtPerBranch);
            }

            // Actualizar tabla
            if (data.tableData) {
                updateTable(data.tableData);
            }
        })
        .catch(error => {
            console.error('Error al cargar datos detallados:', error);
            // Cargar datos de ejemplo en caso de error
            cargarDatosEjemplo();
        });
}

function cargarDatosEjemplo() {
    const datosEjemplo = {
        dataPerBranch: [
            {sucursal: 'Sucursal Centro', total: 45000},
            {sucursal: 'Sucursal Norte', total: 38000},
            {sucursal: 'Sucursal Sur', total: 42500}
        ],
        dataPerPaymentType: [
            {tipo: 'Efectivo', total: 45000},
            {tipo: 'Tarjeta', total: 35000},
            {tipo: 'Transferencia', total: 18000},
            {tipo: 'Crédito', total: 27000}
        ],
        debtPerBranch: [
            {sucursal: 'Sucursal Centro', deuda: 12000},
            {sucursal: 'Sucursal Norte', deuda: 8000},
            {sucursal: 'Sucursal Sur', deuda: 7000}
        ],
        tableData: [
            {sucursal: 'Sucursal Centro', ventas: 45000, pagos: 35000, por_cobrar: 10000},
            {sucursal: 'Sucursal Norte', ventas: 38000, pagos: 30000, por_cobrar: 8000},
            {sucursal: 'Sucursal Sur', ventas: 42500, pagos: 33250, por_cobrar: 9250}
        ]
    };

    updateSucursalChart(datosEjemplo.dataPerBranch);
    updateTipoPagoChart(datosEjemplo.dataPerPaymentType);
    updateDeudaChart(datosEjemplo.debtPerBranch);
    updateTable(datosEjemplo.tableData);
}

function buscarVentasAnuales() {
    const year = document.getElementById('year-select').value;
    const idempresa = document.getElementById('idempresa').value;

    if (!year || !idempresa || idempresa === '-1') {
        Swal.fire('Error', 'Por favor selecciona un año y empresa válidos', 'error');
        return;
    }

    const params = new URLSearchParams({
        year: year,
        idempresa: idempresa
    });

    fetch(`/admin/oficial/ventas-anuales?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateAnnualCharts(data.graficas);
            } else {
                Swal.fire('Error', data.message || 'Error al cargar los datos anuales', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire('Error', 'Error de conexión al servidor', 'error');
        });
}

function buscarProductos() {
    const fechaInicio = document.getElementById('fecha-inicio-productos').value;
    const fechaFin = document.getElementById('fecha-fin-productos').value;
    const idempresa = document.getElementById('idempresa').value;

    if (!fechaInicio || !fechaFin || !idempresa || idempresa === '-1') {
        Swal.fire('Error', 'Por favor completa todos los campos', 'error');
        return;
    }

    const params = new URLSearchParams({
        fecha_inicio: fechaInicio,
        fecha_fin: fechaFin,
        idempresa: idempresa
    });

    fetch(`/admin/oficial/productos?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateProductCharts(data.graficas);
                updateProductTable(data.tabla);
            } else {
                Swal.fire('Error', data.message || 'Error al cargar los datos de productos', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire('Error', 'Error de conexión al servidor', 'error');
        });
}

function buscarFacturacion() {
    const fechaInicio = document.getElementById('fecha-inicio-facturacion').value;
    const fechaFin = document.getElementById('fecha-fin-facturacion').value;
    const idempresa = document.getElementById('idempresa').value;

    if (!fechaInicio || !fechaFin || !idempresa || idempresa === '-1') {
        Swal.fire('Error', 'Por favor completa todos los campos', 'error');
        return;
    }

    const params = new URLSearchParams({
        fecha_inicio: fechaInicio,
        fecha_fin: fechaFin,
        idempresa: idempresa
    });

    fetch(`/admin/oficial/facturacion?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateBillingCharts(data.graficas);
                updateBillingTable(data.tabla);
            } else {
                Swal.fire('Error', data.message || 'Error al cargar los datos de facturación', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire('Error', 'Error de conexión al servidor', 'error');
        });
}

// ===== FUNCIONES DE ACTUALIZACIÓN DE UI =====
function showLoadingMetrics() {
    document.getElementById('ventas-total').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    document.getElementById('pagos-total').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    document.getElementById('por-cobrar-total').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
}

function updateMetrics(metricas) {
    // Actualizar valores
    document.getElementById('ventas-total').textContent = formatCurrency(metricas.ventas_total || 0);
    document.getElementById('pagos-total').textContent = formatCurrency(metricas.pagos_total || 0);
    document.getElementById('por-cobrar-total').textContent = formatCurrency(metricas.por_cobrar_total || 0);

    // Actualizar tendencias
    updateTrend('ventas-trend', metricas.ventas_trend || 0);
    updateTrend('pagos-trend', metricas.pagos_trend || 0);
    updateTrend('deuda-trend', metricas.deuda_trend || 0);
}

function updateTrend(elementId, value) {
    const element = document.getElementById(elementId);
    const isPositive = value > 0;
    const isNegative = value < 0;

    element.className = `metric-trend ${isPositive ? 'positive' : isNegative ? 'negative' : 'neutral'}`;
    element.innerHTML = `
        <i class="fas fa-arrow-${isPositive ? 'up' : isNegative ? 'down' : 'minus'}"></i>
        ${isPositive ? '+' : ''}${value.toFixed(1)}%
    `;
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('es-MX', {
        style: 'currency',
        currency: 'MXN'
    }).format(amount);
}

// ===== FUNCIONES DE GRÁFICAS =====
function updateCharts(graficas) {
    // Actualizar gráfica de sucursales
    if (graficas.sucursales) {
        updateSucursalChart(graficas.sucursales);
    }

    // Actualizar gráfica de tipos de pago
    if (graficas.tipos_pago) {
        updateTipoPagoChart(graficas.tipos_pago);
    }

    // Actualizar gráfica de deuda
    if (graficas.deuda) {
        updateDeudaChart(graficas.deuda);
    }
}

function updateSucursalChart(data) {
    const options = {
        ...getChartThemeOptions(),
        series: [{
            name: 'Ingresos',
            data: data.map(item => item.total)
        }],
        chart: {
            type: 'bar',
            height: 300,
            background: 'transparent'
        },
        xaxis: {
            categories: data.map(item => item.sucursal)
        },
        colors: ['#2563eb'],
        title: {
            text: 'Ingresos por Sucursal',
            align: 'center'
        }
    };

    if (chartSucursal) {
        chartSucursal.destroy();
    }
    chartSucursal = new ApexCharts(document.querySelector("#graficaSucursal"), options);
    chartSucursal.render();
}

function updateTipoPagoChart(data) {
    const options = {
        ...getChartThemeOptions(),
        series: data.map(item => item.total),
        chart: {
            type: 'pie',
            height: 300,
            background: 'transparent'
        },
        labels: data.map(item => item.tipo),
        colors: ['#2563eb', '#059669', '#d97706', '#dc2626'],
        title: {
            text: 'Tipos de Pago',
            align: 'center'
        }
    };

    if (chartTipoPago) {
        chartTipoPago.destroy();
    }
    chartTipoPago = new ApexCharts(document.querySelector("#graficaTipoPago"), options);
    chartTipoPago.render();
}

function updateDeudaChart(data) {
    const options = {
        ...getChartThemeOptions(),
        series: [{
            name: 'Deuda',
            data: data.map(item => item.deuda)
        }],
        chart: {
            type: 'column',
            height: 300,
            background: 'transparent'
        },
        xaxis: {
            categories: data.map(item => item.sucursal)
        },
        colors: ['#dc2626'],
        title: {
            text: 'Deuda por Sucursal',
            align: 'center'
        }
    };

    if (chartDeuda) {
        chartDeuda.destroy();
    }
    chartDeuda = new ApexCharts(document.querySelector("#deudaTotal"), options);
    chartDeuda.render();
}

// ===== FUNCIONES DE TABLAS =====
function updateTable(data) {
    if (tablaIngresosDiarios) {
        tablaIngresosDiarios.destroy();
    }

    tablaIngresosDiarios = $('#tablaIngresosDiarios').DataTable({
        data: data,
        columns: [
            { title: 'Sucursal', data: 'sucursal' },
            { title: 'Ventas', data: 'ventas', render: function(data) { return formatCurrency(data); } },
            { title: 'Pagos', data: 'pagos', render: function(data) { return formatCurrency(data); } },
            { title: 'Por Cobrar', data: 'por_cobrar', render: function(data) { return formatCurrency(data); } }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json'
        },
        responsive: true,
        dom: 'Bfrtip',
        buttons: ['copy', 'csv', 'excel', 'pdf', 'print']
    });
}

// ===== FUNCIONES DE EXPORTACIÓN =====
function exportarExcel() {
    Swal.fire('Info', 'Función de exportación a Excel en desarrollo', 'info');
}

function exportarPDF() {
    Swal.fire('Info', 'Función de exportación a PDF en desarrollo', 'info');
}

function exportarCSV() {
    Swal.fire('Info', 'Función de exportación a CSV en desarrollo', 'info');
}

// ===== FUNCIÓN PARA CERRAR SESIÓN CON CONFIRMACIÓN =====
function confirmarCerrarSesion() {
    Swal.fire({
        title: '¿Cerrar Sesión?',
        text: '¿Estás seguro de que deseas cerrar tu sesión?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="fas fa-sign-out-alt"></i> Sí, Cerrar Sesión',
        cancelButtonText: '<i class="fas fa-times"></i> Cancelar',
        reverseButtons: true,
        customClass: {
            popup: 'swal2-popup-custom',
            title: 'swal2-title-custom',
            content: 'swal2-content-custom',
            confirmButton: 'swal2-confirm-custom',
            cancelButton: 'swal2-cancel-custom'
        },
        buttonsStyling: true,
        allowOutsideClick: false,
        allowEscapeKey: true,
        focusConfirm: false,
        focusCancel: true
    }).then((result) => {
        if (result.isConfirmed) {
            // Mostrar loading mientras se cierra sesión
            Swal.fire({
                title: 'Cerrando Sesión...',
                text: 'Por favor espera un momento',
                icon: 'info',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Redirigir al logout después de un breve delay
            setTimeout(() => {
                window.location.href = '/admin/logout';
            }, 1000);
        }
    });
}

</script>
{% endblock %}
