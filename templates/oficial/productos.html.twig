<div class="cont">
    <label class="filter-title">CATEGORÍAS:</label>
    <div class="col-md-12 miLista">
        <fieldset id="idproductos" onChange="obtenerCategorias()">
            <label class="check" for="todasClases" >
                <input type="checkbox" name="todasClases" id="todasClases" onClick="toggleClases(this)">
            Todas</label>
            {% for productos in productoss %}
                <label class="check" for="checkbox-productos-{{ productos.idproductos }}" >
                    <input type="checkbox" id="checkbox-productos-{{ productos.idproductos }}" name="productos" value="{{ productos.idproductos }}">
                {{ productos.nombre }}</label>
            {% endfor %}
        </fieldset>
    </div>
</div>

<script language="JavaScript">

    function toggleClases(source) 
    {
        checkboxes = document.getElementsByName('productos');
        for(var i=0, n=checkboxes.length;i<n;i++) {
            checkboxes[i].checked = source.checked;
        }
    }
</script>