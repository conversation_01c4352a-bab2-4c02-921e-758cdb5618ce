<div clas="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="table-responsive">
                <div class="col-md-12">
                    <div class="card">

                        <div class="card-body">
                            <h5 class="card-tittle">Filtros:</h5>
                            <div class="row">

                                <div class="col-md-4 d-flex align-items-center">
                                    <select name="empresa" id="idempresa" class="form-control margen-superior">
                                        <option value="-1">Seleccione una empresa</option>
                                        {% for filtroEmpresa in filtrosEmpresas %}
                                        <option value="{{ filtroEmpresa.idempresa }}">{{ filtroEmpresa.Empresa }}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <div class="col-md-4 d-flex align-items-center">
                                    <select name="sucursal" id="idsucursal" class="form-control margen-superior">
                                        <option value="-1">Seleccione una sucursal</option>
                                        <option select="selected" value="todasSucursales">TODAS LAS SUCURSALES</option>
                                        {% for filtroSucursal in filtroSucursales %}
                                        <option value="{{ filtroSucursal.idsucursal }}">{{ filtroSucursal.Sucursal }}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <div class="col-md-4 d-flex align-items-center">
                                    <button type="button" class="btn btn-primary mt-3" onclick="obtenerOrdenesCompraAdministrador();">Buscar</button>
                                </div>

                                    <div class="d-flex align-items-center justify-content-between">
                                        <label class="lab reporte">Rango por días:</label>
                                    </div> 
                                    
                                    <div class="input-daterange input-group rango-tiempo" id="idfilter-calender">
                                        <input id="fecha-inicio-rango-dia" type="text" autocomplete="off" class="input-sm form-control" name="start" />

                                        <span class="input-group-addon"> a </span>

                                        <input id="fecha-fin-rango-dia" type="text" autocomplete="off" class="input-sm form-control" name="end" />

                                        <button class="btn btn-warning" onclick="resetRangoFechaDias()"><i class="fa fa-eraser" aria-hidden="true"></i></button>
                                        
                                    </div>
                                </div>
                            </div>
                            <div id="tableOrdenesAdmindiv"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function () {
        let table = new DataTable('#tableOrdenesAdmin', {
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/es-ES.json',
            },
        });
    });
    
    
    jQuery(function ($) {
        $('#fecha-inicio-rango-dia').datetimepicker({
            "pickTime": false,
            "pickDate": true,
            "minDate": "1/1/1900",
            "maxDate": null,
            "showToday": true,
            "language": "es_MX",
            "defaultDate": "",
            "disabledDates": [],
            "enabledDates": [],
            "icons": {
                "time": "fa fa-clock-o",
                "date": "fa fa-calendar",
                "up": "fa fa-chevron-up",
                "down": "fa fa-chevron-down"
            },
            "useStrict": false,
            "sideBySide": false,
            "daysOfWeekDisabled": [],
            "collapse": true,
            "calendarWeeks": false,
            "viewMode": "days",
            "minViewMode": "days",
            "useCurrent": false,
            "useSeconds": false
        });
    
        $('#fecha-fin-rango-dia').datetimepicker({
            "pickTime": false,
            "pickDate": true,
            "minDate": "1/1/1900",
            "maxDate": null,
            "showToday": true,
            "language": "es_MX",
            "defaultDate": "",
            "disabledDates": [],
            "enabledDates": [],
            "icons": {
                "time": "fa fa-clock-o",
                "date": "fa fa-calendar",
                "up": "fa fa-chevron-up",
                "down": "fa fa-chevron-down"
            },
            "useStrict": false,
            "sideBySide": false,
            "daysOfWeekDisabled": [],
            "collapse": true,
            "calendarWeeks": false,
            "viewMode": "days",
            "minViewMode": "days",
            "useCurrent": false,
            "useSeconds": false
        });
    });
    
    function resetRangoFechaDias() {
        $("#fecha-inicio-rango-dia").val("");
        $("#fecha-fin-rango-dia").val("");
    }

    var idempresa = $("#idempresa").val();
    $('#idempresa').val(idempresa);


    function obtenerOrdenesCompraAdministrador()
{
    
    var fechaInicio = document.getElementById('fecha-inicio-rango-dia').value;
    var fechaFin = document.getElementById('fecha-fin-rango-dia').value;
    var idsucursal = $('#idsucursal').val();
        $('#idsucursal').val(idsucursal);
    var idempresa = $('#idempresa').val();

    var idsucursal = $("#idsucursal").val();
    $('#idsucursal').val(idsucursal);

    var datos = {
        fechaInicio:fechaInicio,
        fechaFin:fechaFin,
        idsucursal: idsucursal,
        idempresa: idempresa,
    };

    console.log(datos);

    $.ajax({
        url: "{{path('table-ordenes-admin')}}",
        data: datos,
        dataType: "html",
        beforeSend: function(xhr){
            loadingGif("contenedor-graficas");
        }
    }).done(function(html){
        $("#tableOrdenesAdmindiv").html(html);
    }).fail(function(hmtl){
        alert("Error tableadmin")
    });
}

    
    </script>