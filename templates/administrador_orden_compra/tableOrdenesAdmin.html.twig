<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">

<!-- Buttons CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.3.6/css/buttons.dataTables.min.css">

<!-- Tu CSS personalizado -->
<link rel="stylesheet" href="{{ asset('/css/administradorOrdenes.css') }}">


<div class="col-md-12">
    <div class="table-responsive">
        <table class="table mx-auto" id="tableOrdenesAdmin">
            <thead>
            <tr class="text-center">
                <th scope="col">Folio</th>
                <th scope="col">Nombre de Sucursal</th>
                <th scope="col">Tipo de Venta</th>
                <th scope="col">Nombre del Cliente</th>
                <th scope="col">Empresa Cliente</th>
                <th scope="col">Beneficiario</th>
                <th scope="col">Número de Empleado</th>
                <th scope="col">Unidad</th>
                <th scope="col">Categoria</th>
                <th scope="col">Subategoria</th>
                <th scope="col">Marca</th>
                <th scope="col">Modelo</th>
                <th scope="col">Diseño/Material/Tratamiento</th>
                <th scope="col">Base</th>
                <th scope="col">Esfera D</th>
                <th scope="col">Cilindro D</th>
                <th scope="col">Eje D</th>
                <th scope="col">Esfera I</th>
                <th scope="col">Cilindro I</th>
                <th scope="col">Eje I</th>
                <th scope="col">Add</th>
                <th scope="col">Etapa</th>
                <th scope="col">Folio Autorización</th>
                <th scope="col">Status Autorizacion</th>
                <th scope="col">Fecha Recepción</th>
                <th scope="col">Fecha de Entrada a Laboratorio</th>
                <th scope="col">Status de Laboratorio</th>
                <th scope="col">Fecha de Salida de Laboratorio</th>
                <th scope="col">Garantia</th>
                <th scope="col" class="hidden">ID</th>
            </tr>
            </thead>
            <tbody>
            {% for orden in ordenesdeCompra %}
                <tr class="text-center align-middle">
                    <td class="folio">{{ orden.Folio }}</td>
                    <td>{{ orden.Sucursal }}</td>
                    <td class="tipoventa">{{ orden.TipodeVenta }}</td>
                    <td>{{ orden.PrimerNombre ~ ' ' ~ orden.ApellidoPaterno ~ ' ' ~ orden.ApellidoMaterno }}</td>
                    <td>{{ orden.EmpresaCliente }}</td>
                    <td>{{ orden.Beneficiario }}</td>
                    <td>{{ orden.NumerodeEmpleado }}</td>
                    <td>{{ orden.Unidad }}</td>
                    <td>{{ orden.Categoria }}</td>
                    <td>{{ orden.Subcategoria }}</td>
                    <td>{{ orden.Marca }}</td>
                    <td>{{ orden.Modelo }}</td>
                    <td>{{ orden.DisenoLente ~ ' / ' ~ orden.Material ~ ' / ' ~ orden.Tratamiento }}</td>

                    <td>
                        <select class="status-select" data-name="statusBase">
                            <option value="0" {% if orden.Base == '0' %}selected{% endif %}>{{ orden.Base }}</option>
                            <option value="/" {% if orden.Base == '/' %}selected{% endif %}>/</option>
                            <option value="PLANA" {% if orden.Base == 'PLANA' %}selected{% endif %}>PLANA</option>
                            <option value="2" {% if orden.Base == '2' %}selected{% endif %}>2</option>
                            <option value="4" {% if orden.Base == '4' %}selected{% endif %}>4</option>
                            <option value="6" {% if orden.Base == '6' %}selected{% endif %}>6</option>
                            <option value="8" {% if orden.Base == '8' %}selected{% endif %}>8</option>
                        </select>
                    </td>

                    <td>{{ orden.EsferaD }}</td>
                    <td>{{ orden.CilD }}</td>
                    <td>{{ orden.EjeD }}</td>
                    <td>{{ orden.EsfI }}</td>
                    <td>{{ orden.CilI }}</td>
                    <td>{{ orden.EjeI }}</td>
                    <td>{{ orden.AddOrden }}</td>
                    <td>{{ orden.Etapa }}</td>
                    <td>{{ orden.FolioAutorizacion }}</td>
                    <td class="StatusAutorizacion">
                        <select class="status-select" data-name="statusAutorizacion">
                            <option value="0"
                                    {% if orden.StatusAutorizacion == '0' %}selected{% endif %}>{{ orden.StatusAutorizacion }}</option>
                            <option value="SUCURSAL" {% if orden.StatusAutorizacion == '1' %}selected{% endif %}>
                                SUCURSAL
                            </option>
                            <option value="CORPOORATIVO" {% if orden.StatusAutorizacion == '2' %}selected{% endif %}>
                                CORPOORATIVO
                            </option>
                            <option value="FACTURADA" {% if orden.StatusAutorizacion == '3' %}selected{% endif %}>
                                FACTURADA
                            </option>
                            <option value="N/A" {% if orden.StatusAutorizacion == '4' %}selected{% endif %}>N/A</option>
                        </select>
                    </td>

                    <td>{{ orden.Recepcion|date('d/m/Y') }}</td>

                    <td class="fechaSalidaLaboratorio">
                        <input type="text" name="fechaSalidaLaboratorio" data-name="fechaSalidaLaboratorio"
                               class="fecha-salida-laboratorio datetimeSalida status-select"
                               value="{{ orden.FechaSalidaLaboratorio|date('d-m-Y') }}">
                    </td>
                    <td class="statusLaboratorio">
                        <select class="status-select" data-name="statusLaboratorio">
                            <option value="0"
                                    {% if orden.StatusLaboratorio == '0' %}selected{% endif %}>{{ orden.StatusLaboratorio }}</option>
                            <option value="ESPERA DE MICAS" {% if orden.StatusLaboratorio == '1' %}selected{% endif %}>
                                ESPERA DE MICAS
                            </option>
                            <option value="PROCESO DE BISELADO"
                                    {% if orden.StatusLaboratorio == '2' %}selected{% endif %}>PROCESO DE BISELADO
                            </option>
                            <option value="TALLADO" {% if orden.StatusLaboratorio == '3' %}selected{% endif %}>TALLADO
                            </option>
                            <option value="LENTE DE CONTACTO"
                                    {% if orden.StatusLaboratorio == '4' %}selected{% endif %}>LENTE DE CONTACTO
                            </option>
                            <option value="REPARACIÓN" {% if orden.StatusLaboratorio == '5' %}selected{% endif %}>
                                REPARACIÓN
                            </option>
                            <option value="ENVIADO" {% if orden.StatusLaboratorio == '6' %}selected{% endif %}>ENVIADO
                            </option>
                            <option value="ORDEN NO RECIBIDA"
                                    {% if orden.StatusLaboratorio == '7' %}selected{% endif %}>ORDEN NO RECIBIDA
                            </option>
                            <option value="MICA DEFECTUOSA" {% if orden.StatusLaboratorio == '8' %}selected{% endif %}>
                                MICA DEFECTUOSA
                            </option>
                            <option value="ORDEN REPETIDA" {% if orden.StatusLaboratorio == '9' %}selected{% endif %}>
                                ORDEN REPETIDA
                            </option>
                            <option value="ESPERA DE ARMAZON"
                                    {% if orden.StatusLaboratorio == '10' %}selected{% endif %}>ESPERA DE ARMAZON
                            </option>
                        </select>
                    </td>

                    <td class="fechaEntradaLaboratorio">
                        <input type="text" name="fechaEntradaLaboratorio" data-name="fechaEntradaLaboratorio"
                               class="fecha-entrada-laboratorio datetimeEntrada status-select"
                               value="{{ orden.FechaEntradaLaboratorio|date('d-m-Y') }}">
                    </td>

                    <td>{{ orden.Garantia }}</td>
                    <td class="idordenlaboratorio hidden">{{ orden.idordenlaboratorio }}</td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
    <div id="modal-edit-input-div"></div>
</div>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>

<!-- JSZip para exportar a Excel -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>

<!-- Buttons JS -->
<script src="https://cdn.datatables.net/buttons/2.3.6/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.print.min.js"></script>



<script>


    $(document).ready(function () {
        console.log("Lenon")
        $('#tableOrdenesAdmin').DataTable({
            // Habilitar los botones
            dom: 'Bfrtip', // Define la posición de los botones
            buttons: [
                {
                    extend: 'excelHtml5',
                    text: 'Descargar Excel',
                    title: 'Ordenes_de_Compra', // Título del archivo
                    exportOptions: {
                        columns: ':not(.hidden)' // Excluir columnas con la clase 'hidden'
                    }
                },
            ],
        });

        console.log("Lenoncini")

        jQuery(function ($) {
            // Configuración para la fecha de salida
            $('.datetimeSalida').datetimepicker({
                "pickTime": false,
                "pickDate": true,
                "minDate": "1/1/1900",
                "maxDate": null,
                "showToday": true,
                format: 'DD-MM-YYYY',
                "language": "es_MX",
                "defaultDate": "",
                "disabledDates": [],
                "enabledDates": [],
                "icons": {
                    "time": "fa fa-clock-o",
                    "date": "fa fa-calendar",
                    "up": "fa fa-chevron-up",
                    "down": "fa fa-chevron-down"
                },
                "useStrict": false,
                "sideBySide": false,
                "daysOfWeekDisabled": [],
                "collapse": true,
                "calendarWeeks": false,
                "viewMode": "days",
                "minViewMode": "days",
                "useCurrent": false,
                "useSeconds": false
            }).on('dp.change', function (e) {
                // Manejo del evento de cambio de fecha para fecha de salida
                var newValue = $(this).val();
                var statusName = $(this).data('name');
                var orderId = $(this).closest('tr').find('.idordenlaboratorio').text();
                var $element = $(this); // Asegúrate de definir $element aquí
                updateStatus(statusName, newValue, orderId, $element);
            });

            jQuery(function ($) {
                $('.datetimeEntrada').datetimepicker({
                    "pickTime": false,
                    "pickDate": true,
                    "minDate": "1/1/1900",
                    "maxDate": null,
                    "showToday": true,
                    format: 'DD-MM-YYYY',
                    "language": "es_MX",
                    "defaultDate": "",
                    "disabledDates": [],
                    "enabledDates": [],
                    "icons": {
                        "time": "fa fa-clock-o",
                        "date": "fa fa-calendar",
                        "up": "fa fa-chevron-up",
                        "down": "fa fa-chevron-down"
                    },
                    "useStrict": false,
                    "sideBySide": false,
                    "daysOfWeekDisabled": [],
                    "collapse": true,
                    "calendarWeeks": false,
                    "viewMode": "days",
                    "minViewMode": "days",
                    "useCurrent": false,
                    "useSeconds": false
                }).on('dp.change', function (e) {
                    var newValue = $(this).val();
                    var statusName = $(this).data('name');
                    var orderId = $(this).closest('tr').find('.idordenlaboratorio').text();
                    var $element = $(this);
                    updateStatus(statusName, newValue, orderId, $element);
                });
            });

        });
        $('.base-select').on('change', function () {
            var campoBase = $(this).val();
            console.log("Con esto se disparo el evento", campoBase);

        });

        $('.status-select').on('change', function () {
            // Así se obtiene el valor del campo que disparó el evento
            var campo = $(this).val();

            console.log("Con esto se disparo el evento", campo);

            /*
            Obtener el nombre del campo (statusAutorizacion, statusLaboratorio o fechaSalidaLaboratorio fechaEntradaLaboratorio)
            */
            var statusName = $(this).data('name');

            console.log("Que status selecciono:", statusName)


            var orderId = $(this).closest('tr').find('.idordenlaboratorio').text();
            var $element = $(this);

            console.log("El elemento que disparó el evento", $element);

            // Estructura para manejar los diferentes campos
            switch (statusName) {
                case 'statusAutorizacion':
                    // Código para manejar el cambio de statusAutorizacion
                    updateStatus('statusAutorizacion', campo, orderId);
                    break;
                case 'statusLaboratorio':
                    // Código para manejar el cambio de statusLaboratorio
                    updateStatus('statusLaboratorio', campo, orderId);
                    break;
                case 'fechaSalidaLaboratorio':
                    // Código para manejar el cambio de fechaSalidaLaboratorio
                    updateStatus('fechaSalidaLaboratorio', campo, orderId);
                    break;
                case 'fechaEntradaLaboratorio':
                    // Código para manejar el cambio de fechaEntradaLaboratorio
                    updateStatus('fechaEntradaLaboratorio', campo, orderId);
                    break;
                case 'statusBase':
                    // Código para manejar el cambio de statusBase
                    updateStatus('statusBase', campo, orderId);
                    break;
                default:
                    console.log('Campo no reconocidoo:', statusName);
            }
            updateStatus(statusName, campo, orderId, $element);
        });
    });


    function updateStatus(statusName, fieldValue, orderId, $element) {
        var data = {
            statusName: statusName,
            fieldValue: fieldValue,
            orderId: orderId
        };

        console.log("¿Qué se está enviando?", data);

        $.ajax({
            url: "{{ path('edit-ordenes-admin') }}",
            type: 'POST',
            data: data,
            success: function (response) {
                // Asegúrate de que $element es un objeto jQuery válido aquí
                if ($element && $element.length) {
                    $element.removeClass('error-border').addClass('success-border');
                } else {
                    console.error('$element is undefined or not a jQuery object');
                }
            },
            error: function (xhr, status, error) {
                if ($element && $element.length) {
                    $element.closest('td').removeClass('success-border').addClass('error-border');
                } else {
                    console.error('$element is undefined or not a jQuery object');
                }
            }
        });
    }


</script>



