{% extends 'admin/layout.html.twig' %}

{% block content %}

<link rel="stylesheet" href="{{ asset('lib/dataTables/dataTables.min.css') }}">
<script src="{{ asset('lib/dataTables/dataTables.min.js') }}"></script>


        <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header card-header-info bg-primary">
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="card-title"> ADMINISTRADOR ÓRDENES DE COMPRA</h4>
                            </div>
                        </div>
                    </div>
                        <br>
                    <div id="container-Admin">
                        <div class="container-fluid" id="tableAdmin"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

        <script>

            $(document).ready(function(){
                filtrosOrdenesAdministrador();
                $("#container-Admin").collapse('show');
            });

            function filtrosOrdenesAdministrador(){
                $.ajax({
                    url: "{{path('filtros_Ordenes_Compra_Administrador')}}",
                    type: 'GET',                   
                    beforeSend: loadingGif("tablaOrdenes"),
                    data: {},                              
                    dataType: "html"                       
                }).done(function( html ){
                    $("#tableAdmin").html(html);
                }).fail(function() {
                    alert( "error" );
                });
            }


        </script>
{% endblock %}