{% extends 'base.html.twig' %}

{% block title %}Subir Códigos Postales{% endblock %}

{% block body %}
    <h1>Subir archivo de Códigos Postales (.txt)</h1>

    <form method="post" enctype="multipart/form-data">
        <input type="file" name="archivo" required>
        <button type="submit">Procesar archivo</button>
    </form>

    {% if registros %}
        <h2>Resultados procesados:</h2>
        <table border="1" cellpadding="5" cellspacing="0">
            <thead>
                <tr>
                    <th>Código Postal</th>
                    <th>Estado</th>
                    <th>Municipio</th>
                    <th>Ciudad</th>
                    <th>Colonias</th>
                </tr>
            </thead>
            <tbody>
                {% for cp, info in registros %}
                    <tr>
                        <td>{{ cp }}</td>
                        <td>{{ info.estado }}</td>
                        <td>{{ info.municipio }}</td>
                        <td>{{ info.ciudad }}</td>
                        <td>
                            <ul>
                                {% for colonia in info.colonias %}
                                    <li>{{ colonia.nombre }} ({{ colonia.tipo }})</li>
                                {% endfor %}
                            </ul>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    {% endif %}
{% endblock %}
