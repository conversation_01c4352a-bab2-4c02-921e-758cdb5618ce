<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>{% block title %}Dashboard Supervisor{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- jQuery UI CSS -->
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/ui-lightness/jquery-ui.css">

    {% block stylesheets %}{% endblock %}

    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar-brand {
            font-weight: bold;
            color: #007bff !important;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .btn-logout {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }
        .btn-logout:hover {
            background-color: #c82333;
            border-color: #bd2130;
            color: white;
        }

        /* Estilo para el título Optimo Opticas */
        .optimo-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2563eb;
            text-align: center;
            margin: 2rem 0;
            text-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
        }

        /* ===== DARK MODE SUPPORT FOR SUPERVISOR LAYOUT ===== */

        /* Dark theme variables (matching executive dashboard) */
        [data-theme="dark"] {
            --primary-blue: #3b82f6;
            --success-green: #10b981;
            --warning-amber: #fbbf24;
            --danger-red: #f87171;
            --info: #06b6d4;

            /* Dark theme grays */
            --gray-50: #0f172a;
            --gray-100: #1e293b;
            --gray-200: #334155;
            --gray-300: #475569;
            --gray-400: #64748b;
            --gray-500: #94a3b8;
            --gray-600: #cbd5e1;
            --gray-700: #e2e8f0;
            --gray-800: #f1f5f9;
            --gray-900: #f8fafc;
            --white: #0f172a;

            /* Background colors */
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;

            /* Text colors */
            --text-primary: #f8fafc;
            --text-secondary: #e2e8f0;
            --text-tertiary: #cbd5e1;

            /* Border colors */
            --border-primary: #334155;
            --border-secondary: #475569;
        }

        /* Apply dark mode to body and main containers */
        [data-theme="dark"] body {
            background-color: var(--bg-secondary) !important;
            color: var(--text-primary) !important;
        }

        [data-theme="dark"] .container-fluid {
            background-color: var(--bg-secondary) !important;
        }

        [data-theme="dark"] main {
            background-color: var(--bg-secondary) !important;
        }

        /* Override Bootstrap classes for dark mode */
        [data-theme="dark"] .bg-light {
            background-color: var(--bg-secondary) !important;
        }

        [data-theme="dark"] .bg-white {
            background-color: var(--bg-primary) !important;
        }

        [data-theme="dark"] .card {
            background-color: var(--bg-primary) !important;
            border-color: var(--border-primary) !important;
            color: var(--text-primary) !important;
        }

        /* Fix text colors in dark mode */
        [data-theme="dark"] .text-dark {
            color: var(--text-primary) !important;
        }

        [data-theme="dark"] .text-muted {
            color: var(--text-tertiary) !important;
        }

        /* Ensure all text inherits dark mode colors */
        [data-theme="dark"] * {
            color: inherit;
        }

        /* Fix specific elements that might stay white */
        [data-theme="dark"] input,
        [data-theme="dark"] select,
        [data-theme="dark"] textarea {
            background-color: var(--bg-tertiary) !important;
            border-color: var(--border-primary) !important;
            color: var(--text-primary) !important;
        }

        [data-theme="dark"] .btn-primary {
            background-color: var(--primary-blue) !important;
            border-color: var(--primary-blue) !important;
        }

        /* Smooth transitions */
        body, .container-fluid, main, .card {
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
        }
    </style>
</head>
<body>


    <!-- Main Content -->
    <main class="container-fluid mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- jQuery UI -->
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Moment.js (requerido por bootstrap-datetimepicker) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/es.min.js"></script>
    <!-- Bootstrap Datetimepicker -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/js/bootstrap-datetimepicker.min.js"></script>

    {% block javascripts %}{% endblock %}
</body>
</html>
