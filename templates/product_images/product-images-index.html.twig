{% block content %}
    <input id="productId" type="hidden" value={{productId}} disabled>
    <div class="container-md">
        <div class="row">
            <div class="col-12">
                <h3 class="text-center">Subir fotos de producto<h3>
                <h3 class="text-center">Se recomienda que las imagenes sean de 1024 (ancho) x 591 (alto) pixeles<h3>
                <form id="upload-product-image-form" enctype="multipart/form-data" class="needs-validation signature" novalidate>
                        <div class="col-5">
                            <input id="file" type="file" name="file" class="form-control text-center" required>
                        </div>
                            <div class="col-2">
                                <button type="submit" class="new-button signature btn" id="submit-product-image">
                                    <i class="fa-solid fa-arrow-up-from-bracket fa-lg" style="color: #115fd1;"></i>
                                </button>
                            </div>

                            
                        </div>
                        <p id="error-msg" class="text-center"></p>
                        <div class="invalid-feedback">Selecciona un archivo</div>
                </form>
            </div>
        </div>
    </div>
    <div id="product-image-table-container"></div>

{% endblock %}

<script>

    var productId = $("#productId").val();

    $(document).ready(function(){

        getProductImagesTable();

    });

    $('#modal-product-image-visor').on('hidden.bs.modal', function () {
        $("#modal-product-images").modal("show");
    })

    function getProductImagesTable(){

        $.ajax({
            url: "{{path('visor-get-product-images-table')}}", 
            data: {productId:productId},
            beforeSend: loadingGif("product-image-table-container"),
            dataType: "html"
        }).done(function( html ) {
            $("#product-image-table-container").html(html);
            $('#error-msg').css({'color': 'white'});
            $('#error-msg').text('');

        }).fail(function() {
            alert( "error" );
        });
    }

    $("#upload-product-image-form").submit(function(e) {

        e.preventDefault();

        changeButton("submitDocumentos", 0, 1);

        var form = e.target;
        var formData = new FormData(form);

        var idflujoexpediente = $("#flujo-expediente-id").val();

        $.ajax({
            
            url: "{{path('visor-upload-product-image')}}?productId=" + productId,
            type: 'POST',
            data: formData,
            success: function(response) {

                changeButton("submit-product-image", 1, 1);
                if (response.success) {
                    $("#file").val(null);
                    getProductImagesTable();
                } else {
                    $('#error-msg').css({'color': 'red'});
                    $('#error-msg').text(response.msg);
                }
            },
            cache: false,
            contentType: false,
            processData: false
        });
    });

    


</script>
