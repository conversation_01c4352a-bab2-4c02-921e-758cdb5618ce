{% if object.showonlinestore == '1' %}
<button href="#" type="button" class="btn btn-sm btn-info" onclick="openProducImagesVisor('{{ object.idproducto }}','{{ object.descripcion }}')">Imagenes</button>

<script>
    function openProducImagesVisor(productId, productModel){
    
        $("#container-modal-product-images").html("");
        $("#product-model").text(productModel);
    
        $.ajax({
            method: "POST",
            url: "{{path('visor-open-product-images-visor')}}",
            data: { productId:productId }
        })
        .done(function( html ) {
            $("#container-modal-product-images").html(html);
            $("#modal-product-images").modal("show");
    
        });
    }
</script>
{% endif %}