

<div class="row justify-content-md-center mt-5">
    {% if productImages|length > 0 %}
    <div class="col-md-10">
        <table class="table" id="tablaDocumentos">
            <thead>
                <tr class="text-start">
                        <th class="text-start text-center" scope="col">#</th>
                        <th class="text-start text-center" scope="col">Producto</th>
                        <th class="text-start text-center" scope="col">Imagen principal</th>
                        <th class="text-start text-center" scope="col"></th>
                    </tr>

            </thead>
            <tbody id = "tableBody-product-images">


                {% for index, productImage in productImages %}

                    <tr>

                        <td class="text-center align-middle ">{{index + 1}}</td>

                        <td class="text-center align-middle ">{{productImage.modelo}}</td>

                        <td class="text-center align-middle ">
                        {% if productImage.mainimage == '1' %}
                            <i class="fa fa-check text-success" aria-hidden="true"></i>
                        {% endif %}

                        </td>

                        <td class="text-center align-middle ">
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#modal-product-image-visor" onclick="showProductImage('{{productImage.filename}}')">
                                <i class="fa-solid fa-eye"></i>
                            </button>
                            <button type="button" class="btn btn-danger" onclick="deleteProductImage('{{productImage.idproductimages}}')">
                                <i class="fa fa-trash"></i>
                            </button>
                        </td>

                    </tr>

                {% endfor %}
             
            </tbody>
        </table>

    </div>
    {% else %}
        <h3 class="text-center">Aún no hay imagenes cargadas</h3>
    {% endif %}
</div>



<script>

    var productId = $("#productId").val();

    function deleteProductImage(productImageId){

        

        Swal.fire({
            title: '¿Está seguro?',
            text: "Se eliminará la imagen",
            type: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28B463',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Aceptar'
          }).then((result) => {
            if (result.value) {

               $.ajax({
                url: "{{path('visor-delete-product-image')}}",
                method: "POST",
                data: {productImageId:productImageId, productId:productId},
                
                success: function (response) {
                    if (response.success) getProductImagesTable();
                },

              });
            }
          })

    }

    function showProductImage(filename) {

        $("#modal-product-images").modal('hide');

        $("#container-modal-product-image-visor").html("");
    
        $.ajax({
            method: "POST",
            url: "{{path('visor-open-product-image-visor')}}",
            data: { filename:filename, productId:productId}
        })
        .done(function( html ) {
            $("#container-modal-product-image-visor").html(html);    
        });
    }

</script>