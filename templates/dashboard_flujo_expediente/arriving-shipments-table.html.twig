{% if shipments | length > 0 %}
<div class="table-responsive">
    <table class="table">
        <thead>
            <th style="color: #FFFFFF;">#</th>
            <th style="color: #FFFFFF;">Número de envío</th>
            <th style="color: #FFFFFF;">Mensajero</th>
            <th style="color: #FFFFFF;">Fecha de envío</th>
            <th style="color: #FFFFFF;">Detalle</th>
        </thead>
        <tbody>
            {% for index, Shipment in shipments %}
            <tr class="text-center">
                <td style="color: #FFFFFF;">{{index + 1}}</td>
                <td style="color: #FFFFFF;">{{Shipment.trackingnumber}}</td>
                <td style="color: #FFFFFF;">{{Shipment.nombre}}</td>
                <td style="color: #FFFFFF;">{{Shipment.creationdate|date("d/m/Y")}}</td>
                <td>
                    <button 
                        class="btn" 
                        data-toggle="modal" 
                        data-target="#shipment-detail-modal"
                        onclick="showShipmentDetail('{{Shipment.idshipment}}'); changeModalTitle('{{Shipment.trackingnumber}}',)"
                    >
                        <i class="fa-solid fa-circle-info fa-lg" style="color: #FFFFFF;"></i>
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
    <h3 class="text-center">No hay entregas programadas para hoy</h3>
{% endif %}