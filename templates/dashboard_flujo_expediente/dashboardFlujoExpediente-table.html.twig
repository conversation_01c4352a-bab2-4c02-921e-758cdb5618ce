{% if activeFlows|length > 0 %}
<div class="table-responsive">
    <table class="table align-middle " id="clinical-record-flows-table">
        <thead >
            <tr >
                <th>Opciones</th>
                <th>Optometrista</th>
                <th>Paciente</th>
                <th>Unidad</th>
                <th>Etapa</th>
                <th>Última actualización</th>

            </tr>
        </thead>
        <tbody class="text-center">
            {% for result in activeFlows %}
                <tr class="text-center" id="row-{{ result.idflujoexpediente }}">
                    <td>
                        <div class="padre-buttom" style="display: flex; justify-content: center;">
                            <div class="padre-buttom-1">
                                <a class="btn " style="margin: 0 20px" target="_blank" href="/admin/expediente-clinico/{{ result.idflujoexpediente }}"><i class="fa-solid fa-right-to-bracket fa-lg" style="color: #2e931a;"></i></a>
                                 Exámen Visual
                            </div>

                            <div class="padre-buttom-2">
                                <button style="margin: 0 0 0 20px" class="btn btn-warning" onclick="eraseFlow('{{ result.idflujoexpediente }}')" data-id="{{ result.idflujoexpediente }}" ><i class="fa-solid fa-trash-can fa-lg" style="color: #e60000;"></i></button>
                            </div>
                        </div>
                    </td>
                    <td>{{ result.nombreUsuario }} {{ result.apellidoUsuario }}</td>
                    <td>{{ result.nombre ~ ' ' ~ result.apellidopaterno ~ ' ' ~ result.apellidomaterno }}</td>
                    <td>{{ result.unidad }}</td>
                    <td>
                        {% if stages[result.etapa - 1] is defined %}
                            {{stages[result.etapa - 1]}}
                        {% else %}
                            Etapa desconocida
                        {% endif %}
                    </td>
                    <td>{{ result.actualizacion|date("d/m/Y") }}</td>

                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>

    $(document).ready(function(){
        let table = new DataTable('#clinical-record-flows-table',{
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/es-ES.json',
            },
        });
    });

</script>
{% else %}
<h4 class="text-center">Este usuario aún no tiene flujos</h4>
{% endif %}