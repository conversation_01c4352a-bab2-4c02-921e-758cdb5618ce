<a class="btn btn-sm btn-primary" title="Enviar Correo" onclick="sendEmail({{ object.clienteIdcliente.idcliente }})">
    <i class="fa fa-envelope"></i> Enviar Correo
</a>

<script>
    function sendEmail(idcliente) {
        console.log("Enviando correo al cliente ID:", idcliente);

        $.ajax({
            url: "/actualizacion/send-email",
            type: "POST",
            data: {
                idcliente: idcliente
            },
            headers: {
                "X-Requested-With": "XMLHttpRequest", // Indica que es una petición AJAX
                "Content-Type": "application/x-www-form-urlencoded"
            }
        })
            .done(function(response) {
                console.log("Éxito:", response);
                alert("Correo enviado correctamente.");
            })
            .fail(function(xhr, status, error) {
                console.error("Error en la petición AJAX:", xhr.responseText);
                alert("Hubo un error al enviar el correo.");
            });
    }
    /admin/expediente-clinico/documento-expediente?idflujoexpediente=187
</script>
