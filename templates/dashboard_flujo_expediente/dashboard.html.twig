{% extends 'admin/layout.html.twig' %}
{% block titleHead %}{% endblock %}
{% block title %}Dashboard{% endblock %}
{% block content %}

    <link rel="stylesheet" href="{{ asset('/lib/jQuery-Autocomplete-master/content/styles.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/puntodeventa/dashboard.css') }}">
    <link rel="stylesheet" href="{{ asset('lib/fontawesome-free-5.9.0-web/css/all.css') }}">
    <input id="url-obtener-formulario-cliente" type="hidden" value="{{ path('obtener-formulario-cliente') }}">
    <input id="url-buscar-cliente" type="hidden" value="{{ path('buscar-cliente') }}">
    <input id="url-buscar-numero-cliente" type="hidden" value="{{ path('buscar-numero-cliente') }}">
    <input id="url-seleccionar-clientes" type="hidden" value="{{ path('seleccionar-clientes') }}">
    <input id="url-seleccionar-email" type="hidden" value="{{ path('seleccionar-email') }}">
    <input id="url-seleccionar-telefono" type="hidden" value="{{ path('seleccionar-telefono') }}">
    <input id="url-dashboardFlujoExpediente-erase-flow" type="hidden"
           value="{{ path('dashboardFlujoExpediente-erase-flow') }}">


    <div class="container-fluid">
        <h2 class="card-title text-center mt-0 title-dashboard">TABLERO EXPEDIENTES</h2>
        <div class="row filter-dashboard">
            <div class="col-12 col-sm-12 col-md-12 col-lg-4 content-dashboard_father">
                <div class="row">
                    <div class="col-4">
                        <label class="label_bussines" style="padding-left: 10px !important">Empresa:</label>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 dashboard_bussines_search">
                        <select onchange="obtenerSucursales()" id="idempresa">
                            <option value="-1">Selecciona una empresa</option>
                            <option value="1">Optimo Opticas</option>
                            <option value="2">Orthopédie</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-12 col-md-12 col-lg-8">
                <div class="row">
                    <div class="col-12">
                        <div id="sucursales">
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="row table-dashboard">
            <div class="col-12 col-sm-12 col-md-12">
                <div id="flow-card" class="card">
                    <div class="card-body">

                        <div class="row card-flow">
                            <div class="col-8 col-md-6 col-lg-7 col-xl-6 card-flow-left">
                                <h3 class="card-title">Flujos de Expediente</h3>
                                <br>
                                <label for="active-users-select">Filtra por un optometrísta</label>
                                <select id="active-users-select" class="form-control" onchange="flowTable(1)"
                                        style="width: 20%;">
                                    <option value="-1">Mis flujos</option>
                                    {% for user in flowUsers %}
                                        <option value="{{ user.idusuario }}">{{ user.nombre }} {{ user.apellidopaterno }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-4 col-md-6 col-lg-5 col-xl-6 text-right card-flow-rigth">
                                <button class="buttom-flow send-buttom dashboard-buttom-flow" data-bs-toggle="mod"
                                        href="#modal-iniciar-flujo"> Iniciar Flujo
                                </button>
                            </div>
                        </div>
                        </br>
                        <div class="row justify-content-center">
                            <div id="flow-table-container"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-12 col-md-12 dashboard_starting-order">
                <div class="card">
                    <div class="card-body">
                        <div class="row card-flow">
                            <div class="col-md-6 card-flow-left">
                                <h3 class="card-title">Orden de Salida</h3>
                            </div>
                        </div>

                        </br>


                        <div class="table-responsive">
                            <table class="table align-middle" id="idordensalida">
                                <thead>
                                <tr>
                                    <th>Responsable Salida</th>
                                    <th>Fecha de traspaso</th>
                                    <th>Sucursal de Destino</th>
                                    <th>Detalle</th>
                                </tr>
                                </thead>
                                <tbody>
                                {% for ordenSalida in orden %}
                                    {% if ordenSalida.Aceptada == '2' %}
                                        <tr class="text-center">
                                            <td>{{ ordenSalida.responsableSalida }}</td>
                                            <td>{{ ordenSalida.creacion|date('Y-m-d H:i') }}</td>
                                            <td>{{ ordenSalida.sucursalDestino }}</td>

                                            <td class="content-buttom-detail">
                                                <a class="content-buttom-detail__a"
                                                   href="/admin/app/ordensalida/{{ ordenSalida.IdOrdensalida }}/detalleOrdenSalida">
                                                    Detalle
                                                    <i class="fa-solid fa-circle-info fa-lg"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    {% endif %}
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-12 col-md-12 dashboard_starting-order">
                <div class="card">
                    <div class="card-body">
                        <div class="row card-flow">
                            <div class="col-md-6 card-flow-left">
                                <h3 class="card-title">Envíos que llegarán</h3>
                            </div>
                        </div>

                        </br>


                        <div id="arriving-shipments-table-container">

                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--Modal Iniciar Flujo-->
    <div class="mod" id="modal-iniciar-flujo" aria-hidden="true" tabindex="-1">
        <div class="mod-dialog mod-lg mod-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modal-iniciar-flujoLabel">Seleccionar Paciente</h5>
                    <div class="mod-footer text-center padre-buttom-2">
                        <button type="button" class="" data-bs-dismiss="mod"><i class="fa-solid fa-circle-xmark fa-xl"
                                                                                style="color: #ff0000;"></i></button>
                    </div>
                </div>
                <div class="mod-body text-center">
                    <div class="container">
                        <div class="col-12 dashboard-user-center">
                            <div class="row dashboard-subline-title">
                                <div class="col-12 dashboard-user-title-left">
                                    <h3 class="card-title">Cliente <span class="text-danger">(obligatorio)</span></h3>
                                </div>
                                <div class="col-6  ">
                                    <div class="form-group">
                                        <label for="permisosempresas" class="subtext">Optometrista que atenderá al
                                            paciente:</label>
                                        <select id="opciones" class="form-control" name="">
                                            {% for user in activeUsers %}
                                                <option value="{{ user.idusuario }}">{{ user.nombre }} {{ user.apellidopaterno }}</option>
                                            {% endfor %}
                                        </select><br>
                                        <smal>Optometrista al que le aparecerá el flujo en su Dashboard</smal>
                                    </div>

                                </div>
                                <div class="col-6  ">
                                    <button onclick="obtenerFormularioAgregarCliente()" type="button"
                                            class=" dashboard-modal__flow dashboard__btn-modal__new"
                                            data-bs-toggle="mod"
                                            data-bs-target="#NuevoClienteModal">Nuevo Cliente
                                    </button>

                                    <button type="button"
                                            class=" dashboard__btn-modal__search btn-buscar-cliente dashboard-modal__flow"
                                            data-bs-toggle="mod" data-bs-target="#modalBuscarCliente"> Buscar Cliente
                                    </button>

                                </div>
                            </div>

                            <div class="form-group">
                                <div class="row">
                                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                        <label class="subtext">Apellido paterno:</label>
                                        <input id="apellidoP" type="text" class="form-control reset registro" disabled>
                                    </div>
                                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                        <label class="subtext">Apellido materno:</label>
                                        <input id="apellidoM" type="text" class="form-control reset registro" disabled>
                                    </div>
                                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                        <label class="subtext">Nombre(s):</label>
                                        <input id="nombre" type="text" class="form-control reset registro" disabled>

                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                        <label for="BuscarEmail" class="subtext">Correo electrónico:</label>
                                        <input id="sin-convenio-email-cliente" class="form-control" type="text"
                                               disabled>
                                    </div>
                                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                        <label for="BuscarTelefono" class="subtext">Teléfono:</label>
                                        <input id="cliente-telefono" class="form-control" type="text" disabled>
                                    </div>
                                    <div class="col-12 col-sm-12 col-md-4 col-lg-4 d-none">
                                        <label for="BuscarID" class="subtext">ID:</label>
                                        <input id="cliente-id" class="form-control" type="text" disabled>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row justify-content-center">
                            <div class="col-md-12">
                                <button id="siguiente-cliente" type="button" class="btn btn-info" onclick="siguiente()">
                                    Siguiente
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--Modal Nuevo Cliente-->
    <div class="mod" id="NuevoClienteModal" aria-hidden="true" tabindex="-1" style="z-index: 99999;">
        <div class="mod-dialog mod-lg mod-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Creación de Nuevo Cliente</h5>
                    <div class="mod-footer text-center padre-buttom-2">
                        <button type="button" class="" data-bs-dismiss="mod" aria-label="Close"><i
                                    class="fa-solid fa-circle-xmark fa-xl" style="color: #ff0000;"></i></button>
                    </div>
                </div>
                <div class="modal-body text-center">
                    <div class="container">
                        <div class="col-8 dashboard-user-center">
                            <div class="row dashboard-subline-title">
                                <div class="col-6 col-sm-6 col-md-6 col-xl-6 dashboard-user-title-left">
                                    <h3 class="card-title">Cliente</h3>
                                </div>
                                <div class="col-6 col-sm-6 col-md-6 col-xl-6 dashboard-user-title-rigth">

                                </div>
                            </div>
                            <div id="create-user-form-container"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal "Buscar Cliente" -->
    <div class="mod" id="modalBuscarCliente" tabindex="-1" aria-hidden="true" style="z-index: 99999;">
        <div class="mod-dialog mod-lg mod-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Buscar Cliente</h5>
                    <div class="mod-footer text-center padre-buttom-2">
                        <button type="button" class="" data-bs-dismiss="mod" aria-label="Cerrar"><i
                                    class="fa-solid fa-circle-xmark fa-xl" style="color: #ff0000;"></i></button>
                    </div>
                </div>

                <div class="modal-body text-center">
                    <div class="container">
                        <div class="col-8 dashboard-user-center">
                            <div class="row dashboard-subline-title">
                                <div class="col-6 col-sm-6 col-md-6 col-xl-6 dashboard-user-title-left">
                                    <h3 class="card-title">Cliente</h3>
                                </div>
                                <div class="col-6 col-sm-6 col-md-6 col-xl-6 dashboard-user-title-rigth">
                                    <button id="" type="button" class="dashboard-cta__flow-user"
                                            onclick="BuscarCliente()">
                                        Buscar
                                    </button>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="row">
                                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                        <label for="BuscarApellidoP" class="subtext">Apellido paterno:</label>
                                        <input id="BuscarApellidoP" class="form-control" type="text"/>
                                    </div>
                                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                        <label for="BuscarApellidoM" class="subtext">Apellido materno:</label>
                                        <input id="BuscarApellidoM" class="form-control" type="text"/>
                                    </div>
                                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                        <label for="BuscarNombre" class="subtext">Nombre(s):</label>
                                        <input id="BuscarNombre" class="form-control" type="text"/>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                        <label for="BuscarEmail" class="subtext">Correo electrónico:</label>
                                        <input id="BuscarEmail" class="form-control" type="text"/>
                                    </div>
                                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                        <label for="BuscarTelefono" class="subtext">Teléfono:</label>
                                        <input id="BuscarTelefono" class="form-control" type="text"/>
                                    </div>
                                    <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                        <label for="BuscarNumeroEmpleado" class="subtext">Número de empleado:</label>
                                        <input id="BuscarNumeroEmpleado" class="form-control" type="text"/>
                                    </div>
                                </div>
                                <br>
                            </div>
                            <div class="row dashboard-subline-title">
                                <div class="col-6 col-sm-6 col-md-6 col-xl-6 dashboard-user-title-left">
                                    <h3 class="card-title">Búsqueda</h3>
                                </div>
                                <div class="col-6 col-sm-6 col-md-6 col-xl-6 dashboard-user-title-rigth">
                                    <!--<button type="button" class="btn btn-secondary" data-bs-dismiss="mod">Cerrar</button>-->
                                </div>
                            </div>
                            <div class="row justify-content-md-center">
                                <div class="col-12">
                                    <div class="table-responsive" style="height:350px">
                                        <table class="table" id="search-client-table">
                                            <thead>
                                            <tr class="text-start">
                                                <th class="text-start" scope="col">#</th>
                                                <th class="text-start" scope="col">Apellido paterno</th>
                                                <th class="text-start" scope="col">Apellido materno</th>
                                                <th class="text-start" scope="col">Nombre</th>
                                                <th class="text-start" scope="col">ID</th>
                                                <th class="text-start" scope="col">Correo</th>
                                                <th class="text-start" scope="col">Teléfono</th>
                                                <th class="text-start" scope="col">Número de empleado</th>
                                                <th></th>
                                            </tr>
                                            </thead>
                                            <tbody id="tableBodyBuscarCliente"></tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mod fade " id="shipment-detail-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content" style="border-radius:10px">
                <div class="modal-header bg-primary">
                    <h1 class="modal-title fs-5" id="shipment-detail-title"></h1>
                    <button type="button" class="close btn-close-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">

                    <div id="shipment-detail-modal-body"></div>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>


{% endblock %}
    {% block javascripts %}
        <script type="text/javascript">

        </script>
        {{ parent() }}

        <script src="{{ asset('js/jquery.formatCurrency-1.4.0.pack.js') }}"></script>
        <script src="{{ asset('lib/jQuery-Autocomplete-master/dist/jquery.autocomplete.min.js') }}"></script>
        <script type="text/javascript"
                src="https://cdn.jsdelivr.net/npm/html2canvas@1.0.0-rc.1/dist/html2canvas.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.debug.js"
                integrity="sha384-NaWTHo/8YCBYJ59830LTz/P4aQZK1sS0SneOgAvhsIl3zBu8r9RevNg5lHCHAuQ/"
                crossorigin="anonymous"></script>
        <script src="{{ asset('js/format-input-field.js') }}"></script>
        <script src="{{ asset('js/iniciar-formato-campos.js') }}"></script>
        <script src="{{ asset('js/nueva-venta.js') }}?v=4"></script>
        <script src="{{ asset('lib/dataTables/dataTables.min.js') }}"></script>
        <link rel="stylesheet" href="{{ asset('lib/dataTables/dataTables.min.css') }}">
        <script type="text/javascript">


            function BuscarCliente() {

                nombre = $("#BuscarNombre").val();
                id = $("#BuscarID").val();
                apellidop = $("#BuscarApellidoP").val();
                apellidom = $("#BuscarApellidoM").val();
                email = $("#BuscarEmail").val();
                telefono = $("#BuscarTelefono").val();
                numeroempleado = $("#BuscarNumeroEmpleado").val();
                console.log(nombre);

                var urlBuscarCliente = $("#url-buscar-cliente").val();

                const prevHtml = $("#search-client-table").html()

                $.ajax({
                    url: urlBuscarCliente,
                    data: {
                        email: email, telefono: telefono, nombre: nombre, id: id,
                        apellidop: apellidop, apellidom: apellidom,
                        numeroempleado: numeroempleado
                    },
                    beforeSend: loadingGif("search-client-table"),
                }).done(function (suggestions) {

                    suggestions = Object.values(suggestions);
                    $("#search-client-table").html(prevHtml)
                    $("#tableBodyBuscarCliente").html("");

                    for (var i = 0; i < suggestions[0].length; i++) {

                        var newRow = document.createElement("tr");
                        var newCell = document.createElement("th");
                        newCell.setAttribute('scope', 'row');
                        newCell.textContent = i + 1;
                        newRow.appendChild(newCell);
                        var newCell = document.createElement("td");
                        newCell.setAttribute('id', 'apellidoP_' + suggestions[0][i].data);
                        newCell.textContent = suggestions[0][i].apellidopaterno;
                        newRow.appendChild(newCell);
                        var newCell = document.createElement("td");
                        newCell.setAttribute('id', 'apellidoM_' + suggestions[0][i].data);
                        newCell.textContent = suggestions[0][i].apellidomaterno;
                        newRow.appendChild(newCell);
                        var newCell = document.createElement("td");
                        newCell.setAttribute('id', 'nombre_' + suggestions[0][i].data);
                        newCell.textContent = suggestions[0][i].value;
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.setAttribute('id', 'id_' + suggestions[0][i].data);
                        newCell.textContent = suggestions[0][i].data;
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.setAttribute('id', 'email_' + suggestions[0][i].data);
                        newCell.textContent = suggestions[0][i].email;
                        newRow.appendChild(newCell);
                        var newCell = document.createElement("td");
                        newCell.setAttribute('id', 'telefono_' + suggestions[0][i].data);
                        newCell.textContent = suggestions[0][i].telefono;
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.setAttribute('id', 'numeroEmpleado_' + suggestions[0][i].data);
                        //newCell.setAttribute('class', 'd-none');
                        newCell.textContent = suggestions[0][i].numeroEmpleado;

                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        var newButton = document.createElement("button");
                        newButton.setAttribute('id', suggestions[0][i].data);

                        newButton.setAttribute('class', 'btn btn-info');
                        newButton.setAttribute('type', 'button');
                        newButton.setAttribute('onclick', 'seleccionarClienteDatos(this.id)');
                        newButton.setAttribute('data-bs-dismiss', "mod");

                        newButton.textContent = 'Seleccionar';

                        newCell.appendChild(newButton);
                        ;
                        newRow.appendChild(newCell);

                        $("#tableBodyBuscarCliente").append(newRow)
                    }
                }).fail(function () {
                    alert("error");
                });
            }


            function seleccionarClienteDatos(id) {


                var telefono = $("#telefono_" + id).text();
                var email = $("#email_" + id).text();
                var apellidoP = $("#apellidoP_" + id).text();
                var apellidoM = $("#apellidoM_" + id).text();
                var nombre = $("#nombre_" + id).text();
                var idCliente = $("#id_" + id).text();
                console.log(idCliente);
                var numeroEmpleado = $("#numeroEmpleado_" + id).text();

                $('#cliente-telefono').val(telefono);
                $('#sin-convenio-email-cliente').val(email);
                $('#apellidoP').val(apellidoP);
                $('#apellidoM').val(apellidoM);
                $('#nombre').val(nombre);
                $('#numero-empleado').val(numeroEmpleado);
                $("#cliente-id").val(id);

            }

            function obtenerSucursales() {
                var idempresa = $("#idempresa").val();

                $.ajax({
                    url: "{{ path('almacen-obtener-sucursal') }}",

                    data: {idempresa: idempresa},
                    dataType: "html"
                }).done(function (html) {
                    $("#sucursales").html(html);

                }).fail(function () {
                    alert("error");
                });
            }


            $(document).ready(function () {

                /*$('#shipment-detail-modal').on('hidden.bs.modal', function () {
                  getArrivingShipmentsTable()
                })*/

                flowTable();
                getArrivingShipmentsTable()

                var urlSeleccionarClientes = $("#url-seleccionar-clientes").val();
                var urlSeleccionarEmail = $("#url-seleccionar-email").val();
                var urlSeleccionarTelefono = $("#url-seleccionar-telefono").val();
                var urlBuscarCliente = $("#url-buscar-cliente").val();
                var urlBuscarNumeroCliente = $("#url-buscar-numero-cliente").val();

                /*para arreglar el problema de que no se ven las opciones en el select 2*/
                $("#opciones").select2({
                    dropdownParent: $("#modal-iniciar-flujo .mod-body")
                });

            });

            function obtenerFormularioAgregarCliente() {

                $.ajax({
                    url: "{{ path('formularioCliente') }}",
                    type: 'GET',
                    beforeSend: loadingGif("create-user-form-container"),
                    dataType: "html"
                }).done(function (html) {
                        $("#create-user-form-container").html(html);

                    }
                );
            }

            function flowTable(userId = -1) {


                if (userId != -1) {
                    userId = $("#active-users-select").val();
                }

                $.ajax({
                    url: "{{ path('dashboardFlujoExpediente-flow-table') }}",
                    data: {userId: userId},
                    beforeSend: loadingGif("flow-table-container"),
                    dataType: "html"
                }).done(function (html) {
                        $("#flow-table-container").html(html);

                    }
                );
            }

            function getArrivingShipmentsTable() {

                $.ajax({
                    url: "{{ path('dashboard-get-arriving-shipments-table') }}",
                    beforeSend: loadingGif("arriving-shipments-table-container"),
                    dataType: "html"
                }).done(function (html) {
                    $("#arriving-shipments-table-container").html(html);

                }).fail(function () {
                    alert("error");
                });
            }

            function changeModalTitle(trackingNumber) {
                $("#shipment-detail-title").text("Detalle de envio " + trackingNumber);
            }

            function showShipmentDetail(shipmentId) {

                $.ajax({
                    url: "{{ path('dashboard-show-shipment-detail') }}",
                    data: {shipmentId: shipmentId},
                    beforeSend: loadingGif("shipment-detail-modal-body"),
                    dataType: "html"
                }).done(function (html) {
                    $("#shipment-detail-modal-body").html(html);

                }).fail(function () {
                    alert("error");
                });

            }

            function processOrders(shipmentId, accept = 0) {

                let commentaries = $("#shipment-commentaries-textarea").val()
                let alertMsg = (accept == 1) ? "Se marcará como aceptado el envío" : "Se marcará como rechazado el envío"

                checkboxesOrder = document.getElementsByName('order');
                orders = [];
                for (var i = 0, n = checkboxesOrder.length; i < n; i++) {

                    if (checkboxesOrder[i].checked) {
                        orders.push(checkboxesOrder[i].value);
                    }

                }

                if (orders.length > 0) {

                    if (accept == 0 && !commentaries) {
                        Swal.fire({
                            title: "Necesitas agregar comentarios",
                            text: "Para rechazar un producto necesitas indicar el porqué",
                            type: "warning"
                        });
                    } else {
                        Swal.fire({
                            title: '¿Está seguro?',
                            text: alertMsg,
                            type: 'question',
                            showCancelButton: true,
                            confirmButtonColor: '#28B463',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'Aceptar',
                            cancelButtonText: 'Cancelar'
                        }).then((result) => {
                            if (result.value) {
                                $.ajax({
                                    url: "{{ path('shipment-process-shipment') }}",
                                    type: 'POST',
                                    data: {
                                        orders: orders,
                                        accept: accept,
                                        commentaries: commentaries,
                                        fromLocation: 1
                                    },
                                    dataType: "json"
                                }).done(function (response) {
                                    if (response.success) {
                                        showShipmentDetail(shipmentId)
                                        getArrivingShipmentsTable()
                                    } else {
                                        Swal.fire({
                                            title: "Hubo un problema",
                                            text: response.msg,
                                            type: "warning"
                                        });
                                    }
                                }).fail(function () {
                                    alert("error");
                                });
                            }
                        })
                    }
                } else {
                    Swal.fire({
                        title: "Hubo un problema",
                        text: "Debes seleccionar al menos un envío",
                        type: "warning"
                    });
                }

            }

        </script>


        <script>

            var urlCreateFlujoExpediente = "{{ path('create_flujo_expediente') }}";


            function siguiente() {
                // Obtener el ID del cliente seleccionado
                var clienteId = document.getElementById("cliente-id").value;
                console.log("Se ha presionado el botón 'Siguiente'. ID del cliente:", clienteId);

                if (clienteId.length > 0) {
                    changeButton("siguiente-cliente", 0, 1)

                    var userId = $("#opciones").val();

                    // Realizar una solicitud AJAX para crear el nuevo registro en FlujoExpediente
                    $.ajax({
                        url: "{{ path('create_flujo_expediente') }}", // Reemplaza por la URL correcta
                        type: "POST",
                        data: {clienteId: clienteId, userId: userId},
                        success: function (response) {
                            location.reload();
                            console.log("Registro creado correctamente1");
                            changeButton("siguiente-cliente", 1, 1)
                        },
                        error: function (error) {
                            console.error("Error al crear el registro:", error);
                            changeButton("siguiente-cliente", 1, 1)
                        }
                    });
                } else {
                    alert("Debe registrar un cliente primero");
                }

            }

            function eraseFlow(idclinicalrecordflow = -1) {

                url = $("#url-dashboardFlujoExpediente-erase-flow").val();

                Swal.fire({
                    title: '¿Está seguro?',
                    text: "Se eliminará el flujo de expediente",
                    type: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#28B463',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Aceptar',
                    cancelButtonText: 'Cancelar'
                }).then((result) => {
                    if (result.value) {
                        $.ajax({
                            url: url,
                            method: "POST",
                            data: {idclinicalrecordflow: idclinicalrecordflow},
                            success: function (response) {
                                flowTable(1);
                            }
                        });
                    }
                })
            }

            $(document).ready(function () {
                $(".btn-warning").click(function () {
                    var flujoExpedienteId = $(this).data("id");
                    var url = "{{ path('cambiar_status', {'id': 'PLACEHOLDER'}) }}".replace('PLACEHOLDER', flujoExpedienteId);
                    Swal.fire({
                        title: 'Estas segur@ que deseas eliminar el flujo?',
                        showDenyButton: true,
                        type: 'question',
                        showCancelButton: true,
                        confirmButtonText: 'Si',
                        denyButtonText: `No`,
                    }).then((result) => {
                        console.log(result.value);
                        if (result.value) {
                            $.ajax({
                                url: url,
                                method: "POST",
                                success: function (response) {
                                    const Toast = Swal.mixin({
                                        toast: true,
                                        position: 'top-end',
                                        showConfirmButton: false,
                                        timer: 2000,
                                        width: '800px',
                                        height: '800px',
                                        timerProgressBar: true,
                                        didOpen: (toast) => {
                                            toast.addEventListener('mouseenter', Swal.stopTimer)
                                            toast.addEventListener('mouseleave', Swal.resumeTimer)
                                        }
                                    })
                                    $("#row-" + flujoExpedienteId).remove();
                                    Toast.fire({
                                        type: 'success',
                                        title: 'Borrado Correctamente'
                                    })
                                },
                                error: function (error) {
                                    console.error("Error al cambiar el estado:", error);
                                }
                            });
                        }
                    });
                });
            });
        </script>

    {% endblock %}
