{% if laboratoryOrders | length > 0 %}
<div class=" mt-3 pb-4">
    <div class="table-responsive-sm mb-4">
        <table class="table mb-0">
            <thead>
                <th scope="col">
                    <input type="checkbox" name="allOrders" onClick="toggleOrders(this)" />
                </th>
                <th scope="col"><PERSON><PERSON></th>
                <th scope="col">Marca</th>
                <th scope="col">Modelo</th>
                <th scope="col">SKU</th>
            </thead>
            <tbody>
                {% for laboratoryOrder in laboratoryOrders %}
                    <tr>
                        <th class="text-center" scope="row">
                            <input type="checkbox" name="order" value="{{laboratoryOrder.idshipmentordenlaboratorio}}"/>
                        </th>
                        <td class="text-center">{{laboratoryOrder.locationName}}</td>
                        <td class="text-center">{{laboratoryOrder.brand}}</td>
                        <td class="text-center">{{laboratoryOrder.modelo}}</td>
                        <td class="text-center">
                            {% if laboratoryOrder.codigobarras != null  %}
                                {{laboratoryOrder.codigobarras}}
                            {% else %}
                                {{laboratoryOrder.codigobarrasuniversal}}
                            {% endif %}
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
        
    </div>
    <div class="form-floating">
        <textarea class="form-control" id="shipment-commentaries-textarea" style="height: 100px"></textarea>
        <label for="shipment-commentaries-textarea">Comentarios</label>
    </div>
    <div class="d-flex justify-content-center mt-3">
        <button class="btn btn-success m-5" onclick="processOrders('{{shipmentId}}',1)">Aceptar productos</button>
        <button class="btn btn-danger m-5" onclick="processOrders('{{shipmentId}}')">Rechazar productos</button>
    </div>
    
</div>
{% else %}
    <h4 class="text-center mt-3 mb-3">No hay envíos pendientes</h4>
{% endif %}


<script>
    function toggleOrders(source) {
        checkboxes = document.getElementsByName('order');
        for(var i=0, n=checkboxes.length;i<n;i++) {
            checkboxes[i].checked = source.checked;
        }
    }

</script>