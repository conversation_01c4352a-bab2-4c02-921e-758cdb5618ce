{% block formulario %}
    {{ form_start(form, {attr: {'id': 'formularioCliente', 'enctype': 'multipart/form-data', 'class':"row"}}) }}

    {{ form_end(form) }}

    {% if idcliente != null %}
        <script>
            console.log("{{ idcliente }}")

            // Actualizar los campos con los datos del cliente
            $("#cliente-id").val("{{ idcliente }}");
            $("#nombre").val("{{ cliente.nombre }}");
            $("#apellidoP").val("{{ cliente.apellidopaterno }}");
            $("#apellidoM").val("{{ cliente.apellidomaterno }}");
            $("#cliente-telefono").val("{{ cliente.telefono }}");
            $("#sin-convenio-email-cliente").val("{{ cliente.email }}");
            $("#cliente-numero-empleado").val("{{ cliente.numeroempleado }}");

            // Ocultar el modal "NuevoClienteModal" después de la carga
            $("#NuevoClienteModal").modal('hide');

            // Enviar el formulario de cliente usando AJAX;
        </script>
    {% endif %}
    <script>

    $("#agregar_beneficiario_holder").select2({
        dropdownParent: $('#NuevoClienteModal')
    });
    
        $("#formularioCliente").submit(function(e) {
            e.preventDefault();
            var formData = new FormData(this);


            $.ajax({
                url: "{{ path('formularioCliente') }}",
                type: 'POST',
                beforeSend: function() {
                    loadingGif("create-user-form-container"); // Llamada al gif de carga antes de enviar
                },
                data: formData,
                success: function (html) {
                    $("#create-user-form-container").html(html);
                },
                cache: false,
                contentType: false,
                processData: false
            });
        })
    </script>

{% endblock %}

