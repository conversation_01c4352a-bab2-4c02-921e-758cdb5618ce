<button type="button" class="btn btn-sm btn-info" onclick="descargardocumento({{ object.stockventaIdstockventa.ventaIdventa }});">Descargar Documentos</button>


<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>

function descargardocumento(idVenta) {
    $.ajax({
        url: 'obtener_documento_uam' + idVenta,
        type: 'GET',
        success: function(data) {
            var blob = new Blob([data]);
            var link = document.createElement('a');
            link.href = window.URL.createObjectURL(blob);
            link.download = "nombreDelDocumentoDescargado.ext"; // Cambia .ext por la extensión de tu documento
            link.click();
        },
        error: function(error) {
            console.error('Hubo un error al descargar el documento:', error);
        }
    });
}


</script>