{% extends 'admin/layout.html.twig' %}
{% block titleHead %}
{% endblock %}
{% block title %}
    Dashboard | Vendedor - Optometrista
{% endblock %}
{% block content %}
    <link rel="stylesheet" href="{{ asset('/lib/jQuery-Autocomplete-master/content/styles.css') }}"/>
    <link rel="stylesheet" href="{{ asset('/css/puntodeventa/dashboard_vendedor_optometrista.css') }}"/>
    <link rel="stylesheet" href="{{ asset('lib/dataTables/dataTables.min.css') }}"/>
    <link rel="preconnect" href="https://fonts.googleapis.com"/>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin/>
    <link href="https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap"
          rel="stylesheet"/>
    <input id="url-app_abrir_visor" type="hidden" value="{{ path('app_abrir_visor') }}">
    <input id="url-app_abrir_visor_anuncios" type="hidden" value="{{ path('app_abrir_visor_anuncios') }}">
    <input id="url-buscar-cliente" type="hidden" value="{{ path('buscar-cliente') }}">

    <style>
        .table-inventario {
            color: white;
        }

        .table-inventario thead th {
            border-color: white;
        }

        .table-inventario tbody td {
            border-color: white;
        }

        .table-inventario td,
        .table-inventario th {
            color: white;
        }

        .table-inventario td {
            vertical-align: top;
        }
    </style>

    <input id="url-dashboardFlujoExpediente-erase-flow" type="hidden"
           value="{{ path('dashboardFlujoExpediente-erase-flow') }}"/>



    <div class="container mt-5">
        <div class="d-flex justify-content-end mb-4">


            <a class="btn btn-lg btn-primary shadow-lg px-5 py-3 mx-3"
               style="font-size: 1.5rem; border-radius: 10px;" data-toggle="modal"
               data-target="#promocionesModal">
                PROMOCIONES
            </a>

            <a class="btn btn-lg btn-primary shadow-lg px-5 py-3 mx-3 position-relative"
               style="font-size: 1.5rem; border-radius: 10px;" data-toggle="modal"
               data-target="#anunciosModal">
                ANUNCIOS
                <i class="fa fa-bell" aria-hidden="true"
                   style="font-size: 1.5rem; margin-left: 10px; position: relative;"></i>

                {% set notificacionesHoy = 0 %}
                {% set fechaActual = "now"|date('Y-m-d') %}
                {% for notificacion in notificaciones %}
                    {% if notificacion.Fecha|date('Y-m-d') == fechaActual %}
                        {% set notificacionesHoy = notificacionesHoy + 1 %}
                    {% endif %}
                {% endfor %}

                {% if notificacionesHoy > 0 %}
                    <span class="position-absolute badge rounded-pill bg-danger"
                          style="top: -10px; color: white; right: -10px; font-size: 1rem; padding: 8px 12px;">
            {{ notificacionesHoy }}
        </span>
                {% endif %}
            </a>
            <div class="d-flex justify-content-end align-items-end shadow-lg px-5 py-3 mx-3"
                 style="font-size: 1.5rem; margin-left: 10px; position: relative font-weight-bold;">

                <span class="ml-2 text-white"> {{ app.user.sucursalIdsucursal.nombre }}</span>
            </div>

            <a class="btn btn-lg btn-primary shadow-lg px-5 py-3 mx-3"
               style="font-size: 1.5rem; border-radius: 10px;"
               data-toggle="modal"
               data-target="#modalAgregarMerma">
                GARANTÍA
            </a>
            </a>
        </div>
        <div class="row g-3 ">
            <div class="col-sm-12 col-lg-4 row">
                <!-- Tarjeta de Flujos -->
                <div class="col-12 dashboard-card">
                    <div class="card dashboard h-100">
                        <div class="card-header dashboard d-flex justify-content-center align-items-center">
                            <h3 class="dashboard-text mt-0 mb-0">FLUJOS</h3>
                        </div>
                        <div class="card-body dashboard-body">
                            <p class="card-text">{{ pendientes }} PENDIENTES</p>
                            <p class="card-text">{{ finalizados }} REALIZADOS</p>
                        </div>
                        <div class="card-footer dashboard">
                            <a type="button" class="btn btn-detail-success" data-dismiss="modal" data-toggle="modal"
                               data-target="#IniciarFlujo">INICIAR FLUJO</a>
                            <a type="button" class="btn btn-detail" data-toggle="modal"
                               data-target="#flujosModal">DETALLES</a>
                        </div>
                    </div>
                </div>

                <div class="modal" id="IniciarFlujo" tabindex="-1" aria-labelledby="" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered modal-lg">
                        <div class="modal-content" style="border-radius: 10px;">
                            <div class="modal-header bg-primary-soft">
                                <h1 class="dashboard-text" id="lens-config-title">INICIAR FLUJO</h1>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body text-center">
                                <div class="row justify-content-center">
                                    <div class="col-12 dashboard-user-center">
                                        <div class="row dashboard-subline-title">
                                            <h3 class="card-title">Cliente <span
                                                        class="text-danger">(obligatorio)</span></h3>
                                        </div>
                                        <div class="p-3 w-100">
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="form-group">
                                                        <label for="opciones">
                                                            Optometrista que atenderá al paciente:
                                                        </label>
                                                        <select id="opciones" class="form-control w-50">
                                                            {% for user in activeUsers %}
                                                                <option value="{{ user.idusuario }}">{{ user.nombre }} {{ user.apellidopaterno }}</option>
                                                            {% endfor %}
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label class="subtext">Nombre(s):</label>
                                                    <input id="nombre" type="text" class="form-control reset registro"
                                                           disabled>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="subtext">Apellido paterno:</label>
                                                    <input id="apellidoP" type="text"
                                                           class="form-control reset registro" disabled>
                                                </div>
                                            </div>

                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label class="subtext">Apellido materno:</label>
                                                    <input id="apellidoM" type="text"
                                                           class="form-control reset registro" disabled>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="BuscarEmail" class="subtext">Correo electrónico:</label>
                                                    <input id="sin-convenio-email-cliente" class="form-control"
                                                           type="text" disabled>
                                                </div>
                                            </div>

                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label for="BuscarTelefono" class="subtext">Teléfono:</label>
                                                    <input id="cliente-telefono" class="form-control" type="text"
                                                           disabled>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="buscarNumeroEmpleado" class="subtext">Número de
                                                        Empleado:</label>
                                                    <input id="cliente-numero-empleado" class="form-control" type="text"
                                                           disabled>
                                                </div>
                                            </div>
                                            <label for="BuscarID" class="subtext" hidden>ID:</label>
                                            <input id="cliente-id" class="form-control" type="hidden" disabled>
                                        </div>

                                        <div class="col-12 mt-4">
                                            <div class="row">
                                                <div class="col-md-4 text-left">
                                                    <button class="btn btn-primary" type="button" id="nuevoCliente"
                                                            onclick="obtenerFormularioAgregarCliente()"
                                                            data-toggle="modal"
                                                            data-target="#NuevoClienteModal">Nuevo Cliente
                                                    </button>
                                                </div>
                                                <div class="col-md-4 text-center">
                                                    <button id="siguiente-cliente" type="button" class="btn btn-success"
                                                            onclick="siguiente()">Siguiente
                                                    </button>
                                                </div>
                                                <div class="col-md-4 text-right">
                                                    <button class="btn btn-warning" type="button" id="buscar-cliente"
                                                            data-toggle="modal"
                                                            data-target="#modalBuscarCliente">Buscar Cliente
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--Modal Nuevo Cliente -->
                <div class="modal" id="NuevoClienteModal" aria-hidden="true" tabindex="-1">
                    <div class="modal-dialog modal-dialog-centered modal-lg" style="position: relative; z-index: 1000;">
                        <div class="modal-content" style="border-radius: 10px;">
                            <div class="modal-header">
                                <h1 class="dashboard-text" id="lens-config-title">CREACIÓN DE NUEVO CLIENTE</h1>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="row justify-content-center">
                                    <div class="col-12">
                                        <div class="row">

                                            <div id="create-user-form-container">
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Modal Buscar Clientes -->

                <div class="modal" id="modalBuscarCliente" tabindex="-1" aria-labelledby="" aria-hidden="true"
                     style="z-index: 9999;">
                    <div class="modal-dialog modal-dialog-centered modal-lg">
                        <div class="modal-content" style="border-radius: 10px;">
                            <div class="modal-header bg-primary-soft">
                                <h1 class="dashboard-text" id="lens-config-title">BUSCAR CLIENTE</h1>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body text-center">
                                <div class="container">
                                    <div class="col-8 dashboard-user-center">
                                        <div class="row dashboard-subline-title">
                                            <div class="col-6 col-sm-6 col-md-6 col-xl-6 dashboard-user-title-left">
                                                <h3 class="card-title">Cliente</h3>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <div class="row">
                                                <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                                    <label for="BuscarApellidoP" class="subtext">Apellido
                                                        paterno:</label>
                                                    <input id="BuscarApellidoP" class="form-control" type="text"/>
                                                </div>
                                                <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                                    <label for="BuscarApellidoM" class="subtext">Apellido
                                                        materno:</label>
                                                    <input id="BuscarApellidoM" class="form-control" type="text"/>
                                                </div>
                                                <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                                    <label for="BuscarNombre" class="subtext">Nombre(s):</label>
                                                    <input id="BuscarNombre" class="form-control" type="text"/>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                                    <label for="BuscarEmail" class="subtext">Correo electrónico:</label>
                                                    <input id="BuscarEmail" class="form-control" type="text"/>
                                                </div>
                                                <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                                    <label for="BuscarTelefono" class="subtext">Teléfono:</label>
                                                    <input id="BuscarTelefono" class="form-control" type="text"/>
                                                </div>
                                                <div class="col-12 col-sm-12 col-md-4 col-lg-4">
                                                    <label for="BuscarNumeroEmpleado" class="subtext">Número de
                                                        empleado:</label>
                                                    <input id="BuscarNumeroEmpleado" class="form-control" type="text"/>
                                                </div>
                                            </div>
                                            <br>
                                        </div>
                                        <div class="row dashboard-subline-title">
                                            <div class="col-12 col-sm-12 col-md-12 col-xl-12 align-items-center">
                                                <button id="" type="button" class="btn btn-primary"
                                                        onclick="BuscarCliente()">
                                                    Buscar
                                                </button>
                                            </div>
                                        </div>
                                        <div class="row dashboard-subline-title">
                                            <div class="col-12 col-sm-12 col-md-12 col-xl-12 align-items-center">
                                                <h3 class="card-title text-center">Búsqueda</h3>
                                            </div>
                                        </div>
                                        <div class="row justify-content-md-center">
                                            <div class="col-12">
                                                <div class="table-responsive" style="height:350px">
                                                    <table class="table" id="search-client-table">
                                                        <thead>
                                                        <tr class="text-start">
                                                            <th class="text-start" scope="col">#</th>
                                                            <th class="text-start" scope="col">Apellido paterno</th>
                                                            <th class="text-start" scope="col">Apellido materno</th>
                                                            <th class="text-start" scope="col">Nombre</th>
                                                            <th class="text-start" scope="col">Correo</th>
                                                            <th class="text-start" scope="col">Teléfono</th>
                                                            <th class="text-start" scope="col">Número de empleado</th>
                                                            <th></th>
                                                        </tr>
                                                        </thead>
                                                        <tbody id="tableBodyBuscarCliente"></tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tarjeta de Traspasos Pendientes -->
                <div class="col-12 dashboard-card">
                    <div class="card dashboard h-100">
                        <div class="card-header dashboard-two">TRASPASOS PENDIENTES</div>
                        <div class="card-body dashboard-body-trasp p-0 m-0">
                            <table class="table table-bordered trasp m-0">
                                <thead>
                                <tr>
                                    <th class="table-traspasos">SUCURSAL DE ORIGEN</th>
                                    <th class="table-traspasos">FECHA DE ENVÍO</th>
                                </tr>
                                </thead>
                                <tbody>
                                {% for traspaso in traspasoDashboard %}
                                    <tr>
                                        <th scope="row" class="table-traspasos">{{ traspaso.nombre }}</th>
                                        <td class="table-traspasos">{{ traspaso.fechasalida|date('d/m/Y') }}</td>
                                    </tr>
                                {% else %}
                                    <tr>
                                        <td colspan="2" class="table-traspasos text-center">No hay traspasos
                                            pendientes
                                        </td>
                                    </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div class="card-footer dashboard">
                            <a href="#" class="btn btn-detail mb-4 mt-4" data-toggle="modal"
                               data-target="#traspasosModal">DETALLES</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-8 row col-sm-12">


                <!-- Tarjeta de Ordenes -->
                <div class="col-12 dashboard-card">
                    <div class="card dashboard h-100">
                        <div class="card-header dashboard d-flex justify-content-between align-items-center">
                            <h3 class="dashboard-text ms-5">ORDENES</h3>
                            <a type="button" class="btn btn-detail me-5" data-toggle="modal"
                               data-target="#detallesModal">DETALLES</a>
                        </div>
                        <div class="card-body dashboard p-0 table-responsive">
                            <table class="table table-bordered border-primary m-0 table-hover text-center mb-0">
                                <thead>
                                <tr>
                                    <th scope="col" class="table-title  col-xs-1">LABORATORIO</th>
                                    <th scope="col " class="table-title col-xs-1">POR RECIBIR</th>
                                    <th scope="col " class="table-title col-xs-1">CALIDAD</th>
                                    <th scope="col " class="table-title col-xs-1">TRATAMIENTOS</th>
                                    <th scope="col " class="table-title col-xs-1">PIEZAS TOTALES</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <th scope="row" id="orders">{{ laboratoryOrders }}</th>
                                    <td id="orders">{{ pendientes }}</td>
                                    <td id="orders">{{ calidadOrders }}</td>
                                    <td id="orders">{{ tratamientos }}</td>
                                    <td id="orders">{{ piezasOrden }}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="col-12 dashboard-card">
                    <div class="card dashboard h-100">
                        <div class="card-header dashboard d-flex justify-content-between align-items-center">
                            <h3 class="dashboard-text ms-5 mt-0 mb-0">ENVÍOS QUE LLEGARÁN</h3>
                        </div>
                        <div class="card-body dashboard p-0">
                            <!-- Tarjeta de ENVIOS -->

                            <div id="arriving-shipments-table-container">

                            </div>

                        </div>
                    </div>
                </div>

                <div class="mod fade " id="shipment-detail-modal" tabindex="-1" role="dialog"
                     aria-labelledby="exampleModalLabel"
                     aria-hidden="true">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content" style="border-radius:10px">
                            <div class="modal-header bg-primary">
                                <h1 class="modal-title fs-5" id="shipment-detail-title"></h1>
                                <button type="button" class="close btn-close-white" data-dismiss="modal"
                                        aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">

                                <div id="shipment-detail-modal-body"></div>

                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tarjeta de Ventas -->
                <div class="col-lg-12 col-12 dashboard-card">
                    <div class="card dashboard h-100">
                        <div class="card-header dashboard d-flex justify-content-between align-items-center">
                            <h3 class="dashboard-text ms-5 mt-0 mb-0">
                                VENTAS
                                <span class="dashboard-date me-3">{{ today|date('d/m/Y') }}</span>
                            </h3>
                            <a type="button" class="btn btn-detail me-5" data-toggle="modal" data-target="#ventaModal">DETALLES</a>
                        </div>
                        <div class="card-body dashboard p-0">
                            <table class="tab m-0 h-100 w-100">
                                <thead>
                                <tr>
                                    <th scope="col" class="table-title border-end border-1">ALMACENABLES VENDIDOS</th>
                                    <th scope="col" class="table-title border-end">SERVICIOS VENDIDOS</th>
                                    <th scope="col" class="table-title">TOTAL DE VENTAS</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <th scope="row" class="border-end">{{ ventas[0].almacenesVendidos }}</th>
                                    <td class="border-end">{{ ventas[0].serviciosVendidos }}</td>
                                    <td>{{ ventas[0].totalVendido|number_format(2, '.', ',') }} MXN</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-4 dashboard-card">
                    <div class="card dashboard h-100">
                        <div class="card-header dashboard-two">NÚMERO DE TICKETS</div>
                        <div class="card-body dashboard-body-two">
                            <p id="ticket">{{ ventas[0].totalVentas }}</p>
                        </div>
                    </div>
                </div>

                <!-- Tarjeta de Inventario -->
                <div class="col-12 col-sm-8 dashboard-card">
                    <div class="card dashboard h-100">
                        <div class="card-header dashboard d-flex justify-content-between align-items-center">
                            <h3 class="dashboard-text ms-5 mt-0 mb-0">INVENTARIO EN {{ sucursalUsuario }}</h3>

                            <a type="button" class="btn btn-detail me-5"
                               onclick="window.open('/admin/stock/list', '_blank')">HISTORIAL</a>
                        </div>
                        <div class="card-body dashboard p-0 dashboard d-flex justify-content-between align-items-center ">
                            <table class="table table-bordered border-primary m-0 h-100 table-inventario ">
                                <thead>
                                <tr>
                                    <th scope="col" class="table-title border-end">MARCAS DESTACADAS</th>
                                    <th scope="col" class="table-title border-end">UNIDADES EN INVENTARIO</th>
                                    <th scope="col" class="table-title">MARCAS VENDIDAS</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td id="productos-mas-inventario" class="border-end">
                                        {% for producto in productosMasExistencia %}
                                            {{ producto.descripcion }} ({{ producto.cantidad }} unidades)<br/>
                                        {% endfor %}
                                    </td>
                                    <td id="unidades-en-inventario" class="border-end">{{ totalUnidades }} unidades</td>
                                    <td id="productos-mas-vendidos">
                                        {% for producto in productosMasVendidos %}
                                            {{ producto.descripcion }} ({{ producto.ventas }} ventas)<br/>
                                        {% endfor %}
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Promociones -->
    <div class="modal" id="promocionesModal" aria-labelledby="promocionesModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content" style="border-radius: 10px;">
                <div class="modal-header bg-primary-soft">
                    <h1 class="dashboard-text" id="lens-config-title">PROMOCIONES</h1>
                    <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive" style="height:350px">
                        <table class="table" id="search-client-table">
                            <thead>
                            <tr class="text-start">
                                <th class="text-start" scope="col">ARCHIVO</th>
                                <th class="text-start" scope="col">Sucursal</th>
                                <th class="text-start" scope="col">Acciones</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% if documentos is defined %}
                                {% for documento in documentos %}
                                    <tr>
                                        <td>{{ documento.archivo }}</td>
                                        <td>{{ documento.nombre }}</td>
                                        <td>
                                            <!-- Botón Visualizar con un ID único basado en iddocumentosucursal -->
                                            <button class="btn btn-primary btn-visualizar"
                                                    id="visualizar-{{ documento.iddocumentos }}"
                                                    onclick="abrirVisorDocumento('{{ documento.iddocumentos }}')">
                                                Visualizar
                                            </button>
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal" id="anunciosModal" aria-labelledby="anunciosModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content" style="border-radius: 10px;">
                <div class="modal-header bg-primary-soft">
                    <h1 class="dashboard-text" id="lens-config-title">ANUNCIOS</h1>
                    <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive" style="height:350px">
                        <table class="table" id="search-client-table">
                            <thead>
                            <tr class="text-start">
                                <th class="text-start" scope="col">ANUNCIO</th>
                                <th class="text-start" scope="col">Acciones</th>
                            </tr>
                            </thead>
                            <tbody>

                            {% for notificacion in notificaciones %}
                                <tr>
                                    <td>{{ notificacion.titulo }}</td>
                                    <td>
                                        <button class="btn btn-primary btn-visualizar"
                                                id="visualizar-{{ notificacion.idanuncios }}"
                                                onclick="abrirVisorAnuncio('{{ notificacion.idanuncios }}')">
                                            Visualizar
                                        </button>
                                    </td>
                                </tr>
                            {% endfor %}

                            <tr>
                                <td colspan="3" class="text-center">No hay anuncios disponibles</td>
                            </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>


    <!-- Modal básico -->
    <div class="modal fade" id="modal-visor" tabindex="-1" role="dialog" aria-labelledby="modalVisorLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalVisorLabel">Visor de Documento</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Cerrar">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="visorDocumentoContent">
                    <div id="contenedor-modal-visor"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="modal-visor-anuncios" tabindex="-1" role="dialog" aria-labelledby="modalVisorLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalVisorLabel">Visor de Documento</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Cerrar">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="contenedor-modal-visor-anuncios">
                    <div id="contenedor-modal-visor"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>


    <!-- Modal de Ventas -->
    <div class="mod" id="ventaModal" tabindex="-1" aria-labelledby="" aria-hidden="true">
        <div class="mod-dialog modal-dialog-scrollable modal-dialog-centered">
            <div class="modal-content" style="border-radius:10px">
                <div class="modal-header">
                    <div>
                        <h1 class="dashboard-text" id="lens-config-title">VENTAS DEL DÍA {{ today|date('d/m/Y') }}</h1>
                    </div>
                    <div>
                        <a type="button" class="btn btn-detail me-5"
                           onclick="window.open('/admin/venta/list', '_blank')">HISTORIAL</a>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                    aria-hidden="true">&times;</span></button>
                    </div>
                </div>
                <div class="modal-body bg-primary p-0">
                    <table id="ventasTable" class="table table-bordered white-border m-0">
                        <thead>
                        <tr>
                            <th scope="col" class="table-title">FOLIO</th>
                            <th scope="col" class="table-title">CLIENTE</th>
                            <th scope="col" class="table-title">FECHA DE VENTA</th>
                            <th scope="col" class="table-title">SUCURSAL</th>
                            <th scope="col" class="table-title">TIPO DE VENTA</th>
                            <th scope="col" class="table-title">TOTAL</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% if ventas[0].totalVentas > 0 %}
                            {% for venta in ventas %}
                                <tr>
                                    <th scope="row">{{ venta.folio }}</th>
                                    <td>{{ venta.nombreCliente }} {{ venta.apellidoPaternoCliente }} {{ venta.apellidoMaternoCliente }}</td>
                                    <td>{{ venta.fechaVenta|date('d/m/Y') }}</td>
                                    <td>{{ venta.nombreSucursal }}</td>
                                    <td>{{ venta.tipoVenta }}</td>
                                    <td>{{ venta.totalVenta|number_format(2, '.', ',') }} MXN</td>
                                </tr>
                            {% endfor %}
                        {% else %}
                        {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>


    <!-- Modal de Flujos -->
    <div class="mod" id="flujosModal" tabindex="-1" aria-labelledby="" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered modal-lg" style="max-width: 80%;">
            <div class="modal-content" style="border-radius:10px">
                <div class="modal-header">
                    <h1 class="dashboard-text" id="lens-config-title">FLUJOS DE EXPEDIENTE</h1>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body bg-primary p-0">
                    <div class="table-responsive-md overflow-auto" style="max-height: 500px;">
                        <table class="table table-bordered table-responsive white-border table-sm m-0 text-sm"
                               id="tabla-flujos-expedientes">
                            <thead>
                            <tr>
                                <th scope="col" class="table-title">ETAPA DE FLUJO</th>
                                <th scope="col" class="table-title">OPTOMETRISTA</th>
                                <th scope="col" class="table-title">PACIENTE</th>
                                <th scope="col" class="table-title">FECHA DE ACTUALIZACaaaIÓN</th>
                                <th scope="col" class="table-title">OPCIONES</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for flujo in flujoExpediente %}
                                <tr>
                                    <th scope="row" class="table-title">
                                        {% if stages[flujo.etapa - 1] is defined %}
                                            {{ stages[flujo.etapa - 1] }}
                                        {% else %}
                                            Etapa desconocida
                                        {% endif %}
                                    </th>
                                    <td>{{ flujo.nombreUsuario }} {{ flujo.apellidoUsuario }}</td>
                                    <td>{{ flujo.nombre }} {{ flujo.apellidopaterno }} {{ flujo.apellidomaterno }}</td>
                                    <td>{{ flujo.actualizacion|date("d/m/Y H:i") }}</td>

                                    <td class="d-flex flex-column align-items-center">
                                        <button type="button" class="btn flujo-op btn-success m-2"
                                                onclick="window.location.href='/admin/expediente-clinico/{{ flujo.idflujoexpediente }}'">
                                            INGRESAR AL FLUJO
                                        </button>
                                        <button type="button" class="btn flujo-op btn-danger m-2"
                                                onclick="borrarFlujo('{{ flujo.idflujoexpediente }}')">ELIMINAR
                                        </button>
                                    </td>
                                </tr>
                            {% else %}
                                <tr>
                                    <td colspan="5">No se encontraron flujos de expediente.</td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal de Órdenes -->
    <div class="mod" id="detallesModal" tabindex="-1" aria-labelledby="" aria-hidden="true">
        <div class="mod-dialog modal-dialog-scrollable modal-dialog-centered">
            <div class="modal-content" style="border-radius:10px">
                <div class="modal-header">
                    <h1 class="dashboard-text" id="lens-config-title">ORDENES DE LABORATORIO</h1>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body bg-primary p-0">
                    <table class="table table-bordered white-border m-0">
                        <thead>
                        <tr>
                            <th scope="col" class="table-title">ESTADO</th>
                            <th scope="col" class="table-title">FOLIO DE VENTA</th>
                            <th scope="col" class="table-title">CLIENTE</th>
                            <th scope="col" class="table-title">FECHA DE ACTUALIZACIÓN</th>
                            <th scope="col" class="table-title">PRODUCTOS</th>
                            <th scope="col" class="table-title">OPCIONES</th>
                        </tr>
                        </thead>
                        <tbody>

                        {% for order in laboratoryOrderDetails %}
                            <tr>
                                <td>{{ orderStages[order.stage - 1] }}</td>
                                <td>{{ order.folio }}</td>
                                <td>{{ order.cliente }}</td>
                                <td>{{ order.actualizacion|date("Y-m-d H:i:s") }}</td>
                                <td>{{ order.productos }}</td>
                                <td>
                                    <a type="button" class="btn btn-detail me-5"
                                       data-toggle="modal"
                                       data-idorden="{{ order.idordenlaboratorio }}"
                                       onclick="detalleFlujoExpediente(this)"
                                       data-target="#detallesOrdenesModal">DETALLES</a>
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Detalle Órdenes -->
    <div class="modal fade" id="detallesOrdenesModal" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered">
            <div class="modal-content" style="border-radius:10px;">
                <div class="modal-header">
                    <h1 class="dashboard-text">DATOS DE ORDEN</h1>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body bg-primary p-0 d-flex flex-column align-items-center"></div>
            </div>
        </div>
    </div>


    <!-- Modal de Traspasos Pendientes -->
    <div class="mod" id="traspasosModal" tabindex="-1" aria-labelledby="" aria-hidden="true">
        <div class="mod-dialog modal-dialog-scrollable modal-dialog-centered">
            <div class="modal-content" style="border-radius:10px">
                <div class="modal-header">
                    <h1 class="dashboard-text">TRASPASOS PENDIENTES {{ today|date('d/m/y') }}</h1>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body bg-primary p-0">
                    <table class="table table-bordered white-border m-0">
                        <thead>
                        <tr>
                            <th scope="col" class="table-title">Sucursal de origen</th>
                            <th scope="col" class="table-title">Sucursal de destino</th>
                            <th scope="col" class="table-title">Fecha de Traspaso</th>
                            <th scope="col" class="table-title">Estado</th>
                            <th scope="col" class="table-title">Responsable de traspaso</th>
                            <th scope="col" class="table-title">Opciones</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for traspaso in traspasoModal %}
                            <tr>
                                <th scope="row">{{ traspaso.sucursal_origen }}</th>
                                <td>{{ traspaso.sucursal_destino }}</td>
                                <td>{{ traspaso.fechasalida|date('d/m/Y') }}</td>
                                <td>
                                    {% if traspaso.aceptada == 1 %}
                                        Aceptada
                                    {% elseif traspaso.aceptada == 2 %}
                                        Pendiente
                                    {% elseif traspaso.aceptada == 0 %}
                                        Rechazada
                                    {% endif %}
                                </td>
                                <td>{{ traspaso.responsable }}</td>
                                <td class="d-flex flex-column align-items-center">
                                    <button type="button" class="btn flujo-op btn-success m-2"
                                            onclick="window.location.href='/admin/app/ordensalida/{{ traspaso.idordensalida }}/detalleOrdenSalida'">
                                        DETALLE
                                    </button>
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="6" class="table-traspasos text-center">No hay traspasos pendientes</td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal de Detalle Traspasos Pendientes -->
    <div class="mod" id="traspasosDetalleModal" tabindex="-1" aria-labelledby="" aria-hidden="true">
        <div class="mod-dialog modal-dialog-scrollable modal-dialog-centered">
            <div class="modal-content" style="border-radius:0px; box-shadow: 0 5px 15px rgba(0, 0, 0, .5);">
                <div class="modal-header">
                    <h1 class="dashboard-text" id="lens-config-title">ENVÍO PARA SUCURSAL</h1>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body bg-primary p-0">
                    <table class="table table-bordered white-border m-0">
                        <thead>
                        <tr>
                            <th scope="col" class="table-title">ETAPA DE ENVÍO</th>
                            <th scope="col" class="table-title">MENSAJERO</th>
                            <th scope="col" class="table-title">ORDENES DE LABORATORIO</th>
                            <th scope="col" class="table-title">FECHA DE ACTUALIZACIÓN</th>
                            <th scope="col" class="table-title">OPCIONES</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <th scope="row">Prueba</th>
                            <td>Prueba</td>
                            <td>Prueba</td>
                            <td>Prueba</td>
                            <td>
                                <a type="button" class="btn btn-detail me-5">DETALLES</a>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Prueba</th>
                            <td>Prueba</td>
                            <td>Prueba</td>
                            <td>Prueba</td>
                            <td>
                                <a type="button" class="btn btn-detail me-5">DETALLES</a>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Prueba</th>
                            <td>Prueba</td>
                            <td>Prueba</td>
                            <td>Prueba</td>
                            <td>
                                <a type="button" class="btn btn-detail me-5">DETALLES</a>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    {% include 'merma/modal_formulario_merma.html.twig' %}
{% endblock %}

    {% block javascripts %}
        {{ parent() }}
        <script src="{{ asset('js/jquery.formatCurrency-1.4.0.pack.js') }}"></script>
        <script src="{{ asset('lib/jQuery-Autocomplete-master/dist/jquery.autocomplete.min.js') }}"></script>
        <script type="text/javascript"
                src="https://cdn.jsdelivr.net/npm/html2canvas@1.0.0-rc.1/dist/html2canvas.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.debug.js"
                integrity="sha384-NaWTHo/8YCBYJ59830LTz/P4aQZK1sS0SneOgAvhsIl3zBu8r9RevNg5lHCHAuQ/"
                crossorigin="anonymous"></script>
        <script type="text/javascript" charset="utf8"
                src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.js"></script>
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/js/all.min.js"></script>
        <script>
            function selectOutputType() {
                console.log('selectOutputType() está siendo llamado...');

                $.ajax({
                    url: "/select-output-type",
                    type: 'GET',
                    beforeSend: loadingGif("departureSelection"),
                    dataType: "html"
                }).done(function (html) {
                    console.log('Respuesta recibida: ', html);
                    $("#departureSelection").html(html);


                }).fail(function (jqXHR, textStatus, errorThrown) {
                    console.error("Error en selectOutputType:", textStatus, errorThrown);
                    console.error("Respuesta del servidor:", jqXHR.responseText);
                });
            }

            $('#modalAgregarMerma').on('hidden.bs.modal', function () {
                if (formSubmitted == 1)
                    obtenerTablaMerma()
                    ;
            })
            console.log("¿selectOutputType está definida?:", typeof selectOutputType !== 'undefined');
            $(document).ready(function () {
                console.log("document.ready() se está ejecutando...");

                getArrivingShipmentsTable();

                console.log("Llamando a selectOutputType...");
                selectOutputType(); // <-- Aquí debería ejecutarse
                console.log("Después de llamar a selectOutputType()");

                obtenerFormularioAgregarMerma();
            });


            function obtenerFormularioAgregarMerma(idmerma = -1) {
                console.log('obtenerFormularioAgregarMerma');

                $.ajax({
                    url: "{{ path('agregar-merma') }}" + "?idmerma=" + idmerma,
                    type: 'GET',
                    beforeSend: loadingGif("formularioMerma"),
                    dataType: "html"
                }).done(function (html) {
                    $("#formularioMerma").html(html);


                }).fail(function () {
                    alert("error");
                });
            }

            function selectOutputType() {
                console.log('selectOutputType');
                $.ajax({
                    url: "/select-output-type",
                    type: 'GET',
                    beforeSend: loadingGif("departureSelection"),
                    dataType: "html"
                }).done(function (html) {
                    $("#departureSelection").html(html);


                }).fail(function () {
                    alert("error");
                });
            }

            function searchSale() {
                const foil = $("#foil-field").val();
                const enterpriseId = $("#enterprise-select").val();

                if (foil != '') {
                    $.ajax({
                        url: "{{ path('merma-select-sale-products') }}",
                        type: 'POST',
                        data: {foil: foil, enterpriseId: enterpriseId},
                        beforeSend: loadingGif("products-container"),
                        dataType: "html"
                    }).done(function (html) {
                        $("#products-container").html(html);
                    }).fail(function () {
                        alert("error");
                    });
                } else {
                    foilMessage("Necesitas ingresar un folio válido", false)
                }

            }

            function getArrivingShipmentsTable() {

                $.ajax({
                    url: "{{ path('dashboard-get-arriving-shipments-table') }}",
                    dataType: "html"
                }).done(function (html) {
                    $("#arriving-shipments-table-container").html(html);

                }).fail(function () {
                    alert("error");
                });
            }

            function changeModalTitle(trackingNumber) {
                $("#shipment-detail-title").text("Detalle de envio " + trackingNumber);
            }

            function showShipmentDetail(shipmentId) {

                $.ajax({
                    url: "{{ path('dashboard-show-shipment-detail') }}",
                    data: {shipmentId: shipmentId},
                    beforeSend: loadingGif("shipment-detail-modal-body"),
                    dataType: "html"
                }).done(function (html) {
                    $("#shipment-detail-modal-body").html(html);

                }).fail(function () {
                    alert("error");
                });

            }


            function processOrders(shipmentId, accept = 0) {

                let commentaries = $("#shipment-commentaries-textarea").val()
                let alertMsg = (accept == 1) ? "Se marcará como aceptado el envío" : "Se marcará como rechazado el envío"

                checkboxesOrder = document.getElementsByName('order');
                orders = [];
                for (var i = 0, n = checkboxesOrder.length; i < n; i++) {

                    if (checkboxesOrder[i].checked) {
                        orders.push(checkboxesOrder[i].value);
                    }

                }

                if (orders.length > 0) {

                    if (accept == 0 && !commentaries) {
                        Swal.fire({
                            title: "Necesitas agregar comentarios",
                            text: "Para rechazar un producto necesitas indicar el porqué",
                            type: "warning"
                        });
                    } else {
                        Swal.fire({
                            title: '¿Está seguro?',
                            text: alertMsg,
                            type: 'question',
                            showCancelButton: true,
                            confirmButtonColor: '#28B463',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'Aceptar',
                            cancelButtonText: 'Cancelar'
                        }).then((result) => {
                            if (result.value) {
                                $.ajax({
                                    url: "{{ path('shipment-process-shipment') }}",
                                    type: 'POST',
                                    data: {
                                        orders: orders,
                                        accept: accept,
                                        commentaries: commentaries,
                                        fromLocation: 1
                                    },
                                    dataType: "json"
                                }).done(function (response) {
                                    if (response.success) {
                                        showShipmentDetail(shipmentId)
                                        getArrivingShipmentsTable()
                                    } else {
                                        Swal.fire({
                                            title: "Hubo un problema",
                                            text: response.msg,
                                            type: "warning"
                                        });
                                    }
                                }).fail(function () {
                                    alert("error");
                                });
                            }
                        })
                    }
                } else {
                    Swal.fire({
                        title: "Hubo un problema",
                        text: "Debes seleccionar al menos un envío",
                        type: "warning"
                    });
                }

            }

            function borrarFlujo(idclinicalrecordflow = -1) {
                url = $('#url-dashboardFlujoExpediente-erase-flow').val()

                Swal.fire({
                    title: '¿Está seguro?',
                    text: 'Se eliminará el flujo de expediente',
                    type: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#28B463',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Aceptar',
                    cancelButtonText: 'Cancelar'
                }).then((result) => {
                    if (result.value) {
                        $.ajax({
                            url: url,
                            method: 'POST',
                            data: {idclinicalrecordflow: idclinicalrecordflow},
                            success: function (response) {
                                flowTable(1)
                            }
                        })
                    }
                })
            }

            function obtenerFormularioAgregarCliente() {

                $.ajax({
                    url: "{{ path('formularioCliente') }}",
                    type: 'GET',
                    beforeSend: loadingGif("create-user-form-container"),
                    dataType: "html"
                }).done(function (html) {
                        $("#create-user-form-container").html(html);

                    }
                );
            }

            function BuscarCliente() {

                nombre = $("#BuscarNombre").val();
                id = $("#BuscarID").val();
                apellidop = $("#BuscarApellidoP").val();
                apellidom = $("#BuscarApellidoM").val();

                email = $("#BuscarEmail").val();

                telefono = $("#BuscarTelefono").val();

                numeroempleado = $("#BuscarNumeroEmpleado").val();

                console.log(nombre);

                var urlBuscarCliente = $("#url-buscar-cliente").val();

                const prevHtml = $("#search-client-table").html()

                $.ajax({
                    url: urlBuscarCliente,
                    data: {
                        email: email, telefono: telefono, nombre: nombre, id: id,
                        apellidop: apellidop, apellidom: apellidom,
                        numeroempleado: numeroempleado
                    },
                    beforeSend: loadingGif("search-client-table"),
                }).done(function (suggestions) {

                    suggestions = Object.values(suggestions);
                    $("#search-client-table").html(prevHtml)
                    $("#tableBodyBuscarCliente").html("");

                    for (var i = 0; i < suggestions[0].length; i++) {

                        var newRow = document.createElement("tr");
                        var newCell = document.createElement("th");
                        newCell.setAttribute('scope', 'row');
                        newCell.textContent = i + 1;
                        newRow.appendChild(newCell);
                        var newCell = document.createElement("td");
                        newCell.setAttribute('id', 'apellidoP_' + suggestions[0][i].data);
                        newCell.textContent = suggestions[0][i].apellidopaterno;
                        newRow.appendChild(newCell);
                        var newCell = document.createElement("td");
                        newCell.setAttribute('id', 'apellidoM_' + suggestions[0][i].data);
                        newCell.textContent = suggestions[0][i].apellidomaterno;
                        newRow.appendChild(newCell);
                        var newCell = document.createElement("td");
                        newCell.setAttribute('id', 'nombre_' + suggestions[0][i].data);
                        newCell.textContent = suggestions[0][i].value;
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.setAttribute('id', 'email_' + suggestions[0][i].data);
                        newCell.textContent = suggestions[0][i].email;
                        newRow.appendChild(newCell);
                        var newCell = document.createElement("td");
                        newCell.setAttribute('id', 'telefono_' + suggestions[0][i].data);
                        newCell.textContent = suggestions[0][i].telefono;
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.setAttribute('id', 'numeroEmpleado_' + suggestions[0][i].data);
                        //newCell.setAttribute('class', 'd-none');
                        newCell.textContent = suggestions[0][i].numeroEmpleado;

                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        var newButton = document.createElement("button");
                        newButton.setAttribute('id', suggestions[0][i].data);

                        newButton.setAttribute('class', 'btn btn-info');
                        newButton.setAttribute('type', 'button');
                        newButton.setAttribute('onclick', 'seleccionarClienteDatos(this.id)');
                        newButton.setAttribute('data-dismiss', "modal");

                        newButton.textContent = 'Seleccionar';

                        newCell.appendChild(newButton);
                        ;
                        newRow.appendChild(newCell);

                        $("#tableBodyBuscarCliente").append(newRow)
                    }
                }).fail(function () {
                    alert("error");
                });
            }

            function seleccionarClienteDatos(id) {


                var telefono = $("#telefono_" + id).text();
                var email = $("#email_" + id).text();
                var apellidoP = $("#apellidoP_" + id).text();
                var apellidoM = $("#apellidoM_" + id).text();
                var nombre = $("#nombre_" + id).text();
                var idCliente = $("#id_" + id).text();
                console.log(idCliente);
                var numeroEmpleado = $("#numeroEmpleado_" + id).text();

                $('#cliente-telefono').val(telefono);
                $('#sin-convenio-email-cliente').val(email);
                $('#apellidoP').val(apellidoP);
                $('#apellidoM').val(apellidoM);
                $('#nombre').val(nombre);
                $('#numero-empleado').val(numeroEmpleado);
                $("#cliente-id").val(id);

            }

            function siguiente() {
                // Obtener el ID del cliente seleccionado
                var clienteId = document.getElementById("cliente-id").value;
                console.log("Se ha presionado el botón 'Siguiente'. ID del cliente:", clienteId);

                if (clienteId.length > 0) {
                    changeButton("siguiente-cliente", 0, 1);

                    var userId = $("#opciones").val();
                    $.ajax({
                        url: "{{ path('create_flujo_expediente') }}",
                        type: "POST",
                        data: {clienteId: clienteId, userId: userId},
                        success: function (response) {
                            // Decodificar la respuesta JSON
                            let data = JSON.parse(response);
                            console.log("ID del flujo:", data.idflow);

                            window.location.href = "/admin/expediente-clinico/" + data.idflow;
                        },
                        error: function (error) {
                            console.error("Error al crear el registro:", error);
                        }
                    });

                } else {
                    Swal.fire({
                        title: "Debe registrar un cliente primero",
                        text: "Aceptar",
                        icon: "error"
                    });
                }
            }

            function abrirVisorDocumento(iddocumentos) {

                $("#contenedor-modal-visor").html("");

                let url = $("#url-app_abrir_visor").val();

                $.ajax({
                    method: "POST",
                    url: url,
                    data: {iddocumentos: iddocumentos}
                })
                    .done(function (html) {
                        $("#contenedor-modal-visor").html(html);
                        $("#modal-visor").modal("show");

                    });
            }

            function abrirVisorAnuncio(idanuncios) {

                $("#contenedor-modal-visor-anuncios").html("");


                let url = $("#url-app_abrir_visor_anuncios").val();


                $.ajax({
                    method: "POST",
                    url: url,
                    data: {idanuncios: idanuncios}
                })
                    .done(function (html) {
                        $("#modal-visor-anuncios").modal("show");
                        $("#contenedor-modal-visor-anuncios").html(html);
                    })
                    .fail(function () {

                        alert("Hubo un error al cargar el anuncio.");
                    });
            }


            function detalleFlujoExpediente(button) {
                let idordenlaboratorio = $(button).data('idorden');

                $.ajax({
                    url: "{{ path('detalleFlujoExp') }}",
                    method: 'POST',
                    data: {idordenlaboratorio: idordenlaboratorio}, // Enviar el ID
                    success: function (response) {
                        $('#detallesOrdenesModal .modal-body').html(response); // Cargar el HTML en el modal
                        $('#detallesOrdenesModal').modal('show'); // Mostrar el modal
                    },
                    error: function () {
                        alert('Error al obtener los detalles.');
                    }
                });
            }


        </script>

        <style>
            .select2-container--default .select2-dropdown {
                z-index: 9999 !important; /* Ensure dropdown appears above all elements */
            }
        </style>

    {% endblock %}
