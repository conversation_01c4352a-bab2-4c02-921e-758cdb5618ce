{% extends 'admin/layout.html.twig' %}
{% block titleHead %}{% endblock %}
{% block title %}Flujo de Expediente
{% endblock %}
{% block content %}

<link rel="stylesheet" href="{{ asset('/lib/jQuery-Autocomplete-master/content/styles.css') }}">
<input id="url-imprimir-ticket" type="hidden" value="{{ path('imprimir-ticket') }}">
<input id="url-ticket" type="hidden" value="{{ path('ticket') }}">
<input id="url-guardar-venta" type="hidden" value="{{ path('guardar-venta') }}">
<input id="url-buscar-cliente" type="hidden" value="{{ path('buscar-cliente') }}">
<input id="url-buscar-numero-cliente" type="hidden" value="{{ path('buscar-numero-cliente') }}">
<input id="url-seleccionar-clientes" type="hidden" value="{{ path('seleccionar-clientes') }}">
<input id="url-seleccionar-email" type="hidden" value="{{ path('seleccionar-email') }}">
<input id="url-seleccionar-telefono" type="hidden" value="{{ path('seleccionar-telefono') }}">
<input id="url-buscar-beneficiarios" type="hidden" value="{{ path('buscar-beneficiarios') }}">
<input id="url-buscar-producto-codigo" type="hidden" value="{{ path('buscar-producto-codigo') }}">
<input id="url-buscar-producto-nombre" type="hidden" value="{{ path('buscar-producto-nombre') }}">
<input id="url-buscar-tratamiento" type="hidden" value="{{ path('buscar-tratamiento') }}">
<input id="url-buscar-venta" type="hidden" value="{{ path('buscar-venta') }}">
<input id="url-nueva-venta" type="hidden" value="{{ path('nueva-venta') }}">
<input id="btn-facturar" type="hidden" value="{{ path('ventafactura') }}">
<input id="url-buscar-cupon" type="hidden" value="{{ path('buscar-cupon') }}">
<input id="url-quitar-cupon" type="hidden" value="{{ path('quitar-cupon') }}">
<input id="url-visor-documentos" type="hidden" value="{{ path('visor-documentos') }}">
<input id="url-obtener-informacion-tipo-venta" type="hidden" value="{{ path('obtener-informacion-tipo-venta') }}">
<input id="url-venta-subir-documento-venta" type="hidden" value="{{ path('venta-subir-documento-venta') }}">
<input id="idventa" type="hidden" value="">
<input id="folio" type="hidden" value="">
<input id="escotizacion" type="hidden" value="">
<input id="descuentoproductosalmacenables" type="hidden" value="">
<input id="descuentoservicios" type="hidden" value="">
<input id="preciofijoproductosalmacenables" type="hidden" value="">
<input id="preciofijoservicios" type="hidden" value="">
<div class="col-md-3">
    <button id="resaltar-formulario" type="button" class="btn btn-primary mt-5 btn-nuevo-cliente" data-bs-toggle="mod"
            data-bs-target="#modalIniciarFlujo">Iniciar Flujo
    </button>
</div>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card nueva-venta">
                <div class="card-header card-header-info">
                    <div class="row">
                        <div class="col-md-7 text-left">
                            <div class="col-md-7">
                                <h2 class="card-title">Dashboard Flujo expediente</h2>
                                <strong>Sucursal:
                                    {{ app.user.sucursalIdsucursal.nombre }}</strong><br>
                                <strong>Usuario conectado:
                                    {{ app.user.nombre ~" "~app.user.apellidopaterno }}</strong>
                            </div>

                        </div>
                        <!-- Modal -->
                        <p class="card-category"></p>
                    </div>
                    <h1>Seleccionar Paciente</h1>
                    <form>
                        <div class="row" id="datos-cliente-sin-convenio">
                            <div class="col-sm-6 col-md-6">
                                <div class="form-group">
                                    <br><br>
                                    <label class="subtext">Apellido paterno:</label>
                                    <input id="apellidoP" type="text" class="form-control reset registro">
                                    <label class="subtext">Apellido materno:</label>
                                    <input id="apellidoM" type="text" class="form-control reset registro">
                                    <label class="subtext">Nombre(s):</label>
                                    <input id="nombre" type="text" class="form-control reset registro">
                                    <input id="cliente-id" type="hidden" class="form-control reset registro">
                                    <label class="subtext">Correo electrónico (opcional)</label>
                                    <input id="sin-convenio-email-cliente"
                                           class="form-control reset registro sin-convenio-email-cliente">

                                    <label class="subtext">Optomoetrista:</label>
                                    <select id="opciones" class="form-control reset registro">
                                        <option value="Lenin">Lenin</option>
                                        <option value="Diogenes">Diogenes</option>
                                        <option value="Dulce">Dulce</option>
                                        <option value="Antonio">Antonio</option>
                                    </select>
                                </div>
                            </div>


                            <div class="col-md-3">
                                <button id="resaltar-formulario" type="button"
                                        class="btn btn-success mt-5 btn-nuevo-cliente" data-bs-toggle="mod"
                                        data-bs-target="#modalBuscarCliente">Buscar Cliente
                                </button>
                            </div>

                            <div class="col-md-3">
                                <button id="resaltar-formulario" type="button"
                                        class="btn btn-primary mt-5 btn-nuevo-cliente" data-bs-toggle="mod"
                                        data-bs-target="#modalNuevoCliente">Nuevo Cliente
                                </button>
                            </div>

                            <di v class="mod" id="modalBuscarCliente" tabindex="-1" aria-labelledby="exampleModalLabel"
                                aria-hidden="true">
                                <div class="mod-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h1 class="modal-title fs-5" id="exampleModalLabel">BUSCAR CLIENTE</h1>
                                            <button type="button" class="btn-close" data-bs-dismiss="mod"
                                                    aria-label="Cerrar"></button>
                                        </div>

                                        <div class="modal-body">
                                            <div class="row">
                                                <div class="col-md-2 buscar">
                                                    <label for="BuscarApellidoP" class="form-label text">Apellido
                                                        paterno:</label>
                                                </div>
                                                <div class="col-md-2 buscar">
                                                    <input id="BuscarApellidoP" class="form-control" type="text"/>
                                                </div>

                                                <div class="col-md-2 buscar">
                                                    <label for="BuscarApellidoM" class="form-label text">Apellido
                                                        materno:</label>
                                                </div>
                                                <div class="col-md-2 buscar">
                                                    <input id="BuscarApellidoM" class="form-control" type="text"/>
                                                </div>

                                                <div class="col-md-2 buscar">
                                                    <label for="BuscarNombre" class="form-label text">Nombre(s):</label>
                                                </div>
                                                <div class="col-md-2 buscar">
                                                    <input id="BuscarNombre" class="form-control" type="text"/>
                                                </div>
                                            </div>
                                            <br><br>
                                            <div class="row">
                                                <div class="col-md-2 buscar">
                                                    <label for="BuscarEmail" class="form-label text">Correo
                                                        electrónico:</label>
                                                </div>
                                                <div class="col-md-2 buscar">
                                                    <input id="BuscarEmail" class="form-control" type="text"/>
                                                </div>

                                                <div class="col-md-2 buscar">
                                                    <label for="BuscarTelefono"
                                                           class="form-label text">Teléfono:</label>
                                                </div>
                                                <div class="col-md-2 buscar">
                                                    <input id="BuscarTelefono" class="form-control" type="text"/>
                                                </div>
                                                <div class="col-md-2 buscar">
                                                    <input id="BuscarNumeroEmpleado" class="form-control" type="text"/>
                                                </div>

                                                <div class="col-md-2 buscar">
                                                    <button id="" type="button" class="btn btn-info col-md-4"
                                                            onclick="BuscarCliente()">Buscar
                                                    </button>
                                                </div>
                                            </div>

                                            <br><br>

                                            <div class="row justify-content-md-center">
                                                <div class="col-md-10">
                                                    <div class="table-responsive">
                                                        <table class="table">
                                                            <thead>
                                                            <tr class="text-start">
                                                                <th class="text-start" scope="col">#</th>
                                                                <th class="text-start" scope="col">Apellido paterno</th>
                                                                <th class="text-start" scope="col">Apellido materno</th>
                                                                <th class="text-start" scope="col">Nombre</th>
                                                                <th class="text-start" scope="col">Correo</th>
                                                                <th class="text-start" scope="col">Teléfono</th>
                                                                <th class="text-start" scope="col">Número de empleado
                                                                </th>
                                                                <th></th>
                                                            </tr>
                                                            </thead>
                                                            <tbody id="tableBodyBuscarCliente"></tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="mod">
                                                Cerrar
                                            </button>
                                        </div>
                                    </div>
                                </div>
                        </div>
                </div>
            </div>


            {% endblock %}

            {% block javascripts %}

                {{ parent() }}

                <script src="{{ asset('js/jquery.formatCurrency-1.4.0.pack.js') }}"></script>
                <script src="{{ asset('lib/jQuery-Autocomplete-master/dist/jquery.autocomplete.min.js') }}"></script>
                <script type="text/javascript"
                        src="https://cdn.jsdelivr.net/npm/html2canvas@1.0.0-rc.1/dist/html2canvas.min.js"></script>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.debug.js"
                        integrity="sha384-NaWTHo/8YCBYJ59830LTz/P4aQZK1sS0SneOgAvhsIl3zBu8r9RevNg5lHCHAuQ/"
                        crossorigin="anonymous"></script>
                <script src="{{ asset('js/format-input-field.js') }}"></script>
                <script src="{{ asset('js/iniciar-formato-campos.js') }}"></script>
                <script src="{{ asset('js/nueva-venta.js') }}?v=4"></script>
                <script type="text/javascript">

                    const checkProductos = new Set();

                    async function uploadFile() {
                        let formData = new FormData();
                        var url = $("#url-venta-subir-documento-venta").val();
                        formData.append("file", fileupload.files[0]);

                        var todoBien = false;
                        var nombreDocumento = "";
                        var msj = "";


                        const response = await fetch(url, {
                            method: "POST",
                            body: formData
                        });
                        const result = await response.json();
                        return result;


                    }

                    function abrirVisor() {
                        var folio = $("#folio").val();

                        $("#modal-visor-body").html("");
                        let url = $("#url-visor-documentos").val();
                        console.log('URL');
                        console.log(url);
                        $.ajax({
                            method: "POST",
                            url: url,
                            data: {
                                folio: folio
                            }
                        }).done(function (html) {
                            $("#modal-visor-body").html(html);

                        });
                    }


                    $(document).ready(function () {

                        cargarMeses();
                        resetCampos();
                        reset();
                        {% if venta is not null or venta is not empty %}$("#escotizacion").val({{ venta.cotizacion }});
                        {% if venta.cotizacion !="1" %}$('#convenio').select2("enable", false);
                        $('#producto-codigo').attr("disabled", true);
                        $('#producto-nombre').attr("disabled", true);
                        $('#cupon-descuento').attr("disabled", true);
                        $('#usuario-venta').select2("enable", false);
                        $('#usuario-donde-nos-conocio').select2("enable", false);
                        $('#guardar-venta').text("Actualizar Venta");
                        $('#buscar-cupon').remove();
                        $('#buscarCupon').remove();
                        $('#resaltar-formulario').remove();
                        $('#guardar-coitizacion').remove();
                        {% endif %}$("#btn-pdf").removeClass("d-none");
                        $("#idventa").val("{{ venta.idventa }}");
                        $("#apellidoP").val("{{ venta.clienteApellidopaterno }}");
                        $("#apellidoM").val("{{ venta.clienteApellidomaterno }}");
                        $("#nombre").val("{{ venta.nombreCliente }}");
                        $("#cliente-id").val("{{ venta.idcliente }}");
                        buscarBeneficiarios("{{ venta.idcliente }}");
                        $("#cliente-telefono").val("{{ venta.telefono }}");
// $("#beneficiario").val(response.venta.beneficiarioNombre);
                        $("#beneficiario-nombre").val("{{ venta.beneficiario }}");
                        $("#sin-convenio-nombre-cliente").val("{{ venta.nombreCliente }}");
                        $("#sin-convenio-email-cliente").val("{{ venta.email }}");
// $("#convenio").val("{{ venta.idtipoventa }}");
// $('#convenio').val({{ venta.idtipoventa }});
                        $('#convenio').val({{ venta.idtipoventa }}).trigger('change');
                        {% if venta.idunidad !="" and venta.idunidad is not null %}$('#unidad').val({{ venta.idunidad }}).trigger('change');
                        {% endif %}$("#numero-empleado").val("{{ venta.numeroempleado }}").trigger('change');
                        $("#usuario-venta").val("{{ venta.idusuario }}").trigger('change');
                        $("#usuario-donde-nos-conocio").val("{{ venta.comonosconocio }}").trigger('change');
                        $("#folio").val("{{ venta.folio }}");

                        var exists = false;
                        var usuarioVenta = "{{ venta.idusuario }}";

                        $("#usuario-venta option").each(function () { // alert("  "+this.value+" vs "+response.venta.idusuario);
                            if (this.value == usuarioVenta) {
                                exists = true;
                                return false;
                            }
                        });
                        if (exists) { // alert("entra 0");
                            $("#usuario-venta").val(usuarioVenta);
                        } else {
// agregamos la opcion
// alert("entra 1");
//    $("#usuario-venta").append('<option selected value="'+"{{ venta.idusuario }}"+'">'+"{{ venta.vendedorNombre }}"+" "+"{{ venta.vendedorApellidopaterno }}"+'</option>' );
                            var option = new Option('{{ venta.vendedorNombre~" "~venta.vendedorApellidopaterno }}', '{{ venta.idusuario }}', true, true);
                            $("#usuario-venta").append(option).trigger('change');

                        }


                        {% if productos %}
                        {% for producto in productos %}


// agregarProductoDB(productoNombre,productoPrecio,productoDescripcion,descuento,modelo,marca,tipoproducto,idproducto,idproductoventa,idstockventa,idstock){
                        agregarProductoDB('{{ producto.modelo }}', '{{ producto.precio }}', '{{ producto.descripcion }}', '{{ producto.porcentajedescuento }}', '{{ producto.modelo }}', '{{ producto.marca }}', '{{ producto.tipoproducto }}', '{{ producto.idproducto }}', "", '{{ producto.idstockventa }}', '{{ producto.idstock }}', '{{ producto.marca }}', '{{ producto.codigobarras }}');
                        {% endfor %}{% endif %}{% if pagos %}{% for pago in pagos %}agregarPagoDB('{{ pago.monto }}', '{{ pago.tipopago }}', '{{ pago.idpago }}');{% endfor %}{% endif %}
                        <!--cupan-->
                        {% if cupon %}
                        $("#cupon-descuento").val('{{ cupon }}');
                        $(".agregar-cupon").addClass("d-none");
                        $(".quitar-cupon").removeClass("d-none");
                        $("#cupon-descuento").attr("disabled", true);{% endif %}

                        {% else %}
                        resetCampos();
                        reset();
                        {% endif %}$("#producto-codigo").on('keyup', function (e) {
                            e.preventDefault();
                            if (e.key === 'Enter' || e.keyCode === 13) {
                                buscarProductoPorCódigo($(this).val());
                            }
                        });
                    });

                    function resaltarFormularioRegistrar(opcion) {

                        if (opcion == "1") {
                            resetCampos();

                            $(".registro").addClass("border border-warning");

                            $(".btn-nuevo-cliente").addClass("d-none");
                            $(".btn-reset-cliente").removeClass("d-none");


                            $(".numero-empleado").autocomplete("disable");
                            $(".cliente").autocomplete("disable");
                            $(".sin-convenio-email-cliente").autocomplete("disable");
                            $(".cliente-telefono").autocomplete("disable");
                            $("#beneficiario").val("");
                            $("#beneficiario").attr("disabled", "disabled");
                        } else {
                            $(".numero-empleado").autocomplete("enable");
                            $(".cliente").autocomplete("enable");
                            resetCampos();

                            $(".registro").removeClass("border border-warning");

                            $(".btn-nuevo-cliente").removeClass("d-none");
                            $(".btn-reset-cliente").addClass("d-none");
// $(".sin-convenio-email-cliente").addClass("d-none");
// $(".cliente-telefono").addClass("d-none");
                            $("#beneficiario").attr("disabled", "false");
                        }

                    }

                    function BuscarCliente() {

                        nombre = $("#BuscarNombre").val();
                        apellidop = $("#BuscarApellidoP").val();
                        apellidom = $("#BuscarApellidoM").val();

                        email = $("#BuscarEmail").val();

                        telefono = $("#BuscarTelefono").val();

                        numeroempleado = $("#BuscarNumeroEmpleado").val();

                        console.log(numeroempleado);

                        var urlBuscarCliente = $("#url-buscar-cliente").val();

                        $.ajax({
                            url: urlBuscarCliente,
                            data: {
                                email: email,
                                telefono: telefono,
                                nombre: nombre,
                                apellidop: apellidop,
                                apellidom: apellidom,
                                numeroempleado: numeroempleado
                            }

                        }).done(function (suggestions) {

                            suggestions = Object.values(suggestions);


                            var tableBody = document.querySelector("#tableBodyBuscarCliente");
                            tableBody.innerHTML = "";


                            for (var i = 0; i < suggestions[0].length; i++) {


                                var newRow = document.createElement("tr");
                                var newCell = document.createElement("th");
                                newCell.setAttribute('scope', 'row');
                                newCell.textContent = i + 1;
                                newRow.appendChild(newCell);
                                var newCell = document.createElement("td");
                                newCell.setAttribute('id', 'apellidoP_' + suggestions[0][i].data);
                                newCell.textContent = suggestions[0][i].apellidopaterno;
                                newRow.appendChild(newCell);
                                var newCell = document.createElement("td");
                                newCell.setAttribute('id', 'apellidoM_' + suggestions[0][i].data);
                                newCell.textContent = suggestions[0][i].apellidomaterno;
                                newRow.appendChild(newCell);
                                var newCell = document.createElement("td");
                                newCell.setAttribute('id', 'nombre_' + suggestions[0][i].data);
                                newCell.textContent = suggestions[0][i].value;

                                newRow.appendChild(newCell);
                                var newCell = document.createElement("td");
                                newCell.setAttribute('id', 'email_' + suggestions[0][i].data);
                                newCell.textContent = suggestions[0][i].email;
                                newRow.appendChild(newCell);
                                var newCell = document.createElement("td");
                                newCell.setAttribute('id', 'telefono_' + suggestions[0][i].data);
                                newCell.textContent = suggestions[0][i].telefono;
                                newRow.appendChild(newCell);

                                var newCell = document.createElement("td");
                                newCell.setAttribute('id', 'numeroEmpleado_' + suggestions[0][i].data);
// newCell.setAttribute('class', 'd-none');
                                newCell.textContent = suggestions[0][i].numeroEmpleado;

                                newRow.appendChild(newCell);

                                var newCell = document.createElement("td");
                                var newButton = document.createElement("button");
                                newButton.setAttribute('id', suggestions[0][i].data);

                                newButton.setAttribute('class', 'btn btn-info');
                                newButton.setAttribute('type', 'button');
                                newButton.setAttribute('onclick', 'seleccionarClienteDatos(this.id)');
                                newButton.setAttribute('data-bs-dismiss', "mod");

                                newButton.textContent = 'Seleccionar';


                                newCell.appendChild(newButton);
                                ;
                                newRow.appendChild(newCell);

                                tableBody.appendChild(newRow);

                            }


                        }).fail(function () {
                            alert("error");
                        });


                    }

                    function seleccionarClienteDatos(id) {


                        var telefono = $("#telefono_" + id).text();
                        var email = $("#email_" + id).text();

                        var apellidoP = $("#apellidoP_" + id).text();
                        var apellidoM = $("#apellidoM_" + id).text();
                        var nombre = $("#nombre_" + id).text();
                        var numeroEmpleado = $("#numeroEmpleado_" + id).text();
                        buscarBeneficiarios(id);


                        $('#cliente-telefono').val(telefono);
                        $('#sin-convenio-email-cliente').val(email);
                        $('#apellidoP').val(apellidoP);
                        $('#apellidoM').val(apellidoM);
                        $('#nombre').val(nombre);
                        $('#numero-empleado').val(numeroEmpleado);
                        $("#cliente-id").val(id);


                    }


                    function resetCampos() {
                        $("#producto-codigo").focus();
                        $("#anticipo-total-pagado").val("");
                        $("#anticipo-restan").val("");
                        $("#cliente").val("");
                        $("#sin-convenio-email-cliente").val("");
                        $("#cliente-telefono").val("");
                        $("#cliente-id").val("");
                        $("#numero-empleado").val("");
                        $("#beneficiario").val("");
                        $("#beneficiario-nombre").val("");
                        $("#beneficiario-id").val("");
// $("#convenio").val(null);
                        $('#mySelect2').val(null).trigger('change');


                    }

                    function seleccionarBeneficiario(input) {
                        var nombre = $(input).find('option:selected').text();
                        var id = $(input).val();
                        $("#beneficiario-nombre").val(nombre);
                        $("#beneficiario-id").val(id);
                    }
                </script>

                <script>
                    function buscarProductoPorCódigo() {

                        var url = $("#url-buscar-producto-codigo").val();

                        var valor = $("#producto-codigo").val();


                        $.ajax({
                            method: "POST",
                            data: {
                                query: valor
                            },
                            url: url,
                            dataType: "json",
                            beforeSend: function (xhr) {
                            }
                        }).done(function (resultado) {
                            console.log(resultado);
                            if (resultado.exito) {
                                $("#producto-precio").val(resultado.producto.precio);
                                $("#producto-descripcion").val(resultado.producto.descripcion);
                                $("#producto-precio").val(resultado.producto.precio);
                                ponerFormatoPesos("#producto-precio");
                                $("#producto-nombre-id").val(resultado.producto.data);
                                $("#producto-nombre").val(resultado.producto.nombre);
                                $("#producto-tipoproducto").val(resultado.producto.tipoproducto);
                                $("#producto-idproducto").val(resultado.producto.idproducto);
                                $("#producto-marca").val(resultado.producto.marca);
                                $("#producto-codigobarras").val(resultado.producto.value);
                                $("#producto-codigobarrasuniversal").val(resultado.producto.codigobarrasuniversal);
                                $("#producto-masivounico").val(resultado.producto.masivounico);
// ponemos el id
// calcularTotal(suggestion.data);
                                agregarProducto(resultado.producto.data);
                            } else {
                                $("#producto-codigo").val("");
                                Swal.fire('No se encontraron resultados!', resultado.msj, 'warning');
                            }
// if($("#producto-nombre-id").val() !=""){

                        });
                    }

                    $(document).ready(function () { // Paso 2
                        var urlSeleccionarClientes = $("#url-seleccionar-clientes").val();
                        var urlSeleccionarEmail = $("#url-seleccionar-email").val();
                        var urlSeleccionarTelefono = $("#url-seleccionar-telefono").val();

                        var urlBuscarCliente = $("#url-buscar-cliente").val();
                        var urlBuscarNumeroCliente = $("#url-buscar-numero-cliente").val();
                        var urlBuscarProductoCodigo = $("#url-buscar-producto-codigo").val();
                        var urlBuscarProductoNombre = $("#url-buscar-producto-nombre").val();
                        var urlBuscarTratamiento = $("#url-buscar-tratamiento").val();


                        $('.numero-empleado').autocomplete({
                            serviceUrl: urlBuscarNumeroCliente,
                            onSelect: function (suggestion) {
                                resetCampos();
                                $("#cliente-id").val(suggestion.data);
                                $("#numero-empleado").val(suggestion.value);
// $("#cliente").val(suggestion.nombre);
                                buscarBeneficiarios(suggestion.data);


                                $("#nombre").val(suggestion.nombre.trim());
                                $("#apellidoP").val(suggestion.apellidopaterno);
                                $("#apellidoM").val(suggestion.apellidomaterno);
                                $("#sin-convenio-email-cliente").val(suggestion.email);
                                $("#cliente-telefono").val(suggestion.telefono);
                                $("#cliente-id").val(suggestion.data);
                            }
                        });

// Paso 1
                        $('.cliente').autocomplete({
                            delay: 2000,
                            serviceUrl: urlSeleccionarClientes,
                            onSelect: function (suggestion) {
                                resetCampos();
                                $("#cliente").val(suggestion.nombre);
                                $("#sin-convenio-email-cliente").val(suggestion.email);
                                $("#cliente-telefono").val(suggestion.telefono);
                                $("#cliente-id").val(suggestion.data);
                                $("#numero-empleado").val(suggestion.numeroempleado);
                                buscarBeneficiarios(suggestion.data);


                            },
                            onSearchComplete: function (event, ui) { /* $("#cliente-id").val("");
           $("#numero-empleado").val("");*/
                            }
                        });
                        $('#producto-nombre').autocomplete({
                            delay: 500,
                            serviceUrl: urlBuscarProductoNombre,
                            onSelect: function (suggestion) {
                                console.log(suggestion);
                                $("#producto-precio").val(suggestion.precio);
                                $("#producto-descripcion").val(suggestion.descripcion);
                                $("#producto-precio").val(suggestion.precio);
                                ponerFormatoPesos("#producto-precio");
                                $("#producto-nombre-id").val(suggestion.data);
                                $("#producto-codigo").val(suggestion.codigo);
                                $("#producto-codigobarras").val(suggestion.codigo);
                                $("#producto-tipoproducto").val(suggestion.tipoproducto);
                                $("#producto-idproducto").val(suggestion.idproducto);
// calcularTotal();
                                agregarProducto(suggestion.data);
                            }
                        });
                        $('#tratamiento').autocomplete({
                            serviceUrl: urlBuscarTratamiento,
                            onSelect: function (suggestion) {
                                $("#tratamiento-id").val(suggestion.data);
                                $("#tratamiento-precio").val(suggestion.precio);
                                agregarTratamiento(suggestion.data);
                            }
                        });
// para seleccionar el tipo de formulario dependeiendo del convenio
                        cambiarFormulario();
                    });

                    function buscarBeneficiarios(idcliente, nombreCliente = "") {
                        $("#beneficiario").html("");
                        var url = $("#url-buscar-beneficiarios").val();
                        $.ajax({
                            method: "POST",
                            data: {
                                idcliente: idcliente
                            },
                            url: url,
                            beforeSend: function (xhr) {
                            }
                        }).done(function (response) { // var res=JSON.parse(response);
                            $("#beneficiario").append('<option value=""></option>');
                            let selected = false;
                            for (beneficiario in response) {
                                console.log(response[beneficiario]);

                                if (response[beneficiario].nombre == nombreCliente) {
                                    selected = true;
                                } else {
                                    selected = false;
                                }
//      $("#beneficiario").append('<option value="'+response[beneficiario].idbeneficiario+'">'+response[beneficiario].nombre+"</option>");
                                $("#beneficiario").append(new Option(response[beneficiario].nombre, response[beneficiario].idbeneficiario, selected, selected));
                            }
                        });
                    }

                    function imprimir2() {
                        PrintElem("ticket");
                    }

                    function PrintElem(elem) {
                        var mywindow = window.open('', 'PRINT', 'height=400,width=800');
                        mywindow.document.write('<html><head><title>' + document.title + '</title>');
                        mywindow.document.write('</head><body >');
                        mywindow.document.write('<h1>' + document.title + '</h1>');
                        mywindow.document.write(document.getElementById(elem).innerHTML);
                        mywindow.document.write('</body></html>');
                        mywindow.document.close(); // necessary for IE >= 10
                        mywindow.focus(); // necessary for IE >= 10*/
                        mywindow.print();
                        mywindow.close();

                        return true;
                    }

                    function calcularTotalProducto(id) {
                        console.log("calcularTotalProducto ");
                        var productoCantidad = quitarFormato($("#producto-cantidad-" + id).val());
                        console.log("productoCantidad " + productoCantidad);
                        var productoPrecio = quitarFormato($("#producto-precio-" + id).val());
                        var productoDescuento = quitarFormato($("#producto-descuento-" + id).val());
                        console.log("productoPrecio " + productoPrecio);
                        console.log("productoDescuento " + productoDescuento);

                        $("#producto-final-" + id).val(productoPrecio * (1 - (productoDescuento * .01)));
                        $("#producto-total-" + id).val(productoPrecio * (1 - (productoDescuento * .01)) * productoCantidad);

                        ponerFormatoPesos("#producto-total-" + id);
                    }

                    async function guardarVenta(esCotizacion = 0) {

                        var validEmail = /^\w+([.-_+]?\w+)*@\w+([.-]?\w+)*(\.\w{2,10})+$/;

// var beneficiario=$("#beneficiario").val();
//    alert("beneficiario "+beneficiario);
                        var idventa = $("#idventa").val();

                        var msj = "";
                        var apellidoP = $("#apellidoP").val();
                        var apellidoM = $("#apellidoM").val();
                        var nombre = $("#nombre").val();


                        var idcliente = $("#cliente-id").val();
                        var convenio = $("#convenio").val();

// $("#convenio").trigger('change');;


                        console.log("convenio " + convenio);
                        var numeroEmpleado = $("#numero-empleado").val();
                        var idunidad = $("#unidad").val();
                        var unidadNombre = $("#unidad").find('option:selected').text();
                        var pidioFactura = "";
                        var tipoPago = "";
                        var subtotal = 0;
                        var iva = 0;
                        var total = 0;
                        var sinConvenioNombreCliente = $("#sin-convenio-nombre-cliente").val();
                        var clienteTelefono = $("#cliente-telefono").val();
                        var sinConvenioEmailCliente = $("#sin-convenio-email-cliente").val();
                        var idUsuarioVenta = $("#usuario-venta").val();
                        var dondeNosConocio = $("#usuario-donde-nos-conocio").val();
                        var cuponDescuento = $("#cupon-descuento").val();
                        var beneficiarioNombre = $("#beneficiario-nombre").val();
                        var beneficiario = $("#beneficiario-id").val();
                        var anticipoTotalPagado = parseFloat(quitarFormato($("#anticipo-total-pagado").val()));

                        var notas = $("#notas").val();


                        console.log("anticipoTotalPagado " + anticipoTotalPagado);
                        console.log("convenio " + convenio);
                        var porcentajeiva = 0.16;
                        var url = $("#url-guardar-venta").val();

                        var documentoobligatoriocerrarventa = $("#documentoobligatoriocerrarventa").val();
                        var foliogarantia = $("#folio-venta-garantia").val();

// obtenemos los poroductos
                        var productos = [];
// esto es para el aparato auditivo
                        var numeroProductos = 0;

                        var todoBien = false;
                        var todoBienDocumento = false;

                        var nombreDocumento = "";
// validamos los datos
                        $(".producto-seleccionado").each(function () {
                            var id = $(this).attr('id');
                            var idproductoventa = $(this).data('idproductoventa');
                            var idstockventa = $(this).data('idstockventa');
                            var idproducto = $(this).data('idproducto');
                            var idstock = $(this).data('idstock');
                            var tipo = $(this).data('tipo');
                            var descripcion = $(this).data('descripcion');
                            var nombre = $(this).data('nombre');
                            var tipoproducto = $(this).data('tipoproducto');
                            var codigobarras = $(this).data('codigobarras');
                            var productoTotal = quitarFormato($("#producto-total-" + id).val());
                            var productoPrecio = quitarFormato($("#producto-precio-" + id).val());
                            var productoCantidad = quitarFormato($("#producto-cantidad-" + id).val());
                            numeroProductos = quitarFormato($("#producto-cantidad-" + id).val());
                            var productoDescuento = quitarFormato($("#producto-descuento-" + id).val());
                            var productoPrecioFinal = quitarFormato($("#producto-final-" + id).val());
                            producto = {
                                codigobarras: codigobarras,
                                tipoproducto: tipoproducto,
                                idstockventa: idstockventa,
                                idstock: idstock,
                                idproducto: idproducto,
                                productoDescuento: productoDescuento,
                                descripcion: descripcion,
                                nombre: nombre,
                                productoTotal: productoTotal,
                                productoPrecio: productoPrecio,
                                productoPrecioFinal: productoPrecioFinal,
                                productoCantidad: productoCantidad,
                                tipo: tipo,
                                idproductoventa: idproductoventa
                            };
                            productos.push(producto);

                        });

                        var checkNumber = /^.{10,}$/;


                        if (nombre != "") {
                            if (productos.length > 0) {
                                if (idUsuarioVenta != "") {
                                    if (convenio != "" && convenio != null) { // si el email es diferente entonces los validamos

                                        if (clienteTelefono != "") {

                                            if (checkNumber.test(clienteTelefono)) { // si los campos extra estan visibles entonves tambien los validamos
                                                if (!$(".datos-extra").hasClass("d-none")) { // validamos
                                                    if (numeroEmpleado != "") {
                                                        if (unidad != "") {
                                                            todoBien = true;
                                                        } else {
                                                            $("#unidad").focus();
                                                            msj = "La unida de procedencia es obligatoria";
                                                        }
                                                    } else {
                                                        $("#numero-empleado").focus();
                                                        msj = "El número de empleado es obligatorio";
                                                    }

                                                } else {
                                                    todoBien = true;
                                                }
                                            } else {
                                                $("#cliente-telefono").focus();
                                                msj = "El teléfono es inválido";
                                            }

                                        } else {
                                            $("#cliente-telefono").focus();
                                            msj = "El teléfono es obligatorio";
                                        }


                                    } else {
                                        $("#convenio").focus();
                                        msj = "El tipo de venta es obligatorio";
                                    }
                                } else {
                                    msj = "Debe seleccionar el usuario que realizó la venta";
                                    $("#usuario-venta").focus();
                                }

                            } else {
                                $("#producto-codigo").focus();
                                msj = "Debe seleccionar por lo menos un producto";
                            }
                        } else {
                            $("#cliente").focus();
                            msj = "El nombre del cliente es obligatorio";
                        }

                        if (sinConvenioEmailCliente != "") {
                            if (validEmail.test(sinConvenioEmailCliente)) {
                            } else {
                                todoBien = false;
                                msj = "El correo eletránico tiene un formato inválido";
                            }
                        }

                        cambiarFormulario();

                        uploadFile().then(result => {

                            if (result.exito) { /*  Swal.fire(
                              'Perfecto!',
                              "todo bien",
                              'success'
                      );*/
                                todoBienDocumento = true;
                                nombreDocumento = result.nombreDocumento;
                            } else if (esCotizacion == "0" && documentoobligatoriocerrarventa == "1") { // Si se quiere que el documento sea obligario se debe poner como false

                                todoBienDocumento = false;


                            } else
                                todoBienDocumento = true;


                            if (todoBien && todoBienDocumento) {


                                if (convenio == "UAM") {
                                    var subtotal = 2583;
                                    var iva = 492;
                                    var total = 3075.00;
                                } else {
                                    var subtotal = quitarFormato($("#subtotal").val());
                                    var iva = quitarFormato($("#iva").val());
                                    var total = 0;
                                    if (convenio == "UAM Auditivos") {
                                        var total = Math.round(quitarFormato($("#total").val()));
                                    } else {
                                        var total = quitarFormato($("#total").val());
                                    }
                                }

                                meses = document.getElementById('cantidad_meses').value;

                                if (esCotizacion != "1" || (esCotizacion == "1" && tipoPago == "")) { // agregamos los pagos
                                    pagos = [];

// si no es cotizacion tomamos los pagos
                                    if (esCotizacion != "1") {
                                        $(".pagos").each(function () {
                                            pagos.push({
                                                monto: parseFloat(quitarFormato($(this).val())),
                                                tipopagoanticipo: $(this).data('tipopagoanticipo'),
                                                idpago: $(this).data('idpago')
                                            });
                                        });
                                    }
                                    Swal.fire({
                                        title: 'Espere por Favor...',
                                        html: '',
                                        showConfirmButton: false,
                                        onBeforeOpen: () => {
                                            $.ajax({
                                                method: "POST",
                                                data: {
                                                    nombreDocumento: nombreDocumento,
                                                    cuponDescuento: cuponDescuento,
                                                    idventa: idventa,
                                                    pagos: pagos,
                                                    dondeNosConocio: dondeNosConocio,
                                                    idUsuarioVenta: idUsuarioVenta,
                                                    pidioFactura: pidioFactura,
                                                    tipoPago: tipoPago,
                                                    esCotizacion: esCotizacion,
                                                    unidadNombre: unidadNombre,
                                                    idunidad: idunidad,
                                                    sinConvenioNombreCliente: sinConvenioNombreCliente,
                                                    sinConvenioEmailCliente: sinConvenioEmailCliente,
                                                    clienteTelefono: clienteTelefono,
                                                    porcentajeiva: porcentajeiva,
                                                    subtotal: subtotal,
                                                    iva: iva,
                                                    total: total,
                                                    beneficiarioNombre: beneficiarioNombre,
                                                    idcliente: idcliente,
                                                    convenio: convenio,
                                                    numeroEmpleado: numeroEmpleado,
                                                    beneficiario: beneficiario,
                                                    productos: productos,
                                                    meses: meses,
                                                    nombre: nombre,
                                                    apellidoP: apellidoP,
                                                    apellidoM: apellidoM,
                                                    notas: notas,
                                                    foliogarantia: foliogarantia
                                                },
                                                url: url,
                                                beforeSend: function (xhr) {
                                                }
                                            }).done(function (response) {

                                                console.log('prueba1');
                                                console.log(clienteTelefono);

                                                if (response.exito) {
                                                    $("#folio").val(response.folio);
                                                    Swal.fire('Listo!', 'Guardado Correctamente!', 'success');
                                                    $("#guardar-venta").hide();
                                                    $("#guardar-coitizacion").hide();
                                                    crearTicket(subtotal, iva, total, porcentajeiva, productos, unidadNombre, response.folio, esCotizacion);

                                                } else {
                                                    Swal.fire('Error!', response.msj, 'warning');
                                                }

                                            }).fail(function (jqXHR, textStatus) {

                                                Swal.fire("Error al imprimir: ", JSON.stringify(jqXHR) + JSON.stringify(textStatus), 'warning')
                                            });
                                        },
                                        onClose: () => {
                                        }
                                    }).then((result) => {
                                    })
                                }


                            } else if (todoBien) {
                                Swal.fire('Error al subir el documento', result.msj, 'warning')
                            } else {
                                Swal.fire('¡Datos incorrectos!', msj, 'warning')
                            }


                        });
                        /*   } else {
                                        msj = "Faltó Agregar pagos";
                                        todoBien=false;
                                      }*/
// })
// }
// }
// }
// revisamos si se sube bien el archivo
// var uploadResult=uploadFile();


                    }

                    function crearTicket(subtotal, iva, total, porcentajeiva, productos, unidadNombre, folio, esCotizacion) {

                        var apellidoP = $("#apellidoP").val();
                        var apellidoM = $("#apellidoM").val();
                        var nombre = $("#nombre").val();

                        var preciofijotipoventa = $("#totalventaiva").val();

                        var cliente = apellidoP + " " + apellidoM + " " + nombre;

                        var convenio = $("#convenio").val();
                        var numeroEmpleado = $("#numero-empleado").val();
                        var beneficiarioNombre = $("#beneficiario-nombre").val();
                        var beneficiario = $("#beneficiario-id").val();
                        var sinConvenioNombreCliente = $("#sin-convenio-nombre-cliente").val();
                        var clienteTelefono = $("#cliente-telefono").val();
                        var sinConvenioEmailCliente = $("#sin-convenio-email-cliente").val();
                        var cuponDescuento = $("#cupon-descuento").val();
                        var usuarioVenta = $("#usuario-venta").val();


                        var url = $("#url-ticket").val();
                        var msj = "";

                        console.log(productos);

                        if (productos.length > 0) {
                            if (convenio == "UAM" || convenio == "UAM Auditivos") {
                                if (cliente.length > 0) {
                                } else {
                                    msj = "Debe agregar el nombre del cliente";
                                }
                            }

                        } else {
                            msj = "Debe agregar por lo menos un producto";
                        }
                        if (msj == "") {
                            Swal.fire({
                                title: 'Guardando...',
                                html: '',
                                showConfirmButton: false,
                                onBeforeOpen: () => {


                                    $.ajax({
                                        method: "POST",
                                        data: {
                                            cuponDescuento: cuponDescuento,
                                            folio: folio,
                                            unidadNombre: unidadNombre,
                                            sinConvenioNombreCliente: sinConvenioNombreCliente,
                                            sinConvenioEmailCliente: sinConvenioEmailCliente,
                                            clienteTelefono: clienteTelefono,
                                            subtotal: subtotal,
                                            iva: iva,
                                            total: total,
                                            porcentajeiva: porcentajeiva,
                                            beneficiarioNombre: beneficiarioNombre,
                                            cliente: cliente,
                                            convenio: convenio,
                                            numeroEmpleado: numeroEmpleado,
                                            beneficiario: beneficiario,
                                            productos: productos,
                                            esCotizacion: esCotizacion,
                                            usuarioVenta: usuarioVenta,
                                            preciofijotipoventa: preciofijotipoventa

                                        },
                                        url: url,
                                        beforeSend: function (xhr) {
                                        }
                                    }).done(function (response) {
                                        if (response.exito) {
                                            $(".nueva-venta").addClass("d-none");
                                            $(".botones-finales").removeClass("d-none");

                                            Swal.fire({
                                                title: 'Listo',
                                                text: 'Ticket Generado Correctamente!',
                                                type: "success",

                                                showLoaderOnConfirm: true,

                                                allowOutsideClick: () => !Swal.isLoading()
                                            });

                                            /*  Swal.fire(
                                                                      'Listo!',
                                                                      'Ticket Generado Correctamente!',
                                                                      'success'
                                                              );*/
                                        } else {
                                            Swal.fire('Algo ocurrió!', 'Error al Imprimir !' + response.msj, 'warning')
                                        }
                                        Swal.fire({
                                            title: 'Listo',
                                            text: 'Guardado Correctamente!',

                                            type: "success",
                                            showLoaderOnConfirm: true,

                                            allowOutsideClick: () => !Swal.isLoading()
                                        });

                                        reset();
                                    }).fail(function (jqXHR, textStatus) {

                                        Swal.fire("Error al imprimir: ", JSON.stringify(jqXHR) + JSON.stringify(textStatus), 'warning')
                                    });
                                },
                                onClose: () => {
                                }
                            }).then((result) => {
                            })
                        } else {
                            Swal.fire('Datos incorrectos!', msj, 'warning')
                        }

                    }


                    function calcularTotal() {

                        preciototaltipoventa = $("#totalventaiva").val();
                        ivafijo = $("#ivafijo").val();
                        subtotalfijo = $("#subtotalfijo").val();
                        var element = document.getElementById("textopreciofijotipoventa");

                        if (preciototaltipoventa > 0) {

                            $("#total").val("$" + formatMoney(preciototaltipoventa));
                            $("#subtotal").val("$" + formatMoney(subtotalfijo));
                            $("#iva").val("$" + formatMoney(ivafijo));

                            element.classList.remove("d-none");


                        } else {

                            element.classList.add("d-none");
                            var total = 0;
                            var iva = 0;
                            var subtotal = 0;
                            $(".producto-seleccionado").each(function () {
                                var id = $(this).attr('id');
                                console.log("producto-seleccionado");
                                console.log("id " + id);
                                var productoTotal = quitarFormato($("#producto-total-" + id).val());
                                console.log("productoTotal " + productoTotal);
// subtotal+=productoTotal;

// iva+=(productoTotal*0.16);

                                productoTotalSinIva = productoTotal / 1.16;
                                subtotalAux = (productoTotalSinIva);
                                subtotal += subtotalAux;
                                ivaAux = (productoTotalSinIva * 0.16);
                                iva += ivaAux;
                                totalAux = (productoTotalSinIva + ivaAux);
                                total += totalAux;
                                console.log("subtotal " + subtotal);
                                console.log("iva " + iva);
                                console.log("total " + total);
                            });
                            console.log("subtotal " + subtotal);
                            $("#subtotal").val("$" + formatMoney(subtotal));
                            $("#iva").val("$" + formatMoney(iva));

                            $("#total").val("$" + formatMoney(total));

                        }

                    }


                    function agregarTratamiento(idtratamiento) {
                        var d = new Date();
                        var n = d.getMilliseconds();
                        var s = d.getSeconds();
                        var id = n + s;
                        var msj = "";
                        var tratamiento = $("#tratamiento").val();
                        var precio = quitarFormato($("#tratamiento-precio").val());


                        if (msj == "") {
                            var producto = '<tr id="producto-' + id + '" class="text-center">' + '<td ><input id="' + id + '" disabled class="producto-seleccionado" data-tipo="tratamiento" data-idproducto="' + idtratamiento + '" data-descripcion="" data-nombre="' + tratamiento + '" data-precio="' + precio + '"  type="hidden" />' + tratamiento + '</td>' + '<td><input id="producto-precio-' + id + '" class="form-control text-center valor-pesos" type="text" value="' + formatMoney(precio, 2, ".", ",") + '" onkeydown="calcularTotalProducto(' + id + ')" onkeyup="calcularTotalProducto(' + id + ')"> </td>' + '<td><input class="form-control text-center" id="producto-cantidad-' + id + '" type="text" class="form-control text-center valor-entero" value="1" onkeydown="validarNumero(this);calcularTotalProducto(' + id + ')" onkeyup="validarNumero(this);calcularTotalProducto(' + id + ')" /></td>' + '<td><input id="producto-total-' + id + '" disabled type="text" value="$' + precio + '" class="form-control text-center valor-pesos" ></td>' + '<td><button class="btn btn-danger" onclick="eliminar(' + id + ');return false;"><i class="fa fa-trash-o" aria-hidden="true"></i></button>' + '</td>' + '</tr>';
                            $("#productos").append(producto);

                            $("#producto-nombre").val("");
                            $("#producto-precio").val("");
                            $("#producto-cantidad").val(1);
                            $("#producto-total").val("");
                            $("#producto-codigo").val("");
                            $("#producto-nombre-id").val("");
                            $("#tratamiento").val("");
                            $("#tratamiento-precio").val("");
                            $("#tratamiento-id").val("");
// calcularTotalProducto(id);
                            calcularTotal();
                        } else {
                            Swal.fire("Producto no Encontrado: ", "Intente nuevamente", 'warning')
                        }
                    }

                    function agregarProducto(idstock) {

                        var producto = "";
                        var d = new Date();
                        var n = d.getMilliseconds();
                        var s = d.getSeconds();
                        var id = n + s;
                        var msj = "";
                        var productoNombre = $("#producto-nombre").val();
                        var productoPrecio = quitarFormato($("#producto-precio").val());

                        var productoDescripcion = $("#producto-descripcion").val();
                        var tipoproducto = $("#producto-tipoproducto").val();
                        var idproducto = $("#producto-idproducto").val();
                        var marca = $("#producto-marca").val();
                        var codigobarras = $("#producto-codigobarras").val();
                        var codigobarrasuniversal = $("#producto-codigobarrasuniversal").val();
                        var masivounico = $("#producto-masivounico").val();

                        if (checkProductos.has(codigobarras)) {

                            //msj = "El producto ya está en la lista";
                            $('#producto-nombre').val("");
                        } else
                            checkProductos.add(codigobarras);


                        if (productoNombre != "") {
                            if (productoPrecio != "") {
                            } else {
                                msj = "El precio del producto es necesario (No se agregó)";
                            }
                        } else {
                            msj = "El nombre del producto es necesario (No se Agregó)";
                        }

                        checkMasivoUnico = "";
                        if (masivounico == "1")
                            checkMasivoUnico = " disabled ";


                        if (msj == "") {
                            var producto = '<tr id="producto-' + id + '" class="text-center">' + '<td >' + '<input id="' + id + '" disabled class="producto-seleccionado" data-tipo="armazon" data-idstockventa="" data-idproductoventa="" data-idstock="' + idstock + '" data-descripcion="' + productoDescripcion + '" data-nombre="' + productoNombre + '" data-precio="' + productoPrecio + '" data-tipoproducto="' + tipoproducto + '" data-idproducto="' + idproducto + '" data-marca="' + marca + '"  data-codigobarras="' + codigobarras + '"  data-sku="' + codigobarrasuniversal + '"  data-masivounico="' + masivounico + '"  type="hidden" />' + productoNombre + '' + '</td>' + '<td>' + '<input id="producto-precio-' + id + '" class="form-control text-center" type="text" value="' + formatMoney(productoPrecio, 2, ".", ",") + '" onkeydown="calcularTotalProducto(' + id + ');calcularTotal()" onkeyup="calcularTotalProducto(' + id + ');calcularTotal()"> ' + '</td>' + '<td>' + '<input id="producto-descuento-' + id + '" class="form-control text-center" type="text" value="" onkeydown="calcularTotalProducto(' + id + ');calcularTotal()" onkeyup="calcularTotalProducto(' + id + ');calcularTotal()"> ' + '</td>' + '<td>' + '<input id="producto-final-' + id + '" class="form-control text-center" type="text" value="' + formatMoney(productoPrecio, 2, ".", ",") + '" onkeydown="calcularTotalProducto(' + id + ');calcularTotal()" onkeyup="calcularTotalProducto(' + id + ');calcularTotal()"> ' + '</td>' + '<td>' + '<input' + checkMasivoUnico + ' class="form-control text-center" id="producto-cantidad-' + id + '" type="text" class="form-control text-center" value="1" onkeydown="validarNumero(this);calcularTotalProducto(' + id + ');calcularTotal()" onkeyup="validarNumero(this);calcularTotalProducto(' + id + ');calcularTotal()" />' + '</td>' + '<td>' + '<input id="producto-total-' + id + '" disabled type="text" value="" class="form-control text-center" ></td><td><button class="btn btn-danger" onclick="eliminar(' + id + ');return false;">' + '<i class="fa fa-trash-o" aria-hidden="true"></i>' + '</button>' + '</td>' + '</tr>';
                            $("#productos").append(producto);

                            $("#producto-nombre").val("");
                            $("#producto-precio").val("");
                            $("#producto-cantidad").val(1);
                            $("#producto-total").val("");
                            $("#producto-codigo").val("");
                            $("#producto-nombre-id").val("");
                            $("#producto-tipoproducto").val("");
                            $("#producto-idproducto").val("");
                            $("#producto-marca").val("");
                            $("#producto-codigobarras").val("");
                            calcularTotalProducto(id);
                            calcularTotal();
                        } else { // $("#msj-error-armazon-codigo").html(msj);
                            Swal.fire(msj, "Intente nuevamente", 'warning')
                        }
                        comprobarSiHayProductosSeleccionados();
                        revisarPreciosDescuentosProductos();

                    }

                    function comprobarSiHayProductosSeleccionados() {
                        let hayProductos = false;

                        $(".producto-seleccionado").each(function () {
                            hayProductos = true;
                        });
                        if (hayProductos) {
                            $(".tabla-productos-seleccionados").removeClass("d-none");
                        } else {
                            $(".tabla-productos-seleccionados").addClass("d-none");
                        }

                    }

                    function eliminar(id) {
                        var productoTotal = 0;
                        var productoIva = 0;
                        var productoSubtotal = 0;
                        var total = 0;
                        var iva = 0;
                        var subtotal = 0;


                        Swal.fire({
                            title: 'Está seguro?',
                            text: "Se eliminará el producto de la lista!",
                            type: 'warning',
                            showCancelButton: true,
                            confirmButtonColor: '#3085d6',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'Si, Borrarlo!'
                        }).then((result) => {
                            if (result.value) {


                                $("#producto-" + id).remove();
                                calcularTotal();
                                comprobarSiHayProductosSeleccionados();

                                swal.close();
                                /* Swal.fire(
                                             'Listo!',
                                             'Eliminado Exitosamente.',
                                             'success'
                                           )*/
                            }
                        })


                    }


                    function formatMoney(amount, decimalCount = 2, decimal = ".", thousands = ",") {
                        try {
                            decimalCount = Math.abs(decimalCount);
                            decimalCount = isNaN(decimalCount) ? 2 : decimalCount;

                            const negativeSign = amount < 0 ? "-" : "";

                            let i = parseInt(amount = Math.abs(Number(amount) || 0).toFixed(decimalCount)).toString();
                            let j = (i.length > 3) ? i.length % 3 : 0;

                            return negativeSign + (j ? i.substr(0, j) + thousands : '') + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + thousands) + (decimalCount ? decimal + Math.abs(amount - i).toFixed(decimalCount).slice(2) : "");
                        } catch (e) {
                            console.log(e)
                        }
                    }
                </script>
                <script>
                    input : 'text',
                        function imprimir2() {
                            PrintElem("ticket");

                        }

                    function PrintElem(elem) {
                        var mywindow = window.open('', 'PRINT', 'height=400,width=800');
                        mywindow.document.write('<html><head><title>' + document.title + '</title>');
                        mywindow.document.write('</head><body >');
                        mywindow.document.write('<h1>' + document.title + '</h1>');
                        mywindow.document.write(document.getElementById(elem).innerHTML);
                        mywindow.document.write('</body></html>');
                        mywindow.document.close(); // necessary for IE >= 10
                        mywindow.focus(); // necessary for IE >= 10*/
                        mywindow.print();
                        mywindow.close();

                        return true;
                    }


                    function revisarPreciosDescuentosProductos() {
                        console.log("entra a revisar tipoVenta");
// vemos si hay descuntos
                        var descuentoproductosalmacenables = $("#descuentoproductosalmacenables").val();
                        var descuentoservicios = $("#descuentoservicios").val();
                        var preciofijoproductosalmacenables = $("#preciofijoproductosalmacenables").val();
                        var preciofijoservicios = $("#preciofijoservicios").val();

                        console.log("descuentoproductosalmacenables " + descuentoproductosalmacenables);
                        console.log("descuentoservicios " + descuentoservicios);
                        console.log("preciofijoproductosalmacenables " + preciofijoproductosalmacenables);
                        console.log("preciofijoservicios " + preciofijoservicios);
                        $(".producto-seleccionado").each(function () { //
                            console.log("*********************************");
                            var precio = $(this).data("precio");
                            var tipoproducto = $(this).data("tipoproducto");
                            var id = $(this).attr("id");
                            console.log("tipoproducto " + tipoproducto);
                            console.log("precio " + precio);
                            console.log("id " + id);
                            if (tipoproducto == "1") {
                                if (descuentoproductosalmacenables > 0) {
                                    $("#producto-descuento-" + id).val(descuentoproductosalmacenables);
                                } else if (preciofijoproductosalmacenables != 0) {
                                    $("#producto-precio-" + id).val(preciofijoproductosalmacenables);
                                }
                            } else if (tipoproducto == "2") {
                                if (descuentoservicios > 0) {
                                    $("#producto-descuento-" + id).val(descuentoservicios);
                                } else if (preciofijoservicios != 0) {
                                    $("#producto-precio-" + id).val(preciofijoservicios);
                                }
                            }
                            calcularTotalProducto(id);
                            calcularTotal();
                        });
                    }

                    function cambiarFormulario(select) {

                        var idtipoventa = $("#convenio").val();

// debemos traer la infromación para poder hacer los claculos

                        var url = $("#url-obtener-informacion-tipo-venta").val();
                        console.log("url " + url);
                        $.ajax({
                            method: "POST",
                            data: {
                                idtipoventa: idtipoventa
                            },
                            url: url,
                            beforeSend: function (xhr) {
                            }
                        }).done(function (response) {
                            console.log(response);
                            if (response.exito) {
                                console.log("asignando valores " + response.Tipoventa.preciofijoproductosalmacenables);
// console.log(response.Tipoventa.totalventaconiva);
                                if (response.Tipoventa.totalventaconiva == null)
                                    response.Tipoventa.totalventaconiva = 0;


                                total = parseFloat(response.Tipoventa.totalventaconiva);
                                iva = (total * 0.16) / 1.16;
                                subtotal = total - iva;

                                $("#totalventaiva").val(total);
                                $("#ivafijo").val(iva);
                                $("#subtotalfijo").val(subtotal);

                                $("#documentoobligatoriocerrarventa").val(response.Tipoventa.documentoobligatoriocerrarventa);

                                var notaConSaltosDeLinea = "";
                                if (response.Tipoventa.nota != null) {
                                    notaConSaltosDeLinea = response.Tipoventa.nota.replace(/\n/g, "<br>");
                                }

                                $("#tipo-venta-nota").html(notaConSaltosDeLinea);
                                $("#msj-tipo-venta").html(notaConSaltosDeLinea);
                                $("#tipo-venta-nota").html(response.Tipoventa.nota);
                                $("#msj-tipo-venta").html(response.Tipoventa.nota);
                                $("#descuentoproductosalmacenables").val(response.Tipoventa.descuentoproductosalmacenables);
                                $("#descuentoservicios").val(response.Tipoventa.descuentoservicios);
                                $("#preciofijoproductosalmacenables").val(response.Tipoventa.preciofijoproductosalmacenables);
                                $("#preciofijoservicios").val(response.Tipoventa.preciofijoservicios);
                                revisarPreciosDescuentosProductos();

                            } else {
                                $("#msj-tipo-venta").html(response.msj);
                            }

                        }).fail(function (jqXHR, textStatus) {

                            Swal.fire("Error al obtener datos del tipo de venta ", JSON.stringify(jqXHR) + JSON.stringify(textStatus), 'warning')
                        });


                        $(".datos-extra").addClass("d-none");
                        valor = $(select).val();
                        mostrarnumeroempleado = $('option:selected', select).data('mostrarnumeroempleado');
                        mostrarbeneficiario = $('option:selected', select).data('mostrarbeneficiario');
                        mostrarunidadprocedencia = $('option:selected', select).data('mostrarunidadprocedencia');


                        if (mostrarnumeroempleado == "1") {
                            $("#contenedor-numero-empleado").removeClass("d-none");
                        }
                        if (mostrarbeneficiario == "1") {
                            $("#contenedor-beneficiario").removeClass("d-none");
                        }
                        if (mostrarunidadprocedencia == "1") {
                            $("#contenedor-unidad-procedencia").removeClass("d-none");
                        }

                    }

                    function delay(callback, ms) {
                        var timer = 0;
                        return function () {
                            var context = this,
                                args = arguments;
                            clearTimeout(timer);
                            timer = setTimeout(function () {
                                callback.apply(context, args);
                            }, ms || 0);
                        };
                    }


                    function buscarCotizacion() {

                        var numeroFolio = quitarFormato($("#busqueda-numero-folio").val());
                        var urlNuevaVenta = $("#url-nueva-venta").val();
                        var urlBuscarVenta = $("#url-buscar-venta").val();


                        reset();

                        $.ajax({
                            method: "POST",
                            data: {
                                numeroFolio: numeroFolio
                            },
                            url: urlBuscarVenta,
                            beforeSend: function (xhr) {
                            }
                        }).done(function (response) {
                            console.log(response);
                            if (response.exito) {
                                document.location.href = urlNuevaVenta + "/" + numeroFolio;
                            } else {
                                $("#modal-busqueda-msj").html(response.msj);
                            }

                        }).fail(function (jqXHR, textStatus) {

                            Swal.fire("Error al buscar el ticket: ", JSON.stringify(jqXHR) + JSON.stringify(textStatus), 'warning')
                        });


                    }


                    function agregarProductoDB(productoNombre, productoPrecio, productoDescripcion, descuento, modelo, marca, tipoproducto, idproducto, idproductoventa, idstockventa, idstock, marca, codigobarras) {
                        var producto = "";
                        var d = new Date();
                        var n = d.getMilliseconds();
                        var s = d.getSeconds();
                        var id = n + s;
                        var msj = "";
                        var esCotizacion = $("#escotizacion").val();
                        var idventa = $("#idventa").val();
// para quitar la opcion de eliminra si es venta

                        var disabled = "disabled";

                        if (esCotizacion != "1" && idventa != "") {
                            disabled = "disabled";
                        }

                        var productoDescripcion = "";
                        if (tipoproducto == "2") {
                            productoDescripcion = modelo
                        } else {
                            productoDescripcion = marca + " / " + modelo + " / " + productoDescripcion;
                        }

                        /*var productoNombre=$("#producto-nombre").val();
                              var productoPrecio=quitarFormato($("#producto-precio").val());

                              var productoDescripcion=$("#producto-descripcion").val();*/

                        if (productoNombre != "") {
                            if (productoPrecio != "") {
                            } else {
                                msj = "El precio del producto es necesario (No se agregó)";
                            }
                        } else {
                            msj = "El nombre del producto es necesario (No se Agregó)";
                        }


                        if (msj == "") {
                            var producto = '<tr id="producto-' + id + '" class="text-center">' + '<td >' + '<input id="' + id + '" disabled class="producto-seleccionado" data-idstockventa="' + idstockventa + '" data-idstock="' + idstock + '" data-idproductoventa="' + idproductoventa + '" data-tipo="armazon" data-idproducto="' + idproducto + '" data-descripcion="' + productoDescripcion + '" data-nombre="' + productoNombre + '" data-precio="' + productoPrecio + '"   data-tipoproducto="' + tipoproducto + '" data-marca="' + marca + '" data-codigobarras="' + codigobarras + '"  type="hidden" />' + productoDescripcion + '' + codigobarras + '</td>' + '<td>' + '<input id="producto-precio-' + id + '" class="form-control text-center" type="text" ' + disabled + ' value="' + formatMoney(productoPrecio, 2, ".", ",") + '" onkeydown="calcularTotalProducto(' + id + ');calcularTotal()" onkeyup="calcularTotalProducto(' + id + ');calcularTotal()"> ' + '</td>' + '<td>' + '<input id="producto-descuento-' + id + '" class="form-control text-center" ' + disabled + ' type="text" value="' + descuento + '" onkeydown="calcularTotalProducto(' + id + ');calcularTotal()" onkeyup="calcularTotalProducto(' + id + ');calcularTotal()"> ' + '</td>' + '<td>' + '<input id="producto-final-' + id + '" class="form-control text-center" type="text" ' + disabled + ' value="' + formatMoney(productoPrecio, 2, ".", ",") + '" onkeydown="calcularTotalProducto(' + id + ');calcularTotal()" onkeyup="calcularTotalProducto(' + id + ');calcularTotal()"> ' + '</td>' + '<td>' + '<input class="form-control text-center" id="producto-cantidad-' + id + '" type="text" ' + disabled + ' class="form-control text-center" value="1" onkeydown="validarNumero(this);calcularTotalProducto(' + id + ');calcularTotal()" onkeyup="validarNumero(this);calcularTotalProducto(' + id + ');calcularTotal()" />' + '</td>' + '<td>' + '<input id="producto-total-' + id + '" disabled type="text" ' + disabled + ' value="" class="form-control text-center" ></td><td>';
                            if (esCotizacion == "1" && idventa != "") {
                                producto += '<button class="btn btn-danger" onclick="eliminar(' + id + ');return false;"><i class="fa fa-trash-o" aria-hidden="true"></i>' + '</button>';
                            }

                            producto += '</td>' + '</tr>';
                            $("#productos").append(producto);

                            $("#producto-nombre").val("");
                            $("#producto-precio").val("");
                            $("#producto-cantidad").val(1);
                            $("#producto-total").val("");
                            $("#producto-codigo").val("");
                            $("#producto-nombre-id").val("");
                            calcularTotalProducto(id);
                            calcularTotal();
                        } else {
                            $("#msj-error-armazon-codigo").html(msj);
                            /*Swal.fire(
                                            msj,
                                      "Intente nuevamente" ,
                                      'warning'
                                    )*/
                        }
                        comprobarSiHayProductosSeleccionados();
                        revisarPreciosDescuentosProductos();
                    }

                    function agregarPagoDB(anticipo, tipoPagoAnticipo, idpago) { /*let anticipo=parseFloat(quitarFormato($("#anticipo").val()));
      let tipoPagoAnticipo=$("#tipo-pago-anticipo").val();*/
                        let id = Date.now() + Math.floor(Math.random() * 100);
                        if (anticipo > 0) {
                            if (tipoPagoAnticipo != "") {
                                $("#tabla-body-pagos").append('<tr id="' + id + '">\n' + '                            <td>' + '<input id="valor-' + id + '" value="' + anticipo + '" type="text" class="form-control-plaintext valor-pesos pagos" data-idpago="' + idpago + '" data-tipopagoanticipo="' + tipoPagoAnticipo + '" readonly></td>\n' + '                            <td>' + tipoPagoAnticipo + '</td>\n' + '                            <td>\n' + '                              <button class="btn "  style="background:#fff"  onclick="quitarPago(' + id + ')">\n' + '                                <img src="/img/icon-remove.jpg" alt="" width="25">\n' + '                              </button>\n' + '                            </td>\n' + '                          </tr>');
                                ponerFormatoPesos("#valor-" + id);
                                calcularPagos();
                                $("#anticipo").val("");
                                $("#tipo-pago-anticipo").val("");
                            } else {
                                $("#tipo-pago-anticipo").focus();
                                Swal.fire('Campo(s) vacios!', 'debe seleccionar un tipo de pago!', 'warning');
                            }
                        } else {
                            $("#anticipo").focus();
                            Swal.fire('Campo(s) vacios!', 'La cantidad debe ser mayor a cero!', 'warning');
                        }


                    }

                    function buscarCupon() {

                        var cuponDescuento = $("#cupon-descuento").val();
                        var url = $("#url-buscar-cupon").val();


                        $.ajax({
                            method: "POST",
                            data: {
                                cuponDescuento: cuponDescuento
                            },
                            url: url,
                            dataType: "json",
                            beforeSend: function (xhr) {
                            }
                        }).done(function (response) {
                            console.log(response);

                            if (response.exito) {


                                response.marcas.forEach(function (marca) {

                                    $(".producto-seleccionado").each(function () {
                                        nombreMarca = $(this).data("marca");
                                        id = $(this).attr("id");


                                        if (marca.nombre == nombreMarca) {
                                            console.log("marca.nombre " + marca.nombre);
                                            console.log("nombreMarca " + nombreMarca);
                                            console.log("marca.porcentajedescuento " + marca.porcentajedescuento);
                                            console.log("id " + id);
// aplicamos el descuento
                                            $("#producto-descuento-" + id).val(marca.porcentajedescuento);
                                            calcularTotalProducto(id);
                                            calcularTotal();
                                        }
                                    });


                                });
                                $(".agregar-cupon").addClass("d-none");
                                $(".quitar-cupon").removeClass("d-none");
                                $("#cupon-descuento").attr("disabled", true);
                            } else {
                                Swal.fire(response.msj, '', 'warning');
                            }

                        }).fail(function (jqXHR, textStatus) {

                            Swal.fire("Error al buscar el ticket: ", JSON.stringify(jqXHR) + JSON.stringify(textStatus), 'warning')
                        });


                    }

                    function quitarCupon() {


                        var cuponDescuento = $("#cupon-descuento").val();


                        var url = $("#url-quitar-cupon").val();

                        var idventa = $("#idventa").val();
                        if (idventa != "") {
                            $.ajax({
                                method: "POST",
                                data: {
                                    idventa: idventa,
                                    cuponDescuento: cuponDescuento
                                },
                                url: url,
                                dataType: "json",
                                beforeSend: function (xhr) {
                                }
                            }).done(function (response) {
                                console.log(response);

                                if (response.exito) {

                                    $(".producto-seleccionado").each(function () { // reseteamos todos los descuentos
                                        id = $(this).attr("id");
                                        $("#producto-descuento-" + id).val("");
                                        calcularTotalProducto(id);
                                        calcularTotal();
                                    });
                                    $("#cupon-descuento").attr("disabled", false);
                                    $(".agregar-cupon").removeClass("d-none");
                                    $(".quitar-cupon").addClass("d-none");
                                    $("#cupon-descuento").val("");
                                } else {
                                    Swal.fire(response.msj, '', 'warning');
                                }

                            }).fail(function (jqXHR, textStatus) {

                                Swal.fire("Error al buscar el ticket: ", JSON.stringify(jqXHR) + JSON.stringify(textStatus), 'warning')
                            });
                        } else {
                            $("#cupon-descuento").attr("disabled", false);
                            $(".agregar-cupon").removeClass("d-none");
                            $(".quitar-cupon").addClass("d-none");
                            $("#cupon-descuento").val("");
                        }
                    }

                    function cargarMeses() {

                        var tipoPago = $("#tipo-pago-anticipo").val();
                        var total = $("#total").val();
                        total = quitarFormato(total);

                        document.getElementById('anticipo').value = "";
                        document.getElementById('anticipo').disabled = false;

                        $("#meses").addClass("d-none");
                        anticipo = document.getElementById('anticipo-total-pagado').value
                        anticipo = quitarFormato(anticipo);

                        if (tipoPago == "tarjeta crédito") {

                            document.getElementById('anticipo').value = total - anticipo;
                            document.getElementById('anticipo').disabled = true;
                            ponerFormatoPesos("#anticipo");
                            $("#meses").removeClass("d-none");

                        }
                        $.ajax({url: "{{ path('obtener-meses') }}", dataType: "html"}).done(function (html) {
                            $("#meses").html(html);
                        }).fail(function () {
                            alert("error");
                        });
                    }

                    function buscarVenta() {

                        var numeroFolio = quitarFormato($("#folio-venta-garantia").val());
                        var urlBuscarVenta = $("#url-buscar-venta").val();


                        $.ajax({
                            method: "POST",
                            data: {
                                numeroFolio: numeroFolio
                            },
                            url: urlBuscarVenta,
                            dataType: "json",
                            beforeSend: function (xhr) {
                            }
                        }).done(function (response) {
                            console.log(response);

                            if (response.exito) {
                                $(".agregar-venta").addClass("d-none");
                                $(".quitar-venta").removeClass("d-none");
                                $("#folio-venta-garantia").attr("disabled", true);
                            } else {
                                Swal.fire(response.msj, '', 'warning');
                            }

                        }).fail(function (jqXHR, textStatus) {

                            Swal.fire("Error al buscar el ticket: ", JSON.stringify(jqXHR) + JSON.stringify(textStatus), 'warning')
                        });


                    }

                    function quitarVenta() {

                        $(".agregar-venta").removeClass("d-none");
                        $(".quitar-venta").addClass("d-none");
                        $("#folio-venta-garantia").attr("disabled", false);

                    }
                </script>
            {% endblock %}
