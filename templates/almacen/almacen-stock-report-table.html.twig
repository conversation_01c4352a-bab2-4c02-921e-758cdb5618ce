
<table class="table" id="stock-report-table">

    <thead>
        <th>Sucursal/bodega/campaña</th>
        <th>Categoría</th>
        <th>Subcategoría</th>
        <th>Marc<PERSON></th>
        <th>Modelo</th>
        <th>Color</th>
        <th>Número de existencias</th>
        <th>Código de barras</th>
        <th>Tags</th>
    </thead>

    <tbody>
        {% for stock in stockData %}

            <tr>
                <td>{{stock.branch}}</td>
                <td>{{stock.category}}</td>
                <td>{{stock.subcategory}}</td>
                <td>{{stock.brand}}</td>
                <td>{{stock.model}}</td>
                <td>{{stock.Color}}</td>
                <td>{{stock.quantity}}</td>
                <td>

                {% if stock.sku != "" %}
                    {{stock.sku}}
                {% else %}
                    {{stock.barcode}}
                {% endif %}   
                </td>
                <td class="text-center">
                {% if tagsPerProducts[stock.idproducto] is defined %}
                    {{tagsPerProducts[stock.idproducto]}}
                {% else %}
                    -
                {% endif %}
                </td>

            </tr>

        {% endfor %}
    </tbody>

</table>

<script>
    $(document).ready(function() {
        $('#stock-report-table').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json'
            },
            dom: 'Bfrtip',
            buttons: [
                {
                    extend: 'excelHtml5',
                    title: 'Reporte de Stock',
                    text: 'Exportar a Excel',
                    className: 'btn btn-success'
                }
            ]
        });
    });
</script>
    