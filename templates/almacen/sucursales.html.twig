<link rel="stylesheet" href="{{ asset('/css/puntodeventa/filter_bussines.css') }}">

<div class="row g-4">

  <!-- Sucursales -->
  <div class="col-12 col-md-6 col-lg-4">
    <div class="card shadow-sm border-0 h-100">
      <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <span class="fw-bold text-uppercase small">Sucursales</span>
        <div class="form-check form-switch m-0">
          <input class="form-check-input" type="checkbox" id="todasSucursales" onclick="toggleChecks('sucursal', this)" checked>
          <label class="form-check-label small" for="todasSucursales">Todas</label>
        </div>
      </div>
      <div class="card-body p-3" style="max-height: 260px; overflow-y: auto;">
        <fieldset class="row row-cols-1 g-1">
          {% for sucursal in sucursales %}
          <div class="col">
            <div class="form-check">
              <input type="checkbox" class="form-check-input" id="checkbox-sucursal-{{ sucursal.idsucursal }}" name="sucursal" value="{{ sucursal.idsucursal }}" checked>
              <label class="form-check-label" for="checkbox-sucursal-{{ sucursal.idsucursal }}">{{ sucursal.nombre }}</label>
            </div>
          </div>
          {% endfor %}
        </fieldset>
      </div>
    </div>
  </div>

  <!-- Bodegas -->
  <div class="col-12 col-md-6 col-lg-4">
    <div class="card shadow-sm border-0 h-100">
      <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <span class="fw-bold text-uppercase small">Bodegas</span>
        <div class="form-check form-switch m-0">
          <input class="form-check-input" type="checkbox" id="todasBodegas" onclick="toggleChecks('bodega', this)" checked>
          <label class="form-check-label small" for="todasBodegas">Todas</label>
        </div>
      </div>
      <div class="card-body p-3" style="max-height: 260px; overflow-y: auto;">
        <fieldset class="row row-cols-1 g-1">
          {% for bodega in bodegas %}
          <div class="col">
            <div class="form-check">
              <input type="checkbox" class="form-check-input" id="checkbox-bodega-{{ bodega.idsucursal }}" name="bodega" value="{{ bodega.idsucursal }}" checked>
              <label class="form-check-label" for="checkbox-bodega-{{ bodega.idsucursal }}">{{ bodega.nombre }}</label>
            </div>
          </div>
          {% endfor %}
        </fieldset>
      </div>
    </div>
  </div>

  <!-- Campañas -->
  <div class="col-12 col-md-6 col-lg-4">
    <div class="card shadow-sm border-0 h-100">
      <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <span class="fw-bold text-uppercase small">Campañas</span>
        <div class="form-check form-switch m-0">
          <input class="form-check-input" type="checkbox" id="todasCampanas" onclick="toggleChecks('campaña', this)" checked>
          <label class="form-check-label small" for="todasCampanas">Todas</label>
        </div>
      </div>
      <div class="card-body p-3" style="max-height: 260px; overflow-y: auto;">
        <fieldset class="row row-cols-1 g-1">
          {% for campaña in campañas %}
          <div class="col">
            <div class="form-check">
              <input type="checkbox" class="form-check-input" id="checkbox-campana-{{ campaña.idsucursal }}" name="campaña" value="{{ campaña.idsucursal }}" checked>
              <label class="form-check-label" for="checkbox-campana-{{ campaña.idsucursal }}">{{ campaña.nombre }}</label>
            </div>
          </div>
          {% endfor %}
        </fieldset>
      </div>
    </div>
  </div>

</div>

<script>
  // Función para alternar todos los checkboxes de un grupo
  function toggleChecks(group, checkbox) {
    const checkboxes = document.querySelectorAll(`input[name="${group}"]`);
    checkboxes.forEach(chk => {
      chk.checked = checkbox.checked; // Cambia el estado de todos los checkboxes según el valor del checkbox principal
    });
  }
</script>
