<!--
<div class="cont">
    <label class="filter-title">TAGS:</label>
    <div class="col-md-12 miLista">
        <fieldset id="idtags" >
            <label class="check" for="allTags" >
                <input type="checkbox" name="allTags" id="allTags" onClick="toggleTags(this)">
            TODOS</label>
            {% for tag in tags %}
                <label class="check" for="tag-{{ tag.idtag  }}" > 
                    <input type="checkbox" id="tag-{{ tag.idtag }}" name="tag" value="{{ tag.idtag }}">
                {{ tag.name }}</label>
            {% endfor %}
        </fieldset>
    </div>
</div>
-->

<script language="JavaScript">
    function toggleTags(source) 
    {
        checkboxes = document.getElementsByName('tag');
        for(var i=0, n=checkboxes.length;i<n;i++) {
            checkboxes[i].checked = source.checked;
        }
    }
</script>
