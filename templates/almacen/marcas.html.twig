<!-- Switch para seleccionar todas -->
<div class="card-header bg-light d-flex justify-content-between align-items-center">
    <div class="form-check form-switch m-0">
        <input type="checkbox" class="form-check-input" id="todasMarcas" onclick="toggleMarcas(this)" checked>
        <label class="form-check-label fw-semibold" for="todasMarcas">Todas</label>
    </div>
</div>

<!-- Marcas individuales -->
<div class="card-body" style="max-height: 300px; overflow-y: auto;">
    <fieldset>
        {% for marca in marcas %}
            <div class="col">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="checkbox-marca-{{ marca.idmarca }}" name="marca" value="{{ marca.idmarca }}" checked>
                    <label class="form-check-label" for="checkbox-marca-{{ marca.idmarca }}">{{ marca.nombre }}</label>
                </div>
            </div>
        {% endfor %}
    </fieldset>
</div>

<script>
function toggleMarcas(source) {
    const checkboxes = document.querySelectorAll('input[name="marca"]');
    checkboxes.forEach(cb => cb.checked = source.checked);
}
</script>
