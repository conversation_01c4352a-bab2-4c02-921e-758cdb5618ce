<div class="card-header bg-light d-flex justify-content-between align-items-center">
    <div class="form-check form-switch m-0">
        <input type="checkbox" class="form-check-input" name="todasCategorias" id="todasCategorias" onclick="toggleCategorias(this)" checked>
        <label class="form-check-label" for="todasCategorias">Todas</label>
    </div>
</div>

<div class="card-body" style="max-height: 250px; overflow-y: auto;">
    <fieldset>
        {% for categoria in categorias %}
            <div class="form-check mb-1">
                <input type="checkbox" class="form-check-input" id="checkbox-categoria-{{ categoria.idcategoria }}" name="categoria" value="{{ categoria.idcategoria }}" checked>
                <label class="form-check-label" for="checkbox-categoria-{{ categoria.idcategoria }}">{{ categoria.nombre }}</label>
            </div>
        {% endfor %}
    </fieldset>
</div>

<script>
function toggleCategorias(source) {
  const checkboxes = document.getElementsByName('categoria');
  checkboxes.forEach(cb => cb.checked = source.checked);
}
function toggleChecks(name, source) {
  document.querySelectorAll(`input[name="${name}"]`).forEach(cb => cb.checked = source.checked);
}
</script>
