
<div class="mod " id="modalDetalle" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" style="z-index: 99999;">
    <div class="mod-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="exampleModalLabel">Detalle</h1>
                <button type="button" class="btn-close" data-bs-dismiss="mod" aria-label="Cerrar"></button>
            </div>
            <div class="modal-body">
                <div class="row justify-content-md-center">
                    <div class="col-md-12">
                        <table class="table" id="tablaDetalles">
                            <thead>
                                <tr class="text-start">
                                    <th class="text-start" scope="col">#</th>
                                    <th class="text-start" scope="col">Cantidad</th>
                                    <th class="text-start" scope="col">Código</th>
                                    <th class="text-start" scope="col">Modelo</th>
                                    <th class="text-start" scope="col">Sucursal</th>
                                </tr>
                            </thead>
                            <tbody id = "tableBodyDetalles"></tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="mod">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<div class="row d-flex justify-content-center">
    <div class="col-md-12 text-center mt-4 mb-4">
        <h2 class="titulos-reportes">Inventario por Marca</h2>
    </div>
    <div class="col-md-6 text-center">
        <div class="card mb-4">
            <div class="card-body">
                <div id="grafica-inventario-marca"></div>
            </div>
        </div>
    </div>
    <div class="col-md-6 text-center">
        <div class="card mb-4">
            <div class="card-body">
                <table class="table table-borderless" id="marcaTable">
                    <thead>
                        <tr>
                            <th>Marca</th>
                            <th>Cantidad</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                    {% set total = 0 %}
                    {% for s in stock %}
                        <tr>
                            <td>{{ s.marca }}</td>
                            <td>{{ s.cantidad }}</td>
                            <td>
                            <button id="{{ s.marca }}"  type="button" class="btn btn-success mt-5 btn-nuevo-cliente" data-bs-toggle="mod" data-bs-target="#modalDetalle" onclick="mostrarDetallesMarca(this.id)">Detalles</button>
                            </td>
                            {% set total = total + s.cantidad %} 
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
                <div class="d-flex justify-content-center align-items-center">
                    <h5 class="me-3"> Total: {{ total }} <h5>
                    <button id="Total"  type="button" class="btn btn-info" data-bs-toggle="mod" data-bs-target="#modalDetalle" onclick="mostrarDetallesMarca(this.id)">Detalles</button>
                </div>
            </div>
        </div>
    </div>
</div>
<hr>

<hr>
<div class="row d-flex justify-content-center">
    <div class="col-md-12 text-center mt-4 mb-4">
        <h2 class="titulos-reportes">Inventario por categoría</h2>
    </div>
    <div class="col-md-6 text-center">
        <div class="card mb-4">
            <div class="card-body">

                <div id="grafica-inventario-categoria"></div>
            </div>
        </div>
    </div>
    <div class="col-md-6 text-center">
        <div class="card mb-4">
            <div class="card-body">
                <table class="table table-borderless" id="categoriaTable">
                    <thead>
                        <tr>
                            <th>Categoría</th>
                            <th>Cantidad</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                    {% set total = 0 %}
                    {% for s in stockCategoria %}
                        <tr>
                            <td>{{ s.categoria }}</td>
                            <td>{{ s.cantidad }}</td>
                            <td>
                            <button id="{{ s.categoria }}"  type="button" class="btn btn-success mt-5 btn-nuevo-cliente" data-bs-toggle="mod" data-bs-target="#modalDetalle" onclick="mostrarDetallesCategoria(this.id)">Detalles</button>
                            </td>
                            {% set total = total + s.cantidad %}
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
                <div class="d-flex justify-content-center align-items-center">
                    <h5 class="me-3"> Total: {{ total }} <h5>
                    <button id="Total"  type="button" class="btn btn-info" data-bs-toggle="mod" data-bs-target="#modalDetalle" onclick="mostrarDetallesCategoria(this.id)">Detalles</button>
                </div>
            </div>
        </div>
    </div>
</div>
<hr>


<hr>
<div class="row d-flex justify-content-center">
    <div class="col-md-12 text-center mt-4 mb-4">
        <h2 class="titulos-reportes">Inventario por Subcategoría</h2>
    </div>
    <div class="col-md-6 text-center">
        <div class="card mb-4">
            <div class="card-body">
                <div id="grafica-inventario-subcategoria"></div>
            </div>
        </div>
    </div>
    <div class="col-md-6 text-center">
        <div class="card mb-4">
            <div class="card-body">
                <table class="table table-borderless" id="subcategoriaTable">
                    <thead>
                        <tr>
                            <th>Subcategoría</th>
                            <th>Cantidad</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                    {% set total = 0 %}
                    {% for sc in stockSubcategoria %}
                        <tr>
                            <td>{{ sc.subcategoria }}</td>
                            <td>{{ sc.cantidad }}</td>
                            <td>
                            <button id="{{ sc.subcategoria }}"  type="button" class="btn btn-success mt-5 btn-nuevo-cliente" data-bs-toggle="mod" data-bs-target="#modalDetalle" onclick="mostrarDetallesSubcategoria(this.id)">Detalles</button>
                            </td>
                            {% set total = total + sc.cantidad %}
                        </tr>
                    {% endfor %}
                    <tr>
                        <td>Total</td>
                        <td>{{ total }}</td>
                        <td>
                            
                        </td>
                    </tr>
                    </tbody>
                </table>
                <div class="d-flex justify-content-center align-items-center">
                    <h5 class="me-3"> Total: {{ total }} <h5>
                    <button id="Total"  type="button" class="btn btn-info" data-bs-toggle="mod" data-bs-target="#modalDetalle" onclick="mostrarDetallesSubcategoria(this.id)">Detalles</button>
                </div>
            </div>
        </div>
    </div>
</div>



  <div class="mod " id="SubcategoryChartModal" tabindex="-1" role="dialog" aria-labelledby="modal-agregar-beneficiarioLabel" aria-hidden="true">
    <div class="mod-dialog" role="document">
      <div class="modal-content" style="border-radius:10px">
        <div class="modal-header bg-primary">
          <h5 class="modal-title" id="exampleModalLabel"></h5>
          <button type="button" class="close btn-close-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>

        <div class="modal-body" id="modal-visor-body">
            <div class="col-md-6 text-center">
                <div class="card mb-4">
                    <div class="card-body">
                        <div id="subcategory-chart-container"></div>
                    </div>
                </div>
            </div>

             <div class="col-md-6 text-center">
                <div class="card mb-4">
                    <div class="card-body">
                        <table class="table table-borderless" id="subcategory-table-filtered">
                            <thead>
                                <tr>
                                    <th>Subcategoría</th>
                                    <th>Cantidad</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody id="subcategory-tablebody-filtered">

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
          

        </div>
        <div class="modal-footer text-center">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
        </div>
      </div>
    </div>
  </div>




<script>
    $(document).ready( function () {
        $('#marcaTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ marcas por página',
                
            },
        });
        $('#categoriaTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ categorías por página',
                
            },
        });
        $('#subcategoriaTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ subcategorías por página',
                
            },
        });

        var cantidades=[];
        var marcas=[];
        var cantidadesMarcaSinLinea=[];
        var marcasSinLinea=[];
        var cantidadesCategoria=[];
        var categorias=[];
        var categoriasIDs=[];
        var cantidadesSubcategoria=[];
        var subcategorias=[];

        {% for s in stock %}
        cantidades.push({{s.cantidad}});
        marcas.push("{{s.marca}}");
        {% if s.marca !="LINEA" and s.marca !="LÍNEA" %}
        cantidadesMarcaSinLinea.push({{s.cantidad}});
        marcasSinLinea.push("{{s.marca}}");
            {% endif %}
        {% endfor %}

        {% for s in stockCategoria %}
        cantidadesCategoria.push({{s.cantidad}});
        categorias.push("{{s.categoria}}");
        categoriasIDs.push({{s.idclase}});
        {% endfor %}

        {% for sc in stockSubcategoria %}
        cantidadesSubcategoria.push({{sc.cantidad}});
        subcategorias.push("{{sc.subcategoria}}");
        {% endfor %}


        var options = {
            series: cantidades,
            labels: marcas,
            chart: {
                width: "100%",
                type: 'donut',
            },
            dataLabels: {
                enabled: false
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        show: false
                    }
                }
            }],
            legend: {
                position: 'right',
                offsetY: 0,
                height: 230,
            }
        };

        var chart = new ApexCharts(document.querySelector("#grafica-inventario-marca"), options);
        chart.render();

        var options = {

            chart: {
                width: "100%",
                type: 'pie',
                events: {
                    dataPointSelection: (event, chartContext, config) => {
                        SubcategoryGraph(categoriasIDs[config.dataPointIndex]);
                    }
                },
            },
            labels: categorias,
            series: cantidadesCategoria,
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }],
        };

        var chart = new ApexCharts(document.querySelector("#grafica-inventario-categoria"), options);
        chart.render();



        var options = {
            labels: subcategorias,
            series: cantidadesSubcategoria,
            chart: {
                type: 'donut',
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }]
        };

        var chart = new ApexCharts(document.querySelector("#grafica-inventario-subcategoria"), options);
        chart.render();
        
        
        let StockMarcasSucursales = Object.values({{ MarcasSucursalMapped|json_encode|raw }});
        let StockCatgoriasSucursales = Object.values({{ CategoriasSucursalMapped|json_encode|raw }});
        let StockSubcategoriasSucursales = Object.values({{ SubcategoriasSucursalMapped|json_encode|raw }});
        let urlEspanol = '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json';

        

    });

    function SubcategoryGraph(categoryid = -1){

        var subcategoryQuantities=[];
        var subcategories=[];
        var subcategoriesIds=[];

        {% for sc in stockSubcategoria %}

        if ({{sc.idclase}} == categoryid){
            subcategoryQuantities.push({{sc.cantidad}});
            subcategories.push("{{sc.subcategoria}}");
            subcategoriesIds.push({{sc.idcategoria}});
        }
        
        {% endfor %}

        var options = {
            labels: subcategories,
            series: subcategoryQuantities,
            chart: {
                type: 'donut',
                id: 'subcategory-chart-filtered',
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }]
        };

        var chart = new ApexCharts(document.querySelector("#subcategory-chart-container"), options);
        chart.render();

        $("#subcategory-table-filtered").dataTable().fnDestroy()
        var tableBody = document.querySelector("#subcategory-tablebody-filtered");
        tableBody.innerHTML = "";

        for (let i = 0; i < subcategories.length; i++){

            var newRow = document.createElement("tr");

            var newCell = document.createElement("td");
            newCell.textContent = subcategories[i];
            newRow.appendChild(newCell);

            var newCell = document.createElement("td");
            newCell.textContent = subcategoryQuantities[i];
            newRow.appendChild(newCell);

            var newCell = document.createElement("td");

            var newButton = document.createElement("button");
                  
            newButton.setAttribute('id', subcategories[i]);  
            newButton.setAttribute('class', 'btn btn-success');
            newButton.setAttribute('type', 'button');
            newButton.setAttribute('onclick', "mostrarDetallesSubcategoria(this.id)");
            newButton.setAttribute('data-bs-toggle', "mod");
            newButton.setAttribute('data-bs-target', "#modalDetalle");

            newButton.textContent = 'Detalles';

            newCell.appendChild(newButton);;

            newRow.appendChild(newCell);

            tableBody.appendChild(newRow);

        }

        $('#subcategory-table-filtered').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ subcategorías por página',
                
            },
        });


        $("#SubcategoryChartModal").modal('show');

        $('#SubcategoryChartModal').on('hidden.bs.modal', function () {
            chart.destroy();
            
        })

    }

    function mostrarDetallesMarca(id){


        var tableBody = document.querySelector("#tableBodyDetalles");
        
        $("#tablaDetalles").dataTable().fnDestroy()
        tableBody.innerHTML = "";

        let productoSucursal = Object.entries({{ modeloSucursalMapped|json_encode|raw }});

        productoSucursal.forEach((marca) => {
            
            if(marca[0] == id || id == "Total")
            {

                for(var i = 0; i < marca[1].length; i++){


                  var newRow = document.createElement("tr");
                  var newCell = document.createElement("th");
                  newCell.setAttribute('scope', 'row');
                  newCell.textContent = i+1;
                  newRow.appendChild(newCell);

                  var newCell = document.createElement("td");
                  newCell.textContent = marca[1][i]['cantidad'];
                  newRow.appendChild(newCell);

                  var newCell = document.createElement("td");
                  newCell.textContent = marca[1][i]['codigo'];
                  newRow.appendChild(newCell);

                  var newCell = document.createElement("td");
                  newCell.textContent = marca[1][i]['modelo'];
                  newRow.appendChild(newCell);

                  var newCell = document.createElement("td");
                  newCell.textContent = marca[1][i]['sucursal'];
                  newRow.appendChild(newCell);

                  

                  tableBody.appendChild(newRow);

                }
                
            }

        });

        $('#tablaDetalles').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ marcas por página',
                
            },
        });




    }

    function mostrarDetallesMarcaSucursal(id){


        keys = id.split("-");

        var tableBody = document.querySelector("#tableBodyDetalles");
        
        $("#tablaDetalles").dataTable().fnDestroy()
        tableBody.innerHTML = "";

        let productoSucursal = Object.entries({{ modeloSucursalMapped|json_encode|raw }});

        

        productoSucursal.forEach((marca) => {
            
            

            if(marca[0] == keys[0] || keys[0] == "Total")
            {
                index = 1;



                for(var i = 0; i < marca[1].length; i++){

                    sucursalNombre = marca[1][i]['sucursal'];

                    

                    if(sucursalNombre == keys[1]){
                        var newRow = document.createElement("tr");
                        var newCell = document.createElement("th");
                        newCell.setAttribute('scope', 'row');
                        newCell.textContent = index;
                        index++;
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.textContent = marca[1][i]['cantidad'];
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.textContent = marca[1][i]['codigo'];
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.textContent = marca[1][i]['modelo'];
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.textContent = marca[1][i]['sucursal'];
                        newRow.appendChild(newCell);

                        

                        tableBody.appendChild(newRow);
                    }

                }
                
            }

        });

        $('#tablaDetalles').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ marcas por página',
                
            },
        });




    }

    function mostrarDetallesCategoria(id){


        var tableBody = document.querySelector("#tableBodyDetalles");
        
        $("#tablaDetalles").dataTable().fnDestroy()
        tableBody.innerHTML = "";

        let productoSucursal = Object.entries({{ categoriaModeloSucursalMapped|json_encode|raw }});

        productoSucursal.forEach((categoria) => {
            
            if(categoria[0] == id || id == "Total")
            {
                for(var i = 0; i < categoria[1].length; i++){



                  var newRow = document.createElement("tr");
                  var newCell = document.createElement("th");
                  newCell.setAttribute('scope', 'row');
                  newCell.textContent = i+1;
                  newRow.appendChild(newCell);

                  var newCell = document.createElement("td");
                  newCell.textContent = categoria[1][i]['cantidad'];
                  newRow.appendChild(newCell);

                  var newCell = document.createElement("td");
                  newCell.textContent = categoria[1][i]['codigo'];
                  newRow.appendChild(newCell);

                  var newCell = document.createElement("td");
                  newCell.textContent = categoria[1][i]['modelo'];
                  newRow.appendChild(newCell);

                  var newCell = document.createElement("td");
                  newCell.textContent = categoria[1][i]['sucursal'];
                  newRow.appendChild(newCell);

                  

                  tableBody.appendChild(newRow);

                }
                
            }

        });

        $('#tablaDetalles').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ categorías por página',
                
            },
        });




    }

    function mostrarDetallesCategoriaSucursal(id){


        keys = id.split("-");


        var tableBody = document.querySelector("#tableBodyDetalles");
        
        $("#tablaDetalles").dataTable().fnDestroy()
        tableBody.innerHTML = "";

        let productoSucursal = Object.entries({{ categoriaModeloSucursalMapped|json_encode|raw }});

    
        productoSucursal.forEach((categoria) => {
            
            if(categoria[0] == keys[0]  || keys[0] == "Total")
            {
                index = 1;
                for(var i = 0; i < categoria[1].length; i++){

                    sucursalNombre = categoria[1][i]['sucursal'];


                    if(sucursalNombre == keys[1]){
                        var newRow = document.createElement("tr");
                        var newCell = document.createElement("th");
                        newCell.setAttribute('scope', 'row');
                        newCell.textContent = index;
                        index++;
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.textContent = categoria[1][i]['cantidad'];
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.textContent = categoria[1][i]['codigo'];
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.textContent = categoria[1][i]['modelo'];
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.textContent = categoria[1][i]['sucursal'];
                        newRow.appendChild(newCell);

                        tableBody.appendChild(newRow);
                    }
                }            
            }
        });

        $('#tablaDetalles').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ categorías por página',               
            },
        });
    }

    function mostrarDetallesSubcategoria(id){

        $("#SubcategoryChartModal").addClass("d-none");

        var tableBody = document.querySelector("#tableBodyDetalles");
        
        $("#tablaDetalles").dataTable().fnDestroy()
        tableBody.innerHTML = "";

        let productoSucursal = Object.entries({{ subcategoriaModeloSucursalMapped|json_encode|raw }});

        productoSucursal.forEach((subcategoria) => {
            
            if(subcategoria[0] == id  || id == "Total")
            {
                for(var i = 0; i < subcategoria[1].length; i++){
                  var newRow = document.createElement("tr");
                  var newCell = document.createElement("th");
                  newCell.setAttribute('scope', 'row');
                  newCell.textContent = i+1;
                  newRow.appendChild(newCell);

                  var newCell = document.createElement("td");
                  newCell.textContent = subcategoria[1][i]['cantidad'];
                  newRow.appendChild(newCell);

                  var newCell = document.createElement("td");
                  newCell.textContent = subcategoria[1][i]['codigo'];
                  newRow.appendChild(newCell);

                  var newCell = document.createElement("td");
                  newCell.textContent = subcategoria[1][i]['modelo'];
                  newRow.appendChild(newCell);

                  var newCell = document.createElement("td");
                  newCell.textContent = subcategoria[1][i]['sucursal'];
                  newRow.appendChild(newCell);

                  tableBody.appendChild(newRow);
                }      
            }
        });

        $('#tablaDetalles').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ subcategorías por página',        
            },
        });
    }

    function mostrarDetallesSubcategoriaSucursal(id){
        keys = id.split("-");

        var tableBody = document.querySelector("#tableBodyDetalles");
        
        $("#tablaDetalles").dataTable().fnDestroy()
        tableBody.innerHTML = "";

        let productoSucursal = Object.entries({{ subcategoriaModeloSucursalMapped|json_encode|raw }});

        productoSucursal.forEach((subcategoria) => {
            if(subcategoria[0] == keys[0] || keys[0] == "Total")
            {
                index = 1;
                for(var i = 0; i < subcategoria[1].length; i++){
                    sucursalNombre = subcategoria[1][i]['sucursal'];

                    if(sucursalNombre == keys[1]){
                        var newRow = document.createElement("tr");
                        var newCell = document.createElement("th");
                        newCell.setAttribute('scope', 'row');
                        newCell.textContent = index;
                        index++;
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.textContent = subcategoria[1][i]['cantidad'];
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.textContent = subcategoria[1][i]['codigo'];
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.textContent = subcategoria[1][i]['modelo'];
                        newRow.appendChild(newCell);

                        var newCell = document.createElement("td");
                        newCell.textContent = subcategoria[1][i]['sucursal'];
                        newRow.appendChild(newCell);

                        tableBody.appendChild(newRow);
                    }
                }     
            }
        });

        $('#tablaDetalles').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ subcategorías por página',
                
            },
        });
    }
</script>