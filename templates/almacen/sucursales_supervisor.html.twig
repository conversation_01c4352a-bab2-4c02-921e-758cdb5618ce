<link rel="stylesheet" href="{{ asset('/css/puntodeventa/filter_bussines.css') }}">

<div class="row g-4">
  <!-- Sucursales con Ventas BIMBO -->
  <div class="col-12">
    <div class="card shadow-sm border-0 h-100">
      <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <span class="fw-bold text-uppercase small">
          <i class="fas fa-store me-2"></i>
          Sucursales con Ventas BIMBO
        </span>
        <div class="form-check form-switch m-0">
          <input class="form-check-input" type="checkbox" id="todasSucursales" onclick="toggleChecks('sucursal', this)" checked>
          <label class="form-check-label small text-white" for="todasSucursales">Todas</label>
        </div>
      </div>
      <div class="card-body p-3" style="max-height: 300px; overflow-y: auto;">
        {% if sucursales|length > 0 %}
          <fieldset class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-2">
            {% for sucursal in sucursales %}
            <div class="col">
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="checkbox-sucursal-{{ sucursal.idsucursal }}" name="sucursal" value="{{ sucursal.idsucursal }}" checked>
                <label class="form-check-label fw-medium" for="checkbox-sucursal-{{ sucursal.idsucursal }}">
                  <i class="fas fa-building me-1 text-primary"></i>
                  {{ sucursal.nombre }}
                </label>
              </div>
            </div>
            {% endfor %}
          </fieldset>
        {% else %}
          <div class="text-center py-4">
            <i class="fas fa-exclamation-triangle text-warning fa-2x mb-3"></i>
            <p class="text-muted mb-0">No se encontraron sucursales con ventas del tipo BIMBO</p>
            <small class="text-muted">Verifique que existan ventas registradas para este tipo de venta.</small>
          </div>
        {% endif %}
      </div>
      {% if sucursales|length > 0 %}
      <div class="card-footer bg-light">
        <small class="text-muted">
          <i class="fas fa-info-circle me-1"></i>
          Mostrando {{ sucursales|length }} sucursal(es) con ventas del tipo BIMBO
        </small>
      </div>
      {% endif %}
    </div>
  </div>
</div>

<script>
  // Función para alternar todos los checkboxes de sucursales
  function toggleChecks(group, checkbox) {
    const checkboxes = document.querySelectorAll(`input[name="${group}"]`);
    checkboxes.forEach(chk => {
      chk.checked = checkbox.checked;
    });
  }
</script>
