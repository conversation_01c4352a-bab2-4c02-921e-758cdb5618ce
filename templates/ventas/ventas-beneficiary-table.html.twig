
<table class="table hover " id="beneficiary-table">

    <thead>
        <th>Apellido paterno</th>
        <th>Apellido materno</th>
        <th>Nombre</th>
        <th>Relación con el cliente</th>
        <th></th>
    </thead>
    <tbody>
        {% for beneficiary in beneficiaries %}
            <tr>
                <td>{{beneficiary.apellidopaterno}}</td>
                <td>{{beneficiary.apellidomaterno}}</td>
                <td>{{beneficiary.nombre}}</td>
                <td>{{beneficiary.beneficiarytype}}</td>
                <td>
                    <button class="btn btn-success" onclick="addBeneficiary('{{beneficiary.idcliente}}','{{beneficiary |json_encode()}}')" >Seleccionar</button>
                    <button class="btn btn-danger" onclick="deleteBeneficiary({{beneficiary.idcliente}})"><i class="fa fa-trash"></i></button>
                </td>
            </tr>
        {% endfor %}
    </tbody>
</table>

<script>

    $(document).ready(function(){

        let table = new DataTable('#beneficiary-table',{
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/es-ES.json',
            },
        });

    });

    function addBeneficiary(beneficiaryid, beneficiary){

        beneficiaries.set(beneficiaryid, beneficiary);

        tableSelectedBeneficiaries();


    }

    function deleteBeneficiary(beneficiaryid){

        var url = $("#url-ventas-delete-beneficiary").val();

        Swal.fire({
            title: '¿Está seguro?',
            text: "Se eliminará el beneficiario",
            type: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28B463',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Aceptar'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    method: "POST",
                    data:{beneficiaryid:beneficiaryid},
                    url: url,
                    beforeSend: function( xhr ) {

                    }
                }).done(function( response ) {

                    if (response.success){
                        deleteSelectedBeneficiary(beneficiaryid.toString());
                        beneficiaryTable();
                    } else {

                        Swal.fire(
                            "No se pudo eliminar el beneficiario" ,
                            response.msg ,
                            'warning'
                        )

                    }
                    
                });
            }
        })

    }

</script>