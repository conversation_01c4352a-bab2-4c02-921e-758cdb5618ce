<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">

<style>
    @page {
        margin-left: 0.4cm;
        margin-right: 0;
    }
    #ticket {
        font-family: "Roboto Condensed", sans-serif;
        font-weight: 400;
        font-style: normal;
    }
    .letra {
        font-family: "Roboto Condensed", sans-serif;
        font-weight: 400;
        font-style: normal;
    }
    .tabla-ancho {
        width:302.20px;
    }
    .centrado {
        text-align: center;
        align-content: center;
    }
    .ticket {
        width: 270px !important;
        font-family: "Roboto Condensed", sans-serif;
        font-weight: 400;
        font-style: normal;
        line-height: 13.5px;
        font-size: 11px;
    }
    .ticket img {
        max-width: inherit;
        width: 250px;
        margin: none;
    }
    .br,p {
        line-height: 8px;
    }
    .img-ticket{
        width:90%;
    }
    @media print {
        .oculto-impresion,
        .oculto-impresion * {
            display: none !important;
        }
    }
</style> 
 
<div id="ticket" class="ticket">
    <table class="ticket">
        <tr>
            <td style="text-align:center;">
                <img class="img-ticket" src="{{ logo }}" alt=""/>
            </td>
        </tr>
        <tr>
            <td>
                <div class="ticket letra" style="width:302px; color:#000000;text-align: center;font-weight: bold;margin-top:8px ">
                    {{ razonsocial }}
                </div>
            </td>
        </tr>
    </table>
    <br>
    {% if esCotizacion =="1" %}
        <p class="centrado ticket letra" style="width:302px; text-align:center;color:#000000; font-weight:bold;">
            COTIZACIÓN
        </p>
    {% else %}
        <p class="centrado ticket letra" style="width:302px; text-align:center;color:#000000; font-weight:bold;">
            VENTA
        </p>
    {% endif %}
    <p class="ticket" style="width:302px; color:#000000;text-align: left;">
        Sucursal: {{ nombreSucursal }}<br class="br">
        {{ direccionSucursal }}<br class="br">
        Teléfono: {{ telefonoSucursal }} <br class="br">
        Vendedor: {{ nombreVendedor }}
    </p>
    <p class="ticket" style=" color:#000000;">
        {% if convenio !="" %}
            Tipo de venta: {{convenio}}<br class="br">
        {% endif %}
        {% if authorizationNumber !="" %}
            Número de autorización: {{authorizationNumber}}<br class="br">
        {% endif %}
        Folio:<strong> {{prefijo}}-{{folio}}</strong><br>
        Cliente: {{cliente}}<br class="br">
        Teléfono: {{clienteTelefono}}<br class="br">
        Teléfono {{sinConvenioEmailCliente}}<br class="br">
        {% if numeroEmpleado !="" and Tipoventa.mostrarnumeroempleado == '1' %}
            Empleado: {{numeroEmpleado}}<br class="br">
        {% endif %}
        {% if unidadNombre !="" and Tipoventa.mostrarunidadprocedencia == '1' %}
            Unidad: {{unidadNombre}}<br class="br">
        {% endif %}
        {% if Tipoventa.mostrarbeneficiario == '1' and beneficiaries != null %}
            <br class="br">
            Beneficiario:{% if beneficiaries|length > 1 %}S{% endif %}
            <br class="br">
            <br class="br">
            {% for beneficiary in beneficiaries %}
                Nombre: {{beneficiary.fullName}}<br class="br">
                Teléfono: {{beneficiary.telefono}}<br class="br">
                Teléfono {{beneficiary.email}}<br class="br">
                <br class="br">
            {% endfor %}
        {% endif %}
        Fecha y Hora:  {{fecha}}
    </p>
    <p>----------------------------------------------------------------------------</p>
    {% if Tipoventa.mostrartopesautorizados =="1"  %}
        <h4>TOPES AUTORIZADOS</h4>
        <p class="ticket">
            {% autoescape %}
                <table class="ticket" style="width:302px; color:#000000">
                    {% set frameCount = 1 %}
                    {% for StockVenta in stockVentas %}
                        {% if StockVenta.masivounico == '1' %}
                        <tr style="text-align:start;color:#000000">
                            <td>
                                {% if StockVenta.tipoproducto == '1' and StockVenta.masivounico == '1' %}
                                    {{StockVenta.nombre}}&nbsp;{% if frameCount > 1 %}{{frameCount}}{% endif %}
                                    {% set frameCount = frameCount + 1 %}
                                {% else %}
                                    {{StockVenta.descripcion}}
                                {% endif %}
                            </td>
                            <td class="cantidad" style=" text-align:start">${{StockVenta.preciofinal  | number_format(2, '.', ',')}}</td>
                        </tr>
                        {% endif %}
                    {% endfor %}
                </table>
            {% endautoescape %}
        </p>
        <p>----------------------------------------------------------------------------</p>
    {% elseif convenio=="UAM Auditivos" %}
        <h4>TOPES AUTORIZADOS</h4>
        <table class="ticket" style="width:302px; color:#000000">
            <tr style="text-align: left;">
                <td style="width:50%">Aparato Auditivo</td>
                {% if productos[0].productoCantidad   > 1 or (productos | length) > 1 %}
                    <td style="width:50%">$49,800.00</td>
                {% else %}
                    <td style="width:50%">$24,900.00</td>
                {% endif %}
            </tr>
        </table>
        <p>----------------------------------------------------------------------------</p>
    {% endif %}
    <table class="ticket" style="width:100%; color:#000000">
        <thead>
            <tr>
                <td   {% if convenio !="UAM" and   convenio!="UAM Auditivos" and convenio !="Campaña Kobrex"  %}colspan="100%"{% else %}colspan="100%"{%  endif %}>
                    <h4>ARTÍCULOS(S) COMPRADOS(s)</h4>
                </td>
            </tr>
            <tr>
                <th style="text-align:center" class="cantidad">Cantidad</th>
                {% if convenio !="UAM" and   convenio!="UAM Auditivos" and convenio!="Campaña Kobrex" %}
                    <th style=" text-align:right" class="precio">Subtotal</th>
                    <th style=" text-align:right" class="precio">Descuento</th>
                    <th style=" text-align:right" class="precio">Total</th>
                {% endif %}
            </tr>
        </thead>
        <tbody>
        {% for producto in productos %}
            <tr  style="text-align:center;color:#000000">
                <td class="cantidad" style=" text-align: center">
                    {{producto.productoCantidad}}
                </td>
                {% if convenio !="UAM" and   convenio!="UAM Auditivos" and convenio  !="Campaña Kobrex" %}
                    <td class="cantidad" style=" text-align:center">${{producto.productoPrecio  | number_format(2, '.', ',')}}</td>
                    <td class="cantidad" style=" text-align:center">{{producto.productoDescuento  | number_format(0, '.', ',')}}%</td>
                    <td class="producto" style="text-align:center">${{producto.productoPrecioFinal  | number_format(2, '.', ',')}}</td>
                {% endif %}
            </tr>
            <tr>
                <td  colspan="100%">{{producto.descripcion}}<br>
                    {% if producto.codigobarras !="" %}
                        Codigo: {{producto.codigobarras}}
                    {% endif %}
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
    <p>----------------------------------------------------------------------------</p>
    <table class="ticket" style="width:302px; color:#000000">
        <tr>
            <td style="width:40%; text-align:left">SUBTOTAL</td>
            <td style="width:60%; text-align:right">${{subtotal  | number_format(2, '.', ',')}}</td>
        </tr>
        <tr>
            <td style="width:40%; text-align:left">IVA</td>
            <td style="width:60%; text-align:right"> ${{iva  | number_format(2, '.', ',')}}</td>
        </tr>
        <tr>
            <td style="width:40%; text-align:left"><strong>TOTAL</strong></td>
            <td style="width:60%; text-align:right"><strong> ${{ total | number_format(2, '.', ',')}}</strong></td>
        </tr>
    </table>
    {% if convenio !="UAM" and   convenio!="UAM Auditivos"%}
        <table class="ticket" style="width:302px; color:#000000">
            <tr>
                <td style="width:40%; text-align:left">Pagado</td>
                <td style="width:60%; text-align:right">${{totalPagado  | number_format(2, '.', ',')}}</td>
            </tr>
            <tr>
                <td style="width:40%; text-align:left">Restante</td>
                <td style="width:60%; text-align:right"> ${{(total -  totalPagado) | number_format(2, '.', ',')}}</td>
            </tr>
        </table>
        {% if cuponDescuento !="" %}
            <table class="ticket" style="width:302px; color:#000000">
                <tr>
                    <td style="width:30%; text-align:left">Cupón: </td>
                    <td style="width:70%; text-align:right;font-weight: bold"> {{ cuponDescuento }}</td>
                </tr>
            </table>
        {% endif %}
        {% if pagos is not empty %}
            <p>----------------------------------------------------------------------------</p>
            <h4>Pago(s)</h4>
            <table  class="ticket" style=" color:#000000">
            <tr style="text-align: center">
                <th>
                    Monto
                </th>
                <th>
                    Tipo de Pago
                </th>
                <th>
                    Fecha
                </th>
            </tr>
            {% for pago in pagos %}
                <tr style="text-align: center">
                    <td>
                        ${{ pago.monto | number_format(2, '.', ',')}}
                    </td>
                    <td>
                        {{ pago.tipopago }}
                    </td>
                    <td>
                        {{ pago.fecha | date("g:ia d/m/Y")  }}
                    </td>
                </tr>
            {% endfor %}
            </table>
            {% if pagos[0].mesesintereses %}
                <strong>
                <p class="centrado ticket" style="width:302px; color:#000000;text-align: center;">
                {{pagos[0].mesesintereses}} MSI cargo mensual: ${{ (total / pagos[0].mesesintereses) | number_format(2, '.', ',')}}
                </p>
                </strong>
            {% endif %}
        {% endif %}
        <p>----------------------------------------------------------------------------</p>
    {% endif %}
    <br><br>
    <table class="ticket" style=" color:#000000">
        <tr>
            <td style="width:100%; text-align:center;border-top: 1px solid #C00;padding-left:20px">
                Nombre y firma del empleado
            </td>
        </tr>
    </table>
    
    {% if convenio =="UAM"   %}
        <p>----------------------------------------------------------------------------</p>
        <p class="ticket" style=" color:#000000;text-align: center;">
            Garantía UAM:
            Válida por un año en armazón, micas y graduación por defectos de fábrica; Revisa nuestra política de garantía en nuestra página web
        </p>
    {% elseif  convenio=="UAM Auditivos"  %}
        <p class="ticket" style=" color:#000000;text-align: center;">
            Garantía UAM:
            Válida por un dos años en aparato auditivo por defectos de fábrica; Revisa nuestra política de garantía en nuestra página web
        </p>
    {% endif %}
    <p>----------------------------------------------------------------------------</p>
    <p class="centrado ticket" style=" text-align:center;color:#000000;  font-weight: 700;">
        {{ pieTicket|nl2br }}</p>
</div>