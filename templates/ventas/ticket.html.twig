<style>

#ticket {

      font-family: Courier, "Lucida Console", monospace;

}
.letra{
    font-family: Courier, "Lucida Console", monospace;
}

.tabla-ancho{
width:100px;
}
/*
 td.producto,
 th.producto {
  width: 75px;
  max-width: 75px;
}
*/
/*
 td.cantidad,
t th.cantidad {
  width: 40px;
  max-width: 40px;
  word-break: break-all;
}
*/
/*
 td.precio,
 th.precio {
  width: 40px;
  max-width: 40px;
  word-break: break-all;
}*/

.centrado {
  text-align: center;
  align-content: center;
}

.ticket {
  width: 250px !important;
    font-family: Courier, "Lucida Console", monospace;
}

.ticket img {
  max-width: inherit;
  width: inherit;
}
.br,p {
   line-height: 12px;
}
@media print {
    .oculto-impresion,
    .oculto-impresion * {
        display: none !important;
    }
}
view raw

</style>
  <div id="ticket" class="ticket">
  <table class="ticket" style="font-size: 10px;">
  <tr>
  <td style="text-align:center;padding-left:20px">
  <img width="150" src="data:image/jpeg;base64,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" alt="" />
  </td>
  </tr>
      <tr>
          <td>
              <div class="ticket letra" style="width:250px;font-size: 12px;color:#000000;text-align: center;font-weight: bold;margin-top:8px ">
                  GRUPO OPTICAS MOSAL ESTILO Y VISIÓN PARA TUS OJOS S.A. de C.V.
              </div>
          </td>
      </tr>
  </table>
<br>
      {% if esCotizacion =="1" %}
          <p class="centrado ticket letra" style="width:250px;font-size: 16px;text-align:center;color:#000000; font-weight:bold;">
              Cotización
          </p>


      {% endif %}
      <p class="ticket" style="width:250px;font-size: 10px;color:#000000;text-align: center;">
          SUCURSAL: {{ nombreSucursal }}<br class="br">
          {{ direccionSucursal }}<br class="br">
          Tel: {{ telefonoSucursal }}
      </p>
      <p class="ticket" style="font-size: 10px;color:#000000;">
          {% if convenio !="" %}
          CONVENIO: {{convenio}}<br class="br">
          {% endif %}
          FOLIO:<strong> 13795</strong><br>
	 {% if convenio =="UAM" or convenio=="UAM Auditivos" %}

       CLIENTE: {{cliente}}<br class="br">
       TELÉFONO: {{clienteTelefono}}<br class="br">

       UNIDAD: {{unidadNombre}}<br class="br">
       EMPLEADO: {{numeroEmpleado}}<br class="br">
       {% if beneficiarioNombre !="" %}
       BENEFICIARIO: {{beneficiarioNombre}}<br class="br">
        {% endif %}
     {% else %}
         CLIENTE: {{sinConvenioNombreCliente}}<br class="br">
         TELÉFONO: {{clienteTelefono}}<br class="br">
	 {% endif %}
       FECHA Y HORA:  16/10/2024 hora 13:40:57
     </p>
     <p>-------------------------------------------------</p>
	 {% if convenio =="UAM"  %}
     <h4 style="font-size: 15px">TOPES AUTORIZADOS</h4>
     <table class="ticket" style="width:250px;font-size: 10px;color:#000000">
       <tr style="text-align: left;">
         <td style="width:50%">Armazón</td>
         <td style="width:50%">$490.00</td>
       </tr>
       <tr>
         <td style="width:50%">Mica</td>
         <td style="width:50%">$2585.00</td>
       </tr>
     </table>
     <p>-------------------------------------------------</p>
      {% elseif convenio=="UAM Auditivos" %}
          <h4 style="font-size: 15px">TOPES AUTORIZADOS</h4>

          <table class="ticket" style="width:250px;font-size: 10px;color:#000000">
              {% for producto in productos %}
              <tr style="text-align: left;">
                  <td>{{producto.productoCantidad}}</td>
                  <td style="width:50%"> Aparato Auditivo</td>
                  <td style="width:50%">${{(producto.productoPrecioFinal *producto.productoCantidad)|round(2, 'floor')  | number_format(2, '.', ',')}}</td>
              </tr>
              {% endfor %}

          </table>
          <p>-------------------------------------------------</p>
	 {% endif %}

   <table class="ticket" style="width:250px;font-size: 10px;color:#000000">
     <thead>
     <tr>
         <td   {% if convenio !="UAM" and   convenio!="UAM Auditivos" %}colspan="5"{% else %}colspan="2"{%  endif %}>
             <h4 style="font-size: 15px">ARTÍCULOS(S) COMPRADOS(s)</h4>
         </td>
     </tr>
       <tr>
         <th style="text-align:center" class="cantidad">CANT</th>
         <th style="text-align:left" class="producto">Artículo</th>
		 {% if convenio !="UAM" and   convenio!="UAM Auditivos" %}
         <th style=" text-align:right" class="precio">Precio</th>
         <th style=" text-align:right" class="precio">Descuento</th>
         <th style=" text-align:right" class="precio">Precio Descuento</th>
		 {% endif %}
       </tr>
     </thead>
     <tbody>
      {% for producto in productos %}

       <tr  style="text-align:center;color:#000000">
         <td class="cantidad" style=" ">{{producto.productoCantidad}} </td>
         <td class="cantidad" style=" text-align:left">{{producto.descripcion}}</td>

		        {% if convenio !="UAM" and   convenio!="UAM Auditivos" %}
                    <td class="cantidad" style=" text-align:left">${{producto.productoPrecio  | number_format(2, '.', ',')}}</td>
                    <td class="cantidad" style=" text-align:left">{{producto.productoDescuento  | number_format(0, '.', ',')}}</td>
                    <td class="producto" style="text-align:right">${{producto.productoPrecioFinal  | number_format(2, '.', ',')}}</td>
		        {% endif %}


       </tr>
      {% endfor %}
     </tbody>
   </table>


    <p>-------------------------------------------------</p>
    <table class="ticket" style="width:250px;font-size: 10px;color:#000000">
      <tr>

        <td style="width:40%; text-align:left">SUBTOTAL</td>
        <td style="width:60%; text-align:right">${{subtotal  | number_format(2, '.', ',')}}</td>
      </tr>
      <tr>
        <td style="width:40%; text-align:left">IVA</td>
        <td style="width:60%; text-align:right"> ${{iva  | number_format(2, '.', ',')}}</td>
      </tr>
      <tr>
        <td style="width:40%; text-align:left"><strong>TOTAL</strong></td>
        <td style="width:60%; text-align:right"><strong> ${{ total | number_format(2, '.', ',')}}</strong></td>
      </tr>
    </table>

      {% if convenio !="UAM" and   convenio!="UAM Auditivos"%}
      <table class="ticket" style="width:250px;font-size: 10px;color:#000000">
          <tr>

              <td style="width:40%; text-align:left">Pagado</td>
              <td style="width:60%; text-align:right">${{totalPagado  | number_format(2, '.', ',')}}</td>
          </tr>
          <tr>
              <td style="width:40%; text-align:left">Restante</td>
              <td style="width:60%; text-align:right"> ${{(total -  totalPagado) | number_format(2, '.', ',')}}</td>
          </tr>

      </table>

          {% if pagos is not empty %}
              <p>--------------------------------------------------</p>
              <h4>Pago(s)</h4>
      <table style="width:250px;font-size: 10px;color:#000000">
          <tr style="text-align: center">
              <th>
                  Monto
              </th>
              <th>
                  Tipo de Pago
              </th>
              <th>
                  Fecha
              </th>
          </tr>

              {% for pago in pagos %}
                <tr style="text-align: center">
                    <td>
                        ${{ pago.monto | number_format(2, '.', ',')}}
                    </td>
                    <td>
                        {{ pago.tipopago }}
                    </td>
                    <td>
                        {{ pago.fecha | date("g:ia d/m/Y")  }}
                    </td>
                </tr>
              {% endfor %}
          {% endif %}
      </table>
          <p>--------------------------------------------------</p>
      {% endif %}

    <br><br>
    <table class="ticket" style="width:250px;font-size: 10px;color:#000000">
      <tr>
        <td style="width:100%; text-align:center;border-top: 1px solid #C00;padding-left:20px">
          Nombre y Firma del Empleado
        </td>
      </tr>
    </table>
   <p>--------------------------------------------------</p>
      {% if convenio =="UAM"   %}
      <p class="ticket" style="width:250px;font-size: 8px;color:#000000;text-align: center;">
          Garantía UAM:
          Válida por un año en armazón, micas y graduación por defectos de fábrica; Revisa nuestra política de garantía en nuestra página web
      </p>
          {% elseif  convenio=="UAM Auditivos"  %}
              <p class="ticket" style="width:250px;font-size: 8px;color:#000000;text-align: center;">
                  Garantía UAM:
                  Válida por un año en aparato auditivo por defectos de fábrica; Revisa nuestra política de garantía en nuestra página web
              </p>
      {% endif %}
   <p>--------------------------------------------------</p>
   <p class="centrado ticket" style="width:250px;font-size: 12px;text-align:center;color:#000000">
     ¡GRACIAS POR SU COMPRA!<br><br>
       Si no Recibes tu ticket la compra es gratis<br>
       Repórtalo al  55-31-03-70-26<br>
       <EMAIL>
        <br>
     <br>www.grupooptimo.mx</p>
  </div>
