<input id="url-ventas-upload-sale-document" type="hidden" value="{{path('ventas-upload-sale-document')}}">
<input id="url-ventas-delete-sale-document" type="hidden" value="{{path('ventas-delete-sale-document')}}">
<input id="number-uploaded-files" type="hidden" value="{{saledocuments | length}}">
<input id="saleid" type="hidden" value="{{saleid}}">
<input id="userid" type="hidden" value="{{userid}}">

<div class="row justify-content-md-center">
    <div class="col-md-10">
        {% if saledocuments | length > 0 %}
            <table class="table" id="tablaDocumentos">
                <thead>
                <tr class="text-start">
                    <th class="text-start text-center" scope="col">#</th>
                    <th class="text-start text-center" scope="col">Categoria</th>
                    <th class="text-start text-center" scope="col"></th>
                </tr>

                </thead>
                <tbody id = "tableBodyDocumentos">

                {% for index, document in saledocuments %}

                    <tr>

                        <td class="text-center align-middle ">{{index + 1}}</td>

                        <td class="text-center align-middle ">{{document.tipodocumento}}</td>

                        <td class="text-center align-middle ">
                            <button type="button" class="btn btn-primary" onclick="openSaleDocumentVisor('{{document.nombredocumento}}','{{document.tipodocumento}}','{{userid}}')" data-toggle="modal" data-target="#sale-document-visor-modal"><i class="fa-solid fa-eye"></i></button>

                            {% if (saledeail == 1 and role == "ROLE_SUPER_ADMIN") or saledeail != 1  %}
                                <button type="button" class="btn btn-danger" onclick="deleteSaleDocument({{document.iddocumentoventa}})"><i class="fa fa-trash"></i></button>
                            {% endif %}

                        </td>

                    </tr>

                {% endfor %}

                </tbody>
            </table>

        {% else %}

            <h4 class="text-center" >No hay un archivo cargado</h4>

        {% endif %}

    </div>
</div>

<div class="modal" id="sale-document-visor-modal" tabindex="-1" aria-labelledby="" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content" style="border-radius:10px">
            <div class="modal-header bg-primary">
                <h1 class="modal-title fs-5" id="sale-document-modal-title"></h1>

            </div>

            <div class="modal-body">

                <div id = "document"></div>

            </div>


        </div>
    </div>
</div>

<script>

    $(document).ready(function(){

        var numberFiles = $("#number-uploaded-files").val();

        {% if saledeail != 1 %}
        setNumberUploadedFiles(numberFiles);
        {% endif %}

    })

    function deleteSaleDocument(saledocumentid = -1){

        var url = $("#url-ventas-delete-sale-document").val();

        var saleid = $("#saleid").val();
        var userid = $("#userid").val();

        console.log(url);

        Swal.fire({
            title: '¿Está seguro?',
            text: "Se eliminará el documento",
            type: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28B463',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Aceptar'
        }).then((result) => {
            if (result.value) {

                $.ajax({
                    url: url,
                    method: "POST",
                    data: {saledocumentid:saledocumentid},

                    success: function (response) {

                        console.log(response);

                        {% if saledeail != 1 %}
                        saleDocumentTable(saleid);
                        {% else %}
                        saleDocumentTable(saleid,userid,1);
                        {% endif %}
                    },

                });
            }
        })
    }

    function openSaleDocumentVisor(filename,filetype,userid){

        $("#document").html("");
        $("#sale-document-modal-title").text(filetype);

        var saleid = $("#saleid").val();

        $.ajax({
            url: "{{path('ventas-open-sale-document-visor')}}",
            data: {saleid:saleid,filename:filename,userid:userid},
            //beforeSend: loadingGif("tablaDocumentos"),
            dataType: "html"
        }).done(function( html ) {
            $("#document").html(html);


        }).fail(function() {
            alert( "error" );
        });
    }



</script>