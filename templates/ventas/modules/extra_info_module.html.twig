<div class="row margin-buttom-new-sale  extrainformation d-none">
	<div class="col-12 border-title-new-sale">
		<h4 class="div-title">Información extra</h4>
	</div>
	<div class="col-12">
		<div class="row">
			<div
				class="col-12 col-md-6">
				<!--el gerete es el qu está conectado y el que hizo la venta es el que se selecciona de aqui-->
				<label for="usuario-venta">¿Quién Hizo la Venta?</label>
				<select class="form-control" name="" id="usuario-venta">
					<option value="">Seleccione una opción</option>
					{% for usuario in usuarios %}
						<option value="{{ usuario.idusuario }}">{{ usuario.nombre ~" "~ usuario.apellidopaterno }}</option>
					{% endfor %}
				</select>
			</div>
			<div class="col-12 col-md-6">
				<label for="usuario-venta">¿Dónde nos conoció?</label>
				<select class="form-control" name="" id="usuario-donde-nos-conocio" onchange="selectOther();" required>
					{% for sellreference in sellReferences %}
						<option value="{{sellreference.idsellreference}}">{{sellreference.name}}</option>
					{% endfor %}
					<option value="18">Otro</option>
				</select>
				<br>
				<!-- Input para el "otro" -->
				<div id="otro-container" class="hidden">
					<input type="text" id="otro-input" class="form-control" placeholder="Otra Opción">
				</div>
			</div>
			<div class="col-12 col-md-12 col-lg-6 separation-top-new-sale">
				<div class="form-group">
					<label for="fileupload" class="form-label">Seleccionar Documento</label>
					<input id="archivoautorizacion" value="" type="hidden">
					<div id="form-upload-sale-document-container"></div>
					<div id="sale-documents-table-container"></div>
				</div>
			</div>
			<div class="col-12 col-md-12 col-lg-6 separation-top-new-sale">
				<label for="authorization-number-input" class="form-label">Número de autorización</label>
				<input id="authorization-number-input" type="text" class="form-control mb-2 mr-sm-2" autocomplete="off">
                <label for="authorization-scan-input" class="form-label">Escaneo de codigo de autorización</label>
				<input id="authorization-scan-input" type="text" class="form-control mb-2 mr-sm-2" autocomplete="off">
				<label for="notas" class="form-label">Notas</label>
				<textarea id="notas" class="col-sm-12 col-md-12 form-control"></textarea>
			</div>
			<div class="col-12 col-sm-6 col-md-6">
				<button id="guardar-venta" type="button" class="btn btn-primary btn-block guardar-venta " onclick="guardarVenta();return false;">Guardar Venta</button>
			</div>
			<div class="col-12 col-sm-6 col-md-6">
				<button id="guardar-cotizacion" type="button" class="btn btn-info btn-block guardar-cotizacion" onclick="guardarVenta(1);return false;">Guardar como cotización</button>
			</div>
			<div class="col-12 col-sm-6 col-md-6"></div>
			<div class="col-12 col-sm-6 col-md-6">
				<button type="button" id="btn-pdf" class="btn btn-success  btn-block d-none btn-pdf" data-bs-toggle="mod" data-bs-target="#modal-visor-documentos" onclick="abrirVisor();">Ticket en PDF</button>

				<button type="button" id="btn-pdf" class="btn btn-info  btn-block d-none btn-pdf" data-bs-toggle="mod" data-bs-target="#modal-visor-documentos" onclick="abrirVisor('especial');">Ticket en PDF Especial</button>
			</div>
		</div>
	</div>
</div>
