<input id="userid" type="hidden" value="{{userid}}">

<div class="card-body signature">
    <div class="card signature large-card">
        <div class="card-body signature">
            <p class="subtable">Subir documentos</p>
            <form id="form-upload-sale-document" enctype="multipart/form-data" class="needs-validation signature" novalidate>
                <div class="row">
                    <div class="col-5">
                        <select name="document-categories" id="document-categories" form="form-upload-sale-document" class="form-control">
                            <option value="Autorización UAM">Autorización UAM</option>
                            <option value="Regalo UAM">Regalo UAM</option>
                            <option value="Regalo UAM">Audiometría</option>
                            <option value="Regalo UAM">Historial Clínico</option>
                            <option value="Otro">Otro</option>


                        </select>
                    </div>
                    <div class="col-5">
                        <input id="file" type="file" name="file" class="form-control text-center" required>
                    </div>
                    <div class="col-2">
                        <button type="submit" class="btn new-button signature" id="submit-sale-documents-form">
                            <i class="fa-solid fa-arrow-up-from-bracket fa-lg" style="color: #115fd1;"></i>
                        </button>
                    </div>
                </div>
                <div class="invalid-feedback">Selecciona un archivo</div>
                <p id="sale-document-error-message"></p>
            </form>
        </div>
    </div>
</div>

<script>

    $("#form-upload-sale-document").submit(function(e) {

        var url = $("#url-ventas-upload-sale-document").val();

        var userid = $("#userid").val();

        e.preventDefault();
        var form = e.target;
        var formData = new FormData(form);

        var saleid = $("#idventa").val();

        changeButton("submit-sale-documents-form",0,1);

        $.ajax({

            url: url+"?saleid="+saleid+'&userid='+userid,
            type: 'POST',
            data: formData,
            success: function(response) {

                changeButton("submit-sale-documents-form",1,1);

                if (response.success) {

                    $("#file").val(null);

                    saleDocumentTable(saleid, userid);

                }
            },
            error: function(jqXHR, textStatus, errorThrown) {

                $("#sale-document-error-message").text(textStatus + " - " + errorThrown);

            },
            cache: false,
            contentType: false,
            processData: false
        });
    });

</script>