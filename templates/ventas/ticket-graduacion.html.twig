<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Barlow+Condensed:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
      rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

<style>
    @page {
        margin-left: 0.4cm;
        margin-right: 0;
    }

    .ticket {
        width: 270px;
        font-family: "Barlow Condensed", sans-serif;
        font-weight: 400;
        font-style: normal;
        font-size: 10px;
        color: #333;
    }

    .img-ticket {
        width: 90%;
        margin-bottom: 10px;
    }

    .encabezado {
        text-align: center;
        border-bottom: 2px solid #2c3e50;
        padding-bottom: 15px;
        margin-bottom: 15px;
    }

    .encabezado h2 {
        color: #2c3e50;
        font-weight: 700;
        font-size: 14px;
        margin: 8px 0;
        letter-spacing: 1px;
    }

    .encabezado h4 {
        color: #34495e;
        font-weight: 500;
        margin: 5px 0;
    }

    .informacion {
        display: block;
        line-height: 1.4;
        margin-bottom: 15px;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 5px;
    }

    .informacion p {
        margin: 3px 0;
    }

    .articulos th, .articulos td {
        font-size: 9px;
    }

    .texto-ticket {
        line-height: 1.3;
        font-size: 9px;
    }

    .tabla-graduacion, .tabla-medidas {
        width: 100%;
        border-collapse: collapse;
        margin: 8px auto;
        font-size: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .tabla-graduacion th, .tabla-graduacion td,
    .tabla-medidas th, .tabla-medidas td {
        border: 1px solid #34495e;
        padding: 6px 4px;
        text-align: center;
    }

    .tabla-graduacion th, .tabla-medidas th {
        background-color: #34495e;
        color: white;
        font-weight: 600;
        font-size: 8px;
    }

    .tabla-graduacion td, .tabla-medidas td {
        background-color: #ffffff;
        font-weight: 500;
    }

    .eye-icon {
        font-size: 12px;
        margin-bottom: 2px;
    }

    .eye-label {
        font-size: 7px;
        display: block;
        text-transform: uppercase;
        margin-top: 2px;
        font-weight: 600;
        color: #2c3e50;
    }

    .graduacion-info h4 {
        text-align: center;
        margin: 15px 0 10px 0;
        font-size: 12px;
        color: #2c3e50;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .producto-card {
        border: 2px solid #3498db;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .producto-info {
        background-color: #ecf0f1;
        padding: 10px;
        border-radius: 6px;
        margin-bottom: 12px;
    }

    .producto-info table {
        width: 100%;
        font-size: 9px;
    }

    .producto-info td {
        padding: 2px 4px;
        border: none;
    }

    .graduacion-data {
        background: linear-gradient(135deg, #e8f4fd 0%, #f1f8ff 100%);
        padding: 12px;
        border-radius: 8px;
        border: 1px solid #3498db;
    }

    .tabla-notas {
        margin-top: 10px;
        font-size: 8px;
    }

    .tabla-notas th {
        background-color: #e74c3c;
        color: white;
        font-weight: 600;
    }

    .tabla-notas td {
        background-color: #ffffff;
        vertical-align: top;
        text-align: left;
        padding: 8px;
    }

    .no-graduaciones {
        color: #e74c3c;
        text-align: center;
        padding: 30px 20px;
        background-color: #fdf2f2;
        border: 2px dashed #e74c3c;
        border-radius: 10px;
        margin: 20px 0;
    }

    .separador {
        border-top: 1px dashed #bdc3c7;
        margin: 10px 0;
    }

</style>



{#<div class="ticket">
    <div class="encabezado">
        <img class="img-ticket" src="{{ logo }}" alt="OPTIMO OPTICAS"/>
        <h2>ORDEN DE LABORATORIO</h2>
        <h4>{{ razonsocial }}</h4>

    </div>
    <div class="informacion">
        <p class="texto-ticket"><strong>Sucursal:</strong> {{ nombreSucursal }}</p>
        <p class="texto-ticket"><strong>Vendedor:</strong> {{ nombreVendedor }}</p>
        <p class="texto-ticket"><strong>Folio:</strong> {{ prefijo }}-{{ folio }}</p>
        <div class="separador"></div>

        <p class="texto-ticket"><strong>Cliente:</strong> {{ cliente|default('-') }}</p>
        <p class="texto-ticket"><strong>Teléfono:</strong> {{ clienteTelefono|default('-') }}</p>
        <p class="texto-ticket"><strong>Email:</strong> {{ sinConvenioEmailCliente|default('-') }}</p>
        {% if numeroEmpleado is defined and numeroEmpleado != "" %}
            <p class="texto-ticket"><strong>Empleado:</strong> {{ numeroEmpleado }}</p>
        {% endif %}

        {% if beneficiaries is defined and beneficiaries != null and beneficiaries|length > 0 %}
            <div class="separador"></div>
            <p class="texto-ticket"><strong>Beneficiario{% if beneficiaries|length > 1 %}s{% endif %}:</strong></p>
            {% for beneficiary in beneficiaries %}
                <p class="texto-ticket">• {{ beneficiary.fullName }}</p>
                <p class="texto-ticket">  Tel: {{ beneficiary.telefono|default('-') }}</p>
                <p class="texto-ticket">  Email: {{ beneficiary.email|default('-') }}</p>
            {% endfor %}
        {% endif %}
        <div class="separador"></div>
        <p class="texto-ticket"><strong>Fecha y Hora:</strong> {{ fecha }}</p>
    </div>

    <div class="graduacion-info">
            <h4>PRODUCTOS CON GRADUACIÓN</h4>

            {% for ordenlab in stockventaordenlaboratorio %}
                {% if ordenlab.graduacion is not null %}
                    {% set stockVenta = ordenlab.stockventa %}
                    <div class="producto-card">
                        <div class="producto-info">
                            <table>
                                <tr>
                                    <td><strong>Producto:</strong></td>
                                    <td>{{ stockVenta.descripcion }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Marca:</strong></td>
                                    <td>{{ stockVenta.marca|default('-') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Modelo:</strong></td>
                                    <td>{{ stockVenta.stock.producto.modelo|default('-') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>SKU:</strong></td>
                                    <td>{{ stockVenta.stock.codigobarras|default('-') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>UPC:</strong></td>
                                    <td>{{ stockVenta.stock.producto.codigobarrasuniversal|default('-') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Cantidad:</strong></td>
                                    <td>{{ stockVenta.cantidad }} pz</td>
                                </tr>
                                <tr>
                                    <td><strong>Precio:</strong></td>
                                    <td>${{ stockVenta.preciofinal|number_format(2, '.', ',') }}</td>
                                </tr>
                            </table>
                        </div>

                        {% if ordenlab.graduacion is defined %}
                            <div class="graduacion-data">
                                <h5 style="text-align: center; margin: 0 0 10px 0; color: #2c3e50; font-size: 10px; font-weight: 700;">GRADUACIÓN ÓPTICA</h5>

                                <table class="tabla-graduacion">
                                    <tr>
                                        <th></th>
                                        <th>ESF</th>
                                        <th>CIL</th>
                                        <th>EJE</th>
                                        <th>ADD</th>
                                    </tr>
                                    <tr>
                                        <th>
                                            <span class="eye-label">OD</span>
                                        </th>
                                        <td>{{ ordenlab.graduacion.odEsfera|default('-') }}</td>
                                        <td>{{ ordenlab.graduacion.odCilindro|default('-') }}</td>
                                        <td>{{ ordenlab.graduacion.odEje|default('-') }}</td>
                                        <td>{{ ordenlab.graduacion.odAdicion|default('-') }}</td>
                                    </tr>
                                    <tr>
                                        <th>
                                            <span class="eye-label">OI</span>
                                        </th>
                                        <td>{{ ordenlab.graduacion.oiEsfera|default('-') }}</td>
                                        <td>{{ ordenlab.graduacion.oiCilindro|default('-') }}</td>
                                        <td>{{ ordenlab.graduacion.oiEje|default('-') }}</td>
                                        <td>{{ ordenlab.graduacion.oiAdicion|default('-') }}</td>
                                    </tr>
                                </table>

                                <h5 style="text-align: center; margin: 10px 0 8px 0; color: #2c3e50; font-size: 10px; font-weight: 700;">MEDIDAS</h5>
                                <table class="tabla-medidas">
                                    <tr>
                                        <th style="width: 30%;">DIP</th>
                                        <td>{{ ordenlab.graduacion.distanciaPupilar|default('-') }}</td>
                                    </tr>
                                    <tr>
                                        <th>AO</th>
                                        <td>{{ ordenlab.graduacion.altura|default('-') }}</td>
                                    </tr>
                                    <tr>
                                        <th>ACO</th>
                                        <td>{{ ordenlab.graduacion._aco|default('-') }}</td>
                                    </tr>
                                </table>

                                {% if ordenlab.graduacion.diagnostico is defined and ordenlab.graduacion.diagnostico is not empty or ordenlab.graduacion.notes is defined and ordenlab.graduacion.notes is not empty %}
                                    <h5 style="text-align: center; margin: 15px 0 8px 0; color: #e74c3c; font-size: 10px; font-weight: 700;">OBSERVACIONES</h5>
                                    <table class="tabla-notas">
                                        <tr>
                                            <th style="width: 50%;">DIAGNÓSTICO</th>
                                            <th style="width: 50%;">NOTAS</th>
                                        </tr>
                                        <tr>
                                            <td>{{ ordenlab.graduacion.diagnostico|default('-')|e('html')|replace({'\n':'<br>'})|raw }}</td>
                                            <td>{{ ordenlab.graduacion.notes|default('-')|e('html')|replace({'\n':'<br>'})|raw }}</td>
                                        </tr>
                                    </table>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                {% endif %}
            {% endfor %}
        {% else %}
            <div class="no-graduaciones">
                <h4 style="color: #e74c3c; margin-bottom: 10px;">SIN GRADUACIONES</h4>
                <p>No se encontraron productos con graduación en esta venta.</p>
                <p style="font-size: 8px; margin-top: 10px;">Verifica las órdenes de laboratorio asociadas.</p>
            </div>
        {% endif %}
    </div>

    <div style="text-align: center; margin-top: 20px; padding-top: 15px; border-top: 2px solid #2c3e50;">
        <p style="font-size: 10px; color: #2c3e50; font-weight: 600;">{{ pieTicket }}</p>
        <p style="font-size: 8px; color: #7f8c8d; margin-top: 5px;">Orden de Laboratorio Generada Automáticamente</p>
    </div>

</div>#}

{% if stockventaordenlaboratorio is defined and stockventaordenlaboratorio|length > 0 %}
    {% for ordenlab in stockventaordenlaboratorio %}
        {% if ordenlab.graduacion is not null %}
            <div class="ticket" {% if not loop.last %}style="page-break-after: always;"{% endif %}>
                <!-- ENCABEZADO -->
                <div class="encabezado">
                    <img class="img-ticket" src="{{ logo }}" alt="OPTIMO OPTICAS">
                    <h2>ORDEN DE LABORATORIO</h2>
                    <h4>{{ razonsocial }}</h4>
                </div>

                <!-- INFORMACIÓN GENERAL -->
                <div class="informacion">
                    <p class="texto-ticket"><strong>Sucursal:</strong> {{ nombreSucursal }}</p>
                    <p class="texto-ticket"><strong>Vendedor:</strong> {{ nombreVendedor }}</p>
                    {% if ordenlab.graduacion.optometristaNombre %}
                        <p class="texto-ticket"><strong>Optometrista:</strong> {{ ordenlab.graduacion.optometristaNombre }}</p>
                    {% endif %}
                    <p class="texto-ticket"><strong>Folio:</strong> {{ prefijo }}-{{ folio }}</p>
                    <p class="texto-ticket"><strong>Cliente:</strong> {{ cliente|default('-') }}</p>
                    <p class="texto-ticket"><strong>Teléfono:</strong> {{ clienteTelefono|default('-') }}</p>
                    <p class="texto-ticket"><strong>Email:</strong> {{ sinConvenioEmailCliente|default('-') }}</p>
                    {% if numeroEmpleado %}
                        <p class="texto-ticket"><strong>Empleado:</strong> {{ numeroEmpleado }}</p>
                    {% endif %}
                    <p class="texto-ticket"><strong>Fecha y Hora:</strong> {{ fecha }}</p>
                </div>

                <!-- PRODUCTO -->
                {% set stockVenta = ordenlab.stockventa %}
                <div class="producto-card">
                    <div class="producto-info">
                        <table>
                            <tr>
                                <td><strong>Producto:</strong></td>
                                <td>{{ stockVenta.descripcion }}</td>
                            </tr>
                            <tr>
                                <td><strong>Marca:</strong></td>
                                <td>{{ stockVenta.marca|default('-') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Modelo:</strong></td>
                                <td>{{ stockVenta.stock.producto.modelo|default('-') }}</td>
                            </tr>
                            <tr>
                                <td><strong>SKU:</strong></td>
                                <td>{{ stockVenta.stock.codigobarras|default('-') }}</td>
                            </tr>
                            <tr>
                                <td><strong>UPC:</strong></td>
                                <td>{{ stockVenta.stock.producto.codigobarrasuniversal|default('-') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Cantidad:</strong></td>
                                <td>{{ stockVenta.cantidad }} pz</td>
                            </tr>
                            <tr>
                                <td><strong>Precio:</strong></td>
                                <td>${{ stockVenta.preciofinal|number_format(2, '.', ',') }}</td>
                            </tr>
                        </table>
                    </div>

                    <!-- GRADUACIÓN -->
                    <div class="graduacion-data">
                        <h4 style="margin: 0 0 5px 0;">GRADUACIÓN</h4>
                        <table class="tabla-graduacion">
                            <tr>
                                <th></th>
                                <th>ESF</th>
                                <th>CIL</th>
                                <th>EJE</th>
                                <th>ADD</th>
                            </tr>
                            <tr>
                                <th>OD</th>
                                <td>{{ ordenlab.graduacion.odEsfera|default('-') }}</td>
                                <td>{{ ordenlab.graduacion.odCilindro|default('-') }}</td>
                                <td>{{ ordenlab.graduacion.odEje|default('-') }}</td>
                                <td>{{ ordenlab.graduacion.odAdicion|default('-') }}</td>
                            </tr>
                            <tr>
                                <th>OI</th>
                                <td>{{ ordenlab.graduacion.oiEsfera|default('-') }}</td>
                                <td>{{ ordenlab.graduacion.oiCilindro|default('-') }}</td>
                                <td>{{ ordenlab.graduacion.oiEje|default('-') }}</td>
                                <td>{{ ordenlab.graduacion.oiAdicion|default('-') }}</td>
                            </tr>
                        </table>

                        <!-- MEDIDAS -->
                        <table class="tabla-medidas">
                            <tr>
                                <th>DIP</th>
                                <td>{{ ordenlab.graduacion.distanciaPupilar|default('-') }}</td>
                            </tr>
                            <tr>
                                <th>AO</th>
                                <td>{{ ordenlab.graduacion.altura|default('-') }}</td>
                            </tr>
                            <tr>
                                <th>ACO</th>
                                <td>{{ ordenlab.graduacion._aco|default('-') }}</td>
                            </tr>
                        </table>

                        {% if ordenlab.graduacion.diagnostico or ordenlab.graduacion.notes %}
                            <!-- OBSERVACIONES -->
                            <table class="tabla-notas">
                                <tr>
                                    <th>DIAGNÓSTICO</th>
                                    <th>NOTAS</th>
                                </tr>
                                <tr>
                                    <td>{{ ordenlab.graduacion.diagnostico|default('-')|nl2br }}</td>
                                    <td>{{ ordenlab.graduacion.notes|default('-')|nl2br }}</td>
                                </tr>
                            </table>
                        {% endif %}

                        {% if ordenlab.graduacion.optometristaNombre %}
                            <!-- OPTOMETRISTA -->
                            <div style="text-align: center; margin-top: 15px; padding: 8px; background-color: #f8f9fa; border-radius: 4px;">
                                <p style="margin: 0; font-size: 10px; font-weight: 600; color: #2c3e50;">
                                    <strong>OPTOMETRISTA:</strong> {{ ordenlab.graduacion.optometristaNombre }}
                                </p>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- PIE -->
                <div class="pie">
                    <p>{{ pieTicket }}</p>
                    <p>Orden de Laboratorio Generada Automáticamente</p>
                </div>
            </div>
        {% endif %}
    {% endfor %}
{% else %}
    <div class="no-graduaciones">
        <h4>SIN GRADUACIONES</h4>
        <p>No se encontraron productos con graduación en esta venta.</p>
    </div>
{% endif %}

