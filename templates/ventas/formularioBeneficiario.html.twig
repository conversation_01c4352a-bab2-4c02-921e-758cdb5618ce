{{ form_start(form, {'attr': {'id': 'FormularioAgregarBeneficiario'}}) }}
{{ form_errors(form) }}

<div class="row">
    <div class="col-4 ">
        {{ form_row(form.nombre) }}
    </div>
    <div class="col-4 ">
        {{ form_row(form.apellidopaterno) }}
    </div>
    <div class="col-4 ">
        {{ form_row(form.apellidomaterno) }}
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        {{ form_row(form.telefono) }}
    </div>
    <div class="col-md-4 ">
        {{ form_row(form.empresaclienteIdempresacliente) }}
    </div>
    <div class="col-md-4">
        {{ form_row(form.fechanacimiento) }}
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        {{ form_row(form.beneficiarytype) }}
    </div>
</div>
<br>
<div class="row">
    <div class="col-md-3">
        {{ form_row(form.email) }}
    </div>
    <div class="col-md-3">
        {{ form_row(form.genero) }}
    </div>
    <div class="col-md-3">
        {{ form_row(form.numeroempleado) }}
    </div>
    <div class="col-md-3">
        {{ form_row(form.ocupacion) }}
    </div>
</div>

<div class="col-md-12 ">
    <br>
        <p id="mensajeErrorBeneficiario" style="color:red"></p>
    <br>
</div>

{{ form_end(form) }}
<style>


</style>

<script>

    jQuery(function ($) {
        $('.fecha').datetimepicker(
            {
                "pickTime": false,
                "pickDate": true,
                "minDate": "1\/1\/1900",
                "maxDate": null,
                "showToday": true,
                "language": "es_MX",
                "defaultDate": "",
                "disabledDates": [],
                "enabledDates": [],
                "icons": {
                    "time": "fa fa-clock-o",
                    "date": "fa fa-calendar",
                    "up": "fa fa-chevron-up",
                    "down": "fa fa-chevron-down"
                },
                "useStrict": false,
                "sideBySide": false,
                "daysOfWeekDisabled": [],
                "collapse": true,
                "calendarWeeks": false,
                "viewMode": "days",
                "minViewMode": "days"
                , "useCurrent": false,
                "useSeconds": false
            });

    });

    $(document).ready(function () {

        // Asegurar que el campo empresacliente no sea requerido
        $("#client_sale_empresaclienteIdempresacliente").prop("required", false);
        $("#client_sale_empresaclienteIdempresacliente").removeAttr("required");

        {% if isClient != '1' %}
        $(".beneficiary").removeClass("d-none");
        $("#client_sale_beneficiarytype").prop("required", true);
        {% endif %}

        // SOLUCIÓN SIMPLE: Backend garantiza que nunca hay null, solo cadenas vacías
        {% if Cliente is defined and Cliente.empresaclienteIdempresacliente is defined %}
        $("#empresa-cliente").val("{{ Cliente.empresaclienteIdempresacliente.nombre|default('1', true) }}");
        $("#empresa-cliente-id").val("{{ Cliente.empresaclienteIdempresacliente.idempresacliente }}");
        {% endif %}

        {% if exito == "true" %}

        {% if isClient == "1" and Cliente.idcliente is defined and Cliente.idcliente != null %}
        $("#empresa-cliente").val("{{ Cliente.empresaclienteIdempresacliente ? Cliente.empresaclienteIdempresacliente.nombre : '' }}");
        $("#empresa-cliente-id").val("{{ Cliente.empresaclienteIdempresacliente ? Cliente.empresaclienteIdempresacliente.idempresacliente : '' }}");

        $('#diasCredito')
            .val("{{ Cliente.diascredito }}")
            .trigger('change');

        $('#cliente-telefono').val("{{ Cliente.telefono }}");
        $('#sin-convenio-email-cliente').val("{{ Cliente.email }}");
        $('#apellidoP').val("{{ Cliente.apellidopaterno }}");
        $('#apellidoM').val("{{ Cliente.apellidomaterno }}");
        $('#nombre').val("{{ Cliente.nombre }}");
        $('#numero-empleado').val("{{ Cliente.numeroEmpleado }}");
        $("#cliente-id").val("{{ Cliente.idcliente }}");
        $("#boton-agregar-beneficiario").removeClass("d-none");
        {% endif %}

        beneficiaryTable();
        $('#modalAgregarBeneficiarios').mod('hide');

        {% endif %}


    });

    function guardarFormularioBeneficiario() {


        idcliente = $("#cliente-id").val();


        var form = document.getElementById("FormularioAgregarBeneficiario");

        if (form.checkValidity()) {

            if (parseInt(idcliente) > 0 || '1' == "{{ isClient }}") {

                var formData = new FormData(form);

                $.ajax({
                    url: "{{ path('agregar-formulario-beneficiario') }}" + "?idcliente=" + idcliente + "&isClient=" + "{{ isClient }}",
                    type: 'POST',
                    data: formData,
                    success: function (html) {

                        $("#formularioBeneficiario").html(html);
                        //buscarBeneficiarios(idcliente);


                    },
                    cache: false,
                    contentType: false,
                    processData: false
                });

            } else $("#mensajeErrorBeneficiario").text("No se seleccionó el cliente");

        } else form.reportValidity();

    }

</script>