<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Barlow+Condensed:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
      rel="stylesheet">

<style>
    @page {
        margin-left: 0.4cm;
        margin-right: 0;
    }

    .ticket {
        width: 270px;
        font-family: "Barlow Condensed", sans-serif;
        font-weight: 400;
        font-style: normal;
        font-size: 11px;
    }

    .img-ticket {
        width: 90%;
    }

    .encabezado {
        text-align: center;
    }

    .informacion {
        display: block;
        line-height: 5px;
    }

    .articulos th, .articulos td {
        font-size: 10px;
    }

    .texto-ticket {
        line-height: 1;
    }
</style>

<div class="ticket">
    <div class="encabezado">
        <img class="img-ticket" src="{{ logo }}" alt="Logo de la empresa"/>
        <h4>{{ razonsocial }}</h4>
        {% if esCotizacion =="1" %}
            <h2>COTIZACIÓN</h2>
        {% else %}
            <h2>VENTA</h2>
        {% endif %}
    </div>
    <div class="informacion">
        <p class="texto-ticket">Sucursal: {{nombreSucursal}}</p>
        <p class="texto-ticket">{{direccionSucursal}}</p>
        <p class="texto-ticket">Teléfono: {{telefonoSucursal}}</p>
        <p class="texto-ticket">Vendedor: {{nombreVendedor}}</p>

        {% if convenio !="" %}
            <p class="texto-ticket">Tipo de venta: {{ convenio }}</p>
        {% endif %}
        {% if authorizationNumber !="" %}
            <p class="texto-ticket">Número de autorización: {{ authorizationNumber }}</p>
        {% endif %}
        <p class="texto-ticket">Folio: <strong>{{ prefijo }}-{{ folio }}</strong></p><br>

        <p class="texto-ticket">Cliente: {{ cliente }}</p>
        <p class="texto-ticket">Teléfono: {{ clienteTelefono }}</p>
        <p class="texto-ticket">Correo electrónico: {{ sinConvenioEmailCliente }}</p>
        {% if numeroEmpleado !="" and Tipoventa.mostrarnumeroempleado == '1' %}
            <p class="texto-ticket">Empleado: {{ numeroEmpleado }}</p>
        {% endif %}
        {% if unidadNombre !="" and Tipoventa.mostrarunidadprocedencia == '1' %}
            <p class="texto-ticket">Unidad: {{ unidadNombre }}</p><br>
        {% endif %}

        {% if Tipoventa.mostrarbeneficiario == '1' and beneficiaries != null %}
            <p class="texto-ticket"><strong>Beneficiario{% if beneficiaries|length > 1 %}s{% endif %}</strong></p>
            {% for beneficiary in beneficiaries %}
                <p class="texto-ticket">Nombre: {{ beneficiary.fullName }}</p>
                <p class="texto-ticket">Teléfono: {{ beneficiary.telefono }}</p>
                <p class="texto-ticket">Correo electrónico: {{ beneficiary.email }}</p><br>
            {% endfor %}
        {% endif %}

        <p>Fecha y Hora: {{ fecha }}</p>
        <p>----------------------------------------------------------------------------</p>
    </div>
    <div class="topes-autorizados">
        {% if Tipoventa.mostrartopesautorizados =="1" and ommitableOverride == false %}
            <h4>TOPES AUTORIZADOS</h4>
            <p class="ticket">
            {% autoescape %}
                <table class="ticket" style="width:100%; color:#000000">
                    {% set frameCount = 1 %}
                    {% for StockVenta in stockVentas %}
                        {% if StockVenta.preciofinal > 0 %}
                            <tr style="text-align:start;color:#000000">
                                <td>
                                    {% if StockVenta.tipoproducto == '1'  %}
                                        {{StockVenta.descripcion}}&nbsp;{% if frameCount > 1 %}{{frameCount}}{% endif %}
                                        {% set frameCount = frameCount + 1 %}
                                    {% else %}
                                        {{StockVenta.descripcion}}
                                    {% endif %}
                                </td>
                                <td class="cantidad" style=" text-align:start">${{StockVenta.preciofinal  | number_format(2, '.', ',')}}</td>
                            </tr>
                        {% endif %}
                    {% endfor %}
                </table>
            {% endautoescape %}
            </p>
            <p>----------------------------------------------------------------------------</p>
        {% endif %}
    </div>
    <div class="articulos-comprados">
        <table class="articulos" style="width:100%;">
            <thead>
            <tr>
                <td colspan="100%">
                    <h4>ARTÍCULO(S) COMPRADO(S)</h4>
                </td>
            </tr>
            <tr>
                <th style="text-align:center">Cantidad</th>
                <th style="text-align:center">Código</th>
                {% if Tipoventa.mostrardetalleprecio == "1" %}
                    <th style="text-align:right">Subtotal</th>
                    <th style="text-align:right">Descuento</th>
                    <th style="text-align:right">Total</th>
                {% endif %}
            </tr>
            </thead>
            <tbody>
            {% for producto in productos %}
                <tr style="text-align:center;">
                    <td style=" text-align: center">
                        {{ producto.productoCantidad }}
                    </td>
                    <td>
                        {% if producto.codigobarras !="" %}
                            {{ producto.codigobarras }}
                        {% else %}
                            {{ producto.codigobarrasuniversal }}
                        {% endif %}
                    </td>
                    {% if Tipoventa.mostrardetalleprecio == "1" %}
                        <td style="text-align:center">${{ producto.productoPrecio  | number_format(2, '.', ',') }}</td>
                        <td style="text-align:center">{{ producto.productoDescuento  | number_format(0, '.', ',') }}%
                        </td>
                        <td style="text-align:center">
                            ${{ (producto.productoPrecioFinal * producto.productoCantidad) | number_format(2, '.', ',') }}</td>
                    {% endif %}
                </tr>
                <tr>
                    <td colspan="100%"><strong>Descripción: </strong>
                        {{ producto.descripcion }}
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
        <p>----------------------------------------------------------------------------</p>
    </div>
    <div class="total-venta">
        <table class="ticket" style="width:100%;">
            <tr>
                <td style="width:40%; text-align:left">Subtotal</td>
                <td style="width:60%; text-align:right">${{ subtotal  | number_format(2, '.', ',') }}</td>
            </tr>
            <tr>
                <td style="width:40%; text-align:left">IVA</td>
                <td style="width:60%; text-align:right"> ${{ iva  | number_format(2, '.', ',') }}</td>
            </tr>
            <tr>
                <td style="width:40%; text-align:left"><strong>Total</strong></td>
                <td style="width:60%; text-align:right"><strong> ${{ total | number_format(2, '.', ',') }}</strong></td>
            </tr>
        </table>


        {% if Tipoventa.pagoalfinal == "0" %}
            <table class="ticket" style="width:100%;">
                <tr>
                    <td style="width:40%; text-align:left">Pagado</td>
                    <td style="width:60%; text-align:right">
                        ${{ totalPagado  | number_format(2, '.', ',') }}</td>
                </tr>
                <tr>
                    <td style="width:40%; text-align:left">Restante</td>
                    <td style="width:60%; text-align:right">
                        ${{ (total -  totalPagado) | number_format(2, '.', ',') }}</td>
                </tr>
            </table>
        {% endif %}
        {% if cuponDescuento !="" %}
            <table class="ticket" style="width:100%;">
                <tr>
                    <td style="width:30%; text-align:left">Cupón:</td>
                    <td style="width:70%; text-align:right;font-weight: bold"> {{ cuponDescuento }}</td>
                </tr>
            </table>
        {% endif %}
        {% if pagos is not empty %}
            <p>----------------------------------------------------------------------------</p>
            <h4>PAGO(S)</h4>
            <table class="ticket">
                <tr style="text-align: center">
                    <th>
                        Monto
                    </th>
                    <th>
                        Tipo de Pago
                    </th>
                    <th>
                        Fecha
                    </th>
                </tr>
                {% for pago in pagos %}
                    <tr style="text-align: center">
                        <td>
                            ${{ pago.monto | number_format(2, '.', ',') }}
                        </td>
                        <td>
                            {{ pago.tipopago }}
                        </td>
                        <td>
                            {{ pago.fecha | date("g:ia d/m/Y") }}
                        </td>
                    </tr>
                {% endfor %}
            </table>
            {% if pagos[0].mesesintereses %}
                <strong>
                    <p class="centrado ticket" style="width:302px;text-align: center;">
                        {{ pagos[0].mesesintereses }} MSI cargo mensual:
                        ${{ (total / pagos[0].mesesintereses) | number_format(2, '.', ',') }}
                    </p>
                </strong>
            {% endif %}
        {% endif %}

    </div>
    <div class="firma">
        <br><br><br>
        <table class="ticket">
            <tr>
                <td style="width:100%; text-align:center;border-top: 1px solid #C00;">
                    Nombre y firma
                </td>
            </tr>
        </table>
    </div>
    <div class="pie">
        {% if Tipoventa.mostrarcampogarantia == "1" %}
            <p>----------------------------------------------------------------------------</p>
            <p class="ticket" style="text-align: center;">
                {{ Tipoventa.notagarantia }}
            </p>
        {% endif %}

        <p>----------------------------------------------------------------------------</p>
        <p class="centrado ticket" style=" text-align:center; font-weight: 700;">
            {{ pieTicket|nl2br }}
        </p>
    </div>
</div>
