{% if opcion == 'graduacion' %}
    {# Graduation ticket view #}
    <div class="graduation-ticket">
        <object data="{{ asset(carpetaDocumentos ~ '/' ~ nombreDocumento) }}" 
                type="application/pdf" 
                width="100%" 
                height="600px">
            <p>El archivo no puede ser mostrado. <a href="{{ asset(carpetaDocumentos ~ '/' ~ nombreDocumento) }}" target="_blank">Descargar PDF</a></p>
        </object>
    </div>
{% else %}
    {# Regular and special ticket view #}
    <div class="document-viewer">
        <object data="{{ asset(carpetaDocumentos ~ '/' ~ nombreDocumento) }}" 
                type="application/pdf" 
                width="100%" 
                height="600px">
            <p>El archivo no puede ser mostrado. <a href="{{ asset(carpetaDocumentos ~ '/' ~ nombreDocumento) }}" target="_blank">Descargar PDF</a></p>
        </object>
        
        {% if venta %}
        <div class="venta-info mt-3">
            <div class="venta-details">
                <div class="ticket-info">
                    <strong>Folio:</strong> {{ venta.folio }}
                </div>
                <div class="ticket-info">
                    <strong>Fecha:</strong> {{ venta.fechacreacion|date('d/m/Y H:i') }}
                </div>
                {% if venta.total is defined %}
                <div class="ticket-info">
                    <strong>Total:</strong> ${{ venta.total|number_format(2) }}
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
{% endif %}

<style>
.document-viewer, .graduation-ticket {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.venta-info, .graduation-info {
    margin-top: 15px;
    padding: 15px;
    border-top: 1px solid #eee;
}

.ticket-info, .graduation-detail {
    margin: 5px 0;
}

.graduation-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-top: 10px;
}
</style>
