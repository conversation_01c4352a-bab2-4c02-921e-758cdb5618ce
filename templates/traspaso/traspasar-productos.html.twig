{% if exito %}
  <h2 class="text-warning">Traspaso Pendiente</h2>
  <h2 class="text-warning">Revisar Sucursal de Destino</h2>
    <script>
        $("#contenedor-productos").html("");
        $("#sucursalSalida").val("");
        $("#sucursalDestino").val("");
        $("#cantidad-productos").html("");
        $("#razonTraspaso").val("");

        checkProducts = new Set();

    </script>
{% else %}
    <table class="table table-condesed">
        <thead>
        <tr>
            <th><span class="text-bold">
                    No se pasó nada<br>Revise la información
                </span></th>
        </tr>
        <tr>
            <th>{{ msj }}</th>
        </tr>
        </thead>
        <tbody>
        {% if (productosNoEncontrados | length) > 0 %}
        <tr>
            <td>Artículo no encontrados o con stock insuficiente:</td>
        </tr>
        {% for producto in productosNoEncontrados %}
            <tr>
                <td><span class="text-danger">Código: {{ producto[0] }}</span></td>
            </tr>
        {% endfor %}
        {% endif %}
        {% if (productosApartados | length) > 0 %}
        <tr>
            <td>Artículo Apartados:</td>
        </tr>
        {% for codigo in productosApartados %}
            <tr>
                <td><span class="text-warning">{{ codigo[0] }}</span></td>
            </tr>
        {% endfor %}
        {% endif %}
        </tbody>
    </table>
{% endif %}