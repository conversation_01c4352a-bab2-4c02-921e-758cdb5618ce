<tr id="producto-{{ id }}">
    <td>
        <label for="">{{ sucursal }}</label>
    </td>
    <td>
        <label for="">{{ modelo }}</label>
        <input type="text" class="form-control my-0 producto-codigo" data-idstock="{{ idstock }}" value="{{ codigo }}" disabled >
    </td>
    <td>
        <input type="text" {% if masivounico =="1" %}disabled{% endif %} class="form-control producto-codigo-cantidad" id="cantidad-{{ idstock }}" value="{{ cantidad|default('1') }}" onkeyup="contarProductos()" onkeydown="contarProductos()">
    </td>
    <td>
        <button class="btn btn-warning btn-sm" onclick="quitarProducto('{{ id }}', '{{codigo}}')">Quitar</button>
    </td>
</tr>
