{% extends 'admin/layout.html.twig' %}

{% block content %}

{% set path = (profilePicExists) ? 'uploads/profilePics/' ~ app.user.idusuario ~ '/' ~ app.user.profilepicture : 'img/profilepic-placeholder.svg' %}
<input id="url-admin-cambiar-contrasena" type="hidden" value="{{ path('admin-cambiar-contrasena') }}">
<div class="container-fluid">   
    <div class="card bg-profile text-white p-md-5 py-5" >
        <h1 class="text-center my-5">Mi perfil</h1>
        <div class="p-md-5">
        {% if app.user %}
            <div class="row">
                <div class="col-xl-4 d-flex p-5 justify-content-center align-items-start">
                    <div class="w-50 ratio ratio-1x1 rounded-circle" 
                    style="
                        background-image: url('{{asset( path )}}');
                        background-size: cover;
                        background-position: center center;
                    "
                >
                        
                    </div>
                </div>
                <div class="col-xl-8 container-fluid">
                    <div class="row text-center ">
                        <div class="col-xl-4 px-3 ">
                            <label class="text-start fs-3 my-4">Nombre(s)</label>
                            <h3 class="my-auto bg-profile-input rounded-5 py-3 text-profile">{{ app.user.nombre }}</h3>
                        </div>
                        <div class="col-xl-4 px-3 ">
                            <label class="text-start fs-3 my-4">Apellido paterno</label>
                            <h3 class="my-auto bg-profile-input rounded-5 py-3 text-profile">{{ app.user.apellidopaterno }}</h3>
                        </div>
                        <div class="col-xl-4 px-3 ">
                            <label class="text-start fs-3 my-4">Apellido materno</label>
                            <h3 class="my-auto bg-profile-input rounded-5 py-3 text-profile">{{ app.user.apellidomaterno }}</h3>
                        </div>
                    </div>
                    <div class="row text-center ">
                        <div class="col-xl-6 px-3 ">
                            <label class="text-start fs-3 my-4">Correo electrónico</label>
                            <h3 class="my-auto bg-profile-input rounded-5 py-3 text-profile">{{ app.user.username }}</h3>
                        </div>
                        <div class="col-xl-6 px-3 ">
                            <label class="text-start fs-3 my-4">Puesto</label>
                            <h3 class="my-auto bg-profile-input rounded-5 py-3 text-profile">{{ app.user.puesto }}</h3>
                        </div>
                    </div>
                    <div class="row text-center ">
                        <div class="col-xl-6 px-3 ">
                            <label class="text-start fs-3 my-4">Empresa</label>
                            <h3 class="my-auto bg-profile-input rounded-5 py-3 text-profile">{{ app.user.sucursalIdsucursal.empresaIdempresa.nombre }}</h3>
                        </div>
                        <div class="col-xl-6 px-3 ">
                            <label class="text-start fs-3 my-4">Sucursal</label>
                            {% if app.user.sucursalIdsucursal %}
                                <select id="sucursal-select" class="my-auto form-control bg-profile-input rounded-5 py-3 text-profile"
                                    {% for sucursal in sucursales %}
                                        <option value="{{ sucursal.getIdsucursal() }}" 
                                            {% if app.user.sucursalIdsucursal and sucursal.getIdsucursal() == app.user.sucursalIdsucursal.getIdsucursal() %}
                                                selected
                                            {% endif %}>
                                            {{ sucursal.getNombre() }}
                                        </option>
                                    {% endfor %}
                                </select>
                            {% else %}
                                <p class="text-danger">No tienes una sucursal asignada.</p>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row text-center pt-5 justify-content-between">
                        <div class="col-xl-6 px-3 mt-5">
                            <button onclick='$("#change-pass-modal").modal("show");' class="btn btn-danger rounded-5 img-fluid fs-3 btn-lg">
                                Restablecer contraseña
                            </button>
                        </div>
                        <div class="col-xl-6 px-3 mt-5">
                            <button onclick="getQR()" class="btn btn-warning rounded-5 img-fluid fs-3 btn-lg">
                                Habilitar autenticación segura
                            </button>
                        </div>
                    </div>
                    
                    
                </div>
            </div>
        {% else %}
        <div class="d-flex justify-content-center">
            <div class="text-center">
                <h3 class="mb-5">No hay un usuario conectado</h3>
                <a href="{{ path('admin_login') }}" class="btn btn-primary w-100">Iniciar sesión</a>
            </div>
        </div>
        {% endif %}
        </div>
    </div>

</div>

<div class="modal fade" id="qr-visor" tabindex="-1" aria-labelledby="qr-visor" aria-hidden="true">
    <div class="modal-dialog ">
        <div class="modal-content">
            <div class="modal-header" style="background:#3C4F81">
                <h3 class="mb-3 text-white">QR Google Authenticator</h3>
            </div>
            <div class="modal-body d-flex justify-content-center align-items-center flex-column">
                <h3>Escanea el siguiente código:</h3>
                <div id="qr-modal-body"></div>
            </div>
            <div class="modal-footer" style="background:#3C4F81">
                <button type="button" class="btn btn-warning" onclick='$("#qr-visor").modal("hide");'>Cerrar</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="change-pass-modal" tabindex="-1" aria-labelledby="qr-visor" aria-hidden="true">
    <div class="modal-dialog ">
        <div class="modal-content">
            <div class="modal-header" style="background:#3C4F81">
                <h3 class="mb-3 text-white">Cambiar contraseña</h3>
            </div>
            <div class="modal-body p-5">
                <div>
                    <h4>Características obligatorias de la contraseña:</h4>
                    {% include 'profile/passwordValidation.html.twig' with {'passId1': "inputPassword1", 'passId2': "inputPassword2"} %}
                    
                    <label for="inputPassword" class="form-label">Contraseña nueva</label>
                    <input type="password" class="form-control mb-3 password-input" id="inputPassword1" autocomplete="new-password" onkeydown = "checkPassword()" onkeyup = "checkPassword()">
                    <label for="inputPassword" class="form-label">Confirmar contraseña nueva</label>
                    <input type="password" class="form-control mb-3 password-input" id="inputPassword2"  autocomplete="new-password" onkeydown = "checkPassword()" onkeyup = "checkPassword()">

                    <div id="error-list-container" class="p-5 d-none">
                        <div class="text-start p-5 border rounded-4 bg-light">
                            <ul id="error-list" class="text-danger"></ul>
                        </div>
                    </div>

                    <button type="button" class="btn btn-secondary" onclick="validardatos({{ app.user.idusuario }})">Cambiar</button>
                </div>
            </div>
            <div class="modal-footer" style="background:#3C4F81">
                <button type="button" class="btn btn-warning" onclick='$("#change-pass-modal").modal("hide");'>Cerrar</button>
            </div>
        </div>
    </div>
</div>


<script>

    function getQR(){
        $("#qr-visor").modal("show");
        $.ajax({
            url: "{{path('google-qr-code')}}", 
            type: 'GET',
            beforeSend: loadingGif("qr-modal-body"),
            xhrFields: {
                responseType: 'blob'
            }
        }).done(function( response ) {
            const url = URL.createObjectURL(response);
            $('#qr-modal-body').html('<img class="img-fluid" src="' + url + '" alt="QR Code"/>');
        }).fail(function() {
            alert( "error" );
        });
    }

    function validardatos(idusuario) {
        let pass1 = $("#inputPassword1").val();
        let pass2 = $("#inputPassword2").val();
        $("#error-list").html('');
        $("#error-list-container").addClass('d-none');
        let url=$("#url-admin-cambiar-contrasena").val()+"/"+idusuario;

        const constraints = [
            {
                regex: /^.{8,4096}$/
            },
            {
                regex: /[A-Z]/
            },
            {
                regex: /[a-z]/
            },
            {
                regex: /\d/
            },
            {
                regex: /[^a-zA-Z\d]/
            }
        ]

        let isValid = true

        for (const constraint of constraints) {
            if (!constraint.regex.test(pass1)) isValid = false
        }

        isValid = isValid && (pass1 == pass2)

        if (isValid){
            $.ajax({
                url: url,
                method: "POST",
                data: {pass:pass1},
                dataType: "json",
            }).done(function(reponse) {
                
                if (reponse.errors.length > 0){
                    let curErrors = "";
                    for (const msg of reponse.errors){
                        curErrors+="<li>"+msg+"</li>"
                    }
                    $("#error-list-container").removeClass('d-none');
                    $("#error-list").html(curErrors);
                }

                if(reponse.exito){
                    Swal.fire({
                        icon: 'success',
                        title: 'Contraseña restablecida correctamente',
                        showConfirmButton: true,
                        timer: 2500
                    })

                    $(".password-input").val('')
                    $('.password-check').removeClass('bg-correct');
                }else{
                    Swal.fire({
                        icon: 'info',
                        title: reponse.msj,
                    })
                }
            }).fail(function(e) {
                alert( "error" );
            });
        }
        else showPasswordErrors()
    }

    $(document).ready(function() {
        $('#sucursal-select').change(function() {
            let sucursalId = $(this).val();

            $.ajax({
                url: "{{ path('profile_change_sucursal') }}",
                type: 'POST',
                data: { sucursal_id: sucursalId },
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token("change_sucursal") }}'
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Sucursal cambiada con éxito',
                            showConfirmButton: false,
                            timer: 2000
                        }).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error al cambiar sucursal',
                            text: response.message
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'No se pudo cambiar la sucursal'
                    });
                }
            });
        });
    });

    
</script>
{% endblock %}





