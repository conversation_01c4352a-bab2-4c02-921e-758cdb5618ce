<style>
    .bg-correct{
        background: rgb(180 255 162);
    }
    .bg-incorrect{
        background: rgb(242 145 145);
    }
</style>
<div class="text-start mt-3">
    <ul class="list-group">
        <li id="min-characters" class="list-group-item password-check">Debe tener al menos 8 caracteres.</li>
        <li id="min-upper" class="list-group-item password-check">Debe contener al menos una letra mayúscula.</li>
        <li id="min-lower" class="list-group-item password-check">Debe contener al menos una letra minúscula.</li>
        <li id="min-number" class="list-group-item password-check">Debe contener al menos un número.</li>
        <li id="min-special" class="list-group-item password-check">Debe contener al menos un carácter especial.</li>
        <li id="no-match" class="list-group-item password-check">Las contraseñas deben coincidir.</li>
    </ul>
</div>
<script>

    function checkPassword(){

        $(".password-check").removeClass("bg-incorrect")

        password = $("#{{passId1}}").val()
        password2 = $("#{{passId2}}").val()

        const constraints = [
            {
                id: 'min-characters',
                regex: /^.{8,4096}$/
            },
            {
                id: 'min-upper',
                regex: /[A-Z]/
            },
            {
                id: 'min-lower',
                regex: /[a-z]/
            },
            {
                id: 'min-number',
                regex: /\d/
            },
            {
                id: 'min-special',
                regex: /[^a-zA-Z\d]/
            },
            {
                id: 'no-match',
                regex: ''
            }
        ]

        for (const constraint of constraints) {
            if (constraint.id != "no-match"){
                if (constraint.regex.test(password)) $("#"+constraint.id).addClass("bg-correct")
                else $("#"+constraint.id).removeClass("bg-correct")
            } else {
                if (password == password2 && password) $("#"+constraint.id).addClass("bg-correct")
                else $("#"+constraint.id).removeClass("bg-correct")
            }
        }
    }

    function showPasswordErrors(){
        $('.password-check:not(.bg-correct)').addClass('bg-incorrect');
    }

</script>