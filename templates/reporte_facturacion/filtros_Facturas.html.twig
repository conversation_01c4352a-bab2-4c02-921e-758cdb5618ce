<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.x/font/bootstrap-icons.css" rel="stylesheet">
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="table-responsive">
                <div class="col-md-12 col-sm-12">
                    <div class="card">
                        <div class="card-body col-md-12 col-sm-12">
                            <h5 class="card-title">Filtros:</h5>
                            <div class="row">
                                <div class="col-md-4 col-sm-6 d-flex align-items-center">
                                    <select name="empresa" id="idempresa" class="form-control margen-superior">
                                        <option value="-1">Seleccione una empresa</option>
                                        {% for filtroEmpresa in filtrosEmpresas %}
                                        <option value="{{ filtroEmpresa.idempresa }}">{{ filtroEmpresa.Empresa }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-4 col-sm-6 d-flex align-items-center">
                                    <select name="sucursal" id="idsucursal" class="form-control margen-superior">
                                        <option value="-1">Seleccione una sucursal</option>
                                        <option select="selected" value="todasSucursales">TODAS LAS SUCURSALES</option>
                                        {% for filtroSucursal in filtroSucursales %}
                                        <option value="{{ filtroSucursal.idsucursal }}">{{ filtroSucursal.Sucursal }}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <h5 class="card-title">Ventas:</h5>
                                <div class="col-md-4 col-sm-6 d-flex align-items-center">
                                    <select name="ventasfacturadas" id="ventasfacturadas" class="form-control margen-superior">
                                        <option value="-1">Ventas</option>
                                        <option select="selected" value="todasSucursales">TODAS LAS SUCURSALES</option>
                                        <option select="selected" value="facturadas">FACTURADAS</option>
                                        <option select="selected" value="nofacturadas">NO FACTURADAS</option>
                                    </select>
                                </div>

                                    <div class="d-flex align-items-center justify-content-between">
                                        <label class="lab reporte">Rango por días:</label>
                                    </div> 
                                    
                                    <div class="input-daterange input-group rango-tiempo" id="idfilter-calender">
                                        <!--first input -->
                                            <div class="col-md-5 col-sm-6">
                                                <input id="fecha-inicio-rango-dia" type="text" autocomplete="off" class="input-sm form-control" name="start" />
                                            </div>

                                            <div>
                                                <span class="input-group-addon"> a </span>
                                            </div>
                                        <!--second input -->
                                            <div class="col-md-5 col-sm-6">
                                                <input id="fecha-fin-rango-dia" type="text" autocomplete="off" class="input-sm form-control" name="end" />
                                            </div>
                                        <!--second input -->
                                        <div class="col-md-1">
                                            <button class="btn btn-warning" onclick="resetRangoFechaDias()"><i class="fa fa-eraser" aria-hidden="true"></i></button>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <button type="button" class="btn btn-primary mt-3" onclick="ventasFacturacion();">Buscar</button>
                                    </div>

                                </div>
                            </div>  
                        </div>
                        <div class="card">
                        <div class="card-body col-md-12 col-sm-12">
                            <div id="ventasFacturacion"></div>      
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
$(document).ready(function () {
    let table = new DataTable('#tablaFacturasReporte', {
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/es-ES.json',
        },
    });
});


jQuery(function ($) {
    $('#fecha-inicio-rango-dia').datetimepicker({
        "pickTime": false,
        "pickDate": true,
        "minDate": "1/1/1900",
        "maxDate": null,
        "showToday": true,
        "language": "es_MX",
        "defaultDate": "",
        "disabledDates": [],
        "enabledDates": [],
        "icons": {
            "time": "fa fa-clock-o",
            "date": "fa fa-calendar",
            "up": "fa fa-chevron-up",
            "down": "fa fa-chevron-down"
        },
        "useStrict": false,
        "sideBySide": false,
        "daysOfWeekDisabled": [],
        "collapse": true,
        "calendarWeeks": false,
        "viewMode": "days",
        "minViewMode": "days",
        "useCurrent": false,
        "useSeconds": false
    });

    $('#fecha-fin-rango-dia').datetimepicker({
        "pickTime": false,
        "pickDate": true,
        "minDate": "1/1/1900",
        "maxDate": null,
        "showToday": true,
        "language": "es_MX",
        "defaultDate": "",
        "disabledDates": [],
        "enabledDates": [],
        "icons": {
            "time": "fa fa-clock-o",
            "date": "fa fa-calendar",
            "up": "fa fa-chevron-up",
            "down": "fa fa-chevron-down"
        },
        "useStrict": false,
        "sideBySide": false,
        "daysOfWeekDisabled": [],
        "collapse": true,
        "calendarWeeks": false,
        "viewMode": "days",
        "minViewMode": "days",
        "useCurrent": false,
        "useSeconds": false
    });
});

function resetRangoFechaDias() {
    $("#fecha-inicio-rango-dia").val("");
    $("#fecha-fin-rango-dia").val("");
}


var idempresa = $("#idempresa").val();
$('#idempresa').val(idempresa);


function ventasFacturacion() {
    
    var fechaInicio = document.getElementById('fecha-inicio-rango-dia').value;
    var fechaFin = document.getElementById('fecha-fin-rango-dia').value;
    var idempresa = $('#idempresa').val();
    var idsucursal = $("#idsucursal").val();
    var ventasfacturadas = $("#ventasfacturadas").val();
    $('#idsucursal').val(idsucursal);

    var datos = {
        fechaInicio: fechaInicio,
        fechaFin: fechaFin,
        idsucursal: idsucursal,
        idempresa: idempresa,
        ventasfacturadas:ventasfacturadas,
    };

    console.log(datos);

    $.ajax({
        url: "{{path('ventas-facturacion')}}",
        data: datos,
        dataType: "html",
    }).done(function (html) {
        $("#ventasFacturacion").html(html);
    }).fail(function (hmtl) {
        console.error("Error en obtener-ventas-facturacion: ", error);
    });
}


</script>

