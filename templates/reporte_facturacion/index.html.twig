{% extends 'admin/layout.html.twig' %}
{% block title %}Reporte de Facturación{% endblock %}
{% block content %}
<link href="//cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css" rel="stylesheet">
<script src="//cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js" rel="stylesheet"></script>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header card-header-info bg-primary">
                    <div class="row">
                        <div class="col-md-12">
                            <h4 class="card-title">Reporte Ventas Facturadas </h4>
                        </div>
                    </div>
                </div>
                <br>
                    <div class="collapse" id="filtrosOrdenes">
                        <div class="container-fluid" id="tableFacturacion"></div>
                    </div>
                <br>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function(){
        filtrosFacturas();
        $("#filtrosOrdenes").collapse('show');
    });
        
    function filtrosFacturas(){
        url="{{path('filtros-facturacion')}}";
        $.ajax({
            url: url,
            type: 'GET',
            data: {},
            dataType: "html"
        }).done(function( html ){
            $("#tableFacturacion").html(html);
        }).fail(function(){
            alert("Error");
        });
    }
    </script>

{% endblock %}