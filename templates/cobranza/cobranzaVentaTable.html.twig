<div class="row justify-content-md-center">
    <div class="col-md-12 text-right">
        <button class="btn btn-warning" onclick="regresarClientes()">Regresar</button>
    </div>
    <div class="col-md-10 responsiveness-table-ventas-cobranza">
        <input id="url-visor-documentos" type="hidden" value="{{path('visor-documentos')}}" disable>
        <table class="table table-ventas-cobranza" id="tablaVentasCliente">
            <thead>
                <tr class="text-start">

                        <th class="text-start text-center" scope="col">Status</th>
                        <th class="text-start text-center" scope="col">Folio</th>
                        <th class="text-start text-center" scope="col">Titular</th>
                        <th class="text-start text-center" scope="col">Beneficiario</th>
                        <th class="text-start text-center" scope="col">Tipo de venta</th>
                        <th class="text-start text-center" scope="col">Fecha de creación</th>
                        <th class="text-start text-center hidden" scope="col">Hora de creación</th>
                        <th class="text-start text-center" scope="col">Fecha de venta</th>
                        <th class="text-start text-center hidden" scope="col">Hora de venta</th>
                        <th class="text-start text-center" scope="col">Último pago</th>
                        <th class="text-start text-center hidden" scope="col">Hora de último pago</th>
                        <th class="text-start text-center" scope="col">Dias de crédito</th>
                        <th class="text-start text-center" scope="col">Dias de la venta</th>
                        <th class="text-start text-center" scope="col">Empresa</th>
                        <th class="text-start text-center" scope="col">Sucursal</th>
                        <th class="text-start text-center" scope="col">Comentarios</th>
                        <th class="text-start text-center" scope="col">Monto de la venta</th>
                        <th class="text-start text-center" scope="col">Monto pagado</th>
                        <th class="text-start text-center" scope="col">Saldo deudor</th>
                        <th class="text-start text-center" scope="col"></th>

                    </tr>

            </thead>
            <tbody id = "tableBodyBuscarCliente">

                {% set suma = 0 %}
                {% for index,venta in ventasClienteNoLiquidadas %}
                    {% set diferenciaDias = venta.diascredito - datosextra['diasActiva'][venta.idventa] %}
                    
                    <tr>
                      <td class="text-center align-middle bg-secondary">
                      
                        {% if diferenciaDias < 0  %} 
                            <span class="d-none">1</span>
                            <p class="fw-bold" style="color: #ff0000;">Vencido</p>
                        {% elseif diferenciaDias <= 10 %}
                            <span class="d-none">2</span>
                            <p class="fw-bold" style="color: #fbf30e;">Por vencer</p>
                        {% else %}
                            <span class="d-none">3</span>
                            <p class="fw-bold" style="color: #26ec18;">Vigente</p>
                        {% endif %}
                      
                      
                      </td>

                      <td class='text-center align-middle'>  <a href="/admin/venta/{{venta.idventa}}/detalleVenta" target="_blank">{{venta.folio}}</a></td>

                      <td class="text-center align-middle ">{{ venta.clienteIdcliente.getNombreCompleto() }}</td>

                      <td class="text-center align-middle ">

                        {% if beneficiaries[venta.idventa] is defined %}
                            {% for beneficiary in beneficiaries[venta.idventa] %}
                                {% if beneficiary is not empty %}
                                    {% if loop.last %} {{beneficiary.fullName}}
                                    {% else %} {{beneficiary.fullName}},
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                      
                      </td>

                      <td class="text-center align-middle ">{{ venta.tipoventaIdtipoventa.nombre }}</td>

                      <td class="text-center align-middle ">{% if venta.fechacreacion is defined %}{{ venta.fechacreacion |date("d/m/Y") }}{% endif %}</td>

                      <td class="text-center align-middle hidden">{% if venta.fechacreacion is defined %}{{ venta.fechacreacion |date("g:ia") }}{% endif %}</td>

                      <td class="text-center align-middle ">{% if venta.fechaventa is defined %}{{ venta.fechaventa |date("d/m/Y") }}{% endif %}</td>

                      <td class="text-center align-middle hidden">{% if venta.fechaventa is defined %}{{ venta.fechaventa |date("g:ia") }}{% endif %}</td>


                      <td class="text-center align-middle ">
                      {% if datosextra['ultimaFechaPago'][venta.idventa] != null %}
                      {{ datosextra['ultimaFechaPago'][venta.idventa] |date("d/m/Y")}}
                      {% else %}
                      No hay pagos
                      {% endif %}
                      </td>

                      <td class="text-center align-middle hidden">
                      {% if datosextra['ultimaFechaPago'][venta.idventa] != null %}
                      {{ datosextra['ultimaFechaPago'][venta.idventa] |date("g:ia")}}
                      {% else %}
                      No hay pagos
                      {% endif %}
                      </td>

                      <td class="text-center align-middle ">{{ venta.diascredito }}</td>
                      <td class="text-center align-middle ">{{ datosextra['diasActiva'][venta.idventa] }}</td>
                      <td class="text-center align-middle ">{{ venta.sucursalIdsucursal.empresaIdempresa.nombre }}</td>
                      <td class="text-center align-middle ">{{ venta.sucursalIdsucursal.nombre }}</td>
                      <td class="text-center align-middle ">{{ venta.notas }}</td>
                      <td class="text-center align-middle ">${{ venta.pagado |number_format(2, '.', ',') }}</td>
                      <td class="text-center align-middle ">${{ deudas[venta.idventa]|number_format(2, '.', ',') }}</td>

                      {% set suma = suma + (venta.pagado - deudas[venta.idventa]) %}
                      <td class="text-center align-middle " id="deuda-{{venta.idventa}}">${{ (venta.pagado - deudas[venta.idventa])|number_format(2, '.', ',') }}</td>

                      <td class="text-center align-middle ">

                      <button id="agregarPagoSucces" onclick="cargarPantallaPago({{venta.idventa}})" type="button" class="btn btn-success" data-toggle="modal" data-target="#modalAgregarPago">
                        Agregar pago
                      </button>


                      <button id="btn-pdf" type="button" class="btn btn-info" data-bs-toggle="mod" data-bs-target="#modal-visor-documentos" onclick="abrirVisor({{ venta.folio }}, {{ venta.sucursalIdsucursal.empresaIdempresa.idempresa }})"  >Ticket en PDF</button>
                      <button onclick="mostrarTablaPagos({{venta.idventa}})" type="button" class="btn btn-primary" data-toggle="modal" data-target="#modalDetallePagos">
                        Detalle de pagos
                      </button>

                      </td>

                    </tr>
                    
                {% endfor %}
             
            </tbody>
        </table>

        <div class="col-md-12 text-center align-middle ">
            <h4>Total de deuda = ${{suma|number_format(2, '.', ',')}}</h4>
            <br>
        </div>

    </div>



    <div class="mod fade " id="modalAgregarPago" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content" style="border-radius:10px">
            <div class="modal-header bg-primary">
                <h1 class="modal-title fs-5" id="exampleModalLabel">AGREGAR PAGO</h1>
                    <button type="button" class="close btn-close-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                
                <div id="pantallaPago"></div>
                             
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
            </div>
            </div>
        </div>
    </div>




    <div class="mod fade " id="modalDetallePagos" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content" style="border-radius:10px">
            <div class="modal-header bg-primary">
                <h1 class="modal-title fs-5" id="exampleModalLabel">Detalles pagos</h1>
                <button type="button" class="close btn-close-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                
                <div id="tablaPagos"></div>
                             
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
            </div>
            </div>
        </div>
    </div>



</div>


<script>

    $(document).ready(function(){

        $(".modal-backdrop").removeClass("modal-backdrop");

        let table = new DataTable('#tablaVentasCliente',{
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/es-ES.json',
            },
            dom: 'Bfrtip',
            buttons: [

                'excelHtml5',
            ]
        });

    });




    function cargarMeses(){

      var tipoPago=$("#tipo-pago-anticipo").val();
      //var total=$("#total").val();
      //total = quitarFormato(total);

      document.getElementById('anticipo').value= "";
      document.getElementById('anticipo').disabled = false;

      $("#meses").addClass("d-none");

      curVenta = $("#cur-venta").val(),
      deuda = $("#deuda-"+curVenta).text();

      console.log(deuda);
      console.log("#deuda-"+curVenta);

      deuda = quitarFormato(deuda);

      if(tipoPago == "tarjeta crédito"){

        document.getElementById('anticipo').value= deuda;
        document.getElementById('anticipo').disabled = true;
        ponerFormatoPesos("#anticipo");
        $("#meses").removeClass("d-none");

      }
      $.ajax({
              url: "{{path('obtener-meses')}}",
                

              dataType: "html"
          }).done(function( html ) {
              $("#meses").html(html);
          }).fail(function() {
              alert( "error" );
          });
    }

    function cargarPantallaPago(idventa){

        deuda = $("#deuda-"+idventa).text();
        deuda = quitarFormato(deuda);

        $.ajax({
            url: "{{path('cobranza-pago')}}",
            data: { deuda: deuda, idventa:idventa, idcliente:"{{idcliente}}" }
        })
        .done(function( html ) {
            $("#pantallaPago").html(html);
            $("#agregarPagoSucces").prop("disabled", true);
            setTimeout(function() {
                $("#agregarPagoSucces").prop("disabled", false);
            }, 50000);
        });
    }

    $('#modalAgregarPago').on('hidden.bs.modal', function () {
        $("#pantallaPago").html("");
    });

    $('#modalDetallePagos').on('hidden.bs.modal', function () {
        $("#tablaPagos").html("");
        obtenerTablaVentaCobranza("{{idcliente}}");
    });

    function mostrarTablaPagos(idventa){

        $.ajax({
            url: "{{path('cobranza-tabla-pagos')}}",
            beforeSend: loadingGif("tablaPagos"),
            data: { idventa:idventa }
        })
        .done(function( html ) {
            $("#tablaPagos").html(html);

        });
    }

    

</script>