{% extends 'admin/layout.html.twig' %}

{% block content %}
    <link rel="stylesheet" href="{{ asset('lib/dataTables/dataTables.min.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/puntodeventa/cobranza.css') }}"> 
<div class="container-fluid">
    <input id="flujo-expediente-id" type="hidden" class="form-control">
    <input id="idordenlaboratorio" type="hidden" class="form-control">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header card-header-info bg-primary">
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="card-title">VENTAS A CRÉDITO</h4>
                            </div>
                        </div>
                    </div>
                    
                    <ul class="nav nav-tabs mb-3" id="ex-with-icons" role="tablist">
                        <li class="nav-item" role="presentation" id = "tab1">
                            <a class="btn-collapse" data-bs-toggle="collapse" href="#cobranza" role="button" aria-expanded="false" aria-controls="cobranza" ><i class="fa fa-file me-3" aria-hidden="true"></i>CLIENTES</a>
                        </li>
                        <li class="nav-item" role="presentation" id = "tab3">
                            <a class="btn-collapse" data-bs-toggle="collapse" href="#informacionventas" role="button" aria-expanded="false" aria-controls="informacionventas" ><i class="fa fa-money-bills me-3" aria-hidden="true"></i></i>VENTAS</a>
                        </li>
                        <li class="nav-item" role="presentation" id = "tab2">
                            <a class="btn-collapse" data-bs-toggle="collapse" href="#informacionventascliente" role="button" aria-expanded="false" aria-controls="informacionventascliente" ><i class="fa fa-money me-3" aria-hidden="true"></i></i>VENTAS POR CLIENTE</a>
                        </li>
                    </ul>
                    
                        <!-- MERMAS -->
                        <div class="collapse" id="cobranza">

                            <div class="container-fluid" id="tablaCobranza"></div>
                            
                        </div>

                        <!-- TABLA DE COBRANZA POR CLIENTE -->
                        <div class="collapse" id="informacionventascliente">

                            <div class="container-fluid" id="tablaVentasCobranza"></div>

                        </div>

                        <!-- TABLA DE COBRANZA POR CLIENTE -->
                        <div class="collapse" id="informacionventas">

                            <div class="container-fluid" id="tablaVentasTotalCobranza"></div>

                        </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mod " id="modal-visor-documentos" tabindex="-1" aria-labelledby="modal-buscar-cotizacionlLabel" aria-hidden="true">
        <div class="mod-dialog">
            <div class="modal-content" style="border-radius:10px">
                <div class="modal-header bg-primary">
                    <h1 class="modal-title fs-5" id="modalTicketTitle"></h1>
                    <button type="button" class="btn-close" data-bs-dismiss="mod" aria-label="Close"></button>
                </div>

                <div class="modal-body" id="modal-visor-body">

                </div>

                <div class="modal-footer text-center">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="mod">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ asset('lib/dataTables/dataTables.min.js') }}"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>

<script>

    function regresarClientes(){
        obtenerTablaCobranza();
        $("#informacionventascliente").collapse('hide');
        $("#cobranza").collapse("show");
    }

    document.querySelectorAll('.collapse').forEach (ocurrence => {
        ocurrence.addEventListener('show.bs.collapse', function(event) {
            $(".collapse").removeClass("show");
            $(event.target.id).addClass("show");
        })
    })

    $(document).ready(function(){
        obtenerTablaCobranza();
        obtenerTablaVentaCobranza();
        obtenerTablaTotalVentas();

        $("#tab2").addClass("d-none");
        $("#cobranza").collapse('show');

        

    });

    $("#cobranza").on('show.bs.collapse', function(){
        $("#tab2").addClass("d-none");
        obtenerTablaCobranza();
    });

    function obtenerTablaCobranza(){

        $.ajax({
            url: "{{path('tabla-cobranza')}}", 
            type: 'GET',
            beforeSend: loadingGif("tablaCobranza"),
            data: {},
            dataType: "html"
        }).done(function( html ) {
            $("#tablaCobranza").html(html);
            //$(".modal-backdrop").removeClass("modal-backdrop");

        }).fail(function() {
            alert( "error" );
        });
    }

    function obtenerTablaVentaCobranza(idcliente = null){

        if(idcliente){
            $("#informacionventascliente").collapse('show');
            $("#tab2").removeClass("d-none");
        }

        $.ajax({
            url: "{{path('cobranza-tabla-ventas')}}", 
            type: 'GET',
            beforeSend: loadingGif("tablaVentasCobranza"),
            data: {idcliente:idcliente},
            dataType: "html"
        }).done(function( html ) {
            $("#tablaVentasCobranza").html(html);

        }).fail(function() {
            alert( "error" );
        });
    }

    function obtenerTablaTotalVentas(){

        $.ajax({
            url: "{{path('cobranza-tabla-total-ventas')}}", 
            type: 'GET',
            beforeSend: loadingGif("tablaVentasTotalCobranza"),
            dataType: "html"
        }).done(function( html ) {
            $("#tablaVentasTotalCobranza").html(html);

        }).fail(function() {
            alert( "error" );
        });
    }

    function abrirVisor(folio="", empresaid = "",opcion="ticket"){


        $("#modalTicketTitle").text("TICKET PARA FOLIO "+folio);
        
        $("#modal-visor-body").html("");
        let url=$("#url-visor-documentos").val();

        $.ajax({
            method: "POST",
            url: url,
            data: { folio: folio, idempresa:empresaid, opcion:opcion, }
        })
        .done(function( html ) {
            $("#modal-visor-body").html(html);

        });
    }


    

</script>
{% endblock %}

