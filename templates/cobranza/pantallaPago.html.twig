<label class="" for="tipo-pago-anticipo">T<PERSON><PERSON> de <PERSON>go</label>
<select name="" id="tipo-pago-anticipo" class="form-control" data-style="btn btn-link" onchange="cargarMeses()">
    <option value=""></option>
    {% for PaymentType in paymentTypes %}
        <option value="{{PaymentType.idpaymenttype}}" data-payment-name="{{PaymentType.name}}">{{PaymentType.name}}</option>
    {% endfor %}
</select>
<div id="meses"></div>

<div class="form-group  label-floating has-success">
    <label class="" for="anticipo">Cantidad (pesos)</label>
    <input id="anticipo" type="text" class="form-control mb-2 mr-sm-2 valor-pesos" autocomplete="off">
</div>
<div class="col-md-12 text-center">
    <button id="agregar-pago" type="button" class="btn pt-4" style="vertical-align:middle;background:#fff" onclick="guardarPago()" data-dismiss="modal">
        <img src="{{ asset('img/icon-add-pay.jpg') }}" alt="" style="width: 50px;">
    </button>
</div>

<script src="{{asset('js/format-input-field.js')}}"></script>
<script src="{{asset('js/iniciar-formato-campos.js')}}"></script>

<script>


    function cargarMeses(){

        var tipoPago=$("#tipo-pago-anticipo").val();

        document.getElementById('anticipo').value= "";
        document.getElementById('anticipo').disabled = false;

        $("#meses").addClass("d-none");

        const selectedOption = $("#tipo-pago-anticipo").children('option:selected');
        const paymentName = selectedOption.data('payment-name');


        if(paymentName == "Tarjeta de crédito"){

            document.getElementById('anticipo').value= parseFloat("{{deuda}}");
            document.getElementById('anticipo').disabled = true;
            ponerFormatoPesos("#anticipo");
            $("#meses").removeClass("d-none");

        }
        $.ajax({
            url: "{{path('obtener-meses')}}",
            dataType: "html"
        }).done(function( html ) {
            $("#meses").html(html);
        }).fail(function() {
            alert( "error" );
        });
    }

    function guardarPago(){

        monto = $("#anticipo").val();
        monto = quitarFormato(monto);
        tipoPago = $("#tipo-pago-anticipo").val();
        meses = $("#cantidad_meses").val();

        $.ajax({
            url: "{{path('cobranza-guardar-pago')}}",
            data: {monto:monto,idventa:"{{idventa}}",tipoPago:tipoPago,meses:meses,deuda:"{{deuda}}"},
            dataType: "json"
        }).done(function( response ) {
            
            if (response.exito){
                obtenerTablaVentaCobranza("{{idcliente}}");
                obtenerTablaTotalVentas();

            }else{
                Swal.fire(
                'Revisa los datos',
                response.msj,
                'warning'
                )
            }

        }).fail(function() {
            alert( "error" );
        });
    }


</script>