{% if pagos is not empty %}
<div class="row justify-content-md-center">
    <div class="col-md-10">
        <table class="table" id="tablaMermas">
            <thead>
                <tr class="text-start">
                        <th class="text-start text-center" scope="col">#</th>
                        <th class="text-start text-center" scope="col">Fecha</th>
                        <th class="text-start text-center" scope="col">Método de pago</th>
                        <th class="text-start text-center" scope="col">Monto</th>
                        <th class="text-start text-center" scope="col"></th>
                    </tr>

            </thead>
            <tbody id = "tableBodyBuscarCliente">

                {% for index,pago in pagos %}
                    <tr>
                      <td class="text-center align-middle ">{{index + 1}}</td>
                      <td class="text-center align-middle ">{{ pago.fecha|date("d/m/Y") }}</td>
                      <td class="text-center align-middle ">{{ pago.tipopago }}</td>
                      <td class="text-center align-middle ">${{ pago.monto }}</td>
                      <td class="text-center align-middle ">
                      <button type="button" onclick="eliminarPago({{pago.idpago}})" class="btn btn-labeled btn-danger">
                        <span class="btn-label"><i class="fa fa-remove"></i></span></button>
                      </td>
                    </tr>
                {% endfor %}
             
            </tbody>
        </table>
        

    </div>
</div>
{% else %}

<p class="align-middle text-center modal-title fs-5">No hay pagos<p>

{% endif %}

<script>

    function eliminarPago(idpago){
        Swal.fire({
            title: '¿Está seguro?',
            text: "Se eliminará el pago",
            type: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28B463',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Borrar'
        }).then((result) => {
            if (result.value) {

               $.ajax({
                    url: "{{path('borrar-pago')}}",
                    data:{idpago:idpago},
                        dataType: "json"
                }).done(function( response ) {
                    
                    if (response.exito){
                        mostrarTablaPagos("{{idventa}}");
                    }
                    
                }).fail(function() {
                    alert( "error" );
                });

            }
        })
        
    }
    

</script>