
<div class="row justify-content-md-center">

</div>

<div class="row justify-content-md-center">
    <div class="col-md-10 text-right mb-4">
        <strong>Total a deber:</strong> <strong class="text-danger">{{ globalSeDebe |number_format(2, '.', ',') }}</strong>
    </div>
    <div class="col-md-10">
        <table class="table" id="tablaClientes">
            <thead>
                <tr class="text-start">

                    <th class="text-start text-center" scope="col">Nombre</th>
                    <th class="text-start text-center" scope="col">Teléfono</th>
                    <th class="text-start text-center" scope="col">Correo electrónico</th>
                    <th class="text-start text-center" scope="col">Debe</th>
                    <th class="text-start text-center" scope="col">Fecha último pago</th>
                    <th class="text-start text-center" scope="col">Opción</th>

                </tr>

            </thead>
            <tbody id = "tableBodyBuscarCliente">

                {% for index,venta in ventasNoLiquidadas %}
                    <tr>

                      <td class="text-center align-middle ">
                      {{ venta.nombre }} {{ venta.apellidopaterno }} {{ venta.apellidomaterno }}
                          {{ venta.idcliente}}
                      </td>
                      <td class="text-center align-middle ">{{ venta.telefono }}</td>
                      <td class="text-center align-middle ">{{ venta.email }}</td>
                      <td class="text-center align-middle ">${{ arrayPagado[venta.idcliente] |number_format(2, '.', ',') }}</td>
                      <td class="text-center align-middle ">
                          {% if venta.fechaUltimoPago != null %}
                          {{ venta.fechaUltimoPago | date("d/m/Y g:ia") }}</td>
                           {% else %}
                        No hay pagos aún
                               {% endif %}
                      <td class="text-center align-middle ">

                      <button onclick="obtenerTablaVentaCobranza({{venta.idcliente}})" type="button" class="btn btn-success">
                        Ver ventas
                      </button>
                      

                      </td>

                    </tr>
                {% endfor %}
             
            </tbody>
        </table>
        

    </div>
</div>
<script>
    $(document).ready(function(){
        let table = new DataTable('#tablaClientes',{
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/es-ES.json',
            },
        });
    })
</script>