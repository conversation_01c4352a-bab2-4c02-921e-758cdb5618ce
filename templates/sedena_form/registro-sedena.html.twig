{% extends 'layout-form.html.twig' %}
{% block title %}REGISTRO SEDENA{% endblock %}

{% block content %}
    <section class="login-container"></section>
    <div class="container-fluid">
        <div class="page row">
            <div class="form-box col-md-6 col-10 mx-auto vertical-center">
                <div id="sedena-form-container"></div>
            </div>
        </div>
    </div>

{% endblock %}

{% block scripts %}
    {{parent()}}
    <script>
        var currentFile;

        $(document).ready(function(){
            getSedenaForm()
        });

        function getSedenaForm(){
            $("#sedena-form-container").removeClass("p-5");
            $.ajax({
                url: "{{path('sedena-get-sedena-form')}}", 
                type: 'GET',
                beforeSend: loadingGif("sedena-form-container"),
                dataType: "html"
            }).done(function( html ) {
                $("#sedena-form-container").addClass("p-5");
                $("#sedena-form-container").html(html);

            }).fail(function() {
                alert( "error" );
            });
        }

        function setCurrentData(newFile){
            currentFile = newFile
        }
        
        function createFlow(clientId, userId) {

            $.ajax({
                url: "{{ path('dashboard-create-flow-sedena') }}",
                method: "POST",
                data: { clienteId: clientId, userId:userId },
                success: function(response) {
                    if(response.success){
                        uploadDocument(response.idflow)
                        const newURL = "/admin/expediente-clinico/" + response.idflow;
                        window.open(newURL, "_blank");
                    }
                    else {
                        Swal.fire({
                            title: "No se pudo crear el flujo",
                            text: response.msg,
                            icon: "warning"
                        });
                    }
                },
                error: function(error) {
                    Swal.fire({
                        title: "Algo salió mal",
                        text: error,
                        icon: "warning"
                    });
                }
            });
        }

        

        function uploadDocument(idflujoexpediente) {
            
            var formData = new FormData();
            formData.append('file', currentFile);
            formData.append('categoriaDocumentos', "Receta SEDENA");

            $.ajax({
                
                url: "{{path('upload_file')}}?idflujoexpediente=" + idflujoexpediente+"&tipoarchivo=examen",
                type: 'POST',
                data: formData,
                success: function(response) {},
                cache: false,
                contentType: false,
                processData: false
            });
        }

    </script>

{% endblock %}