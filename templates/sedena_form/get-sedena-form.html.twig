<div class="row justify-content-between align-items-center">
    <h2 class="m-0 fw-bold title-form col-9 fs-md-1 fs-4">Registro SEDENA</h2>
    <div class="col-3">
        <button class="form-button p-2" onclick="clearForm()">
            <label class="m-0 d-md-block d-none">Limpiar campos</label>
            <i class="fa-solid fa-trash d-block d-md-none"></i>
        </button>
    </div>
</div>

{{ form_start(form, {'attr': {'id': 'sedena-form', 'enctype': 'multipart/form-data' }}) }}

<div class="row">

    <div class="col-xl-4">
        {{ form_row(form.nombre) }}
    </div>
    <div class="col-xl-4">
        {{ form_row(form.apellidopaterno) }}
    </div>
    <div class="col-xl-4">
        {{ form_row(form.apellidomaterno) }}
    </div>

</div>

<div class="row">

    <div class="col-xl-2 col-md-6">
        {{ form_row(form.edad) }}
    </div>
    <div class="col-xl-2 col-md-6">
        {{ form_row(form.genero) }}
    </div>
    <div class="col-xl-4">
        {{ form_row(form.telefono) }}
    </div>
    <div class="col-xl-4">
        {{ form_row(form.numeroempleado) }}
    </div>

</div>

<div class="row">

    <div class="col-xl-6">
        {{ form_row(form.comentarios) }}
    </div>
    <div class="col-xl-6">
        {{ form_row(form.beneficiarytype) }}
    </div>

</div>

<div class="row">

    <div class="col-12">
        {{ form_row(form.doctorname) }}
    </div>
    <div class="col-12">
        {{ form_row(form.file) }}
    </div>
    <div class="col-12 ">
        {{ form_row(form.Enviar) }}
    </div>

</div>
{{ form_widget(form) }}
{{ form_end(form) }}

<script>

    var userId = "{{(app.user) ? app.user.idusuario : null}}"
    var clientId = "{{clientId}}"

    $(document).ready(function(){

        if (clientId) {
            Swal.fire({
                title: "Usuario creado",
                text: "",
                icon: "success"
            });

            $(':input','#sedena-form')
                .not('#sedena_numeroempleado, #sedena_doctorname, select, :hidden')
                .val('')
                .prop('checked', false)

            if (userId) createFlow(clientId, userId)
        }

        
    })

    $("#sedena-form").submit(function(e) {
        e.preventDefault();
        changeButton("sedena_Enviar");

        var formData = new FormData(this);

        var idflujoexpediente = $("#flujo-expediente-id").val();

        const idNumber = $("#sedena_numeroempleado").val()
        const beneficiaryType = $("#sedena_beneficiarytype").val()

        $.ajax({
            url: "{{path('sedena-get-sedena-form')}}?idNumber="+idNumber+"&beneficiaryType="+beneficiaryType,
            type: 'POST',
            data: formData,
            success: function (html) {
                var fileInput = formData.get('sedena[file]');
                setCurrentData(fileInput)
                changeButton("sedena_Enviar", 1);
                $("#sedena-form-container").html(html);

            },
            cache: false,
            contentType: false,
            processData: false
        });
           
    });

    function clearForm(){
        $(':input','#sedena-form')
        .not(':button, :submit, :reset, :hidden, select, :hidden')
        .val('')
        .prop('checked', false)
    }

</script>