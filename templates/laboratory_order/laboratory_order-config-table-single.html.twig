<td class="text-center">
	<div class="border-0 d-flex flex-column">
		{% if laboratoryOrder.stage <= 4 %}
			<button class="btn btn-primary mt-2" onclick="sendlab('{{laboratoryOrder.idordenlaboratorio}}')">Enviar a laboratorio</button>
			<button class="btn btn-info mt-2" onclick="getSuppliers('{{laboratoryOrder.idordenlaboratorio}}')" data-toggle="modal" data-target="#order-history-modal">
				Enviar a un proveedor externo
			</button>
		{% endif %}
		<a class="btn btn-warning my-2" href="{{ path('app_pdf_generator_etiqueta', {qr_value: laboratoryOrder.idordenlaboratorio}) }}">
			<PERSON><PERSON><PERSON>
		</a>
		<button class="btn btn-success mb-2" onclick="showOrderHistory('{{laboratoryOrder.idordenlaboratorio}}')" data-toggle="modal" data-target="#order-history-modal">
			Mostrar historial
		</button>
	</div>
</td>
<td>{{orderStages[laboratoryOrder.stage - 1]}}</td>
<td>{{laboratoryOrder.locationName}}</td>
<td>
	{% if mappedSales is defined %}
		{% for folio in mappedSales %}
			{{folio}}&nbsp;
		{% endfor %}
	{% else %}
		{{laboratoryOrder.folio}}
	{% endif %}
</td>
<td>{{laboratoryOrder.fullName}}</td>
<td class="text-center">
	<table class="table">
		{% if mappedProducts is defined %}
			{% for index, mappedProduct in mappedProducts %}
				<tr>
					<td>
						{% if mappedProduct.main.errase == 0 %}
							<button class="delete-product btn btn-danger" data-product-id="{{ mappedProduct.main.id }}" onclick="deleteStockventaOrdenlaboratorio(this,'{{mappedProduct.main.id}}', '{{laboratoryOrder.idordenlaboratorio}}')">
								<i class="fa-solid fa-xmark"></i>
							</button>
						{% endif %}
					</td>
					<th class="text-start">{{ index + 1 }}</th>
					<td class="{{ mappedProduct.main.errase >= 2 ? 'text-danger' : '' }}">{{ mappedProduct.main.modelo }}</td>
					<td>
						{% if mappedProduct.main.tipoproducto == 2 and laboratoryOrder.stage <= 4 %}
							<button type="button" class="btn btn-info" data-toggle="modal" data-target="#lens-config-modal" onclick="openAdditionalProducts('{{ laboratoryOrder.fullName }}', '{{ laboratoryOrder.idordenlaboratorio }}', '{{ mappedProduct.main.id }}')">
								<i class="fa-solid fa-recycle"></i>
							</button>
						{% else %}
							<button type="button" class="btn btn-secondary" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ mappedProduct.main.revision ?? 'none' }}">
								<i class="fa-solid fa-circle-info"></i>
							</button>
						{% endif %}
					</td>
				</tr>
				{% if mappedProduct.children is defined and mappedProduct.children|length > 0 %}
					<tr>
						<td colspan="4">
							<table class="table table-sub">
								{% for child in mappedProduct.children %}
									<tr>
										<td>
											{% if child.errase == 0 %}
												<button class="delete-product btn btn-danger" data-product-id="{{ child.id }}" onclick="deleteStockventaOrdenlaboratorio(this,'{{ child.id }}', '{{ laboratoryOrder.idordenlaboratorio }}')">
													<i class="fa-solid fa-xmark"></i>
												</button>
											{% endif %}
										</td>
										<th class="text-start">{{ loop.index }}</th>
										<td class="{{ child.errase >= 2 ? 'text-danger' : '' }}">{{ child.modelo }}</td>
										<td>
											{% if child.tipoproducto == 2 and laboratoryOrder.stage <= 4 %}
												<button type="button" class="btn btn-info" data-toggle="modal" data-target="#lens-config-modal" onclick="openAdditionalProducts('{{ laboratoryOrder.fullName }}', '{{ laboratoryOrder.idordenlaboratorio }}', '{{ child.id }}')">
													<i class="fa-solid fa-recycle"></i>
												</button>
											{% else %}
												<button type="button" class="btn btn-secondary" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ child.revision ?? 'none' }}">
													<i class="fa-solid fa-circle-info"></i>
												</button>
											{% endif %}
										</td>
									</tr>
								{% endfor %}
							</table>
						</td>
					</tr>
				{% endif %}
			{% endfor %}
		{% endif %}
	</table>

</td>
<td class="text-center">

	{% set curBase = laboratoryOrder.base ? laboratoryOrder.base : '0' %}
	<select id="base-select-{{laboratoryOrder.idordenlaboratorio}}" class="form-control" onchange="setBase('{{laboratoryOrder.idordenlaboratorio}}')">
		<option value='0' {{'0' == curBase ? 'selected' : '' }}>0</option>
		<option value='2' {{'2' == curBase ? 'selected' : '' }}>2</option>
		<option value='4' {{'4' == curBase ? 'selected' : '' }}>4</option>
		<option value='6' {{'6' == curBase ? 'selected' : '' }}>6</option>
		<option value='8' {{'8' == curBase ? 'selected' : '' }}>8</option>
		<option value='PLANA' {{'PLANA' == curBase ? 'selected' : '' }}>PLANA</option>
	</select>
	<i id="check-{{laboratoryOrder.idordenlaboratorio}}" class="fa-solid fa-check d-none" style="color: green"></i>
</td>
<td>
	<table class="table">
		<tr>
			<th class="text-start">Esfera</th>
			<td>{{laboratoryOrder.esferaoi}}</td>
		</tr>
		<tr>
			<th class="text-start">Cilindro</th>
			<td>{{laboratoryOrder.cilindrooi}}</td>
		</tr>
		<tr>
			<th class="text-start">Eje</th>
			<td>{{laboratoryOrder.ejeoi}}</td>
		</tr>
	</table>
</td>
<td>
	<table class="table">
		<tr>
			<th class="text-start">Esfera</th>
			<td>{{laboratoryOrder.esferaod}}</td>
		</tr>
		<tr>
			<th class="text-start">Cilindro</th>
			<td>{{laboratoryOrder.cilindrood}}</td>
		</tr>
		<tr>
			<th class="text-start">Eje</th>
			<td>{{laboratoryOrder.ejeod}}</td>
		</tr>
	</table>
</td>
<td>{{laboratoryOrder.addordenlaboratorio}}</td>
<td class="text-center">
	<button class="btn btn-success" data-toggle="modal" data-target="#lens-config-modal" onclick="openAdditionalProducts('{{laboratoryOrder.fullName}}', '{{laboratoryOrder.idordenlaboratorio}}', '-1')">
		<i class="fa-solid fa-plus "></i>
	</button>
</td>
