<table id="order-laboratory-addtable" class="table position-sticky">
	<thead class="position-sticky">
		<th>Sucursal</th>
		<th>SKU</th>
		<th>Serie</th>
		<th>Modelo</th>
		<th>Nombre</th>
		<th>Esfera</th>
		<th>Diseño</th>
		<th><PERSON><PERSON><PERSON><PERSON></th>
		<th>Eje</th>
		<th>Adicion</th>
		<th><PERSON><PERSON><PERSON></th>
		<th></th>
	</thead>
	<tbody>
		{% for product in addproducts %}
			<tr>
				<td class="text-center">{{product.num}}</td>
				<td class="text-center">{{(product.sku != '' ? product.sku : 'Sin SKU') }}</td>
				<td class="text-center">{{(product.serie != '' ? product.serie : 'Sin Serie') }}</td>
				<td class="text-center">{{product.modelo}}</td>
				<td class="text-center">{{product.nombre}}</td>
				<td class="text-center">{{(product.esfera != '' ? product.esfera : 'Sin Esfera') }}</td>
				<td class="text-center">{{(product.design != '' ? product.design : 'Sin Diseño') }}</td>
				<td class="text-center">{{(product.cilindro != '' ? product.cilindro : 'Sin Cilindro') }}</td>
				<td class="text-center">{{(product.eje != '' ? product.eje : 'Sin Eje') }}</td>
				<td class="text-center">{{(product.adicion != '' ? product.adicion : 'Sin Adición') }}</td>
				<td class="text-center">{{(product.cantidad != '' ? product.cantidad : 'Sin Cantidad') }}</td>
				<td class="text-center">
					{% if idstockventaordenlaboratorio == '-1' %}
						<button class="btn btn-success" onclick="sendProduct('{{ product.idstock }}', this)">
							<i class="fa-solid fa-plus "></i>
						</button>
					{% else %}
						<button class="btn btn-primary"  onclick="changeProduct('{{ product.idstock }}', this)">
							<i class="fa-solid fa-recycle"></i>
						</button>

					{% endif %}
				</td>
			</tr>
		{% endfor %}
	</tbody>
</table>

<script>

	$(document).ready(function () {

$('#order-laboratory-addtable').DataTable({
language: {
url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
lengthMenu: 'Mostrar _MENU_ ordenes por página'
},
"lengthMenu": [
[
5,
10,
25,
50,
-1
],
[
5,
10,
25,
50,
"All"
]
],
responsive: false
});
});
</script>
