{% if events | length > 0 %}
    <div class="table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Responsable</th>
                    <th>Fecha</th>
                    <th>Número de envío</th>
                    <th>Descripción</th>
                    <th>Comentarios</th>
                </tr>
            </thead>
            <tbody class="text-center">
                {% for index, event in events %}
                    <tr>
                        <td>{{ index + 1 }}</td>
                        <td>{{ event.nombre }} {{ event.apellidopaterno }} {{ event.apellidomaterno }}</td>
                        <td>{{ event.creationdate|date("d/m/Y H:i") }}</td>
                        <td>
                        {% if event.trackingnumber != null %}
                            {{ event.trackingnumber }}
                        {% else %}
                            <p class="text-center">-</p>
                        {% endif %}
                        </td>
                        <td>{{ event.description }}</td>
                        <td>{{ event.comments }}</td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <a href="{{ path('laboratory-order-show-order-history-export', {laboratoryOrderId: laboratoryOrderId}) }}" class="btn btn-primary">
        Descargar como Excel
    </a>
{% else %}
    <h4 class="text-center">No hay historial de esta orden</h4>
{% endif %}
