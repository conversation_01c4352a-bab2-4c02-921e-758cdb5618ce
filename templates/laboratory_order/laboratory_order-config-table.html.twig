<div class="table-responsive">
	<table id="order-laboratory-config-table" class="table table-responsive">
		<thead>
			<th></th>
			<th>Estado</th>
			<th>Sucursal</th>
			<th>Folio de venta</th>
			<th>Cliente</th>
			<th>Productos</th>
			<th>Base</th>
			<th><PERSON><PERSON></th>
			<th>Ojo derecho</th>
			<th>Add</th>
			<th></th>
		</thead>
		<tbody>
		{% for index, laboratoryOrder in laboratoryOrders %}
	<tr id="order-{{laboratoryOrder.idordenlaboratorio}}">
			{% include 'laboratory_order/laboratory_order-config-table-single.html.twig' 
				with {
					'laboratoryOrder': laboratoryOrder,
					'mappedProducts' : mappedProducts[laboratoryOrder.idordenlaboratorio] ?? null,
            		'orderStages' : orderStages,
            		'mappedSales' : mappedSales[laboratoryOrder.idflujoexpediente]?? null
				} 
			%}
				
			{% endfor %}
			</tr>
		</tbody>
	</table>
</div>

<script>

	$(document).ready(function () {

    $('#order-laboratory-config-table').DataTable({
        language: {
            lengthMenu: 'Mostrar _MENU_ ordenes por página'
        },
        "lengthMenu": [
            [
                5,10,25,50,-1
            ],
            [
                5,10,25,50,"All"
            ]
        ],
        responsive: false
    	});
	});

	function deleteStockventaOrdenlaboratorio(btn, deleteid, updateid) {

		btn.disable = true;

		loadingGif("order-" + updateid);
		$.ajax({
			url: "{{ path('laboratory-order-delete-stock-Order') }}",
			type: 'POST',
			data: {
				deleteid: deleteid,
				updateid: updateid
			},
			beforeSend: loadingGif("order-" + updateid),
			dataType: "html"
		}).done(function (html) {
			$("#order-" + updateid).html(html);
			$("#lens-config-result").html('<p></p>');
		}).fail(function () {
			alert("error");
		});
	}


	function sendlab(updateid) {

		const etapanum = $("#etapanum").val() ?? 3;

		loadingGif("order-" + updateid)

		$.ajax({
			url: "{{ path('laboratory-order-sendlab', { 'etapanum': 'ETAPA_NUM' }) }}".replace('ETAPA_NUM', etapanum),
			type: 'POST',
			data: {
				updateid: updateid
			},
			beforeSend: loadingGif("order-" + updateid),
			dataType: "html"
		}).done(function (html) {
			$("#order-" + updateid).html(html);
			$("#lens-config-result").html('<p></p>');
		}).fail(function () {
			alert("error");
		});
	}


	function setBase(idlaboratoryOrder) {
		newBase = $('#base-select-' + idlaboratoryOrder).val();

		$.ajax({
			url: "{{ path('laboratory-order-set-base') }}",
			type: 'GET',
			data: {
				idlaboratoryOrder: idlaboratoryOrder,
				newBase: newBase
			},
			dataType: "json"
		}).done(function (response) {
			console.log(response);
			if (response.success) {
				$('#base-select-' + idlaboratoryOrder).css({ 'background-color': 'rgba(139, 242, 113, 0.5)' });
				$('#check-' + idlaboratoryOrder).removeClass('d-none');
			} else
				$('#base-select-' + idlaboratoryOrder).css({ 'background-color': 'rgba(242, 113, 113, 0.5)' });



			setTimeout(function () {
				$('#base-select-' + idlaboratoryOrder).css({ 'background-color': '#FFFFFF' });
				$('#check-' + idlaboratoryOrder).addClass('d-none');
			}, 1500);
		}).fail(function () {
			alert("error");
		});
	}

	function openAdditionalProducts(clientName, ordenlab, idstockventaordenlaboratorio ) {

		$('#lens-config-title').text("Orden de laboratorio de: " + clientName);

		var formData = {
			ordenlab,
			idstockventaordenlaboratorio
		};

		$.ajax({
			url: "{{ path('getMicasFilters') }}",
			type: 'POST',
			data: JSON.stringify(formData),
			beforeSend: loadingGif("lens-config-filters"),
			dataType: "html"
		}).done(function (html) {
			$("#lens-config-filters").html(html);
			$("#lens-config-result").html();

		}).fail(function () {
			alert("error");
		});
	}
</script>


