<div class="container-fluid row justify-content-center">

	<div class="row justify-content-center">

		<div class="form-group row justify-content-between col-md-5 px-4">
			<label class="col-12" for="sku">SKU:</label>

			<input type="text" class="form-control col-12" id="sku" value="" placeholder="...">

		</div>

		<div class="form-group row justify-content-between col-md-7">
			<label class="col-12" for="serie"># Serie:</label>

			<input type="text" class="form-control col me-4" id="serie" value="" placeholder="...">
			<button class="btn btn-primary col-auto" onclick="getAditionalProducts()">
				Filtrar y Búscar
				<i class="fa-solid fa-filter px-2" style="color: #ffffff;"></i>
			</button>

		</div>

		{% include 'multi_CheckBox.html.twig' with {'your_array': filters.checkBoxes} %}
	</div>

	<div class="row justify-content-around d-flex">
		{% include 'multi_select.html.twig' with {'your_array': filters.selects} %}
	</div>

</div>

<script>
	$(document).ready(function () {
		Globalordenlab = {{ ordenlab }}
	});


	function getCheckedValue(category) {
		foundValues = [];

		$('input[name="' + category + '"]').each(function () { // Check if the checkbox is checked
			if ($(this).is(':checked')) {
				var value = $(this).val(); // Get the checkbox value
				foundValues.push(value); // Add the value to the array
			}
		});

		return foundValues;
	}

	function getSelectedValue(nameAttribute) { // Use jQuery to select the <select> element with the specified name

		var selectedValue = $('#' + nameAttribute).val();

		return selectedValue;
	}

	function collectMultiValues(yourArray, categoryData) {
		var selectedValues = {};
		// Loop through each category
		for (category in yourArray) {

			switch (categoryData) {
				case 'checkboxes': selectedValues[yourArray[category].db_name] = getCheckedValue(category);
					break;
				case 'select': selectedValues[yourArray[category].db_name] = getSelectedValue(category);
					break;
				// Add more cases if needed for other types
			}
		}

		return selectedValues;
	}

	function getAditionalProducts() {

		var filters = {{ filters| json_encode | raw
	}};

	const ordenlab = {{ ordenlab }}
	const idstockventaordenlaboratorio = {{ idstockventaordenlaboratorio }}

	var checkBoxes = collectMultiValues(filters.checkBoxes, 'checkboxes');
	var selects = collectMultiValues(filters.selects, 'select');

	var sku = $("#sku").val();
	var serie = $("#serie").val();

	filters = {
		checkBoxes,
		selects
	};

	var formData = {
		filters,
		ordenlab,
		sku,
		serie,
		idstockventaordenlaboratorio
	};

	$.ajax({
		type: "POST",
		url: "{{ path('getMicas') }}", // Change this to your server-side endpoint URL
		data: JSON.stringify(formData),
		contentType: "application/json",
		beforeSend: loadingGif("lens-config-result")
	}).done(function (html) {
		$("#lens-config-result").html(html);
	}).fail(function () {
		alert("error");
	});
	}

	function sendProduct(idstock, btn) {

		var filters = {{ filters| json_encode | raw
	}};

	const ordenlab = {{ ordenlab }}
	const idstockventaordenlaboratorio = {{ idstockventaordenlaboratorio }}

	var checkBoxes = collectMultiValues(filters.checkBoxes, 'checkboxes');
	var selects = collectMultiValues(filters.selects, 'select');

	var sku = $("#sku").val();
	var serie = $("#serie").val();

	filters = {
		checkBoxes,
		selects
	};

	var formData = {
		filters,
		idstock,
		ordenlab,
		idstockventaordenlaboratorio,
		sku,
		serie
	};

	btn.disable = true;

	loadingGif("lens-config-result")

	$.ajax({
		type: "POST",
		url: "{{ path('addProductToLabOrder') }}", // Change this to your server-side endpoint URL
		data: JSON.stringify(formData),
		contentType: "application/json",
		beforeSend: loadingGif("lens-config-result")
	}).done(function (html) {
		$("#lens-config-result").html(html);
	}).fail(function () {
		alert("error");
	});
	}

	function changeProduct(idstock, btn) {

		var filters = {{ filters| json_encode | raw }};

		const ordenlab = {{ ordenlab }}
		const idstockventaordenlaboratorio = {{ idstockventaordenlaboratorio }}

		var checkBoxes = collectMultiValues(filters.checkBoxes, 'checkboxes');
	var selects = collectMultiValues(filters.selects, 'select');

	var sku = $("#sku").val();
	var serie = $("#serie").val();

	btn.disable = true;


		filters = {
		checkBoxes,
		selects
		};

		var formData = {
			filters,
			idstock,
			ordenlab,
			idstockventaordenlaboratorio,
			sku,
			serie
		};

		loadingGif("lens-config-result")

		$.ajax({
			type: "POST",
			url: "{{ path('switchProductToLabOrder') }}", // Change this to your server-side endpoint URL
			data: JSON.stringify(formData),
			contentType: "application/json",
		}).done(function (html) {
			$("#lens-config-result").html(html);
		}).fail(function () {
			alert("error");
		});
	}


</script>
