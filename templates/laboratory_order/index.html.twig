{% extends 'admin/layout.html.twig' %}

{% block title %}configuración ordened de laboratorio
{% endblock %}

{% block content %}

	<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.css"/>

	<div class="card">
		<div class="card-body">
			<div class="container-fluid">
				<h3 class="text-center">Configuración de órdenes de laboratorio</h3>

				<label for="etapanum">Seleccionar Etapa:</label>
				<select id="etapanum" name="etapanum" onchange="laboratoryOrderConfigTable()">
					{% for key, stage in orderStages %}
						<option value="{{ key + 3 }}">{{ stage }}</option>
					{% endfor %}
				</select>
				<div class="row">
					<div class="col-12" id="laboratory-order-table-container"></div>
				</div>
			</div>
		</div>

		<div class="mod" id="lens-config-modal" tabindex="-1" aria-labelledby="" aria-hidden="true">
			<div class="mod-dialog modal-dialog-scrollable">
				<div class="modal-content" style="border-radius:10px">
					<div class="modal-header bg-primary">
						<h1 class="modal-title fs-5" id="lens-config-title"></h1>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
					<div class="modal-body row px-4 justify-content-center">
						<div class="col-md-5" id="lens-config-filters"></div>
						<div class="col-md-7" style="overflow-x: auto;" id="lens-config-result"></div>
					</div>
				</div>
			</div>
		</div>

		<div class="mod fade" id="order-history-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-lg" role="document">
				<div class="modal-content" style="border-radius:10px">
					<div class="modal-header bg-primary">
						<h1 class="modal-title fs-5" id="order-history-title">Añadir producto</h1>
						<button type="button" class="close btn-close-white" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
					<div class="modal-body">
						<div id="order-history-modal-body"></div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
					</div>
				</div>
			</div>
		</div>
	</div>

	{% endblock %}

	{% block javascripts %}
		{{parent()}}

		<script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
		<script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
		<script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
		<script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>

		<script>
		var Globalordenlab = 0;
			$(document).ready(function () {
				laboratoryOrderConfigTable();

				$('#lens-config-modal').on('hidden.bs.modal', function () {
					singleUpdate();
				});
			});

			function singleUpdate() {
				if(Globalordenlab == 0) return;
				$("#lens-config-result").html();
				$("#lens-config-result").html("<p></p>");
				console.log("order-"+Globalordenlab);

				var curorderId = Globalordenlab;

				loadingGif("order-" + curorderId)

				$.ajax({
					url: "{{ path('laboratory_order_get_single', {'id': 'ORDER_ID'}) }}".replace('ORDER_ID', curorderId),
					type: 'GET',
					beforeSend: loadingGif("order-" + curorderId),
					dataType: "html"
				}).done(function(html) {
					$("#order-" + curorderId).html(html);
					$("#lens-config-result").html(); 
				$("#lens-config-result").html("<p></p>");

					Globalordenlab = 0;
				}).fail(function() {
					alert("error");
				});
			}

			function laboratoryOrderConfigTable() {
				const etapanum = $("#etapanum").val() ?? 3;

				console.log(etapanum);

				$("#lens-config-result").html();
				$.ajax({
					url: "{{ path('laboratory-order-laboratory-order-config-table', { 'etapanum': 'ETAPA_NUM' }) }}".replace('ETAPA_NUM', etapanum), 
					type: 'GET', 
					beforeSend: loadingGif("laboratory-order-table-container"), 
					dataType: "html"
				}).done(function (html) {
					$("#laboratory-order-table-container").html(html);
					$("#lens-config-result").html();

				}).fail(function () {
					alert("error");
				});
			}

			function showOrderHistory(laboratoryOrderId) {
				$("#order-history-title").text("Historial de la orden "+laboratoryOrderId);
				$.ajax({
					url: "{{ path('laboratory-order-show-order-history') }}", 
					type: 'POST',
					data: {laboratoryOrderId:laboratoryOrderId},
					beforeSend: loadingGif("order-history-modal-body"), 
					dataType: "html"
				}).done(function (html) {
					$("#order-history-modal-body").html(html);
				}).fail(function () {
					alert("error");
				});

			}

		function getSuppliers(laboratoryOrderId) {
			$("#order-history-title").text("Seleccionar un proveedor externo");
			$.ajax({
				url: "{{ path('laboratory-get-suppliers') }}",
				type: 'POST',
				data: {laboratoryOrderId:laboratoryOrderId},
				beforeSend: loadingGif("order-history-modal-body"),
				dataType: "html"
			}).done(function (html) {
				$("#order-history-modal-body").html(html);
			}).fail(function () {
				alert("error");
			});
		}


		function doesItSendGlasses()
		{
			const selectedOption = $("#suppliers-select").find(':selected');
			const sendGlassesValue = selectedOption.data('sendglasses');
			if (sendGlassesValue == "1") $("#location-supplier-container").removeClass('d-none');
			else $("#location-supplier-container").addClass('d-none');
		}

		function sendToSupplier(laboratoryOrderId)
		{
			const supplierId = $("#suppliers-select").val();
			const selectedOption = $("#suppliers-select").find(':selected');
			const sendglasses = selectedOption.data('sendglasses');
			const locationId = (sendglasses == 1) ? $("#locations-suppliers-select").val() : -1;
			if (supplierId == -1) Swal.fire("Selecciona un proveedor",'No has seleccionado un proveedor válido', "warning");
			else if (supplierId != -1 && sendglasses == 1 && locationId == -1) Swal.fire("Selecciona una sucursal",'No has seleccionado una sucursal válida', "warning");
			else {
				changeButton("send-supplier-btn",0,1)
				$.ajax({
					url: "{{ path('laboratory-send-to-supplier') }}",
					type: 'POST',
					data: {
						laboratoryOrderId:laboratoryOrderId,
						supplierId:supplierId,
						sendglasses:sendglasses,
						locationId:locationId,
					},
					dataType: "json"
				}).done(function (response) {
					if (response.success) {
						Swal.fire("Orden asignada a proveedor",'', "success");
						$("#order-history-modal .close-button").click()
						laboratoryOrderConfigTable();
					}else Swal.fire("Algo ocurrió", response.msg, "warning");
					changeButton("send-supplier-btn",1,1)
				}).fail(function () {
					alert("error");
					changeButton("send-supplier-btn",1,1)
				});
			}
		}

		</script>
	{% endblock %}
