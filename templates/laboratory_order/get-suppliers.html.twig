<label class="form-label" for="suppliers-select">Proveedores:</label>
<select id="suppliers-select" class="form-control" onchange="doesItSendGlasses()">
    <option value="-1" data-sendglasses="0">Selecciona un proveedor</option>
    {% for Supplier in suppliers %}
        <option value="{{Supplier.idproveedor}}" data-sendglasses="{{Supplier.sendglasses}}">{{Supplier.nombre}}</option>
    {% endfor %}
</select>
<div id="location-supplier-container" class="d-none">
    <p class="mt-5">Este proveedor entrega a sucursal, selecciona a la que deseas mandar el envío</p>
    <label class="form-label" for="locations-suppliers-select">Sucursales:</label>
    <select id="locations-suppliers-select" class="form-control">
        {% for Location in locations %}
            <option value="{{Location.idsucursal}}" {% if Location.idsucursal == targetLocation %}selected{% endif %}>{{Location.nombre}}</option>
        {% endfor %}
    </select>
</div>
<div class="text-center mt-3">
    <button id="send-supplier-btn" class="btn btn-success" onclick="sendToSupplier('{{laboratoryOrderId}}')">Enviar a proveedor</button>
</div>