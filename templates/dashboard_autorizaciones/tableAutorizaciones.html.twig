<table id="autorizacionesTable" class="table table-bordered table-hover text-center table-striped">
    <thead class="title-table">
        <tr class="text-center">
            <th></th>
            <th>Folio</th>
            <th>Número de autorización</th>
            <th>Estado</th>
            <th>Tipo de venta</th>
            <th><PERSON><PERSON> de venta</th>
            <th>Sucursal</th>
            <th>Liquidada</th>
            <th>Cliente</th>
            <th>Beneficiario</th>
        </tr>
    </thead>
    <tbody>
    {% for autorizacion in autorizaciones %}
        <tr class="text-center">
            <td>
                <button onclick="getDocuments('{{ autorizacion.idventa }}')" class="btn btn-primary w-75 mb-1" data-bs-toggle="mod" data-bs-target="#modal-autorizaciones">Documentos</button>
                <button onclick="getHistory('{{ autorizacion.idventa }}')" class="btn btn-info w-75 mb-1" data-bs-toggle="mod" data-bs-target="#modal-autorizaciones">Historial</button>
                <button onclick="getChangeStateForm('{{ autorizacion.idventa }}')" class="btn btn-warning w-75 mb-1" data-bs-toggle="mod" data-bs-target="#modal-autorizaciones">Cambiar etapa</button>
            </td>
            <td>{{ autorizacion.folio }}</td>
            <td>{{ autorizacion.authorizationnumber ?: 'N/A' }}</td>
            <td id="auth-stage-{{autorizacion.idventa}}">{{ autorizacion.name ?: 'Sin asociar' }}</td>
            <td>{{ autorizacion.tipoVenta }}</td>
            <td>{{ autorizacion.fechaventa | date("d/m/Y")}}</td>
            <td>{{ autorizacion.location }}</td>
            <td>{{ autorizacion.liquidada == '1' ? "Liquidada" : "No liquidada" }}</td>
            <td>{{ autorizacion.clientName }}</td>
            <td>{{ autorizacion.beneficiario ?: 'N/A'}}</td>
        </tr>
    {% else %}
        <tr>
            <l1 colspan="4" class="text-center">No se encontraron autorizaciones.</l1>
        </tr>
    {% endfor %}
    </tbody>
</table>

<div class="row pt-5">
    <div class="col-md-6">
        <div id="apexchart"></div>
    </div>
    <div class="col-md-6">
        <table id="cantidadApex" class="table table-bordered table-hover text-center table-striped">
            <thead class="title-table">
                <tr>
                    <th class="text-center">Estado</th>
                    <th class="text-center">Cantidad</th>
                    <th class="text-center">Importe</th>
                </tr>
            </thead>
            <tbody>
            {% if estadoCounts is empty %}
                <tr>
                    <td colspan="2" class="text-center">No se encontraron autorizaciones.</td>
                </tr>
            {% else %}
                {% for estado, count in estadoCounts %}
                    <tr>
                        <td class="text-center">{{ estado }}</td>
                        <td class="text-center">{{ count }}</td>
                        <td class="text-center">${{ estadoTotales[estado]|number_format(2, '.', ',') }}</td>
                    </tr>
                {% endfor %}
            {% endif %}
            </tbody>
        </table>
    </div>
</div>

<script>
    var options = {
        chart: {
            type: 'bar'
        },
        plotOptions: {
            bar: {
                horizontal: true
            }
        },
        series: [{
            name: "Cantidad",
            data: [
                {% for estado, count in estadoCounts %}
                {
                    x: '{{ estado }}',
                    y: {{ count }},
                    fillColor: '#EB8C87',
                    strokeColor: '#c23829'
                },
                {% endfor %}
            ]
        }],
    };
    var chart = new ApexCharts(document.querySelector("#apexchart"), options);
    chart.render();
</script>
