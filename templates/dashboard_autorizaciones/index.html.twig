{% extends 'admin/layout.html.twig' %}

{% block title %}| Autorizaciones{% endblock %}

{% block content %}
    <link rel="stylesheet" href="{{ asset('css/styles/autorizaciones.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&family=League+Spartan:wght@100..900&display=swap" rel="stylesheet">

    <style>
        #numeroAutorizacion::placeholder {
            color: #2f2f2f;
            opacity: 1;
        }
    </style>

    <div class="body pt-5 d-flex flex-column align-items-center">
        <div class="container-fluid mt-4 bg-white rounded-4 py-4 shadow">
            <h1 class="text-center page-title mb-4">Autorizaciones</h1>
            <h2 class="page-subtitle mx-4 mb-0 text-start">Filtros:</h2>
            <div class="row mb-3 justify-content-center">
                <div class="col-md-3 mb-2">
                    <select id="autorizacionState" class="form-control custom-select">
                        <option value="-2">Todos los estados</option>
                        <option value="0">Sin asociar</option>
                        {% for AuthStage in authStages %}
                            <option value="{{ AuthStage.idauthstage }}">{{ AuthStage.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-2">
                    <select id="tipoVenta" class="form-control custom-select">
                        <option value="-1">Selecciona un tipo de venta</option>
                        <option value="-2">Todos los tipos de venta</option>
                        {% for filtrosAutorizacion in filtrosAutorizaciones %}
                            <option value="{{ filtrosAutorizacion.idtipoventa }}">{{ filtrosAutorizacion.nombre }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-2 d-flex align-items-center">
                    <input type="number" class="form-control input-aut" id="numeroAutorizacion" placeholder="Número de autorización"/>
                </div>
                <div class="col-md-3 d-flex justify-content-center align-items-center">
                    <input type="number" class="form-control input-aut" id="folioVenta" placeholder="Folio de venta"/>
                </div>
                <div class="col-md-3 d-flex justify-content-center align-items-center">
                    <input type="text" class="form-control input-aut" id="scanvalue" placeholder="Scaner"/>
                </div>
            </div>
            <div class="row d-flex justify-content-start align-items-center">
                <div class="col-md-auto text-center">
                    <label class="text-input m-0" for="idempresa">Empresas: </label><br>
                </div>
                <div class="col text-center">
                    {% if enterprises is not empty %}
                        {# Si hay empresas en la lista, se creará una opción en el select para cada una de ellas. #}
                        <select name="enterprises" id="idempresa" class="form-control" onchange="obtenerSucursales();">
                            <option value=-1>Seleccione una empresa</option>
                            {% for enterprise in enterprises %}
                                <option value="{{ enterprise.idempresa }}">{{ enterprise.nombre }}</option>
                            {% endfor %}
                        </select>
                    {% else %}
                        {# Si la lista de empresas está vacía (es decir, el usuario no tiene permiso para ninguna empresa), se mostrará este mensaje. #}
                        <h4>Aún no tienes empresas asignadas</h4>
                    {% endif %}
                </div>
                <div class="col-md-auto text-center p-0 ps-3">
                    <label for="rango" class="m-0 text-input">Rango de fecha:</label>
                </div>
                <div class="col-md-5 d-flex justify-content-center align-items-center py-3">
                    <input id="fecha-inicio-rango-dia" type="text" autocomplete="off"
                            class="input-aut form-control mx-2"
                            name="start"/>
                    <span class="px-2 py-2">a</span>
                    <input id="fecha-fin-rango-dia" type="text" autocomplete="off"
                            class="input-aut form-control mx-2"
                            name="end"/>
                    <button class="btn btn-warning" onclick="resetRangoFechaDias()">
                        <i class="fa fa-eraser" aria-hidden="true"></i>
                    </button>
                </div>
                <div class="col-md-2 text-center">
                    <button class="btn btn-primary btn-block" id="buscarBtn" onclick="buscarAutorizaciones();">Buscar
                    </button>
                </div>
            </div>
            <div id="sucursales"></div>
            <div id="tablaAutorizaciones" class="container-fluid mt-5">
            </div>
        </div>
    </div>

    <div class="mod " id="modal-autorizaciones" tabindex="-1" aria-labelledby="modal-dashboard-autorizaciones" aria-hidden="true">
        <div class="mod-dialog">
            <div class="modal-content" style="border-radius:10px">
                <div class="modal-header title-table">
                    <h1 class="modal-title fs-5" id="modal-autorizaciones-title"></h1>
                    <button type="button" class="btn-close" data-bs-dismiss="mod" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="modal-autorizaciones-body"></div>
                <div class="modal-footer text-center">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="mod">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script>
        $(document).ready(function () {

            $('#buscarAutorizacionesBtn').on('click', function () {
                buscarAutorizaciones();
            });
        });

        function buscarAutorizaciones() {
            var autorizacionState = $('#autorizacionState').val();
            var tipoVenta = $('#tipoVenta').val();
            var numeroAutorizacion = $('#numeroAutorizacion').val();
            var fechaInicio = $('#fecha-inicio-rango-dia').val();
            var fechaFin = $('#fecha-fin-rango-dia').val();
            var folioVenta = $('#folioVenta').val();
            var scanvalue = $('#scanvalue').val();

            var locations = $('input[name="sucursal"]:checked, input[name="bodega"]:checked, input[name="campaña"]:checked').map(function() {
                return this.value;
            }).get();

            var idempresa=$("#idempresa").val();

            var datos = {
                autorizacionState: autorizacionState,
                tipoVenta: tipoVenta,
                fechaInicio: fechaInicio,
                fechaFin: fechaFin,
                numeroAutorizacion: numeroAutorizacion,
                folioVenta: folioVenta,
                scanvalue: scanvalue,
                locations: locations,
                idempresa: idempresa
            };

            console.log(datos);

            $.ajax({
                url: "{{ path('tabla_dashboard_autorizaciones') }}",
                method: 'GET',
                data: datos,
                beforeSend: loadingGif("tablaAutorizaciones"),
                success: function (response) {
                    $("#tablaAutorizaciones").html(response);
                    $('#autorizacionesTable').DataTable({
                        dom: 'Bfrtip',
                        buttons: [
                            {
                                extend: 'excelHtml5', // Tipo de archivo a exportar (Excel)
                                title: 'Reporte Flujo Expediente', // Título del archivo Excel
                            },
                        ],
                        "paging": true,
                        "searching": true,
                        "ordering": true,
                        "info": true,
                        "autoWidth": false,
                        "lengthChange": false,
                        "pageLength": 15,
                        "language": {
                            "lengthMenu": "Mostrar _MENU_ registros por página",
                            "zeroRecords": "No se encontraron resultados",
                            "info": "Mostrando _PAGE_ de _PAGES_ páginas",
                            "infoEmpty": "No hay registros disponibles",
                            "infoFiltered": "(Filtrado de _MAX_ registros en total)",
                            "search": "Buscar:",
                            "paginate": {
                                "first": "Primerxo",
                                "last": "Último",
                                "next": "Siguiente",
                                "previous": "Anterior"
                            }
                        }
                    });
                    // No necesitas renderizar ApexCharts aquí, ya se hace en `tableAutorizaciones.html.twig`
                },
                error: function (xhr, status, error) {
                    console.error("Ocurrió un error:", error);
                    // Puedes mostrar un mensaje de error al usuario
                }
            });
        }


    </script>
    <script>
        jQuery(function ($) {
            $('#fecha-inicio-rango-dia').datetimepicker({
                "pickTime": false,
                "pickDate": true,
                "minDate": "1/1/1900",
                "maxDate": null,
                "showToday": true,
                "language": "es_MX",
                "defaultDate": "",
                "disabledDates": [],
                "enabledDates": [],
                "icons": {
                    "time": "fa fa-clock-o",
                    "date": "fa fa-calendar",
                    "up": "fa fa-chevron-up",
                    "down": "fa fa-chevron-down"
                },
                "useStrict": false,
                "sideBySide": false,
                "daysOfWeekDisabled": [],
                "collapse": true,
                "calendarWeeks": false,
                "viewMode": "days",
                "minViewMode": "days",
                "useCurrent": false,
                "useSeconds": false
            });

            $('#fecha-fin-rango-dia').datetimepicker({
                "pickTime": false,
                "pickDate": true,
                "minDate": "1/1/1900",
                "maxDate": null,
                "showToday": true,
                "language": "es_MX",
                "defaultDate": "",
                "disabledDates": [],
                "enabledDates": [],
                "icons": {
                    "time": "fa fa-clock-o",
                    "date": "fa fa-calendar",
                    "up": "fa fa-chevron-up",
                    "down": "fa fa-chevron-down"
                },
                "useStrict": false,
                "sideBySide": false,
                "daysOfWeekDisabled": [],
                "collapse": true,
                "calendarWeeks": false,
                "viewMode": "days",
                "minViewMode": "days",
                "useCurrent": false,
                "useSeconds": false
            });
        });

        function resetRangoFechaDias() {
            $("#fecha-inicio-rango-dia").val("");
            $("#fecha-fin-rango-dia").val("");
        }

        function obtenerSucursales(){
            var idempresa=$("#idempresa").val();
            $.ajax({
                url: "{{path('almacen-obtener-sucursal')}}",
                data: {idempresa:idempresa},
                dataType: "html"
            }).done(function( html ) {
                $("#sucursales").html(html);
            }).fail(function() {
                alert( "error" );
            });
        }

        function getDocuments(saleId){
            $.ajax({
                url: "{{path('dashboard-get-documents')}}",
                data: {saleId:saleId},
                dataType: "html",
                beforeSend: loadingGif("modal-autorizaciones-body"),
            }).done(function( html ) {
                $("#modal-autorizaciones-body").html(html);
            }).fail(function() {
                alert( "error" );
            });
        }
        function getHistory(saleId){
            $.ajax({
                url: "{{path('dashboard-get-history')}}",
                data: {saleId:saleId},
                dataType: "html",
                beforeSend: loadingGif("modal-autorizaciones-body"),
            }).done(function( html ) {
                $("#modal-autorizaciones-body").html(html);
            }).fail(function() {
                alert( "error" );
            });
        }

        function getChangeStateForm(saleId){
            $.ajax({
                url: "{{path('dashboard-get-change-state-form')}}",
                data: {saleId:saleId},
                dataType: "html",
                beforeSend: loadingGif("modal-autorizaciones-body"),
            }).done(function( html ) {
                $("#modal-autorizaciones-body").html(html);
            }).fail(function() {
                alert( "error" );
            });
        }

    </script>
{% endblock %}
