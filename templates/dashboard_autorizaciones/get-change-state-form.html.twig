{% if authStages|length > 0 %}
    <div class="text-center container">
        <h3>Cambia el estado de esta venta</h3>
        <div class="row my-3">
            <div class="col-md-6">
                <label class="form-label" for="select-stage">Selecciona el nuevo estado</label>
                <select id="select-stage" class="form-control">
                {% for AuthStage in authStages %}
                    <option value="{{AuthStage.stageorder}}">{{AuthStage.name}}</option>
                {% endfor %}
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label" for="comment-stage">¿Por qué lo estás cambiando?</label>
                <textarea id="comment-stage" class="form-control" id="exampleFormControlTextarea1" rows="3"></textarea>
            </div>
        </div>
        <button id="save-stage-button" class="btn btn-info" onclick="chanegStage()">Guardar</button>
    </div>
{% else %}
    <h3 class="text-center">No hay un estado disponible para esta venta</h3>
{% endif %}

<script>
    function chanegStage(){
        const comment = $("#comment-stage").val()
        const stage = $("#select-stage").val()
        const stageText = $("#select-stage option:selected").text()
        $("#save-stage-button").prop("disabled",true);
        if (comment){
            Swal.fire({
                title: '¿Está seguro?',
                text: "Se cambiará el estado de esta venta",
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28B463',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Aceptar',
                cancelButtonText: 'Cancelar'
            }).then((result) => {
                if (result.value) {
                    $.ajax({
                        url: "{{path('dashboard-change-auth-stage')}}",
                        data: {
                            saleId:"{{saleid}}",
                            stage:stage,
                            comment:comment
                        },
                        dataType: "json"
                    }).done(function( response ) {
                        if (response.success){
                            $("#auth-stage-{{saleid}}").text(stageText);
                            getChangeStateForm('{{saleid}}')
                        } else {
                            Swal.fire({
                                title: 'Algo salió mal',
                                text: response.msg,
                                icon: "error",
                                confirmButtonText: 'Entendido'
                            })
                        }
                        $("#save-stage-button").prop("disabled",false);
                    }).fail(function() {
                        alert( "error" );
                    });
                } else $("#save-stage-button").prop("disabled",false);
            })
            
        } else {
            Swal.fire({
                title: 'Alerta',
                text: 'Necesitas agregar la razón del cambio',
                icon: "warning",
                confirmButtonText: 'Entendido'
            })
            $("#save-stage-button").prop("disabled",false);
        }
    }
</script>