{% extends 'admin/layout.html.twig' %}

{% block title %}| Reporte Flujo Expediente{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.dataTables.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/select/1.3.3/css/select.dataTables.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/searchpanes/1.4.0/css/searchPanes.dataTables.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/rowgroup/1.1.3/css/rowGroup.dataTables.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/fixedheader/3.2.2/css/fixedHeader.dataTables.min.css">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="{{ asset('css/reportes/reporte-flujo-expediente.css') }}">
{% endblock %}

{% block content %}
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header justify-content-between d-flex align-items-center mb-4">
                            <i class="fas fa-file-alt fa-2x text-primary mr-3"></i>
                            <h1 class="page-title mb-0">Reporte de Ventas | Graduaciones</h1>
                        <div class="header-actions">
                            <button class="btn btn-light" id="toggleFilters">
                                <i class="fas fa-filter"></i> Mostrar Filtros
                            </button>
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="filtros-container" style="display: none;">
                            <!-- Sección de Fechas -->
                            <div class="filtros-section">
                                <h4 class="filtros-section-title">
                                    <i class="fas fa-calendar-alt"></i> Fechas
                                </h4>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Fecha de Venta Desde</label>
                                            <input type="date" class="form-control" id="fechaVentaDesde">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Fecha de Venta Hasta</label>
                                            <input type="date" class="form-control" id="fechaVentaHasta">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Fecha de Creación Desde</label>
                                            <input type="date" class="form-control" id="fechaCreacionDesde">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label>Fecha de Creación Hasta</label>
                                            <input type="date" class="form-control" id="fechaCreacionHasta">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Sección de Información de Venta -->
                            <div class="filtros-section">
                                <h4 class="filtros-section-title">
                                    <i class="fas fa-shopping-cart"></i> Información de Venta
                                </h4>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Folio</label>
                                            <input type="text" class="form-control" id="folioFilter" placeholder="Buscar por folio">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Sucursal</label>
                                            <select class="form-control" id="sucursalFilter">
                                                <option value="">Todas las sucursales</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Tipo</label>
                                            <select class="form-control" id="cotizacionFilter">
                                                <option value="">Todos</option>
                                                <option value="0">Venta</option>
                                                <option value="1">Cotización</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Sección de Información del Cliente -->
                            <div class="filtros-section">
                                <h4 class="filtros-section-title">
                                    <i class="fas fa-user"></i> Información del Cliente
                                </h4>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Nombre del Cliente</label>
                                            <input type="text" class="form-control" id="nombreClienteFilter" placeholder="Nombre del cliente">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Correo Electrónico</label>
                                            <input type="email" class="form-control" id="emailClienteFilter" placeholder="Correo electrónico">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Teléfono</label>
                                            <input type="text" class="form-control" id="telefonoClienteFilter" placeholder="Número telefónico">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Sección de Estado -->
                            <div class="filtros-section">
                                <h4 class="filtros-section-title">
                                    <i class="fas fa-check-circle"></i> Estado
                                </h4>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Estado de Pago</label>
                                            <select class="form-control" id="liquidadaFilter">
                                                <option value="">Todos</option>
                                                <option value="1">Liquidada</option>
                                                <option value="0">No liquidada</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Agrupar por Venta</label>
                                            <select class="form-control" id="agruparPorVentaFilter">
                                                <option value="0">No agrupar</option>
                                                <option value="1" selected>Agrupar por Venta</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="row">
                                            <div class="col-md-6" style="padding-top: 37px;">
                                                <div class="form-group mt-4">
                                                    <button class="btn-aplicar-filtros" id="aplicarFiltros">
                                                        <i class="fas fa-search"></i> Aplicar Filtros
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="col-md-6" style="padding-top: 32px;">
                                                <div class="form-group mt-4">
                                                    <button class="btn-limpiar-filtros" id="limpiarFiltros">
                                                        <i class="fas fa-times"></i> Limpiar Filtros
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <table id="tablaExpedientes" class="table table-bordered table-striped w-100" style="width:100%">
                            <thead>
                                <tr>

                                    <th>Opciones</th>
                                    <th>Folio</th>
                                    <th>Autorización</th>
                                    <th>Tipo Venta</th>
                                    <th>Crédito</th>
                                    <th>Empresa</th>
                                    <th>Sucursal</th>
                                    <th>Núm. Empleado</th>
                                    <th>Empresa Cliente</th>
                                    <th>Unidad</th>
                                    <th>Referencia</th>
                                    <th>Porcentaje IVA</th>
                                    <th>IVA</th>
                                    <th>SubTotal</th>
                                    <th>Total</th>
                                    <th>Deuda</th>
                                    <th>Fecha Creación</th>
                                    <th>Fecha Venta</th>
                                    <th>Fecha Actualización</th>
                                    <th>UPC</th>
                                    <th>Liquidada</th>
                                    <th>Cantidad</th>
                                    <th>Precio</th>
                                    <th>Precio Final</th>
                                    <th>Descuento</th>
                                    <th>Cliente</th>
                                    <th>Esfera OD</th>
                                    <th>Cilindro OD</th>
                                    <th>Eje OD</th>
                                    <th>Esfera OI</th>
                                    <th>Cilindro OI</th>
                                    <th>Eje OI</th>
                                    <th>DIP</th>
                                    <th>AO</th>
                                    <th>ACO</th>
                                    <th>ADD Ojo Izquierdo</th>
                                    <th>ADD Ojo Derecho</th>
                                    <th>Productos de la Venta</th>
                                    <th>Diagnostico</th>
                                    <th>Notas</th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/select/1.3.3/js/dataTables.select.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/searchpanes/1.4.0/js/dataTables.searchPanes.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/rowgroup/1.1.3/js/dataTables.rowGroup.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/fixedheader/3.2.2/js/dataTables.fixedHeader.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
        $(document).ready(function() {
            // Toggle filtros
            $('#toggleFilters').click(function() {
                $('.filtros-container').slideToggle(300);
                $(this).find('i').toggleClass('fa-filter fa-filter-slash');

                // Cambiar texto del botón
                const buttonText = $(this).text().trim();
                $(this).html(
                    `<i class="fas ${$(this).find('i').attr('class').includes('slash') ? 'fa-filter-slash' : 'fa-filter'}"></i> ${
                        buttonText === 'Mostrar Filtros' ? 'Ocultar Filtros' : 'Mostrar Filtros'
                    }`
                );
            });

            const spanishLanguage = {
                "processing": "Procesando...",
                "lengthMenu": "Mostrar _MENU_ registros",
                "zeroRecords": "No se encontraron resultados",
                "emptyTable": "Ningún dato disponible en esta tabla",
                "info": "Mostrando registros del _START_ al _END_ de un total de _TOTAL_ registros",
                "infoEmpty": "Mostrando registros del 0 al 0 de un total de 0 registros",
                "infoFiltered": "(filtrado de un total de _MAX_ registros)",
                "search": "Buscar:",
                "infoThousands": ",",
                "loadingRecords": "Cargando...",
                "paginate": {
                    "first": "Primero",
                    "last": "Último",
                    "next": "Siguiente",
                    "previous": "Anterior"
                },
                "aria": {
                    "sortAscending": ": Activar para ordenar la columna de manera ascendente",
                    "sortDescending": ": Activar para ordenar la columna de manera descendente"
                },
                "buttons": {
                    "copy": "Copiar",
                    "colvis": "Visibilidad",
                    "collection": "Colección",
                    "colvisRestore": "Restaurar visibilidad",
                    "copyKeys": "Presione ctrl o u2318 + C para copiar los datos de la tabla al portapapeles del sistema. <br \/> <br \/> Para cancelar, haga clic en este mensaje o presione escape.",
                    "copySuccess": {
                        "1": "Copiada 1 fila al portapapeles",
                        "_": "Copiadas %ds fila al portapapeles"
                    },
                    "copyTitle": "Copiar al portapapeles",
                    "csv": "CSV",
                    "excel": "Excel",
                    "pageLength": {
                        "-1": "Mostrar todas las filas",
                        "_": "Mostrar %d filas"
                    },
                    "pdf": "PDF",
                    "print": "Imprimir",
                    "renameState": "Cambiar nombre",
                    "updateState": "Actualizar",
                    "createState": "Crear Estado",
                    "removeAllStates": "Remover Estados",
                    "removeState": "Remover",
                    "savedStates": "Estados Guardados",
                    "stateRestore": "Estado %d"
                }
            };

            $('#sucursalFilter').select2({
                placeholder: 'Seleccionar sucursal',
                allowClear: true,
                width: 'resolve',
                dropdownAutoWidth: false,
                dropdownParent: $('.filtros-container'),
                minimumResultsForSearch: 6,
                ajax: {
                    url: "{{ path('obtener_sucursales_activas') }}",
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        return {
                            q: params.term,
                            page: params.page
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data.items.map(function(item) {
                                return {
                                    id: item.idsucursal,
                                    text: item.nombre
                                };
                            })
                        };
                    },
                    cache: true
                },
                templateResult: function(data) {
                    if (data.loading) return data.text;
                    var $container = $(
                        '<div class="select2-result-item">' +
                        '<div class="select2-result-item__title"></div>' +
                        '</div>'
                    );
                    $container.find(".select2-result-item__title").text(data.text);
                    return $container;
                },
                language: {
                    inputTooShort: function() {
                        return "Por favor ingrese 1 o más caracteres";
                    },
                    noResults: function() {
                        return "No se encontraron resultados";
                    },
                    searching: function() {
                        return "Buscando...";
                    },
                    errorLoading: function() {
                        return "Error cargando resultados";
                    }
                }
            });

            var liquidadaFilter = $('#liquidadaFilter').val();

            // Inicialización de DataTable
            var table = $('#tablaExpedientes').DataTable({
                processing: true,
                scrollX: true,
                scrollCollapse: true,
                autoWidth: false,
                pageLength: 25,
                fixedHeader: true,



                ajax: {
                    url: "{{ path('obtener_datos_reporte') }}",
                    type: "GET",
                    data: function (d) {
                        d.fechaVentaDesde = $('#fechaVentaDesde').val();
                        d.fechaVentaHasta = $('#fechaVentaHasta').val();
                        d.fechaCreacionDesde = $('#fechaCreacionDesde').val();
                        d.fechaCreacionHasta = $('#fechaCreacionHasta').val();
                        d.cotizacion = $('#cotizacionFilter').val();
                        d.folio = $('#folioFilter').val();
                        d.sucursal = $('#sucursalFilter').val();
                        d.nombreCliente = $('#nombreClienteFilter').val();
                        d.emailCliente = $('#emailClienteFilter').val();
                        d.telefonoCliente = $('#telefonoClienteFilter').val();
                        d.liquidada = $('#liquidadaFilter').val();
                        console.log(d.folio);
                        return;
                    }
                },
                columns: [
                    {
                        data: null,
                        orderable: false,
                        render: function (data) {
                            const urlTemplate = "{{ path('reporte_detalle_venta', {'id': '__ID__'}) }}";
                            const url = urlTemplate.replace('__ID__', data.idstockventa);

                            return `
        <div class="d-flex justify-content-center">
          <button
            class="btn-action btn-detalle"
            onclick="window.location.href='${url}'"
          >
            <i class="fas fa-eye"></i> Detalle
          </button>
        </div>
      `;
                        }
                    },
                    {data: 'folioVenta'},
                    {data: 'authorizationnumber'},
                    {data: 'tipoVenta'},
                    {data: 'credito'},
                    {data: 'empresa'},
                    {data: 'sucursal'},
                    {data: 'numeroEmpleado'},
                    {data: 'nombreEmpresaCliente'},
                    {data: 'nombreUnidad'},
                    {data: 'nombreReferencia'},
                    {
                        data: 'porcentajeIva',
                        render: d => d + '%',

                    },
                    {data: 'iva'},
                    {data: 'subtotal'},
                    {data: 'total'},
                    {data: 'deuda'},
                    {
                        data: 'fechaCreacion',
                        render: d => moment(d).format('DD/MM/YYYY')
                    },
                    {
                        data: 'fechaVenta',
                        render: d => moment(d).format('DD/MM/YYYY')
                    },
                    {
                        data: 'fechaactualizacion',
                        render: d => moment(d).format('DD/MM/YYYY'),
                        className: 'fecha-actualizacion-column'
                    },
                    {
                        data: 'codigobarras'
                    },
                    {
                        data: 'liquidada',
                        render: d => d === '1' ? 'Sí' : 'No'
                    },

                    {
                        data: 'cantidadVendida',
                        className: 'text-right'
                    },
                    {
                        data: 'precioVenta',
                        render: d => {
                            const numero = typeof d === 'string' ? parseFloat(d) : d;
                            return !isNaN(numero) ? new Intl.NumberFormat('es-MX', {
                                style: 'currency',
                                currency: 'MXN'
                            }).format(numero) : '$0.00';
                        },
                        className: 'text-right'
                    },
                    {
                        data: 'precioFinal',
                        render: d => new Intl.NumberFormat('es-MX', {style: 'currency', currency: 'MXN'}).format(d),
                        className: 'text-right'
                    },
                    {
                        data: 'descuento',
                        render: d => d + '%',
                        className: 'text-right'
                    },
                    {
                        data: null,
                        render: d => `${d.nombreCliente ?? ''} ${d.apellidoPaternoCliente ?? ''} ${d.apellidoMaternoCliente ?? ''}`
                    },
                    {data: 'esferaDerecha'},
                    {data: 'cilindroDerecho'},
                    {data: 'ejeDerecho'},
                    {data: 'esferaIzquierda'},
                    {data: 'cilindroIzquierdo'},
                    {data: 'ejeIzquierdo'},
                    {data: 'distanciaPupilar'},
                    {data: 'alturaOjos'},
                    {data: 'ACO'},
                    {data: 'AVCercaAddOI'},
                    {data: 'AVCercaAddOD'},
                    {
                        data: 'ProductosVenta',
                        render: function (data) {
                            if (!data) return '';
                            try {
                                const productos = JSON.parse(data);
                                return productos.map(p => {
                                    let detalles = [];
                                    if (p.modelo) detalles.push(`Modelo: ${p.modelo}`);
                                    if (p.codigobarras) detalles.push(`Codigo Barras: ${p.codigobarras}`);
                                    if (p.codigobarrasuniversal) detalles.push(`UPC: ${p.codigobarrasuniversal}`);
                                    return detalles.join(' | ');
                                }).join('\n');
                            } catch (e) {
                                return data || '';
                            }
                        }
                    },
                    {data: 'Diagnostico'},
                    {data: 'Notas'},
                ],
                dom: '<"row"<"col-sm-12 col-md-6"B><"col-sm-12 col-md-6"f>>rtip',
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel"></i> Excel',
                        className: 'btn btn-primary excel-btn',
                        exportOptions: {
                            columns: ':not(:first-child)',
                            format: {
                                body: function (data, row, column, node) {
                                    if (column === 30) {
                                        try {
                                            const productos = JSON.parse(data);
                                            return productos.map(p => {
                                                let detalles = [];
                                                if (p.modelo) detalles.push(`Modelo: ${p.modelo}`);
                                                if (p.codigobarras) detalles.push(`Codigo Barras: ${p.codigobarras}`);
                                                if (p.codigobarrasuniversal) detalles.push(`UPC: ${p.codigobarrasuniversal}`);
                                                return detalles.join(' | ');
                                            }).join('\n');
                                        } catch (e) {
                                            return data || '';
                                        }
                                    }
                                    return data;
                                }
                            }
                        }
                    },
                ],
                language: spanishLanguage,
                initComplete: function () {
                    // Función para aplicar o quitar el agrupamiento
                    function toggleGrouping() {
                        const agruparPorVenta = $('#agruparPorVentaFilter').val() === '1';

                        if (agruparPorVenta) {
                            table.rowGroup().enable().dataSrc('idventa');
                            $('#tablaExpedientes').addClass('grouped-by-venta');
                        } else {
                            table.rowGroup().disable();
                            $('#tablaExpedientes').removeClass('grouped-by-venta');
                        }

                        table.draw();
                        // Recalcular anchos de columnas después de dibujar la tabla
                        setTimeout(function () {
                            table.columns.adjust();
                        }, 100);
                    }

                    // Inicializar agrupamiento según el valor por defecto
                    toggleGrouping();

                    // Evento para cambiar el agrupamiento
                    $('#agruparPorVentaFilter').change(function () {
                        toggleGrouping();
                    });

                    // Evento para aplicar filtros
                    $('#aplicarFiltros').click(function () {
                        table.ajax.reload(function () {
                            // Recalcular anchos de columnas después de recargar datos
                            setTimeout(function () {
                                table.columns.adjust();
                            }, 100);
                        });
                    });

                    // Inicializar FixedHeader después de que la tabla esté completamente cargada
                    new $.fn.dataTable.FixedHeader(table);

                    // Recalcular anchos cuando cambia el tamaño de la ventana
                    $(window).on('resize', function () {
                        table.columns.adjust();
                    });
                },
                rowGroup: {
                    dataSrc: 'idventa',
                    startRender: function (rows, group) {
                        return $('<tr class="group-header"/>')
                            .append('<td colspan="42"><i class="fas fa-shopping-cart mr-2"></i><strong>Venta ID: ' + group + '</strong> (' + rows.count() + ' productos)</td>');
                    },
                    enable: true
                }
            });

            // Función para limpiar filtros
            $('#limpiarFiltros').click(function() {
                $('.filtros-container input, .filtros-container select').not('#agruparPorVentaFilter').val('');
                $('#agruparPorVentaFilter').val('1'); // Mantener el agrupamiento por defecto

                table.ajax.reload(function() {
                    setTimeout(function() {
                        const agruparPorVenta = $('#agruparPorVentaFilter').val() === '1';
                        if (agruparPorVenta) {
                            table.rowGroup().enable().dataSrc('idventa');
                            $('#tablaExpedientes').addClass('grouped-by-venta');
                        }
                        table.draw();

                        setTimeout(function() {
                            table.columns.adjust();
                        }, 100);
                    }, 100);
                });
            });

            table.on('draw.dt', function() {
                setTimeout(function() {
                    table.columns.adjust();
                }, 100);
            });


            if (window.innerWidth < 768) {
                setTimeout(function() {
                    table.columns.adjust();
                }, 500);
            }


        });

        // Función para cancelar venta
        function cancelarVenta(idventa) {
            Swal.fire({
                title: '¿Estás seguro?',
                text: "¿Deseas cancelar esta venta?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Sí, cancelar',
                cancelButtonText: 'No, mantener'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = "{{ path('reporte_cancelar_venta', {'id': '0'}) }}".replace('0', idventa);
                }
            });
        }
    </script>
{% endblock %}
