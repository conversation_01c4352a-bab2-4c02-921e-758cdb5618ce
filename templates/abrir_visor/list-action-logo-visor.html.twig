<button href="#" type="button" class="btn btn-sm btn-info" onclick="openEnterpriseVisor('{{ object.logoimagen }}','logo')">Logo</button>

<script>
function openEnterpriseVisor(logoname, filetype, enterpriseName){
    
        $("#container-modal-enterprise-visor").html("");
    
        $.ajax({
            method: "POST",
            url: "{{path('app-enterprise-visor')}}",
            data: { logoname:logoname, filetype:filetype, enterpriseName:enterpriseName }
        })
        .done(function( html ) {
            $("#container-modal-enterprise-visor").html(html);
            $("#modal-enterprise-visor").modal("show");
    
        });
    }
</script>