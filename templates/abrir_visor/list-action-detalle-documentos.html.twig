<button href="#" class="btn btn-sm btn-info" onclick="abrirVisor({{ object.iddocumentos }})  ; return false;">Visualizar</button>

<!-- Modal -->
<div class="modal fade" id="modal-visor" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Visualizador de Documentos</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="contenedor-modal-visor">
                <!-- Aquí se cargará el contenido del documento via AJAX -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>


<script>
    function abrirVisor(iddocumentos) {

        console.log("Lenon")

        $("#contenedor-modal-visor").html("");

        let url = $("#url-app_abrir_visor").val();

        $.ajax({
            method: "POST",
            url: url,
            data: {iddocumentos: iddocumentos}
        })
            .done(function (html) {
                $("#contenedor-modal-visor").html(html);
                $("#modal-visor").modal("show");

            });
    }
</script>
