<style type="text/css">
	.tg {
		border-collapse: collapse;
		border-spacing: 0;
	}
	.tg td {
		border-color: black;
		border-style: solid;
		border-width: 1px;
		font-family: Arial, sans-serif;
		font-size: 14px;
		overflow: hidden;
		padding: 10px 5px;
		word-break: normal;
	}
	.tg th {
		border-color: black;
		border-style: solid;
		border-width: 1px;
		font-family: Arial, sans-serif;
		font-size: 12px;
		font-weight: normal;
		overflow: hidden;
		padding: 5px;
		word-break: normal;
	}
	.tg .tg-0lax {
		text-align: center;
		vertical-align: top
	}
</style>

<table class="tg" width="100%" border="1">
	<thead></thead>
	<tbody>
		<tr>
			<td colspan="5" style="width: 100%; ">

				<div style=" width: 100%; height: auto; background-color: #eFe6e6;">
					<img src="{{ logo }}" alt="logo" style="height: 20px; float: left; margin: 0;">
					<h4 style="float: left; margin: 1; margin-left: 23%;">Orden de Laboratorio</h4>
					<h4 style="float: right; margin: 1; margin-right: 5%;">PRO-19-A</h4>
				</div>
				<div style="clear: both;"></div>

				<div style="float: right; width: 200px; ">
					<h5 style="display: inline-block; margin: 0;">Folio Ticket:</h5>
					<p style="display: inline-block; margin: 0; text-decoration: underline;">&nbsp;
						{% for folio in folios %}
							{{ folio.folio }}&nbsp;
						{% endfor %}
					</p>
				</div>

				<div style="clear: both;"></div>

				<div style=" width: 100%; height: auto; ">

					<div style="float: left;">
						<h5 style="display: inline-block; margin: 0;">Sucursal:</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">&nbsp;{{ ol.flujoexpedienteIdflujoexpediente.sucursalIdsucursal.nombre }}&nbsp;</p>
					</div>

					<div style="float: left; margin-left: 30px;">
						<h5 style="display: inline-block; margin: 0;">Procedencia:</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">
							{% if ol.clienteIdcliente.empresaclienteIdempresacliente is null %}
								{% for _ in 1..20 %}
									&nbsp;
								{% endfor %}
							{% else %}
								&nbsp;{{ ol.clienteIdcliente.empresaclienteIdempresacliente.nombre }}&nbsp;
							{% endif %}
						</p>
					</div>

					<div style="float: right; width: 200px; ">
						<h5 style="display: inline-block; margin: 0;">Fecha:</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">&nbsp;{{ ol.creacion|date("d / m / Y ") }}&nbsp;</p>
					</div>

				</div>
				<div style="clear: both;"></div>

			</td>
		</tr>

		<tr>
			<td colspan="5">

				<div style=" width: 100%; height: auto; ">
					<div style="float: left;">
						<h5 style="display: inline-block; margin: 0;">Titular:</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">
							{% if ol.clienteIdcliente.holder is null %}
								&nbsp;{{ ol.clienteIdcliente.nombre }}&nbsp;{{ ol.clienteIdcliente.apellidopaterno }}&nbsp;{{ ol.clienteIdcliente.apellidomaterno }}&nbsp;
							{% else %}
								&nbsp;{{ ol.clienteIdcliente.holder.nombre }}&nbsp;{{ ol.clienteIdcliente.holder.apellidopaterno }}&nbsp;{{ ol.clienteIdcliente.holder.apellidomaterno }}&nbsp;
							{% endif %}
						</p>
					</div>
					<div style="float: right; width: 200px; ">
						<h5 style="display: inline-block; margin: 0;">Edad:</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">&nbsp;{{ ol.clienteIdcliente.edad }}&nbsp;</p>
					</div>
				</div>
				<div style="clear: both;"></div>

				<div style=" width: 100%; height: auto; ">
					<div style="float: left;">
						<h5 style="display: inline-block; margin: 0;">Beneficiario (*1):</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">
							{% if ol.clienteIdcliente.holder is null %}
								{% for _ in 1..20 %}
									&nbsp;
								{% endfor %}
							{% else %}
								&nbsp;{{ ol.clienteIdcliente.nombre }}&nbsp;{{ ol.clienteIdcliente.apellidopaterno }}&nbsp;{{ ol.clienteIdcliente.apellidomaterno }}&nbsp;
							{% endif %}
						</p>
					</div>
				</div>
				<div style="clear: both;"></div>

				<div style=" width: 100%; height: auto; ">
					<div style="float: left;">
						<h5 style="display: inline-block; margin: 0;">N° (*1):</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">
							{% if ol.clienteIdcliente.numeroempleado is null %}
								{% for _ in 1..15 %}
									&nbsp;
								{% endfor %}
							{% else %}
								&nbsp;{{ ol.clienteIdcliente.numeroempleado }}&nbsp;
							{% endif %}
						</p>
					</div>
					<div style="float: right; width: 200px; ">
						<h5 style="display: inline-block; margin: 0;">Unidad:(*z):</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">
							{% if ol.clienteIdcliente.unidadIdunidad is null %}
								{% for _ in 1..10 %}
									&nbsp;
								{% endfor %}
							{% else %}
								&nbsp;{{ ol.clienteIdcliente.unidadIdunidad.nombre }}&nbsp;
							{% endif %}

						</p>
					</div>
				</div>
				<div style="clear: both;"></div>

			</td>
		</tr>
		<tr>
			<th class="tg-0lax" style="width: 5%;"></th>
			<th class="tg-0lax" style="width: 20%;">Esfera</th>
			<th class="tg-0lax" style="width: 20%;">Cilindro</th>
			<th class="tg-0lax" style="width: 20%;">Eje</th>
			<th class="tg-0lax" style="width: 35%;">Add</th>
		</tr>
		<tr>
			<td class="tg-0lax">OD</td>
			<td class="tg-0lax">{{ol.esferaod}}</td>
			<td class="tg-0lax">{{ol.cilindrood}}</td>
			<td class="tg-0lax">{{ol.ejeod}}</td>
			<td class="tg-0lax" rowspan="2" style="align-content: center;">
				<p>{{ol.addordenlaboratorio}}</p>
			</td>
		</tr>
		<tr>
			<td class="tg-0lax">OI</td>
			<td class="tg-0lax">{{ol.esferaoi}}</td>
			<td class="tg-0lax">{{ol.cilindrooi}}</td>
			<td class="tg-0lax">{{ol.ejeoi}}</td>
		</tr>
		<tr>
			<td class="tg-0lax" colspan="5">
				<div style="width: 100%; text-align: center;">
					<div style="display: inline-block; text-align: left;">
						<h5 style="display: inline-block; margin: 0;">DIP:</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">
							{% if ol.dip is null %}
								{% for _ in 1..10 %}
									&nbsp;
								{% endfor %}
							{% else %}
								{% for _ in 1..5 %}
									&nbsp;
								{% endfor %}
								{{ ol.dip }}
								{% for _ in 1..5 %}
									&nbsp;
								{% endfor %}
							{% endif %}
						</p>
					</div>

					<div style="display: inline-block; text-align: left;">
						<h5 style="display: inline-block; margin: 0;">AO:</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">
							{% if ol.dip is null %}
								{% for _ in 1..10 %}
									&nbsp;
								{% endfor %}
							{% else %}
								{% for _ in 1..5 %}
									&nbsp;
								{% endfor %}
								{{ ol.ao }}
								{% for _ in 1..5 %}
									&nbsp;
								{% endfor %}
							{% endif %}
						</p>
					</div>

					<div style="display: inline-block; text-align: left;">
						<h5 style="display: inline-block; margin: 0;">ACO:</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">
							{% if ol.dip is null %}
								{% for _ in 1..10 %}
									&nbsp;
								{% endfor %}
							{% else %}
								{% for _ in 1..5 %}
									&nbsp;
								{% endfor %}
								{{ ol.aco }}
								{% for _ in 1..5 %}
									&nbsp;
								{% endfor %}
							{% endif %}
						</p>
					</div>
				</div>
				<div style="clear: both;"></div>

				<div style="width: 100%; display: table; text-align: justify; margin:3;">
					<div style="display: table-cell; text-align: left;">
						<h5 style="display: inline-block; margin: 0;">Marca de Armazón:</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">
							{% if p1 is null %}
								{% for _ in 1..10 %}
									&nbsp;
								{% endfor %}
							{% else %}
								{% for _ in 1..10 %}
									&nbsp;
								{% endfor %}
								{{ p1.marca }}
								{% for _ in 1..10 %}
									&nbsp;
								{% endfor %}
							{% endif %}
						</p>
					</div>

					<div style="display: table-cell; text-align: left;">
						<h5 style="display: inline-block; margin: 0;">Modelo:</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">
							{% if p1 is null %}
								{% for _ in 1..10 %}
									&nbsp;
								{% endfor %}
							{% else %}
								{% for _ in 1..10 %}
									&nbsp;
								{% endfor %}
								{{ p1.modelo }}
								{% for _ in 1..10 %}
									&nbsp;
								{% endfor %}
							{% endif %}
						</p>
					</div>

					<div style="display: table-cell; text-align: center;">
						<h5 style="display: inline-block; margin: 0;">Color:</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">
							{% if p1 is null %}
								&nbsp;Sin color defindio&nbsp;
							{% else %}
								{% for _ in 1..10 %}
									&nbsp;
								{% endfor %}
								{{ p1.codigocolor }}
								{% for _ in 1..10 %}
									&nbsp;
								{% endfor %}
							{% endif %}
						</p>
					</div>
				</div>
				<div style="clear: both;"></div>

				<div style="display: table; text-align: left; margin:3;">
					<h5 style="display: inline-block; margin: 0;">Diseño, material y tx:</h5>
					<p style="display: inline-block; margin: 0; text-decoration: underline;">

						{% for _ in 1..10 %}
							&nbsp;
						{% endfor %}

						{% if ol.disenolenteIddisenolente is not null  and ol.disenolenteIddisenolente != '' %}
							{{ ol.disenolenteIddisenolente.nombre }}&nbsp;
						{% else %}
							&nbsp;Sin diseño defindio&nbsp;
						{% endif %}

						{% if p2 is not null %}
							{{ p2.material }}&nbsp;
						{% else %}
							&nbsp;Sin material defindio&nbsp;

						{% endif %}

						{% if ol.tratamientoIdtratamiento is not null %}
							{{ol.tratamientoIdtratamiento.nombre}}
						{% else %}
							Sin tratamiento defindio
						{% endif %}

						{% for _ in 1..10 %}
							&nbsp;
						{% endfor %}

					</p>
				</div>
				<div style="clear: both;"></div>

			</td>
		</tr>
		<tr>
			<td class="tg-0lax" colspan="5">

				<div style="width: 100%; display: table; text-align: justify; margin:3;">
					<div style="display: table-cell; text-align: left;">
						<h5 style="display: inline-block; margin: 0;">REPARACIÓN O AJUSTE:</h5>

					</div>

					<div style="display: table-cell; text-align: right;">
						<div style="float: right;">

							<label for="no" style=" margin: 5px;">Si</label>
							<input type="checkbox" id="no" name="reparacion_ajuste" value="Si" style="transform: scale(2);  margin-right: 50px;">

							<label for="no" style=" margin: 5px;">No</label>
							<input type="checkbox" id="no" name="reparacion_ajuste" value="No" style="transform: scale(2);  margin-right: 100px;">

						</div>
					</div>
				</div>
				<div style="clear: both;"></div>

				<div style="width: 100%; display: table; text-align: justify; margin:3;">
					<div style="display: table-cell; text-align: left;">
						<h5 style="display: inline-block; margin: 0;">SI (Describir):</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">
							{% for _ in 1..80 %}
								&nbsp;
							{% endfor %}
						</p>
					</div>
				</div>
				<div style="clear: both;"></div>

				<div style="width: 100%; display: table; text-align: justify; margin:4;">
					<div style="display: table-cell; text-align: left;">
						<h5 style="display: inline-block; margin: 0;">Nombre de quién envía al laboratorio:</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">

							{% for _ in 1..2 %}
								&nbsp;
							{% endfor %}
							{{ ol.flujoexpedienteIdflujoexpediente.usuarioIdusuario.nombre }}
							{{ ol.flujoexpedienteIdflujoexpediente.usuarioIdusuario.apellidopaterno }}
							{{ ol.flujoexpedienteIdflujoexpediente.usuarioIdusuario.apellidomaterno }}
							{% for _ in 1..2 %}
								&nbsp;
							{% endfor %}

						</p>
					</div>

					<div style="display: table-cell; text-align: left;">
						<h5 style="display: inline-block; margin: 0;">Fecha:</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">
							{% if ol is null %}
								{% for _ in 1..10 %}
									&nbsp;
								{% endfor %}
							{% else %}
								{% for _ in 1..10 %}
									&nbsp;
								{% endfor %}
								{{ ol.fechalaboratorio }}
								{% for _ in 1..10 %}
									&nbsp;
								{% endfor %}
							{% endif %}
						</p>
					</div>

					<div style="display: table-cell; text-align: center;">
						<h5 style="display: inline-block; margin: 0;">Firma:</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">

							{% for _ in 1..10 %}
								&nbsp;
							{% endfor %}

						</p>
					</div>
				</div>
				<div style="clear: both;"></div>

				<div style="width: 100%; display: table; text-align: justify; margin:3;">
					<div style="display: table-cell; text-align: left;">
						<h5 style="display: inline-block; margin: 0;">RECEPCIÒN DEL TRABAJO DEL LABORATORIO ¿ES CORRECTO?</h5>

					</div>
					<div style="display: table-cell; text-align: right;">
						<div style="float: right;">

							<label for="no" style=" margin: 5px;">Si</label>
							<input type="checkbox" id="no" name="reparacion_ajuste" value="Si" style="transform: scale(2);  margin-right: 50px;">

							<label for="no" style=" margin: 5px;">No</label>
							<input type="checkbox" id="no" name="reparacion_ajuste" value="No" style="transform: scale(2);  margin-right: 100px;">

						</div>
					</div>
				</div>
				<div style="clear: both;"></div>

				<div style="width: 100%; display: table; text-align: justify; margin:3;">
					<div style="display: table-cell; text-align: left;">
						<h5 style="display: inline-block; margin: 0;">Observaciones (*3):</h5>
						<p style="display: inline-block; margin: 0; text-decoration: underline;">
							{% for _ in 1..76 %}
								&nbsp;
							{% endfor %}
						</p>
					</div>
				</div>
				<div style="clear: both;"></div>

			</td>
		</tr>
		<tr>
			<td colspan="5" style="position: relative;">
				<p style="font-size: 8px; letter-spacing: 0.1px;">NOTAS: (*1). ANOTAR INFORMACIÓN SOLO PARA CLIENTE CORPORATIVO. Ej. Nombre del Beneficiario, Nº DE EXPEDIENTE cliente SCT cuando lo proporcione, Nº DE TRABAJADOR cliente UAM. O lo que aplique en el convenio corporativo. (No aplica para Público en General).</p>
				<p style="font-size: 8px; letter-spacing: 0.1px;">(*2) APLICA OTROS PARA CONVENIO CORPORATIVO de/cliente UAM; UNIDAD; anotar el nombre del PLANTEL del que se trata.</p>
				<p style="font-size: 8px; letter-spacing: 0.1px;">(*3) Describa los errores o comentarios de satisfacción que hubiera al recibir el trabajo del laboratorio.</p>

				<img src="{{ qrCodeSrc }}" alt="QR Code" style="height:auto; width: 45px; position: absolute; top: 30px; right: 30px; margin: 1;">
			</td>
		</tr>
	</tbody>
</table>
