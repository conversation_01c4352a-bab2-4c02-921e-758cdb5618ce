
		<table class="w-100">
			<thead>
				<tr>
					<th class="shadow"><h3>Sección</h3></th>
					<th class="shadow"><h3>Entrypoints</h3></th>
				</tr>
			</thead>
			<tbody >
				{% for role, entrypoints in roles %}
					<tr>
						<td class="p-4">{{ role }}</td>
						<td class="row p-3 m-4" style="overflow-y:auto; overflow-x:hidden; max-height:20vh;">
							{% for entrypoint in entrypoints %}
                            <div class="col-4">
								<input type="checkbox" name="entrypoints[]" value="{{ entrypoint[0] }}" {% if entrypoint[1] %} checked {% endif %}>
								{{ entrypoint[0] }}</div>
							{% endfor %}
						</td>
					</tr>
				{% endfor %}
			</tbody>
		</table>
        <div class="row justify-content-center p-3">
		    <button type="button" class="input-toggable-value col-auto rounded-pill" onclick="getCheckedValues('{{ usrID }}')">Actualizar</button>
        </div>


	<script>
        function getCheckedValues(userid) {
            const checkboxes = document.querySelectorAll('input[name="entrypoints[]"]:not(:checked)');
            const checkedValues = Array.from(checkboxes).map(checkbox => checkbox.value);
            console.log(checkedValues);

            var url = '{{ path('update_entry_points') }}';

            $('.input-toggable-value').prop('disabled', true);

            $.ajax({
                url: url,
                dataType: "json",
                method: "POST",
                data: {
                    checkedValues: checkedValues,
                    usrID: userid
                }
            }).done(function (response) {
                console.log(response);

                $('.input-toggable-value').prop('disabled', false);

                Swal.fire({
                    title: 'Carga:' + (
                        response.exito ? 'success ' : 'error '
                    ) + response.tipoDocumento,
                    text: "mensaje: " + response.msj,
                    showCancelButton: true,
                    confirmButtonText: 'aceptar'
                })
            });
        }
	</script>
