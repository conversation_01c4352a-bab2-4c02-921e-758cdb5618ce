{# usr_permissions/index.html.twig #}
{% extends 'admin/layout.html.twig' %}

{% block title %}dashboard laboratorio
{% endblock %}

{% block content %}
	<div class="bg-white rounded-4 row p-2 shadow">
		<h1 class="text-center mt-3">Configuración de Roles</h1>
		<div class="row p-2">
			<select class="input-toggable-value p-0" onchange="myFunction(this.value)">
				{% for option in usrsresult %}
					<option class="input-toggable-value" value="{{ option.idusuario }}">{{ option.name }}</option>
				{% endfor %}
			</select>
		</div>

		<div id="entryConfContent"></div>
	</div>

{% endblock %}

{% block javascripts %}
	{{parent()}}

	<script>

		function myFunction(value) {
		console.log('Selected value:', value);

		var url = '{{ path('display_entry_points') }}';

		$('.input-toggable-value').prop('disabled', true);

		$.ajax({
		url: url,
		dataType: "html",
		method: "GET",
		data: {
		usrID: value
		}
		}).done(function (response) {

		$('.input-toggable-value').prop('disabled', false);
		$("#entryConfContent").html(response);

		});
		}
	</script>

{% endblock %}
