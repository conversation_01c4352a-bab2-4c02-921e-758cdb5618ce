{% extends 'admin/layout.html.twig' %}

{% block title %}Reporte de ventas{% endblock %}

{% block content %}
	<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.css"/>
	<div class="container-fluid">
		<div class="card">
			<div class="card-body">
				<div class="row">
					<div class="col-md-12">
						<div class="row">
							<div class="col-md-9">
								<h3 class="card-title">REPORTE DE VENTAS</h3>
							</div>
						</div>
					</div>

					<div class="col-md-12">
						<div class="card">
							<div class="card-body">
								<div class="row">
									<div class="col-md-4">
										<label for="empresa" class="lab reporte">Empresas:</label>
										<br>
										<select id="idempresa" class="form-select" aria-label="Default select example" onchange="obtenerSucursales();obtenerTiposVenta();">
											<option value="">TODAS</option>
											{% for empresa in empresas %}
												<option value="{{ empresa.idempresa }}">{{ empresa.nombre }}</option>
											{% endfor %}
										</select>
									</div>

									<div class="col-md-4">
                                        <label for="fecha_ventaCotizacion" class="lab reporte">Fecha de Ventas:</label>
										<br>
										<select id="fecha_ventaCotizacion" class="form-select" name="fecha_ventaCotizacion">
											<option value="1">Fecha de Creación</option>
											<option selected="selected" value="0">Fecha de Venta</option>
										</select>
									</div>


									<div class="row mt-3" style="display: none;">
                                            <label class="lab reporte">Fecha:</label>
										<div class="col text-center">
											<div class="input-daterange input-group rango-tiempo" id="datepicker-rango">
												<input id="fecha-inicio-rango-dia" type="text" autocomplete="off" class="form-control" name="start"/>
												<span class="input-group-addon">a</span>
												<input id="fecha-fin-rango-dia" type="text" autocomplete="off" class="form-control" name="end"/>
												<button class="btn btn-warning" onclick="resetRangoFechaDias()">
													<i class="fa fa-eraser" aria-hidden="true"></i>
												</button>
											</div>
										</div>
									</div>

								</div>

								<div class="row mt-3">
									<div class="col text-center">
										<button class="btn btn-primary reporte" onclick="revisarFiltros()">Buscar</button>
									</div>
								</div>
							</div>
						</div>
						<br>
					</div>

					<div class="col-md-12">
						<div class="card">
							<div class="card-body">
								<label class="lab reporte">Filtros:</label>
								<div class="contenedor">
                                <div id="sales-quotations"></div>
                                <div id="tipoventas"></div>
									<div id="sucursales" class="col-md-9"></div>
									
									
								</div>
							</div>
						</div>
					</div>

					<!-- Sección de Estadísticas -->
					<div class="col-md-12">
						<div id="estadisticas"></div>
					</div>
				</div>
			</div>
		</div>
	</div>

{% endblock %}

{% block javascripts %}
{{parent()}}
<script>
    jQuery(function ($) {
        $('#fecha-inicio-rango-dia').datetimepicker(
            {"pickTime":false,
                "pickDate":true,
                "minDate":"1\/1\/1900",
                "maxDate":null,
                "showToday":true,
                "language":"es_MX",
                "defaultDate":"",
                "disabledDates":[],
                "enabledDates":[],
                "icons":{"time":"fa fa-clock-o","date":"fa fa-calendar","up":"fa fa-chevron-up","down":"fa fa-chevron-down"},
                "useStrict":false,
                "sideBySide":false,
                "daysOfWeekDisabled":[],
                "collapse":true,
                "calendarWeeks":false,
                "viewMode":"days",
                "minViewMode":"days"
                ,"useCurrent":false,
                "useSeconds":false});

        $('#fecha-fin-rango-dia').datetimepicker(
            {"pickTime":false,
                "pickDate":true,
                "minDate":"1\/1\/1900",
                "maxDate":null,
                "showToday":true,
                "language":"es_MX",
                "defaultDate":"",
                "disabledDates":[],
                "enabledDates":[],
                "icons":{"time":"fa fa-clock-o","date":"fa fa-calendar","up":"fa fa-chevron-up","down":"fa fa-chevron-down"},
                "useStrict":false,
                "sideBySide":false,
                "daysOfWeekDisabled":[],
                "collapse":true,
                "calendarWeeks":false,
                "viewMode":"days",
                "minViewMode":"days"
                ,"useCurrent":false,
                "useSeconds":false});
    });

            function resetRangoFechaDias() {
                $("#fecha-inicio-rango-dia").val("");
                $("#fecha-fin-rango-dia").val("");
            }

</script>
    
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="{{ asset('js/funciones.js') }}"></script>
    <script src="{{asset('lib/jQuery-Autocomplete-master/dist/jquery.autocomplete.min.js')}}"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.js"></script>
    <script src="https://cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>


    <script>

        $(document).ready(function(){

            obtenerTiposVenta();
            saleQuotationsFilter();

        });

        

        function obtenerSucursales(){
            var idempresa=$("#idempresa").val();
            $.ajax({
                url: "{{path('almacen-obtener-sucursal')}}",
                
                data: {idempresa:idempresa},
                dataType: "html"
            }).done(function( html ) {
                $("#sucursales").html(html);
                

            }).fail(function() {
                alert( "error" );
            });
        }

        function obtenerTiposVenta(){

            var idempresa=$("#idempresa").val();

            $.ajax({
                url: "{{path('reporte-ventas-tipoventa')}}",
                
                data: {idempresa:idempresa},
                dataType: "html"
            }).done(function( html ) {
                $("#tipoventas").html(html);

            }).fail(function() {
                alert( "error" );
            });
        }

        function saleQuotationsFilter(){

            $.ajax({
                url: "{{path('reporte-ventas-sale-quotations-filter')}}",
                dataType: "html"
            }).done(function( html ) {
                $("#sales-quotations").html(html);

            }).fail(function() {
                alert( "error" );
            });
        }

        function alMenosUnoSeleccionado(checkboxes) {

            for (var i = 0; i < checkboxes.length; i++) {
                if (checkboxes[i].checked) {
                    return true;
                }
            }
            return false; 
        }

        function obtenerVentas(){

            checkboxesTipoVenta = document.getElementsByName('tipoVenta');
            checkboxesSucursal = document.getElementsByName('sucursal');
            checkboxesBodegas = document.getElementsByName('bodega');
            checkboxesCampañas = document.getElementsByName('campaña');
            checkboxsSaleQuotation = document.getElementsByName('sale-quotation');

            let fechaVentaCotizacion = $('#fecha_ventaCotizacion').val();
            var fechaInicio = document.getElementById('fecha-inicio-rango-dia').value;
            var fechaFin = document.getElementById('fecha-fin-rango-dia').value;

            tipoventas = [];
            sucursales = [];
            bodegas = [];
            campañas = [];
            saleQuotations = [];
            
            if (!alMenosUnoSeleccionado(checkboxesTipoVenta)) {
                Swal.fire('Atención', 'Por favor, selecciona al menos un tipo de venta.', 'warning');
                return; // Detiene la ejecución si no pasa la verificación
            }
            
            if (!alMenosUnoSeleccionado(checkboxesSucursal) && !alMenosUnoSeleccionado(checkboxesBodegas) && !alMenosUnoSeleccionado(checkboxesCampañas)) {
                Swal.fire('Atención', 'Por favor, selecciona al menos una sucursal, bodega o campaña.', 'warning');
                return; // Detiene la ejecución si no pasa la verificación
            }

            if (!alMenosUnoSeleccionado(checkboxsSaleQuotation)) {
                Swal.fire('Atención', 'Por favor, selecciona al menos una opción de venta o cotización.', 'warning');
                return; // Detiene la ejecución si no pasa la verificación
            }

            for(var i=0, n=checkboxesTipoVenta.length; i<n; i++) {
                
                if(checkboxesTipoVenta[i].checked)
                {
                    labelId = checkboxesTipoVenta[i].getAttribute('id');
                    labelElement = document.querySelector(`label[for="${labelId}"]`);
                    labelText = labelElement.textContent.trim();
                    tipoventas.push([checkboxesTipoVenta[i].value, labelText]);
                }
                    
            }

            for(var i=0, n=checkboxesSucursal.length;i<n;i++) {
                
                if(checkboxesSucursal[i].checked)
                {
                    sucursales.push(checkboxesSucursal[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesBodegas.length;i<n;i++) {
                
                if(checkboxesBodegas[i].checked)
                {
                    bodegas.push(checkboxesBodegas[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesCampañas.length;i<n;i++) {
                
                if(checkboxesCampañas[i].checked)
                {
                    campañas.push(checkboxesCampañas[i].value);
                }
                    
            }

            sucursalesSeleccionadas = [...sucursales, ...bodegas, ...campañas];

            for(var i=0, n=checkboxsSaleQuotation.length;i<n;i++) {
                
                if(checkboxsSaleQuotation[i].checked)
                {
                    saleQuotations.push(checkboxsSaleQuotation[i].value);
                }
                    
            }

            var enterpriseId=$("#idempresa").val();

            let dataToSend = {
                    tipoventas: tipoventas,
                    sucursales: sucursalesSeleccionadas,
                    fechaInicio: fechaInicio,
                    fechaFin: fechaFin,
                    saleQuotations: saleQuotations,
                    enterpriseId: enterpriseId,
                    fecha_ventaCotizacion: fechaVentaCotizacion
            };


            $.ajax({
                url: "{{path('obtener-ventas')}}",
                method: "POST",
                data:dataToSend,
                dataType: "html"
            }).done(function( html ) {
                $("#estadisticas").html(html);
            }).fail(function() {
                alert( "error" );
            });
        }

        function revisarFiltros()
        {
            let  idempresa = $("#idempresa").val();
            let tipoVenta = $("#tipo_venta").val();
            let fechaInicio = $("#fecha-inicio-rango-dia").val();
            let fechaFin = $("#fecha-fin-rango-dia").val();
            
            
            if (!idempresa) {
                Swal.fire({
                    title: 'Olvidaste seleccionar una empresa',
                    text: 'Por favor, seleccionala.',
                    icon: 'warning',
                    confirmButtonText: 'Ok'
                });
                return false;
            }else if(!fechaInicio && !fechaFin ){
                Swal.fire({
                    title: 'Olvidaste seleccionar el rango de fechas',
                    text: 'Por favor, seleccionala.',
                    icon: 'warning',
                    confirmButtonText: 'Ok'
                });
                return false;
            }else{
                obtenerVentas();
            }
            
        }
       
    </script>
{% endblock %}
