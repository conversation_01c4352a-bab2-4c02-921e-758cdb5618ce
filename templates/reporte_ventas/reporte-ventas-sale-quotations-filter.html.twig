<div id="sale-quotation-container" class="cont">
    <label class="filter-title">Cotización / Venta:</label>
    <div class="col-md-9 miLista" style="max-height: 300px; overflow-y: auto;">
        <fieldset>
            <!-- Switch para seleccionar todas las opciones -->
            <div class="form-check form-switch mb-3">
                <input type="checkbox" class="form-check-input" id="allSaleQuotations" onclick="toggleSaleQuotation(this)" checked>
                <label class="form-check-label fw-semibold" for="allSaleQuotations">TODAS</label>
            </div>

            <!-- Filtros individuales -->
            <div class="form-check mb-2">
                <input type="checkbox" class="form-check-input" name="sale-quotation" value="1" checked>
                <label class="form-check-label" for="sale-quotation-1">Cotización</label>
            </div>
            <div class="form-check mb-2">
                <input type="checkbox" class="form-check-input" name="sale-quotation" value="0" checked>
                <label class="form-check-label" for="sale-quotation-0">Venta</label>
            </div>
        </fieldset>
    </div>
</div>

<script>
    function toggleSaleQuotation(source) {
        // Obtener todos los checkboxes con el nombre 'sale-quotation'
        const checkboxes = document.getElementsByName('sale-quotation');
        // Establecer el estado de todos los checkboxes según el estado del 'TODAS' checkbox
        checkboxes.forEach(cb => cb.checked = source.checked);
    }
</script>
