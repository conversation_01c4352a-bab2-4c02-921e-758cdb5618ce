<div>
    <div class="card shadow-sm border-0 h-100">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <span class="fw-bold text-uppercase small">Tipos de venta</span>
        </div>

        <div class="card-body p-3" style="max-height: 300px; overflow-y: auto;">
            <fieldset id="idtipoventa">
                <!-- Switch para seleccionar todas -->
                <div class="form-check form-switch mb-3">
                    <input type="checkbox" class="form-check-input" id="todasTipoVenta" onclick="toggleTiposVenta(this)" checked>
                    <label class="form-check-label fw-semibold" for="todasTipoVenta">TODAS</label>
                </div>

                <!-- Filtros individuales de tipos de venta -->
                <div class="row row-cols-1 g-1">
                    {% for tipoventa in tiposVenta %}
                        <div class="col">
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="checkbox-tipoventa-{{ tipoventa.idtipoventa }}" name="tipoVenta" value="{{ tipoventa.idtipoventa }}" checked>
                                <label class="form-check-label" for="checkbox-tipoventa-{{ tipoventa.idtipoventa }}">
                                    {% if tipoventa.nombre == "" %}Público en General
                                    {% elseif tipoventa.nombre == "UAM" %}Prestación UAM
                                    {% else %}{{ tipoventa.nombre }}
                                    {% endif %}
                                </label>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </fieldset>
        </div>
    </div>
</div>

<script>
    function toggleTiposVenta(source) {
        // Obtener todos los checkboxes con el nombre 'tipoVenta'
        const checkboxes = document.getElementsByName('tipoVenta');
        // Establecer el estado de todos los checkboxes según el estado del 'TODAS' checkbox
        checkboxes.forEach(cb => cb.checked = source.checked);
    }
</script>
