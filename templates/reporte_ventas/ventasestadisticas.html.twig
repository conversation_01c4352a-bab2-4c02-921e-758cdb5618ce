<div class="card">
	<div class="card-body">
		<div class="row justify-content-md-center">
			<div class="col-md-12 table-responsive">
				<table class="table table-striped caption-top table-bordered table-secondary" id="tablaTipoVentaTotales">
					<caption style="font-weight: bold">Concentrado de ventas por sucursal</caption>
					<thead>

						<tr class="text-center">
							<th class="" scope="col">Sucursal</th>

							{% for tipoventa in tipoventas %}

								<th colspan="2" scope="col" class="text-center">{{tipoventa[1]}}</th>

							{% endfor %}

						</tr>

						<tr class="text-center">
							<td class="" scope="col"></td>

							{% for tipoventa in tipoventas %}

								<th scope="row">Monto total</th>
								<th scope="row"># ventas</th>

							{% endfor %}

						</tr>

					</thead>
					<tbody id="tableBodyVentas">

						{% for sucursal in totalesSucursal %}


							<tr>
								<th scope="row">{{ sucursal[0].sucursal }}</th>

								{% for tipoventa in tipoventas %}

									<td class="text-center">
										{% set break = false %}
										{% for dato in sucursal  if not break %}
											{% if dato.idtipoventa == tipoventa[0] %}
												${{ dato.cantidad|number_format(2, '.', ',') }}
												{% set break = true %}

											{% endif %}
										{% endfor %}

									</td>

									<td class="text-center">
										{% set break = false %}
										{% for dato in sucursal  if not break %}
											{% if dato.idtipoventa == tipoventa[0] %}
												{{dato.salesAmount}}
												{% set break = true %}

											{% endif %}
										{% endfor %}

									</td>


								{% endfor %}

							</tr>

						{% endfor %}

						<tr>
							<th scope="row">Total por tipo de venta</th>
							{% for tipoventa in tipoventas %}

								<td class="text-center">

									{% set break = false %}
									{% for dato in totales  if not break %}
										{% if dato.idtipoventa == tipoventa[0] %}

											${{ dato.cantidad|number_format(2, '.', ',') }}
											{% set break = true %}

										{% endif %}
									{% endfor %}

								</td>
								<td class="text-center">

									{% set break = false %}
									{% for dato in totales  if not break %}
										{% if dato.idtipoventa == tipoventa[0] %}

											{{dato.salesAmount}}
											{% set break = true %}

										{% endif %}
									{% endfor %}

								</td>

							{% endfor %}

						</tr>

						{% set totalventas = 0 %}
						{% set totalsalesAmount = 0 %}

						{% for dato in totales %}

							{% set totalventas = totalventas + dato.cantidad %}
							{% set totalsalesAmount = totalsalesAmount + dato.salesAmount %}

						{% endfor %}

						<tr>
							<th scope="row" class="text-center"><span class="d-none">Z</span>Total</th>
							{% for tipoventa in tipoventas %}

								<td class="text-center">
									{% if loop.first %}

										${{ totalventas|number_format(2, '.', ',') }}

									{% endif %}


								</td>

								<td class="text-center">
									{% if loop.first %}

										{{totalsalesAmount}}

									{% endif %}

								</td>

							{% endfor %}

						</tr>


					</tbody>
				</table>
			</div>
			<div class="card mb-10">
				<div class="card-body">
					<div id="grafica-tipo-venta-sucursal"></div>
				</div>
			</div>


			<div class="card col-6">
				<div class="card-body">

					<div id="grafica-tipo-venta"></div>
				</div>
			</div>
			<div class="col-6">
				<div class="card col-12">
					<div class="card-body">
						<div id="category-sale-graph"></div>
					</div>
				</div>
				<div class="card col-12">
					<div class="card-body">
						<div id="subcategory-sale-graph"></div>
					</div>
				</div>
			</div>
		</div>

	</div>

  <script>


    $(document).ready( function () {

    $('#tablaTipoVentaTotales').DataTable({
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excelHtml5',
                title: 'Datos de Productos',
                text: 'Exportar a Excel',
                className: 'btn btn-success'
            }
        ]
    });

        var table = document.getElementById("tablaTipoVentaTotales");
        var columnValues = [];

        for (var i = 1; i < table.rows.length; i++) {
          var cell = table.rows[i].cells[1]; // Index 1 represents the Age column

          // Get the value from the cell
          var value = cell.textContent || cell.innerText;

          // Add the value to the array
          columnValues.push(value);
        }


        let totales = Object.values({{ totales|json_encode|raw }});

        let tiposventa = totales.map(obj => obj.tipoventa);
        let cantidades = totales.map(obj => parseFloat(obj.cantidad));

        for (var i = 0; i < tiposventa.length; i++){
          if(tiposventa[i] == null) tiposventa[i] = "Público general";
        }

        var options = {
          series: cantidades,
          labels: tiposventa,
          chart: {
              width: "100%",
              type: 'donut',
          },
          dataLabels: {
              enabled: true
          },
          title: {
            text: 'Ventas por tipo de venta'
          },
          responsive: [{
              breakpoint: 480,
              options: {
                  chart: {
                      width: 200
                  },
                  legend: {
                      show: false
                  }
              }
          }],
          legend: {
              position: 'right',
              offsetY: 0,
              height: 230,
          },
          tooltip: {
              enabled: true,
              theme: "dark",
              x: {
                  show: true,
                  format: 'dd/MM/yy HH:mm'
              },
              y: {
                  formatter: function (val) {
                      // Formato de número con comas como separadores de miles
                      return " $" + val.toLocaleString();
                  },
                  title: {
                      formatter: (seriesName) => seriesName + ": ",
                  },
              },
              z: {
                  formatter: undefined,
                  title: 'Size: '
              }
          }
      };
      


        var chart = new ApexCharts(document.querySelector("#grafica-tipo-venta"), options);
        chart.render();

        let productoSucursal = Object.entries({{ totalesTipoVentaSucursal|json_encode|raw }});

        let totalSalesType = Object.entries({{ totalSalesType|json_encode|raw }});

        //let productoSucursal2 = productoSucursal[1].map(obj => obj.tipoventa);

        tiposventa1 = [];
        cantidades1 = [];
        sucursales1 = [];

        productoSucursal.forEach((tipoventa) =>{

            tiposventa1.push(tipoventa[0]);
            cantidades1.push(tipoventa[1].map(obj => parseFloat(obj.cantidad, 10)));
            sucursales1.push(tipoventa[1].map(obj => obj.sucursal));

        });

        datos = [];

        for ( var i = 0; i < tiposventa1.length; i++){

          if (tiposventa1[i] ==  '' ) {
            tiposventa1[i] = "Público general";
          }
          
        } 

        for ( var i = 0; i < cantidades1.length; i++){

            temp = {name: tiposventa1[i], data: cantidades1[i]};
            datos.push(temp);

        }

        totalSalesTypeMapped = [];

        for ( var i = 0; i < totalSalesType.length; i++){

          totalSalesTypeMapped[totalSalesType[i][0]] = totalSalesType[i][1];

        }

        var options2 = {
    series: datos,
    chart: {
        type: 'bar',
        height: 500, 
        stacked: true,
        toolbar: {
            show: true
        },
        zoom: {
            enabled: true
        },
    },
    responsive: [{
        breakpoint: 480,
        options: {
            legend: {
                position: 'bottom',
                offsetX: -10,
                offsetY: 0
            }
        }
    }],
    plotOptions: {
        bar: {
            horizontal: false,
            borderRadius: 10,
            dataLabels: {
                total: {
                    enabled: true,
                },
                enabled: true,
                textAnchor: 'start',
                formatter: function(val, opt) {
                    // Formatear el valor como número con comas y dos decimales
                    return opt.w.globals.labels[opt.dataPointIndex] + ":  $" + val.toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                },
                offsetX: 0,
            },
        },
    },
    dataLabels: {
        enabled: true,
        total: { enabled: true },
        formatter: function (val, opt) {
            // Formatear el valor como número con comas y dos decimales
            return "$ " + val.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }
    },
    title: {
        text: 'Ventas por sucursal y tipo de venta'
    },
    xaxis: {
        categories: sucursales1[0],
    },
    yaxis: {
        labels: {
            formatter: function (val) {
                // Formatear el valor como número con comas y dos decimales
                return '$' + val.toLocaleString('en-US', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });
            }
        }
    },
    legend: {
        position: 'right',
        offsetY: 40
    },
    fill: {
        opacity: 1
    }
};


        var chart = new ApexCharts(document.querySelector("#grafica-tipo-venta-sucursal"), options2);
        chart.render();

      let categoryProfit = Object.values({{ categoryProfit|json_encode|raw }});

      categoryProfitLabels = [];
      categoryProfitValues = [];

      for (i = 0; i < categoryProfit.length; i++){
        categoryProfitLabels.push(categoryProfit[i]['nombre']);
        categoryProfitValues.push(parseFloat(categoryProfit[i]['total']));
      }

      //ventas por categoria
      var options = {
        series: categoryProfitValues,
        chart: {
            width: 500,
            type: 'pie',
            toolbar: {
                show: true
            },
            animations: {
                enabled: true
            }
        },
        title: {
            text: 'Ventas por categoría'
        },
        labels: categoryProfitLabels,
        tooltip: {
            enabled: true,
            theme: 'dark',
            onDatasetHover: {
                highlightDataSeries: true,
            },
            x: {
                show: true,
                format: 'dd MMM',
                formatter: undefined,
            },
            y: {
                formatter: (value) => {
                    // Formatear el valor como número con comas y dos decimales
                    return `$${value.toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    })}`;
                }, 
                title: {
                    formatter: (seriesName) => { return seriesName; },
                },
            },
            z: {
                formatter: undefined,
                title: 'Size: '
            },
            marker: {
                show: true,
            },
            items: {
                display: 'flex',
            },
            fixed: {
                enabled: false,
                position: 'topRight',
                offsetX: 0,
                offsetY: 0,
            },
        },
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    width: 200
                },
                legend: {
                    position: 'bottom'
                }
            }
        }]
    };
    
    

      var chart = new ApexCharts(document.querySelector("#category-sale-graph"), options);
      chart.render();

      let subcategoryProfit = Object.values({{ subcategoryProfit|json_encode|raw }});

      subcategoryProfitLabels = [];
      subcategoryProfitValues = [];

      for (i = 0; i < subcategoryProfit.length; i++){
        subcategoryProfitLabels.push(subcategoryProfit[i]['nombre']);
        subcategoryProfitValues.push(parseFloat(subcategoryProfit[i]['total']));
      }

      var options = {
        series: subcategoryProfitValues,
        chart: {
            width: 500,
            type: 'pie',
            toolbar: {
                show: true
            },
            animations: {
                enabled: true
            }
        },
        title: {
            text: 'Ventas por subcategoría'
        },
        labels: subcategoryProfitLabels,
        tooltip: {
            enabled: true,
            theme: 'dark',
            onDatasetHover: {
                highlightDataSeries: true,
            },
            x: {
                show: true,
                format: 'dd MMM',
                formatter: undefined,
            },
            y: {
                formatter: (value) => {
                    // Formatear el valor como número con comas y dos decimales
                    return `$${value.toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    })}`;
                },
                title: {
                    formatter: (seriesName) => { return seriesName; },
                },
            },
            z: {
                formatter: undefined,
                title: 'Tamaño: '
            },
            marker: {
                show: true,
            },
            items: {
                display: 'flex',
            },
            fixed: {
                enabled: false,
                position: 'topRight',
                offsetX: 0,
                offsetY: 0,
            },
        },
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    width: 200
                },
                legend: {
                    position: 'bottom'
                }
            }
        }]
    };
    
        var chart = new ApexCharts(document.querySelector("#subcategory-sale-graph"), options);
        chart.render(); 
    });
</script>
