{% extends 'admin/layout.html.twig' %}
{% block title %}Calendario{% endblock %}

{% block content %}

    {% if is_granted('ROLE_SUPER_ADMIN') %}
        <a href="...">Delete</a>
    {% endif %}

    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.11/index.global.min.js"></script>
    <!-- Google API Client Library -->
    <script src="https://apis.google.com/js/api.js"></script>

    <!-- Modal for adding and removing events -->
    <div class="modal" id="eventModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Detalles del Evento <i class="fa fa-calendar" aria-hidden="true"></i></h5>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <p><strong>Nombre:</strong> <span id="eventTitle"></span></p>
                    <p><strong>Fecha de Inicio:</strong> <span id="eventStartDate"></span></p>
                    <p><strong>Descripción:</strong> <span id="eventDescription"></span></p>
                    <p><strong>Email:</strong> <a href="#" id="eventEmail"></a></p>
                    <p><strong>Teléfono:</strong> <a href="#" id="eventPhone"></a></p>

                    <input type="hidden" id="eventIdevent" name="eventIdevent" value="">
                    <button type="button" class="btn btn-primary" data-dismiss="modal" onclick="isDoneCalendar()"> Confirmar <i class="fa fa-calendar" aria-hidden="true"></i></button>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-dismiss="modal" id="addToCalendar"> Agregar a Google Calendar <i class="fa fa-calendar" aria-hidden="true"></i></button>
                </div>
            </div>
        </div>
    </div>

    <h4 id="alertCalendar"></h4>

    <div id="calendar"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var calendarEl = document.getElementById('calendar');
            var calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'timeGridWeek',
                initialDate: new Date(), // Set initial date as today
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },
                events: {{ eventsJson | raw }}, // Render events from PHP controller
                eventClick: function(info) { // Function to execute when an event is clicked
                    $('#eventTitle').text(info.event.title);
                    $('#eventStartDate').text(info.event.start.toLocaleString());
                    $('#eventEndDate').text(info.event.end ? info.event.end.toLocaleString() : 'Sin fecha de fin');
                    $('#eventEmail').text(info.event.extendedProps.email ? info.event.extendedProps.email.toLocaleString() : 'Sin email');
                    $('#eventPhone').text(info.event.extendedProps.telefono ? info.event.extendedProps.telefono.toLocaleString() : 'Sin telefono');
                    $('#eventDescription').text(info.event.extendedProps.description ? info.event.extendedProps.description : 'Sin descripción');

                    $('#eventEmail').attr('href', info.event.extendedProps.email ? 'mailto:' + info.event.extendedProps.email: "#");
                    $('#eventPhone').attr('href', info.event.extendedProps.telefono ? 'tel:' + info.event.extendedProps.telefono: "#");

                    $('#eventIdevent').val(info.event.extendedProps.idevent); // Set the value of hidden input field

                    $('#eventModal').modal('show'); // Show the modal
                }
            });

            calendar.render();
        });

        $('#addToCalendar').click(function() {
            var eventTitle = $('#eventTitle').text();
            var eventStart = $('#eventStartDate').text();
            var eventDescription = $('#eventDescription').text();
            var asistente = $('#eventEmail').text();

            //addToCalendar(eventTitle, eventStart, eventStart, eventDescription);
            insertNewEvent(eventTitle, eventDescription, eventStart, asistente);
        });

        function isDoneCalendar() {

            var eventid = $('#eventIdevent').val();

            Swal.fire({
                title: 'Estas segur@ que el Cliente atendio a la cita?',
                showDenyButton: true,
                showCancelButton: true,
                confirmButtonText: 'Si',
                denyButtonText: `No`
            }).then((result) => {
                console.log(result.value);
                if (result.value) {
                    $.ajax({
                        url: "{{ path('app_done_date') }}",
                        data: { eventid: eventid },
                        dataType: "html",
                        method: "POST",
                        data: {
                            eventid: eventid,
                        },
                    }).done(function (html) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: 'Event created successfully!',
                        });
                    }).fail(function () {
                        alert("error");
                    });
                }
            })

        }

        function formatToRFC5545(date) {
            // Get the year, month, and day
            var year = date.getFullYear();
            var month = (date.getMonth() + 1).toString().padStart(2, '0'); // Add leading zero if needed
            var day = date.getDate().toString().padStart(2, '0'); // Add leading zero if needed

            // Get the hours, minutes, and seconds
            var hours = date.getHours().toString().padStart(2, '0'); // Add leading zero if needed
            var minutes = date.getMinutes().toString().padStart(2, '0'); // Add leading zero if needed
            var seconds = date.getSeconds().toString().padStart(2, '0'); // Add leading zero if needed

            // Format the date into RFC 5545 format
            var formattedDate = `${year}-${month}-${day}T${hours}:${minutes}:00`;

            return formattedDate;
        }

        function parseDate(input) {
    // Split the input string by comma and whitespace
    const parts = input.split(/,\s+/);
    
    // Split the date part into day, month, and year
    const dateParts = parts[0].split('/');
    
    // Reformat the date into YYYY-MM-DD format
    const dateString = `${dateParts[2]}-${dateParts[1].padStart(2, '0')}-${dateParts[0].padStart(2, '0')}`;
    
    // Construct the final date string with the time part
    const finalDate = `${dateString}T${parts[1]}`;
    
    return finalDate;
}


        async function insertNewEvent(eventTitle, eventDescription, eventStart, asistente) {
            try {

                const initdate=parseDate(eventStart);
                var startDate = new Date(initdate);
                var endDate = new Date(initdate);
                endDate.setMinutes(startDate.getMinutes() + 30);

                const startDateRFC5545 = formatToRFC5545(startDate);
                const endDateRFC5545 = formatToRFC5545(endDate); 

                console.log([eventStart]);
                console.log([initdate]);
                console.log([startDate,endDate]);
                console.log([startDateRFC5545,endDateRFC5545]);

                const event = {
                    'summary': eventTitle,
                    'description': eventDescription,
                    'start': {
                        'dateTime': startDateRFC5545,
                        'timeZone': 'America/Mexico_City',
                    },
                    'end': {
                        'dateTime': endDateRFC5545,
                        'timeZone': 'America/Mexico_City',
                    },
                    'attendees': [
                        {'email': asistente}
                    ],
                    'reminders': {
                        'useDefault': false,
                        'overrides': [
                            { 'method': 'email', 'minutes': 12 * 60 },
                            { 'method': 'popup', 'minutes': 10 },
                        ],
                    },
                };

                console.log(event);

                const response = await gapi.client.calendar.events.insert({
                    'calendarId': 'primary',
                    'resource': event,
                });

                request.execute(function(event) {
                appendPre('Event created: ' + event.htmlLink);
                });

                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'Event created successfully!',
                });
            } catch (error) {
                console.error('Error creating event: ', error);
                Swal.fire({
                    title: 'Error',
                    text: error.result.error.message,
                });
            }
        }


      /* exported gapiLoaded */
      /* exported gisLoaded */
      /* exported handleAuthClick */
      /* exported handleSignoutClick */

      // TODO(developer): Set to client ID and API key from the Developer Console
       const CLIENT_ID = '920138527269-euigacmlshkhcm2493hrj2kuebooknro.apps.googleusercontent.com';
        const API_KEY = 'AIzaSyB1-aw7lXPz5acAYPnQF5XjPE66stFeAVk';

      // Discovery doc URL for APIs used by the quickstart
      const DISCOVERY_DOC = 'https://www.googleapis.com/discovery/v1/apis/calendar/v3/rest';

      // Authorization scopes required by the API; multiple scopes can be
      // included, separated by spaces.
      const SCOPES = 'https://www.googleapis.com/auth/calendar.readonly';

      let tokenClient;
      let gapiInited = false;
      let gisInited = false;

      /**
       * Callback after api.js is loaded.
       */
      function gapiLoaded() {
        gapi.load('client', initializeGapiClient);
      }

      /**
       * Callback after the API client is loaded. Loads the
       * discovery doc to initialize the API.
       */
      async function initializeGapiClient() {
        await gapi.client.init({
          apiKey: API_KEY,
          discoveryDocs: [DISCOVERY_DOC],
        });
        gapiInited = true;
        maybeEnableButtons();
      }

      /**
       * Callback after Google Identity Services are loaded.
       */
      function gisLoaded() {
        tokenClient = google.accounts.oauth2.initTokenClient({
          client_id: CLIENT_ID,
          scope: SCOPES,
          callback: '', // defined later
        });
        gisInited = true;
        maybeEnableButtons();
      }

      /**
       * Enables user interaction after all libraries are loaded.
       */
      function maybeEnableButtons() {
        if (gapiInited && gisInited) {

            var token = gapi.auth.getToken();
            console.log('gapi',token);

            tokenClient.callback = async (resp) => {
                if (resp.error !== undefined) {
                    throw (resp);
                }

                const newtoken = gapi.client.getToken();

                console.log('newtoken', newtoken);

                const encodedToken = JSON.stringify(newtoken);

                $.ajax({
                    url: "{{ path('app_set_GoogleToken') }}",
                    type: 'POST',
                    data: { token: encodedToken},
                    dataType: "json"
                }).done(function (response) {
                    console.log(response);
                }).fail(function (error) {
                    alert( error);
                });

            };

            $.ajax({
                url: "{{ path('app_get_GoogleToken') }}",
                type: 'GET',
                dataType: "json"
            }).done(function (response) {

                const decodedToken = JSON.parse(response.token);
                console.log('response',decodedToken);

                if(decodedToken)
                { 
                    gapi.auth.setToken(decodedToken);
                    tokenClient.requestAccessToken({prompt: ''});
                }
                else{
                    tokenClient.requestAccessToken({ prompt: 'consent' });
                }
            }).fail(function () {
                tokenClient.requestAccessToken({ prompt: 'consent' });
            });
        }
    }

    function isTokenExpired(decodedToken) {
    // Get the current time in seconds
    const currentTime = Math.floor(Date.now() / 1000);

    console.log(currentTime);
    console.log(decodedToken.expires_in);

    // Check if the token has an expiry time and if it's expired
    if (decodedToken.expires_in && decodedToken.expires_in > 0) {
        const expiryTime = decodedToken.expires_in;
        return expiryTime < currentTime;
    }

    // If the token doesn't have an expiry time, consider it expired
    return true;
}

    </script>

    <script async defer src="https://apis.google.com/js/api.js" onload="gapiLoaded()"></script>
    <script async defer src="https://accounts.google.com/gsi/client" onload="gisLoaded()"></script>
 
{% endblock %}
