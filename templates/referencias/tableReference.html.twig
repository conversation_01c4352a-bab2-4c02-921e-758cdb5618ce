{% if reference %}

<div class="container-fluid">
    <div class="row justify-content-center">
        <!-- Columna para la tabla -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="references">
                            <thead class="table-primary">
                                <tr class="text-center">
                                    <th scope="col">Folio</th>
                                    <th scope="col">Empresa</th>
                                    <th scope="col">Sucursal</th>
                                    <th scope="col">Fecha de Creación</th>
                                    <th scope="col">Nombre del Cliente</th>
                                    <th scope="col">Referencia de Venta</th>
                                    <th scope="col">Tipo de Venta</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ref in reference %}
                                    <tr>
                                        <td>{{ ref.folio }}</td>
                                        <td>{{ ref.Empresa }}</td>
                                        <td>{{ ref.Sucursal }}</td>
                                        <td>{{ ref.fechacreacion|date('Y-m-d') }}</td>
                                        <td>{{ ref.NombreCliente ~ ' ' ~ ref.Paterno ~ ' ' ~ ref.Materno }}</td>
                                        <td>{{ ref.name }}</td>
                                        <td>{{ ref.TipoVenta }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Columna para el gráfico de ApexCharts -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="title">
                        <h4>Referencias:</h4>
                    </div>
                    <div id="salesPieChart" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endif %}



<script>
    $(document).ready(function () {
        
        if (typeof ApexCharts !== 'undefined') {
            let table = new DataTable('#references', {
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/es-ES.json',
                },
            });

            var namesCount = {};
            $("#references tbody tr").each(function() {
                var name = $(this).find("td:nth-child(6)").text(); 
                if (namesCount[name]) {
                    namesCount[name]++;
                } else {
                    namesCount[name] = 1;
                }
            });
        
            var names = Object.keys(namesCount);
            var counts = Object.values(namesCount);
        
            var options = {
                series: counts,
                chart: {
                    type: 'pie',
                    width: '80%',
                    height: 250,
                },
                labels: names,
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            width: 100
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }]
            };
        
            var chart = new ApexCharts(document.querySelector("#salesPieChart"), options);
            chart.render();
        } else {
            console.error("ApexCharts no está definido.");
        }
    });
</script>