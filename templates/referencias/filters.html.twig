<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.x/font/bootstrap-icons.css" rel="stylesheet">
<div class="container-fluid">
	<div class="row justify-content-center">
		<div
			class="col-md-12">
			<div class="card">
				<div style="padding-left: 1em;">
					<h5>Filtros:</h5>
				</div>
				<div class="card-body">
					<div class="row g-3 align-items-center">
						<div class="col-md-5">
							<select name="empresa" id="idempresa" class="form-control" onchange="obtenerSucursales();obtenerTiposVenta();saleQuotationsFilter();">
								<option value="-1">Seleccione una empresa</option>
								{% for empresa in empresas %}
									<option value="{{ empresa.idempresa }}">{{ empresa.nombre }}</option>
								{% endfor %}
							</select>
						</div>

						<div class="col-md-2">
							<button type="button" class="btn btn-primary" onclick="getTable();">Buscar</button>
						</div>

						<div class="container" style="padding-top: 1em;">
							<div class="card">
								<div class="d-flex align-items-center justify-content-between" style="padding-left: 10px;">
									<label class="lab reporte">Rango por días:</label>
								</div>

								<div class="input-daterange input-group rango-tiempo" id="idfiltercalender">
									<input id="fecha-inicio-rango-dia" type="text" class="form-control " name="start"/>

									<span class="input-group-addon"> a </span>

									<input id="fecha-fin-rango-dia" type="text" class="input-sm form-control" name="end"/>

									<button class="btn btn-warning" onclick="resetRangoFechaDias()">
										<i class="fa fa-eraser" aria-hidden="true"></i>
									</button>

								</div>
							</div>
						</div>
						<br>
						<div class="contenedor">
							<div id="sucursales"></div>
							<div id="sales-quotations"></div>
							<div id="tipoventas"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<br>
		<div id="tablereference" class="mt-4"></div>
	</div>
</div></div>


<script>

	jQuery(function ($) {
    $('#fecha-inicio-rango-dia').datetimepicker({
        "pickTime": false,
        "pickDate": true,
        "minDate": "1/1/1900",
        "maxDate": null,
        "showToday": true,
        "language": "es_MX",
        "defaultDate": "",
        "disabledDates": [],
        "enabledDates": [],
        "icons": {
            "time": "fa fa-clock-o",
            "date": "fa fa-calendar",
            "up": "fa fa-chevron-up",
            "down": "fa fa-chevron-down"
        },
        "useStrict": false,
        "sideBySide": false,
        "daysOfWeekDisabled": [],
        "collapse": true,
        "calendarWeeks": false,
        "viewMode": "days",
        "minViewMode": "days",
        "useCurrent": false,
        "useSeconds": false
    });

    $('#fecha-fin-rango-dia').datetimepicker({
        "pickTime": false,
        "pickDate": true,
        "minDate": "1/1/1900",
        "maxDate": null,
        "showToday": true,
        "language": "es_MX",
        "defaultDate": "",
        "disabledDates": [],
        "enabledDates": [],
        "icons": {
            "time": "fa fa-clock-o",
            "date": "fa fa-calendar",
            "up": "fa fa-chevron-up",
            "down": "fa fa-chevron-down"
        },
        "useStrict": false,
        "sideBySide": false,
        "daysOfWeekDisabled": [],
        "collapse": true,
        "calendarWeeks": false,
        "viewMode": "days",
        "minViewMode": "days",
        "useCurrent": false,
        "useSeconds": false
    });
});

function resetRangoFechaDias() {
    $("#fecha-inicio-rango-dia").val("");
    $("#fecha-fin-rango-dia").val("");
}

function obtenerSucursales() {
	var idempresa = $("#idempresa").val();
		$.ajax({
		url: "{{ path('almacen-obtener-sucursal') }}",
		data: {
		idempresa: idempresa
		},
		dataType: "html"
			}).done(function (html) {
			$("#sucursales").html(html);
			}).fail(function () {
			alert("error");
			});
}

function saleQuotationsFilter() {

	$.ajax({url: "{{ path('reporte-ventas-sale-quotations-filter') }}", dataType: "html"}).done(function (html) {
	$("#sales-quotations").html(html);

	}).fail(function () {
		alert("error");
	});
}

function obtenerTiposVenta() {
	var idempresa = $("#idempresa").val();
		$.ajax({
		url: "{{ path('reporte-ventas-tipoventa') }}",
		data: {
		idempresa: idempresa
		},
		dataType: "html"
		}).done(function (html) {
		$("#tipoventas").html(html);
		}).fail(function () {
		alert("error");
		});
}

function getTable() {
var url = $("#table-reference").val();
var idempresa = $("#idempresa").val();




checkboxesTipoVenta = document.getElementsByName('tipoVenta');
checkboxesSucursal = document.getElementsByName('sucursal');
checkboxesBodegas = document.getElementsByName('bodega');
checkboxesCampañas = document.getElementsByName('campaña');
checkboxsSaleQuotation = document.getElementsByName('sale-quotation');
let fechaInicio = $('#fecha-inicio-rango-dia').val();
let fechaFin = $('#fecha-fin-rango-dia').val();

tipoventas = [];
sucursales = [];
bodegas = [];
campañas = [];
saleQuotations = [];




for (var i = 0, n = checkboxesTipoVenta.length; i < n; i++) {

if (checkboxesTipoVenta[i].checked) {
labelId = checkboxesTipoVenta[i].getAttribute('id');
labelElement = document.querySelector(`label[for="${labelId}"]`);
labelText = labelElement.textContent.trim();
tipoventas.push([
checkboxesTipoVenta[i].value,
labelText
]);
}

}

for (var i = 0, n = checkboxesSucursal.length; i < n; i++) {

if (checkboxesSucursal[i].checked) {
sucursales.push(checkboxesSucursal[i].value);
}

}

for (var i = 0, n = checkboxesBodegas.length; i < n; i++) {

if (checkboxesBodegas[i].checked) {
bodegas.push(checkboxesBodegas[i].value);
}

}

for (var i = 0, n = checkboxesCampañas.length; i < n; i++) {

if (checkboxesCampañas[i].checked) {
campañas.push(checkboxesCampañas[i].value);
}

}

sucursalesSeleccionadas = [
...sucursales,
...bodegas,
...campañas
];

for (var i = 0, n = checkboxsSaleQuotation.length; i < n; i++) {

if (checkboxsSaleQuotation[i].checked) {
saleQuotations.push(checkboxsSaleQuotation[i].value);
}

}

if (!idempresa || idempresa === "-1") {
	Swal.fire({
		icon: 'error',
		title: 'Oops...',
		text: 'Por favor, seleccione una empresa.'
	});
	return;
}


if (!fechaInicio || !fechaFin) {
	Swal.fire({
		icon: 'error',
		title: 'Oops...',
		text: 'Por favor, seleccione un rango de fechas completo.'
	});
	return;
}

if (tipoventas.length === 0 || saleQuotations.length === 0) {
	Swal.fire({
		icon: 'error',
		title: 'Filtros Requeridos',
		text: 'Por favor, seleccione al menos una opción en "Cotización / Venta" y "Tipos de venta".'
	});
	return;
}

var enterpriseId = $("#idempresa").val();

let dataToSend = {
tipoventas: tipoventas,
idempresa: idempresa,
sucursales: sucursalesSeleccionadas,
saleQuotations: saleQuotations,
enterpriseId: enterpriseId,
fechaInicio: fechaInicio,
fechaFin: fechaFin,
};



console.log(dataToSend);


$.ajax({
	url: url,
	type: 'GET',
	data: dataToSend,
	dataType: "html"
	}).done(function (html) {
		$("#tablereference").html(html);
	}).fail(function () {
	alert("error dataTable");
	});
}
</script>
