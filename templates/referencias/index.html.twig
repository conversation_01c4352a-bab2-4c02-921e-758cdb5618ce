{% extends 'admin/layout.html.twig' %}

{% block title %}| Reporte Referencias
{% endblock %}

{% block content %}
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">
    <input type="hidden" id="filters-route" value="{{ path('filters') }}">
    <input type="hidden" id="filters-route" value="{{ path('filters') }}">
    <input type="hidden" id="table-reference" value="{{ path('table-reference') }}">
    
    <div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                
                <div class="card-header bg-primary text-white">
                    <h4 class="card-title mb-0">REPORTE DE REFERENCIAS</h4>
                </div>

                
                <div class="card-body">
                    <div id="filtros">
                        <div class="container-fluid" id="filters">

                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
</div>



    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

    <script>
        $(document).ready(function(){
            tablaReference();
        });

        function tablaReference(){
            var url = $("#filters-route").val();

            $.ajax({
                url:url,
                type:'GET',
                data: {},
                dataType: "html"
            }).done(function(html){
                $("#filters").html(html);
            }).fail(function(){
                alert("error filters");
            });
        }

    </script>
{% endblock %}
