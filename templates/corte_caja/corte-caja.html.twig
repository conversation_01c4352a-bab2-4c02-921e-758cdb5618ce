{% extends 'admin/layout.html.twig' %}
{% block titleHead %}{% endblock %}
{% block title %}Nueva Venta{% endblock %}
{% block content %}

    <input id="url_corte_caja_obtener_resultados" type="hidden" value="{{ path('corte_caja_obtener_resultados') }}">
    <link id="bsdp-css" href="//cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css" rel="stylesheet">

    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header card-header-info">
                        <div class="row">
                            <div class="col-md-6">
                            
                                <h4 class="card-title">Corte de Caja </h4>
                                <strong>Usuario conectado: {{ app.user.nombre ~" "~ app.user.apellidopaterno }}</strong>
                            </div>
                            <div class="col-md-6 text-right"></div>
                        </div>
                        <p class="card-category"></p>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12 ">
                                <div class="form-group">
                                    <label for="empresa">Empresas</label><br>
                                    {% if empresas is not empty %}
                                        {% for empresa in empresas %}
                                            <div class="form-check  form-check-inline">
                                                <input class="form-check-input" type="checkbox" name="empresa" checked value="{{ empresa.idempresa }}" id="{{ empresa.idempresa }}">
                                                <label class="form-check-label" for="{{ empresa.idempresa }}">
                                                    {{ empresa.nombre }}
                                                </label>
                                            </div>
                                        {% endfor %}
                                    {% else %}
                                        <h4>Aún no tienes empresas asignadas</h4>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-7">
                                <label>Rango por Días</label>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div id="sandbox-container">
                                            <div class="input-daterange input-group rango-tiempo" id="datepicker-rango">
                                                <input id="fecha-inicio-rango-dia" type="text" autocomplete="off" class="input-sm form-control" name="start" />
                                                <span class="input-group-addon"> a </span>
                                                <input id="fecha-fin-rango-dia" type="text" autocomplete="off" class="input-sm form-control" name="end" />
                                                <button class="btn btn-warning" onclick="resetRangoFechaDias()"><i class="fa fa-eraser" aria-hidden="true"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <script>
                                jQuery(function ($) {
                                    $('#fecha-inicio-rango-dia').datetimepicker(
                                        {"pickTime":false,
                                            "pickDate":true,
                                            "minDate":"1\/1\/1900",
                                            "maxDate":null,
                                            "showToday":true,
                                            "language":"es_MX",
                                            "defaultDate":"",
                                            "disabledDates":[],
                                            "enabledDates":[],
                                            "icons":{"time":"fa fa-clock-o","date":"fa fa-calendar","up":"fa fa-chevron-up","down":"fa fa-chevron-down"},
                                            "useStrict":false,
                                            "sideBySide":false,
                                            "daysOfWeekDisabled":[],
                                            "collapse":true,
                                            "calendarWeeks":false,
                                            "viewMode":"days",
                                            "minViewMode":"days"
                                            ,"useCurrent":false,
                                            "useSeconds":false});

                                    $('#fecha-fin-rango-dia').datetimepicker(
                                        {"pickTime":false,
                                            "pickDate":true,
                                            "minDate":"1\/1\/1900",
                                            "maxDate":null,
                                            "showToday":true,
                                            "language":"es_MX",
                                            "defaultDate":"",
                                            "disabledDates":[],
                                            "enabledDates":[],
                                            "icons":{"time":"fa fa-clock-o","date":"fa fa-calendar","up":"fa fa-chevron-up","down":"fa fa-chevron-down"},
                                            "useStrict":false,
                                            "sideBySide":false,
                                            "daysOfWeekDisabled":[],
                                            "collapse":true,
                                            "calendarWeeks":false,
                                            "viewMode":"days",
                                            "minViewMode":"days"
                                            ,"useCurrent":false,
                                            "useSeconds":false});
                                });
                            </script>
                            <div class="col-md-5">
                                <label>Selecciona una Opción</label>

                                <select id="opciones-ventas" class="form-select" aria-label="Default select example">
                                    <option selected value="{{ app.user.idusuario }}">Mis Ventas</option>
                                    {% if is_granted('ROLE_ADMIN') %}
                                        <option value="resultadoscomogerente">Resultados como Gerente</option>
                                    {% endif %}
                                    {% if is_granted('ROLE_SUPER_ADMIN') %}
                                        <option value="resultadoscomoadministrador">Todas Sucursales</option>
                                    {% endif %}
                                </select>

                            </div>
                            <div class="col-md-12 text-center mt-4">
                                <button class="btn btn-success mt-2" onclick="obtenerCorteCaja()">
                                    Generar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 mt-2" id="resultados"></div>
        </div><!--fin de row-->
    </div>
{% endblock %}
{% block javascripts %}
    {{parent()}}
    <script>
        function imprim1(id){
            var mywindow = window.open('', 'PRINT', 'height=400,width=600');
            mywindow.document.write('<html><head>');
            mywindow.document.write('<style>.detalles-ventas {visibility: hidden !important;display: none !important;;}.tabla{width:100%;border-collapse:collapse;margin:16px 0 16px 0;}.tabla th{border:1px solid #ddd;padding:4px;background-color:#d4eefd;text-align:left;font-size:15px;}.tabla td{border:1px solid #ddd;text-align:left;padding:6px;}</style>');
            mywindow.document.write('</head><body >');
            mywindow.document.write(document.getElementById(id).innerHTML);
            mywindow.document.write('</body></html>');
            mywindow.document.close(); // necesario para IE >= 10
            mywindow.focus(); // necesario para IE >= 10
            mywindow.print();
            mywindow.close();
            return true;
        }
    </script>
    <script type="text/javascript">
        $(function () {
          //  $('#datetimepicker1').datetimepicker();
         /*   $('#sandbox-container .rango-tiempo').datepicker({
                uiLibrary: 'bootstrap4',
                locale: 'es-es',
                format: "dd/mm/yyyy",

                todayBtn: true,
                todayHighlight: true,
                clearBtn: true,
                language: "es",
                autoclose: true
            }).on("changeDate", function(e,ev) {

            });*/
        });
    </script>
    <script>
        function obtenerCorteCaja(){
            var url=$("#url_corte_caja_obtener_resultados").val();
            let fechaInicioRangoDia=$("#fecha-inicio-rango-dia").val();
            let fechaFinRangoDia=$("#fecha-fin-rango-dia").val();
            let opcionesVentas=$("#opciones-ventas").val();

            checkboxesEmpresas = document.getElementsByName('empresa');

            empresas = [];

            for(var i=0, n=checkboxesEmpresas.length;i<n;i++) {
                
                if(checkboxesEmpresas[i].checked)
                {
                    empresas.push(checkboxesEmpresas[i].value);
                }
                    
            }

            $.ajax({
                method: "POST",
                data:{opcionesVentas:opcionesVentas,fechaInicioRangoDia:fechaInicioRangoDia,fechaFinRangoDia:fechaFinRangoDia,empresas:empresas},
                url: url,
                beforeSend: function( xhr ) {
                   let timerInterval
                    Swal.fire({
                        title: 'Cargando...',
                        imageUrl: '/img/log.gif',
                        timerProgressBar: true,
                        showConfirmButton: false,
                        didOpen: () => {
                            
                            const b = Swal.getHtmlContainer().querySelector('b')
                            timerInterval = setInterval(() => {
                            b.textContent = Swal.getTimerLeft()
                            }, 100)
                        }
                        }).then((result) => {
                        /* Read more about handling dismissals below */
                        if (result.dismiss === Swal.DismissReason.timer) {
                            console.log('I was closed by the timer')
                        }
                    })
                }
            }).done(function( response ) {
                Swal.close()
                $("#resultados").html(response);
            });
        }

        function resetRangoFechaDias() {
            $("#fecha-inicio-rango-dia").val("");
            $("#fecha-fin-rango-dia").val("");
        }
    </script>
{% endblock %}
