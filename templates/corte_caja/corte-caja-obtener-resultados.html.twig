<!--globales-->

<div class="card text-dark bg-light border-info mb-3 my-4"  id="imprimir-resultado-global">
    <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
        <div class="bg-gradient-info shadow-primary border-radius-lg pt-4 pb-3">
            <h3 class=" text-capitalize ps-3">Resultados Globales</h3>
        </div>
    </div>
    <div class="card-body px-0 pb-2"  >
        <div class="row">
            <div  class="col-md-12">
                <div class="row"><div class="col-md-6 text-center">
    <strong>Corte de Caja</strong>
    Del {{ fechaInicioRangoDia | date("d/m/Y") }} al {{ fechaFinRangoDia | date("d/m/Y") }}
    <br><br>
<!--globales-->
    {% if tiposVentaDataGlobalCotizacion is not empty %}
        <table class="table table-striped">
            <tr>
                <td class="text-center">Tipo de Cotización</td>
                <td class="text-center">Número de Cotizaciones</td>
            </tr>
            {% for keyTv, tv in tiposVentaDataGlobalCotizacion['cotizaciones'] %}
                <tr>
                    <td>{{ keyTv }}</td>
                    <td class="text-center">{{ tv }}</td>
                </tr>
            {% endfor %}
        </table>
        <br>
    {% endif %}
</div>

<div class="col-md-6 text-center" id="Tipodeventa">
    <!--Del {{ fechaInicioRangoDia | date("d/m/Y") }} al {{ fechaFinRangoDia | date("d/m/Y") }}-->
    <br><br>

    {% if tiposVentaDataGlobal is not empty %}
        <table class="table table-striped">
            <tr>
                <td class="text-center">Tipo de Venta</td>
                <td class="text-center">Número de Ventas</td>
            </tr>
            {% for keyTv, tv in tiposVentaDataGlobal['tiposVenta'] %}
                <tr>
                    <td>{{ keyTv }}</td>
                    <td class="text-center">{{ tv }}</td>
                </tr>
            {% endfor %}
        </table>
        <br>
    {% endif %}
</div>

{% if conveniosDataGlobal is defined and conveniosDataGlobal is not empty %}
    <div class="table-responsive p-0" id="tipo-de-pago">
        <table class="table table-striped table-hover align-items-center mb-0" id="table">
            <thead>
                <tr class="text-center">
                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Tipo de Pago</th>
                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Monto</th>
                    <th class="text-secondary opacity-7 detalles-ventas"></th>
                </tr>
            </thead>
            <tbody>
                {% set totalConvenio = 0 %}
                {% for convenio in conveniosDataGlobal %}
                    {% set totalConvenio = convenio.totalPagos + totalConvenio %}
                    <tr>
                        <td class="text-center">
                            <strong class="mb-0 text-sm">
                                {% if convenio.nombre == "UAM" %}
                                    Prestación UAM
                                {% elseif convenio.nombre == "" %}
                                    Público General
                                {% else %}
                                    {{ convenio.nombre }}
                                {% endif %}
                            </strong>
                        </td>
                        <td class="align-middle text-center text-sm">
                            <p class="text-xs font-weight-bold mb-0">${{ convenio.totalPagos | number_format(2, '.', ',') }}</p>
                        </td>
                        <td class="align-middle text-center detalles-ventas">
                            <a href="javascript:;" class="text-secondary font-weight-bold text-xs btn btn-info detalles-ventas" data-bs-toggle="mod" data-bs-target="#modal-convenio-{{ loop.index }}">
                                Detalle
                            </a>
                        </td>
                    </tr>
                {% endfor %}
                <tr class="text-center negritas">
                    <td>Total</td>
                    <td>${{ totalConvenio | number_format(2, '.', ',') }}</td>
                    <td class="detalles-ventas"></td>
                </tr>
            </tbody>
        </table>
        {% for convenio in conveniosDataGlobal %}
            <div class="mod detalles-ventas" id="modal-convenio-{{ loop.index }}" tabindex="-1" aria-labelledby="modal-convenio-{{ loop.index }}" aria-hidden="true">
                <div class="mod-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="exampleModalLabel">Detalle de Pagos</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="mod" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>FOLIO</th>
                                            <th>CONVENIO</th>
                                            <th>TIPO DE PAGO</th>
                                            <th>FECHA DE PAGO</th>
                                            <th>FECHA DE VENTA</th>
                                            <th>FACTURA</th>
                                            <th>MONTO DEL PAGO</th>
                                            <th>MONTO DE LA VENTA</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for venta in convenio.ventas %}
                                            <tr>
                                                <td>{{ venta.folio }}</td>
                                                <td>
                                                    {% if venta.convenio == "UAM" %}
                                                        Prestación UAM
                                                    {% elseif venta.convenio == "" %}
                                                        Público General
                                                    {% else %}
                                                        {{ venta.convenio }}
                                                    {% endif %}
                                                </td>
                                                <td>{{ venta.tipopago }}</td>
                                                <td>{{ venta.fechaPago | date("d/m/Y g:ia ") }}</td>
                                                <td>{{ venta.fechaVenta | date("d/m/Y g:ia ") }}</td>
                                                <td>
                                                    {% if venta.pidiofactura == "1" %}
                                                        Si
                                                    {% else %}
                                                        No
                                                    {% endif %}
                                                </td>
                                                <td>${{ venta.monto | number_format(2, '.', ',') }}</td>
                                                <td>${{ (venta.montoVenta + venta.iva) | number_format(2, '.', ',') }}</td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="mod">Cerrar</button>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
        <div class="table-responsive p-0">
            <table class="table table-striped table-hover align-items-center mb-0 p-0">
                <thead>
                    <tr>
                        <td colspan="2">
                            <br>
                            <h4 class="text-center">Pagos con Factura</h4>
                        </td>
                    </tr>
                    <tr class="text-center">
                        <th>Concepto</th>
                        <th>Cantidad</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="text-center">
                        <td>Pagos Con Factura</td>
                        <td>${{ globalMontoVentaConFactura | number_format(2, '.', ',') }}</td>
                    </tr>
                    <tr class="text-center">
                        <td>Pagos Sin Factura</td>
                        <td>${{ globalMontoVentaSinFactura | number_format(2, '.', ',') }}</td>
                    </tr>
                    <tr class="text-center negritas">
                        <th>TOTAL</th>
                        <td>${{ (globalMontoVentaConFactura + globalMontoVentaSinFactura) | number_format(2, '.', ',') }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
{% else %}
    <div class="row">
        <div class="col-md-12 text-center">
            <h4>Sin Resultados</h4>
        </div>
    </div>
{% endif %}
</div>

        <div class="row detalles-ventas">
            <div class="col-md-12 text-center">
                <button class="btn btn-default" onclick="javascript:imprim1('imprimir-resultado-global');">
                    <img src=" {{asset('img/icon-print.png')}}" alt="" width="35px">Imprimir
                </button>
            </div>
        </div>
    </div>
</div>



    <div class="row">
        <hr>
        <!--sucursales-->
        {% set indexAux=0 %}
        {% for sucursal in sucursalesData %}

        {% if   sucursal.tipopagoData is defined  and  (sucursal.tipopagoData is not empty or sucursal.autorizacionesUAM > 0) %}

        {% set indexAux=indexAux +1 %}
        <div class="card text-dark bg-light border-info mb-3 my-4" id="imprimir-resultado-global-{{indexAux}}">
            <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
                <div class="bg-gradient-info shadow-primary border-radius-lg pt-4 pb-3">
                    <h3 class=" text-capitalize ps-3">Sucursal {{ sucursal.nombre  }}</h3>
                </div>
                <strong>Resultados </strong>
            </div>
            <div class="card-body px-0 pb-2"  >
                <div class="row">
                    <div class="col-md-6  text-center">
                        <table class="table table-striped">
                        <tr>
                            <td class="text-center">
                                <strong>Tipo de Cotización</strong>
                            </td>
                            <td class="text-center">
                                <strong>Número de Cotizaciones</strong>
                            </td>
                        </tr>
                        {% for cotizacion in sucursal['cotizaciones'] %}
                            <tr>
                                <td>{{ cotizacion.tipoVenta }}</td>
                                <td class="text-center">{{ cotizacion.numeroVentas }}</td>
                            </tr>
                        {% endfor %}
                    </table>
            <hr>
                    </div>
                    <div class="col-md-6 ">
                        <table class="table table-striped">
                            <tr>
                                <td class="text-center">
                                    <strong>Tipo de Venta</strong>
                                </td>
                                <td class="text-center">
                                    <strong>Número de Ventas</strong>
                                </td>
                            </tr>
                            {% for keyTv,tv in  sucursal['tiposVenta'] %}

                                <tr>
                                    <td>{{ tv.tipoVenta }}</td>
                                    <td class="text-center">{{ tv.numeroVentas }}</td>

                                </tr>

                            {% endfor %}
                        </table>
                            <hr>
                        <table class="table table-striped table-hover text-center">
                            <tr>
                                <td>
                                <strong>Número de Pagos</strong>
                                </td>
                                <th>
                                    {{ (sucursal.sinfactura | length) + (sucursal.confactura  | length) }}
                                </th>
                            </tr>

                        </table>

                    </div>


                    <div class="col-md-12">


                        {% if   sucursal.tipopagoData is defined and  sucursal.tipopagoData is not empty %}

                            <div class="table-responsive p-0">

                                <table class="table  table-striped table-hover align-items-center mb-0">

                                    <thead>
                                    <tr class="text-center">

                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Tipo Pago</th>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Monto</th>



                                        <th class="text-secondary opacity-7 detalles-ventas"></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {% set totalConvenio =  0 %}




                                    {% for convenio in sucursal.tipopagoData %}
                                        {% set totalConvenio =  convenio.totalPagosMonto + totalConvenio %}


                                        <tr>

                                            <td class=" ">
                                                <p class="text-center font-weight-bold mb-0 ">{{convenio.nombre}}</p>

                                            </td>
                                            <td class="align-middle text-center text-sm">
                                                <p class="text-xs font-weight-bold mb-0">${{ convenio.totalPagosMonto | number_format(2, '.', ',') }}</p>
                                            </td>
                                            <!-- BOTOOON DE DETALLE -->
                                            <td class="align-middle text-center detalles-ventas">
                                                <a href="javascript:;" class="text-secondary font-weight-bold text-xs btn btn-info"  data-bs-toggle="mod" data-bs-target="#modal-convenio-{{ indexAux }}-{{ loop.index }}" >
                                                    Detalle
                                                </a>
                                                <!-- Modal -->
                                                <div class="mod" id="modal-convenio-{{ indexAux }}-{{ loop.index }}" tabindex="-1" aria-labelledby="modal-convenio-{{ indexAux }}-{{ loop.index }}" aria-hidden="true">
                                                    <div class="mod-dialog  modal-xl">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="exampleModalLabel">Detalle de Pagos</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="mod" aria-label="Close"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <div class="table-responsive">
                                                                    <table class="table  table-striped table-hover">
                                                                        <thead>
                                                                        <tr>
                                                                            <th>FOLIO</th>
                                                                            <th>CONVENIO</th>
                                                                            <th>TIPO DE PAGO</th>
                                                                            <th>FECHA DE PAGO</th>
                                                                            <th>FECHA DE VENTA</th>
                                                                            <th>FACTURA</th>
                                                                            <th>MONTO DEL PAGO</th>
                                                                            <th>MONTO DE LA VENTA</th>


                                                                        </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                        {% for venta in convenio.pagos %}
                                                                            <tr>
                                                                                <td>{{ venta.folio }}</td>
                                                                                <td>{% if venta.convenio =="UAM" %}Prestación UAM{% elseif venta.convenio ==""  %}Público General{% else %}{{ venta.convenio }}{% endif %}</td>
                                                                                <td>{{ venta.tipopago }}</td>
                                                                                <td>{{ venta.fechaPago | date("d/m/Y g:ia ") }}</td>
                                                                                <td>{{ venta.fechaVenta | date("d/m/Y g:ia ") }}</td>
                                                                                <td>{% if venta.pidiofactura =="1" %}Si{% else %}No{% endif %}</td>
                                                                                <td>  ${{ venta.monto  | number_format(2, '.', ',')}}</td>
                                                                                <td>  ${{ (venta.montoVenta +venta.iva)  | number_format(2, '.', ',')}}</td>


                                                                            </tr>
                                                                        {% endfor %}
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="mod">Cerrar</button>

                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>

                                    {% endfor %}
                                    <tr class="text-center">
                                        <td>
                                            Total
                                        </td>
                                        <td>
                                            ${{  totalConvenio | number_format(2, '.', ',') }}
                                        </td>
                                        <td class="detalles-ventas">

                                        </td>
                                    </tr>

                                    </tbody>
                                </table>

                                <hr>

                            </div>
                            <div class="row">
                                <div clas="col-md-12 ">
                                    <br>
                                    <h4 class="text-center">Pagos con Factura</h4>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table  table-striped table-hover align-items-center mb-0">
                                    <thead>
                                    <tr class="text-center">
                                        <th>Concepto</th>
                                        <th>Cantidad</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr class="text-center">
                                        <td>
                                            Pagos Con Factura
                                        </td>
                                        <td>
                                            ${{sucursal.montoVentaConFactura | number_format(2, '.', ',')}}
                                        </td>
                                    </tr>
                                    <tr class="text-center">
                                        <td>
                                            Pagos Sin Factura
                                        </td>
                                        <td>
                                            ${{sucursal.montoVentaSinFactura | number_format(2, '.', ',')}}
                                        </td>
                                    </tr>
                                    <tr class="text-center">
                                        <th>
                                            TOTAL
                                        </th>
                                        <td>
                                            ${{  (sucursal.montoVentaSinFactura+ sucursal.montoVentaConFactura) | number_format(2, '.', ',') }}
                                        </td>
                                    </tr>

                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="row">
                                <div class="col-md-12 text-center">
                                    <h4>Sin Resultados</h4>
                                </div>
                            </div>

                        {% endif  %}
                    </div>
                    <!---->

                </div>
                <div class="row detalles-ventas">
                <br>
                    <div class="col-md-12 text-center">
                        <button class="btn btn-default" onclick="javascript:imprim1('imprimir-resultado-global-{{indexAux}}');">
                            <img src=" {{asset('img/icon-print.png')}}" alt="" width="35px">Imprimir
                        </button>
                    </div>
                </div>
            </div>
        </div>

    {% endif %}

{% endfor  %}
