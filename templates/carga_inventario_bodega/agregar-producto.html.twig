<tr id="producto-{{ id }}">
	<td>
		<div class="row align-items-center text-center">
			<div class="col-auto d-flex">
				<label class="me-2">{{ value.CLAVE }}&nbsp;{{ value.MODELO }}&nbsp;{{ value.UPC }}:
				</label>
				{% if dictionary == 'Carga-Costo' %}
					{% if oldObj is not empty %}

						<label>&nbsp;Precio: Actual:{{ oldObj.precio ?? '0' }}$  Nuevo:{{ value.PRECIO }}$</label>

						<label>&nbsp;&nbsp;Costo: Actual:{{ oldObj.costo ?? '0' }}$ Nuevo:{{ value.COSTO }}$</label>

					{% else %}
						<label style="color:red;">
							Aún no exsiste como PRODUCTO! no se puede dar COSTO y PRECIO
						</label>
					{% endif %}

				{% endif %}
				{% if dictionary == 'Carga-Producto' %}

					<label>&nbsp;{{ oldObj == null ? 'Nuevo' : 'Actualización' }}</label>

				{% endif %}
				{% if dictionary == 'Carga-Inv' %}

					{% if oldObj is not empty %}

						{% if ObjStock is not empty %}
							<label style="color:orange;">
								Actualización Costo: {{value.COSTO == ''? ObjStock.costo : value.COSTO}} Precio: {{value.PRECIO == ''? ObjStock.precio : value.PRECIO}}
							</label>
						{% else %}
							<label>&nbsp;
								{{ value.ALMACEN }}: &nbsp; Cantidad anterior:{{oldAmount}}
								&nbsp; Cantidad a sumar:{{ value.CANTIDAD ?? 'Sin Definir' }}
							</label>
						{% endif %}

					{% else %}
						<label style="color:red;">
							Aún no exsiste como PRODUCTO! no se puede cargar STOCK</label>
					{% endif %}
				{% endif %}
			</div>
		</div>

		{% if dictionary == 'Carga-Producto' or oldObj is not empty %}
			<input type="text" class="producto-encontrado" value="1" hidden>
		{% endif %}
	</td>
	<td>
		<button class="btn btn-warning btn-sm" onclick="quitarProducto('{{ id }}', '{{ value|json_encode }}')">Quitar</button>
	</td>
</tr>
