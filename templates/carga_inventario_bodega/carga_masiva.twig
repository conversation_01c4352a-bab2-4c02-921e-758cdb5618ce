{% extends 'admin/layout.html.twig' %}
{% block titleHead %}{% endblock %}
{% block title %}Transpaso Productos
{% endblock %}
{% block content %}
<link rel="stylesheet" href="{{ asset('/lib/jQuery-Autocomplete-master/content/styles.css') }}">
<link rel="stylesheet" href="{{ asset('css/traspaso.css') }}">
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.css"/>
<input id="url-traspaso-agregar-producto" type="hidden" value="{{ path('traspaso-agregar-producto') }}">
<input id="url-traspaso-traspasar-productos" type="hidden" value="{{ path('traspaso-traspasar-productos') }}">
<input id="url-traspaso-add-products" type="hidden" value="{{ path('traspaso-add-products') }}">
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header card-header-info">
                    <div class="row">
                        <div class="col-md-12">
                            <h4 class="card-title">CARGA DE INVENTARIO</h4>
                        </div>

                        <div class="col-md-12">
                            <div>
                                <div class="row my-3">
                                    <div class="col">
                                        <label for="codigoBarras">Cargar productos</label>
                                        <a target="_blank"
                                           href="https://docs.google.com/spreadsheets/d/1h4f7QZKgjeGCMwBu9RrUvjPfS9hFRgeEUQ9jv9v50nk/edit?usp=sharing"
                                           class="btn btn-info">
                                            Plantilla <i class="fa fa-file" aria-hidden="true"></i>
                                        </a>
                                        <button class="btn btn-warning "
                                                onclick="messageshow('Se indicara si el producto es nuevo o una actualización.', 'Cargar productos')">
                                            <i class='fa-solid fa-triangle-exclamation'></i>
                                        </button>
                                    </div>
                                    <div class="col">
                                        <label for="codigoBarras">Cargar Costos</label>
                                        <a target="_blank"
                                           href="https://docs.google.com/spreadsheets/d/1bbk56yQFxQaT9ZFDkUVGzUAjR-oXEbLSzakWV3MgxX4/edit?usp=sharing"
                                           class="btn btn-info">
                                            Plantilla <i class="fa fa-file" aria-hidden="true"></i>
                                        </a>
                                        <button class="btn btn-warning "
                                                onclick="messageshow('Para cambiar los precios de los productos puede llenar el SKU, CLAVE, UPC, MODELO y en ese mismo orden se busca para actualizarse, no es necesario llenar los tres campos \n los campos relacionados con el producto son: COSTO, PRECIO, PRECIO SUBDISTRIBUIDOR, PRECIO DISTRIBUIDOR, PRECIO ESPECIAL', 'Cargar Costos')">
                                            <i class='fa-solid fa-triangle-exclamation'></i>
                                        </button>
                                    </div>
                                    <div class="col">
                                        <label for="codigoBarras">Cargar Inventario</label>
                                        <a target="_blank"
                                           href="https://docs.google.com/spreadsheets/d/1QJIKrBE9dPCwK2JO2ktHADOvyqKAoPcrRwCFGyfov40/edit?usp=sharing"
                                           class="btn btn-info">
                                            Plantilla <i class="fa fa-file" aria-hidden="true"></i>
                                        </a>
                                        <button class="btn btn-warning "
                                                onclick="messageshow('Actualizar Stock: para actualizar un stock se debe poner el código SKU y el campo de COSTO y/o PRECIO', 'Cargar Inventario')">
                                            <i class='fa-solid fa-triangle-exclamation'></i>
                                        </button>
                                    </div>
                                </div>

                                <div>
                                    <button id="picker_button" class="btn btn-success d-none" onclick="createPicker()">
                                        Buscar archivo
                                    </button>
                                    <button id="authorize_button" class="btn btn-primary d-none"
                                            onclick="handleAuthClick()">Iniciar sesión
                                    </button>
                                    <button id="signout_button" class="btn btn-danger d-none"
                                            onclick="handleSignoutClick()">Cerrar sesión
                                    </button>


                                </div>
                            </div>
                            <p id="msj" class="col-12 mb-0 text-capitalize text-center px-3">...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <br>
        <div class="row mt-3">
            <div class="col-md-6">
                <div class="card">
                    <table class="table table-bordered table-borderless">
                        <thead>
                        <tr>
                            <th>
                                <p>Productos Encontrados</p>
                            </th>
                            <th>
                                <p id="doc-type">
                                    []
                                </p>
                            </th>
                        </tr>
                        </thead>
                        <tbody id="contenedor-productos"></tbody>
                    </table>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="row">
                        <div class="col-sm-6 mx-auto">
                            <div class="card ">
                                <div class="text-center pt-3 pb-3 ">
                                    <p class="text-sm mb-0 text-capitalize">instancias Validas</p>
                                    <h1 class="mb-0" id="cantidad-productos"></h1>
                                </div>
                                <button id="error_button" class="btn btn-warning d-none" data-toggle="modal"
                                        data-target="#modal-error-detail" onclick="makeDataTable()"></button>
                            </div>
                        </div>


                        <div class="col-md-12 text-center mt-3">

                            <div class="row justify-content-center p-5" id="contenedor-extracontent"></div>
                            <div class="d-flex justify-content-center">

                                <button id="btnTraspasar" class="btn btn-lg btn-info" onclick="procesarYDeshabilitar()">
                                    Cargar
                                </button>
                            </div>
                        </div>

                        <div class="col-md-12 text-center">
                            <img id="loader" src="{{ asset('img/log.gif') }}" alt="" width="150px" class="d-none">
                        </div>
                        <div class="col-md-12 text-center" id="contenedor-respuesta"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal" id="modal-error-detail" tabindex="-1" aria-labelledby="" aria-hidden="true">
        <div class="mod-dialog modal-dialog-scrollable">
            <div class="modal-content" style="border-radius:10px">
                <div class="modal-header bg-primary">
                    <h1 class="modal-title fs-5">Detalle de errores</h1>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <table class="table" id="error-table">
                        <thead>
                        <th>
                            Fila (0..N)
                        </th>
                        <th>
                            Producto
                        </th>
                        <th>Mensaje</th>
                        </thead>
                        <tbody id="error-table-body"></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    {% endblock %}
    {% block javascripts %}
        {{ parent() }}

        <script>
            function messageshow(msj, title) {
                Swal.fire({title: title, text: msj, icon: "info"});
            }
        </script>

        <script>

            const CLIENT_ID = '920138527269-euigacmlshkhcm2493hrj2kuebooknro.apps.googleusercontent.com';
            const API_KEY = 'AIzaSyB1-aw7lXPz5acAYPnQF5XjPE66stFeAVk';
            const DISCOVERY_DOC = 'https://sheets.googleapis.com/$discovery/rest?version=v4';
            const SCOPES = 'https://www.googleapis.com/auth/drive.readonly https://www.googleapis.com/auth/spreadsheets.readonly';
            const APP_ID = 'pv360-416621';

            let tokenClient;
            let accessToken = null;
            let pickerInited = false;
            let gisInited = false;
            let gapiInited = false;

            var checkProducts = new Set();
            var cols = {};

            var errorCount = 0
            var dictKey = "";


            function addProducts(formatedCodes) {
                var btn = document.getElementById('btnTraspasar');
                btn.disabled = true;

                $("#error-table").dataTable().fnDestroy();
                $("#error-table-body").html('');
                $("#contenedor-extracontent").html('');


                errorCount = 0
                $("#error_button").addClass("d-none")

                var url = '{{ path('carga_masiva_traspaso_add_products') }}';

                $.ajax({
                    url: url,
                    dataType: "json",
                    method: "POST",
                    data: {
                        formatedCodes: formatedCodes
                    },
                    beforeSend: function (xhr) {
                        $("#loader").removeClass("d-none");
                    }
                }).done(function (response) {

                    btn.disabled = false;

                    Swal.fire({
                        icon: (response.exito ? "success" : "error"),
                        title: 'Lectura de archivo: ' + (
                            response.exito ? 'Exitosa' : 'Invalida'
                        ),
                        text: response.msj + ' Carga :Pendiente'
                    });

                    $("#msj").text(response.msj);

                    if (response.exito) {
                        $("#contenedor-productos").html(response.html);
                        $("#contenedor-extracontent").html(response.extrahtml);
                        $("#codigoBarras").val("");
                        $("#codigoBarras").focus("");
                        $("#doc-type").text("[" + response.tipoDoc + "]");

                        contarProductos();

                        checkProducts = new Set();
                        cols = response.cols;
                        dictKey = response.dictKey;

                        response.codesAdded.forEach((code) => {
                            var parsedData = JSON.parse(code);
                            checkProducts.add(code); // You can set any value, true is often used as a placeholder
                        });

                    }
                    displayErrorsTable(response.errors)
                    $("#loader").addClass("d-none");
                });


            }

            function displayErrorsTable(errors) {
                let html = "";
                errorCount += errors.length
                if (errorCount > 0) {
                    $("#error_button").removeClass("d-none")

                }
                errors.forEach((product) => {
                    html += "<tr><td>" + product.index + "</td><td>" + product.code + "</td><td>" + product.msg + "</td></tr>"
                })
                $("#error_button").html("<i class='fa-solid fa-triangle-exclamation me-2'></i> invalidas: " + errorCount);
                $("#error-table-body").append(html);
            }

            function quitarProducto(id, codigo) {
                $("#producto-" + id).remove();
                var parsedData = JSON.parse(codigo);

                checkProducts.delete(codigo);
                contarProductos();
            }

            function makeDataTable() {
                if (!$.fn.DataTable.isDataTable('#error-table')) {
                    $('#error-table').DataTable({
                        language: {
                            url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                            lengthMenu: 'Mostrar _MENU_ códigos por página'
                        },
                        dom: 'Bfrtip',
                        buttons: [
                            {
                                className: 'btn-primary btn',
                                filename: 'codigos_errores',
                                extend: 'excelHtml5',
                                text: 'Exportar excel'
                            }
                        ]
                    });
                }
            }

            function contarProductos() {
                cantidadProductos = 0;
                $(".producto-encontrado").each(function () {
                    cantidadProductos += 1;
                });

                $("#cantidad-productos").text(cantidadProductos);

            }

            function procesarYDeshabilitar() {

                procesarTodo();

                var btn = document.getElementById('btnTraspasar');
                btn.disabled = true;
                /*setTimeout(function () {
                                btn.disabled = false;
                            }, 5000);*/
            }


            function procesarTodo() {

                var checkProductsArray = Array.from(checkProducts).map(function (jsonString) {
                    return JSON.parse(jsonString);
                });

                console.log(checkProductsArray);


                if (checkProductsArray.length > 0) {

                    var url = '{{ path('carga_masiva_upload_drive') }}';
                    var btn = document.getElementById('btnTraspasar');


                    Swal.fire({
                        icon: "info",
                        title: 'Estas segur@ que deseas cargar estos productos?',
                        showDenyButton: true,
                        showCancelButton: true,
                        confirmButtonText: 'Si',
                        denyButtonText: `No`
                    }).then((result) => {

                        if (result.value) {
                            var btn = document.getElementById('btnTraspasar');
                            btn.disabled = true;

                            const elements = document.querySelectorAll('.extrainput');

                            // Initialize an empty object to store the extrainfo
                            const extrainfo = {};

                            // Iterate over each element
                            elements.forEach(element => { // Get the name and value of the element
                                const name = element.getAttribute('name');
                                const value = element.value;

                                // Update the extrainfo object
                                extrainfo[name] = value;
                            });

                            $.ajax({
                                url: url,
                                dataType: "json",
                                method: "POST",
                                data: {
                                    checkProducts: checkProductsArray,
                                    cols: cols,
                                    dictKey: dictKey,
                                    extrainfo: extrainfo
                                },
                                beforeSend: function (xhr) {
                                    $("#loader").removeClass("d-none");
                                }
                            }).done(function (response) {

                                console.log(response);

                                $("#contenedor-extracontent").html(response.html);

                                btn.disabled = false;
                                $("#loader").addClass("d-none");
                                Swal.fire({
                                    icon: (response.exito ? "success" : "error"),
                                    title: 'Carga:' + (
                                        response.exito ? 'Exitosa ' : 'Error '
                                    ) + response.tipoDocumento,
                                    text: "mensaje: " + response.msj,
                                    showCancelButton: true,
                                    confirmButtonText: 'aceptar'
                                })

                            });
                        }
                    })

                } else {
                    Swal.fire('Datos Incompletos', 'Debe Agregar un Producto', 'warning');

                    btn.disabled = false;
                }

            }


            function gapiLoaded() {
                gapi.load('client:picker', initializePicker);
                gapi.load('client', initializeGapiClient);
            }

            async function initializeGapiClient() {
                await gapi.client.init({apiKey: API_KEY, discoveryDocs: [DISCOVERY_DOC]});
                gapiInited = true;
                maybeEnableButtons();
            }

            async function initializePicker() {
                await gapi.client.load('https://www.googleapis.com/discovery/v1/apis/drive/v3/rest');
                pickerInited = true;
                maybeEnableButtons();
            }

            function gisLoaded() {
                tokenClient = google.accounts.oauth2.initTokenClient({
                    client_id: CLIENT_ID, scope: SCOPES, callback: '', // defined later
                });
                gisInited = true;
                maybeEnableButtons();
            }


            function maybeEnableButtons() {
                if (pickerInited && gisInited && gapiInited) {
                    $("#authorize_button").removeClass("d-none")
                }
            }


            function handleAuthClick() {
                tokenClient.callback = async (response) => {
                    if (response.error !== undefined) {
                        throw (response);
                    }
                    accessToken = response.access_token;
                    $("#signout_button").removeClass("d-none")
                    $("#picker_button").removeClass("d-none")
                    $("#authorize_button").text("Cambiar de cuenta")

                    await createPicker();
                };

                if (accessToken === null)
                    tokenClient.requestAccessToken({prompt: 'consent'});
                else
                    tokenClient.requestAccessToken({prompt: ''});


            }

            function handleSignoutClick() {
                if (accessToken) {
                    accessToken = null;
                    google.accounts.oauth2.revoke(accessToken);
                    $("#authorize_button").text("Iniciar sesión")
                    $("#signout_button").addClass("d-none")
                    $("#picker_button").addClass("d-none")
                    $("#error_button").addClass("d-none")
                }
            }

            function createPicker() {
                const view = new google.picker.View(google.picker.ViewId.DOCS);
                view.setMimeTypes('application/vnd.google-apps.spreadsheet');
                const picker = new google.picker.PickerBuilder().enableFeature(google.picker.Feature.NAV_HIDDEN).setDeveloperKey(API_KEY).setAppId(APP_ID).setOAuthToken(accessToken).addView(view).addView(new google.picker.DocsUploadView()).setCallback(pickerCallback).build();
                picker.setVisible(true);
            }

            async function pickerCallback(data) {
                if (data.action === google.picker.Action.PICKED) {
                    const document = data[google.picker.Response.DOCUMENTS][0];
                    const fileId = document[google.picker.Document.ID];

                    // Retrieve the content of the cells in the Google Sheets file
                    const response = await gapi.client.sheets.spreadsheets.values.get({
                        spreadsheetId: fileId,
                        range: 'Sheet1',
                        majorDimension: 'ROWS'
                    });

                    const values = response.result.values;

                    if (values && values.length > 0) { // Determine the size of the first row array
                        const maxRowLength = values[0].length;

                        // Pad shorter row arrays with empty values
                        const paddedValues = values.map(row => {
                            const diff = maxRowLength - row.length;
                            return row.concat(Array(diff > 0 ? diff : 0).fill(''));
                        });


                        console.log(paddedValues);
                        addProducts(paddedValues);
                    }
                }
            }
        </script>
        <script async defer src="https://apis.google.com/js/api.js" onload="gapiLoaded()"></script>
        <script async defer src="https://accounts.google.com/gsi/client" onload="gisLoaded()"></script>
        <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.js"></script>
        <script src="https://cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>


    {% endblock %}
