{% extends 'admin/layout.html.twig' %}

{% block title %}Fechas{% endblock %}

{% block content %}
    <input type="hidden" id="url-correr-proceso" value="{{ path('correr-proceso') }}">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="card-title">Cambio de fechas</h4>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <h5 class="text-center">FECHAS SIN FECHA VENTA, CREACIÓN, ACTUALIZACIÓN</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="tablaFechasAntiguas">
                                        <thead>
                                        <tr>
                                            <th>Fecha</th>
                                            <th>Folio</th>
                                            <th>Fecha Creación</th>
                                            <th><PERSON><PERSON>ent<PERSON></th>
                                            <th>Fecha Actualización</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        {% for venta in ventasFechasAntiguas %}
                                            <tr>
                                                <td>{{ venta.fecha ? venta.fecha|date('d/m/Y H:i') : 'N/A' }}</td>
                                                <td>{{ venta.folio }}</td>
                                                <td>{{ venta.fechacreacion ? venta.fechacreacion|date('d/m/Y H:i') : 'N/A' }}</td>
                                                <td>{{ venta.fechaventa ? venta.fechaventa|date('d/m/Y H:i') : 'N/A' }}</td>
                                                <td>{{ venta.fechaactualizacion ? venta.fechaactualizacion|date('d/m/Y H:i') : 'N/A' }}</td>
                                            </tr>
                                        {% else %}
                                            <tr>
                                                <td colspan="5" class="text-center">No hay ventas con estas características</td>
                                            </tr>
                                        {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-right mt-4">
                                    <button class="btn btn-success" onclick="correrProceso()">Correr Proceso</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function(){
            $('#tablaFechasAntiguas').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/es-ES.json',
                },
            });
        });
        function correrProceso(){
            var url = $("#url-correr-proceso").val();
            $.ajax({
                url: url,
                method: 'POST',
                success: function(response) {
                    if (response.status === 'success') {
                        Swal.fire({
                            icon: 'success',
                            title: 'Éxito',
                            text: response.message,
                            confirmButtonText: 'OK'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                location.reload();  // Recargar la página para ver los cambios
                            }
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Hubo un problema al correr el proceso.',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Hubo un problema al correr el proceso.',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }
    </script>
{% endblock %}
