{% extends 'admin/layout.html.twig' %}

{% block title %}Hello ReporteUAMClientesController!
{% endblock %}


{% block content %}
<link rel="stylesheet" href="{{ asset('/css/reporteUam.css') }}">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/v/1.10.22/css/jquery.dataTables.min.css"/>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.6.4/css/buttons.dataTables.min.css"/>

<script type="text/javascript" src="https://cdn.datatables.net/buttons/1.6.4/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/buttons/1.6.4/js/buttons.html5.min.js"></script>


	<div class="row">
		<div class="col-md-12">
            <div class="container card"> 
            <table id="reporteUAM">
				<thead>
					<tr>
						<th>Titular</th>
						<th>Unidad</th>
						<th>Teléfono</th>
						<th>Email</th>
						<th>Número de beneficiarios</th>
						<th>Fecha Última compra 2023</th>
						<th>Fecha Última compra 2024</th>
						<th>Número de compras 2023</th>
						<th>Número de compras 2024</th>
					</tr>
				</thead>
				<tbody>
                    {% for cliente in clientes %}
                    <tr>
                        <td>{{ cliente.Titular }}</td>
                        <td>{{ cliente.Unidad }}</td>
                        <td>{{ cliente.Telefono }}</td>
                        <td>{{ cliente.Email }}</td>
                        <td>{{ cliente.NumeroBeneficiarios|default(0) }}</td>
                        <td>{{ cliente.FechaUltimaCompra2023 ? cliente.FechaUltimaCompra2023|date('Y-m-d-h') : 'N/A' }}</td>
                        <td>{{ cliente.FechaUltimaCompra2024 ? cliente.FechaUltimaCompra2024|date('Y-m-d-h') : 'N/A' }}</td>
                        <td>{{ cliente.Ventas2023|default(0)}}</td>
                        <td>{{ cliente.Ventas2024|default(0)}}</td>
                    </tr>
                {% endfor %}
				</tbody>
			</table>
            </div>
		</div>
	</div>

    <script type="text/javascript">
    $(document).ready(function() {
        $('#reporteUAM').DataTable({
            dom: 'Bfrtip',
            buttons: [
                {
                    extend: 'excelHtml5',
                    title: 'Reporte UAM',
                    text: 'Exportar a Excel',
                    exportOptions: {
                        columns: ':visible'
                    }
                }
            ]
        });
    });
    </script>
    
{% endblock %}
