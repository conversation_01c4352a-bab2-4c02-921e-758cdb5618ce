{% extends 'admin/layout.html.twig' %}
  {% block stylesheets %}
      {{parent()}}
      <link rel="stylesheet" href="//cdn.datatables.net/1.13.1/css/jquery.dataTables.min.css">
{% endblock %}
{% block javascripts %}
{{parent()}}
    <script src="//cdn.datatables.net/1.13.1/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>

    <script>
        $(document).ready( function () {
            $('.tabla').DataTable({

                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.13.1/i18n/es-ES.json"
                },
                dom: 'Bfrtip',
                buttons: [
                    {
                        className: 'btn-primary btn',
                        filename: 'transfer_detail_{{transferId}}',
                        extend: 'excelHtml5',
                        text: 'Exportar excel',
                    }
                ]
            });
        } );
    </script>
{% endblock %}
{% block sonata_admin_content %}

<div class="container">
        <div  class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h2>Detalle del traspaso</h2>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-condensed">
                                <tr>
                                    <th>
                                        Usuario responsable:
                                    </th>
                                    <td>
                                        {{ traspasoalmacen.nombreUsuarioEnvia~" "~traspasoalmacen.apellidopaternoUsuarioEnvia~" "~traspasoalmacen.apellidomaternoUsuarioEnvia }}
                                    </td>
                                    <th>
                                        Fecha de traspaso:
                                    </th>
                                    <td>
                                        {{ traspasoalmacen.creacion | date("d/m/Y  g:ia ","America/Mexico_City" ) }}
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        Sucursal salida:
                                    </th>
                                    <td>
                                        {{ traspasoalmacen.sucursalSalida }}
                                    </td>
                                    <th>
                                        Sucursal destino:
                                    </th>
                                    <td>
                                        {{ traspasoalmacen.sucursalDestino }}
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="100%">
                                        <strong>Nota:</strong>
                                        <p>{{ traspasoalmacen.notas }}</p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                       <div class="table-responsive">
                           <table class="table table-striped tabla">
                               <thead>
                                    <tr>
                                        <th>Modelo</th>
                                        <th>SKU</th>
                                        <th>Código de barras</th>
                                        <th>Marca</th>
                                        <th>Cantidad</th>

                                    </tr>
                               </thead>
                               <tbody>
                               {% for producto in productos %}
                                <tr>
                                    <td>{{ producto.modelo }}</td>
                                    <td>{{ producto.codigobarras }}</td>
                                    <td>{{ producto.codigobarrasuniversal }}</td>
                                    <td>{{ producto.marca }}</td>
                                    <td>{{ producto.cantidad }}</td>
                                </tr>
                               {% endfor %}
                               </tbody>

                           </table>
                       </div>
                        <div class="row">
                            <div class="col-md-12 text-center">
                                <a class="btn btn-sm btn-success" href="{{ listUrl }}">Regresar</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</div>


{% endblock %}
