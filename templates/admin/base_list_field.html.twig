<td class="sonata-ba-list-field sonata-ba-list-field-{{ field_description.type }} text-center" objectId="{{ admin.id(object) }}"{% if field_description.options.row_align is defined %} style="text-align:{{ field_description.options.row_align }}"{% endif %}>
    {% set route = field_description.options.route.name|default(null) %}

    {% if
        field_description.options.identifier|default(false)
        and route
        and admin.hasRoute(route)
        and admin.hasAccess(route, route in ['show', 'edit'] ? object : null)
    %}
        <a class="sonata-link-identifier" href="{{ admin.generateObjectUrl(route, object, field_description.options.route.parameters) }}">
            {%- block field %}
                {% apply spaceless %}
                {% if field_description.options.collapse is defined %}
                    {% set collapse = field_description.options.collapse %}
                    <div class="sonata-readmore"
                          data-readmore-height="{{ collapse.height|default(40) }}"
                          data-readmore-more="{{ collapse.more|default('read_more')|trans({}, 'SonataAdminBundle') }}"
                          data-readmore-less="{{ collapse.less|default('read_less')|trans({}, 'SonataAdminBundle') }}">{{ value }}</div>
                {% else %}

                  {% if field_description.type =="number" %}
                  ${{ (value)|number_format(2, '.', ',') }}
                  {% elseif field_description.type =="integer"%}
                  {{ (value)|number_format(0, '.', ',') }}
                  {% else %}
                    {{ value }}
                  {% endif %}


                {% endif %}
                {% endapply %}
            {% endblock -%}
        </a>
    {% else %}
        {% set isEditable = field_description.options.editable is defined and field_description.options.editable and admin.hasAccess('edit', object) %}
        {% set xEditableType = field_description.type|sonata_xeditable_type %}

        {% if isEditable and xEditableType %}
            {% set url = path(
                'sonata_admin_set_object_field_value',
                admin.getPersistentParameters|default([])|merge({
                    'context': 'list',
                    'field': field_description.name,
                    'objectId': admin.id(object),
                    'code': admin.code(object)
                })
            ) %}

            {% if field_description.type == 'date' and value is not empty %}
                {% set data_value = value.format('Y-m-d') %}
            {% elseif field_description.type == 'boolean' and value is empty %}
                {% set data_value = 0 %}
            {% else %}
                {% set data_value = value %}
            {% endif %}

            <span {% block field_span_attributes %}class="x-editable"
                  data-type="{{ xEditableType }}"
                  data-value="{{ data_value }}"
                  data-title="{{ field_description.label|trans({}, field_description.translationDomain) }}"
                  data-pk="{{ admin.id(object) }}"
                  data-url="{{ url }}" {% endblock %}>
                {{ block('field') }}
            </span>
        {% else %}
            {{ block('field')  }}

        {% endif %}
    {% endif %}
</td>
