{% extends 'admin/layout.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* Contenedor general tipo ticket */
        .ticket-container {
            max-width: 600px;
            margin: 0 auto;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: none;
            font-family: 'Inter', sans-serif;
        }
        /* Cabecera azul */
        .ticket-header {
            background: #124DDE;
            color: #fff;
            padding: 12px 16px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .ticket-header h2 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        .ticket-header .folio {
            font-size: 14px;
            opacity: .9;
        }
        /* Línea punteada inferior para efecto "perforado" */
        .ticket-header::after {
            content: '';
            display: block;
            border-bottom: 1px dashed #ccc;
            margin-top: 8px;
        }
        /* Bloques de contenido */
        .ticket-block {
            padding: 12px 16px;
            border-bottom: 1px dashed #eee;
        }
        .ticket-block:last-of-type {
            border-bottom: none;
        }
        .ticket-block h5 {
            font-size: 14px;
            margin-bottom: 8px;
            text-transform: uppercase;
            color: #555;
        }
        .ticket-block p {
            margin: 0;
            font-size: 14px;
            color: #333;
        }
        /* Tablas reducidas */
        .ticket-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
            margin-top: 8px;
        }
        .ticket-table th,
        .ticket-table td {
            padding: 6px 8px;
            border-bottom: 1px solid #f0f0f0;
            text-align: left;
            color: #333;
        }
        .ticket-table thead th {
            background: #f9f9f9;
            font-weight: 600;
        }
        .text-right { text-align: right; }
        /* Badges compactos */
        .badge-ticket {
            padding: 2px 6px;
            font-size: 11px;
            border-radius: 3px;
        }
        /* Botones estilo minimalista */
        .ticket-actions {
            display: flex;
            flex-wrap: wrap; /* Permite que los botones bajen de línea en pantallas chicas */
            justify-content: space-between;
            gap: 12px; /* Espacio entre elementos */
            padding: 12px 16px;
        }

        .ticket-actions > div {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            align-items: center;
        }

        /* Botón regresar a la izquierda */
        .ticket-actions > div.text-start {
            flex: 1 1 100%; /* Ocupa toda la línea en móvil */
            justify-content: flex-start;
            margin-bottom: 8px;
        }

        /* Contenedor de botones a la derecha */
        .ticket-actions > div.text-end,
        .ticket-actions > div.justify-content-end {
            flex: 1 1 100%; /* Ocupa toda la línea en móvil */
            justify-content: flex-end;
        }

        /* En pantallas medianas en adelante */
        @media (min-width: 576px) {
            .ticket-actions > div.text-start {
                flex: 0 1 auto; /* Tamaño automático en escritorio */
                margin-bottom: 0;
            }
            .ticket-actions > div.text-end,
            .ticket-actions > div.justify-content-end {
                flex: 0 1 auto;
                justify-content: flex-end;
            }
        }

        /* Botones tamaño mínimo y responsivo */
        .ticket-actions .btn {
            font-size: 13px;
            padding: 6px 12px;
            min-width: 120px;
            white-space: nowrap;
        }

        /* Estilos para productos cancelados */
        .bg-light.text-muted {
            background-color: #f8f9fa !important;
            opacity: 0.7;
        }

        .bg-light.text-muted td {
            text-decoration: line-through;
            color: #6c757d !important;
        }

        .bg-light.text-muted .badge {
            text-decoration: none;
        }

        .bg-light.text-muted small {
            text-decoration: none;
        }

    </style>
{% endblock %}

{% block sonata_admin_content %}
    <div class="ticket-container">

        {# ─── HEADER ──────────────────────────── #}
        <div class="ticket-header position-relative" style="padding: 16px; background: #124DDE;">
            <div class="d-flex align-items-center">
                <h2 class="m-0 me-4" style="color: #fff; font-size: 2.5rem; font-weight: 600;">
                    TICKET
                </h2>
            </div>
            <div class="folio" style="
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        color: rgba(255,255,255,0.85);
        font-size: 2.5rem;">
                #{{ venta.folio }}
            </div>
        </div>

        {# ─── FECHAS ──────────────────────────── #}
        <div class="ticket-block">
            <h5>Fecha de Creación | Fecha de Venta</h5>
            <p>
                {{ venta.fechacreacion ? venta.fechacreacion|date('d/m/Y H:i') : '—' }}
                &nbsp;|&nbsp;
                {{ venta.fechaventa ? venta.fechaventa|date('d/m/Y H:i') : '—' }}
            </p>
        </div>

        {# ─── DATOS PRINCIPALES ───────────────── #}
        <div class="ticket-block">
            <h5>Datos de venta</h5>
            <p><strong>Venta | Cotización:</strong>

                {% if venta.cotizacion == '1' %}
                    <span style="
                display: inline-block;
                background: #FFCA28;
                color: #000;
                font-size: 1rem;
                font-weight: 700;
                padding: 0.5rem 1.25rem;
                border-radius: 4px;
                text-transform: uppercase;
            ">
                Cotización
            </span>
                {% else %}
                    <span style="
                display: inline-block;
                background: #43A047;
                color: #fff;
                font-size: 1rem;
                font-weight: 700;
                padding: 0.5rem 1.25rem;
                border-radius: 4px;
                text-transform: uppercase;
            ">
                Venta
            </span>
                {% endif %}
            </p>
            <p><strong>Tipo de Venta:</strong>
                {{ venta.tipoventaIdtipoventa is defined
                ? venta.tipoventaIdtipoventa.nombre
                : venta.convenio }}
            </p>
            <p><strong>Cliente:</strong> {{ venta.clienteIdcliente.nombre }}</p>
            {% if venta.unidadIdunidad %}
                <p><strong>Unidad:</strong> {{ venta.unidadIdunidad.nombre }}</p>
            {% endif %}
            <p><strong>Beneficiario:</strong> {{ venta.beneficiario ?: '—' }}</p>
        </div>

        {# ─── SUCURSAL / VENDEDOR ─────────────── #}
        <div class="ticket-block">
            <h5>Sucursal & Vendedor</h5>
            <p><strong>Sucursal:</strong> {{ venta.sucursalIdsucursal.nombre }}</p>
            <p><strong>Vendedor:</strong> {{ venta.usuarioIdusuario.nombre }}</p>
            {% if venta.status is defined and venta.status == '0' %}
                <div style="margin: 12px 0; padding: 10px; border: 2px solid #dc3545; background: #fff0f0; border-radius: 6px; display: flex; align-items: center; gap: 10px;">
                    <span class="badge badge-danger" style="background: #dc3545; color: #fff; font-size: 1rem; padding: 6px 14px; border-radius: 4px; font-weight: bold; letter-spacing: 1px;">CANCELADA</span>
                    {% if venta.usuarioResponsablecancelacion %}
                        <span style="color: #dc3545; font-weight: bold; font-size: 1.1rem;">
                            <i class="fa fa-user-times" aria-hidden="true"></i>
                            Cancelada por: {{ venta.usuarioResponsablecancelacion.nombre }} {{ venta.usuarioResponsablecancelacion.apellidopaterno }}{% if venta.usuarioResponsablecancelacion.apellidomaterno %} {{ venta.usuarioResponsablecancelacion.apellidomaterno }}{% endif %}
                        </span>
                    {% endif %}
                </div>
            {% endif %}
        </div>

        {# ─── DETALLE DE LA VENTA ──────────────────── #}
        <div class="ticket-block">
            <h5>Detalle de la venta</h5>

            <table class="ticket-table text-center">
                <thead>
                    <tr>
                        <th class="text-right">Subtotal</th>
                        <th class="text-right">IVA</th>
                        <th class="text-right">Total</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="text-right">${{ venta.total|number_format(2) }}</td>
                        <td class="text-right item-center">${{ venta.iva|number_format(2) }}</td>
                        <td class="text-right">${{ venta.pagado|number_format(2) }}</td>
                    </tr>
                </tbody>
            </table>

            <table class="ticket-table mt-3 text-center ">
                <thead>
                    <tr>
                        <th class="text-right">Estado</th>
                        <th class="text-right">Pagado</th>
                        <th class="text-right">Deuda</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            {% if venta.liquidada == '1' %}
                                <span class="badge badge-ticket bg-success">Liquidada</span>
                            {% else %}
                                <span class="badge badge-ticket bg-warning text-dark">Pendiente</span>
                            {% endif %}
                        </td>
                        <td  class="text-right text-success">${{ venta.pagadoTotal|number_format(2) }}</td>
                        <td class="text-right text-danger">${{ venta.deuda|number_format(2) }}</td>
                    </tr>
                </tbody>
            </table>
        </div>



        {# ─── PRODUCTOS ───────────────────────── #}
        <div class="ticket-block">
            <h5>Productos</h5>
            <table class="ticket-table">
                <thead>
                <tr>
                    <th>Artículo</th><th>Cant.</th><th>U.Precio</th><th>Total</th><th>Estado</th>
                </tr>
                </thead>
                <tbody>
                {% for p in productos %}
                    <tr class="{{ p.status!='1' ? 'bg-light text-muted' }}">
                        <td>
                            {{ p.modelo }}
                            {% if p.status != '1' %}
                                <br><small class="text-danger"><i class="fa fa-times-circle"></i> Producto cancelado</small>
                            {% endif %}
                        </td>
                        <td class="text-right">{{ p.cantidad }}</td>
                        <td class="text-right">${{ p.preciofinal|number_format(2) }}</td>
                        <td class="text-right">${{ (p.cantidad * p.preciofinal)|number_format(2) }}</td>
                        <td class="text-center">
                            {% if p.status == '1' %}
                                <span class="badge badge-ticket bg-success">Activo</span>
                            {% else %}
                                <span class="badge badge-ticket bg-danger">Cancelado</span>
                                {% if p.modificacion %}
                                    <br><small class="text-muted">{{ p.modificacion|date('d/m/Y H:i') }}</small>
                                {% endif %}
                            {% endif %}
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>

        {# ─── PAGOS ───────────────────────────── #}
        <div class="ticket-block">
            <h5>Pagos</h5>
            {% if pagos is not empty %}
                <table class="ticket-table">
                    <thead>
                    <tr><th>Fecha</th><th>Monto</th><th>Tipo</th></tr>
                    </thead>
                    <tbody>
                    {% for pago in pagos %}
                        <tr>
                            <td>{{ pago.fecha|date('d/m/Y H:i') }}</td>
                            <td class="text-right">${{ pago.monto|number_format(2) }}</td>
                            <td>{{ pago.tipopago }}</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <p class="text-center text-muted mb-0">— Sin pagos registrados —</p>
            {% endif %}
        </div>

     {# ─── ACCIONES ────────────────────────── #}
        <div class="ticket-actions">
            <div class="text-start">
                <a class="btn btn-sm btn-success" href="{{ listUrl }}">Regresar</a>
            </div>
            <div class="text-end d-flex flex-wrap gap-2">
                <button type="button" class="btn btn-warning"
                        data-bs-toggle="mod"
                        data-bs-target="#modal-visor-documentos"
                        onclick="abrirVisor({{ venta.folio }})">
                    Ticket en PDF
                </button>
                <button type="button" class="btn btn-info"
                        data-bs-toggle="mod"
                        data-bs-target="#modal-visor-documentos"
                        onclick="abrirVisor('especial'); return false;">
                    Ticket Especial en PDF
                </button>
                <button type="button" class="btn btn-success"
                        data-bs-toggle="mod"
                        data-bs-target="#modal-visor-documentos"
                        onclick="abrirVisor({{ venta.folio }}, 'graduacion'); return false;">
                    Ticket de Graduación
                </button>
                <button type="button" class="btn btn-primary"
                        data-bs-toggle="mod"
                        data-bs-target="#modal-documentos-venta"
                        onclick="saleDocumentTable('{{ venta.idventa }}', '{{ venta.usuarioIdusuario.idusuario }}', 1)">
                    Ver Documentos
                </button>
            </div>
        </div>

       <input type="hidden" id="folio" value="{{ venta.folio }}">
        <input type="hidden" id="idempresa" value="{{ venta.sucursalIdsucursal.empresaIdempresa.idempresa }}">
        <input type="hidden" id="archivoautorizacion" value="{{ venta.archivoautorizacion }}">
        <input type="hidden" id="url-visor-documentos" value="{{ path('visor-documentos') }}">
        <input type="hidden" id="url-ventas-sale-document-table" value="{{ path('ventas-sale-document-table') }}">

  {# ─── Modal Visor Documentos ────────────────────────── #}
   <div class="mod " id="modal-visor-documentos" tabindex="-1" aria-labelledby="modal-buscar-cotizacionlLabel"
         aria-hidden="true">
        <div class="mod-dialog">
            <div class="modal-content">
                <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Ticket de venta</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="mod" aria-label="Close"></button>
                </div>

                <div class="modal-body" id="modal-visor-body"></div>

                <div class="modal-footer text-center">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="mod">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    {# ─── Modal Documentos Venta ────────────────────────── #}
    <div class="mod" id="modal-documentos-venta" tabindex="-1" aria-labelledby="modal-documentos-venta-label"
         aria-hidden="true">
        <div class="mod-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modal-documentos-venta-label">Documentos de la Venta</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="mod" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <div id="sale-documents-table-container"></div>
                </div>

                <div class="modal-footer text-center">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="mod">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <script>
     $(document).ready(function () {

            saleDocumentTable("{{ venta.idventa }}", "{{ venta.usuarioIdusuario.idusuario }}", 1);
        })


     function abrirVisor(folio = "", opcion = "ticket") {

         if (folio == "") {
             var folio = $("#folio").val();
         }
         $("#modal-visor-body").html("");

         let url = $("#url-visor-documentos").val();
         let data = {
             folio: folio, 
             idempresa: {{ empresaid }}, 
             opcion: opcion
         };

         console.log("URL:", url);
         console.log("Datos a enviar:", data);

         $.ajax({
             method: "POST",
             url: url,
             data: data
         })
             .done(function (html) {
                 $("#modal-visor-body").html(html);
             })
             .fail(function(jqXHR, textStatus, errorThrown) {
                 console.error("Error al abrir el visor:", textStatus, errorThrown);
                 $("#modal-visor-body").html('<div class="alert alert-danger">Error al cargar el documento</div>');
             });
     }

        function saleDocumentTable(saleid = -1, userid = -1, saledeail = -1) {

            var url = $("#url-ventas-sale-document-table").val();

            $.ajax({
                url: url,
                data: {saleid: saleid, userid: userid, saledeail: saledeail},
                beforeSend: loadingGif("sale-documents-table-container"),
                dataType: "html"
            }).done(function (html) {
                $("#sale-documents-table-container").html(html);

            }).fail(function () {
                alert("error");
            });
        }

        function loadingGif(elementId) {
            $("#" + elementId).html('<div class="text-center"><img src="/img/loading.gif" alt="Cargando..." style="width: 50px;"></div>');
        }
    </script>
    </div>
{% endblock %}
