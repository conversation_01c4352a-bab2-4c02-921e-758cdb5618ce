{% extends 'admin/layout.html.twig' %}
{% block sonata_admin_content %}
    <input id="url-admin-cambiar-contrasena" type="hidden" value="{{ path('admin-cambiar-contrasena') }}">
    <div class="container">
        <div class="card text-center">
            <div class="card-header">
              CAMBIAR CONTRASEÑA
            </div>
            
            <div id="error-list-container" class="p-5 d-none">
                <div class="text-start p-5 border rounded-4 bg-light">
                <ul id="error-list" class="text-danger"></ul>
                </div>
            </div>
            <div class="card-body">
                {% include 'profile/passwordValidation.html.twig' with {'passId1': "inputPassword1", 'passId2': "inputPassword2"} %}
                <label for="inputPassword" class="col-sm-2 col-form-label">Contraseña nueva</label>
                <div class="col-sm-10">
                    <input type="password" class="form-control" id="inputPassword1" autocomplete="new-password" onkeydown = "checkPassword()" onkeyup = "checkPassword()">
                </div>
                <br> <br> <br>
                <label for="inputPassword" class="col-sm-2 col-form-label">Confirmar contraseña nueva</label>
                <div class="col-sm-10">
                    <input type="password" class="form-control" id="inputPassword2"  autocomplete="new-password" onkeydown = "checkPassword()" onkeyup = "checkPassword()">
                </div>
                <br> <br> <br>
                <button type="button" class="btn btn-secondary" onclick="validardatos({{ idusuario }})">Cambiar</button>
            </div>
        </div>
    </div>
{% endblock %} 
{% block javascripts %}
    {{parent()}}

    <script>

        function validardatos(idusuario) {
            let pass1 = $("#inputPassword1").val();
            let pass2 = $("#inputPassword2").val();
            $("#error-list").html('');
            $("#error-list-container").addClass('d-none');
            let url=$("#url-admin-cambiar-contrasena").val()+"/"+idusuario;

            const constraints = [
                {
                    regex: /^.{8,4096}$/
                },
                {
                    regex: /[A-Z]/
                },
                {
                    regex: /[a-z]/
                },
                {
                    regex: /\d/
                },
                {
                    regex: /[^a-zA-Z\d]/
                }
            ]

            let isValid = true

            for (const constraint of constraints) {
                if (!constraint.regex.test(pass1)) isValid = false
            }

            isValid = isValid && (pass1 == pass2)

            if (isValid){
                $.ajax({
                    url: url,
                    method: "POST",
                    data: {pass:pass1},
                    dataType: "json",
                }).done(function(reponse) {
                    
                    if (reponse.errors.length > 0){
                        let curErrors = "";
                        for (const msg of reponse.errors){
                            curErrors+="<li>"+msg+"</li>"
                        }
                        $("#error-list-container").removeClass('d-none');
                        $("#error-list").html(curErrors);
                    }

                    if(reponse.exito){
                        Swal.fire({
                            icon: 'success',
                            title: 'Contraseña restablecida correctamente',
                            showConfirmButton: true,
                            timer: 2500
                        })

                        $(".password-input").val('')
                        $('.password-check').removeClass('bg-correct');
                    }else{
                        Swal.fire({
                            icon: 'info',
                            title: reponse.msj,
                        })
                    }
                }).fail(function(e) {
                    alert( "error" );
                });
            }
            else showPasswordErrors()
        }


        
    </script>
{% endblock %} 