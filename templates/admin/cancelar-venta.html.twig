{% extends 'admin/layout.html.twig' %}
{% block sonata_admin_content %}
<input id="url-cancelar-venta" value="{{path('cancelar-venta')}}/{{idventa}}" type="hidden" /> 
<input id="url-list" value="{{listUrl}}" type="hidden" /> 
    <div class="container">
        <div class="card text-center">
            <div class="card-header">
               CANCELAR VENTA
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="exampleFormControlTextarea1" class="form-label">¿Por que desea cancelar esta venta?</label>
                    <textarea class="form-control" id="exampleFormControlTextarea1" rows="3"></textarea>
                </div>
                <br>
                <h5 class="card-title">¿Esta seguro de cancelar esta venta?</h5>
                <button type="button" class="btn btn-warning" onclick="validardatos()">Si</button>
            </div>
        </div>
    </div>
{% endblock %} 
{% block javascripts %}
    {{parent()}}

    <script>
        function validardatos() {
            let porquesecancelo = $("#exampleFormControlTextarea1").val();
            let url = $('#url-cancelar-venta').val();
            let urldirec = $('#url-list').val();

            if(porquesecancelo=="") {
                Swal.fire({
                type: 'info',
                title: 'Campos vacios.',
                text: 'Has dejado un campo vacio, verifica tus respuestas e intente nuevamente',
                })
            } else {
                $.ajax({
                    url: url,
                    method: "POST",
                    dataType: "json",
                    data: {porquesecancelo:porquesecancelo},
                    beforeSend: function( xhr ) {
                    
                    }
                })
                .done(function( response ) {
                    console.log(response);
                    if(response.exito){
                       Swal.fire({   
                        type: 'success',
                        title: 'Cancelado correctamente',
                        showConfirmButton: false,
                        timer: 2500
                        })

                        setTimeout(function(){
                            document.location.href = urldirec;
                        }, 3000);
                    } else {
                        Swal.fire({
                        type: 'info',
                        title: 'No se pudo procesar tu solicitud',
                        text: response.msj,
                        })
                    }
                });
            }
        }
    </script>
{% endblock %} 