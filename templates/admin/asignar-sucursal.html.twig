{% extends 'admin/layout.html.twig' %}

{% block sonata_admin_content %}
    <div class="container mt-5">
        <div class="card">
            <div class="card-header text-white bg-primary">
                <h4 class="mb-0">Asignar Sucursal</h4>
            </div>
            <div class="card-body">
                <div class="row justify-content-center">
                    <div class="col-md-12 mb-3">
                        <label for="sucursal" class="form-label">Asignar Sucursal</label>
                        <!-- Checkbox para seleccionar todos -->
                        <div class="form-check mb-3">
                            <input
                                    type="checkbox"
                                    class="form-check-input"
                                    id="seleccionar-todos"
                                    onchange="seleccionarTodos(this)">
                            <label class="form-check-label" for="seleccionar-todos">
                                Seleccionar todos
                            </label>
                        </div>

                        <div class="row">
                            {% for sucursal in sucursalesDisponibles %}
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input
                                                type="checkbox"
                                                class="form-check-input sucursal-checkbox"
                                                name="sucursal[]"
                                                id="sucursal{{ sucursal.idsucursal }}"
                                                value="{{ sucursal.idsucursal }}"
                                                {% if sucursal.idsucursal in sucursalesAsociadasIds %}checked{% endif %}
                                        >
                                        <label class="form-check-label" for="sucursal{{ sucursal.idsucursal }}">
                                            {{ sucursal.nombre }}
                                        </label>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <button type="button" class="btn btn-success" onclick="validardatos()">Guardar Cambios</button>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}

    <script>
        // Función para seleccionar/deseleccionar todos los checkboxes
        function seleccionarTodos(source) {
            console.log("source", source);
            let checkboxes = document.querySelectorAll('.sucursal-checkbox');
            checkboxes.forEach(checkbox => checkbox.checked = source.checked);
        }

        function validardatos() {
            let sucursalSeleccionada = [];
            $('input[name="sucursal[]"]:checked').each(function() {
                sucursalSeleccionada.push($(this).val());
            });
            let urldirec = $('#url-list').val();

            if (sucursalSeleccionada.length === 0) {
                Swal.fire({
                    icon: 'info',
                    title: 'Selecciona una Sucursal',
                    text: 'Por favor, selecciona al menos una sucursal.',
                });
            } else {
                $.ajax({
                    url: '{{ path('asignar-sucursal',{'iddocumentos': iddocumentos}) }}',
                    method: "POST",
                    dataType: "json",
                    data: {
                        sucursal: sucursalSeleccionada,
                    },
                    beforeSend: function() {
                    }
                })
                    .done(function(response) {
                        if (response.exito) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Sucursal Asignada Correctamente',
                                showConfirmButton: false,
                                timer: 2500
                            });
                            setTimeout(function() {
                                window.history.back(); // Esto redirige a la página anterior en el historial del navegador
                            }, 2500);
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: response.msj,
                            });
                        }
                    });
            }
        }
    </script>
{% endblock %}
