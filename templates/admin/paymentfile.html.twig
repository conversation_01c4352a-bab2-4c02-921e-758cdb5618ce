{% extends 'admin/layout.html.twig' %}
{% block sonata_admin_content %}
	<div class="container-fluid bg-white rounded-4 py-4 shadow">
		<div class="row">
			<div class="col-md-12 text-start">
				{% if object.paymentfile %}
					<a type="button" class="btn btn-sm btn-default mb-3" href="{{ asset('uploads/FacturasEnGrupo/' ~ object.paymentfile) }}" download>
						<strong>Descargar pago</strong>
					</a>
				{% endif %}
				<br>
				{% if existZip %}
					{% if zipFilePath starts with 'http' %}
						<!-- If it's a URL, use it directly -->
						<a href="{{ zipFilePath }}" class="btn btn-info" target="_blank">Descargar archivo ZIP</a>
					{% else %}
						<!-- If it's a local file, use the asset function -->
						<a href="{{ asset(zipFilePath) }}" class="btn btn-info" download>Descargar archivo ZIP</a>
					{% endif %}
				{% else %}
					<h4>No hay archivo</h4>
				{% endif %}
			</div>
		</div>

		<table class="table table-bordered table-hover mt-5">
			<thead>
				<tr>
					<th class="text-center"></th>
					<th class="text-center">Folio</th>
					<th class="text-center">Cliente</th>
					<th class="text-center">Beneficiario</th>
					<th class="text-center">Relación</th>
					<th class="text-center">Numero de empleado</th>
					<th class="text-center">Total</th>
				</tr>
			</thead>
			<tbody>

				{% for index, venta in ventas %}
					{% include 'factura_autorizacion/auth_scan.html.twig' with {'venta': venta, 'index': index + 1, 'checkclass': 'none'} %}
				{% endfor %}
			</tbody>
		</table>
	</div>
{% endblock %}
