{% extends 'admin/layout.html.twig' %}
{% block sonata_admin_content %}
    <div class="container"> 
        <div class="card">
            <div class="card-header stock-encabezado">
                DETALLE DE STOCK 
            </div>
            <div class="jumbotron">
                <div class="row">
                    <div class="col-md-6 text-center stock-subencabezado">
                        <h3>SKU: {{ stock.codigobarras }}</h3>
                    </div>
                    <div class="col-md-6 text-center stock-subencabezado">
                        <h3>Tipo de producto: 
                            {% if stock.tipoproducto == "2" %}
                                Servicio
                            {% else %}
                                Almacenable
                            {% endif %}
                        </h3>
                    </div>
                </div>
                {% for cil in cargainventariologs %}
                    <div class="row">
                        <div class="col-md-4 text-center stock-subencabezado">
                            <h3>Fecha de ingreso al inventario: {{ cil.fecha | date("d/m/Y h:m a", "America/Mexico_City") }}</h3>
                        </div>
                        <div class="col-md-4 text-center stock-subencabezado">
                            <h3>Productos ingresados: {{ cil.cantidad }}</h3>
                        </div>
                        <div class="col-md-4 text-center stock-subencabezado">
                            <h3>Archivo de excel: 
                                <a href="{{  asset(cil.archivoexcel, 'inventario') }}" download>{{ cil.archivoexcel }}</a>
                            </h3>
                        </div>

                        </div>
                    </div>
                {% endfor %}

                {% if stockventas[0] is defined %} 
                    <div class="row">
                        <div class="col-md-6 text-center stock-subencabezado">
                            <h3>Sucursal: {{stockventas[0].sucursal}}</h3>
                        </div>
                        <div class="col-md-6 text-center stock-subencabezado">
                            <h3>Usuario quien lo subió: {{stockventas[0].nombre}}</h3>
                        </div>
                    </div>
                {% endif %}
                <div class="row">
                    <div class="col-md-6 text-center stock-subencabezado">
                        <h3>Stock en almacén: {{ stock.cantidad }}</h3>
                    </div>
                    <div class="col-md-6 text-center stock-subencabezado">
                        <h3>Modelo: {{ stock.modelo }}</h3>
                    </div>
                </div>
                <br>
                <hr>
                <div class="row text-center">
                    <h2 class="col-md-12 text-center stock-subencabezado">Historial de traspasos</h2>
                    <div class="p-5 table-responsive-md">
                        <table class="table table-striped stock-table">
                            <thead>
                                <tr>
                                    <th>Sucursal de salida</th>
                                    <th>Sucursal de entrada</th>
                                    <th>Encargado de traspaso</th>
                                    <th>Fecha de traspaso</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for producto in productos %}
                                    <tr>
                                        <td class='td-detalle text-center'>{{ producto.sucursalSalida }}</td>
                                        <td class='td-detalle text-center'>{{ producto.sucursalDestino }}</td>
                                        <td class='td-detalle text-center'>{{ producto.nombreUsuarioEnvia ~ ' ' ~ producto.apellidopaternoUsuarioEnvia}}</td>
                                        <td class='td-detalle text-center'>{{ producto.creacion | date("d/m/Y h:m a", "America/Mexico_City") }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <br>
                <div class="row text-center">
                    <h2 class="col-md-12 text-center stock-subencabezado">Historial de venta</h2>
                    <div class="p-5 table-responsive-md">
                    <table class="table table-striped stock-table">
                        <thead>
                            <tr>
                                <th>Folio de ticket</th>
                                <th>Vendedor</th>
                                <th>Sucursal</th>
                                <th>Fecha de creación</th>
                                <th>Cliente</th>
                                <th>Cupón</th>
                                <th>Descuento</th>
                                <th>Precio</th>
                                <th>Costo</th>
                                <th>Estado</th>
                                <th>Tipo</th>
                                <th>Tipo Venta</th>
                            </tr>
                        </thead>

                        <tbody>
                            {% for stockventa in stockventas %}
                                <tr>
                                    <td class='td-detalle text-center'>  <a href="/admin/venta/{{stockventa.idventa}}/detalleVenta" target="_blank">{{stockventa.folio}}</a>                                </td>
                                    <td class='td-detalle text-center'>{{stockventa.nombre ~ ' ' ~ stockventa.apellidopaterno}}</td>
                                    <td class='td-detalle text-center'>{{stockventa.sucursal}}</td>
                                    <td class='td-detalle text-center'>{{stockventa.fecha | date("d/m/Y h:m a", "America/Mexico_City") }}</td>
                                    <td class='td-detalle text-center'>{{stockventa.cliente}}</td>
                                    <td class='td-detalle text-center'>{{stockventa.codigo}}</td>
                                    <td class='td-detalle text-center'>{{stockventa.porcentajedescuento ~ '%'}}</td>
                                    <td class='td-detalle text-center'>{{stockventa.total | number_format(2, '.', ',')}}</td>
                                    <td class='td-detalle text-center'>{{stockventa.costo | number_format(2, '.', ',')}}</td>
                                    <td class='td-detalle text-center'>
                                        {% if stockventa.status == "1" %}
                                            Activo
                                        {% else %}
                                            Cancelado
                                        {% endif %}
                                    </td>
                                    <td class='td-detalle text-center'>
                                        {% if stockventa.cotizacion == "1" %}
                                            Cotización
                                        {% else %}
                                            Venta
                                        {% endif %}
                                    </td>
                                    <td class='td-detalle text-center'>{{stockventa.TipoventaNombre}}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    </div>
                </div>
                {% if stockHistories | length > 0 %}
                <br>
                <div class="row text-center">
                    <h2 class="col-md-12 text-center stock-subencabezado">Historial de estados de Stock</h2>
                    <div class="p-5 table-responsive-md">
                        <table class="table table-striped stock-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Estado</th>
                                    <th>Responsable</th>
                                    <th>Fecha</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for index, StockHistory in stockHistories %}
                                    <tr>
                                        <td class='td-detalle text-center'>{{ index + 1 }}</td>
                                        <td class='td-detalle text-center'>{{ StockHistory.state }}</td>
                                        <td class='td-detalle text-center'>{{ StockHistory.fullName}}</td>
                                        <td class='td-detalle text-center'>{{ StockHistory.date | date("d/m/Y H:i") }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <br>
                {% endif %}
                {% if merma is not null %}
                <br>
                <div class="row text-center">
                    <h2 class="col-md-12 text-center stock-subencabezado">Historial de merma</h2>
                    <div class="p-5 table-responsive-md">
                        <table class="table table-striped stock-table">
                            <thead>
                                <tr>
                                    <th>Folio</th>
                                    <th>Tipo</th>
                                    <th>Producto</th>
                                    <th>SKU</th>
                                    <th>Código de barras</th>
                                    <th>Clave</th>
                                    <th>Fecha</th>
                                    <th>Responsable</th>
                                    <th>Cantidad</th>
                                    <th>Costo unitario</th>
                                    <th>Perdida Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <td class="text-center align-middle ">{{merma.folio}}</td>
                                <td class="text-center align-middle ">
                                    {{merma.departurecauseIddeparturecause.name}}
                                </td>
                                <td class="text-center align-middle ">{{merma.stockIdstock.productoIdproducto.modelo}}</td>
                                <td class="text-center align-middle ">{{merma.stockIdstock.codigobarras}}</td>
                                <td class="text-center align-middle ">{{merma.stockIdstock.productoIdproducto.codigobarrasuniversal}}</td>
                                <td class="text-center align-middle ">{{merma.stockIdstock.productoIdproducto.clave}}</td>
                                <td class="text-center align-middle ">{{merma.fechaincidencia|date("d/m/Y")}}</td>
                                <td class="text-center align-middle ">{{merma.usuarioIdusuario.nombre}}</td>
                                <td class="text-center align-middle ">{{merma.cantidad}}</td>
                                <td class="text-center align-middle ">${{merma.stockIdstock.productoIdproducto.costo}}</td>
                                <td class="text-center align-middle ">${{(merma.stockIdstock.productoIdproducto.costo * merma.cantidad)|number_format(2, '.', ',')}}</td>
                            </tbody>
                        </table>
                    </div>
                </div>
                <br>
                {% endif %}
            </div>
        </div>
    </div>
{% endblock %}