
{#
<td>
    {% if object.flujoexpedienteIdflujoexpediente and object.flujoexpedienteIdflujoexpediente.idflujoexpediente %}
        {% set products = admin.getProducts(object.flujoexpedienteIdflujoexpediente.idflujoexpediente) %}
        {% if products is iterable %}
            <ul>
                {% for product in products %}
                    <li>{{ product.modelo }} ({{ product.codigobarrasuniversal }})</li>
                {% endfor %}
            </ul>
        {% else %}
            <span>No hay productos</span>
        {% endif %}
    {% else %}
        <span>Información no disponible</span>
    {% endif %}
</td>
#}
