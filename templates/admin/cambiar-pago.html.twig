{% extends 'admin/layout.html.twig' %}

{% block sonata_admin_content %}
    <input id="url-cambiar-pago" value="{{ path('cambiar-pago', { 'idpago': idpago }) }}" type="hidden" />
    <input id="url-list" value="{{ listUrl }}" type="hidden" />

    <div class="container mt-5">
        <div class="card">
            <div class="card-header text-white bg-primary">
                <h4 class="mb-0">Cambiar Pago</h4>
            </div>
            <div class="card-body">
                <form>
                    <div class="row justify-content-center">
                        <div class="col-md-5 mb-3">
                            <label for="tipoPago" class="form-label">Tipo de Pago</label>
                            <select name="tipoPago" id="tipoPago" class="form-control">
                                {% for tipo in tiposPago %}
                                    <option value="{{ tipo.idpaymenttype }}">{{ tipo.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-5 mb-3" style="padding-top: 18px;">
                            <label for="cantidad" class="form-label">Cantidad</label>
                            <div class="input-group">
                                <input id="cantidad" class="form-control" type="number" value="{{ cantidad }}" placeholder="Ingresa la cantidad">
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-4">
                        <button type="button" class="btn btn-success" onclick="validardatos()">Guardar Cambios</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
{% endblock %}



{% block javascripts %}
    {{ parent() }}

    <script>
        function validardatos() {
            let tipoPago = $("#tipoPago").val();

            let cantidad = $("#cantidad").val();
            let url = $('#url-cambiar-pago').val();
            let urldirec = $('#url-list').val();

            if(tipoPago === "" || cantidad === "") {
                Swal.fire({
                    icon: 'info',
                    title: 'Campos vacíos',
                    text: 'Por favor, completa ambos campos antes de enviar.',
                });
            } else {
                $.ajax({
                    url: url,
                    method: "POST",
                    dataType: "json",
                    data: {
                        tipoPago: tipoPago,
                        cantidad: cantidad
                    },
                    beforeSend: function() {
                        // Opcional: Puedes agregar algún indicador de carga aquí
                    }
                })
                    .done(function(response) {
                        if(response.exito) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Pago cambiado correctamente',
                                showConfirmButton: false,
                                timer: 2500
                            });
                            setTimeout(function() {
                                document.location.href = urldirec;
                            }, 2500);
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: response.msj,
                            });
                        }
                    });
            }
        }
    </script>
{% endblock %}
