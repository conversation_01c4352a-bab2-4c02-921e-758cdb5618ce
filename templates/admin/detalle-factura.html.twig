{% extends 'admin/layout.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* Contenedor general tipo ticket */
        .ticket-container {
            max-width: 600px;
            margin: 0 auto;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: none;
            font-family: 'Inter', sans-serif;
        }
        /* Cabecera azul */
        .ticket-header {
            background: #124DDE;
            color: #fff;
            padding: 12px 16px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .ticket-header h2 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        .ticket-header .folio {
            font-size: 14px;
            opacity: .9;
        }
        /* Línea punteada inferior para efecto "perforado" */
        .ticket-header::after {
            content: '';
            display: block;
            border-bottom: 1px dashed #ccc;
            margin-top: 8px;
        }
        /* Bloques de contenido */
        .ticket-block {
            padding: 12px 16px;
            border-bottom: 1px dashed #eee;
        }
        .ticket-block:last-of-type {
            border-bottom: none;
        }
        .ticket-block h5 {
            font-size: 14px;
            margin-bottom: 8px;
            text-transform: uppercase;
            color: #555;
        }
        .ticket-block p {
            margin: 0;
            font-size: 14px;
            color: #333;
        }
        /* Tablas reducidas */
        .ticket-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
            margin-top: 8px;
        }
        .ticket-table th,
        .ticket-table td {
            padding: 6px 8px;
            border-bottom: 1px solid #f0f0f0;
            text-align: left;
            color: #333;
        }
        .ticket-table thead th {
            background: #f9f9f9;
            font-weight: 600;
        }
        .text-right { text-align: right; }
        /* Badges compactos */
        .badge-ticket {
            padding: 2px 6px;
            font-size: 11px;
            border-radius: 3px;
        }
        /* Botones estilo minimalista */
        .ticket-actions {
            display: flex;
            justify-content: space-between;
            padding: 12px 16px;
        }
        .ticket-actions .btn {
            font-size: 13px;
            padding: 6px 12px;
        }
    </style>
{% endblock %}

{% block sonata_admin_content %}
    <div class="ticket-container">
        <!-- Cabecera del ticket -->
        <div class="ticket-header">
            <h2>Detalle de Factura</h2>
            <span class="folio">Folio: {{ productos[0].folio }}</span>
        </div>

        <!-- Bloque de datos de facturación -->
        <div class="ticket-block">
            <h5>Datos de Facturación</h5>
            <table class="ticket-table">
                <tr>
                    <th>Razón Social</th>
                    <td>{{ facturas.razonsocial }}</td>
                </tr>
                <tr>
                    <th>RFC</th>
                    <td>{{ facturas.rfc }}</td>
                </tr>
                <tr>
                    <th>Email</th>
                    <td>{{ facturas.email }}</td>
                </tr>
                <tr>
                    <th>Código Postal</th>
                    <td>{{ facturas.codigopostal }}</td>
                </tr>
                <tr>
                    <th>Régimen Fiscal</th>
                    <td>{{ facturas.regimenfiscal }}</td>
                </tr>
                <tr>
                    <th>Uso CFDI</th>
                    <td>{{ facturas.usocfdi }}</td>
                </tr>
                <tr>
                    <th>Constancia</th>
                    <td>
                        <a href="/uploads/constanciasSF/{{ facturas.constanciasituacionfiscal }}" download>{{ facturas.constanciasituacionfiscal }}</a>
                    </td>
                </tr>
            </table>
        </div>

        <!-- Bloque de productos -->
        <div class="ticket-block">
            <h5>Productos</h5>
            <table class="ticket-table">
                <thead>
                    <tr>
                        <th>Producto</th>
                        <th>Cantidad</th>
                        <th>Precio</th>
                        <th>Subtotal</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    {% for producto in productos %}
                        <tr {% if producto.status !="1" %} class="bg-danger" {% endif %}>
                            <td>
                                {{ producto.modelo }}<br>
                                <small>{{ producto.descripcion }}</small>
                            </td>
                            <td>{{ producto.cantidad | number_format(0, '.', ',') }}</td>
                            <td>
                                {% set iva = 1+producto.porcentajeiva %}
                                {% set precioSinIvaUnitario = producto.preciofinal/ (iva) %}
                                ${{ precioSinIvaUnitario | number_format(2, '.', ',') }}
                            </td>
                            <td>
                                {% set subtotal = precioSinIvaUnitario * producto.cantidad %}
                                ${{ subtotal | number_format(2, '.', ',')}}
                                {% if producto.status !="1" %}
                                    <span class="badge badge-ticket bg-warning">Eliminado</span>
                                {% endif %}
                            </td>
                            <td>
                                ${{ producto.pagado | number_format(0, '.', ',') }}
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>

            </table>
        </div>

        <!-- Bloque de pagos -->
        <div class="ticket-block">
            <h5>Pagos</h5>
            {% if pagos is not empty %}
                <table class="ticket-table">
                    <thead>
                        <tr>
                            <th>Fecha</th>
                            <th>Tipo</th>
                            <th>Monto</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for pago in pagos %}
                            <tr>
                                <td>{{ pago.fecha | date("d/m/Y g:ia") }}</td>
                                <td>{{ pago.tipopago }}</td>
                                <td>${{ pago.monto | number_format(2, '.', ',') }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <p class="text-muted">Aún no hay pagos registrados.</p>
            {% endif %}
        </div>

        <!-- Bloque de acciones -->
        <div class="ticket-actions">
            {% if existZip %}
                {% if zipFilePath starts with 'http' %}
                    <!-- If it's a URL, use it directly -->
                    <a class="btn btn-success" href="{{ zipFilePath }}" target="_blank">
                        <i class="fa fa-download"></i> Descargar ZIP
                    </a>
                {% else %}
                    <!-- If it's a local file, use the admin.generateUrl function -->
                    {% set parts = zipFileName|split('/') %}
                    {% if parts|length > 1 %}
                        <a class="btn btn-success" href="{{ admin.generateUrl('download_zip', { 'clienteId': parts[0], 'filename': parts[1] }) }}">
                            <i class="fa fa-download"></i> Descargar ZIP
                        </a>
                    {% else %}
                        <a class="btn btn-success" href="{{ admin.generateUrl('download_zip', { 'clienteId': 'default', 'filename': zipFileName }) }}">
                            <i class="fa fa-download"></i> Descargar ZIP
                        </a>
                    {% endif %}
                {% endif %}
            {% else %}
                <span class="text-muted">Sin ZIP disponible</span>
            {% endif %}
        </div>
    </div>
{% endblock %}
