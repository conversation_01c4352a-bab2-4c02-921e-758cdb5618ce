{% extends 'admin/standard-layout.html.twig' %}
{% block stylesheets %}
    <link rel="apple-touch-icon" sizes="57x57" href="/favicon/apple-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="/favicon/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/favicon/apple-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/favicon/apple-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="/favicon/apple-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/favicon/apple-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/favicon/apple-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/favicon/apple-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/favicon/apple-icon-180x180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/favicon/android-icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="/favicon/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon/favicon-16x16.png">
    <link rel="manifest" href="/favicon/manifest.json">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/js/all.min.js"></script>
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="msapplication-TileImage" content="/favicon/ms-icon-144x144.png">
    <meta
            name="theme-color" content="#ffffff">
    <!--<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">-->
    <link href="{{ asset('css/bootstrap.css') }}" rel="stylesheet">
    {{ parent() }}

    <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
    <link href="{{ asset('/lib/datepicker-for-bootstrap/css/bootstrap-datepicker.css') }}" rel="stylesheet"/>
    <link href="{{ asset('/lib/datepicker-for-bootstrap/css/bootstrap-datepicker3.css') }}" rel="stylesheet"/>
    <link href="{{ asset('css/global-styles.css') }}?version={{ version }}" rel="stylesheet"/>
    <link href="{{ asset('css/styles/expediente-clinico.css') }}?version={{ version }}" rel="stylesheet"/>
    <link href="{{ asset('css/admin.css') }}?version={{ version }}" rel="stylesheet"/>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Courier+Prime&display=swap" rel="stylesheet">
    <link
            href="{{ asset('css/estilo.css') }}?version={{ version }}" rel="stylesheet"/>


    <!--<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" >
      <script src="https://code.jquery.com/jquery-3.2.1.slim.min.js" ></script>
     <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
      <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" ></script>-->

{% endblock %}

{% block content %}


<div class="modal fade" id="modal-visor-ventas" tabindex="-1" aria-labelledby="modal-visor-ventas"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Venta</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div
                    class="modal-body" id="contenedor-modal-visor-venta">
                <!-- Aquí se llenará el contenido del modal con la respuesta de la petición AJAX -->

            </div>
            <div class="modal-footer text-center">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modal-visor-anuncio" tabindex="-1" aria-labelledby="modal-visor-anuncio"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Anuncio</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div
                    class="modal-body-anuncio" id="contenedor-modal-visor-anuncio"><!-- Contenido del modal -->

            </div>
            <div class="modal-footer text-center">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<div class="mod " id="modal-enterprise-visor" tabindex="-1" aria-labelledby="" aria-hidden="true">
    <div class="mod-dialog">
        <div class="modal-content" style="border-radius:10px">
            <div class="modal-header bg-primary">
                <h1 class="modal-title fs-5" id="modalDocumentoTitle"></h1>
            </div>

            <div class="modal-body" id="modal-body-logos">

                <div id="container-modal-enterprise-visor"></div>

            </div>

        </div>
    </div>
</div>

<div class="mod " id="modal-product-images" tabindex="-1" aria-labelledby="" aria-hidden="true">
    <div class="mod-dialog">
        <div class="modal-content" style="border-radius:10px">
            <div class="modal-header bg-primary">
                <h1 class="modal-title fs-5" id="product-model"></h1>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <div class="modal-body" id="modal-body-product-images">

                <div id="container-modal-product-images"></div>

            </div>

        </div>
    </div>
</div>

<div class="modal " id="modal-product-image-visor" tabindex="-1" aria-labelledby="" aria-hidden="true">
    <div class="mod-dialog">
        <div class="modal-content" style="border-radius:10px">
            <div class="modal-header bg-secondary">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body-product-image-visor">
                <div id="container-modal-product-image-visor"></div>
            </div>
        </div>
    </div>
</div>

<div class="mod " id="modal-product-tag" tabindex="-1" aria-labelledby="" aria-hidden="true">
    <div class="mod-dialog modal-dialog-scrollable">
        <div class="modal-content" style="border-radius:10px">
            <div class="modal-header bg-primary">
                <div class="modal-header bg-primary">
                    <h1 class="modal-title fs-5" id="product-model-tag"></h1>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="container-product-tag"></div>
            </div>
        </div>
    </div>

    {% endblock %}

    {% block javascripts %}
        {{ parent() }}

        <!--script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script-->
        <!--<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+jjXkk+Q2h455rYXK/7HAuoJl+0I4" crossorigin="anonymous"></script>-->
        <script src="{{ asset('js/bootstrap.bundle.js') }}"></script>
        <script src="{{ asset('js/jquery.formatCurrency-1.4.0.pack.js') }}"></script>
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
        <script src="{{ asset('lib/bootstrap-datepicker/js/bootstrap-datepicker.min.js') }}"></script>
        <script src="{{ asset('js/scripts.js') }}?version={{ version }}"></script>
        <script src="{{ asset('js/funciones.js') }}?version={{ version }} "></script>
        <script src="https://cdn.tiny.cloud/1/pdhteokdcwiugr4fpl5vq1dwbx9j3c30f4lt813xezj8kmbk/tinymce/6/tinymce.min.js"></script>
        <script src="{{ asset('js/tinymce-config.js') }}"></script>
        <script src="{{ asset('theme/material-dashboard-master/assets/js/plugins/moment.min.js') }}"></script>
        <script src="{{ asset('theme/material-dashboard-master/assets/js/plugins/jquery.dataTables.min.js') }}"></script>
        <script>
            $(document).ready(function () {
                $(".defective-select").select2('destroy');
                $(".defective-select").addClass("form-control mt-2")
            })

            function changeDefective(stockId, defective) {
                $.ajax({
                    url: "{{ path('actualizacion-change-defective') }}",
                    data: {
                        stockId: stockId,
                        defective: defective
                    },
                    dataType: "json"
                }).done(function (response) {

                    let indicator = (response.success) ? 'rgba(242, 113, 113, 0.5)' : 'rgba(139, 242, 113, 0.5)'
                    $('#defective-select-' + stockId).css({'background-color': indicator})
                    setTimeout(function () {
                        $('#defective-select-' + stockId).css({'background-color': '#FFFFFF'})
                    }, 1500)

                })
            }
             document.addEventListener("DOMContentLoaded", function () {
            const cpInput = document.querySelector('input[name$="[codigo_postal]"]');
            const ciudadInput = document.querySelector('input[name$="[ciudad]"]');
            const municipioInput = document.querySelector('input[name$="[municipio_delegacion]"]');
            const estadoInput = document.querySelector('input[name$="[estado]"]');

            let timer = null;

            if (!cpInput) {
                console.warn("⚠ No se encontró el input del Código Postal.");
                return;
            }

            cpInput.addEventListener('input', function () {
                const value = cpInput.value.trim();

                if (value.length !== 5 || isNaN(value)) return;

                if (timer) clearTimeout(timer); 
                timer = setTimeout(() => {
                    fetch(`/api/codigos-postales?cp=${value}`)
                        .then(res => res.json())
                        .then(data => {

                            if (data.estado && estadoInput) estadoInput.value = data.estado;
                            if (data.municipio && municipioInput) municipioInput.value = data.municipio;
                            if (data.ciudad && ciudadInput) ciudadInput.value = data.ciudad;

                            if (data.message) {
                                alert("⚠ " + data.message);
                            }
                        })
                        .catch(err => {
                            console.error("Error al buscar CP:", err);
                        });
                }, 1000); 
            });
        });
                </script>


    {% endblock %}
    {% block sonata_sidebar_search %}{% endblock sonata_sidebar_search %}

    {% block side_bar_after_nav %}

        <input id="url-app_abrir_visor" type="hidden" value="{{ path('app_abrir_visor') }}">
        <input id="url_app_abrir_visor_anuncio" type="hidden" value="{{ path('app_abrir_visor_anuncio') }}">
        <input id="url-app_abrir_visor_ventas" type="hidden" value="{{ path('app_abrir_visor_ventas') }}">

        <div
                class="sidebar-wrapper">
            {#
            {% if is_granted('ROLE_SUPER_ADMIN') == true %}
              <!-- <li class="nav-item active ">
                  <a class="nav-link" href="{{path('almacen-stock')}}">

                    <p><i class="fa fa-cloud-upload" aria-hidden="true"></i>Ver Stock por Sucursal</p>
                  </a>
                </li> -->
              <!--  <li class="nav-item active ">
                  <a class="nav-link" href="{{path('seleccionar-productos')}}">

                    <p><i class="fa fa-cloud-upload" aria-hidden="true"></i>Transpaso Productos</p>
                  </a>
                </li> -->
              <!--   <li class="nav-item active ">
                  <a class="nav-link" href="{{path('subir-excel')}}">

                    <p><i class="fa fa-cloud-upload" aria-hidden="true"></i> Cargar Datos UAM</p>
                  </a>
                </li> -->
              <!-- <li class="nav-item active ">
                  <a class="nav-link" href="{{path('cargar_inventario')}}">

                    <p><i class="fa fa-cloud-upload" aria-hidden="true"></i> Cargar Productos</p>
                  </a>
                </li> -->
              <!-- <li class="nav-item active ">
                  <a class="nav-link" href="{{path('r_Inventario_carga_inventario_bodega')}}">

                    <p><i class="fa fa-cloud-upload" aria-hidden="true"></i> Cargar Inventario</p>
                  </a>
                </li>-->
              <!-- <li class="nav-item active ">
                  <a class="nav-link" href="{{path('cargar_costos')}}">

                    <p><i class="fa fa-cloud-upload" aria-hidden="true"></i> Cargar Costos</p>
                  </a>
                </li>-->
            {% endif %}
            <!-- <li class="nav-item active ">
                    <a class="nav-link" href="{{path('corte_caja')}}">

                      <p><i class="fa fa-money" aria-hidden="true"></i> Corte de Caja</p>
                    </a>
                  </li>-->
            #}

            {% if is_granted('ROLE_VENDEDOR') %}
                <li class="nav-item active ">
                    <a class="nav-link" href="{{ path('nueva-venta') }}">

                        <p>
                            <i class="fa fa-opencart" aria-hidden="true"></i>
                            Nueva venta</p>
                    </a>
                </li>
            {% endif %}

        </div>


    {% endblock %}
