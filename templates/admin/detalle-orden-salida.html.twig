{% extends 'admin/layout.html.twig' %}
  {% block stylesheets %}
      {{parent()}}
      <link rel="stylesheet" href="//cdn.datatables.net/1.13.1/css/jquery.dataTables.min.css">
{% endblock %}
{% block sonata_admin_content %}

<div class="container text-center">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h2>Orden de Salida</h2>
                </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-condensed">
                                <tr>
                                    <th>
                                        Usuario responsable:
                                    </th>
                                    <td>
                                     {{traspasoalmacen.nombreUsuarioEnvia~" "~traspasoalmacen.apellidopaternoUsuarioEnvia~" "~traspasoalmacen.apellidomaternoUsuarioEnvia}}
                                    </td>
                                    <th>
                                        Fecha de traspaso:
                                    </th>
                                    <td>
                                    {{traspasoalmacen.creacion | date("d/m/Y  g:ia ","America/Mexico_City" )}}
                                    </td>
                                </tr>
                                <tr>
                                    <th>
                                        Sucursal salida:
                                    </th>
                                    <td>
                                        {{traspasoalmacen.sucursalSalida}}
                                    </td>
                                    <th>
                                        Sucursal destino:
                                    </th>
                                    <td>
                                        {{traspasoalmacen.sucursalDestino}}
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="100%">
                                        <strong>Nota: {{traspasoalmacen.notas}}</strong>
                                        <p></p>
                                    </td>
                                </tr>

                                <tr>
                                    <td colspan = "100%">
                                        <strong>Solicitud: 
                                        {% if traspasoalmacen.aceptada == 2 %}
                                            Pendiente
                                        {% elseif traspasoalmacen.aceptada == 1 %}
                                            Aceptado
                                        {% elseif traspasoalmacen.aceptada == 0 %}
                                            Negado
                                        {% else %}
                                            Desconocido
                                        {% endif %}
                                        </strong>
                                        <p></p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                       <div class="table-responsive text-start">
                           <table class="table table-striped tabla">
                               <thead>
                                    <tr>
                                        <th>Origen</th>
                                        <th>Destino</th>
                                        <th>Estado</th>
                                        <th>Modelo</th>
                                        <th>SKU</th>
                                        <th>Código de barras</th>
                                        <th>Marca</th>
                                        <th>Cantidad</th>

                                    </tr>
                               </thead>
                               <tbody>
                               {% for producto in productos %}
                                <tr>
                                    <td>{{traspasoalmacen.sucursalSalida}}</td>
                                    <td>{{traspasoalmacen.sucursalDestino}}</td>
                                    <td>
                                        {% if traspasoalmacen.aceptada == 2 %}
                                            Pendiente
                                        {% elseif traspasoalmacen.aceptada == 1 %}
                                            Aceptado
                                        {% elseif traspasoalmacen.aceptada == 0 %}
                                            Negado
                                        {% else %}
                                            Desconocido
                                        {% endif %}
                                    </td>
                                    <td>{{ producto.modelo }}</td>
                                    <td>{{ producto.SKU }}</td>
                                    <td>{{ producto.codigobarras }}</td>
                                    <td>{{ producto.marca }}</td>
                                    <td>{{ producto.cantidad }}</td>

                                </tr>
                               {% endfor %}
                               </tbody>
                           </table>
                       </div>
                            <div class="row justify-content-center mt-3">
                                <div class="col-md-8">
                                {% if estadoAceptada == '2' %}
                                    <label for="razonRechazo">Razón del rechazo:</label>
                                    <textarea id="razonRechazo" class="form-control" placeholder="Escriba la razón..."></textarea>
                                    <small id="errorRazon" class="text-danger d-none">Este campo es obligatorio para rechazar.</small>
                                    {% endif %}
                                </div>
                            </div>
                                </br>
                            <div class="row">
                                <div class="col-md-12 text-center">
                                    {% if estadoAceptada == '2' %}
                                        <button type="button" class="btn btn-success cambiar-estado" data-id="{{ ordenSalida }}" data-estado="1">Aceptar</button>
                                        <button type="button" class="btn btn-danger cambiar-estado" data-id="{{ ordenSalida }}" data-estado="0">Rechazar</button>
                                    {% elseif estadoAceptada == '1' %}
                                        <p>La orden ya fue aceptada.</p>
                                    {% elseif estadoAceptada == '0' %}
                                        <p>La orden fue rechazada.</p>
                                    {% else %}
                                        <p>Estado de orden desconocido.</p>
                                    {% endif %}
                                </div>
                            </div>
                    </div>
                </div>
            </div>
        </div>
</div>
{% endblock %}
{% block javascripts %}
{{parent()}}
    <script src="//cdn.datatables.net/1.13.1/js/jquery.dataTables.min.js"></script>
    <script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script><script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>

    <script>
        $(document).ready( function () {
            $('.tabla').DataTable({

                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.13.1/i18n/es-ES.json"
                },
                dom: 'Bfrtip',
                buttons: [
                    {
                        className: 'btn-primary btn',
                        filename: 'marca_data',
                        extend: 'excelHtml5',
                        text: 'Exportar excel',
                    }
                ]
            });
        } );
    </script>
    <script>
// Evento de clic en cualquier elemento con la clase .cambiar-estado
$(document).on('click', '.cambiar-estado', function(e) {
    e.preventDefault(); // Previene la acción predeterminada del evento de clic

    // Recupera los valores de los atributos de datos y campos del formulario
    var id = $(this).data('id');
    var estado = $(this).data('estado');
    var razonRechazo = $('#razonRechazo').val();
    var url = $("#url_orden_salida_aceptar").val();

    console.log(razonRechazo); // Registra la razón del rechazo en la consola

    // Verifica si la razón del rechazo es necesaria y si no se proporcionó
    if (estado === 0 && !razonRechazo.trim()) {
        $('#errorRazon').removeClass('d-none'); // Muestra el mensaje de error
        return; // Sale de la función
    } else {
        $('#errorRazon').addClass('d-none'); // Oculta el mensaje de error
    }

    // Realiza una solicitud AJAX al servidor
    $.ajax({
        url: '/traspaso/orden-salida/' + (estado === 1 ? 'aceptar/' : 'rechazar/') + id, // URL dependiendo del estado
        method: 'POST', // Método POST
        data:{
            nota: $('#razonRechazo').val() // Envía la razón del rechazo
        },
        success: function(response) { // Función de éxito
            if (response.exito) {
                swal("¡Bien hecho!", "Estado actualizado con éxito", "success"); // Alerta de éxito
                $('.cambiar-estado').prop('disabled', true);  // Deshabilita los botones
            } else {
                swal("¡UPS!", "Error al actualizar estado: " + response.msj, "error"); // Alerta de error con mensaje del servidor
            }
        },
        error: function() { // Función de error
            swal("¡Oh no!", "Error al hacer la solicitud al servidor", "error"); // Alerta de error de comunicación
        }
    });
});


    </script>
{% endblock %}



