<form id="custom-support-form">
    <label for="name">Nombre:</label>
    <input type="text" id="name" name="name">

    <label for="email">Correo Electrónico:</label>
    <input type="email" id="email" name="email">

    <label for="category">Categoría:</label>
    <select id="category" name="category">
        <option value="soporte_tecnico">Soporte Técnico</option>
        <option value="punto_de_venta">Punto de Venta</option>
        <option value="desarrollo_software">Desarrollo de Software</option>
        <option value="diseno">Diseño</option>
        <option value="administracion">Administración</option>
    </select>

    <!-- Añadir más campos personalizados aquí -->

    <label for="description">Descripción del Problema:</label>
    <textarea id="description" name="description"></textarea>

    <button type="submit">Enviar</button>
</form>

<script>
    document.getElementById('custom-support-form').addEventListener('submit', function(e) {
        e.preventDefault();
        // Aquí puedes hacer una llamada AJAX a la API de Zammad o al backend de tu aplicación
    });
</script>
