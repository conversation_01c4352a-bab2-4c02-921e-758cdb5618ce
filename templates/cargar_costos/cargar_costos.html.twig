{% extends 'admin/layout.html.twig' %}
{% block titleHead %}Cargar Costos | PV360 {% endblock %}
{% block title %}Cargar Costos {% endblock %}
{% block stylesheets %}
{{parent()}}
    <link href="{{asset('lib/jQuery-Upload-File/4.0.11/uploadfile.css')}}" rel="stylesheet">
    <link href="//cdn.datatables.net/1.11.4/css/jquery.dataTables.min.css" rel="stylesheet">

{% endblock %}
{% block content %}

<div class="container">
    <div class="card">
        <div class="card-header card-header-success">
            <h4 class="card-title">Cargar Costos</h4>
        </div>
        <div class="card-body">
            <input id="url-cargar-costos-subir-documento" type="hidden" value="{{path('carga-masiva-upload-doc')}}">

            <div class="row">
                <div class="col-md-12 col-sm-12 text-center">
                    <h4>Sube el documento para cambiar los costos </h4>
                    <a href="{{ asset('/plantillas-excel/plantilla-actualizar-costos-precios.xlsx') }}" class="btn "><img
                    src="{{ asset('img/icon-excel.png') }}" width="35" alt="">Descarga Plantilla</a>
                </div><!---fin de diov col-md-6-->
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div id="documento1"></div>
                </div>
                <div class="col-md-6 text-center">
                    <button  id="extrabutton" class="btn btn-success" >Procesar Documentos</button>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12 col-sm-12 text-center">
                    <div id="respuesta"></div>
                </div>
            </div>

        </div>
    </div>
</div>


{% endblock %}
{% block javascripts %}
{{parent()}}
    <script src="{{asset('lib/jQuery-Upload-File/4.0.11/jquery.uploadfile.min.js')}}"></script>
    <script src="//cdn.datatables.net/1.11.4/js/jquery.dataTables.min.js"></script>

    <script>


        var documento1Cargado1="";
        var documento1Cargado2="";
        var errorSubirDocumentos="";

        var archivo1Seleccionado1=false;
        var archivo1Seleccionado2=false;
        var nombreDocumento1="";
        var nombreDocumento2="";
        $(document).ready(function(){
            var url=$("#url-cargar-costos-subir-documento").val();
            var uploadObj=$("#documento1").uploadFile({
                url:url,
                multiple:true,
                dragDrop:true,
                fileName:"file1",
                showFileSize:true,
                showFileCounter:true,
                sequential:true,
                sequentialCount:1,
                acceptFiles:"application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                uploadStr:"Reporte",
                onSelect:function(files)
                {
                    $("#extrabutton").removeClass("d-none");
                },
                extraHTML:function()
                {
                    var d = new Date();
                    var n = d.getTime();
                    var id=Math.floor(Math.random() * 100000)+n;
                    var html = '<div id="'+id+'">';
                    html += "<input type='hidden' name='id' value='"+id+"' />";
                    html += "</div>";
                    return html;
                },
                onError: function(files,status,errMsg,pd)
                {
                    errorSubirDocumentos=true;
                    //files: list of files
                    //status: error status
                    //errMsg: error message
                    Swal.fire({
                        type: 'error',
                        title: files+" "+errMsg,
                        showConfirmButton: false,
                        timer: 1500
                    });
                },
                autoSubmit:false,
                onSubmit: function(){
                    $(".log").html("Procesando...");
                },
                onSelect:function(files){
                    files[0].name;
                    files[0].size;
                    console.log(files[0].name);
                    archivo1Seleccionado1=true;
                },
                onSuccess:function(files,data,xhr,pd)
                {
                    archivo1Seleccionado1=false;
                    //files: list of files
                    /*  console.log(JSON.stringify(files));
                      console.log(JSON.stringify(data));
                      console.log(JSON.stringify(xhr));
                      console.log(JSON.stringify(pd));*/
                    //data: response from server
                    //xhr : jquer xhr object
                    //  alert("listo");
                    console.log(data);

                    if(data.exito){
                        $("#"+data.idVista).html("Listo <br>Reporte: "+data.tipoDocumento);
                        $("#respuesta").html(data.html);
                        $("#"+data.idVista).closest(".ajax-file-upload-statusbar").addClass( "bg-success text-white" );


                        Swal.fire({
                            type: 'success',
                            title: "Documentos Procesados..",
                            showConfirmButton: true
                        });
                    }else{
                        $("#"+data.idVista).closest(".ajax-file-upload-statusbar").addClass( "bg-warning text-dark" );
                        $("#respuesta").html(data.msj);
                        $("#"+data.idVista).html(data.msj);
                        //  errorSubirDocumentos=true;
                        documento1Cargado1=false;
                        Swal.fire({
                            type: 'warning',
                            title: "Hubo un problema.."+data.msj,
                            showConfirmButton: true
                        });
                    }

                }
            });


            //validamos que no esten vacios los Documentos
            $("#extrabutton").click(function() {

                Swal.fire({
                    title: 'Espere Por favor..',
                    html: '<div class="log"></div>',
                    closeOnClickOutside: false,

                    onBeforeOpen: () => {
                        Swal.showLoading()

                    },
                    onClose: () => {
                        //  clearInterval(timerInterval)
                    }
                }).then((result) => {
                    console.log(result);


                });

                if (archivo1Seleccionado1 === false) {
                    console.log("Error al subir docuentos 1");
                    //if (documento1Cargado1 === false) {
                    console.log("error al subir documentos");

                    Swal.fire({
                        type: 'warning',
                        title: "Seleccione un documento por favor",
                        showConfirmButton: true
                    });

                    uploadObj.reset();
                    //  }
                } else{
                    uploadObj.startUpload();
                }
                console.log("entra aqui 2");
                //  if($("#documento1").val() !="" && $("#documento2").val() !=""){


                //   verificarCargaDocumentos();
                //  }else{
                /*Swal.fire({
                  title: 'Debe seleccionar los documentos',
                  animation: true,
                   type: 'warning',
                  customClass: {
                    popup: 'animated tada'
                  }
                });*/
                //}

            });
            function verificarCargaDocumentos(){
                $(".log").html("Verificando Carga");
                console.log("Entra a verificar carha de documentos");
                console.log("documento1Cargado1 "+documento1Cargado1);

                console.log("errorSubirDocumentos "+errorSubirDocumentos);

                if(archivo1Seleccionado1== true){
                    setTimeout(function(){
                        verificarCargaDocumentos()
                    },2000);
                    if((documento1Cargado1===true) && errorSubirDocumentos!==true){
                        unirDocumentos();

                    }else if(documento1Cargado1 === false){
                        console.log("Error al subir docuentos 1");
                        if(documento1Cargado1===false){
                            console.log("error al subir documentos");

                            Swal.fire({
                                type: 'warning',
                                title: "Error al subir documentos",
                                showConfirmButton: true
                            });

                            uploadObj.reset();
                        }
                    }else if(errorSubirDocumentos===true){
                        console.log("Error de sistema al subir docuentos 2");
                        Swal.fire({
                            type: 'error',
                            title: "Error de sistema al subir docuentos",
                            showConfirmButton: true
                        });
                    }else{

                        console.log("entra a else de verificar documentos");
                        setTimeout(function(){
                            verificarCargaDocumentos()
                        },2000);
                    }
                }else{
                    Swal.fire({
                        type: 'warning',
                        title: "Debe seleccionar los dos documentos",
                        showConfirmButton: true
                    });
                }




            }


            function unirDocumentos(){
                documento1Cargado1="";

                errorSubirDocumentos="";

                archivo1Seleccionado1=false;



//     uploadObj.reset();

                $(".log").html("Uniendo Documentos");
                Swal.fire({
                    type: 'success',
                    title: "Documentos Procesados..",
                    showConfirmButton: true
                });
                //crearExcel();

            }
        });


        function crearExcel(){
//me quede aqui
            var url=$("#url-crear-excel").val();
            $.ajax({
                url: url,
                data:{nombreDocumento1:nombreDocumento1,nombreDocumento2:nombreDocumento2}
            }).done(function( data ) {
                console.log(data);
                console.log(data.exito);
                if(data.exito==true){

                    Swal.fire({
                        type: 'success',
                        title: "Documentos Subidos exitosamente",
                        showConfirmButton: true,
                        html:
                            '<div class="resultado">' +
                            '</div>',
                    });
                }else{
                    Swal.fire({
                        type: 'warning',
                        title: data.msj,
                        showConfirmButton: true
                    });
                }
                $(".resultado").html('<a terget="new" href="{{asset(uploads_front~"/")}}'+data.nombreFinal+'">Descargar</a> ');
            }).fail(function(jqXHR, textStatus, errorThrown ) {
                Swal.fire({
                    type: 'error',
                    title: JSON.stringify(jqXHR)+JSON.stringify(textStatus)+JSON.stringify(errorThrown),
                    showConfirmButton: true
                });
            })
        }



    </script>
{% endblock %}
