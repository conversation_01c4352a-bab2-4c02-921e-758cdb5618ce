{% if existSchool %}
<div class="row mb-3 justify-content-center test">
    <div class="col-md-3 col-5 d-flex align-items-center">
        <img class="logo mx-auto" src="{{schoolLogo}}" />
    </div>
    <div class="col-md-3 col-7 d-flex align-items-center">
        <img class="logo mx-auto" src="{{ asset('img/newOptimoLogo.png') }}" />
    </div>
</div>
<div class="row justify-content-between align-items-center mb-2">
    <h2 class="m-0 fw-bold title-form col-sm-9 fs-md-1 fs-4">Registro {{schoolTitle}}</h2>
    <div class="col-sm-3">
        <button class="form-button p-md-2 p-1 mt-sm-0 mt-3" onclick="clearForm()">
            <label class="m-0 d-xl-block d-none">Limpiar campos</label>
            <i class="fa-solid fa-trash d-block d-xl-none"></i>
        </button>
    </div>
</div>

{{ form_start(form, {'attr': {'id': 'uam-form', 'enctype': 'multipart/form-data' }}) }}

<div class="row">

    <div class="col-xl-4">
        {{ form_row(form.nombre) }}
    </div>
    <div class="col-xl-4">
        {{ form_row(form.apellidopaterno) }}
    </div>
    <div class="col-xl-4">
        {{ form_row(form.apellidomaterno) }}
    </div>

</div>

<div class="row">

    <div class="col-xl-3">
        {{ form_row(form.unidadIdunidad) }}
    </div>
    <div class="col-xl-4">
        {{ form_row(form.ocupacion) }}
    </div>
    <div class="col-xl-2">
        {{ form_row(form.schoolyearsemester) }}
    </div>
    <div class="col-xl-3">
        {{ form_row(form.numeroempleado) }}
    </div>

</div>

<div class="row">

    <div class="col-xl-2 col-md-6">
        {{ form_row(form.edad) }}
    </div>
    <div class="col-xl-2 col-md-6">
        {{ form_row(form.genero) }}
    </div>
    <div class="col-xl-4">
        {{ form_row(form.telefono) }}
    </div>
    <div class="col-xl-4">
        {{ form_row(form.email) }}
    </div>

</div>

<div class="row">

    <div class="col-xl-4 col-md-6">
        {{ form_row(form.calle) }}
    </div>
    <div class="col-xl-2 col-md-6">
        {{ form_row(form.numero) }}
    </div>
    <div class="col-xl-3">
        {{ form_row(form.colonia) }}
    </div>
    <div class="col-xl-3">
        {{ form_row(form.codigopostal) }}
    </div>

</div>

<div class="row">
    
    {% for index, question in questions %}
    <div class="col-12">
        <label class="form-label mt-3" for="{{index}}">{{question}}</label>
        <input type="text" id="{{index}}" name="{{question}}" class="form-control mapped-question" required>
    </div>
    {% endfor %}

</div>

<div class="row">
    <div class="col-12 ">
        {{ form_row(form.Enviar) }}
    </div>
</div>
{{ form_widget(form) }}
{{ form_end(form) }}

<script>


    $(document).ready(function(){
        
        {% if success %}
            Swal.fire({
                title: "Registro guardado correctamente",
                text: "Para seguir con el proceso debe realizarse el exámen visual",
                icon: "success"
            });
            $(':input','#uam-form')
                .not('select, :hidden')
                .val('')
                .prop('checked', false)
        {% endif %}
        
    })

    $("#uam-form").submit(function(e) {
        e.preventDefault();
        changeButton("uam_Enviar", 0);

        var formData = new FormData(this);

        /*for (var key of formData.keys()) {
            console.log(key);
        }*/

        const schoolId = $("#uam_numeroempleado").val();

        $.ajax({
            url: "{{path('uam-get-uam-form')}}/"+currSchool+"?schoolId="+schoolId,
            type: 'POST',
            data: formData,
            success: function (html) {

                changeButton("uam_Enviar", 1);
                $("#uam-form-container").html(html);

            },
            cache: false,
            contentType: false,
            processData: false
        });
           
    });

    function clearForm(){
        $(':input','#uam-form')
        .not(':button, :submit, :reset, :hidden, select, :hidden')
        .val('')
        .prop('checked', false)
    }


</script>
{% endif %}