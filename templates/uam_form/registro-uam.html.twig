{% extends 'layout-form.html.twig' %}
{% block title %}REGISTRO {% endblock %}

{% block content %}
    <section class="login-container"></section>
    <div class="container-fluid">
        <div class="page row">
            <div class="form-box col-md-6 col-10 mx-auto vertical-center">
                {% if existSchool %}
                    <div id="uam-form-container"></div>
                {% else %}
                    <div class="">
                        <h2 class="m-0 fw-bold title-form fs-md-1 fs-4 text-center">No se encontró el formulario</h2>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

{% endblock %}

{% block scripts %}
    {{parent()}}
    <script>

        var currSchool = "{{school}}"

        $(document).ready(function(){
            {% if existSchool %}
                getUAMForm()
            {% endif %}
        });

        function getUAMForm(){
            $("#uam-form-container").removeClass("p-5");
            $.ajax({
                url: "{{path('uam-get-uam-form')}}/"+currSchool, 
                type: 'GET',
                beforeSend: loadingGif("uam-form-container"),
                dataType: "html"
            }).done(function( html ) {
                $("#uam-form-container").addClass("");
                $("#uam-form-container").html(html);

            }).fail(function() {
                alert( "error" );
            });
        }

    </script>

{% endblock %}