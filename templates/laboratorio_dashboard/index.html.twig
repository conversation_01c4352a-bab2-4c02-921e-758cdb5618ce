{% extends 'admin/layout.html.twig' %}
{% block title %}Dashboard | Laboratorio{% endblock %}
{% block content %}
	<link rel="stylesheet" href="{{ asset('/lib/jQuery-Autocomplete-master/content/styles.css') }}">
	<link rel="stylesheet" href="{{ asset('/css/puntodeventa/laboratorio.css') }}">
	<link rel="stylesheet" href="{{ asset('lib/dataTables/dataTables.min.css') }}">
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900&display=swap" rel="stylesheet">
	<link rel="stylesheet" href="{{ asset('lib/fontawesome-free-5.9.0-web/css/all.css') }}">

	<style>
		.loading-container {
			display: flex;
			justify-content: center;
			align-items: center;
		}
	</style>

	<div class="container-fluid">
		<div class="row">
			<h1 class="dashboard-title justify-content-center">LABORATORIO</h1>
			<div class="loading-container col-12">
				<div id="dashboardTable"></div>
			</div>
		</div>
	</div>

{% endblock %}

{% block javascripts %}
	{{ parent() }}

	<script>
		$(document).ready(function () {
			dashboardTable();
		});

		function dashboardTable() {
			$.ajax({
				url: "{{ path('app_laboratorio_dashboardD') }}",
				type: 'GET',
				beforeSend: function () {
					$("#dashboardTable").html('<img src="/img/dashboard.gif" alt="Loading..." />');
				},
				dataType: "html"
			}).done(function (html) {
				$("#dashboardTable").html(html);
			}).fail(function () {
				alert("error");
			});
		}
	</script>

	<script src="{{ asset('js/jquery.formatCurrency-1.4.0.pack.js') }}"></script>
	<script src="{{ asset('lib/jQuery-Autocomplete-master/dist/jquery.autocomplete.min.js') }}"></script>
	<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/html2canvas@1.0.0-rc.1/dist/html2canvas.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.debug.js" integrity="sha384-NaWTHo/8YCBYJ59830LTz/P4aQZK1sS0SneOgAvhsIl3zBu8r9RevNg5lHCHAuQ/" crossorigin="anonymous"></script>
	<script src="{{ asset('lib/dataTables/dataTables.min.js') }}"></script>
{% endblock %}
