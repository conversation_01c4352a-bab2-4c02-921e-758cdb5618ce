<div class="row d-flex justify-content-center" style="overflow-y: scroll;">
	<div class="col-12 d-flex flex-row justify-content-center">
		<div class="col-2 laboratorio-colum">
			<h2 class="dashboard-subtitle">NÚMERO DE ORDEN</h2>
		</div>
		<div class="col-2 laboratorio-colum">
			<h2 class="dashboard-subtitle">CLIENTE</h2>
		</div>
		<div class="col-2 laboratorio-colum">
			<h2 class="dashboard-subtitle">ARMAZÓN</h2>
		</div>
		<div class="col-2 laboratorio-colum">
			<h2 class="dashboard-subtitle">ESTADO</h2>
		</div>
		<div class="col-2 laboratorio-colum">
			<h2 class="dashboard-subtitle">BISELADOR</h2>
		</div>
		<div class="col-2 laboratorio-colum">
			<h2 class="dashboard-subtitle">ETAPA</h2>
		</div>
	</div>
	{% for index, ol in laboratoryOrders %}
		<div class="col-12 d-flex flex-row justify-content-center mb-2">
			<div class="col-2 laboratorio-columna">
				<p class="dashboard-text">{{ index + 1 }}</p>
			</div>
			<div class="col-2 laboratorio-columna">
				<p class="dashboard-text">{{ ol.fullName }}</p>
			</div>
			<div class="col-2 laboratorio-columna">
				{% if mappedProducts[ol.idordenlaboratorio] is defined %}
					{% for mappedProduct in mappedProducts[ol.idordenlaboratorio] %}
						<p class="dashboard-text">MARCA: {{ mappedProduct.marca }}</p>
						<p class="dashboard-text">MODELO: {{ mappedProduct.modelo }}</p>
						<p class="dashboard-text">DESCRIPCIÓN: {{ mappedProduct.descripcion }}</p>
					{% endfor %}
				{% endif %}
				<a type="button" class="btn btn-detail" data-toggle="modal" data-target="#armazonModal{{ index }}">DETALLES</a>
			</div>
			<div class="col-2 laboratorio-columna">
				<p class="dashboard-text">{{ orderStages[ol.stage - 1] }}</p>
			</div>
			<div class="col-2 laboratorio-columna">
				<p class="dashboard-text">ENCARGADO</p>
			</div>


			<div class="col-2 laboratorio-columna">
                {% if ol.stage < 6 %}
                    <button class="m-1 btn btn-success" onclick="changestage('{{ol.idordenlaboratorio}}', '6')">
                        Materiales Recibidos
                    </button>
                    <button class="m-1 btn btn-danger" data-toggle="modal" data-target="#lab-dashboard-modal" onclick="orderProductsRevision('{{ol.idordenlaboratorio}}', '3')">
                        Daño o extravio
                    </button>
                {% elseif ol.stage < 7 %}
                    <button class="m-1 btn btn-success" onclick="changestage('{{ol.idordenlaboratorio}}', '7')">
                        Empezar
                    </button>
                    <button class="m-1 btn btn-danger" data-toggle="modal" data-target="#lab-dashboard-modal" onclick="orderProductsRevision('{{ol.idordenlaboratorio}}', '3')">
                        Daño o extravio
                    </button>
                {% elseif ol.stage < 8 %}
                    <h3 class="time-display dashboard-text" id="timeDisplay{{ index }}">{{ '00:00:00' }}</h3>
                    <button class="m-1 btn btn-success" onclick="changestage('{{ol.idordenlaboratorio}}', '9')">
                        Mandar a {{orderStages[8]}}
                    </button>
                    <button class="m-1 btn btn-primary" onclick="changestage('{{ol.idordenlaboratorio}}', '8')">
                        Pausar
                    </button>

                {% elseif ol.stage < 9 %}
                    <h3 class="time-display dashboard-text" id="timeDisplayStatic{{ index }}">{{ '00:00:00' }}</h3>
                    <button class="m-1 btn btn-primary" onclick="changestage('{{ol.idordenlaboratorio}}', '7')">
                        Reanudar
                    </button>

                    <button class="m-1 btn btn-danger dashboard-text" data-toggle="modal" data-target="#lab-dashboard-modal" onclick="orderProductsRevision('{{ol.idordenlaboratorio}}', '3')">
                        Daño o extravio
                    </button>
                {% elseif ol.stage < 10 %}
                    <button class="m-1 btn btn-success" onclick="changestage('{{ol.idordenlaboratorio}}', '10')">
                        Aprobar
                    </button>
                    <button class="m-1 btn btn-danger" data-toggle="modal" data-target="#lab-dashboard-modal" onclick="orderProductsRevision('{{ol.idordenlaboratorio}}', '3')">
                        Daño o extravio
                    </button>
                    <button class="m-1 btn btn-primary" data-toggle="modal" data-target="#lab-dashboard-modal" onclick="orderProductsRevision('{{ol.idordenlaboratorio}}', '6')">
                        Regresar a Laboratorio
                    </button>
                {% elseif ol.stage == 10 %}
                    <h3 class="dashboard-text"><strong>Aprobado </strong></h3>
                    <p class="dashboard-text">Sin Opciones. en espera de envio.</p>
                {% endif %}
            </div>
		</div>
	{% endfor %}
</div>

{% for index, ol in laboratoryOrders %}
	<!-- Modal de Ventas-->
	<div class="mod" id="armazonModal{{index}}" tabindex="-1" aria-labelledby="" aria-hidden="true">
		<div class="mod-dialog modal-dialog-scrollable modal-dialog-centered">
			<div class="modal-content" style="border-radius:10px; box-shadow: 0 5px 15px rgba(0, 0, 0, .5);">
				<div class="modal-header">
					<h1 class="modal-title">ORDEN DE LABORATORIO y DATOS DE ORDEN</h1>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body bg-primary p-0 d-flex flex-column align-items-center">
					<div class="row col-12 justify-content-center">
						<div class="col-12 section d-flex">
							<h4 class="table-title ms-5">Paciente: {{ol.fullName}}</h4>
						</div>
					</div>
					<hr>
					<div class="row col-12 justify-content-center">
						<div class="col div-con-borde-derecho">
							<h4 class="table-title">Información del Solicitante:</h4>
							<p class="table-title">Graduación:</p>
							<p class="table-title">Miopia:</p>
							<p class="table-title">Astigmatismo: </p>
							<p class="table-title">Hipermetropia:</p>
							<div class="d-flex align-items-center">
								<img class="icon-orden" src="/img/doc.png" alt="Revisar documento">
								<p class="table-title m-0">Revisar documento</p>
							</div>
						</div>
						<div class="col-8 d-flex flex-column justify-content-center section">
							<h4 class="table-title">Descripción:</h4>
							<p class="table-title"></p>
							<h4 class="table-title">Nota:</h4>
							<p class="table-title"></p>
							<div class="row">
								<div class="col">
									<h4 class="table-title dashboard-text">Ojo Izquierdo</h4>
									<table class="table">
										<tr>
											<th class="text-start dashboard-text">Esfera</th>
											<td class="dashboard-text">{{ol.esferaoi}}</td>
										</tr>
										<tr>
											<th class="text-start dashboard-text">Cilindro</th>
											<td class="dashboard-text">{{ol.cilindrooi}}</td>
										</tr>
										<tr>
											<th class="text-start dashboard-text">Eje</th>
											<td class="dashboard-text">{{ol.ejeoi}}</td>
										</tr>
									</table>
								</div>
								<div class="col">
									<h4 class="table-title dashboard-text">Ojo Derecho</h4>
									<table class="table">
										<tr>
											<th class="text-start dashboard-text">Esfera</th>
											<td class="dashboard-text">{{ol.esferaod}}</td>
										</tr>
										<tr>
											<th class="text-start dashboard-text">Cilindro</th>
											<td class="dashboard-text">{{ol.cilindrood}}</td>
										</tr>
										<tr>
											<th class="text-start dashboard-text">Eje</th>
											<td class="dashboard-text">{{ol.ejeod}}</td>
										</tr>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
{% endfor %}

<script>
    var laboratoryOrders = {{ laboratoryOrders|json_encode|raw }};
    var mappedProducts = {{ mappedProducts|json_encode|raw }};
    var eventQuery = {{ eventQuery|json_encode|raw }};

    $(document).ready(function(){

        $('#order-laboratory-config-table').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ ordenes por página',
            },
            "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
            responsive: false
        });


    });

    var intervalId = setInterval(function() {
        for (var index = 0; index < laboratoryOrders.length; index++) {

            var ol = laboratoryOrders[index];

            var displayId = 'timeDisplay' + index;

            eventQuery[ol.idordenlaboratorio] += 1;
            // Find the distance between now and the count down date
            var initialValue = eventQuery[ol.idordenlaboratorio];

            var displayElement = document.getElementById(displayId);
            if (displayElement) {
                displayElement.textContent = formatTime(initialValue);
            }

        }
    }, 1000);

    for (var index = 0; index < laboratoryOrders.length; index++) {

        var ol = laboratoryOrders[index];

        var displayId = 'timeDisplayStatic' + index;

        // Find the distance between now and the count down date
        var initialValue = eventQuery[ol.idordenlaboratorio];

        var displayElement = document.getElementById(displayId);
        if (displayElement) {
            displayElement.textContent = formatTime(initialValue);
        }

    }

    function formatTime(seconds) {
        var hours = Math.floor(seconds / 3600);
        var minutes = Math.floor((seconds % 3600) / 60);
        var remainingSeconds = seconds % 60;

        return (
            (hours < 10 ? '0' : '') + hours + ':' +
            (minutes < 10 ? '0' : '') + minutes + ':' +
            (remainingSeconds < 10 ? '0' : '') + remainingSeconds
        );
    }

    // Function to update a specific time display
    function updateTimeDisplay(displayId, initialValue) {
        // Get the current time from the displayed element
        var currentTime = parseInt(document.getElementById(displayId).textContent);

        // Increment the time by 1 second
        currentTime++;

        // Format the time and update the displayed time
        document.getElementById(displayId).textContent = formatTime(currentTime);
    }

    function orderProductsRevision(updateorderid, stage){

        $.ajax({
            url: "{{path('app_lab_dashboard_order_products')}}",
            type: 'POST',
            data: {updateorderid:updateorderid, stage:stage},
            beforeSend: loadingGif("lab-dashboard-result"),
            dataType: "html"
        }).done(function( html ) {
            $("#lab-dashboard-result").html(html);
        }).fail(function() {
            alert( "error" );
        });
    }

    function updateProduct(updateid, updateorderid, value, revisionid){

        clearInterval(intervalId);

        const textarea = document.getElementById(revisionid);

        // Get the value of the textarea
        const textareaValue = textarea.value;

        $.ajax({
            url: "{{path('laboratory-dashboard-updatestocklabord')}}",
            type: 'POST',
            data: {updateid:updateid, updateorderid:updateorderid, revision:textareaValue, value:value},
            beforeSend: loadingGif("lab-dashboard-result"),
            dataType: "html"
        }).done(function( html ) {
            $("#lab-dashboard-result").html(html);
        }).fail(function() {
            alert( "error" );
        });
    }


    function changestage(updateid, num){

        clearInterval(intervalId);

        var comment = $('#changestage-comment-textarea').val() ?? 'Sin comentarios';


        $.ajax({
            url: "{{path('laboratory-order-changestage')}}",
            type: 'POST',
            data: {updateid:updateid, num:num, comment:comment},
            beforeSend: loadingGif("laboratory-order-change-dashboard"),
            dataType: "html"
        }).done(function( html ) {
            $("#laboratory-dashboard-table-container").html(html);
        }).fail(function() {
            alert( "error" );
        });
    }

    // Function to format time as HH:mm:ss

</script>
