<input type="hidden" name="tableCustomerController" id="tableCustomer" value="{{ path('table-Customer') }}" />
<div class="row justify-content-center">
  <div class="container boxShadow text-center">
    <div class="col-md-12">
      <div class="container boxShadow">
        <div class="row justify-content-center align-items-center">
          <h3 class="text-center"><i class="fas fa-user"></i>Selección de Clientes</h3>
        </div>
      </div>
      <br />
      <div class="row">
        <div class="col-12 col-md-6 col-lg-4 buscar">
          <label for="BuscarApellidoP_" class="form-label text">Apellido paterno:</label>
          <input id="BuscarApellidoP" class="form-control" type="text" />
        </div>
        <div class="col-12 col-md-6 col-lg-4 buscar">
          <label for="BuscarApellidoM_" class="form-label text">Apellido materno:</label>
          <input id="BuscarApellidoM" class="form-control" type="text" />
        </div>
        <div class="col-12 col-md-6 col-lg-4 buscar">
          <label for="BuscarNombre_" class="form-label text">Nombre(s):</label>
          <input id="BuscarNombre" class="form-control" type="text" />
        </div>
        <div class="col-12 col-md-6 col-lg-4 buscar">
          <label for="BuscarEmail_" class="form-label text">Correo electrónico:</label>
          <input id="BuscarEmail" class="form-control" type="text" />
        </div>
        <div class="col-12 col-md-6 col-lg-4 buscar">
          <label for="BuscarTelefono_" class="form-label text">Teléfono:</label>
          <input id="BuscarTelefono" class="form-control" type="text" />
        </div>
        <div class="col-12 col-md-6 col-lg-4 buscar">
          <label for="BuscarNumeroEmpleado_" class="form-label text">Número de empleado:</label>
          <input id="BuscarNumeroEmpleado" class="form-control" type="text" />
        </div>
        <div class="row">
          <div class="col-md-4">
            <div class="form-group">
              <label for="tipoCliente" class="subtext">Tipo de Cliente:</label>
              <select name="tipoCustomer" id="tipoCliente" class="form-control">
                <option value="-1" selected="selected">Selecciona el Tipo de Cliente</option>
                {% for filterss in filters %}
                  <option value="{{ filterss.idempresacliente }}">{{ filterss.nombreEmpresaCliente }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              <label for="ageSliderLabel" class="subtext">Rango de Edad:</label>
              <br />
              <div style="padding-top: 8px;">
                <input id="ageSlider" type="text" data-provide="slider" data-slider-min="18" data-slider-max="85" data-slider-step="1" data-slider-value="[18,85]" />
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              <label class="subtext">Filtrar por fecha de cotización:</label>
              <br />
              <div class="row justify-content-between align-items-center">
                <div class="col-6 text-center">
                  <input type="text" class="text-center date-input form-control" autocomplete="off" placeholder="Fecha de inicio" id="start-date-parameter" />
                </div>
                <div class="col-6 text-center">
                  <input type="text" class="text-center date-input form-control" autocomplete="off" placeholder="Fecha de fin" id="end-date-parameter" />
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              <label for="location-select" class="subtext">Sucursal de la venta:</label>
              <select name="location-select" id="location-select" class="form-control">
                <option value="-1" selected="selected">Selecciona la sucursal de venta</option>
                {% for location in locations %}
                  <option value="{{ location.idsucursal }}">{{ location.nombre }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-4" style="padding-top: 33px;">
            <a class="btn btn-primary searchCustomer" onclick="tableCustomer();">
              <i class="fas fa-search"></i>
              Buscar Clientes
            </a>
          </div>
        </div>
        </div>
      </div>
    </div>
  </div>
  <br>
  <div id="tableCustom"></div>
</div>

<script>
  jQuery(function ($) {
    $('.date-input').datetimepicker({
      pickTime: false,
      pickDate: true,
      minDate: '1/1/1900',
      maxDate: null,
      showToday: true,
      language: 'es_MX',
      defaultDate: '',
      disabledDates: [],
      enabledDates: [],
      icons: {
        time: 'fa fa-clock-o',
        date: 'fa fa-calendar',
        up: 'fa fa-chevron-up',
        down: 'fa fa-chevron-down'
      },
      useStrict: false,
      sideBySide: false,
      daysOfWeekDisabled: [],
      collapse: true,
      calendarWeeks: false,
      viewMode: 'days',
      minViewMode: 'days',
      useCurrent: false,
      useSeconds: false
    })
  })
  
  $(document).ready(function () {
    $('#ageSlider').slider()
  })
  
  // tabla cliente
  
  function tableCustomer() {
    var url = $('#tableCustomer').val()
  
    var tipoCustomer = $('#tipoCliente').val()
  
    var rangoEdad = $('#ageSlider').val()
    var apellidoPaterno = $('#BuscarApellidoP').val()
    var apellidoMaterno = $('#BuscarApellidoM').val()
    var nombre = $('#BuscarNombre').val()
    var correoElectronico = $('#BuscarEmail').val()
    var telefono = $('#BuscarTelefono').val()
    var numeroEmpleado = $('#BuscarNumeroEmpleado').val()
    var dateStart = $('#start-date-parameter').val()
    var dateEnd = $('#end-date-parameter').val()
    var location = $('#location-select').val()
  
    var dataSend = {
      tipoCustomer: tipoCustomer,
      rangoEdad: rangoEdad,
      apellidoPaterno: apellidoPaterno,
      apellidoMaterno: apellidoMaterno,
      nombre: nombre,
      correoElectronico: correoElectronico,
      telefono: telefono,
      numeroEmpleado: numeroEmpleado,
      dateStart: dateStart,
      dateEnd: dateEnd,
      location: location
    }
  
    $.ajax({
      url: url,
      type: 'GET',
      dataType: 'html',
      data: dataSend,
      beforeSend: function () {
        loadingGif('tableCustom')
      }
    })
      .done(function (html) {
        $('#tableCustom').html(html)
      })
      .fail(function () {
        alert('Hubo un error al cargar los datos.')
      })
  }
  
  function loadingGif(elementId) {
    $('#' + elementId).html('<div align="center" style="margin-top: 20px;"><img style="max-width: 100%;" src="/img/log.gif"></div>')
  }
</script>
