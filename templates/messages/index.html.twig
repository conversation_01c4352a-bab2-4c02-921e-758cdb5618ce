{% extends 'admin/layout.html.twig' %}

{% block title %}| Mensajes
{% endblock %}

{% block content %}
	<input id="url-dashboard-messages" type="hidden" value="{{ path('dashboard_messages') }}">

	<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header card-header-info bg-primary">
                    <div class="row">
                        <div class="col-md-1">
                            
                        </div>
                        <div class="col-md-11">
                            <h4 class="card-title"><PERSON><PERSON> de Mensajes</h4><i class="fas fa-envelope"></i>
                        </div>
                    </div>
                </div>
                <br>
                <div class="card-body" id="dashboard"></div>
            </div>
        </div>
    </div>
</div>

	<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
	<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.3/css/jquery.dataTables.css">
	<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-slider/11.0.2/css/bootstrap-slider.min.css" rel="stylesheet">
 <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.js"></script>
	 <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-slider/11.0.2/bootstrap-slider.min.js"></script>
	 <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
	 <script src="https://cdn.tiny.cloud/1/j6o46rt0i77l3o8r6xg40npvdj14x3qeyzvnwvloi8z6vudb/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>


	 <script>
	
	
	$(document).ready(function () {
	dashboardMessages();
	});
	
	function dashboardMessages() {
	
	let url = $("#url-dashboard-messages").val();
	
	$.ajax({url: url, type: 'GET', data: {}, dataType: "html"}).done(function (html) {
	$("#dashboard").html(html);
	}).fail(function () {
	alert("Error dashboard");
	});
	
	}
		</script>

{% endblock %}
