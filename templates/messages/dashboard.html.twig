<style>

#smsArea:focus, #emailEditor:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Mejoras visuales para los botones */
#selectFilters {
    border: none;
    background-image: linear-gradient(to right, #007bff, #0056b3);
    color: white;
    padding: 10px 30px;
    border-radius: 5px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease;
}

#selectFilters:hover {
    background-image: linear-gradient(to right, #0056b3, #004085);
}

/* Mejoras visuales para los botones */
#selectFilters {
    border: none;
    background-image: linear-gradient(to right, #007bff, #0056b3);
    color: white;
    padding: 10px 30px;
    border-radius: 5px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease;
}

#selectFilters:hover {
    background-image: linear-gradient(to right, #0056b3, #004085);
}
</style>

<link rel="stylesheet" href="{{ asset('/css/messagesMarketing.css') }}">
<input type="hidden" name="filters_messages" id="filters_messages" value="{{path('filters-messages')}}">
<div class="table-responsive">
	<div class="container boxShadow">
		<hr>
		<div class="row justify-content-between align-items-center">
			<div class="col">
				<h3>Selección de Contactos</h3>
			</div>
			<div class="col-auto">
				<a href="#" class="btn btn-primary" id="selectFilters" data-toggle="modal" data-target="#customerModal" style="padding: 10px 70px;">
					<i class="fas fa-search"></i>
					Buscar
				</a>
			</div>
		</div>
		<hr>
		<div class="container boxShadow">
			<table id="tableUsuarios" class="table mx-auto">
				<thead>
					<tr class="text-start">
						<th>Nombre</th>
						<th>Edad</th>
						<th>Número de Empleado</th>
						<th>Teléfono</th>
						<th>Tipo Cliente</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td class="text-center align-middle">.</td>
						<td class="text-center align-middle">.</td>
						<td class="text-center align-middle">.</td>
						<td class="text-center align-middle">.</td>
						<td class="text-center align-middle">.</td>
					</tr>
				</tbody>
				<tfoot>
					<tr>
						<th>Nombre</th>
						<th>Edad</th>
						<th>Número de Empleado</th>
						<th>Teléfono</th>
						<th>Tipo Cliente</th>
					</tr>
				</tfoot>
			</table>
		</div>
		<br>
		<br>
		<div class="container boxShadow">
			<div class="row align-items-center">
				<div class="col-md-6">
					<h3>Selecciona una Opción</h3>
					<select class="form-control" id="messageType" onchange="handleMessageTypeChange()">
						<option value="">-- Elige una opción --</option>
						<option value="sms">SMS</option>
						<option value="email">EMAIL</option>
					</select>
				</div>
				<div class="col-md-6">
				<br>
				<br>
					<a href="#" class="btn btn-success" id="selectMessages" onclick="enviarMensajesAClientesSeleccionados();">
						<i class="fas fa-search"></i>
						Enviar Mensajes
					</a>
				</div>
			</div>
			<br>
			<div class="row align-items-center">
				<div class="col-md-6">
					<textarea id="smsArea" style="display: none;" class="form-control" placeholder="Ingresa tu mensaje SMS aquí..."></textarea>
					<textarea id="emailEditor" style="display: none;" class="form-control" placeholder="Redacta tu email aquí..."></textarea>
				</div>
			</div>
		</div>
	</div>
</div>


<div class="mod fade" id="customerModal" tabindex="-1" role="dialog" aria-labelledby="customerModalLabel" aria-hidden="true">
	<div class="mod-dialog modal-xl custom-modal-xl" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="customerModalLabel">Datos del Cliente</h5>
				<button type="button" class="close" data-dismiss="modal" aria-label="Cerrar">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-body">
				<br>
				<div class="container boxShadow ">
					<div class="row justify-content-center">
						<div class="filters" id="filters"></div>
					</div>
					<br>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
			</div>
		</div>
	</div>
</div></div></div>

<script>
	var customerSelect = [];
	
	$(document).ready(function () {
	$('#tableUsuarios').DataTable();
	});
	
	var urlFilters = $("#filters_messages").val();
	
	$("#selectFilters").click(function () {

	$.ajax({
		url: urlFilters,
		type: 'GET',
		dataType: 'html',
		data: {}
		}).done(function (html) {
			$("#filters").html(html);
		}).fail(function () {
			alert("ERROR CON FILTROS");
		})
	});
	
	function enviarMensajesAClientesSeleccionados() {
		const messageId = $("#message-select").val();
		var sms_area = $("#smsArea").val();
		var email_area = tinymce.get('emailEditor') ? tinymce.get('emailEditor').getContent() : '';
		
		if (customerSelect.length > 0) {
			console.log("customerSelect");

			if (sms_area == "" || email_area == "") {
				$.ajax({
					url: '/send-messages',
					type: 'POST',
					data: {
						clientIds: customerSelect,
						sms_area: sms_area,
						email_area: email_area
					},
					success: function (response) {
						Swal.fire({title: "¡Listo!", text: "Tus mensajes fueron enviados correctamente", icon: "success"});
					},
					error: function (error) {
						console.error(error);
					}
				});
			} else {
				Swal.fire({title: "¡Cuidado!", text: "Necesitas escribir un mensaje válido", icon: "warning"});
			}
		} else {
			Swal.fire({title: "¡Cuidado!", text: "Necesitas seleccionar al menos un cliente", icon: "warning"});
		}
	}

	function initTinyMCE() {
			tinymce.init({
				selector: '#emailEditor',
				plugins: 'link image code',
				toolbar: 'undo redo | bold italic | alignleft aligncenter alignright | code',
				setup: function(editor) {
					editor.on('init', function(e) {
						console.log('Editor is initialized.');
						editor.setContent('');
					});
				}
			});
		}
		
		function handleMessageTypeChange() {
			var messageTypeSelect = document.getElementById('messageType');
			var smsArea = document.getElementById('smsArea');
			var emailEditor = document.getElementById('emailEditor');
		
			smsArea.style.display = 'none';
			emailEditor.style.display = 'none';
		
			if (tinymce.get('emailEditor')) {
				tinymce.get('emailEditor').remove();
			}
		
			if (messageTypeSelect.value === 'sms') {
				smsArea.style.display = 'block';
			} else if (messageTypeSelect.value === 'email') {
				emailEditor.style.visibility = 'hidden';
				emailEditor.style.position = 'absolute';
				emailEditor.style.display = '';
				setTimeout(function() {
					emailEditor.style.visibility = 'visible';
					emailEditor.style.position = 'static';
					initTinyMCE();
				}, 0);
			}
		}
	</script>
