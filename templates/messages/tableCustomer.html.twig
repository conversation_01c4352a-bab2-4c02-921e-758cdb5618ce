<br>
<div class="row justify-content-center">
<div class="container boxShadow">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="row justify-content-between align-items-center">
                <div class="col-md-4">
                    <h3 class="totalSeleccionadosTittle">Total Seleccionados: <span id="totalSeleccionados">0</span></h3>
                </div>
                <div class="col-md-4">
                <div class="form-group text-center">
                    <a class="btn btn-success selectCustomer" onclick="confirmarSeleccion()">
                        <i class="fa-solid fa-check"></i>
                        Confirmar Contactos</a>
                </div>
                </div>
                <div class="col-md-4 text-end">
                    <label for="SeleccionarTodo">Seleccionar Todo</label>
                    <input type="checkbox" name="" id="checkAllCustomer">
                </div>
            </div>
            <br>
            <div class="table-responsive">
                <table class="table table-hover" id="tableCustom_">
                    <thead>
                        <tr>
                            <th style="display: none;">ID</th>
                            <th>Nombre</th>
                            <th>Edad</th>
                            <th>Número de Empleado</th>
                            <th>Teléfono</th>
                            <th>Tipo Cliente</th>
                            <th>Opción</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for cliente in clientes %}
                        <tr>
                            <td style="display: none;">{{ cliente.idcliente }}</td>
                            <td class="text-center">{{ cliente.nombre }} {{ cliente.apellidopaterno }} {{ cliente.apellidomaterno }}</td>
                            <td class="text-center">{{ cliente.Edad }}</td>
                            <td class="text-center">{{ cliente.NumEmpleado }}</td>
                            <td class="text-center">{{ cliente.Telefono }}</td>
                            <td class="text-center">{{ cliente.TipoCliente }}</td>
                            <td class="text-center">
                                <a data-id="{{ cliente.idcliente }}" class="btn btn-primary selectCustom">Seleccionar</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr>
						<th style="display: none;">ID</th>
						<th>Nombre</th>
						<th>Edad</th>
						<th>Número de Empleado</th>
						<th>Teléfono</th>
						<th>Tipo Cliente</th>
                        <th>Opción</th>
					</tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
    

    $(document).ready(function () {
        $('#tableCustom_').DataTable({
            paging: false,
        });

        $(".selectCustom").click(function () {
            var customerId = $(this).data("id");

            if (!customerSelect.includes(customerId)) {
                customerSelect.push(customerId);
                $(this).addClass('btn-success').removeClass('btn-primary').text('Seleccionado');
            } else {
                var index = customerSelect.indexOf(customerId);
                if (index !== -1) {
                    customerSelect.splice(index, 1);
                }
                $(this).removeClass('btn-success').addClass('btn-primary').text('Seleccionar');
            }
            $("#totalSeleccionados").text(customerSelect.length);
        });

        $("#checkAllCustomer").change(function () {
            if (this.checked) {
                $(".selectCustom").each(function () {
                    var customerId = $(this).data("id");
                    if (!customerSelect.includes(customerId)) {
                        customerSelect.push(customerId);
                        $(this).addClass('btn-success').removeClass('btn-primary').text('Seleccionado');
                    }
                });
            } else {
                $(".selectCustom").each(function () {
                    var customerId = $(this).data("id");
                    var index = customerSelect.indexOf(customerId);
                    if (index !== -1) {
                        customerSelect.splice(index, 1);
                        $(this).removeClass('btn-success').addClass('btn-primary').text('Seleccionar');
                    }
                });
            }
            $("#totalSeleccionados").text(customerSelect.length);
        });
    });



    function confirmarSeleccion() {
        Swal.fire({
            title: '¿Estás seguro?',
            text: "Confirmarás la selección de clientes.",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Sí, confirmar',
            cancelButtonText: 'Cancelar',
            customClass: {
                confirmButton: 'btn-confirm',
                cancelButton: 'btn-cancel'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                transferirClientesSeleccionados();
            }
        });
    }

    function transferirClientesSeleccionados() {
    
    $("#tableUsuarios tbody").empty();
    
    customerSelect.forEach(function(customerId) {

        var customerDetails = obtenerDetallesClientePorId(customerId);

        var newRow = `<tr>
            <td class="text-center align-middle">${customerDetails.nombre}</td>
            <td class="text-center align-middle">${customerDetails.edad}</td>
            <td class="text-center align-middle">${customerDetails.numeroEmpleado}</td>
            <td class="text-center align-middle">${customerDetails.telefono}</td>
            <td class="text-center align-middle">${customerDetails.tipoCliente}</td>
            </tr>`;
        $("#tableUsuarios tbody").append(newRow);
    });
    $('#customerModal').modal('hide');
}

function obtenerDetallesClientePorId(customerId) {

    var clienteSeleccionado = $(`.selectCustom[data-id='${customerId}']`).closest('tr');
    var detallesCliente = {
        nombre: clienteSeleccionado.find('td:nth-child(2)').text().trim(),
        edad: clienteSeleccionado.find('td:nth-child(3)').text().trim(),
        numeroEmpleado: clienteSeleccionado.find('td:nth-child(4)').text().trim(),
        telefono: clienteSeleccionado.find('td:nth-child(5)').text().trim(),
        tipoCliente: clienteSeleccionado.find('td:nth-child(6)').text().trim()
    };

    return detallesCliente;
}


</script>
