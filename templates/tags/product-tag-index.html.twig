{% block content %}
    <div class="container">
        <div class="row">
            <div class="col-12 mb-5">
                <h3 class="text-center">Agregar tags para {{enterpriseName}}</h3>
                <div class="d-flex mx-auto">
                
                    <input id="tag-name-input" type="text" class="form-control me-4" placeholder="Ingresa el nombre del Tag"/>
                    <select id="tag-category-input" class="form-select me-3">
                        <option value="">Select category</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}">{{ category.name }}</option>
                        {% endfor %}
                        <option value="other$elect">Other</option>
                    </select>

                    <div id="collapseOther" class="collapse me-2">
                        <input type="text" class="form-control" id="otherCategoryName" placeholder="Enter other category">
                    </div>     

                    <button class="btn btn-success me-2" onclick="addTag()"><i class="fa-solid fa-plus"></i></button>

                </div>
                <p id="msg-error-input" class="text-danger text-center mt-3"></p>
            </div>
            <div class="col-6">
                <h3 class="text-center">Tags disponibles</h3>
                <div id="available-table-container"></div>
            </div>
            <div class="col-6">
                <h3 class="text-center">Tags seleccionados</h3>
                <div id="selected-table-container"></div>
            </div>
        </div>
    </div>

{% endblock %}

<script>

    $(document).ready(function(){   

        availableTagsTable();
        selectedTagsTable();

    });

    document.getElementById('tag-category-input').addEventListener('change', function() {
        var otherOption = document.querySelector('#tag-category-input option[value="other$elect"]');
        var collapseOther = document.getElementById('collapseOther');

        if (this.value === 'other$elect') {
            collapseOther.classList.add('show');
        } else if (otherOption && otherOption.selected) {
            collapseOther.classList.remove('show');
        }
    });

    function availableTagsTable(){

        $.ajax({
            url: "{{path('tag-available-tags-table')}}",
            type: 'POST',
            data: {productId:'{{productId}}', enterpriseId:'{{enterpriseId}}'},
            beforeSend: loadingGif("available-table-container"),
            dataType: "html"
        }).done(function( html ) {
            $("#available-table-container").html(html);
        }).fail(function() {
            alert( "error" );
        });
    }

    function selectedTagsTable(){

        $.ajax({
            url: "{{path('tag-selected-tags-table')}}",
            type: 'POST',
            data: {productId:'{{productId}}', enterpriseId:'{{enterpriseId}}'},
            beforeSend: loadingGif("selected-table-container"),
            dataType: "html"
        }).done(function( html ) {
            $("#selected-table-container").html(html);
        }).fail(function() {
            alert( "error" );
        });
    }

    function addProductTag(tagId){
        $.ajax({
            url: "{{path('tag-add-product-tag')}}",
            type: 'POST',
            data: {productId:'{{productId}}',tagId:tagId},
            dataType: "json"
        }).done(function( response ) {
            if (response.success){
                availableTagsTable();
                selectedTagsTable();
            } else {
                Swal.fire({
                    title: "No se pudo completar la operación",
                    text: response.msg,
                    icon: "warning"
                });
            }
        }).fail(function() {
            alert( "error" );
        });
    }

    function deleteProductTag(productTagId){
        $.ajax({
            url: "{{path('tag-delete-product-tag')}}",
            type: 'POST',
            data: {productTagId:productTagId},
            dataType: "json"
        }).done(function( response ) {
            if (response.success){
                availableTagsTable();
                selectedTagsTable();
            } else {
                Swal.fire({
                    title: "No se pudo completar la operación",
                    text: response.msg,
                    icon: "warning"
                });
            }
        }).fail(function() {
            alert( "error" );
        });
    }

    function addTag(){
        tagName = $("#tag-name-input").val();
        tagCategory = $("#tag-category-input").val() != 'other$elect'? $("#tag-category-input").val() : $("#otherCategoryName").val();

        $("#msg-error-input").text('');

        $.ajax({
            url: "{{path('tag-add-tag')}}",
            type: 'POST',
            data: {tagName:tagName, tagCategory:tagCategory, enterpriseId:'{{enterpriseId}}'},
            dataType: "json"
        }).done(function( response ) {
            if (response.success){
                $("#tag-name-input").val('');
                $("#otherCategoryName").val('');
                availableTagsTable();
            } else $("#msg-error-input").text(response.msg);
        }).fail(function() {
            alert( "error" );
        });
    }

    function deleteTag(tagId){

        Swal.fire({
            title: '¿Está seguro?',
            text: "Se eliminará el tag",
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#28B463',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Aceptar',
            cancelButtonText: 'Cancelar'
          }).then((result) => {
            if (result.value) {

                $.ajax({
                    url: "{{path('tag-delete-tag')}}",
                    type: 'POST',
                    data: {tagId:tagId},
                    dataType: "json"
                }).done(function( response ) {
                    if (response.success){
                        availableTagsTable();
                        selectedTagsTable();
                    } else {
                        Swal.fire({
                            title: "No se pudo completar la operación",
                            text: response.msg,
                            icon: "warning"
                        });
                    }
                }).fail(function() {
                    alert( "error" );
                });
            }
        })
        
    }

    function editTag(tagId, tagCategory = null, save = 0){
        if (save == 0){
            $("#input-name-tag-"+tagId).removeAttr("disabled");
            $(".edit-tag").addClass("d-none");
            $(".save-tag-button").removeClass("d-none");
        }
        else if (save == -1){
            availableTagsTable();
        }
        else{
            tagName = $("#input-name-tag-"+tagId).val();
            $.ajax({
                url: "{{path('tag-add-tag')}}",
                type: 'POST',
                data: {tagName:tagName, tagCategory:tagCategory, enterpriseId:'{{enterpriseId}}', tagId:tagId},
                dataType: "json"
            }).done(function( response ) {
                if (response.success){
                    availableTagsTable();
                } else {
                    Swal.fire({
                        title: "No se pudo completar la operación",
                        text: response.msg,
                        icon: "warning"
                    });
                }
            }).fail(function() {
                alert( "error" );
            });
        }
    }

</script>

<style>
    .selected-product-tag-row:hover{
        background-color: #F94F4F;
        cursor:pointer;
        color:white;
    }


</style>
