{% if availableTags|length > 0 %}

	{% for index, section in availableTags %}
		<table class="table">
			<thead>
				<tr>
					<th class="text-center">#</th>
					<th class="text-center">{{section.category}}</th>
					<th></th>
				</tr>
			</thead>
			<tbody id="table-body-product-tag">
				
				{% for index, tag in section.tags %}
					<tr>
						<td class="text-center">{{index + 1}}</td>
						<td class="text-center">
							<input value="{{tag.name}}" type="text" class="form-control" id="input-name-tag-{{tag.idtag}}" disabled/>
						</td>
						<td class="d-flex justify-content-center">
							<button class="btn btn-success me-2 edit-tag" onclick="addProductTag({{tag.idtag}})">
								<i class="fa-solid fa-plus"></i>
							</button>
							<button class="btn btn-danger me-2 edit-tag" onclick="deleteTag({{tag.idtag}})">
								<i class="fa-solid fa-trash-can"></i>
							</button>
							<button class="btn btn-info edit-tag" onclick="editTag({{tag.idtag}})">
								<i class="fa-solid fa-pencil"></i>
							</button>
							<button class="btn btn-success d-none save-tag-button me-2" onclick="editTag({{tag.idtag}}, 1)">
								<i class="fa-solid fa-check"></i>
							</button>
							<button class="btn btn-danger d-none save-tag-button" onclick="editTag({{tag.idtag}}, -1)">
								<i class="fa-solid fa-xmark"></i>
							</button>
						</td>
					</tr>
				{% endfor %}

			</tbody>
		</table>
	{% endfor %}
{% else %}
	<h4 class="text-center mt-5">No hay tags disponibles</h4>
{% endif %}
