{% if returnproducts is not null and returnproducts is not empty %}
	<table id="laboratory-dashboard-returnproducts" class="table">
		<thead>
			<th>#</th>
			<th>Modelo</th>
			<th><PERSON>a</th>
			<th>Revision</th>
			<th></th>
		</thead>

		<tbody>
			{% for index, product in returnproducts %}
				<tr>
					<td class="text-start">{{ index + 1 }}</td>
					<td class="{{ (product.errase >= 2) ? 'text-danger' : '' }}">{{ product.modelo }}</td>
					<td class="{{ (product.errase >= 2) ? 'text-danger' : '' }}">{{ product.marca }}</td>
					<td class="text-center">
						<textarea id="{{ product.id }}-textarea" rows="1" cols="100" onchange="updateProduct('{{ product.id }}', '{{ idordenlaboratorio }}','{{ product.errase }}', '{{ product.id }}-textarea' )">{{ product.revision }}</textarea>
					</td>
					<td class="text-center">
						<button class="btn {{ (product.errase >= 2) ? 'btn-primary' : 'btn-danger' }}" data-product-id="{{ product.id }}" id="{{ product.id }}" onclick="updateProduct('{{ product.id }}', '{{ idordenlaboratorio }}','{{ (product.errase == 2) ? '0': (product.errase == 1) ? '3': (product.errase == 3) ? '1':'2'}}', '{{ product.id }}-textarea' )">
							<i class="fa-solid {{ (product.errase >= 2) ? 'fa-check' : 'fa-x' }}"></i>
						</button>
					</td>
				</tr>
			{% endfor %}
		</tbody>
	</table>

	<h3> Razón: </h3>

	<textarea id="changestage-comment-textarea" rows="1" cols="100" ></textarea>

	<div class="row">
		<button type="button" class="col-auto btn btn-primary" data-dismiss="modal" onclick="changestage('{{ idordenlaboratorio }}', '{{ route }}')">
			Mandar reporte
		</button>
	</div>
{% endif %}
