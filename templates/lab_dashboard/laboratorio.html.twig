{% extends 'admin/layout.html.twig' %}
{% block titleHead %}{% endblock %}
{% block title %}Dashboard | Laboratorio{% endblock %}
{% block content %}
    <link rel="stylesheet" href="{{ asset('/lib/jQuery-Autocomplete-master/content/styles.css') }}">
    <link rel="stylesheet" href="{{ asset('/css/puntodeventa/laboratorio.css') }}"> 
    <link rel="stylesheet" href="{{ asset('lib/dataTables/dataTables.min.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('lib/fontawesome-free-5.9.0-web/css/all.css') }}">

    <div class="container-fluid">
        <h1 class="dashboard-title">LABORATORIO</h1>
        <div class="row col-12 d-flex justify-content-center">
            <div class="col-12 d-flex flex-row justify-content-center">
                <div class="col-2 laboratorio-colum">
                    <h2 class="dashboard-subtitle">ORDEN</h1>   
                </div>
                <div class="col-2 laboratorio-colum">
                    <h2 class="dashboard-subtitle">CLIENTE</h1>  
                </div>
                <div class="col-2 laboratorio-colum">
                    <h2 class="dashboard-subtitle">ARMAZÓN</h1>  
                </div>
                <div class="col-2 laboratorio-colum">
                    <h2 class="dashboard-subtitle">ESTADO</h1>  
                </div>
                <div class="col-2 laboratorio-colum">
                    <h2 class="dashboard-subtitle">BISELADOR</h1>  
                </div>
                <div class="col-2 laboratorio-colum">
                    <h2 class="dashboard-subtitle">ETAPA</h1>  
                </div>
            </div>
            <div class="col-12 d-flex flex-row justify-content-center">
                <div class="col-2 laboratorio-columna">
                    <p class="dashboard-text">101</p>
                </div>
                <div class="col-2 laboratorio-columna">
                    <p class="dashboard-text">NOMBRE</p>
                </div>
                <div class="col-2 laboratorio-columna">
                    <p class="dashboard-text">MARCA:</p>
                    <p class="dashboard-text">SKU:</p>
                    <p class="dashboard-text">MODELO:</p>
                    <a type="button" class="btn btn-detail" data-toggle="modal" data-target="#armazonModal">DETALLES</a>
                </div>
                <div class="col-2 laboratorio-columna">
                    <p class="dashboard-text">PENDIENTE DE RECIBIR MATERIALES</p>
                </div>
                <div class="col-2 laboratorio-columna">
                    <p class="dashboard-text">ENCARGADO</p>
                </div>
                <div class="col-2 laboratorio-columna">
                    <p class="dashboard-text">YA LLEGO MATERIAL</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Ventas-->
    <div class="mod" id="armazonModal" tabindex="-1" aria-labelledby="" aria-hidden="true">
        <div class="mod-dialog modal-dialog-scrollable modal-dialog-centered">
            <div class="modal-content" style="border-radius:10px; box-shadow: 0 5px 15px rgba(0, 0, 0, .5);">
                <div class="modal-header">
                    <h1 class="modal-title">ORDEN DE LABORATORIO & DATOS DE ORDEN</h1>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body bg-primary p-0 d-flex flex-column align-items-center">
                    <div class="row col-12 justify-content-center">
                        <div class="col-12 section d-flex">
                            <h4 class="table-title ms-5">Paciente: Erreh Muñoz Castillo</h4>
                        </div>
                    </div>
                    <hr>
                    <div class="row col-12 justify-content-center">
                        <div class="col div-con-borde-derecho">
                            <h4 class="table-title">Información del Solicitante:</h4>
                            <p class="table-title">Graduación:  0.0</p>
                            <p class="table-title">Miopia: 0.0</p>
                            <p class="table-title">Astigamtismo: 0.0</p>
                            <p class="table-title">Hipermetropia: 0.0</p>
                            <div class="d-flex align-items-center">
                                <img class="icon-orden" src="/img/doc.png" alt="Revisar documento">
                                <p class="table-title m-0">Revisar documento</p>
                            </div>
                        </div>
                        <div class="col-8 d-flex flex-column justify-content-center section">
                            <h4 class="table-title">Descripción:</h4>
                            <p class="table-title">Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip </p>
                            <h4 class="table-title">Nota:</h4>
                            <p class="table-title">Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip </p>

                            <div class="d-flex justify-content-between">
                               <div class="d-flex align-items-center">
                                    <img class="icon-orden" src="/img/temp.png" alt="Temporizador">
                                    <p class="table-title m-0">00 : 00 : 00</p>
                                </div>
                                <div class="d-flex align-items-center">
                                    <button type="button" class="btn btn-warning mx-4">PAUSAR</button>
                                    <button type="button" class="btn btn-success mx-4">TERMINAR</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block javascripts %}
    {{parent()}}
    <script src="{{asset('js/jquery.formatCurrency-1.4.0.pack.js')}}"></script>
    <script src="{{asset('lib/jQuery-Autocomplete-master/dist/jquery.autocomplete.min.js')}}"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/html2canvas@1.0.0-rc.1/dist/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.debug.js" integrity="sha384-NaWTHo/8YCBYJ59830LTz/P4aQZK1sS0SneOgAvhsIl3zBu8r9RevNg5lHCHAuQ/" crossorigin="anonymous"></script>
    <script src="{{ asset('lib/dataTables/dataTables.min.js') }}"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/js/all.min.js"></script>
{% endblock %}
