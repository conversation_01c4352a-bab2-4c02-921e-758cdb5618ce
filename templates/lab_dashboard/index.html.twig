{% extends 'admin/layout.html.twig' %}

{% block title %}dashboard laboratorio
{% endblock %}

{% block content %}

	<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.css"/>

	<div class="card">
		<div class="card-body">
			<div class="container-fluid">
				<h3 class="text-center">Dashboard Laboratorio</h3>
				<div class="row">
					<div class="col-12" id="laboratory-dashboard-table-container"></div>
				</div>
			</div>
		</div>

		<div class="mod " id="lab-dashboard-modal" tabindex="-1" aria-labelledby="" aria-hidden="true">
			<div class="mod-dialog modal-dialog-scrollable">
				<div class="modal-content" style="border-radius:10px">
					<div class="modal-header bg-primary">
						<h1 class="modal-title fs-5" id="lab-dashboard-title"></h1>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
					<div class="modal-body">

						<div id="lab-dashboard-result"></div>

					</div>
				</div>
			</div>
		</div>
	</div>

	{% endblock %}

	{% block javascripts %}
		{{parent()}}

		<script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
		<script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>

		<script>
			$(document).ready(function () {
				laboratoryOrderConfigTable();
				$('#lab-dashboard-modal').on('hidden.bs.modal', function () {
					laboratoryOrderConfigTable();
				});
			});

			function laboratoryOrderConfigTable() {
	
				$.ajax({
                    url: "{{ path('app_lab_dashboard_table') }}", 
                    type: 'GET', 
                    beforeSend: loadingGif("laboratory-dashboard-table-container"), 
                    dataType: "html"})
                .done(function (html) {
					$('#order-laboratory-config-table').DataTable();
					$("#laboratory-dashboard-table-container").html(html);

				}).fail(function () {
					alert("error");
				});
			}
			
		</script>


	{% endblock %}
