<div class="table-responsive">
	<table id="order-laboratory-config-table" class="table">
		<thead>
			<th># orden</th>
			<th>Creacion</th>
			<th>Actualización</th>
			<th>Cliente</th>
			<th>Estado</th>
			<th>Productos</th>
			<th>Base</th>
			<th>Ojo i<PERSON>o</th>
			<th>Ojo derecho</th>
			<th>Add</th>
			<th>Opciones</th>

		</thead>
		<tbody>
			{% for index, ol in laboratoryOrders %}
				<tr>
					<td>{{index + 1}}</td>
					<td>{{ol.creacion|date("d/m/Y")}}</td>
					<td>{{ol.actualizacion|date("d/m/Y")}}</td>
					<td>{{ol.fullName}}</td>
					<td>{{orderStages[ol.stage - 1]}}</td>

					<td class="text-center">
						<table class="table">

							<thead>
								<th>#</th>
								<th>Modelo</th>
								<th><PERSON><PERSON></th>
								<th>Descripción</th>
							</thead>

							<tbody>
								{% if mappedProducts[ol.idordenlaboratorio] is defined %}
									{% for index, mappedProduct in mappedProducts[ol.idordenlaboratorio] %}

										{% if mappedProduct.errase <= 1 or ol.stage == 9 %}
											<tr>
												<th class="text-start">{{index + 1}}</th>
												<td class={{ (mappedProduct.errase >= 2) ? 'text-danger':''}} >{{mappedProduct.modelo}}</td>
												<td class={{ (mappedProduct.errase >= 2) ? 'text-danger':''}} >{{mappedProduct.marca}}</td>
												<td class={{ (mappedProduct.errase >= 2) ? 'text-danger':''}} >{{mappedProduct.descripcion}}</td>
											</tr>
										{% endif %}
									{% endfor %}
								{% endif %}
							</tbody>

						</table>
					</td>
					<td>{{ol.base}}</td>
					<td>
						<table class="table">
							<tr>
								<th class="text-start">Esfera</th>
								<td>{{ol.esferaoi}}</td>
							</tr>
							<tr>
								<th class="text-start">Cilindro</th>
								<td>{{ol.cilindrooi}}</td>
							</tr>
							<tr>
								<th class="text-start">Eje</th>
								<td>{{ol.ejeoi}}</td>
							</tr>
						</table>
					</td>
					<td>
						<table class="table">
							<tr>
								<th class="text-start">Esfera</th>
								<td>{{ol.esferaod}}</td>
							</tr>
							<tr>
								<th class="text-start">Cilindro</th>
								<td>{{ol.cilindrood}}</td>
							</tr>
							<tr>
								<th class="text-start">Eje</th>
								<td>{{ol.ejeod}}</td>
							</tr>
						</table>
					</td>
					<td>{{ol.addordenlaboratorio}}</td>
					<td class="text-center">

					<div class="row d-flex justify-content-between">

						{% if ol.stage < 6 %}
							<button class="m-1 btn btn-success" onclick="changestage('{{ol.idordenlaboratorio}}', '6')">
								Materiales Recibidos
							</button>
							<button class="m-1 btn btn-danger" data-toggle="modal" data-target="#lab-dashboard-modal" onclick="orderProductsRevision('{{ol.idordenlaboratorio}}', '3')">
								Daño o extravio
							</button>
						{% elseif ol.stage < 7 %}
							<button class="m-1 btn btn-success" onclick="changestage('{{ol.idordenlaboratorio}}', '7')">
								Empezar
							</button>
							<button class="m-1 btn btn-danger" data-toggle="modal" data-target="#lab-dashboard-modal" onclick="orderProductsRevision('{{ol.idordenlaboratorio}}', '3')">
								Daño o extravio
							</button>
						{% elseif ol.stage < 8 %}
							<h3 class="time-display" id="timeDisplay{{ index }}">{{ '00:00:00' }}</h3>
							<button class="m-1 btn btn-success" onclick="changestage('{{ol.idordenlaboratorio}}', '9')">
								Mandar a {{orderStages[8]}}
							</button>
							<button class="m-1 btn btn-primary" onclick="changestage('{{ol.idordenlaboratorio}}', '8')">
								Pausar
							</button>

						{% elseif ol.stage < 9 %}
							<h3 class="time-display" id="timeDisplayStatic{{ index }}">{{ '00:00:00' }}</h3>
							<button class="m-1 btn btn-primary" onclick="changestage('{{ol.idordenlaboratorio}}', '7')">
								Reanudar
							</button>

							<button class="m-1 btn btn-danger" data-toggle="modal" data-target="#lab-dashboard-modal" onclick="orderProductsRevision('{{ol.idordenlaboratorio}}', '3')">
								Daño o extravio
							</button>
						{% elseif ol.stage < 10 %}
							<button class="m-1 btn btn-success" onclick="changestage('{{ol.idordenlaboratorio}}', '10')">
								Aprobar
							</button>
							<button class="m-1 btn btn-danger" data-toggle="modal" data-target="#lab-dashboard-modal" onclick="orderProductsRevision('{{ol.idordenlaboratorio}}', '3')">
								Daño o extravio
							</button>
							<button class="m-1 btn btn-primary" data-toggle="modal" data-target="#lab-dashboard-modal" onclick="orderProductsRevision('{{ol.idordenlaboratorio}}', '6')">
								Regresar a Laboratorio
							</button>
						{% elseif ol.stage == 10 %}
							<h3><strong>Aprobado </strong></h3>
							<p>Sin Opciones. en espera de envio.</p>
						{% endif %}

					</div>

					</td>
				</tr>
			{% endfor %}
		</tbody>
	</table>
</div>

<script>
	var laboratoryOrders = {{ laboratoryOrders|json_encode|raw }};
	var mappedProducts = {{ mappedProducts|json_encode|raw }};
	var eventQuery = {{ eventQuery|json_encode|raw }};

    $(document).ready(function(){

        $('#order-laboratory-config-table').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ ordenes por página',
            },
            "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
            responsive: true
        });


    });

		var intervalId = setInterval(function() {
		for (var index = 0; index < laboratoryOrders.length; index++) {
			
			var ol = laboratoryOrders[index];
			
			var displayId = 'timeDisplay' + index;

			eventQuery[ol.idordenlaboratorio] += 1;
  			// Find the distance between now and the count down date
  			var initialValue = eventQuery[ol.idordenlaboratorio];

			var displayElement = document.getElementById(displayId);
            if (displayElement) {
                displayElement.textContent = formatTime(initialValue);
            }

		}
	}, 1000);

	for (var index = 0; index < laboratoryOrders.length; index++) {
			
			var ol = laboratoryOrders[index];
			
			var displayId = 'timeDisplayStatic' + index;

  			// Find the distance between now and the count down date
  			var initialValue = eventQuery[ol.idordenlaboratorio];

			var displayElement = document.getElementById(displayId);
            if (displayElement) {
                displayElement.textContent = formatTime(initialValue);
            }

		}
	
	function formatTime(seconds) {
		var hours = Math.floor(seconds / 3600);
		var minutes = Math.floor((seconds % 3600) / 60);
		var remainingSeconds = seconds % 60;

		return (
		(hours < 10 ? '0' : '') + hours + ':' +
		(minutes < 10 ? '0' : '') + minutes + ':' +
		(remainingSeconds < 10 ? '0' : '') + remainingSeconds
		);
	}

	// Function to update a specific time display
	function updateTimeDisplay(displayId, initialValue) {
		// Get the current time from the displayed element
		var currentTime = parseInt(document.getElementById(displayId).textContent);

		// Increment the time by 1 second
		currentTime++;

		// Format the time and update the displayed time
		document.getElementById(displayId).textContent = formatTime(currentTime);
	}

	function orderProductsRevision(updateorderid, stage){

        $.ajax({
            url: "{{path('app_lab_dashboard_order_products')}}",
            type: 'POST',
            data: {updateorderid:updateorderid, stage:stage},
            beforeSend: loadingGif("lab-dashboard-result"),
            dataType: "html"
        }).done(function( html ) {
            $("#lab-dashboard-result").html(html);
        }).fail(function() {
            alert( "error" );
        });
    }

    function updateProduct(updateid, updateorderid, value, revisionid){

		clearInterval(intervalId);

		const textarea = document.getElementById(revisionid);

  		// Get the value of the textarea
  		const textareaValue = textarea.value;

        $.ajax({
            url: "{{path('laboratory-dashboard-updatestocklabord')}}",
            type: 'POST',
            data: {updateid:updateid, updateorderid:updateorderid, revision:textareaValue, value:value},
            beforeSend: loadingGif("lab-dashboard-result"),
            dataType: "html"
        }).done(function( html ) {
            $("#lab-dashboard-result").html(html);
        }).fail(function() {
            alert( "error" );
        });
    }


    function changestage(updateid, num){

		clearInterval(intervalId);

		var comment = $('#changestage-comment-textarea').val() ?? 'Sin comentarios';

        $.ajax({
            url: "{{path('laboratory-order-changestage')}}",
            type: 'POST',
            data: {updateid:updateid, num:num, comment:comment},
            beforeSend: loadingGif("laboratory-dashboard-table-container"),
            dataType: "html"
        }).done(function( html ) {
            $("#laboratory-dashboard-table-container").html(html);
        }).fail(function() {
            alert( "error" );
        });
    }

	// Function to format time as HH:mm:ss

</script>