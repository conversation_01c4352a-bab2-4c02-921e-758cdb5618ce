{% for category, items in your_array %}

	<div class="col-12 px-2 py-2">
		<div class="card">
			<div class="card-header" style="cursor: pointer;" data-bs-toggle="collapse" href="#collapse{{category}}" aria-expanded="false" aria-controls="collapse{{category}}">
				<h5 class="card-title">{{ category|capitalize }}</h5>
			</div>
			<div class="collapse" id="collapse{{category}}" style="max-height: 15rem; overflow-y: auto; overflow-x: hidden;">
				<div class="row p-3">
					{% for item in items.values %}
					<div class="col-4 mb-2">
						<div class="form-check">
							<input type="checkbox" class="form-check-input" id="{{ category }}_{{ item.id }}" name="{{ category }}" value="{{ item.id }}">
							<label class="form-check-label" for="{{ category }}_{{ item.id }}">{{ item.name }}</label>
						</div>
					</div>
					{% endfor %}
				</div>
			</div>
		</div>
	</div>

{% endfor %}
