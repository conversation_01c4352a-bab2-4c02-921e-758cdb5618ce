{% extends 'admin/layout.html.twig' %}

{% block title %}| Comisiones{% endblock %}

{% block content %}
    <link href="{{ asset('css/styles/comisiones.css') }}?version={{ version }}" rel="stylesheet"/>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&family=League+Spartan:wght@100..900&display=swap"
          rel="stylesheet">

    <div class="body d-flex flex-column align-items-center pt-4">
        <div class="container-fluid bg-white rounded-4 py-4 shadow">
            <h1 class="text-center page-title mb-5">Reporte de Comisiones</h1>
            <h2 class="page-subtitle mx-4 mb-0 text-start">Filtros:</h2>
            <div class="row mb-3">
                <div class="col-md-3 mb-2">
                    <select id="empresaD" class="form-control custom-select">
                        <option value="-1">Selecciona una empresa</option>
                        {% for empresa in empresas %}
                            <option value="{{ empresa.idempresa }}">{{ empresa.nombre }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 mb-2">
                    <select id="sucursal" class="form-control custom-select">
                        <option value="-1">Selecciona la sucursal</option>
                    </select>
                </div>
                <div class="col-md-2 mb-2">
                    <select id="vendedor" class="form-control custom-select">
                        <option value="-1">Selecciona al vendedor</option>
                        <option value="-2">Todos los vendedores</option>
                    </select>
                </div>
                <!-- div class="col-md-2 mb-2">
                    <select id="tipoPago" class="form-control custom-select">
                        <option value="-1">Tipo de Pago</option>
                        {% for tipopagos in tipopago %}
                            <option value="{{ tipopagos.idpaymenttype }}">{{ tipopagos.name }}</option>
                        {% endfor %}
                    </select>
                </div -->
                <div class="col-md-2 mb-2">
                    <select id="ventaLiquidada" class="form-control custom-select">
                        <option value="1">Liquidada</option>
                        <option value="0">NO Liquidadas</option>
                        <option value="-1">Ambos</option>
                    </select>
                </div>
            </div>
            <div class="row  mb-3 justify-content-start align-items-center">
                <div class="col-md-3 d-flex align-items-center py-0 justify-content-end">
                    <select id="tipoVenta" class="form-control custom-select">
                        <option value="-1">Selecciona tipo de venta</option>
                        {% for tipoventas in tipoVenta %}
                            <option value="{{ tipoventas.idtipoventa }}">{{ tipoventas.tipoVenta }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-center py-0 justify-content-end">
                    <label for="rango" class="m-0 p-0 text-input text-end">Rango de fecha:</label>
                </div>
                <div class="col-md-5 d-flex align-items-center">
                    <input id="fecha-inicio-rango-dia" type="text" autocomplete="off"
                           class="date-input form-control mx-2 mb-0" name="start"/>
                    <span class="px-2 py-2">a</span>
                    <input id="fecha-fin-rango-dia" type="text" autocomplete="off"
                           class="date-input form-control mx-2 mb-0" name="end"/>
                    <button class="btn btn-warning" onclick="resetRangoFechaDias()">
                        <i class="fa fa-eraser" aria-hidden="true"></i>
                    </button>
                </div>
                <div class="col-md-2 text-center">
                    <button class="btn btn-primary btn-block" id="buscarBtn" onclick="buscarComisiones(event)">Buscar
                    </button>
                </div>
            </div>
            <br>
            <div id="tablaComisiones" class="container-fluid"></div>
        </div>
    </div>


    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(document).ready(function () {
            var sucursalesUrl = '{{ path('get_sucursales', {'empresaId': 0}) }}';
            var vendedoresUrl = '{{ path('get_vendedores', {'sucursalId': 0}) }}';

            $('#empresaD').change(function () {
                var empresaId = $(this).val();
                if (empresaId) {
                    var url = sucursalesUrl.replace('/0', '/' + empresaId);
                    $.ajax({
                        url: url,
                        method: 'GET',
                        success: function (data) {
                            var sucursalSelect = $('#sucursal');
                            sucursalSelect.empty();
                            sucursalSelect.append('<option value="">Selecciona la sucursal</option>');
                            $.each(data, function (index, sucursal) {
                                sucursalSelect.append('<option value="' + sucursal.idsucursal + '">' + sucursal.nombre + '</option>');
                            });
                        }
                    });
                } else {
                    var sucursalSelect = $('#sucursal');
                    sucursalSelect.empty();
                    sucursalSelect.append('<option value="">Selecciona la sucursal</option>');
                }
            });

            $('#sucursal').change(function () {
                var sucursalId = $(this).val();
                if (sucursalId) {
                    var url = vendedoresUrl.replace('/0', '/' + sucursalId);
                    $.ajax({
                        url: url,
                        method: 'GET',
                        success: function (data) {
                            var vendedorSelect = $('#vendedor');
                            vendedorSelect.empty();
                            vendedorSelect.append('<option value="">Selecciona al vendedor</option>');
                            $.each(data, function (index, vendedor) {
                                vendedorSelect.append('<option value="' + vendedor.idusuario + '">' + vendedor.nombre + ' ' + vendedor.apellidopaterno + ' ' + vendedor.apellidomaterno + '</option>');
                            });
                        }
                    });
                } else {
                    var vendedorSelect = $('#vendedor');
                    vendedorSelect.empty();
                    vendedorSelect.append('<option value="">Selecciona al vendedor</option>');
                }
            });
        });

        function buscarComisiones(e) {
            var sucursal = $('#sucursal').val() || '-1';
            var vendedor = $('#vendedor').val() || '-1';
            var tipoPago = $('#tipoPago').val() || '-1';
            var ventaLiquidada = $('#ventaLiquidada').val() || '-1';
            var tipoVenta = $('#tipoVenta').val() || '-1';
            var fechaInicio = $('#fecha-inicio-rango-dia').val();
            var fechaFin = $('#fecha-fin-rango-dia').val();

            // Verificar que los campos obligatorios estén llenos
            if (!fechaInicio || !fechaFin) {
                e.preventDefault(); // Evitar el comportamiento por defecto del botón
                Swal.fire({
                    type: "error",  // Cambia 'icon' por 'type' si es versión antigua de SweetAlert2
                    title: "Oops...",
                    text: "Selecciona una Fecha y Empresa",
                });
                return;
            }

            var datos = {
                sucursal: sucursal,
                vendedor: vendedor,
                ventaLiquidada: ventaLiquidada,
                tipoPago: tipoPago,
                fechaInicio: fechaInicio,
                fechaFin: fechaFin,
                tipoVenta: tipoVenta
            };

            $.ajax({
                url: '{{ path('tablaComisiones') }}',
                method: 'POST',
                data: datos,
                success: function (response) {
                    $("#tablaComisiones").html(response);
                }
            });
        }

        jQuery(function ($) {
            $('#fecha-inicio-rango-dia').datetimepicker({
                "pickTime": false,
                "pickDate": true,
                "minDate": "1/1/1900",
                "maxDate": null,
                "showToday": true,
                "language": "es_MX",
                "defaultDate": "",
                "disabledDates": [],
                "enabledDates": [],
                "icons": {
                    "time": "fa fa-clock-o",
                    "date": "fa fa-calendar",
                    "up": "fa fa-chevron-up",
                    "down": "fa fa-chevron-down"
                },
                "useStrict": false,
                "sideBySide": false,
                "daysOfWeekDisabled": [],
                "collapse": true,
                "calendarWeeks": false,
                "viewMode": "days",
                "minViewMode": "days",
                "useCurrent": false,
                "useSeconds": false
            });

            $('#fecha-fin-rango-dia').datetimepicker({
                "pickTime": false,
                "pickDate": true,
                "minDate": "1/1/1900",
                "maxDate": null,
                "showToday": true,
                "language": "es_MX",
                "defaultDate": "",
                "disabledDates": [],
                "enabledDates": [],
                "icons": {
                    "time": "fa fa-clock-o",
                    "date": "fa fa-calendar",
                    "up": "fa fa-chevron-up",
                    "down": "fa fa-chevron-down"
                },
                "useStrict": false,
                "sideBySide": false,
                "daysOfWeekDisabled": [],
                "collapse": true,
                "calendarWeeks": false,
                "viewMode": "days",
                "minViewMode": "days",
                "useCurrent": false,
                "useSeconds": false
            });
        });

        function resetRangoFechaDias() {
            $("#fecha-inicio-rango-dia").val("");
            $("#fecha-fin-rango-dia").val("");
        }
    </script>
{% endblock %}
