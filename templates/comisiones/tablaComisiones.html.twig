<link rel="stylesheet" href="{{ asset('css/styles/comisiones.css') }}">
<br>
<div class="row">
    <div class="col-md-12">
        <div class="table-responsive">
            <table id="comisionesTable" class="table table-bordered table-hover text-center table-striped">
                <thead class="title-table">
                <tr>
                    <th>Folio</th>
                    <th>Tipo de Venta</th>
                    <th>Sucursal</th>
                    <th>Vendedor</th>
                    <th>Liquidada</th>
                    <th>Fecha de ultimo pago</th>
                    <th>Monto total</th>
                    {% for tipoPago in tiposDePago %}
                        <th>{{ tipoPago }} - Monto total</th>
                        <th>{{ tipoPago }} - # Meses Sin intereses</th>
                    {% endfor %}
                    <th>Número de pagos</th>
                    <th>Adeudo</th>
                    <th>Número de Autorización</th> <!-- Nueva columna -->
                    <th>Estado de Autorización</th> <!-- Nueva columna -->
                </tr>
                </thead>
                <tbody>
                {% for venta in ventas %}
                    <tr>
                        <td>{{ venta.folio }}</td>
                        <td>{{ venta.tipoVenta }}</td>
                        <td>{{ venta.sucursal }}</td>
                        <td>{{ venta.vendedor ~ " " ~ venta.apellidopaterno ~ " " ~ venta.apellidomaterno }}</td>
                        <td>{{ venta.liquidada ? 'Sí' : 'No' }}</td>
                        <td>{{ venta.fechaLiquidacion|date('d-m-Y') }}</td>
                        <td>${{ venta.montoTotal | number_format(2, '.', ',') }}</td>
                        {% for tipoPago in tiposDePago %}
                            <td>${{ venta.pagos[tipoPago].montoTotal ?? 0 | number_format(2, '.', ',') }}</td>
                            <td>{{ venta.pagos[tipoPago].mesesintereses ?? 0 | number_format(2, '.', ',') }}</td>
                        {% endfor %}
                        <td>{{ venta.numeroPagos }}</td>
                        <td>${{ venta.adeudo | number_format(2, '.', ',') }}</td>
                        <td>{{ venta.authorizationNumber }}</td>
                        <td class="text-center">{{ venta.autorizacionState ?: 'N/A' }}</td>

                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>


<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.1.1/css/buttons.dataTables.min.css">
<script type="text/javascript" charset="utf8"
        src="https://cdn.datatables.net/buttons/2.1.1/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" charset="utf8"
        src="https://cdn.datatables.net/buttons/2.1.1/js/buttons.html5.min.js"></script>
<script type="text/javascript" charset="utf8"
        src="https://cdn.datatables.net/buttons/2.1.1/js/buttons.print.min.js"></script>
<!-- Include JSZip for Excel export -->
<script type="text/javascript" charset="utf8"
        src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    $(document).ready(function () {
        var table = $('#comisionesTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "lengthChange": false,
            "pageLength": 15,
            "language": {
                "lengthMenu": "Mostrar _MENU_ registros por página",
                "zeroRecords": "No se encontraron resultados",
                "info": "Mostrando página _PAGE_ de _PAGES_",
                "infoEmpty": "No hay registros disponibles",
                "infoFiltered": "(filtrado de _MAX_ registros en total)",
                "search": "Buscar:",
                "paginate": {
                    "first": "Primero",
                    "last": "Último",
                    "next": "Siguiente",
                    "previous": "Anterior"
                }
            },
            dom: 'Bfrtip', // Add this line to include the Buttons extension
            buttons: [
                {
                    extend: 'excelHtml5',
                    text: 'Exportar a excel',
                    className: 'btn-succes'
                }
            ]
        });

        table.buttons().container().appendTo('.dataTables_wrapper');

        var labels = {{ labels|raw }};
        var data = {{ data|raw }};
        var monthNames = ["Enero 2024", "Febrero 2024", "Marzo 2024", "Abril 2024", "Mayo 2024", "Junio 2024", "Julio 2024", "Agosto 2024", "Septiembre 2024", "Octubre 2024", "Noviembre 2024", "Diciembre 2024"];
        labels = labels.map(label => monthNames[parseInt(label.split('-')[0]) - 1]);
        var backgroundColors = [];
        for (var i = 0; i < labels.length; i++) {
            backgroundColors.push(`hsl(${i * 360 / labels.length}, 100%, 50%)`);
        }
        var ctx = document.getElementById('comisionesChart').getContext('2d');
        var comisionesChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Comisiones por mes',
                    data: data,
                    backgroundColor: backgroundColors,
                    borderColor: '#ffffff',
                    borderWidth: 1
                }]
            },
            options: {
                plugins: {
                    legend: {
                        labels: {
                            color: 'white' // Color de las etiquetas de la leyenda
                        }
                    }
                }
            }
        });
    });
</script>