<div class="col-md-9 miLista">
    <fieldset id="idtipoventa">
        <label class="filter-title">TIPO DE VENTA:</label>
        <label for="todasTipoVenta">
            <input type="checkbox" name="todasTipoVenta" onclick="toggleTiposVenta(this)" id="todasTipoVenta">
        Todas</label>
        {% for tipoventa in tiposVenta %} 
            <label for="{{ tipoventa.idtipoventa }}">
                <input type="checkbox" id="{{ tipoventa.idtipoventa }}" name="tipoVenta" value="{{ tipoventa.idtipoventa }}">
            {% if tipoventa.nombre == "" %}Público en General
            {% elseif tipoventa.nombre == "UAM" %}Prestación UAM
            {% else %}{{ tipoventa.nombre }}
            {% endif %}</label>
        {% endfor %}
    </fieldset>
</div>

<fieldset id="idsucursal" >
    <legend>Sucursales:</legend>


    
    <label for="todasSucursales" >
    <input type="checkbox" name="todasSucursales" onClick="toggleSucursales(this)" id="todasSucursales" checked>
    Todas</label>

    {% for sucursal in sucursales %}

   
      
      <label for="{{ sucursal.idsucursal }}" >
      <input type="checkbox" id="{{ sucursal.idsucursal }}" name="sucursal" value="{{ sucursal.idsucursal }}" checked>
      {{ sucursal.nombre }}</label>
    
    
    {% endfor %}
    
    

</fieldset>

<fieldset id="idsucursal" >
    <legend>Bodegas:</legend>


    
    <label for="todasBodegas" >
    <input type="checkbox" name="todasBodegas" onClick="toggleBodegas(this)" id="todasBodegas" checked>
    Todas</label>

    {% for bodega in bodegas %}

   
      
      <label for="{{ bodega.idsucursal }}" >
      <input type="checkbox" id="{{ bodega.idsucursal }}" name="bodega" value="{{ bodega.idsucursal }}" checked>
      {{ bodega.nombre }}</label>
    
    
    {% endfor %}
    
    

</fieldset>

<fieldset id="idsucursal" >
    <legend>hols:</legend>


    
    <label for="todasCampañas" >
    <input type="checkbox" name="todasCampañas" onClick="toggleCampañas(this)" id="todasCampañas" checked>
    Todas</label>

    {% for campaña in campañas %}

   
      
      <label for="{{ campaña.idsucursal }}" >
      <input type="checkbox" id="{{ campaña.idsucursal }}" name="campaña" value="{{ campaña.idsucursal }}" checked>
      {{ campaña.nombre }}</label>
    
    
    {% endfor %}
    
    

</fieldset>


<script language="JavaScript">
    function toggleSucursales(source) {
        checkboxes = document.getElementsByName('sucursal');
        for(var i=0, n=checkboxes.length;i<n;i++) {
            checkboxes[i].checked = source.checked;
        }
    }
    function toggleBodegas(source) {
        checkboxes = document.getElementsByName('bodega');
        for(var i=0, n=checkboxes.length;i<n;i++) {
            checkboxes[i].checked = source.checked;
        }
    }
    function toggleCampañas(source) {
        checkboxes = document.getElementsByName('campaña');
        for(var i=0, n=checkboxes.length;i<n;i++) {
            checkboxes[i].checked = source.checked;
        }
    }
    function toggleClases(source) {
        checkboxes = document.getElementsByName('clase');
        for(var i=0, n=checkboxes.length;i<n;i++) {
            checkboxes[i].checked = source.checked;
        }
    }
    function toggleTiposVenta(source) {
        checkboxes = document.getElementsByName('tipoVenta');
        for(var i=0, n=checkboxes.length;i<n;i++) {
            checkboxes[i].checked = source.checked;
        }
    
    }
</script>