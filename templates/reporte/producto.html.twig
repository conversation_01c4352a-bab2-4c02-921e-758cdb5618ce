{% extends 'admin/layout.html.twig' %}
{% block titleHead %}{% endblock %}
{% block title %}Reporte de Productos{% endblock %}
{% block content %}
    <input id="url-almacen-obtener-informacion-producto" type="hidden" value="{{ path('almacen-obtener-informacion-producto') }}">
    <input id="url-autocompletar-modelo" type="hidden" value="{{path('autocompletar-modelo')}}">
    <style></style>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.css" />
    <div class="container-fluid">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="col-md-12">
                            <div class="col-md-9">
                                <h3 class="card-title">REPORTE DE PRODUCTOS VENDIDOS</h3>
                            </div>
                            <div class="col-md-3 button-buscar"> 
                                <button class="btn btn-primary reporte" onClick="obtenerStock()">Buscar</button>
                            </div>
                        </div>
                        <div class="card">
                        <div class="row">
                            <div class="col-md-12 opciones">
                                <div class="col-md-4 opciones">
                                    <label for="empresa" class="lab reporte">Empresas: </label>
                                    {% if empresas is not empty %}
                                        <select name="empresa" id="idempresa" class="form-control" onchange="obtenerSucursales(); obtenerClases(); obtenerTiposVenta()">
                                            <option value=-1>Seleccione una empresa</option>
                                            {% for empresa in empresas %}
                                                <option value="{{ empresa.idempresa }}">{{ empresa.nombre }}</option>
                                            {% endfor %}
                                        </select>
                                    {% else %}
                                        <h4>Aún no tienes empresas asignadas</h4>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 opciones">
                                    <label for="product-type-select" class="lab reporte">Tipo de producto: </label>
                                    <select id="product-type-select" class="form-control">
                                        <option value=-1>Todos los productos</option>
                                        <option value=1>Productos</option>
                                        <option value=2>Micas / Servicios</option>
                                    </select>
                                </div>
                                <div class="col-md-4 opciones cent">
                                    <label class="lab reporte m-0 mt-4">Rango por días: </label>
                                    <div class="row ">
                                        <div class="col-md-12 mt-5 mb-5">
                                            <div id="sandbox-container">
                                                <div class="input-daterange input-group rango-tiempo" id="datepicker-rango">
                                                    <input id="fecha-inicio-rango-dia" type="text" autocomplete="off" class="input-sm form-control" name="start" />
                                                    <span class="input-group-addon"> a </span>
                                                    <input id="fecha-fin-rango-dia" type="text" autocomplete="off" class="input-sm form-control" name="end" />
                                                    <button class="btn btn-warning" onclick="resetRangoFechaDias()"><i class="fa fa-eraser" aria-hidden="true"></i></button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                        <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
                        <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
                        <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
                        <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
                        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
                        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
                        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
                        <script>
                            jQuery(function ($) {
                                $('#fecha-inicio-rango-dia').datetimepicker(
                                    {"pickTime":false,
                                        "pickDate":true,
                                        "minDate":"1\/1\/1900",
                                        "maxDate":null,
                                        "showToday":true,
                                        "language":"es_MX",
                                        "defaultDate":"",
                                        "disabledDates":[],
                                        "enabledDates":[],
                                        "icons":{"time":"fa fa-clock-o","date":"fa fa-calendar","up":"fa fa-chevron-up","down":"fa fa-chevron-down"},
                                        "useStrict":false,
                                        "sideBySide":false,
                                        "daysOfWeekDisabled":[],
                                        "collapse":true,
                                        "calendarWeeks":false,
                                        "viewMode":"days",
                                        "minViewMode":"days"
                                        ,"useCurrent":false,
                                        "useSeconds":false});

                                $('#fecha-fin-rango-dia').datetimepicker(
                                    {"pickTime":false,
                                        "pickDate":true,
                                        "minDate":"1\/1\/1900",
                                        "maxDate":null,
                                        "showToday":true,
                                        "language":"es_MX",
                                        "defaultDate":"",
                                        "disabledDates":[],
                                        "enabledDates":[],
                                        "icons":{"time":"fa fa-clock-o","date":"fa fa-calendar","up":"fa fa-chevron-up","down":"fa fa-chevron-down"},
                                        "useStrict":false,
                                        "sideBySide":false,
                                        "daysOfWeekDisabled":[],
                                        "collapse":true,
                                        "calendarWeeks":false,
                                        "viewMode":"days",
                                        "minViewMode":"days"
                                        ,"useCurrent":false,
                                        "useSeconds":false});
                            });

                            function resetRangoFechaDias() {
                                $("#fecha-inicio-rango-dia").val("");
                                $("#fecha-fin-rango-dia").val("");
                            }
                        </script>
                    </div>
                    <br> 
                    <div class="col-md-12 filter">
                        <label class="lab reporte">Filtros: </label>
                    </div>
                    <br>  
                    <div class="contenedor">
                     <div class="row g-4 mb-4">
                        <div id="sucursales"  class="col-md-9"></div>
                        <div id="clases" class="col-3"></div>
                        <div id="subcategorias"  class="col-md-4"></div>
                         <div id="tipoventas"  class="col-md-4"></div>
                        <div id="marcas" class="col-md-4"></div>
                      
                     
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="container-fluid mt-2">
        <div class="card"> 
            <div class="card-body" id="contenedor-graficas" >
              <div class="row">
                  <div class="col-md-6">
                      <div id="chart"></div>
                  </div>
                  <div class="col-md-6">

                  </div>
              </div>
            </div>
        </div>
    </div>

    <div id = "estadisticas"></div>

{% endblock %}

{% block javascripts %}
{{parent()}}
    <script src="{{ asset('lib/apexcharts-bundle/dist/apexcharts.js') }}"></script>
    <script src="{{ asset('js/funciones.js') }}"></script>
    <script src="{{asset('lib/jQuery-Autocomplete-master/dist/jquery.autocomplete.min.js')}}"></script>
    <script src="{{asset('lib/jQuery-Autocomplete-master/dist/jquery.autocomplete.min.js')}}"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.js"></script>
    <script src="https://cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json"></script>

    <script>
        document.querySelectorAll('.collapse').forEach (ocurrence => {
            ocurrence.addEventListener('show.bs.collapse', function(event) {

                $(".collapse").removeClass("show");
                $(event.target.id).addClass("show");
            })
        })

        $(document).ready(function(){


            //obtenerTiposVenta();
            
            $("#contenedor-graficas").addClass("d-none");
            var urlAutocompletarModelo=$("#url-autocompletar-modelo").val();

            $('#modelo').autocomplete({
                serviceUrl: urlAutocompletarModelo,
                delay: 100,
                minChars: 3,
                search: function(e,ui){
                    $(this).data("ui-autocomplete").menu.bindings = $();
                },
                onSelect: function (suggestion) {

                $("#modelo").val(suggestion.value);
                $("#descripcion").val(suggestion.descripcion);
                }
            });
        });

        function obtenerTiposVenta(){
            var idempresa=$("#idempresa").val();

            $.ajax({
                url: "{{path('reporte-ventas-tipoventa')}}",
                data: {idempresa:idempresa},

                dataType: "html"
            }).done(function( html ) {
                $("#tipoventas").html(html);

            }).fail(function() {
                alert( "error" );
            });
        }

        function obtenerSucursales(){
            var idempresa=$("#idempresa").val();


            $.ajax({
                url: "{{path('almacen-obtener-sucursal')}}",
                
                data: {idempresa:idempresa},
                dataType: "html"
            }).done(function( html ) {
                $("#sucursales").html(html);
                obtenerCategorias();

            }).fail(function() {
                alert( "error" );
            });
        }

        function obtenerClases(){
            var idempresa=$("#idempresa").val();


            $.ajax({
                url: "{{path('almacen-obtener-clase')}}",
                
                data: {idempresa:idempresa},
                dataType: "html"
            }).done(function( html ) {
                $("#clases").html(html);
                obtenerCategorias();

            }).fail(function() {
                alert( "error" );
            });
        }

        function obtenerCategorias(){
            
            checkboxesClase = document.getElementsByName('clase');

            clases = [];

            for(var i=0, n=checkboxesClase.length;i<n;i++) {
                
                if(checkboxesClase[i].checked)
                {
                    clases.push(checkboxesClase[i].value);
                }
                    
            }

            $.ajax({
                url: "{{path('almacen-obtener-subcategoria')}}",
                
                data: {clases:clases},
                dataType: "html"
            }).done(function( html ) {
                $("#subcategorias").html(html);
                obtenerMarcas();

            }).fail(function() {
                alert( "error" );
            });
        }

        function obtenerMarcas(){
            
            checkboxesCategorias = document.getElementsByName('categoria');

            categorias = [];

            for(var i=0, n=checkboxesCategorias.length;i<n;i++) {
                
                if(checkboxesCategorias[i].checked)
                {
                    categorias.push(checkboxesCategorias[i].value);
                }
                    
            }


            $.ajax({
                url: "{{path('almacen-obtener-marcas')}}",
        
                data: {categorias:categorias},
                dataType: "html"
            }).done(function( html ) {
                $("#marcas").html(html);

            }).fail(function() {
                alert( "error" );
            });
        }

        function obtenerStock(){
            var fechaInicio = document.getElementById('fecha-inicio-rango-dia').value;
            var fechaFin = document.getElementById('fecha-fin-rango-dia').value;

            $("#contenedor-graficas").removeClass("d-none");
            checkboxesSucursal = document.getElementsByName('sucursal');
            checkboxesBodegas = document.getElementsByName('bodega');
            checkboxesCampañas = document.getElementsByName('campaña');
            checkboxesClase = document.getElementsByName('clase');
            checkboxesCategorias = document.getElementsByName('categoria');
            checkboxesMarcas = document.getElementsByName('marca');
            checkboxesTipoVenta = document.getElementsByName('tipoVenta');
            modelo = $("#modelo").val();


            
            sucursales = [];
            bodegas = [];
            campañas = [];
            clases = [];
            categorias = [];
            marcas = [];
            tipoventas = [];

            for(var i=0, n=checkboxesSucursal.length;i<n;i++) {
                
                if(checkboxesSucursal[i].checked)
                {
                    sucursales.push(checkboxesSucursal[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesBodegas.length;i<n;i++) {
                
                if(checkboxesBodegas[i].checked)
                {
                    bodegas.push(checkboxesBodegas[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesCampañas.length;i<n;i++) {
                
                if(checkboxesCampañas[i].checked)
                {
                    campañas.push(checkboxesCampañas[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesClase.length;i<n;i++) {
                
                if(checkboxesClase[i].checked)
                {
                    clases.push(checkboxesClase[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesCategorias.length;i<n;i++) {
                
                if(checkboxesCategorias[i].checked)
                {
                    categorias.push(checkboxesCategorias[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesMarcas.length;i<n;i++) {
                
                if(checkboxesMarcas[i].checked)
                {
                    marcas.push(checkboxesMarcas[i].value);
                }
                    
            }

            for(var i=0, n=checkboxesTipoVenta.length;i<n;i++) {
                
                if(checkboxesTipoVenta[i].checked)
                {
                    tipoventas.push(checkboxesTipoVenta[i].value);
                }
                    
            }

            sucursalesSeleccionadas = [...sucursales, ...bodegas, ...campañas];

            var url =$("#url-almacen-obtener-informacion-producto").val();

            let productType = $("#product-type-select").val()
            var idempresa=$("#idempresa").val();

            // Agrega las fechas al objeto de datos que se enviará a tu controlador Symfony.
            var datos = {
                clases: clases,
                sucursales: sucursalesSeleccionadas,
                categorias: categorias,
                marcas: marcas,
                modelo: modelo,
                fechaInicio: fechaInicio, 
                fechaFin: fechaFin,
                tipoventas:tipoventas,
                productType:productType
            };

            $.ajax({
                url: url,
                data: datos,
                dataType: "html",
                beforeSend: function( xhr ) {
                    loadingGif("contenedor-graficas");
                }
            }).done(function( html ) {
                $("#contenedor-graficas").html(html);

            }).fail(function() {
                alert( "error" );
            });

            
        }

        
        function obtenerVentas(){
            checkboxesTipoVenta = document.getElementsByName('tipoVenta');
            checkboxesSucursal = document.getElementsByName('sucursal');
            var fechaInicio = document.getElementById('fecha-inicio-rango-dia').value;
            var fechaFin = document.getElementById('fecha-fin-rango-dia').value;

            tipoventas = [];
            sucursales = [];

            for(var i=0, n=checkboxesTipoVenta.length;i<n;i++) {
                
                if(checkboxesTipoVenta[i].checked)
                {
                    labelId = checkboxesTipoVenta[i].getAttribute('id');
                    labelElement = document.querySelector(`label[for="${labelId}"]`);
                    labelText = labelElement.textContent.trim();
                    tipoventas.push([checkboxesTipoVenta[i].value, labelText]);
                }
                    
            }

            for(var i=0, n=checkboxesSucursal.length;i<n;i++) {
                
                if(checkboxesSucursal[i].checked)
                {
                    sucursales.push(checkboxesSucursal[i].value);
                }
                    
            }


            $.ajax({
                url: "{{path('obtener-ventas')}}",
                
                data: {tipoventas:tipoventas,sucursales:sucursales,fechaInicio:fechaInicio,fechaFin:fechaFin},
                dataType: "html"
            }).done(function( html ) {
                
                $("#estadisticas").html(html);


            }).fail(function() {
                alert( "error" );
            });
        }
        

    </script>
{% endblock %}
