<div class="card">
            <div class="card-body">
                <div class="row justify-content-md-center">
                        <div class="col-md-12 table-responsive">
                            <table class="table table-striped caption-top" id="tablaTipoVentaTotales">
                            <caption style="font-weight: bold">Concentrado anual ventas por sucursal</caption>
                              <thead>
                                
                                <tr class="text-start">
                                  <th class="text-start" scope="col">Sucursal</th>

                                    {% for tipoventa in tipoventas %}

                                        <th class="text-start" scope="col">{{tipoventa[1]}}</th>

                                    {% endfor %}

                                </tr>
                              </thead>
                              <tbody id = "tableBodyVentas">
                                
                                {% for sucursal in totalesSucursal %}


                                    <tr >
                                    <td>{{ sucursal[0].sucursal }}</td>

                                    {% for tipoventa in tipoventas %}

                                        <td>
                                        {% set break = false %}
                                        {% for dato in sucursal  if not break %}
                                            {% if dato.idtipoventa == tipoventa[0] %}
                                                {{ dato.cantidad }}
                                                {% set break = true %}

                                            
                                            

                                            {% endif %}                
                                        {% endfor %}

                                        </td>

                                        

                                    {% endfor %}
                                    
                                    </tr>

                                {% endfor %}
                                
               
                              </tbody>
                            </table>
                        </div>

                        <div class="card mb-10">
                          <div class="card-body">

                              <div id="grafica-tipo-venta-sucursal"></div>
                          </div>
                        </div>

                        <div class="card col-6">
                          <div class="card-body">

                              <div id="grafica-tipo-venta"></div>
                          </div>
                        </div>

                        

                </div>


</div>

<script>


    $(document).ready( function () {
        
        /*var tableBody = document.querySelector("#tableBodyVentas");
        
        $("#tablaTipoVentaTotales").dataTable().fnDestroy()
        tableBody.innerHTML = "";*/

        $('#tablaTipoVentaTotales').DataTable({
            responsive: true,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ sucursales por página',
                
            },
        });

        let totales = Object.values({{ totales|json_encode|raw }});

        let tiposventa = totales.map(obj => obj.tipoventa);
        let cantidades = totales.map(obj => parseInt(obj.cantidad, 10));

        for (var i = 0; i < tiposventa.length; i++){
          if(tiposventa[i] == null) tiposventa[i] = "Público general";
        }



        var options = {
            series: cantidades,
            labels: tiposventa,
            chart: {
                width: "100%",
                type: 'donut',
            },
            dataLabels: {
                enabled: false
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        show: false
                    }
                }
            }],
            legend: {
                position: 'right',
                offsetY: 0,
                height: 230,
            }
        };

        var chart = new ApexCharts(document.querySelector("#grafica-tipo-venta"), options);
        chart.render();

        let productoSucursal = Object.entries({{ totalesTipoVentaSucursal|json_encode|raw }});

        //let productoSucursal2 = productoSucursal[1].map(obj => obj.tipoventa);

        tiposventa1 = [];
        cantidades1 = [];
        sucursales1 = [];

        productoSucursal.forEach((tipoventa) =>{

            tiposventa1.push(tipoventa[0]);
            cantidades1.push(tipoventa[1].map(obj => parseInt(obj.cantidad, 10)));
            sucursales1.push(tipoventa[1].map(obj => obj.sucursal));

        });

        datos = [];

        for ( var i = 0; i < cantidades1.length; i++){

            temp = {name: tiposventa1[i], data: cantidades1[i]};
            datos.push(temp);

        }


        var options = {
          series: datos,
          chart: {
          type: 'bar',
          height: 500, 
          stacked: true,
          toolbar: {
            show: true
          },
          zoom: {
            enabled: true
          },

        },
        responsive: [{
          breakpoint: 480,
          options: {
            legend: {
              position: 'bottom',
              offsetX: -10,
              offsetY: 0
            }
          }
        }],
        plotOptions: {
          bar: {
            horizontal: false,
            borderRadius: 10,
            dataLabels: {
            enabled: true,
            textAnchor: 'start',
            formatter: function(val, opt) {
                return opt.w.globals.labels[opt.dataPointIndex] + ":  " + val
            },
            offsetX: 0,
            },
          },
        },
        dataLabels: {
          enabled: true,

          formatter: function (val, opt) {
            return "$ " + number_format(val,2)
          }

        },
        xaxis: {
          categories: sucursales1[0],
        },
        yaxis: {
            labels: {
                formatter: function (val) {
                    return '$' + number_format(val,2);
                }
            }
        },
        legend: {
          position: 'right',
          offsetY: 40
        },
        fill: {
          opacity: 1
        }
        };

        var chart = new ApexCharts(document.querySelector("#grafica-tipo-venta-sucursal"), options);
        chart.render();
    
        
    });

    


    
</script>