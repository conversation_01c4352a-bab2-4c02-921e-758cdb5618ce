<div>
    <select id="defective-select-{{object.idstock}}" class="defective-select" onchange="changeDefective('{{object.idstock}}', this.value)">
        {% for StockState in admin.stockStates %}
            {% set selected = object.stockstateIdstockstate is not null and object.stockstateIdstockstate.idstockstate == StockState.idstockstate %}
            {% if StockState.status == '1' or selected %}
            <option value="{{StockState.idstockstate}}" {{ selected ? 'selected' : null}}>{{StockState.name | title}}</option>
            {% endif %}
        {% endfor %}
    </select>
</div>
