{% set today = "now"|date("Y-m-d") %}

{% if is_granted('ROLE_SUPER_ADMIN') %}
    {% if object.status == "1" %}
        <a class="btn btn-sm btn-warning" href="{{ admin.generateObjectUrl('cancelarVenta', object) }}"
           data-bs-toggle="tooltip" data-bs-placement="top" title="Cancelar Venta">
            <i class="fa fa-window-close" aria-hidden="true"></i>
        </a>
    {% endif %}
{% elseif object.cotizacion == "1" %}
    {% if object.status == "1" %}
        <a class="btn btn-sm btn-warning" href="{{ admin.generateObjectUrl('cancelarVenta', object) }}"
           data-bs-toggle="tooltip" data-bs-placement="top" title="Cancelar Venta">
            <i class="fa fa-window-close" aria-hidden="true"></i>
        </a>
    {% endif %}
{% endif %}

{% if is_granted('ROLE_VENDEDOR') and app.user.puesto == "Optometrista" %}
    {% if object.status == "1" and object.fechacreacion|date("Y-m-d") == today %}
        <a class="btn btn-sm btn-success" href="{{ admin.generateObjectUrl('cancelarVenta', object) }}"
           data-bs-toggle="tooltip" data-bs-placement="top" title="Cancelar Venta">
            <i class="fa fa-window-close" aria-hidden="true"></i>
        </a>
    {% endif %}
{% elseif object.cotizacion == "1" %}
    {% if object.status == "1" and object.fechacreacion|date("Y-m-d") == today %}
        <a class="btn btn-sm btn-success" href="{{ admin.generateObjectUrl('cancelarVenta', object) }}"
           data-bs-toggle="tooltip" data-bs-placement="top" title="Cancelar Venta">
            <i class="fa fa-window-close" aria-hidden="true"></i>
        </a>
    {% endif %}
{% endif %}
