<div>
    <a href="#" class="btn btn-sm btn-success " data-toggle="modal" data-target="#modalChangePrecio">
Cambiar Precio
    </a>
</div>

<div class="modal fade" id="modalChangePrecio" tabindex="-1" role="dialog" aria-labelledby="modalChangePrecioLabel"
     aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalChangePrecioLabel">Cambiar Precio</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="formChangePrecio">
                    <div class="form-group">
                        <div class="form-group">
                            <label for="price">Nuevo Precio (SIN IVA):</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="price" name="price" placeholder="0.00">
                            </div>
                        </div>
                    </div>
                    <button type="button"
                            onclick="changePrice('{{ path('admin.stockalmacen.changePrecio', {'id': object.idstock}) }}');"
                            class="btn btn-primary">Guardar
                    </button>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<script>

    function changePrice(url) {
        let priceStock = $("#price").val();
        console.log(priceStock)
        $.ajax({
            url: url,
            data: {priceStock: priceStock},
            method: 'POST',
            success: function (response) {
                Swal.fire({
                    title: "El precio ha cambiado",
                    text: "Aceptar",
                    icon: "success"
                });
                $('#modalChangePrecio').modal('hide');
                location.reload();
            },
            error: function () {
                Swal.fire({
                    title: "Algo ha ocurrido mal",
                    text: "Aceptar",
                    icon: "error"
                });
            }
        });
        console.log(priceStock);
    }
</script>
