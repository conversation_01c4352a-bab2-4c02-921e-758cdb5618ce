<div>
    <select id="defective-select-{{object.idordenlaboratorio}}" class="defective-select" onchange="changeDefective('{{object.idordenlaboratorio}}', this.value)">
        {% for key,OrderStage in admin.orderStages %}
            {% set selected = (object.stage -1) == key%}
           
            <option value="{{ key }}" {{ selected ? 'selected' : null }}>{{ OrderStage }}</option>
          
        {% endfor %}
    </select>
</div>

<script>
    function changeDefective(orderId, newStage) {
        console.log(`Intentando cambiar stage de la orden ${orderId} a ${newStage}`);

        fetch('/admin/dashboard/update-stage', { 
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                idordenlaboratorio: orderId,
                stage: newStage
            })
        })
        .then(response => {
            console.log("Código de respuesta:", response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Respuesta del servidor:', data);
            if (data.success) {
                alert("Estado actualizado correctamente.");
            } else {
                alert("Error al actualizar: " + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert("Hubo un problema: " + error.message);
        });
    }
</script>