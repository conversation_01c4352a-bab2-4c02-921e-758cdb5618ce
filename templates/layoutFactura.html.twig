<!--
=========================================================
* Material Dashboard 2 - v3.0.1
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard
* Copyright 2022 Creative Tim (https://www.creative-tim.com)
* Licensed under MIT (https://www.creative-tim.com/license)
* Coded by Creative Tim

=========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
-->
<!DOCTYPE html>
<html lang="en">

<head>
  <link rel="apple-touch-icon" sizes="76x76" href="../assets/img/apple-icon.png">
  <link rel="icon" type="image/png" href="../assets/img/favicon.png">
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>
        {% block titleHead %}

        {% endblock %}
    </title>

    <!--     Fonts and icons     -->
    <link rel="stylesheet" type="text/css" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,900|Roboto+Slab:400,700" />
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet">
    <!-- Nucleo Icons -->
    <link href="{{('/theme/facturacion/css/nucleo-icons.css')}}" rel="stylesheet" />
    <link href="{{('/theme/facturacion/css/nucleo-svg.css')}}" rel="stylesheet" />
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/js/all.min.js"></script>
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
    <!-- Theme CSS -->
    <link type="text/css" href="{{('/theme/facturacion/css/material-kit.min.css')}}" rel="stylesheet">
    <!-- CSS Files -->
    <link id="pagestyle" href="{{('/theme/facturacion/css/material-kit.css?v=3.0.4')}}" rel="stylesheet" />
    <link href="{{ asset('css/admin.css')}}?version={{ version }}" rel="stylesheet" />
    <link href="{{ asset('css/styles.css')}}?version={{ version }}" rel="stylesheet" />
    {% block stylesheets %}
    {% endblock %}

    <!-- WhatsApp Floating Button Styles -->
    <style>
        .whatsapp-float {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            background: linear-gradient(135deg, #25d366, #128c7e);
            border-radius: 50px;
            padding: 15px 20px;
            box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-width: 70px;
            animation: pulse 2s infinite;
        }

        .whatsapp-float:hover {
            background: linear-gradient(135deg, #128c7e, #25d366);
            transform: translateY(-3px);
            box-shadow: 0 6px 25px rgba(37, 211, 102, 0.6);
            color: white;
            text-decoration: none;
        }

        .whatsapp-float:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.3);
            color: white;
            text-decoration: none;
        }

        .whatsapp-float img {
            width: 35px;
            height: 35px;
            margin-bottom: 5px;
            filter: brightness(0) invert(1);
        }

        .whatsapp-float .icon-text {
            font-size: 10px;
            font-weight: 700;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            line-height: 1;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
            }
            50% {
                box-shadow: 0 4px 20px rgba(37, 211, 102, 0.7);
            }
            100% {
                box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
            }
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {
            .whatsapp-float {
                bottom: 15px;
                right: 15px;
                padding: 12px 16px;
                min-width: 60px;
            }

            .whatsapp-float img {
                width: 30px;
                height: 30px;
                margin-bottom: 3px;
            }

            .whatsapp-float .icon-text {
                font-size: 9px;
            }
        }

        /* Small mobile devices */
        @media (max-width: 480px) {
            .whatsapp-float {
                bottom: 10px;
                right: 10px;
                padding: 10px 14px;
                min-width: 55px;
            }

            .whatsapp-float img {
                width: 28px;
                height: 28px;
            }

            .whatsapp-float .icon-text {
                font-size: 8px;
            }
        }

        /* Tablet landscape */
        @media (min-width: 769px) and (max-width: 1024px) {
            .whatsapp-float {
                bottom: 25px;
                right: 25px;
            }
        }

        /* Large screens */
        @media (min-width: 1200px) {
            .whatsapp-float {
                bottom: 30px;
                right: 30px;
                padding: 18px 22px;
                min-width: 75px;
            }

            .whatsapp-float img {
                width: 40px;
                height: 40px;
            }

            .whatsapp-float .icon-text {
                font-size: 11px;
            }
        }

        /* Ensure it doesn't interfere with other fixed elements */
        .whatsapp-float {
            pointer-events: auto;
        }

        /* Accessibility improvements */
        .whatsapp-float:focus-visible {
            outline: 2px solid #25d366;
            outline-offset: 2px;
        }

        /* Social buttons in card header */
        .social-btn {
            position: relative;
            z-index: 10;
            transition: all 0.3s ease;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .social-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            color: white !important;
            text-decoration: none;
        }

        .social-btn:focus {
            outline: 2px solid rgba(255, 255, 255, 0.5);
            outline-offset: 2px;
            color: white !important;
            text-decoration: none;
        }

        .social-btn i {
            font-size: 16px;
            color: white;
        }

        /* Ensure card header has proper z-index */
        .card-header {
            position: relative;
            z-index: 5;
        }

        /* Fix for page-header overlay issue */
        .page-header {
            pointer-events: none;
        }

        .page-header .container,
        .page-header .card,
        .page-header .btn,
        .page-header a,
        .page-header button,
        .page-header input,
        .page-header select,
        .page-header textarea {
            pointer-events: auto;
        }

        /* Floating Social Media Buttons */
        .social-float-container {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 999;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .social-float {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: white;
            font-size: 20px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }

        .facebook-float {
            background: linear-gradient(135deg, #1877f2, #42a5f5);
        }

        .facebook-float:hover {
            transform: translateX(-5px) scale(1.1);
            box-shadow: 0 6px 25px rgba(24, 119, 242, 0.4);
            color: white;
            text-decoration: none;
        }

        .instagram-float {
            background: linear-gradient(135deg, #e4405f, #f77737, #fcaf45);
        }

        .instagram-float:hover {
            transform: translateX(-5px) scale(1.1);
            box-shadow: 0 6px 25px rgba(228, 64, 95, 0.4);
            color: white;
            text-decoration: none;
        }

        .social-float:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
            color: white;
            text-decoration: none;
        }

        /* Responsive adjustments for social buttons */
        @media (max-width: 768px) {
            .social-float-container {
                right: 15px;
                gap: 12px;
            }

            .social-float {
                width: 45px;
                height: 45px;
                font-size: 18px;
            }
        }

        @media (max-width: 480px) {
            .social-float-container {
                right: 10px;
                gap: 10px;
            }

            .social-float {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }
        }

        /* Large screens */
        @media (min-width: 1200px) {
            .social-float-container {
                right: 30px;
                gap: 18px;
            }

            .social-float {
                width: 55px;
                height: 55px;
                font-size: 22px;
            }
        }
    </style>
</head>

<body class="sign-in-basic">
  <!-- Navbar Transparent -->

  <!-- End Navbar -->
  <div class="page-header align-items-start min-vh-100" style="background-image: url('{{ asset('img/facturacion-'~prefijo~'.png') }}');" >
    <span class="mask bg-gradient-dark opacity-6"></span>
    <div class="container-fluid d-flex align-items-center justify-content-center">
      <div class="row">
        <div class="col-lg-12 col-md-8 col-12 mx-auto">
          <div class="card z-index-0 fadeIn3 fadeInBottom">
            <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
              <div class="bg-gradient-primary shadow-primary border-radius-lg py-3 pe-1" id="Container">
                <img class="logo-empresa" width="180" src="{{logo64}}" alt="" />
                <h5 class="text-white font-weight-bolder text-center mt-2 mb-0" style="padding-top: 14px;">Facturación</h5>
                <div class="row mt-3">
                  <div class="col-2 text-center ms-auto">
                    <a class="btn btn-link px-3 text-white social-btn" target="_blank" href="https://www.facebook.com/Optimo.opticas" aria-label="Facebook">
                      <i class="fab fa-facebook-f"></i>
                    </a>
                  </div>
                  <div class="col-2 text-center me-auto">
                    <a class="btn btn-link px-3 text-white social-btn" target="_blank" href="https://www.instagram.com/optimo.opticas/" aria-label="Instagram">
                      <i class="fab fa-instagram"></i>
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <div class="card-body">
        {% block content %}
        {% endblock %}
            </div>
          </div>
        </div>
      </div>
      <!-- WhatsApp Floating Button -->
      <a href="https://wa.me/5545150310" target="_blank" class="whatsapp-float" aria-label="Contactar por WhatsApp">
        <img src="{{ asset('img/whatsapp.svg') }}" alt="WhatsApp" />
        <p class="icon-text">AYUDA</p>
      </a>

      <!-- Floating Social Media Buttons -->
      <div class="social-float-container d-none">
        <a href="https://www.facebook.com/Optimo.opticas" target="_blank" class="social-float facebook-float" aria-label="Síguenos en Facebook">
          <i class="fab fa-facebook-f"></i>
        </a>
        <a href="https://www.instagram.com/optimo.opticas/" target="_blank" class="social-float instagram-float" aria-label="Síguenos en Instagram">
          <i class="fab fa-instagram"></i>
        </a>
      </div>
    </div>
    <footer class="footer position-absolute bottom-2 mt-20 w-100">
      <div class="container">
        <div class="row align-items-center justify-content-lg-between">
          <div class="col-12 col-md-6 my-auto">
            <div class="copyright text-center text-sm text-white text-lg-start">
              © <script>document.write(new Date().getFullYear())</script>,
              Óptimo Ópticas
            </div>
          </div>
          <div class="col-12 col-md-6">
            <!-- Social media links moved to floating buttons -->
          </div>
        </div>
      </div>
    </footer>
  </div>


{% block javascripts %}
{% endblock %}
  <!--   Core JS Files   -->

  <script src="{{('/theme/facturacion/js/core/popper.min.js')}}" type="text/javascript"></script>
  <script src="{{('/theme/facturacion/js/core/bootstrap.min.js')}}" type="text/javascript"></script>
  <script src="{{('/theme/facturacion/js/plugins/perfect-scrollbar.min.js')}}"></script>
  <!-- <script src="{{('/public/js/funciones.js')}}"></script> -->
  <script src="{{ asset('js/funciones.js') }}"></script>
  <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <!--  Plugin for Parallax, full documentation here: https://github.com/wagerfield/parallax  -->
  <script src="{{('/theme/facturacion/js/plugins/parallax.min.js')}}"></script>
  <!-- Control Center for Material UI Kit: parallax effects, scripts for the example pages etc -->
  <!--  Google Maps Plugin    -->
  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDTTfWur0PDbZWPr7Pmq8K3jiDp0_xUziI"></script>
  <script src="{{('/theme/facturacion/js/material-kit.min.js?v=3.0.4')}}" type="text/javascript"></script>
</body>

</html>
