{% block javascripts %}
  <script>


    $("#formularioClienteFactura").submit(function(e) {
        e.preventDefault();
        var formData = new FormData(this);

        $.ajax({
            url: "{{path('formularioFacturacion')}}",
            type: 'POST',
            data: formData,
            success: function (html) {

                $("#formulario").html(html);
              //  console.log(datos);
                //alert('Tus datos se han mandado correctamente y se enviarán por correo en 24 horas')
                //Swal.fire('Tus datos se han mandado correctamente y se enviarán por correo en 24 horas')
              //  formulario();
            },
            cache: false,
            contentType: false,
            processData: false
        });
    });
    </script>
<script>
    $("#clientefacturadatos_tipoPersona").change(function() {
      var tipoPersona = $(this).val();
      
      $.ajax({
        url: "{{path('seleccionar-tipo-persona')}}",
        type: 'POST',
        dataType:"html",
        data: { tipoPersona: tipoPersona },
        success: function(html) {
          $("#tipoFM").html(html);
          
          //console.log(response);
        },
        error: function() {
          console.log("error")
        }
      });
    });


</script>


{% endblock %}



{% if app.session.flashBag.has('notice') %}
    {% for flash_message in app.session.flashbag.get('notice') %}
        <div class="flash-notice">
            {{ flash_message }}
            <button type="button" id="aceptar" onclick='formulario()'>Aceptar</button>
        </div>
{% endfor %}
{% else %}


    {% if exito != true %}
        
    
        {% if msj != "" %}

        <script>
          selectRFCDFI();
          Swal.fire('No se pudo completar la solicitud','{{ msj }}','warning')
        </script>

        {% endif %}

    {% else %}
        {% if formularioGuardado %}
            <script>
                Swal.fire('Tus datos se han mandado correctamente y se enviarán por correo en 24 horas','Nota: recuerda revisar la carperta de spam','success')
                formulario();
            </script>
        {% endif %}
    {% endif %}

    
    
{{ form_start(form, {attr: {'id': 'formularioClienteFactura', 'enctype': 'multipart/form-data' }}) }}
{{ form_errors(form) }}

<div class="row">
  <div class="col-md-6 ">
    {{ form_row(form.folio) }}
  </div>
  <div class="col-md-6 ">
    {{ form_row(form.email) }}
  </div>
</div>

<div class="col-md-12 ">
{{ form_row(form.razonsocial) }}
</div>

<div class="row">
  <div class="col-md-6 ">
    {{ form_row(form.rfc) }}
  </div>
  <div class="col-md-6 ">
    {{ form_row(form.codigopostal) }}
  </div>
</div>

<div class="col-md-12 form">
{{ form_row(form.tipoPersona) }}
</div>


<div id="tipoFM" class="col-md-12 form">
</div>

<div class="col-md-12 form">
{{ form_row(form.brochure, {attr: {'id': 'archivo' }}) }}

{{ form_end(form) }}
</div>


{% endif %}


