{% extends 'layoutFactura.html.twig' %} 

{% block title %}Hello FacturacionController!{% endblock %}

{% block javascripts %}
    {{ parent() }}

    <script src={{ asset('js/jquery.js') }}></script>
    <script>

        $(document).ready(function () {
            formulario();


        });


        function selectRFCDFI() {

            var tipoPersona = $("#clientefacturadatos_tipoPersona").val();

            var request = $.ajax({
                url: "{{ path('seleccionar-tipo-persona') }}",
                method: 'POST',
                dataType: 'html',
                data: {tipoPersona: tipoPersona},

            });

            request.done(function (html) {
                $("#tipoFM").html(html);
                usoCFDI = $("#clientefacturadatos_usocfdi").val();
                Rf = $("#clientefacturadatos_regimenfiscal").val();

                if (usoCFDI) $("#usocdfi").val(usoCFDI);
                else {

                    var newOption = $('<option>', {
                        value: 'Selecciona un tipo de persona primero',
                        text: 'Selecciona un tipo de persona primero'
                    });
                    $('#usocdfi').append(newOption);
                    $("#usocdfi").val("Selecciona un tipo de persona primero");
                }

                if (Rf) $("#regimenfiscal").val(Rf);
                else {
                    var newOption = $('<option>', {
                        value: 'Selecciona un tipo de persona primero',
                        text: 'Selecciona un tipo de persona primero'
                    });
                    $('#regimenfiscal').append(newOption);
                    $("#regimenfiscal").val("Selecciona un tipo de persona primero");
                }


            });

            request.fail(function (jqXHR, textStatus) {
                alert("Request failed: " + textStatus);
            });
        }

        function formulario() {


            var request = $.ajax({
                url: "{{ path('formularioFacturacion') }}",
                method: 'GET',
                dataType: 'html',

            });

            request.done(function (html) {
                $("#formulario").html(html);
                selectRFCDFI();
            });

            request.fail(function (jqXHR, textStatus) {
                alert("Request failed: " + textStatus);
            });
        }


        function asignarValorUsoCfdi(valor) {

            $("#clientefacturadatos_usocfdi").val(valor);
        }

        function asignarValorRegimenFiscal(valor) {

            $("#clientefacturadatos_regimenfiscal").val(valor);
        }

    </script>

{% endblock %}


{% block content %}

    <div id="formulario">
    </div>


{% endblock %}
