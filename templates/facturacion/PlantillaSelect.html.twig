<div id="tipoFM" > 

<label class="form-label required">Uso CFDI</label>
<select name="usocfdiMoral" class="form-control" onchange="TwoFunction(this.value)" id="usocdfi">
  
</select>

<label class="form-label required">Regimen Fiscal</label>
<select name="regimenFiscal" class="form-control" onchange="asignarValorRegimenFiscal(this.value)" id="regimenfiscal">
  
</select>
</div>

<script>

var usoCDFI1 = Object.values({{ opcionesUsoCfdi|json_encode|raw }});
var regimenFiscal = Object.values({{ regimenFiscal|json_encode|raw }});

var selectUsoCDFI = document.getElementById('usocdfi');
var selectRF = document.getElementById('regimenfiscal');



for (var i = 0; i < usoCDFI1.length; i++) {
    var opt = document.createElement('option');
    opt.value = usoCDFI1[i];
    opt.innerHTML = usoCDFI1[i];
    selectUsoCDFI.appendChild(opt);
}

for (var i = 0; i < regimenFiscal.length; i++) {
  var opt = document.createElement('option');
  opt.value = regimenFiscal[i];
  opt.innerHTML = regimenFiscal[i];
  if ( regimenFiscal[i] != "Sueldos y salarios e ingresos asimilados a salarios") selectRF.appendChild(opt);
      
}

function TwoFunction(value){

  asignarValorUsoCfdi(value)
  ChangeRF();


}

function ChangeRF(){

  check = $("#usocdfi").val();
  if(check == "Honorarios médicos, dentales y gastos hospitalarios"){
    $("#regimenfiscal").empty();

    for (var i = 0; i < regimenFiscal.length; i++) {
      var opt = document.createElement('option');
      opt.value = regimenFiscal[i];
      opt.innerHTML = regimenFiscal[i];
      selectRF.appendChild(opt);

    }

  }
  else{
    $("#regimenfiscal").empty();

    for (var i = 0; i < regimenFiscal.length; i++) {
      var opt = document.createElement('option');
      opt.value = regimenFiscal[i];
      opt.innerHTML = regimenFiscal[i];
      if ( regimenFiscal[i] != "Sueldos y salarios e ingresos asimilados a salarios") selectRF.appendChild(opt);

    }
  }

}

</script>

