{% if mappedOrders | length > 0 %}
<div id="accordion">
  {% for trackingNumber, orders in mappedOrders %}
  <div class="card">
    <div class="card-header" id="headingTwo">
      <h5 class="mb-0">
        <button class="btn btn-link collapsed" data-toggle="collapse" data-target="#collapse-{{trackingNumber}}" aria-expanded="false" aria-controls="collapse-{{trackingNumber}}">
          Pedido {{trackingNumber}}
        </button>
      </h5>
    </div>
    <div id="collapse-{{trackingNumber}}" class="collapse" aria-labelledby="headingTwo" data-parent="#accordion">
      <div class="card-body table-responsive">
        <table class="table">
          <thead class="table-success">
            <th scope="col">
              <input type="checkbox" name="allOrders" onClick="toggleOrdersPerShipment(this,'{{trackingNumber}}')" />
            </th>
            <th scope="col"><PERSON><PERSON></th>
            <th scope="col">Marca</th>
            <th scope="col">Modelo</th>
            <th scope="col">SKU</th>
          </thead>
          <tbody class="text-center">
            {% for order in orders %}
              <tr>
                <th class="text-center" scope="row">
                  <input type="checkbox" name="order-{{trackingNumber}}" value="{{order.idshipmentordenlaboratorio}}"/>
                </th>
                <td>{{order.locationName}}</td>
                <td>{{order.brand}}</td>
                <td>{{order.modelo}}</td>
                <td>
                  {% if order.codigobarras != null  %}
                      {{order.codigobarras}}
                  {% else %}
                      {{order.codigobarrasuniversal}}
                  {% endif %}
                </td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
        <div class="form-floating">
          <textarea class="form-control" id="shipment-commentaries-textarea-{{trackingNumber}}" style="height: 100px"></textarea>
          <label for="shipment-commentaries-textarea">Comentarios</label>
      </div>
        <div class="d-flex justify-content-center mt-3">
          <button class="btn btn-success ms-5 me-5" onclick="processOrders('{{trackingNumber}}',1)">Se entregaron</button>
          <button class="btn btn-danger ms-5 me-5" onclick="processOrders('{{trackingNumber}}')">No se pudo entregar</button>
      </div>
      </div>
    </div>
  </div>
  {% endfor %}
</div>
{% else %}
<h4 class="text-center m-5">No hay envíos creados</h4>
{% endif %}

<script>
    function toggleOrdersPerShipment(source,trackingNumber) {
        checkboxes = document.getElementsByName('order-'+trackingNumber);
        for(var i=0, n=checkboxes.length;i<n;i++) {
            checkboxes[i].checked = source.checked;
        }
    }

</script>