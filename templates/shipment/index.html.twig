{% extends 'admin/layout.html.twig' %}

{% block title %}configuración de envíos
{% endblock %}

{% block content %}

	<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.css"/>

	<div class="card">
		<div class="card-header card-header-info">
			<div class="container-fluid">
				<h3 class="text-center">Configuración de envíos</h3>
			</div>
		</div>
        <ul class="nav mb-3" id="ex-with-icons" role="tablist">
            <li class="nav-item" role="presentation" id = "tab1">
                <a id="collapse-button-1" class="btn-collapse" data-bs-toggle="collapse" href="#stage-1" role="button" aria-expanded="false" aria-controls="stage-1">
                    <i class="fa-regular fa-clock fa-lg me-3" aria-hidden="true"></i>PENDIENTES
                </a>
            </li>
            <li class="nav-item" role="presentation" id = "tab2">
                <a id="collapse-button-2" class="btn-collapse" data-bs-toggle="collapse" href="#stage-2" role="button" aria-expanded="false" aria-controls="stage-2">
                    <i class="fa-solid fa-van-shuttle fa-lg me-3" aria-hidden="true"></i>ENVIADOS
                </a>
            </li>
        </ul>
        <div class="tab-content" id="ex-with-icons-content">
            <!-- PENDIENTES -->
            <div class="collapse" id="stage-1">
                <div class="container-fluid">
					<div id="pending-shipments-table-container"></div>
                </div>
            </div>
            <!-- ENVIADOS -->
            <div class="collapse" id="stage-2">
                <div class="container-fluid">
					<div id="transit-shipments-table-container"></div>
                </div>
            </div>
	</div>

	<div class="mod " id="lens-config-modal" tabindex="-1" aria-labelledby="" aria-hidden="true">
		<div class="mod-dialog modal-dialog-scrollable">
			<div class="modal-content" style="border-radius:10px">
				<div class="modal-header bg-primary">
					<h1 class="modal-title fs-5" id="lens-config-title"></h1>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
				</div>
			</div>
		</div>
	</div>

	{% endblock %}

	{% block javascripts %}
		{{parent()}}

		<script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
		<script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
		<script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
		<script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>

		<script>

			$(document).ready(function () {
				
				getShipmentTable()
				getTransitShipmentTable()

				document.querySelectorAll('.collapse').forEach (ocurrence => {
					ocurrence.addEventListener('show.bs.collapse', function(event) {

						$(".collapse").removeClass("show");
						$(".btn-collapse").removeClass("bg-primary");
						$(".btn-collapse").removeClass("text-white");

						idDString = event.target.id;
						match = idDString.match(/stage-(\d+)/);

						if (match) {
							extractedNumber = match[1];
							number = parseInt(extractedNumber, 10);
							$("#collapse-button-"+number).addClass("bg-primary");
							$("#collapse-button-"+number).addClass("text-white");
						}
					})
				})

				$("#stage-1").collapse('show');
			});

			function getShipmentTable() {
				$.ajax({
					url: "{{ path('laboratory-get-pending-shipment-table') }}",
					type: "GET",
					beforeSend: loadingGif("pending-shipments-table-container"),
					dataType: "html"
				}).done(function (html) {
					$("#pending-shipments-table-container").html(html);
				}).fail(function () {
					alert("error");
				});
			}

			function getTransitShipmentTable() {
				$.ajax({
					url: "{{ path('laboratory-get-transit-shipment-table') }}",
					type: "GET",
					beforeSend: loadingGif("transit-shipments-table-container"),
					dataType: "html"
				}).done(function (html) {
					$("#transit-shipments-table-container").html(html);
				}).fail(function () {
					alert("error");
				});
			}
			
		</script>


	{% endblock %}
