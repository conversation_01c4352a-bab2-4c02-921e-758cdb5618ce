<table id="transit-order-laboratory-table" class="table table-responsive">
    <thead>
        <th># orden</th>
        <th>Folio de venta</th>
        <th>Sucursal</th>
        <th>Cliente</th>
        <th>Productos</th>
        <th>Mensajero</th>
        <th>Estado</th>
        <!--<th></th>-->
    </thead>
    <tbody>
        {% for index, ol in laboratoryOrders %}
            
            <tr>
                <td>{{ (index + 1) | number_format }}</td>
                <td>
                    {% if mappedSales[ol.idflujoexpediente] is defined %}
                        {% for foils in mappedSales[ol.idflujoexpediente] %}
                            {{foils}}&nbsp;
                        {% endfor %}
                    {% else %}
                        {{ol.folio}}
                    {% endif %}
                </td>
                <td>{{ol.destination}}</td>
                <td>{{ol.fullName}}</td>
                
                <td class="text-start">
                    <table class="nested-table table">
                        {% if mappedProducts[ol.idordenlaboratorio] is defined %}
                        {% for index, mappedProduct in mappedProducts[ol.idordenlaboratorio] %}
                            <tr class="row">
                                <th class="text-start col-1">{{index + 1}}</th>
                                <td class="col-6">{{mappedProduct.modelo}}</td>
                            </tr>
                        {% endfor %}
                        {% endif %}
                    </table>
                </td>
                <td>{{ol.deliveryMan}}</td>
                <td>{{orderStages[ol.stage - 1]}}</td>
                {# 
                <td class="text-center">
                    <button class="btn btn-primary"  onclick="openAdditionalProducts('{{ol.fullName}}', '{{ol.idordenlaboratorio}}')">
                        <i class="fa-solid fa-eye "></i>
                    </button>
                </td>
                #}
               
            </tr>
            
        {% endfor %}
    </tbody>
</table>

<script>

    $(document).ready(function(){

        $('#transit-order-laboratory-table').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ ordenes por página',
            },
            "columnDefs": [
                { "type": "num", "targets": [0] }
            ],
            dom: 'Bfrtip',
            buttons: [
                {
                    className: 'btn-primary btn',
                    filename: 'laboratory_order',
                    extend: 'excelHtml5',
                    text: 'Exportar excel',
                }
            ]
        });
    });

</script>
