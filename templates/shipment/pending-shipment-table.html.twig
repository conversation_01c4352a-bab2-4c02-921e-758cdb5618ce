<table id="order-laboratory-config-table" class="table table-responsive">
    <thead>
    <th>Número de Orden</th>
    <th>Folio de venta</th>
    <th>Sucursal</th>
    <th>Cliente</th>
    <th>Productos</th>
    <th>Mensajero</th>
    <th>Estado</th>
    </thead>
    <tbody>
    {% for index, ol in laboratoryOrders %}

        <tr>
            <td>{{ (index + 1) | number_format }}</td>
            <td>
                {% if mappedSales[ol.idflujoexpediente] is defined %}
                    {% for foils in mappedSales[ol.idflujoexpediente] %}
                        {{ foils }}&nbsp;
                    {% endfor %}
                {% else %}
                    {{ ol.folio }}
                {% endif %}
            </td>
            <td>
                {% set curLocation = (ol.idsucursal) ? ol.idsucursal : -1 %}
                <select id="location-select-{{ ol.idshipmentordenlaboratorio }}" class="form-control"
                        onchange="setNewLocation('{{ ol.idshipmentordenlaboratorio }}')">
                    {% for location in locations %}
                        <option value="{{ location.idsucursal }}" {{ (curLocation == location.idsucursal) ? 'selected' : null }}>
                            {{ location.nombre }}
                        </option>
                    {% endfor %}
                </select>
                <i id="check-location-{{ ol.idshipmentordenlaboratorio }}" class="fa-solid fa-check d-none"
                   style="color: green"></i>
            </td>
            <td>{{ ol.fullName }}</td>

            <td class="text-start">
                <table class="nested-table table">
                    {% if mappedProducts[ol.idordenlaboratorio] is defined %}
                        {% for index, mappedProduct in mappedProducts[ol.idordenlaboratorio] %}
                            <tr class="row">
                                <th class="text-start col-1">{{ index + 1 }}</th>
                                <td class="col-6">{{ mappedProduct.modelo }}</td>
                            </tr>
                        {% endfor %}
                    {% endif %}
                </table>
            </td>
            <td>
                {% set curDelivery = (ol.idusuario) ? ol.idusuario : -1 %}
                <select id="deliver-select-{{ ol.idshipmentordenlaboratorio }}" class="form-control"
                        onchange="setDeliveryMan('{{ ol.idshipmentordenlaboratorio }}')">
                    <option value="-1" {{ (curDelivery == -1) ? 'selected' : null }}>Sin mensajero</option>
                    {% for deliveryMan in deliveryMen %}
                        <option value="{{ deliveryMan.idusuario }}" {{ (curDelivery == deliveryMan.idusuario) ? 'selected' : null }}>
                            {{ deliveryMan.fullName }}
                        </option>
                    {% endfor %}
                </select>
                <i id="check-delivery-{{ ol.idshipmentordenlaboratorio }}" class="fa-solid fa-check d-none"
                   style="color: green"></i>
            </td>
            <td>{{ orderStages[ol.stage - 1] }}</td>
            {#
            <td class="text-center">
                <button class="btn btn-primary" data-toggle="modal" data-target="#lens-config-modal" onclick="openAdditionalProducts('{{ol.fullName}}', '{{ol.idordenlaboratorio}}')">
                    <i class="fa-solid fa-eye "></i>
                </button>
            </td>
            #}

        </tr>

    {% endfor %}
    </tbody>
</table>

<script>

    $(document).ready(function () {

        $('#order-laboratory-config-table').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json',
                lengthMenu: 'Mostrar _MENU_ ordenes por página',
            },
            "columnDefs": [
                {"type": "num", "targets": [0]}
            ],
            dom: 'Bfrtip',
            buttons: [
                {
                    className: 'btn-primary btn',
                    filename: 'laboratory_order',
                    extend: 'excelHtml5',
                    text: 'Exportar excel',
                }
            ]
        });
    });

    function setDeliveryMan(idshipmentordenlaboratorio) {
        var newDeliveryMan = $('#deliver-select-' + idshipmentordenlaboratorio).val();


        $.ajax({
            url: "{{ path('shipment-set-delivery-man') }}",
            type: 'GET',
            data: {idshipmentordenlaboratorio: idshipmentordenlaboratorio, newDeliveryMan: newDeliveryMan},
            dataType: "json"
        }).done(function (response) {
            if (response.success) {
                $('#deliver-select-' + idshipmentordenlaboratorio).css({'background-color': 'rgba(139, 242, 113, 0.5)'});
                $('#check-delivery-' + idshipmentordenlaboratorio).removeClass('d-none');
            } else $('#deliver-select-' + idshipmentordenlaboratorio).css({'background-color': 'rgba(242, 113, 113, 0.5)'});
            setTimeout(function () {
                $('#deliver-select-' + idshipmentordenlaboratorio).css({'background-color': '#FFFFFF'});
                $('#check-delivery-' + idshipmentordenlaboratorio).addClass('d-none');
            }, 1500);
        }).fail(function () {
            alert("error");
        });
    }

    function setNewLocation(idshipmentordenlaboratorio) {
        newLocation = $('#location-select-' + idshipmentordenlaboratorio).val();
        $.ajax({
            url: "{{ path('shipment-set-new-location') }}",
            type: 'GET',
            data: {idshipmentordenlaboratorio: idshipmentordenlaboratorio, newLocation: newLocation},
            dataType: "json"
        }).done(function (response) {
            if (response.success) {
                $('#location-select-' + idshipmentordenlaboratorio).css({'background-color': 'rgba(139, 242, 113, 0.5)'});
                $('#check-location-' + idshipmentordenlaboratorio).removeClass('d-none');
            } else $('#location-select-' + idshipmentordenlaboratorio).css({'background-color': 'rgba(242, 113, 113, 0.5)'});
            setTimeout(function () {
                $('#location-select-' + idshipmentordenlaboratorio).css({'background-color': '#FFFFFF'});
                $('#check-location-' + idshipmentordenlaboratorio).addClass('d-none');
            }, 1500);
        }).fail(function () {
            alert("error");
        });
    }

</script>
