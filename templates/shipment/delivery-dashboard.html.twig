{% extends 'admin/layout.html.twig' %}

{% block title %}Dashboard de envíos{% endblock %}

{% block content %}

	<div class="card rounded-4">
		<div class="card-header card-header-info">
			<div class="container-fluid">
				<h3 class="text-center">Dashboard de envíos</h3>
			</div>
		</div>
        <div class="card-body container">
            <div class="row">
                <div class="col-md-6">
                    <h4 class="text-center">Entregas pendientes</h4>
                    <div id="delivery-order-table-container"></div>
                    <button class="btn btn-success text-center" onclick="setNewShipment()">
                        Iniciar entregas
                    </button>
                </div>
                <div class="col-md-6">
                    <h4 class="text-center">Envíos pendientes</h4>
                    <div id="delivery-shipment-table-container"></div>
                </div>
            </div>

        </div>
        
	</div>

    <div class="mod " id="mark-delivered-modal" tabindex="-1" aria-labelledby="" aria-hidden="true">
		<div class="mod-dialog modal-dialog-scrollable">
			<div class="modal-content" style="border-radius:10px">
				<div class="modal-header bg-primary">
					<h1 class="modal-title fs-5" id="lens-config-title"></h1>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="mark-delivered-modal-body">
				</div>
			</div>
		</div>
	</div>

{% endblock %}

{% block javascripts %}
    
	{{parent()}}

	<script>

        $(document).ready(function(){
            getDeliveryOrderTable()
            getDeliveryShipmentTable()
        })

        function getDeliveryOrderTable(){
             $.ajax({
                url: "{{path('shipment-get-delivery-order-table')}}", 
                type: 'GET',
                beforeSend: loadingGif("delivery-order-table-container"),
                dataType: "html"
            }).done(function( html ) {
                $("#delivery-order-table-container").html(html);
            }).fail(function() {
                alert( "error" );
            });
        }

        function getDeliveryShipmentTable(){
             $.ajax({
                url: "{{path('shipment-get-delivery-shipments-table')}}", 
                type: 'GET',
                beforeSend: loadingGif("delivery-shipment-table-container"),
                dataType: "html"
            }).done(function( html ) {
                $("#delivery-shipment-table-container").html(html);
            }).fail(function() {
                alert( "error" );
            });
        }

        function setNewShipment(){
            checkboxesOrder = document.getElementsByName('order');
            orders = [];
            for(var i=0, n=checkboxesOrder.length;i<n;i++) {
                
                if(checkboxesOrder[i].checked)
                {
                    orders.push(checkboxesOrder[i].value);
                }
                    
            }

            if (orders.length > 0){

                $.ajax({
                    url: "{{path('shipment-set-new-shipment')}}", 
                    type: 'POST',
                    data: {orders:orders},
                    dataType: "json"
                }).done(function( response ) {
                    if (response.success){
                        Swal.fire({
                            title: "Envío creado",
                            text: "Número de rastreo: "+response.trackingNumber,
                            type: "success"
                        });
                        getDeliveryOrderTable()
                        getDeliveryShipmentTable()
                    } else {
                        Swal.fire({
                            title: "Hubo un problema",
                            text: response.msg,
                            type: "warning"
                        });
                    }
                }).fail(function() {
                    alert( "error" );
                });
            } else {
                Swal.fire({
                    title: "Hubo un problema",
                    text: "Debes seleccionar al menos un envío",
                    type: "warning"
                });
            }
        }

        function processOrders(trackingNumber, accept = 0){

            let commentaries = $("#shipment-commentaries-textarea-"+trackingNumber).val()
            let alertMsg = (accept == 1) ? "Se marcará como entregado el envío" : "Se marcará como no entregado el envío"

            console.log(commentaries);

            checkboxesOrder = document.getElementsByName('order-'+trackingNumber);
            orders = [];
            for(var i=0, n=checkboxesOrder.length;i<n;i++) {
                
                if(checkboxesOrder[i].checked) orders.push(checkboxesOrder[i].value);
                    
            }

            if (orders.length > 0){

                if (accept == 0 && !commentaries){
                Swal.fire({
                    title: "Necesitas agregar comentarios",
                    text: "Para marcar como no necesitas indicar el porqué",
                    type: "warning"
                });
                }
                else {
                Swal.fire({
                    title: '¿Está seguro?',
                    text: alertMsg,
                    type: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#28B463',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Aceptar',
                    cancelButtonText: 'Cancelar'
                }).then((result) => {
                    if (result.value) {
                    $.ajax({
                        url: "{{path('shipment-process-shipment')}}", 
                        type: 'POST',
                        data: {
                        orders:orders, 
                        accept:accept, 
                        commentaries:commentaries, 
                        fromLocation: 0
                        },
                        dataType: "json"
                    }).done(function( response ) {
                        if (response.success) getDeliveryShipmentTable()
                        else {
                        Swal.fire({
                            title: "Hubo un problema",
                            text: response.msg,
                            type: "warning"
                        });
                        }
                    }).fail(function() {
                        alert( "error" );
                    });
                    }
                })
                }
            } else {
                Swal.fire({
                title: "Hubo un problema",
                text: "Debes seleccionar al menos un envío",
                type: "warning"
                });
            }

        }
            
			
	</script>




{% endblock %}
