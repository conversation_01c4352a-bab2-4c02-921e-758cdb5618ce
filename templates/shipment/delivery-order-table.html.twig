{% if laboratoryOrders | length > 0 %}
<div class="table-responsive border-0">
    <table class="table border">
        <thead class="table-primary">
            <th scope="col">
                <input type="checkbox" name="allOrders" onClick="toggleOrders(this)" />
            </th>
            <th scope="col"><PERSON><PERSON></th>
            <th scope="col">Marc<PERSON></th>
            <th scope="col">Modelo</th>
            <th scope="col">SKU</th>
        </thead>
        <tbody>
            {% for laboratoryOrder in laboratoryOrders %}
                <tr>
                    <th class="text-center" scope="row">
                        <input type="checkbox" name="order" value="{{laboratoryOrder.idshipmentordenlaboratorio}}"/>
                    </th>
                    <td class="text-center">{{laboratoryOrder.locationName}}</td>
                    <td class="text-center">{{laboratoryOrder.brand}}</td>
                    <td class="text-center">{{laboratoryOrder.modelo}}</td>
                    <td class="text-center">
                        {% if laboratoryOrder.codigobarras != null  %}
                            {{laboratoryOrder.codigobarras}}
                        {% else %}
                            {{laboratoryOrder.codigobarrasuniversal}}
                        {% endif %}
                    </td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
    <h4 class="m-5 text-center">No hay entregas</h4>
{% endif %}


<script>
    function toggleOrders(source) {
        checkboxes = document.getElementsByName('order');
        for(var i=0, n=checkboxes.length;i<n;i++) {
            checkboxes[i].checked = source.checked;
        }
    }

</script>