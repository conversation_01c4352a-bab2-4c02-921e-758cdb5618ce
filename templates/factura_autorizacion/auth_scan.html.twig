
<tr id="row_{{ index }}{% if group is defined %}_{{group}}{% endif %}">
    <td id="index_{{ index }}" class=" text-center px-3">{{ index }}</td>
    <td id="folio_{{ index }}" class=" text-center px-3">{{ venta.folio }}</td>
    <td id="client_{{ index }}" class=" text-center px-3">{{ venta.cl_n }}</td>
    <td id="alt_client_{{ index }}" class=" text-center px-3">{{ venta.cl2_n ?? 'NA' }}</td>
    <td id="beneficiary_{{ index }}" class=" text-center px-3">{{ venta.beneficiarytype ?? 'NA' }}</td>
    <td id="employee_number_{{ index }}" class=" text-center px-3">{{ venta.cl2_nun ?? venta.cl_num }}</td>
    <td id="total_{{ index }}" class=" text-center px-3">${{ venta.pagado|number_format(2, '.', ',')}}</td>
    {% if checked is defined %}
    <td class=" text-center p-3">
        <input 
            type="checkbox" 
            id="checkbox_{{ index }}{% if group is defined %}_{{group}}{% endif %}" 
            class="idventa {% if group is defined %}checkbox-{{group}}{% endif %}" 
            name="scans[]" 
            value="{{ venta.idventa }}"
            {% if group is defined %}onchange = "calculateSelectedAuth('checkbox_{{ index }}_{{group}}','{{venta.pagado}}')"{% endif %}
            {{ checked == 'true' ? 'checked' : '' }}
        >
    </td>
    {% endif %}
</tr>