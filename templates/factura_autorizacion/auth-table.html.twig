{% for key, items in values %}
    {% if items is not empty %}
    
        <div class="row my-2">
            {% set id_key = key|replace({' ': ''}) %}
            <button class="btn btn-primary" type="button" data-toggle="collapse" data-target="#collapse{{id_key}}" aria-expanded="false" aria-controls="collapse{{id_key}}">
                {{key}}
            </button>

            <div class="collapse text-black py-2" id="collapse{{id_key}}">
                <table class="table table-bordered">
                    <tr>
                        <th></th>
                        <th class="px-">Folio</th>
                        <th class="px-3">Titular</th>
                        <th class="px-3">Beneficiario</th>
                        <th class="px-3">Relación</th>
                        <th class="px-3">Número de empleado</th>
                        <th class="px-3">Total</th>
                        <th class="px-3">Todos:&nbsp;&nbsp;<input type="checkbox" id="checkall-{{id_key}}" class="check-all" onchange="checkAll('{{id_key}}')"></th>
                    </tr>
                    {% for index, item in items %}
                        {% include 'factura_autorizacion/auth_scan.html.twig' with {'venta': item, 'index':index + 1, 'checked': 'false', 'group': id_key} %}
                    {% endfor %}
                </table>
            </div>
        </div>
    
    {% endif %}
{% endfor %}

<script>
    $(document).ready(function(){
        $("#submitbutton").prop("disabled",false)
    })
</script>