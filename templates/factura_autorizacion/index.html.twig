{% extends 'admin/layout.html.twig' %} 

{% block title %}| Autorizaciones{% endblock %}

{% block content %}
    <link href="{{ asset('css/styles/factura-autorizacion.css')}}?version={{ version }}" rel="stylesheet"/>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&family=League+Spartan:wght@100..900&display=swap" rel="stylesheet">

    <div class="container-fluid bg-white rounded-4 py-4 shadow">
        <h2 class="text-center page-title">{{titulo}}</h2>
        {% if stage < 4 %}
        <form id="myForm" onsubmit="handleSubmit(event)" class=" container scan">
            <label for="inputField" class="col-md-9 text-input">Escanea el código de barras: </label>
            <input type="text" class="col-md-3 form-control scan-input" id="inputField" name="inputField">
        </form>
        {% endif %}

        {% if stage < 4 %}
            <div class="container">
                <h4 class="page-text">
                    Autorizaciones
                    <strong class="text-danger">
                        (Escanea el código para agregar una autorización)
                    </strong>
                </h4>
            </div>
        {% endif %}

        <div class="container">
            <form id="secondForm" onsubmit="recompileAndSubmit(event)">
                {% if stage < 4 %}

                    <table id="scannedValuesContainer" class="table table-bordered">
                        <tr>
                            <th>#</th>
                            <th class="px-2">Folio</th>
                            <th class="px-3">Titular</th>
                            <th class="px-3">Beneficiario</th>
                            <th class="px-3">Relación</th>
                            <th class="px-3">Número de empleado</th>
                            <th class="px-3">Cantidad de la venta</th>
                            <th class="px-3">Seleccionar</th>
                        </tr>
                    </table>

                {% elseif stage == 4 %}
                <h3 class="text-center page-title text-black m-0">Autorizaciones seleccionadas: <span class="text-primary" id="auth-num">0<span></h3>
                <h3 class="text-center page-title text-black m-0 mb-3">Total seleccionado: <span class="text-primary" id="auth-total">0<span></h3>
                <div id="auth-table-container"></div>
            
                <h4 class="page-subtitle mt-5">Factura</h4>
                <label for="fileInput" class="text-input p-0">Carga un archivo:</label>
                <input type="file" id="fileInput" name="fileInput" required>
                {% endif %}
                <div class="row justify-content-center py-3">
                    <button class="col-auto btn btn-warning" id="submitbutton" type="submit">
                    {% if stage == 2 %}
                        Recibir
                    {% elseif stage == 3 %}
                        Enviar a facturar
                    {% elseif stage == 4 %}
                        Facturar
                    {% else %}
                        Enviar
                    {% endif %}
                    </button>
                    
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const collapses = document.querySelectorAll('.collapse');

            collapses.forEach(collapse => {
                collapse.addEventListener('show.bs.collapse', function () {
                    // Close other collapses
                    collapses.forEach(otherCollapse => {
                        if (otherCollapse !== collapse && otherCollapse.classList.contains('show')) {
                            new bootstrap.Collapse(otherCollapse, {
                                toggle: false
                            }).hide();
                        }
                    });
                });

                collapse.addEventListener('hide.bs.collapse', function () {
                    // Uncheck all checkboxes inside this collapse
                    const checkboxes = collapse.querySelectorAll('input[type="checkbox"]');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = false;
                    });
                });
            });
        });

    </script>

    <script>
        var scannedValues = [];
        var globalindex = 1;

        function handleSubmit(event) {
            event.preventDefault(); // Prevent the default form submission
            const inputValue = document.getElementById('inputField').value;
            document.getElementById('inputField').value = ''; // Clear the input field immediately

            console.log('rows', inputValue);

            if (!scannedValues.includes(inputValue)) {
                const curindex = globalindex;
                globalindex ++;
                // Make AJAX request to the Symfony backend
                fetch('{{ path('app_factura_autorizacion_auth') }}' + '/' + inputValue + '/' + curindex + '/' + {{stage}})
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && !scannedValues.includes(inputValue)) {
                            scannedValues.push(inputValue);
                            addValueToDiv(data.data, curindex); // Add the HTML content to the div
                        } else if (scannedValues.includes(inputValue)) {
                            Swal.fire({
                                title: 'Alerta',
                                text: 'Esta autorización ya esta registrada: ' + inputValue,
                                icon: "warning",
                                confirmButtonText: 'Entendido'
                            })
                        } else {
                            Swal.fire({
                                title: 'Alerta el valor ' + inputValue,
                                text: data.message,
                                icon: "warning",
                                confirmButtonText: 'Entendido'
                            })
                        }
                    })
                    .catch(error => {
                        console.error(error);
                    }
                );
            } else {
                Swal.fire({
                    title: 'Alerta',
                    text: 'Esta autorización ya esta registrada: ' + inputValue,
                    icon: "warning",
                    confirmButtonText: 'Entendido'
                })
            }
        }

        function addValueToDiv(htmlContent, divindex) {
            const container = document.getElementById('scannedValuesContainer');
            const div = document.createElement('tr');
            div.id = `row_${divindex}`;
            div.innerHTML = htmlContent;
            container.appendChild(div);
        }

        function recompileAndSubmit(event) {
            event.preventDefault();

            let button = document.getElementById('submitbutton');
            button.disabled = true;

            var formData = new FormData(event.target);

            // Collect data from table rows
            const rows = document.querySelectorAll('tr[id^="row_"]');

            const checkPrices = new Set();
            const checkLocations = new Set();
            rows.forEach(row => {
                
                const index = row.id.split('_')[1]; // Extract the index from the row ID
                const location = row.id.split('_')[2];
                checkLocation = (location) ? `_${location}` : '';
                const checkbox = document.getElementById(`checkbox_${index}`+checkLocation);
                if (checkbox.checked) {
                    const data = {
                        index: document.getElementById(`index_${index}`).innerText,
                        folio: document.getElementById(`folio_${index}`).innerText,
                        client: document.getElementById(`client_${index}`).innerText,
                        altClient: document.getElementById(`alt_client_${index}`).innerText,
                        beneficiaryType: document.getElementById(`beneficiary_${index}`).innerText,
                        employeeNumber: document.getElementById(`employee_number_${index}`).innerText,
                        total: document.getElementById(`total_${index}`).innerText,
                        idventa: checkbox.value,
                        isChecked: checkbox.checked
                    };
                    checkPrices.add($("#total_"+index).text());
                    checkLocations.add(location);
                    // Append data to formData
                    for (const key in data) {
                        formData.append(`rows[${index}][${key}]`, data[key]);
                    }
                }
            });

            if ({{stage}} < 4 || (checkPrices.size <= 1 && checkLocations.size <= 1)) {
                fetch('{{ path('app_factura_compiled_submit') }}/'+{{ stage + 1 }}, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            icon: "success",
                            title: "Enviadas correctamente",
                            showConfirmButton: false,
                            timer: 2000
                        });

                        var scancont = document.getElementById('scannedValuesContainer');
                        if (scancont) {
                            document.getElementById('scannedValuesContainer').innerHTML = '';
                            scannedValues = [];
                        }

                        if (data.file) {
                            const link = document.createElement('a');
                            link.href = 'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,' + data.file;
                            link.download = 'scans.xlsx';
                            document.body.appendChild(link);
                            link.click();
                            console.log("click");
                            document.body.removeChild(link);
                        }
                    } else {
                        Swal.fire({
                            icon: "error",
                            title: "Error",
                            text: "Error: " + data.message,
                        });
                    } 
                    
                    button.disabled = false;
                    {% if stage == 4 %}
                        getAuthTable()
                    {% endif %}
                })
                .catch(error => {
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: "No seleccionaste una autorización",
                    });
                    console.log(error);
                    button.disabled = false;
                });
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: "Todas las autorizaciones deben tener el mismo precio y misma unidad",
                });
                {% if stage == 4 %}
                    getAuthTable()
                {% endif %}
            }
        }
    </script>
    {% if stage == 4 %}
    <script>
        var authTotal = 0;
        var authNumber = 0;
        var selectedIds = new Set()
        
        $(document).ready(function(){
            getAuthTable()
        })

        function getAuthTable(){
            authTotal = 0;
            authNumber = 0;
            selectedIds.clear()
            $("#auth-num").text(authNumber)
            $("#auth-total").text(authTotal)
            ponerFormatoPesos("#auth-total");

            $.ajax({
                url: "{{path('facturaautorizacion-get-auth-table')}}", 
                type: 'GET',
                beforeSend: loadingGif("auth-table-container"),
                dataType: "html"
            }).done(function( html ) {
                $("#auth-table-container").html(html);

            }).fail(function() {
                alert( "error" );
            });
        }

        function checkAll(id){
            if($("#checkall-"+id).is(':checked')) $('.checkbox-'+id).prop('checked', true).trigger('change');
            else $('.checkbox-'+id).prop('checked', false).trigger('change');
        }

        function addEventListenerButton(){
            
                const $button = $('#submitbutton');

                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.attributeName === 'disabled') {
                            if ($button.prop('disabled')) {
                                console.log('Button was disabled, re-enabling it.');
                                $button.prop('disabled', false); // Re-enable the button
                            }
                        }
                    });
                });

                observer.observe($button[0], { attributes: true });
            
        }

        function calculateSelectedAuth(id, total){

            if ($("#"+id).is(':checked') && !selectedIds.has(id)){
                authNumber++
                authTotal+=parseFloat(total)
                selectedIds.add(id)
            } else if (!$("#"+id).is(':checked') && selectedIds.has(id)) {
                authNumber--
                authTotal-=parseFloat(total)
                selectedIds.delete(id)
            }
            $("#auth-num").text(authNumber)
            $("#auth-total").text(authTotal)
            ponerFormatoPesos("#auth-total");
        }

    </script>
    {% endif %}
{% endblock %}
